<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/3/10
 * Time: 17:58
 */
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('PRO_NAME') or define('PRO_NAME', 'dreame');//API项目名
defined('PRO_MALL_NAME') or define('PRO_MALL_NAME', 'dreame-php-mall');//API项目名
defined('PRO_ADMIN') or define('PRO_ADMIN', 'DAdmin');//管理后台项目名
defined('START_TIME') or define('START_TIME', microtime(true));//响应起始时间
defined('MAIN_MODULE') or define('MAIN_MODULE', 'main');//主模块
defined('MAIN_MODULE_V1') or define('MAIN_MODULE_V1', 'main\v1');//主模块(v1模块)
defined('MALL_MODULE') or define('MALL_MODULE', 'mall');//商城模块
defined('MSG_MODULE') or define('MSG_MODULE', 'msg');//消息模块
defined('LOG_MODULE') or define('LOG_MODULE', 'log');//日志模块
defined('HOST_NAME') or define('HOST_NAME', gethostname());//当前主机hostname
defined('WEB_PATH') or define('WEB_PATH', __DIR__.'/web');
defined('UPLOAD_TMP_PATH') or define('UPLOAD_TMP_PATH', __DIR__.'/runtime/tmp');
defined('IS_CLI') or define('IS_CLI',PHP_SAPI == 'cli');//是否是cli模式

#项目目录下的env文件和README.md同级,只存放dev/test/prod 三值之一; 正式服不存放该文件
$env = is_file(__DIR__."/.env") ? trim(file_get_contents(__DIR__."/.env")) : "prod";

defined('YII_ENV') or define('YII_ENV', $env);//定义环境

require(__DIR__ . '/vendor/autoload.php');
require(__DIR__ . '/vendor/yiisoft/yii2/Yii.php');
