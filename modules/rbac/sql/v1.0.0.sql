
CREATE TABLE IF NOT EXISTS `db_dreame_admin`.`t_role` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `r_name` varchar(10) NOT NULL DEFAULT '' COMMENT '角色名称',
    `r_access` text NOT NULL COMMENT '角色权限（保存t_uri表ID）',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0(是否禁用0：否，1:是)',
    `time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_n` (`r_name`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='角色权限表';


CREATE TABLE IF NOT EXISTS `db_dreame_admin`.`t_user` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `r_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '角色ID',
    `account` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '账号',
    `p_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级id',
    `pwd` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '密码(建议和SVN一致)',
    `salt` varchar(20) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '盐值',
    `nick` varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '昵称',
    `avatar` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '头像',
    `time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0(是否禁用0：否，1:是)',
    `ext_access` text CHARACTER SET utf8 NOT NULL COMMENT '扩展权限',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_a` (`account`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';


#############################初始化管理员账号-[admin:123456]###############################################

INSERT INTO `db_dreame_admin`.`t_role` (`r_name`,`r_access`,`time`) VALUE ("超级管理员","all",**********);
INSERT INTO `db_dreame_admin`.`t_user` (`r_id`,`account`,`pwd`,`salt`,`nick`,`time`,`ext_access`) VALUE (1,"admin","8d421e892a47dff539f46142eb09e56b","1234","管理员",**********,'');

########################################################################################################


CREATE TABLE IF NOT EXISTS `db_dreame_admin`.`t_sys_log` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `module_id` int(11) NOT NULL DEFAULT '0',
    `time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作时间',
    `ip` varchar(20) NOT NULL COMMENT 'IP',
    `text` text NOT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY `index_uid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统日志';
CREATE TABLE IF NOT EXISTS `db_dreame_admin`.`t_sys_log_2022` LIKE `db_dreame_admin`.`t_sys_log`;
CREATE TABLE IF NOT EXISTS `db_dreame_admin`.`t_uri` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) DEFAULT NULL COMMENT '组件名称',
    `p_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
    `uri` varchar(50) NOT NULL DEFAULT '' COMMENT '路由地址',
    `uri_name` varchar(100) NOT NULL DEFAULT '' COMMENT 'uri路由备注',
    `time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除（0:否，1:是）',
    `is_check` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '是否权限验证（0：否，1是）',
    `is_menu` tinyint(1) DEFAULT '1' COMMENT '是否为菜单 0否 1是',
    `rank` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '排序0-255（值越大越靠前）',
    `ext_uri` varchar(255) NOT NULL DEFAULT '' COMMENT '扩展uri',
    PRIMARY KEY (`id`) ,
    KEY `index_uri` (`uri`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='路由表';

########################默认权限接口
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (1, 'defaultAouth', 0, '', '默认权限', 1625817024, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (2, 'dataUpload', 1, 'back/data/upload', '图片上传接口', 1625817040, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (3, 'refreshMenu', 1, 'rbac/login/refresh-menu', '路由菜单刷新接口', 1625817051, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (4, 'auth-check', 1, 'rbac/manage/auth-check', '权限接口验证接口', 1625817031, 0, 1, 0, 0, '');
########################默认权限接口

########################系统设置
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (5, 'systemSettings', 0, '', '系统设置', 1620372140, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (6, 'uriManager', 5, 'system/uri', '路由管理', 1623066230, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (7, 'uriList', 6, 'rbac/manage/uri-list', '获取路由列表接口', 1623067124, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (8, 'modifyUri', 6, 'rbac/manage/modify-uri', '添加修改路由接口', 1623124209, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (9, 'uriDetail', 6, 'rbac/manage/uri-detail', '路由详情接口', 0, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (10, 'roleManger', 5, 'system/role', '角色管理', 1623066224, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (11, 'rolesList', 10, 'rbac/manage/roles', '获取角色列表接口', 1623124135, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (12, 'modifyRoles', 10, 'rbac/manage/modify-roles', '修改添加角色接口', 0, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (13, 'rolesDetail', 10, 'rbac/manage/roles-detail', '获取角色详情接口', 1623067152, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (14, 'roleDisable', 10, 'rbac/manage/role-disable', '角色禁用启用接口', 1625802411, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (15, 'userManger', 5, 'system/user', '管理员管理', 1623066219, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (16, 'userList', 15, 'rbac/manage/user', '获取管理列表接口', 1623067070, 0, 1, 0, 0, 'manage/lead-list,manage/roles-auth');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (17, 'userDetail', 15, 'rbac/manage/user-detail', '获取管理员详情接口', 1623067064, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (18, 'modifyUser', 15, 'rbac/manage/modify-users', '添加修改管理员接口', 1623067005, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (19, 'userDelete', 15, 'rbac/manage/user-delete', '删除管理员接口', 1623067019, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (24, 'rolesAuthList', 15, 'rbac/manage/roles-auth', '获取角色权限列表接口', 1623066487, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (20, 'logs', 5, 'system/list', '系统日志', 1623311087, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (21, 'modelLogs', 20, 'rbac/manage/logs-module', '日志模块条件接口', 1623122888, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (22, 'logsList', 20, 'rbac/manage/logs', '获取日志列表接口', 1623122885, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (23, 'loadLogsUrl', 20, 'rbac/manage/logs-export', '导出日志接口', 1623122882, 0, 1, 0, 0, '');
########################系统设置

########################控制面板
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (25, 'controlPanel', 0, '', '控制面板', 1623065887, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (26, 'apiLog', 25, 'control/log', '接口日志', 1623123345, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (27, 'logsList', 26, 'back/data/post', '获取请求记录列表接口', 1624437329, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (28, 'againExec', 26, 'back/data/again-exec', '列表接口再次请求接口', 1624437316, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (29, 'apiRequest', 25, 'control/api-request', '接口并发监控', 1622627763, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (30, 'requsetTime', 29, 'back/data/api-request', '接口并发监控接口', 1623123895, 0, 1, 0, 0, '');

####仅内网使用##########
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (31, 'codeExec', 25, 'control/code-exec', '代码执行', 1622627798, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (32, 'codeExec', 31, 'back/data/code-exec', '代码执行接口', 1623123864, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (33, 'codeDeploy', 25, 'control/code-deploy', '构建&热更新', 1623123375, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (34, 'codeDeploy', 33, 'back/cmd/bm-rebuild', '代码自动化部署接口', 1623123932, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (35, 'hotUpdate', 33, 'back/cmd/hot-update', '服务器代码热更新接口', 1623125392, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (36, 'cmdLock', 33, 'back/cmd/lock', '锁定服务器功能接口', 1625477323, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (37, 'cmdIsLock', 33, 'back/cmd/is-lock', '查看服务器锁定状态接口', 1625477444, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (38, 'codeRelease', 25, 'control/code-release', '外网代码同步', 1623123425, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (39, 'codeSync', 38, 'back/cmd/sync-code', '外网代码同步接口', 1623125467, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (40, 'syncList', 38, 'back/cmd/sync-code-list', '外网代码同步记录接口', 1623125497, 0, 1, 0, 0, '');
####仅内网使用##########

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (41, 'serverLoad', 25, 'control/server-load', '服务器负载', 1623134408, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (42, 'dataServerList', 41, 'back/data/server-list', '获取服务器列表接口', 1624438224, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (43, 'dataLoadAvg', 41, 'back/data/load-avg', '服务器负载接口', 1624438170, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (44, 'dataRedisInfo', 41, 'back/data/redis-info', 'redis信息接口', 1624438203, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (45, 'dataBranch', 41, 'back/data/branch', '查询分支信息接口', 1624438528, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (46, 'qps', 25, 'control/qps', 'QPS监控', 1628242083, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (47, 'dataQps', 46, 'back/data/qps', 'qps数据接口权限', 1628242380, 0, 1, 0, 0, '');
########################控制面板

########################用户查询########################
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (48, 'userCenter', 0, '', '用户中心', 1627031382, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (49, 'userInfo', 48, 'user/info', '用户查询', 1627031404, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (50, 'dataDiscard', 49, 'back/data/discard', '废弃指定用户接口权限', **********, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` VALUES (51, 'dataUser', 49, 'back/data/user', '查询用户信息接口权限', **********, 0, 1, 0, 0, '');


CREATE TABLE `db_dreame_admin`.`t_sync_code` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `tag` varchar(20) DEFAULT NULL COMMENT 'GIT TAG',
  `msg` varchar(100) NOT NULL DEFAULT '' COMMENT '简要描述',
  `opt` tinyint(4) unsigned NOT NULL DEFAULT '1' COMMENT '0回滚，1发布',
  `account` varchar(255) NOT NULL DEFAULT '' COMMENT '操作者',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='代码同步记录';