<?php


namespace app\modules\rbac\models;

use app\components\AdminRedisKeys;
use app\models\CUtil;
use yii\db\ActiveRecord;
use app\models\by;


class RolesModel extends ActiveRecord
{
    protected $expire_time = 3600;

    CONST STATUS = [
        'disable' => 1,  //禁用
        'enable'  => 0   //启用
    ];
    CONST ADMIN_RID = 1; //超级管理员id

    public static function getDb()
    {
        return by::dbMaster();
    }

    public static function tableName(): string
    {
        return "`db_dreame_admin`.`t_role`";
    }

    /**
     * @param $page
     * @param $page_size
     * @param string $name
     * @return array
     * 角色列表
     */
    public function RList($page, $page_size, $name = null)
    {
        $limit = CUtil::pagination($page, $page_size);

        $connection = self::find();
        $connection->from(self::tableName())->select(['*', 'FROM_UNIXTIME(time, "%Y-%m-%d %H:%i:%S") time']);
        $name = trim($name);
        if ($name != null) {
            $connection->andWhere(['like', 'r_name', $name]);
        }

        return $connection->asArray()->offset($limit[0])->orderBy('id ASC')->limit($limit[1])->all();
    }

    /**
     * @param $name
     * @return int
     * 角色总数
     */
    public function RCount($name = null)
    {
        $connection = self::find();
        $connection->from(self::tableName())->select(['*']);
        $name = trim($name);
        if ($name != null) {
            $connection->andWhere(['like', 'r_name', $name]);
        }
        $count = $connection->asArray()->count('*');
        return intval($count);
    }


    /**
     * @param $id
     * @param $name  //角色名称
     * @param $access //权限id 逗号分割
     * @param  $r_id //当前操作的角色ID
     * @return array
     * @throws \yii\db\StaleObjectException
     *  修改权限
     */
    public function modify($id, $name, $access, $r_id)
    {
        if (empty($name)) {
            return [false, '角色名称不能为空'];
        }
        if (empty($access)) {
            return [false, '角色权限不能为空'];
        }
        if ($id === 1) {
            return [false, '不允许修改超级管理员'];
        }
        //判断角色是否存在
        $account_data = self::find()->where(['r_name' => $name])->one();
        if (!empty($account_data) && ($account_data['id'] != $id || $id == '')) {
            return [false, '角色名称已存在'];
        }

        //todo 只有正式服严格检测
        if (YII_ENV_PROD){
            //获取当前 登录者的权限ID
            $all_access = by::adminRolesModel()->rolesAccess($r_id)['access_ids'] ?? null;
            if (empty($all_access)) {
                return [false, '没有获取到添加者的权限'];
            }
            $bool = by::adminUserModel()->isAdministrator($r_id);
            if (!$bool && $diff = array_diff(explode(',', $access), $all_access)) {  //被添加的角色权限不能大于添加者
                return [false, '权限设置错误, 用户没有权限id' . implode(',', $diff)];
            }
        }

        if ($id) {
            $record = self::findOne($id);
            $name && $record->r_name = $name;
            $access && $record->r_access = is_array($access) ? implode(',', $access) : $access;
            $record->time = time();
            $record->update();

            //删除角色权限key
            $key = AdminRedisKeys::roleMenuList();
            $redis = by::redis();
            $redis->hDel($key, $id);
            $key1 = AdminRedisKeys::roleInfo();
            $redis->hDel($key1, $id);
            $key2 = AdminRedisKeys::rolesAccess($id);
            $redis->del($key2);
        } else {
            $this->r_name = $name;
            $this->r_access = $access;
            $this->time = time();
            $this->save();
        }

        return [true, "修改成功，请当前角色账号重新登录以刷新权限"];
    }

    /**
     * @throws \yii\db\Exception
     * 角色修改状态
     */
    public function actionRoleDisable()
    {
        $id     = \Yii::$app->request->post('id', 0);
        $status = \Yii::$app->request->post('status', 0);
        if(!in_array($status, RolesModel::STATUS) || empty($id)){
            CUtil::json_response(-1,'参数错误');
        }
        if ($id == 1) {
            CUtil::json_response(-1,'超级管理员不能修改');
        }
        list($st, $ret) = by::adminRolesModel()->modifyStatus($id,$status);
        if(!$st) {
            CUtil::json_response(-1,$ret);
        }
        $msg  = $status == 0 ? '启用':'禁用';
        $msg  .= "管理员{$id}";
        by::systemLogsModel()->record($msg, RbacInfoModel::ROLE_MANAGER);
        CUtil::json_response(1, $ret);
    }

    /**
     * @param $r_id
     * TODO 获取指定角色的权限ID
     */
    public function rolesAccess($r_id)
    {
        $access_ids = $this->getInfo($r_id)['r_access'] ?? '';
        $access_ids = explode(',', $access_ids);
        $cache_data['access_ids'] = $access_ids;

        $cache_data['uri_path'] = [];
        $key   = AdminRedisKeys::rolesAccess($r_id);
        $redis = by::redis();
        $ttl = $redis->ttl($key);
        if($ttl < 0){
            $list       = by::adminUriModel()->menuList($r_id, $access_ids);
            foreach ($list as $val) {
                if ($val['uri'] !== '' && !in_array($val['uri'], $cache_data['uri_path'])) {
                   $cache_data['uri_path'][$val['uri']] = 1;
                }
            }
            $redis->hMSet($key, $cache_data['uri_path']);
            $redis->expire($key, $this->expire_time);
            $ttl = $this->expire_time;
        }
        $cache_data['ttl'] = $ttl;
        return $cache_data;
    }

    /**
     * @param $id
     * @return array
     * TODO 获取角色详情
     */
    public function getDetail($id)
    {
        $role_info = self::getInfo($id);
        $current_access = by::adminUriModel()->menuList($id, explode(',', $role_info['r_access']));
        $current_access = by::adminUriModel()->getParentList($current_access);
        return ['role_info' => $role_info, 'current_access' => $current_access];
    }

    /**
     * @param $id
     * 获取角色信息
     */
    public function getInfo($id)
    {
        $key = AdminRedisKeys::roleInfo();
        $redis = by::redis();
        $cache_data = json_decode($redis->hGet($key, $id), true);

        if (empty($cache_data)) {
            $cache_data = self::find()->where(['id' => $id])->asArray()->one();
            $redis->hSet($key, $id, json_encode($cache_data));
            $redis->expire($key, $this->expire_time);
        }
        return $cache_data;
    }

    /**
     * @param $id
     * @param $status
     * @return array
     * @throws \yii\db\Exception
     * 修改角色状态
     */
    public function modifyStatus($id,$status)
    {
        $info       = $this->getInfo($id);
        if(empty($info)) {
            return [false, '角色信息错误'];
        }

        $str        = $status == 0 ? '启用':'禁用';
        $tb         = self::tableName();
        $res        = by::dbMaster()->createCommand()->update($tb,['status'=>$status,'time'=>START_TIME],['id'=>$id])->execute();
        if($res){
            //修改角色下的管理员状态
            by::adminUserModel()->modifyStatus($id,$status);
            return [true, $str.'成功'];
        }

        return [true, $str.'失败'];
    }
}