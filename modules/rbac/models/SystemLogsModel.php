<?php


namespace app\modules\rbac\models;

use app\models\by;
use app\models\CUtil;
use yii\db\ActiveRecord;
use yii\db\Exception;

class SystemLogsModel extends ActiveRecord
{

    public static function getDb()
    {
        return by::dbMaster();
    }

    /**
     * @return string
     * 基本表
     */
    private static function __baseTb(): string
    {
        return  "db_dreame_admin.t_sys_log";
    }

    /**
     * @param null $time
     * @throws \yii\db\Exception
     * 建表sql 提前创建今年，明年的表
     */
    public function CreateDbTb($time=null) {

        $time    = is_null($time) ? time() : CUtil::uint($time);
        $base_tb = self::__baseTb();

        //提前创建今年，明年的表
        for($i=0; $i<=1; $i++) {
            $now     = strtotime("+{$i} year",intval($time));
            $year    = date("Y",intval($now));
            $tb      = "{$base_tb}_{$year}";
            $tb_sql  = "CREATE TABLE IF NOT EXISTS {$tb} LIKE {$base_tb}";

            by::dbMaster()->createCommand($tb_sql)->execute();
        }
    }

    /**
     * @param $time
     * @return string
     */
    public static function tbName($time): string
    {
        $base_tb = self::__baseTb();
        $time    = is_null($time) ? time() : CUtil::uint($time);
        $year    = date("Y", intval($time));

        //防止出现超出时间范围的查询
        $year = max($year, by::Omain()::DB_TIMEZONE['ST']);
        $year = min($year, by::Omain()::DB_TIMEZONE['ED']);

        return "{$base_tb}_{$year}";
    }

    /**
     * @param $page
     * @param $page_size
     * @param $data
     * @return array|ActiveRecord[]
     * 系统日志
     */
    public function logs($page, $page_size, $data): array
    {

        $admin_id   = $data['admin_id'] ?? '';
        $start      = $data['start_time'] ?? 0;
        $end        = $data['end_time'] ?? 0;
        $module_id  = $data['module_id'] ?? 0;
        $time       = empty($data['year']) ? time() : strtotime("{$data['year']}0101");
        $limit      = CUtil::pagination($page, $page_size);
        $tb         = self::tbName($time);
        $table_b    = UserModel::tableName();
        $where = "1=1";
        if (!empty($admin_id)) {
            $where .= " AND A.user_id = {$admin_id}";
        }
        if (!empty($start) && !empty($end)) {
            $where .= " AND A.time between {$start} and {$end}";
        }
        if (!empty($module_id)) {
            $where .= " AND A.module_id = {$module_id}";
        }
        $sql   = "SELECT A.id,A.user_id,A.ip,A.text,A.module_id,A.time,B.account FROM {$tb} A LEFT JOIN {$table_b} B ON A.user_id = B.id WHERE {$where} ORDER BY A.id DESC LIMIT {$limit[0]},{$limit[1]}";
        $list  = by::dbMaster()->createCommand($sql)->queryAll();
        $total = by::dbMaster()->createCommand("SELECT count(*) FROM {$tb} A WHERE {$where}")->queryScalar();

        foreach ($list as &$value) {
            $value['module_name'] = RbacInfoModel::MODULE_LIST[$value['module_id']] ?? '未知模块';
            $value['time']        = date('Y-m-d H:i:s', $value['time']);
        }
        $pages = CUtil::getPaginationPages($total, $page_size);
        return ['total' => intval($total), 'pages' => $pages, 'list' => $list];

    }

    /**
     * @param string $text
     * @param int $module_id
     * @param int $user_id
     * @return array
     * @throws \yii\db\Exception
     * 记录日志
     */
    public function record($text='', $module_id=0, $user_id=0): array
    {
        $db      = by::dbMaster();
        $user_id = $user_id ?: by::adminUserModel()->getUserIDFromSession();
        $user_id = CUtil::uint($user_id);
        $time    = intval(START_TIME);
        $tb      = self::tbName($time);
        $ip      = \Yii::$app->request->getUserIP();

        $db->createCommand()->insert($tb,
            ['user_id'=>$user_id,'module_id'=>$module_id,'time'=>$time,'ip'=>$ip,'text'=>$text]
        )->execute();

        return [true, '保存成功'];
    }

    /**
     * @param $admin_id
     * @param $start_time
     * @param $end_time
     * @param $module_id
     * @param $year
     * 导出日志
     */
    public function export($admin_id, $start_time, $end_time, $module_id, $year)
    {
        $head   = ['用户名','模块名称', '操作内容', 'IP', '操作时间'];
        $f_name = '日志列表' . date('Ymd') . mt_rand(1000, 9999);
        $time   = empty($year) ? time() : strtotime("{$year}0101");
        $tb     = self::tbName($time);

        $where           = "1=:init";
        $params[':init'] = 1;

        if (!empty($admin_id)) {
            $where             .= " AND logs.user_id = :user_id";
            $params[':user_id'] = $admin_id;
        }

        if (!empty($start_time) && !empty($end_time)) {
            $where                .= " AND logs.time between :start_time and :end_time";
            $params[":start_time"] = $start_time;
            $params[":end_time"]   = $end_time;
        }

        if (!empty($module_id)) {
            $where              .= " AND logs.module_id = :module_id";
            $params[":module_id"] = $module_id;
        }

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params) {

            $id         = 0;
            $user_table = UserModel::tableName();
            $sql        = "SELECT logs.id,logs.ip,logs.text,logs.module_id,logs.time,user.account FROM {$tb} logs
                            LEFT JOIN {$user_table} user ON logs.user_id = user.id WHERE logs.id > :id AND {$where} ORDER BY logs.id LIMIT 200";

            while (true) {
                $params[':id'] = $id;
                $list         = by::dbMaster()->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val) {
                    $data[] = [
                        'account'     => $val['account'],
                        'module_name' => RbacInfoModel::MODULE_LIST[$val['module_id']] ?? '未知模块',
                        'text'        => str_replace(',', ';', $val['text']),
                        'ip'          => $val['ip'],
                        'time'        => date('Y-m-d H:i:s', $val['time'])
                    ];
                }

                yield $data;
            }

        }, $f_name);
    }

    public function exportData($admin_id, $start_time, $end_time, $module_id, $year)
    {
        $head   = ['用户名','模块名称', '操作内容', 'IP', '操作时间'];
        $time   = empty($year) ? time() : strtotime("{$year}0101");
        $tb     = self::tbName($time);

        $where           = "1=:init";
        $params[':init'] = 1;

        if (!empty($admin_id)) {
            $where             .= " AND logs.user_id = :user_id";
            $params[':user_id'] = $admin_id;
        }

        if (!empty($start_time) && !empty($end_time)) {
            $where                .= " AND logs.time between :start_time and :end_time";
            $params[":start_time"] = $start_time;
            $params[":end_time"]   = $end_time;
        }

        if (!empty($module_id)) {
            $where              .= " AND logs.module_id = :module_id";
            $params[":module_id"] = $module_id;
        }

        //导出
        $id         = 0;
        $user_table = UserModel::tableName();
        $sql        = "SELECT logs.id,logs.ip,logs.text,logs.module_id,logs.time,user.account FROM {$tb} logs
                        LEFT JOIN {$user_table} user ON logs.user_id = user.id WHERE logs.id > :id AND {$where} ORDER BY logs.id LIMIT 200";

        $data[] = $head;
        while (true) {
            $params[':id'] = $id;
            $list         = by::dbMaster()->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val) {
                $data[] = [
                    'account'     => '\''.$val['account'],
                    'module_name' => RbacInfoModel::MODULE_LIST[$val['module_id']] ?? '未知模块',
                    'text'        => str_replace(',', ';', $val['text']),
                    'ip'          => $val['ip'],
                    'time'        => date('Y-m-d H:i:s', $val['time'])
                ];
            }
        }
        return $data;
    }
}