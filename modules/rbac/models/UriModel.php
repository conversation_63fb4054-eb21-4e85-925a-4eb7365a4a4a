<?php


namespace app\modules\rbac\models;

use app\components\AdminRedisKeys;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\ActiveRecord;


class UriModel extends ActiveRecord
{
    protected $expire_time = 3600;

    public $countdown      = 60000;//菜单刷新倒计时毫秒

    public static function getDb()
    {
        return by::dbMaster();
    }

    public function rules()
    {
        return [
                [['p_id', 'uri', 'uri_name', 'is_del', 'is_check', 'is_menu', 'is_hidden', 'rank', 'name', 'ext_uri', 'time'], 'safe'],
            // 其他规则...
        ];
    }

    public static function tableName()
    {
        return "`db_dreame_admin`.`t_uri`";
    }

    /**
     * @param $uri
     * @param int $is_del
     * @return array|null|ActiveRecord
     * 返回当前uri权限信息
     * true 需要
     * false 不需要
     */
    public function uriInfo($uri, $is_del = 0)
    {
        $uriInfo = self::find()->select(['id', 'is_check'])
            ->where('uri=:uri AND is_del=:is_del')
            ->addParams([':uri' => $uri, ':is_del' => $is_del])
            ->one();
        return $uriInfo;
    }


    /**
     * @param int $r_id
     * @param array $access_ids
     * @return array
     * 获取当前模块菜单列表
     * @throws RedisException
     */
    public function menuList(int $r_id, array $access_ids): array
    {
        $key   = AdminRedisKeys::roleMenuList();
        $redis = by::redis();
        $aJson = $redis->hGet($key, $r_id);

        $cache_data = json_decode($aJson, true);

        if (!is_array($cache_data) || empty($cache_data)) {
            $access_ids = array_filter($access_ids);

            $userModel = by::adminUserModel();
            $is_admin  = $userModel->isAdministrator($r_id);

            // 基础查询构建，提前加好排序
            $query = self::find()
                    ->select(['*'])
                    ->where(['is_del' => 0])
                    ->orderBy(['rank' => SORT_DESC, 'id' => SORT_ASC]);

            if (!$is_admin && !empty($access_ids)) {
                $query->andWhere(['id' => $access_ids]);
            }

            $count = (int) $query->count();
            $page_size = 100;
            $pages = CUtil::getPaginationPages($count, $page_size);

            $cache_data = [];

            for ($page = 1; $page <= $pages; $page++) {
                list($offset) = CUtil::pagination($page, $page_size);

                $pageQuery = clone $query;
                $list = $pageQuery
                        ->asArray()
                        ->offset($offset)
                        ->limit($page_size)
                        ->all();

                if (!empty($list)) {
                    $menu = $this->fetchWithParents($list); // 注意这里调用新优化的方法
                    array_push($cache_data, ...$menu);
                }
            }

            if (!empty($cache_data)) {
                $redis->hSet($key, $r_id, json_encode($cache_data, JSON_UNESCAPED_UNICODE));
                $redis->expire($key, $this->expire_time);
            }
        }

        return $cache_data;
    }

    /**
     * 向上递归查询所有父菜单（一次性查询完成）
     * @param array $list 子菜单数组
     * @return array 完整菜单数组
     */
    private function fetchWithParents(array $list): array
    {
        if (empty($list)) {
            return [];
        }

        $result = $list;
        $all_ids = array_column($list, 'id');
        $parent_ids = array_column($list, 'p_id');
        $need_ids = array_diff(array_unique($parent_ids), [0]);

        while (!empty($need_ids)) {
            $parents = self::find()
                    ->select(['*'])
                    ->where(['id' => $need_ids, 'is_del' => 0])
                    ->orderBy(['rank' => SORT_DESC, 'id' => SORT_ASC])
                    ->asArray()
                    ->all();

            if (empty($parents)) {
                break;
            }

            foreach ($parents as $parent) {
                if (!in_array($parent['id'], $all_ids)) {
                    $result[] = $parent;
                    $all_ids[] = $parent['id'];
                }
            }

            $parent_ids = array_column($parents, 'p_id');
            $need_ids = array_diff(array_unique($parent_ids), [0]);
        }

        return $result;
    }



    /**
     * 修改或新增路由记录（菜单/权限节点）
     *
     * 根据传入的 ID 判断是更新已有记录，还是新增一条记录。
     * 同时会进行必要的参数校验和唯一性检查，并在成功后清除相关权限缓存。
     *
     * @param int $id   路由记录的主键ID，若为0或空则表示新增
     * @param array $data 路由信息数据，包括 uri、uri_name、p_id、name 等字段
     *
     * @return array [bool 成功标志, string 提示信息]
     *
     * @throws \yii\db\StaleObjectException 当更新失败时抛出
     */

    public function modify(int $id, array $data): array
    {
        $uri      = trim($data['uri'] ?? '', '/');
        $p_id     = (int) ($data['p_id'] ?? 0);
        $uri_name = $data['uri_name'] ?? '';
        $name     = $data['name'] ?? '';
        $ext_uri  = $data['ext_uri'] ?? '';
        $rank     = min(CUtil::uint($data['rank'] ?? 0), 255);

        $is_del    = (int) ($data['is_del'] ?? 0);
        $is_check  = (int) ($data['is_check'] ?? 0);
        $is_menu   = (int) ($data['is_menu'] ?? 0);
        $is_hidden = (int) ($data['is_hidden'] ?? 0);

        // ========== 参数校验 ==========
        if ($p_id !== 0 && empty($uri)) {
            return [false, 'uri不能为空'];
        }
        if (empty($uri_name)) {
            return [false, '路由名称不能为空'];
        }
        if (empty($name)) {
            return [false, '组件名称不能为空'];
        }

        // ========== 唯一性校验 ==========
        if ($tmp = self::find()->where(['uri_name' => $uri_name])->one()) {
            if ($tmp['id'] != $id) {
                return [false, '路由名称已存在'];
            }
        }

        if ($p_id !== 0 && $tmp = self::find()->where(['uri' => $uri])->one()) {
            if ($tmp['id'] != $id) {
                return [false, 'uri已存在'];
            }
        }

        // ========== 数据持久化 ==========
        $time = time();

        if ($id) {
            $record = self::findOne($id);
            if (!$record) {
                return [false, '记录不存在'];
            }
        } else {
            $record = new self();
        }

        // 批量赋值
        $record->setAttributes([
                'p_id'      => $p_id,
                'uri'       => $uri,
                'uri_name'  => $uri_name,
                'is_del'    => $is_del,
                'is_check'  => $is_check,
                'is_menu'   => $is_menu,
                'is_hidden' => $is_hidden,
                'rank'      => $rank,
                'name'      => $name,
                'ext_uri'   => $ext_uri,
                'time'      => $time,
        ]);

        $id ? $record->update() : $record->save();

        // ========== 清除权限相关 Redis 缓存 ==========
        $redis = by::redis();
        $redis->del(
                AdminRedisKeys::roleMenuList(),
                AdminRedisKeys::roleInfo(),
                AdminRedisKeys::menuTreeList()
        );

        $roleAccessKeys = $redis->keys(AdminRedisKeys::rolesAccess('*'));
        if (!empty($roleAccessKeys)) {
            $redis->del($roleAccessKeys);
        }

        return [true, "操作成功"];
    }


    /**
     * @param null $page
     * @param null $page_size
     * @param null $search
     * @return array
     * 获取所有路由
     */
    public function allMenus($page, $page_size, $search)
    {
        $limit = CUtil::pagination($page, $page_size);
        $command = self::find();
        $command->from(self::tableName() . " A")->select([
            'A.*', 'FROM_UNIXTIME(`A`.`time`, "%Y-%m-%d %H:%i:%S") `time`',
        ]);
        if (!empty($search)&&is_numeric($search)) {
            $command->andWhere(['or',
                ['and', ['=','p_id',$search]],
                ['and', ['=','id',$search]]
            ]);
        }else if(!empty($search)){
            $command->andWhere(['or',
                ['and',['like', 'name', $search]],
                ['and',['like', 'uri_name', $search]],
                ['and',['like', 'uri', $search]]
            ]);
        }
        $list = $command->asArray()->orderBy('`A`.`p_id` ASC,`A`.`rank` DESC,`A`.`id` DESC')->offset($limit[0])->limit($limit[1])->all();
        $pids = array_unique(array_column($list, 'p_id'));
        $pids = array_filter($pids);
        $pidMap = self::find()->from(self::tableName())->where(['id' => $pids])->select(['id', 'uri_name'])->indexBy('id')->asArray()->all();
        foreach ($list as $k => $v) {
            if (empty($v['p_id'])) {
                $list[$k]['parent_name'] = '-';
            } else {
                $list[$k]['parent_name'] = $pidMap[$v['p_id']]['uri_name'] ?? '';
            }
        }
        $total = $command->count();
        $pages = CUtil::getPaginationPages($total, $page_size);
        return ['total' => intval($total), 'pages' => $pages, 'list' => $list];
    }

    /**
     * 将平铺数组构建为树形结构
     * @param array $data 原始平铺数据
     * @param string $id 节点ID字段名
     * @param string $pid 父节点字段名
     * @param string $son 子节点字段名
     * @return array 树形结构数组
     */
    function getParentList(array $data = [], string $id = 'id', string $pid = 'p_id', string $son = 'son'): array
    {
        $tree = [];
        $refs = [];

        // 先将数据索引化
        foreach ($data as $item) {
            $refs[$item[$id]] = $item;
        }

        // 遍历并组装树结构
        foreach ($refs as $key => &$item) {
            $parentId = $item[$pid] ?? 0;

            if ($parentId && isset($refs[$parentId])) {
                $refs[$parentId][$son][] = &$item;
            } else {
                $tree[] = &$item;
            }
        }
        unset($item); // 解除最后一个引用，防止后续意外引用污染

        return $tree;
    }


    /**
     * @param $id
     * @return array|int
     * TODO 获取路由详情
     */
    public function getDetail($id)
    {
        $command = self::find();
        $command->from(self::tableName() . " A")->select([
            'A.*', 'FROM_UNIXTIME(`A`.`time`, "%Y-%m-%d %H:%i:%S") `time`',
        ]);
        $command->where(['A.id' => $id]);
        return $command->asArray()->one();
    }

    /**
     * 获取菜单下拉列表
     * @param array $params
     * @return array 返回菜单列表
     */
    public function getMenuSelectList(array $params): array
    {
        $key = AdminRedisKeys::menuTreeList();
        $redis = by::redis();

        $cacheData = $redis->get($key);
        $cacheData = json_decode($cacheData, true);
        if (empty($cacheData)) {
            $query = self::find();
            $query->select(['id', 'name', 'p_id', 'uri', 'uri_name', 'is_menu', 'is_check', 'is_hidden'])->where(['is_menu' => 1, 'is_del' => 0]);
            $res = $query->asArray()->all();
            $pids = array_unique(array_column($res, 'p_id'));
            $pids = array_filter($pids);
            $pidMap = self::find()->from(self::tableName())->where(['id' => $pids])->select(['id', 'uri_name'])->indexBy('id')->asArray()->all();
            foreach ($res as $k => $v) {
                if (empty($v['p_id'])) {
                    $res[$k]['parent_name'] = '-';
                } else {
                    $res[$k]['parent_name'] = $pidMap[$v['p_id']]['uri_name'] ?? '';
                }
            }
            $treeData = CUtil::formatTree($res);

            $redis->set($key, json_encode($treeData, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), ['EX' => empty($treeData) ? 10 : 3600]);
            $cacheData = $treeData;
        }

        return $cacheData;
    }
}