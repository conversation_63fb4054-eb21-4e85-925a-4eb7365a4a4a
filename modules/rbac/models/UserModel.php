<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/6/19
 * Time: 14:17
 */

namespace app\modules\rbac\models;

use app\components\AdminRedisKeys;
use app\components\AliYunSms;
use app\components\JwtTools;
use app\models\CUtil;
use RedisException;
use spec\Prophecy\Argument\Token\IdenticalValueTokenSpec;
use yii\base\Exception;
use yii\db\ActiveRecord;
use app\models\by;

class UserModel extends ActiveRecord
{

    public $expire_time = 7200;
    public $_account;
    public $_pwd;
    public $_phone;
    public $verifyCode;
    public $_user = false;

    const WX_CODE_EXPIRE = 180;

    public static function getDb()
    {
        return by::dbMaster();
    }

    public static function tableName()
    {
        return "`db_dreame_admin`.`t_user`";
    }

    /**
     * @param $r_id
     * @return bool
     * 验证用户是否是超级管理员
     */
    public function isAdministrator($r_id)
    {
        return intval($r_id) === RolesModel::ADMIN_RID;
    }


    /**
     * @param $pwd
     * @param $salt
     * @return string
     * 计算密码
     */
    public function password($pwd, $salt)
    {
        return md5($pwd . $salt);
    }

    /**
     * @return array|bool
     * TODO 发送验证码
     */
    public function sendCodeToWx()
    {
        $redis = by::redis();
        $key   = AdminRedisKeys::sendCodeToWx($this->_account);
        $info  = $redis->get($key);
        if (!empty($info)) {
            return [FALSE, '请勿频繁操作'];
        }
        $info['time'] = time();
        $info['code'] = YII_ENV_PROD ? CUtil::createVerifyCode(4) : 123456;
        //验证码有效期3分钟
        $redis->set($key, json_encode($info), self::WX_CODE_EXPIRE);

        //发送验证码至飞书
        $aData = [
            'account' => $this->_account,
            'code' => $info['code'],
            'exp' => self::WX_CODE_EXPIRE,
        ];

        $app_name = \Yii::$app->name;

        $notice = [
            "{$aData['account']} 您正在进行【{$app_name}】管理后台登录\n",
            "验证码为：{$aData['code']}\n",
            "客户端IP：" . CUtil::get_client_ip() . "\n",
            "有效期：{$aData['exp']}s，请勿泄露给第三方！！！\n",
            "环境：".YII_ENV."\n",
        ];

        //飞书通知
        $fsUrl = CUtil::getConfig('loginNotice', 'spread', MAIN_MODULE);
        $body  = ['msg_type' => 'text', 'content' => ['text' => is_array($notice) ? implode(' ', $notice) : $notice,]];
        !YII_ENV_DEV && CUtil::curl_post($fsUrl, json_encode($body, 320), ["Content-Type:application/json"]);

        AliYunSms::sendSms($this->_phone,AliYunSms::TEMPLATE_CODE['admin'],['code'=>$info['code']]);
        return [TRUE, '发送失败'];
    }


    /**
     * 密码校验
     */
    public function validatePassword(): array
    {
        $user = $this->getUser();
        if (empty($user)) {
            return [FALSE, '账号不存在'];
        }
        if ($user['status'] == 1) {
            return [FALSE, '账号已被停用，请联系管理员'];
        }
        $pwd = $this->password($this->_pwd, $user['salt']);
        if ($pwd != $user['pwd']) {
            return [FALSE, '密码错误'];
        }
        $this->_user = $user;
        return [TRUE, null];
    }

    /**
     * @return bool
     * 验证码校验
     */
    public function validateWxCode(): bool
    {
        $redis = by::redis();
        if (empty($this->verifyCode)) {
            $this->addError('verifyCode', '验证码不能为空');
            return false;
        }
        $key = AdminRedisKeys::sendCodeToWx($this->_account);
        $info = $redis->get($key);
        $info = json_decode($info, true);
        if (empty($info['code'])) {
            return false;
        }
        if ($this->verifyCode != $info['code']) {
            return false;
        }
        $redis->del($key);
        return true;
    }


    /**
     * @param $post
     * @return array
     * 登陆
     * @throws RedisException
     * @throws Exception
     */
    public function login($post): array
    {
        // 手机号登录处理
        if (!empty($post['phone'])) {
            $this->_phone = $post['phone'];
            $user         = self::findByPhone($post['phone']);
            if (empty($user)) {
                return ['status' => 0, 'msg' => '手机号不存在'];
            }
            $this->_account = $user['account'];
        } else {
            // 账号密码登录校验
            if (empty($post['account']) || empty($post['pwd'])) {
                $missingField = empty($post['account']) ? '用户名' : '密码';
                return ['status' => 0, 'msg' => "{$missingField}不能为空"];
            }

            $this->_account = $post['account'];
            $this->_pwd     = $post['pwd'];
            list($status, $msg) = $this->validatePassword($post['pwd']);
            if ($status == 0) {
                return ['status' => 0, 'msg' => $msg];
            }
        }

        $redis = by::redis();

        // 验证码发送处理
        if (!empty($post['is_send'])) {
            $codeKey    = AdminRedisKeys::sendCodeToWx($this->_account);
            $cachedInfo = json_decode($redis->get($codeKey), true) ?? [];

            if (!empty($cachedInfo['time'])) {
                $remainingTime = self::WX_CODE_EXPIRE - (time() - $cachedInfo['time']);
                return ['status' => 0, 'msg' => "验证码已发送，请于{$remainingTime}秒后操作"];
            }

            list($sendStatus, $sendMsg) = $this->sendCodeToWx();
            return $sendStatus
                    ? ['status' => 1, 'msg' => '验证码已发送至短信和企业微信，请注意查收']
                    : ['status' => 0, 'msg' => $sendMsg];
        }

        // 验证码校验
        if (empty($post['verify_code'])) {
            return ['status' => 0, 'msg' => '验证码不能为空'];
        }

        $this->verifyCode = $post['verify_code'];
        if(!$this->validateWxCode()){
            return ['status' => 0, 'msg' =>  '验证码错误'];
        }

        // 用户信息处理
        unset($this->_user['pwd']);
        $this->_user = $this->getUserForWeb($this->_account);

        // 删除上次登录的 token
        $sessionKey = AdminRedisKeys::sessionKey($this->_user['id']);
        if ($redis->exists($sessionKey)) {
            $redis->del($sessionKey);
        }

        // Token生成&有效时间
        $this->expire_time = $post['checked'] == 1 ? 604800 : $this->expire_time; // 7天
        $token             = JwtTools::factory()::encodeJsonWebToken($this->_user, $this->expire_time);

        // Redis存储
        $sessionData = [
                'sessid'  => $token,
                'checked' => $post['checked']
        ];
        $redis->hMSet($sessionKey, $sessionData);
        $redis->expire($sessionKey, $this->expire_time);

        $path = YII_ENV_TEST ? "/".strtolower(\Yii::$app->name)."/" : "/";
        //setcookie 过长会导致报错不set
//        setcookie('sessid',$token,time()+$this->expire_time,$path,null,null);

        // 返回菜单
        $menu_list = by::adminUriModel()->getParentList(
                by::adminUriModel()->menuList($this->_user['r_id'], $this->_user['access'])
        );
        // 返回数据结构
        unset($this->_user['access']);
        return ['status' => 2, 'data' => [
                'sessid'    => $token,
                'user'      => $this->_user,
                'menu_list' => $menu_list
        ]];
    }


    /**
     * @return array|bool|null|ActiveRecord
     * 全部信息
     */
    public function getUser()
    {
        if ($this->_user === false) {
            $this->_user = self::findByAccount(trim($this->_account));
        }

        return $this->_user;
    }

    /**
     * @param string $account
     * @return array|bool|null|ActiveRecord
     * 供页面展示使用用户信息
     */
    public function getUserForWeb($account = '')
    {
        $_user = self::findByAccount(trim($account));

        if (!empty($_user)) {
            $r_access = explode(',', $_user['r_access']);
            $ext_access = explode(',', $_user['ext_access']);

            $_user['access'] = array_merge($r_access, $ext_access);
            unset(
                $_user['r_access'],
                $_user['ext_access'],
                $_user['pwd'],
                $_user['salt']
            );
        }
        return $_user;
    }

    /**
     * @param $account
     * @return array|null|ActiveRecord
     * 获取账号详情
     */
    public static function findByAccount($account)
    {
        $role_table = RolesModel::tableName();
        $user = self::find()
            ->from(self::tableName() . ' A')
            ->select([
                'A.id', 'A.r_id', 'A.account', 'A.pwd',
                'A.salt', 'A.nick', 'A.avatar', 'A.time',
                'A.status', 'A.ext_access', 'A.p_id', 'B.r_access', 'B.r_name'
            ])->leftJoin("$role_table B", 'B.id = A.r_id')
            ->where(['A.account' => $account, 'A.status' => 0])
            ->asArray()
            ->one();

        return $user;
    }

    /**
     * @param $phone
     * @return array|null|ActiveRecord
     * 获取账号详情通过手机号
     */
    public static function findByPhone($phone)
    {
        $role_table = RolesModel::tableName();
        $user = self::find()
            ->from(self::tableName() . ' A')
            ->select([
                'A.id', 'A.r_id', 'A.account', 'A.pwd',
                'A.salt', 'A.nick', 'A.avatar', 'A.time',
                'A.status', 'A.ext_access', 'A.p_id', 'B.r_access', 'B.r_name'
            ])->leftJoin("$role_table B", 'B.id = A.r_id')
            ->where(['A.phone' => $phone, 'A.status' => 0])
            ->asArray()
            ->one();

        return $user;
    }

    /**
     * @param $id
     * @return array|null|ActiveRecord
     * 获取账号详情
     */
    public function getOneById($id)
    {
        $role_table = RolesModel::tableName();
        $user = self::find()
            ->from(self::tableName() . ' A')
            ->select([
                'A.id', 'A.r_id', 'A.account', 'A.pwd','A.phone',
                'A.salt', 'A.nick', 'A.avatar', 'A.time',
                'A.status', 'A.ext_access', 'B.r_access', 'B.r_name'
            ])->leftJoin("$role_table B", 'B.id = A.r_id')
            ->where('A.id=:id')
            ->addParams([':id' => $id])
            ->asArray()
            ->one();

        return $user;
    }

    /**
     * 获取用户列表
     * @param array $ids
     * @param array|string[] $columns
     * @return array|ActiveRecord[]
     */
    public function getListByIds(array $ids, array $columns = ['id']): array
    {
        if (empty($ids)) {
            return [];
        }
        $data = self::find()
            ->select($columns)
            ->where(['id' => $ids])
            ->asArray()
            ->all();
        return $data;
    }

    /**
     * @param $page
     * @param $page_size
     * @param string $account
     * @return array|ActiveRecord[]
     * 获取所有用户
     */
    public function getAllUsers($page, $page_size, $account = '')
    {
        $limit      = CUtil::pagination($page, $page_size);
        $role_table = RolesModel::tableName();
        $connection = self::find()
            ->from(self::tableName() . ' A')
            ->select([
                'A.id', 'A.r_id', 'A.account', 'A.phone',
                'A.salt', 'A.nick', 'A.avatar',
                'A.status', 'A.ext_access', 'B.r_access', 'B.r_name'
            ])->leftJoin("$role_table B", 'B.id = A.r_id')
            ->where(['A.status' =>0]);
        if ($account) {
            $connection->where(['like', 'A.account', $account]);
        }

        $user = $connection->asArray()->offset($limit[0])->orderBy('A.id DESC')->limit($limit[1])->all();
        $host = \Yii::$app->request->hostInfo;
        foreach ($user as &$val) {
            !empty($val['avatar']) && $val['avatar'] = $host . $val['avatar'];
        }
        return $user;
    }


    public function GetUsersList($input,$page,$page_size)
    {
        $limit      = CUtil::pagination($page, $page_size);
        $role_table = RolesModel::tableName();
        $connection = self::find()
            ->from(self::tableName() . ' A')
            ->select([
                'A.id', 'A.r_id', 'A.account', 'A.phone',
                'A.salt', 'A.nick', 'A.avatar',
                'A.status', 'A.ext_access', 'B.r_access', 'B.r_name'
            ])->leftJoin("$role_table B", 'B.id = A.r_id')
            ->where(['A.status' =>0]);
        if (!empty($input['account'])) {
            $connection->where(['like', 'A.account', $input['account']]);
        }

        if(!empty($input['phone'])){
            $connection->where(['A.phone' => $input['phone']]);
        }

        if(!empty($input['r_id'])){
            $connection->where(['A.r_id' => $input['r_id']]);
        }

        if(!empty($input['nick'])){
            $connection->where(['like', 'A.nick', $input['nick']]);
        }

        if(!empty($input['ids'])){ // $ids 是数组
            if(!is_array($input['ids'])){
                $input['ids'] = explode(',', $input['ids']);
            }
            $connection->where(['A.id' => $input['ids']]);
        }

        $user = $connection->asArray()->offset($limit[0])->orderBy('A.id DESC')->limit($limit[1])->all();
        return $user;
    }


    /**
     * @param $account
     * @return int
     * 总用户数
     */
    public function getAllUsersCount($account)
    {
        $role_table = RolesModel::tableName();
        $connection = self::find()
            ->from(self::tableName() . ' A')
            ->leftJoin("{$role_table} B", 'B.id = A.r_id')
            ->where(['A.status' =>0]);
        if ($account) {
            $connection->where(['like', 'A.account', $account]);
        }
        $count = $connection->asArray()->count('*');
        return intval($count);
    }

    /**
     * @param $id
     * @param $nick
     * @param $ext_access
     * @param $r_id
     * @param $pwd
     * @param $status
     * @param $account
     * @param $phone
     * @param $user_is_admin
     * @return array
     * @throws \yii\db\Exception
     * @throws \yii\db\StaleObjectException
     * 修改
     */
    public function modify($id, $nick, $ext_access, $r_id, $pwd, $status, $account,$phone='', $user_is_admin=false)
    {
        if (empty($nick)) {
            return [false, '昵称不能为空'];
        }
        if (empty($account)) {
            return [false, '用户名不能为空'];
        }
        if (empty($r_id)) {
            return [false, '角色不能为空'];
        }

        //判断角色组是否存在
        $role_data = by::adminRolesModel()->getInfo($r_id);
        if (empty($role_data)) {
            return [false, '角色不存在'];
        }

        //判断用户是否存在
        $account_data = self::find()->where(['account' => $account, 'status' => 0])->one();
        if (!empty($account_data) && ($account_data['id'] != $id || $id == '')) {
            return [false, '用户名已存在'];
        }

        //判断手机是否存在
        if($phone){
            $phone_data = self::find()->where(['phone' => $phone, 'status' => 0])->one();
            if (!empty($phone_data) && ($phone_data['id'] != $id || $id == '')) {
                return [false, '手机号已存在'];
            }
        }

        $salt = uniqid();
        if ($id) {
            $record = self::findOne($id);
            if (empty($record)) {
                return [false, '账号不存在'];
            }

            if ($pwd) {
                $pwd          = $this->password($pwd, $salt);
                $record->pwd  = $pwd;
                $record->salt = $salt;
            }

            if ($phone){
                $record->phone = $phone;
            }

            //判断是否是超级管理员
            if ($record['r_id'] == 1) {
                if (!$user_is_admin) {
                    return [false, '非超级管理员不允许修改超级管理员信息'];
                }

                $ret = $record->update(false);
                if (!$ret) {
                    return [false, '无数据修改(1)'];
                }
                (new SystemLogsModel())->record("修改了用户信息ID:{$id}|pwd:{$pwd}|状态：" . $ret ? "成功" : "失败", RbacInfoModel::USER_MANAGER);
                return [true, '不允许修改超级管理员其他信息，只能修改密码和手机号'];
            }

            $record->nick = $nick;
            $record->r_id = $r_id;
            $record->time = time();

            ($status >= 0) && $record->status  = $status;

            if (!empty($ext_access)) {
                $record->ext_access = implode(',', $ext_access);
            }

            $ret = $record->update(false);
            if (!$ret) {
                return [false, '无数据修改(2)'];
            }

            (new SystemLogsModel())->record("修改了用户信息ID:{$id}|nick:{$nick}|状态：" . $ret ? "成功" : "失败", RbacInfoModel::USER_MANAGER);
        } else {
            if (empty($pwd)) {
                return [false, '密码不能为空'];
            }

            $pwd = $this->password($pwd, $salt);
            $this->status       = $status;
            $this->nick         = $nick;
            $this->account      = $account;
            $this->ext_access   = $ext_access;
            $this->pwd          = $pwd;
            $this->salt         = $salt;
            $this->r_id         = $r_id;
            $this->phone        = $phone;
            $this->time         = time();
            $ret = $this->save();
            if (!$ret) {
                return [false, '添加新用户失败'];
            }
            (new SystemLogsModel())->record("新增了用户:{$nick}", RbacInfoModel::USER_MANAGER);
        }

        return [true, "操作成功"];
    }

    /**
     * @param int $user_id
     * @return string
     * session key 标准
     */
    public static function sessionKey($user_id)
    {
        $redis_key = AdminRedisKeys::sessionKey($user_id);
        return $redis_key;
    }

    /**
     * @param $user_id
     * @return string
     * 获取用户session
     */
    public static function GetSessId($user_id)
    {
        $redis = by::redis('core');
        $session_key = self::sessionKey($user_id);
        return $redis->hget($session_key, 'sessid');
    }

    /**
     * @param $user_id
     * @param string $token
     * @return array
     * TODO 检测管理员是否登录
     * @throws RedisException
     */
    public function checkLogin($user_id, string $token = ''): array
    {
        $redis    = by::redis('core');
        $sess_key = self::sessionKey($user_id);

        // 获取当前用户保存的 sessid 并校验
        $r_sessid = $redis->hGet($sess_key, 'sessid');

        if ($r_sessid != $token) {
            return [false, 0];
        }

        // 检查是否为“记住我”状态
        if ($this->getChecked($user_id) == 1) {
            $this->expire_time = 7 * 86400;
        }

        // 给缓存续期
        $redis->expire($sess_key, $this->expire_time);

        // 解析 JWT Token
        list($status, $jwtDecodeSub) = JwtTools::factory()::decodeJsonWebToken($token);

        return [true, $jwtDecodeSub ?? []];
    }

    /**
     * @param $user_id
     * @return string
     * 获取用户七天免登陆状态
     */
    public function getChecked($user_id)
    {
        $redis = by::redis('core');
        $session_key = self::sessionKey($user_id);
        return $redis->hget($session_key, 'checked');
    }

    /**
     * @param $user_id
     * @param $token
     * @return bool
     * @throws RedisException
     */
    public function delUserInfoToSession($user_id, $token): bool
    {
        // 移除cookie
        $path = YII_ENV_TEST ? "/".strtolower(\Yii::$app->name)."/" : "/";
        // 设置过期时间为过去的时间（例如当前时间减去1小时）
        setcookie('sessid', '', time() - 3600, $path, null, null, true);

        $redis       = by::redis('core');
        $session_key = AdminRedisKeys::sessionKey($user_id);

        // 删除 Redis 中的会话信息
        $redis->del($session_key);

        // 将当前 Token 加入黑名单
        $jwtTools = JwtTools::factory();
        return $jwtTools::addTokenToBlacklist($user_id, $token);
    }


    /**
     * 获取登录用户的信息
     */
    public static function getUserInfo(): array
    {
        $jwtToken = \Yii::$app->request->post('sessid');
        if (empty($jwtToken)) {
            return [];
        }
        list($status, $decodedPayload) = JwtTools::factory()->decodeJsonWebToken($jwtToken);
        return $status ? $decodedPayload : [];
    }

    /**
     * @return int
     * 从session 获取用户ID
     */
    public function getUserIDFromSession(): int
    {
        $user = $this->getUserInfo();
        $id   = $user['id'] ?? 0;
        return CUtil::uint($id);
    }

    /**
     * @param  array $user
     * @param  string $path : 当前的请求uri
     * @return bool
     * 判断登录用户的访问权限
     */
    public function checkAuth($user=[], $path=''): bool
    {
        $r_id = $user['r_id'] ?? 0;
        if(empty($r_id)) {
            return false;
        }

        //超级管理员有全部权限
        $bool = by::adminUserModel()->isAdministrator($user['r_id']);
        if ($bool) {
            return true;
        }

        //检测路由
        $key      = AdminRedisKeys::rolesAccess($user['r_id']);
        $redis    = by::redis('core');
        $ttl      = $redis->ttl($key);
        if($ttl < 0){
            by::adminRolesModel()->rolesAccess($user['r_id']);
        }
        $bool     = $redis->hGet($key, $path);
        if (!$bool) {
            return false;
        }

        return true;
    }


    /**
     * TODO 获取所有的管理员
     */
    public function getUserList()
    {
        $data = self::find()->select('id, account')->where(['status' => 0])->asArray()->all();
        return $data;
    }

    /**
     * @param $id
     * @return array
     * @throws \yii\db\StaleObjectException
     * 删除管理员
     */
    public function deleteData($id)
    {
        $admin_info = self::findOne($id);
        if (empty($admin_info) || $admin_info->status == 1) {
            return [false, '管理员不存在'];
        }

        /*if ($this->isAdministrator($admin_info['r_id'])) {
            return [false, '超级管理员不允许删除'];
        }*/

        $admin_info->status = 1;
        $admin_info->time = time();
        $admin_info->update();

        //清除登录
        $session_key = AdminRedisKeys::sessionKey($id);
        $token       = self::GetSessId($id);
        if (!empty($token)) {
            // 加入黑名单
            JwtTools::factory()::addTokenToBlacklist($id, $token);
        }

        by::redis()->del($session_key);

        return [true, '操作成功'];
    }

    /**
     * @param $r_id
     * @param $status
     * @return array
     * @throws \yii\db\Exception
     * 更新管理员状态
     */
    public function modifyStatus($r_id,$status)
    {
        $tb  = self::tableName();
        $sql = "select `id` from {$tb} where `r_id` =:rid";
        $ids = by::dbMaster()->createCommand($sql,[':rid' => $r_id])->queryColumn();
        foreach ($ids as $id) {
            by::dbMaster()->createCommand()->update($tb,['status'=>$status,'time'=>START_TIME],['id'=>$id])->execute();

            //禁用清除登录
            if($status == 1){
                $session_key = AdminRedisKeys::sessionKey($id);
                $token       = self::GetSessId($id);
                if (!empty($token)) {
                    // 加入黑名单
                    JwtTools::factory()::addTokenToBlacklist($id, $token);
                }
                by::redis()->del($session_key);
            }
        }

        return [true, '操作成功'];
    }

    /**
     * TODO  修改密码
     */
    public function updatePwd($old_pwd,$new_pwd,$new_two_pwd,$user_id)
    {
        if(empty($old_pwd) || empty($new_pwd) || empty($new_two_pwd)){
            return [false, '参数不能为空'];
        }
        $admin_info = self::findOne($user_id);
        if (empty($admin_info)) {
            return [false, '管理员不存在'];
        }
        if($admin_info->pwd != $this->password($old_pwd,$admin_info->salt)){
            return [false, '旧密码输入错误'];
        }
        $new_pwd     = trim($new_pwd);
        $new_two_pwd = trim($new_two_pwd);
        if($new_pwd != $new_two_pwd){
            return [false, '两次输入的新密码不一致'];
        }
        $re = preg_match('/^[a-zA-Z0-9@\.]{8,16}$/i',$new_pwd);
        if(!$re){
            return [false, '新密码设置错误，仅限输入8到16位字母或数字或@或点'];
        }
        $zm   = preg_match('/[a-z]/',$new_pwd);
        $dzm  = preg_match('/[A-Z]/',$new_pwd);
        $sz   = preg_match('/[0-9]/',$new_pwd);
        $fh   = preg_match('/[@\.]/',$new_pwd);
        $zong = $zm + $dzm + $sz + $fh;
        if($zong < 2){
            return [false, '新密码至少包含以下两种字符：大写字母、小写字母、数字、符号'];
        }

        $pwd = $this->password($new_pwd, $admin_info->salt);
        $admin_info->pwd = $pwd;
        $admin_info->time = time();
        $admin_info->update();
        //清除登录
        $session_key = AdminRedisKeys::sessionKey($user_id);
        $token = by::redis()->get($session_key);
        if ($token !== false) {
            // 加入黑名单
            JwtTools::factory()::addTokenToBlacklist($user_id, $token);
        }
        by::redis()->del($session_key);
        return [true, '修改密码成功'];
    }
}
