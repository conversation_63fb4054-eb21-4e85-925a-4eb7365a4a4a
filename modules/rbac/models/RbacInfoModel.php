<?php


namespace app\modules\rbac\models;


class RbacInfoModel
{
    const  LOGIN_MANAGER       = 1;
    const  USER_MANAGER        = 2;
    const  URI_MANAGER         = 3;
    const  ROLE_MANAGER        = 4;
    const  BANNER_MANAGER      = 5;
    const  MARKET_MANAGER      = 6;
    const  SIGN_MANAGER        = 7;
    const  ACTIVITY_MANAGER    = 8;
    const  GOODS_MANAGER       = 9;
    const  BOX_GOODS_MANAGER   = 91;
    const  ORDER_MANAGER       = 10;
    const  RETAILERS_MANAGER   = 20;
    const  FREIGHT_CFG         = 30;
    const  PRODUCT_MANAGER     = 40;
    const  WARRANTY_APPLY      = 41;
    const  WARRANTY_CARD       = 42;
    const  POINT_PUSH          = 43;
    const  MARKET_SEND         = 50;
    const  MARKET_DEFINE       = 51;
    const  SUBSCRIBE_MANAGER   = 60;
    const  GOODS_INI_PRICE     = 92;
    const  GOODS_CATE          = 101;
    const  SMART_ACCESSORIES   = 100;
    const  CONTENT             = 102;
    const  CONTENT_ROLE        = 103;
    const  WARES_GOODS_MANAGER = 104;
    const  TRY_BEFORE_BUY      = 105;
    const  PRODUCT_MATRIX      = 106;
    const  REPORT              = 107;
    const  PRIVACY_POLICY      = 108;


    const MODULE_LIST = [
            1   => '登录管理',
            2   => '用户管理',
            3   => '路由管理',
            4   => '角色管理',
            5   => 'Banner配置',
            6   => '营销配置',
            7   => '签到配置',
            8   => '活动配置',
            9   => '商品管理-普通商品',
            91  => '商品管理-抽盒机商品',
            10  => '订单管理',
            20  => '门店管理-添加门店配置',
            30  => '运费配置',
            40  => '产品管理',
            50  => '删除营销资源',
            51  => '营销资源自定义',
            60  => '预约管理',
            92  => '商品管理-价格配置',
            100 => '智能配件',
            101 => '商品类目',
            102 => '内容管理',
            103 => '内容角色管理',
            41  => '保修卡申请',
            42  => '保修卡',
            104 => '新商品管理',
            105 => '先用后付管理',
            43  => '积分/觅享分推送',
            106 => '全系产品站管理',
            107 => '举报',
            108 => '隐私管理',

    ];
}
