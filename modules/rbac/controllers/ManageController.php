<?php


namespace app\modules\rbac\controllers;

use app\components\AdminRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\RolesModel;
use app\modules\rbac\models\SystemLogsModel;
use OpenApi\Annotations as OA;
use Yii;


class ManageController extends CommController
{

    /**
     * TODO 后台路由列表
     */
    public function actionUriList()
    {
        $page      = Yii::$app->request->post('page', 1);
        $search    = Yii::$app->request->post('search', '');
        $page_size = 15;
        $data      = by::adminUriModel()->allMenus($page, $page_size,$search);
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     * TODO 获取路由详情
     */
    public function actionUriDetail()
    {
        $id = Yii::$app->request->post('id');
        if (empty($id)) {
            CUtil::json_response(-1, 'id不能为空');
        }
        $data = by::adminUriModel()->getDetail($id);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/rbac/manage/modify-uri",
     *     operationId="modifyUri",
     *     summary="新增或修改后台路由",
     *     description="用于新增或修改菜单/权限节点，支持菜单隐藏、校验权限、排序等配置。",
     *     tags={"菜单管理"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"uri", "uri_name", "name"},
     *                 @OA\Property(property="id", type="integer", description="ID（为空表示新增）", example=1),
     *                 @OA\Property(property="p_id", type="integer", description="父级菜单ID", example=0),
     *                 @OA\Property(property="uri", type="string", description="路由 URI", example="admin/user/list"),
     *                 @OA\Property(property="uri_name", type="string", description="路由名称", example="用户列表"),
     *                 @OA\Property(property="name", type="string", description="组件名称", example="UserList"),
     *                 @OA\Property(property="is_del", type="integer", description="是否删除：0-否，1-是", example=0),
     *                 @OA\Property(property="is_check", type="integer", description="是否权限校验：0-否，1-是", example=1),
     *                 @OA\Property(property="is_menu", type="integer", description="是否菜单：0-否，1-是", example=1),
     *                 @OA\Property(property="is_hidden", type="integer", description="是否隐藏菜单：0-否，1-是", example=0),
     *                 @OA\Property(property="rank", type="integer", description="排序（0-255）", example=10),
     *                 @OA\Property(property="ext_uri", type="string", description="额外URI匹配，用于兼容旧路由", example="/admin/user/info")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="返回结果",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="操作成功"),
     *             @OA\Property(property="data", type="object", description="返回数据，一般为空")
     *         )
     *     )
     * )
     */
    public function actionModifyUri()
    {
        $request = Yii::$app->request;
        $id      = (int) $request->post('id', 0);
        $p_id    = (int) $request->post('p_id', 0);

        $data = [
                'uri'       => trim($request->post('uri', '')),
                'uri_name'  => trim($request->post('uri_name', '')),
                'is_del'    => (int) $request->post('is_del', 0),
                'is_check'  => (int) $request->post('is_check', 0),
                'is_menu'   => (int) $request->post('is_menu', 0),
                'rank'      => (int) $request->post('rank', 0),
                'name'      => trim($request->post('name', '')),
                'ext_uri'   => trim($request->post('ext_uri', '')),
                'is_hidden' => (int) $request->post('is_hidden', 0),
                'p_id'      => ($p_id == $id) ? 0 : $p_id,
        ];

        $model = by::adminUriModel();
        list($success, $message) = $model->modify($id, $data);

        if (!$success) {
            CUtil::json_response(-1, $message);
        }

        // 记录系统操作日志
        $logMsg = $id
                ? "修改路由 [ID: {$id}]"
                : "新增路由 [URI: {$data['uri']}]";

        $logMsg .= ", 数据：" . json_encode($data, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($logMsg, RbacInfoModel::URI_MANAGER);

        CUtil::json_response(1, $message);
    }

    /**
     * TODO 后台角色列表
     */
    public function actionRoles()
    {
        $page      = Yii::$app->request->post('page', 1);
        $page_size = 15;
        $name      = Yii::$app->request->post('name', '');
        $module    = by::adminRolesModel();
        $list      = $module->RList($page, $page_size, $name);
        $count     = $module->RCount($name);
        $pages     = CUtil::getPaginationPages($count, $page_size);
        $aData = [
            'total' => $count,
            'pages' => $pages,
            'list' => $list,
        ];
        CUtil::json_response(1, 'ok', $aData);
    }

    /**
     * TODO 获取指定角色的详情
     */
    public function actionRolesDetail()
    {
        $id = Yii::$app->request->post('id');
        if (empty($id)) {
            CUtil::json_response(-1, '角色id不能为空');
        }
        $aData = by::adminRolesModel()->getDetail($id);
        CUtil::json_response(1, 'ok', $aData);
    }

    /**
     *  TODO  获取当前登录角色所有的权限 以及能添加的角色类型
     */
    public function actionRolesAuth()
    {
        $role_type = by::adminRolesModel()->RList(1, 100);
        $role_type = array_column($role_type, 'r_name', 'id');
        $data['role_type'] = $role_type;
        //有编辑权限的下发所有路由
        //todo 正式服暂时不动
        $id = !YII_ENV_PROD ? RolesModel::ADMIN_RID : $this->user_info['r_id'];
        $data['current_access'] = by::adminRolesModel()->getDetail($id);
        CUtil::json_response(1, ' 获取成功', $data);
    }


    /**
     * TODO 添加修改 角色
     */
    public function actionModifyRoles()
    {
        $name   = Yii::$app->request->post('name', '');
        $access = Yii::$app->request->post('access', '');
        $id     = Yii::$app->request->post('id', 0);
        if($id == 1){
            CUtil::json_response(-1,'超级管理员不能编辑');
        }
        list($st, $ret) = by::adminRolesModel()->modify($id, $name, $access, $this->user_info['r_id']);
        if(!$st) {
            CUtil::json_response(-1,$ret);
        }
        $msg  = $id ? "修改了角色{$name}权限:{$access}" : "新增了角色{$name}权限:{$access}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::ROLE_MANAGER);
        CUtil::json_response(1, $ret);
    }


    /**
     * TODO 获取管理员列表
     */
    public function actionUser()
    {
        $page       = Yii::$app->request->post('page', 1);
        $page_size  = 15;
        $account    = Yii::$app->request->post('account', '');
        $module     = by::adminUserModel();

        $list       = $module->getAllUsers($page, $page_size, $account);
        $count      = $module->getAllUsersCount($account);
        $pages      = CUtil::getPaginationPages($count, $page_size);
        $aData = [
            'total' => $count,
            'pages' => $pages,
            'list' => $list,
        ];
        CUtil::json_response(1, '获取成功', $aData);
    }

    /**
     * TODO 管理员详情
     */
    public function actionUserDetail()
    {
        $id = Yii::$app->request->post('id', null);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $data = by::adminUserModel()->getOneById($id);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * TODO 修改管理员
     */
    public function actionModifyUsers()
    {
        $id         = Yii::$app->request->post('id', 0);
        $nick       = Yii::$app->request->post('nick', 0);
        $ext_access = Yii::$app->request->post('ext_access', '');
        $account    = Yii::$app->request->post('account', '');
        $r_id       = Yii::$app->request->post('r_id', 0); // 角色ID
        $pwd        = Yii::$app->request->post('pwd', '');
        $phone      = Yii::$app->request->post('phone', '');
        $status     = Yii::$app->request->post('status', 0);//删除
        $model      = by::adminUserModel();

        //当为超级管理员
        $user_is_admin = $model->isAdministrator($this->user_info['r_id']);
        if (!$user_is_admin) {
            if($id && $pwd){
                CUtil::json_response(-1, '只有超级管理员才能修改密码');
            }
            if($r_id == 1){
                CUtil::json_response(-1, '只有超级管理员才能操作');
            }
        }

        list($st, $ret) = $model->modify($id, $nick, $ext_access, $r_id, $pwd, $status, $account,$phone, $user_is_admin);

        CUtil::json_response($st ? 1 : -1, $ret);
    }

    /**
     * TODO  管理员删除
     */
    public function actionUserDelete()
    {
        $id = Yii::$app->request->post('id');
        list($st, $ret) = by::adminUserModel()->deleteData($id);
        if(!$st) {
            CUtil::json_response(-1,$ret);
        }
        $msg  = "删除管理员{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::USER_MANAGER);
        CUtil::json_response(1, $ret);
    }


    /**
     * TODO 系统日志
     */
    public function actionLogs()
    {
        $page               = Yii::$app->request->post('page', 1);
        $data['admin_id']   = Yii::$app->request->post('admin_id');
        $data['start_time'] = Yii::$app->request->post('start_time');
        $data['end_time']   = Yii::$app->request->post('end_time');
        $data['module_id']  = Yii::$app->request->post('module_id');  //模块ID
        $data['year']       = Yii::$app->request->post('year', date('Y'));

        //无数据
        if($data['year'] < 2022 || $data['year'] > date('Y')) {
            CUtil::json_response(1, "OK", []);
        }

        $res = by::systemLogsModel()->logs($page, 15, $data);

        CUtil::json_response(1, '获取成功', $res);
    }

    /**
     * TODO 系统日志模块
     */
    public function actionLogsModule()
    {
        $data['module_list'] = RbacInfoModel::MODULE_LIST;
        $data['user_list'] = by::adminUserModel()->getUserList();
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     * 路由检测
     */
    public function actionAuthCheck()
    {
        $paths      = Yii::$app->request->post('paths');
        $paths      = explode('|', $paths);
        $key        = AdminRedisKeys::rolesAccess($this->user_info['r_id'] ?? 0);
        $auth       = by::redis('core')->hMGet($key, $paths);
        CUtil::json_response(1, 'ok', $auth);
    }

    /**
     * TODO  管理员修改自己账号的密码
     */
    public function actionUpdatePwd()
    {
        $old_pwd        = Yii::$app->request->post('old_pwd');
        $new_pwd        = Yii::$app->request->post('new_pwd');
        $new_two_pwd    = Yii::$app->request->post('new_two_pwd');
        list($st, $ret) = by::adminUserModel()->updatePwd($old_pwd,$new_pwd,$new_two_pwd,$this->user_info['id']);
        CUtil::json_response($st ? 1 : -1, $ret);
    }

    /**
     * @OA\Post(
     *     path="/rbac/manage/menu-select-list",
     *     summary="获取菜单下拉列表",
     *     description="获取菜单下拉列表",
     *     tags={"菜单管理"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionMenuSelectList()
    {
        $post = Yii::$app->request->post();
        $model = by::adminUriModel();
        $data = $model->getMenuSelectList($post);
        CUtil::json_response(1, ' 获取成功', $data);
    }
}
