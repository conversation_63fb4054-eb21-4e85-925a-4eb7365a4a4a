<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/3/26
 * Time: 11:57
 */

namespace app\modules\rbac\controllers;

use app\components\JwtTools;
use app\models\by;
use app\models\CUtil;
use Yii;
use app\modules\main\controllers\CommController As CC;

class CommController extends CC {

    /**
     * @OA\SecurityScheme(
     *     type="apiKey",
     *     in="header",
     *     name="sessid",
     *     @OA\Schema(type="string"),
     *     securityScheme="sessid"
     * )
     */

    //系统用户
    public $user_info;

    //微信版本控制
    public $wxVersion;
    public $wxSyncLock = 1;
    public $wxSyncCenterLock = 1;


    //是否能查看敏感信息
    public $viewSensitive = false;

    /**
     * 微信版本控制主副库和用户同步APP
     * @return void
     */
    protected function _versionControl()
    {
        $config = CUtil::getConfig('wx_version_ctrl','common',MAIN_MODULE);
        $this->wxVersion = CUtil::getRequestParam('headers','wx-version');
        if($this->wxVersion === ($config['wx_version']??'-1')){
            $this->userAuth = 0;
        }
        $this->wxSyncLock = $config['wx_sync_lock']??1;
        $this->wxSyncCenterLock = $config['wx_sync_center_lock']??1;
    }

    /**
     * @return void
     * 是否能查看敏感信息
     */
    protected function _viewSensitive(){
        //RBAC权限校验
        $check_ret = by::adminUserModel()->checkAuth($this->user_info, 'back/data/view_sensitive');
        if($check_ret) {
           $this->viewSensitive = true;
        }
    }

    /**
     * @return void
     */
    protected function _backWhiteIpsValid(){
        $ranges = CUtil::getConfig('backWhiteIps','common','rbac');
        $ip = CUtil::get_client_ip();
        $access_allowed = false;
        foreach ($ranges as $range){
            if(CUtil::is_ip_in_range($ip,$range)){$access_allowed = true;break;}
        }
        if(!$access_allowed)  CUtil::json_response(-1,'禁止登录 ip:'.$ip);
    }

    protected function _setBackUserData()
    {
        // 如果 Token 为空，则直接返回
        if (empty($this->jwtToken)) {
            $this->user_id = CUtil::checkUuid($this->user_id);
            return;
        }

        // 解析JWT Token
        list($status, $jwtDecodeSub) = JwtTools::factory()::decodeJsonWebToken($this->jwtToken);
        if ($status && JwtTools::factory()::isTokenInvalid($jwtDecodeSub['id'] ?? 0, $this->jwtToken)) {
            $this->_expired();
            return;
        }

        $this->user_info = $jwtDecodeData ?? [];
        $this->user_id   = $jwtDecodeSub['id'] ?? 0;
    }


    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool
    {
        try{
            // 往header里面添加request_id
            $request_id = CUtil::getRequestParam('headers','request_id','');
            if(empty($request_id)){
                $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
                Yii::$app->request->headers->add('request_id', $request_id);
            }
            
            //IP白名单校验
//            $this->_backWhiteIpsValid();

            $this->_initBase();

            $this->_setBackUserData();

            //签名合法性校验
            $this->_isValidSign();

            //app开关
            $this->_versionControl();



            //登录态白名单
            $path_info = Yii::$app->request->getPathInfo();
            $no_check  = CUtil::getConfig("noCheckUri",'common','rbac');
            if(in_array($path_info,$no_check,true)) {
                return true;
            }

            if(!YII_ENV_PROD && isset($this->_aPost['test_user_id']) && $this->_aPost['test_user_id'] == '3'){
                $this->user_info = by::adminUserModel()->getOneById(3);
                return true;
            }

            //登陆态校验
            if(!$this->user_id) {
                $this->_expired();
            }

            //登陆态校验
            list($status,$this->user_info) = by::adminUserModel()->checkLogin($this->user_id,$this->jwtToken);
            if(!$status) {
                $this->_expired();
            }

            //敏感信息查看
            $this->_viewSensitive();


            //RBAC权限校验
            $check_ret = by::adminUserModel()->checkAuth($this->user_info, $path_info);
            if($check_ret === false) {
                CUtil::json_response(401, "没有访问的权限");
            }


            //临时垃圾回收
            $this->_gc();

            return true;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
            return false;
        }
    }
}
