<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/6/8
 * Time: 14:46
 */

namespace app\modules\rbac\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\UserModel;
use app\modules\rbac\models\SystemLogsModel;

class LoginController extends CommController
{

    /**
     * @OA\Post(
     *     path="/rbac/login/index",
     *     summary="用户-登录",
     *     description="用户登录",
     *     tags={"后台用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              required={"api", "sign", "sign_time"},
     *              @OA\Property(property="api",  type="string", default="p_1643028263",description="平台API"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign_time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test",description="有此参数，不校验签名"),
     *              @OA\Property(property="sessid",  type="string", default="",description="用户sessid"),
     *              @OA\Property(property="version",  type="string", default="999.999.999",description="平台version"),
     *              @OA\Property(property="phone", type="string", default="", description="手机号：手机登录必传，其他传，account pwd"),
     *              @OA\Property(property="account", type="string", default="", description="用户名 【必要】"),
     *              @OA\Property(property="pwd", type="string", default="", description="密码 【必要】"),
     *              @OA\Property(property="verify_code", type="string", default="123456", description="登录验证码，登录时，is_send参数应设置为false"),
     *              @OA\Property(property="is_send", type="integer", default="0", description="是否发送验证码，实际登录时传空字符串"),
     *              @OA\Property(property="checked", type="boolean", default=true, description="token缓存七天,七天免登陆"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolean",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionIndex()
    {
        $model      = new UserModel();
        $phone      = \Yii::$app->request->post('phone', '');
        $account    = \Yii::$app->request->post('account', '');
        $pwd        = \Yii::$app->request->post('pwd', '');
        $verifyCode = \Yii::$app->request->post('verify_code', '');
        $isSend     = \Yii::$app->request->post('is_send', false); // 为true 发送验证码
        $checked    = \Yii::$app->request->post('checked', false); // 为true token缓存七天
        $post = [
            'phone'       => $phone,
            'account'     => $account,
            'pwd'         => $pwd,
            'is_send'     => $isSend,
            'verify_code' => $verifyCode,
            'checked'     => $checked,
        ];

        $ret = $model->login($post);
        if ($ret['status'] == 1) {
            CUtil::json_response(1, '验证码发送成功!');
        }
        if ($ret['status'] == 2) {
            (new SystemLogsModel())->record('登录', RbacInfoModel::LOGIN_MANAGER, $ret['data']['user']['id']);
            CUtil::json_response(1, '登录成功!', $ret['data']);
        }
        CUtil::json_response(-1, $ret['msg']);
    }

    /**
     * @OA\Post(
     *     path="/rbac/login/logout",
     *     summary="用户-登出",
     *     description="用户登出",
     *     tags={"后台用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              required={"api", "sign", "sign_time", "sessid"},
     *              @OA\Property(property="api",  type="string", default="p_1643028263",description="平台API"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign_time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test",description="有此参数，不校验签名"),
     *              @OA\Property(property="sessid",  type="string", default="",description="用户sessid"),
     *              @OA\Property(property="version",  type="string", default="999.999.999",description="平台version"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolean",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionLogout()
    {
        // 记录退出日志
        $logModel = new SystemLogsModel();
        $logModel->record("退出", RbacInfoModel::LOGIN_MANAGER);

        // 删除会话中的用户信息
        $userModel = by::adminUserModel();
        $userModel->delUserInfoToSession($this->user_id, $this->jwtToken);

        // 返回退出成功的响应
        CUtil::json_response(1, '退出登录成功');
    }


    /**
     * @OA\Post(
     *     path="/rbac/login/refresh-menu",
     *     summary="用户-权限列表",
     *     description="用户权限",
     *     tags={"后台用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              required={"api", "sign", "sign_time", "sessid"},
     *              @OA\Property(property="api",  type="string", default="p_1643028263",description="平台API"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign_time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test",description="有此参数，不校验签名"),
     *              @OA\Property(property="sessid",  type="string", default="",description="用户sessid"),
     *              @OA\Property(property="version",  type="string", default="999.999.999",description="平台version"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolean",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionRefreshMenu()
    {
        $adminUriModel  = by::adminUriModel();
        $user_info      = by::adminUserModel()->getUserInfo();

        $data           = by::adminRolesModel()->getDetail($user_info['r_id'] ?? 0);
        $menu_list      = $data['current_access'] ?? [];
        $countdown      = $adminUriModel->countdown;

        CUtil::json_response(1, 'ok', ['menu_list' => $menu_list, 'countdown' => $countdown]);
    }
}
