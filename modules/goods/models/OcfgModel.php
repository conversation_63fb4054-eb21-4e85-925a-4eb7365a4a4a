<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单商品快照表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\main\models\PlatformModel;
use yii\db\Exception;
use yii\helpers\Json;


class OcfgModel extends CommModel {

    /**
     * @param $order_no
     * @return string
     * 分表
     */
    public static function  tbName($order_no): string
    {
        $time       = by::Omain()->GetTbNameByOrderId($order_no, false);
        $year       = date("Y",intval($time));

        return  "`db_dreame_goods`.`t_uo_cfg_{$year}`";
    }


    public static function  tbNameByTime($time): string
    {
        $year       = date("Y",intval($time));

        return  "`db_dreame_goods`.`t_uo_cfg_{$year}`";
    }

    public $tb_fields = [
        'id','order_no','user_id','gid','sid','cfg','goods_type'
    ];

    /**
     * @param $order_no
     * @return string
     * 订单商品快照表
     */
    private function __getOcfgHashKey($order_no) {
        return AppCRedisKeys::getOcfgHash($order_no);
    }

    /**
     * @param $order_no
     * @return int
     * 清理订单商品列表
     */
    public function DelCache($order_no) : int
    {
        $r_key1 = $this->__getOcfgHashKey($order_no);
        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, array $aData, int $spriceType = 0)
    {
        YII_ENV_PROD && CUtil::debug(json_encode($aData),'save.ocfg');
        $gcombines      = $aData['gcombines']   ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]

        if (empty($gcombines)) {
            return [false, '参数错误(6)'];
        }

        // 是否员工
        $uid        = by::Phone()->getUidByUserId($user_id);
        $isEmployee = byNew::UserEmployeeModel()->isEffectiveEmployee($uid);

        $save           = [];
        foreach($gcombines as $gcombine) {
            $gData     = by::Gmain()->GetOneByGidSid($gcombine['gid'], $gcombine['sid'], false, false, $spriceType);
            $platforms = $gData['platforms'];
            $platforms = array_column($platforms, null, 'platform_id');

            //保存关键商品信息
            $cfg = [
                'type'                 => $gData['type'] ?? 0,
                'sku'                  => $gData['sku'] ?? '',
                'short_code'           => $gData['short_code'] ?? '',
                'name'                 => $gData['name'] ?? '',
                'mprice'               => $gData['mprice'] ?? 0,
                'price'                => $gData['price'] ?? 0,
                'fprice'               => $gData['fprice'] ?? 0,
                'atype'                => $gData['atype'] ?? 0,
                'is_coupons'           => $gData['is_coupons'] ?? 0,
                'limit_num'            => $gData['limit_num'] ?? 0,
                'is_internal_purchase' => $gData['is_internal_purchase'] ?? 0,
                'is_internal'          => $gData['is_internal'] ?? 0,
                'is_ini'               => $gData['is_ini'] ?? 0,          //是否是自定义价格
                'gini_id'              => $gData['gini_id'] ?? 0,         //自定义价格ID
                'is_presale'           => $gData['is_presale'] ?? 0,      //是否参与预售
                'is_yjhx'              => $gcombine['is_yjhx'] ?? 0,      //是否是以旧换新
                'presale_time'         => $gData['presale_time'] ?? 0,    //预售截止时间
                'deposit'              => $gData['deposit'] ?? 0,         //定金
                'expand_price'         => $gData['expand_price'] ?? 0,    //膨胀金额
                'start_payment'        => $gData['start_payment'] ?? 0,   //尾款开始时间
                'end_payment'          => $gData['end_payment'] ?? 0,     //尾款结束时间
                'surplus_time'         => $gData['surplus_time'] ?? 0,    //距离尾款几小时推送
                'scheduled_number'     => $gData['scheduled_number'] ?? 0,//预定人数
                'tids'                 => $gData['tids'] ?? [],
                'spec'                 => $gData['spec'] ?? [],
                'cover_image'          => $gData['cover_image'] ?? '',
                'market_image'         => $gData['market_image'] ?? '',
                'pc_image'             => $platforms[PlatformModel::PLATFORM['PC']]['cover_image'] ?? '',
                'is_employee'          => $isEmployee ? 1 : 0,            //是否是员工
            ];

            $save[] = [
                    'order_no' => $order_no,
                    'user_id'  => $user_id,
                    'gid'      => $gcombine['gid'],
                    'sid'      => $gcombine['sid'],
                    'cfg'      => json_encode($cfg),
                    'ctime'    => time(),
            ];
        }

        $tb             = self::tbName($order_no);
        by::dbMaster()->createCommand()->batchInsert($tb, ['order_no', 'user_id', 'gid', 'sid', 'cfg', 'ctime'], $save)->execute();

        return [true, 'ok'];
    }



    /**
     * @param $order_no
     * @param $gid
     * @param $sid
     * @return array|false
     * @throws \yii\db\Exception
     * 根据唯一索引获取数据
     */
    public function GetOneByUnique($order_no, $gid, $sid)
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getOcfgHashKey($order_no);
        $sub_key     = CUtil::getAllParams(__FUNCTION__,$gid,$sid);
        $aJson       = $redis->hGet($r_key,$sub_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName($order_no);
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no AND `gid`=:gid AND `sid`=:sid LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no,':gid'=>$gid,':sid'=>$sid])->queryOne();
            $aData   = $aData ?: [];

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,600);
        }

        if (empty($aData)) {
            return [];
        }

        $cfg     = $aData['cfg'];
        $cfg     = json_decode($cfg, true);

        unset($aData['cfg']);

        $aData = array_merge($aData, $cfg);

        return $aData;
    }

    /**
     * 获取属性值
     * @param array $items
     * @return array|\yii\db\DataReader
     * @throws Exception
     */
    public function getAttributeList(array $items)
    {
        $data = [];
        $attrValueIds = [];
        foreach ($items as $item) {
            $attr = (array)Json::decode($item['spec']['av_ids'] ?? '', true);
            $attrValueIds = array_merge($attrValueIds, $attr);
        }
        $attrValueIds = array_unique($attrValueIds);
        // 获取属性值
        $data = by::Gav()->getListByIds($attrValueIds);
        return $data;
    }

    /**
     * 格式化数据
     * @param array $items
     * @return array
     */
    public function formatOrderConfigItems(array $items): array
    {
        $data = [];
        foreach ($items as $item) {
            $order_no = $item['order_no'];
            $gid = $item['gid'];
            $sid = $item['sid'];
            // 拼装数据
            $data[$order_no][$gid][$sid] = [
                'order_no' => $order_no,
                'user_id'  => $item['user_id'],
                'name'     => $item['name'],
                'gid'      => $gid,
                'sid'      => $sid,
                'tids'     => $item['tids']??[],
                'sku'      => $item['spec']['sku'] ?? $item['sku'], // 多规格的，则取具体的规格的sku
                'av_ids'   => (array)Json::decode($item['spec']['av_ids'] ?? '', true),
                'price'    => $item['spec']['price'] ?? $item['price'],
            ];
        }
        return $data;
    }

    /**
     * 根据订单号获取信息（无缓存）
     * @param array $orderNos
     * @return array
     * @throws Exception
     */
    public function getListByOrderNos(array $orderNos): array
    {
        $items = [];
        // 查询字段
        $columns = implode("`,`", $this->tb_fields);
        // 分组查询
        $groupOrderNos = $this->groupOrderNo($orderNos);
        foreach ($groupOrderNos as $index => $nos) {
            $tb = self::tbName($nos[0]); // 获取表名称
            // 查询条件
            $nos = implode("','", $nos);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `order_no` IN ('{$nos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $items = array_merge($items, $res);
        }
        // 处理数据
        foreach ($items as &$item) {
            // 解析config
            $cfg = $item['cfg'];
            $cfg = (array)json_decode($cfg, true);
            // 将config与结果合并
            unset($item['cfg']);
            $item = array_merge($item, $cfg);
        }
        return $items;
    }

    /**
     * 订单号分组
     * @param array $orderNos
     * @return array
     */
    private function groupOrderNo(array $orderNos): array
    {
        $data = [];
        foreach ($orderNos as $orderNo) {
            // 获取年份，分组
            $index = substr($orderNo, 0, 4); // 年份
            $data[$index][] = $orderNo;
        }
        return $data;
    }

}
