<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 邀请规则奖品详情模型
 * 
 * @property int $id 主键
 * @property int $rule_id 关联 invite_gift_rules 表的ID
 * @property string $gift_id 奖品ID，关联奖品表
 * @property int $gift_type 奖品类型，区分不同类型的礼品ID
 * @property int $quantity 奖品数量
 * @property int $created_at 创建时间（秒时间戳）
 * @property int $updated_at 最后修改时间（秒时间戳）
 */
class UserInviteRuleGiftItemsModel extends CommModel
{
    // 奖品类型
    const GIFT_TYPE = [
        'GIFT_CARD' => 1, // 礼品卡
        'COUPON' => 2, // 优惠券
        'POINTS' => 3, // 积分
        'GOODS' => 4, // 商品
    ];

    public $tb_fields = [
        'id', 'rule_id', 'gift_id', 'gift_type', 'quantity', 'created_at', 'updated_at'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_rule_gift_items`";
    }

    /**
     * 根据规则ID获取奖品列表
     * @param int $ruleId 规则ID
     * @return array
     * @throws Exception
     */
    public function getGiftItemsByRuleId(int $ruleId): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE rule_id = :rule_id 
                ORDER BY created_at ASC";
        
        $params = [':rule_id' => $ruleId];
        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据规则ID列表获取奖品列表
     * @param array $ruleIds 规则ID列表
     * @return array
     * @throws Exception
     */
    public function getGiftItemsByRuleIds(array $ruleIds): array
    {
        if (empty($ruleIds)) {
            return [];
        }

        $ruleIdPlaceholders = implode(',', array_fill(0, count($ruleIds), '?'));
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE rule_id IN ({$ruleIdPlaceholders})
                ORDER BY rule_id ASC, created_at ASC";
        
        return by::dbMaster()->createCommand($sql, $ruleIds)->queryAll() ?: [];
    }

    /**
     * 根据ID获取奖品项
     * @param int $id 奖品项ID
     * @return array
     * @throws Exception
     */
    public function getGiftItemById(int $id): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " WHERE id = :id LIMIT 1";
        $result = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
        return $result ?: [];
    }

    /**
     * 创建奖品项
     * @param array $data 奖品项数据
     * @return array
     * @throws Exception
     */
    public function createGiftItem(array $data): array
    {
        $now = time();
        $data['created_at'] = $now;
        $data['updated_at'] = $now;

        // 验证必填字段
        $requiredFields = ['rule_id', 'gift_id', 'gift_type', 'quantity'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return [false, "字段 {$field} 不能为空"];
            }
        }

        // 验证数量
        if ($data['quantity'] <= 0) {
            return [false, '奖品数量必须大于0'];
        }

        try {
            $fields = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO " . self::tbName() . " ({$fields}) VALUES ({$placeholders})";
            
            $result = by::dbMaster()->createCommand($sql, $data)->execute();
            
            if ($result > 0) {
                $insertId = by::dbMaster()->getLastInsertID();
                return [true, $insertId];
            } else {
                return [false, '奖品项创建失败'];
            }
        } catch (\Exception $e) {
            // 处理唯一键冲突
            if (strpos($e->getMessage(), 'uk_rule_gift_item') !== false) {
                return [false, '该规则下已存在相同的奖品项'];
            }
            return [false, '奖品项创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 批量创建奖品项
     * @param int $ruleId 规则ID
     * @param array $giftItems 奖品项列表
     * @return array
     * @throws Exception
     */
    public function batchCreateGiftItems(int $ruleId, array $giftItems): array
    {
        if (empty($giftItems)) {
            return [false, '奖品项列表不能为空'];
        }

        $db = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            $insertedIds = [];
            
            foreach ($giftItems as $item) {
                $item['rule_id'] = $ruleId;
                list($status, $result) = $this->createGiftItem($item);
                
                if (!$status) {
                    throw new \Exception($result);
                }
                
                $insertedIds[] = $result;
            }

            $transaction->commit();
            return [true, $insertedIds];
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 更新奖品项
     * @param int $id 奖品项ID
     * @param array $data 更新数据
     * @return array
     * @throws Exception
     */
    public function updateGiftItem(int $id, array $data): array
    {
        if (empty($data)) {
            return [false, '更新数据不能为空'];
        }

        $data['updated_at'] = time();

        // 验证数量（如果有）
        if (isset($data['quantity']) && $data['quantity'] <= 0) {
            return [false, '奖品数量必须大于0'];
        }

        try {
            $setParts = [];
            $params = [':id' => $id];
            
            foreach ($data as $field => $value) {
                $setParts[] = "{$field} = :{$field}";
                $params[":{$field}"] = $value;
            }
            
            $setClause = implode(', ', $setParts);
            $sql = "UPDATE " . self::tbName() . " SET {$setClause} WHERE id = :id";
            
            $result = by::dbMaster()->createCommand($sql, $params)->execute();
            
            if ($result > 0) {
                return [true, '奖品项更新成功'];
            } else {
                return [false, '奖品项更新失败或数据无变化'];
            }
        } catch (\Exception $e) {
            // 处理唯一键冲突
            if (strpos($e->getMessage(), 'uk_rule_gift_item') !== false) {
                return [false, '该规则下已存在相同的奖品项'];
            }
            return [false, '奖品项更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 删除奖品项
     * @param int $id 奖品项ID
     * @return array
     * @throws Exception
     */
    public function deleteGiftItem(int $id): array
    {
        try {
            $sql = "DELETE FROM " . self::tbName() . " WHERE id = :id";
            $result = by::dbMaster()->createCommand($sql, [':id' => $id])->execute();
            
            if ($result > 0) {
                return [true, '奖品项删除成功'];
            } else {
                return [false, '奖品项删除失败或不存在'];
            }
        } catch (\Exception $e) {
            return [false, '奖品项删除失败：' . $e->getMessage()];
        }
    }

    /**
     * 根据规则ID删除所有奖品项
     * @param int $ruleId 规则ID
     * @return array
     * @throws Exception
     */
    public function deleteGiftItemsByRuleId(int $ruleId): array
    {
        try {
            $sql = "DELETE FROM " . self::tbName() . " WHERE rule_id = :rule_id";
            $result = by::dbMaster()->createCommand($sql, [':rule_id' => $ruleId])->execute();
            
            return [true, "删除了 {$result} 个奖品项"];
        } catch (\Exception $e) {
            return [false, '批量删除奖品项失败：' . $e->getMessage()];
        }
    }

    /**
     * 检查奖品项是否存在
     * @param int $ruleId 规则ID
     * @param string $giftId 奖品ID
     * @param int $giftType 奖品类型
     * @return bool
     * @throws Exception
     */
    public function existsGiftItem(int $ruleId, string $giftId, int $giftType): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE rule_id = :rule_id AND gift_id = :gift_id AND gift_type = :gift_type";
        
        $params = [
            ':rule_id' => $ruleId,
            ':gift_id' => $giftId,
            ':gift_type' => $giftType,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }

    /**
     * 获取奖品项列表（分页）
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param int|null $ruleId 规则ID筛选
     * @param int|null $giftType 奖品类型筛选
     * @return array
     * @throws Exception
     */
    public function getGiftItemList(int $page = 1, int $pageSize = 20, int $ruleId = null, int $giftType = null): array
    {
        $offset = ($page - 1) * $pageSize;
        
        $where = "1=1";
        $params = [];
        
        if ($ruleId !== null) {
            $where .= " AND rule_id = :rule_id";
            $params[':rule_id'] = $ruleId;
        }
        
        if ($giftType !== null) {
            $where .= " AND gift_type = :gift_type";
            $params[':gift_type'] = $giftType;
        }

        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM " . self::tbName() . " WHERE {$where}";
        $countResult = by::dbMaster()->createCommand($countSql, $params)->queryOne();
        $total = (int)($countResult['count'] ?? 0);

        // 获取列表
        $listSql = "SELECT * FROM " . self::tbName() . " 
                    WHERE {$where}
                    ORDER BY created_at DESC 
                    LIMIT {$pageSize} OFFSET {$offset}";
        $list = by::dbMaster()->createCommand($listSql, $params)->queryAll() ?: [];

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }
} 