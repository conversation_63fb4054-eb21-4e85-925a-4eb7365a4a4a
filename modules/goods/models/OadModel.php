<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单地址表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\ExpressTen;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class OadModel extends CommModel {


    public static function tbName($order_no): string
    {
        $time = by::Omain()->GetTbNameByOrderId($order_no, false);
        $year = date("Y",intval($time));

        return  "`db_dreame_goods`.`t_uo_ad_{$year}`";
    }

    public $tb_fields = [
        'id','order_no','address','mail_no','express_code','express_name',
        'nick','phone','pid','cid','aid','detail','address_id',
    ];

    /**
     * @param $order_no
     * @return string
     * 订单收发货唯一缓存KEY
     */
    private function __getOneInfoKey($order_no): string
    {
        return AppCRedisKeys::getOneOadInfo($order_no);
    }

    /**
     * @param $order_no
     * @return int
     * 清理订单收发货缓存
     */
    public function DelCache($order_no) : int
    {
        $r_key1 = $this->__getOneInfoKey($order_no);
        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $user_id
     * @param string $order_no
     * @param int $aid
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, int $aid)
    {

        $address = [];
        $aData = [];
        if ($aid!= 0) {
            $aData = by::Address()->GetOneAddress($user_id,$aid);
            $address = by::Address()->SafeInfo($aData);
        }


        $save    = [
            'order_no'      => $order_no,
            'address_id'    => $aid,
            'nick'          => $aData['nick'] ?? '',
            'phone'         => $aData['phone'] ?? '',
            'pid'           => $aData['pid'] ?? 0,
            'cid'           => $aData['cid'] ?? 0,
            'aid'           => $aData['aid'] ?? 0,
            'detail'        => $aData['detail'] ?? '',
            'address'       => json_encode($address),
        ];

        $tb = $this->tbName($order_no);

        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        return [true, 'ok'];
    }

    public function GetOneByOrderNo(string $order_no)
    {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($order_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName($order_no);
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->queryOne();
            $aData   = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),600);
        }

        if (empty($aData)) {
            return [];
        }

        $aData['address'] = (array)json_decode($aData['address'], true);

        return $aData;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 根据订单号查物流详情
     */
    public function GetExpressByOrderNo($user_id, $order_no): array
    {
        if (empty($order_no)) {
            return [false, '参数错误',[]];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作',[]];
        }

        //需要展示商品信息
        $mOgoods = by::Ogoods();
        $oGoods = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);

        if (empty($oGoods)) {
            return [true, '暂未发货(2)', ['goods' => []]];
        }

        $goods = by::Ocfg()->GetOneByUnique($order_no, $oGoods[0]['gid'], $oGoods[0]['sid']);
        $aData['goods'] = ['name' => $goods['name'], 'cover_image' => $goods['cover_image']];

        $aInfo = $this->GetOneByOrderNo($order_no);

        // 优选使用订单地址表的物流单号
        $mail_no = $aInfo['mail_no'] ?? '';
        $express_code = $aInfo['express_code'] ?? '';
        $express_name = $aInfo['express_name'] ?? '';
        
        $has_third = false;
        $third_count = 0;
        foreach ($oGoods as $oGoodsItem) {
            if ($oGoodsItem['is_third'] == 1) {
                $has_third = true;
                $third_count++;
            }
        }

        // 先判断订单中是否包含优选商品，以及是否全部为优选商品还是部分为优选商品，全部为优选商品就不从订单地址表中获取物流，如果为部分优选商品就从订单地址表中获取物流
        if (! $has_third) {
            // 订单不包含优选商品，从订单地址表中获取
            if (empty($mail_no)) {
                return [true, '暂未发货', $aData];
            }
        } elseif(count($oGoods) == $third_count) {
            // 全部为优选商品，从订单商品表中获取物流，不从订单地址表获取
            foreach ($oGoods as $oGoodsItem) {
                if ($oGoodsItem['is_third'] == 1) {
                    if (! empty($oGoodsItem['mail_no']) && ! empty($oGoodsItem['express_code']) && ! empty($oGoodsItem['express_name'])) {
                        $mail_no = $oGoodsItem['mail_no'];
                        $express_code = $oGoodsItem['express_code'];
                        $express_name = $oGoodsItem['express_name'];
                        break;
                    }
                }
            }
        }

        if (empty($mail_no)) {
            return [true, '暂未发货', $aData];
        }

        $aInfo['express_code'] = ExpressTen::factory()->getKD100ExpressCode($express_code);

        list($status, $expressData) = ExpressTen::factory()->GetInfo($mail_no, $aInfo['express_code'], $aInfo['phone'] ?? '');

        if (!$status) {
            return [true,$expressData,[]];
        }
        $aData = array_merge($aData, $expressData);
        $aData['nu'] = $aData['nu'] ?? $mail_no;
        $aData['nick'] = $aInfo['nick'];
        $aData['phone'] = $aInfo['phone'];
        $aData['express_name'] = $express_name;
        $address = $aInfo['address'];
        $address['detail'] = $aInfo['detail'];
        $aData['address'] = $address;

        return [true, 'ok',$aData];
    }


    /**
     * @param $order_no
     * @param array $data
     * @return array
     * @throws Exception
     * 更新数据
     */
    public function UpdateData($order_no, array $data)
    {
        $allowed = ['mail_no','express_code','express_name'];

        foreach ($data as $field => $val) {
            if ( !in_array($field, $allowed) ) {
                unset($data[$field]);
            }
        }
        if (empty($data)) {
            return [false, '无数据更改'];
        }

        $tb = self::tbName($order_no);
        by::dbMaster()->createCommand()->update($tb, $data, ['order_no' => $order_no])->execute();

        $this->DelCache($order_no);

        return [true, 'ok'];
    }


    /**
     * 根据订单号获取数据（无缓存）
     * @param array $orderNos
     * @param array|string[] $columns
     * @return array
     * @throws \yii\db\Exception
     */
    public function getListByOrderNos(array $orderNos, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $columns);
        // 分组查询
        $groupOrderNos = $this->groupOrderNo($orderNos);
        foreach ($groupOrderNos as $index => $nos) {
            $tb = self::tbName($nos[0]); // 获取表名称
            // 查询条件
            $nos = implode("','", $nos);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `order_no` IN ('{$nos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 订单号分组
     * @param array $orderNos
     * @return array
     */
    private function groupOrderNo(array $orderNos): array
    {
        $data = [];
        foreach ($orderNos as $orderNo) {
            // 获取年份，分组
            $index = substr($orderNo, 0, 4); // 年份
            $data[$index][] = $orderNo;
        }
        return $data;
    }

}
