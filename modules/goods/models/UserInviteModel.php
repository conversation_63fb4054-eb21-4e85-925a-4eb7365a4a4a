<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 用户邀请模型
 *
 * @property int $id 主键
 * @property int $inviter_id 邀请人ID
 * @property int $invitee_id 被邀请人ID
 * @property int $invite_type 邀请类型 1=一元秒杀
 * @property int $relate_id 与邀请类型关联的外键ID，例如拼团ID、秒杀ID等
 * @property int $invited_at 邀请时间（秒时间戳）
 * @property string $remark 备注信息
 */
class UserInviteModel extends CommModel
{
    // 邀请类型
    const INVITE_TYPE = [
            'ONE_YUAN_SECKILL' => 1,// 一元秒杀
            'FRIEND_BUY'       => 2,// 邀请好友半价购买
            'REG_MALL_INVITE'  => 3,// 商城注册邀请
    ];

    public $tb_fields = [
            'id',
            'inviter_id',
            'invitee_id',
            'invite_type',
            'relate_id',
            'invited_at',
            'device_id',
            'remark'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invites`";
    }

    /**
     * 创建邀请记录
     * @param int $inviterId 邀请人ID
     * @param int $inviteeId 被邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param string|null $deviceId 设备ID
     * @param string|null $remark 备注信息
     * @return array
     * @throws Exception
     */
    public function createInvite(int $inviterId, int $inviteeId, int $inviteType, int $relateId, string $deviceId = null, string $remark = null): array
    {
        // 对于invite_type=1的情况，验证invitee_id+invite_type的唯一性
        if ($inviteType == self::INVITE_TYPE['ONE_YUAN_SECKILL']) {
            if ($this->hasInvitedByType($inviteeId, $inviteType)) {
                return [
                        false,
                        '用户已参与过此类型的邀请活动'
                ];
            }
        }

        $data = [
                'inviter_id'  => $inviterId,
                'invitee_id'  => $inviteeId,
                'invite_type' => $inviteType,
                'relate_id'   => $relateId,
                'invited_at'  => time(),
        ];

        // 添加设备ID（如果提供）
        if ($deviceId !== null) {
            $data['device_id'] = $deviceId;
        }

        // 添加备注信息（如果提供）
        if ($remark !== null) {
            $data['remark'] = $remark;
        }

        // 检查设备ID唯一性（如果提供）
        if ($deviceId !== null && $this->hasDeviceId($deviceId)) {
            return [
                    false,
                    '系统检测到你的设备已参与过本次活动，无法再次参与。邀请新的朋友一起来试试吧！'
            ];
        }

        $fields       = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        $sql          = "INSERT INTO " . self::tbName() . " 
                ({$fields}) VALUES ({$placeholders})";

        try {
            $command = by::dbMaster()->createCommand($sql, $data);
            $result  = $command->execute();

            if ($result > 0) {
                return [
                        true,
                        '邀请成功'
                ];
            } else {
                return [
                        false,
                        '邀请记录创建失败'
                ];
            }
        } catch (\Exception $e) {
            // 处理唯一键冲突
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                return [
                        false,
                        '邀请记录已存在'
                ];
            }
            return [
                    false,
                    '邀请失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取邀请记录列表
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param bool $todayOnly 是否只获取当天邀请，默认false保持兼容性
     * @return array
     * @throws Exception
     */
    public function getInviteList(int $inviterId, int $inviteType, int $relateId, bool $todayOnly = false): array
    {
        $sql = "SELECT ui.*
                FROM " . self::tbName() . " ui
                WHERE ui.inviter_id = :inviter_id 
                AND ui.invite_type = :invite_type 
                AND ui.relate_id = :relate_id";

        $params = [
                ':inviter_id'  => $inviterId,
                ':invite_type' => $inviteType,
                ':relate_id'   => $relateId,
        ];

        // 如果需要只获取当天邀请的用户，添加时间条件
        if ($todayOnly) {
            $startTime = strtotime(date('Y-m-d 00:00:00'));
            $endTime = strtotime(date('Y-m-d 23:59:59'));
            $sql .= " AND ui.invited_at BETWEEN :start_time AND :end_time";
            $params[':start_time'] = $startTime;
            $params[':end_time'] = $endTime;
        }

        $sql .= " ORDER BY ui.invited_at DESC";

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 获取邀请人数统计
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param bool $todayOnly 是否只统计当天邀请，默认false保持兼容性
     * @return int
     * @throws Exception
     */
    public function getInviteCount(int $inviterId, int $inviteType, int $relateId, bool $todayOnly = false): int
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id 
                AND invite_type = :invite_type 
                AND relate_id = :relate_id";

        $params = [
                ':inviter_id'  => $inviterId,
                ':invite_type' => $inviteType,
                ':relate_id'   => $relateId,
        ];

        // 如果需要只统计当天邀请的用户，添加时间条件
        if ($todayOnly) {
            $startTime = strtotime(date('Y-m-d 00:00:00'));
            $endTime = strtotime(date('Y-m-d 23:59:59'));
            $sql .= " AND invited_at BETWEEN :start_time AND :end_time";
            $params[':start_time'] = $startTime;
            $params[':end_time'] = $endTime;
        }

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int) ($result['count'] ?? 0);
    }

    /**
     * 检查用户是否已经为某个活动助力过
     * @param int $inviteeId 被邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @return bool
     * @throws Exception
     */
    public function hasInvited(int $inviteeId, int $inviteType, int $relateId): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE invitee_id = :invitee_id 
                AND invite_type = :invite_type 
                AND relate_id = :relate_id";

        $params = [
                ':invitee_id'  => $inviteeId,
                ':invite_type' => $inviteType,
                ':relate_id'   => $relateId,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }

    /**
     * 检查用户是否已经参与过某类型的邀请活动（用于invite_type=1的唯一性验证）
     * @param int $inviteeId 被邀请人ID
     * @param int $inviteType 邀请类型
     * @return bool
     * @throws Exception
     */
    public function hasInvitedByType(int $inviteeId, int $inviteType): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE invitee_id = :invitee_id 
                AND invite_type = :invite_type";

        $params = [
                ':invitee_id'  => $inviteeId,
                ':invite_type' => $inviteType,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }

    /**
     * 检查是否为自己助力
     * @param int $inviterId 邀请人ID
     * @param int $inviteeId 被邀请人ID
     * @return bool
     */
    public function isSelfInvite(int $inviterId, int $inviteeId): bool
    {
        return $inviterId === $inviteeId;
    }

    /**
     * 批量获取用户信息
     * @param array $userIds 用户ID列表
     * @return array
     * @throws Exception
     */
    public function getBatchUserInfo(array $userIds): array
    {
        if (empty($userIds)) {
            return [];
        }

        $userIdPlaceholders = implode(',', array_fill(0, count($userIds), '?'));
        $sql                = "SELECT user_id, nick, avatar FROM `db_dreame_user`.`t_users` 
                WHERE user_id IN ($userIdPlaceholders)";

        return by::dbMaster()->createCommand($sql, $userIds)->queryAll() ?: [];
    }

    /**
     * 删除邀请记录（用于取消助力等场景）
     * @param int $inviterId 邀请人ID
     * @param int $inviteeId 被邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @return bool
     * @throws Exception
     */
    public function deleteInvite(int $inviterId, int $inviteeId, int $inviteType, int $relateId): bool
    {
        $sql = "DELETE FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id 
                AND invitee_id = :invitee_id 
                AND invite_type = :invite_type 
                AND relate_id = :relate_id";

        $params = [
                ':inviter_id'  => $inviterId,
                ':invitee_id'  => $inviteeId,
                ':invite_type' => $inviteType,
                ':relate_id'   => $relateId,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 检查设备ID是否已存在
     * @param string $deviceId 设备ID
     * @return bool
     * @throws Exception
     */
    public function hasDeviceId(string $deviceId): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE device_id = :device_id";

        $params = [':device_id' => $deviceId];
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }


    /**
     * 获取用户今日邀请数量
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @return int
     * @throws Exception
     */
    public function getTodayInviteCount(int $inviterId, int $inviteType): int
    {
        $startTime = strtotime(date('Y-m-d 00:00:00'));
        $endTime   = strtotime(date('Y-m-d 23:59:59'));

        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id 
                AND invite_type = :invite_type 
                AND invited_at BETWEEN :start_time AND :end_time";

        $params = [
                ':inviter_id'  => $inviterId,
                ':invite_type' => $inviteType,
                ':start_time'  => $startTime,
                ':end_time'    => $endTime,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int) ($result['count'] ?? 0);
    }

    /**
     * 检查用户是否已被任何类型的邀请过（全局检查）
     * @param int $inviteeId 被邀请人ID
     * @return bool
     * @throws Exception
     */
    public function hasGlobalInvited(int $inviteeId): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE invitee_id = :invitee_id";

        $params = [':invitee_id' => $inviteeId];
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }

    /**
     * 获取用户邀请统计信息
     * @param int $inviterId 邀请人ID
     * @return array
     * @throws Exception
     */
    public function getInviteStatistics(int $inviterId): array
    {
        $sql = "SELECT 
                    invite_type,
                    COUNT(*) as total_count,
                    COUNT(DISTINCT invitee_id) as unique_invitee_count,
                    MIN(invited_at) as first_invite_time,
                    MAX(invited_at) as last_invite_time
                FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id 
                GROUP BY invite_type";

        $params = [':inviter_id' => $inviterId];
        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 获取邀请趋势数据（按日期统计）
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $days 统计天数，默认30天
     * @return array
     * @throws Exception
     */
    public function getInviteTrend(int $inviterId, int $inviteType, int $days = 30): array
    {
        $startTime = strtotime("-{$days} days");

        $sql = "SELECT 
                    DATE(FROM_UNIXTIME(invited_at)) as invite_date,
                    COUNT(*) as count
                FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id 
                AND invite_type = :invite_type 
                AND invited_at >= :start_time
                GROUP BY DATE(FROM_UNIXTIME(invited_at))
                ORDER BY invite_date ASC";

        $params = [
                ':inviter_id'  => $inviterId,
                ':invite_type' => $inviteType,
                ':start_time'  => $startTime,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 批量检查用户是否已被邀请
     * @param array $inviteeIds 被邀请人ID列表
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @return array
     * @throws Exception
     */
    public function batchCheckInvited(array $inviteeIds, int $inviteType, int $relateId = 0): array
    {
        if (empty($inviteeIds)) {
            return [];
        }

        $inviteeIdStr = implode(',', array_map('intval', $inviteeIds));
        $sql          = "SELECT DISTINCT invitee_id FROM " . self::tbName() . " 
                WHERE invitee_id IN ({$inviteeIdStr}) 
                AND invite_type = :invite_type";

        $params = [':invite_type' => $inviteType];

        if ($relateId > 0) {
            $sql                  .= " AND relate_id = :relate_id";
            $params[':relate_id'] = $relateId;
        }

        $invitedUsers = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $invitedIds   = array_column($invitedUsers, 'invitee_id');

        // 构建结果数组
        $result = [];
        foreach ($inviteeIds as $inviteeId) {
            $result[$inviteeId] = in_array($inviteeId, $invitedIds);
        }

        return $result;
    }

    /**
     * 获取邀请人的邀请记录列表
     * @param int $inviterId 邀请人ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param int|null $inviteType 邀请类型筛选
     * @param int|null $relateId 关联ID筛选
     * @return array
     * @throws Exception
     */
    public function getInviteListByInviter(int $inviterId, int $page = 1, int $pageSize = 20, int $inviteType = null, int $relateId = null): array
    {
        $offset = ($page - 1) * $pageSize;

        // 构建WHERE条件
        $where = "ui.inviter_id = :inviter_id";
        $params = [':inviter_id' => $inviterId];

        if ($inviteType !== null) {
            $where .= " AND ui.invite_type = :invite_type";
            $params[':invite_type'] = $inviteType;
        }

        if ($relateId !== null) {
            $where .= " AND ui.relate_id = :relate_id";
            $params[':relate_id'] = $relateId;
        }

        // 获取基础邀请记录
        $sql = "SELECT 
                    ui.id,
                    ui.inviter_id,
                    ui.invitee_id,
                    ui.invite_type,
                    ui.relate_id,
                    ui.invited_at,
                    ui.device_id,
                    ui.remark
                FROM " . self::tbName() . " ui
                WHERE {$where}
                ORDER BY ui.invited_at DESC
                LIMIT :offset, :page_size";

        $params[':offset'] = $offset;
        $params[':page_size'] = $pageSize;

        $inviteList = by::dbMaster()->createCommand($sql, $params)->queryAll();

        if (empty($inviteList)) {
            return [];
        }


        // 合并数据
        $result = [];
        foreach ($inviteList as $invite) {
            $inviteeId = $invite['invitee_id'];
            $user      = by::users()->getOneByUid($inviteeId);
            $nick      = $user['nick'] ?? '';
            $len       = mb_strlen($nick);
            // 昵称只显示首位
            if ($len > 1) {
                $nick = mb_substr($nick, 0, 1) . str_repeat('*', $len - 1);
            }

            $result[] = array_merge($invite, [
                    'nick'   => $nick,
                    'avatar' => CUtil::avatar($user)
            ]);
        }

        return $result;
    }

    /**
     * 获取邀请人的邀请记录总数
     * @param int $inviterId 邀请人ID
     * @param int|null $inviteType 邀请类型筛选
     * @param int|null $relateId 关联ID筛选
     * @return int
     * @throws Exception
     */
    public function getInviteCountByInviter(int $inviterId, int $inviteType = null, int $relateId = null): int
    {
        // 构建WHERE条件
        $where = "inviter_id = :inviter_id";
        $params = [':inviter_id' => $inviterId];

        if ($inviteType !== null) {
            $where .= " AND invite_type = :invite_type";
            $params[':invite_type'] = $inviteType;
        }

        if ($relateId !== null) {
            $where .= " AND relate_id = :relate_id";
            $params[':relate_id'] = $relateId;
        }

        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE {$where}";

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int) ($result['count'] ?? 0);
    }

    /**
     * 检查被邀请人是否是追觅推广大使
     * @param int $inviteeId 被邀请人ID
     * @return array [是否是追觅大使, 邀请人ID, 被邀请人ID]
     */
    public function checkDreameAmbassador(int $inviteeId): array
    {
        try {
            // 首先检查用户是否通过REG_MALL_INVITE邀请注册
            $sql = "SELECT inviter_id FROM " . self::tbName() . "
            WHERE invitee_id = :invitee_id 
            AND invite_type = :invite_type 
            LIMIT 1";

            $params = [
                    ':invitee_id'  => $inviteeId,
                    ':invite_type' => self::INVITE_TYPE['REG_MALL_INVITE']
            ];

            $result = by::dbMaster()->createCommand($sql, $params)->queryOne();

            // 如果用户不是通过REG_MALL_INVITE邀请注册的
            if (empty($result)) {
                return [
                        'is_dream_ambassador' => false,
                        'inviter_id'          => 0,
                        'invitee_id'          => $inviteeId,
                        'total_invites'       => 0
                ];
            }

            // 用户的邀请人
            $originalInviterId = $result['inviter_id'];

            // 直接检查 user_invite_rank 表中的 is_invite_ambassador 字段
            $userInviteRankModel = by::UserInviteRankModel();
            $rankRecord = $userInviteRankModel->getUserRecord($originalInviterId);
            
            $isDreamAmbassador = !empty($rankRecord) && (int)($rankRecord['is_invite_ambassador'] ?? 0) === 1;
            $totalInvites = (int)($rankRecord['invite_count'] ?? 0);

            return [
                    'is_dream_ambassador' => $isDreamAmbassador,
                    'inviter_id'          => (int) $originalInviterId,
                    'invitee_id'          => (int)$inviteeId,
                    'total_invites'       => $totalInvites
            ];
        } catch (\Exception $e) {
            return [
                    'is_dream_ambassador' => false,
                    'inviter_id'          => 0,
                    'invitee_id'          => $inviteeId,
                    'total_invites'       => 0
            ];
        }
    }

    /**
     * 按类型获取用户邀请记录
     * @param int $type 邀请类型 1=一元秒杀 2=半价购
     * @return array
     * @throws Exception
     */
    public function getUserInviteRecordByType(int $type = 0): array
    {
        $where = '1 = 1';
        $params = [];
        if (! empty($type)) {
            $where .= ' AND ui.invite_type = :invite_type';
            $params[':invite_type'] = $type;
        }

        $sql = "SELECT
                    ui.inviter_id,
                    ui.remark,
                    ui.invited_at
                FROM " . self::tbName() . " ui
                WHERE {$where}
                ORDER BY ui.invited_at DESC;";

        return by::dbMaster()->createCommand($sql, $params)->queryAll();
    }
} 