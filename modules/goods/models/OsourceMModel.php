<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 导购订单关系表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\DataReader;
use yii\db\Exception;


class OsourceMModel extends CommModel
{
    CONST EXP = 600;
    const DB_TIMEZONE = [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030]; //数据库的时间跨度

    public static function tbName($time): string
    {
        $year = date("Y", intval($time));

        //防止出现超出时间范围的查询
        $year = max($year, by::Omain()::DB_TIMEZONE['ST']);
        $year = min($year, by::Omain()::DB_TIMEZONE['ED']);

        return "`db_dreame_goods`.`t_osource_m_{$year}`";
    }

    public $tb_fields = [
        'id', 'user_id', 'order_no', 'union', 'euid', 'referer','status','live_mark',
        'price', 'finish_time', 'ctime'
    ];

    /**
     * @param $union
     * @return string
     */
    private function __getListKey($union): string
    {
        return AppCRedisKeys::getOsourceMList($union);
    }

    /**
     * @param string $union
     * 清理缓存
     */
    public function delListCache(string $union = '')
    {
        $r_key1 = $this->__getListKey($union);

        by::redis('core')->del($r_key1);
    }


    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 详情唯一缓存KEY
     */
    private function __getInfoByOrderNoKey($user_id, $order_no): string
    {
        return AppCRedisKeys::getOsMInfoByOrderNo($user_id, $order_no);
    }


    /**
     * @param $user_id
     * @param $order_no
     * 清理详情缓存
     */
    public function delInfoCache($user_id, $order_no)
    {
        $r_key  = $this->__getInfoByOrderNoKey($user_id, $order_no);

        by::redis('core')->del($r_key);
    }

    /**
     * @param $sourceData
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog($sourceData): array
    {
        if (
            (empty($sourceData['union']) && empty($sourceData['live_mark']) && empty($sourceData['referer'])) || empty($sourceData['user_id']) ||
            empty($sourceData['order_no']) || empty($sourceData['ctime'])
        ) {
            return [false, '渠道来源，参数错误！'];
        }

        $save = [
            'user_id'   => $sourceData['user_id'],
            'order_no'  => $sourceData['order_no'],
            'union'     => $sourceData['union'],
            'euid'      => $sourceData['euid'] ?? '',
            'price'     => $sourceData['price'] ?? 0,
            'referer'   => $sourceData['referer'] ?? '',
            'live_mark' => $sourceData['live_mark'] ?? '',
            'ctime'     => $sourceData['ctime']
        ];

        $tb = self::tbName($save['ctime']);
        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        $this->delListCache($sourceData['union']);

        return [true, 'ok'];
    }


    /**
     *
     * 查看渠道来源订单列表
     * @param $union
     * @param string $year
     * @param array $input
     * @param bool $base_all
     * @return array
     * @throws Exception
     */
    public function getList($union, string $year = '', array $input=[], bool $base_all = false): array
    {
        $union = CUtil::removeXss($union);
        $year = CUtil::uint($year);
        if (($year > 0 && $year < by::Omain()::DB_TIMEZONE['ST']) || $year > by::Omain()::DB_TIMEZONE['ED']) {
            return [];
        }

        $redis   = by::redis('core');
        $r_key   = $this->__getListKey($union);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $year, json_encode($input));
        $aJson   = $redis->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $time = $year == 0 ? time() : strtotime("{$year}0101");
            $tb   = self::tbName($time);
            list($where, $params) = $this->__getCondition($union, $input);

            $sql   = "SELECT `user_id`,`order_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $aData = !empty($aData) ? $aData : [];

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : self::EXP);
        }

        $list = [];
        foreach ($aData as $v) {
            $or_info = $this->getInfoByOrderNo($v['user_id'], $v['order_no']);
            if (empty($or_info)) {
                continue;
            }
            //获取商品参数
            $o_info = [];
            if($base_all){
                $mOuser     = by::Ouser();
                $o_info = $mOuser -> CommPackageInfo($v['user_id'],$v['order_no']) ?? [];
            }
            $or_info['o_info'] = $o_info;

            $list[] = $or_info;
        }

        return $list;
    }


    /**
     * @param $union
     * @param $input
     * @return array
     */
    private function __getCondition($union, $input): array
    {
        $user_id  = CUtil::uint($input['user_id'] ?? 0);
        $order_no = CUtil::removeXss($input['order_no'] ?? 0);
        $status   = intval($input['status'] ?? -1);
        $stime    = intval($input['stime'] ?? 0);
        $etime    = intval($input['etime'] ?? 0);
        $liveMark = CUtil::removeXss($input['live_mark'] ?? '');
        $euid     = CUtil::removeXss($input['euid'] ?? '');

        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //推荐人ID
        if ($union) {
            $where          .= " AND `union`=:union";
            $params[":union"] = $union;
        }

        //euid
        if (!empty($euid)) {
            $where           .= " AND `euid`=:euid";
            $params[":euid"] = $euid;
        }

        //用户ID
        if ($user_id > 0) {
            $where             .= " AND `user_id`=:user_id";
            $params[":user_id"] = $user_id;
        }

        //订单号
        if (!empty($order_no)) {
            $where              .= " AND `order_no`=:order_no";
            $params[":order_no"] = $order_no;
        }


        if ($status == by::Omain()::ORDER_STATUS['CANCELED']) {
            $where            .= " AND `status`>:status";
            $params[":status"] = by::Omain()::ORDER_STATUS['CANCELED'];
        }

        if ($stime && $etime){
            $where            .= " AND (`ctime`>:stime AND `ctime`< :etime)";
            $params[":stime"] = $stime;
            $params[":etime"] = $etime;
        }

        //直播标识
        if (!empty($liveMark)) {
            $where              .= " AND `live_mark`=:live_mark";
            $params[":live_mark"] = $liveMark;
        }

        return [$where, $params];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array|DataReader
     * @throws Exception
     * 获取来源订单详情
     */
    public function getInfoByOrderNo($user_id, $order_no, $format_price=true)
    {
        $user_id = CUtil::uint($user_id);
        if($user_id == 0 || empty($order_no)) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByOrderNoKey($user_id, $order_no);
        $json       = $redis->get($redis_key);
        $info       = (array)json_decode($json,true);

        if($json === false){
            $time   = by::Omain()->GetTbNameByOrderId($order_no, false);
            $tb     = $this->tbName($time);
            $fields = implode("`,`",$this->tb_fields);
            $input = ['user_id'=>$user_id,'order_no'=>$order_no];
            list($where, $param) = $this->__getCondition('',$input);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql,$param)->queryOne();
            $info   = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX'=>empty($info) ? 10 : self::EXP]);
        }

        if ($format_price && $info) {
            $info['price']     = $this->totalFee($info['price']??0, 1);
        }

        return $info;
    }

    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public function totalFee($price, int $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }



}
