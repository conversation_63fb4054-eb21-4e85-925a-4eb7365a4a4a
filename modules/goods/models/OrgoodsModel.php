<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 退款商品表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class OrgoodsModel extends CommModel {

    public $tb_fields = [
        'id','refund_no','order_no','user_id','og_id','goods_type'
    ];

    public static function tbName($order_no): string
    {
        $time = by::Omain()->GetTbNameByOrderId($order_no, false);
        $year = date("Y",intval($time));
        return  "`db_dreame_goods`.`t_orefund_g_{$year}`";
    }

    public static function tbNameByTime($time): string
    {
        $year = date("Y",intval($time));
        return  "`db_dreame_goods`.`t_orefund_g_{$year}`";
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return string
     * 缓存KEY
     */
    private function __getListKey($user_id,$refund_no): string
    {
        return AppCRedisKeys::getLgByRefundNo($user_id,$refund_no);
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return int
     * 缓存清理
     */
    private function __delCache($user_id,$refund_no): int
    {
        $r_key = $this->__getListKey($user_id,$refund_no);

        return  by::redis('core')->del($r_key);
    }

    /**
     * @param string $order_no
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(string $order_no, array $arr): array
    {
        $refund_no  = $arr['refund_no'] ?? '';
        $user_id    = $arr['user_id']   ?? '';
        $og_ids     = $arr['og_ids']   ?? '';
        $goods_type = CUtil::uint($arr['goods_type']   ?? '');

        $tb         = self::tbName($order_no);

        foreach($og_ids as $og_id) {
            $save[]     = [
                'refund_no'     => $refund_no,
                'order_no'      => $order_no,
                'user_id'       => $user_id,
                'og_id'         => $og_id,
                'goods_type'    => $goods_type,
            ];
        }

        $columns        = array_keys(reset($save));
        by::dbMaster()->createCommand()->batchInsert($tb, $columns, $save)->execute();

        $this->__delCache($user_id, $refund_no);

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @param $order_no
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 根据退款单号获取数据
     */
    public function GetListByRefundNo($user_id, $refund_no, $order_no)
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getListKey($user_id,$refund_no);
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {

            $tb         = self::tbName($order_no);
            $fields     = implode("`,`",$this->tb_fields);
            $sql        = "SELECT `{$fields}` FROM {$tb} WHERE `refund_no`=:refund_no AND `user_id` = :user_id";
            $aData      = by::dbMaster()->createCommand($sql,[':refund_no'=>$refund_no, ':user_id'=>$user_id])->queryAll();

            $redis->set($r_key, json_encode($aData), ['EX' => 600]);
        }

        return $aData;
    }

}
