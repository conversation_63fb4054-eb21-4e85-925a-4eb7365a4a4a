<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 用户邀请排行榜模型
 * 
 * @property int $id 主键
 * @property int $user_id 用户ID
 * @property int $invite_count 邀请人数
 * @property int $is_invite_ambassador 是否为邀请推广大使：0-否，1-是
 * @property int $created_at 创建时间(秒时间戳)
 * @property int $updated_at 更新时间(秒时间戳)
 */
class UserInviteRankModel extends CommModel
{
    public $tb_fields = [
        'id', 'user_id', 'invite_count', 'is_invite_ambassador', 'created_at', 'updated_at'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_rank`";
    }

    /**
     * 更新用户邀请人数统计
     * @param int $userId 用户ID
     * @param int $incrementCount 增加的邀请人数，默认为1
     * @return bool
     * @throws Exception
     */
    public function updateInviteCount(int $userId, int $incrementCount = 1): bool
    {
        $now = time();
        
        $sql = "INSERT INTO " . self::tbName() . " 
                (user_id, invite_count, created_at, updated_at)
                VALUES (:user_id, :invite_count, :created_at, :updated_at)
                ON DUPLICATE KEY UPDATE 
                invite_count = invite_count + VALUES(invite_count),
                updated_at = VALUES(updated_at)";
        
        $params = [
            ':user_id' => $userId,
            ':invite_count' => $incrementCount,
            ':created_at' => $now,
            ':updated_at' => $now,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 更新用户的追觅推广大使状态
     * @param int $userId 用户ID
     * @param int $isAmbassador 是否为推广大使：0-否，1-是
     * @return bool
     * @throws Exception
     */
    public function updateAmbassadorStatus(int $userId, int $isAmbassador = 1): bool
    {
        $now = time();
        
        $sql = "UPDATE " . self::tbName() . " 
                SET is_invite_ambassador = :is_invite_ambassador, updated_at = :updated_at
                WHERE user_id = :user_id";
        
        $params = [
            ':user_id' => $userId,
            ':is_invite_ambassador' => $isAmbassador,
            ':updated_at' => $now,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取用户邀请排行榜
     * @param int $limit 返回条数，默认100
     * @return array
     * @throws Exception
     */
    public function getRankList(int $limit = 100): array
    {
        $sql = "SELECT uir.*, u.nick_name, u.avatar 
                FROM " . self::tbName() . " uir
                LEFT JOIN `db_dreame`.`t_users_mall` u ON uir.user_id = u.id
                WHERE uir.invite_count > 0
                ORDER BY uir.invite_count DESC, uir.updated_at ASC
                LIMIT :limit";
        
        $params = [':limit' => $limit];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 获取用户在排行榜中的排名
     * @param int $userId 用户ID
     * @return array
     * @throws Exception
     */
    public function getUserRank(int $userId): array
    {
        // 先获取用户的邀请人数
        $userRecord = $this->getUserRecord($userId);
        if (empty($userRecord)) {
            return [
                'rank' => 0,
                'invite_count' => 0,
                'total_users' => 0
            ];
        }

        // 获取排名
        $sql = "SELECT COUNT(*) + 1 as rank 
                FROM " . self::tbName() . " 
                WHERE invite_count > :invite_count 
                OR (invite_count = :invite_count AND updated_at < :updated_at)";
        
        $params = [
            ':invite_count' => $userRecord['invite_count'],
            ':updated_at' => $userRecord['updated_at']
        ];

        $rankResult = by::dbMaster()->createCommand($sql, $params)->queryOne();
        $rank = (int)($rankResult['rank'] ?? 0);

        // 获取总参与人数
        $totalSql = "SELECT COUNT(*) as total FROM " . self::tbName() . " WHERE invite_count > 0";
        $totalResult = by::dbMaster()->createCommand($totalSql)->queryOne();
        $total = (int)($totalResult['total'] ?? 0);

        return [
            'rank' => $rank,
            'invite_count' => (int)$userRecord['invite_count'],
            'total_users' => $total
        ];
    }

    /**
     * 获取用户邀请记录
     * @param int $userId 用户ID
     * @return array
     * @throws Exception
     */
    public function getUserRecord(int $userId): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " WHERE user_id = :user_id";
        $params = [':user_id' => $userId];

        return by::dbMaster()->createCommand($sql, $params)->queryOne() ?: [];
    }

    /**
     * 重新计算用户的邀请人数统计（修复数据用）
     * @param int $userId 用户ID
     * @return bool
     * @throws Exception
     */
    public function recalculateUserInviteCount(int $userId): bool
    {
        $userInviteModel = by::UserInviteModel();
        
        // 统计用户总邀请人数
        $sql = "SELECT COUNT(*) as total_count FROM " . $userInviteModel::tbName() . " 
                WHERE inviter_id = :inviter_id";
        
        $params = [':inviter_id' => $userId];
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        $totalCount = (int)($result['total_count'] ?? 0);

        // 更新排行榜数据
        $now = time();
        $updateSql = "INSERT INTO " . self::tbName() . " 
                      (user_id, invite_count, created_at, updated_at)
                      VALUES (:user_id, :invite_count, :created_at, :updated_at)
                      ON DUPLICATE KEY UPDATE 
                      invite_count = VALUES(invite_count),
                      updated_at = VALUES(updated_at)";
        
        $updateParams = [
            ':user_id' => $userId,
            ':invite_count' => $totalCount,
            ':created_at' => $now,
            ':updated_at' => $now,
        ];

        try {
            $command = by::dbMaster()->createCommand($updateSql, $updateParams);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 批量重新计算所有用户的邀请人数统计（修复数据用）
     * @return bool
     * @throws Exception
     */
    public function recalculateAllUserInviteCount(): bool
    {
        $userInviteModel = by::UserInviteModel();
        
        // 统计所有用户的邀请人数
        $sql = "SELECT inviter_id, COUNT(*) as invite_count 
                FROM " . $userInviteModel::tbName() . " 
                GROUP BY inviter_id
                HAVING invite_count > 0";
        
        $inviteStats = by::dbMaster()->createCommand($sql)->queryAll();
        
        if (empty($inviteStats)) {
            return true;
        }

        $now = time();
        $batchData = [];
        
        foreach ($inviteStats as $stat) {
            $batchData[] = [
                $stat['inviter_id'],
                $stat['invite_count'],
                $now,
                $now
            ];
        }

        try {
            // 先清空排行榜表
            by::dbMaster()->createCommand("TRUNCATE TABLE " . self::tbName())->execute();
            
            // 批量插入新数据
            by::dbMaster()->createCommand()->batchInsert(
                self::tbName(),
                ['user_id', 'invite_count', 'created_at', 'updated_at'],
                $batchData
            )->execute();
            
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }
} 