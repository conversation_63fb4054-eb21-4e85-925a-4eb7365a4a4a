<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;

/**
 * 以旧换新订单表
 */
class TradeInOrderModel extends CommModel
{
    // 表字段
    public $tb_fields = [
        'id', 'order_no', 'trade_in_order_no', 'product', 'status', 'notify', 'ctime', 'utime'
    ];

    /**
     * 表名称
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_trade_in_order`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 订单状态
    const STATUS = [
        'AWAITING_CONFIRMATION'       => 0,    // 等待确认
        'AWAITING_RETURN'             => 5,    // 等待寄回
        'AWAITING_RECEIPT'            => 10,   // 等待签收
        'PENDING_INSPECTION'          => 20,   // 待验机
        'AWAITING_QUOTE_CONFIRMATION' => 30,   // 待确认
        'AWAITING_PAYMENT'            => 50,   // 等待付款
        'AWAITING_RETURN_TO_CUSTOMER' => 60,   // 等待退回
        'ORDER_CLOSED'                => 70,   // 订单关闭
        'ORDER_CANCELED'              => 80,   // 订单取消
        'ORDER_EXCEPTION'             => 90,   // 订单异常
        'ORDER_COMPLETED'             => 100,  // 订单完成
    ];

    // 订单状态名称
    const STATUS_NAME = [
        0   => '等待确认',  // 等待客服与你确认订单
        5   => '等待寄回',  // 用户提交订单后,等待用户回寄机器或者上门回收机器
        10  => '等待签收',  // 验机中心等待收到用户设备
        20  => '待验机',   // 签收后,等待质检中心验机检查确认回收金额
        30  => '待确认',   // 用户确认回收报价
        50  => '等待付款',  // 平台支付回收款项和核销销售订单金额
        60  => '等待退回',  // 等待寄回用户
        70  => '订单关闭',  // 用户下单后,如果超时未回寄或者无效,订单取消
        80  => '订单取消',  // 已退寄用户,订单完结
        90  => '订单异常',  // 订单异常
        100 => '订单完成',  // 回收完成,生成发货订单
    ];

    /**
     * 获取以旧换新订单
     * @param string $order_no
     * @return array
     */
    public function getOrderByOrderNo(string $order_no): array
    {
        $item = self::find()
            ->from(self::tableName())
            ->where(['order_no' => $order_no])
            ->one();
        return $item ? $item->toArray() : [];
    }

    /**
     * 获取以旧换新订单列表
     * @param array $order_nos
     * @return array
     */
    public function getOrderListByOrderNos(array $order_nos): array
    {
        return self::find()
            ->from(self::tableName())
            ->where(['IN', 'order_no', $order_nos])
            ->asArray()
            ->all();
    }

    /**
     * 获取以旧换新订单
     * @param string $order_no
     * @param string $trade_in_order_no
     * @return array
     */
    public function getOrderByOrderNoAndTradeInOrderNo(string $order_no, string $trade_in_order_no): array
    {
        $item = self::find()
            ->from(self::tableName())
            ->where(['order_no' => $order_no, 'trade_in_order_no' => $trade_in_order_no])
            ->one();
        return $item ? $item->toArray() : [];
    }

    /**
     * 保存数据
     * @param int $id
     * @param array $data
     * @return int
     * @throws \yii\db\Exception
     */
    public function store(int $id, array $data): int
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        if ($id) {
            // 更新
            $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
        } else {
            // 添加
            $db->createCommand()->insert($tb, $data)->execute();
            $id = $db->getLastInsertID();
        }
        return $id;
    }

    /**
     * 获取以旧换新订单
     * @param int $id 订单ID
     * @param int $status 订单状态
     * @param int $notify_limit 通知次数限制
     * @param int $limit 查询限制数
     * @param bool $less 通知次数限制（true为小于，false为大于等于）
     * @return array
     */
    public function getOrderListByStatus(int $id, int $status, int $notify_limit, int $limit, bool $less = true): array
    {
        $operator = $less ? '<' : '>=';
        return self::find()
            ->from(self::tableName())
            ->where(['>', 'id', $id])
            ->andWhere(['status' => $status])
            ->andWhere([$operator, 'notify', $notify_limit]) // 根据传入参数决定使用的比较运算符
            ->limit($limit)
            ->asArray()
            ->all();
    }

    // 更新通知次数，将notify的值+1
    public function updateNotify(array $orderNos)
    {
        $db = by::dbMaster();
        $db->createCommand()->update(self::tableName(), ['notify' => new \yii\db\Expression('notify + 1')], ['IN', 'order_no', $orderNos])->execute();
    }
}