<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;
use RedisException;
use Throwable;
use yii\db\DataReader;
use yii\db\Exception;
use yii\db\Query;


class DrawActivityModel extends CommModel
{
    use ModelTrait;
    
    public static function getInstance(): DrawActivityModel
    {
        return byNew::DrawActivity();
    }
    
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_draw_activity`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static $tb_fields = [
        'id', 'module_relation_id', 'name', 'start_time', 'end_time', 'daily_free_times', 'status', 'desc', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'NO'  => 0,
        'YES' => 1
    ];

    // 活动状态
    const STATUS = [
        'ON'  => 1, // 开启
        'OFF' => 2  // 停止
    ];

    private function getActivityDetailByAcId($acId): string
    {
        return AppCRedisKeys::getDrawActivityDetailByAcId($acId);
    }


    public function __delActivityDetailByAcId($acId)
    {
        $redis        = by::redis();
        $redisKey     = $this->getActivityDetailByAcId($acId);
        $redis->del($redisKey);
    }

    /**
     * @param $acId
     * @return array
     * @throws RedisException
     * 通过活动ID获取活动详情
     */
    public function getActivityDetail($acId): array
    {
        $redis    = by::redis();
        $redisKey = $this->getActivityDetailByAcId($acId);

        // 尝试从 Redis 获取活动详情
        $aJson = $redis->get($redisKey);
        if ($aJson) {
            $activityData = (array)json_decode($aJson, true);
        } else {
            // 从数据库获取活动详情
            $activityData = self::find()
                ->select(self::$tb_fields)
                ->where(['id' => $acId, 'is_del' => self::IS_DEL['NO']])
                ->one();
            // 将活动详情存入 Redis
            $activityData = $activityData ? $activityData->toArray() : [];
            $redis->set($redisKey, json_encode($activityData, JSON_UNESCAPED_UNICODE), ['EX' => empty($activityData) ? 10 : 1800]);
        }

        return $activityData;
    }
    
    public function checkDrawActivityNameExists(string $name)
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        $sql = "SELECT `module_relation_id` FROM {$tb} WHERE `name`=:name LIMIT 1";
        $command = $db->createCommand($sql, [':name' => $name]);
        return $command->queryOne();
    }

    public function doCreate(array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $data['ctime'] = time();
            $data['utime'] = time();
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('抽奖活动添加失败');
            }
            
            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

    public function doUpdate(int $module_relation_id, array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $data['utime'] = time();
            $one = self::find()->where(['module_relation_id' => $module_relation_id, 'is_del' => self::IS_DEL['NO']])->limit(1)->asArray()->one();
            if (empty($one)) {
                throw new BusinessException('抽奖活动数据不存在');
            }
            $id = $one['id'];
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException('抽奖活动保存失败');
            }

            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

}
