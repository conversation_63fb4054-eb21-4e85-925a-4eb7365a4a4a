<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 用户商品推荐明细表模型
 */
class GoodsRecommendUsersModel extends CommModel
{
    // 删除状态
    const IS_DELETE = [
        'NO' => 0,  // 未删除
        'YES' => 1  // 已删除
    ];

    public $tb_fields = [
        'id', 'user_id', 'gid', 'sid', 'uid', 'create_time', 'delete_time', 'is_delete'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`goods_recommend_users`";
    }

    /**
     * 获取用户推荐列表缓存键
     * @param int $userId
     * @param int $page
     * @param int $pageSize
     * @return string
     */
    private function __getUserRecommendListKey(int $userId, int $page, int $pageSize): string
    {
        return AppCRedisKeys::getUserRecommendListKey($userId, $page, $pageSize);
    }

    /**
     * 删除用户推荐列表缓存
     * @param int $userId
     */
    private function __delUserRecommendListCache(int $userId)
    {
        $redis = by::redis('core');
        $pattern = AppCRedisKeys::getUserRecommendListKey($userId, '*', '*');
        $keys = $redis->keys($pattern);
        if (!empty($keys)) {
            $redis->del($keys);
        }
    }

    /**
     * 用户推荐商品
     * @param int $userId
     * @param int $gid
     * @param int $sid
     * @param string $uid
     * @return array
     * @throws Exception
     */
    public function recommendGoods(int $userId, int $gid, int $sid, string $uid = ''): array
    {
        // 强化参数验证
        if ($userId <= 0 || $gid <= 0 || $sid < 0) {
            return [false, '参数格式错误'];
        }

        // 检查是否已经推荐过（双重检查，防止并发问题）
        $existing = $this->getUserRecommendStatus($userId, $gid, $sid);
        if ($existing) {
            return [false, '您已经推荐过该商品'];
        }

        // 检查用户今日推荐次数限制
//        $dailyLimit = 50; // 每日最多推荐50个商品
//        $today = date('Y-m-d');
//        $todayCount = $this->getUserDailyRecommendCount($userId, $today);
//        if ($todayCount >= $dailyLimit) {
//            return [false, '今日推荐次数已达上限'];
//        }

        $tb = $this->tbName();
        $data = [
            'user_id' => $userId,
            'gid' => $gid,
            'sid' => $sid,
            'uid' => trim($uid),
            'create_time' => time(),
            'is_delete' => self::IS_DELETE['NO']
        ];

        $db = by::dbMaster();
        $trans = $db->beginTransaction();

        try {
            // 插入用户推荐记录
            $result = $db->createCommand()->insert($tb, $data)->execute();
            if (!$result) {
                throw new \Exception('推荐记录插入失败');
            }

            // 增加推荐总次数
            $goodsRecommendModel = new GoodsRecommendModel();
            $incrementResult = $goodsRecommendModel->incrementRecommendTimes($gid, $sid);
            if (!$incrementResult) {
                throw new \Exception('推荐次数更新失败');
            }

            $trans->commit();
            // 清除用户推荐列表缓存
//            $this->__delUserRecommendListCache($userId);

            return [true, '推荐成功'];

        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录详细错误信息
            CUtil::writeLog('recommend_goods_error', 0, "userId: {$userId}, gid: {$gid}, error: " . $e->getMessage(), $userId, 'error', __METHOD__);
            return [false, '推荐失败，请稍后再试'];
        }
    }

    /**
     * 取消推荐商品（改进版本）
     * 使用 UPSERT 模式避免唯一键冲突
     * @param int $userId
     * @param int $gid
     * @param int $sid
     * @return array
     * @throws Exception
     */
    public function cancelRecommendGoodsV2(int $userId, int $gid, int $sid): array
    {
        // 检查是否已经推荐过
        if (!$this->getUserRecommendStatus($userId, $gid, $sid)) {
            return [false, '您还未推荐该商品'];
        }

        $tb = $this->tbName();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();

        try {
            // 硬删除用户推荐记录
            $sql = "DELETE FROM {$tb} 
                    WHERE user_id = :user_id AND gid = :gid AND sid = :sid";

            $params = [
                ':user_id' => $userId,
                ':gid' => $gid,
                ':sid' => $sid
            ];

            $result = $db->createCommand($sql, $params)->execute();
            if (!$result) {
                throw new \Exception('取消推荐记录更新失败');
            }

            // 减少推荐总次数
            $goodsRecommendModel = new GoodsRecommendModel();
            $decrementResult = $goodsRecommendModel->decrementRecommendTimes($gid, $sid);
            if (!$decrementResult) {
                throw new \Exception('推荐次数更新失败');
            }

            $trans->commit();

            // 清除用户推荐列表缓存
//            $this->__delUserRecommendListCache($userId);

            return [true, '取消推荐成功'];

        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录详细错误信息
            $errorMsg = "userId: {$userId}, gid: {$gid}, sid: {$sid}, error: " . $e->getMessage();
            CUtil::writeLog('cancel_recommend_v2_error', 0, $errorMsg, $userId, 'error', __METHOD__);
            
            return [false, '取消推荐失败，请稍后再试'];
        }
    }

    /**
     * 获取用户推荐状态
     * @param int $userId
     * @param int $gid
     * @param int $sid
     * @return bool
     * @throws Exception
     */
    public function getUserRecommendStatus(int $userId, int $gid, int $sid): bool
    {
        $tb = $this->tbName();
        $sql = "SELECT id FROM {$tb} 
                WHERE user_id = :user_id AND gid = :gid AND sid = :sid AND is_delete = :is_delete 
                LIMIT 1";
        
        $params = [
            ':user_id' => $userId,
            ':gid' => $gid,
            ':sid' => $sid,
            ':is_delete' => self::IS_DELETE['NO']
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return !empty($result);
    }

    /**
     * 获取用户推荐商品列表
     * @param int $userId
     * @param int $page
     * @param int $pageSize
     * @return array
     * @throws Exception
     */
    public function getUserRecommendList(int $userId, int $page = 1, int $pageSize = 10): array
    {
        // 检查缓存
//        $redis = by::redis('core');
//        $cacheKey = $this->__getUserRecommendListKey($userId, $page, $pageSize);
//        $cached = $redis->get($cacheKey);
//        if ($cached !== false) {
//            return json_decode($cached, true);
//        }

        $tb = $this->tbName();
        $offset = ($page - 1) * $pageSize;

        // 查询用户推荐的商品列表
        $sql = "SELECT gid, sid, create_time FROM {$tb} 
                WHERE user_id = :user_id AND is_delete = :is_delete 
                ORDER BY create_time DESC 
                LIMIT :offset, :page_size";
        
        $params = [
            ':user_id' => $userId,
            ':is_delete' => self::IS_DELETE['NO'],
            ':offset' => $offset,
            ':page_size' => $pageSize
        ];

        $data = by::dbMaster()->createCommand($sql, $params)->queryAll();

        // 查询总数
        $countSql = "SELECT COUNT(*) as total FROM {$tb} 
                     WHERE user_id = :user_id AND is_delete = :is_delete";
        $countParams = [
            ':user_id' => $userId,
            ':is_delete' => self::IS_DELETE['NO']
        ];
        $countResult = by::dbMaster()->createCommand($countSql, $countParams)->queryOne();
        $total = (int)($countResult['total'] ?? 0);

        $result = [
            'list' => $data,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'pages' => CUtil::getPaginationPages($total, $pageSize)
        ];

        // 缓存结果
//        $redis->setex($cacheKey, 1800, json_encode($result)); // 缓存30分钟

        return $result;
    }

    /**
     * 批量获取用户推荐状态
     * @param int $userId
     * @param array $goodsList 格式：[['gid' => gid, 'sid' => sid], ...]
     * @return array 格式：['gid_sid' => bool, ...]
     * @throws Exception
     */
    public function batchGetUserRecommendStatus(int $userId, array $goodsList): array
    {
        if (empty($goodsList)) {
            return [];
        }

        $tb = $this->tbName();
        $conditions = [];
        $params = [':user_id' => $userId, ':is_delete' => self::IS_DELETE['NO']];

        foreach ($goodsList as $index => $goods) {
            $gid = (int)$goods['gid'];
            $sid = (int)$goods['sid'];
            $conditions[] = "(gid = :gid{$index} AND sid = :sid{$index})";
            $params[":gid{$index}"] = $gid;
            $params[":sid{$index}"] = $sid;
        }

        $sql = "SELECT gid, sid FROM {$tb} 
                WHERE user_id = :user_id AND is_delete = :is_delete AND (" . implode(' OR ', $conditions) . ")";

        $data = by::dbMaster()->createCommand($sql, $params)->queryAll();

        // 构建结果数组
        $result = [];
        foreach ($goodsList as $goods) {
            $gidSid = $goods['gid'] . '_' . $goods['sid'];
            $result[$gidSid] = false;
        }

        foreach ($data as $row) {
            $gidSid = $row['gid'] . '_' . $row['sid'];
            $result[$gidSid] = true;
        }

        return $result;
    }

    /**
     * 获取用户今日推荐次数
     * @param int $userId
     * @param string $date
     * @return int
     * @throws Exception
     */
    private function getUserDailyRecommendCount(int $userId, string $date): int
    {
        $tb = $this->tbName();
        $startTime = strtotime($date . ' 00:00:00');
        $endTime = strtotime($date . ' 23:59:59');
        
        $sql = "SELECT COUNT(*) FROM {$tb} 
                WHERE user_id = :user_id 
                AND create_time BETWEEN :start_time AND :end_time 
                AND is_delete = :is_delete";
        
        $params = [
            ':user_id' => $userId,
            ':start_time' => $startTime,
            ':end_time' => $endTime,
            ':is_delete' => self::IS_DELETE['NO']
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryScalar();
        return intval($result);
    }
} 