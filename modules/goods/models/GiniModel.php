<?php

/**
 * 商品自定义价格
 */

namespace app\modules\goods\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class GiniModel extends CommModel
{

    public $tb_fields = [
        'id', 'sku', 'stock', 'sales','wait','no_limit', 'tag', 'status', 'iniprice', 'stime', 'etime', 'ctime', 'utime', 'is_del'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_gini`";
    }


    const INI_GOODS_PRICE_TAB_STATUS = [
        'tab_1' => [
            0, 1, 2
        ],
        'tab_2' => [
            3, 99
        ],
    ];


    /**
     * @return string
     * 获取自定义价格列表KEY
     */
    private function __getIniPriceListKey(): string
    {
        return AppCRedisKeys::getIniPriceList();
    }

    /**
     * @return string
     * 获取唯一自定义价格
     */
    private function __getOneIniKey($id): string
    {
        return AppCRedisKeys::getOneIniKey($id);
    }

    /**
     * @return string
     * 获取生效中的自定义价格列表
     */
    private function __getEffectIniListKey(): string
    {
        return AppCRedisKeys::getEffectIniListKey();
    }

    /**
     * @return string
     * 获取生效中商品自定义价格
     */
    private function __getEffectSkuIniKey($sku): string
    {
        return AppCRedisKeys::getEffectSkuIniKey($sku);
    }


    /**
     * @param $gini_id
     * @return string
     * 库存缓存KEY
     */
    private function __getIniStockKey($gini_id): string
    {
        return AppCRedisKeys::gIniStock($gini_id);
    }

    /**
     * @param $gini_id
     * @return string
     * 销量缓存KEY
     */
    private function __getIniSalesKey($gini_id): string
    {
        return AppCRedisKeys::gIniSales($gini_id);
    }

    /**
     * @param $gini_id
     * @return string
     * 未付款数缓存KEY
     */
    private function __getIniWaitKey($gini_id): string
    {
        return AppCRedisKeys::gIniWait($gini_id);
    }


    /**
     * @param $id
     * @return void
     * 缓存清理
     * @throws Exception
     */
    private function __delCache($id)
    {
        $r_key1 = $this->__getOneIniKey($id);

        by::redis('core')->del($r_key1);
    }

    /**
     * @return int
     * 清理缓存列表(每种类型都要清理)
     */
    public function __delListCache(): int
    {
        $r_key = $this->__getIniPriceListKey();
        return by::redis('core')->del($r_key);
    }


    /**
     * @return int
     * 删除生效中的sku价格缓存
     */
    public function __delOneBySku($sku):int
    {
        $r_sku = $this->__getEffectSkuIniKey($sku);
        return by::redis('core')->del($r_sku);
    }

    /**
     * @param $gini_id
     * @return void
     * 删除库存、销量、未付款数缓存
     */
    public function DelAllIniCache($gini_id)
    {
        $r_key1 = $this->__getIniStockKey($gini_id);
        $r_key2 = $this->__getIniSalesKey($gini_id);
        $r_key3 = $this->__getIniWaitKey($gini_id);

        by::redis()->del($r_key1, $r_key2, $r_key3);
    }

    /**
     * @param int $gini_id
     * @param string $opt
     * @param int $num
     * @return array|int
     * @throws Exception
     * 获取运行中的自定义价格库存
     *
     */
    public function OptIniStock(int $gini_id,string $opt = 'GET', int $num = 0)
    {
        $gini_id = CUtil::uint($gini_id);
        $r_key      = $this->__getIniStockKey($gini_id);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $stock      = $redis->get($r_key);
                if ($stock === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `stock` FROM {$tb} WHERE `id`=:gini_id  LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gini_id' => $gini_id])->queryOne();
                    $stock  = $aLog['stock'] ?? 0;
                    $redis->set($r_key, $stock, ['NX', 'EX' => 600]);
                }
                return intval($stock);
                break;
            case 'INCR':
                !$redis->exists($r_key) &&  $this->OptIniStock($gini_id);
                $s_num  = $redis->incrBy($r_key, $num);
                if ($s_num < 0) {
                    $num < -1 && $this->OptIniStock($gini_id,'DEL');
                    return [false, '库存不足(6)'];
                }
                return [true, 'ok'];
                break;
            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }


    /**
     * @param int $gini_id
     * @param string $opt
     * @param int $num
     * @param bool $positive
     * @return array|int
     * @throws Exception
     * 获取正在使用中已发放的库存
     *
     */
    public function OptIniSales(int $gini_id,string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gini_id     = CUtil::uint($gini_id);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getIniSalesKey($gini_id);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $sales      = $redis->get($r_key);

                if ($sales === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `sales` FROM {$tb} WHERE `id`=:gini_id LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gini_id' => $gini_id])->queryOne();

                    $sales  = $aLog['sales'] ?? 0;
                    $redis->set($r_key, $sales, ['NX', 'EX' => 600]);
                }

                return intval($sales);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param int $gini_id
     * @param string $opt
     * @param int $num
     * @param bool $positive
     * @return array|int
     * @throws Exception
     * 获取未付款的正在进行的商品
     */
    public function OptIniWait(int $gini_id,string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gini_id        = CUtil::uint($gini_id);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getIniWaitKey($gini_id);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $wait      = $redis->get($r_key);

                if ($wait === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `wait` FROM {$tb} WHERE `id`=:gini_id  LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gini_id' => $gini_id])->queryOne();

                    $wait  = $aLog['wait'] ?? 0;
                    $redis->set($r_key, $wait, ['NX', 'EX' => 600]);
                }

                return intval($wait);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }


    /**
     * @param $sku
     * @param $stock
     * @return array
     * @throws Exception
     * 同步自定义价格库存
     *
     */
    public function SyncIniStock($sku,$stock)
    {
        //同步库存，按照sku进行同步库存
        //1.查找正在进行或者后面待执行的自定义价格进行同步库存
        //获取有效的自定义价格列表
        $input = [
            'statuses' =>[0,1,2],
            'sku' => $sku,
            'begin_time' =>intval(START_TIME),
        ];
        $aData = $this -> GetList($input,1,0);
        if($aData && intval($stock)>0){
            foreach ($aData as $giniId){
                $iniInfo = $this->GetOneById($giniId);
                $noLimit = $iniInfo['no_limit'] ?? 0;
                //更新库存
                if($noLimit){
                    $this->UpdateStock($giniId, $stock, 'SET');
                }
            }
        }

        return [true, 'ok'];
    }


    /**
     * @param int $giniId
     * @param string $opt
     * @param int $num
     * @param bool $positive
     * @return array
     * @throws \yii\db\Exception
     * 库存操作 ROLL:商品购买及回滚 SET：设置库存（同步库存用）SALE：更改销量（退款） WAIT：更改未付款数
     */
    public function UpdateStock($giniId, $num = 0, $opt = 'ROLL', bool $positive = true)
    {
        CUtil::debug($giniId.'|'.$num.'|'.$opt.'|'.$positive,'define.list');
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;

        $tb         = self::tbName();

        switch ($opt) {
            case 'ROLL':
                //1、先更新redis库存数据-第二次校验库存
                list($s, $m) = $this->OptIniStock($giniId, 'INCR', -$num);
                if (!$s) {
                    return [false, $m];
                }

                //2、更新数据库数据-第三次数据库校验库存-库存字段非负
                $sql    = "UPDATE {$tb} SET `stock`=`stock`-(:num),`sales`=`sales`+(:num),`wait`=`wait`+(:num)
                           WHERE `id` = :giniId  AND `stock`-(:num) >= 0 LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':giniId' => $giniId])->execute();

                if ($u_num == 0) {
                    return [false, '库存不够(3)'];
                }

                //更新销量缓存
                $this->OptIniSales($giniId, 'INCR', $num, $positive);
                //更新未付款缓存
                $this->OptIniWait($giniId, 'INCR', $num, $positive);

                break;

            case 'SET':
                $num        = $num < 0 ? 0 : $num;
                $wait       = $this->OptIniWait($giniId);

                //如果erp库存比未付款数还小，则更新库存为0
                if ($wait >= $num) {
                    $num        = 0;
                    $set_str    = " :num";
                } else {
                    $set_str    = " :num - `wait`";
                }

                $sql    = "UPDATE {$tb} SET `stock` = {$set_str} WHERE `id` = :giniId  LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':giniId' => $giniId])->execute();

                if ($u_num > 0) {
                    $this->optIniStock($giniId, 'DEL');
                }

                break;

            case 'SALE':
                $sql = "UPDATE {$tb} SET `sales` = `sales` + (:num) WHERE `id` = :giniId  LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':giniId' => $giniId])->execute();
                //删除销量缓存
                $this->OptIniSales($giniId, 'DEL');
                break;

            case 'WAIT':
                $wait = $this->OptIniWait($giniId);

                if ($wait <= $num) {
                    $set_str    = " :num";
                    $num        = 0;
                } else {
                    $set_str = " `wait` - :num";
                }

                $sql = "UPDATE {$tb} SET `wait` = {$set_str} WHERE `id` = :giniId  LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':giniId' => $giniId])->execute();
                //删除未付款缓存
                $this->OptIniWait($giniId, 'DEL');
                break;
        }

        return [true, 'ok'];
    }




    /**
     * @throws Exception
     */
    public function GetList($input, $page = 1, $page_size = 50, $cache = false): array
    {
        $r_key = $this->__getIniPriceListKey();

        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, json_encode($input));

        $aJson = $cache ? by::redis('core')->hGet($r_key, $sub_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `stime` DESC,`utime` DESC,`ctime` DESC,`id` DESC,`etime` DESC";

            if ($page_size) {
                $sql .= " LIMIT {$offset},{$page_size}";
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            $cache && by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            $cache && CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }


    public function GetListCount($input)
    {

        $r_key   = $this->__getIniPriceListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, json_encode($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return intval($count);
    }


    private function __getCondition($input): array
    {
        //字段解析
        $id        = intval($input['id'] ?? 0);
        $sku       = trim($input['sku'] ?? '');
        $status    = intval($input['status'] ?? -1);
        $settime   = intval($input['settime'] ?? 0);
        $limittime = intval($input['limittime'] ?? 0);
        $beginTime = intval($input['begin_time'] ?? 0);
        $tab       = intval($input['tab'] ?? -1);
        $tag       = trim($input['tag'] ?? '');
        $statuses  = $input['statuses'] ?? [];
        $nid       = intval($input['nid'] ?? 0);
        $stime     = intval($input['stime']??0);
        $etime     = intval($input['etime']??0);


        //初始化查询
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;
        if (!empty($id)) {
            $where         .= " AND `id` = :id";
            $params[":id"] = $id;
        }


        if (!empty($sku)) {
            $where          .= " AND `sku` = :sku";
            $params[":sku"] = $sku;
        }


        if ($status >= 0) {
            $where             .= " AND `status` = :status";
            $params[":status"] = intval(!!$status);
        }

        if (is_array($statuses) && !empty($statuses)) {
            $statuses = implode("','", $statuses);
            $where    .= " AND `status` IN ('{$statuses}')";
        }

        if ($tab >= 0) {
            $statusTabs = self::INI_GOODS_PRICE_TAB_STATUS['tab_' . $tab] ?? [];
            if ($statusTabs) {
                $statuses1 = implode("','", $statusTabs);
                $where     .= " AND `status` IN ('{$statuses1}')";
            }
        }

        if (!empty($tag)) {
            $where          .= " AND `tag` LIKE :tag";
            $params[":tag"] = "%{$tag}%";
        }

        if (!empty($settime)) {
            $where              .= " AND `stime` <= :settime AND `etime`>= :settime";
            $params[":settime"] = $settime;
        }

        if (!empty($nid)){
            $where .= " AND `id` != {$nid}";
        }

        if (!empty($limittime)) {
            $s_limit              = $limittime - 300;
            $l_limit              = $limittime + 300;
            $where                .= " AND ((`stime` <= :limittime AND `etime`>= :limittime) OR (`stime` <= :s_limit AND `etime`>= :s_limit) OR (`stime` <= :l_limit AND `etime`>= :l_limit))";
            $params[":limittime"] = $limittime;
            $params[":s_limit"]   = $s_limit;
            $params[":l_limit"]   = $l_limit;
        }

        if (!empty($beginTime)){
            $where                .= " AND `etime`>= :beginTime";
            $params[":beginTime"] = $beginTime;
        }

        if($stime && $etime){
            $where                .= " AND ((`stime`>= {$stime} AND `etime`<= {$etime}) OR (`stime`<{$stime} AND `etime`>{$etime}))";
        }


        return [$where, $params];
    }


    public function LockIniPriceList($id)
    {
        //查看该ID是否存在
        $iniInfo = $this->GetOneById($id);
        if (empty($iniInfo)) {
            return [false, "自定义价格不存在，参数不合法！"];
        }
        if ($iniInfo['status'] == 1) {
            return [false, "活动正在进行中，禁止修改！"];
        }
        //获取该商品的所有ID并进行锁定，清除缓存
        $queryData = ['id'=> $id,];
        $saveData  = [
            'status' => 2,
            'utime'  => time()
        ];
        $this->__batchCheckIniData($queryData, $saveData);

        //再次请求自定义价格详情
        $iniInfo = $this->GetOneById($id);
        return [true, $iniInfo];
    }

    public function __batchCheckIniData($queryData, $saveData)
    {
        $aData = $this->GetList($queryData, 1, 0);
        $db    = by::dbMaster();
        $tb    = $this->tbName();
        if ($aData && $saveData) {
            foreach ($aData as $id) {
                //更改状态
                $db->createCommand()->update($tb, $saveData, "`id`=:id", [":id" => $id])->execute();
                //清除缓存
                $this->__delCache($id);
            }
        }
        $this->__delListCache();
        return true;
    }


    /**
     * 获取指定价格详细信息
     * @param $id
     * @return array|\yii\db\DataReader
     * @throws Exception
     */

    public function GetOneById($id,$format_price = true)
    {
        $id = CUtil::uint($id);
        if ($id <= 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOneIniKey($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb            = $this->tbName();
            $fields        = implode("`,`", $this->tb_fields);
            $where         = "id = :id";
            $params[':id'] = $id;
            $sql           = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $aData         = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $aData         = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        if ($format_price == true) {
            $aData['iniprice']     = $this->totalFee($aData['iniprice'], 1);
        }

        return $aData;
    }


    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 商品主自定义价格修改
     */
    public function SaveIni(array $aData): array
    {
        //校验主表参数
        $id       = CUtil::uint($aData['id'] ?? 0);
        $sku      = trim($aData['sku'] ?? '');
        $stock     = CUtil::uint($aData['stock'] ?? 0);
        $tag      = CUtil::filterEmoji(trim($aData['tag'] ?? ''));
        $stime    = intval($aData['stime'] ?? 0);
        $etime    = intval($aData['etime'] ?? 0);
        $noLimit  = CUtil::uint($aData['no_limit'] ?? 0);
        $iniprice = $aData['iniprice'] ?? 0;
        $iniprice = sprintf("%.2f", $iniprice);

        $aData = [
            'id'       => $id,
            'stock'    => $stock,
            'sku'      => $sku,
            'tag'      => $tag,
            'stime'    => $stime,
            'etime'    => $etime,
            'no_limit' => $noLimit,
            'iniprice' => $iniprice,
        ];

        list($s, $m) = $this->__check($aData);
        if (!$s) {
            return [false, $m];
        }

        $save = [
            'stock'    => empty($m['stock'] ?? 0) ? $stock : $m['stock'],
            'sku'      => $sku,
            'tag'      => $tag,
            'stime'    => $stime,
            'etime'    => $etime,
            'no_limit' => $noLimit,
            'iniprice' => $this->totalFee($iniprice),
        ];

        if ($id) {
            //编辑判断该自定义价格是否为封锁状态
            $aInfo  = $this->GetOneById($id);
            $status = $aInfo['status'] ?? 0;
            if (empty($aInfo) || $status != 2) {
                return [false, '该自定义价格不是封锁状态，不允许修改'];
            }
        } else {
            $save['ctime'] = intval(START_TIME);
        }

        $save['utime']  = intval(START_TIME);
        $save['status'] = 0;//置为未执行状态

        $db    = by::dbMaster();
        $tb    = $this->tbName();
        $trans = $db->beginTransaction();

        try {
            if ($id) {
                $db->createCommand()->update($tb, $save, "`id`=:id", [":id" => $id])->execute();
                $this->__delCache($id); //todo 清空详情缓存
            } else {
                $db->createCommand()->insert($tb, $save)->execute();
                $id = $db->getLastInsertID();
            }

            //立即开始执行
            $nowTime = intval(START_TIME);
            $newStatus = 0;
            if($stime <= $nowTime && $etime> $nowTime){
                $newStatus = 1;
            }

            //获取最新的自定义价格
            $info = $this ->GetOneById($id,false);
            $redis = by::redis();
            $sitime = $info['stime'] ?? 0;
            $eitime = $info['etime'] ?? 0;
            if($sitime <= $nowTime && $eitime> $nowTime){
                //设置当前的运行的商品
                $sku_key = $this->__getEffectSkuIniKey($sku);
                $redis->set($sku_key, json_encode($info), ['EX' => intval($eitime-$nowTime)]);
            }

            //创建结束之后，把封锁状态的自定义字段全部改成未执行状态，超时的改为执行完成
            $queryData = ['id' => $id];
            $saveData  = [
                'status' => $newStatus,
                'utime'  => time()
            ];
            $this->__batchCheckIniData($queryData, $saveData);

            $trans->commit();


        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gini');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gini');
            return [false, '操作失败'];
        }

        return [true, $id];
    }


    public function forceBreakDown($id)
    {
        $iniInfo = $this->GetOneById($id,false);
        if (empty($iniInfo)) {
            return [false, "自定义价格不存在，参数不合法！"];
        }
        if ($iniInfo['status'] != 1) {
            return [false, "活动不在进行中，请查看是否选择出错！"];
        }
        $db       = by::dbMaster();
        $tb       = $this->tbName();
        $saveData = [
            'status' => 99,
            'utime'  => time()
        ];
        //置为强制失效状态
        $db->createCommand()->update($tb, $saveData, "`id`=:id", [":id" => $id])->execute();
        //清除缓存
        $this->__delCache($id);
        $this->__delListCache();

        //再次请求自定义价格详情
        $iniInfo = $this->GetOneById($id);
        $this->__delOneBySku($iniInfo['sku']??'');
        return [true, $iniInfo];
    }


    /**
     * @param array $aData
     * @return array
     * 校验主表参数
     * @throws Exception
     */
    private function __check(array $aData)
    {
        //商品sku必填
        if (empty($aData['sku'])) {
            return [false, "商品SKU不存在（1）"];
        }

        //商品sku控制
        $gid = $sid = 0;
        $mainSku = by::Gmain()->GetOneBySku($aData['sku']);
        $gid = $mainSku['id'] ?? 0;
        if (empty($mainSku)) {
            $mainSku = by::Gspecs()->GetOneBySku($aData['sku']);
            $sid = $mainSku['id'] ?? 0;
            $gid = $mainSku['gid'] ?? 0;
        }

        if (empty($mainSku)) {
            return [false, "商品SKU不存在（2）"];
        }

        //标签必填
        if(empty($aData['tag']??'')){
            return [false, "商品标签必填！"];
        }

        //商品数量控制
        if(empty($gid) && empty($sid)){
            return [false, "商品不存在"];
        }
        $stock = $aData['stock'] ?? 0;

        $sku      = by::GoodsStockModel()->GetSkuByGidAndSid($gid, $sid, by::GoodsStockModel()::SOURCE['MAIN']);
        $hasStock = empty($sku) ? 0 : by::GoodsStockModel()->OptStock($sku);
        if($stock > intval($hasStock)){
            return [false, "主商品库存不足！请先同步主商品库存"];
        }

        if($aData['no_limit']){
            $aData['stock'] = $hasStock;
        }

        //价格控制
        if (empty($aData['iniprice'])) {
            return [false, "自定义价格必填"];
        }

        $iniprice = $this->totalFee($aData['iniprice']);

        $gInfo = by::Gmain()->GetOneByGidSid($gid, $sid, false, true, 0);
        if($sid){
            $mprice   = $gInfo['spec']['price'] ?? 0;
        }else{
            $mprice   = $gInfo['mprice'] ?? 0;
        }

        $is_internal_purchase = $gInfo['is_internal_purchase'] ?? 0;
        if($is_internal_purchase){
            return [false, "内购商品不得参与自定义价格！"];
        }

        // 配件专区商品
        $mainTag = by::Gtag()->GetMainTag();
        $tagCode = by::Gtag()->GetTagCodeMap();
        // if (in_array(GtagModel::TAG['FITTINGS'], $gInfo['tids']) && // 标签包含配件专区
        if (in_array($tagCode['FITTINGS'], $gInfo['tids']) && // 标签包含配件专区
            // empty(array_intersect(GtagModel::MAIN_TAG, $gInfo['tids'])) && // 标签不包含主商品标签
            empty(array_intersect($mainTag, $gInfo['tids'])) && // 标签不包含主商品标签
            $mprice >= 100 // 价格大于等于1元
        ) {
            if ($iniprice < 100) {
                return [false, "配件专区价格大于等于1元的商品，自定义价格不能小于1元！"];
            }
        } else {
            if ($iniprice < intval($mprice / 2)) {
                return [false, "自定义价格不能小于初始价的50%！"];
            }
        }

        //生效时间和截止时间
        $stime = $aData['stime'] ?? 0;
        $etime = $aData['etime'] ?? 0;
        if (empty($stime) || empty($etime)) {
            return [false, "生效时间不能为空！"];
        }

        if ($stime >= $etime) {
            return [false, "生效开始时间不能小于生效截止时间！"];
        }

        if ($etime <= ($stime + 300)) {
            return [false, "生效时间不能小于5分钟！"];
        }

        //生效截止时间
        if ($etime <= (time() + 5 * 60)) {
            return [false, "生效截止时间必须大于目前时间5分钟！"];
        }

        //时间区间校验 获取所有目前的时间区域
        $sta = $this->GetListCount(['sku' => $aData['sku'], 'statuses' => [0, 1, 2], 'limittime' => $stime, 'nid'=> $aData['id']?? 0]);
        if ($sta > 0) {
            return [false, "生效开始时间有5分钟之内重叠！"];
        }

        $eda = $this->GetListCount(['sku' => $aData['sku'], 'statuses' => [0, 1, 2], 'limittime' => $etime, 'nid'=> $aData['id']?? 0]);
        if ($eda > 0) {
            return [false, "生效结束时间有5分钟之内重叠！"];
        }

        $mta = $this->GetListCount(['sku' => $aData['sku'], 'statuses' => [0, 1, 2], 'stime' => $stime, 'etime'=>$etime, 'nid'=> $aData['id']?? 0]);
        if ($mta > 0) {
            return [false, "活动价格生效期间有重叠"];
        }

        return [true, $aData];
    }


    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public function totalFee($price, $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }


    public function Del($id)
    {
        //查看该ID是否存在
        $iniInfo = $this->GetOneById($id);
        if (empty($iniInfo)) {
            return [false, "自定义价格不存在，参数不合法！"];
        }
        if ($iniInfo['status'] == 1) {
            return [false, "自定义价格生效中不允许删除！"];
        }

        $db = by::dbMaster();
        $tb = self::tbName();

        $trans = $db->beginTransaction();

        try {
            $db->createCommand()->update($tb,
                ['is_del' => 1, 'dtime' => time()],
                ['id' => $id]
            )->execute();

            $trans->commit();

            $this->__delCache($id);
            $this->__delListCache();
            $this->__delOneBySku($iniInfo['sku'] ?? 0);

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.ginidel');
            return [false, '操作失败'];
        }

    }


    /**
     * 实时获取生效的自定义价格列表
     * @return array
     * @throws Exception
     */
    public function getEffectIniList(): array
    {
        $tb = $this->tbName();
        $r_key      = $this->__getEffectIniListKey();
        $redis      = by::redis();
        $aJson      = $redis->get($r_key);
        $originIds = empty($aJson)?[]:(array)json_decode($aJson, true);
        //获取有效的自定义价格列表
        $input = [
            'statuses' =>[0,1],
            'settime' =>intval(START_TIME),
        ];
        list($where, $params) = $this->__getCondition($input);
        $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `etime` DESC,`ctime` DESC,`id` DESC ";
        $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $newIds = empty($aData) ? [] : array_column($aData, "id");
        $newSku = [];
        //异步状态改为执行中（最新数据）
        if($newIds){
            foreach ($newIds as $newId) {
                $info = $this -> GetOneById($newId,false);
                $etime = $info['etime'] ?? 0;
                $now  = intval(START_TIME);
                if ($info && isset($info['sku'])) {
                    $sku_key = $this->__getEffectSkuIniKey($info['sku']);
                    $originStatus = $info['status'] ?? 0;
                    $info['status'] = 1;
                    $redis->set($sku_key, json_encode($info), ['EX' => intval($etime-$now)]);
                    $newSku[] = $info['sku'];
                    //todo 改为执行中状态 (共80个商品故同步进行)
                    $sql    = "UPDATE {$tb} SET `status` = 1 , `utime` = {$now} WHERE `id` = :giniId  LIMIT 1";
                    CUtil::uint($originStatus) == 0 && by::dbMaster()->createCommand($sql, [':giniId' => $newId])->execute();
                    $this->__delCache($newId);
                }
            }
        }
        //异步数据改成已完成（旧数据）
        $oldIds = array_diff($originIds,$newIds);
        if($oldIds){
            $now  = intval(START_TIME);
            foreach ($oldIds as $oldId) {
                //删除对应的sku
                $info = $this -> GetOneById($oldId);
                $originStatus = $info['status'] ?? 0;
                $sku_key = $this->__getEffectSkuIniKey($info['sku']??0);
                !in_array($sku_key,$newSku) && $redis->del($sku_key);
                //todo 状态改为已完成
                $sql    = "UPDATE {$tb} SET `status` = 3 ,`utime`= {$now} WHERE `id` = :giniId  LIMIT 1";
                CUtil::uint($originStatus) == 1 && by::dbMaster()->createCommand($sql, [':giniId' => $oldId])->execute();
                $this->__delCache($oldId);
            }
        }

        //替换对应的自定义价格列表
        $redis->set($r_key, json_encode($newIds), ['EX' => empty($newIds) ? 10 : 3600]);
        $this->__delListCache();

        return $newIds;
    }


    /**
     * @param $aData
     * @param $sku
     * @return mixed
     * @throws Exception
     * 获取商品自定义价格
     */
    public function getGiniPriceBySku($aData,$sku,$format_price,$isInternal=0)
    {
        $isIni = 0;
        $gini_info = [];
        $gini_id = 0;
        $gini_tag = '';
        $gini_etime = 0;
        $redis      = by::redis();
        $sku_key = empty($sku) ? '' : $this->__getEffectSkuIniKey($sku);
        if ($sku_key && empty($isInternal)) {
            $skuJson = $redis->get($sku_key);
            $skuData = empty($skuJson) ? [] : (array)json_decode($skuJson, true);
            if ($skuData && isset($skuData['id'])) {
                //获取库存
                $gini_id = CUtil::uint($skuData['id'] ?? 0);
                $stock   = by::Gini()->OptIniStock($gini_id);
                if($format_price){
                    $skuData['iniprice']     = $this->totalFee($skuData['iniprice']??0, 1);
                }
                if ($stock > 0) {
                    $aData['price'] = floatval($skuData['iniprice'] ?? 0) > 0 ? $skuData['iniprice'] : $aData['price'];
                    $isIni          = 1;
                    $gini_info      = $skuData;
                    $gini_tag       = $skuData['tag'] ?? '';
                    $gini_etime     = $skuData['etime'] ?? 0;
                }else{
                    $gini_id = 0;
                }
            }
        }
        $aData['is_ini']     = $isIni;
        $aData['gini_info']  = $gini_info;
        $aData['gini_id']    = $gini_id;
        $aData['gini_tag']   = $gini_tag;
        $aData['gini_etime'] = $gini_etime;

        return $aData;
    }


}
