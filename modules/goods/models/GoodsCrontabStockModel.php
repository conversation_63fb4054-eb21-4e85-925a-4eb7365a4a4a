<?php
/**
 * 商品库存 定时设置
 */
namespace app\modules\goods\models;

use app\models\BusinessException;
use app\models\by;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;

class GoodsCrontabStockModel  extends CommModel
{

    use  ModelTrait;
    public $tb_fields = [
        'id', 'gid', 'sid', 'stock', 'status', 'trigger_time','type', 'ctime', 'utime'
    ];

    //ModelTrait 必须要定义$fillable属性
    private $fillable = [
        'id', 'gid', 'sid', 'stock', 'status', 'trigger_time', 'type', 'ctime', 'utime'
    ];

    // 状态常量
    const STATUS_DISABLED = 0;  // 关闭
    const STATUS_ENABLED = 1;   // 启用


    /**
     * @return string
     */
    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_goods_crontab_stock`";
    }

    /**
     * 可选：定义属性标签（用于表单、提示等）
     */
    public function attributeLabels()
    {
        return [
            'id' => '主键ID',
            'gid' => '商品ID',
            'sid' => '规格ID',
            'stock' => '预设库存量',
            'trigger_time' => '执行时间',
            'status' => '状态',
            'ctime' => '创建时间',
            'utime' => '更新时间',
        ];
    }

    /**
     * 可选：定义验证规则
     */
    public function rules()
    {
        return [
            [['gid', 'sid','trigger_time'], 'required'],
            [['gid', 'sid', 'stock', 'status', 'ctime', 'utime'], 'integer'],
            ['stock', 'default', 'value' => 0],
            ['status', 'in', 'range' => [0, 1]],
        ];
    }

    /**
     * 可选：在保存时自动更新 ctime/utime
     */
    public function beforeSave($insert)
    {
        if ($insert) {
            $this->ctime = time();
        }
        $this->utime = time();
        return parent::beforeSave($insert);
    }

    /**
     * 删除数据
     * @param array $ids 主键IDs
     * @return array
     */
    public function doDelete(array $ids): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            if (empty($resp)) {
                throw new BusinessException('删除失败');
            }
            return [true, 'success'];
        } catch (\Throwable $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 创建活动
     * @param array $data 创建数据
     * @return array
     */
    public function doCreate(array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $data['ctime'] = time();
            $data['utime'] = time();
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('创建活动失败');
            }

            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (\Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

    /**
     * 更新活动
     * @param int $id 活动ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            unset($data['id']);
            $data['utime'] = time();
            $one                 = self::find()->where(['id' => $id])->limit(1)->asArray()->one();
            if (empty($one)) {
                throw new BusinessException(sprintf('数据[%s]不存在', $id));
            }
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException('更新失败');
            }

            return [true, 'success'];
        } catch (\Throwable $e) {
            return [false, $e->getMessage()];
        }
    }



    private static $_instance;
    public static function getInstance()
    {
        if (! isset(self::$_instance)) {
            self::$_instance = new static();
        }
        return self::$_instance;
    }
}