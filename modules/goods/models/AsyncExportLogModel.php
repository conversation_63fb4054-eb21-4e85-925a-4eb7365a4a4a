<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

/**
 * 异步导出任务
 * @property mixed|null $export_time
 */
class AsyncExportLogModel extends BaseModel
{
    // 缓存时间1小时
    const EXPIRE_TIME = 1 * 60 * 60;

    // 状态
    const STATUS = [
        'DOING' => 0,    //进行中
        'DONE'  => 1,    //已完成
        'FAIL'  => 2,    //导出失败
    ];

    // 状态名字
    const STATUS_NAME = [
        0  => '进行中',   //进行中
        1  => '已完成',   //已完成
        2  => '导出失败',  //导出失败
    ];

    // 表名称
    public static function tableName(): string
    {
        return "`db_dreame_log`.`t_async_export_log`";
    }

    /**
     * 获取导出任务列表（包含分页）
     * @param array $condition
     * @return array
     */
    public function getExportLogList(array $condition): array
    {
        // 从缓存中获取数据
        $redis = by::redis();
        $r_key = self::__getAsyncExportLogKey();
        // 序列化 id 的参数
        $hash_key = serialize($condition);
        $res = $redis->hGet($r_key, $hash_key);

        // 缓存结果
        if ($res !== false) {
            return (array)json_decode($res, true);
        }

        // 创建一个 DB 查询
        $query = self::find()
            ->where($this->__getCondition($condition));
        // 列表的总数
        $total = $query->count();

        // 分页查询
        list($offset, $limit) = CUtil::pagination($condition['page'], $condition['page_size']);
        $items = $query
            ->offset($offset)
            ->limit($limit)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
        $data = ['items' => $items, 'total' => $total];

        // 插入数据到缓存，并更新过期时间
        $redis->hSet($r_key, $hash_key, json_encode($data));
        CUtil::ResetExpire($r_key, self::EXPIRE_TIME);

        return $data;
    }

    /**
     * 更新或添加
     * @param $id
     * @param $data
     * @return mixed|string
     * @throws \yii\db\Exception
     */
    public function saveLog($id, $data)
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        if ($id) {
            // 更新
            $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
        } else {
            // 添加
            $db->createCommand()->insert($tb, $data)->execute();
            $id = $db->getLastInsertID();
        }

        // 删除缓存
        $this->delAsyncExportLogCache();
        return $id;
    }

    /**
     * 删除 redis 缓存
     */
    public function delAsyncExportLogCache()
    {
        $r_key = self::__getAsyncExportLogKey();
        by::redis('core')->del($r_key);
    }

    /**
     * 获取 redis key
     * dreame|getParamsKey
     * @return string
     */
    private static function __getAsyncExportLogKey(): string
    {
        return AppCRedisKeys::getAsyncExportLogList();
    }

    /**
     * 获取查询条件
     * @param array $condition
     * @return array
     */
    private function __getCondition(array $condition): array
    {
        $data = [];
        if (isset($condition['admin_user_id'])) {
            $data['admin_user_id'] = $condition['admin_user_id'];
        }
        return $data;
    }
}