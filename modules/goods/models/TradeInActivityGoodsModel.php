<?php

namespace app\modules\goods\models;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException as RedisExceptionAlias;
use yii\db\ActiveQuery;

class TradeInActivityGoodsModel extends CommModel
{


    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_trade_in_activity_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function getTadeInGoods(): ActiveQuery
    {
        return $this->hasOne(TradeInGoodsModel::class, ['gid' => 'gid'])->select(['id', 'gid', 'category_id']);
    }


    /**
     *分类列表
     */
    public function getTabList():array
    {
        return CUtil::rememberCache([byNew::TradeInActivityModel()->getValidActivityKey('page'), 'tabList'], function () {
            $activity = byNew::TradeInActivityModel()->getValidActivity();
            if (empty($activity)) {
                return [];
            }
            // 获取活动商品分类
            $activityGoods = byNew::TradeInGoodsModel()::find()
                    ->leftJoin(byNew::TradeInActivityGoodsModel()::tableName(), 't_trade_in_activity_goods.gid = t_trade_in_goods.gid')
                    ->where(['is_del' => 0, 'activity_id' => $activity['id']])
                    ->select(['category_id'])->groupBy('category_id')->asArray()->all();
            $cateIds       = array_column($activityGoods, 'category_id');
            return byNew::TradeInCategoryModel()::find()->where(['is_del' => 0, 'id' => $cateIds])->select(['id', 'name'])->orderBy('id')->asArray()->all();
        });
    }


    /**
     * 通过分类获取活动商品
     * @param $page_cache_key
     * @param $categoryId
     * @return array
     */
    public function getActivityGoodsByCateId($page_cache_key, $categoryId): array
    {
        return CUtil::rememberCache([byNew::TradeInActivityModel()->getValidActivityKey('page'), $page_cache_key], function () use ($categoryId) {
            // 获取有效的活动
            $activity = byNew::TradeInActivityModel()->getValidActivity();

            if (empty($activity)) {
                return [];
            }

            // 活动商品信息
            $activityGoodList = byNew::TradeInGoodsModel()::find()
                    ->where(['is_del' => 0, 'category_id' => $categoryId, 'activity_id' => $activity['id']])
                    ->with(['category', 'goods' => function ($query) {
                        $query->with(['type0' => function ($query) {
                            $query->select(['price', 'cover_image as image', 'gid', 'atype']);
                        }]);
                    }])->leftJoin(byNew::TradeInActivityGoodsModel()::tableName(), 't_trade_in_activity_goods.gid = t_trade_in_goods.gid')
                    ->select(['sids', 'category_id', 't_trade_in_goods.gid'])->groupBy('t_trade_in_goods.gid');

            $data         = CUtil::pg($activityGoodList);
            $data['list'] = self::__formatList($activity['id'], $data['list'] ?? []);

            //活动过期时间
            $expire_seconds = CUtil::calculateCacheTtl([$activity]);
            return ['set_expire' => $expire_seconds, 'expire_time' => time() + $expire_seconds, 'data' => $data];
        });

    }

    /**
     * 格式化活动商品列表
     * @param $activity_id
     * @param $data
     * @return array
     */
    public function __formatList($activity_id, $data): array
    {
        $gids  = array_column($data, 'gid');
        $specs = byNew::TradeInActivityGoodsModel()->find()
                ->where(['gid' => $gids, 'is_del' => 0, 'activity_id' => $activity_id])->andWhere(['<>', 'price', 0])->andWhere(['<>', 'underline_price', 0])
                ->select(['gid', 'sid', 'price', 'underline_price'])
                ->asArray()->all();
        $specs = Collection::make($specs)->groupBy('gid')->toArray();

        return Collection::make($data)->map(function ($goods) use ($activity_id, $specs) {
            $spec = $specs[$goods['gid']] ?? [];

            if (empty($spec)) {
                return [];
            }

            // 获取商品规格
            $sids  = array_column($spec, 'sid');
            $atype = $goods['goods']['type0']['atype'] ?? 0;

            $goods['spec'] = [];

            // 过滤商品规格
            if ($atype > 0) {
                $specsInfo     = by::Gspecs()->getSpecNameByGid($goods['gid']) ?? [];
                $goods['spec'] = Collection::make($specsInfo)->map(function ($item) use ($sids, $specs) {
                    if (in_array($item['id'], $sids) && isset($specs[$item['gid']])) {
                        $spec = Collection::make($specs[$item['gid']])->where('sid', $item['id'])->first();
                        return array_merge($item, $spec);
                    }
                    return [];
                })->filter()->values()->toArray();
            } else {
                $goods['spec'][] = $spec[0] ?? [];
            }

            return $goods;
        })->filter()->values()->all();
    }


    private $getActivityGoodsByGidCache = [];

    /**
     * 获取活动商品
     * @param $gid
     * @return array
     * @throws RedisExceptionAlias
     */
    public function getActivityGoodsByGid($gid): array
    {
        if (isset($this->getActivityGoodsByGidCache[$gid])) {
            return $this->getActivityGoodsByGidCache[$gid];
        }
        $data                                   = $this->getActivityGoods($gid);
        $this->getActivityGoodsByGidCache[$gid] = $data;
        return $data;

    }


    /**
     * 获取所有以旧换新商品ID
     */
    public function getActivityGoods($gid=''): array
    {
        $key   = byNew::TradeInActivityModel()->getValidActivityKey('goods');
        $redis = by::redis('core');

        if (empty($gid)) {
            $data = $redis->hgetall($key);
            if ($data) {
                return array_map(function ($item) {
                    return json_decode($item, true);
                }, $data);
            }
        }else{
            $data  = $redis->hget($key, $gid);
            if ($data) {
                return json_decode($data, true);
            }
        }



        // key是否已过期
        $ttl = $redis->ttl($key);
        if ($ttl > 0) {
            return [];
        }

        // 从数据库获取数据
        $activity = byNew::TradeInActivityModel()->getValidActivity();
        if (empty($activity)) {
            return [];
        }
        $activity_id = $activity['id'];

        $goods = byNew::TradeInActivityGoodsModel()->find()
                ->where(['is_del' => 0, 'activity_id' => $activity_id])->andWhere(['<>', 'price', 0])->andWhere(['<>', 'underline_price', 0])
                ->select(['gid', 'sid', 'price', 'underline_price'])
                ->asArray()->all();

        // 设置缓存的过期时间为活动结束时间或默认过期时间
        $expire = CUtil::calculateCacheTtl([$activity]);
        $ret = [];

        foreach ($goods as $good) {
            $ret[$good['gid']][] = $good;
        }

        foreach ($ret as $g => $goods) {
            $redis->hset($key, $g, json_encode($goods));
        }

        $redis->expire($key, $expire);

        if (empty($gid)) {
            return $ret;
        }

        return $ret[$gid] ?? [];


    }
}