<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 * 运费地区表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;
use app\modules\main\models\CommModel;

class OfCfgModel extends CommModel
{

    public $tb_fields = [
        'id','type','of_id','pid','cid'
    ];

    public static function tbName()
    {
        return "`db_dreame_goods`.`t_of_cfg`";
    }

    /**
     * @param $type
     * @param $cid
     * @return string
     * 通过id获取数据
     */
    private function __getOneByCidKey($type, $cid): string
    {
        return AppCRedisKeys::getOfCfgByCid($type, $cid);
    }

    private function __delCache($type, $cids)
    {
        if (empty($cids)) {
            return '';
        }

        $r_key  = [];
        foreach ($cids as $cid) {
            $r_key[]  = $this->__getOneByCidKey($type, $cid);
        }

        return by::redis()->del(...$r_key);
    }

    /**
     * @param int $of_id
     * @param int $type
     * @param string $aids [{"province":"110000","city":["110100","110100"]}]
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog(int $of_id, int $type, string $aids): array
    {
        if (empty($aids)) {
            return [false, '请选择地区设置(1)'];
        }

        $arr       = (array)json_decode($aids, true);

        $r_cids = [];
        foreach ($arr as $one_arr) {
            $pid        = $one_arr['province']  ?? 0;
            $cids       = $one_arr['city']      ?? [];
            $cids       = array_filter($cids);

            if (empty($pid) || $pid == 1) {
                return [false, '请选择地区设置(2)'];
            }

            //判断cid是否正确
            $aList      = by::model('AreaModel', MAIN_MODULE)->GetList($pid);
            $aList      = array_column($aList, 'id');

            if (empty($cids)) {
                $r_cids[$pid]     = $aList;
            } else {
                if (array_diff($cids, $aList)) {
                    return [false, '请选择正确的市区(1)'];
                }

                $r_cids[$pid]     = $cids;
            }
        }

        if (empty($r_cids)) {
            return [false, '请选择地区设置(3)'];
        }

        $o_cids     = $this->GetListByOfId($of_id);
        $o_cids     = array_column($o_cids,'cid');

        $db         = by::dbMaster();
        $tb         = self::tbName();

        $trans      = $db->beginTransaction();

        try {

            $a_cids1    = [];
            $n_cids1    = [];
            foreach($r_cids as $pid => $n_cids) {
                //新增的数据
                $a_cids     = array_diff($n_cids, $o_cids);

                $save       = [];

                foreach($a_cids as $cid) {
                    $save[] = [
                        'type'      => $type,
                        'of_id'     => $of_id,
                        'pid'       => $pid,
                        'cid'       => $cid,
                    ];
                }

                $db->createCommand()->batchInsert($tb, ['type','of_id','pid','cid'], $save)->execute();

                $n_cids1    = array_merge($n_cids1, $n_cids);
                $a_cids1    = array_merge($a_cids1, $a_cids);
            }

            //删除的数据
            $d_cids     = array_diff($o_cids, $n_cids1);

            !empty($d_cids) && $db->createCommand()->delete($tb, ['of_id'=>$of_id,'type'=>$type,'cid'=>$d_cids])
                ->execute();

            $ccids = array_merge($a_cids1, $d_cids);
            $this->__delCache($type, $ccids);

            $trans->commit();

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            return [false, '操作失败(2)'];
        }

    }

    /**
     * @param int $of_id
     * @param int $pid
     * @return array
     * @throws Exception
     * 根据of_id获取数据
     */
    public function GetListByOfId(int $of_id, $pid = 0)
    {
        $of_id   = CUtil::uint($of_id);

        $where   = " `of_id` = :of_id ";
        $params  = [':of_id' => $of_id];

        if ($pid > 0) {
            $where         .= " AND `pid` = :pid";
            $params[':pid'] = $pid;
        }

        $tb      = $this::tbName();
        $fields  = implode("`,`",$this->tb_fields);
        $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE {$where}";
        $aData   = by::dbMaster()->createCommand($sql, $params)->queryAll();

        return $aData;
    }

    /**
     * @param $type
     * @param $cid
     * @return array|false
     * @throws Exception
     * 根据cid获取数据
     */
    public function GetOneByCid($type, $cid)
    {
        $redis   = by::redis('core');
        $r_key   = $this->__getOneByCidKey($type, $cid);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `type`=:type AND `cid`=:cid LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':type' => $type, ':cid'=>$cid])->queryOne();
            $aData   = $aData ?: [];

            $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param $of_id
     * @param $type
     * @return array
     * @throws Exception
     * 删除数据
     */
    public function Del($of_id, $type)
    {
        $of_id      = CUtil::uint($of_id);

        $aLog       = $this->GetListByOfId($of_id);

        if (empty($aLog)) {
            return [true, '无需操作'];
        }

        $db         = by::dbMaster();
        $tb         = self::tbName();

        $db->createCommand()->delete($tb, ['of_id' => $of_id])->execute();

        $cids       = array_column($aLog, 'cid');
        $this->__delCache($type, $cids);

        return [true, 'ok'];
    }

}
