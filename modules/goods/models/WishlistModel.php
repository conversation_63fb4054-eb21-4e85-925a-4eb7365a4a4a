<?php
/**
 * User: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date: 2025/7/16
 * Time: 14:45
 * 心愿单
 */
namespace app\modules\goods\models;


use app\components\AppNRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;


class WishlistModel extends CommModel
{
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`wishlist`";
    }

    public function rules(): array
    {
        return [
                [['user_id', 'goods_id', 'specs_id', 'add_time', 'expire_time', 'is_notified', 'is_deleted', 'created_time'], 'integer'],
                [['remark'], 'string', 'max' => 500],
        ];
    }

    public function getMyWishListKey($userId): string
    {
        return AppNRedisKeys::wishlist($userId);
    }

    public static function delMyWishListKey($userId)
    {
        $cacheKey = AppNRedisKeys::wishlist($userId);
        $cache    = by::redis();
        $cache->del($cacheKey);
    }

    /**
     * @param int $userId
     * @return array
     * @throws RedisException
     * 获取用户心愿单列表，带 Redis 缓存
     */
    public function getMyWishlist(int $userId): array
    {
        if ($userId <= 0) {
            return [];
        }

        $redis    = by::redis();
        $redisKey = $this->getMyWishListKey($userId);

        // 尝试从 Redis 获取活动详情
        $aJson = $redis->get($redisKey);
        if ($aJson) {
            $wishlist = (array) json_decode($aJson, true);
        } else {
            // 从数据库获取活动详情
            $wishlist = self::find()
                    ->where(['user_id' => $userId, 'is_deleted' => 0])
                    ->orderBy(['add_time' => SORT_DESC])
                    ->asArray()
                    ->all();
            // 将活动详情存入 Redis

            $redis->set($redisKey, json_encode($wishlist, JSON_UNESCAPED_UNICODE), ['EX' => empty($wishlist) ? 10 : 86400]);
        }

        return $wishlist;
    }


}
