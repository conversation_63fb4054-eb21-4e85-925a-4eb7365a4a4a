<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单主表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\main\models\PlatformModel;


class OdepositEModel extends CommModel {

    public static function tbName($user_id): string
    {
        $mod = intval($user_id) % 10;
        return  "`db_dreame_goods`.`t_odeposit_e_{$mod}`";
    }

    public $tb_fields = [
        'id','user_id','order_no','cfg','price','status','r_id','guide_id','guide_type','union','platform_source','source',
        'euid','live_mark','referer','ctime','utime'
    ];


    private $__format_fields = ['price','mprice','expand_price','deposit'];

    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($user_id,$order_no): string
    {
        return AppCRedisKeys::getOneDepositEInfo($user_id,$order_no);
    }


    /**
     * @param $user_id
     * @return string
     * 订单列表
     */
    private function __getDepositEListKey($user_id): string
    {
        return AppCRedisKeys::getDepositEList($user_id);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return int
     * 清理单个订单信息缓存
     */
    public function DelInfoCache($user_id,$order_no): int
    {
        $r_key1 = $this->__getOneInfoKey($user_id,$order_no);
        return  by::redis('core')->del($r_key1);
    }


    /**
     * @param $user_id
     * @return int
     * 清理订单列表 （有数据新增时）
     */
    public function DelListCache($user_id) : int
    {
        $r_key1 = $this->__getDepositEListKey($user_id);
        return  by::redis('core')->del($r_key1);
    }




    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, array $aData)
    {

        //获取商品信息
        $gData     = by::Gmain()->GetOneByGidSid($aData['gid'] ?? 0, $aData['sid'] ?? 0, false);
        $platforms = $gData['platforms'];
        $platforms = array_column($platforms, null, 'platform_id');

        //保存关键商品信息
        !empty($gData) && $cfg = [
            'id'                   => $gData['id'] ?? 0,
            'gid'                  => $gData['gid'] ?? 0,
            'sid'                  => $gData['sid'] ?? 0,
            'type'                 => $gData['type'] ?? 0,
            'sku'                  => $gData['sku'] ?? '',
            'name'                 => $gData['name'] ?? '',
            'mprice'               => $gData['mprice'] ?? 0,
            'price'                => $gData['price'] ?? 0,
            'fprice'               => $gData['fprice'] ?? 0,
            'cover_image'          => $gData['cover_image'] ?? '',
            'atype'                => $gData['atype'] ?? 0,
            'is_coupons'           => $gData['is_coupons'] ?? 0,
            'limit_num'            => $gData['limit_num'] ?? 0,
            'is_presale'           => $gData['is_presale'] ?? 0,
            'presale_time'         => $gData['presale_time'] ?? 0,
            'coefficient'          => $gData['coefficient'] ?? 0,
            'deposit'              => $gData['deposit'] ?? 0,
            'expand_price'         => $gData['expand_price'] ?? 0,
            'start_payment'        => $gData['start_payment'] ?? 0,
            'end_payment'          => $gData['end_payment'] ?? 0,
            'surplus_time'         => $gData['surplus_time'] ?? 0,
            'scheduled_number'     => $gData['scheduled_number'] ?? 0,
            'is_internal_purchase' => $gData['is_internal_purchase'] ?? 0,
            'is_internal'          => $gData['is_internal'] ?? 0,
            'ck_code'              => $gData['ck_code'] ?? 0,
            'tids'                 => $gData['tids'] ?? [],
            'spec'                 => $gData['spec'] ?? [],
            'market_image'         => $gData['market_image'] ?? '',
            'pc_image'             => $platforms[PlatformModel::PLATFORM['PC']]['cover_image'] ?? '',
        ];


        $price           = $aData['price'] ?? 0;
        $cfg             = json_encode($cfg ?? [], 320);
        $r_id            = $aData['r_id'] ?? 0;
        $guide_id        = $aData['guide_id'] ?? 0;
        $guide_type      = $aData['ouid_type'] ?? 0;
        $union           = $aData['union'] ?? '';
        $euid            = $aData['euid'] ?? '';
        $live_mark       = $aData['live_mark'] ?? '';
        $referer         = $aData['referer'] ?? '';
        $ctime           = $aData['ctime'] ?? 0;
        $source          = $aData['source'] ?? 0;
        $platform_source = $aData['platform_source'] ?? 0;

        $save = [
            'order_no'        => $order_no,
            'user_id'         => $user_id,
            'price'           => $price,
            'cfg'             => $cfg,
            'r_id'            => $r_id,
            'guide_id'        => $guide_id,
            'guide_type'      => $guide_type,
            'union'           => $union,
            'euid'            => $euid,
            'live_mark'       => $live_mark,
            'referer'         => $referer,
            'source'          => $source,
            'platform_source' => $platform_source,
            'ctime'           => $ctime,
            'utime'           => intval(START_TIME),
        ];

        $tb = $this->tbName($user_id);

        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        return [true, 'ok'];
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取订单数据
     */
    public function GetInfoByOrderId($user_id, $order_no, $format_price = true,bool $cache=true)
    {
        $user_id = CUtil::uint($user_id);

        if(empty($order_no) || empty($user_id)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($user_id,$order_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName($user_id);
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no AND `user_id`=:user_id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no, ':user_id'=>$user_id])->queryOne();
            $aData   = $aData ?: [];
            $cache == true && $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }
        $cfg = $aData['cfg'] ?? '';
        if($cfg){
            $cfg = json_decode($cfg,true);
            if($format_price){
                foreach ($this->__format_fields as $field){
                    isset($cfg[$field]) && $cfg[$field] = by::Gtype0()->totalFee($cfg[$field], 1);
                }
            }
            $aData['cfg'] = $cfg;
        }

        return $aData;
    }

}
