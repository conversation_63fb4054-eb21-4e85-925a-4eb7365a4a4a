<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class GtagModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'tid'
    ];

    const TAG_TYPE = [
            10 => 'washing',
            11 => 'sweep_robot',
            12 => 'vacuum_cleaner',
            13 => 'hairdryer',
            14 => 'water_purifier',
            15 => 'washing_drying',
            16 => 'mite_cleaner',
            17 => 'airp',
            18 => 'window_clean',

    ];

    const TAG = [
            'ALL'            => -1,  //全部
            'NEW'            => 1,   //重磅新品
            'RECOM'          => 2,   //热销
            'COMBO_PURCHASE' => 3,   //觅享组合购
            'CLEAN'          => 10,  //智能洗地机
            'SWEEP'          => 11,  //扫地机器人
            'VACUUM'         => 12,  //手持吸尘器
            'H_DRYER'        => 13,  //高速吹风机
            'WATER_PURIFIER' => 14,  //净饮一体机
            'WASHING_DRYING' => 15,  //洗烘一体机
            'MITE_CLEAN'     => 16,  //科技除螨仪
            'AIRP'           => 17,  //空气净化器
            'WINDOW_CLEAN'   => 18,  //擦窗机器人
            'FITTINGS'       => 20,  //配件专区
    ];

    const TAG_NAME = [
            '-1' => '全部',
            '2'  => '热销',
            '1'  => '重磅新品',
            '3'  => '觅享组合购',
            '11' => '扫地机器人',
            '10' => '智能洗地机',
            '12' => '手持吸尘器',
            '13' => '高速吹风机',
            '14' => '净饮一体机',
            '15' => '洗烘一体机',
            '16' => '科技除螨仪',
            '17' => '空气净化器',
            '18' => '擦窗机器人',
            '20' => '配件专区',
    ];

    const TAG_IMAGE = [
        '10' => 'https://wpm-cdn.dreame.tech/images/202407/668cd2b7c30587992208850.png',
        '11' => 'https://wpm-cdn.dreame.tech/images/202407/668cd292462022872702886.png',
        '12' => 'https://wpm-cdn.dreame.tech/images/202407/668cd2d6e614d9420808414.png',
        '13' => 'https://wpm-cdn.dreame.tech/images/202407/668cd30b4e1a63202734890.png',
        '14' => 'https://wpm-cdn.dreame.tech/images/202407/668cd34557b3b3592702856.png',
        '15' => 'https://wpm-cdn.dreame.tech/images/202407/668cd320177cc0962631641.png',
        '16' => 'https://wpm-cdn.dreame.tech/images/202407/668cd2f2e799f9492759643.png',
        '17' => 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202504/68118ae6e29d99283331224.png',
        '18' => 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202504/68118b01dd1cf9063331220.png',
        '20' => 'https://wpm-cdn.dreame.tech/images/202407/668cd35d99a756292208886.png',
    ];

    //iot 平台对应配置
    const TAG_IOT_MODEL = [
            11 => 'vacuum', //扫地机器人
            10 => 'hold',   //洗地机
    ];

    // iot 平台对应配置
    const TAG_IOT = [
            'hold'       => 10,    //洗地机
            'vacuum'     => 11,    //扫地机器人
            'cleaner'    => 12,    //吸尘器
            'hairdryer'  => 13,    //吹风机
            'mite_clean' => 16,    //除螨仪
    ];


    const MAIN_TAG = [
            10, 11, 12, 13, 14, 15, 16, 17, 18
    ];

    const RECOMMAND_TAG = [
        10, 11, 12, 13, 14, 15, 16, 17, 18, 20
    ];

    const NOT_MAIN_TAG = [
        2, 3, 20
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_gtag`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @param $gid
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getListByGidKey($gid): string
    {
        return AppCRedisKeys::getTagListByGid($gid);
    }

    /**
     * @return string
     * 所有商品对应的gid tid
     */
    private function __getGidAndTag(): string
    {
        return AppCRedisKeys::getGidAndTag();
    }

    private function __getPartGidCache() : string
    {
        return AppCRedisKeys::getPartGid();
    }
    /**
     * @param $gid
     * @return int
     * 缓存清理
     * @throws Exception
     */
    private function __delCache($gid): int
    {
        $r_key = $this->__getListByGidKey($gid);

        return by::redis('core')->del($r_key);
    }

    /**
     * @param array $tids
     * @return string
     * 获取标签名
     */
    public function GetTagName($tids = [])
    {
        if (empty($tids)) {
            return '';
        }

        $t_name = [];
        $tagMap = $this->GetTagNameMap();
        foreach ($tids as $tid) {
            $temp = $tagMap[$tid] ?? '';
            if (empty($temp)) {
                continue;
            }

            $t_name[] = $temp;
        }

        return implode(',', $t_name);
    }

    /**
     * @return array
     * 获取所有标签
     */
    public function GetTagCnfList()
    {
        try {
            $dictData = CUtil::dictData('goods_tags');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $return = [];
            foreach ($dictData as $val) {
                $return[] = [
                    'tid' => (string) ($val['value'] ?? ''),
                    'name' => $val['label'] ?? '',
                ];
            }

            return $return;
        } catch (\Throwable $e) {
            // 兜底数据
            $list = self::TAG_NAME;
            $return = [];
            foreach ($list as $tid => $name) {
                $return[] = [
                    'tid' => (string)$tid,
                    'name' => $name,
                ];
            }

            return $return;
        }
    }

    public function GetMainTag(): array
    {
        try {
            $dictData = CUtil::dictData('main_goods_tags');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $mainTag = [];
            foreach ($dictData as $val) {
                $mainTag[] = (int) ($val['value'] ?? 0);
            }

            return $mainTag;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::MAIN_TAG;
        }
    }

    public function GetNotMainTag(): array
    {
        try {
            $dictData = CUtil::dictData('not_main_goods_tags');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $notMainTag = [];
            foreach ($dictData as $val) {
                $notMainTag[] = (int) ($val['value'] ?? 0);
            }

            return $notMainTag;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::NOT_MAIN_TAG;
        }
    }

    public function GetTagNameMap(): array
    {
        try {
            $dictData = CUtil::dictData('goods_tags');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $tagMap = [];
            foreach ($dictData as $val) {
                $tagMap[$val['value']] = $val['label'] ?? '';
            }

            if (empty($tagMap)) {
                throw new BusinessException('字典数据为空');
            }

            return $tagMap;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::TAG_NAME;
        }
    }

    public function GetTagCodeMap(): array
    {
        try {
            $dictData = CUtil::dictData('goods_tags_code');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $tagMap = [];
            foreach ($dictData as $val) {
                $tagMap[$val['label']] = (int) ($val['value'] ?? '');
            }

            return $tagMap;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::TAG;
        }
    }

    public function GetTagTypeMap(): array
    {
        try {
            $dictData = CUtil::dictData('goods_tags_type');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $tagMap = [];
            foreach ($dictData as $val) {
                $tagMap[$val['label']] = $val['value'] ?? '';
            }

            return $tagMap;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::TAG_TYPE;
        }
    }

    public function GetTagImageMap(): array
    {
        try {
            $dictData = CUtil::dictData('goods_tags_image');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $tagMap = [];
            foreach ($dictData as $val) {
                $tagMap[$val['label']] = $val['value'] ?? '';
            }

            if (empty($tagMap)) {
                throw new BusinessException('字典数据为空');
            }

            return $tagMap;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::TAG_IMAGE;
        }
    }

    public function GetRecommendTag(): array
    {
        try {
            $dictData = CUtil::dictData('goods_recommend_tags');

            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $tagMap = [];
            foreach ($dictData as $val) {
                $tagMap[] = (int) ($val['value'] ?? 0);
            }

            if (empty($tagMap)) {
                throw new BusinessException('字典数据为空');
            }

            return $tagMap;
        } catch (\Throwable $e) {
            // 兜底数据
            return self::RECOMMAND_TAG;
        }
    }
    
    public function GetYouXuanGoodsTag(): array
    {
        try {
            $dictData = CUtil::dictData('youxuan_goods_tags');
            
            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $return = [];
            foreach ($dictData as $val) {
                $return[] = [
                    'tid' => (string) ($val['value'] ?? ''),
                    'name' => $val['label'] ?? '',
                ];
            }

            if (empty($return)) {
                throw new BusinessException('字典数据为空');
            }

            return $return;
        } catch (\Throwable $e) {
            // 兜底数据
            return [];
        }
    }
    
    public function GetYanXuanGoodsTag(): array
    {
        try {
            $dictData = CUtil::dictData('yanxuan_goods_tags');
            
            if (empty($dictData)) {
                throw new BusinessException('字典数据为空');
            }

            $return = [];
            foreach ($dictData as $val) {
                $return[] = [
                    'tid' => (string) ($val['value'] ?? ''),
                    'name' => $val['label'] ?? '',
                ];
            }

            if (empty($return)) {
                throw new BusinessException('字典数据为空');
            }

            return $return;
        } catch (\Throwable $e) {
            // 兜底数据
            return [];
        }
    }

    /**
     * @param int $gid
     * @param array $aData
     * @return array
     * @throws Exception
     * tag增删
     */
    public function SaveLog(int $gid, array $aData)
    {
        $gid = CUtil::uint($gid);

        if (empty($gid)) {
            return [false, "商品不能为空"];
        }

        //参数过滤
        $tid = $aData['tid'] ?? "";

        $tids = [];
        if (!empty($tid)) {
            $tids = explode(',', $tid);
        }

        $tagCode = by::Gtag()->GetTagCodeMap();
        // if (array_diff($tids, self::TAG)) {
        if (array_diff($tids, $tagCode)) {
            return [false, '请选择正确的标签'];
        }


        //查修原来的标签
        $o_tids = $this->GetListByGid($gid);
        $o_tids = array_column($o_tids, 'tid');

        //新增的标签
        $a_tids = array_diff($tids, $o_tids);
        //删除的标签
        $d_tids = array_diff($o_tids, $tids);

        $tb = $this->tbName();
        $db = by::dbMaster();
        if ($a_tids) {
            $sql = "INSERT IGNORE {$tb} (`gid`,`tid`) VALUES ";
            foreach ($a_tids as $tid) {
                $sql .= "({$gid},{$tid}),";
            }
            $sql = rtrim($sql, ',');
            $db->createCommand($sql)->execute();
        }

        if ($d_tids) {
            $db->createCommand()->delete($tb, ['gid' => $gid, 'tid' => $d_tids])->execute();
        }

        $this->__delCache($gid); //todo 清空缓存

        return [true, 'ok'];
    }

    /**
     * @param int $gid
     * @return array
     * @throws Exception
     * 根据gid获取标签列表
     */
    public function GetListByGid(int $gid)
    {
        $gid = CUtil::uint($gid);
        if ($gid <= 0) {
            return [];
        }

        $redis = by::redis('core');
        $redis_key = $this->__getListByGidKey($gid);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql = "SELECT `{$fields}` FROM {$tb} WHERE `gid`=:gid ORDER BY `tid`";
            $aData = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryAll();

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }
    
    public function getTidsByGid(int $gid)
    {
        if (empty($gid)) {
            return [];
        }
        $res = $this->GetListByGid($gid);
        return array_unique(array_column($res, 'tid'));
    }

    /**
     * @return array|DataReader
     * @throws RedisException
     * @throws Exception
     *  获取主机
     */
    public function GetGidAndTag()
    {
        $redis = by::redis('core');
        $redis_key = $this->__getGidAndTag();
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        $aJson = false;
        if ($aJson === false) {
            $tb = $this->tbName();
            $notMainTag = by::Gtag()->GetNotMainTag();
            // $not_main_tag= implode(",",self::NOT_MAIN_TAG);
            $not_main_tag= implode(",",$notMainTag);
            $fields = implode("`,`", $this->tb_fields);
            $sql = "SELECT `{$fields}` FROM {$tb} WHERE `tid` NOT IN ($not_main_tag)";
            $aData = by::dbMaster()->createCommand($sql)->queryAll();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * @param $type
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取主机or配件gid
     */
    public function getGidInfo($type): array
    {
        $redis = by::redis('core');
        switch ($type) {
            case by::partsSalesModel()::HOST_TYPE:
                $redis_key = $this->__getGidAndTag();
                $notMainTag = by::Gtag()->GetNotMainTag();
                // $not_main_tag = implode(",", self::NOT_MAIN_TAG);
                $not_main_tag = implode(",", $notMainTag);
                $where = " WHERE `tid` NOT IN ($not_main_tag)";
                break;
            case by::partsSalesModel()::PART_TYPE:
                $redis_key = $this->__getPartGidCache();
                $where = " WHERE `tid` =20";
                break;
            default:
                $redis_key = $this->__getGidAndTag();
                $notMainTag = by::Gtag()->GetNotMainTag();
                // $not_main_tag = implode(",", self::NOT_MAIN_TAG);
                $not_main_tag = implode(",", $notMainTag);
                $where = " WHERE `tid` NOT IN ($not_main_tag)";
        }
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        if (!$aData){
            $tb = $this->tbName();
            $stock_tb = by::Gstock()::tbName();
            $sql = "SELECT `tag`.`id`,`tag`.`gid`,`tag`.`tid` FROM {$tb} as `tag` {$where}";
            $aData = by::dbMaster()->createCommand($sql)->queryAll();
            foreach ($aData as &$value){
                $stock_sql = "SELECT `stock` FROM {$stock_tb} WHERE `gid` =:gid";
                $stock = by::dbMaster()->createCommand($stock_sql,[':gid'=>$value['gid']])->queryOne();
                $value['stock'] = $stock['stock'] ?? 0;
            }
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * @throws RedisException
     * 新增商品时删除tag缓存
     */
    public function __delGtagCache()
    {
        $redis = by::redis('core');
        $host_key = $this->__getGidAndTag();
        $part_key = $this->__getPartGidCache();
        $redis->del($host_key,$part_key);
    }


    /**
     * @param $tid
     * @return array
     * 根据tid查询所绑定的gid
     * 后台专用（不加缓存）
     * @throws Exception
     */
    public function getGidByTid($tid): array
    {
        if (empty($tid)) {
            return [];
        }

        $tid = intval($tid);
        $tb  = self::tbName();
        $sql = "SELECT `gid` FROM {$tb} WHERE `tid` = :tid";

        $gids = by::dbMaster()->createCommand($sql, [':tid' => $tid])->queryColumn();

        return $gids;
    }
    
    /**
     * 获取商品标签列表
     * @param int $type 0=全部，1=普通商品标签，2=优选商品标签，3=严选商品标签
     * @return array
     */
    public function getTagList(int $type = 0): array
    {
        $list = by::Gtag()->GetTagCnfList();
        //优选商品标签
        $youxuanlist = by::Gtag()->GetYouXuanGoodsTag();
        //严选商品标签
        $yanxuanlist = by::Gtag()->GetYanXuanGoodsTag();
        $youxuanTagIds = array_column($youxuanlist, 'tid');
        $yanxuanTagIds = array_column($yanxuanlist, 'tid');
        if ($type == 1) {
            // 仅展示普通商品标签
            foreach ($list as $key => $item) {
                // 去除优选标签
                if (in_array($item['tid'], $youxuanTagIds)) {
                    unset($list[$key]);
                }
                // 去除严选标签
                if (in_array($item['tid'], $yanxuanTagIds)) {
                    unset($list[$key]);
                }
            }
        } elseif ($type == 2) {
            // 仅展示优选商品标签
            foreach ($list as $key => $item) {
                if (! in_array($item['tid'], $youxuanTagIds)) {
                    unset($list[$key]);
                }
            }
        } elseif ($type == 3) {
            // 仅展示严选商品标签
            foreach ($list as $key => $item) {
                if (! in_array($item['tid'], $yanxuanTagIds)) {
                    unset($list[$key]);
                }
            }
        }

        return array_values($list);
    }


}
