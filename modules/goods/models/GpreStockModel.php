<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品库存
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GpreStockModel extends CommModel
{


    public $tb_fields = [
        'id', 'gid', 'sid', 'stock', 'sales', 'wait', 'is_del'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gprestock`";
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 库存缓存KEY
     */
    private function __getPresaleGoodsStockKey($gid, $sid): string
    {
        return AppCRedisKeys::gPresaleStock($gid, $sid);
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 销量缓存KEY
     */
    private function __getPresaleGoodsSalesKey($gid, $sid): string
    {
        return AppCRedisKeys::gPresaleSales($gid, $sid);
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 未付款数缓存KEY
     */
    private function __getPresaleGoodsWaitsKey($gid, $sid): string
    {
        return AppCRedisKeys::gPresaleWait($gid, $sid);
    }


    /**
     * @param $gid
     * @return string
     * 商品库存列表
     */
    private function __getPresaleGoodsListStockKey($gid): string
    {
        return AppCRedisKeys::gPresaleListStock($gid);
    }


    /**
     * @param $gid
     * @return void
     * 清除商品库存列表
     */
    private function __delCache($gid)
    {
        $r_key  = $this->__getPresaleGoodsListStockKey($gid);

        by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @param array $sids
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 删除所有sid缓存
     */
    private function __delOptCache($gid, $sids = [])
    {
        foreach ($sids as $sid) {
            $this->OptStock($gid, $sid, 'DEL');
        }
    }

    /**
     * @param $gid
     * @param $sid
     * @return void
     * 删除库存、销量、未付款数缓存
     */
    public function DelAllCache($gid, $sid)
    {
        $r_key1 = $this->__getPresaleGoodsStockKey($gid, $sid);
        $r_key2 = $this->__getPresaleGoodsSalesKey($gid, $sid);
        $r_key3 = $this->__getPresaleGoodsWaitsKey($gid, $sid);

        by::redis()->del($r_key1, $r_key2, $r_key3);
    }

    /**
     * @param $gid
     * @param array $data
     * @param array $specsData
     * @return array
     * @throws \yii\db\Exception
     * 数据增改
     */
    public function SaveLog($gid, $data = [], $specsData = [])
    {
        if(isset($data['pre_stock']) && $data['pre_stock']){
            $data['stock'] = $data['pre_stock'];
        }

        $tb         = self::tbName();
        $mGtype0    = by::Gtype0();
        $aData      = [];
        $need_fid   = ['gid', 'sid', 'stock'];

        $atype      = $data['atype'] ?? -1;

        switch ($atype) {
            case $mGtype0::ATYPE['SPECS']:
                $aData = by::Gspecs()->getListByGid($gid);
                $aData = array_column($aData, 'sku', 'id');
                break;
        }

        //保存数据
        $values     = '';

        if (!empty($aData)) {
            //添加子商品库存
            $specsData  = array_column($specsData, null, 'sku');
            foreach ($aData as $sid => $sku) {
                $stock      = $specsData[$sku]['stock'] ?? 0;
                $stock      = CUtil::uint($stock);
                $values    .= "({$gid}, {$sid}, {$stock}),";
            }
            $values  = rtrim($values, ',');
        } else {
            $stock          = $data['stock'] ?? 0;
            $stock          = CUtil::uint($stock);
            $values .= "({$gid}, 0, {$stock})";
        }


        $field  = implode(',', $need_fid);
        $sql    = "INSERT INTO {$tb} ({$field})  
                        VALUES {$values} 
                        ON DUPLICATE KEY UPDATE `stock` = values(`stock`)";
        by::dbMaster()->createCommand($sql)->execute();


        //删除的规格
        if (!empty($aData)) {
            $sida   = array_keys($aData);
            $sids   = implode(',', $sida);
            by::dbMaster()->createCommand()->update(
                $tb,
                ['is_del' => 1],
                "`gid` = {$gid} AND `sid` NOT IN ({$sids})"
            )->execute();
        }

        //删除所有sid缓存
        $this->__delOptCache($gid, ($sida ?? [0]));
        $this->__delCache($gid);

        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive
     * @return array
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 库存操作 ROLL:商品购买及回滚 SET：设置库存（同步库存用）SALE：更改销量（退款） WAIT：更改未付款数
     */
    public function UpdateStock($gid, $sid = 0, $num = 0, $opt = 'ROLL', bool $positive = true)
    {
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;

        $tb         = self::tbName();

        switch ($opt) {
            case 'ROLL':
                //1、先更新redis库存数据-第二次校验库存
                list($s, $m) = $this->OptStock($gid, $sid, 'INCR', -$num);
                if (!$s) {
                    return [false, $m];
                }

                //2、更新数据库数据-第三次数据库校验库存-库存字段非负
                $sql    = "UPDATE {$tb} SET `stock`=`stock`-(:num),`sales`=`sales`+(:num),`wait`=`wait`+(:num)
                           WHERE `gid` = :gid AND `sid` = :sid AND `stock`-(:num) >= 0 LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid])->execute();

                if ($u_num == 0) {
                    return [false, '库存不够(3)'];
                }

                //更新销量缓存
                $this->OptSales($gid, $sid, 'INCR', $num, $positive);
                //更新未付款缓存
                $this->OptWait($gid, $sid, 'INCR', $num, $positive);

                break;

            case 'SET':
                $num        = $num < 0 ? 0 : $num;
                $wait       = $this->OptWait($gid, $sid);

                //如果erp库存比未付款数还小，则更新库存为0
                if ($wait >= $num) {
                    $num        = 0;
                    $set_str    = " :num";
                } else {
                    $set_str    = " :num - `wait`";
                }

                $sql    = "UPDATE {$tb} SET `stock` = {$set_str} WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid])->execute();

                if ($u_num > 0) {
                    $this->optStock($gid, $sid, 'DEL');
                }

                break;

            case 'SALE':
                $sql = "UPDATE {$tb} SET `sales` = `sales` + (:num) WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid,])->execute();
                //删除销量缓存
                $this->OptSales($gid, $sid, 'DEL');
                break;

            case 'WAIT':
                $wait = $this->OptWait($gid, $sid);

                if ($wait <= $num) {
                    $set_str    = " :num";
                    $num        = 0;
                } else {
                    $set_str = " `wait` - :num";
                }

                $sql = "UPDATE {$tb} SET `wait` = {$set_str} WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid,])->execute();
                //删除未付款缓存
                $this->OptWait($gid, $sid, 'DEL');
                break;
        }

        return [true, 'ok'];
    }


    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @return array|int
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 获取设置商品库存
     */
    public function OptStock(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $r_key      = $this->__getPresaleGoodsStockKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $stock      = $redis->get($r_key);

                if ($stock === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `stock` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $stock  = $aLog['stock'] ?? 0;
                    $redis->set($r_key, $stock, ['NX', 'EX' => 600]);
                }

                return intval($stock);
                break;

            case 'INCR':
                !$redis->exists($r_key) &&  $this->OptStock($gid, $sid);
                $s_num  = $redis->incrBy($r_key, $num);
                if ($s_num < 0) {
                    $num < -1 && $this->OptStock($gid, $sid, 'DEL');
                    return [false, '库存不足(6)'];
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 获取设置商品销量
     */
    public function OptSales(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getPresaleGoodsSalesKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $sales      = $redis->get($r_key);

                if ($sales === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `sales` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $sales  = $aLog['sales'] ?? 0;
                    $redis->set($r_key, $sales, ['NX', 'EX' => 600]);
                }

                return intval($sales);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品未付款数
     */
    public function OptWait(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getPresaleGoodsWaitsKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $wait      = $redis->get($r_key);

                if ($wait === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `wait` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $wait  = $aLog['wait'] ?? 0;
                    $redis->set($r_key, $wait, ['NX', 'EX' => 600]);
                }

                return intval($wait);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }


    /**
     * @param $gid
     * @param $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据商品ID获取库存列表
     */
    public function getStockListByGid($gid,$cache=true)
    {
        $gid    = CUtil::uint($gid);
        $redis = by::redis();
        $r_key = $this->__getPresaleGoodsListStockKey($gid);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = self::tbName();
            $fields     = implode("`,`",$this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `gid`=:gid";
            $aData  = by::dbMaster()->createCommand($sql, ['gid' => $gid])->queryAll();

            $redis->set($r_key, json_encode($aData), ['ex' => empty($aData) ? 10 : 600]);
        }

        return $aData;
    }




    /**
     * @param $gid
     * @return array
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 获取商品所有规格库存
     */
    public function GetSumData($gid): array
    {
        $data = $this->getStockListByGid($gid);
        if(empty($data)){
            return [0,0];
        }
        $stock  = 0;
        $sales  = 0;
        foreach ($data as $k=>$v){
            $stock +=intval($v['stock']??0);
            $sales +=intval($v['sales']??0);
        }
        return [$stock,$sales];
    }

}
