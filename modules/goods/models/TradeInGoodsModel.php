<?php

namespace app\modules\goods\models;

use app\modules\main\models\CommModel;
use yii\db\ActiveQuery;

class TradeInGoodsModel extends CommModel
{


    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_trade_in_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * 关联分类表
     * @return ActiveQuery
     */
    public function getCategory(): ActiveQuery
    {
        return $this->hasOne(TradeInCategoryModel::class, ['id' => 'category_id'])->where(['is_del' => 0])->select(['id', 'name']);
    }

    /**
     * 关联商品表
     */
    public function getGoods(): ActiveQuery
    {
        return $this->hasOne(GmainModel::class, ['id' => 'gid'])->select(['id', 'name','status']);
    }


}