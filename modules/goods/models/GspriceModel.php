<?php

/**
 * 商品自定义价格
 */

namespace app\modules\goods\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class GspriceModel extends CommModel
{
    const DECIMAL_RANGE = "99999999.99"; //价格上限
    public $tb_fields = [
        'id', 'sku', 'sprice', 'sprice_type', 'ctime', 'utime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_gsprice`";
    }


    /**
     *
     * @return string
     * 根据sku获取自定义列表
     */
    private function __getGspriceListBySku($sku): string
    {
        return AppCRedisKeys::getGspriceListBySku($sku);
    }


    /**
     * @return string
     * 获取唯一自定义价格
     */
    private function __getGspricebyId($id): string
    {
        return AppCRedisKeys::getGspricebyId($id);
    }

    /**
     * @return string
     * 获取唯一自定义价格
     */
    private function __getGspricebySku($sku,$sprice_type): string
    {
        return AppCRedisKeys::getGspricebySku($sku,$sprice_type);
    }


    /**
     * @param $sku
     * @param $sprice_type
     * @return int
     * 删除sku相关
     */
    private function __delSkuCache($sku,$sprice_type): int
    {
        $r_key = $this->__getGspricebySku($sku,$sprice_type);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $id
     * @return int
     * 删除id缓存
     */
    private function __delIdCache($id): int
    {
        $r_key = $this->__getGspricebyId($id);
        return by::redis('core')->del($r_key);
    }


    /**
     * @param $sku
     * @return int
     * 列表缓存清理
     */
    private function __delListCache($sku): int
    {
        $r_key = $this->__getGspriceListBySku($sku);
        return by::redis('core')->del($r_key);
    }


    /**
     * @param $id
     * @param bool $format_price
     * @param bool $cache
     * @return array|false
     * @throws Exception
     * 根据id获取数据
     */
    public function GetOneById($id, $format_price = true, $cache = true)
    {
        $id         = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key = $this->__getGspricebyId($id);

        !$cache && $redis->del($r_key);

        $aJson      = $redis->get($r_key);
        $aData      = (array)json_decode($aJson, true);

        if ($aJson  === false) {
            $tb      = $this::tbName();
            $fields  = implode(",", $this->tb_fields);
            $sql     = "SELECT {$fields} FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData   = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        if ($format_price) {
            $aData['sprice']     = $this->totalFee($aData['sprice'], 1);
        }

        return $aData;
    }



    /**
     * @param string $sku
     * @param bool $cache
     * @return array
     * @throws Exception
     * 根据sku查询
     */
    public function GetOneBySku(string $sku,int $sprice_type,$cache = true): array
    {
        if (empty($sku)) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = $this->__getGspricebySku($sku,$sprice_type);

        $aJson      = $cache ? $redis->get($r_key) : false;
        $aData      = (array)json_decode($aJson, true);

        if ($aJson  === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id`,`sku` FROM  {$tb} WHERE `sku`=:sku AND `sprice_type`=:sprice_type AND `is_del`=0 LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':sku' => $sku,':sprice_type'=>$sprice_type])->queryOne();
            $aData   = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $this->GetOneById($aData['id'], false, $cache);
    }


    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 增加商品sku
     */
    public function SaveLog(array $aData): array
    {
        $sku         = trim($aData['sku'] ?? "");
        $sprice      = $aData['sprice'] ?? 0;
        $sprice      = sprintf("%.2f", $sprice);
        $sprice_type = intval($aData['sprice_type'] ?? 0);

        if (empty($sku)  || empty($sprice_type)) {//不做修改
            return [true, "ok"];
        }

        if (bccomp($sprice, self::DECIMAL_RANGE, 2) > 0 || bccomp($sprice, 0, 2) < 0) {
            return [false, "商品设置价格-非法价格"];
        }


        $aLog = $this->GetOneBySku($sku,$sprice_type);
        if ($aLog) {
            return [false, "设置价格{$sku}-{$sprice_type}已经存在"];
        }

        $save = [
            'sku'       => $sku,
            'sprice'     => by::Gtype0()->totalFee($sprice),
            'sprice_type'     => $sprice_type,
            'ctime'=>intval(START_TIME),
            'utime'=>intval(START_TIME),
        ];

        $tb      = self::tbName();
        $ret     = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id      = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，设置价格属性sku新增失败"];
        }

        $this->__delSkuCache($sku,$sprice_type);
        $this->__delIdCache($id);
        $this->__delListCache($sku);

        return [true, "ok"];
    }


    /**
     * @param string $sku
     * @param int $sprice_type
     * @param array $update
     * @return array
     * @throws Exception
     * 编辑数据
     */
    public function UpdateLog(string $sku,int $sprice_type, array $update): array
    {
        $sprice      = $update['sprice'] ?? 0;
        $sprice      = sprintf("%.2f", $sprice);
        $update['sprice'] = $sprice;
        if (empty($sku) || empty($sprice_type)) {
            return [true, "ok"];
        }

        //允许修改的字段
        $allowed = [
            'sprice','is_del','utime','dtime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['sprice'])) {
            $update['sprice'] = $this ->totalFee($update['sprice']);
        }

        $aLog    = $this->GetOneBySku($sku,$sprice_type);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb      = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['id' => $aLog['id']])->execute();

        $this->__delSkuCache($sku,$sprice_type);
        $this->__delIdCache($aLog['id']);
        $this->__delListCache($sku);

        return [true, "成功"];
    }


    /**
     * @param string $sku
     * @return array
     * @throws Exception
     * 根据sku获取设置价格列表
     */
    public function GetListBySku(string $sku,$format_price = true, $cache = true): array
    {
        if (empty($sku)) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = $this->__getGspriceListBySku($sku);
        $aJson      = $cache ? $redis->get($r_key) : false;
        $aData      = (array)json_decode($aJson, true);

        if ($aJson  === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id` FROM {$tb} WHERE `sku` = :sku AND `is_del` = 0";
            $aData   = by::dbMaster()->createCommand($sql, [':sku' => $sku])->queryAll();

            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        $return = [];
        foreach ($aData as $val) {
            $return[] = $this->GetOneById($val['id'],$format_price);
        }

        return $return;
    }


    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public function totalFee($price, $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }

}
