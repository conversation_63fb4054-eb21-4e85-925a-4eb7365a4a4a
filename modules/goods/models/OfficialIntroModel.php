<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\exceptions\ProductMatrixException;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class OfficialIntroModel extends CommModel
{

    public static function tableName(): string
    {
        return '`db_dreame_goods`.`t_official_intro`';
    }

    public static $tb_fields = [
            'id', 'title', 'status', 'intro', 'ctime', 'utime'
    ];


    const STATUS = [
            'UP'   => 0,    //上架
            'DOWN' => 1     //下架
    ];
    private function getIntroDetailKey($id): string
    {
        return AppCRedisKeys::getIntroDetailKey($id);
    }


    public function __delIntroDetailKey($id)
    {
        $redis    = by::redis();
        $redisKey = $this->getIntroDetailKey($id);
        $redis->del($redisKey);
    }

    /**
     * @param $save
     * @return array
     * @throws ProductMatrixException
     *  保存简介
     */
    public function saveIntro($save): array
    {
        $id    = $save['id'] ?? 0;
        $intro = self::findOne($id);

        if ($intro) {
            // 编辑
            $save['utime'] = time();
        } else {
            // 新增
            $intro         = new self();
            $save['ctime'] = time();
        }

        // 设置属性并保存，使用修改器
        foreach ($save as $attribute => $value) {
            $intro->$attribute = $value;
        }

        // 尝试保存数据，如果保存失败抛出异常
        if (!$intro->save()) {
            // 抛出带有错误信息的异常
            throw new ProductMatrixException('保存失败: ' . implode(', ', $intro->errors));
        }

        $this->__delIntroDetailKey($id);
        // 返回保存成功，并且将保存后的数据返回
        return [true, $intro->attributes];
    }

    public function getDetail($id): array
    {
        $redis    = by::redis();
        $redisKey = $this->getIntroDetailKey($id);

        // 尝试从 Redis 获取缓存数据
        $cachedData = $redis->get($redisKey);

        if ($cachedData) {
            // 如果缓存中存在数据，直接返回
            return json_decode($cachedData, true);
        }

        // 使用 select() 来限制查询的字段
        $intro = self::find()
                ->select(['id', 'title', 'status', 'intro', 'ctime', 'utime']) // 只选择特定字段
                ->where(['id' => $id])                               // 根据 id 查询
                ->one(); // 获取一条记录
        $intro = $intro ? $intro->toArray() : [];
        // 将查询到的数据存入 Redis
        $redis->set($redisKey, json_encode($intro, 320), ['EX' => empty($intro) ? 10 : 7200]);

        // 返回查询结果，如果没有找到返回空数组
        return $intro;
    }

    public function getList($input, $page, $size): array
    {
        // 初始化查询对象
        $query = self::find()
                ->select(['id', 'title', 'status', 'intro', 'ctime', 'utime']) // 选择需要的字段
                ->orderBy(['id' => SORT_DESC]); // 按id降序排列

        // 动态生成查询条件
        $this->applyConditions($query, $input);

        // 获取总记录数
        $total = $query->count();

        // 设置分页参数
        list($offset) = CUtil::pagination($page, $size);
        $query->offset($offset)->limit($size);

        // 获取当前页数据
        $list = $query->asArray()->all();

        // 返回分页数据
        return [
                'total' => $total,
                'list'  => $list,
        ];
    }

    /**
     * @param $query
     * @param $input
     * @return void
     * 动态生成查询条件
     */
    private function applyConditions($query, $input)
    {
        if (!empty($input['title'])) {
            $query->andWhere(['like', 'title', $input['title']]); // 模糊查询
        }
    }


}
