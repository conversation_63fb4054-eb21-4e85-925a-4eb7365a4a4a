<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * E3+订单表
 */
class E3OrderModel extends CommModel
{
    
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_e3_order`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }
    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData, $gData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();

            // 保存商品表数据
            foreach ($gData as $v) {
                list($status,$msg) = by::E3OrderGoodModel()->SaveLog($v);
                if (!$status) {
                    throw new \Exception($msg);
                }
            }
            $trans->commit();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'E3Order.info');
            return [false, '保存操作失败'];
        }
    }

    public function getOrderByNo($order_no){
        $item = self::find()
            ->where(['order_no' => $order_no])
            ->one();
        return $item ? $item->toArray() : [];
    }

    public function updateStatusByOrderNo($order_no, $status){
        $item = self::findOne(['order_no' => $order_no,'status' => 0]);
        if($item){
            $item->status = $status;
            return $item->save();
        }else{
            return false;
        }
    }
}