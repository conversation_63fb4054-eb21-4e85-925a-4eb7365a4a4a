<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品属性值
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GavModel extends CommModel {

    public $tb_fields = [
        'id','ak_id','at_val','is_del','at_image'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gav`";
    }

    /**
     * @param $ak_id
     * @param $at_val
     * @return string
     * 通过attrid 获取规格
     */
    private function __getOneAvKey($ak_id, $at_val): string
    {
        return AppCRedisKeys::getOneAvByAkId($ak_id, $at_val);
    }

    /**
     * @param $id
     * @return string
     * 通过id获取数据
     */
    private function __getOneAvByIdKey($id): string
    {
        return AppCRedisKeys::getOneAvById($id);
    }

    /**
     * @param $ak_id
     * @return mixed
     * 属性值列表
     */
    private function __getAvListKey($ak_id) {
        return AppCRedisKeys::getAvList($ak_id);
    }

    /**
     * @param $id
     * @return int
     * 缓存清理
     */
    private function __delIdCache($id): int
    {
        $r_key = $this->__getOneAvByIdKey($id);
        return  by::redis('core')->del($r_key);
    }

    /**
     * @param $ak_id
     * @param $at_val
     * @return int
     * 缓存清理
     */
    private function __delCache($ak_id,$at_val): int
    {
        $r_key = $this->__getOneAvKey($ak_id, $at_val);
        return  by::redis('core')->del($r_key);
    }

    /**
     * @param $ak_id
     * @return int
     * 列表缓存清理
     */
    private function __delListCache($ak_id): int
    {
        $r_key = $this->__getAvListKey($ak_id);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 增加规格增改
     */
    public function SaveLog(array $aData): array
    {
        $ak_id    = $aData['ak_id'] ?? "";     //规格id
        $at_val   = $aData['at_val'] ?? "";    //属性值
        $at_image = $aData['at_image'] ?? "";  //属性值图片
        if (empty($ak_id) || empty($at_val)) {
            return [false, "参数缺失"];
        }

        $db      = by::dbMaster();
        $tb      = self::tbName();

        $aInfo   = $this->GetOneByAkVal($ak_id,$at_val,false);//根据 ID 和 规格值名称查数据
        //https://wpm-cdn.dreame.tech/images/2023010/908524-1696831526871.jpg

        //如果相等 则不需要改变  不等需要改变
        if ($at_image && $at_image === ($aInfo['at_image'] ?? '')) {
            return [true, $aInfo['id']]; // 存在数据 并且 图片相等 直接返回ID
        }

        $save = [
            'ak_id'    => $ak_id,
            'at_val'   => $at_val,
            'at_image' => $at_image,
        ];

        if ($aInfo) {
            $ret = $db->createCommand()->update($tb, ['at_image' => $at_image], ['id' => $aInfo['id']])->execute();
        } else {
            $ret = $db->createCommand()->insert($tb, $save)->execute();
        }


        if (!$ret) {
            return [false, '规格属性值添加失败'];
        }

        $av_id = $db->getLastInsertID(); //新增成功的规格属性值id

        $this->__delIdCache($av_id);
        $this->__delCache($ak_id, $at_val);
        $this->__delListCache($ak_id);

        return [true, $av_id];
    }


    /**
     * @param int $ak_id
     * @param string $at_val
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 查询单条
     */
    public function GetOneByAkVal(int $ak_id=0, string $at_val='', $cache = true): array
    {
        $ak_id   = CUtil::uint($ak_id);

        $redis   = by::redis('core');
        $r_key   = $this->__getOneAvKey($ak_id, $at_val);

        !$cache && $redis->del($r_key);

        $id      = $redis->get($r_key);

        if($id === false) {
            $tb      = $this::tbName();

            list($where,$params) = $this->__getCondition($ak_id,$at_val);

            $sql     = "SELECT `id` FROM  {$tb} WHERE {$where}  LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,$params)->queryOne();
            $id      = $aData['id'] ?? 0;
            $cache && $redis->set($r_key,$id, 1800);
        }

        if(!$id) {
            return [];
        }

        return $this->GetOneById($id);
    }

    /**
     * @param int $ak_id
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 根据ak_id查询列表
     */
    public function GetListByAkId(int $ak_id=0, $cache = true): array
    {
        $ak_id   = CUtil::uint($ak_id);
        $redis   = by::redis('core');
        $r_key   = $this->__getAvListKey($ak_id);

        !$cache && $redis->del($r_key);

        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $fields  = implode(',',$this->tb_fields);

            $sql     = "SELECT {$fields} FROM  {$tb} WHERE `ak_id` = :ak_id AND `is_del` = 0";
            $aData   = by::dbMaster()->createCommand($sql,[':ak_id' => $ak_id])->queryAll();
            $cache && $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $ak_id
     * @param string $at_val
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $ak_id=0, string $at_val=''): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        if($ak_id) {
            $where              .= " AND `ak_id` =:ak_id";
            $params[":ak_id"]    = $ak_id;
        }

        if($at_val) {
            $where              .= " AND `at_val` = :at_val";
            $params[":at_val"]   = $at_val;

            $where              .= " AND `is_del`=:is_del";
            $params[':is_del']   = 0;
        }

        return [$where,$params];
    }

    /**
     * @param int $ak_id
     * @param string $at_val
     * @return array
     * @throws \yii\db\Exception
     * 编辑规格属性值
     */
    public function UpdateLog(int $ak_id=0, string $at_val=''): array
    {
        $ak_id  = CUtil::uint($ak_id);

        $aLog    = $this->GetOneByAkVal($ak_id,$at_val);

        $tb      = self::tbName();
        $ret     = by::dbMaster()->createCommand()->update($tb,['is_del'=>1],['id' => $aLog['id']])->execute();

        if (!$ret) {
            return [false, '未知原因，规格属性值修改失败'];
        }

        $this->__delIdCache($aLog['id']);
        $this->__delCache($ak_id, $at_val);
        $this->__delListCache($ak_id);
        return [true, 'ok'];
    }

    /**
     * @param $id
     * @return array|false
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id)
    {
        $id      = CUtil::uint($id);

        $redis   = by::redis('core');
        $r_key   = $this->__getOneAvByIdKey($id);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id'=>$id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $av_id
     * @return array
     * @throws \yii\db\Exception
     * 根据id转换属性配置
     */
    public function IdToName(int $av_id)
    {
        $av_id   = CUtil::uint($av_id);
        $avData  = $this->GetOneById($av_id);
        if(empty($avData)) return [];

        $akData  = by::Gak()->GetOneById($avData['ak_id']);

        $data    = [
            'at_name' => $akData['at_name'],
            'at_val'  => $avData['at_val'],
        ];

        return $data;
    }


    /**
     * 获取商品属性值（无缓存）
     * @param array $ids
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public function getListByIds(array $ids)
    {
        if (empty($ids)) {
            return [];
        }
        $tb     = $this::tbName();
        $fields = implode("`,`", $this->tb_fields);
        $ids    = implode(',', $ids);
        $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id` IN ({$ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }


}
