<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 用户邀请礼品发放模型
 *
 * @property int $id 主键
 * @property int $inviter_id 邀请人ID
 * @property int $rule_id 关联 invite_gift_rules 表的ID
 * @property int $rule_gift_item_id 关联 invite_rule_gift_items 表的ID
 * @property string $gift_id 奖品ID
 * @property int $gift_type 奖品类型
 * @property int $quantity 奖品数量
 * @property int $claim_sequence 用户对该规则的领取次数序列，例如第一次领取为1，第二次为2
 * @property int $issued_at 发放时间（秒时间戳）
 * @property int $status 状态 0=未发放, 1=已发放
 * @property int $created_at 创建时间（秒时间戳）
 * @property int $updated_at 最后修改时间（秒时间戳）
 */
class UserInviteGiftsModel extends CommModel
{
    // 发放状态
    const STATUS = [
        'PENDING' => 0, // 未发放
        'ISSUED' => 1, // 已发放
    ];

    // 奖品类型 - 与InviteRuleGiftItemsModel保持一致
    const GIFT_TYPE = [
        'GIFT_CARD' => 1, // 礼品卡
        'COUPON' => 2, // 优惠券
        'POINTS' => 3, // 积分
        'GOODS' => 4, // 商品
        'SHOPPING_VOUCHER' => 5, // 购物金
        'CONSUME_MONEY' => 6,// 消费金
        'GOLD' => 8, // 金币
    ];

    public $tb_fields = [
        'id', 'inviter_id', 'rule_id', 'rule_gift_item_id', 'gift_id',
        'gift_type', 'quantity', 'claim_sequence', 'issued_at', 'status', 'created_at', 'updated_at'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_gifts`";
    }

    /**
     * 根据邀请人ID获取礼品列表
     * @param int $inviterId 邀请人ID
     * @param int|null $status 状态筛选
     * @return array
     * @throws Exception
     */
    public function getGiftsByInviterId(int $inviterId, int $status = null): array
    {
        $where = "inviter_id = :inviter_id";
        $params = [':inviter_id' => $inviterId];

        if ($status !== null) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE {$where}
                ORDER BY created_at DESC";

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据规则ID获取礼品列表
     * @param int $ruleId 规则ID
     * @param int|null $status 状态筛选
     * @return array
     * @throws Exception
     */
    public function getGiftsByRuleId(int $ruleId, int $status = null): array
    {
        $where = "rule_id = :rule_id";
        $params = [':rule_id' => $ruleId];

        if ($status !== null) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE {$where}
                ORDER BY created_at DESC";

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据ID获取礼品记录
     * @param int $id 礼品记录ID
     * @return array
     * @throws Exception
     */
    public function getGiftById(int $id): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " WHERE id = :id LIMIT 1";
        $result = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
        return $result ?: [];
    }

    /**
     * 创建礼品发放记录
     * @param array $data 礼品数据
     * @return array
     * @throws Exception
     */
    public function createGift(array $data): array
    {
        $now = time();
        $data['created_at'] = $now;
        $data['updated_at'] = $now;

        // 设置默认状态为未发放
        $data['status'] = $data['status'] ?? self::STATUS['PENDING'];

        // 验证必填字段
        $requiredFields = ['inviter_id', 'rule_id', 'rule_gift_item_id', 'gift_id', 'gift_type', 'quantity'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return [false, "字段 {$field} 不能为空"];
            }
        }

        // 验证数量
        if ($data['quantity'] <= 0) {
            return [false, '奖品数量必须大于0'];
        }

        try {
            $fields = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO " . self::tbName() . " ({$fields}) VALUES ({$placeholders})";

            $result = by::dbMaster()->createCommand($sql, $data)->execute();

            if ($result > 0) {
                $insertId = by::dbMaster()->getLastInsertID();
                return [true, $insertId];
            } else {
                return [false, '礼品记录创建失败'];
            }
        } catch (\Exception $e) {
            return [false, '礼品记录创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 批量创建礼品发放记录
     * @param int $inviterId 邀请人ID
     * @param array $giftItems 礼品项列表
     * @return array
     * @throws Exception
     */
    public function batchCreateGifts(int $inviterId, array $giftItems): array
    {
        if (empty($giftItems)) {
            return [false, '礼品项列表不能为空'];
        }

        $db = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            $insertedIds = [];

            foreach ($giftItems as $item) {
                $item['inviter_id'] = $inviterId;
                list($status, $result) = $this->createGift($item);

                if (!$status) {
                    throw new \Exception($result);
                }

                $insertedIds[] = $result;
            }

            $transaction->commit();
            return [true, $insertedIds];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 更新礼品记录状态
     * @param int $id 礼品记录ID
     * @param int $status 新状态
     * @return array
     * @throws Exception
     */
    public function updateGiftStatus(int $id, int $status): array
    {
        $updateData = [
            'status' => $status,
            'updated_at' => time(),
        ];

        // 如果是发放状态，设置发放时间
        if ($status == self::STATUS['ISSUED']) {
            $updateData['issued_at'] = time();
        }

        return $this->updateGift($id, $updateData);
    }

    /**
     * 更新礼品记录
     * @param int $id 礼品记录ID
     * @param array $data 更新数据
     * @return array
     * @throws Exception
     */
    public function updateGift(int $id, array $data): array
    {
        if (empty($data)) {
            return [false, '更新数据不能为空'];
        }

        $data['updated_at'] = time();

        try {
            $setParts = [];
            $params = [':id' => $id];

            foreach ($data as $field => $value) {
                $setParts[] = "{$field} = :{$field}";
                $params[":{$field}"] = $value;
            }

            $setClause = implode(', ', $setParts);
            $sql = "UPDATE " . self::tbName() . " SET {$setClause} WHERE id = :id";

            $result = by::dbMaster()->createCommand($sql, $params)->execute();

            if ($result > 0) {
                return [true, '礼品记录更新成功'];
            } else {
                return [false, '礼品记录更新失败或数据无变化'];
            }
        } catch (\Exception $e) {
            return [false, '礼品记录更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 检查用户是否已经领取过某个规则的奖励（兼容最大领取次数功能）
     * @param int $inviterId 邀请人ID
     * @param int $ruleId 规则ID
     * @return bool 如果用户已达到该规则的最大领取次数则返回true，否则返回false
     * @throws Exception
     */
    public function hasReceivedRuleGift(int $inviterId, int $ruleId): bool
    {
        // 获取规则信息
        $ruleModel = by::InviteGiftRulesModel();
        $rule = $ruleModel->getRuleById($ruleId);

        if (empty($rule)) {
            return false;
        }

        $maxClaims = $rule['max_claims'] ?? 1;

        // 如果max_claims为0，表示不限制领取次数，始终返回false（可以继续领取）
        if ($maxClaims == 0) {
            return false;
        }

        // 获取用户已领取次数
        $userClaimCount = $this->getUserRuleClaimCount($inviterId, $ruleId);

        // 如果已领取次数达到或超过最大次数，返回true（不能再领取）
        return $userClaimCount >= $maxClaims;
    }

    /**
     * 获取用户对某个规则的详细领取状态
     * @param int $inviterId 邀请人ID
     * @param int $ruleId 规则ID
     * @return array 包含详细状态信息的数组
     * @throws Exception
     */
    public function getRuleClaimStatus(int $inviterId, int $ruleId): array
    {
        // 获取规则信息
        $ruleModel = by::InviteGiftRulesModel();
        $rule = $ruleModel->getRuleById($ruleId);

        if (empty($rule)) {
            return [
                'rule_exists' => false,
                'max_claims' => 0,
                'current_claims' => 0,
                'can_claim' => false,
                'remaining_claims' => 0,
                'is_unlimited' => false,
            ];
        }

        $maxClaims = $rule['max_claims'] ?? 1;
        $currentClaims = $this->getUserRuleClaimCount($inviterId, $ruleId);
        $isUnlimited = $maxClaims == 0;
        $canClaim = $isUnlimited || $currentClaims < $maxClaims;
        $remainingClaims = $isUnlimited ? -1 : max(0, $maxClaims - $currentClaims);

        return [
            'rule_exists' => true,
            'max_claims' => $maxClaims,
            'current_claims' => $currentClaims,
            'can_claim' => $canClaim,
            'remaining_claims' => $remainingClaims, // -1表示无限制
            'is_unlimited' => $isUnlimited,
        ];
    }

    /**
     * 获取用户对某个规则的领取次数
     * @param int $inviterId 邀请人ID
     * @param int $ruleId 规则ID
     * @return int
     * @throws Exception
     */
    public function getUserRuleClaimCount(int $inviterId, int $ruleId): int
    {
        $sql = "SELECT COUNT(DISTINCT claim_sequence) as count FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id AND rule_id = :rule_id";

        $params = [
            ':inviter_id' => $inviterId,
            ':rule_id' => $ruleId,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int)($result['count'] ?? 0);
    }

    /**
     * 获取用户对某个规则的下一个领取序列号
     * @param int $inviterId 邀请人ID
     * @param int $ruleId 规则ID
     * @return int
     * @throws Exception
     */
    public function getNextClaimSequence(int $inviterId, int $ruleId): int
    {
        $sql = "SELECT COALESCE(MAX(claim_sequence), 0) + 1 as next_sequence FROM " . self::tbName() . " 
                WHERE inviter_id = :inviter_id AND rule_id = :rule_id";

        $params = [
            ':inviter_id' => $inviterId,
            ':rule_id' => $ruleId,
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int)($result['next_sequence'] ?? 1);
    }

    /**
     * 根据规则ID列表获取待发放的奖励记录
     * @param array $ruleIds 规则ID列表
     * @param int $limit 限制数量
     * @return array
     * @throws Exception
     */
    public function getPendingGiftsByRuleIds(array $ruleIds, int $limit = 100): array
    {
        if (empty($ruleIds)) {
            return [];
        }

        $ruleIdStr = implode(',', array_map('intval', $ruleIds));
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE rule_id IN ({$ruleIdStr}) 
                AND status = :status 
                ORDER BY created_at ASC 
                LIMIT :limit";

        $params = [
            ':status' => self::STATUS['PENDING'],
            ':limit' => $limit,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 获取用户奖励历史（分页）
     * @param int $inviterId 邀请人ID
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @return array
     * @throws Exception
     */
    public function getGiftHistoryByInviter(int $inviterId, int $page = 1, int $pageSize = 20): array
    {
        $offset = ($page - 1) * $pageSize;

        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                     WHERE inviter_id = :inviter_id";
        $countResult = by::dbMaster()->createCommand($countSql, [':inviter_id' => $inviterId])->queryOne();
        $total = (int)($countResult['count'] ?? 0);

        // 获取列表
        $listSql = "SELECT * FROM " . self::tbName() . " 
                    WHERE inviter_id = :inviter_id 
                    ORDER BY created_at DESC 
                    LIMIT {$pageSize} OFFSET {$offset}";
        $list = by::dbMaster()->createCommand($listSql, [':inviter_id' => $inviterId])->queryAll() ?: [];

        // 补充奖品类型名称和状态名称
        foreach ($list as &$item) {
            $item['gift_type_name'] = $this->getGiftTypeName($item['gift_type']);
            $item['status_name'] = $this->getStatusName($item['status']);
            $item['issued_at_str'] = $item['issued_at'] ? date('Y-m-d H:i:s', $item['issued_at']) : '';
        }

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 获取奖品类型名称
     * @param int $giftType 奖品类型
     * @return string
     */
    public function getGiftTypeName(int $giftType): string
    {
        $typeNames = [
            self::GIFT_TYPE['GIFT_CARD'] => '礼品卡',
            self::GIFT_TYPE['COUPON'] => '优惠券',
            self::GIFT_TYPE['POINTS'] => '积分',
            self::GIFT_TYPE['GOODS'] => '商品',
        ];

        return $typeNames[$giftType] ?? '未知类型';
    }

    /**
     * 获取状态名称
     * @param int $status 状态
     * @return string
     */
    public function getStatusName(int $status): string
    {
        $statusNames = [
            self::STATUS['PENDING'] => '待发放',
            self::STATUS['ISSUED'] => '已发放',
        ];

        return $statusNames[$status] ?? '未知状态';
    }

    /**
     * 获取用户已领取的奖励统计
     * @param int $inviterId 邀请人ID
     * @param int|null $giftType 奖品类型筛选
     * @return array
     * @throws Exception
     */
    public function getGiftStatistics(int $inviterId, int $giftType = null): array
    {
        $where = "inviter_id = :inviter_id";
        $params = [':inviter_id' => $inviterId];

        if ($giftType !== null) {
            $where .= " AND gift_type = :gift_type";
            $params[':gift_type'] = $giftType;
        }

        $sql = "SELECT 
                    gift_type,
                    status,
                    COUNT(*) as count,
                    SUM(quantity) as total_quantity
                FROM " . self::tbName() . " 
                WHERE {$where}
                GROUP BY gift_type, status
                ORDER BY gift_type, status";

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 获取礼品列表（分页）
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param int|null $inviterId 邀请人ID筛选
     * @param int|null $status 状态筛选
     * @param int|null $giftType 奖品类型筛选
     * @return array
     * @throws Exception
     */
    public function getGiftList(int $page = 1, int $pageSize = 20, int $inviterId = null, int $status = null, int $giftType = null): array
    {
        $offset = ($page - 1) * $pageSize;

        $where = "1=1";
        $params = [];

        if ($inviterId !== null) {
            $where .= " AND inviter_id = :inviter_id";
            $params[':inviter_id'] = $inviterId;
        }

        if ($status !== null) {
            $where .= " AND status = :status";
            $params[':status'] = $status;
        }

        if ($giftType !== null) {
            $where .= " AND gift_type = :gift_type";
            $params[':gift_type'] = $giftType;
        }

        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM " . self::tbName() . " WHERE {$where}";
        $countResult = by::dbMaster()->createCommand($countSql, $params)->queryOne();
        $total = (int)($countResult['count'] ?? 0);

        // 获取列表
        $listSql = "SELECT * FROM " . self::tbName() . " 
                    WHERE {$where}
                    ORDER BY created_at DESC 
                    LIMIT {$pageSize} OFFSET {$offset}";
        $list = by::dbMaster()->createCommand($listSql, $params)->queryAll() ?: [];

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 批量发放礼品
     * @param array $giftIds 礼品记录ID列表
     * @return array
     * @throws Exception
     */
    public function batchIssueGifts(array $giftIds): array
    {
        if (empty($giftIds)) {
            return [false, '礼品ID列表不能为空'];
        }

        $db = by::dbMaster();
        $transaction = $db->beginTransaction();

        try {
            $now = time();
            $giftIdPlaceholders = implode(',', array_fill(0, count($giftIds), '?'));
            $sql = "UPDATE " . self::tbName() . " 
                    SET status = ?, issued_at = ?, updated_at = ?
                    WHERE id IN ({$giftIdPlaceholders}) AND status = ?";

            $params = array_merge([self::STATUS['ISSUED'], $now, $now], $giftIds, [self::STATUS['PENDING']]);
            $result = by::dbMaster()->createCommand($sql, $params)->execute();

            $transaction->commit();
            return [true, "成功发放 {$result} 个礼品"];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, '批量发放礼品失败：' . $e->getMessage()];
        }
    }

    /**
     * 获取待发放的礼品数量
     * @param int|null $inviterId 邀请人ID筛选
     * @return int
     * @throws Exception
     */
    public function getPendingGiftCount(int $inviterId = null): int
    {
        $where = "status = :status";
        $params = [':status' => self::STATUS['PENDING']];

        if ($inviterId !== null) {
            $where .= " AND inviter_id = :inviter_id";
            $params[':inviter_id'] = $inviterId;
        }

        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " WHERE {$where}";
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int)($result['count'] ?? 0);
    }
} 