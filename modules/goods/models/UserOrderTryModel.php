<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use yii\db\DataReader;
use yii\db\Exception;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class UserOrderTryModel extends CommModel
{
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_uo_try`";
    }

    const TRY_STATUS = [
        'WAIT_PAY'    => 0,//待支付
        'HAS_CANCEL'  => 50,//已取消
        'WAIT_TRY'    => 100,//待体验
        'ON_TRY'      => 200,//体验中
        'WAIT_BACK'   => 300,//待退机
        'BACK'        => 400,//已退机
        'WAIT_DEDUCT' => 500,//待扣款
        'FINISHED'    => 600,//已完成
        'DEDUCTED'    => 700,//扣款完成
        'DEDUCT_FAIL' => 800,//扣款失败
    ];

    const PHONE_SALT = '%try_order%';

    const INFORM_BEGIN_TIME =  YII_ENV_PROD ? 1720108800 : 1719990037;

    const TRY_TIME_PERIOD = YII_ENV_PROD ? 86400 : 60;

    const TRY_STATUS_NAME = [
        0   => '待支付',
        50  => '已取消',
        100 => '待体验',
        200 => '体验中',
        300 => '待退机',
        400 => '已退机',
        500 => '待扣款',
        600 => '已完成',
        700 => '扣款完成',
        800 => '扣款失败'
    ];


    const EXCEPETION_STATUS = [
        'TIMEOUT_NOT_TRY' =>1,
        'NOT_ACTIVATE'    =>2,
        'TIMEOUT_NOT_BACK'=>3,
        'DEDUCT_FAIL'     =>4,
    ];

    const EXCEPETION_STATUS_NAME = [
        1 => '超时未试用',
        2 => '尚未激活',
        3 => '超时未退机',
        4 => '扣款失败',
    ];


    const USER_TRY_STATUS = [
        1 => ['WAIT_TRY'],
        2 => ['ON_TRY', 'WAIT_BACK'],
        3 => ['FINISHED', 'WAIT_DEDUCT', 'DEDUCTED', 'BACK', 'DEDUCT_FAIL'],
        4 => ['WAIT_PAY']
    ];

    //用户试用列表状态TAB
    const USER_APPLY_STATUS = [
        'ALREADY_ORDER' => 1,//已下单
        'TRYING'        => 2,//体验中
        'TRY_FINISH'    => 3,//体验完成
        'APPLY_EXPIRED' => 4,//申请失效
        'APPLY'         => 5,//已申请
    ];

    /**
     * 保存用户试用订单详情
     */
    public function SaveLog($data)
    {
        if (empty($data['order_no']) || empty($data['user_id'])) {
            return [false, '参数错误'];
        }
        //存在则更新，不存在则插入
        $data['user_id'] = CUtil::uint($data['user_id']);
        $fields          = array_keys($data);
        $fields          = implode("`,`", $fields);
        $rows            = implode("','", $data);
        $dup             = [];
        foreach ($data as $key => $value) {
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup = implode(' , ', $dup);
        $tb  = self::tbName();
        $sql = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";
        $res = by::dbMaster()->createCommand($sql)->execute();
        if ($res === false) {
            return [false, '保存失败'];
        }
        return [true, '保存成功'];
    }

    public function PhoneEncrypt($phone)
    {
        return CUtil::encrypt($phone, self::PHONE_SALT);
    }

    public function PhoneDecrypt($phone)
    {
        return CUtil::decrypt($phone, self::PHONE_SALT);
    }


    public function GetOneInfo(array $input,bool $format_price = true)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, $params)->queryOne();
        if ($result) {
            if($format_price){
                $result['amount'] = CUtil::totalFee($result['amount'], 1);
                $result['fund_amount'] = CUtil::totalFee($result['fund_amount'] ?? 0, 1);
                $result['credit_amount'] = CUtil::totalFee($result['credit_amount'] ?? 0, 1);
            }
            return $result;
        } else {
            return []; // 如果没有查询到数据，返回空数组
        }
    }

    public function GetList(array $input, bool $format_price = true)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause.' ORDER BY `ctime` DESC';

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, $params)->queryAll() ?? [];
        if($result){
            foreach ($result as &$item){
                if($format_price){
                    $item['amount'] = CUtil::totalFee($item['amount'], 1);
                }
            }
        }
        return $result;
    }

    public function GetCount(array $input)
    {
        $tb = self::tbName();
        //查询数据
        $select = "SELECT COUNT(*) FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 执行查询并返回结果
        return by::dbMaster()->createCommand($select, $params)->queryScalar() ?? 0;
    }

    /**
     * @param $userId
     * @return array|DataReader
     * @throws Exception
     * 获取单个用户所有完成订单
     */
    public function GetUserAllOrder($userId)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb} WHERE `user_id` = :user_id AND `try_status` != '' ORDER BY `ctime` DESC";
        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, [':user_id'=>$userId])->queryAll();
        if ($result) {
            return $result ;
        } else {
            return []; // 如果没有查询到数据，返回空数组
        }
    }


    /**
     * @param $userId
     * @return array|DataReader
     * @throws Exception
     * 根据条件更新数据
     */
    public function SaveUserOrderTryByType($user_id,$order_no,$type,$update_data){
        $info = $this->GetOneInfo([
            CUtil::buildCondition('user_id', '=', $user_id),
            CUtil::buildCondition('order_no', '=', $order_no),
        ]);
        if($info){
            if($type == 'arrival_time' && $info['try_status'] == self::TRY_STATUS['WAIT_TRY']){
                $oldTime = $info['arrival_time'] ?? 0;
                $newTime = $update_data['arrival_time'] ?? 0;
                $update_data['user_id'] = $user_id;
                $update_data['order_no'] = $order_no;
                $update_data['utime'] = time();
                $update_data['try_status'] = self::TRY_STATUS['ON_TRY'];//体验中
                if($newTime && (empty($oldTime) || $oldTime > $newTime)){
                    return $this->SaveLog($update_data);
                }
            }
        }
        return [true, '保存成功'];
    }


    public function GetUnActivateOrder($type,$input = [])
    {
        $tb = self::tbName();

        // 构建SQL查询
        $sql = "SELECT * FROM {$tb}";

        // 准备查询参数
        $params = [];

        // 构建条件
        $where = $this->__condition($type,$input, $params);

        if (!empty($where)) {
            $sql .= " WHERE $where";
        }

        // 执行查询并返回结果
        $orders = by::dbMaster()->createCommand($sql)->bindValues($params)->queryAll();
        return $orders;
    }

    private function __condition($type,$input, &$params)
    {
        $day        = $input['day'] ?? 3;
        $user_id    = $input['user_id'] ?? 0;
        $try_status = $input['try_status'] ?? -1;

        //避免测试数据过多，只查询2024年7月1日之后的数据
        $informBeginTime = self::INFORM_BEGIN_TIME;
        $where           = " `ctime` >  '{$informBeginTime}' AND";
        switch ($type) {
            case "activateInform":
                if (!empty($day)) {
                    // 计算X天前的时间戳
                    $nowTime       = time();
                    $timeThreshold = $nowTime - ($day * self::TRY_TIME_PERIOD); // 86400秒等于1天

                    // 添加到条件中
                    $where .= " `arrival_time` > 0 AND `arrival_time` < :timeThreshold  AND `register_time` = 0 AND `refund_no` = ''";

                    // 添加到查询参数中
                    $params[':timeThreshold'] = $timeThreshold;
                }
                break;
            case "unArriveInform":
                $where .= " `arrival_time` = 0 AND `register_time` = 0";
                break;
            case "tryEndInform":
            case "returnProductInform":
                $where .= " (`arrival_time` > 0 OR `register_time` > 0)";
                break;
        }



        if (intval($try_status)> -1) {
            // 添加到查询参数中
            $params[':try_status'] = $try_status;

            // 添加到条件中
            $where .= empty($where) ? '' : ' AND';
            $where .= " `try_status` = :try_status";
        }

        if (!empty($user_id)) {
            // 添加到查询参数中
            $params[':user_id'] = $user_id;

            // 添加到条件中
            $where .= empty($where) ? '' : ' AND';
            $where .= " `user_id` = :user_id";
        }

        return $where;
    }




    public function GetExceptionOrderList(array $input, $page=1, $page_size=20)
    {
        $tb     = self::tbName();
        $tbMain = byNew::TryOrdersModel()->getStr($input);
        $tbAct  = byNew::ActivityTypeModel()::tableName();
        list($offset) = CUtil::pagination($page, $page_size);

        list($where, $params) = $this->__getCondition($input);
        //查询数据
        $sql = "SELECT `try`.*,`main`.`status`,`act`.`validity`,`act`.`delivery_period`,`act`.`return_period`
                FROM {$tb} AS `try` INNER JOIN {$tbMain} AS `main` ON `try`.`order_no` = `main`.`order_no` and `try`.`user_id` = `main`.`user_id`
                INNER JOIN {$tbAct} AS `act` ON `try`.`ac_id` = `act`.`ac_id` 
                WHERE {$where} ORDER BY `try`.`ctime` DESC LIMIT {$offset},{$page_size}";
        return by::dbMaster()->createCommand($sql, $params)->queryAll();        
    }



    public function __getCondition($input): array
    {
        //SQL初始化条件
        $where           = "1=:init";

        $goods_name  = $input['goods_name'] ?? '';
        $sku         = $input['sku'] ?? '';
        $order_no    = $input['order_no'] ?? '';
        $order_nos   = $input['order_nos'] ?? [];
        $refund_no   = $input['refund_no'] ?? '';
        $status      = $input['status'] ?? 0;
        $source      = $input['source'] ?? -1;
        $order_time  = $input['order_time'] ?? ['st' => 0, 'ed' => 0];
        $p_sources   = $input['p_sources'] ?? '';
        $order_type  = $input['order_type'] ?? '';
        $user_iden   = $input['user_iden'] ?? '';
        $label       = $input['label'] ?? -1;
        $tryStatus   = $input['try_status'] ?? -1;

        //订单状态必须是已经发货，没有发货的订单不会有试用订单
        $where .= " AND `main`.`status` > :main_status";

        //默认异常状态数据
        // `try_status` =  self::TRY_STATUS['WAIT_TRY']  1 待体验 并且 `try`.`arrival_time` 为空 并且 `try`.`pay_time` + `act`.`delivery_period` * 86400 < UNIX_TIMESTAMP()  有效期内未激活
        // 或者 `try_status` 在待扣款 扣款失败 和扣款完成 之间  4 7
        $conditions = [
            "(`try`.`try_status` = :try_status1 AND (`try`.`arrival_time` = 0 OR `try`.`register_time` = 0 ) AND `try`.`pay_time` + `act`.`delivery_period` * :time_period < UNIX_TIMESTAMP())",
            "(`try`.`try_status` IN (:try_status2,:try_status3))"
        ];

        
        $where .= ' AND (' . implode(' OR ', $conditions) . ')';

        $params = [
            ':init'        => 1,
            ':time_period' => self::TRY_TIME_PERIOD,
            ':main_status' => by::Omain()::ORDER_STATUS['WAIT_SEND'],
            ':try_status1' => self::TRY_STATUS['WAIT_TRY'],
            ':try_status2' => self::TRY_STATUS['WAIT_DEDUCT'],
            ':try_status3' => self::TRY_STATUS['DEDUCT_FAIL'],
        ];

        if (!empty($goods_name)) {
            $where .= " AND `try`.`goods_name` LIKE :goods_name";
            $params[':goods_name'] = '%' . $goods_name . '%';
        }

        if (!empty($sku) || !empty($order_nos)) {
            // 多规格找主sku
            $sku = by::Gspecs()->GetMainSkuBySpecsSku($sku);

            // 获取积分商品和上架/下架商品信息
            $waresGid = by::GoodsMainModel()->GetList(['name' => $goods_name, 'sku' => $sku], 1, 9999999);
            $gids     = by::Gmain()->GetList(1, 9999999, '', -1, -1, $goods_name, $sku);

            // 合并订单号
            $orderNos = array_merge(
                by::Ogoods()->getAllOrderByGids($waresGid, 2),
                by::Ogoods()->getAllOrderByGids($gids),
                $order_nos
            );

            $orderNos = implode("','", $orderNos);
            $where    .= " AND `try`.`order_no` IN ('{$orderNos}')";
        }


        //注意范围
        if (!empty($order_no)) {
            //order_no 必须为数字组合的字符串
            $order_no = trim($order_no);
            if (is_numeric($order_no)) {
                $where               .= " AND `try`.`order_no`=:order_no";
                $params[":order_no"] = $order_no;
            }
        }

        if(!empty($refund_no)) {
            //$refund_no 必须为数字组合的字符串
            $refund_no = trim($refund_no);
            if (is_numeric($refund_no)) {
                $where               .= " AND `try`.`refund_no`=:refund_no";
                $params[":refund_no"] = $refund_no;
            }
        }

        if (!empty($user_iden)) {
            if (strlen($user_iden) == 11 && CUtil::uint($user_iden) != 0) {
                $uids = by::Phone()->GetUidsByPhone($user_iden);
                if (!empty($uids)) {
                    $uids  = implode(',', $uids);
                    $where .= " AND `try`.`user_id` IN ({$uids})";
                } else {
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where .= " AND `try`.`user_id` = -1";
                }
            } elseif (CUtil::uint($user_iden) == 0) {
                //获取MallInfo
                $mallInfo = by::usersMall()->getMallInfoByUid($user_iden, false);
                $mallId   = $mallInfo['id'] ?? 0;
                $userInfo = by::Phone()->getInfoByMallId($mallId);
                if ($userInfo) {
                    //获取用户
                    $user_id = $userInfo['user_id'] ?? 0;
                    $where   .= " AND `try`.`user_id` = {$user_id}";
                } else {
                    $where .= " AND `try`.`user_id` = -1";
                }
            } else {
                $where              .= " AND `try`.`user_id` = :user_id";
                $params[':user_id'] = $user_iden;
            }

        }

        if ($status >= 0) {
            list($w, $p) = by::Omain()->GetOrderStatus($status,true);

            if ($w) {
                $where  .= " AND `main`.{$w}";
                $params = array_merge($params, $p);
            }
        }

        if ($source > -1) {
            $where             .= " AND `main`.`source` = :source";
            $params[":source"] = $source;
        }

        if (!empty($order_type)) {
            $where           .= " AND `main`.`type` = :type";
            $params[':type'] = $order_type;
        }

        if (intval($p_sources) != '-1' && !empty($p_sources)) {
            $p_sources = trim($p_sources);
            $p_sources = implode("','", explode(',', $p_sources));
            $where     .= " AND `main`.`platform_source` in ('{$p_sources}')";
        }

        if (!empty($order_time['st']) && !empty($order_time['ed'])) {
            $where               .= " AND `main`.`ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"] = $order_time['st'];
            $params[":order_ed"] = $order_time['ed'];
        }

    
        if (!empty($label)){
            $where .= " AND `try`.`label` =:label ";
            $params[':label'] = $label;
        }

        if ($tryStatus >= 0) {
            $where .= " AND `try`.`try_status` = :try_status";
            $params[':try_status'] = $tryStatus;
        }


        return [$where, $params];
    }


    public function GetExceptionListCount($input)
    {
        $tb     = self::tbName();
        $tbMain = byNew::TryOrdersModel()->getStr($input);
        $tbAct  = byNew::ActivityTypeModel()::tableName();
        list($where, $params) = $this->__getCondition($input);
        //查询数据
        $sql = "SELECT count(*)
                FROM {$tb} AS `try` INNER JOIN {$tbMain} AS `main` ON `try`.`order_no` = `main`.`order_no` and `try`.`user_id` = `main`.`user_id`
                INNER JOIN {$tbAct} AS `act` ON `try`.`ac_id` = `act`.`ac_id` WHERE {$where}";
        $command             = by::dbMaster()->createCommand($sql,$params);
        $count               = $command->queryScalar();

        return intval($count);
    }


    /**
     * @param $user_id
     * @param $ac_id
     * @param string $phone_str
     * @return bool
     * @throws Exception
     * 判断用户是否有试用订单
     */
    public function CheckUserHasTry($user_id, $ac_id, string $phone_str='')
    {
        $tryS = false;
        if(empty($phone_str)){
            //1.查出用户手机号
            $phone = by::Phone()->GetPhoneByUid($user_id);
            if (empty($phone)) {
                return false;
            }
            //2.手机号加密字符串
            $phone_str = $this->PhoneEncrypt($phone);
        }

        //3.查询用户是否有试用订单
        $orderInfo = $this->GetOneInfo([
            CUtil::buildCondition('phone_str', '=', $phone_str),
            CUtil::buildCondition('ac_id', '=', $ac_id),
            CUtil::buildCondition('try_status', '!=', self::TRY_STATUS['HAS_CANCEL'])
        ]);

        if ($orderInfo) {
            $tryS = true;
        }

        return $tryS;
    }

}
