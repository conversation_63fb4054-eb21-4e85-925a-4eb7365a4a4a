<?php

namespace app\modules\goods\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class IotProductModel extends CommModel
{
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_iot_product`";
    }


    private function __getIotProductListKey(): string
    {
        return AppWRedisKeys::GetIotProductList();
    }

    public function GetOneInfo(array $input)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, $params)->queryOne();
        return $result ?? [];
    }


    public function GetList(array $input, $page = 1, $page_size = 10000)
    {
        $tb     = self::tbName();
        $r_key   = $this->__getIotProductListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {

            //查询数据
            $select = "SELECT * FROM {$tb}";
            $params = [];

            // 构建 WHERE 子句
            $whereClause = $this->buildWhereClause($input, $params);
            // 构建完整的 SQL 语句
            $select .= $whereClause;

            // 执行查询并返回结果
            $aData = by::dbMaster()->createCommand($select, $params)->queryAll() ?? [];

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 1800);
        }
        return $aData;
    }

    public function GetCount(array $input)
    {
        $tb = self::tbName();
        //查询数据
        $select = "SELECT COUNT(*) FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 执行查询并返回结果
        return by::dbMaster()->createCommand($select, $params)->queryScalar() ?? 0;
    }

  

}
