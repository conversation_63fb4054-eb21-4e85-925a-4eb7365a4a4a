<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;

/**
 * 短信模板模型
 */
class SmsTmplModel extends BaseModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public static $tb_fields = [
        'id', 'code', 'name', 'content', 'tmpl_url', 'is_del', 'ctime'
    ];

    /**
     * 表名称
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame`.`t_sms_tmpl`";
    }

    /**
     * 获取全部
     * @param $columns
     * @return array
     */
    public function getSmsTmplList($columns): array
    {
        // 获取数据（从缓存中获取）
        $items = self::__getAllTmpl();
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)->select($columns);
    }

    /**
     * 根据 code 获取模板数据
     * @param string $code
     * @param array $columns
     * @return array
     */
    public static function getSmsTmplByCode(string $code, array $columns): array
    {
        // 获取数据（从缓存中获取）
        $items = self::__getAllTmpl();
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->where('code', $code)
            ->select($columns);
    }

    /**
     * 删除 redis 缓存
     */
    public function delSmsTmplCache()
    {
        $r_key = self::__getSmsTmplKey();
        by::redis('core')->del($r_key);
    }

    /**
     * 获取所有模板（Aside-Cache：旁路缓存）
     * @return array
     */
    private static function __getAllTmpl(): array
    {
        // redis 缓存
        $redis = by::redis();
        $r_key = self::__getSmsTmplKey();

        // 获取，从缓存中获取数据
        $res = $redis->get($r_key);

        // 缓存中有数据，直接返回
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 获取 redis key
     * dreame|getParamsKey
     * @return string
     */
    private static function __getSmsTmplKey(): string
    {
        return AppCRedisKeys::getSmsTmplList();
    }
}