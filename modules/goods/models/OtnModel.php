<?php
/**
 * Created by IntelliJ IDEA.
 * User: CP
 * Date: 2023/10/12
 * 以旧换新记录表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class OtnModel extends CommModel {


    public static function tbName($order_no): string
    {
        $time = by::Omain()->GetTbNameByOrderId($order_no, false);
        $year = date("Y",intval($time));
        return  "`db_dreame_goods`.`t_otn_{$year}`";
    }

    public $tb_fields = [
        'id','order_no','user_id','image','ctime'
    ];

    /**
     * @param $order_no
     * @return string
     * 以旧换新数据缓存
     */
    private function __getOneInfoKey($order_no): string
    {
        return AppCRedisKeys::getOneOtnInfo($order_no);
    }

    /**
     * @param $order_no
     * @return int
     * 清理以旧换新数据
     */
    public function DelCache($order_no) : int
    {
        $r_key1 = $this->__getOneInfoKey($order_no);
        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, array $arr)
    {

        $save = [
            'order_no' => $order_no,
            'user_id'  => $user_id,
            'image'    => $arr['image'],
            'ctime'    => $arr['ctime'] ?? time(),
        ];

        $tb = $this->tbName($order_no);

        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        return [true, 'ok'];
    }

    public function GetOneByOrderNo(string $order_no)
    {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($order_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName($order_no);
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->queryOne();
            $aData   = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),600);
        }

        if (empty($aData)) {
            return [];
        }

        return $aData;
    }

}
