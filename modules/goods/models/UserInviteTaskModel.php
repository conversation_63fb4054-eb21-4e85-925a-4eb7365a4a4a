<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 邀请任务表模型
 *
 * 表结构：`db_dreame_goods`.`invite_task`
 * - id BIGINT PK AI
 * - inviter_id  BIGINT      邀请人ID
 * - invitee_id  BIGINT      被邀请人ID
 * - invite_type TINYINT     邀请类型 1=一元秒杀 …
 * - sub_type    VARCHAR(50) 邀请子类型，默认 new_user
 * - relate_id   BIGINT      与邀请类型关联的外键ID（如拼团ID、秒杀ID）
 * - invited_at  BIGINT      邀请时间（秒时间戳）
 * - device_id   VARCHAR(50) 设备ID
 * - remark      VARCHAR(255) 备注
 *
 * 约束与索引：
 * - UNIQUE(invitee_id, sub_type)
 * - INDEX(inviter_id)
 * - INDEX(invite_type, relate_id)
 */
class UserInviteTaskModel extends CommModel
{
    // 默认子类型
    const DEFAULT_SUB_TYPE = 'new_user';

    // 常见邀请类型（按需扩展）
    const INVITE_TYPE = [
        'ONE_YUAN_SECKILL' => 1, // 一元秒杀
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_task`";
    }

    /**
     * 新增邀请记录（唯一键冲突时覆盖部分字段）
     *
     * ON DUPLICATE KEY UPDATE 策略：当 (invitee_id, sub_type) 冲突时，更新 inviter_id、invite_type、relate_id、invited_at、device_id、remark
     * 返回 [bool, mixed] => [是否成功, 自增ID或已存在记录的ID/错误信息]
     */
    public function upsertInvite(
        int    $inviterId,
        int    $inviteeId,
        int    $inviteType,
        int    $relateId,
        string $subType = self::DEFAULT_SUB_TYPE,
        int    $invitedAt = 0,
        string $deviceId = '',
        string $remark = ''
    ): array
    {
        $invitedAt = $invitedAt > 0 ? $invitedAt : time();

        $sql = "INSERT INTO " . self::tbName() . "
            (inviter_id, invitee_id, invite_type, sub_type, relate_id, invited_at, device_id, remark)
            VALUES (:inviter_id, :invitee_id, :invite_type, :sub_type, :relate_id, :invited_at, :device_id, :remark)
            ON DUPLICATE KEY UPDATE
                inviter_id = VALUES(inviter_id),
                invite_type = VALUES(invite_type),
                relate_id = VALUES(relate_id),
                invited_at = VALUES(invited_at),
                device_id = VALUES(device_id),
                remark = VALUES(remark)";

        $params = [
            ':inviter_id'  => $inviterId,
            ':invitee_id'  => $inviteeId,
            ':invite_type' => $inviteType,
            ':sub_type'    => $subType,
            ':relate_id'   => $relateId,
            ':invited_at'  => $invitedAt,
            ':device_id'   => $deviceId,

            ':remark' => $remark,
        ];

        try {
            $cmd = by::dbMaster()->createCommand($sql, $params);
            $affected = $cmd->execute();

            // MySQL: 插入新行时返回自增ID；更新时 lastInsertId 可能为 0
            $lastId = (int)by::dbMaster()->getLastInsertID();
            if ($lastId > 0) {
                return [true, $lastId];
            }

            // 冲突更新场景，查回既有记录ID
            $exists = $this->getByInviteeSubType($inviteeId, $subType);
            if (!empty($exists)) {
                return [true, (int)$exists['id']];
            }

            return [false, '保存失败'];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    /** 获取单条：按唯一键 (invitee_id, sub_type) */
    public function getByInviteeSubType(int $inviteeId, string $subType = self::DEFAULT_SUB_TYPE): array
    {
        return self::find()
            ->where(['invitee_id' => $inviteeId, 'sub_type' => $subType])
            ->asArray()
            ->one() ?: [];
    }

    /** 判断是否已存在邀请关系（按 inviter, invitee, sub_type） */
    public function hasInvite(int $inviterId, int $inviteeId, string $subType = self::DEFAULT_SUB_TYPE): bool
    {
        $count = self::find()
            ->where([
                'inviter_id' => $inviterId,
                'invitee_id' => $inviteeId,
                'sub_type'   => $subType,
            ])
            ->count();
        return (int)$count > 0;
    }

    /**
     * 邀请列表（支持简单筛选与分页）
     * 支持的筛选键：inviter_id, invitee_id, invite_type, sub_type, relate_id, invited_at_start, invited_at_end
     */
    public function getList(array $params = [], int $page = 1, int $pageSize = 10, string $orderBy = 'id DESC'): array
    {
        $query = self::find();

        if (isset($params['inviter_id'])) {
            $query->andWhere(['inviter_id' => (int)$params['inviter_id']]);
        }
        if (isset($params['invitee_id'])) {
            $query->andWhere(['invitee_id' => (int)$params['invitee_id']]);
        }
        if (isset($params['invite_type'])) {
            $query->andWhere(['invite_type' => (int)$params['invite_type']]);
        }
        if (!empty($params['sub_type'])) {
            $query->andWhere(['sub_type' => (string)$params['sub_type']]);
        }
        if (isset($params['relate_id'])) {
            $query->andWhere(['relate_id' => (int)$params['relate_id']]);
        }
        if (!empty($params['invited_at_start'])) {
            $query->andWhere(['>=', 'invited_at', (int)$params['invited_at_start']]);
        }
        if (!empty($params['invited_at_end'])) {
            $query->andWhere(['<=', 'invited_at', (int)$params['invited_at_end']]);
        }

        $list = $query->orderBy($orderBy)
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->asArray()
            ->all();

        $total = (clone $query)->count();

        return ['list' => $list, 'total' => (int)$total];
    }

    /** 统计数量（同 getList 的筛选条件） */
    public function getCount(array $params = []): int
    {
        $query = self::find();

        if (isset($params['inviter_id'])) {
            $query->andWhere(['inviter_id' => (int)$params['inviter_id']]);
        }
        if (isset($params['invitee_id'])) {
            $query->andWhere(['invitee_id' => (int)$params['invitee_id']]);
        }
        if (isset($params['invite_type'])) {
            $query->andWhere(['invite_type' => (int)$params['invite_type']]);
        }
        if (!empty($params['sub_type'])) {
            $query->andWhere(['sub_type' => (string)$params['sub_type']]);
        }
        if (isset($params['relate_id'])) {
            $query->andWhere(['relate_id' => (int)$params['relate_id']]);
        }
        if (!empty($params['invited_at_start'])) {
            $query->andWhere(['>=', 'invited_at', (int)$params['invited_at_start']]);
        }
        if (!empty($params['invited_at_end'])) {
            $query->andWhere(['<=', 'invited_at', (int)$params['invited_at_end']]);
        }

        return (int)$query->count();
    }

    /** 按邀请人获取其邀请的被邀请用户ID列表（可筛选类型/子类型/关联ID） */
    public function getInviteeIdsByInviter(int $inviterId, array $filters = []): array
    {
        $query = self::find()->select(['invitee_id'])->where(['inviter_id' => $inviterId]);
        if (isset($filters['invite_type'])) {
            $query->andWhere(['invite_type' => (int)$filters['invite_type']]);
        }
        if (!empty($filters['sub_type'])) {
            $query->andWhere(['sub_type' => (string)$filters['sub_type']]);
        }
        if (isset($filters['relate_id'])) {
            $query->andWhere(['relate_id' => (int)$filters['relate_id']]);
        }
        $rows = $query->asArray()->all();
        return array_values(array_unique(array_column($rows, 'invitee_id')));
    }

    /** 获取某邀请类型+关联ID下的邀请记录列表 */
    public function getByRelate(int $inviteType, int $relateId): array
    {
        return self::find()
            ->where(['invite_type' => $inviteType, 'relate_id' => $relateId])
            ->orderBy(['invited_at' => SORT_DESC, 'id' => SORT_DESC])
            ->asArray()
            ->all();
    }
}


