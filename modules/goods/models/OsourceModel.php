<?php
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\main\models\CommModel;
use yii\db\DataReader;
use yii\db\Exception;


class OsourceModel extends CommModel
{

    CONST ListExpire = 600;

    CONST T_TYPE = [
        'DAY'       => 1,   //今日
        'MONTH'     => 2,   //当月
        'YEAR'      => 3,   //今年
    ];

    CONST GET_TYPE = [
        'ALL'  => 1, //所有
        'PAGE' => 2, //分页
    ];

    public $tb_fields = [
        'id', 'ouid', 'user_id', 'order_no', 'price', 'type','ctime'
    ];

    public static function tbName($time): string
    {
        $year = date("Y", intval($time));

        // 防止乱输入订单号 默认为当前年份
        if ($year < by::Omain()::DB_TIMEZONE['ST'] || $year > by::Omain()::DB_TIMEZONE['ED']) {
            $year = date('Y', time());
        }

        return "`db_dreame_goods`.`t_osource_{$year}`";
    }

    /**
     * @param $user_id
     * @return int
     * 列表缓存
     */
    private function __getListKey($user_id)
    {
        return AppCRedisKeys::getOsourceList($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 订单总金额缓存
     */
    private function __getSumKey($user_id)
    {
        return AppCRedisKeys::getOsourceSum($user_id);
    }

    /**
     * @param $ouid
     * @param $order_no
     * @return string
     * 详情唯一缓存KEY
     */
    private function __getOneInfoKey($ouid, $order_no): string
    {
        return AppCRedisKeys::getOneOsourceInfo($ouid, $order_no);
    }

    /**
     * @param $ouid
     * @param $order_no
     * @return string
     * 详情唯一缓存KEY
     */
    private function __getOuidByOrderKey($ouid, $order_no): string
    {
        return AppCRedisKeys::getOuidByOrderKey($ouid, $order_no);
    }

    /**
     * @param $user_id
     * @return int
     * @throws \RedisException
     * 清理缓存
     */
    public function DelCache($user_id): int
    {
        $r_key  = $this->__getListKey($user_id);

        return  by::redis('core')->del($r_key);
    }

    /**
     * @param $user_id
     * @param $status
     * @return int
     * @throws \RedisException
     * 删除订单总额缓存-未付款、已取消的状态不统计到订单总额
     */
    public function DelSumCache($user_id, $status=0): int
    {
        $a_status   = by::Omain()::ORDER_STATUS;

        //状态改为已付款后才删缓存
        if ($status == $a_status['WAIT_SEND']) {
            $r_key = $this->__getSumKey($user_id);

            return  by::redis('core')->del($r_key);
        }

        return 0;
    }

    /**
     * @param $ouid
     * @param $order_no
     * @return int
     * @throws \RedisException
     * 清理详情缓存
     */
    public function delInfoCache($ouid, $order_no): int
    {
        $r_key  = $this->__getOneInfoKey($ouid, $order_no);

        return  by::redis('core')->del($r_key);
    }







    /**
     * @param $order_no
     * @param $gudie_id
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog($order_no, $gudie_id, array $arr)
    {
        if (empty($gudie_id)) {
            return [false, '参数错误(4)'];
        }

        $user_id        = $arr['user_id']   ?? 0;
        $price          = $arr['price']     ?? 0;
        $ctime          = $arr['ctime']     ?? 0;
        $type           = $arr['type']     ?? 1;

        if (empty($user_id) || empty($ctime)) {
            return [false, '参数错误(3)'];
        }

        $is_guide = by::guide()->getGuideInfo($user_id);
        if (!empty($is_guide)) {
            return [false, '导购请直接下单'];
        }

        if ($gudie_id == $user_id) {
            return [false, '请分享给用户下单'];
        }

        //todo 判断ouid是否为导购 hack
        $save = [
            'ouid'      => $gudie_id,
            'user_id'   => $user_id,
            'order_no'  => $order_no,
            'price'     => $price,
            'type'      => $type,
            'ctime'     => $ctime,
        ];

        $tb     = self::tbName($ctime);
        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        $this->delInfoCache($gudie_id, $order_no);

        return [true, 'ok'];
    }

    /**
     * @param $ouid
     * @param $get_type
     * @param $t_type
     * @param $status
     * @param $page
     * @param $page_size
     * @param $year
     * @return array|DataReader
     * @throws Exception
     * @throws \RedisException
     * 订单列表
     */
    public function GetList($ouid, $get_type = self::GET_TYPE['PAGE'], $t_type = 1, $status = -1, $page = 1, $page_size = 10, $year = '')
    {
        $page    = CUtil::uint($page, 1);
        $redis   = by::redis('core');
        $r_key   = $this->__getListKey($ouid);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $get_type, $t_type, $status, $page, $page_size, $year);
        $aJson   = $redis->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $time                 = empty($year) ? time() : strtotime("{$year}0101");
            $tb                   = self::tbName($time);
            list($offset)         = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($ouid, $t_type, $status);

            switch ($get_type) {
                case self::GET_TYPE['ALL'] :
                    $sql = "SELECT `order_no`,`user_id` FROM {$tb} WHERE {$where}";

                    break;
                default :
                    $sql = "SELECT `order_no`,`user_id` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC LIMIT {$offset},{$page_size}";

                    break;
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, self::ListExpire);
        }

        return $aData;
    }

    /**
     * @param $ouid
     * @param $t_type
     * @param $status
     * @param $year
     * @param bool $is_stat
     * @return int
     * @throws Exception
     * @throws \RedisException
     * 订单总数
     */
    public function GetListCount($ouid, $t_type = 1, $status = -1, $year = '', bool $is_stat = true): int
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getListKey($ouid);
        $sub_key     = CUtil::getAllParams(__FUNCTION__, $t_type, $status, $year, $is_stat);
        $count       = $redis->hGet($r_key, $sub_key);

        if ($count === false) {
            $time                 = empty($year) ? time() : strtotime("{$year}0101");
            $tb                   = self::tbName($time);
            list($where, $params) = $this->__getCondition($ouid, $t_type, $status);

            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();

            if (!$is_stat && $status == -1) {
                $where .= " AND `status` > 100";
                $sql    = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
                $count  = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            }

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, self::ListExpire);
        }

        return intval($count);
    }


    /**
     * @param $ouid
     * @param $t_type
     * @param $status
     * @param $year
     * @return int
     * @throws Exception
     * @throws \RedisException
     * 订单总金额
     */
    public function GetListSum($ouid, $t_type = 1, $status = -1, $year = ''): int
    {
        $redis   = by::redis('core');
        $r_key   = $this->__getSumKey($ouid);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $t_type, $status, $year);
        $price   = $redis->hGet($r_key, $sub_key);

        if ($price === false) {
            $time                 = empty($year) ? time() : strtotime("{$year}0101");
            $tb                   = self::tbName($time);
            list($where, $params) = $this->__getCondition($ouid, $t_type, $status);

            if ($status == -1) {
                $where .= " AND `status` > 100";
            }

            $sql     = "SELECT sum(`price`) FROM {$tb} WHERE {$where}";
            $command = by::dbMaster()->createCommand($sql, $params);
            $price   = $command->queryScalar();

            by::redis('core')->hSet($r_key, $sub_key, $price);
            CUtil::ResetExpire($r_key, self::ListExpire);
        }

        return intval($price);
    }

    /**
     * @param $ouid
     * @param $t_type
     * @param $status
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($ouid, $t_type, $status = -1): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if (!empty($ouid)) {
            $where                 .= " AND `ouid`=:ouid";
            $params[":ouid"]        = $ouid;
        }

        switch ($t_type) {
            case self::T_TYPE['DAY']:
                $st     = strtotime("today");
                $ed     = strtotime("tomorrow");
                break;
            case self::T_TYPE['MONTH']:
                $st     = strtotime(date('Y-m-01 00:00:00'));
                $ed     = strtotime("+1 month", $st);
                break;
            case self::T_TYPE['YEAR']:
                $st     = strtotime(date('Y-01-01 00:00:00'));
                $ed     = strtotime("+1 year", $st);
                break;
        }

        if (!empty($st) && !empty($ed)) {
            $where               .= " AND `ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"]  = $st;
            $params[":order_ed"]  = $ed;
        }

        //订单状态
        if($status > -1) {
            list($w, $p) = by::Omain()->GetOrderStatus($status, true);

            if ($w) {
                $where .= " AND {$w}";
                $params = array_merge($params, $p);
            }
        }

        return [$where, $params];
    }


    /**
     * @param string $year
     * @param string $order_no
     * @param string $user_iden
     * @param array $order_time
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 管理后台展示
     */
    public function GetAdmList($year = '', $order_no = '', $user_iden = '', $order_time = [], $name = '', $page = 1, $page_size = 50)
    {
        $page                = CUtil::uint($page, 1);

        if ($order_no) {
            $time            = by::Omain()->GetTbNameByOrderId($order_no, false);
        } else {
            $time            = empty($year) ? time() : strtotime("{$year}0101");
        }
        $tb                  = self::tbName($time);

        list($offset)        = CUtil::pagination($page, $page_size);
        list($where, $params) = $this->__getAdmCondition($order_no, $user_iden, $order_time, $name);
        $sql                 = "SELECT `order_no`,`user_id`,`ouid` FROM {$tb} WHERE {$where} 
                                ORDER BY `id` DESC LIMIT {$offset},{$page_size}";

        $aData               = by::dbMaster()->createCommand($sql, $params)->queryAll();

        return $aData;
    }

    /**
     * @param string $year
     * @param string $order_no
     * @param string $user_iden
     * @param array $order_time
     * @return int
     * @throws Exception 订单总数-后台用
     */
    public function GetAdmListCount($year = '', $order_no = '', $user_iden = '', $order_time = [], $name = '')
    {
        if ($order_no) {
            $time            = by::Omain()->GetTbNameByOrderId($order_no, false);
        } else {
            $time            = empty($year) ? time() : strtotime("{$year}0101");
        }
        $tb                  = self::tbName($time);

        list($where, $params) = $this->__getAdmCondition($order_no, $user_iden, $order_time, $name);
        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $command             = by::dbMaster()->createCommand($sql, $params);
        $count               = $command->queryScalar();

        return intval($count);
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param array $order_time
     * @return array
     * @throws Exception
     * 后台规范化查询条件
     */
    private function __getAdmCondition($order_no = '', $user_iden = '', $order_time = [], $name=''): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if (!empty($order_no)) {
            $where               .= " AND `order_no`=:order_no";
            $params[":order_no"]  = $order_no;
        }

        if (!empty($user_iden)) {
            if (strlen($user_iden) == 11) {
                $uids = by::Phone()->GetUidsByPhone($user_iden);
                if (!empty($uids)) {
                    $uids    = implode(',', $uids);
                    $where  .= " AND `ouid` IN ({$uids})";
                } else {
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where  .= " AND `ouid` = -1";
                }
            } else {
                if(!is_numeric($user_iden) && intval($user_iden) == 0){
                    $userList = by::guide()->getList(1,100,'',$user_iden);
                    $uids = empty($userList) ? []: array_column($userList,'user_id');
                    if (!empty($uids)) {
                        $uids  = implode(',', $uids);
                        $where .= " AND `ouid` IN ({$uids})";
                    }
                }else{
                    $user_iden = intval($user_iden);
                    $where  .= " AND `ouid` = {$user_iden}";
                }
            }
        }

        if($name){
            $userList = by::guide()->getList(1,1000,0,$name);
            $uids = empty($userList) ? []: array_column($userList,'user_id');
            if (!empty($uids)) {
                $uids  = implode(',', $uids);
                $where .= " AND `ouid` IN ({$uids})";
            }else{
                $where .= " AND `ouid` = '-1'";
            }
        }


        if (!empty($order_time['st']) && !empty($order_time['ed'])) {
            $where               .= " AND `ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"]  = $order_time['st'];
            $params[":order_ed"]  = $order_time['ed'];
        }

        return [$where, $params];
    }

    /**
     * @param $ouid
     * @param $order_no
     * @return array|false
     * @throws Exception
     * 获取详情
     */
    private function __getInfoByOrderId($ouid, $order_no)
    {
        $ouid = CUtil::uint($ouid);
        if (empty($order_no) || empty($ouid)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($ouid, $order_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $time    = by::Omain()->GetTbNameByOrderId($order_no, false);
            $tb      = $this->tbName($time);
            $fields  = implode("`,`", $this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no AND `ouid`=:ouid LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':order_no' => $order_no, ':ouid' => $ouid])->queryOne();
            $aData   = $aData ?: [];
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }

    /**
     * @param $ouid
     * @param $user_id
     * @param $order_no
     * @return array|false|DataReader
     * @throws Exception
     * 获取导购订单详情
     */
    public function GetOrderInfo($ouid, $user_id, $order_no)
    {
        $ouid       = CUtil::uint($ouid);
        $user_id    = CUtil::uint($user_id);

        $aLog       = $this->__getInfoByOrderId($ouid, $order_no);
        if (empty($aLog) || $aLog['user_id'] != $user_id) {
            return [];
        }

        $info       = by::Ouser()->CommPackageInfo($user_id, $order_no, true, true, false, false);

        return $info;
    }

    /**
     * @param $user_id
     * @param $order_no
     */
    public function getOuidByOrder($user_id,$order_no){
        $user_id    = CUtil::uint($user_id);

        if (empty($order_no) || empty($user_id)) {
            return '';
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOuidByOrderKey($user_id, $order_no);
        $ouid        = $redis->get($redis_key);

        if ($ouid === false) {
            $time    = by::Omain()->GetTbNameByOrderId($order_no, false);
            $tb      = $this->tbName($time);
            $sql     = "SELECT `ouid` FROM {$tb} WHERE `order_no`=:order_no AND `user_id`=:user_id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':order_no' => $order_no, ':user_id' => $user_id])->queryOne();
            $ouid    = $aData['ouid'] ?? '';
            $redis->set($redis_key, $ouid, ['EX' => empty($ouid) ? 10 : 3600]);
        }

        return $ouid;
    }

    public function exportData($year = '', $order_no = '', $order_st = 0, $order_ed = 0, $user_iden = '', $job_no='',$viewSensitive = false)
    {
        $head   = [
            '导购员ID', '导购员名称','工号', '所属门店', '订单编号', '订单状态', '订单金额', '创建时间'
        ];

        $order_time = [
            'st' => $order_st ?? 0,
            'ed' => $order_ed ?? 0,
        ];

        if ($order_no) {
            $time            = by::Omain()->GetTbNameByOrderId($order_no, false);
        } else {
            $time            = empty($year) ? time() : strtotime("{$year}0101");
        }
        $tb                  = self::tbName($time);

        list($where, $params) = $this->__getAdmCondition($order_no, $user_iden, $order_time, $job_no);

        //导出

        $db      = by::dbMaster();
        $muser   = by::users();
        $minfo   = by::guide();
        $omain   = by::Omain();
        $mOuser  = by::Ouser();

        $id     = 0;
        $sql    = "SELECT `id`,`order_no`,`user_id`,`ouid` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $ret) {

                //导购员信息
                $user           = $muser->getOneByUid($ret['ouid']);

                $info           = $mOuser->GetInfoByOrderId($ret['user_id'], $ret['order_no']);

                //所属门店
                $s_info         = $minfo->getGuideInfo($ret['ouid']);

                //订单状态
                list(, $status)  = $omain->SplitOrderStatus($info['status']);

                $data[] = [
                    'ouid'      => $ret['ouid'],
                    'onick'     => '\''.($user['nick'] ?? ''),
                    'job_no'    => $s_info['job_no'] ?? '未知',
                    'store'     => $s_info['store'] ?? '未知',
                    'order_no'  => $info['order_no'] ?? '',
                    'status'    => $omain::STATUS_NAME[$status] ?? '未知',
                    'price'     => by::Gtype0()->totalFee($info['price'] ?? 0, 1),
                    'ctime'     => isset($info['ctime']) ? date("Y-m-d H:i:s", $info['ctime']) : 0,
                ];
            }
        }
        return $data;
    }



    public function exportGoodsData($year = '', $order_no = '', $order_st = 0, $order_ed = 0, $user_iden = '', $job_no='',$viewSensitive = false)
    {
        // 设置脚本的最大执行时间为10分钟
        ini_set('max_execution_time', '600');
        $head   = [
            '导购员ID', '导购员名称', '工号', '所属门店', '订单编号', '订单状态', '付款时间','商品名','商品类型','商品编号','规格','单价','数量','应付金额','优惠券金额','积分抵扣金额',
            '实付金额', '用户昵称', '用户手机号', '用户来源', '用户标签', '创建时间'
        ];

        $order_time = [
            'st' => intval($order_st ?? 0),
            'ed' => intval($order_ed ?? 0),
        ];

        if ($order_no) {
            $time            = by::Omain()->GetTbNameByOrderId($order_no, false);
        } else {
            $time            = empty($year) ? time() : strtotime("{$year}0101");
        }
        $tb                  = self::tbName($time);

        list($where, $params) = $this->__getAdmCondition($order_no, $user_iden, $order_time, $job_no);

        //导出

        $db      = by::dbMaster();
        $muser   = by::users();
        $mphone  = by::Phone();
        $minfo   = by::guide();
        $omain   = by::Omain();
        $mOuser  = by::Ouser();

        $orderGoodsModel   = by::Ogoods();
        $orderConfigModel  = by::Ocfg();
        $userExtendModel   = by::userExtend();
        $goodsTypeModel    = by::Gtype0();

        // 标签
        $tagMap = by::Gtag()->GetTagNameMap();
        $tagNames  = $tagMap;

        $id     = 0;
        $sql    = "SELECT `id`,`order_no`,`user_id`,`ouid`,`ctime` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            // 订单号集合
            $orderNos = array_column($list, 'order_no');
            $osourceItems = array_column($list,null,'order_no');

            // 用户集合
            $userIds = array_unique(array_column($list, 'user_id'));

            // 导购集合
            $ouids = array_unique(array_column($list, 'ouid'));

            // 导购用户数据
            $ouidStr = implode('|',$ouids);
            $ouidList = $minfo->getList(1,1000,$ouidStr,'');
            if($ouidList){
                $ouidListArr = [];
                foreach ($ouidList as $item){
                    $ouidListArr[] = $minfo->getGuideInfo($item['user_id']);
                }
                $ouidItems = array_column($ouidListArr,null,'user_id');
            }

            // 获取用户订单数据
            $userOrderItems = $mOuser->getListByUserIdsAndOrderNos($userIds, $orderNos);
            $userOrderItems = array_column($userOrderItems, null, 'order_no');

            // 获取订单商品数据
            $columns = ['order_no', 'gid', 'sid', 'num', 'oprice', 'price', 'cprice', 'coin_price', 'status'];
            $orderGoodsItems = $orderGoodsModel->getListByUserIdsAndOrderNos($userIds, $orderNos, $columns);


            // 获取订单配置数据
            $orderConfigItems = $orderConfigModel->getListByOrderNos($orderNos);

            // 获取属性值
            $orderAttributeItems = $orderConfigModel->getAttributeList($orderConfigItems);
            $orderAttributeItems = array_column($orderAttributeItems, 'at_val', 'id');

            // 订单配置数据格式化
            $orderConfigItems = $orderConfigModel->formatOrderConfigItems($orderConfigItems);

            // 获取用户的数据
            $userItems = $muser->getListByUserIds($userIds, ['user_id', 'nick']);
            $userItems = array_column($userItems, null, 'user_id');

            // 获取用户手机号数据
            $userPhoneItems = $mphone->getListByUserIds($userIds);
            $userPhoneItems = array_column($userPhoneItems, null, 'user_id');

            // 获取用户来源数据
            $userExtendItems = $userExtendModel->getListByUserIds($userIds, ['user_id', 'source', 'tag']);
            $userExtendItems = array_column($userExtendItems, null, 'user_id');


            // 拼装数据
            foreach ($orderGoodsItems as $item) {
                // 订单商品的信息
                $order_no = $item['order_no'];
                $gid      = $item['gid'];
                $sid      = $item['sid'];

                //导购员信息
                $osourceItem = $osourceItems[$order_no];
                $ouid = $osourceItem['ouid'] ?? 0;


                // 导购员信息
                $ouidItem = $ouidItems[$ouid]??[];


                // 用户订单信息
                $userOrderItem = $userOrderItems[$order_no];
                $userId = $userOrderItem['user_id'];

                // 订单状态
                $status = $item['status'];
                if ($item['status'] == $orderGoodsModel::STATUS['NORMAL']) {
                    $status = $userOrderItem['status'];
                }

                // 订单配置详情
                $orderConfigItem = $orderConfigItems[$order_no][$gid][$sid] ?? [];

                // 付款时间
                $pay_time = empty($userOrderItem['pay_time']) ? '' : date('Y-m-d H:i:s', $userOrderItem['pay_time']);

                // 商品标签名称
                $goods_tag_names = array_map(function ($tid) use ($tagNames) {
                    return $tagNames[$tid] ?? '';
                }, $orderConfigItem['tids'] ?? []);
                $goods_tag_names = implode('、', $goods_tag_names);

                // 商品属性值
                $attr = '';
                if (!empty($orderConfigItem['av_ids'])) {
                    $tmpAttr = [];
                    foreach ($orderConfigItem['av_ids'] as $av_id) {
                        $tmpAttr[] = $orderAttributeItems[$av_id] ?? '';
                    }
                    $attr = implode(',', $tmpAttr);
                }

                // 用户昵称
                $nick = $userItems[$userId]['nick'] ?? '';
                // 手机号
                $phone = $userPhoneItems[$userId]['phone'] ?? '';
                // 用户来源
                list($source)   = $userExtendModel->sourceConfig($userExtendItems[$userId]['source'] ?? '');

                // 结果集合
                $data[] = [
                    'ouid'      => $ouid,
                    'onick'     => '\''.($ouidItem['name'] ?? ''),
                    'job_no'    => $ouidItem['job_no'] ?? '未知',
                    'store'     => $ouidItem['store'] ?? '未知',
                    'order_no'  => $order_no . "\t",
                    'status'    => $omain::STATUS_NAME[$status] ?? '未知',
                    'pay_time'   => $pay_time,
                    'g_name'     => $orderConfigItem['name'],
                    'g_tids'     => $goods_tag_names,
                    'code'       => $orderConfigItem['sku'] ?? '',
                    'attr'       => $attr,
                    'g_uprice'   => $goodsTypeModel->totalFee($orderConfigItem['price'], 1),
                    'g_num'      => $item['num'],
                    'g_oprice'   => $goodsTypeModel->totalFee($item['oprice'], 1),
                    'cprice'     => $goodsTypeModel->totalFee($item['cprice'], 1),
                    'coin_price' => $goodsTypeModel->totalFee($item['coin_price'], 1),
                    'price'      => $goodsTypeModel->totalFee($item['price'], 1),
                    'nick'       => '\''.$nick,
                    'phone'      => $phone,
                    'u_source'   => $source,
                    'tag'        => $userExtendItems[$userId]['tag'] ?? '',
                    'ctime'      => isset($osourceItem['ctime']) ? date("Y-m-d H:i:s", $osourceItem['ctime']) : 0,
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);
        return $data;
    }
}
