<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单收货表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\components\ExpressTen;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;
use app\jobs\UserOrderTryJob;


class OfinishModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_ofinish`";
    }

    public $tb_fields = [
        'id',
        'user_id',
        'order_no',
        'ctime'
    ];

    /**
     * @param int $user_id
     * @param string $order_no
     * @param int $check_time
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, int $check_time)
    {
        $check_time = $check_time ?: time();

        $tb = $this->tbName();

        $sql = " INSERT IGNORE INTO {$tb} (`user_id`,`order_no`,`check_time`)
                          VALUE (:user_id, :order_no, :check_time)";

        by::dbMaster()->createCommand(
            $sql,
            [':user_id' => $user_id, ':order_no' => $order_no, ':check_time' => $check_time]
        )->execute();

        return [true, 'ok'];
    }

    /**
     * @param $order_no
     * @param array $notify_data
     * @return array
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 快递100回调
     */
    public function notify($order_no, $notify_data = [])
    {
        if(!YII_ENV_PROD && isset($notify_data['test'])){
            $state = 3;
            $nu = $order_no.'test';
            $data = [
                'lastResult' => [
                    'state' => $state,
                    'nu'    => $nu,
                    'data'  => [
                        ['status' => '签收', 'ftime' => date('Y-m-d H:i:s')]
                    ]
                ]
            ];
        }else{
            $sign = $notify_data['sign'] ?? '';
            $param = $notify_data['param'] ?? '';

            $nsign = md5($param . ExpressTen::SALT);

            if (strtolower($sign) != strtolower($nsign)) {
                return [false, '签名校验失败'];
            }

            $data = json_decode($param, true);

            $state = $data['lastResult']['state'] ?? 0;
            $nu = $data['lastResult']['nu'] ?? '';
        }

        if (empty($nu)) {
            return [false, '数据有误'];
        }

        //TODO 订单物流状态通知
        //state 1 揽件 0 在途 5 派件 3 签收 2 疑难
        switch ($state) {
            case 1:
                EventMsg::factory()->run('orderMsgSend', ['event' => 'shipped', 'order_no' => $order_no]);
                break;
            case 5:
                EventMsg::factory()->run('orderMsgSend', ['event' => 'dispatch', 'order_no' => $order_no]);
                break;
            case 3:
                EventMsg::factory()->run('orderMsgSend', ['event' => 'signed', 'order_no' => $order_no]);
                break;
            case 2:
                EventMsg::factory()->run('orderMsgSend', ['event' => 'abnormal', 'order_no' => $order_no]);
                break;
            default:
                break;
        }

        //保存最后一个状态
        $redis = by::redis();
        $r_key = AppCRedisKeys::expressStatus($nu);
        $ostate = $redis->get($r_key);
        $redis->set($r_key, $state);
        $redis->expireAt($r_key, strtotime('+6 days'));

        if ($state != 3 || $ostate == $state) {
            return [false, '未签收或已处理'];
        }

        $user_id = by::Omain()->GetUserIdByNo($order_no);
        if (empty($user_id)) {
            return [false, '无此订单号'];
        }

        foreach ($data['lastResult']['data'] as $val) {
            if ($val['status'] == '签收') {
                $ftime = $val['ftime'] ?? 0;
                $check_time = $ftime != 0 ? strtotime($ftime) : 0;
                break;
            }
        }

        $this->SaveLog($user_id, $order_no, $check_time ?? 0);

        // 判断是否是先试后买订单，显示后买订单直接已完成
        $info = byNew::UserOrderTry()->GetOneInfo([
            CUtil::buildCondition('user_id', '=', $user_id),
            CUtil::buildCondition('order_no', '=', $order_no),
        ]);

        if($info){// 先试后买订单直接已完成
            list($s,$msg) = by::Omain()->Finish($user_id, $order_no);
            if(!$s){
                return [false, $msg];
            }
        }

        return [true, 'ok'];
    }

    /**
     * @param $order_no
     * @return array
     * 单号是否签收
     * @throws Exception
     */
    private function __isCheckExpress($order_no)
    {
        $aInfo = by::Oad()->GetOneByOrderNo($order_no);
        if (empty($aInfo['mail_no'])) {
            return [false, '暂未发货'];
        }
        $aInfo['express_code'] = ExpressTen::factory()->getKD100ExpressCode($aInfo['express_code']);

        list($status, $aData) = ExpressTen::factory()->GetInfo($aInfo['mail_no'], $aInfo['express_code'], $aInfo['phone'] ?? '', '', '', 6);
        if (!$status) {
            return [false, $aData];
        }
        //更新物流信息
        by::Omain()->updateDataByOrderNo($order_no, $aData);


        $state = $aData['state'] ?? 0;

        if ($state != 3) {
            return [false, '暂未签收'];
        }

        $data = $aData['data'] ?? [];
        foreach ($data as $val) {
            if ($val['status'] == '签收') {
                $ftime = $val['ftime'] ?? 0;
                $check_time = $ftime != 0 ? strtotime($ftime) : 0;
                break;
            }
        }

        return [true, $check_time ?? 0];

    }

    /**
     * @throws \yii\db\Exception
     * 定时查询10天前已发货订单是否已签收
     */
    public function ShellCheck()
    {
        $days = '10'; //查n天之前待收货的数据
        $db = by::dbMaster();
        $mOmain = by::Omain();
        //分表可能跨年
        $this_year = date("Y");
        $years = [$this_year, date("Y", strtotime("-{$days} days"))];
        $years = array_unique($years);

        $id = 0;
        $stime = YII_ENV_PROD ? strtotime("-{$days} days") : strtotime("-1 minutes");
        $where = " `id` > :id AND `stime` < {$stime}";
        $params = [];

        list($w, $p) = $mOmain->GetOrderStatus($mOmain::ORDER_STATUS['WAIT_RECEIVE'], true);
        if ($w) {
            $where .= " AND {$w}";
            $params = array_merge($params, $p);
        }

        $orderState = CUtil::getConfig('need_query_order_state', 'express', MAIN_MODULE);
        if ($orderState) {
            $orderState = implode(',', $orderState);
            $where .= " AND `state` in ({$orderState})";
        }


        foreach ($years as $year) {
            //确定分表
            $date = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime = strtotime($date);
            $tb_main = by::Omain()::tbName($ctime);

            $sql = "SELECT `id`,`user_id`,`order_no`,`status` FROM {$tb_main} WHERE {$where} ORDER BY `id` LIMIT 100";

            while (true) {
                $params['id'] = $id;

                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id = $end['id'];

                foreach ($list as $val) {

                    list($s, $check_time) = $this->__isCheckExpress($val['order_no']);

                    if (!$s) {
                        continue;
                    }

                    $this->SaveLog($val['user_id'], $val['order_no'], $check_time);
                }
            }
        }
    }


    /**
     * @throws \yii\db\Exception
     * 定时将已签收7天的订单置为已完成
     */
    public function ShellFinish()
    {
        $days = '7'; //查n天之前待收货的数据
        $db = by::dbMaster();
        $tb = self::tbName();

        $id = 0;
        $check_time = YII_ENV_PROD ? strtotime("-{$days} days") : strtotime("-1 minutes");
        $where = " `id` > :id AND `check_time` < {$check_time}";
        $params = [];

        $sql = "SELECT `id`,`user_id`,`order_no` FROM {$tb} WHERE {$where} ORDER BY `id` LIMIT 100";

        while (true) {
            $params['id'] = $id;

            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id = $end['id'];

            foreach ($list as $val) {

                $trans = $db->beginTransaction();
                try {
                    by::Omain()->Finish($val['user_id'], $val['order_no']);

                    $db->createCommand()->delete($tb, ['id' => $val['id']])->execute();

                    $trans->commit();
                } catch (\Exception $e) {
                    $trans->rollBack();

                    CUtil::debug("{$val['order_no']}|" . $e->getMessage(), 'err.finish');
                }

            }
        }
    }
}
