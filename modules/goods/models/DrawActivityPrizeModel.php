<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;
use RedisException;
use Throwable;
use yii\db\Exception;

//抽奖活动-奖品（关联）表
class DrawActivityPrizeModel extends CommModel
{
    use ModelTrait;
    
    public static function getInstance(): DrawActivityPrizeModel
    {
        return byNew::DrawActivityPrize();
    }
    
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_draw_activity_prize`";
    }

    public static $tb_fields = [
        'id', 'activity_id', 'prize_id', 'prize_num', 'prize_limit', 'prize_issue_num', 'prize_lock_num', 'rate', 'is_default', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'NO'  => 0,
        'YES' => 1
    ];

    // 是否默认奖品，如果抽中的奖品数量为空，则中默认的奖品
    const IS_DEFAULT = [
        'NO'  => 0,
        'YES' => 1
    ];

    public function __deletePrizeListCache($acId): bool
    {
        $cacheKey = $this->getPrizeListCacheKey($acId);
        $redis = by::redis('core');
        $redis->del($cacheKey);
        return true;
    }


    public function getPrizeListCacheKey($acId): string
    {
        return AppCRedisKeys::getPrizeListCacheKey($acId);
    }


    /**
     * 获取奖品列表，用缓存
     * 返回结果：
     * Array
     * (
     *   [0] => Array
     *   (
     *     [id] => 1
     *     [activity_id] => 1
     *     [prize_id] => 1
     *     [prize_num] => 99
     *     [prize_limit] => 3
     *     [prize_issue_num] => 1
     *     [rate] => 50.00
     *     [is_default] => 1
     *     [is_del] => 0
     *     [ctime] => 1696736281
     *     [utime] => 1696736281
     *     [dtime] => 0
     *   )
     * )
     * @param int $acId
     * @return array
     * @throws RedisException
     */
    public function getPrizeListByAcId(int $acId): array
    {
        $redis = by::redis('core');
        $redisKey = $this->getPrizeListCacheKey($acId);

        // 尝试从 Redis 获取缓存数据
        $cachedData = $redis->hGetAll($redisKey);
        if (!empty($cachedData)) {
            return array_map('json_decode', $cachedData, array_fill(0, count($cachedData), true));
        }

        // 从数据库获取数据
        $prizes = self::find()
            ->from(self::tbName())
            ->select(self::$tb_fields)
            ->where(['activity_id' => $acId, 'is_del' => self::IS_DEL['NO']])
            ->asArray()
            ->all();

        if ($prizes) {
            $redisPipeline = $redis->multi(2);
            foreach ($prizes as $prize) {
                $redisPipeline->hSet($redisKey, $prize['prize_id'], json_encode($prize, JSON_UNESCAPED_UNICODE));
            }
            $redisPipeline->expire($redisKey, 1800);
            $redisPipeline->exec();
        }

        return $prizes;
    }
    
    public function doCreate(array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('抽奖任务奖品添加失败2');
            }
            
            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 更新
     * @param int $id 抽奖活动奖品ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $data['utime'] = time() + 1;
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException('抽奖活动奖品保存失败');
            }

            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 删除
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['dtime' => time(), 'is_del' => 1], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }
            
            if (empty($resp)) {
                throw new BusinessException('删除抽奖奖品失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 根据活动模块ID获取活动商品列表
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function getListByRelationId(int $moduleRelationId): array
    {
        $tb = self::tbName();
        return self::find()->from($tb)->where(['module_relation_id' => $moduleRelationId, 'dtime' => 0])->asArray()->all();
    }
    
    /**
     * 根据活动模块ID获取活动商品IDs
     * @param int $moduleRelationId
     * @return array
     */
    public function getIdsByRelationId(int $moduleRelationId): array
    {
        $res = $this->getListByRelationId($moduleRelationId);
        
        return array_column($res, 'id');
    }

    /**
     * 重置默认奖品
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function resetDefaultPrize(int $moduleRelationId): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $resp = $db->createCommand()->update($tb, ['is_default' => 0, 'utime' => time()], ['module_relation_id' => $moduleRelationId])->execute();
            if (empty($resp)) {
                throw new BusinessException('重置默认奖品失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
}
