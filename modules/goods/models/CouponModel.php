<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 卡券表
 */
class CouponModel extends CommModel
{
    public $tb_fields = [
            'id', 'name', 'image', 'code', 'validity', 'type', 'stock_num', 'appid', 'app_path', 'originid', 'origin_path', 'short_chain', 'create_at', 'update_at'
    ];

    public static $get_fields = [
            'id', 'name', 'image', 'validity', 'type', 'appid', 'app_path', 'originid', 'origin_path', 'short_chain'
    ];

    const TYPE = [
            1 => '停车券',
            2 => '京东E卡'
    ];


    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_coupon`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @return array
     * 主表增改
     */
    public function SaveLog(array $aData): array
    {
        $db    = by::dbMaster();
        $tb    = $this->tbName();
        $trans = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb, $aData)->execute();
            $trans->commit();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug('保存操作失败:' . $e->getMessage(), 'coupon.info');
            return [false, '保存操作失败'];
        }
    }

    public function add($aData)
    {
        $db = by::dbMaster();
        $tb = $this->tbName();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb, $aData)->execute();

            return true;
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:' . $e->getMessage(), 'coupon.info');
            return false;
        }
    }

    public function getList($params, $page, $pageSize)
    {
        $tb = $this->tbName();
        list($offset) = CUtil::pagination($page, $pageSize);
        list($where, $params) = $this->__getCondition($params);
        $fields  = implode("`,`", $this->tb_fields);
        $sql     = "SELECT `{$fields}` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$pageSize}";
        $command = by::dbMaster()->createCommand($sql, $params);
        $aData   = $command->queryAll();
        $aData   = empty($aData) ? [] : $aData;
        return $aData;
    }

    public function getCount($params)
    {
        $tb = $this->tbName();
        list($where, $params) = $this->__getCondition($params);
        $sql     = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $command = by::dbMaster()->createCommand($sql, $params);
        $count   = $command->queryScalar();
        return $count;
    }

    public function getInfoById($id)
    {
        $tb     = $this->tbName();
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id`=:id LIMIT 1";
        $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
        $aData  = empty($aData) ? [] : $aData;
        return $aData;
    }

    /**
     * @param $data
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($data): array
    {

        $where                 = "is_deleted=:is_deleted";
        $params[':is_deleted'] = 0;

        if (isset($data['name']) && !empty($data['name'])) {
            $where           .= " AND (`name` LIKE :name )";
            $params[":name"] = "%{$data['name']}%";
        }

        if (isset($data['code']) && !empty($data['code'])) {
            $where           .= " AND (`code` LIKE :code )";
            $params[":code"] = "%{$data['code']}%";
        }
        if (isset($data['create_begin_at']) && !empty($data['create_begin_at'])) {
            $where                      .= " AND (`create_at` >= :create_begin_at )";
            $params[":create_begin_at"] = $data['create_begin_at'];
        }
        if (isset($data['create_end_at']) && !empty($data['create_end_at'])) {
            $where                    .= " AND (`create_at` <= :create_end_at )";
            $params[":create_end_at"] = $data['create_end_at'];
        }
        if (isset($data['begin_validity']) && !empty($data['begin_validity'])) {
            $where                     .= " AND (`validity` >= :begin_validity )";
            $params[":begin_validity"] = $data['begin_validity'];
        }
        if (isset($data['end_validity']) && !empty($data['end_validity'])) {
            $where                   .= " AND (`validity` <= :end_validity )";
            $params[":end_validity"] = $data['end_validity'];
        }

        return [$where, $params];
    }
}