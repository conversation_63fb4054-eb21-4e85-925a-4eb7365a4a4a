<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品规格
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class GspecsModel extends CommModel
{
    const DECIMAL_RANGE = "99999999.99"; //价格上限

    public static function tbName(): string
    {
        return '`db_dreame_goods`.`t_gspecs`';
    }

    public $tb_fields = [
            'id', 'gid', 'sku', 'price', 'av_ids', 'image', 'is_del'
    ];

    /**
     * @param $gid
     * @return string
     * 根据gid获取属性列表
     */
    private function __getSpecsListByGid($gid,$isDelete): string
    {
        return AppCRedisKeys::getSpecsListByGid($gid,$isDelete);
    }

    /**
     * @param $sku
     * @return string
     * 根据 sku 获取数据
     */
    private function __getSpecsbySkuKey($sku): string
    {
        return AppCRedisKeys::getSpecsbySku($sku);
    }

    /**
     * @param $gid
     * @param $id
     * @return string
     * 根据id获取数据
     */
    private function __getSpecsbyIdKey($gid, $id): string
    {
        return AppCRedisKeys::getSpecsbyId($gid, $id);
    }

    /**
     * @param $sku
     * @return int
     * 删除sku缓存
     */
    private function __delSkuCache($sku): int
    {
        $r_key = $this->__getSpecsbySkuKey($sku);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @param $id
     * @return int
     * 删除id缓存
     */
    private function __delIdCache($gid, $id): int
    {
        $r_key = $this->__getSpecsbyIdKey($gid, $id);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @return int
     * 列表缓存清理
     */
    private function __delListCache($gid): int
    {
        $r_key        = $this->__getSpecsListByGid($gid, true);
        $r_key_delete = $this->__getSpecsListByGid($gid, false);
        return by::redis('core')->del($r_key, $r_key_delete);
    }

    /**
     * @param string $str
     * @param $ck_code
     * @param string $str :json [{"at_val":"颜色:红色,尺码:s","sku":"12211","price":"0.01","image":"http://abc.jpg"}]
     * @return array
     * @return array
     * @throws Exception
     * 校验商品sku参数
     */
    public function checkSpecs(string $str, $ck_code)
    {
        $arr = json_decode($str, true);
        if (!is_array($arr)) {
            return [false, "属性sku参数有误"];
        }

        foreach ($arr as $k => $v) {
            $at_val = $v['at_val'] ?? "";
            $sku    = $v['sku'] ?? "";
            $stock  = $v['stock'] ?? 0;
            $price  = $v['price'] ?? 0;
            $image  = $v['image'] ?? "";

            if (empty($at_val) || empty($sku) || empty($price) || empty($image)) {
                return [false, "属性sku参数有误-缺少必要参数"];
            }

            $price = sprintf("%.2f", $price);
            if (bccomp($price, self::DECIMAL_RANGE, 2) > 0 || bccomp($price, 0, 2) <= 0) {
                return [false, "属性sku参数有误-非法价格"];
            }

            //设置价格
            $pcombines            = $v['pcombines'] ?? []; //设置价格组合 数组
            $arr[$k]['pcombines'] = $pcombines;

            //todo erp查库存-优化点：可一次性查
            $lockOldErp = CUtil::omsLock(0, time());
            if ($lockOldErp) {
                //查它自身的库存
                $stock_num = $stock;
            } else {
                list($s, $stock_num) = Erp::factory()->getStockNew($sku, $ck_code, true);
                if (!$s) {
                    $stock_num = 0;
                }
            }

            $arr[$k]['stock'] = $stock_num;
        }

        return [true, $arr];
    }

    /**
     * @param string $sku
     * @param bool $cache
     * @return array
     * @throws Exception
     * 根据sku查询
     */
    public function GetOneBySku(string $sku, $cache = true, $spriceType = 0): array
    {
        if (empty($sku)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getSpecsbySkuKey($sku);

        !$cache && $redis->del($r_key);

        $aJson = $redis->get($r_key);
        $aData = (array) json_decode($aJson, true);

        if ($aJson === false) {
            $tb    = $this::tbName();
            $sql   = "SELECT `id`,`gid` FROM  {$tb} WHERE `sku`=:sku AND `is_del`=0 LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [':sku' => $sku])->queryOne();
            $aData = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $this->GetOneById($aData['gid'], $aData['id'], false, $cache, $spriceType);
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 增加商品sku
     */
    public function SaveLog(array $aData): array
    {
        $gid    = $aData['gid'] ?? 0;
        $av_ids = $aData['av_ids'] ?? "";
        $sku    = $aData['sku'] ?? "";
        $price  = $aData['price'] ?? 0;
        $image  = $aData['image'] ?? "";

        $price = sprintf("%.2f", $price);

        if (empty($gid) || empty($av_ids) || empty($sku) || empty($price) || empty($image)) {
            return [false, "增加属性sku失败-缺少必要参数"];
        }

        $aLog = $this->GetOneBySku($sku, false);
        if ($aLog) {
            return [false, "规格编号{$sku}已经存在"];
        }

        $save = [
                'gid'    => $gid,
                'av_ids' => $av_ids,
                'sku'    => $sku,
                'price'  => by::Gtype0()->totalFee($price),
                'image'  => $image,
        ];

        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id  = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，属性sku新增失败"];
        }

        $this->__delSkuCache($sku);
        $this->__delIdCache($gid, $id);
        $this->__delListCache($gid);
        return [true, "ok"];
    }

    /**
     * @param $gid
     * @param $id
     * @param $format_price
     * @param $cache
     * @param $spriceType
     * @return array|\yii\db\DataReader
     * @throws Exception
     * 根据id获取数据
     */
    public function GetOneById($gid, $id, $format_price = true, $cache = true, $spriceType = 0)
    {
        $gid = CUtil::uint($gid);
        $id  = CUtil::uint($id);

        if (empty($gid) || empty($id)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getSpecsbyIdKey($gid, $id);

        !$cache && $redis->del($r_key);

        $aJson = $redis->get($r_key);
        $aData = (array) json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(",", $this->tb_fields);
            $sql    = "SELECT {$fields} FROM  {$tb} WHERE `id`=:id AND `gid`=:gid LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id, ':gid' => $gid])->queryOne();
            $aData  = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        $aData['original_price'] = $aData['price'] ?? 0;
        if ($format_price == true) {
            $aData['price']          = by::Gtype0()->totalFee($aData['price'], 1);
            $aData['original_price'] = $aData['price'];
        }
        //获取当前设置价格
        $aData['pcombines'] = by::Gsprice()->GetListBySku($aData['sku'] ?? 0, $format_price);
        $typeData           = by::Gtype0()->GetOneByGid($gid, $spriceType);
        $isInternal         = 0;
        if (($typeData['is_internal_purchase'] ?? 0) && $aData['pcombines']) { // 内购商品，取内购价格
            $pcombines      = array_column($aData['pcombines'], 'sprice', 'sprice_type');
            $aData['price'] = floatval($pcombines[1] ?? 0) > 0 ? $pcombines[1] : $aData['price'];
            $isInternal     = 1;
        }
        $aData = by::Gini()->getGiniPriceBySku($aData, $aData['sku'] ?? 0, $format_price, $isInternal);

        $aData['is_internal'] = $isInternal;

        return $aData;
    }

    /**
     * @param int $gid
     * @return array
     * @throws Exception
     * 根据gid获取属性列表
     */
    public function GetListByGid(int $gid, int $spriceType = 0, $isDelete = false): array
    {
        if (empty($gid)) {
            return [];
        }
        $gid = CUtil::uint($gid);

        $redis = by::redis('core');
        $r_key = $this->__getSpecsListByGid($gid, $isDelete);
        $aJson = $redis->get($r_key);
        $aData = (array) json_decode($aJson, true);

        if ($aJson === false) {
        $tb = $this::tbName();
        if ($isDelete) {
            $sql = "SELECT `id` FROM {$tb} WHERE `gid` = :gid ";
        } else {
            $sql = "SELECT `id` FROM {$tb} WHERE `gid` = :gid AND `is_del` = 0";
        }

        $aData = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryAll();
        $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        $return = [];
        foreach ($aData as $val) {
            $return[] = $this->GetOneById($gid, $val['id'], true, true, $spriceType);
        }

        return $return;
    }

    /**
     * @param int $gid
     * @param array $aData
     * @return array
     * @throws Exception
     * 属性名转换
     */
    public function AttrToName(int $gid, array $aData)
    {
        foreach ($aData as $key => $val) {
            $av_ids = json_decode($val['av_ids'], true);

            foreach ($av_ids as $k => $v) {
                $cnf = by::Gav()->IdToName($v);
                !empty($cnf) && $aData[$key]['attr_cnf'][$k] = $cnf;
            }

            $sku                  = by::GoodsStockModel()->GetSkuByGidAndSid($gid, $val['id'], by::GoodsStockModel()::SOURCE['MAIN']);
            $hasStock             = empty($sku) ? 0 : by::GoodsStockModel()->OptStock($sku);
            $aData[$key]['stock'] = $hasStock;
        }

        return $aData;
    }

    /**
     * @param int $gid
     * @param string $sku
     * @param array $update
     * @return array
     * @throws Exception
     * 编辑数据
     */
    public function UpdateLog(int $gid, string $sku, array $update): array
    {
        //允许修改的字段
        $allowed = [
                'price', 'av_ids', 'image', 'is_del'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['price'])) {
            $update['price'] = by::Gtype0()->totalFee($update['price']);
        }

        $gid  = CUtil::uint($gid);
        $aLog = $this->GetOneBySku($sku);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid, 'id' => $aLog['id']])->execute();

        $this->__delSkuCache($sku);
        $this->__delIdCache($gid, $aLog['id']);
        $this->__delListCache($gid);

        return [true, "成功"];
    }

    /**
     * @param int $gid
     * @param string $av_ids
     * @param int $num
     * @return array
     * @throws Exception
     * 规格匹配 规格属性 获取sid
     */
    public function GetOneByAvIds(int $gid, string $av_ids, int $num, $arr = []): array
    {
        $gid    = CUtil::uint($gid);
        $num    = CUtil::uint($num);
        $av_ids = (array) json_decode($av_ids, true);
        if (empty($gid) || empty($num)) {
            return [false, '参数错误'];
        }

        $getChannel = $arr['get_channel'] ?? 0;//优惠券来源控制
        $spriceType = $arr['sprice_type'] ?? 0;//自定义设置价格类型

        $gInfo = by::Gmain()->GetAllOneByGid($gid, true, false, $spriceType);

        if (empty($gInfo)) {
            return [false, '参数错误(2)'];
        }

        switch ($gInfo['atype']) {
            case by::Gtype0()::ATYPE['SPEC']:
                return [true, [
                        'gid'          => $gid,
                        'sku'          => '',
                        'price'        => $gInfo['price'],
                        'av_ids'       => '',
                        'image'        => $gInfo['cover_image'],
                        'gini_id'      => $gInfo['gini_id'] ?? 0,
                        'is_ini'       => $gInfo['is_ini'] ?? 0,
                        'gini_tag'     => $gInfo['gini_tag'] ?? 0,
                        'gini_info'    => $gInfo['gini_info'] ?? [],
                        'gini_etime'   => $gInfo['gini_etime'] ?? 0,
                        'is_presale'   => $gInfo['is_presale'] ?? 0,
                        'presale_info' => [
                                'is_presale'       => $gInfo['is_presale'] ?? 0,
                                'presale_time'     => $gInfo['presale_time'] ?? '',
                                'start_payment'    => $gInfo['start_payment'] ?? '',
                                'end_payment'      => $gInfo['end_payment'] ?? '',
                                'surplus_time'     => $gInfo['surplus_time'] ?? '',
                                'scheduled_number' => $gInfo['scheduled_number'] ?? 0,
                                'deposit'          => sprintf('%.2f', $gInfo['deposit'] ?? 0),
                                'expand_price'     => sprintf('%.2f', $gInfo['expand_price'] ?? 0),
                        ],
                        'num'          => $num,
                ]];
                break;
            default:
                if (empty($av_ids)) {
                    return [false, '参数错误'];
                }
                $aLog = $this->GetListByGid($gid, $spriceType);
                foreach ($aLog as $val) {
                    $av1_ids = json_decode($val['av_ids'], true);
                    if (!array_diff($av1_ids, $av_ids)) {
                        if (isset($val['price'])) {
                            $val['num'] = $num;
                        }
                        return [true, $val];
                    }
                }
                return [false, '该商品没有此属性'];
        }
    }

    /**
     * @param int $gid
     * @param int $sid
     * @return array
     * @throws Exception
     * sid查属性
     */
    public function GetAttrByGid(int $gid): array
    {
        $gid = CUtil::uint($gid);

        if (empty($gid)) {
            return [false, '参数错误'];
        }

        $aData = by::Gak()->GetListByGid($gid);
        //商品属性值
        foreach ($aData as $k => $v) {
            $aData[$k]['val'] = by::Gav()->GetListByAkId($v['id']);
        }

        return [true, $aData];
    }

    /**
     * @throws Exception
     * 多规格sku找主sku
     */
    public function GetMainSkuBySpecsSku($sku)
    {
        $mainSku = by::Gmain()->GetOneBySku($sku);

        if ($mainSku) return $sku;

        $specsInfo = $this->GetOneBySku($sku);

        $gid = $specsInfo['gid'] ?? 0;

        $gid && $mainInfo = by::Gmain()->GetOneByGid($gid);

        return $mainInfo['sku'] ?? $sku;
    }

    /**
     * 根据gid获取规格名称
     * @param $gid
     * @return array
     * @throws Exception
     */
    public function getSpecNameByGid($gid): array
    {
        // 获取规格列表
        $aData = by::Gspecs()->GetListByGid($gid);

        // 获取规格名称
        foreach ($aData as $key => $val) {
            $av_ids = json_decode($val['av_ids'], true);

            foreach ($av_ids as $k => $v) {
                $cnf = by::Gav()->IdToName($v);
                !empty($cnf) && $aData[$key]['attr_cnf'][$k] = $cnf;
                !empty($cnf) && $aData[$key]['attr_cnf'][$k]['id'] = $v;
            }
        }
        return $aData;
    }
}
