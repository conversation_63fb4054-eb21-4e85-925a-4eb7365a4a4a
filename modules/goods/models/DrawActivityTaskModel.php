<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;
use RedisException;
use Throwable;
use yii\db\ActiveRecord;
use yii\db\DataReader;
use yii\db\Exception;

//抽奖活动-任务（关联）model
class DrawActivityTaskModel extends CommModel
{
    use ModelTrait;
    
    public static function getInstance(): DrawActivityTaskModel
    {
        return byNew::DrawActivityTask();
    }
    
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_draw_activity_task`";
    }

    public static $tb_fields = [
        'id', 'activity_id', 'task_id', 'task_limit', 'task_limit_type', 'draw_times', 'consume_type', 'consume_gold', 'extra', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'NO'  => 0,
        'YES' => 1
    ];

    //任务限制周期类型
    const TASK_LIMIT_TYPE =
        [
            'everyday'        => 1,//每天
            'activity_period' => 2,//活动期间
        ];



    /**
     * @param $taskId
     * @return string
     * 根据任务id获取key
     */
    private function getActivityIdByTaskIdKey($taskId): string
    {
        return AppCRedisKeys::getActivityIdByTaskId($taskId);
    }

    /**
     * @throws RedisException
     * 根据任务id清除key
     */
    public function __delActivityIdByTaskIdKey($taskId)
    {
        $redis    = by::redis();
        $redisKey = $this->getActivityIdByTaskIdKey($taskId);
        $redis->del($redisKey);
    }


    /**
     * @param $acId
     * @return string
     * 通过活动ID 获取 任务列表信息
     */
    private function getTaskIdsByAcIdKey($acId): string
    {
        return AppCRedisKeys::getTaskIdsByAcIdKey($acId);
    }

    /**
     * @param $acId
     * @return void
     * @throws RedisException
     * 通过活动ID 删除 任务列表信息
     */
    public function __delTaskIdsByAcIdKey($acId)
    {
        $redis = by::redis();
        $redisKey = $this->getTaskIdsByAcIdKey($acId);
        $redis->del($redisKey);
    }


    /**
     * @param $taskId
     * @return array|mixed|ActiveRecord
     * @throws RedisException 通过任务ID 获取所有绑定该任务的活动ID
     */
    public function getActivityId($taskId)
    {
        // 生成一个唯一的缓存键，以 acId 为基础
        $redisKey = $this->getActivityIdByTaskIdKey($taskId);

        $redis = by::redis();

        // 尝试从缓存获取活动详情
        $aJson = $redis->get($redisKey);
        if ($aJson) {
            // 缓存命中，直接返回缓存的活动详情
            $activityId = json_decode($aJson, true);
        } else {
            // 缓存未命中，从数据库获取活动详情
            $activityId = self::find()
                ->from(self::tbName())
                ->select(self::$tb_fields)
                ->where(['task_id' => $taskId, 'is_del' => self::IS_DEL['NO']])
                ->asArray(true)
                ->all();

            // 将活动详情存入 Redis
            $redis->set($redisKey, json_encode($activityId, JSON_UNESCAPED_UNICODE), ['EX' => empty($activityId) ? 10 : 1800]);
        }

        // 返回活动详情
        return $activityId ? : [];
    }


    /**
     * @param $acId
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 获取活动下绑定任务通过活动ID
     */
    public function getTaskIdsByAcId($acId)
    {
        $redis    = by::redis();
        $redisKey = $this->getTaskIdsByAcIdKey($acId);
        $aJson    = $redis->get($redisKey);
        if ($aJson) {
            $taskIds = (array)json_decode($aJson, true);
        } else {
            $tb      = self::tbName();
            $sql     = "SELECT `task_id`,`task_limit`,`task_limit_type` FROM {$tb} WHERE `activity_id`=:activity_id AND `is_del`=:is_del";
            $taskIds = by::dbMaster()->createCommand($sql, [':activity_id' => $acId, 'is_del' => self::IS_DEL['NO']])->queryAll();

            // 将活动详情存入 Redis
            $redis->set($redisKey, json_encode($taskIds, JSON_UNESCAPED_UNICODE), ['EX' => empty($taskIds) ? 10 : 1800]);
        }

        return $taskIds ?? [];
    }
    
    
    public function doCreate(array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $resp = $db->createCommand()->batchInsert($tb, ['activity_id', 'module_relation_id', 'task_id', 'task_limit_type', 'task_limit', 'draw_times', 'consume_type', 'consume_gold', 'extra', 'ctime', 'utime'], $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('邀请任务创建失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 更新
     * @param int $id 抽奖活动任务ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $data['utime'] = time();
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException(sprintf('%抽奖活动任务保存失败', $data['task_name'] ?? ''));
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 删除
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['dtime' => time(), 'is_del' => 1], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }
            
            if (empty($resp)) {
                throw new BusinessException('删除活动任务失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 根据活动模块ID获取活动商品列表
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function getListByRelationId(int $moduleRelationId): array
    {
        $tb = self::tbName();
        return self::find()->from($tb)->where(['module_relation_id' => $moduleRelationId, 'dtime' => 0])->asArray()->all();
    }
    
    /**
     * 根据活动模块ID获取活动商品IDs
     * @param int $moduleRelationId
     * @return array
     */
    public function getIdsByRelationId(int $moduleRelationId): array
    {
        $res = $this->getListByRelationId($moduleRelationId);
        
        return array_column($res, 'id');
    }
}
