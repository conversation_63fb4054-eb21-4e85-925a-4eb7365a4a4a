<?php

namespace app\modules\goods\models;

use app\modules\main\models\CommModel;

// 订单支付流水
class OrderPayHistoryModel extends CommModel
{
    private static $year = null;

    // 设置年份
    public static function setYear($year)
    {
        self::$year = $year;
    }

    // 返回表名
    public static function tableName(): string
    {
        $year = self::$year;
        // 生成表名
        return "db_dreame_goods.t_order_pay_history_{$year}";
    }

    /**
     * 获取支付数据
     * @param string $order_no
     * @param int $pay_type
     * @return array|null
     */
    public function getOrderPayData(string $order_no, int $pay_type = 0,$paymentPlan='NO_INST'): array
    {
        // 从订单号提取年份
        $year = substr($order_no, 0, 4);

        // 设置要查询的年份表
        self::setYear($year);

        // 构建查询
        $query = self::find()
            ->where(['order_no' => $order_no]);

        // 根据条件追加查询：支付类型
        if ($pay_type > 0) {
            $query->andWhere(['pay_type' => $pay_type]);
            $query->andWhere(['payment_plan'=>$paymentPlan]);
        }

        // 按过期时间倒序
        $query->orderBy(['expire_time' => SORT_DESC]);

        // 返回查询结果
        return $query->asArray()->all() ?: [];
    }

    /**
     * 保存支付流水
     * @param string $order_no
     * @param array $data
     * @return true
     * @throws \Exception
     */
    public function saveOrderPayData(string $order_no, array $data)
    {
        // 从订单号提取年份
        $year = substr($order_no, 0, 4);

        // 设置要查询的年份表
        self::setYear($year);

        // 当前时间戳
        $currentTimestamp = time();

        // 创建新的支付数据对象
        $orderPay = new OrderPayHistoryModel();

        // 设置支付数据属性
        $orderPay->order_no    = $order_no;
        $orderPay->pay_type    = $data['pay_type'];
        $orderPay->prepay_id   = $data['prepay_id'];
        $orderPay->expire_time = $data['expire_time'];
        $orderPay->ctime       = $currentTimestamp;
        $orderPay->utime       = $currentTimestamp;
        $orderPay->payment_plan = $data['payment_plan']??'NO_INST';

        // 使用ORM保存数据
        if (!$orderPay->save()) {
            throw new \Exception('保存支付流水数据失败');
        }

        return true;
    }
}