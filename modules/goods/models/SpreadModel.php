<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 导购订单关系表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;


class SpreadModel extends CommModel
{

    CONST SPREAD_CODE_EXP = YII_ENV_PROD ? 24 * 365 * 3600 : 24 * 3600 * 7;

    /**
     * @return string
     * 推广列表查询和创建
     */
    private function __getSpreadCode($md5Scene) {
        return AppCRedisKeys::getSpreadCode($md5Scene);
    }


    private function __delCache($md5Scene): int
    {
        $r_key1 = $this->__getSpreadCode($md5Scene);

        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param $md5Scene
     * @param $data
     * @return array
     */
    public function setSpreadCode($md5Scene,$data)
    {
        if(empty($md5Scene)||empty($data)){
            return [false,'渠道参数不正确！'];
        }
        //清除对应的缓存
        $redis      = by::redis('core');
        $redis_key  = $this->__getSpreadCode($md5Scene);
        $redis->set($redis_key,json_encode($data),['EX'=>self::SPREAD_CODE_EXP]);

        return $data;
    }


    public function getSpreadCode($md5Scene)
    {
        if(empty($md5Scene)){
            return [false,'渠道参数不正确~'];
        }
        $redis      = by::redis('core');
        $redis_key  = $this->__getSpreadCode($md5Scene);
        $json = $redis -> get($redis_key);
        if(empty($json)){
            return [false,'链接已过期！'];
        }
        return [true,json_decode($json,true)];
    }



}
