<?php

/**
 * 商品活动抵扣金额
 */

namespace app\modules\goods\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GmainAcDeprice extends CommModel
{

    public $tb_fields = [
        'id', 'sku', 'sn', 'deprice', 'ctime', 'utime', 'dtime', 'is_del'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_gmain_acdeprice`";
    }


    /**
     * @return string
     * 根据SN前缀获取商品活动抵扣列表
     */
    private function __getAcDepriceListBySn($sn): string
    {
        return AppCRedisKeys::getAcDepriceListBySn($sn);
    }

    /**
     * @param $sn
     * @param $sku
     * @return string
     * 根据SN前缀获取商品活动单条数据
     */
    private function __getAcDepriceInfo($sn,$sku): string
    {
        return AppCRedisKeys::getAcDepriceInfo($sn,$sku);
    }

    /**
     * @param $sn
     * @return void
     * 清理缓存
     */
    private function __delListCache($sn)
    {
        $r_key1 = $this->__getAcDepriceListBySn($sn);

        by::redis('core')->del($r_key1);
    }

    private function _delInfoCache($sn,$sku)
    {
        $r_key1 = $this->__getAcDepriceInfo($sn,$sku);

        by::redis('core')->del($r_key1);
    }

    /**
     * @param $sn
     * @return array
     * @throws \yii\db\Exception
     * 根据SN前缀获取商品活动抵扣列表
     */
    public function GetListBySn($sn,$format_price = true): array
    {
        if(empty($sn)||is_array($sn)) return [];

        $r_key = $this->__getAcDepriceListBySn($sn);
        $redis = by::redis('core');
        $aJson = $redis->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);

            $where    = "1=1 ";
            $snNumber = implode("','", explode(',', $sn));
            $where    .= " AND `sn` in ('{$snNumber}')";

            $sql = "SELECT `{$fields}` FROM {$tb}  WHERE {$where} ORDER BY `id` DESC";

            $aData = by::dbMaster()->createCommand($sql)->queryAll();
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if($format_price){
            foreach ($aData as &$item){
                $item['deprice']     = CUtil::totalFee($item['deprice'], 1);
            }
        }

        return $aData;
    }


    /**
     * @param $sn
     * @param $sku
     * @param $format_price
     * @return array
     * @throws \yii\db\Exception
     * 根据sku和sn获取折扣金额
     */
    public function GetInfoBySnAndSku($sn,$sku,$format_price = true): array
    {
        if(empty($sn)||is_array($sn)) return [];

        $r_key = $this->__getAcDepriceInfo($sn,$sku);
        $redis = by::redis('core');
        $aJson = $redis->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql = "SELECT `{$fields}` FROM {$tb}  WHERE `sku`=:sku AND `sn`=:sn AND `is_del`= 0 LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [':sku'=>$sku, ':sn'=>$sn])->queryOne();
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if($format_price){
            $aData['deprice'] =  CUtil::totalFee($aData['deprice'], 1);
        }

        return $aData;
    }


    /**
     * @param $list
     * @param $field
     * @return mixed
     * @throws \yii\db\Exception
     * 绑定是否可以参与活动
     */
    public function bindAcDeprice($list, $field = 'pre_sn')
    {
        if (empty($list)) return $list;
        //绑定显示
        $listArr      = array_column($list, null, $field);
        $keys = array_filter(array_unique(array_keys($listArr)));
        sort($keys);
        $keys_str     = implode(',', $keys);
        $acSnInfos    = $this->GetListBySn($keys_str);
        $arr          = array_column($acSnInfos, 'sku', 'sn');
        foreach ($list as &$item) {
            $item['act'] = empty($arr[$item[$field] ?? 0] ?? 0) ? 0 : 1;
        }
        //字段排序（先有效后无效）
        $actColumn = array_column($list, 'act');
        $createColumn = array_column($list,'create_time');
        array_multisort($actColumn, SORT_DESC,$createColumn,SORT_DESC, $list);

        return $list;
    }

}
