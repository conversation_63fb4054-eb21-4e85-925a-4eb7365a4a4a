<?php

namespace app\modules\goods\models;

use app\modules\main\models\CommModel;

class PaymentActivityGoodsModel extends CommModel
{
    public static function tableName()
    {
        return "`db_dreame_goods`.`t_payment_activity_goods`";
    }

    public function rules()
    {
        return [
                [['activity_id', 'gid'], 'required'],
                [['activity_id', 'interest_free', 'gid', 'ctime', 'utime'], 'integer'],
                ['interest_free', 'in', 'range' => [0, 3, 6, 12]],
        ];
    }

    public function getActivity()
    {
        return $this->hasOne(PaymentActivityModel::class, ['id' => 'activity_id']);
    }
}
