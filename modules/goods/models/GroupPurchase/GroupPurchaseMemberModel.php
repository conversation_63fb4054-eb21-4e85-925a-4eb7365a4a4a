<?php

namespace app\modules\goods\models\GroupPurchase;

use app\models\by;
use app\models\byNew;
use app\modules\main\models\CommModel;

class GroupPurchaseMemberModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_group_purchase_members`";
    }


    public static function tableName(): string
    {
        return self::tbName();
    }

    public static $tb_fields = [
            'id', 'activity_id', 'group_purchase_id', 'user_id', 'order_no', 'items_qty', 'ctime', 'utime'
    ];

    /**
     * 获取拼团成员信息、订单号、购买数量
     * @param $activityId
     * @param $groupPurchaseId
     * @return array
     */
    public function getGroupMembers($activityId, $groupPurchaseId): array
    {
        $groupMembers = self::find()
                ->where(['group_purchase_id' => $groupPurchaseId])
                ->andWhere(['activity_id' => $activityId])
                ->orderBy(['ctime' => SORT_DESC])
                ->groupBy('user_id')
                ->asArray()
                ->all();

        if (empty($groupMembers)) {
            return [];
        }

        // 获取所有user_id
        $userIds = array_column($groupMembers, 'user_id');

        $usersData = by::phone()->find()
                ->leftJoin(by::usersMall()->tableName(), '`t_phone`.mall_id = `t_users_mall`.id')
                ->where(['t_phone.user_id' => $userIds])
                ->select(['t_phone.user_id', 't_users_mall.nick_name', 't_users_mall.phone', 't_users_mall.avatar'])
                ->asArray()
                ->all();

        $usersData = array_column($usersData, null, 'user_id');

        // 合并数据
        foreach ($groupMembers as &$member) {
            $userId = $member['user_id'];
            if (isset($usersData[$userId])) {
                $member = array_merge($member, $usersData[$userId]);
            }
        }

        return $groupMembers;
    }

    public function getGroupList($activityId, $groupPurchaseId)
    {
        $groupMembers = self::find()
        ->where(['group_purchase_id' => $groupPurchaseId])
        ->andWhere(['activity_id' => $activityId])
        ->asArray()
        ->all();
        return $groupMembers;
    }



    /**
     * 获取指定拼团的唯一用户数
     *
     * @param int $groupPurchaseId
     * @return int 返回唯一用户的计数
     */
    public function getMemberDistinctCount(int $groupPurchaseId): int
    {
        // 使用 select count(distinct user_id) 查询，并确保返回值为整数类型
        $count = self::find()
                ->where(['group_purchase_id' => $groupPurchaseId])
                ->select('count(distinct user_id)')
                ->scalar();

        return (int) ($count ?? 0); // 确保返回值为整数，如果为空则返回0
    }


    /**
     * 获取用户购买数量
     * @param $user_id
     * @param $group_purchase_id
     * @return bool|int|mixed|string|null
     */
    public function getUserGoodsSum($user_id,$group_purchase_id)
    {
        return byNew::GroupPurchaseMemberModel()->find()->where(['user_id' => $user_id, 'group_purchase_id' => $group_purchase_id])->sum('items_qty');
    }

    public function getInfoByOrderNo($order_no){
        return self::find()->where(['order_no' => $order_no])->asArray()->one();
    }


    public function getList($params){
        $groupMembers = self::find();
        $groupMembers->where(['>','finish_time',0]);

        if (isset($params['finish_time'])) {
            $groupMembers->andWhere(['<=','finish_time',$params['finish_time']]);
        }
        if (isset($params['is_reward'])) {
            $groupMembers->andWhere(['is_reward' => $params['is_reward']]);
        }
        return $groupMembers->asArray()->all();

    }

}