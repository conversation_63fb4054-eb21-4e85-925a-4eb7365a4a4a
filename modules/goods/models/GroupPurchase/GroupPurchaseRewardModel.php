<?php

namespace app\modules\goods\models\GroupPurchase;

use app\models\by;
use app\models\byNew;
use app\modules\main\models\CommModel;

class GroupPurchaseRewardModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_group_purchase_reward`";
    }


    public static function tableName(): string
    {
        return self::tbName();
    }

    public static $tb_fields = [
            'id', 'gift_card_id', 'code', 'user_id', 'order_no', 'ctime', 'utime'
    ];

    public function saveLog($data = []){
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $db->createCommand()->insert($tb,$data)->execute();
        return [true, '保存操作成功'];
    }


}