<?php

namespace app\modules\goods\models\GroupPurchase;

use app\models\by;
use app\models\byNew;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use app\modules\main\services\GroupPurchaseService;
use yii\db\ActiveQuery;

class GroupPurchaseModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_group_purchases`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }


    public static $tb_fields = [
            'id', 'activity_id', 'gid', 'user_id', 'phone', 'nick_name', 'status', 'total_items', 'total_members', 'is_del', 'ctime', 'utime'
    ];

    // 可修改字段
    const CAN_UPDATE_FIELDS  = ['status', 'total_items', 'total_members', 'total_price', 'utime'];
    const STATUS_IN_PROGRESS = 0; // 拼团进行中
    const STATUS_SUCCESS     = 1; // 拼团成功
    const STATUS_END         = 2; // 团已满(拼团结束)

    /**
     * @param array $where
     * @return array
     * 获取最后一条拼团记录
     */
    public function getLastGroupPurchaseRecord(array $where): array
    {
        $item = self::find()
                ->andFilterWhere($where)
                ->orderBy(['id' => SORT_DESC])
                ->one();

        // 如果找到记录，调用 toArray()，否则返回空数组
        return $item ? $item->toArray() : [];
    }


    /**
     * 获取拼团记录
     * @param $activityId
     * @param $startTime
     * @param $endTime
     * @param $uid
     * @param $nick_name
     * @param $gid
     * @return ActiveQuery
     */
    public function getGroupRecordQuery($activityId, $startTime, $endTime, $uid, $nick_name, $gid): \yii\db\ActiveQuery
    {
        $query = self::find();
        $query->with(['activity'=>function ($query) {
            $query->select(['id', 'name', 'start_time', 'end_time']);
        }]);
        $query->leftJoin(GroupPurchaseActivityGoodsModel::tbName(), '`t_group_purchases`.gid = `t_group_purchase_goods`.gid and `t_group_purchases`.activity_id = `t_group_purchase_goods`.activity_id');
        $query->leftJoin(by::usersMall()->tableName(), '`t_group_purchases`.uid = `t_users_mall`.uid COLLATE utf8mb4_0900_ai_ci');
        $query->leftJoin('`db_dreame_goods`.`t_gmain`', '`t_group_purchases`.gid = `t_gmain`.id');
        $query->leftJoin('`db_dreame_wares`.`t_gift_card_goods`', 't_group_purchase_goods.member_gift_card_id = `t_gift_card_goods`.id');
//        $query->andWhere(['`t_users_mall`.is_deleted' => 0]);
        $query->andFilterWhere(['like', '`t_users_mall`.nick_name', $nick_name]);
        $query->andFilterWhere(['`t_group_purchases`.activity_id' => $activityId]);
        $query->andFilterWhere(['>=', '`t_group_purchases`.ctime', $startTime]);
        $query->andFilterWhere(['<=', '`t_group_purchases`.ctime', $endTime]);
        $query->andFilterWhere(['`t_group_purchases`.gid' => $gid]);
        $query->andFilterWhere(['`t_group_purchases`.uid' => $uid]);
        $query->orderBy('`t_group_purchases`.ctime desc');

        $query->select([
                '`t_group_purchases`.id as group_purchase_id',
                '`t_group_purchases`.uid',
                '`t_group_purchases`.gid',
                '`t_group_purchases`.activity_id',
                '`t_group_purchases`.total_members',
                '`t_group_purchase_goods`.purchase_limit',
                '`t_group_purchases`.ctime',
                '`t_group_purchases`.status',
                '`t_group_purchases`.total_items',
                '`t_group_purchases`.id',
                '`t_users_mall`.nick_name',
                '`t_users_mall`.phone',
                't_group_purchase_goods.max_members',
                't_group_purchase_goods.member_gift_card_id',
                't_gmain.name as goods_name',
                't_gift_card_goods.name as gift_card_name'

        ]);
        return $query;
    }

    // 创建关联模型 activity
    public function getActivity()
    {
        return $this->hasOne(GroupPurchaseActivityModel::class, ['id' => 'activity_id']);
    }


    /**
     * 团长基本信息
     * @param $id
     * @return array
     */
    public function getLeaderInfo($id): array
    {
        $query = self::find();
        $query->select([
                '`t_group_purchases`.user_id',
                '`t_group_purchases`.uid',
                '`t_group_purchases`.gid',
                '`t_group_purchases`.sid',
                '`t_group_purchases`.activity_id',
                '`t_group_purchases`.ctime',
                '`t_group_purchases`.status',
                '`t_group_purchases`.total_items',
                '`t_group_purchases`.total_members',
                '`t_group_purchases`.id',
        ]);
        $query->andWhere(['`t_group_purchases`.id' => $id]);
        $groupPurchase = $query->asArray()->one() ?? [];

        if (!empty($groupPurchase)) {
            $userQuery = by::usersMall()->find();
            $userQuery->select(['nick_name', 'phone']);
            $userQuery->andWhere(['uid' => $groupPurchase['uid']]);
            $userInfo = $userQuery->asArray()->one() ?? [];

            $groupPurchase = array_merge($groupPurchase, $userInfo);
        }

        return $groupPurchase;
    }



    /**
     * 获取团长未支付订单
     * @param $userId
     * @param $groupPurchaseIds
     * @return array
     */
    public function getLeaderUnPayOrder($userId, $groupPurchaseIds): array
    {
        if (empty($groupPurchaseIds)) {
            return [];
        }
        $query = by::Ouser()->find()->from(by::Ouser()->tbName($userId));
        $query->where(['user_id' => $userId, 'status' => by::Omain()::ORDER_STATUS['WAIT_PAY'], 'group_purchase_id' => $groupPurchaseIds]);
        $query->select(['group_purchase_id', 'order_no']);
        return $query->asArray()->all();
    }

    public function getUnPayOrder($userId,$group_purchase_id): array
    {
        $query = by::Ouser()->find()->from(by::Ouser()->tbName($userId));
        $query->where(['user_id' => $userId, 'status' => by::Omain()::ORDER_STATUS['WAIT_PAY'], 'group_purchase_id' => $group_purchase_id]);
        $query->andWhere(['!=', 'group_purchase_id', 0]);
        $query->select(['group_purchase_id', 'order_no']);
        $result = $query->asArray()->one();
        return $result ? $result : [];
    }



    /**
     * 获取拼团ID，如果存在进行中的团则加入，否则创建新团
     *
     * @param  $user_id
     * @param  $group_activity_id
     * @param  $gcombines
     * @return int 拼团ID
     * @throws  MyExceptionModel 拼团订单创建失败
     */
    public static function getOrCreateGroupPurchase($user_id, $group_activity_id, $gcombines): int
    {
        // 初始化拼团ID
        $groupPurchaseId = 0;

        // 提取商品ID
        $gid = $gcombines[0]['gid'] ?? 0;

        // 获取最近的拼团记录
        $groupPurchaseModel  = byNew::GroupPurchaseModel();
        $groupPurchaseRecord = $groupPurchaseModel->getLastGroupPurchaseRecord([
                'activity_id' => $group_activity_id,
                'gid'         => $gid,
                'user_id'     => $user_id,
        ]);

        // 如果有团ID且状态为进行中，直接返回团ID
        if (!empty($groupPurchaseRecord['id']) && $groupPurchaseRecord['status'] == GroupPurchaseModel::STATUS_IN_PROGRESS) {
            return $groupPurchaseRecord['id'];
        }

        // 否则，创建新团
        $groupPurchaseService = GroupPurchaseService::getInstance();
        list($groupStatus, $groupData) = $groupPurchaseService->initiateGroupPurchase([
                'user_id'     => $user_id,
                'activity_id' => $group_activity_id,
                'gid'         => $gid,
        ], $user_id);

        // 如果创建团失败，抛出异常
        if (!$groupStatus) {
            $errorMessage = $groupData['message'] ?? "未知原因";
            throw new MyExceptionModel("拼团订单创建失败，原因: " . $errorMessage);
        }

        // 返回新创建的拼团记录ID
        return $groupData['id'] ?? 0;
    }

    // 拼团状态获取 0:进行中 1:成功
    public static function setStatus($goodsMaxMembers, $currentMemberCount): int
    {
        // 拼团成功
        if ($currentMemberCount >= $goodsMaxMembers) {
            return self::STATUS_SUCCESS;
        }
        // 拼团进行中
        return self::STATUS_IN_PROGRESS;
    }


    public function getListByFail(): array
    {
        $finishTime = time() - 86400;
        $query      = self::find();
        $query->where(['status' => 0]);
        $query->andWhere([
                '<',
                'ctime',
                $finishTime
        ]);
        return $query->asArray()->all();
    }

    /**
     * 获取当前整点往前3小时的拼团列表
     * @return array
     */
    public function getListByLastThreeHours(): array
    {
        // 获取当前整点时间
        $currentTime = strtotime(date('Y-m-d H:00:00'));
        // 3小时前的时间
        $threeHoursAgo = $currentTime - (3 * 3600);

        $query = self::find();
        $query->where(['status' => 0]); // 只获取进行中的拼团
        $query->andWhere(['>=', 'ctime', $threeHoursAgo]);
        $query->andWhere(['<', 'ctime', $currentTime]);

        return $query->asArray()->all();
    }

    public function getListByStatus($status, $is_shipment): array
    {
        $query = self::find();
        $query->where(['is_shipment' => $is_shipment]);
        $query->where(['status' => $status]);
        return $query->asArray()->all();
    }

    public function getInfoById($id)
    {
        return self::find()->where(['id' => $id])->asArray()->one();
    }

}