<?php

namespace app\modules\goods\models\GroupPurchase;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\ActiveRecord;

class GroupPurchaseActivityGoodsModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_group_purchase_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static $tb_fields = [
        'id', 'gid', 'activity_id', 'min_members', 'max_members', 'purchase_limit', 'min_group_qty', 'tag_id'
        ];


    /**
     * 获取商品列表
     * @param int $activityId 活动ID
     * @param string $type 查询类型：'all' 返回所有数据，'page' 返回分页数据
     * @param string|null $goods_name 商品名称搜索关键词
     * @param array $filterGids
     * @param array $getGids
     * @param int $filter_is_del 是否过滤已删除的商品
     * @return array
     */
    public static function getActivityGoodsList(int $activityId, string $type = 'all', string $goods_name = null, array $filterGids = [], array $getGids = [], int $filter_is_del = 0, int $tag_id = 0): array
    {
        // 1. 获取活动商品基础数据
        $query = self::find()->where(['activity_id' => $activityId]);

        if (!empty($filterGids)){
            $query->andWhere( ["NOT IN",'gid',$filterGids]);
        }

        if (!empty($getGids)){
            $query->andWhere(['in','gid',$getGids]);
        }

        if ($filter_is_del==1){
            $query->andWhere(['is_del' => 0]);
        }

        // 添加 tag_id 筛选条件，0表示全部
        if ($tag_id > 0) {
            $query->andWhere(['tag_id' => $tag_id]);
        }

        $query->orderBy('sort asc, id asc');

        // 2. 根据类型返回不同格式的数据
        if ($type === 'all') {
            $activityGoods = $query->asArray()->all();
            $pages=1;
        } else {
            $activity_pg     = CUtil::Pg($query);
            $activityGoods = $activity_pg['list'] ?? [];
            $pages = $activity_pg['pages'] ?? 1;
        }

        if (empty($activityGoods)) {
            return [ [],$pages];
        }
        $giftCardIds = [];
        foreach ($activityGoods as $item) {
            if ($item['leader_gift_card_id']) {
                $giftCardIds[] = $item['leader_gift_card_id'];
            }
            if ($item['member_gift_card_id']) {
                $giftCardIds[] = $item['member_gift_card_id'];
            }
        }
        // 去重
        $giftCardIds = array_unique($giftCardIds);
        $giftCardListById = [];
        if (count($giftCardIds) > 0){
            $giftCardList = byNew::GiftCardGoods()->getGoodsListByIds($giftCardIds);
            foreach ($giftCardList as $item) {
                $giftCardListById[$item['id']] = $item;
            }
        }


        // 3. 获取商品ID列表
        $gids = array_column($activityGoods, 'gid');

        // 4. 构建商品查询
        $goodsQuery = by::Gmain()->find()
            ->select([
                't_gmain.id',
                't_gmain.name',
                'price' => 'COALESCE(t_gtype_0.price)',
                'mprice',
                't_gtype_0.cover_image',
                'atype',
                't_gmain.status'
            ])
            ->leftJoin('`db_dreame_goods`.t_gtype_0', 't_gmain.id = t_gtype_0.gid')
            ->where(['t_gmain.id' => $gids]);

        // 5. 添加商品名称搜索条件
        if (!empty($goods_name)) {
            $goodsQuery->andWhere(['t_gmain.status' => by::Gmain()::STATUS['ON_SALE']]);
            $goodsQuery->andFilterWhere(['like', 't_gmain.name', $goods_name]);
        }



        // 6. 获取商品数据
        $goods = $goodsQuery->asArray()->all();

        if (empty($goods)) {
            return [[], $pages];
        }

        // 7. 将活动商品数据转换为以gid为键的关联数组
        $activityGoodsMap = array_column($activityGoods, null, 'gid');

        // 8. 合并商品数据和活动商品数据
        $data= array_map(function ($goods) use ($activityGoodsMap,$giftCardListById) {
            $activityGoods = $activityGoodsMap[$goods['id']] ?? [];

            return [
                'id'                    => $activityGoods['id'] ?? 0,
                'gid'                   => $goods['id'],
                'name'                  => $goods['name'],
                'price'                 => CUtil::totalFee(bcmul($goods['price'], $activityGoods['rate'] ?? 1, 2), 1),
                'mprice'                => CUtil::totalFee($goods['mprice'], 1),
                'status'                => $goods['status'],
                'cover_image'           => $goods['cover_image'],
                'atype'                 => $goods['atype'],
                'min_members'           => $activityGoods['min_members'] ?? 0,
                'max_members'           => $activityGoods['max_members'] ?? 0,
                'purchase_limit'        => $activityGoods['purchase_limit'] ?? 0,
                'min_group_qty'         => $activityGoods['min_group_qty'] ?? 0,
                'member'                => $activityGoods['member'] ?? 0,
                'rate'                  => $activityGoods['rate'] ?? 0,
                'sort'                  => $activityGoods['sort'] ?? 0,
                'group_key'             => $activityGoods['group_key'] ?? '',
                'leader_gift_card_id'   => $activityGoods['leader_gift_card_id'] ?? 0,
                'member_gift_card_id'   => $activityGoods['member_gift_card_id'] ?? 0,
                'discount_activity_id'  => $activityGoods['discount_activity_id'] ?? 0,
                'discount_gid'          => $activityGoods['discount_gid'] ?? 0,
                'tag_id' => $activityGoods['tag_id'] ?? 0,
                'leader_gift_card_name' => $giftCardListById[$activityGoods['leader_gift_card_id']]['name'] ?? '',
                'member_gift_card_name' => $giftCardListById[$activityGoods['member_gift_card_id']]['name'] ?? ''
            ];
        }, $goods);


        // 9. 按sort排序
        array_multisort(array_column($data, 'sort'), SORT_ASC, $data);

        return [$data, $pages];
    }

    /**
     * 检查商品是否在当前活动中
     * @param $gid
     * @param $aid
     * @return array|ActiveRecord|null
     */
    public function getInfoByGIdAndAid($gid,$aid){
        return self::find()->where(['gid'=>$gid,'activity_id'=>$aid, 'is_del' => 0])->asArray()->one();
    }

}