<?php

namespace app\modules\goods\models\GroupPurchase;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\wares\services\goods\GoodsMainService;

class GroupPurchaseActivityModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_group_purchase_activities`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static $tb_fields = [
            'id', 'name', 'start_time', 'end_time', 'details', 'min_members', 'max_members'];

    public static function find()
    {
        return parent::find()->where(['is_del' => self::IS_DEL['no']]);
    }

    public function delete()
    {
        if ($this->hasAttribute('is_del') && $this->getAttribute('is_del') == self::IS_DEL['no']) {
            $this->is_del = self::IS_DEL['yes'];
            $this->dtime  = time(); // 假设你有这样的字段来记录更新时间
            return $this->save();
        } else {
            return false;
        }
    }


    /** 获取活动列表
     * @param $activityId
     * @param $name
     * @param $startTime
     * @param $endTime
     * @return array
     */
    public function getActivityList($activityId, $name, $startTime, $endTime): array
    {
        $query = self::find();

        $query->andFilterWhere(['id' => $activityId]);
        $query->andFilterWhere(['like', 'name', $name]);

        // 活动时间查询 查询时间段 和 活动时间段有交集的活动
        $query->andFilterWhere(['<=', 'start_time', $endTime]);
        $query->andFilterWhere(['>=', 'end_time', $startTime]);

        $query->orderBy('ctime desc');
        $query->select(['id', 'name', 'start_time', 'end_time']);
        return CUtil::Pg($query);
    }

    /**
     * 保存活动 6
     * @param $data
     * @return array|string[]
     */
    public function saveActivity($data): array
    {
        // 验证活动时间，同一时间不能有多个活动
//        $db = by::dbMaster();
//        $tb = self::tbName();
//        $sql = "SELECT id FROM {$tb} WHERE is_del=0 AND ((start_time < :startTime AND end_time > :startTime) OR (start_time < :endTime AND end_time > :endTime) OR (start_time > :startTime AND end_time < :endTime) OR (start_time < :startTime AND end_time > :endTime))";
//        $params = [
//            ':startTime' => $data['start_time'],
//            ':endTime' => $data['end_time'],
//        ];
//        $result = $db->createCommand($sql, $params)->queryAll();
//        if (count($result) > 0) {
//            if (count($result) > 1){
//                return ['status' => false, 'message' => '活动时间冲突，同一时间段内不能有多个活动！'];
//            }elseif(count($result)  == 1){
//                if ($result[0]['id'] != $data['id']) {
//                    return ['status' => false, 'message' => '活动时间冲突，同一时间段内不能有多个活动！']; // 如果是当前活动的更新操作，则允许保存
//                }
//            }
//        }


        $activity = CUtil::mSave(byNew::GroupPurchaseActivityModel(), $data);
        if (!$activity['status']) {
            return $activity;
        }

        // 使用自定义的保存逻辑，实现软删除和去重
        $activityGoods = $this->saveActivityGoods($activity['data']['id'], $data['goods']);

        if ($activityGoods['status']) {
            $activity['data']['goods'] = $activityGoods['data'];
            return $activity;
        }

        return ['status' => false, 'message' => 'Failed to save activity goods'];
    }

    /**
     * 活动商品详情 (主要)
     * @param $activityId
     * @param $gid
     * @return array
     */
    public function getActivityGoodsDetail($activityId,$gid): array
    {

        $activity = self::find()->select(self::$tb_fields)->where(['id' => $activityId])->asArray()
                ->one();
        if (!$activity) {
            return ['status' => false, 'message' => '活动不存在！'];
        }

        list($activityGoods,$pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList($activityId,'all',null,[],[$gid]);

        if (empty($activityGoods)){
            $activity['goods'] =[];
        }else{
            $activity['goods'] = array_map(function ($item) {
                $item['specs'] = GoodsMainService::getInstance()->GetMainStockByGid($item['gid']);
                return $item;
            }, $activityGoods);
        }

        return ['status' => true, 'data' => $activity];
    }


    /**
     *
     * 获取活动商品列表详情 (主要)
     * @param int $activityId 团购活动ID
     * @return array 返回格式为['status' => bool, 'data|message' => mixed]
     *               成功时返回活动详情及商品列表
     *               失败时返回错误信息
     */
    public function getActivityGoodsList($activityId): array
    {

        $activity = self::find()->select(self::$tb_fields)->where(['id' => $activityId])->asArray()
                ->one();
        if (!$activity) {
            return [
                'status'  => false,
                'message' => '活动不存在！'
            ];
        }

        list($activityGoods, $pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList($activityId, 'all', null, [], [], 1);

        if (empty($activityGoods)) {
            $activity['goods'] = [];
        } else {
            $activity['goods'] = array_map(function ($item) {
                $item['specs'] = GoodsMainService::getInstance()->GetMainStockByGid($item['gid']);
                return $item;
            }, $activityGoods);
        }

        return [
                'status' => true,
                'data'   => $activity
        ];
    }

    /**
     * 删除活动
     */

    public function deleteActivity($activityId): array
    {
        $activity = self::findOne($activityId);
        if (!$activity) {
            return ['status' => false, 'message' => 'Activity not found'];
        }
        $activity->delete();
        return ['status' => true, 'message' => 'Activity deleted'];
    }

    /**
     * 保存活动商品，实现软删除和去重逻辑
     * @param int $activityId 活动ID
     * @param array $goodsData 商品数据
     * @return array
     */
    private function saveActivityGoods(int $activityId, array $goodsData): array
    {
        $result = [];
        $transaction = by::dbMaster()->beginTransaction();
        $time = time();
        
        try {
            $goodsModel = byNew::GroupPurchaseActivityGoodsModel();
            
            // 1. 对于相同的gid，只保留最后一个配置（按数组顺序）
            $uniqueGoods = [];
            foreach ($goodsData as $goods) {
                $gid = $goods['gid'];
                $uniqueGoods[$gid] = $goods; // 后面的会覆盖前面的，保证只保留最后一个
            }
            
            // 2. 先软删除当前活动的所有商品
            $goodsModel::updateAll(
                ['is_del' => 1, 'dtime' => $time],
                ['activity_id' => $activityId, 'is_del' => 0]
            );
            
            // 3. 保存新的商品数据
            foreach ($uniqueGoods as $gid => $record) {
                $record['activity_id'] = $activityId;
                $record['utime'] = $time;
                if (empty($record['group_key'])){
                    $record['group_key']=$record['gid'];
                }
                
                // 检查是否已存在相同的 activity_id 和 gid 的记录
                $existingRecord = $goodsModel::find()
                    ->where(['activity_id' => $activityId, 'gid' => $gid])
                    ->orderBy('id DESC')
                    ->one();
                
                if ($existingRecord) {
                    // 如果存在，更新现有记录并取消软删除
                    $existingRecord->setAttributes($record, false);
                    $existingRecord->is_del = 0;
                    $existingRecord->dtime = null;
                    
                    if (!$existingRecord->save()) {
                        $transaction->rollBack();
                        return ['status' => false, 'message' => '保存失败', 'errors' => $existingRecord->getErrors()];
                    }
                    $record['id'] = $existingRecord->id;
                } else {
                    // 如果不存在，创建新记录
                    $model = new $goodsModel();
                    $record['ctime'] = $time;
                    $record['is_del'] = 0;
                    $model->setAttributes($record, false);
                    
                    if (!$model->save()) {
                        $transaction->rollBack();
                        return ['status' => false, 'message' => '保存失败', 'errors' => $model->getErrors()];
                    }
                    $record['id'] = $model->id;
                }
                
                $result[] = $record;
            }
            
            $transaction->commit();
            return ['status' => true, 'message' => '保存成功', 'data' => $result];
            
        } catch (\Exception $e) {
            $transaction->rollBack();
            return ['status' => false, 'message' => '保存失败', 'errors' => $e->getMessage()];
        }
    }

    // 获取已结束的活动列表
    public function getListByEndTime(){
        $query = self::find();
        $query->andFilterWhere(['<=', '`t_group_purchase_activities`.end_time', time()]);
        $query->andFilterWhere(['`t_group_purchase_activities`.is_end' => 0]);
        $query->andFilterWhere(['`t_group_purchase_activities`.is_del' => 0]);
        $list = $query->asArray()->all();
        return $list;
    }

    public function getInfoById($id){
        return byNew::GroupPurchaseActivityModel()::find()->where(['id' => $id])->andWhere(['is_del' => 0])->asArray()->one();
    }


    public function getCurrentActivity()
    {
        $dic     = CUtil::dictData('group_purchase');
        $subdata = array_column($dic, 'value', 'label');
        if (isset($subdata['activity_id'])) {
            return $this->getInfoById($subdata['activity_id']);
        }else{
            $currentTime = time();
            return byNew::GroupPurchaseActivityModel()::find()->where(['and', ['<', 'start_time', $currentTime], ['>', 'end_time', $currentTime]])->andWhere(['is_del' => 0])->asArray()->one();
        }

    }


}