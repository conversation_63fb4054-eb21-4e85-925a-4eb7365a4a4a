<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 抽奖活动-抽奖记录
 */
class EnvelopeRecordModel extends CommModel
{
    // 奖品类型
    const PRIZE_TYPES = [
            'DEFAULT'         => 1, // 谢谢参与
            'POINT'           => 2, // 积分
            'COUPON'          => 3, // 优惠券
            'EXTERNAL_COUPON' => 4, // 兑换券（第三方券码）
            'PRICE'           => 5, // 现金红包
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`member_activity_envelope_record`";
    }

    public static $tb_fields = [
            'id', 'activity_relation_id', 'user_id', 'user_phone', 'prize_name', 'prize_value', 'prize_num', 'collect_time', 'ctime', 'utime'
    ];

    public function getPrizeRecordCountCacheKey($acId, $phone): string
    {
        return AppCRedisKeys::getPrizeRecordCountCacheKey($acId, $phone);
    }

    /**
     * 获取中奖记录 无缓存
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getEnvelopeRecordList(array $params, int $page = 1, int $pageSize = 20)
    {
        // 校验偏移量
        if ($page === 0) {
            $offset = 0;
        } else {
            $offset = ($page - 1) * $pageSize;
        }
        if ($offset < 0) {
            return [];
        }
        // 查询条件
        $conditions = $this->getConditions($params);
        // 查询数据
        return self::find()
                ->from(self::tbName())
                ->select(self::$tb_fields)
                ->where($conditions)
                ->limit($pageSize)->offset($offset)
                ->orderBy(['collect_time' => SORT_DESC])
                ->asArray(true)
                ->all();
    }


    /**
     * 获取查询条件
     * @param array $params
     * @return array
     */
    public function getConditions(array $params): array
    {
        $conditions = [];

        if (isset($params['id'])) {
            $conditions['id'] = $params['id'];
        }

        if (isset($params['activity_relation_id'])) {
            $conditions['activity_relation_id'] = $params['activity_relation_id'];
        }

        if (isset($params['user_id'])) {
            $conditions['user_id'] = $params['user_id'];
        }

        return $conditions;
    }
}
