<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

//抽奖任务model
class DrawTaskModel extends CommModel
{
    use ModelTrait;
    
    public static function getInstance(): DrawTaskModel
    {
        return byNew::DrawTask();
    }
    
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_draw_task`";
    }

    public static $tb_fields = [
        'id', 'name', 'task_code', 'type', 'desc', 'status', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'NO'  => 0,
        'YES' => 1
    ];

    const STATUS = [
        'UP'   => 1,
        'DOWN' => 2
    ];

    const TYPE = [
        'DEFAULT' => 1,
        'ROUTINE' => 2
    ];

    /**
     * @param $taskCode
     * @return string
     * 通过task_code获取任务详情key
     */
    private function getTaskInfoByTaskCodeKey($taskCode): string
    {
        return AppCRedisKeys::getTaskInfoByTaskCodeKey($taskCode);
    }


    /**
     * @throws RedisException
     * 通过task_code清除任务详情key
     */
    public function __delTaskInfoByTaskCodeKey($taskCode)
    {
        $redis    = by::redis();
        $redisKey = $this->getTaskInfoByTaskCodeKey($taskCode);
        $redis->del($redisKey);
    }


    /**
     * @param $taskId
     * @return string
     * 通过任务id获取详情
     */
    private function getTaskInfoByTaskIdKey($taskId): string
    {
        return AppCRedisKeys::getTaskInfoByTaskIdKey($taskId);
    }
    
    private function getTaskListCacheKey(): string
    {
        return AppCRedisKeys::getTaskListCacheKey();
    }

    /**
     * @param $taskId
     * @return void
     * @throws RedisException
     * 通过任务id清除详情
     */
    public function __delTaskInfoByTaskIdKey($taskId)
    {
        $redis    = by::redis();
        $redisKey = $this->getTaskInfoByTaskIdKey($taskId);
        $redis->del($redisKey);
    }

    /**
     * @param $taskCode
     * @return array|mixed|DataReader
     * @throws Exception
     * @throws RedisException
     * 通过task_code获取任务详情
     */
    public function getTaskInfoByTaskCode($taskCode)
    {
        // 获取 Redis 实例和键
        $redis    = by::redis();
        $redisKey = $this->getTaskInfoByTaskCodeKey($taskCode);

        // 尝试从 Redis 获取数据
        $aJson = $redis->get($redisKey);
        $aData = json_decode($aJson, true);

        if ($aJson === false) {
            // 从数据库获取任务信息
            $tb     = self::tbName();
            $sql    = "SELECT `id`,`name`,`task_code`,`type`,`desc`,`status` FROM {$tb} WHERE `status`=:status AND `is_del`=:is_del AND `task_code`=:task_code LIMIT 1";
            $params = [':status' => self::STATUS['UP'], ':is_del' => self::IS_DEL['NO'], ':task_code' => $taskCode];
            $aData  = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            // 将查询结果存入 Redis 缓存
            $redis->set($redisKey, json_encode($aData, JSON_UNESCAPED_UNICODE), ['EX' => empty($aData) ? 10 : 1800]);  // 缓存有效期 1800 秒
        }

        return $aData;
    }


    /**
     * @param $taskId
     * @return array|mixed|DataReader
     * @throws Exception
     * @throws RedisException 通过任务ids获取任务列表
     */
    public function getTaskListById($taskId)
    {
        // 获取 Redis 实例和键
        $redis    = by::redis();
        $redisKey = $this->getTaskInfoByTaskIdKey($taskId);

        // 尝试从 Redis 获取数据
        $aJson = $redis->get($redisKey);
        $aData = json_decode($aJson, true);

        if ($aJson === false) {
            // 从数据库获取任务信息
            $tb     = self::tbName();
            $sql    = "SELECT `id`,`name`,`task_code`,`type`,`desc` FROM {$tb} WHERE `status`=:status AND `is_del`=:is_del AND `id` =:task_id AND `type`=:type";
            $params = [':status' => self::STATUS['UP'], ':is_del' => self::IS_DEL['NO'], ':task_id' => $taskId, ':type' => self::TYPE['ROUTINE']];
            $aData  = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            // 将查询结果存入 Redis 缓存
            $redis->set($redisKey, json_encode($aData, JSON_UNESCAPED_UNICODE), ['EX' => empty($aData) ? 10 : 1800]);  // 缓存有效期 1800 秒
        }

        return $aData;
    }
    
    public function getTaskList()
    {
        $redis = by::redis();
        $redisKey = $this->getTaskListCacheKey();
        
        // 尝试从 Redis 获取数据
        $aJson = $redis->get($redisKey);
        $aData = json_decode($aJson, true);
        
        if ($aJson === false) {
            // 从数据库获取任务信息
            $tb     = self::tbName();
            $sql    = "SELECT `id`,`name`,`task_code`,`type`,`desc` FROM {$tb} WHERE `status`=:status AND `is_del`=:is_del";
            $params = [':status' => self::STATUS['UP'], ':is_del' => self::IS_DEL['NO']];
            $aData  = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $aData  = empty($aData) ? [] : $aData;
            // 将查询结果存入 Redis 缓存
            $redis->set($redisKey, json_encode($aData, JSON_UNESCAPED_UNICODE), ['EX' => empty($aData) ? 10 : 1800]);  // 缓存有效期 1800 秒
        }
        
        return $aData;
    }

}
