<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 邀请奖励规则模型
 *
 * @property int $id 主键
 * @property int $invite_type 邀请类型
 * @property int $required_count 所需邀请数
 * @property string $rule_name 规则名称，方便管理和理解
 * @property string $rule_details 活动规则详情，接口返回无需鉴权
 * @property int $grant_mode 奖品发放方式：1=自动发放，2=手动领取，3=定时发放
 * @property int $is_active 规则是否激活 0=否, 1=是
 * @property int $max_claims 每个用户可领取该规则奖励的最大次数，0表示不限制
 * @property int $start_time 活动开始时间（秒时间戳），0表示不限制
 * @property int $end_time 活动结束时间（秒时间戳），0表示不限制
 * @property int $created_at 创建时间（秒时间戳）
 * @property int $updated_at 最后修改时间（秒时间戳）
 */
class UserInviteGiftRulesModel extends CommModel
{
    // 规则状态
    const IS_ACTIVE = [
        'NO' => 0,
        'YES' => 1,
    ];


    // 奖品发放方式
    const GRANT_MODE = [
        'AUTO' => 1,      // 自动发放
        'MANUAL' => 2,    // 手动领取
        'SCHEDULED' => 3, // 定时发放
    ];

    public $tb_fields = [
        'id', 'invite_type', 'required_count', 'rule_name', 'rule_details', 'grant_mode', 'is_active',
        'max_claims', 'start_time', 'end_time', 'created_at', 'updated_at'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_gift_rules`";
    }

    /**
     * 获取活跃的规则
     * @param int $inviteType 邀请类型
     * @param int|null $currentTime 当前时间戳，默认为当前时间
     * @return array
     * @throws Exception
     */
    public function getActiveRules(int $inviteType, int $currentTime = null): array
    {
        if ($currentTime === null) {
            $currentTime = time();
        }

        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE invite_type = :invite_type 
                AND is_active = :is_active
                AND (start_time = 0 OR start_time <= :current_time)
                AND (end_time = 0 OR end_time >= :current_time)
                ORDER BY required_count ASC";

        $params = [
            ':invite_type' => $inviteType,
            ':is_active' => self::IS_ACTIVE['YES'],
            ':current_time' => $currentTime,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据邀请类型获取所有激活的规则
     * @param int $inviteType 邀请类型
     * @param int|null $currentTime 当前时间戳，默认为当前时间
     * @return array
     * @throws Exception
     */
    public function getActiveRulesByType(int $inviteType, int $currentTime = null): array
    {
        return $this->getActiveRules($inviteType, $currentTime);
    }

    /**
     * 根据邀请数量获取符合条件的规则
     * @param int $inviteType 邀请类型
     * @param int $inviteCount 邀请数量
     * @param int|null $currentTime 当前时间戳
     * @return array
     * @throws Exception
     */
    public function getRulesByInviteCount(int $inviteType, int $inviteCount, int $currentTime = null): array
    {
        if ($currentTime === null) {
            $currentTime = time();
        }

        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE invite_type = :invite_type 
                AND is_active = :is_active
                AND required_count <= :invite_count
                AND (start_time = 0 OR start_time <= :current_time)
                AND (end_time = 0 OR end_time >= :current_time)
                ORDER BY required_count DESC";

        $params = [
            ':invite_type' => $inviteType,
            ':is_active' => self::IS_ACTIVE['YES'],
            ':invite_count' => $inviteCount,
            ':current_time' => $currentTime,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据发放模式获取规则
     * @param int $grantMode 发放模式
     * @param int|null $currentTime 当前时间戳
     * @return array
     * @throws Exception
     */
    public function getRulesByGrantMode(int $grantMode, int $currentTime = null): array
    {
        if ($currentTime === null) {
            $currentTime = time();
        }

        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE grant_mode = :grant_mode 
                AND is_active = :is_active
                AND (start_time = 0 OR start_time <= :current_time)
                AND (end_time = 0 OR end_time >= :current_time)
                ORDER BY required_count ASC";

        $params = [
            ':grant_mode' => $grantMode,
            ':is_active' => self::IS_ACTIVE['YES'],
            ':current_time' => $currentTime,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 根据ID获取规则
     * @param int $id 规则ID
     * @return array
     * @throws Exception
     */
    public function getRuleById(int $id): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " WHERE id = :id LIMIT 1";
        $result = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
        return $result ?: [];
    }

    /**
     * 创建规则
     * @param array $data 规则数据
     * @return array
     * @throws Exception
     */
    public function createRule(array $data): array
    {
        $now = time();
        $data['created_at'] = $now;
        $data['updated_at'] = $now;

        // 验证必填字段
        $requiredFields = ['invite_type', 'required_count'];
        foreach ($requiredFields as $field) {
            if (!isset($data[$field])) {
                return [false, "字段 {$field} 不能为空"];
            }
        }

        // 设置默认值
        $data['is_active'] = $data['is_active'] ?? self::IS_ACTIVE['YES'];
        $data['grant_mode'] = $data['grant_mode'] ?? self::GRANT_MODE['AUTO'];
        $data['start_time'] = $data['start_time'] ?? 0;
        $data['end_time'] = $data['end_time'] ?? 0;

        // 验证发放模式
        if (!in_array($data['grant_mode'], array_values(self::GRANT_MODE))) {
            return [false, '无效的发放模式'];
        }

        try {
            $fields = implode(',', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));
            $sql = "INSERT INTO " . self::tbName() . " ({$fields}) VALUES ({$placeholders})";

            $result = by::dbMaster()->createCommand($sql, $data)->execute();

            if ($result > 0) {
                $insertId = by::dbMaster()->getLastInsertID();
                return [true, $insertId];
            } else {
                return [false, '规则创建失败'];
            }
        } catch (\Exception $e) {
            return [false, '规则创建失败：' . $e->getMessage()];
        }
    }

    /**
     * 更新规则
     * @param int $id 规则ID
     * @param array $data 更新数据
     * @return array
     * @throws Exception
     */
    public function updateRule(int $id, array $data): array
    {
        if (empty($data)) {
            return [false, '更新数据不能为空'];
        }

        $data['updated_at'] = time();

        // 验证发放模式（如果提供）
        if (isset($data['grant_mode']) && !in_array($data['grant_mode'], array_values(self::GRANT_MODE))) {
            return [false, '无效的发放模式'];
        }

        try {
            $setParts = [];
            $params = [':id' => $id];

            foreach ($data as $field => $value) {
                $setParts[] = "{$field} = :{$field}";
                $params[":{$field}"] = $value;
            }

            $setClause = implode(', ', $setParts);
            $sql = "UPDATE " . self::tbName() . " SET {$setClause} WHERE id = :id";

            $result = by::dbMaster()->createCommand($sql, $params)->execute();

            if ($result > 0) {
                return [true, '规则更新成功'];
            } else {
                return [false, '规则更新失败或数据无变化'];
            }
        } catch (\Exception $e) {
            return [false, '规则更新失败：' . $e->getMessage()];
        }
    }

    /**
     * 删除规则（软删除，设置为不活跃）
     * @param int $id 规则ID
     * @return array
     * @throws Exception
     */
    public function deleteRule(int $id): array
    {
        return $this->updateRule($id, ['is_active' => self::IS_ACTIVE['NO']]);
    }

    /**
     * 获取规则列表（分页）
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param int|null $inviteType 邀请类型筛选
     * @param int|null $isActive 活跃状态筛选
     * @param int|null $grantMode 发放模式筛选
     * @return array
     * @throws Exception
     */
    public function getRuleList(int $page = 1, int $pageSize = 20, int $inviteType = null, int $isActive = null, int $grantMode = null): array
    {
        $offset = ($page - 1) * $pageSize;

        $where = "1=1";
        $params = [];

        if ($inviteType !== null) {
            $where .= " AND invite_type = :invite_type";
            $params[':invite_type'] = $inviteType;
        }

        if ($isActive !== null) {
            $where .= " AND is_active = :is_active";
            $params[':is_active'] = $isActive;
        }

        if ($grantMode !== null) {
            $where .= " AND grant_mode = :grant_mode";
            $params[':grant_mode'] = $grantMode;
        }

        // 获取总数
        $countSql = "SELECT COUNT(*) as count FROM " . self::tbName() . " WHERE {$where}";
        $countResult = by::dbMaster()->createCommand($countSql, $params)->queryOne();
        $total = (int)($countResult['count'] ?? 0);

        // 获取列表
        $listSql = "SELECT * FROM " . self::tbName() . " 
                    WHERE {$where}
                    ORDER BY created_at DESC 
                    LIMIT {$pageSize} OFFSET {$offset}";
        $list = by::dbMaster()->createCommand($listSql, $params)->queryAll() ?: [];

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
        ];
    }

    /**
     * 获取发放模式名称
     * @param int $grantMode 发放模式
     * @return string
     */
    public function getGrantModeName(int $grantMode): string
    {
        $modes = [
            self::GRANT_MODE['AUTO'] => '自动发放',
            self::GRANT_MODE['MANUAL'] => '手动领取',
            self::GRANT_MODE['SCHEDULED'] => '定时发放',
        ];

        return $modes[$grantMode] ?? '未知模式';
    }
} 