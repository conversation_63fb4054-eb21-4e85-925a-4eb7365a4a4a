<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单商品表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class OgoodsModel extends CommModel
{
    CONST EXP = 600;

    //订单商品状态
    CONST STATUS = [
        'NORMAL'        => 10000,   //正常状态
        'REFUNDING'     => 10000000,//申请退款
        'REFUNDIED'     => 20000000,//退款成功
    ];

    CONST GOODS_TYPE = [
      'BASE'=>1,//基础商品模块
      'WARES'=>2,//新商品模块
    ];

    public static function tbName($user_id): string
    {
        $mod = intval($user_id) % 10;
        return  "`db_dreame_goods`.`t_uo_g_{$mod}`";
    }

    public $tb_fields = [
            'id', 'user_id', 'order_no', 'sub_no', 'gid', 'sid', 'num', 'gini_id', 'oprice', 'exprice', 'price', 'cprice', 'consume_price',
            'coin', 'coin_price', 'status', 'goods_type', 'acdeprice', 'gift_card_type', 'gift_card_value', 'subsidy_price', 'shopping_price', 'is_third', 'mail_no', 'express_code', 'express_name'
    ];

    /**
     * @param $order_no
     * @return string
     * 订单商品列表
     */
    private function __getOgoodsListKey($order_no): string
    {
        return AppCRedisKeys::getOgoodsList($order_no);
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $gid
     * @param $sid
     * @return string
     * 订单商品详情表
     */
    private function __getOgoodsInfoKey($user_id,$order_no,$gid,$sid): string
    {
        return AppCRedisKeys::getOgoodsInfo($user_id,$order_no,$gid,$sid);
    }


    /**
     * @param $order_no
     * @return string
     * 通过订单号获取支付未退款总价格
     */
    private function __getPriceByOrderNoKey($order_no): string
    {
        return AppCRedisKeys::getPriceByOrderNo($order_no);
    }

    /**
     * @param $user_id
     * @return string
     * 订单商品列表
     */
    private function __getListByGidKey($user_id): string
    {
        return AppCRedisKeys::getOgoodsByGid($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 获取已完成的订单
     */
    private function getOrderGidListByUid($user_id):string
    {
        return AppCRedisKeys::getOrderGidListByUid($user_id);
    }


    /**
     * @param $order_no
     * @return int
     * 清理订单商品列表
     */
    public function DelListCache($order_no) : int
    {
        $r_key1 = $this->__getOgoodsListKey($order_no);
        $r_key2 = $this->__getPriceByOrderNoKey($order_no);
        return  by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $gid
     * @param $sid
     * @return int|\Redis
     * 清理info 详情
     */
    public function DelInfoCache($user_id,$order_no,$gid,$sid)
    {
        $r_key = $this->__getOgoodsInfoKey($user_id,$order_no,$gid,$sid);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $user_id
     * @return int
     * 清理订单商品列表1
     */
    public function DelList1Cache($user_id) : int
    {
        $r_key1 = $this->__getListByGidKey($user_id);
        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $gcombines
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, array $gcombines, array $arr)
    {

        //分摊优惠价格
        list($s, $gcombines) = $this->__splitPrice($user_id, $gcombines, $arr);
        if (!$s) {
            return [false, $gcombines];
        }


        $tb             = self::tbName($user_id);

        $save   = [];
        foreach ($gcombines as $gcombine) {
            // TODO 暂时使用标签判定是否为三方商品，后面改用商品类型判定
            // $tids = by::Gtag()->getTidsByGid($gcombine['gid'] ?? 0);
            //是否为三方商品
            // $is_third = (int) by::Gmain()->checkThirdGoods($tids);
            $is_third = (int) by::Gmain()->checkThirdGoods($gcombine['gid'] ?? 0);
            $save[] = [
                    'order_no'        => $order_no,
                    'user_id'         => $user_id,
                    'gid'             => $gcombine['gid'],
                    'sid'             => $gcombine['sid'],
                    'num'             => $gcombine['num'],
                    'gini_id'         => $gcombine['gini_id'] ?? 0,
                    'oprice'          => $gcombine['tprice'],
                    'price'           => bcsub($gcombine['price'], bcadd(bcadd($gcombine['subsidy_price'] ?? 0, $gcombine['shopping_price'] ?? 0), $gcombine['consume_money'] ?? 0)),
                    'cprice'          => $gcombine['cprice'] ?? 0,
                    'consume_price'   => $gcombine['consume_price'] ?? 0,
                    'coin'            => $gcombine['coin'] ?? 0,
                    'coin_price'      => $gcombine['coin_price'] ?? 0,
                    'acdeprice'       => $gcombine['acdeprice'] ?? 0,
                    'exprice'         => $gcombine['exprice'] ?? 0,
                    'subsidy_price'   => $gcombine['subsidy_price'] ?? 0,
                    'shopping_price'  => $gcombine['shopping_price'] ?? 0,
                    'consume_money'   => $gcombine['consume_money'] ?? 0,
                    'gift_card_type'  => $gcombine['gift_card_type'] ?? 0,
                    'gift_card_value' => $gcombine['gift_card_value'] ?? 0,
                    'is_third'        => $is_third, //是否为三方商品
            ];
        }

        $columns = array_keys(reset($save));
        by::dbMaster()->createCommand()->batchInsert($tb, $columns, $save)->execute();

        return [true, 'ok'];
    }
    
    /**
     * @param $id
     * @param $user_id
     * @param $order_no
     * @param array $data
     * @return array
     * @throws Exception 更新数据
     */
    public function UpdateData($id, $user_id, $order_no, array $data): array
    {
        $allowed = ['mail_no','express_code','express_name'];
        
        foreach ($data as $field => $val) {
            if ( !in_array($field, $allowed) ) {
                unset($data[$field]);
            }
        }
        if (empty($data)) {
            return [false, '无数据更改'];
        }

        $tb = self::tbName($user_id);
        by::dbMaster()->createCommand()->update($tb, $data, ['id' => $id, 'order_no' => $order_no])->execute();
        
        $this->DelListCache($order_no);
        $this->DelList1Cache($user_id);
        
        return [true, 'ok'];
    }


    public function SaveWaresLog(int $user_id, string $order_no, array $gcombines, array $arr)
    {

        $tb             = self::tbName($user_id);

        $save   = [];
        foreach($gcombines as $gcombine) {
            $save[] = [
                'order_no'   => $order_no,
                'user_id'    => $user_id,
                'gid'        => $gcombine['gid'],
                'sid'        => $gcombine['sid'],
                'num'        => $gcombine['num'],
                'oprice'     => $gcombine['toprice'],
                'price'      => $gcombine['tprice'],
                'cprice'     => $gcombine['tcouponPrice'] ?? 0,
                'coin'       => $gcombine['tpoint'] ?? 0,
                'coin_price' => $gcombine['tpointPrice'] ?? 0,
                'goods_type' => $arr['goods_type'] ?? 1,
                'exprice'    => $gcombine['exprice'] ?? 0,
            ];
        }

        $columns = array_keys(reset($save));
        by::dbMaster()->createCommand()->batchInsert($tb, $columns, $save)->execute();

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param int $status
     * @return array
     * @throws Exception
     * 根据订单号获取数据
     */
    public function GetInfoByOrderNo($user_id, $order_no, $gid, $sid): array
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getOgoodsInfoKey($user_id, $order_no, $gid, $sid);
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb         = $this->tbName($user_id);
            $fields     = implode("`,`",$this->tb_fields);

            $where      = " `order_no`=:order_no AND `user_id` = :user_id AND `gid` = :gid AND `sid` = :sid ";
            $params     = [':order_no' => $order_no,':user_id'=>$user_id,':gid'=>$gid,':sid'=>$sid];

            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";
            $aData   = by::dbMaster()->createCommand($sql,$params)->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param int $status
     * @return array
     * @throws Exception
     * 根据订单号获取数据
     */
    public function GetListByOrderNo($user_id, $order_no, $status = -1)
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getOgoodsListKey($order_no);
        $h_key       = CUtil::getAllParams(__FUNCTION__, $status);
        $aJson       = $redis->hGet($r_key, $h_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb         = $this->tbName($user_id);
            $fields     = implode("`,`",$this->tb_fields);

            $where      = " `order_no`=:order_no";
            $params     = [':order_no' => $order_no];
            if ($status > -1) {
                $where             .= " AND `status` = :status";
                $params[':status']  = $status;
            }

            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";
            $aData   = by::dbMaster()->createCommand($sql,$params)->queryAll();

            if (!empty($aData)) {
                $redis->hSet($r_key, $h_key, json_encode($aData));
                CUtil::ResetExpire($r_key,600);
            }

        }

        if (empty($aData)) {
            return [];
        }

        return $aData;
    }

    public function getPriceByOrderNo($order_no, $user_id)
    {
        $redis = by::redis('core');
        $r_key = $this->__getPriceByOrderNoKey($order_no);
        $price = $redis->get($r_key);

        if ($price === false) {
            $tb     = $this->tbName($user_id);
            $where  = " `order_no`=:order_no AND `status`=:status ";
            $params = [':order_no' => $order_no, ':status' => self::STATUS['NORMAL']];
            $sql    = "SELECT sum(`price`) FROM {$tb} WHERE {$where}";

            $price  = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $price  = empty($price) ? 0 : $price;

            $redis->set($r_key, $price, ['EX' => empty($price) ? 10 : self::EXP]);
        }

        return $price;
    }

    /**
     * @param $user_id
     * @param $gid
     * @param int $now_num
     * @param int $limit_num
     * @return array
     * @throws Exception
     * 判断是否超过限购
     */
    public function CanLimitBuy($user_id, $gid, $now_num = 0, $limit_num = 0, $goods_type = 1, $groupPurchaseId = 0, $isCheckGroupActivityId = false)
    {

        // 1. 如果限购数量小于等于 0，直接返回允许购买
        if ($limit_num <= 0) {
            return [true, 'ok'];
        }

        // 2. 当前数量已经超过限购数量，返回错误提示
        if ($now_num > $limit_num) {
            return [false, '超出此产品限购数量'];
        }

        // 3. 获取用户当前商品的订单数据
        $orderData   = $this->__getListByGid($user_id, $gid, $goods_type);
        $mOuser      = by::Ouser();
        $orderStatus = by::Omain()::ORDER_STATUS;

        // 4. 定义需要忽略的订单状态（已取消或已退款）
        $ignoreStatus = [
                $orderStatus['CANCELED'],
                $orderStatus['RERUNDED']
        ];


        // 5. 遍历用户订单数据，检查限购逻辑
        foreach ($orderData as $order) {
            // 获取订单详情
            $orderInfo = $mOuser->GetInfoByOrderId($user_id, $order['order_no']);

            // 如果订单状态是取消或退款，跳过该订单
            if (in_array($orderInfo['status'], $ignoreStatus)) {
                continue;
            }

            // 如果是团长首次发起拼团只有活动ID 或者有团购ID，且订单不属于当前团，则跳过该订单
            if ($isCheckGroupActivityId || ($groupPurchaseId && $groupPurchaseId != $orderInfo['group_purchase_id'])) {
                continue;
            }

            // 累加当前购买数量
            $now_num = bcadd($now_num, $order['num']);
            // 如果累计数量超出限购数量，返回错误提示
            if ($now_num > $limit_num) {
                return [false, '超出此产品限购数量'];
            }

        }


        // 6. 通过所有检查，返回允许购买
        return [true, 'ok'];
    }



    public function GetLimitBuyGid($user_id,$gid,$limit_num=0,$goods_type=1)
    {
        if ($limit_num <= 0) {
            return 0;
        }
        $aData      = $this->__getListByGid($user_id, $gid, $goods_type);
        $mOuser     = by::Ouser();
        $order_st   = by::Omain()::ORDER_STATUS;

        $igonre_st  = [
            $order_st['CANCELED'],$order_st['RERUNDED']
        ];
        $now_num = 0;
        foreach ($aData as $v) {
            $info = $mOuser->GetInfoByOrderId($user_id,$v['order_no']);
            if(in_array($info['status'],$igonre_st)){
                continue;
            }
            $now_num = bcadd($now_num, $v['num']);
        }

        $limit = bcsub($limit_num,$now_num);

        return intval($limit);
    }

    /**
     * @param $user_id
     * @param $gid
     * @return array|int
     * @throws Exception
     * 订单商品列表
     */
    private function __getListByGid($user_id,$gid,$goods_type)
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getListByGidKey($user_id);
        $h_key       = CUtil::getAllParams(__FUNCTION__, $gid, $goods_type);
        $aJson       = $redis->hGet($r_key, $h_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                 = self::tbName($user_id);
            $where              = "`user_id` = :user_id AND `gid` = :gid AND `goods_type` = :goods_type";
            $params[':user_id'] = $user_id;
            $params[':gid']     = $gid;
            $params[':goods_type']     = $goods_type;

            $status             = [
                self::STATUS['NORMAL'],self::STATUS['REFUNDING']
            ];
            $in_status          = implode(',',$status);
            $where             .= " AND `status` IN ($in_status)";

            $sql = "SELECT `order_no`,`num` FROM {$tb} WHERE {$where}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            $redis->hSet($r_key, $h_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 600);
        }

        return $aData;
    }

    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $ids
     * @param int $status
     * @return array
     * @throws Exception
     * 更改订单商品表状态
     */
    public function UpdateStatus(int $user_id, string $order_no, array $ids, int $status)
    {
        if (empty($ids)) {
            return [false, '参数错误'];
        }

        if (!in_array($status, self::STATUS)) {
            return [false, '参数错误(2)'];
        }

        $tb     = self::tbName($user_id);
        by::dbMaster()->createCommand()->update($tb, ['status' => $status], ['id' => $ids])->execute();

        $this->DelListCache($order_no);
        $this->DelList1Cache($user_id);

        return [true, 'ok'];

    }

    /**
     * @param $user_id
     * @param $gcombines
     * @param $arr
     * @return array
     * @throws Exception
     * 分摊价格（优惠券、积分）
     */
    public function __splitPrice($user_id,$gcombines, $arr)
    {
        $coupon_id     = $arr['coupon_id'] ?? 0;
        $cprice        = $arr['cprice'] ?? 0;
        $consume_id    = $arr['consume_id'] ?? 0;
        $consume_price = $arr['consume_price'] ?? 0;
        $coin          = $arr['coin'] ?? 0;
        $giftCardPrice = $arr['gift_card_value'] ?? 0;
        $giftCardType  = $arr['gift_card_type'] ?? 0;
        $coin_price    = $arr['coin_price'] ?? 0;
        $tprice        = $arr['tprice'] ?? 0;
        $exprice       = $arr['exprice'] ?? 0;
        $subsidyPrice  = $arr['subsidy_price'] ?? 0;
        $shoppingPrice = $arr['shopping_price'] ?? 0;
        $consumeMoney  = $arr['consume_money'] ?? 0;

        $mMarket       = by::marketConfig();

        $mPoint         = by::point();
        $mGtype0        = by::Gtype0();

        $usable     = $usableconsume =[];
        $ctprice    = 0;    // 可使用优惠券的商品总价
        $cconsumeprice =0;  // 可使用消费券的商品总价


        //消费券分摊判断
        if($consume_id > 0){
            $c_info = by::userCard()->getCardById($user_id,$consume_id);
            $m_info = $mMarket->getOneById($c_info['market_id']);
            foreach($gcombines as $k => $gcombine) {
                if (empty($gcombine['is_coupons'])) {
                    continue;
                }
                  // todo 优惠券条件判断
                if (!empty($m_info)) {
                    // 商品标签判断
                    if ( !empty($m_info['c_tag_val']) ) {
                        $c_tag_val  = explode(',', $m_info['c_tag_val']);
                        $flag       = array_intersect($gcombine['tids'], $c_tag_val);

                        if ($m_info['c_tag_type'] == $mMarket::C_TAG_TYPE['apply']) {
                            if ( empty($flag) ) {
                                continue;
                            }
                        }
                        if ($m_info['c_tag_type'] == $mMarket::C_TAG_TYPE['out']) {
                            if ( !empty($flag) ) {
                                continue;
                            }
                        }
                    }

                    // 商品判断
                    if ( !empty($m_info['c_goods_val']) ) {
                        $c_goods_val = explode(',', $m_info['c_goods_val']);
                        $flag   = in_array($gcombine['gid'], $c_goods_val);

                        if ($m_info['c_goods_type'] == $mMarket::C_GOODS_TYPE['apply']) {
                            if (!$flag) {
                                continue;
                            }
                        }
                        if ($m_info['c_goods_type'] == $mMarket::C_GOODS_TYPE['out']) {
                            if ($flag) {
                                continue;
                            }
                        }
                    }
                    $cconsumeprice    = bcadd($cconsumeprice, $gcombine['tprice']);
                    $usableconsume[]   = $k;
                }
            } 
        }


        //优惠券分摊判断
        if ($coupon_id > 0) {
            unset($usable,$c_info,$m_info);
            $c_info = by::userCard()->getCardById($user_id,$coupon_id);
            $m_info = $mMarket->getOneById($c_info['market_id']);

            foreach($gcombines as $k => $gcombine) {

                if ($c_info['type'] != by::userCard()::TYPE['voucher']) {
                    if (empty($gcombine['is_coupons'])) {
                        continue;
                    }
                }

                // todo 优惠券条件判断
                if (!empty($m_info)) {
                    // 商品标签判断
                    if ( !empty($m_info['c_tag_val']) ) {
                        $c_tag_val  = explode(',', $m_info['c_tag_val']);
                        $flag       = array_intersect($gcombine['tids'], $c_tag_val);

                        if ($m_info['c_tag_type'] == $mMarket::C_TAG_TYPE['apply']) {
                            if ( empty($flag) ) {
                                continue;
                            }
                        }
                        if ($m_info['c_tag_type'] == $mMarket::C_TAG_TYPE['out']) {
                            if ( !empty($flag) ) {
                                continue;
                            }
                        }
                    }

                    // 商品判断
                    if ( !empty($m_info['c_goods_val']) ) {
                        $c_goods_val = explode(',', $m_info['c_goods_val']);
                        $flag   = in_array($gcombine['gid'], $c_goods_val);

                        if ($m_info['c_goods_type'] == $mMarket::C_GOODS_TYPE['apply']) {
                            if (!$flag) {
                                continue;
                            }
                        }
                        if ($m_info['c_goods_type'] == $mMarket::C_GOODS_TYPE['out']) {
                            if ($flag) {
                                continue;
                            }
                        }
                    }
                }

                $ctprice    = bcadd($ctprice, $gcombine['tprice']);
                $usable[]   = $k;
            }
        }


        $has_cprice             = $has_consumeprice = 0;
        $has_coin               = 0;
        $a_cprice               = $a_coin = $a_consume = 0;
        $hasGiftCardPrice       = $allGiftCardPrice = 0;
        $count                  = count($gcombines);
        $shoppingAllocatedTotal = 0; // 初始化全局已分摊的购物金
        $consumeAllocatedTotal  = 0; // 初始化全局已分摊的消费金

        foreach($gcombines as $k => $gcombine) {
            // 分摊购物金
            if (bccomp($shoppingPrice, 0) > 0) {

                $isLastItem = ($count - $k <= 1);

                if ($isLastItem) {
                    // 最后一项直接用剩余金额
                    $currentShare = bcsub($shoppingPrice, $shoppingAllocatedTotal);
                } else {
                    // 按比例分摊
                    $ratio        = bcdiv($gcombine['tprice'], $tprice, 4); // 当前商品价格 / 总价
                    $share        = bcmul($ratio, $shoppingPrice, 4);
                    $currentShare = round($share, 4);
                }

                // 转为整数（如需要）
                $currentShare = CUtil::uint($currentShare);

                // 超额校准（防止最后一笔超出）
                if (bccomp(bcadd($shoppingAllocatedTotal, $currentShare), $shoppingPrice) == 1) {
                    $currentShare = bcsub($shoppingPrice, $shoppingAllocatedTotal);
                }

                // 累加已分摊金额
                $shoppingAllocatedTotal = bcadd($shoppingAllocatedTotal, $currentShare);

                // 写入当前组合
                $gcombines[$k]['shopping_price'] = $currentShare;

            } else {
                $gcombines[$k]['shopping_price'] = 0;
            }

            // 分摊消费金
            if (bccomp($consumeMoney, 0) > 0) {
                $isLastItem = ($count - $k <= 1);

                if ($isLastItem) {
                    // 最后一项直接用剩余金额
                    $currentShare = bcsub($consumeMoney, $consumeAllocatedTotal);
                } else {
                    // 按比例分摊
                    $ratio        = bcdiv($gcombine['tprice'], $tprice, 4); // 当前商品价格 / 总价
                    $share        = bcmul($ratio, $consumeMoney, 4);
                    $currentShare = round($share, 4);
                }

                // 转为整数（如需要）
                $currentShare = CUtil::uint($currentShare);

                // 超额校准（防止最后一笔超出）
                if (bccomp(bcadd($consumeAllocatedTotal, $currentShare), $consumeMoney) == 1) {
                    $currentShare = bcsub($consumeMoney, $consumeAllocatedTotal);
                }

                // 累加已分摊金额
                $consumeAllocatedTotal = bcadd($consumeAllocatedTotal, $currentShare);

                // 写入当前组合
                $gcombines[$k]['consume_money'] = $currentShare;

            } else {
                $gcombines[$k]['consume_money'] = 0;
            }


            //todo 积分分摊
            if ($count - $k <= 1) {
                $n_coin         = bcsub($coin, $has_coin);
            } else {
                $coin_rate      = bcdiv($gcombine['tprice'], $tprice, 4);
                $n_coin         = round(bcmul($coin_rate, $coin,4));
                $n_coin         = CUtil::uint($n_coin);

                if (bccomp(bcadd($has_coin, $n_coin), $coin) == 1) {
                    $n_coin     = bcsub($coin, $has_coin);
                }

                $has_coin       = bcadd($has_coin, $n_coin);
            }

            $a_coin = bcadd($a_coin, $n_coin);

            //todo 优惠券分摊
            if ( !in_array($k, $usable) ) {
                $n_cprice       = 0;
            } else {
                $key    =array_search($k ,$usable);
                unset($usable[$key]);
                //最后一个分摊
                if (count($usable) == 0) {
                    $n_cprice       = bcsub($cprice, $has_cprice);

                } else {
                    $rate           = bcdiv($gcombine['tprice'], $ctprice, 4);
                    $n_cprice       = round(bcmul($rate, $cprice,4));
                    $n_cprice       = CUtil::uint($n_cprice);

                    if (bccomp(bcadd($has_cprice, $n_cprice), $cprice) == 1) {
                        $n_cprice     = bcsub($cprice, $has_cprice);
                    }
                    $has_cprice       = bcadd($has_cprice, $n_cprice);
                }

            }

            //todo 消费券分摊
            if ( !in_array($k, $usableconsume) ) {
                $n_consume       = 0;
            } else {
                $key    =array_search($k ,$usableconsume);
                unset($usableconsume[$key]);
                //最后一个分摊
                if (count($usableconsume) == 0) {
                    $n_consume       = bcsub($consume_price, $has_consumeprice);

                } else {
                    $rate           = bcdiv($gcombine['tprice'], $cconsumeprice, 4);
                    $n_consume       = round(bcmul($rate, $consume_price,4));
                    $n_consume       = CUtil::uint($n_consume);

                    if (bccomp(bcadd($has_consumeprice, $n_consume), $consume_price) == 1) {
                        $n_consume     = bcsub($consume_price, $has_consumeprice);
                    }
                    $has_consumeprice       = bcadd($has_consumeprice, $n_consume);
                }

            }

            //todo 礼品卡分摊
            if ($giftCardType == 1) {
                $nGiftCardPrice = ($count - $k <= 1)
                    ? bcsub($giftCardPrice, $hasGiftCardPrice)
                    : round(bcmul(bcdiv($gcombine['tprice'], $tprice, 4), $giftCardPrice, 4));

                $nGiftCardPrice = CUtil::uint($nGiftCardPrice);

                if (bccomp(bcadd($hasGiftCardPrice, $nGiftCardPrice), $giftCardPrice) == 1) {
                    $nGiftCardPrice = bcsub($giftCardPrice, $hasGiftCardPrice);
                }

                $hasGiftCardPrice = bcadd($hasGiftCardPrice, $nGiftCardPrice);
                $allGiftCardPrice = bcadd($allGiftCardPrice, $nGiftCardPrice);

                $gcombines[$k]['gift_card_value'] = $nGiftCardPrice;
            } else {
                $gcombines[$k]['gift_card_value'] = $giftCardPrice;
            }

            $a_cprice = bcadd($a_cprice, $n_cprice);
            $a_consume = bcadd($a_consume, $n_consume);

            $gcombines[$k]['cprice']          = $n_cprice;
            $gcombines[$k]['consume_price']   = $n_consume;
            $gcombines[$k]['coin']            = $n_coin;
            $gcombines[$k]['gift_card_type'] = $giftCardType;

            $n_coin_price               = $n_coin <= 0 ? 0 : $mPoint->convert($n_coin,'RMB',$user_id);
            $n_coin_price               = $mGtype0->totalFee($n_coin_price);
            $gcombines[$k]['coin_price']= $n_coin_price;

            // 消费券抵扣
            $price                      = bcsub($gcombine['tprice'], $n_consume);

            // 优惠券抵扣
            $price                      = bcsub($price, $n_cprice);
            if (isset($c_info['type']) && $c_info['type'] == by::userCard()::TYPE['voucher']) {
                if ($price < 0) {
                    return [false, '优惠券使用失败(01)'];
                }
            } else {
                if ($price <= 0) {
                    return [false, '优惠券使用失败(1)'];
                }
            }

            $price = bcsub($price, $gcombines[$k]['coin_price']);
            // if (isset($c_info['type']) && $c_info['type'] == by::userCard()::TYPE['voucher']) {
            //     if ($price < 0) {
            //         return [false, '积分使用失败(01)'];
            //     }
            // } else {
            //     if ($price <= 0) {
            //         return [false, '积分使用失败(1)'];
            //     }
            // }


            if ($giftCardType == 1) {
                $price = bcsub($price, $gcombines[$k]['gift_card_value']);
                if ($price < 0) {
                    return [false, '礼品卡使用失败(01)'];
                }
            } elseif ($giftCardType == 2) {
                $price = 0;//指定兑换卡实付款为0
            }

            //膨胀金额处理
            $price = bcsub($price,$gcombines[$k]['texprice'] ?? 0);

            //活动金额处理
            $price = bcsub($price,$gcombines[$k]['acdeprice'] ?? 0);

            $gcombines[$k]['price']         = $price;
            $gcombines[$k]['subsidy_price'] = $subsidyPrice;
            $gcombines[$k]['exprice']       = $gcombines[$k]['texprice'] ?? 0;
        }



        if ( bccomp($cprice, $a_cprice) != 0 ) {
            return [false, '优惠信息有误(1)'];
        }

        if ( bccomp($consume_price, $a_consume) != 0 ) {
            return [false, '消费券信息有误(1)'];
        }

        if ( bccomp($coin, $a_coin) != 0 ) {
            return [false, '优惠信息有误(2)'];
        }

        if ($giftCardType == 1 && bccomp($giftCardPrice, $allGiftCardPrice) != 0 ) {
            return [false, '优惠信息有误(3)'];
        }
        return [true, $gcombines];
    }


    /**
     * @param $user_id
     * @return array|DataReader
     * @throws RedisException
     * @throws Exception
     * 获取已完成的订单
     */
    public function getOrderListByUid($user_id,$myOrderNumber)
    {
        $redis = by::redis('core');
        $redis_key = $this->getOrderGidListByUid($user_id);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName($user_id);
            $fields     = implode("`,`",$this->tb_fields);
            $sql = "SELECT `{$fields}` FROM {$tb} WHERE user_id =:user_id AND `order_no` in ('{$myOrderNumber}')";
            $aData = by::dbMaster()->createCommand($sql, [':user_id'=>$user_id])->queryAll();

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * @throws RedisException
     */
    public function __delGetOrderGidByUidKey($user_id)
    {
        $redis = by::redis('core');
        $redis_key = $this->getOrderGidListByUid($user_id);
        $redis->del($redis_key);
    }

    /**
     * @param $gid
     * @param int $now_num
     * @param int $scheduled_number
     * @return array
     * @throws Exception
     * @throws RedisException
     * 判断是否超过预定人数
     */
    public function CanScheduledsBuy($gid, int $now_num=0, int $scheduled_number=0): array
    {
        if ($scheduled_number <= 0) {
            return [true, 'ok'];
        }

        if ($now_num > $scheduled_number) {
            return [false, '已超过预定人数'];
        }
        $oCount      = by::Odeposit()->getPaidNumByGid($gid);

        $now_num = bcadd($now_num, $oCount);

        if ($now_num > $scheduled_number) {
            return [false, '预售商品已达到限购人数'];
        }

        return [true, 'ok'];
    }

    /**
     * 根据用户ID和订单号获取信息（无缓存）
     * @param array $userIds
     * @param array $orderNos
     * @param array|string[] $columns
     * @return array
     * @throws Exception
     */
    public function getListByUserIdsAndOrderNos(array $userIds, array $orderNos, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $columns);
        // 订单号
        $orderNos = implode("','", $orderNos);
        // 分组查询
        $groupUserIds = $this->groupUserId($userIds, 10);
        foreach ($groupUserIds as $index => $ids) {
            $tb = $this->tbName($index);
            // 查询条件
            $ids = implode(',', $ids);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `user_id` IN ({$ids}) AND `order_no` IN ('{$orderNos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 用户ID分组
     * @param array $userIds
     * @param int $mod
     * @return array
     */
    private function groupUserId(array $userIds, int $mod): array
    {
        $data = [];
        foreach ($userIds as $userId) {
            // 取模
            $index = intval($userId) % $mod;
            $data[$index][] = $userId;
        }
        return $data;
    }


    /**
     * @param $gids
     * @param int $goodsType 商品类型 1普通商品 2积分商城商品
     * @return array
     * @throws Exception 通过商品ID 获取相关订单号
     */
    public function getAllOrderByGids($gids, $goodsType = 1): array
    {
        $gids = implode("','", $gids);

        $sql = "SELECT `order_no` FROM (";

        for ($i = 0; $i <= 9; $i++) {
            $tableName = self::tbName($i);
            $sql       .= " SELECT `order_no` FROM {$tableName} WHERE `gid` IN ('{$gids}') AND `goods_type`={$goodsType}";
            if ($i < 9) {
                $sql .= " UNION ALL ";
            }
        }

        $sql .= ") as `subqueries`";

        $orderNos = by::dbMaster()->createCommand($sql)->queryAll();

        return array_column($orderNos, 'order_no');
    }
}
