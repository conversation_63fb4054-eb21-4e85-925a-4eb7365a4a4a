<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 卡券券码表
 */
class CouponCodeModel extends CommModel
{
    public $tb_fields = [
        'id', 'coupon_code', 'ticket_code', 'status', 'receive_user_id', 'receive_at', 'order_no','create_at','update_at'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_coupon_codes`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @param array $aData
     * @param $gData
     * @return array
     * 主表增改
     */
    public function SaveLog(array $aData, $gData): array
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();
            $trans->commit();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'coupon.info');
            return [false, '保存操作失败'];
        }
    }

    public function addBatch($code,$list){
        $insert = [];
        foreach ($list as $item) {
            $insert[] = [
                'coupon_code' => $code,
                'ticket_code'    => $item,
                'status'  => 0,
                'create_at' => time(),
                'update_at' => time(),
            ];
        }
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        try {
            $db->createCommand()->batchInsert($tb,['coupon_code','ticket_code','status','create_at','update_at'],$insert)->execute();
            return true;
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'coupon.info');
            return false;
        }
    }

    public function getListByCode($code,$page,$page_size){
        $tb     = $this->tbName();
        list($offset) = CUtil::pagination($page, $page_size);
        $where             = "is_deleted=:is_deleted";
        $params[':is_deleted'] = 0;
        
        $where .= " AND coupon_code=:coupon_code";
        $params[':coupon_code'] = $code;
        
        $fields = implode("`,`", $this->tb_fields);
        $sql     = "SELECT `{$fields}` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
        $command = by::dbMaster()->createCommand($sql, $params);
        $aData   = $command->queryAll();
        $aData   = empty($aData) ? [] : $aData;
        return $aData;
    }

    public function getCountByCode($code){
        $tb     = $this->tbName();
        $where             = "is_deleted=:is_deleted";
        $params[':is_deleted'] = 0;
        
        $where .= " AND coupon_code=:coupon_code";
        $params[':coupon_code'] = $code;
        
        $sql     = "SELECT count(*) FROM {$tb} WHERE {$where}";
        $command = by::dbMaster()->createCommand($sql, $params);
        $count   = $command->queryScalar();
        return $count;
    }

}