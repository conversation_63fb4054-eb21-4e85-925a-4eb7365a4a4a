<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品库存
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\components\ErpNew;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GstockModel extends CommModel
{


    public $tb_fields = [
        'id', 'gid', 'sid', 'stock', 'sales', 'wait', 'is_del'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gstock`";
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 库存缓存KEY
     */
    private function __getGoodsStockKey($gid, $sid): string
    {
        return AppCRedisKeys::gStock($gid, $sid);
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 销量缓存KEY
     */
    private function __getGoodsSalesKey($gid, $sid): string
    {
        return AppCRedisKeys::gSales($gid, $sid);
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 未付款数缓存KEY
     */
    private function __getGoodsWaitsKey($gid, $sid): string
    {
        return AppCRedisKeys::gWait($gid, $sid);
    }

    /**
     * @param $gid
     * @return string
     * 获取商品的库存列表
     */
    private function __getGoodsListStockKey($gid): string
    {
        return AppCRedisKeys::gListStock($gid);
    }

    /**
     * @param $gid
     * @return void
     * 删除商品的库存列表
     */
    public function __delCache($gid)
    {
        $r_key  = $this->__getGoodsListStockKey($gid);

        by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @param array $sids
     * @throws \yii\db\Exception
     * 删除所有sid缓存
     */
    private function __delOptCache($gid, $sids = [])
    {
        foreach ($sids as $sid) {
            $this->OptStock($gid, $sid, 'DEL');
        }
    }

    /**
     * @param $gid
     * @param $sid
     * 删除库存、销量、未付款数缓存
     */
    public function DelAllCache($gid, $sid)
    {
        $r_key1 = $this->__getGoodsStockKey($gid, $sid);
        $r_key2 = $this->__getGoodsSalesKey($gid, $sid);
        $r_key3 = $this->__getGoodsWaitsKey($gid, $sid);

        by::redis()->del($r_key1, $r_key2, $r_key3);
    }

    /**
     * @param $gid
     * @param array $data
     * @param array $specsData
     * @return array
     * @throws \yii\db\Exception
     * 数据增改
     */
    public function SaveLog($gid, $data = [], $specsData = [])
    {
        $tb         = self::tbName();
        $mGtype0    = by::Gtype0();
        $aData      = [];
        $need_fid   = ['gid', 'sid', 'stock'];

        $atype      = $data['atype'] ?? -1;

        switch ($atype) {
            case $mGtype0::ATYPE['SPECS']:
                $aData = by::Gspecs()->getListByGid($gid);
                $aData = array_column($aData, 'sku', 'id');
                break;
        }

        //保存数据
        $values     = '';

        if (!empty($aData)) {
            //添加子商品库存
            $specsData  = array_column($specsData, null, 'sku');
            foreach ($aData as $sid => $sku) {
                $stock      = $specsData[$sku]['stock'] ?? 0;
                $stock      = CUtil::uint($stock);
                $values    .= "({$gid}, {$sid}, {$stock}),";
            }
            $values  = rtrim($values, ',');
        } else {
            $stock          = $data['stock'] ?? 0;
            $stock          = CUtil::uint($stock);
            $values .= "({$gid}, 0, {$stock})";
        }


        $field  = implode(',', $need_fid);
        $sql    = "INSERT INTO {$tb} ({$field})  
                        VALUES {$values} 
                        ON DUPLICATE KEY UPDATE `stock` = values(`stock`)";
        by::dbMaster()->createCommand($sql)->execute();


        //删除的规格
        if (!empty($aData)) {
            $sida   = array_keys($aData);
            $sids   = implode(',', $sida);
            by::dbMaster()->createCommand()->update(
                $tb,
                ['is_del' => 1],
                "`gid` = {$gid} AND `sid` NOT IN ({$sids})"
            )->execute();
        }

        //删除所有sid缓存
        $this->__delOptCache($gid, ($sida ?? [0]));
        $this->__delCache($gid);

        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param int $sid
     * @param int $num
     * @return array
     * @throws \yii\db\Exception
     * 预售商品预增待支付库存
     */
    public function UpdatePreSaleStock($gid, int $sid=0, int $num=0)
    {
        $tb     = self::tbName();
        $sql    = "UPDATE {$tb} SET `sales`=`sales`+(:num),`wait`=`wait`+(:num) WHERE `gid` = :gid AND `sid` = :sid  LIMIT 1";
        $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid])->execute();
        if ($u_num == 0) {
            return [false, '新增预售订单失败'];
        }
        return [true,'OK'];
    }



    /**
     * @param $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive
     * @return array
     * @throws \yii\db\Exception
     * 库存操作 ROLL:商品购买及回滚 SET：设置库存（同步库存用）SALE：更改销量（退款） WAIT：更改未付款数
     */
    public function UpdateStock($gid, $sid = 0, $num = 0, $opt = 'ROLL', bool $positive = true)
    {
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;

        $tb         = self::tbName();

        switch ($opt) {
            case 'ROLL':
                //1、先更新redis库存数据-第二次校验库存
                list($s, $m) = $this->OptStock($gid, $sid, 'INCR', -$num);
                if (!$s) {
                    return [false, $m];
                }

                //2、更新数据库数据-第三次数据库校验库存-库存字段非负
                $sql    = "UPDATE {$tb} SET `stock`=`stock`-(:num),`sales`=`sales`+(:num),`wait`=`wait`+(:num)
                           WHERE `gid` = :gid AND `sid` = :sid AND `stock`-(:num) >= 0 LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid])->execute();

                if ($u_num == 0) {
                    return [false, '库存不够(3)'];
                }

                //更新销量缓存
                $this->OptSales($gid, $sid, 'INCR', $num, $positive);
                //更新未付款缓存
                $this->OptWait($gid, $sid, 'INCR', $num, $positive);

                break;

            case 'SET':
                $num        = $num < 0 ? 0 : $num;
                $wait       = $this->OptWait($gid, $sid);

                //如果erp库存比未付款数还小，则更新库存为0
                if ($wait >= $num) {
                    $num        = 0;
                    $set_str    = " :num";
                } else {
                    $set_str    = " :num - `wait`";
                }

                $sql    = "UPDATE {$tb} SET `stock` = {$set_str} WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid])->execute();

                if ($u_num > 0) {
                    $this->optStock($gid, $sid, 'DEL');
                }

                break;

            case 'SALE':
                $sql = "UPDATE {$tb} SET `sales` = `sales` + (:num) WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid,])->execute();
                //删除销量缓存
                $this->OptSales($gid, $sid, 'DEL');
                break;

            case 'WAIT':
                $wait = $this->OptWait($gid, $sid);

                if ($wait <= $num) {
                    $set_str    = " :num";
                    $num        = 0;
                } else {
                    $set_str = " `wait` - :num";
                }

                $sql = "UPDATE {$tb} SET `wait` = {$set_str} WHERE `gid` = :gid AND `sid` = :sid LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':gid' => $gid, ':sid' => $sid,])->execute();
                //删除未付款缓存
                $this->OptWait($gid, $sid, 'DEL');
                break;
        }


        return [true, 'ok'];
    }


    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品库存
     */
    public function OptStock(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $r_key      = $this->__getGoodsStockKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $stock      = $redis->get($r_key);

                if ($stock === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `stock` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $stock  = $aLog['stock'] ?? 0;
                    $redis->set($r_key, $stock, ['NX', 'EX' => 600]);
                }

                return intval($stock);
                break;

            case 'INCR':
                !$redis->exists($r_key) &&  $this->OptStock($gid, $sid);
                $s_num  = $redis->incrBy($r_key, $num);
                if ($s_num < 0) {
                    $num < -1 && $this->OptStock($gid, $sid, 'DEL');
                    return [false, '库存不足(6)'];
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品销量
     */
    public function OptSales(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getGoodsSalesKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $sales      = $redis->get($r_key);

                if ($sales === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `sales` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $sales  = $aLog['sales'] ?? 0;
                    $redis->set($r_key, $sales, ['NX', 'EX' => 600]);
                }

                return intval($sales);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param int $gid
     * @param int $sid
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品未付款数
     */
    public function OptWait(int $gid, int $sid = 0, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $gid        = CUtil::uint($gid);
        $sid        = CUtil::uint($sid);
        $num        = abs($num);
        $num        = $positive == true ? $num : -$num;
        $r_key      = $this->__getGoodsWaitsKey($gid, $sid);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET':
                $wait      = $redis->get($r_key);

                if ($wait === false) {
                    $tb     = self::tbName();
                    $sql    = "SELECT `wait` FROM {$tb} WHERE `gid`=:gid AND `sid`=:sid LIMIT 1";
                    $aLog   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':sid' => $sid])->queryOne();

                    $wait  = $aLog['wait'] ?? 0;
                    $redis->set($r_key, $wait, ['NX', 'EX' => 600]);
                }

                return intval($wait);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param string $av_ids
     * @return array
     * @throws \yii\db\Exception
     * 多规格判断库存（小程序置灰）
     */
    public function InStock($gid, $av_ids = '')
    {
        //所有属性名
        $ak     = by::Gak()->GetListByGid($gid);

        $ak     = array_column($ak, null, 'id');

        //规格列表
        $specs  = by::Gspecs()->GetListByGid($gid);
        $av_ids = empty($av_ids) ? [] : json_decode($av_ids, true);

        //没选属性值，判断第一级
        if (empty($av_ids)) {
            $fist_ak = reset($ak);
        } else {
            foreach ($av_ids as $av_id) {
                $aLog = by::Gav()->GetOneById($av_id);
                unset($ak[$aLog['ak_id']]);
            }
            $fist_ak = empty($ak) ? [] : reset($ak);
        }

        $ak_id = $fist_ak['id'] ?? 0;

        if (empty($ak_id)) {
            return [];
        }

        $av     = by::Gav()->GetListByAkId($ak_id);
        foreach ($av as $k => $v) {
            $av[$k]['in_stock'] = 0;

            foreach ($specs as $v1) {
                $av1_ids = json_decode($v1['av_ids'], true);
                if (!array_diff([$v['id']], $av1_ids)) {
                    //判断库存
                    $stock = $this->OptStock($gid, $v1['id']);
                    if ($stock > 0) {
                        $av[$k]['in_stock'] = 1;
                        break;
                    }
                }
            }
        }

        $fist_ak['val'] = $av;
        return $fist_ak;
    }


    /**
     * @param $gid
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取商品库存列表
     */
    public function getStockListByGid($gid, bool $cache=true)
    {
        $gid    = CUtil::uint($gid);
        $r_key = $this->__getGoodsListStockKey($gid);
        $redis = by::redis();
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `gid`=:gid";
            $aData  = by::dbMaster()->createCommand($sql, ['gid' => $gid])->queryAll();

            $redis->set($r_key, json_encode($aData), ['ex' => empty($aData) ? 10 : 600]);
        }
        return $aData;
    }



    /**
     *
     * @param $gid
     * @return array
     * @throws \yii\db\Exception
     * 获取商品所有规格库存
     */
    public function GetSumData($gid): array
    {
        $data = $this->getStockListByGid($gid);
        if(empty($data)){
            return [0,0];
        }
        $stock  = 0;
        $sales  = 0;
        foreach ($data as $k=>$v){
            $stock +=intval($v['stock']??0);
            $sales +=intval($v['sales']??0);
        }
        return [$stock,$sales];
    }

    /**
     * @param $gid
     * @return array
     * @throws \yii\db\Exception
     * 同步库存
     */
    public function SyncStock($gid)
    {
        $gid        = CUtil::uint($gid);
        if (empty($gid)) {
            return [false, '参数错误'];
        }
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__,$gid);
        list($anti) = self::ReqAntiConcurrency(0, $unique_key, 10);
        if (!$anti) {
            return [false, '10s内只能同步一次'];
        }

        //新旧erp锁
        $lockOldErp = CUtil::omsLock(0,time());
        if($lockOldErp){
            return ErpNew::factory()->pushGoods($gid);
        }


        $mGmain     = by::Gmain();
        $mGtype0    = by::Gtype0();
        $aMain      = $mGmain->GetOneByGid($gid);
        if (empty($aMain)) {
            return [false, '数据不存在'];
        }

        switch ($aMain['type']) {
            case $mGmain::TYPE['COMMON']:
            case $mGmain::TYPE['YOUXUAN']:
            case $mGmain::TYPE['YANXUAN']:
                $aData  = $mGtype0->GetOneBaseByGid($gid, false);
                if (empty($aData)) {
                    return [false, 'gtype不存在'];
                }

                switch ($aData['atype']) {
                    case $mGtype0::ATYPE['SPEC']:
                        list($s, $stock) = Erp::factory()->getStockNew($aMain['sku'], $aMain['ck_code']);
                        if (!$s) {
                            return [false, '同步失败，请稍候'];
                        }
                        //更新库存
                         by::GoodsStockModel()->UpdateStock($gid,0,$stock,'SET',true,by::GoodsStockModel()::SOURCE['MAIN']);

                        //更新自定义价格库存
                        by::Gini()->SyncIniStock($aMain['sku'],$stock);

                        break;

                    default:
                        $aSpecs = by::Gspecs()->GetListByGid($gid);
                        foreach ($aSpecs as $spec) {
                            list($s, $stock) = Erp::factory()->getStockNew($spec['sku'], $aMain['ck_code']);
                            if (!$s) {
                                continue;
                            }
                            //更新库存
                            by::GoodsStockModel()->UpdateStock($gid,$spec['id'],$stock,'SET',true,by::GoodsStockModel()::SOURCE['MAIN']);

                            //更新自定义价格库存
                            by::Gini()->SyncIniStock($spec['sku'],$stock);
                        }
                }

                break;
            default:
                return [false, 'type不存在'];
        }

        return [true, 'ok'];
    }


}
