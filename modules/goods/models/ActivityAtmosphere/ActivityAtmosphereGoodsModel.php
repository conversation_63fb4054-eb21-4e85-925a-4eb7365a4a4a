<?php

namespace app\modules\goods\models\ActivityAtmosphere;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\MyExceptionModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseActivityGoodsModel;
use app\modules\main\models\CommModel;
use app\modules\main\services\GroupPurchaseService;
use yii\db\Exception;
use yii\db\ActiveQuery;

class ActivityAtmosphereGoodsModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`activity_atmosphere_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }


    public static $tb_fields = [
            'id', 'activity_id', 'gid'
    ];


    public function getActivity()
    {
        return $this->belongsTo(ActivityAtmosphereModel::class, ['activity_id' => 'id']);
    }
}