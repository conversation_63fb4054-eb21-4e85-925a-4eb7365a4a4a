<?php

namespace app\modules\goods\models\ActivityAtmosphere;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\goods\models\GroupPurchase\GroupPurchaseActivityGoodsModel;
use app\modules\main\models\CommModel;
use app\modules\main\services\GroupPurchaseService;
use yii\db\ActiveRecord;
use yii\db\Exception;
use yii\db\ActiveQuery;

class ActivityAtmosphereModel extends CommModel
{

    /**
     * 位置 1 列表 2 详情
     */
    const POS =[
            'LIST'=>1,
            'INFO'=>2
    ] ;

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`activity_atmosphere`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }


    public static $tb_fields = [
            'id', 'activity_id', 'gid', 'img', 'name', 'start_time', 'end_time', 'is_del', 'pos'
    ];


    public function getGoods(): ActiveQuery
    {
        return $this->hasMany(ActivityAtmosphereGoodsModel::class, ['activity_id' => 'id']);
    }

    private function __getGoodsAtmosphere($gid, $pos): string
    {
        return AppCRedisKeys::getGoodsAtmosphere($gid . '_' . $pos);
    }

    /**
     * 根据商品id获取当前时间的活动信息
     * @param $gid
     * @param $pos
     * @return array|ActiveRecord|null
     */
    public function getByGid($gid, $pos)
    {
        $data = CUtil::rememberCache($this->__getGoodsAtmosphere($gid, $pos), function () use ($gid, $pos) {
            $info               = byNew::ActivityAtmosphereModel()::find()
                    ->leftJoin(byNew::ActivityAtmosphereGoodsModel()::tableName(), 'activity_atmosphere_goods.activity_id = activity_atmosphere.id')
                    ->select(['gid', 'activity_id', 'img', 'name', 'start_time', 'end_time', 'pos'])
                    ->where(['gid' => $gid, 'activity_atmosphere.is_del' => 0, 'pos' => $pos])
                    ->andWhere(['<', 'start_time', time()])
                    ->andWhere(['>', 'end_time', time()])
                    ->asArray()
                    ->one();
            $info['set_expire'] = CUtil::calculateCacheTtl([$info]);
            return $info;
        });

        if (empty($data) || empty($data['start_time']) || empty($data['end_time'])) {
            return [];
        }

        // 判断时间是否在活动时间内
        if (time() < $data['start_time'] || time() > $data['end_time']) {
            return [];
        }

        return $data;

    }

    public function delCache($id)
    {
        $gids = ActivityAtmosphereGoodsModel::find()->select('gid')->where(['activity_id' => $id])->column();
        foreach ($gids as $gid) {
            $pos = ActivityAtmosphereModel::POS;
            foreach ($pos as $k => $p) {
                CUtil::delCache($this->__getGoodsAtmosphere($gid, $p));
            }
        }
    }

}