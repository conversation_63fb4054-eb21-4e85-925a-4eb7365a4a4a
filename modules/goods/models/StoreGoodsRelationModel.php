<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 导购订单关系表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use spec\Prophecy\Exception\Prediction\AggregateExceptionSpec;
use yii\db\ActiveQuery;
use yii\db\Exception;


class StoreGoodsRelationModel extends CommModel
{
    public $tb_fields = [
        'id', 'goods_id', 'user_id', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`dreame_store_goods_relation`";
    }

    public function existingGoods($user_id,$goods_ids):array
    {
        // 获取已存在的商品，避免重复添加
        $existingGoods = self::find()
            ->where(['user_id' => $user_id])
            ->andWhere(['in', 'goods_id', $goods_ids])
            ->select('goods_id')
            ->column();
        if(empty($existingGoods)){
            return [];
        }
        return $existingGoods;
    }

    public function getExistingGoods($user_id){
        $list = self::find()->where(['user_id'=>$user_id])->select('goods_id')->column();
        return $list;
    }

    public function deleteData($ids,$user_id){
        $db = by::dbMaster();
        $tb = self::tableName();
        $ids = implode(',', $ids);
        $resp = $db->createCommand()->delete($tb,"user_id = :user_id and goods_id in ({$ids})", [':user_id' => $user_id])->execute();
        return $resp;
    }

    public function deleteByGid($gids){
        $db = by::dbMaster();
        $tb = self::tableName();
        $ids = implode(',', $gids);
        $resp = $db->createCommand()->delete($tb,"goods_id in ({$ids})", [])->execute();
        return $resp;
    }



    /**
     * 批量添加商品到用户选择
     * @param array $rows 组装
     * @return array 返回操作结果
     */
    public  function batchAdd(array $rows)
    {
        // 开启事务
        $db = by::dbMaster();
        $tb = self::tableName();
        $trans  = $db->beginTransaction();
        try {
            // 使用批量插入
            $db->createCommand()->batchInsert($tb, ['user_id', 'goods_id', 'ctime', 'utime'],$rows)->execute();
            $trans->commit();
            return true;
        } catch (\Exception $e) {
            $trans->rollBack();
            return false;
        }
    }
    public function GetStoreGoods(
        $user_id=0,$page=1, $page_size=50, $version='',$name, $tid=-1,$platformId,$goodsStatus,$orderType
    ): array
    {
        $type = 0;
        $status = 0;
        $r_key   = $this->__getStoreGoodsRelationListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$user_id,$page,$page_size,$version,$type,$status,$tid,$platformId);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aJson = false;
        if ($aJson === false){
            $tb = $this->tbName();

            $gmain_tb = GmainModel::tbName();

            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getConditionNew($version, $status,$name, $tid, $platformId, $type);
            if ($goodsStatus != -1) {
                $where .= " AND `dreame_store_goods_relation`.`status` = {$goodsStatus}";
            }
            $orderBy = "`t_gmain`.`id` DESC";
            if ($orderType == 1) {
                $orderBy = "`dreame_store_goods_relation`.`ctime` DESC";
            }else if ($orderType == 2) {
                $orderBy = "`dreame_store_goods_relation`.`ctime` ASC";
            }else if ($orderType == 3) {
                $orderBy = "`t_gtype_0`.`price` DESC";
            }else if ($orderType == 4) {
                $orderBy = "`t_gtype_0`.`price` ASC";
            }

            $sql = "SELECT DISTINCT `t_gmain`.`id`
                    FROM {$tb} 
                    LEFT JOIN {$gmain_tb} ON `t_gmain`.`id` = `dreame_store_goods_relation`.`goods_id`
                    LEFT JOIN `db_dreame_goods`.`t_gtype_0` ON `t_gmain`.`id` = `t_gtype_0`.`gid`
                    WHERE {$where} AND `dreame_store_goods_relation`.`user_id` = {$user_id}
                    ORDER BY {$orderBy}
                    LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 600);
        }else{
            $aData   = (array)json_decode($aJson,true);
        }
        return empty($aData) ? [] : array_column($aData,"id");
    }
    public function GetStoreGoodsCount($user_id=0, $version='',$name, $tid=-1,$platformId,$goodsStatus){
        $type = 0;
        $status = 0;
        $tb = $this->tbName();
        $gmain_tb = GmainModel::tbName();
        list($where, $params)= $this->__getConditionNew($version, $status,$name, $tid, $platformId, $type);
        if ($goodsStatus != -1) {
            $where .= " AND `dreame_store_goods_relation`.`status` = {$goodsStatus}";
        }
        $sql = "SELECT COUNT(`t_gmain`.`id`)
                FROM {$tb} 
                left JOIN {$gmain_tb} ON `t_gmain`.`id` = `dreame_store_goods_relation`.`goods_id`
                WHERE {$where} AND `dreame_store_goods_relation`.`user_id` = {$user_id}";
        $count = by::dbMaster()->createCommand($sql,$params)->queryScalar();
        $count = empty($count) ? 0 : $count;
        return intval($count);

    }
    private function __getConditionNew($version = '', $status = -1, $name = '', $tid = -1, $platformId = 0, $type = -1): array
    {
        //SQL初始化条件
        $where             = "`t_gmain`.`is_del`=:is_del";
        $params[':is_del'] = 0;

        if(!empty($name)) {
            $where                .= " AND `t_gmain`.`name` LIKE :name";
            $params[":name"]       = "%{$name}%";
        }

        if($status >= 0) {
            $where                .= " AND `t_gmain`.`status`=:status";
            $params[":status"]     = intval(!!$status);
        }

        if ($tid > 0) {
            $tb                    = by::model('GtagModel', 'goods')->tbName();
            $where                .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `tid`={$tid})";
        }

        if ($type > -1) {
            if ($type != 999) {
                $where                .= " AND `t_gmain`.`type` =:type";
                $params[":type"]        = "$type";
            } else {
                $where                .= " AND `t_gmain`.`type` in (0,1,2)";    // 0=普通商品，1=优选商品，2=严选商品
            }
        }

        // 是否是当前平台商品
        if (!empty($platformId)) {
            $gIdsData = byNew::GoodsPlatformModel()->getDataList(['gid'], ['platform_id' => $platformId]);
            $gIds     = array_column($gIdsData, 'gid');
            if (empty($gIds)) {
                $where .= " AND 1=2";
            } else {
                $where .= " AND `t_gmain`.`id` IN (" . implode(',', $gIds) . ")";
            }
        }
        //版本号控制
        $versions  = $this->GetAccessVersions($version);
        $versions  = implode(',',$versions);
        $where    .= " AND `t_gmain`.`version` IN ({$versions})";

        return [$where,$params];
    }

    /**
     * @param $page
     * @param $page_size
     * @param $version  : 当前版本号
     * @param $type     : 商品类型
     * @param $status   : 0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 标签
     * @return array
     * @throws Exception
     *
     */
    public function GetStoreGoodsOld(
        $user_id=0,
        $page=1,    $page_size=50, $version='', $type=-1,
        $status=-1, $name='',      $sku='',     $tid=-1,
        $detailData=[], $sort=[]
    ): array
    {

        $otype = $type;
        if ($otype == -2) {
            // // 20250723 和产品运营确认推荐展示所有品的商品
            // $type = 999;
            // 上面推迟，保持推荐展示品牌类型商品
            $type = 0;
        }
        $r_key   = $this->__getStoreGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$version,$type,$status,$name,$sku,$tid,json_encode($detailData),json_encode($sort));
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);
        $aJson = false;
        if ($aJson === false) {
            $tb         = $this->tbName();
            $t_gtype_0  = Gtype0Model::tbName();
            $t_store_relation  = StoreGoodsRelationModel::tbName();

            $t_gtype_99 = Gtype99Model::tbName();
            $t_gspecs            = GspecsModel::tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($version, $status, $name, $sku, $tid, $detailData, $type);
            $join_tag = '';
            // 排序规则
            $default_sort_rule = ' `t_gmain`.`id` DESC';
            $sortField         = 'sort';
            $sortType          = 'DESC';

            // 首页排序专用
            $sort_rule = empty($sort) ? "`{$sortField}`" . ' ' . $sortType . ', ' . $default_sort_rule : "`{$sort['sort_field']}`" . ' ' . $sort['sort_type'] . ', ' . $default_sort_rule;
            if ($sort && !in_array(strtoupper($sort['sort_type']), ['DESC', 'ASC'])) {
                $sort['sort_type'] = 'DESC';
            }

            // 定义类型与标签列表类型的映射关系
            $typeMap = [
                0 => 1,   // 普通商品标签
                1 => 2,   // 优选商品标签
                2 => 3    // 严选商品标签
            ];

            // 处理标签相关逻辑
            if (isset($typeMap[$type]) && $tid == -1) {
                // 获取有效标签ID
                $list = by::Gtag()->getTagList($typeMap[$type]);
                $tids = array_filter(array_column($list, 'tid'), function ($v) {
                    return $v > 0;
                });

                if (!empty($tids)) {
                    $t_gtag        = by::model('GtagModel', 'goods')->tbName();
                    $tag_condition = "`tid` IN (" . implode(',', $tids) . ")";
                    $join_tag      = "INNER JOIN {$t_gtag} ON `t_gmain`.`id` = {$t_gtag}.`gid` AND {$t_gtag}.{$tag_condition}";
                    if (!empty($sort)) {
                        $sort_rule = "`{$sort['sort_field']}`" . ' ' . $sort['sort_type'] . ', ' . "FIELD({$t_gtag}.`tid`, " . implode(',', $tids) . "), " . $default_sort_rule;
                    } else {
                        $sort_rule = "`{$sortField}`" . ' ' . $sortType . ', ' . $default_sort_rule;
                    }
                }
            }


            $sql = "SELECT DISTINCT `t_gmain`.`id`, COALESCE(`t_gtype_0`.`price`, `t_gtype_99`.`price`) AS price 
                    FROM {$tb} 
                    JOIN {$t_store_relation} ON `t_gmain`.`id` = `dreame_store_goods_relation`.`goods_id`
                    LEFT JOIN {$t_gspecs} ON `t_gmain`.`id` = `t_gspecs`.`gid`
                    LEFT JOIN {$t_gtype_0} ON `t_gmain`.`id` = `t_gtype_0`.`gid` 
                    LEFT JOIN {$t_gtype_99} ON `t_gmain`.`id` = `t_gtype_99`.`gid` 
                    " . $join_tag . "
                    WHERE {$where} AND `dreame_store_goods_relation`.`user_id` = {$user_id}
                    GROUP BY `t_gmain`.`id`
                    ORDER BY {$sort_rule}
                    LIMIT {$offset},{$page_size}";
            var_dump($sql);
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            if ($otype == -2) {
                shuffle($aData);
            }
            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 600);
        }

        return empty($aData) ? [] : array_column($aData,"id");
    }




    /**
     * @param $page
     * @param $page_size
     * @param $version  : 当前版本号
     * @param $type     : 商品类型
     * @param $status   : 0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 标签
     * @return array
     * @throws Exception
     *
     */
    public function GetStoreList(

        $page=1,    $page_size=50, $version='', $type=-1,
        $status=-1, $name='',      $sku='',     $tid=-1,
        $detailData=[], $sort=[]
    ): array
    {

        $otype = $type;
        if ($otype == -2) {
            // // 20250723 和产品运营确认推荐展示所有品的商品
            // $type = 999;
            // 上面推迟，保持推荐展示品牌类型商品
            $type = 0;
        }
        $r_key   = $this->__getStoreGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$version,$type,$status,$name,$sku,$tid,json_encode($detailData),json_encode($sort));
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);
        $aJson = false;
        if ($aJson === false) {
            $tb         = $this->tbName();
            $t_gtype_0  = Gtype0Model::tbName();
            $t_store  = StoreGoodsModel::tbName();

            $t_gtype_99 = Gtype99Model::tbName();
            $t_gspecs            = GspecsModel::tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($version, $status, $name, $sku, $tid, $detailData, $type);
            $join_tag = '';
            // 排序规则
            $default_sort_rule = ' `t_gmain`.`id` DESC';
            $sortField         = 'sort';
            $sortType          = 'DESC';

            // 首页排序专用
            $sort_rule = empty($sort) ? "`{$sortField}`" . ' ' . $sortType . ', ' . $default_sort_rule : "`{$sort['sort_field']}`" . ' ' . $sort['sort_type'] . ', ' . $default_sort_rule;
            if ($sort && !in_array(strtoupper($sort['sort_type']), ['DESC', 'ASC'])) {
                $sort['sort_type'] = 'DESC';
            }

            // 定义类型与标签列表类型的映射关系
            $typeMap = [
                0 => 1,   // 普通商品标签
                1 => 2,   // 优选商品标签
                2 => 3    // 严选商品标签
            ];

            // 处理标签相关逻辑
            if (isset($typeMap[$type]) && $tid == -1) {
                // 获取有效标签ID
                $list = by::Gtag()->getTagList($typeMap[$type]);
                $tids = array_filter(array_column($list, 'tid'), function ($v) {
                    return $v > 0;
                });

                if (!empty($tids)) {
                    $t_gtag        = by::model('GtagModel', 'goods')->tbName();
                    $tag_condition = "`tid` IN (" . implode(',', $tids) . ")";
                    $join_tag      = "INNER JOIN {$t_gtag} ON `t_gmain`.`id` = {$t_gtag}.`gid` AND {$t_gtag}.{$tag_condition}";
                    if (!empty($sort)) {
                        $sort_rule = "`{$sort['sort_field']}`" . ' ' . $sort['sort_type'] . ', ' . "FIELD({$t_gtag}.`tid`, " . implode(',', $tids) . "), " . $default_sort_rule;
                    } else {
                        $sort_rule = "`{$sortField}`" . ' ' . $sortType . ', ' . $default_sort_rule;
                    }
                }
            }


            $sql = "SELECT DISTINCT `t_gmain`.`id`, COALESCE(`t_gtype_0`.`price`, `t_gtype_99`.`price`) AS price 
                    FROM {$tb} 
                    JOIN {$t_store} ON `t_gmain`.`id` = `dreame_store_goods`.`goods_id`
                    LEFT JOIN {$t_gspecs} ON `t_gmain`.`id` = `t_gspecs`.`gid`
                    LEFT JOIN {$t_gtype_0} ON `t_gmain`.`id` = `t_gtype_0`.`gid` 
                    LEFT JOIN {$t_gtype_99} ON `t_gmain`.`id` = `t_gtype_99`.`gid` 
                    " . $join_tag . "
                    WHERE {$where} 
                    GROUP BY `t_gmain`.`id`
                    ORDER BY {$sort_rule}
                    LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            if ($otype == -2) {
                shuffle($aData);
            }
            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 600);
        }

        return empty($aData) ? [] : array_column($aData,"id");
    }



    /**
     * @return string
     * 商品哈希列表
     */
    private function __getStoreGoodsListKey() {
        return AppCRedisKeys::getStoreGoodsList();
    }


    /**
     * @return string
     * 商品哈希列表
     */
    private function __getStoreGoodsRelationListKey() {
        return AppCRedisKeys::getStoreGoodsRealationList();
    }


    /**
     * @param $version   :  当前版本号
     * @param $status   :  0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 商品标签
     * @return array
     * @throws Exception
     * 规范化查询条件
     */
    private function __getCondition($version = '', $status = -1, $name = '', $sku = '', $tid = -1, $detailData = [], $type = -1, $isMain = false, $platformId = 0,$atype = 0): array
    {
        //SQL初始化条件
        $where             = "`t_gmain`.`is_del`=:is_del";
        $params[':is_del'] = 0;

        if(!empty($name)) {
            $where                .= " AND `t_gmain`.`name` LIKE :name";
            $params[":name"]       = "%{$name}%";
        }


        if(!empty($sku)) {
            $where                .= " AND (`t_gmain`.`sku` LIKE :sku OR `t_gspecs`.`sku` LIKE :sku)";
            $params[":sku"]        = "{$sku}%";
        }


        if($status >= 0) {
            $where                .= " AND `t_gmain`.`status`=:status";
            $params[":status"]     = intval(!!$status);
        }

        if ($tid > 0) {
            $tb                    = by::model('GtagModel', 'goods')->tbName();
            $where                .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `tid`={$tid})";
        }

        if ($type > -1) {
            if ($type != 999) {
                $where                .= " AND `t_gmain`.`type` =:type";
                $params[":type"]        = "$type";
            } else {
                $where                .= " AND `t_gmain`.`type` in (0,1,2)";    // 0=普通商品，1=优选商品，2=严选商品
            }
        }

        //是否是主机
        if ($isMain) {
            // 获取主标签的 gid 列表
            $mainTag = by::Gtag()->GetMainTag();
            // $tag      = GtagModel::MAIN_TAG;
            $tag      = $mainTag;
            $subQuery = GtagModel::find()->select('gid')->where(['tid' => $tag]);

            // 构建主查询条件
            $where .= " AND `t_gmain`.`id` IN (" . $subQuery->createCommand()->getRawSql() . ")";
        }

        //是否是多规格
        if ($atype) {
            $gids = Gtype0Model::find()->select('gid')->where(['atype' => $atype])->column();
            if (!empty($gids)) {
                $where .= " AND `t_gmain`.`id` NOT IN (" . implode(',', $gids) . ")";
            }
        }

        // 是否是当前平台商品
        if (!empty($platformId)) {
            $gIdsData = byNew::GoodsPlatformModel()->getDataList(['gid'], ['platform_id' => $platformId]);
            $gIds     = array_column($gIdsData, 'gid');
            if (empty($gIds)) {
                $where .= " AND 1=2";
            } else {
                $where .= " AND id IN (" . implode(',', $gIds) . ")";
            }
        }

        if (!empty($detailData) && is_array($detailData)) {
            $isRecommend = $detailData['is_recommend'] ?? -1;
            $isInternalPurchase = intval($detailData['is_internal_purchase'] ?? -1);
            $tb   = by::model('Gtype0Model', 'goods')->tbName();
            $t_goods_platform = GoodsPlatformModel::tbName(); // 商品平台表
            $tb99 = by::model('Gtype99Model', 'goods')->tbName();
            if (intval($isRecommend) >= 0) {
                $where .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `is_recommend`={$isRecommend} UNION SELECT `gid` FROM {$tb99} WHERE `is_recommend`={$isRecommend})";
            }
            $platformIds = $detailData['platformIds'] ?? [];
            if($platformIds){
                $where .= " AND `t_gmain`.`id` IN (SELECT DISTINCT(`gid`) FROM {$t_goods_platform} WHERE `platform_id` in (".implode(',',$platformIds).") AND `goods_type` = 1)";
            }
            if ($isInternalPurchase >= 0) {
                $where .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `is_internal_purchase`={$isInternalPurchase} UNION SELECT `gid` FROM {$tb99} WHERE `is_internal_purchase`={$isInternalPurchase})";
            }
            $isPresale= $detailData['is_presale'] ?? -1;
            if ($isPresale>=0) {
                $where .= " AND `t_gtype_0`.`is_presale` = {$detailData['is_presale']}";
            }

        }

        //版本号控制
        $versions  = $this->GetAccessVersions($version);
        $versions  = implode(',',$versions);
        $where    .= " AND `t_gmain`.`version` IN ({$versions})";

        return [$where,$params];
    }


    /**
     * @param $version
     * @return array
     * 获取可访问商品版本号
     */
    public function GetAccessVersions($version): array
    {
        $version  = CUtil::version2long($version);
        $version = $version === '' ? 0 : $version;
        return array_unique([0,$version]);
    }


    /*** crud start -------------------- ***/


    /**
     * 默认列表
     */
    public function getPageList($params = []){
        $order = $params['order'] ?? 'ctime';
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        $query = self::find();
        $query = $this->handleSearch($query, $params);

        $list = $query->orderBy([$order => SORT_DESC])
            ->offset(($page - 1) * $pageSize)->limit($pageSize)
            ->asArray()->all();
        $total = $query->count();
        return ['list' => $list, 'total' => $total, 'pages' => ceil($total / $pageSize)];
    }

    public function saveData(array $data): bool
    {
        // 获取用户记录
        $info = self::findOne(['goods_id' => $data['goods_id']]);

        if ($info === null) {
            // 如果用户不存在，创建新的记录
            $info              = new self();
            $info->ctime       = time(); // 设置创建时间
        }

        // 更新或设置
        $info->setAttributes($data, false);

        $info->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$info->save(false)) {
            return false;
        }

        return true;
    }

    public function doCreate($data)
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        $data['ctime'] = time();
        $data['utime'] = time();
        $db->createCommand()->insert($tb, $data)->execute();
        $id = $db->getLastInsertID();
        return $id;
    }


    /**
     * 获取信息
     * @return array|null
     */
    public function getInfo($where)
    {
        return self::find()
            ->where($where)
            ->asArray()
            ->one();
    }

    public function getInfos($ids): array
    {
        $list = self::find()
            ->where(['id' => $ids])
            ->asArray()
            ->all();

        $list = array_column($list, null, 'id');
        return $list ?: [];
    }

    public function updateInfo($where,array $params){
        $db = by::dbMaster();
        $tb = self::tbName();
        $params['utime'] = time();
        return $db->createCommand()->update($tb, $params, $where)->execute();
    }

    public function deleteInfo($where){
        $db = by::dbMaster();
        $tb = self::tbName();
        return $db->createCommand()->delete($tb, $where)->execute();
    }

    public  function batch($fields,array $rows)
    {
        // 开启事务
        $db = by::dbMaster();
        $tb = self::tableName();

        // 使用批量插入
        $a =  $db->createCommand()->batchInsert($tb, $fields, $rows)->execute();
        return true;
    }


    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        $attrs = $this->getTbFields();
        $map = [];
        $params = array_filter($params, function ($v) {
            return (!empty($v) || $v === '0' || $v === 0 );
        });
        foreach ($attrs as  $field) {
            if (isset($params[$field])) {
                $map[$field] = $params[$field];
            }
        }
        $query->andWhere($map);
        return $query;
    }

    public function getTbFields(): array
    {
        return ['id', 'user_id', 'goods_id','ctime','utime'];
    }

    /*** crud end  -------------------- ***/

}
