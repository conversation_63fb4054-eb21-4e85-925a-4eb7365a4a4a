<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\components\Crm;
use app\components\Erp;
use app\components\ErpNew;
use app\components\PointCenter;
use app\components\EventMsg;
use app\components\ExpressTen;
use app\jobs\ProductSaveJob;
use app\jobs\UserShopMoneyJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\AddressModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\UserCardModel;
use app\modules\main\models\UserEmployeeModel;
use app\modules\main\models\UserShopMoneyModel;
use app\modules\main\services\GiftCardService;
use app\modules\main\services\GroupPurchaseService;
use app\modules\main\services\MemberActivityService;
use app\modules\main\services\TradeInService;
use app\modules\wares\models\GiftUserCardsModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class OuserModel extends CommModel
{

    const ListExpire = 600;//列表缓存
    const EXPIRE_DAY = YII_ENV_PROD ? 15 : 0; //多少天后订单奖励推荐人 (单位天) 测试环境0天


    const USAGE_OPTION = [
        'USAGE_OPTIMAL' => 1, //最优
        'USAGE_SELECTED' => 2, //已选
        'USAGE_NOT_USED' => 3, //不使用
    ];


    public static function tbName($user_id): string
    {
        $mod = intval($user_id) % 10;
        return "`db_dreame_goods`.`t_uo_{$mod}`";
    }

    public $tb_fields = [
            'id',
            'user_id',
            'order_no',
            'deposit_order_no',
            'oprice',
            'price',
            'fprice',
            'exprice',
            'subsidy_price',
            'deposit_price',
            'coupon_id',
            'consume_id',
            'consume_price',
            'shopping_price',
            'consume_money',
            'cprice',
            'coin',
            'coin_price',
            'coin_logid',
            'gift_card_ids',
            'gift_card_price',
            'status',
            'ctime',
            'stime',
            'pay_time',
            'finish_time',
            'note',
            'cr_type',
            'syn_crm',
            'syn_iot',
            'acdeprice',
            'group_purchase_id',
            'relation_type',
            'relation_id',
    ];

    private $__format_fields = ['oprice', 'price', 'fprice', 'cprice', 'coin_price', 'exprice', 'deposit_price', 'acdeprice', 'gift_card_price', 'consume_price', 'subsidy_price', 'shopping_price', 'consume_money'];

    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($user_id, $order_no): string
    {
        return AppCRedisKeys::getOneOuserInfo($user_id, $order_no);
    }


    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoByDepositKey($user_id, $order_no): string
    {
        return AppCRedisKeys::getOneOuserInfoByDeposit($user_id, $order_no);
    }


    /**
     * @param $user_id
     * @return string
     * 订单列表
     */
    private function __getOuserListKey($user_id)
    {
        return AppCRedisKeys::getOuserList($user_id);
    }


    /**
     * @param $user_id
     * @return string
     * 未同步crm订单的积分
     */
    private function __getPointByOrder($user_id)
    {
        return AppCRedisKeys::getPointByOrder($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 获取已完成的订单
     */
    private function __getOrderListByUid($user_id): string
    {
        return AppCRedisKeys::getOrderListByUid($user_id);
    }

    /**
     * @param $user_id
     * @param $order_id
     * @return int
     * 清理单个订单信息缓存
     */
    public function DelInfoCache($user_id, $order_id): int
    {
        $r_key1 = $this->__getOneInfoKey($user_id, $order_id);
        return by::redis('core')->del($r_key1);
    }

    /**
     * @param $user_id
     * @param $deposit_order_no
     * @return int|\Redis
     * 清理单个数据
     */
    public function DelInfoCacheByDeposit($user_id, $deposit_order_no)
    {
        $r_key1 = $this->__getOneInfoByDepositKey($user_id, $deposit_order_no);
        return by::redis('core')->del($r_key1);
    }

    /**
     * @param $user_id
     * @return int
     * 清理订单列表 （有数据新增时）
     */
    public function DelListCache($user_id): int
    {
        $r_key1 = $this->__getOuserListKey($user_id);
        return by::redis('core')->del($r_key1);
    }

    /**
     * @param $user_id
     * @return int
     * 清理未同步crm订单的积分
     */
    public function DelPointCache($user_id): int
    {
        $r_key1 = $this->__getPointByOrder($user_id);
        //清理查询的CRM积分锁（有进bug）
        $r_key = AppCRedisKeys::scoreAccount($user_id);
        return by::redis('core')->del($r_key1, $r_key);
    }


    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, string $order_no, array $aData)
    {
        $oprice            = $aData['oprice'] ?? 0;
        $price             = $aData['price'] ?? 0;
        $fprice            = $aData['fprice'] ?? 0;
        $exprice           = $aData['exprice'] ?? 0;
        $subsidyPrice      = $aData['subsidy_price'] ?? 0;
        $deposit_price     = $aData['deposit_price'] ?? 0;
        $coupon_id         = $aData['coupon_id'] ?? 0;
        $coupon_market_id  = $aData['coupon_market_id'] ?? 0;
        $consume_id        = $aData['consume_id'] ?? 0;
        $consume_market_id = $aData['consume_market_id'] ?? 0;
        $consume_price     = $aData['consume_price'] ?? 0;
        $cprice            = $aData['cprice'] ?? 0;
        $coin              = $aData['coin'] ?? 0;
        $coin_price        = $aData['coin_price'] ?? 0;
        $shopping_price    = $aData['shopping_price'] ?? 0;
        $consume_money     = $aData['consume_money'] ?? 0;
        $acdeprice         = $aData['acdeprice'] ?? 0;
        $ctime             = $aData['ctime'] ?? 0;
        $note              = $aData['note'] ?? "";
        $coin_logid        = $aData['coin_logid'] ?? 0;
        $deposit_order_no  = $aData['deposit_order_no'] ?? '';
        $gift_card_ids     = $aData['gift_card_ids'] ?? '';
        $gift_card_price   = $aData['gift_card_price'] ?? 0;
        $group_purchase_id = $aData['group_purchase_id'] ?? 0;
        $relationType      = $aData['relation_type'] ?? 0;
        $relationId        = $aData['relation_id'] ?? 0;

        $save = [
                'order_no'          => $order_no,
                'user_id'           => $user_id,
                'oprice'            => $oprice,
                'exprice'           => $exprice,
                'subsidy_price'     => $subsidyPrice,
                'deposit_price'     => $deposit_price,
                'price'             => $price,
                'fprice'            => $fprice,
                'coupon_id'         => $coupon_id,
                'coupon_market_id'  => $coupon_market_id,
                'cprice'            => $cprice,
                'consume_id'        => $consume_id,
                'consume_market_id' => $consume_market_id,
                'consume_price'     => $consume_price,
                'coin'              => $coin,
                'coin_price'        => $coin_price,
                'shopping_price'    => $shopping_price,
                'consume_money'     => $consume_money,
                'coin_logid'        => $coin_logid,
                'acdeprice'         => $acdeprice,
                'ctime'             => $ctime,
                'note'              => $note,
                'deposit_order_no'  => $deposit_order_no,
                'gift_card_ids'     => $gift_card_ids,
                'gift_card_price'   => $gift_card_price,
                'group_purchase_id' => $group_purchase_id,
                'relation_type'     => $relationType,
                'relation_id'       => $relationId,

        ];

        $tb = $this->tbName($user_id);

        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        return [true, 'ok'];
    }


    /**
     * @param $user_id
     * @param $deposit_order_no
     * @param bool $cache
     * @return array|DataReader
     * @throws Exception
     * 根据depositNo 获取订单数据
     */
    public function GetInfoByDepositNo($user_id, $deposit_order_no, bool $cache = true)
    {
        $user_id = CUtil::uint($user_id);
        if (empty($deposit_order_no) || empty($user_id)) {
            return [];
        }
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoByDepositKey($user_id, $deposit_order_no);
        $aJson     = $cache ? $redis->get($redis_key) : false;
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this->tbName($user_id);
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `deposit_order_no`=:deposit_order_no AND `user_id`=:user_id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':deposit_order_no' => $deposit_order_no, ':user_id' => $user_id])->queryOne();
            $aData  = $aData ?: [];
            $cache == true && $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        return $aData;
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param bool $cache
     * @return array|false
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetInfoByOrderId($user_id, $order_no, $cache = true)
    {
        $user_id = CUtil::uint($user_id);

        if (empty($order_no) || empty($user_id)) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoKey($user_id, $order_no);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false || !$cache) {
            $tb = $this->tbName($user_id);

            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no AND `user_id`=:user_id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':order_no' => $order_no, ':user_id' => $user_id])->queryOne();
            $aData  = $aData ?: [];
            $cache == true && $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        $aData['stime'] = $aData['stime'] ?? 0; //hack 新加字段 下版删除

        //获取卡券类型
        $coupon_id = CUtil::uint($aData['coupon_id']);
        if ($coupon_id > 0) {
            $user_card_info = by::userCard()->getCardById($user_id, $aData['coupon_id']);
        }
        $aData['card_type'] = $user_card_info['type'] ?? 0;

        //获取消费券类型
        $consume_id = CUtil::uint($aData['consume_id'] ?? 0);
        if($consume_id >0) {
            $user_consume_info = by::userCard()->getCardById($user_id, $aData['consume_id']);
        }
        $aData['consume_type'] = $user_consume_info['type'] ?? 0;

        return $aData;
    }

    /**
     * @param $user_id
     * @param $status
     * @param $order_no
     * @param $platformSource
     * @param $page
     * @param $page_size
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 订单列表
     */
    public function GetList($user_id, $status = -1, $order_no = '',  $platformSource = '', $page = 1, $page_size = 10)
    {
        $page        = CUtil::uint($page,1);
        $redis       = by::redis('core');
        $r_key       = $this->__getOuserListKey($user_id);
        $sub_key     = CUtil::getAllParams(__FUNCTION__,$status,$order_no,$platformSource,$page,$page_size);
        $aJson       = $redis->hGet($r_key,$sub_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = $this->tbName($user_id);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($user_id,$status,$order_no,$platformSource);
            $sql                 = "SELECT `order_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC 
                                    LIMIT {$offset},{$page_size}";

            $command             = by::dbMaster()->createCommand($sql,$params);
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param int $status
     * @param string $order_no
     * @return int
     * @throws \yii\db\Exception
     * 订单总数
     */
    public function GetListCount($user_id, $status = -1, $order_no = '', $platformSource = '')
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getOuserListKey($user_id);
        $sub_key     = CUtil::getAllParams(__FUNCTION__, $status, $order_no, $platformSource);
        $count       = $redis->hGet($r_key,$sub_key);

        if($count === false) {
            $tb                  = $this->tbName($user_id);
            list($where,$params) = $this->__getCondition($user_id, $status, $order_no, $platformSource);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $command             = by::dbMaster()->createCommand($sql,$params);
            $count               = $command->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return intval($count);
    }


    /**
     * @param $user_id
     * @param $status
     * @param $order_no
     * @param $platformSource
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($user_id = 0, $status = -1, $order_no = '', $platformSource = ''): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if ($user_id > 0) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"] = intval($user_id);
        }

        //订单状态
        if ($status > -1) {
            list($w, $p) = by::Omain()->GetOrderStatus($status, true);

            if ($w) {
                $where  .= " AND {$w}";
                $params = array_merge($params, $p);
            }
        }

        //指定订单
        if (!empty($order_no)) {
            $where               .= " AND `order_no` = :order_no";
            $params[":order_no"] = $order_no;
        }

        //先试后买订单号
        $orderInfos = byNew::UserOrderTry()->GetList([
            CUtil::buildCondition('user_id', '=', $user_id),
        ], false);

        if ($orderInfos) {
            $orderNos = array_column($orderInfos, 'order_no');
            $orderArr = implode("','", $orderNos);
            if (in_array($platformSource, [9, 10])) {
                //支付宝小程序 只展示先试后买订单
                $where .= " AND `order_no` IN  ('{$orderArr}')";
            } else {
                //不展示先试后买订单
                $where .= " AND `order_no` NOT IN  ('{$orderArr}')";
            }
        }else{
            if (in_array($platformSource, [9, 10])) {
                $where .= " AND `order_no` is null";
            }
        }


        return [$where,$params];
    }

    // 通用函数处理图片逻辑
    private function getImage($images)
    {
        foreach ($images as $image) {
            if (!empty($image)) {
                return $image;
            }
        }
        return '';
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param bool $can_user
     * @param bool $can_attr
     * @param bool $can_ad
     * @param bool $can_remain
     * @param bool $cache
     * @param bool $isShortCode
     * @return array|false
     * @throws Exception
     * @throws RedisException
     * 统一打包数据
     */
    public function CommPackageInfo($user_id, $order_no, $can_user = false, $can_attr = false, $can_ad = false, $can_remain = false, $cache = true, $isShortCode = false, $can_goods = true, $can_main = true)
    {
        $info = $this->GetInfoByOrderId($user_id, $order_no, $cache);
        if (empty($info)) {
            return [];
        }
        $mainModel = by::Omain();

        $info['is_presale_order'] = by::Gmain()::PTYPE['NO_PRESALE'];     //普通订单
        $info['user_order_type']  = $mainModel::USER_ORDER_TYPE['COMMON'];//订单类型-普通订单
        $info['deposit_price']    = 0;
        $info['ac_info']          = [];

        if ($can_main) {
            $mainInfo                = $mainModel->getInfoByOrderNo($user_id, $order_no);
            $info['platform_source'] = $mainInfo['platform_source'] ?? 0; //平台来源
            $info['user_order_type'] = CUtil::uint($mainInfo['type'] ?? $mainModel::USER_ORDER_TYPE['COMMON']);//订单类型-普通订单
        }

        //先试后买订单查询活动ID
        if ($info['user_order_type'] == $mainModel::USER_ORDER_TYPE['BAT']) {
            $orderTryInfo     = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('user_id', '=', $user_id), CUtil::buildCondition('order_no', '=', $order_no)]);
            $acInfo           = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($orderTryInfo['ac_id'] ?? 0);
            $orderTryInfo['remain_try_time'] = 0;
            $orderTryInfo['remain_deduct_time'] = 0;
            if($orderTryInfo && $acInfo){
                //todo 试用截止时间
                if ($orderTryInfo['arrival_time'] != 0 && $orderTryInfo['try_status'] == byNew::UserOrderTry()::TRY_STATUS['ON_TRY']) {
                    $orderTryInfo['remain_try_time'] = $orderTryInfo['arrival_time'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $acInfo['validity']-time();
                }
                //todo 退机截止时间
                if ($orderTryInfo['try_status'] == byNew::UserOrderTry()::TRY_STATUS['WAIT_BACK'] && $orderTryInfo['arrival_time'] != 0) {
                    $orderTryInfo['remain_deduct_time'] = $orderTryInfo['arrival_time'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $acInfo['validity'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $acInfo['return_period']-time();
                }
            }
            $info['try_info'] = $orderTryInfo;
            $info['ac_info']  = $acInfo;
        }

        //补充定金订单信息
        $deposit_order_no = $info['deposit_order_no'] ?? '';
        if ($deposit_order_no) {
            $info['deposit_order_info'] = by::Odeposit()->getInfoByDepositOrderNo($user_id, $deposit_order_no, false, false, true, false);
            $info['deposit_price']      = $info['deposit_order_info']['price'] ?? 0;
            $info['is_presale_order']   = by::Gmain()::PTYPE['PRESALE']; //参与过定金订单
            $info['user_order_type']    = $mainModel::USER_ORDER_TYPE['TAIL']; //订单类型-尾款订单
        }

        // 获取拼团信息
//        $group_purchase_id = $info['group_purchase_id'] ??0;
//        if ($group_purchase_id && $info['status'] > OmainModel::ORDER_STATUS['CANCELED']) {
//            //  组装当前团状态信息
//            $groupInfo = byNew::GroupPurchaseModel()->getGroupInfo($user_id, $group_purchase_id);
//            if ($groupInfo) {
//                $info['group_info'] = $groupInfo;
//            }
//        }
        //尾款金额
        $tailPrice = 0;

        //订单商品数据
        if ($can_goods) {
            $oGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
            foreach ($oGoods as $k => $val) {
                $oCfg = by::Ocfg()->GetOneByUnique($order_no, $val['gid'], $val['sid']);
                if (empty($oCfg)) {continue;}
                $uprice     = $oCfg['spec']['price'] ?? $oCfg['price'] ?? 0;
                $upoint     = $oCfg['spec']['point'] ?? $oCfg['point'] ?? 0;
                $ucoupon_id = $oCfg['spec']['coupon_id'] ?? $oCfg['coupon_id'] ?? 0;
                $ucomsume_id = $oCfg['spec']['consume_id'] ?? $oCfg['consume_id'] ?? 0;
                $goods_type = $val['goods_type'] ?? 0;
                if ($goods_type == by::Ogoods()::GOODS_TYPE['WARES']) {
                    $oGoods[$k] = [
                            'id'            => $val['id'],
                            'gid'           => $val['gid'],
                            'sid'           => $val['sid'] ?? 0,
                            'sku'           => $oCfg['spec']['sku'] ?? $oCfg['sku'] ?? '',
                            'name'          => $oCfg['name'] ?? '',
                            'cover_image'   => $oCfg['spec']['image'] ?? $oCfg['cover_image'] ?? '',
                            'is_send'       => $oCfg['is_send'] ?? 1,
                            'label'         => $oCfg['label'] ?? 0,
                            'num'           => $val['num'],
                            'coin'          => $val['coin'],
                            'oprice'        => CUtil::totalFee($val['oprice'], 1),
                            'price'         => CUtil::totalFee($val['price'], 1),
                            'cprice'        => CUtil::totalFee($val['cprice'], 1),
                            'coin_price'    => CUtil::totalFee($val['coin_price'], 1),
                            'uprice'        => CUtil::totalFee($uprice, 1),
                            'upoint'        => $upoint,
                            'ucoupon_id'    => CUtil::uint($ucoupon_id),
                            'status'        => $val['status'],
                            'type'          => $oCfg['type'] ?? '0',
                            'subtype'       => $oCfg['subtype'] ?? '0',
                            'goods_type'    => $goods_type,
                            'market_image'  => $oCfg['spec']['image'] ?? $oCfg['market_image'] ?? $oCfg['cover_image'] ?? '',
                            'is_yjhx'       => $oCfg['is_yjhx'] ?? 0,
                            'coupon_id'     => $oCfg['coupon_id'] ?? 0,
                            'consume_price' => CUtil::totalFee($val['consume_price'] ?? 0, 1),
                            'ucomsume_id'   => CUtil::uint($ucomsume_id),
                            'comsume_id'    => $oCfg['comsume_id'] ?? 0,
                            'is_employee'   => $oCfg['is_employee'] ?? 0, // 是否员工购买
                            'is_third'      => $val['is_third'] ?? 0,
                            'mail_no'       => $val['mail_no'] ?? '',
                            'express_code'  => $val['express_code'] ?? '',
                            'express_name'  => $val['express_name'] ?? '',
                    ];

                    $oGoods[$k]['short_code'] = ($isShortCode && $oGoods[$k]['sku'])
                        ? (by::GoodsMainModel()->GetOneByGid($oGoods[$k]['sku'])['short_code'] ?? '') : '';

                    //规格属性名
                    if ($can_attr) {
                        $av_ids   = $oCfg['spec']['av_ids'] ?? '';
                        $attr_cnf = [];
                        if ($av_ids) {
                            $av_ids = json_decode($av_ids, true);
                            foreach ($av_ids as $k1 => $v1) {
                                $cnf = by::GoodsAvModel()->IdToName($v1);
                                $cnf && $attr_cnf[] = $cnf;
                            }
                        }
                        $oGoods[$k]['attr_cnf'] = $attr_cnf;
                    }
                } else {
                    // 从配置中提取图片字段，确保字段存在
                    $specImage   = $oCfg['spec']['image'] ?? '';
                    $coverImage  = $oCfg['cover_image'] ?? '';
                    $marketImage = $oCfg['market_image'] ?? '';
                    $pcImage     = $oCfg['pc_image'] ?? '';

                    // 处理封面图：如果存在多规格图片，则取多规格图片，否则取封面图
                    $cover_image = $this->getImage([
                        $specImage,
                        $coverImage
                    ]);

                    // 处理营销图：如果存在多规格图片，则取多规格图片，否则取营销图。如果营销图为空，则取封面图，如果封面图也为空，则返回空
                    $market_image = $this->getImage([
                        $specImage,
                        $marketImage,
                        $coverImage
                    ]);

                    // 处理PC图：如果存在多规格图片，则取多规格图片，否则取PC图。如果PC图为空，则取营销图，如果营销图也为空，则取封面图，如果封面图也为空，则返回空
                    $pc_image = $this->getImage([
                        $specImage,
                        $pcImage,
                        $marketImage,
                        $coverImage
                    ]);

                    $oGoods[$k] = [
                            'id'                   => $val['id'],
                            'gid'                  => $val['gid'],
                            'sid'                  => $val['sid'] ?? 0,
                            'sku'                  => $oCfg['spec']['sku'] ?? $oCfg['sku'] ?? '',
                            'name'                 => $oCfg['name'] ?? '',
                            'cover_image'          => $cover_image,
                            'market_image'         => $market_image,
                            'pc_image'             => $pc_image,
                            'tids'                 => $oCfg['tids'] ?? [],
                            'is_internal_purchase' => $oCfg['is_internal_purchase'] ?? 0,
                            'is_internal'          => $oCfg['is_internal'] ?? 0,
                            'is_yjhx'              => $oCfg['is_yjhx'] ?? 0,
                            'gini_id'              => $oCfg['gini_id'] ?? 0,
                            'is_presale'           => CUtil::uint($oCfg['is_presale'] ?? 0),
                            'num'                  => $val['num'],
                            'coin'                 => $val['coin'],
                            'oprice'               => by::Gtype0()->totalFee($val['oprice'], 1),
                            'price'                => by::Gtype0()->totalFee($val['price'], 1),
                            'cprice'               => by::Gtype0()->totalFee($val['cprice'], 1),
                            'consume_price'        => by::Gtype0()->totalFee($val['consume_price'] ?? 0, 1),
                            'coin_price'           => by::Gtype0()->totalFee($val['coin_price'], 1),
                            'subsidy_price'        => by::Gtype0()->totalFee($val['subsidy_price'] ?? 0, 1),
                            'uprice'               => by::Gtype0()->totalFee($uprice, 1),
                            'exprice'              => by::Gtype0()->totalFee($val['exprice'] ?? 0, 1),
                            'deposit_price'        => by::Gtype0()->totalFee($info['deposit_price'] ?? 0, 1),
                            'shopping_price'       => by::Gtype0()->totalFee($val['shopping_price'] ?? 0, 1),

                            'status'          => $val['status'],
                            'type'            => $oCfg['type'] ?? '0',
                            'goods_type'      => $goods_type,
                            'gift_card_type'  => $val['gift_card_type'] ?? 0,
                            'gift_card_value' => $val['gift_card_value'] ?? 0,
                            'is_employee'     => $oCfg['is_employee'] ?? 0, // 是否员工购买
                            'is_third'      => $val['is_third'] ?? 0,
                            'mail_no'       => $val['mail_no'] ?? '',
                            'express_code'  => $val['express_code'] ?? '',
                            'express_name'  => $val['express_name'] ?? '',
                    ];

                    $oGoods[$k]['presale_info'] = [];
                    if ($oGoods[$k]['is_presale'] == 1) {
                        $oGoods[$k]['presale_info'] = [
                            'deposit_status'    => (($info['deposit_order_info']['status'] ?? 0) >= 300) ? 1 : 0,//定金订单状态
                            'presale_time'      => $oCfg['presale_time'] ?? 0,//预售截止时间
                            'deposit'           => by::Gtype0()->totalFee($oCfg['deposit'] ?? 0, 1),//定金
                            'expand_price'      => by::Gtype0()->totalFee($oCfg['expand_price'] ?? 0, 1),//膨胀金额
                            'start_payment'     => $oCfg['start_payment'] ?? 0,//尾款开始时间
                            'end_payment'       => $oCfg['end_payment'] ?? 0,//尾款结束时间
                            'surplus_time'      => $oCfg['surplus_time'] ?? 0,//距离尾款几小时推送
                            'scheduled_number'  => $oCfg['scheduled_number'] ?? 0,//预定人数
                            'tail_price'        => by::Gtype0()->totalFee(bcsub(($oCfg['spec']['price'] ?? $oCfg['price']) ?? 0, $oCfg['expand_price'] ?? 0, 2), 1),
                            'tail_order_status' => (($info['status'] ?? 0) >= 300) ? 1 : 0
                        ];
                        $oGoods[$k]['presale_info'] = by::Odeposit()->_judgePayOrder($oGoods[$k]['presale_info']);
                        $tailPrice                  = bcadd($tailPrice, bcmul($oGoods[$k]['presale_info']['tail_price'] ?? 0, $oGoods[$k]['num'] ?? 1, 2), 2);
                    }

                    if ($isShortCode && $oGoods[$k]['sku']) {
                        $oGoods[$k]['short_code'] = by::Gmain()->GetOneBySku($oGoods[$k]['sku'])['short_code'] ?? '';
                    } else {
                        $oGoods[$k]['short_code'] = '';
                    }
                    //规格属性名
                    if ($can_attr) {
                        $av_ids   = $oCfg['spec']['av_ids'] ?? '';
                        $attr_cnf = [];
                        if ($av_ids) {
                            $av_ids = json_decode($av_ids, true);
                            foreach ($av_ids as $k1 => $v1) {
                                $cnf = by::Gav()->IdToName($v1);
                                $cnf && $attr_cnf[] = $cnf;
                            }
                        }
                        $oGoods[$k]['attr_cnf'] = $attr_cnf;
                    }
                }
            }
        }

        $info['goods'] = $oGoods ?? [];


        if ($can_user) {
            $user         = by::users()->getOneByUid($user_id);
            $info['user'] = [
                'avatar' => $user['avatar'] ?? "",
                'nick'   => $user['nick'] ?? "",
            ];
        }

        //收货地址
        if ($can_ad) {
            $oAd = by::Oad()->GetOneByOrderNo($order_no);
            if (!empty($deposit_order_no) && empty($oAd)) {
                $oAd = by::Oad()->GetOneByOrderNo($deposit_order_no);
            }

            // 是否默认地址
            $defaultAddress   = by::Address()->GetDefaultAddress($user_id, 0, false);
            $isDefaultAddress = 0;
            if (
                $defaultAddress &&
                $defaultAddress['id'] == ($oAd['address_id'] ?? 0) &&
                $defaultAddress['status'] == AddressModel::STATUS['YES']
            ) {
                $isDefaultAddress = 1;
            }

            !empty($oAd) && $address = [
                'id'                 => $oAd['address_id'], // 收货地址ID
                'nick'               => $oAd['nick'],
                'phone'              => $oAd['phone'],
                'province'           => $oAd['address']['province'],
                'city'               => $oAd['address']['city'],
                'area'               => $oAd['address']['area'],
                'detail'             => $oAd['detail'],
                'pid'                => $oAd['pid'],
                'cid'                => $oAd['cid'],
                'aid'                => $oAd['aid'],
                'is_default_address' => $isDefaultAddress,
            ];
            $info['address'] = $address ?? [];
            $info['mail']    = [
                'mail_no'      => $oAd['mail_no'] ?? '',
                'express_code' => $oAd['express_code'] ?? '',
                'express_name' => $oAd['express_name'] ?? '',
            ];
        }

        //收货地址
        if ($can_remain && $info['status'] == $mainModel::ORDER_STATUS['WAIT_PAY']) {
            $now_time = intval(START_TIME);
            $remain_time            = $info['ctime'] + $mainModel::PAY_EXPIRE - $now_time + 5;
            $info['remain_time']    = $remain_time < 0 ? 0 : $remain_time;

            //尾款订单处理
            if (!empty($deposit_order_no)) {
                $endPayment = CUtil::uint($oGoods[0]['presale_info']['end_payment'] ?? 0);
                if ($endPayment) {
                    $remain_time         = $endPayment - $now_time;
                    $info['remain_time'] = $remain_time < 0 ? 0 : $remain_time;
                }
            }
        }

        foreach ($this->__format_fields as $field) {
            $info[$field] = by::Gtype0()->totalFee($info[$field] ?? 0, 1);
        }

        $info['dprice']      = bcsub($info['oprice'], $info['price'], 2);
        $info['oreal_price'] = bcadd($info['oprice'], $info['fprice'], 2);
        $info['real_price']  = bcadd($info['price'], $info['fprice'], 2);

        //预售商品处理
        $info['dprice']      = bcsub($info['dprice'], $info['deposit_price'] ?? 0, 2);
        $info['oreal_price'] = bcsub($info['oreal_price'], $info['exprice'] ?? 0, 2);
        $info['oreal_price'] = bcadd($info['oreal_price'], $info['deposit_price'] ?? 0, 2);
        $info['tail_price']  = $tailPrice;

        if ($info['status'] >= $mainModel::ORDER_STATUS['WAIT_SEND']) {
            $info['real_price'] = bcadd($info['real_price'], $info['deposit_price'] ?? 0, 2);
        }

        //订单状态
        list($_, $info['status']) = $mainModel->SplitOrderStatus($info['status']);

        //获取卡券类型
        $coupon_id = CUtil::uint($info['coupon_id']);
        if ($coupon_id > 0) {
            $user_card_info = by::userCard()->getCardById($user_id, $info['coupon_id']);
        }
        $cr_type           = $info['cr_type'] ?? 1;
        $info['card_type'] = $user_card_info['type'] ?? 0;
        $cr_type > 0 && $info['cr_name'] = $mainModel::R_TYPE[$info['cr_type'] ?? 1];

        return $info;
    }


    private function handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, $card_type, $coupon_id = null, $sub_price = null) {
        $card_arr = [
            'gcombines' => is_array($gcombines) ? json_encode($gcombines) : $gcombines,
            'coupon_id' => $coupon_id,
        ];
        list($status, $ret) = by::cart()->canUseCard($user_id, $card_arr, $getChannel, $spriceType, $card_type, $sub_price);
        return [$status, $ret];
    }



    // 常量定义（替换魔法值，提升可维护性）
    const COIN_TYPE_NOT_USE   = 2;                                // 积分不使用
    const COUPON_TYPE_NOT_USE = 3;                                // 优惠券不使用
    const VOUCHER_NUM_LIMIT   = 1;                                // 兑换券限购数量
    const SUBSIDY_APIS        = ['a_1664246268', 'i_1666147923', 'a_1643178000', 'i_1643178026']; // 支持国补的API
    const SHOPPING_PRICE_APIS = ['a_1664246268', 'i_1666147923']; // 支持购物金的API
    const CONSUME_MONEY_APIS  = ['a_1664246268', 'i_1666147923']; // 支持消费金的API
    const PRICE_PRECISION     = 2;                                // 价格计算精度（保留2位小数）

    /**
     * @param int $userId
     * @param array $params
     * @return array
     * @throws Exception
     * @throws RedisException
     * 订单结算页
     */
    public function buyInfo(int $userId, array $params): array
    {
        // ======================== 1. 参数解析与初始化 ========================
        // 商品组合（JSON转数组）
        $goodsCombinations = $params['gcombines'] ?? '';
        $goodsCombinations = (array) json_decode($goodsCombinations, true);
        // 活动数据（JSON转数组）
        $acData = $params['ac_data'] ?? '';
        $acData = (array) json_decode($acData, true);

        // 基础参数提取
        $sn                 = $acData['sn'] ?? '';
        $cartIds            = $params['cart_ids'] ?? '';
        $addressId          = (int) ($params['aid'] ?? 0);
        $couponId           = (int) ($params['coupon_id'] ?? 0);
        $consumeId          = (int) ($params['consume_id'] ?? 0);
        $coin               = (int) ($params['coin'] ?? 0);
        $coinType           = (int) ($params['coin_type'] ?? 0);
        $usageCouponType    = (int) ($params['usage_coupon_type'] ?? 0);
        $guideCode          = $params['code'] ?? '';
        $getChannel         = (int) ($params['get_channel'] ?? 0);
        $spriceType         = (int) ($params['sprice_type'] ?? 0);
        $giftCardIdsStr     = $params['gift_card_ids'] ?? '';
        $giftCardIds        = empty($giftCardIdsStr) ? [] : explode(',', $giftCardIdsStr);
        $isTradeIn          = (int) ($params['is_trade_in'] ?? 0);
        $groupActivityId    = (int) ($params['group_activity_id'] ?? 0);
        $disableAllDiscount = (int) ($params['disable_all_discount'] ?? 0);

        $relationType = $arr['relation_type'] ?? 'COMMON';
        $relationType = OmainModel::ORDER_RELATION_TYPE[$relationType] ?? 1; // 业务类型
        $is_wxds = 0;
        // 2025/8/11 去掉追觅大使8折资格
//        $wxds = byNew::UserSmileModel()->getInfo($userId,'user_id');
//        if ($wxds){
//            $is_wxds = 1;
//        }

        // 初始化优惠相关标记
        $usageCouponNum  = 0;
        $usageCouponTag  = 0;
        $usageConsumeNum = 0;
        $usageConsumeTag = 0;
        $couponTag       = $couponId;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  // 优惠券使用标识
        $consumeTag      = $consumeId;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             // 消费券使用标识


        // ======================== 2. 禁用所有优惠处理 ========================
        if ($disableAllDiscount) {
            $couponId        = 0;
            $consumeId       = 0;
            $coin            = 0;
            $coinType        = self::COIN_TYPE_NOT_USE; // 不使用积分
            $giftCardIds     = [];
            $giftCardIdsStr  = "";
            $usageCouponType = self::COUPON_TYPE_NOT_USE; // 不使用优惠券
        }


        // ======================== 3. 活动信息校验（若有活动SN） ========================
        $preSn = '';
        if (!empty($sn)) {
            $couponTag = -1; // 活动场景不允许用优惠券
            // 过滤SN特殊字符
            $sn = CUtil::removeXss($sn);
            $sn = str_replace(['\\', ';', ','], '', $sn);

            // 校验活动时间
            if (!ActivityConfigEnum::judgeActivityTime($userId)) {
                return [false, '不在活动时间内，不允许参加哟！'];
            }

            // 校验产品注册信息
            $regInfo = by::productReg()->getPRegInfo($userId, $sn);
            if (empty($regInfo)) {
                return [false, '您不符合参与活动规定！'];
            }
            $productId   = CUtil::uint($regInfo['product_id'] ?? 0);
            $productInfo = by::product()->getOneById($productId);
            if (empty($productInfo) || empty($productInfo['sn'])) {
                return [false, '您不符合参与活动规定！'];
            }
            $preSn = $productInfo['sn']; // 可兑换商品SKU的活动SN
        }


        // ======================== 4. 优惠券与消费券类型校验 ========================
        $cardType        = 0;
        $consumeType     = 0;
        $userCardInfo    = [];
        $userConsumeInfo = [];

        if (!$disableAllDiscount) {
            // 校验消费券类型
            $consumeId = (int) $consumeId;
            if ($consumeId > 0) {
                $userConsumeInfo = by::userCard()->getCardById($userId, $consumeId, $getChannel);
            }
            $consumeType = $userConsumeInfo['type'] ?? 0;

            // 校验优惠券类型
            $couponId = (int) $couponId;
            if ($couponId > 0) {
                $userCardInfo = by::userCard()->getCardById($userId, $couponId, $getChannel);
            }
            $cardType = $userCardInfo['type'] ?? 0;

            // 兑换券不能与消费券同时使用
            if ($cardType == UserCardModel::TYPE['voucher'] && $consumeType != 0) {
                return [false, '兑换券使用时不允许使用消费券！'];
            }
        }


        // ======================== 5. 商品信息校验与基础价格计算 ========================
        // 校验商品合法性
        list($checkStatus, $checkMsg) = by::Omain()->checkGoods($userId, $goodsCombinations);
        if (!$checkStatus) {
            return [false, $checkMsg];
        }

        // 初始化商品总价相关变量
        $totalPrice          = 0;                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           // 商品总价
        $activityDeductPrice = 0; // 活动抵扣金额
        $minPayPrice         = 0; // 订单最少付款额
        $isYjhx              = 0; // 是否有以旧换新商品
        $shippingData        = []; // 运费计算所需数据
        $buyGoods = []; // 购买商品列表 id=> 商品信息

        // 1、根据活动ID  $groupActivityId 拉取当前活动下配置了哪些商品
        $groupActivityGoodsIds = GroupPurchaseService::getInstance()->getGidByActivityId($groupActivityId);
        $buyGoodsMap = []; // 购买商品列表 map
        foreach ($goodsCombinations as $key => $item) {
            $gid = CUtil::uint($item['gid']);
            $sid = CUtil::uint($item['sid']);
            $num = CUtil::uint($item['num'] ?? 0);

            // 校验商品数量
            if ($num <= 0) {
                return [false, '商品数量不能为0'];
            }

            // 兑换券限购1个（非禁用优惠时）
            if (!$disableAllDiscount && $cardType == UserCardModel::TYPE['voucher'] && $num != self::VOUCHER_NUM_LIMIT) {
                return [false, '兑换券仅可兑换单个商品'];
            }

            // 获取商品详情
            $goodsInfo = by::Gmain()->GetOneByGidSid($gid, $sid, true, true, $spriceType);
            if (empty($goodsInfo)) {
                continue;
            }

            // 活动折扣价格计算（若有活动SN和商品SKU）
            $sku                    = $goodsInfo['spec']['sku'] ?? $goodsInfo['sku'];
            $goodsInfo['acdeprice'] = 0;
            if ($preSn && $sku) {
                $acDepriceInfo          = by::GmainAcDepriceModel()->GetInfoBySnAndSku($preSn, $sku);
                $goodsInfo['acdeprice'] = $acDepriceInfo['deprice'] ?? 0;
                $goodsInfo['is_yjhx']   = 1;
                $isYjhx++;
            }

            // 收集运费计算数据
            $shippingData[] = [
                    'gid'              => $gid,
                    'sid'              => $sid,
                    'num'              => $num,
                    'price'            => $goodsInfo['spec']['price'] ?? $goodsInfo['price'],
                    'is_free_shipping' => $goodsInfo['is_free_shipping'] ?? 0,
            ];

            // 清理商品信息冗余字段
            unset($goodsInfo['sort'], $goodsInfo['mprice'], $goodsInfo['images'], $goodsInfo['is_coupons'],
                    $goodsInfo['t_status'], $goodsInfo['t_time'], $goodsInfo['detail'], $goodsInfo['tids']);

            // 更新商品组合信息（含自定义价格）
            $goodsCombinations[$key] = by::Ouser()->GetGoodsGiniId($goodsInfo, $item);

            // 计算商品小计
            $singlePrice         = $goodsInfo['spec']['price'] ?? $goodsInfo['price'];
            $goodsInfo['tprice'] = bcmul($singlePrice, $num, self::PRICE_PRECISION);

            // 团购活动限购处理
            if ($groupActivityId > 0) {
                // 2、校验当前商品是否都为团购商品、如果有不是团购的商品 给出提示 团购商品只能单独购买
                if (!in_array($gid, $groupActivityGoodsIds)) {
                    return [false, '团购商品只能单独购买呦！'];
                }
                $rate = GroupPurchaseService::getInstance()->getDiscountRate($gid);

                $goodsInfo['limit_num'] = GroupPurchaseService::getInstance()->limitBuy($groupActivityId, $gid, $userId);
                $goodsInfo['tprice']    = bcmul($goodsInfo['tprice'], $rate, 2);
            }
            // 微笑大使给8折
            elseif ($is_wxds && $relationType == 1){
                $goodsInfo['tprice']    = bcmul($goodsInfo['tprice'], 0.8, 2);
            }

            $goodsInfo['num']    = (string) $num;

            // 累计总价相关数据
            $totalPrice          = bcadd($totalPrice, $goodsInfo['tprice'], self::PRICE_PRECISION);
            $activityDeductPrice = bcadd($activityDeductPrice, $goodsInfo['acdeprice'], self::PRICE_PRECISION);
            $minPayPrice         = bcadd($minPayPrice, bcmul($num, 0.01, self::PRICE_PRECISION), self::PRICE_PRECISION);

            // 收集商品列表数据
            $resultData['list'][] = $goodsInfo;
            // 收集购买商品信息
            $buyGoods[$gid]=$goodsInfo['name'] ?? '';
            $buyGoodsMap[$gid] = [
                'type'  => $goodsInfo['type'] ?? -1,
                'name'  => $goodsInfo['name'] ?? '',
            ];
        }

        // 3、如果是团购商品 计算价格 只能用消费金优惠  并且金额为6折
        if ($groupActivityId > 0) {
            // 禁用其他优惠
            $disableAllDiscount = 1;
        }


        // ======================== 6. 购物金处理 ========================
        // 判断你买的商品是否支持购物金（如果全都不支持那就不使用购物金、如果部分支持部分不支持，给出提示哪部分不支持、如果都支持那就正常下单）
        list($success, $isUseShoppingPrice, $userShoppingMoney, $reallyShoppingMoney, $errorMsg) =
                $this->applyShoppingMoney($userId, $params, $buyGoods, $totalPrice, $disableAllDiscount);

        if (!$success) {
            return [false, $errorMsg];
        }

        // 若使用购物金成功，则强制禁用其他优惠
        if ($isUseShoppingPrice) {
            $couponId    = -1;
            $consumeId   = -1;
            $coin        = 0;
            $coinType    = self::COIN_TYPE_NOT_USE;
            $giftCardIds = [];
        }


        // ======================== 7. 优惠券/消费券折扣计算 ========================
        if (!$disableAllDiscount) {
            $couponDiscount = $this->applyCouponDiscount($userId, $goodsCombinations, $getChannel, $spriceType, $couponId, $consumeId);
        } else {
            // 禁用优惠时默认折扣信息
            $couponDiscount = [
                    'discount_price'    => 0,
                    'cprice'            => 0,
                    'consume_price'     => 0,
                    'usage_coupon_tag'  => self::COUPON_TYPE_NOT_USE,
                    'usage_coupon_num'  => 0,
                    'usage_consume_tag' => self::COUPON_TYPE_NOT_USE,
                    'usage_consume_num' => 0,
                    'coupon_id'         => 0,
                    'consume_id'        => 0,
                    'card_type'         => 0
            ];
        }

        // 解构优惠券折扣结果
        $discountPrice   = $couponDiscount['discount_price'];
        $couponPrice     = $couponDiscount['cprice'];
        $consumePrice    = $couponDiscount['consume_price'];
        $usageCouponTag  = $couponDiscount['usage_coupon_tag'];
        $usageCouponNum  = $couponDiscount['usage_coupon_num'];
        $usageConsumeTag = $couponDiscount['usage_consume_tag'];
        $usageConsumeNum = $couponDiscount['usage_consume_num'];
        $couponId        = $couponDiscount['coupon_id'];
        $consumeId       = $couponDiscount['consume_id'];
        $cardType        = $couponDiscount['card_type'];


        // ======================== 8. 积分折扣处理 ========================
        $canCoin      = 0; // 可使用积分
        $coinPrice    = 0; // 积分抵扣金额
        $currentPrice = 0; // 积分处理后的价格
        $freightOther = []; // 运费计算附加信息

        if (!$disableAllDiscount) {
            if ($cardType == UserCardModel::TYPE['voucher']) {
                // 兑换券不使用积分
                $canCoin      = 0;
                $coinPrice    = 0;
                $currentPrice = bcsub($totalPrice, $couponPrice, self::PRICE_PRECISION);
                $freightOther = [
                        'card_type' => $cardType,
                        'market_id' => $userCardInfo['market_id'] ?? 0,
                ];
            } else {
                // 正常积分抵扣计算
                list($pointStatus, $pointDiscount) = $this->applyPointDiscount($userId, $totalPrice, $discountPrice, $activityDeductPrice, $minPayPrice, $coin, $coinType);
                if (!$pointStatus) {
                    return [false, $pointDiscount];
                }
                $canCoin      = $pointDiscount['can_coin'];
                $coinPrice    = $pointDiscount['coin_price'];
                $currentPrice = $pointDiscount['price'];
            }
        } else {
            // 禁用优惠时不使用积分
            $canCoin      = 0;
            $coinPrice    = 0;
            $currentPrice = $totalPrice;
        }


        // ======================== 9. 应用购物金抵扣（积分后执行） ========================
        if ($reallyShoppingMoney > 0) {
            $currentPrice = bcsub($currentPrice, $reallyShoppingMoney, self::PRICE_PRECISION);
            // 避免价格为负
            if (bccomp($currentPrice, 0, self::PRICE_PRECISION) < 0) {
                $currentPrice = '0.00';
            }
        }


        // ======================== 10. 运费计算 ========================
        if (!$disableAllDiscount) {
            list($shippingStatus, $shippingResult) = $this->calculateFreightAndPrice($currentPrice, $userId, $addressId, $freightOther, $giftCardIds, $cardType, $shippingData);
        } else {
            // 禁用优惠时重新计算运费（不含礼品卡）
            list($shippingStatus, $shippingResult) = $this->calculateFreightAndPrice($currentPrice, $userId, $addressId, [], [], 0, $shippingData);
        }
        if (!$shippingStatus) {
            return [false, $shippingResult];
        }
        $realPrice    = $shippingResult['real_price']; // 含运费的实际价格
        $freightPrice = $shippingResult['fprice'];     // 运费

        // ======================== 11. 礼品卡处理（使用购物金时不可用） ========================
        $giftCardCanUseNumber = 0;                     // 可用礼品卡数量
        $giftCardUseAmount    = 0;                     // 礼品卡使用金额

        if (!$disableAllDiscount) {
            // 获取礼品卡信息（无论是否使用，先获取可用数据）
            list($giftCardStatus, $giftCardData) = $this->applyGiftCardDiscount($userId, $giftCardIds, $currentPrice, $realPrice, $goodsCombinations, $spriceType, $cardType);
            if (!$giftCardStatus) {
                return [false, $giftCardData];
            }

            $giftCardCanUseNumber = $giftCardData['giftCardCanUseNumber'];
            $giftCardUseAmount    = $giftCardData['useAmount'];
            $realPrice            = $giftCardData['real_price'];

            // 使用购物金时，礼品卡不抵扣金额
            if (bccomp($reallyShoppingMoney, 0, self::PRICE_PRECISION) <= 0) {
                $currentPrice = bcsub($currentPrice, $giftCardUseAmount, self::PRICE_PRECISION);
            } else {
                $giftCardUseAmount = 0; // 重置使用金额
            }
        }


        // ======================== 12. 国补处理（使用购物金时不适用） ========================
        // 国补金额（2020/07/08追觅大使身份不可叠加国补）
        $subsidyPrice = 0;

        // 检查是否符合国补条件：未禁用所有折扣、API在补贴范围内、实际购物金额为0、非微信小程序场景
        $isSubsidyEligible = !$disableAllDiscount
                && in_array($params['api'] ?? '', self::SUBSIDY_APIS)
                && bccomp($reallyShoppingMoney, 0, self::PRICE_PRECISION) === 0
                && $is_wxds == 0; // 2020/07/08追觅大使身份不可叠加国补

        if ($isSubsidyEligible) {
            // 应用国补折扣
            list($subsidyStatus, $subsidyMsg, $subsidyPrice) = $this->applySubsidyDiscount($goodsCombinations, $currentPrice);

            // 国补申请失败时返回错误信息
            if (!$subsidyStatus) {
                return [false, $subsidyMsg];
            }
        }
        // 如果使用了国补，则从当前价格中减去国补金额
        $price      = bcsub($currentPrice, $subsidyPrice, self::PRICE_PRECISION);
        $real_price = bcsub($realPrice, $subsidyPrice, self::PRICE_PRECISION);

        // todo 消费金使用
        // ======================== 13. 消费金处理（确认下来可以和购物金一起用） ========================
        list($success, $isUseConsumeMoney, $userConsumeMoney, $reallyConsumeMoney, $errorMsg) = $this->applyConsumeMoney($userId, $params, $buyGoodsMap, $totalPrice, $price, $disableAllDiscount);

        if (!$success) {
            return [false, $errorMsg];
        }

        if ($reallyConsumeMoney > 0) {
            $price = bcsub($price, $reallyConsumeMoney, self::PRICE_PRECISION);
            $real_price = bcsub($real_price, $reallyConsumeMoney, self::PRICE_PRECISION);
            // 避免价格为负
            if (bccomp($price, 0, self::PRICE_PRECISION) < 0) {
                $price = '0.00';
            }
            if (bccomp($real_price, 0, self::PRICE_PRECISION) < 0) {
                $real_price = '0.00';
            }
        }

        // ======================== 14. 积分配置与最终数据整合 ========================
        // 积分比例配置
        $pointRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 10;
        // 用户总积分
        $totalCoin = by::point()->get($userId);

        // 禁用优惠时重置积分信息
        if ($disableAllDiscount) {
            $totalCoin = by::point()->get($userId);
            $canCoin   = 0;
            $coinPrice = 0;
            $coinType  = self::COIN_TYPE_NOT_USE;
        }

        // 整合返回数据
        $resultData['total'] = [
                'tprice'         => $totalPrice,
                'fprice'         => $freightPrice,
                'acdeprice'      => $activityDeductPrice,
                'is_yjhx'        => $isYjhx,
                'coin'           => [
                        'tcoin'      => $totalCoin,
                        'can_coin'   => $canCoin,
                        'coin_price' => $coinPrice,
                        'rate'       => $pointRate,
                        'coin_type'  => $coinType,
                        'coin'       => $coinType == 1 ? $coin : 0
                ],
                'coupon'         => [
                        'coupon_id' => $couponId,
                        'card_type' => $cardType,
                        'cprice'    => $couponPrice,
                ],
                'consume'        => [
                        'consume_id'    => $consumeId,
                        'consume_type'  => by::userCard()::TYPE['consume'],
                        'consume_price' => $consumePrice,
                ],
                'gift_card'      => [
                        'can_number'    => $giftCardCanUseNumber,
                        'use_amount'    => $giftCardUseAmount,
                        'gift_card_ids' => $giftCardIds,
                ],
                'shopping_price' => [
                        'can_use_shopping_price' => $reallyShoppingMoney,
                        'is_use_shopping_price'  => $isUseShoppingPrice,
                        'total_shopping_price'   => $userShoppingMoney,
                ],
                'consume_money' => [    // 消费金
                    'can_use_consume_money' => $reallyConsumeMoney,   // 能使用的消费金
                    'is_use_consume_money'  => $isUseConsumeMoney,    // 是否使用了消费金
                    'total_consume_money'   => $userConsumeMoney,     // 用户消费金总额
                ],
                'price'          => $price,
                'real_price'     => $real_price,
                'subsidy_price'  => $subsidyPrice,
        ];

        // 补充其他返回字段
        $resultData['gcombines']            = $goodsCombinations;
        $resultData['cart_ids']             = json_decode($cartIds, true);
        $resultData['code']                 = $guideCode;
        $resultData['usage_coupon_num']     = $usageCouponNum;
        $resultData['usage_coupon_tag']     = $usageCouponTag;
        $resultData['usage_consume_num']    = $usageConsumeNum;
        $resultData['usage_consume_tag']    = $usageConsumeTag;
        $resultData['disable_all_discount'] = $disableAllDiscount;

        return [true, $resultData];
    }


    /**
     * 处理购物金逻辑
     *
     * @param int $userId 用户ID
     * @param array $params 接口参数
     * @param array $buyGoods 购买的商品信息 ['gid' => '商品名称']
     * @param float $totalPrice 商品总价（元）
     * @param bool $disableAllDiscount 是否禁用所有优惠
     * @return array [$success, $isUseShoppingPrice, $userShoppingMoney, $reallyShoppingMoney, $errorMessage]
     * @throws MyExceptionModel
     */
    private function applyShoppingMoney(int $userId, array $params, array $buyGoods, float $totalPrice, bool $disableAllDiscount): array
    {
        $isUseShoppingPrice  = 0;
        $userShoppingMoney   = 0;
        $reallyShoppingMoney = 0;
        $errorMessage        = '';

        if (!in_array($params['api'] ?? '', self::SHOPPING_PRICE_APIS)) {
            return [true, 0, 0, 0, '']; // 非APP接口，不使用购物金
        }

        $isUseShoppingPrice = (int) ($params['is_use_shopping_price'] ?? 1); // 0:不使用 1:使用
        $userShoppingMoney  = byNew::UserShopMoneyModel()->getInfoByUserId($userId);
        $userShoppingMoney  = bcdiv($userShoppingMoney, 100, self::PRICE_PRECISION); // 分转元

        if ($disableAllDiscount) {
            return [true, 0, $userShoppingMoney, 0, '']; // 禁用优惠，不使用购物金
        }

        if (bccomp($userShoppingMoney, 0, self::PRICE_PRECISION) <= 0 || !$isUseShoppingPrice) {
            return [true, 0, $userShoppingMoney, 0, '']; // 无购物金或用户未勾选使用
        }

        // 半价购活动ID，读取配置文件
        $halfPriceBuyId = CUtil::getConfig('shopping_price_id', 'config', \Yii::$app->id);

        // 获取活动信息以验证时间
        list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($halfPriceBuyId, $userId);

        // 验证活动时间
        $now       = time();
        $startTime = $activityData['base_info']['start_time'] ?? 0;
        $endTime   = $activityData['base_info']['end_time'] ?? 0;
        if (!$activityStatus || $now < $startTime || $now > $endTime) {
            return [true, $isUseShoppingPrice, 0, 0, '']; // 未配置活动/活动已过期
        }

        $module = $activityData['modules'];
        if (count($module) > 2) {
            return [true, $isUseShoppingPrice, 0, 0, '']; // 活动模块异常
        }
        // 支持购物金的商品ID列表
        $supportShoppingPriceGoodsIds = array_column($activityData['modules'][0]['extra']['goods'], 'goods_id');
        $buyGoodsIds                  = array_keys($buyGoods);
        // 抵扣比例
        $percentage = bcsub(100, $activityData['modules'][0]['extra']['shopping_money_rate'] ?? 50);

        $unsupportedGoodsIds = array_diff($buyGoodsIds, $supportShoppingPriceGoodsIds);
        $supportedGoodsIds   = array_intersect($buyGoodsIds, $supportShoppingPriceGoodsIds);

        if (empty($supportedGoodsIds)) {
            return [true, 0, $userShoppingMoney, 0, '']; // 全部不支持，强制不使用
        }

        if (!empty($unsupportedGoodsIds)) {
            $supportedGoods      = array_intersect_key($buyGoods, array_flip($supportedGoodsIds));
            $supportedGoodsNames = [];
            foreach ($supportedGoods as $goods) {
                $supportedGoodsNames[] = $goods ?: '未知商品';
            }
            $nameList = implode('、', $supportedGoodsNames);
            return [false, 0, $userShoppingMoney, 0, "仅以下商品支持使用购物金：{$nameList}"];
        }
        // 正常使用购物金
        $shoppingMoneyRate      = bcdiv($percentage, 100, self::PRICE_PRECISION);
        $maxCanUseShoppingMoney = bcmul($totalPrice, $shoppingMoneyRate, self::PRICE_PRECISION);
        $reallyShoppingMoney    = bccomp($maxCanUseShoppingMoney, $userShoppingMoney, self::PRICE_PRECISION) <= 0
                ? $maxCanUseShoppingMoney
                : $userShoppingMoney;

        return [true, 1, $userShoppingMoney, $reallyShoppingMoney, ''];
    }



    /**
     * 处理国补活动相关逻辑（判断商品是否参与国补、验证购买规则、计算补贴金额）
     *
     * @param array $gcombines 下单商品组合列表
     * @param float $price 商品价格（用于计算补贴金额）
     * @return array [是否成功, 错误信息(成功时为空), 补贴金额]
     */
    public function applySubsidyDiscount(array $gcombines, float $price): array
    {
        // 获取当前生效的国补活动
        $subsidyActivity = byNew::SubsidyActivityModel()->getCurrentActivity();
        $subsidyPrice    = 0;

        // 活动不存在或ID无效时直接返回
        if (!$subsidyActivity) {
            return [true, '', $subsidyPrice];
        }

        $subsidyActivityId = intval($subsidyActivity['id'] ?? 0);

        // 提取所有下单商品ID
        $orderedGoodsIds = array_column($gcombines, 'gid');

        $hasSubsidyGoods = false;
        // 检查是否包含国补商品
        foreach ($orderedGoodsIds as $goodsId) {
            if (byNew::SubsidyActivityGoodsModel()->isGoodsInSubsidy($goodsId, $subsidyActivityId)) {
                $hasSubsidyGoods = true;
            }
        }
        // 包含国补商品时验证购买规则并计算补贴
        if ($hasSubsidyGoods) {
            // 验证购买数量限制
            if (count($orderedGoodsIds) > 1) {
                return [false, '国补商品只能单独购买哟！', $subsidyPrice];
            }

            // 计算补贴金额
            $subsidyPrice = byNew::SubsidyActivityGoodsModel()
                    ->getGoodsSubsidyAmount($orderedGoodsIds[0], $price);
        }

        return [true, '', $subsidyPrice];
    }

    /**
     * @param $user_id
     * @param $gcombines
     * @param $getChannel
     * @param $spriceType
     * @param $coupon_id
     * @param $consume_id
     * @return array
     * @throws Exception
     * 使用优惠券/消费券
     * 用户ID，商品组合，优惠券来源，价格类型，优惠券ID，消费券ID
     */
    public function applyCouponDiscount($user_id, $gcombines, $getChannel = 0, $spriceType = 0, $coupon_id = 0, $consume_id = 0): array
    {
        $bestCouple = $this->GetBestCouple($user_id, $gcombines, $getChannel, $spriceType, $coupon_id, $consume_id);

        $discount_price    = $bestCouple['discount_price'];
        $coupon_id         = $bestCouple['id'];
        $cprice            = $bestCouple['cprice'];
        $consume_id        = $bestCouple['consume_id'];
        $consume_price     = $bestCouple['consume_price'];
        $usage_coupon_tag  = $bestCouple['usage_coupon_tag'];
        $usage_consume_tag = $bestCouple['usage_consume_tag'];

        // 获取可使用的优惠券条数
        $usage_consume_num = $consume_id > 0 ? 1 : 0;
        $usage_coupon_num  = $coupon_id > 0 ? 1 : 0;

        if ($usage_consume_tag == 3) {
            // 查找可用的消费券
            list($status, $result) = by::userCard()->canUseCard($user_id, by::userCard()::TYPE['consume'], $gcombines, 0, $getChannel, $spriceType);
            $consume_ids       = $result['consume']['ids'] ?? [];
            $usage_consume_num = count($consume_ids);
        }

        if ($usage_coupon_tag == 3) {
            // 查找可用的优惠券
            list($status, $result) = by::userCard()->canUseCard($user_id, by::userCard()::TYPE['all'], $gcombines, 0, $getChannel, $spriceType);
            $ids              = $result['select']['ids'] ?? [];
            $usage_coupon_num = count($ids);
        }

        $card_type = 0;
        $user_card_info = [];
        if ($coupon_id > 0) { // 优惠券使用
            $user_card_info = by::userCard()->getCardById($user_id, $coupon_id, $getChannel);
            $card_type      = $user_card_info['type'] ?? 0;
        }

        // 返回数组
        return [
                'discount_price'    => $discount_price,    // 优惠后的价格（总优惠金额）
                'usage_coupon_tag'  => $usage_coupon_tag,  // 优惠券的使用标识（是否使用优惠券）
                'usage_consume_tag' => $usage_consume_tag, // 消费券的使用标识（是否使用消费券）
                'card_type'         => $card_type,         // 卡券类型（优惠券、消费券等）
                'coupon_id'         => $coupon_id,         // 优惠券ID（用户选择的优惠券）
                'consume_id'        => $consume_id,        // 消费券ID（用户选择的消费券）
                'usage_coupon_num'  => $usage_coupon_num,  // 可使用的优惠券数量
                'usage_consume_num' => $usage_consume_num, // 可使用的消费券数量
                'cprice'            => $cprice,            // 优惠券具体优惠金额
                'consume_price'     => $consume_price,     // 消费券具体优惠金额
                'user_card_info'    => $user_card_info,    // 用户卡券信息
        ];
    }


    /**
     * @param $user_id
     * @param $tprice
     * @param $discount_price
     * @param $acdeprice
     * @param $than_price
     * @param $coin
     * @param $coin_type
     * @return array
     * @throws Exception
     * 使用积分
     * 用户ID，总价格，优惠券折扣金额，活动抵扣金额，订单最少付款额，积分数量，积分使用类型
     */
    protected function applyPointDiscount($user_id, $tprice, $discount_price, $acdeprice, $than_price, $coin, $coin_type): array
    {
        // 积分权益
        $pointRate = by::memberCenterModel()->getPointCrash($user_id);
        $mPoint    = by::point();
        $tcoin     = $mPoint->get($user_id); // 总积分
        // 计算初始折后价格，确保不小于0
        $price = max(0, bcsub(bcsub($tprice, $discount_price, 2), $acdeprice, 2));

        // 计算可用积分对应的金额
        $can_price = bccomp($price, $than_price, 2) == 1 ? bcsub($price, $than_price, 2) : 0;
        $can_coin  = min($mPoint->convert($can_price, 'POINT',$user_id), $tcoin);

        // 如果积分使用类型为0（默认），则不使用积分
        $can_coin = $this->__canCoinByBenefit($can_coin, $price, $pointRate, $user_id);
        // 积分使用超限校验
        if ($coin > 0 && $coin_type == 1 && $coin > $can_coin) {
            return [false, '积分使用超限'];
        }

        // 计算最终积分折扣金额和调整后价格
        $coin_price = ($coin_type == 1 && $coin > 0) ? $mPoint->convert($coin,'RMB',$user_id) : $mPoint->convert($can_coin,'RMB',$user_id);
        $price      = ($coin_type == 1 && $coin > 0) ? bcsub($price, $coin_price, 2) : $price;

        // 返回最终折后价格及相关信息
        return [
                true, [
                        'price'      => $price,      // 应用积分后的价格
                        'can_coin'   => $can_coin,   // 可使用的最大积分数量
                        'coin_price' => $coin_price, // 积分折扣金额
                ]
        ];
    }




    /**
     * 计算运费和最终价格
     */
    protected function calculateFreightAndPrice($price, $user_id, $aid, $freight_other, $giftCardIds, $card_type,$shippingData): array
    {
        $fprice = $this->shippingPrice($shippingData, $user_id, $aid, $freight_other, $price);

        // 计算最终价格（商品价格 + 运费）
        $real_price = bcadd($price, $fprice, 2);

        // 如果没有足够的余额支付（且不是兑换券），或者网络繁忙，返回错误信息
        if (empty($giftCardIds) && bccomp($real_price, 0, 2) < 0 && $card_type != by::userCard()::TYPE['voucher']) {
            return [false, '网络繁忙~~']; // 返回网络繁忙错误
        }

        // 返回运费和最终价格
        return [true, [
                'fprice'     => $fprice,                      // 运费
                'real_price' => $real_price,                  // 最终价格
        ]];
    }


    /**
     * @param $user_id
     * @param $giftCardIds
     * @param $price
     * @param $real_price
     * @param $gcombines
     * @param $spriceType
     * @param $card_type
     * @return array
     * @throws Exception
     * @throws RedisException
     * 应用礼品卡折扣
     * 用户ID，礼品卡ID，价格，实际价格，商品组合，价格类型，卡券类型
     */
    protected function applyGiftCardDiscount($user_id,$giftCardIds, $price, $real_price, $gcombines, $spriceType, $card_type): array
    {
        $useAmount = 0;
        // 检查并计算礼品卡的抵扣金额
        if (array_filter($giftCardIds)) {
            foreach ($giftCardIds as $val) {
                $giftCardInfo = byNew::GiftUserCards()->userGiftCardInfo($val);

                // 如果卡不存在则跳过
                if (empty($giftCardInfo)) {
                    continue;
                }

                // 检查礼品卡状态和过期时间
                if ($giftCardInfo['expire_time'] < time() || in_array($giftCardInfo['status'], [
                                GiftUserCardsModel::STATUS['FROZEN'],
                                GiftUserCardsModel::STATUS['SHARED'],
                                GiftUserCardsModel::STATUS['USED']
                        ])) {
                    return [false, '礼品卡已过期或已使用，请检查'];
                }

                // 将金额转换为元
                $giftCardInfo['amount'] = bcdiv($giftCardInfo['amount'], 100, 2);

                if ($giftCardInfo['type'] == 2) {
                    // 校验使用规则
                    if (count($giftCardIds) > 1) {
                        return [false, '指定兑换卡只可以单独使用'];
                    }
                    if (count($gcombines) > 1 || array_sum(array_column($gcombines, 'num')) > 1) {
                        return [false, '指定兑换卡只可以购买单件商品时使用'];
                    }

                    // 获取商品ID
                    $goodsId = (count($gcombines) == 1) ? implode(",", array_column($gcombines, 'gid')) : '';

                    // 校验抵扣卡适用商品
                    $cardInfo  = byNew::GiftCard()->getCardInfo($giftCardInfo['card_id']);
                    $goods_ids = explode(",", $cardInfo['goods_ids']);
                    if (!in_array($goodsId, $goods_ids)) {
                        return [false, '指定兑换卡不适用此商品'];
                    }
                    $sid = (count($gcombines) == 1) ? implode(",", array_column($gcombines, 'sid')) : '';
                    if ($cardInfo['sku'] != 'all'){
                        if ($sid != $cardInfo['sku']){
                            return [false, '指定兑换卡不适用此商品规格'];
                        }
                    }


                    // 计算抵扣金额
                    $gInfo     = by::Gmain()->GetOneByGidSid($gcombines[0]['gid'] ?? 0, $gcombines[0]['sid'] ?? 0, true, true, $spriceType);
                    $useAmount = $gInfo['spec']['price'] ?? $gInfo['price'] ?? 0;
                } else {
                    $useAmount = bcadd($useAmount, $giftCardInfo['amount'] ?? 0, 2);
                }
            }

            $useAmount  = min($price, $useAmount);
            $real_price = sprintf("%.2f", max(0, bcsub($real_price, $useAmount, 2)));
        }

        // 获取可用礼品卡数量
        list($s, $ret) = GiftCardService::getInstance()->getUserCardList($user_id, 1, array_column($gcombines, 'gid'));
        $giftCardList         = $ret['list'] ?? [];
        $giftCardCanUseNumber = ($card_type == by::userCard()::TYPE['voucher']) ? 0 : 0;

        foreach ($giftCardList as $item) {
            if ($item['back'] == 1) {
                $giftCardCanUseNumber = bcadd($giftCardCanUseNumber, 1);
            }
        }

        return [true, [
                'real_price'           => $real_price,           // 实际支付金额
                'useAmount'            => $useAmount,            // 礼品卡抵扣金额
                'giftCardCanUseNumber' => $giftCardCanUseNumber, // 可用礼品卡数量
        ]];
    }


    public function GetBestCouple($user_id, $gcombines, $getChannel,$spriceType, $coupon_id, $consume_id)
    {
        // todo ---------消费券和优惠券的使用-------
        $consume_price = $cprice = $discount_price = 0;
        $coupon_tag = intval($coupon_id); // 优惠券是否使用标识
        $consume_tag = intval($consume_id); // 消费券是否使用标识
        $ret = ['id' => 0, 'consume_id'=> 0,'source_price' => 0, 'discount_price' => 0, 'cprice'=>0, 'consume_price'=> 0, 'real_price' => 0, 'type'=>0, 'consume_type'=>0];

        if ($consume_tag >= 0 && $coupon_tag == -1) { // 选择消费券
            list($stc, $ret) = self::handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, by::userCard()::TYPE['consume'], $consume_id);
            $usage_consume_tag = self::USAGE_OPTION['USAGE_SELECTED'];
            $usage_coupon_tag  = self::USAGE_OPTION['USAGE_NOT_USED'];
        } elseif ($consume_tag == -1 && $coupon_tag >= 0) { // 选择优惠券
            list($stc,$ret) = self::handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, by::userCard()::TYPE['all'], $coupon_id);
            $usage_consume_tag = self::USAGE_OPTION['USAGE_NOT_USED'];
            $usage_coupon_tag = self::USAGE_OPTION['USAGE_SELECTED'];
        } elseif ($coupon_tag >= 0 && $consume_tag >= 0) { // 优惠券和消费券都选择
            // 优惠券
            if($consume_tag == 0 && $coupon_tag == 0){
                $usage_consume_tag = $usage_coupon_tag = self::USAGE_OPTION['USAGE_OPTIMAL'];
            }else{
                $usage_consume_tag = $usage_coupon_tag = self::USAGE_OPTION['USAGE_SELECTED'];
            }
            list($stc,$ret2) = self::handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, by::userCard()::TYPE['all'], $coupon_id);
            $sub_price = 0;
            $source_price = $ret2['source_price'] ?? 0;
            if ($stc){
                $cprice += $ret2['cprice'] ?? 0;
                $sub_price += $cprice;
            }
            if (($ret2['type'] ?? -1) != by::userCard()::TYPE['voucher']) {//不是兑换券
                list($stc,$ret1) = self::handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, by::userCard()::TYPE['consume'], $consume_id, $sub_price);
                if ($stc ) { // 优惠券和消费券都可用
                    $consume_price += $ret1['consume_price'] ?? 0;
                }
                if(bcsub($source_price, $sub_price <0) <  $consume_price){ //如果算出负的，则消费券不可用
                    $consume_price = 0;
                    $ret1['consume_id'] = -1; // 消费券不可用
                }
            }else{ // 消费券不可用
                $ret1['consume_id'] = -1;
            }
            $ret = [
                'id'             => $ret2['id'] ?? 0,
                'consume_id'     => $ret1['consume_id'] ?? 0,
                'source_price'   => $ret2['source_price'] ?? 0,
                'cprice'         => $cprice,
                'consume_price'  => $consume_price,
                'discount_price' => bcadd($cprice,$consume_price,2)
            ];
        } else { // 不使用
            // 获取商品原价
            list($stc, $ret0) = self::handleCardUsage($user_id, $gcombines, $getChannel, $spriceType, by::userCard()::TYPE['all'], $coupon_id);
            if($stc){
                $ret['source_price'] = $ret0['source_price'] ?? 0;
            }
            $usage_consume_tag = $usage_coupon_tag = self::USAGE_OPTION['USAGE_NOT_USED'];
        }
        $source_price = $ret['source_price'] ?? 0;
        $real_price = bcsub($source_price, $ret['discount_price'], 2);


        $returnData  = [
            'id'                => $ret['id'] ?? 0,
            'discount_price'    => $ret['discount_price'],
            'coupon_id'         => $ret['id'],
            'cprice'            => $ret['cprice'],
            'consume_id'        => $ret['consume_id'],
            'consume_price'     => $ret['consume_price'],
            'usage_coupon_tag'  => $usage_coupon_tag,
            'usage_consume_tag' => $usage_consume_tag,
            'source_price'      => $source_price,
            'real_price'        => $real_price,
        ];

        if ($coupon_tag == -1) {
            $returnData['coupon_id'] = $returnData['id'] = -1;
        }
        if ($consume_tag == -1) {
            $returnData['consume_id'] = -1;
        }

        return $returnData;
    }


    /**
     * @param $canCoin
     * @param $price
     * @param $rate
     * @param $userId
     * @return mixed
     * 获取用户实际可使用积分
     */
    public function __canCoinByBenefit($canCoin, $price, $rate, $userId)
    {
        // 校验活动时间，若符合条件则设置最低比率为25
        if (ActivityConfigEnum::judgeActivityTime($userId, 'POINT_DEDUCTION')) {
            $rate = max($rate, ActivityConfigEnum::POINT_DEDUCTION_MULTIPLE);
        }
        // 积分中心返回比例  v1:10 v2:20 v3:30 诸如此类...
        $memberRate = bcdiv($rate, 100, 2); // 将比例转换为小数形式
        // 商城后台配置抵扣系数
        $deductionRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
        
        // 计算实际可使用的积分比例
        $rate = bcmul($memberRate, $deductionRate);
        // 1-100 积分抵扣比例  原本是100  现在改为10  原本逻辑为 100 抵1块钱  现在 10抵1块钱 原本概率为 30
        // 计算允许的最大可使用积分额
        $deductionAmount = CUtil::uint(floatval($price) * $rate);

        // 返回可用的最小抵扣积分值
        return min($canCoin, $deductionAmount);
    }


    public function GetGoodsGiniId($gInfo, $gcombine)
    {
        if (empty($gcombine))
            return $gcombine;
        $gini_id = CUtil::uint($gInfo['gini_id'] ?? 0);
        $sid     = $gcombine['sid'] ?? 0;
        if ($sid > 0 && isset($gInfo['spec']) && $gInfo['spec']) {
            $spec    = $gInfo['spec'];
            $gini_id = CUtil::uint($spec['gini_id'] ?? 0);
        }
        $gcombine['gini_id'] = $gini_id;
        return $gcombine;
    }


    public function CheckGoodsIniPrice($gInfo, $gcombine)
    {
        $gini_id = CUtil::uint($gcombine['gini_id'] ?? 0);
        $sid     = $gcombine['sid'] ?? 0;

        $isIni      = CUtil::uint($gInfo['is_ini'] ?? 0);
        $gIniRealId = CUtil::uint($gInfo['gini_id'] ?? 0);

        if ($sid > 0 && isset($gInfo['spec']) && $gInfo['spec']) {
            $spec        = $gInfo['spec'];
            $isIniS      = CUtil::uint($spec['is_ini'] ?? 0);
            $gIniRealIdS = CUtil::uint($spec['gini_id'] ?? 0);
            if ($gini_id != $gIniRealIdS) {
                return [false, '“' . ($gInfo['name'] ?? '') . '”-' . '商品信息发生变动，请返回重新进入~~！'];
            }
        } else {
            if ($gini_id != $gIniRealId) {
                return [false, '“' . ($gInfo['name'] ?? '') . '”-' . '商品信息发生变动，请返回重新进入~！'];
            }
        }
        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param int $level
     * @param array $o_info
     * @param bool $cache
     * @return array
     * @throws Exception
     * 同步erp订单
     */
    public function ErpAddOrder($user_id, $order_no, $level = 0, $o_info = [], $cache = true)
    {
        if (empty($o_info)) {
            $o_info = by::Ouser()->CommPackageInfo($user_id, $order_no, false, false, true, false, $cache);
        }

        if (empty($o_info)) {
            return [false, '数据不存在'];
        }

        //同步订单给erp -- 三次重试
        for ($i = 0; $i < 3; $i++) {
            //            list($s, $m) = ErpNew::factory()->addOrder($o_info);
            list($s, $m) = Erp::factory()->addOrder($o_info);

            if ($s) {
                break;
            }
            sleep(1);
        }

        //同步失败丢入队列再一次执行
        if (!$s) {
            CUtil::debug("{$o_info['user_id']}|{$o_info['order_no']} 同步到erp失败-{$level}", 'err.erp');

            $r_key = AppCRedisKeys::erpAddOrder();
            $r_val = CUtil::getAllParams($o_info['user_id'], $o_info['order_no'], $level);
            by::redis()->rPush($r_key, $r_val);

            return [false, '同步失败'];
        }

        return [true, 'ok'];
    }

    /**
     * @throws \yii\db\Exception
     * 定时脚本-同步未成功的订单
     */
    public function ShellAddOrder()
    {
        $redis = by::redis();
        $r_key = AppCRedisKeys::erpAddOrder();
        $len   = (int)$redis->lLen($r_key);

        for ($i = 1; $i <= $len; $i++) {

            $one = $redis->lPop($r_key);
            if (empty($one)) {
                break;
            }

            list($user_id, $order_no, $level) = explode('|', $one, 3);
            if (empty($user_id) || empty($order_no)) {
                continue;
            }

            $this->ErpAddOrder($user_id, $order_no, ++$level);
        }
    }

    /**
     * 更换账号重新订阅任务，只执行一次
     * @return void
     * @throws Exception
     */
    public function pushOrderToNewAccount()
    {
        $db     = by::dbMaster();
        $erp    = Erp::factory();
        $mOmain = by::Omain();
        $mOad   = by::Oad();
        //分表可能跨年
        $this_year = date("Y");
        $years     = [$this_year, date("Y", strtotime("-30 days"))];
        $years     = array_unique($years);

        $id = 0;
        //        $ctime         = YII_ENV_PROD ? strtotime("-12 hours") : strtotime("-1 minute");
        $ctime  = YII_ENV_PROD ? time() : time(); //体验版测试
        $where  = " `id` > :id AND `ctime` < {$ctime}";
        $params = [];

        list($w, $p) = $mOmain->GetOrderStatus($mOmain::ORDER_STATUS['WAIT_RECEIVE'], true);
        if ($w) {
            $where  .= " AND {$w}";
            $params = array_merge($params, $p);
        }

        foreach ($years as $year) {
            //确定分表
            $date    = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime   = strtotime($date);
            $tb_main = by::Omain()::tbName($ctime);

            $sql = "SELECT `id`,`user_id`,`order_no`,`status` FROM {$tb_main} WHERE {$where} ORDER BY `id` LIMIT 100";

            while (true) {
                $params['id'] = $id;

                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id  = $end['id'];

                foreach ($list as $val) {

                    $this->__pushToKD100($val['order_no'], $val, $erp, $mOmain, $mOad, $db);

                }
            }
        }
        echo '订阅完毕';
        exit();
    }

    private function __pushToKD100($order_no, $arr, $erp = null, $mOmain = null, $mOad = null, $db = null)
    {
        $aInfo = by::Oad()->GetOneByOrderNo($order_no);
        if (empty($aInfo['mail_no'])) {
            return [false, '暂未发货'];
        }
        $aInfo['express_code'] = ExpressTen::factory()->getKD100ExpressCode($aInfo['express_code']) ?? '';
        //todo 加入快递100订阅
        for ($i = 0; $i < 3; $i++) {
            list($s, $m) = ExpressTen::factory()->poll($aInfo['mail_no'], $aInfo['express_code'], $order_no, $aInfo['phone'] ?? '');
            if ($s) {
                break;
            }
            sleep(1);
        }
        return [true, $aInfo['mail_no']];

    }


    /**
     * @throws \yii\db\Exception
     * 定时脚本-获取订单信息
     */
    public function ShellDetailOrder()
    {
        $db     = by::dbMaster();
        $erp    = Erp::factory();
        $mOmain = by::Omain();
        $mOad   = by::Oad();
        //分表可能跨年
        $this_year = date("Y");
        $years     = [$this_year, date("Y", strtotime("-30 days"))];
        $years     = array_unique($years);

        $id = 0;
        //        $ctime         = YII_ENV_PROD ? strtotime("-12 hours") : strtotime("-1 minute");
        $ctime  = YII_ENV_PROD ? strtotime("-10 minute") : strtotime("-1 minute"); //体验版测试
        $where  = " `id` > :id AND `ctime` < {$ctime}";
        $params = [];

        list($w, $p) = $mOmain->GetOrderStatus($mOmain::ORDER_STATUS['WAIT_SEND'], true);
        if ($w) {
            $where  .= " AND {$w}";
            $params = array_merge($params, $p);
        }

        foreach ($years as $year) {
            //确定分表
            $date    = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime   = strtotime($date);
            $tb_main = by::Omain()::tbName($ctime);

            $sql = "SELECT `id`,`user_id`,`order_no`,`status` FROM {$tb_main} WHERE {$where} ORDER BY `id` LIMIT 100";

            while (true) {
                $params['id'] = $id;

                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id  = $end['id'];

                foreach ($list as $val) {

                    $this->__shellDetailOrder($val['order_no'], $val, $erp, $mOmain, $mOad, $db);


                }
            }
        }
    }

    /**
     * @param $order_no
     * @param $arr
     * @param null $erp
     * @param null $mOmain
     * @param null $mOad
     * @param null $db
     * @return array
     * @throws Exception
     * OMS拉取某一个订单
     */
    private function __shellDetailOrder($order_no, $arr, $erp = null, $mOmain = null, $mOad = null, $db = null)
    {
        $erp = $erp == null ? Erp::factory() : $erp;

        list($s, $ret) = $erp->getOrderList($order_no);
        if (!$s || empty($ret)) {
            return [-1, 'erp查询失败'];
        }

        $shipping_status  = $ret['shipping_status'] ?? 0;
        $shipping_code    = $ret['shipping_code'] ?? '';
        $shipping_sn      = $ret['shipping_sn'] ?? '';
        $shipping_name    = $ret['shipping_name'] ?? '';
        $shipping_time_fh = $ret['shipping_time_fh'] ?? 0; //发货时间
        $receiver_mobile  = $ret['receiver_mobile'] ?? '';//收货人电话

        if ($shipping_status < 6 || empty($shipping_sn)) {
            return [10, $shipping_status];
        }

        $shipping_time_fh = $shipping_time_fh == 0 ? time() : strtotime($shipping_time_fh);

        $status  = $arr['status'];
        $user_id = $arr['user_id'];

        $mOmain = $mOmain == null ? by::Omain() : $mOmain;
        $mOad   = $mOad == null ? by::Oad() : $mOad;
        $db     = $db == null ? by::dbMaster() : $db;

        $trans = $db->beginTransaction();

        try {
            //更改订单状态
            $next_st = $mOmain->SetOrderStatus($status, $mOmain::ORDER_STATUS['WAIT_RECEIVE']);
            list($s, $m) = $mOmain->SyncInfo($user_id, $order_no, $next_st, ['stime' => $shipping_time_fh], ['stime' => $shipping_time_fh]);
            if (!$s) {
                throw new \Exception($m);
            }

            $save = [
                'mail_no'      => $shipping_sn,
                'express_code' => $shipping_code,
                'express_name' => $shipping_name,
            ];
            $mOad->UpdateData($order_no, $save);

            $trans->commit();
            // //todo 订单同步crm
            // Crm::factory()->push($user_id, 'order', ['user_id' => $user_id, 'order_no' => $order_no]);
            // Crm::factory()->push($user_id, 'orderLine', ['user_id' => $user_id, 'order_no' => $order_no]);

            $shipping_code = ExpressTen::factory()->getKD100ExpressCode($shipping_code);

            //todo 加入快递100订阅
            for ($i = 0; $i < 3; $i++) {
                list($s, $m) = ExpressTen::factory()->poll($shipping_sn, $shipping_code, $order_no, $receiver_mobile);
                if ($s) {
                    break;
                }
                sleep(1);
            }

            return [1, $shipping_status];

        }
        catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.detail.order');

            return [-10, $shipping_status];
        }
    }

    /**
     * @param $user_id
     * @param $type //0 累计，1 注册30天，2 今年
     * @return false|string|null
     * @throws Exception
     * 获取用户累计付款金额
     */
    public function getOrderAmount($user_id, $type = 0)
    {
        $r_key = $this->__getOuserListKey($user_id);
        $h_key = CUtil::getAllParams(__FUNCTION__, $type);
        $redis = by::redis();
        $sum   = $redis->hGet($r_key, $h_key);
        if ($sum === false) {
            $tb     = self::tbName($user_id);
            $status = OmainModel::ORDER_STATUS;
            unset($status['WAIT_PAY'], $status['CANCELED']);
            $status = implode(',', $status);
            $sql    = "select sum(`price`) from {$tb} where `user_id`={$user_id} and `status` in ({$status})";
            if ($type == 1) {
                $userMain = by::users()->getUserMainInfo($user_id);
                $reg_time = $userMain['reg_time'] ?? 0;
                $e_time   = $reg_time + 30 * 86400;
                $sql      .= " and pay_time between {$reg_time} and {$e_time}";
            } elseif ($type == 2) {
                $time = mktime(0, 0, 0, 1, 1, date('Y'));
                $sql  .= " and pay_time >= {$time}";
            }
            $sum = by::dbMaster()->createCommand($sql)->queryScalar();
            $sum = $sum ?: 0;
            $redis->hSet($r_key, $h_key, $sum);
            CUtil::ResetExpire($r_key, 1800);
        }

        return floatval($sum);
    }


    /**
     * 获取未同步crm的订单使用的积分
     */
    public function getUsePoint($user_id)
    {
        $user_id = CUtil::uint($user_id);
        $r_key   = $this->__getPointByOrder($user_id);
        $point   = by::redis()->get($r_key);
        if ($point === false) {
            $tb = $this->tbName($user_id);

            $sql = "SELECT `id`,`user_id`,`order_no`,`coin` FROM {$tb} WHERE `user_id` = :user_id  AND  `coin` >0 AND `status` != 100 AND `syn_crm` <> 1 and `syn_iot` <> 1";

            $list = by::dbMaster()->createCommand($sql, ['user_id' => $user_id])->queryAll();

            $point = 0;
            foreach ($list as $order) {
                $point = bcadd($point, $order['coin']);
            }

            by::redis()->set($r_key, $point, 3600);
        }
        return CUtil::uint($point);
    }

    /**
     * 修改订单的crm同步状态
     */
    public function upCrm($user_id, $order_no): array
    {
        $user_id = CUtil::uint($user_id);
        $tb      = $this->tbName($user_id);

        by::dbMaster()->createCommand()->update(
            $tb,
            ['syn_crm' => 1],
            ['order_no' => $order_no, 'user_id' => $user_id]
        )->execute();


        $this->DelPointCache($user_id);

        return [true, 'ok'];
    }
    
    /**
     * 修改订单的iot同步状态
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     */
    public function upIot($user_id, $order_no): array
    {
        $user_id = CUtil::uint($user_id);
        $tb      = $this->tbName($user_id);
        
        by::dbMaster()->createCommand()->update(
            $tb,
            ['syn_iot' => 1],
            ['order_no' => $order_no, 'user_id' => $user_id]
        )->execute();
        
        
        $this->DelPointCache($user_id);
        
        return [true, 'ok'];
    }


    /**
     * 改造：会累计越来越多的订单
     * @return array|void
     * @throws Exception
     */
    public function finishOrderReward()
    {
        $db          = by::dbMaster();
        $days        = self::EXPIRE_DAY;
        $expire_time = YII_ENV_PROD ? strtotime("-{$days} days") : strtotime("-1 minutes");
        $userNumIds  = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        $status      = by::Omain()::ORDER_STATUS['FINISHED'];
        foreach ($userNumIds as $userNumId) {
            $id        = 0;
            $tb_o_user = self::tbName($userNumId);
            $fields    = implode("`,`", $this->tb_fields);
            $sql       = "SELECT `{$fields}` FROM {$tb_o_user} WHERE `id` > :id AND `syn_iot_reward` = 0 
                            AND `status` in ({$status}) AND `finish_time` < {$expire_time} ORDER BY `id` limit 500";

            while (1) {
                $list = $db->createCommand($sql, [':id' => $id])->queryAll();
                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id  = $end['id'];


                foreach ($list as $info) {
                    if (empty($info)) {
                        continue;
                    }
//                    $trans = $db->beginTransaction();
                    try {
                        //更新订单是否已发放
                        $res = $db->createCommand()->update(
                            $tb_o_user,
                            ['syn_iot_reward' => 1],//除了普通订单其他订单实际上没给，状态更新了，但是奖励没有发放 在任务中处理的
                            ['order_no' => $info['order_no'], 'user_id' => $info['user_id']]
                        )->execute();

                        if ($res <= 0) {
                            throw new \Exception("发放订单完成奖励没有发放!");
                        }

                        EventMsg::factory()->run('buyGoods', ['user_id' => $info['user_id'], 'order_no' => $info['order_no']]);//发放订单完成奖励
                        
                        EventMsg::factory()->run('buyGoodsMoney', ['user_id' => $info['user_id'], 'order_no' => $info['order_no']]);//发放订单完成消费金

                        if (!empty($info['group_purchase_id'])) {
                            // 参与团购活动获得消费金奖励
                            EventMsg::factory()->run('addGroupBuy', ['user_id' => $info['user_id'], 'order_no' => $info['order_no']]);//参与团购活动获得消费金奖励
                        }

                        // 发放佣金
                        $commissionInfo = byNew::SalesCommissionModel()->getInfo($info['order_no']);
                        if ($commissionInfo){
                            if ($commissionInfo['status'] == 500){
                                $avt = YII_ENV_PROD ? 32 : 170;
                                if ($commissionInfo['activity_id'] == 1){
                                    // 合伙人入提现表
                                    byNew::EmployeeStatisticsModel()->patch($commissionInfo['referrer'],'balance',$commissionInfo['commission'],'+');
                                }else if ($commissionInfo['activity_id'] == $avt){
                                    // 追觅小店入消费金表
                                    \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $commissionInfo['referrer'], 'money_type' => 'add', 'type' => 2, 'money' => $commissionInfo['commission'], 'extend' => $info['user_id'], 'remark' => '追觅小店分享佣金']));
                                }
                            }
                        }

//                        $trans->commit();

                        $this->delInfoCache($info['user_id'], $info['order_no']);
                    }
                    catch (\Exception $e) {
                        CUtil::debug(json_encode($info) . "|" . $e->getMessage(), 'order-finish-reward-err');
//                        $trans->rollBack();
                        return [false, $e->getMessage()];
                    }
                }

            }
        }

        exit('同步订单奖励完成！');
    }


    /**
     * @param $user_id
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 获取已完成的订单
     */
    public function getOrderListByUid($user_id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOrderListByUid($user_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = self::tbName($user_id);
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE user_id =:user_id AND `status` =:status";
            $aData  = by::dbMaster()->createCommand($sql, [':user_id' => $user_id, ':status' => 500])->queryAll();

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * @throws RedisException
     */
    public function __delGetOrderListByUidKey($user_id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOrderListByUid($user_id);
        $redis->del($redis_key);
    }

    /**
     * 根据用户ID和订单号获取用户订单信息（无缓存）
     * @param array $userIds
     * @param array $orderNos
     * @return array
     * @throws Exception
     */
    public function getListByUserIdsAndOrderNos(array $userIds, array $orderNos): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $this->tb_fields);
        // 订单号
        $orderNos = implode("','", $orderNos);
        // 分组查询
        $groupUserIds = $this->groupUserId($userIds, 10);
        foreach ($groupUserIds as $index => $ids) {
            $tb = $this->tbName($index);
            // 查询条件
            $ids = implode(',', $ids);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `user_id` IN ({$ids}) AND `order_no` IN ('{$orderNos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 根据条件或订单信息
     * @param int $user_id
     * @param array $conditions
     * @param array $fields
     * @param string $orderBy
     * @return array
     * @throws Exception
     */
    public function getOrders(int $user_id, array $conditions, array $fields, string $orderBy = 'id DESC'): array
    {
        $tableName = self::tbName($user_id);

        // 动态构建 SELECT 字段
        $selectFields = implode(', ', array_map(function ($field) {
            return "`$field`";
        }, $fields));

        // 初始化 WHERE 子句和参数
        $whereClauses = [];
        $params = [];

        // 动态构建 WHERE 子句和参数绑定
        foreach ($conditions as $key => $value) {
            if (is_array($value)) {
                // 假设数组的第一个元素是操作符，第二个元素是值
                $operator = $value[0];
                $val = $value[1];
                $whereClauses[] = "`$key` $operator :$key";
                $params[":$key"] = $val;
            } else {
                $whereClauses[] = "`$key` = :$key";
                $params[":$key"] = $value;
            }
        }
        $whereClause = implode(' AND ', $whereClauses);

        // 构建 SQL 语句
        $sql = "SELECT {$selectFields} FROM {$tableName} WHERE $whereClause ORDER BY {$orderBy}";

        // 执行查询
        $data = by::dbMaster()->createCommand($sql, $params)->queryAll();

        // 如果只选择了一个字段，返回该字段的一维数组；否则返回完整记录
        if (count($fields) === 1) {
            return array_column($data, $fields[0]);
        } else {
            return $data;
        }
    }

    public function getOrderByStatusAndFinish($index, $status, array $finish_time)
    {
        $tableName = self::tbName($index);
        // finish_time在指定的时间范围内
        $sql = "SELECT `order_no` FROM {$tableName} WHERE `status` = :status AND `finish_time` BETWEEN :start AND :end";
        $params = [':status' => $status, ':start' => $finish_time['start'], ':end' => $finish_time['end']];
        $items = by::dbMaster()->createCommand($sql, $params)->queryAll();
        return array_column($items, 'order_no');
    }

    /**
     * 用户ID分组
     * @param array $userIds
     * @param int $mod
     * @return array
     */
    private function groupUserId(array $userIds, int $mod): array
    {
        $data = [];
        foreach ($userIds as $userId) {
            // 取模
            $index          = intval($userId) % $mod;
            $data[$index][] = $userId;
        }
        return $data;
    }

    /**
     *  用户权益邮费
     * @param $user_id
     * @param $price
     * @param $aid
     * @param $freight_other
     * @return int|mixed
     */
    public function getUserFprice($user_id, $price, $aid, $freight_other)
    {
        // 获取用户的免运费权益
        $shippingPrice = by::memberCenterModel()->getUserRightList($user_id, 'free_shipping');

        // 计算基础运费价格
        $otprice = by::Gtype0()->totalFee($price);

        // 如果用户有免运费权益，并且运费达到免运费阈值，则免运费
        if ($shippingPrice && $otprice >= ($shippingPrice * 100)) {
            $fprice = 0; // 免运费
        } else {
            // 如果没有免运费权益，则获取实际运费
            $fprice = by::model('OfreightModel', 'goods')->GetFreight($user_id, $aid, $otprice, $freight_other??[]);
            $fprice = by::Gtype0()->totalFee($fprice, 1); // 确保精度
        }
        return $fprice;
    }

    /**
     * 预售计算运费
     * @param $shippingData
     * @param $user_id
     * @param $price
     * @return int
     * @throws Exception
     */
    public function getPreSaleFprice($shippingData,$cid, $user_id,  $price): int
    {
        $shippingPrice = by::memberCenterModel()->getUserRightList($user_id, 'free_shipping');
        if ($shippingPrice && $price >= CUtil::uint(floatval($shippingPrice) * 100)) {
            $fprice = 0;
        } else {
            // 获取运费
            $cnf        = by::Ofreight()->GetOneByType(by::Ofreight()::TYPE['WITH_FREE']);
            $free       = $cnf['cnf'] ?? 0;
            //免运费
            if ($free > 0 && bccomp($price, $free) >= 0) {
                $fprice = 0;
            }else{
                $aData = by::model('OfCfgModel', 'goods')->GetOneByCid(by::Ofreight()::TYPE['AREA'], $cid);
                if (empty($aData)) {
                    $cnf = by::Ofreight()->GetOneByType(by::Ofreight()::TYPE['DEFAULT']);
                } else {
                    $cnf = by::Ofreight()->GetOneById($aData['of_id']);
                }
                $fprice = intval($cnf['cnf'] ?? 0);
            }
        }
        return $fprice;
    }

    /**
     * 运费是否包邮计算
     * @param $shippingData
     * @param $user_id
     * @param $aid
     * @param $freight_other
     * @param $price
     * @param string $type
     * @param int $cid
     * @return int|mixed
     * @throws Exception
     */
    public function shippingPrice($shippingData, $user_id, $aid, $freight_other, $price, string $type='normal',$cid=0)
    {
        // 包邮商品数量
        $shippingData      = Collection::make($shippingData);
        $freeShippingCount = $shippingData->where('is_free_shipping', 1)->count();

        // 如果所有商品都包邮，则免运费
        if ($freeShippingCount == count($shippingData)) {
            $fprice = 0;
        }else {
            // 预售要取历史的运费价格
            if ($type=='presale'){
                $fprice = $this->getPreSaleFprice($shippingData, $user_id, $price,$cid);
            } else{
            // 普通商品
                $fprice = $this->getUserFprice($user_id, $price, $aid, $freight_other);
            }
        }
        return $fprice;
    }

    /**
     * 处理消费金逻辑
     *
     * @param int $userId 用户ID
     * @param array $params 接口参数
     * @param array $buyGoodsMap 购买的商品信息 ['gid' => ['type' => 0, 'name' => '商品名称']]
     * @param float $totalPrice 商品总价（元）
     * @param string $price 商品优惠后需要支付的总价（元）
     * @param bool $disableAllDiscount 是否禁用所有优惠
     * @return array [$success, $isUseConsumeMoney, $userConsumeMoney, $reallyConsumeMoney, $errorMessage]
     * @throws MyExceptionModel
     */
    private function applyConsumeMoney(int $userId, array $params, array $buyGoodsMap, float $totalPrice, string $price, bool $disableAllDiscount): array
    {
        $isUseConsumeMoney  = (int) ($params['is_use_consume_money'] ?? 0); // 0：不使用 1：使用
        $userConsumeMoney   = 0;
        $reallyConsumeMoney = 0;
        $errorMessage       = '';

        // 非APP接口，不使用消费金
        if (!in_array($params['api'] ?? '', self::CONSUME_MONEY_APIS)) {
            return [true, 0, 0, 0, ''];
        }

        // 禁用优惠，不使用消费金
        if ($disableAllDiscount) {
            return [true, 0, $userConsumeMoney, 0, ''];
        }

        // 用户当前剩余消费金
        $userConsumeMoney = byNew::UserShopMoneyModel()->getInfoByUserId($userId, UserShopMoneyModel::TYPE_CONSUME_MONEY, 2);

        // 无购物金或用户未勾选使用
        if (bccomp($userConsumeMoney, 0, self::PRICE_PRECISION) <= 0 || empty($isUseConsumeMoney)) {
            return [true, 0, $userConsumeMoney, 0, ''];
        }
        
        // 2025-07-31 改为品牌全品类都支持包括配件，下面判断支持消费金的商品ID列表功能注释
        foreach ($buyGoodsMap as $gid => $goodsItem) {
            $goodsType = $goodsItem['type'] ?? 0;
            // 非品牌商品（含配件）不能使用购物金
            if ($goodsType != 0) {
                // return [true, 0, $userConsumeMoney, 0, ''];
                return [true, 0, $userConsumeMoney, 0, sprintf('%s 不支持使用消费金', $goodsItem['name'] ?? '')];
            }
        }

        // 消费金活动ID，读取配置文件
        $consumeMoneyActivityId = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id) ?? 0;

        if (empty($consumeMoneyActivityId)) {
            return [true, 0, $userConsumeMoney, 0, ''];
        }

        // 获取活动信息以验证时间
        list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($consumeMoneyActivityId, $userId);
        // 验证活动时间
        $now       = time();
        $startTime = $activityData['base_info']['start_time'] ?? 0;
        $endTime   = $activityData['base_info']['end_time'] ?? 0;
        if (!$activityStatus || $now < $startTime || $now > $endTime) {
            return [true, 0, $userConsumeMoney, 0, '']; // 未配置活动/活动已过期
        }

        $module = $activityData['modules'];
        if (count($module) > 2) {                       // 只能配置一个模块
            return [true, 0, $userConsumeMoney, 0, '']; // 活动模块异常
        }

        $extra = $module[0]['extra'] ?? [];
        // 抵扣比例
        // 能使用的消费金比例
        $canUseConsumeMoneyRate = $extra['consume_money_rate'] ?? 0;
        if (!empty(floatval($canUseConsumeMoneyRate))) {
            $canUseConsumeMoneyRate = bcdiv($canUseConsumeMoneyRate, 100, self::PRICE_PRECISION);
        }

        // // 支持消费金的商品ID列表
        // $supportedGoodsIds = array_column($extra['goods'], 'goods_id');
        //
        // // 当前购买的商品ID列表
        // $buyGoodsIds = array_keys($buyGoods);
        //
        // // 不支持的商品ID，取差集，返回在当前购买的商品ID列表存在，但在支持消费金的商品ID列表不存在的商品ID
        // $unsupportedGoodsIds = array_diff($buyGoodsIds, $supportedGoodsIds);
        // // 支持的商品ID，取交集
        // $supportedGoodsIds = array_intersect($buyGoodsIds, $supportedGoodsIds);
        //
        // // 判断当前下单商品是否全都不支持消费金
        // if (empty($supportedGoodsIds)) {
        //     return [true, 0, $userConsumeMoney, 0, '']; // 全部不支持，强制不使用
        // }
        //
        // // 判断如果存在不支持的商品ID，则返回提示信息
        // if (!empty($unsupportedGoodsIds)) {
        //     $supportedGoods      = array_intersect_key($buyGoods, array_flip($supportedGoodsIds));
        //     $supportedGoodsNames = [];
        //     foreach ($supportedGoods as $goods) {
        //         $supportedGoodsNames[] = $goods ?: '未知商品';
        //     }
        //     $nameList = implode('、', $supportedGoodsNames);
        //     return [false, 0, $userConsumeMoney, 0, "仅以下商品支持使用购物金：{$nameList}"];
        // }

        // 最多可使用的消费金
        $maxCanUseConsumeMoney = 0;
        if (!empty(floatval($canUseConsumeMoneyRate))) {
            // 最大优惠金额 = 商品总价 * 可使用消费金比例
            $canUseConsumeMoneyByRate = bcmul($totalPrice, $canUseConsumeMoneyRate, self::PRICE_PRECISION);
            // 最小支付金额 如果 待支付金额 大于 最小支付金额，那么多出的部分则为可以使用的消费金 如果小于或等于，那么不能使用消费金
            $minPayPrice = bcsub($totalPrice, $canUseConsumeMoneyByRate, self::PRICE_PRECISION);

            if ($price > $minPayPrice) {
                $maxCanUseConsumeMoney = bcsub($price, $minPayPrice, 2);
            } else {
                $maxCanUseConsumeMoney = 0; // 小于或等于，不能使用消费金
            }
        }

        // 校验最多能使用的消费金是否超出当前用户剩余的消费金，为超出则使用最多能使用的，否则相当于把用户剩余的全扣掉
        $reallyConsumeMoney = bccomp($maxCanUseConsumeMoney, $userConsumeMoney, self::PRICE_PRECISION) <= 0
                ? $maxCanUseConsumeMoney
                : $userConsumeMoney;

        return [true, $isUseConsumeMoney, $userConsumeMoney, $reallyConsumeMoney, ''];
    }

}
