<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 动销系统订单表子表
 */
class ErpOrderProductModel extends CommModel
{
    
    public static function tbName($year): string
    {
        return "`db_dreame_goods`.`t_erp_order_product_{$year}`";
    }
    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData,$pay_time = 0)
    {
        $db     = by::dbMaster();
        if ($pay_time == 0){
            $year = date('Y',time());
        }else{
            $year = date('Y',$pay_time);
        }
        $tb     = $this->tbName($year);
        // $trans  = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'E3OrderGoods.info');
            // $trans->rollBack();
            return [false, '操作失败'];
        }
    }

    public function batchUpdate($data,$pay_time = 0){
        $db     = by::dbMaster();
        if ($pay_time == 0){
            $year = date('Y',time());
        }else{
            $year = date('Y',$pay_time);
        }
        $tb     = $this->tbName($year);
        foreach($data as $k=>$v){
            $db->createCommand()->update($tb, ['status'=>$v['status'],'utime'=>time(),'refund_score'=>$v['refund_score']], "`id`=:id", [":id" => $v['id']])->execute();
        }
        return true;
    }
    public function deleteByOrderNo($order_no,$pay_time){
        if ($pay_time == 0){
            $year = date('Y',time());
        }else{
            $year = date('Y',$pay_time);
        }
        $tb  = self::tbName($year);
        $sql = "DELETE FROM  {$tb} WHERE `order_no`=:order_no";
        $row = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->execute();
        return $row;
    }
}