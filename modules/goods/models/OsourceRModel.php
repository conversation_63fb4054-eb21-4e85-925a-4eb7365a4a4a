<?php

/**
 * Created by PhpStorm.
 * User: fantasy
 * Date: 2022/5/17
 * Time: 14:45
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\EventMsg;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\main\models\UserCardModel;
use app\modules\mall\models\UsersMallModel;
use yii\db\DataReader;
use yii\db\Exception;
use function GuzzleHttp\Psr7\build_query;


class OsourceRModel extends CommModel
{
    const EXP = 600;

    const EXPIRE_DAY = YII_ENV_PROD ? 15 : 0; //多少天后订单奖励推荐人 (单位天) 测试环境0天

    const DB_TIMEZONE = [2022, 2023, 2024, 2025, 2026, 2027, 2028, 2029, 2030]; //数据库的时间跨度

    const REWARD_STATUS_NAME = [
        1 => '未到账',
        2 => '冻结中',
        3 => '已取消',
        4 => '已领取'
    ];

    const REWARD_STATUS = [
        'PREDICT' => 1,
        'REFUNDING' => 2,
        'CANCELED' => 3,
        'RECEIVED' => 4
    ];

    public static function tbName($time): string
    {
        $year = date("Y", intval($time));

        //防止出现超出时间范围的查询
        $year = max($year, by::Omain()::DB_TIMEZONE['ST']);
        $year = min($year, by::Omain()::DB_TIMEZONE['ED']);

        return "`db_dreame_goods`.`t_osource_r_{$year}`";
    }

    public $tb_fields = [
        'id', 'user_id', 'r_id','r_type', 'order_no', 'status', 'price', 'reward_coupon_num', 'reward_point',
        'is_grant', 'is_valid', 'ctime', 'finish_time'
    ];

    /**
     * @param $r_id
     * @return string
     * 列表缓存
     */
    private function __getListKey($r_id): string
    {
        return AppCRedisKeys::getOsourceRList($r_id);
    }

    /**
     * @return string
     * 获取gmv列表缓存
     */
    private function __getGmvByYearKey(): string
    {
        return AppCRedisKeys::getGmvByYearList();
    }

    /**
     * @param $r_id
     * @param $order_no
     * @return string
     * 详情唯一缓存KEY
     */
    private function __getInfoByOrderNoKey($r_id, $order_no): string
    {
        return AppCRedisKeys::getOsRInfoByOrderNo($r_id, $order_no);
    }

    /**
     * @param $r_id
     * 清理缓存
     */
    public function delListCache($r_id = 0)
    {
        $r_key1 = $this->__getListKey($r_id);
        $r_key2 = $this->__getGmvByYearKey();

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param $user_id
     * @param $order_no
     * 清理详情缓存
     */
    public function delInfoCache($user_id, $order_no)
    {
        $r_key = $this->__getInfoByOrderNoKey($user_id, $order_no);

        by::redis('core')->del($r_key);
    }


    /**
     * @param int $r_id
     * @param int $user_id
     * @param string $order_no
     * @param int $is_grant
     * @param int $is_valid
     * @param int $reward_status
     * @param int $status
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $r_id = 0, int $user_id = 0, string $order_no = '', int $is_valid = -1, int $is_grant = -1, int $reward_status = 0, int $status = -1): array
    {
        //SQL初始化条件
        $where = "1=:init";
        $params[':init'] = 1;

        //推荐人ID
        if ($r_id > 0) {
            $where .= " AND `r_id`=:r_id";
            $params[":r_id"] = $r_id;
        }

        //用户ID
        if ($user_id > 0) {
            $where .= " AND `user_id`=:user_id";
            $params[":user_id"] = $user_id;
        }

        //订单号
        if (!empty($order_no)) {
            $where .= " AND `order_no`=:order_no";
            $params[":order_no"] = $order_no;
        }

        //是否领取奖励
        if ($is_grant > -1) {
            $where .= " AND `is_grant`=:is_grant";
            $params[":is_grant"] = $is_grant;
        }

        //是否有效推荐订单
        if ($is_valid > -1) {
            $where .= " AND `is_valid`=:is_valid";
            $params[":is_valid"] = $is_valid;
        }

        //奖励状态
        $o_main = by::Omain();
        switch ($reward_status) {
            case self::REWARD_STATUS['PREDICT']:
                //预计到账
                $predict_status = [
                    $o_main::ORDER_STATUS['WAIT_SEND'],
                    $o_main::ORDER_STATUS['WAIT_RECEIVE'],
                    $o_main::ORDER_STATUS['FINISHED'],
                    $o_main::ORDER_STATUS['RERUNDED_FINISHED']
                ];
                $in_status = implode(',', $predict_status);
                $where .= " AND`status` IN ({$in_status}) ";

                break;
            case self::REWARD_STATUS['REFUNDING']:
                //冻结中
                $where .= " AND `status`=:status";
                $params[":status"] = $o_main::ORDER_STATUS['REFUNDING'];

                break;
            case self::REWARD_STATUS['CANCELED']:
                //已取消
                $where .= " AND `status`=:status";
                $params[":status"] = $o_main::ORDER_STATUS['RERUNDED'];

                break;
            default :
                break;
        }

        if ($status == by::Omain()::ORDER_STATUS['CANCELED']) {
            $where .= " AND `status`>:status";
            $params[":status"] = by::Omain()::ORDER_STATUS['CANCELED'];
        }

        return [$where, $params];
    }

    /**
     * @param $sourceData
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog($sourceData): array
    {
        if (
            empty($sourceData['user_id']) || empty($sourceData['r_id'])
            || empty($sourceData['order_no']) || empty($sourceData['ctime'])
        ) {
            return [false, '参数错误'];
        }

        $save = [
            'user_id' => $sourceData['user_id'],
            'r_id' => $sourceData['r_id'],
            'r_type' => $sourceData['r_type'] ?? 0,
            'order_no' => $sourceData['order_no'],
            'price' => $sourceData['price'] ?? 0,
            'ctime' => $sourceData['ctime'],
            'r_type' => $sourceData['r_type'],
        ];

        $ac_model = by::activityConfigModel();
        $ac_info = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift']);
        $order_min_money = by::Gtype0()->totalFee($ac_info['order_min_money'] ?? 0); //分
        $pay_price = $sourceData['pay_price'] ?? 1;
        if ($save['r_type'] == 1){
            if ($pay_price >= $order_min_money && !empty($sourceData['expire_time']) && $sourceData['ctime'] <= $sourceData['expire_time']) {
                $save['is_valid'] = 1;
            }
        }else{
            // 类型不为1时，只判断时间
            if (!empty($sourceData['expire_time']) && $sourceData['ctime'] <= $sourceData['expire_time']) {
                $save['is_valid'] = 1;
            }
        }
        $tb = self::tbName($save['ctime']);
        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        $this->delListCache($sourceData['r_id']);

        return [true, 'ok'];
    }
    public function updateData($score,$order_no,$user_id,$ctime){
        $db = by::dbMaster();
        $tb_osr = self::tbName($ctime);

        $res = $db->createCommand()->update(
            $tb_osr,
            ['reward_point' => $score],
            ['order_no' => $order_no, 'user_id' => $user_id]
        )->execute();
        return $res;
    }

    public function GetBuyCountByUserId($user_id){
        $info = self::find()->select(['count(DISTINCT user_id) as count'])->where([
            'r_id'=>$user_id,
            'is_valid'=>1,
            'status'=>[300, 400, 500],
            'is_new_user'=>1
        ])->andWhere([ 'ctime','>',1752681600])->asArray()->one();
        return $info['count'] ?? 0;
    }

    /**
     * @param $r_id
     * @param string $year
     * @param int $is_valid
     * @param int $is_grant
     * @param int $reward_status
     * @param int $status
     * @return array
     * @throws Exception
     * 订单列表
     */
    public function getList($r_id, string $year = '', int $is_valid = -1, int $is_grant = -1, int $reward_status = 0, int $status = -1): array
    {
        $r_id = CUtil::uint($r_id);
        $year = CUtil::uint($year);
        if ($r_id == 0 || ($year > 0 && $year < by::Omain()::DB_TIMEZONE['ST']) || $year > by::Omain()::DB_TIMEZONE['ED']) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getListKey($r_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $year, $is_valid, $status);
        $aJson = $redis->hGet($r_key, $sub_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $time = $year == 0 ? time() : strtotime("{$year}0101");
            $tb = self::tbName($time);
            list($where, $params) = $this->__getCondition($r_id, 0, '', $is_valid, $is_grant, $reward_status, $status);

            $sql = "SELECT `user_id`,`order_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $aData = !empty($aData) ? $aData : [];

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : self::EXP);
        }

        $list = [];
        foreach ($aData as $v) {
            $or_info = $this->getInfoByOrderNo($v['user_id'], $v['order_no']);
            if (empty($or_info)) {
                continue;
            }

            if ($is_valid == 1) {
                //获取用户信息
                $u_info = by::users()->getOneByUid($v['user_id']);
                $or_info['user'] = [
                    'avatar' => $u_info['avatar'] ?? "",
                    'nick' => $u_info['nick'] ?? ""
                ];

                //获取奖励状态名
                $status_name = $this->getStatusName($v['user_id'], $v['order_no']);
                $or_info['status_name'] = $status_name ?? '未知';
            }

            $list[] = $or_info;
        }

        return $list;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array|DataReader
     * @throws Exception
     * 获取推荐人详情
     */
    public function getInfoByOrderNo($user_id, $order_no)
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id == 0 || empty($order_no)) {
            return [];
        }

        $redis = by::redis('core');
        $redis_key = $this->__getInfoByOrderNoKey($user_id, $order_no);
        $json = $redis->get($redis_key);
        $info = (array)json_decode($json, true);

        if ($json === false) {
            $time = by::Omain()->GetTbNameByOrderId($order_no, false);
            $tb = $this->tbName($time);
            $fields = implode("`,`", $this->tb_fields);
            list($where, $param) = $this->__getCondition(0, $user_id, $order_no);
            $sql = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info = by::dbMaster()->createCommand($sql, $param)->queryOne();
            $info = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        return $info;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * @throws Exception
     * 获取奖励状态
     */
    public function getStatusName($user_id, $order_no): string
    {
        //未到账订单状态
        $o_main = by::Omain();
        $predict_status = [
            $o_main::ORDER_STATUS['WAIT_SEND'],
            $o_main::ORDER_STATUS['WAIT_RECEIVE'],
            $o_main::ORDER_STATUS['FINISHED'],
            $o_main::ORDER_STATUS['RERUNDED_FINISHED']
        ];

        //冻结中订单状态
        $refunding_status = [
            $o_main::ORDER_STATUS['REFUNDING'],
            $o_main::ORDER_STATUS['REFUNDING_WAIT_SEND'],
            $o_main::ORDER_STATUS['REFUNDING_WAIT_RECEIVE'],
            $o_main::ORDER_STATUS['REFUNDING_FINISHED'],
            $o_main::ORDER_STATUS['RERUNDED_WAIT_SEND'],
            $o_main::ORDER_STATUS['RERUNDED_WAIT_RECEIVE'],
        ];

        //已取消订单状态
        $canceled_status = [$o_main::ORDER_STATUS['RERUNDED']];

        $ou_info = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        $status = $ou_info['status'] ?? 0;
        $osr_info = $this->getInfoByOrderNo($user_id, $order_no);
        $is_grant = $osr_info['is_grant'] ?? 0;

        if ($is_grant == 1) {
            $status_name = self::REWARD_STATUS_NAME[4]; //已领取
        } else {
            switch (true) {
                case in_array($status, $predict_status):
                    $data = $this->getGoodsPrice($order_no, $user_id);
                    if ($data['goods_price'] + $data['f_price'] < $data['order_min_money']) {
                        $status_name = self::REWARD_STATUS_NAME[3]; //已取消
                    } else {
                        $status_name = self::REWARD_STATUS_NAME[1]; //未到账
                    }

                    break;
                case in_array($status, $refunding_status):
                    $status_name = self::REWARD_STATUS_NAME[2]; //冻结中

                    break;
                case in_array($status, $canceled_status):
                    $status_name = self::REWARD_STATUS_NAME[3]; //已取消

                    break;
                default :
                    $status_name = "未知";

                    break;
            }
        }

        return $status_name;
    }

    /**
     * @param $r_id
     * @param $type
     * @param $reward_status
     * @param $year
     * @return int
     * @throws Exception
     * 获取每年奖励数量
     */
    public function getRewardNumByYear($r_id, $type, $reward_status, $year): int
    {
        $r_id = CUtil::uint($r_id);
        $year = CUtil::uint($year);
        if (
            $r_id == 0 || empty($type) || empty($reward_status) || $year == 0 || $year < by::Omain()::DB_TIMEZONE['ST']
            || $year > by::Omain()::DB_TIMEZONE['ED']) {
            return 0;
        }

        //奖励类型
        switch ($type) {
            case by::activityConfigModel()::REWARD_TYPE['coupon']:
                $field = "reward_coupon_num";

                break;
            case by::activityConfigModel()::REWARD_TYPE['point']:
                $field = "reward_point";

                break;
            default :
                return 0;
        }

        //奖励状态
        switch ($reward_status) {
            case self::REWARD_STATUS['PREDICT']:
                //预计到账
                $num_list = [];
                $list = $this->getList($r_id, $year, 1, 0, self::REWARD_STATUS['PREDICT']);

                foreach ($list as &$info) {
                    $data = $this->getGoodsPrice($info['order_no'], $info['user_id']);
                    if ($data['goods_price'] + $data['f_price'] < $data['order_min_money']) {
                        continue;
                    }

                    $num_list[] = $info;
                }

                $num = !empty($num_list) ? array_sum(array_column($num_list, $field)) : 0;
                $num = empty($num) ? 0 : $num;

                break;
            case self::REWARD_STATUS['RECEIVED']:
                //已到账
                $redis = by::redis('core');
                $redis_key = $this->__getListKey($r_id);
                $sub_Key = CUtil::getAllParams(__FUNCTION__, $type, $reward_status, $year);
                $num = $redis->hGet($redis_key, $sub_Key);

                if ($num === false) {
                    list($where, $params) = $this->__getCondition($r_id, 0, '', 1, 1);
                    $time = strtotime("{$year}0101");
                    $tb = self::tbName($time);
                    $sql = "SELECT sum(`{$field}`) FROM {$tb} WHERE {$where}";

                    $num = by::dbMaster()->createCommand($sql, $params)->queryScalar();
                    $num = empty($num) ? 0 : $num;

                    $redis->hSet($redis_key, $sub_Key, $num);
                    CUtil::ResetExpire($redis_key, empty($num) ? 10 : self::EXP);
                }

                break;
            default :
                return 0;
        }

        return intval($num);
    }

    /**
     * @param $r_id
     * @param $type
     * @param $reward_status
     * @return int
     * @throws Exception
     * 获取总奖励数量
     */
    public function getRewardNum($r_id, $type, $reward_status): int
    {
        $r_id = CUtil::uint($r_id);
        if ($r_id == 0 || empty($type) || empty($reward_status)) {
            return 0;
        }

        $year_arr = self::DB_TIMEZONE;
        $total_num = 0;
        foreach ($year_arr as $year) {
            $year_num = $this->getRewardNumByYear($r_id, $type, $reward_status, $year);
            $total_num += $year_num;
        }

        return $total_num;
    }

    /**
     * @param $year
     * @return int
     * @throws Exception
     * 获取每年Gmv
     */
    public function getGmvByYear($year): int
    {
        $year = CUtil::uint($year);
        if ($year == 0 || $year < by::Omain()::DB_TIMEZONE['ST'] || $year > by::Omain()::DB_TIMEZONE['ED']) {
            return 0;
        }

        $redis = by::redis('core');
        $r_key = $this->__getGmvByYearKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $year);
        $json = $redis->hGet($r_key, $sub_key);
        $order_nos = (array)json_decode($json, true);

        if ($json === false) {
            $time = strtotime("{$year}0101");
            $tb = self::tbName($time);
            $o_main = by::Omain();
            $status = [
                $o_main::ORDER_STATUS['WAIT_PAY'],
                $o_main::ORDER_STATUS['CANCELED'],
                $o_main::ORDER_STATUS['REFUNDING'],
                $o_main::ORDER_STATUS['RERUNDED']
            ];
            $in_status = implode(',', $status);
            $where = " `status` NOT IN ({$in_status}) ";
            $sql = "SELECT `user_id`, `order_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC";
            $order_nos = by::dbMaster()->createCommand($sql)->queryAll();
            $order_nos = empty($order_nos) ? [] : $order_nos;

            by::redis('core')->hSet($r_key, $sub_key, json_encode($order_nos));
            CUtil::ResetExpire($r_key, empty($order_nos) ? 10 : self::EXP);
        }

        $year_price = 0;
        foreach ($order_nos as $v) {
            $price = by::Ogoods()->getPriceByOrderNo($v['order_no'], $v['user_id']);
            $year_price += $price;
        }

        return $year_price;
    }

    /**
     * @return int
     * @throws Exception
     * 获取gmv
     */
    public function getTotalGmv(): int
    {
        $year_arr = self::DB_TIMEZONE;
        $total_price = 0;
        foreach ($year_arr as $year) {
            $year_price = $this->getGmvByYear($year);
            $total_price += $year_price;
        }

        return $total_price;
    }

    /**
     * @throws Exception
     * 脚本发放推荐人奖励(收货15天后给推荐人发放奖励)
     */
    public function orderReward(): array
    {
        $db = by::dbMaster();
        $days = self::EXPIRE_DAY;
        $expire_time = YII_ENV_PROD ? strtotime("-{$days} days") : strtotime("-1 minutes");

        //分表跨年
        $this_year = date("Y");
        $years = [$this_year-1,$this_year, date("Y", $expire_time)];
        $years = array_unique($years);

        $am_model = by::aM();
        $ac_model = by::activityConfigModel();
        $ac_id = $ac_model->getActivityIdsByType(by::activityConfigModel()::GRANT_TYPE['recommend_gift']);
        $ac_info = $ac_model->getActivityOne($ac_id);
        $reward_type = $ac_info['reward_type'] ?? 0;

        foreach ($years as $year) {
            //分表跨年
            $date = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime = strtotime($date);
            $tb_osr = self::tbName($ctime);
            $fields = implode("`,`", $this->tb_fields);
            $status = by::Omain()::ORDER_STATUS['FINISHED'] . "," . by::Omain()::ORDER_STATUS['RERUNDED_FINISHED'];
            $id = 0;

            $sql = "SELECT `{$fields}` FROM {$tb_osr} WHERE `id` > :id AND `is_grant` = 0 
                            AND `status` in ({$status}) AND `finish_time` < {$expire_time} ORDER BY `id` limit 50";

            while (true) {
                $list = $db->createCommand($sql, [':id' => $id])->queryAll();
                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id = $end['id'];

                foreach ($list as $info) {
                    if (empty($info)) {
                        continue;
                    }

                    $data = $this->getGoodsPrice($info['order_no'], $info['user_id']);
                    if ($data['goods_price'] + $data['f_price'] < $data['order_min_money']) {
                        continue;
                    }

                    //todo 有效订单且未发放
                    if (!empty($info['r_id']) && !empty($info['is_valid'])) {
                        $trans = $db->beginTransaction();
                        try {
                            switch ($reward_type) {
                                case $ac_model::REWARD_TYPE['coupon'] :
                                    //领取优惠券
                                    $get_channel = UserCardModel::GET_CHANNEL['activity'];
                                    list(, $am_ids) = $am_model->getIdsByAid($ac_id);//查询绑定优惠券
                                    if (!empty($am_ids)) {
                                        foreach ($am_ids as $am_id) {
                                            //配置资源信息验证
                                            $am_info = $am_model->getOneById($am_id);
                                            if (!$am_info) {
                                                continue;
                                            }

                                            //扣除库存
                                            list($status, $res) = $am_model->stockModify($ac_id, $am_id, 1, $am_info['stock'], $info['r_id']);
                                            if (!$status) {
                                                CUtil::debug("优惠劵库存不足, am_id:{$am_id}, 失败原因:{$res}", 'reward-stock-modify.err');
                                                continue;
                                            }

                                            list($status, $msg) = by::userCard()->setCard($info['r_id'], $am_info['mc_id'], $get_channel, $am_id);
                                            if (!$status) {
                                                $am_info = $am_model->getOneById($am_id);
                                                $am_model->stockModify($ac_id, $am_id, -1, $am_info['stock'], $info['r_id']);//回滚库存
                                                CUtil::debug("绑定活动发放优惠券异常信息:{$msg},user_id={$info['user_id']},资源信息：" . json_encode($am_info), 'order-reward-card.err');
                                            }
                                        }
                                    }

                                    $u_set = "`coupon_reward_count` = `coupon_reward_count`+(:coupon_reward_count)";
                                    $params = [
                                        ':user_id' => $info['r_id'],
                                        ':coupon_reward_count' => 1
                                    ];

                                    break;
                                case $ac_model::REWARD_TYPE['point'] :
                                    $u_set = "`point_reward_count` = `point_reward_count`+(:point_reward_count)";
                                    $params = [
                                        ':user_id' => $info['r_id'],
                                        ':point_reward_count' => 1
                                    ];

                                    break;
                                default :
                                    throw new \Exception('类型不合法');
                            }

                            //更新推荐人下单奖励发放次数
                            $tb_u = by::users()::userTb($info['r_id']);
                            $where = "`user_id`=:user_id";
                            $sql_u = "UPDATE {$tb_u} SET {$u_set} WHERE {$where} LIMIT 1";
                            $res = by::dbMaster()->createCommand($sql_u, $params)->execute();
                            if ($res <= 0) {
                                throw new \Exception("无数据更新（1）");
                            }

                            //更新订单是否已发放
                            $res = $db->createCommand()->update(
                                $tb_osr,
                                ['is_grant' => 1],
                                ['order_no' => $info['order_no'], 'user_id' => $info['user_id']]
                            )->execute();
                            if ($res <= 0) {
                                throw new \Exception("无数据更新（2）");
                            }
                            if ($info['r_type'] == 1){
                                EventMsg::factory()->run('inviteBuyComm', ['user_id' => $info['r_id'],'order_no'=>$info['order_no']]);//推荐人发奖
                            }else if ($info['r_type'] == 2){
                                EventMsg::factory()->run('inviteBuy', ['user_id' => $info['r_id'],'order_no'=>$info['order_no']]);//推荐人发奖
                            }else if ($info['r_type'] == 3){
                                // EventMsg::factory()->run('inviteBuyComm', ['user_id' => $info['r_id'],'order_no'=>$info['order_no']]);//推荐人发奖
                            }else if ($info['r_type'] == 4){
                                EventMsg::factory()->run('inviteBuyGoodsMoney', ['user_id' => $info['r_id'],'order_no'=>$info['order_no']]);//推荐人发奖
                            }

                            

                            $trans->commit();



                            $this->delListCache($info['r_id']);
                            $this->delInfoCache($info['user_id'], $info['order_no']);
                            by::users()->deleteRedisCache($info['r_id']);
                        } catch (\Exception $e) {
                            CUtil::debug(json_encode($info) . "|" . $e->getMessage(), 'order-reward-err');

                            $trans->rollBack();

                            return [false, $e->getMessage()];
                        }
                    }
                }
            }
        }

        return [true, 'ok'];
    }

    /**
     * @param  $r_code
     * @param  $gid
     * @param  $type
     * @return array
     * @throws Exception 获取分享二维码
     */
    public function shareRcode($r_code = '', $gid = 0, $type = 'goods'): array
    {
        if (empty($r_code)) {
            return [false, '分享参数错误（1）'];
        }

        list($r_id, $rand) = by::userGuide()->decrypt($r_code);
        if (empty($r_id) || empty($rand)) {
            return [false, '分享参数错误（2）'];
        }

        $types = [
                'goods',     //普通商品
                'pointsMall',//积分商品
        ];
        if (!in_array($type, $types)) {
            return [false, '分享参数错误（3）'];
        }

        $user = by::users()->getOneByUid($r_id);
        $data = [
                'scene'  => "o={$r_code}&gid={$gid}&type={$type}",
                'nick'   => $user['nick'] ?? '',
                'avatar' => $user['avatar'] ?? '',
        ];

        return [true, $data];
    }

    /**
     * @param $order_no
     * @param $user_id
     * @return array
     * @throws Exception
     * 获取订单商品价格
     */
    public function getGoodsPrice($order_no, $user_id): array
    {
        $ac_model = by::activityConfigModel();
        $ac_info = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift']);
        $order_min_money = by::Gtype0()->totalFee($ac_info['order_min_money'] ?? 0);
        $goods_price = by::Ogoods()->getPriceByOrderNo($order_no, $user_id) ?? 0;
        $f_price = by::Ouser()->GetInfoByOrderId($user_id, $order_no)['fprice'] ?? 0;
        $f_price = $goods_price == 0 ? 0 : $f_price;

        return [
            "goods_price" => $goods_price,
            "f_price" => $f_price,
            "order_min_money" => $order_min_money
        ];
    }

    /**
     * 根据订单查询信息（无缓存）
     * @param array $orderNos
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getListByOrderNos(array $orderNos, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $columns);
        // 分组查询
        $groupOrderNos = $this->groupOrderNo($orderNos);
        foreach ($groupOrderNos as $index => $nos) {
            // 例如：生成2023/1/1 00:00:00的时间戳
            $time = strtotime(date("{$index}-01-01"));
            $tb = self::tbName($time); // 获取表名称
            // 查询条件
            $nos = implode("','", $nos);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `order_no` IN ('{$nos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 订单号分组
     * @param array $orderNos
     * @return array
     */
    private function groupOrderNo(array $orderNos): array
    {
        $data = [];
        foreach ($orderNos as $orderNo) {
            // 获取年份，分组
            $index = substr($orderNo, 0, 4); // 年份
            $data[$index][] = $orderNo;
        }
        return $data;
    }

    // 查询需要处理的订单
    public function getNeedHandleOrder(){
        $tb = self::tbName(time()); // 获取表名称
        $where = "status in (300,400,500) and is_valid = 1 and ctime > 1752767999";
        $sql = "SELECT * FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC";
        $aData = by::dbMaster()->createCommand($sql, [])->queryAll();
        foreach ($aData as $k => $v) {
            $user = byNew::UserEmployeeModel()->getEmployeeInfo($v['r_id'],'user_id');
            
            if ($user){
                // 把数据同步到分佣表
                $save = [];
                $save['order_no'] = $v['order_no'];
                $save['user_id'] = $v['user_id'];
                $save['referrer'] = $user['user_id'];
                $save['price'] = $v['price'];
                $save['status'] = $v['status'];
                $save['rate'] = 0.2;
                $save['commission'] = round($v['price'] * 0.2);
                $save['ctime'] = $v['ctime'];
                $save['utime'] = time();
                $save['extend'] = json_encode($user);
                byNew::SalesCommissionModel()->saveData($save);
            }
        }
    }
}
