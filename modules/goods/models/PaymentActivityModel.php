<?php

namespace app\modules\goods\models;

use app\components\collection\Collection;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class PaymentActivityModel extends CommModel
{

    const PAY_TYPE = [
        'BAITIAO' => 1,
    ];

    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_payment_activity`";
    }


    public function getGoods(): \yii\db\ActiveQuery
    {
        return $this->hasMany(PaymentActivityGoodsModel::class, ['activity_id' => 'id']);
    }

    // 获取商品免息期数活动的最大最小可分期数
    public function getBaiTiaoInst(array $gids, $type = 'max'): int
    {
        if ($type !== 'max' && $type !== 'min') {
            return 0;
        }

        $gids   = array_unique($gids);
        $result = self::find()
                ->select(['id'])
                ->where(['is_del' => 0, 'pay_type' => self::PAY_TYPE['BAITIAO']])
                ->andWhere(['<=', 'start_time', time()])
                ->andWhere(['>=', 'end_time', time()])
                ->asArray()
                ->all();

        $activityIds = array_column($result, 'id');

        if (empty($activityIds)) {
            return 0;
        }

        $goods = byNew::PaymentActivityGoodsModel()::find()
                ->select(['activity_id','interest_free','gid'])
                ->where(['in', 'activity_id', $activityIds])
                ->andWhere(['in', 'gid', $gids])
                ->asArray()
                ->all();

        $result = array_filter($goods, function ($item) {
            return $item['interest_free'] > 0;
        });

        if (empty($result)) {
            return 0;
        }

        // 获取所有商品的免息活动
        $freeGids = array_unique(array_column($result, 'gid'));
        if (count($gids) !== count($freeGids)) {
            return 0;
        }

        return Collection::make($result)->groupBy('gid')->map(function ($item) use ($type) {
            return Collection::make($item)->max('interest_free');
        })->values()->{$type}();


    }
}