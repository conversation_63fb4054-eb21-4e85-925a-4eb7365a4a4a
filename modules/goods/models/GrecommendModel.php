<?php


namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class GrecommendModel extends CommModel
{


    public $tb_fields = [
        'cate_id', 'gid', 'tid', 'title', 'image', 'pcombines', 'ctime', 'utime', 'sort', 'pc_image', 'h5_image'
    ];


    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_grecommend`";
    }

    private function __getRecommendByCateId($cateId): string
    {
        return AppCRedisKeys::getRecommendByCateId($cateId);
    }

    /**
     * @throws RedisException
     */
    public function __delRecommendByCateId($cateId)
    {
        $redis     = by::redis();
        $redis_key = $this->__getRecommendByCateId($cateId);
        $redis->del($redis_key);
    }

    private function __getRecommendList(): string
    {
        return AppCRedisKeys::getRecommendList();
    }

    public function __delRecommendList()
    {
        $redis     = by::redis();
        $redis_key = $this->__getRecommendList();
        $redis->del($redis_key);
    }
    /**
     * @throws Exception
     * @throws RedisException
     */
    public function saveLog($post, $cate_id): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();

        $save = [
            'cate_id'   => $cate_id,
            'gid'       => $post['gid'] ?? 0,
            'tid'       => $post['tid'] ?? '',
            'title'     => $post['title'] ?? '',
            'image'     => $post['image'] ?? '',
            'pc_image'  => $post['pc_image'] ?? '',
            'h5_image'  => $post['h5_image'] ?? '',
            'pcombines' => $post['pcombines'] ?? '',
            'utime'     => intval(START_TIME),
            'sort'      => intval($post['sort'] ?? 0),
        ];

        $dup = [];
        foreach ($save as $key => $value) {
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup            = implode(' , ', $dup);
        $save ['ctime'] = intval(START_TIME);
        $rows           = implode("','", $save);
        $fields         = array_keys($save);
        $fields         = implode("`,`", $fields);
        $sql            = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";
        $db->createCommand($sql)->execute();

        $this->__delRecommendByCateId($cate_id);
        $this->__delRecommendList();
        return [true, 'ok'];
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getRecommendInfoByCateId($cateId)
    {
        $redis     = by::redis();
        $redis_key = $this->__getRecommendByCateId($cateId);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $fields = $this->tb_fields;
            $fields = implode("`,`", $fields);
            $tb     = self::tbName();
            $db     = by::dbMaster();
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `cate_id`=:cate_id LIMIT 1";
            $aData  = $db->createCommand($sql, [":cate_id" => $cateId])->queryOne();
            $aData  = $aData ? : [];
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getList(): array
    {
        $redis     = by::redis();
        $redis_key = $this->__getRecommendList();
        $sub_key   = CUtil::getAllParams(__FUNCTION__);
        $aJson     = $redis->hGet($redis_key, $sub_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aJson) {
            $tb    = self::tbName();
            $db    = by::dbMaster();
            $sql   = "SELECT `cate_id` FROM {$tb}  ORDER BY `sort` DESC";
            $aData = $db->createCommand($sql)->queryAll();
            $aData = array_column($aData, 'cate_id');
            $aData = empty($aData) ? [] : $aData;
            $redis->hSet($redis_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }

        return $aData ?? [];
    }



}
