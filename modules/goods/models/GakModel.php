<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品属性名
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GakModel extends CommModel {

    public $tb_fields = [
        'id','gid','at_name','is_del'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gak`";
    }

    /**
     * @param $id
     * @return string
     * 通过id获取数据
     */
    private function __getOneAkByIdKey($id): string
    {
        return AppCRedisKeys::getOneAkById($id);
    }

    /**
     * @param $gid
     * @param $at_name
     * @return string
     * 属性名缓存
     */
    private function __getAkByNameKey($gid, $at_name) {
        return AppCRedisKeys::getAkByName($gid, $at_name);
    }

    /**
     * @param $gid
     * @return string
     * 属性列表缓存
     */
    private function __getAkListByGidKey($gid) {
        return AppCRedisKeys::getAkListByGid($gid);
    }

    /**
     * @param $id
     * @return int
     * 缓存清理
     */
    private function __delIdCache($id): int
    {
        $r_key = $this->__getOneAkByIdKey($id);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @param $at_name
     * @return int
     * 缓存清理
     */
    private function __delNameCache($gid, $at_name): int
    {
        $r_key = $this->__getAkByNameKey($gid, $at_name);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @return int
     * 列表缓存清理
     */
    private function __delListCache($gid): int
    {
        $r_key = $this->__getAkListByGidKey($gid);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 增加规格增改
     */
    public function SaveLog(array $aData): array
    {
        $gid        = $aData['gid']     ?? 0;
        $at_name    = $aData['at_name']    ?? '';

        if (empty($gid) || empty($at_name)) {
            return [false, "参数缺失"];
        }

        $aInfo =  $this->GetOneByName($gid,$at_name, false);
        if ($aInfo) {
            return [true, $aInfo['id']]; //存在，直接返回规格id
        }

        $save = [
            'gid'       => $gid,
            'at_name'   => $at_name,
        ];

        $db      = by::dbMaster();
        $tb      = self::tbName();
        $ret     = $db->createCommand()->insert($tb,$save)->execute();
        if (!$ret) {
            return [false, "规格新增失败"];
        }

        $ak_id    = $db->getLastInsertID();
        $this->__delIdCache($ak_id);
        $this->__delNameCache($gid, $at_name);
        $this->__delListCache($gid);

        return [true, $ak_id];
    }

    /**
     * @param int $gid
     * @param string $at_name
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 通过name 查询规格
     */
    public function GetOneByName(int $gid, string $at_name, $cache = true): array
    {
        $gid        = CUtil::uint($gid);

        if (empty($gid) || empty($at_name)){
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = $this->__getAkByNameKey($gid, $at_name);

        !$cache && $redis->del($r_key);

        $id         = $redis->get($r_key);

        if($id === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id` FROM  {$tb} WHERE `gid`=:gid AND `at_name`=:at_name AND `is_del`= 0 LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':gid'=>$gid, ':at_name'=>$at_name])->queryOne();
            $id      = $aData['id'] ?? 0;

            $cache && $redis->set($r_key,$id, 1800);
        }

        if(!$id) {
            return [];
        }

        return $this->GetOneById($id);
    }

    /**
     * @param string $attrCnf
     * @return array
     * 规格名-规格值 [{"at_name":"颜色","at_val":["红色","白色"]}]
     * 商品规格校验
     */
    public function checkAttr(string $attrCnf): array
    {
        $attrCnfArr = json_decode($attrCnf, true);
        if (!is_array($attrCnfArr)) {
            return [false, "规格参数有误(1)"];
        }

        foreach ($attrCnfArr as $k => $v) {
            if (!isset($v['at_name'])) {
                return [false, "规格参数有误(2)"];
            }
            if (!isset($v['at_val']) || !is_array($v['at_val'])) {
                return [false, "规格参数有误(3)"];
            }
        }

        return [true, $attrCnfArr];
    }

    /**
     * @param int $gid
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 商品规格列表
     */
    public function GetListByGid(int $gid, $cache = true): array
    {
        if(empty($gid)){
            return [];
        }
        $gid     = CUtil::uint($gid);

        $redis   = by::redis('core');
        $r_key   = $this->__getAkListByGidKey($gid);

        !$cache && $redis->del($r_key);

        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id`,`at_name` FROM  {$tb} WHERE `gid`=:gid AND `is_del`= 0 ORDER BY `id` asc";
            $aData   = by::dbMaster()->createCommand($sql, [':gid'=>$gid])->queryAll();

            $cache && $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $gid
     * @param string $at_name
     * @return array
     * @throws \yii\db\Exception
     * 编辑商品属性名 暂时只改is_del
     */
    public function UpdateLog(int $gid, string $at_name): array
    {
        $gid  = CUtil::uint($gid);

        if(empty($gid) || empty($at_name)){
            return [false, '缺少参数'];
        }

        $aLog =  $this->GetOneByName($gid,$at_name);
        if (empty($aLog)) {
            return [false, "规格-{$at_name}不存在！"];
        }

        $tb      = self::tbName();
        $ret     = by::dbMaster()->createCommand()->update($tb,
            ['is_del'=>1],
            ["id"=>$aLog['id']]
        )->execute();

        if (!$ret) {
            return [false, "未知原因，规格修改失败"];
        }

        $this->__delIdCache($aLog['id']);
        $this->__delNameCache($gid, $at_name);
        $this->__delListCache($gid);

        return [true, $aLog['id']];
    }

    /**
     * @param $id
     * @return array|false
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id)
    {
        $id      = CUtil::uint($id);

        $redis   = by::redis('core');
        $r_key   = $this->__getOneAkByIdKey($id);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id'=>$id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

}
