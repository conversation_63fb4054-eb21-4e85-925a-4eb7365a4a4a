<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class UserOrderTryConversionModel extends CommModel
{
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_uo_try_conversion`";
    }

    /**
     * 保存用户试用订单详情
     */
    public function SaveLog($data)
    {
        if (empty($data['user_id']) || empty($data['model'])) {
            return [false, '参数错误'];
        }
        //存在则更新，不存在则插入
        $data['user_id'] = CUtil::uint($data['user_id']);
        $data['model']   = trim($data['model']);
        $data['sn']      = trim($data['sn']);
        $fields          = array_keys($data);
        $fields          = implode("`,`", $fields);
        $rows            = implode("','", $data);
        $dup             = [];
        foreach ($data as $key => $value) {
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup = implode(' , ', $dup);
        $tb  = self::tbName();
        $sql = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";
        $res = by::dbMaster()->createCommand($sql)->execute();
        if ($res === false) {
            return [false, '保存失败'];
        }
        return [true, '保存成功'];
    }



    public function BatchInsert(array $data)
    {
        if (empty($data)) {
            return [false, '参数错误'];
        }

        $table        = self::tbName();
        $fields       = array_keys($data[0]);
        $placeholders = [];
        $values       = [];

        foreach ($data as $index => $item) {
            $row = [];
            foreach ($item as $key => $value) {
                // 添加唯一前缀防止重复
                $param          = ":{$key}_{$index}";
                $row[]          = $param;
                $values[$param] = $value;
            }
            $placeholders[] = '(' . implode(',', $row) . ')';
        }

        $sql = sprintf(
                "INSERT INTO %s (%s) VALUES %s",
                $table,
                '`' . implode('`,`', $fields) . '`',
                implode(',', $placeholders)
        );

        try {
            $res = by::dbMaster()->createCommand($sql, $values)->execute();
            if ($res === false) {
                return [false, '保存失败'];
            }
            return [true, '保存成功'];
        } catch (\Exception $e) {
            return [false, '保存失败: ' . $e->getMessage()];
        }
    }

    public function GetOneInfo(array $input)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, $params)->queryOne();
        return $result ?? [];
    }

    public function GetList(array $input, $filterSn = true, $page = 1, $pageSize = 1000)
    {
        $tb     = self::tbName();
        //查询数据
        $select = "SELECT * FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 默认过滤已经试用过的机器
        $user_id = $input['user_id'] ?? 0;
        if ($filterSn && $user_id) {
            $orderTryDb = byNew::UserOrderTry()::tbName();
            $select .= " AND `sn` not in (select `sn` from `{$orderTryDb}` where `user_id` = {$user_id})";
        }


        // 构建 LIMIT 子句
        list($offset) = CUtil::pagination($page, $pageSize);
        $select .= " LIMIT {$offset}, {$pageSize}";

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($select, $params)->queryAll() ?? [];
        return $result;
    }


    public function GetCount(array $input,$filterSn = true)
    {
        $tb = self::tbName();
        //查询数据
        $select = "SELECT COUNT(*) FROM {$tb}";
        $params = [];

        // 构建 WHERE 子句
        $whereClause = $this->buildWhereClause($input, $params);
        // 构建完整的 SQL 语句
        $select .= $whereClause;

        // 默认过滤已经试用过的机器
        $user_id = $input['user_id'] ?? 0;
        if ($filterSn && $user_id) {
            $orderTryDb = byNew::UserOrderTry()::tbName();
            $select .= " AND `sn` not in (select `sn` from `{$orderTryDb}` where `user_id` = {$user_id})";
        }

        // 执行查询并返回结果
        return by::dbMaster()->createCommand($select, $params)->queryScalar() ?? 0;
    }

  

}
