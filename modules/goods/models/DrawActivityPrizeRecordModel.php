<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 抽奖活动-抽奖记录
 */
class DrawActivityPrizeRecordModel extends CommModel
{
    // 奖品类型
    const PRIZE_TYPES = [
        'DEFAULT'         => 1, // 谢谢参与
        'POINT'           => 2, // 积分
        'COUPON'          => 3, // 优惠券
        'EXTERNAL_COUPON' => 4, // 兑换券（第三方券码）
        'PRICE'           => 5, // 现金红包
        'GOLD'            => 6, // 金币
        'FREE_AGAIN'      => 7, // 再来一次
        'CUSTOM_FORM'     => 8, // 再来一次
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_draw_activity_prize_record`";
    }

    public static $tb_fields = [
        'id', 'activity_id', 'prize_id', 'user_id', 'prize_name', 'prize_type', 'prize_value', 'prize_num', 'draw_time', 'consume_gold', 'ctime', 'utime'
    ];

    public function getPrizeRecordCountCacheKey($acId, $phone): string
    {
        return AppCRedisKeys::getPrizeRecordCountCacheKey($acId, $phone);
    }

    /**
     * 获取中奖记录 无缓存
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getPrizeRecordList(array $params, int $page = 1, int $pageSize = 20)
    {
        // 校验偏移量
        if ($page === 0) {
            $offset = 0;
        } else {
            $offset = ($page - 1) * $pageSize;
        }
        if ($offset < 0) {
            return [];
        }
        // 查询条件
        $conditions = $this->getConditions($params);
        // 查询数据
        return self::find()
            ->from(self::tbName())
            ->select(self::$tb_fields)
            ->where($conditions)
            ->limit($pageSize)->offset($offset)
            ->orderBy(['draw_time' => SORT_DESC])
            ->asArray(true)
            ->all();
    }


    public function getGoldPrizeRecordCount(array $params)
    {
        // 复用相同的查询条件
        $conditions = $this->getConditions($params);

        // 查询符合条件的总记录数
        return self::find()
                ->from(self::tbName())
                ->where($conditions)
                ->count();
    }

    /**
     * 获取中奖数
     * 返回结果（奖品ID => 数量之和）：
     *  Array
     *  (
     *     [1] => 1
     *     [2] => 1
     *  )
     * @param int $activity_id
     * @param int $phone
     * @return array
     * @throws \RedisException
     */
    public function getPrizeRecordCount(int $activity_id, int $phone): array
    {
        // 1、从 redis 缓存中获取数据
        $redis = by::redis('core');
        $redisKey = $this->getPrizeRecordCountCacheKey($activity_id, $phone);
        $cachedData = $redis->hGetAll($redisKey);

        if (!empty($cachedData)) {
            return $cachedData;
        }

        // 2、无缓存，从 DB 中获取数据
        $items = self::find()
            ->from(self::tbName())
            ->select('prize_id, SUM(prize_num) as prize_sum')
            ->where(['activity_id' => $activity_id, 'user_phone' => $phone])
            ->groupBy('prize_id')
            ->asArray()
            ->all();

        // 3、一次执行redis命令
        if ($items) {
            $redisPipeline = $redis->multi(2);
            foreach ($items as $item) {
                $redisPipeline->hSet($redisKey, $item['prize_id'], $item['prize_sum']);
            }
            $redisPipeline->expire($redisKey, 1800);
            $redisPipeline->exec();
        }

        return array_column($items, 'prize_sum', 'prize_id');
    }


    /**
     * 获取查询条件
     * @param array $params
     * @return array
     */
    public function getConditions(array $params): array
    {
        $conditions = [];

        if (isset($params['id'])) {
            $conditions['id'] = $params['id'];
        }

        if (isset($params['activity_id'])) {
            $conditions['activity_id'] = $params['activity_id'];
        }

        if (isset($params['prize_id'])) {
            $conditions['prize_id'] = $params['prize_id'];
        }

        if (isset($params['user_id'])) {
            $conditions['user_id'] = $params['user_id'];
        }

        if (isset($params['prize_type'])) {
            $conditions['prize_type'] = $params['prize_type'];
        }

        return $conditions;
    }
    
    public function getOneById(int $id): array
    {
        $res = self::find()
            ->from(self::tbName())
            ->select('id, prize_id, user_id, user_phone, prize_name, prize_type, prize_value')
            ->where(['id' => $id])
            ->asArray()
            ->one();
        return empty($res) ? [] : $res;
    }
    
    public function getListByIds(array $ids): array
    {
        $res = self::find()
            ->from(self::tbName())
            ->select('id, prize_id, user_id, user_phone, prize_name, prize_type, prize_value')
            ->where(['id' => $ids])
            ->asArray()
            ->all();
        return empty($res) ? [] : $res;
    }
    
    public function getDrawRecordRoll(array $params): array
    {
        $drawActivityId = $params['draw_activity_id'] ?? 0;
        $where = ['activity_id' => $drawActivityId];
        if (isset($params['prize_type']) && $params['prize_type'] != '') {
            $where = array_merge($where, ['prize_type' => $params['prize_type']]);
        }

        $query = self::find()->from(self::tbName());

        $res = $query->where($where)->select(['id', 'user_id', 'prize_name'])->limit(20)->orderBy('id DESC')->asArray()->all();
        return empty($res) ? [] : $res;
    }
}
