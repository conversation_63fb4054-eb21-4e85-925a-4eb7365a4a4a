<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\CUtil;
use yii\db\ActiveQuery;

/**
 * 短信记录
 */
class SmsSendRecordModel extends BaseModel
{
    // 缓存时间1小时
    const EXPIRE_TIME = 1 * 60 * 60;

    // 表字段
    public static $tb_fields = [
        'id', 'sms_code', 'phone', 'content', 'send_time'
    ];

    /**
     * 表名称
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame_log`.`t_sms_send_record`";
    }

    /**
     * 批量插入数据
     * @param $columns
     * @param $rows
     * @return array
     * @throws \yii\db\Exception
     */
    public function batchInsertLog($columns, $rows): array
    {
        $res = by::dbMaster()->createCommand()
            ->batchInsert(self::tableName(), $columns, $rows, 100)
            ->execute();
        if (!$res) {
            return [false, '未知原因，插入失败'];
        }
        // 删除缓存
        $this->delSmsSendRecordCache();
        return [true, 'ok'];
    }

    /**
     * 获取列表
     * @param array $condition
     * @param $columns
     * @return array
     */
    public function getSmsSendRecordList(array $condition, $columns): array
    {
        // 获取数据（从缓存中获取）
        $res = self::__getSmsSendRecordList($condition);
        if (empty($res)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return ['items' => Collection::wrap($res['items'])->select($columns), 'total' => $res['total']];
    }

    /**
     * 获取列表
     * @param array $condition
     * @return array
     */
    private function __getSmsSendRecordList(array $condition): array
    {
        // 从缓存中获取数据
        $redis = by::redis();
        $r_key = self::__getSmsSendRecordKey();
        // 序列化 id 的参数
        $hash_key = serialize($condition);
        $res = $redis->hGet($r_key, $hash_key);

        // 结果
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询数据
        $query = $this->__getActiveQuery($condition);
        if (isset($condition['page']) && isset($condition['page_size'])) {
            // 分页偏移量
            list($offset, $limit) = CUtil::pagination($condition['page'], $condition['page_size']);
            // 查询，从库中查询数据
            $items = $query
                ->offset($offset)->limit($limit) // 分页
                ->asArray()->all();
            // 查询数量
            $total = $query->count();
            $data = ['items' => $items, 'total' => $total];
        } else {
            // 查询，从库中查询数据
            $items = $query->asArray()->all();
            $data = ['items' => $items, 'total' => count($items)];
        }

        // 插入数据到缓存，并更新过期时间
        $redis->hSet($r_key, $hash_key, json_encode($data));
        CUtil::ResetExpire($r_key, self::EXPIRE_TIME);

        return $data;
    }

    /**
     * 获取ActiveQuery
     * @param array $condition
     * @return ActiveQuery
     */
    private function __getActiveQuery(array $condition): ActiveQuery
    {
        /**
         * @var ActiveQuery
         */
        $query = self::find();

        // id 查询
        if (!empty($condition['id'])) {
            $query->andWhere(['id' => $condition['id']]);
        }

        // sms_code 查询
        if (!empty($condition['sms_code'])) {
            $query->andWhere(['sms_code' => $condition['sms_code']]);
        }

        // phone 查询
        if (!empty($condition['phone'])) {
            $query->andWhere(['phone' => $condition['phone']]);
        }

        // 发送开始时间、结束时间 查询
        if (!empty($condition['s_send_time']) && !empty($condition['e_send_time'])) {
            $query->andWhere(['between', 'send_time', $condition['s_send_time'], $condition['e_send_time']]);
        }

        // 排序
        if (!empty($condition['column']) && !empty($condition['sort'])) {
            $query->orderBy(sprintf("`%s` %s", $condition['column'], $condition['sort']));
        } else {
            $query->orderBy('`id` DESC');

        }

        return $query;
    }

    /**
     * 删除 redis 缓存
     */
    public function delSmsSendRecordCache()
    {
        $r_key = self::__getSmsSendRecordKey();
        by::redis('core')->del($r_key);
    }

    /**
     * 获取 redis key
     * dreame|getParamsKey
     * @return string
     */
    private static function __getSmsSendRecordKey(): string
    {
        return AppCRedisKeys::getSmsSendRecordList();
    }

}