<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\common\models\BaseModel;
use yii\db\ActiveRecord;
use yii\db\Exception;

/**
 * 国补活动商品关联表模型
 *
 * @property int $id 关联ID
 * @property int $activity_id 关联活动ID
 * @property int $gid 商品ID
 * @property int $tid 分类ID
 * @property float $subsidy_ratio 国补比例
 * @property int $create_time 创建时间
 */
class SubsidyActivityGoodsModel extends BaseModel
{
    // 最大补贴比例
    const MAX_SUBSIDY_RATIO = 100.00;

    public static function tableName(): string
    {
        return '`db_dreame_goods`.`subsidy_activity_goods`';
    }

    public static function getDb()
    {
        return by::dbMaster();
    }

    public function rules()
    {
        return [
                [['activity_id', 'gid', 'tid', 'subsidy_ratio'], 'required'],
                [['activity_id', 'gid', 'tid', 'create_time'], 'integer'],
                [['subsidy_ratio'], 'number', 'min' => 0.01, 'max' => self::MAX_SUBSIDY_RATIO],
                [['subsidy_ratio'], 'match', 'pattern' => '/^\d+(\.\d{1,2})?$/', 'message' => '补贴比例最多保留两位小数'],
        ];
    }

    public function attributeLabels()
    {
        return [
                'id'            => '关联ID',
                'activity_id'   => '活动ID',
                'gid'           => '商品ID',
                'tid'           => '分类ID',
                'subsidy_ratio' => '国补比例',
                'create_time'   => '创建时间',
        ];
    }

    /**
     * 关联活动表
     */
    public function getActivity()
    {
        return $this->hasOne(SubsidyActivityModel::class, ['id' => 'activity_id']);
    }

    /**
     * 保存活动商品关联
     * @param int $activity_id
     * @param array $goods
     * @return array
     */
    public function saveActivityGoods(int $activity_id, array $goods): array
    {
        if (empty($goods)) {
            return [false, '商品列表不能为空'];
        }

        $transaction = self::getDb()->beginTransaction();
        try {
            // 删除原有关联
            self::deleteAll(['activity_id' => $activity_id]);

            $current_time = time();
            $batch_data   = [];

            foreach ($goods as $item) {
                $gid           = (int) ($item['gid'] ?? 0);
                $tid           = (int) ($item['tid'] ?? 0);
                $subsidy_ratio = (float) ($item['subsidy_ratio'] ?? 0);

                if ($gid <= 0) {
                    throw new \Exception('商品ID不能为空');
                }

                if ($tid <= 0) {
                    throw new \Exception('分类ID不能为空');
                }

                if ($subsidy_ratio <= 0 || $subsidy_ratio > self::MAX_SUBSIDY_RATIO) {
                    throw new \Exception('补贴比例必须在0.01%-100%之间');
                }

                // 验证商品是否为主机商品
                if (!$this->isHostGoods($gid)) {
                    $goods_name = by::Gmain()->GetOneByGid($gid)['name'] ?? "商品ID:{$gid}";
                    throw new \Exception("商品「{$goods_name}」不是主机商品，不能参与国补活动");
                }

                $batch_data[] = [
                        'activity_id'   => $activity_id,
                        'gid'           => $gid,
                        'tid'           => $tid,
                        'subsidy_ratio' => $subsidy_ratio,
                        'create_time'   => $current_time,
                ];
            }

            // 批量插入
            if (!empty($batch_data)) {
                self::getDb()->createCommand()->batchInsert(
                        self::tableName(),
                        ['activity_id', 'gid', 'tid', 'subsidy_ratio', 'create_time'],
                        $batch_data
                )->execute();
            }

            $transaction->commit();
            return [true, '保存成功'];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 检查商品是否为主机商品
     * @param int $gid
     * @return bool
     */
    private function isHostGoods(int $gid): bool
    {
        try {
            // 获取商品标签信息
            $tags = by::model('GtagModel', 'goods')->GetListByGid($gid);

            if (empty($tags)) {
                return false;
            }

            $tag_ids = array_column($tags, 'tid');

            // 获取非主机标签列表
            $main_tags = by::Gtag()->getMainTag();

            // 如果tag_ids中不包含主机标签，则不是主机商品
            if (empty(array_intersect($main_tags, $tag_ids))) {
                return false;
            }

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 获取活动商品列表
     * @param int $activity_id
     * @param null $tid
     * @return array
     * @throws Exception
     */
    public function getActivityGoods(int $activity_id, $tid = null): array
    {
        $goods_list = self::find()
                ->where(['activity_id' => $activity_id])
                ->andFilterWhere(['tid' => $tid])
                ->asArray()
                ->all();

        if (empty($goods_list)) {
            return [];
        }

        // 获取商品详细信息
        $gids       = array_column($goods_list, 'gid');
        $goods_info = [];

        foreach ($gids as $gid) {
            $goods = by::Gmain()->GetAllOneByGid($gid);
            if (!empty($goods)) {
                $goods_info[$gid] = $goods;
            }
        }

        // 组装返回数据
        $result = [];
        foreach ($goods_list as $item) {
            $gid = $item['gid'];
            if (isset($goods_info[$gid])) {
                $result[] = [
                        'gid'           => $gid,
                        'tid'           => $item['tid'],
                        'sku'           => $goods_info[$gid]['sku'] ?? '',
                        'name'          => $goods_info[$gid]['name'] ?? '',
                        'cover_image'   => $goods_info[$gid]['cover_image'] ?? '',
                        'price'         => $goods_info[$gid]['price'] ?? 0,
                        'subsidy_ratio' => $item['subsidy_ratio'],
                        'subsidy_price' => byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount((int)$gid, (int)$goods_info[$gid]['price'] ?? 0)
                ];
            }
        }

        return $result;
    }

    /**
     * 获取商品的国补比例
     * @param int $gid
     * @param int|null $activity_id 指定活动ID，不传则获取当前生效活动
     * @return float
     * @throws \Throwable
     */
    public function getGoodsSubsidyRatio(int $gid, int $activity_id = null): float
    {
        if ($activity_id === null) {
            // 获取当前生效的活动
            $activity = (new SubsidyActivityModel())->getCurrentActivity();
            if (!$activity) {
                return 0;
            }
            $activity_id = $activity['id'];
        }

        $goods = $this->getSubsidyGoods((int) $activity_id, $gid);

        return $goods ? (float) $goods->subsidy_ratio : 0;
    }


    /**
     * 根据活动ID和商品ID查询补贴信息
     * @param int $activity_id
     * @param int $gid
     * @return ActiveRecord|array|null
     * @throws \Throwable
     */
    public function getSubsidyGoods(int $activity_id, int $gid)
    {
        return CUtil::rememberCache(AppCRedisKeys::getSubsidyActivityGoods($activity_id, $gid), function (&$option) use ($activity_id, $gid) {
            $option['emptyExpire'] = 3600;

            return self::find()
                    ->where(['activity_id' => $activity_id, 'gid' => $gid])
                    ->one();
        });
    }

    /**
     * 根据活动ID和分类ID查询补贴商品列表
     * @param int $activity_id
     * @param int $tid
     * @return array
     * @throws \Throwable
     */
    public function getSubsidyGoodsByTid(int $activity_id, int $tid): array
    {
        $query = self::find()
                ->where(['activity_id' => $activity_id, 'tid' => $tid]);
        return CUtil::pg($query);
    }

    /**
     * 获取商品的国补金额
     * @param int $gid
     * @param float $price
     * @return string
     * @throws \Throwable
     */
    public function getGoodsSubsidyAmount(int $gid, float $price): string
    {
        $ratio = $this->getGoodsSubsidyRatio($gid);
        return bcmul($price, bcdiv($ratio, 100, 2), 2);
    }

    /**
     * 检查商品是否参与国补活动
     * @param int $gid
     * @param int|null $activity_id
     * @return bool
     * @throws \Throwable
     */
    public function isGoodsInSubsidy(int $gid, int $activity_id = null): bool
    {
        return $this->getGoodsSubsidyRatio($gid, $activity_id) > 0;
    }


    // 获取所有活动商品分类
    public function getActivityGoodsTid($activity_id): array
    {
        $result = self::find()->where(['activity_id' => $activity_id])->select(['tid'])->groupBy('tid')->asArray()->all();
        //  获取tid 名称
        $tid_name = by::Gtag()->GetTagNameMap();
        foreach ($result as $key => $value) {
            $result[$key]['name'] = $tid_name[$value['tid']] ?? '';
        }
        return $result;
    }

}
