<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 一元秒杀模型
 *
 * @property int $id 一元秒杀ID
 * @property int $activity_id 对应活动ID
 * @property int $gid 商品ID
 * @property string $order_no 订单号（创建订单时回写）
 * @property int $user_id 用户ID
 * @property int $invite_members 已邀请人数
 * @property int $status 助力状态：0-进行中，1-助力成功
 * @property int $created_at 创建时间(秒时间戳)
 * @property int $updated_at 更新时间(秒时间戳)
 */
class OneYuanSeckillModel extends CommModel
{
    // 助力状态
    const STATUS = [
            'IN_PROGRESS' => 0,
            // 进行中
            'SUCCESS'     => 1,
            // 助力成功
    ];

    // 邀请类型
    const INVITE_TYPE = [
            'ONE_YUAN_SECKILL' => 1,
            // 一元秒杀
    ];

    public $tb_fields = [
            'id',
            'activity_id',
            'gid',
            'order_no',
            'user_id',
            'invite_members',
            'status',
            'created_at',
            'updated_at'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`one_yuan_seckill`";
    }

    /**
     * 创建一元秒杀记录
     * @param int $userId
     * @param int $activityId
     * @param int $gid
     * @return array
     */
    public function createSeckill(int $userId, int $activityId, int $gid): array
    {
        $now = time();

        $data = [
                'activity_id'    => $activityId,
                'gid'            => $gid,
                'user_id'        => $userId,
                'invite_members' => 0,
                'status'         => self::STATUS['IN_PROGRESS'],
                'created_at'     => $now,
                'updated_at'     => $now,
        ];

        $sql = "INSERT INTO " . self::tbName() . "(activity_id, gid, user_id, invite_members, status, created_at, updated_at) VALUES (:activity_id, :gid, :user_id, :invite_members, :status, :created_at, :updated_at)";

        try {
            $command = by::dbMaster()->createCommand($sql, $data);
            $command->execute();
            $seckillId = by::dbMaster()->getLastInsertID();

            return [
                    true,
                    $seckillId
            ];
        } catch (\Exception $e) {
            CUtil::debug('startSeckill error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [
                    false,
                    '发起助力失败'
            ];
        }
    }

    /**
     * 根据用户ID、活动ID和商品ID获取秒杀记录
     * @param int $userId
     * @param int $activityId
     * @param int $gid
     * @return array|null
     * @throws Exception
     */
    public function getSeckillByUserActivityGid(int $userId, int $activityId, int $gid)
    {
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE user_id = :user_id AND activity_id = :activity_id AND gid = :gid 
                ORDER BY created_at DESC LIMIT 1";

        $params = [
                ':user_id'     => $userId,
                ':activity_id' => $activityId,
                ':gid'         => $gid,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryOne() ?: null;
    }

    /**
     * 根据用户ID、活动ID和商品ID获取秒杀记录
     * @param int $userId
     * @param int $activityId
     * @return array|null
     * @throws Exception
     */
    public function getSeckillByUserActivityOne(int $userId, int $activityId)
    {
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE user_id = :user_id AND activity_id = :activity_id
                ORDER BY created_at DESC LIMIT 1";

        $params = [
            ':user_id'     => $userId,
            ':activity_id' => $activityId,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryOne() ?: null;
    }

    /**
     * 根据ID获取秒杀记录
     * @param int $id
     * @return array|null
     * @throws Exception
     */
    public function getSeckillById(int $id)
    {
        $sql = "SELECT * FROM " . self::tbName() . " WHERE id = :id";
        return by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne() ?: null;
    }

    /**
     * 更新邀请人数
     * @param int $id
     * @param int $inviteMembers
     * @return bool
     */
    public function updateInviteMembers(int $id, int $inviteMembers): bool
    {
        $sql = "UPDATE " . self::tbName() . " 
                SET invite_members = :invite_members, updated_at = :updated_at 
                WHERE id = :id";

        $params = [
                ':invite_members' => $inviteMembers,
                ':updated_at'     => time(),
                ':id'             => $id,
        ];

        try {
            by::dbMaster()->createCommand($sql, $params)->execute();
            return true;
        } catch (\Exception $e) {
            CUtil::debug('updateInviteMembers error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return false;
        }
    }

    /**
     * 更新助力状态
     * @param int $id
     * @param int $status
     * @return bool
     * @throws Exception
     */
    public function updateStatus(int $id, int $status): bool
    {
        $sql = "UPDATE " . self::tbName() . " 
                SET status = :status, updated_at = :updated_at 
                WHERE id = :id";

        $params = [
                ':status'     => $status,
                ':updated_at' => time(),
                ':id'         => $id,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 更新订单号
     * @param int $id
     * @param string $orderNo
     * @return bool
     * @throws Exception
     */
    public function updateOrderNo(int $id, string $orderNo): bool
    {
        $sql = "UPDATE " . self::tbName() . " 
                SET order_no = :order_no, updated_at = :updated_at 
                WHERE id = :id";

        $params = [
                ':order_no'   => $orderNo,
                ':updated_at' => time(),
                ':id'         => $id,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            CUtil::debug('updateOrderNo error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return false;
        }
    }

    /**
     * 获取用户当前进行中的秒杀活动
     * @param int $userId
     * @return array
     * @throws Exception
     */
    public function getUserInProgressSeckills(int $userId): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE user_id = :user_id AND status = :status 
                ORDER BY created_at DESC";

        $params = [
                ':user_id' => $userId,
                ':status'  => self::STATUS['IN_PROGRESS'],
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }

    /**
     * 检查用户在指定活动和商品下是否有进行中的秒杀
     * @param int $userId
     * @param int $activityId
     * @param int $gid
     * @return bool
     * @throws Exception
     */
    public function hasInProgressSeckill(int $userId, int $activityId, int $gid): bool
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
                WHERE user_id = :user_id AND activity_id = :activity_id AND gid = :gid";

        $params = [
                ':user_id'     => $userId,
                ':activity_id' => $activityId,
                ':gid'         => $gid
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }

    /**
     * 获取用户在指定活动中的进行中秒杀活动
     * @param int $userId
     * @param int $activityId
     * @return array
     * @throws Exception
     */
    public function getUserInProgressSeckillsByActivity(int $userId, int $activityId): array
    {
        $sql = "SELECT * FROM " . self::tbName() . " 
                WHERE user_id = :user_id AND activity_id = :activity_id 
                ORDER BY created_at DESC";

        $params = [
                ':user_id'     => $userId,
                ':activity_id' => $activityId,
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryAll() ?: [];
    }


    public function getOneYuanSeckillInfo($orderNo)
    {
        if (empty($orderNo)) {
            return [];
        }
        return self::find()->where(['order_no' => $orderNo])->asArray()->one() ?: [];
    }

    /**
     * 查找用户在某活动下，状态成功且已有订单号的最新记录（用于绑定切换商品前置条件）
     */
    public function getLatestCompletedSeckillWithOrderByActivity(int $userId, int $activityId)
    {
        $sql = "SELECT * FROM " . self::tbName() . "
                WHERE user_id = :user_id AND activity_id = :activity_id 
                  AND status = :status AND order_no IS NOT NULL
                ORDER BY updated_at DESC, id DESC LIMIT 1";

        $params = [
            ':user_id'     => $userId,
            ':activity_id' => $activityId,
            ':status'      => self::STATUS['SUCCESS'],
        ];

        return by::dbMaster()->createCommand($sql, $params)->queryOne() ?: null;
    }

    /**
     * 更新绑定商品 gid（仅用于特定业务）
     */
    public function updateGid(int $id, int $gid): bool
    {
        $sql = "UPDATE " . self::tbName() . " SET gid = :gid, updated_at = :updated_at WHERE id = :id  and order_no is null";
        $params = [
            ':gid'        => $gid,
            ':updated_at' => time(),
            ':id' => $id
        ];
        try {
            $cmd = by::dbMaster()->createCommand($sql, $params);
            return $cmd->execute() > 0;
        } catch (\Exception $e) {
            CUtil::debug('updateGid error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return false;
        }
    }

    /**
     * 商品条件验证：
     * 商品组合(gcombines)必须只包含一件商品(数组长度为1)
     * 商品的购买数量(num)必须为1
     * 商品ID(gid)必须有效(不为空)
     * 秒杀资格验证：
     * 该秒杀记录的ID必须存在且匹配
     * 用户ID必须匹配
     * 商品ID必须匹配
     * 助力状态必须为成功(STATUS['SUCCESS'])
     * 订单号必须为空(order_no is null)，表示未被购买过
     * 只有同时满足以上所有条件，该用户才能进行一元秒杀购买。这些验证确保了：
     * 一个秒杀活动只能购买一件商品
     * 必须是助力成功的用户才能购买
     * 同一个秒杀活动不能重复购买
     * @param $seckillId
     * @param $userId
     * @param $gcombines
     * @return bool
     * @throws Exception
     */
    public function checkCanBuy($seckillId, $userId, $gcombines): bool
    {

        //如果只有一条且能获得到gid
        if (count($gcombines) != 1)
            return false;

        //数量为一验证
        if ($gcombines[0]['num'] != 1)
            return false;

        $gid = $gcombines[0]['gid'] ?? 0;
        if (empty($gid))
            return false;

        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . " 
        WHERE id = :id AND user_id = :user_id AND gid = :gid AND status = :status AND order_no is null";

        $params = [
                ':id'       => $seckillId,
                ':user_id'  => $userId,
                ':gid'      => $gid,
                ':status'   => self::STATUS['SUCCESS'],
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return ($result['count'] ?? 0) > 0;
    }
} 