<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\exceptions\ProductMatrixException;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class GoodsCategoryModel extends CommModel
{

    public static function tableName(): string
    {
        return '`db_dreame_goods`.`t_goods_category`';
    }

    public static $tb_fields = [
            'id', 'name', 'sort', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
            'NO'  => 0,
            'YES' => 1
    ];

    private function getCategoryListKey(): string
    {
        return AppCRedisKeys::getCategoryListKey();
    }

    public function __delCategoryListKey()
    {
        $redis    = by::redis();
        $redisKey = $this->getCategoryListKey();
        $redis->del($redisKey);
    }


    /**
     * @param $data
     * @return array
     * @throws ProductMatrixException
     * 删除分类
     */
    public function deleteCategory($data): array
    {
        $id       = $data['id'] ?? 0;
        $category = self::find()->where(['id' => $id, 'is_del' => self::IS_DEL['NO']])->one();
        if (!$category) {
            return [false, '分类不存在'];
        }

        $category->is_del = self::IS_DEL['YES'];
        $category->dtime  = time();

        if (!$category->save()) {
            throw new ProductMatrixException('删除失败: ' . implode(', ', $category->errors));
        }

        $this->__delCategoryListKey();
        return [true, $category->attributes];
    }

    public function getList(): array
    {
        $redis    = by::redis();
        $redisKey = $this->getCategoryListKey();

        // 尝试从 Redis 获取缓存数据
        $cachedData = $redis->get($redisKey);
        if ($cachedData) {
            // 如果缓存中存在数据，直接返回
            return json_decode($cachedData, true);
        }

        // 如果缓存中没有数据，从数据库查询
        $query = self::find()
                ->select(['id', 'name', 'sort'])                  // 只查询需要的字段
                ->where(['is_del' => self::IS_DEL['NO']])         // 查询未删除的分类
                ->orderBy(['sort' => SORT_DESC]);                 // 按照排序字段降序排列

        // 获取分类数据
        $data = $query->asArray()->all();

        // 将查询到的数据存入 Redis
        $redis->set($redisKey, json_encode($data), ['EX' => empty($data) ? 10 : 7200]);

        return $data;
    }

    /**
     * @param array $deleteIds
     * 批量删除
     */
    public function deleteByIds(array $deleteIds)
    {
        $this->__delCategoryListKey();
        self::updateAll(['is_del' => self::IS_DEL['YES'], 'dtime' => time()], ['id' => $deleteIds]);
    }

    /**
     * @param array $categoryAdd
     *
     * @throws \yii\db\Exception
     * 批量新增
     */
    public function batchInsertCategory(array $categoryAdd)
    {
        $this->__delCategoryListKey();
        // 执行批量插入操作，假设使用 Yii2 的批量插入方法
        by::dbMaster()->createCommand()->batchInsert(self::tableName(), ['name', 'sort', 'ctime'], $categoryAdd)->execute();
    }

    /**
     * @param array $categoryUpdate
     * @throws \yii\db\Exception
     * 批量更新
     */
    public function batchUpdateCategory(array $categoryUpdate)
    {
        $this->__delCategoryListKey();
        // 执行批量更新操作
        $updateSql = CUtil::batchUpdate($categoryUpdate, 'id', self::tableName());
        by::dbMaster()->createCommand($updateSql)->execute();
    }


}
