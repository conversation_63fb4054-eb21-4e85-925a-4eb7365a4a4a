<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 动销系统订单表
 */
class ErpOrderModel extends CommModel
{
    
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_erp_order`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function getList($data){
        $tb = $this->tbName();
        $where           = "1=:init";
        $params[':init'] = 1;

        if (isset($data['status'])){
            $where .= " AND `status` = :status";
            $params[':status'] = $data['status'];
        }
        if (isset($data['points_grant_status'])){
            $where .= " AND `points_grant_status` = :points_grant_status";
            $params[':points_grant_status'] = $data['points_grant_status'];
        }
        if (isset($data['pay_time'])){
            $where .= " AND `pay_time` < :pay_time";
            $params[':pay_time'] = $data['pay_time'];
        }
        if (isset($data['uid'])){
            $where .= " AND (uid is not null AND uid <> '') ";
        }

        // 构建 SQL 查询
        $sql = "SELECT * FROM {$tb} WHERE {$where} ";
        // 执行查询
        $aData = by::dbMaster()
                ->createCommand($sql)
                ->bindValues($params)
                ->queryAll();

        return $aData;
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData, $gData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();

            // 保存商品表数据
            foreach ($gData as $v) {
                list($status,$msg) = by::ErpOrderProductModel()->SaveLog($v,$aData['pay_time']);
                if (!$status) {
                    throw new \Exception($msg);
                }
            }
            $trans->commit();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'E3Order.info');
            return [false, '保存操作失败'];
        }
    }
    public function updateAllLog($order_no,$updateData,$gData = [],$pay_time = 0){
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();
        try {
            // 更新主表数据
            $db->createCommand()->update($tb,$updateData,['order_no' => $order_no])->execute();

            // 保存商品表数据
            if (count($gData) > 0){
                by::ErpOrderProductModel()->deleteByOrderNo($order_no,$pay_time);
                foreach ($gData as $v) {
                    list($status,$msg) = by::ErpOrderProductModel()->SaveLog($v,$pay_time);
                    if (!$status) {
                        throw new \Exception($msg);
                    }
                }
            }
            
            $trans->commit();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'E3Order.info');
            return [false, '保存操作失败'];
        }
    }

    public function getOrderByNo($order_no){
        $item = self::find()
            ->where(['order_no' => $order_no])
            ->one();
        return $item ? $item->toArray() : [];
    }

    public function updateStatusByOrderNo($order_no, $status){
        $item = self::findOne(['order_no' => $order_no]);
        if($item){
            $item->status = $status;
            return $item->save();
        }else{
            return false;
        }
    }
}