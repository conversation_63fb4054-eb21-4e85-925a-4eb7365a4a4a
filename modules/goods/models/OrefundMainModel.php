<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 退款表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\Erp;
use app\components\ErpNew;
use app\components\PointCenter;
use app\jobs\CancelTradeInOrderJob;
use app\jobs\SyncPointGrowJob;
use app\jobs\UserShopMoneyJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\pay\MpPayModel;
use app\modules\main\services\refund\GoodsOrder;
use app\modules\main\services\refund\Order;
use yii\db\Exception;
use app\components\AliZhima;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\SalesCommissionJob;
use app\modules\back\services\GroupPurchaseService;

class OrefundMainModel extends CommModel {

    CONST REFUND_EXPIRE        = YII_ENV_PROD ? 7 * 24 * 3600 : 1800; //已完成订单可退款时间窗口
    CONST ImageLimit           = 3;     //商品图片数量上限
    CONST PASS_EXPIRE          = YII_ENV_PROD ? 600 : 60;   //可同意退款时间窗口
    CONST REFUND_LIMIT_TIME    = 1800;

    public $tb_fields   = [
        'id','refund_no','order_no','user_id','ctime','status'
    ];

    CONST STATUS = [
        'ALL'           => -1,
        'AUDIT'         => 1000,     //平台待审核
        'P_PASS'        => 1010,     //平台审核通过
        'P_REJECT'      => 1020,     //平台审核拒绝
        'CANCELED'      => 100,      //已取消
        'SUCCESS'       => 20000000, //退款成功
    ];

    CONST STATUS_NAME = [
        -1           => '未知',
        1000         => '待审核',     //平台待审核
        1010         => '通过',       //平台审核通过
        1020         => '拒绝',       //平台审核拒绝
        100          => '已取消',      //已取消
        20000000     => '退款完成',    //退款成功
    ];

    //退款原因
    const R_TYPE = [
        1 => '7天无理由退货',
        2 => '缺货',
        3 => '未按约定时间发货',
        4 => '商品质量问题',
        5 => '不喜欢/不想要',
        6 => '商品价格问题（买贵、降价等）',
        7 => '商品信息拍错（规格/尺码/颜色等）',
        8 => '地址/电话信息填写错误',
        9 => '其他',
    ];


    //退款方式
    const M_TYPE = [
        1 => '我要退款',
        2 => '我要退货退款',
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_orefund_main`";
    }

    /**
     * @param $refund_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($refund_no): string
    {
        return AppCRedisKeys::getOneRefundMain($refund_no);
    }


    /**
     * @param $refund_no
     * @return int
     * 缓存清理
     */
    private function __delInfoCache($refund_no): int
    {
        $r_key = $this->__getOneInfoKey($refund_no);

        return  by::redis('core')->del($r_key);
    }

    /**
     * @param int $user_id
     * @param array $arr
     * @return array
     * @throws Exception
     * 申请退款
     */
    public function ApplyRefund(int $user_id, array $arr, $check_try_time = true)
    {
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        list($lock,$msg) = CUtil::payLock($user_id);
        if($lock){
            CUtil::json_response(-1,$msg);
        }

        $ctime      = intval(START_TIME);
        $order_no   = $arr['order_no']  ?? '';
        $m_type     = $arr['m_type']    ?? 0;
        $r_type     = $arr['r_type']    ?? 0;
        $describe   = $arr['describe']  ?? "";
        $og_ids     = $arr['og_ids']    ?? ''; //uo_g表id["1"]
        $images     = $arr['images']    ?? "";

        // 新增物流信息数据
        $mail_no = $arr['mail_no'] ?? ""; //退货物流单号
        $express_code = $arr['express_code'] ?? ""; //退货物流公司编码
        $express_name = $arr['express_name'] ?? ""; //退货物流公司名称

        if(substr_count($images,"|") > self::ImageLimit) {
            return [false,"图片数量最多".self::ImageLimit."张"];
        }

        $m_type     = CUtil::uint($m_type);
        $r_type     = CUtil::uint($r_type);

        if ( !array_key_exists($m_type, self::M_TYPE) ) {
            return [false, '服务类型有误'];
        }
        if ( !array_key_exists($r_type, self::R_TYPE) ) {
            return [false, '退款原因有误'];
        }

        if (mb_strlen($describe) > 500) {
            return [false, '描述最长500字'];
        }

        $og_ids     = (array)json_decode($og_ids, true);
        $og_ids     = array_filter($og_ids);
        if (empty($og_ids)) {
            return [false, '请选择退款数据(1)'];
        }

        //todo 判断所选数据是否正常
        $mOmain     = by::Omain();
        $mOgoods    = by::Ogoods();

        $oGoods     = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);

        //todo 积分商城类型限制
        $oGoodsType = array_unique(array_filter(array_column($oGoods,'goods_type')));
        if(empty($oGoodsType) || count($oGoodsType)>1){
            return [false, '非同类型商品不允许退款~'];
        }
        $oGoodsType = $oGoodsType[0];

        $oGoods     = array_column($oGoods, 'status','id');
        if (array_diff($og_ids, array_keys($oGoods))) {
            return [false, '请选择正确的退款数据(1)'];
        }


        $oInfo      = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        if (empty($oInfo)) {
            return [false, '订单信息异常'];
        }

        $lockOldErp = CUtil::omsLock($user_id,$order_no);

        //订单简单状态
        list($_, $rstatus)  = $mOmain->SplitOrderStatus($oInfo['status']);
        if ($m_type == 2 && $rstatus == $mOmain::ORDER_STATUS['WAIT_SEND']) {
            return [false, '未发货订单请选择仅退款'];
        }

        //商品在途中也可以申请仅退款
//        if ($m_type == 1 && $rstatus >= $mOmain::ORDER_STATUS['WAIT_RECEIVE']){
//            return [false, '已发货的订单请选择退货退款'];
//        }


        $can_status = $mOmain->GetCanActStatus();
        // 去掉优惠券类型限制
        if ( !in_array($oInfo['status'], $can_status)) {
            return [false, '该订单不可退款'];
        }

        //判断是否为先试后买订单，如果是先试后买订单，且E3+已经收货，不允许再次创建退款
        $omianInfo = by::Omain()->getInfoByOrderNo($user_id, $order_no);

        //订单完成7天后不能退款(退款时间兼容)
        $refundValue = by::memberCenterModel()->getUserRightList($user_id, 'return_exchange');
        !YII_ENV_PROD && $refundValue = 180;
        $refundExpireTime = empty($refundValue) ? self::REFUND_EXPIRE : ($refundValue * (YII_ENV_PROD ? 24*3600 : 24*3600));

        if ( !empty($oInfo['finish_time']) && bcsub($ctime, $oInfo['finish_time']) > $refundExpireTime && $omianInfo['type'] != by::Omain()::USER_ORDER_TYPE['BAT'] ) {
            return [false, '该订单已超时不可退款'];
        }

        //判断是否有活动订单未退款，如果有不允许退款
        if(!by::activityConfigModel()->activityRefundRule($user_id,$order_no)){
            return [false, '此订单与618活动积分秒杀订单绑定，请先取消或退款积分订单~'];
        }

        //判断是否为先试后买订单，如果是先试后买订单，且E3+已经收货，不允许再次创建退款
        $omianInfo = by::Omain()->getInfoByOrderNo($user_id, $order_no);
        $orderTry = null;
        if ($omianInfo['type'] == by::Omain()::USER_ORDER_TYPE['BAT']) {
            $orderTry = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no),CUtil::buildCondition('user_id', '=', $user_id)]);
            //0.已有退款单判断
            if ($orderTry['refund_no']) {
                return [false, '该订单已有退款单，不允许再次创建退款~'];
            }
            if ($check_try_time) {
                //1.超时判断
                $acInfo = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($orderTry['ac_id']);
                //退机有效期
                if (!empty($orderTry['arrival_time'])) {
                    $activeTime = $orderTry['arrival_time'] + ($acInfo['validity'] + $acInfo['return_period']) * byNew::UserOrderTry()::TRY_TIME_PERIOD;
                    if ($activeTime < $ctime) {
                        return [false, '订单信息发生变更，请返回重试~'];
                    }
                }
                //2.状态判断
                if ($orderTry['try_status'] >= byNew::UserOrderTry()::TRY_STATUS['BACK'] || $orderTry['try_status'] <= byNew::UserOrderTry()::TRY_STATUS['WAIT_PAY']) {
                    return [false, '先试后买订单状态已变更，不允许创建退款~'];
                }
            }else{
                // 先试后买订单只有在待扣款无法改变的情况下客服才能创建退款订单
                if ($orderTry['try_status'] != byNew::UserOrderTry()::TRY_STATUS['WAIT_DEDUCT']){
                    return [false, '只有在待扣款无法改变的情况下客服才能创建退款订单~'];
                }
            }
        }

        //生成退款单号
        $refund_no  = $mOmain->CreateOrderNo($ctime);

        $tb         = self::tbName();
        $db         = by::dbMaster();

        //待修改的订单状态
        $complete   = false;
        if (count($og_ids) == count($oGoods)) {
            $complete   = true;
        }
        $next_st    = $mOmain->SetOrderStatus($oInfo['status'], $mOmain::ORDER_STATUS['REFUNDING'], true, $complete);

        $trans      = $db->beginTransaction();

        try {
            //插入退款主表数据
            $save       = [
                'refund_no'     => $refund_no,
                'order_no'      => $order_no,
                'user_id'       => $user_id,
                'm_type'        => $m_type,
                'ctime'         => $ctime,
            ];
            $db->createCommand()->insert($tb, $save)->execute();

            //插入退款表数据
            $save = [
                'order_no'      => $order_no,
                'refund_no'     => $refund_no,
                'm_type'        => $m_type,
                'r_type'        => $r_type,
                'describe'      => $describe,
                'images'        => $images,
                'ctime'         => $ctime,
                'ostatus'       => $oInfo['status'],
                'mail_no'       => $mail_no,
                'express_code'  => $express_code,
                'express_name'  => $express_name,
            ];
            by::Orefund()->SaveLog($user_id, $save);

            //插入退款商品表数据
            $save_g     = [
                'refund_no' => $refund_no,
                'user_id'   => $user_id,
                'og_ids'    => $og_ids,
                'goods_type'   => $oGoodsType,
            ];
            by::Orgoods()->SaveLog($order_no, $save_g);

            //更改订单状态
            if ($next_st != $oInfo['status']) {
                list($s, $m) = $mOmain->SyncInfo($user_id, $order_no, $next_st);
                if (!$s) {
                    throw new \Exception($m);
                }
            }

            //更改订单商品状态
            list($s, $m) = $mOgoods->UpdateStatus($user_id, $order_no, $og_ids, $mOmain::ORDER_STATUS['REFUNDING']);
            if (!$s) {
                throw new \Exception($m);
            }

            // 先试后买订单保存退款信息 2021-07-06
            if($orderTry){
                $saveTry = [
                    'user_id'   => $user_id,
                    'order_no'  => $order_no,
                    'refund_no' => $refund_no,
                    'utime'     => time()
                ];
                list($sta,$ms) = byNew::UserOrderTry()->SaveLog($saveTry);
                if(!$sta){
                    throw new \Exception($ms);
                }
            }

            /**
             * shipping_status 0-初始1-预分配缺货处理中 2-已完成预分配 3-已通知配货4-拣货中(已分配拣货任务) 5-已完成拣货 6-已发货 7-已出库 9-取消
             * shipping_status >= 3 时置无效不了，会打日志
             * todo 订单状态（仅退款未发货-整张订单置无效）
             */
            if (!$lockOldErp && $m_type == 1) {
                list($s, $shipping_status) = $this->__isDelivery($order_no, $oInfo['status']);

                if(!$s){
                    //todo 塞入待执行数据库
                    by::OrderZwx()->saveLog($order_no,$user_id,$oInfo['status']);
                }

                if ($s && $shipping_status < 6) {
                    //置无效
                    list($s, $m) = Erp::factory()->orderZwx($order_no);
                    if (!$s) {
                        CUtil::debug("{$order_no}|{$m}", "zwx.refund");
                    }
                }
            }

            $trans->commit();

            // 以旧换新订单（异步取消）
            \Yii::$app->queue->push(new CancelTradeInOrderJob([
                'user_id'  => $user_id,
                'order_no' => $order_no
            ]));

            //佣金订单增加退款中
            byNew::SalesCommissionModel()->patch(['order_no'=>$order_no],['status'=>10000000,'utime'=>time()]);

            //todo 清缓存
            $this->__delInfoCache($refund_no);

            // //todo 订单同步crm
            // Crm::factory()->push($user_id,'order',['user_id'=>$user_id,'order_no'=>$order_no]);
            // Crm::factory()->push($user_id,'orderLine',['user_id'=>$user_id,'order_no'=>$order_no]);
            //
            // //todo 退款crm
            // Crm::factory()->push($user_id,'refund',['user_id'=>$user_id,'refund_no'=>$refund_no]);
            // Crm::factory()->push($user_id,'refundLine',['user_id'=>$user_id,'refund_no'=>$refund_no]);

            //todo 推送erp-new
            $m_type == 1 && $this->refundPushOms($m_type,$user_id,$refund_no,$order_no);
            //todo 已发货退款
            $sendData = [
                '告警信息:该订单已经发货，用户申请仅退款，请关注OMS发货情况，及时拦截',
                '订单号：'.$order_no,
                '退款订单号：'.$refund_no,
                '用户ID：'.$user_id,
            ];
            $m_type == 1 && $oInfo['status'] == by::Omain()::ORDER_STATUS['WAIT_RECEIVE'] && CUtil::sendMsgToFs($sendData);
            $m_type == 1 && $oInfo['status'] == by::Omain()::ORDER_STATUS['WAIT_RECEIVE'] && CUtil::debug(json_encode($sendData,320),'record.order_refund');

            return [true, $refund_no];
        } catch (\Exception $e) {

            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.refund');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $order_no
     * @param $order_status
     * @param bool $apply 只在申请时用
     * @return array
     * 订单是否为未发货状态
     */
    private function __isDelivery($order_no, $order_status)
    {
        $mOmain             = by::Omain();

        $d_status           = 6; //发货状态分割点

        list($_, $rstatus)  = $mOmain->SplitOrderStatus($order_status);

        if ($rstatus == $mOmain::ORDER_STATUS['WAIT_SEND']) {

            list($s, $ret)  = Erp::factory()->getOrderList($order_no);

            if ( !$s || empty($ret) ) {
                return [false, '获取OMS订单状态失败'];
            }

            $d_status   = $ret['shipping_status'];
        }

        return [true, $d_status];
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @param $status
     * @param array $arr
     * @return array
     * @throws Exception
     * 状态结果同步
     */
    public function SyncInfo($user_id,$refund_no,$status,$arr=[])
    {

        if($user_id <= 0 || empty($refund_no) || !in_array($status,self::STATUS)) {
            return [false,"非法参数"];
        }

        $db       = by::dbMaster();
        $trans    = $db->beginTransaction();
        try{

            $tb_main = self::tbName();
            $ret     = $db->createCommand()->update(
                $tb_main,
                ['status'=>$status],
                ['refund_no' => $refund_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new \Exception("无数据更新(1)~");
            }

            $arr['status'] = $status;

            $tb_info = by::Orefund()::tbName($user_id);

            $ret     = $db->createCommand()->update(
                $tb_info,
                $arr,
                ['refund_no' => $refund_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new \Exception("无数据更新(2)");
            }

            $trans->commit();

            //todo 清理退款表缓存
            by::Orefund()->DelCache($user_id, $refund_no);

            return [true,"OK"];

        } catch (\Exception $e) {
            $trans->rollBack();

            return [false,$e->getMessage()];
        }
    }

    /**
     * @param string $order_no
     * @param string $user_iden 用户身份（id或手机号）
     * @param int $status
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 管理后台展示
     */
    public function GetList($order_no='', $user_iden='', $status=-1,$order_time=[],$goods_name = '',$page=1, $page_size=50)
    {  // 默认不包含先试后买订单，先试后买订单不包含优惠券
        $page                = CUtil::uint($page,1);

        $tb                  = self::tbName();

        list($offset)        = CUtil::pagination($page,$page_size);
        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time,$goods_name);

        $sql                 = "SELECT `main`.`user_id`,`main`.`order_no`,`main`.`refund_no` FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
         WHERE  {$where} ORDER BY `main`.`id` DESC LIMIT {$offset},{$page_size}";
        $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

        return $aData;
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param array $order_time
     * @return int
     * @throws Exception
     * 订单总数
     */
    public function GetListCount($order_no='', $user_iden='', $status=-1,$order_time=[],$goods_name = '')
    {
        $tb                  = self::tbName();

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time,$goods_name);
        $sql                 = "SELECT COUNT(*) FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
         WHERE {$where}";
        $command             = by::dbMaster()->createCommand($sql,$params);
        $count               = $command->queryScalar();

        return intval($count);
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param array $order_time
     * @return array
     * @throws Exception
     * 规范化查询条件
     */
    private function __getCondition($order_no='', $user_iden='', $status=-1,$order_time=[],$goods_name = ''): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        $where               .= " AND `try`.`order_no` is null";

        //注意范围
        if(!empty($order_no)) {
            $where               .= " AND `main`.`order_no`=:order_no";
            $params[":order_no"]  = $order_no;
        }
        if (!empty($goods_name)) {
            // 获取积分商品和上架/下架商品信息
            $waresGid = by::GoodsMainModel()->GetList(['name' => $goods_name], 1, 9999999);
            $gids = by::Gmain()->GetList(1, 9999999, '', -1, -1, $goods_name);

            // 合并订单号
            $orderNos = array_merge(
                by::Ogoods()->getAllOrderByGids($waresGid, 2),
                by::Ogoods()->getAllOrderByGids($gids)
            );

            // 如果有订单号，则构建条件
            if (!empty($orderNos)) {
                $orderNos = implode("','", $orderNos);
                $where .= " AND `main`.`order_no` IN ('{$orderNos}')";
            }else{
                $where               .= " AND `main`.`order_no`=:order_no";
                $params[":order_no"]  = '-1';
            }
        }

        if(!empty($user_iden)) {
            $user_iden = (int)$user_iden;
            if (strlen($user_iden) == 11) {
                $uids = by::Phone()->GetUidsByPhone($user_iden);
                if(!empty($uids)) {
                    $uids    = implode(',',$uids);
                    $where  .= " AND `main`.`user_id` IN ({$uids})";
                }else{
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where  .= " AND `main`.`user_id` = -1";
                }
            } else {
                $where  .= " AND `main`.`user_id` = {$user_iden}";
            }

        }

        if($status >= 0) {
            $where                 .= " AND `main`.`status` = :status";
            $params[":status"]      = $status;
        }

        if(!empty($order_time['st']) && !empty($order_time['ed'])) {
            $where               .= " AND `main`.`ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"]  = $order_time['st'];
            $params[":order_ed"]  = $order_time['ed'];
        }
        return [$where,$params];
    }

    /**
     * @param $arr
     * @return array
     * @throws Exception
     * 退款单审核
     */
    public function Audit($arr): array
    {
        $refund_no = $arr['refund_no'] ?? '';
        $order_no  = $arr['order_no'] ?? '';
        $status    = $arr['status'] ?? 0;
        $a_reason  = $arr['a_reason'] ?? '';
        $force     = $arr['force'] ?? '';
        $is_complete = $arr['is_complete'] ?? 1;
        $is_check = $arr['is_check'] ?? true;

        if (empty($refund_no) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($refund_no, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if (mb_strlen($a_reason) > 500) {
            return [false, '拒绝原因最长500字'];
        }

        $status = CUtil::uint($status);
        if(!in_array($status,[self::STATUS['P_PASS'],self::STATUS['P_REJECT']])) {
            return [false, '参数错误(1)'];
        }

        $rm_info = $this->GetInfoByRefundNo($refund_no);
        if (empty($rm_info)) {
            return [false, '退款订单异常'];
        }

        $user_id = $rm_info['user_id'];
        $r_info  = by::Orefund()->CommPackageInfo($user_id,$refund_no,false,true);

        if ($r_info['status'] != self::STATUS['AUDIT']) {
            return [false, '退款订单异常(2)'];
        }

        //todo OMS没有返回禁止退款
        $rback =  CUtil::uint($r_info['rback'] ?? 0);
        $ostatus = $r_info['ostatus'] ?? 0;
        //oms 新旧锁
        $lockOldErp = CUtil::omsLock($user_id,$order_no);
        if($lockOldErp && empty($force) && empty($rback) && $status == self::STATUS['P_PASS']){
            return [false,'OMS暂无仅退款信息，不允许通过！'];
        }


        //记录生成后10分钟内无法操作同意退款
        $d_time     = $r_info['ctime'] + self::PASS_EXPIRE - time();
        if ($status == self::STATUS['P_PASS'] && $d_time > 0 && $is_check) {
            return [false, "请{$d_time}秒后再操作"];
        }


        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            list($s, $m) = $this->SyncInfo($user_id, $refund_no, $status, ['a_reason' => $a_reason]);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 更改订单状态
            list($s, $ret) = $this->__auditByStatus($user_id, $order_no, $refund_no, $status, $r_info['ostatus']);
            if (!$s) {
                throw new MyExceptionModel($ret);
            }

            if ($status == self::STATUS['P_PASS']) {
                //todo 获取最新支付方式，选择退款渠道
                $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
                if (empty($aLog)) {
                    throw new MyExceptionModel('无法获取支付流水，数据有误！');
                }


                //todo 如果没有实付金额，不予退款直接成功
                // 实付金额
                $price = CUtil::uint($aLog['price'] ?? 0);
                // 退款金额，因为此接口都是MALL，所以退款费用为$refund_amount
                $refund_amount = $this->getRefundAmount($user_id, $order_no, $refund_no, $ret['r_freight']);
                if ($price > 0 && $refund_amount > 0) {
                    $payType = $aLog['pay_type'] ?? '';
                    $trade_no = $aLog['tid'] ?? '';  // 交易单号
                    if($payType == by::Omain()::PAY_BY_WX || $payType == by::Omain()::PAY_BY_WX_APP){
                        //todo 微信执行退款
                        list($s, $m) = by::wxPay()->refund($user_id, $order_no, $refund_no, $ret['r_freight'], by::WxPay()::SOURCE['MALL'], $payType);
                        if (!$s) {
                            throw new MyExceptionModel($m);
                        }
                    } elseif ($payType == by::Omain()::PAY_BY_WX_H5) {
                        //todo 微信H5执行退款
                        list($s, $m) = by::wxH5Pay()->refund($user_id, $order_no, $refund_no, $ret['r_freight']);
                        if (!$s) {
                            throw new MyExceptionModel($m);
                        }
                    }elseif($payType == by::Omain()::PAY_BY_ALIPAY) {//支付宝退款
                        // 获取退款金额（普通订单）
                        list($s, $m) = AliPayModel::getInstance()->refund($order_no, $refund_no, $refund_amount);
                        if (!$s) {
                            throw new MyExceptionModel($m);
                        }
                    }elseif ($payType == by::Omain()::PAY_BY_MP_WX || $payType == by::Omain()::PAY_BY_MP_ALIPAY||$payType == by::Omain()::PAY_JD_BAITIAO||$payType == by::Omain()::PAY_JD_BAITIAO_APP||$payType == by::Omain()::PAY_JD_BAITIAO_PC||$payType == by::Omain()::PAY_JD_BAITIAO_H5||$payType == by::Omain()::PAY_BY_MP_WEB_WX_H5||$payType == by::Omain()::PAY_BY_MP_WEB_ALIPAY_H5) {//支付中台（微信/支付宝/京东白条）
                        // 获取退款金额（普通订单）
                        $attach = [
                            'user_id'    => $user_id,
                            'uid'        => by::Phone()->getUidByUserId($user_id),
                            'order_no'   => $order_no,
                            'order_type' => Order::ORDER_TYPE['GOODS'], // 商品订单
                            'pay_type'   => $payType,                   // 支付方式
                        ];
                        list($s, $m) = MpPayModel::getInstance()->refund($trade_no, $refund_no, $refund_amount, $attach);
                        if (!$s) {
                            throw new MyExceptionModel($m);
                        }
                    }elseif ($payType == by::Omain()::PAY_BY_ALI_ZHIMA) {//直接认为退款完成
                        $successStatus = by::OrefundMain()::STATUS['SUCCESS'];
                        $this->SyncInfo($user_id, $refund_no, $successStatus, ['rtime' => time(), 'price' => $price]);
                    }else{
                        throw new MyExceptionModel('支付渠道有误！无法退款');
                    }
                }else{
                    //实付金额小于0，退还优惠券
                    if($price <= 0 || $refund_amount <= 0){ // 退还优惠券
                        $goodsOrderService = new GoodsOrder($user_id, $order_no, $refund_no, $refund_amount, '', '', '');
                        // 3.退还优惠券
                        // 兑换券一样退还
                        $goodsOrderService->returnCard($user_id, $order_no, $refund_no, null,'coupon_id');
                        // 4.退还消费券
                        $goodsOrderService->returnCard($user_id, $order_no, $refund_no, null,'consume_id');
                        // 团购订单退款消息推送
                        GroupPurchaseService::getInstance()->sendRefundMessage($order_no);
                    }
                    //订单支付金额为0，修改订单状态
                    $successStatus = by::OrefundMain()::STATUS['SUCCESS'];
                    $this->SyncInfo($user_id,$refund_no,$successStatus, ['rtime' => time(), 'price' => 0]);
                }

                $oInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);

                // 是否使用礼品卡
                if (!empty($oInfo['gift_card_ids'])) { // 使用了礼品卡
                    list($s, $m) = byNew::GiftCardExpendRecord()->refund($user_id, $order_no, $refund_no);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }

                //退款审核通过，同步退定金
                $depositOrderNo = $oInfo['deposit_order_no'] ?? '';
                CUtil::debug('尾款订单|' . $order_no . '定金订单|' . $depositOrderNo, 'orefund.deposit');
                if ($depositOrderNo) {
                    list($s, $msg) = by::OrefundDepositMain()->_judgeRefundDeposit($user_id, $depositOrderNo);
                    if (!$s) {
                        throw new MyExceptionModel($msg . '|' . $depositOrderNo);
                    }
                }

                if ($price == 0 || $refund_amount == 0) {
                    // 处理纯积分订单
                    PointCenter::factory()->refundPush($user_id, $refund_no);
                }

                // 购物金处理
                if (!empty($oInfo['shopping_price']) && $oInfo['shopping_price'] > 0) {
                    \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'add','type'=>1, 'money' => $oInfo['shopping_price'], 'extend' => $order_no, 'remark' => '订单退款返还购物金']));
                }
                if (!empty($oInfo['consume_money']) && $oInfo['consume_money'] > 0) {
                    \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'add','type'=>2, 'money' => $oInfo['consume_money'], 'extend' => $order_no, 'remark' => '订单退款返还消费金']));
                }

                // 追觅大使埋点
                \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$user_id,'order_no' => $order_no,'type'=>3,'field'=>'','number'=>0,'addOrsubtract'=>'-']));

                // 修改分佣表
                \Yii::$app->queue->push(new SalesCommissionJob(['order_no' => $order_no, 'user_id' => $user_id,'recomand_user_id' => 0, 'price' => 0, 'status' => 20000000]));

            }



            // 先试后买订单处理
            list($st,$tmsg) = $this->TryOrderAudit($arr);
            if(!$st){
                throw new MyExceptionModel($tmsg);
            }

            $trans->commit();

            if ($status == self::STATUS['P_PASS']) {

                // 以旧换新订单（异步取消） 必须在事务提交后执行
                \Yii::$app->queue->push(new CancelTradeInOrderJob([
                    'user_id'  => $user_id,
                    'order_no' => $order_no
                ]));
            }

            //todo 推送oms
            $this->refundPushOms($r_info['m_type'] ?? 0, $user_id, $refund_no, $order_no);


            return [true, 'ok'];


        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit');

            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit');

            return [false, '操作失败'];
        }

    }


    public function TryOrderAudit($arr)
    {
        $order_no = $arr['order_no'] ?? '';
        $is_complete = $arr['is_complete'] ?? 1;
        if(empty($order_no)){
            return [false, '订单号不存在'];
        }
        $orderTryInfo = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no)]);
        if(empty($orderTryInfo)){
            return [true, '订单不存在'];
        }
        $status    = intval($arr['status'] ?? 0);
        $user_id   = $orderTryInfo['user_id'];

        //状态判断
        if($status == self::STATUS['P_PASS'] && in_array($orderTryInfo['try_status'],[byNew::UserOrderTry()::TRY_STATUS['FINISHED'],byNew::UserOrderTry()::TRY_STATUS['DEDUCTED']]) ){
            return [false, '先试后买订单已扣款或已完成，不允许完结'];
        }

        if($status === self::STATUS['P_PASS']){
            $refund_no = $arr['refund_no'] ?? '';
            if(empty($refund_no)){
                return [false, '订单完结用户一定要申请完结！'];
            }
            list($sta1, $orderStr) = AliZhima::factory()->UnFreezeOrder($orderTryInfo['auth_no'] ?? '', $refund_no, $orderTryInfo['amount'] ?? 0, $is_complete, $refund_no . '-退款成功');
            if (!$sta1) {
                return [false, '先试后买订单完结失败'];
            }
            // 更新先试后买订单状态
            $save = ['user_id'=>$user_id,'order_no'=>$order_no,'try_status' => byNew::UserOrderTry()::TRY_STATUS['BACK'],'back_time'=>time()];
            list($s, $m)    = byNew::UserOrderTry()->SaveLog($save);
            if (!$s) {
                return [false, "先试后买订单完结失败~订单状态更新失败~"];
            }
        }

        if($status === self::STATUS['P_REJECT']){
            $orderInfo = by::Ouser()->CommPackageInfo($user_id,$order_no);
            $orderInfo['auth_no'] = $orderTryInfo['auth_no'] ?? '';
            if(in_array($orderTryInfo['try_status'], [byNew::UserOrderTry()::TRY_STATUS['WAIT_DEDUCT'], byNew::UserOrderTry()::TRY_STATUS['DEDUCT_FAIL']])){
                // 强制扣款
                list($sta, $orderStr) = AliZhima::factory()->TradePay($orderInfo);
                if (!$sta) {
                    // 更新先试后买订单状态 （扣款失败）
                    $save = ['user_id'=>$user_id,'order_no'=>$order_no,'try_status' => byNew::UserOrderTry()::TRY_STATUS['DEDUCT_FAIL']];
                    list($s, $m)    = byNew::UserOrderTry()->SaveLog($save);
                    if (!$s) {
                        return [false, "先试后买订单扣款失败~订单状态更新失败~"];
                    }
                    return [false, $orderStr];
                }
                if($orderTryInfo['try_status'] == byNew::UserOrderTry()::TRY_STATUS['DEDUCT_FAIL']){
                    return [true, 'OK！'];
                }
            }else{
                return [false, '先试后买订单状态异常'];
            }
        }

        return [true, 'OK!'];
    }

    /**
     * @throws Exception
     */
    public function refundPushOms($m_type, $user_id, $refund_no, $order_no): bool
    {
        $lockOldErp = CUtil::omsLock($user_id,$order_no);
        if($lockOldErp){
            if($m_type == 1) {
                //todo A002-refund退款申请推送
                ErpNew::factory()->synErp('refundPush', ['user_id' => $user_id, 'refund_no' => $refund_no]);
            }else{
                //todo A003-return退货申请推送
                ErpNew::factory()->synErp('orderReturnAdd', ['user_id' => $user_id, 'refund_no' => $refund_no]);
            }
        }
        return true;
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array
     * @throws Exception
     * 退单
     */
    public function Rback($user_id, $refund_no)
    {
        $user_id    = CUtil::uint($user_id);
        if (empty($user_id) || empty($refund_no)) {
            return [false, '参数错误'];
        }

        $r_info         = by::Orefund()->CommPackageInfo($user_id,$refund_no,false,true);

        $n_st   = [
            self::STATUS['AUDIT'],
            self::STATUS['P_PASS'],
            self::STATUS['SUCCESS'],
        ];

        if ( !in_array($r_info['status'], $n_st) ) {
            return [false, '退款状态异常'];
        }

        if ($r_info['rback'] == 1) {
            return [false, '该数据已退单'];
        }

        //只有退货退款才需要退单
        if ($r_info['m_type'] != 2) {
            return [false, '仅退款无需退单'];
        }

        //有退款物流才能退单
        if (empty($r_info['mail_no'])) {
            return [false, '该退款单还未填写退款物流'];
        }


        //新旧erp锁
        $lockOldErp = CUtil::omsLock($user_id,$r_info['order_no']);

        if(!$lockOldErp){
            list($s, $shipping_status) = $this->__isDelivery($r_info['order_no'], $r_info['ostatus']);
            if (!$s) {
                return [false, $shipping_status];
            }

            if ($shipping_status < 6) {
                return [false, '该订单暂未发货'];
            }

            //退单接口
            list($s, $m) = Erp::factory()->orderReturnAdd($r_info);
            if (!$s) {
                return [false, 'OMS操作失败'];
            }

            //保存当前退款单已退单
            $save = ['rback' => 1];
            list($s, $m)    = by::Orefund()->UpdateData($user_id, $refund_no, $save);

        }else{
            ErpNew::factory()->synErp('orderReturnAdd',['user_id'=>$user_id,'refund_no'=>$refund_no]);
            list($s, $m) = [true,'ok'];
        }
        if (!$s) {
            return [false, $m];
        }


        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param $status
     * @param $ostatus
     * @return array
     * @throws Exception
     * 各状态下审核
     *1、   一个商品退一个
     * 待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 待发货
     *2、 两个商品退一个
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改状态为 待发货
     *2.1、 两个商品继续退一个
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 退款完成&待发货
     *3     三个商品退一个
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改状态为 待发货
     *3.1   三个商品继续退一个
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改原状态为 退款完成&待发货
     *3.2   三个商品再继续退一个(都退)
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 退款完成&待发货
     */
    private function __auditByStatus($user_id, $order_no, $refund_no, $status, $ostatus)
    {
        $mOmain         = by::Omain();
        $mOuser         = by::Ouser();
        $o_info         = $mOuser->GetInfoByOrderId($user_id, $order_no);

        $mOgoods        = by::Ogoods();
        //订单商品表数据
        $oGoods         = $mOgoods->GetListByOrderNo($user_id, $order_no);
        $oGoods = array_filter($oGoods, function ($v) use($mOgoods) {
            if ($v['status'] == $mOgoods::STATUS['REFUNDIED']) {
                return false;
            }
            return true;
        });

        //退款商品表数据
        $rGoods         = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        $ids            = array_column($rGoods, 'og_id');

        //是否全部退款
        $complete   = false;
        if (count($rGoods) == count($oGoods)) {
            $complete   = true;
        }

        $r_freight      = false;
        switch ($status) {
            case self::STATUS['P_PASS'] :
                $next_st = $mOmain->SetOrderStatus($o_info['status'], $mOmain::ORDER_STATUS['RERUNDED'], true, $complete);
                $next_gst= $mOgoods::STATUS['REFUNDIED'];

                //是否需要退邮费
                if ($next_st == $mOmain::ORDER_STATUS['RERUNDED']) {
                    $or_info = by::Orefund()->GetInfoByRefundNo($user_id,$refund_no);
                    if ($or_info['m_type'] == 1){//未收到货 才退运费
                        $r_freight  = true;
                    }
                }

                break;

            default :
                $next_st = $mOmain->SetOrderStatus($o_info['status'], $mOmain::RERUND_DTS, true, $complete, $ostatus);
                $next_gst= $mOgoods::STATUS['NORMAL'];
        }

        //订单商品状态修改
        list($s, $m)    = $mOgoods->UpdateStatus($user_id, $order_no, $ids, $next_gst);
        if (!$s) {
            return [false, $m];
        }

        //订单状态修改
        if ($next_st != $o_info['status']) {
            list($s, $m)    = $mOmain->SyncInfo($user_id, $order_no, $next_st);
            if (!$s) {
                return [false, $m];
            }
        }


        return [true, ['r_freight' => $r_freight, 'complete' => $complete]];
    }

    /**
     * @param $order_no
     * @param $refund_no
     * @param int $user_id
     * @return array|false
     * @throws Exception
     * 获取指定详情信息-只查退款单对应的user_id（慎用-微信退款查询专用）
     */
    public function GetInfoByRefundNo($refund_no)
    {
        if(empty($refund_no)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($refund_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb         = $this->tbName();

            $sql        = "SELECT `user_id` FROM {$tb} WHERE `refund_no`=:refund_no LIMIT 1";
            $aData      = by::dbMaster()->createCommand($sql,[':refund_no' => $refund_no])->queryOne();
            $aData      = $aData ?: [];
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * 申请退款-仅退款（未发货）先挂起，然后后续全人工来操作-挂起失败不管
     *
     *
     * 退款-后台审批操作时，若订单为挂起状态无法进行任何操作；提示需手动取消挂起后才能审批通过/不通过
     * 退款-审核通过（部分退款未发货）-不操作
     * 退款-审核通过（全部退款未发货）-订单置无效
     * 退款-审核通过（部分退款-已发货、全部退款-已发货）- 退单
     */
    /**
     * @param $order_no
     * @return array
     * 查询订单是否挂起
     */
    private function __getIsHandup($order_no)
    {
        list($s, $ret) = Erp::factory()->getOrderList($order_no);
        if (!$s) {
            return [false, 'OMS订单查询失败，请稍候(1)'];
        }

        if (empty($ret)) {
            return [false, 'OMS订单查询失败，请稍候(2)'];
        }

        $is_handup  = $ret['is_handup']; //是否挂起（0：否 1：是）

        return [true, $is_handup];

    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param int $order_st
     * @param int $order_ed
     * @throws Exception
     * 订单导出
     */
    public function export($order_no='', $user_iden='', $status=-1, int $order_st = 0, int $order_ed = 0, $viewSensitive=false)
    {
        $head   = [
            '订单编号','退款单号','用户id','退款金额','申请时间','退款时间','退款状态','退款前状态','类型','子类型','买家姓名','手机号','退款原因','邮费','申请件数','补充描述','退款物流单号','退款物流公司'
        ];

        $f_name = '退款订单列表' . date('Ymd') . mt_rand(1000, 9999);

        $tb     = self::tbName();

        $order_time = [
            'st' => $order_st,
            'ed' => $order_ed,
        ];

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params, $viewSensitive) {

            $db         = by::dbMaster();
            $mOrefund   = by::Orefund();
            $mPhone     = by::Phone();
            $mOmain     = by::Omain();
//            $mOsource   = by::Osource();

            $id     = 0;

            $sql    = "SELECT `main`.`id`,`main`.`user_id`,`main`.`order_no`,`main`.`refund_no` FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
            WHERE `main`.`id` > :id AND {$where} ORDER BY `main`.`id` ASC LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $data       = [];


                foreach ($list as $val) {
                    $info           = $mOrefund->CommPackageInfo($val['user_id'],$val['refund_no'],true);
                    $phone          = $mPhone->GetPhoneByUid($val['user_id']);
                    list(,$ostatus) = $mOmain->SplitOrderStatus($info['ostatus']);
                    //订单来源
                    $omainInfo      = $mOmain->getInfoByOrderNo($val['user_id'],$info['order_no']);
                    $source         = $omainInfo['source'] ?? 0;
//                    $guid           = $mOsource->getOuidByOrder($val['user_id'],$info['order_no']);
//                    $source         = empty($guid) ? 0 : 1;

                    $data[] = [
                        'order_no'      => $info['order_no']."\t",
                        'refund_no'     => $info['refund_no']."\t",
                        'user_id'       => $info['user_id']."\t",
                        'price'         => $info['price']."\t",
                        'ctime'         => date('Y-m-d H:i:s',$info['ctime']),
                        'rtime'         => !empty($info['rtime']) ? date('Y-m-d H:i:s',$info['rtime']) : '',
                        'status'        => self::STATUS_NAME[$info['status']]??'未知',
                        'ostatus'       => OmainModel::STATUS_NAME[$ostatus]??'未知',
                        'type'          => OmainModel::USER_ORDER_TYPE_NAME[$info['user_order_type']] ?? '未知',
                        'source'        => $mOmain::SOURCE[$source] ?? '未知',
                        'name'          => $info['user']['nick'] ??'',
                        'phone'         => $phone ?? ''."\t",
                        'r_msg'         => $info['r_msg'],
                        'fprice'        => $info['fprice'] ?? "",
                        'num'           => $info['num'],
                        'describe'      => $info['describe']."\t",
                        'mail_no'       => $info['mail_no']."\t",
                        'express_name'  => $info['express_name']."\t",
                    ];
                }
                !$viewSensitive &&  $data = Response::responseList($data,['phone'=>'tm']);

                yield $data;
            }

        }, $f_name);
    }

    public function exportData($order_no='', $user_iden='', $status=-1, int $order_st = 0, int $order_ed = 0, $viewSensitive=false)
    {
        $head   = [
            '订单编号','退款单号','用户id','退款金额','申请时间','退款时间','退款状态','退款前状态','类型','子类型','买家姓名','手机号','退款原因','邮费','申请件数','补充描述','退款物流单号','退款物流公司'
        ];

        $tb     = self::tbName();

        $order_time = [
            'st' => $order_st,
            'ed' => $order_ed,
        ];

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        //导出

        $db         = by::dbMaster();
        $mOrefund   = by::Orefund();
        $mPhone     = by::Phone();
        $mOmain     = by::Omain();
//            $mOsource   = by::Osource();

        $id     = 0;
        $sql    = "SELECT `main`.`id`,`main`.`user_id`,`main`.`order_no`,`main`.`refund_no` FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
            WHERE `main`.`id` > :id AND {$where} ORDER BY `main`.`id` ASC LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];


            foreach ($list as $val) {
                $info           = $mOrefund->CommPackageInfo($val['user_id'],$val['refund_no'],true);
                $phone          = $mPhone->GetPhoneByUid($val['user_id']);
                list(,$ostatus) = $mOmain->SplitOrderStatus($info['ostatus']);
                //订单来源
                $omainInfo      = $mOmain->getInfoByOrderNo($val['user_id'],$info['order_no']);
                $source         = $omainInfo['source'] ?? 0;
//                    $guid           = $mOsource->getOuidByOrder($val['user_id'],$info['order_no']);
//                    $source         = empty($guid) ? 0 : 1;

                $data[] = [
                    'order_no'      => $info['order_no']."\t",
                    'refund_no'     => $info['refund_no']."\t",
                    'user_id'       => $info['user_id']."\t",
                    'price'         => $info['price']."\t",
                    'ctime'         => date('Y-m-d H:i:s',$info['ctime']),
                    'rtime'         => !empty($info['rtime']) ? date('Y-m-d H:i:s',$info['rtime']) : '',
                    'status'        => self::STATUS_NAME[$info['status']]??'未知',
                    'ostatus'       => OmainModel::STATUS_NAME[$ostatus]??'未知',
                    'type'          => OmainModel::USER_ORDER_TYPE_NAME[$info['user_order_type']] ?? '未知',
                    'source'        => $mOmain::SOURCE[$source] ?? '未知',
                    'name'          => '\''.($info['user']['nick'] ??''),
                    'phone'         => $phone ?? ''."\t",
                    'r_msg'         => $info['r_msg'],
                    'fprice'        => $info['fprice'] ?? "",
                    'num'           => $info['num'],
                    'describe'      => $info['describe']."\t",
                    'mail_no'       => $info['mail_no']."\t",
                    'express_name'  => $info['express_name']."\t",
                ];
            }
        }
        !$viewSensitive &&  $data = Response::responseList($data,['phone'=>'tm']);

        return $data;
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @throws Exception
     * 导出订单商品
     */
    public function exportGoods($order_no='', $user_iden='', $status=-1,$order_time=[])
    {
        $head   = [
            '订单编号','退款编号','买家姓名','买家id','买家手机号','商品编号','商品名称','规格名称','商品标签','销售金额','优惠后金额','购买数量','退款物流单号','退款物流公司'
        ];

        $f_name = '退款订单商品' . date('Ymd') . mt_rand(1000, 9999);

        $tb     = self::tbName();

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params) {

            $db         = by::dbMaster();
            $mPhone     = by::Phone();
            $mOrefund   = by::Orefund();
            $tagMap = by::Gtag()->GetTagNameMap();
            $tag_names  = $tagMap;
            $id     = 0;

            $sql    = "SELECT `main`.`id`,`main`.`user_id`,`main`.`order_no`,`main`.`refund_no` FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
            WHERE `main`.`id` > :id AND {$where} ORDER BY `main`.`id` ASC LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $data       = [];

                foreach ($list as $val) {
                    $info       = $mOrefund->CommPackageInfo($val['user_id'],$val['refund_no'],true,true);

                    $phone      = $mPhone->GetPhoneByUid($val['user_id']);

                    foreach($info['goods'] as $v) {
                        $tag_name   =  array_map(function ($v) use($tag_names) {
                            return $tag_names[$v] ?? '';
                        }, $v['tids']);

                        $data[] = [
                            'order_no'      => $info['order_no']."\t",
                            'refund_no'     => $info['refund_no']."\t",
                            'name'          => $info['user']['nick'] ??'',
                            'user_id'       => $info['user_id']."\t",
                            'phone'         => $phone??''."\t",
                            'g_code'        => $v['sku']."\t",
                            'g_name'        => $v['name']."\t",
                            'attr'          => !empty($v['attr_cnf']) ? implode(',',array_column($v['attr_cnf'],'at_val')) :'' ,
                            'g_tids'        => !empty($tag_name) ? implode('、',array_filter($tag_name)) :'无',
                            'g_oprice'      => $v['oprice']."\t",
                            'g_uprice'      => $v['price']."\t",
                            'num'           => $v['num'] ?? "",
                            'mail_no'       => $info['mail_no']."\t",
                            'express_name'  => $info['express_name'],
                        ];
                    }
                }

                yield $data;
            }

        }, $f_name);
    }

    public function exportGoodsData($order_no='', $user_iden='', $status=-1,$order_time=[])
    {
        $head   = [
            '订单编号','退款编号','买家姓名','买家id','买家手机号','商品编号','商品名称','规格名称','商品标签','销售金额','优惠后金额','购买数量','退款物流单号','退款物流公司'
        ];

        $tb     = self::tbName();

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        //导出

        $db         = by::dbMaster();
        $mPhone     = by::Phone();
        $mOrefund   = by::Orefund();
        $tagMap = by::Gtag()->GetTagNameMap();
        $tag_names  = $tagMap;
        $id     = 0;

        $sql    = "SELECT `main`.`id`,`main`.`user_id`,`main`.`order_no`,`main`.`refund_no` FROM {$tb} as `main` LEFT JOIN `db_dreame_goods`.`t_uo_try` AS `try` ON `main`.`order_no` = `try`.`order_no`
            WHERE `main`.`id` > :id AND {$where} ORDER BY `main`.`id` ASC LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $val) {
                $info       = $mOrefund->CommPackageInfo($val['user_id'],$val['refund_no'],true,true);

                $phone      = $mPhone->GetPhoneByUid($val['user_id']);

                foreach($info['goods'] as $v) {
                    $tag_name   =  array_map(function ($v) use($tag_names) {
                        return $tag_names[$v] ?? '';
                    }, $v['tids']);

                    $data[] = [
                        'order_no'      => $info['order_no']."\t",
                        'refund_no'     => $info['refund_no']."\t",
                        'name'          => '\''.($info['user']['nick'] ??''),
                        'user_id'       => $info['user_id']."\t",
                        'phone'         => $phone??''."\t",
                        'g_code'        => $v['sku']."\t",
                        'g_name'        => $v['name']."\t",
                        'attr'          => !empty($v['attr_cnf']) ? implode(',',array_column($v['attr_cnf'],'at_val')) :'' ,
                        'g_tids'        => !empty($tag_name) ? implode('、',array_filter($tag_name)) :'无',
                        'g_oprice'      => $v['oprice']."\t",
                        'g_uprice'      => $v['price']."\t",
                        'num'           => $v['num'] ?? "",
                        'mail_no'       => $info['mail_no']."\t",
                        'express_name'  => $info['express_name'],
                    ];
                }
            }
        }
        return $data;
    }

    /**
     * @throws Exception
     * (脚本)退货退款7天内没提交退款物流，自动拒绝退款
     */
    public function ExpressTime()
    {
        $expire = by::Orefund()::EXPRESS_EXPIRE;
        $ntime  = intval(START_TIME);
        $db     = by::dbMaster();
        $tb     = self::tbName();
        $id     = 0;
        $ctime  = $ntime - $expire;

        $rej_st     = self::STATUS['P_REJECT'];
        $audit_st   = self::STATUS['AUDIT'];

        while (true) {
            $sql    = "SELECT `id`,`user_id`,`refund_no`,`order_no` FROM {$tb} WHERE `id` > :id 
                       AND `status` = {$audit_st} AND `m_type` = 2 AND `ctime` < :ctime ORDER BY `id` LIMIT 100";

            $list   = $db->createCommand($sql, [':id' => $id, ':ctime' => $ctime])->queryAll();

            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $id     = $end['id'];

            foreach ($list as $val) {
                //先试后买订单不做操作
                $orderTry = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $val['order_no']),CUtil::buildCondition('refund_no', '=', $val['refund_no'])]);
                if ($orderTry) {
                   continue;
                }
                $arr = [
                    'refund_no' => $val['refund_no'],
                    'order_no'  => $val['order_no'],
                    'status'    => $rej_st,
                    'a_reason'  => '退款订单申请未通过，自动关闭，可联系客服处理',
                ];

                list($s, $m) = $this->Audit($arr);

                if (!$s) {
                    CUtil::debug("{$val['refund_no']}|{$m}", 'err.express');
                }
            }

        }

    }


    /**
     * @param $user_id
     * @param $order_no
     * @return int
     * @throws Exception
     * @throws \RedisException
     * 根据订单号获取已经退款商品数量
     */
    public function getCountByOrderNo($user_id, $order_no): int
    {
       $list = $this->GetList($order_no,$user_id,self::STATUS['SUCCESS']);
       $count = 0;
       $orgoods = by::Orgoods();
       if($list){
           foreach ($list as $li){
               $refundNo = $li['refund_no'] ?? '';
               $rGoods = $orgoods->GetListByRefundNo($user_id, $refundNo, $order_no);
               if($rGoods) $count += count($rGoods);
           }
       }
       return $count;
    }

    /**
     * 退款金额
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param $freight
     * @return string
     * @throws Exception
     * @throws \RedisException
     */
    private function getRefundAmount($user_id, $order_no, $refund_no, $freight)
    {
        // 订单商品
        $orderGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);

        // 退款商品
        $refundGoods = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        $goodsIds = array_flip(array_column($refundGoods, 'og_id')); // 将数组值作为键名

        // 退款金额
        $amount = 0;
        foreach ($orderGoods as $val) {
            if (isset($goodsIds[$val['id']])) {
                $amount = bcadd($amount, $val['price']);
            }
        }

        // 退运费
        if ($freight) {
            $userOrder = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
            if ($userOrder && isset($userOrder['fprice'])) {
                $amount = bcadd($amount, $userOrder['fprice']);
            }
        }
        return by::Gtype0()->totalFee($amount, 1); // 分转元
    }


}
