<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;

/**
 * 抽奖活动-任务（完成）记录
 */
class DrawActivityTaskRecordModel extends CommModel
{
    public static function tbName()
    {
        return "`db_dreame_goods`.`t_draw_activity_task_record`";
    }

    public static $tb_fields = [
        'id', 'activity_id', 'task_id', 'user_id', 'user_phone', 'draw_times', 'used_draw_times', 'complete_time', 'ctime', 'utime'
    ];

    /**
     * @param $acId
     * @param $userId
     * @return string
     * 抽奖次数key
     */
    private function getUserDrawTimesKey($acId, $userId): string
    {
        return AppCRedisKeys::getUserDrawTimesKey($acId, $userId);
    }

    /**
     * @param $acId
     * @param $userId
     * @return void
     * @throws RedisException
     * 清除抽奖次数key
     */
    public function __delUserDrawTimesKey($acId, $userId)
    {
        $redis    = by::redis();
        $redisKey = $this->getUserDrawTimesKey($acId, $userId);
        $redis->del($redisKey);
    }

    /**
     * @param $acId
     * @param $phone
     * @return string
     * 返回已完成任务条数列表
     */
    private function getTaskCompleteCountKey($acId, $phone): string
    {
        return AppCRedisKeys::getTaskCompleteCountKey($acId, $phone);
    }

    /**
     * @throws RedisException
     * 清除已完成任务条数列表
     */
    public function __delTaskCompleteCountKey($acId, $phone)
    {
        $redis    = by::redis();
        $redisKey = $this->getTaskCompleteCountKey($acId, $phone);
        $redis->del($redisKey);
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取用户剩余抽奖次数
     */
    public function getUserDrawTimes($userId, $acId, $cache = true)
    {
        $redis     = by::redis();
        $redisKey  = $this->getUserDrawTimesKey($acId, $userId);

        // 尝试从 Redis 获取抽奖次数
        $aJson = $cache ? $redis->get($redisKey) : false;

        if ($aJson !== false) {
            $drawTimes = $aJson;
        } else {
            // 从数据库获取抽奖次数
            $currentTime = time();
            $tb          = self::tbName();
            $sql         = "SELECT SUM(`draw_times`) as `draw_times`,MIN(`draw_expire_time`) AS `min_draw_expire_time` FROM {$tb} WHERE `user_id`=:user_id AND `activity_id`=:activity_id AND `draw_expire_time` >:draw_expire_time ";

            $userDrawInfo   = by::dbMaster()->createCommand($sql, [':user_id' => $userId, 'activity_id' => $acId, ':draw_expire_time' => $currentTime])->queryOne();
            $drawTimes      = $userDrawInfo['draw_times'] ?? 0;
            $drawExpireTime = $userDrawInfo['min_draw_expire_time'] ?? 0;
            //缓存过期时间
            $ex = $drawExpireTime - $currentTime;
            // 将抽奖次数存入 Redis
            $redis->set($redisKey, $drawTimes, ['EX' => empty($drawTimes) ? 10 : $ex]);
        }

        return $drawTimes;
    }


    /**
     * @throws RedisException
     * @throws Exception
     * 刷缓存中抽奖次数
     */
    public function updateDrawTimes($userId, $acId, $times)
    {
        $redis    = by::redis();
        $redisKey = $this->getUserDrawTimesKey($acId, $userId);
        //如果此key不存在不继续执行返回默认赠送次数
        if (!$redis->exists($redisKey)) {
            //当天晚上最后一秒时间戳
            $currentNightTime   = mktime(23, 59, 59);
            //当前时间和最后一秒的差
            $currentNightExTime = $currentNightTime - time();
            //随机有效期
            $randTime           = rand(10, 30);
            //最小有效期
            $exMin              = min($currentNightExTime, $randTime);
            $drawTimes          = $this->getUserDrawTimes($userId, $acId);
            $drawTimes          = $drawTimes + $times;
            return $redis->set($redisKey, $drawTimes, ['EX' => $exMin]);
        }
        if ($times < 0) {
            // 使用 `decrBy` 减少次数
            return $redis->decrBy($redisKey, abs($times));
        } else {
            // 使用 `incrBy` 增加次数
            return $redis->incrBy($redisKey, $times);
        }
    }

    /**
     * @param $input
     * @return array
     * 批量插入任务完成记录
     */
    public function batchInsertLog($input): array
    {
        try {
            $tb = self::tbName();

            $ret = by::dbMaster()->createCommand()->batchInsert($tb, ['activity_id', 'task_id', 'user_id', 'user_phone', 'task_type', 'draw_times', 'complete_time', 'draw_expire_time', 'ctime', 'utime'], $input)->execute();
            if (!$ret) {
                return [false, '未知原因，插入失败'];
            }

            $acIdAndPhone = array_unique(array_column($input, 'activity_id', 'user_phone'));
            //删除缓存记录
            foreach ($acIdAndPhone as $k => $v) {
                $this->__delTaskCompleteCountKey($v, $k);
            }

            return [true, '插入成功'];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            return [false, '插入失败: ' . $error];
        }
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取完成任务记录条数
     */
    public function getTaskCompleteCount($acId, $taskId, $phone, $endTime)
    {
        $redis    = by::redis();
        $redisKey = $this->getTaskCompleteCountKey($acId, $phone);
        $subKey   = CUtil::getAllParams(__FUNCTION__, $taskId, $endTime);
        // 尝试从 Redis 获取数据
        $count = $redis->hGet($redisKey, $subKey);

        if (!$count) {
            $tb     = self::tbName();
            $sql    = "SELECT COUNT(1) FROM {$tb} WHERE `activity_id`=:activity_id AND `task_id`=:task_id AND `user_phone`=:user_phone AND `draw_expire_time`>= :endTime";
            $params = [':activity_id' => $acId, ':task_id' => $taskId, ':user_phone' => $phone, ':endTime' => $endTime];

            // 查询数据库并获取计数
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();

            // 存储到 Redis
            $redis->hSet($redisKey, $subKey, $count);
            // 设置适当的过期时间 默认1800秒
            CUtil::ResetExpire($redisKey);
        }

        return $count;
    }

}


