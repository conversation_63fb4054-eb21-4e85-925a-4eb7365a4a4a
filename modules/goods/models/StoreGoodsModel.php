<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 导购订单关系表
 */

namespace app\modules\goods\models;

use app\models\BusinessException;
use app\modules\main\models\CommModel;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\MemberActivityService;
use spec\Prophecy\Exception\Prediction\AggregateExceptionSpec;
use yii\db\ActiveQuery;
use yii\db\Exception;


class StoreGoodsModel extends CommModel
{
    public $tb_fields = [
        'id', 'goods_id', 'rate', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`dreame_store_goods`";
    }

    public function GetGoodsList(
        $page=1, $page_size=50, $version='',$name, $tid=-1,$platformId
    ): array
    {
        $type = 0;
        $status = 0;
        $r_key   = $this->__getStoreGoodsListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$name,$version,$type,$status,$tid,$platformId);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aJson = false;
        if ($aJson === false){
            $tb = $this->tbName();

            $gmain_tb = GmainModel::tbName();

            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getConditionNew($version, $status,$name, $tid, $platformId, $type);

            $sql = "SELECT DISTINCT `t_gmain`.`id`
                    FROM {$tb} 
                    left JOIN {$gmain_tb} ON `t_gmain`.`id` = `dreame_store_goods`.`goods_id`
                    WHERE {$where}
                    ORDER BY `t_gmain`.`id` DESC 
                    LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 600);
        }else{
            $aData   = (array)json_decode($aJson,true);
        }
        return empty($aData) ? [] : array_column($aData,"id");
    }
    public function GetGoodsListCount($version='',$name, $tid=-1,$platformId){
        $type = 0;
        $status = 0;
        
        $tb = $this->tbName();
        $gmain_tb = GmainModel::tbName();
        list($where, $params)= $this->__getConditionNew($version, $status,$name, $tid, $platformId, $type);
        $sql                 = "SELECT  COUNT(`t_gmain`.`id`)
                                FROM {$tb} 
                                left JOIN {$gmain_tb} ON `t_gmain`.`id` = `dreame_store_goods`.`goods_id`
                                WHERE {$where}";
        $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
        $count               = empty($count) ? 0 : $count;
           

        return intval($count);

    }
    private function __getConditionNew($version = '', $status = -1, $name = '', $tid = -1, $platformId = 0, $type = -1): array
    {
        //SQL初始化条件
        $where             = "`t_gmain`.`is_del`=:is_del";
        $params[':is_del'] = 0;

        if(!empty($name)) {
            $where                .= " AND `t_gmain`.`name` LIKE :name";
            $params[":name"]       = "%{$name}%";
        }

        if($status >= 0) {
            $where                .= " AND `t_gmain`.`status`=:status";
            $params[":status"]     = intval(!!$status);
        }

        if ($tid > 0) {
            $tb                    = by::model('GtagModel', 'goods')->tbName();
            $where                .= " AND `t_gmain`.`id` IN (SELECT `gid` FROM {$tb} WHERE `tid`={$tid})";
        }

        if ($type > -1) {
            if ($type != 999) {
                $where                .= " AND `t_gmain`.`type` =:type";
                $params[":type"]        = "$type";
            } else {
                $where                .= " AND `t_gmain`.`type` in (0,1,2)";    // 0=普通商品，1=优选商品，2=严选商品
            }
        }

        // 是否是当前平台商品
        if (!empty($platformId)) {
            $gIdsData = byNew::GoodsPlatformModel()->getDataList(['gid'], ['platform_id' => $platformId]);
            $gIds     = array_column($gIdsData, 'gid');
            if (empty($gIds)) {
                $where .= " AND 1=2";
            } else {
                $where .= " AND `t_gmain`.`id` IN (" . implode(',', $gIds) . ")";
            }
        }
        //版本号控制
        $versions  = $this->GetAccessVersions($version);
        $versions  = implode(',',$versions);
        $where    .= " AND `t_gmain`.`version` IN ({$versions})";

        return [$where,$params];
    }
    /**
     * @param $version
     * @return array
     * 获取可访问商品版本号
     */
    public function GetAccessVersions($version): array
    {
        $version  = CUtil::version2long($version);
        $version = $version === '' ? 0 : $version;
        return array_unique([0,$version]);
    }
    /**
     * @return string
     * 商品哈希列表
     */
    private function __getStoreGoodsListKey() {
        return AppCRedisKeys::getStoreGoodsList();
    }

    public function getNeedHandleOrder(){
        $sql   = "SELECT `id` FROM `db_dreame_goods`.`t_gmain` WHERE `type`= 0 and `status` = 0 and `is_del` = 0 ";
        $aData = by::dbMaster()->createCommand($sql, [])->queryAll();
        $gids = array_column($aData, 'id');

        // 获取团购商品列表
        $sql   = "SELECT `gid` FROM `db_dreame_goods`.`t_group_purchase_goods`";
        $aData = by::dbMaster()->createCommand($sql, [])->queryAll();
        $ggids = array_column($aData, 'gid');

        // 获取国补商品
        $sql   = "SELECT `gid` FROM `db_dreame_goods`.`subsidy_activity_goods`";
        $aData = by::dbMaster()->createCommand($sql, [])->queryAll();
        $gbids = array_column($aData, 'gid');

        // 获取一元购商品 线上5
        $service = MemberActivityService::getInstance();
        list($status, $data) = $service->getInfo(5, 0);
        $oneIds = array_column($data['modules'][0],'goods_id');

        // 三折购 线上 38
        list($status, $data) = $service->getInfo(38, 0);
        $threeIds = array_column($data['modules'][0],'goods_id');

        // 六折购
        list($status, $data) = $service->getInfo(35, 0);
        $sixIds = array_column($data['modules'][0],'goods_id');
        $rows = [];
        foreach($gids as $k=>$v) {
            if(in_array($v, $ggids) || in_array($v, $gbids) || in_array($v, $oneIds) || in_array($v, $threeIds) || in_array($v, $sixIds)) {
                continue;
            }else{
                $rows[] = [
                    'goods_id' => $v,
                    'ctime' => time(),
                    'utime' => time(),
                ];
            }
        }
        
        $status = $this->batchAdd($rows);
    }
    public  function batchAdd(array $rows)
    {
        // 开启事务
        $db = by::dbMaster();
        $tb = self::tableName();
        
        // 使用批量插入
        $a =  $db->createCommand()->batchInsert($tb, ['goods_id', 'ctime', 'utime'],$rows)->execute();
        return true;
    }


    /**
     * 默认列表
     */
    public function getPageList($params = []){
        $order = $params['order'] ?? 'ctime';
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        $query = self::find();
        $query = $this->handleSearch($query, $params);

        $list = $query->orderBy([$order => SORT_DESC])
            ->offset(($page - 1) * $pageSize)->limit($pageSize)
            ->asArray()->all();
        $total = $query->count();
        return ['list' => $list, 'total' => $total, 'pages' => ceil($total / $pageSize)];
    }

    public function saveData(array $data): bool
    {
        // 获取用户记录
        $info = self::findOne(['goods_id' => $data['goods_id']]);

        if ($info === null) {
            // 如果用户不存在，创建新的记录
            $info              = new self();
            $info->ctime       = time(); // 设置创建时间
        }

        // 更新或设置
        $info->setAttributes($data, false);

        $info->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$info->save(false)) {
            return false;
        }

        return true;
    }

    public function doCreate($data)
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        $data['ctime'] = time();
        $data['utime'] = time();
        $db->createCommand()->insert($tb, $data)->execute();
        $id = $db->getLastInsertID();
        return $id;
    }


    /**
     * 获取信息
     * @return array|null
     */
    public function getInfo($where)
    {
        return self::find()
            ->where($where)
            ->asArray()
            ->one();
    }

    public function getInfos($ids): array
    {
        $list = self::find()
            ->where(['id' => $ids])
            ->asArray()
            ->all();

        $list = array_column($list, null, 'id');
        return $list ?: [];
    }

    public function updateInfo($where,array $params){
        $db = by::dbMaster();
        $tb = self::tbName();
        $params['utime'] = time();
        return $db->createCommand()->update($tb, $params, $where)->execute();
    }

    public function deleteInfo($where){
        $db = by::dbMaster();
        $tb = self::tbName();
        return $db->createCommand()->delete($tb, $where)->execute();
    }

    public  function batch($fields,array $rows)
    {
        // 开启事务
        $db = by::dbMaster();
        $tb = self::tableName();

        // 使用批量插入
        $a =  $db->createCommand()->batchInsert($tb, $fields, $rows)->execute();
        return true;
    }


    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        $params = array_map(function ($v) {return trim($v);}, $params);
        $params = array_filter($params, function ($v) {
            return (!empty($v) || $v === '0' || $v === 0 );
        });

        if ($params['goods_name'] ?? '') {
            $gmainTb = GmainModel::tbName();
            $query->innerJoin("{$gmainTb} as g","g.id=goods_id");
            $query->andFilterWhere(['like', 'g.name', $params['goods_name'] ?? '']);
        }

        $attrs = $this->getTbFields();
        $map = [];

        foreach ($attrs as  $field) {
            if (isset($params[$field])) {
                $map[$field] = $params[$field];
            }
        }
        $query->andWhere($map);
        return $query;
    }

    public function getTbFields(): array
    {
        return ['id', 'goods_id', 'rate','ctime','utime'];
    }
}
