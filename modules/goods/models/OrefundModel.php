<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 退款表
 */
namespace app\modules\goods\models;

use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\ExpressTen;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use RedisException;
use yii\BaseYii;
use yii\db\DataReader;
use yii\db\Exception;


class OrefundModel extends CommModel {

    //每个退款单每天最多提交9次
    CONST UPLOAD_NUM        = 15;
    CONST EXPRESS_EXPIRE    = YII_ENV_PROD ? 7 * 24 * 3600 : 1800*2;   //可提交退款物流时间窗口

    CONST STATUS = [
        'audit'     => 1000,    //待审核
        'pass'      => 1010,    //审核通过
        'refusal'   => 1020,    //审核拒绝
        'Cancel'    => 100,     //取消
        'success'   => 20000000,//退款成功
    ];

    CONST SOURCE = [
        'CENTER' => 1, //导购中心
        'SALE'   => 2  //售后列表
    ];

    public $tb_fields   = [
        'id','refund_no','order_no','user_id','r_type','m_type','ctime','rtime','status','ostatus','describe',
        'images','a_reason','mail_no','express_code','express_name','price','rback'
    ];

    public static function tbName($user_id): string
    {
        $mod = intval($user_id) % 5;
        return  "`db_dreame_goods`.`t_orefund_{$mod}`";
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($user_id,$refund_no): string
    {
        return AppCRedisKeys::getOneRefund($user_id,$refund_no);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getListKey($user_id): string
    {
        return AppCRedisKeys::getListRefund($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getRePrice($user_id): string
    {
        return AppCRedisKeys::getRePrice($user_id);
    }

    /**
     * @return string
     * 导购退款订单缓存KEY
     */
    private function __getSrNosKey(): string
    {
        return AppCRedisKeys::getSrNoList();
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return int
     * 缓存清理
     */
    public function DelCache($user_id, $refund_no): int
    {
        $r_key1 = $this->__getOneInfoKey($user_id,$refund_no);
        $r_key2 = $this->__getListKey($user_id);
        $r_key3 = $this->__getRePrice($user_id);
        $r_key4 = $this->__getSrNosKey();

        return  by::redis('core')->del($r_key1,$r_key2,$r_key3,$r_key4);
    }

    /**
     * @param int $user_id
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, array $arr)
    {
        $order_no       = $arr['order_no']  ?? '';
        $refund_no      = $arr['refund_no'] ?? '';
        $m_type         = $arr['m_type']    ?? 0;
        $r_type         = $arr['r_type']    ?? 0;
        $describe       = $arr['describe']  ?? "";
        $images         = $arr['images']    ?? "";
        $ctime          = $arr['ctime']     ?? 0;
        $ostatus        = $arr['ostatus']   ?? 0;
        $mail_no        = $arr['mail_no']   ?? "";
        $express_code   = $arr['express_code'] ?? "";
        $express_name   = $arr['express_name'] ?? "";

        $save       = [
            'refund_no'     => $refund_no,
            'order_no'      => $order_no,
            'user_id'       => $user_id,
            'r_type'        => $r_type,
            'm_type'        => $m_type,
            'ctime'         => $ctime,
            'ostatus'       => $ostatus,
            'describe'      => $describe,
            'images'        => $images,
            'mail_no'       => $mail_no,
            'express_code'  => $express_code,
            'express_name'  => $express_name,
        ];

        $tb = $this->tbName($user_id);

        by::dbMaster()->createCommand()->insert($tb, $save)->execute();

        $this->DelCache($user_id, $refund_no);
        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array|false
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetInfoByRefundNo($user_id, $refund_no)
    {
        $user_id     = CUtil::uint($user_id);

        if(empty($user_id) || empty($refund_no)) {
            return [];
        }

        $redis       = by::redis('core');
        $r_key       = $this->__getOneInfoKey($user_id,$refund_no);
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb         = $this->tbName($user_id);
            $fields     = implode("`,`",$this->tb_fields);

            $sql        = "SELECT `{$fields}` FROM {$tb} WHERE `refund_no`=:refund_no AND `user_id`=:user_id LIMIT 1";


            $aData      = by::dbMaster()->createCommand($sql,[':refund_no' => $refund_no,':user_id'=>$user_id])->queryOne();
            $aData      = $aData ?: [];
            $redis->set($r_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 1800]);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 小程序退款订单列表
     */
    public function GetList($user_id, $status = -1, $platformSource = 0, $page = 1, $page_size = 10)
    {
        $page    = CUtil::uint($page, 1);
        $redis   = by::redis('core');
        $r_key   = $this->__getListKey($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $status, $platformSource, $page, $page_size);
        $aJson   = $redis->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb                  = $this->tbName($user_id);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($user_id, $status, 0, 0, $platformSource);
            $sql                 = "SELECT `refund_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC 
                                    LIMIT {$offset},{$page_size}";

            $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,1800);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param int $status
     * @return int
     * @throws Exception
     * 订单总数
     */
    public function GetListCount($user_id, $status = -1, $platformSource = 0)
    {
        $redis   = by::redis('core');
        $r_key   = $this->__getListKey($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $status, $platformSource);
        $count   = $redis->hGet($r_key, $sub_key);

        if($count === false) {
            $tb                  = $this->tbName($user_id);
            list($where, $params) = $this->__getCondition($user_id, $status, 0, 0, $platformSource);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,1800);
        }

        return intval($count);
    }


    /**
     * @param $user_id
     * @param $t_type
     * @param $source
     * @return array
     * @throws Exception
     * @throws RedisException
     * 导购小程序退款订单列表
     */
    public function getSrNos($user_id, $t_type, $source): array
    {
        $redis   = by::redis('core');
        $r_key   = $this->__getSrNosKey();
        $s_key   = CUtil::getAllParams(__FUNCTION__, $user_id, $t_type, $source);
        $sr_json = $redis->hGet($r_key, $s_key);
        $sr_list = (array)json_decode($sr_json, true);

        if($sr_json === false) {
            switch ($source) {
                case by::Orefund()::SOURCE['SALE']:
                    $type = $t_type;

                    break;
                default :
                    $type = -1;

                    break;
            }

            $s_list = by::Osource()->GetList($user_id, by::Osource()::GET_TYPE['ALL'], $type, -1);

            $sr_list = [];
            foreach ($s_list as $v) {
                if(empty($v['user_id']) || empty($v['order_no'])) {
                    continue;
                }

                $tb                   = $this->tbName($v['user_id']);
                list($where, $params) = $this->__getCondition($v['user_id'], -1, $t_type, $source);
                $params[':order_no']  = $v['order_no'];
                $sql                  = "SELECT `refund_no`, `user_id`, `ctime` FROM {$tb} WHERE {$where} AND `order_no`=:order_no";
                $r_list               = by::dbMaster()->createCommand($sql,$params)->queryAll();

                if (empty($r_list)) {
                    continue;
                }

                array_map(function($v) use (&$sr_list){
                    $sr_list[] = $v;
                }, $r_list);
            }

            $time_arr = [];
            foreach ($sr_list as $sr_info) {
                $time_arr[] = $sr_info['ctime'];
            }

            array_multisort($time_arr, SORT_DESC, $sr_list);

            by::redis('core')->hSet($r_key, $s_key,json_encode($sr_list));
            CUtil::ResetExpire($r_key, 1800);
       }

        return $sr_list;
    }

    /**
     * @param $user_id
     * @param $t_type
     * @param int $source
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取导购退款列表
     */
    public function getSrList($user_id, $t_type, int $source = 1): array
    {
        $o_refund       = by::Orefund();
        $sr_list        = $o_refund->getSrNos($user_id, $t_type, $source);
        $return['list'] = [];
        $price          = 0;
        $count          = 0;

        foreach ($sr_list as $v) {
            if(empty($v['user_id']) || empty($v['refund_no'])) {
                continue;
            }

            $r_info = $o_refund->CommPackageInfo($v['user_id'], $v['refund_no'], false, true);

            $sr_info = [
                'refund_no' => $r_info['refund_no'],
                'uid'       => $r_info['user_id'],
                'status'    => $r_info['status'],
                'm_type'    => $r_info['m_type'],
                'price'     => $r_info['price'],
                'goods'     => $r_info['goods']
            ];

            switch ($source) {
                case self::SOURCE['CENTER']:
                    if ($r_info['status'] == self::STATUS['success'] || $r_info['status'] == self::STATUS['pass']) {
                        $price = bcadd($price, $r_info['goods_price']);
                        $count = bcadd($count, 1);
                    }

                    break;
                case self::SOURCE['SALE']:
                    if ($r_info['status'] == self::STATUS['success'] || $r_info['status'] == self::STATUS['pass'] || $r_info['status'] == self::STATUS['audit']) {
                        $price = bcadd($price, $r_info['goods_price']);
                        $count = bcadd($count, 1);
                    }

                    break;
                default :
                    break;
            }

            $return['list'][] = $sr_info;
        }

        $return['total'] = [
            'count' => $count,
            'price' => by::Gtype0()->totalFee($price, 1)
        ];

        return $return;
    }

    /**
     * @param $user_id
     * @param $status
     * @param $t_type
     * @param $source
     * @param $platformSource
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($user_id = 0, $status = -1, $t_type = 0, $source = 0, $platformSource = ''): array
    {

        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if($user_id > 0) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"]  = intval($user_id);
        }

        if($status > 0) {
            $where              .= " AND `status`=:status";
            $params[":status"]   = intval($status);
        }

        switch ($t_type) {
            case by::Osource()::T_TYPE['DAY']:
                $st = strtotime("today");
                $ed = strtotime("tomorrow");

                break;
            case by::Osource()::T_TYPE['MONTH']:
                $st = strtotime(date('Y-m-01 00:00:00'));
                $ed = strtotime("+1 month", $st);

                break;
            case by::Osource()::T_TYPE['YEAR']:
                $st = strtotime(date('Y-01-01 00:00:00'));
                $ed = strtotime("+1 year", $st);

                break;
            default :
                break;
        }

        if (!empty($st) && !empty($ed)) {
            switch ($source) {
                case by::Orefund()::SOURCE['CENTER']:
                    $where .= " AND `rtime` BETWEEN :r_st AND :r_ed";

                    break;
                case by::Orefund()::SOURCE['SALE']:
                    $where .= " AND `ctime` BETWEEN :r_st AND :r_ed";

                    break;
                default :
                    break;
            }

            $params[":r_st"]  = $st;
            $params[":r_ed"]  = $ed;
        }


        //先试后买订单号
        $orderInfos = byNew::UserOrderTry()->GetList([
            CUtil::buildCondition('user_id', '=', $user_id),
        ], false);

        if ($orderInfos) {
            $orderNos = array_column($orderInfos, 'order_no');
            $orderArr = implode("','", $orderNos);
            if (in_array($platformSource, [9, 10])) {
                //支付宝小程序 只展示先试后买订单
                $where .= " AND `status` != 1020 AND `order_no` IN  ('{$orderArr}')";
            } else {
                //不展示先试后买订单
                $where .= " AND `order_no` NOT IN  ('{$orderArr}')";
            }
        }else{
            if (in_array($platformSource, [9, 10])) {
                $where .= " AND `status` != 1020 AND `order_no` is null";
            }
        }

        return [$where, $params];
    }

    private function getImage($images)
    {
        foreach ($images as $image) {
            if (!empty($image)) {
                return $image;
            }
        }
        return '';
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @param bool $can_user
     * @param bool $can_attr
     * @return array|false
     * @throws Exception
     * 统一打包数据
     */
    public function CommPackageInfo($user_id,$refund_no,$can_user=false,$can_attr=false)
    {
        $info           = $this->GetInfoByRefundNo($user_id,$refund_no);
        if(empty($info)) {
            return [];
        }

        $info['r_msg']  = by::OrefundMain()::R_TYPE[$info['r_type']] ?? '无';

        $order_no       = $info['order_no'];

        //退款商品
        $rGoods         = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        $og_ids         = array_column($rGoods, 'og_id');

        //订单商品所有数据
        $oGoods         = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
        $oprice         = 0; //退款原价
        $price          = 0; //退款金额
        $deposit_price  = 0; //定金价格
        $refunded_price = 0; //退款金额(已完成+审核通过)
        $refund_price   = 0; //退款金额(申请+审核通过+已完成)
        $num            = 0; //退款件数
        $coin           = 0; //退款积分
        foreach($oGoods as $k => $val) {
            if ( !in_array($val['id'], $og_ids) ) {
                unset($oGoods[$k]);
                continue;
            }

            $oCfg       = by::Ocfg()->GetOneByUnique($order_no, $val['gid'], $val['sid']);
            $uprice     = $oCfg['spec']['price'] ?? $oCfg['price'];
            $upoint     = $oCfg['spec']['point'] ?? ($oCfg['point'] ?? 0);
            $ucoupon_id = $oCfg['spec']['coupon_id'] ?? ($oCfg['coupon_id']??0);

            $isPresale = CUtil::uint($oCfg['is_presale'] ?? 0);
            $deposit = !empty($isPresale) ? ($oCfg['deposit'] ?? 0): 0;

            // 从配置中提取图片字段，确保字段存在
            $specImage   = $oCfg['spec']['image'] ?? '';
            $coverImage  = $oCfg['cover_image'] ?? '';
            $marketImage = $oCfg['market_image'] ?? '';
            $pcImage     = $oCfg['pc_image'] ?? '';

            // 处理封面图：如果存在多规格图片，则取多规格图片，否则取封面图
            $cover_image = $this->getImage([
                $specImage,
                $coverImage
            ]);

            // 处理营销图：如果存在多规格图片，则取多规格图片，否则取营销图。如果营销图为空，则取封面图，如果封面图也为空，则返回空
            $market_image = $this->getImage([
                $specImage,
                $marketImage,
                $coverImage
            ]);

            // 处理PC图：如果存在多规格图片，则取多规格图片，否则取PC图。如果PC图为空，则取营销图，如果营销图也为空，则取封面图，如果封面图也为空，则返回空
            $pc_image = $this->getImage([
                $specImage,
                $pcImage,
                $marketImage,
                $coverImage
            ]);


            $oGoods[$k] = [
                'id'            => $oCfg['id'],
                'gid'           => $oCfg['gid'],
                'sku'           => $oCfg['spec']['sku'] ?? $oCfg['sku'],
                'name'          => $oCfg['name'],
                'cover_image'   => $cover_image,
                'market_image'  => $market_image,
                'pc_image'      => $pc_image,
                'tids'          => $oCfg['tids'] ?? [],
                'deposit_price' => by::Gtype0()->totalFee(bcmul($deposit ?? 0, $val['num'] ?? 0, 2), 1),
                'is_presale'    => $isPresale,
                'num'           => $val['num'],
                'coin'          => $val['coin'],
                'coin_price'    => by::Gtype0()->totalFee($val['coin_price'], 1),
                'oprice'        => by::Gtype0()->totalFee($val['oprice'], 1),
                'price'         => by::Gtype0()->totalFee($val['price'], 1),
                'exprice'       => by::Gtype0()->totalFee($val['exprice'] ?? 0, 1),
                'uprice'        => by::Gtype0()->totalFee($uprice, 1),
                'upoint'        => $upoint,
                'ucoupon_id'    => $ucoupon_id,
                'type'          => $oCfg['type'] ?? '0',
                'goods_status'  => $val['status'],
                'goods_type'    => $oCfg['goods_type'] ?? 1,
            ];

            //规格属性名
            if ($can_attr) {
                $avModel = (intval($oCfg['goods_type'] ?? 1) == 2) ? by::GoodsAvModel() : by::Gav();
                $av_ids         = $oCfg['spec']['av_ids'] ?? '';
                $attr_cnf       = [];
                if ($av_ids) {
                    $av_ids = json_decode($av_ids, true);
                    foreach ($av_ids as $k1 => $v1){
                        $cnf = $avModel->IdToName($v1);
                        $cnf && $attr_cnf[] = $cnf;
                    }
                }
                $oGoods[$k]['attr_cnf'] = $attr_cnf;
            }

            $price         = bcadd($price, $val['price']);
            $coin          = bcadd($coin, $val['coin']);
            $oprice        = bcadd($oprice, $val['oprice']);
            $deposit_price = bcadd($deposit_price, $oGoods[$k]['deposit_price'] ?? 0, 2);
            $num           = bcadd($num, $val['num']);
        }

        $info['goods_price'] = $price;

        if($can_user) {
            $user         = by::users()->getOneByUid($user_id);
            $info['user'] = [
                'avatar' => $user['avatar'] ?? "",
                'nick'   => $user['nick']   ?? "",
            ];
            //手机号
            $info['user']['phone']          = by::Phone()->GetPhoneByUid($user_id);
            //来源
            $source                         = by::userExtend()->getUserExtendInfo($user_id);
            list($info['user']['source'])   = by::userExtend()->sourceConfig($source);
        }

        //未收到货 要 退邮费
        $o_info     = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        if($info['m_type'] == 1){
            $price      = $info['price'] ?: bcadd($price, $o_info['fprice']);
        }


        $info['oprice'] = by::Gtype0()->totalFee($oprice, 1);
        $info['price']  = by::Gtype0()->totalFee($price, 1);
        $info['coin']   = $coin;//退还积分


        //定金订单加上
        $info['real_price'] = bcadd($info['price'],$deposit_price,2);
        $info['deposit_price'] = $deposit_price;

        $info['fprice'] = by::Gtype0()->totalFee($o_info['fprice'], 1);
        $info['order_ctime'] = $o_info['ctime']; // 订单创建时间
        $info['num']    = $num;

        $info['goods']  = array_values($oGoods);

        $mainModel = by::Omain();
        $mainInfo = $mainModel->getInfoByOrderNo($user_id,$order_no);
        $info['user_order_type'] = CUtil::uint($mainInfo['type'] ?? $mainModel::USER_ORDER_TYPE['COMMON']);//订单类型-普通订单
        $info['platform_source'] = $mainInfo['platform_source'] ?? 0;                                      // 平台来源

        if($info['user_order_type'] == $mainModel::USER_ORDER_TYPE['BAT']){
            $info['try_info'] = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no),CUtil::buildCondition('user_id', '=', $user_id)]);
        }

        $info['try_info'] = $info['try_info'] ?? [];

        return $info;
    }

    /**
     * @param int $user_id
     * @param array $arr
     * @return array
     * @throws Exception
     * 退货物流单号
     */
    public function Rexpress(int $user_id, array $arr)
    {
        $refund_no      = $arr['refund_no']     ?? '';
        $mail_no        = $arr['mail_no']       ?? '';
        $express_name   = $arr['express_name']  ?? '';
        $express_code   = $arr['express_code']  ?? '';

        if (empty($refund_no)) {
            return [false, '网络繁忙'];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__,$refund_no);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if (empty($mail_no) || empty($express_name) || empty($express_code)) {
            return [false, '参数不能为空'];
        }

        if (mb_strlen($mail_no) > 50 || mb_strlen($express_name) > 20 ) {
            return [false, '请输入正确的物流单号~'];
        }

        //物流只能提交一次
        $aLog       = $this->GetInfoByRefundNo($user_id, $refund_no);
        if (empty($aLog)) {
            return [false, '该数据不存在'];
        }
        if ($aLog['m_type'] != 2) {
            return [false, '退货单才需要提交物流哦'];
        }
        if (!empty($aLog['mail_no'])) {
            return [false, '该退款单物流已提交'];
        }

        $r_info  = by::Orefund()->CommPackageInfo($user_id,$refund_no,false,true);

        //退款物流只能7天内提交
        //2025-03-17 测试要求不做限制
        $d_time = time() - $aLog['ctime'];
        if ($d_time >= self::EXPRESS_EXPIRE && $r_info['user_order_type'] != by::Omain()::USER_ORDER_TYPE['BAT']) {
            // return [false, '该退款单超时，已关闭'];
        }

        $save   = [
            'mail_no'       => $mail_no,
            'express_code'  => $express_code,
            'express_name'  => $express_name,
        ];

        list($s, $m) = $this->UpdateData($user_id, $refund_no, $save);

        if (!$s) {
            return [false, '操作失败'];
        }

        //todo 推送OMS物流信息 填完物流信息
        by::OrefundMain()->refundPushOms($r_info['m_type']??0,$user_id,$refund_no,$r_info['order_no']);

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @param $u_data
     * @return array
     * @throws Exception
     * 更改数据
     */
    public function UpdateData($user_id, $refund_no, $u_data)
    {
        if (empty($user_id)) {
            return [false, '参数错误'];
        }
        if (empty($refund_no)) {
            return [false, '请输入正确的单号'];
        }
        //允许修改的字段
        $allowed = ['mail_no','express_code','express_name','rback'];

        foreach ($u_data as $field => $val) {
            if ( !in_array($field, $allowed) ) {
                unset($u_data[$field]);
            }
        }

        if (empty($u_data)) {
            return [false, '无字段更改'];
        }

        $tb     = self::tbName($user_id);

        by::dbMaster()->createCommand()->update($tb, $u_data, ['refund_no' => $refund_no])->execute();

        //缓存清理
        $this->DelCache($user_id, $refund_no);

        //todo 推送OMS物流信息 填完物流信息
        $r_info = by::Orefund()->CommPackageInfo($user_id, $refund_no, false, true);
        if ((isset($u_data['mail_no']) || isset($u_data['express_code'])) && $r_info['express_code'] && $r_info['mail_no']) {
            by::OrefundMain()->refundPushOms($r_info['m_type'] ?? 0, $user_id, $refund_no, $r_info['order_no']);
        }

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $file
     * @param array $arr
     * @return array
     * @throws Exception
     * 上传退款说明图片
     */
    public function FileSave($user_id,$file, array $arr)
    {
        if(empty($file)) {
            return [false,"参数错误~"];
        }

        $order_no       = $arr['order_no']  ?? '';

        $unique_key = CUtil::getAllParams(__FUNCTION__,$order_no);
        list($s)    = by::model("CommModel",MAIN_MODULE)->AccFrequency($user_id,$unique_key,43200,"EX",self::UPLOAD_NUM);
        if (!$s) {
            CUtil::json_response(-1,'图片上传达到限制，明天再来吧！');
        }

        //文件上传
        list($status,$ret) = AliYunOss::factory()->uploadFileToOss($file, AliYunOss::IMG_TYPE, true, 3);
        if(!$status) {
            return [false,$ret];
        }

        return [true, $ret];
    }

    /**
     * @param $user_id
     * @param $arr
     * @return array
     * @throws Exception
     * 取消申请
     */
    /*public function Cancel($user_id, $arr)
    {
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 2);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        $refund_no      = $arr['refund_no'] ?? '';
        $order_no       = $arr['order_no']  ?? '';

        $r_info         = $this->GetInfoByRefundNo($user_id,$refund_no);

        $mOrefundMain   = by::OrefundMain();
        if (empty($r_info) || $r_info['status'] != $mOrefundMain::STATUS['AUDIT']) {
            return [false, '该退款单不能取消'];
        }

        $db             = by::dbMaster();
        $trans          = $db->beginTransaction();

        try {
            //退款申请表修改
            list($s, $m) = $mOrefundMain->SyncInfo($user_id,$refund_no,$mOrefundMain::STATUS['CANCELED'],$arr);
            if (!$s) {
                throw new \Exception($m);
            }

            //退款商品表数据
            $rGoods         = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
            $ids            = array_column($rGoods, 'og_id');

            //订单商品状态修改
            $mOgoods        = by::Ogoods();
            list($s, $m)    = $mOgoods->UpdateStatus($user_id, $order_no, $ids, $mOgoods::STATUS['NORMAL']);
            if (!$s) {
                throw new \Exception($m);
            }

            //订单状态修改
            list($s, $m)    = by::Omain()->SyncInfo($user_id, $order_no,$r_info['ostatus']);
            if (!$s) {
                throw new \Exception($m);
            }

            $trans->commit();
            return [true, 'ok'];

        } catch (\Exception $e) {

            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.refund.can');
            return [false, '取消失败'];
        }

    }*/

    /**
     * @param $user_id
     * @return false|string|null
     * @throws Exception
     * 获取用户累计退款金额
     */
    public function getOrderAmount($user_id)
    {
        $redis     = by::redis('core');
        $r_key     = $this->__getRePrice($user_id);
        $sum       = $redis->get($r_key);

        if($sum === false) {
            $tb     = self::tbName($user_id);
            $status = self::STATUS['success'];
            $sql    = "select `price` from {$tb} where `user_id`={$user_id} and `status`={$status}";
            $list   = by::dbMaster()->createCommand($sql)->queryAll();
            $sum = 0;
            foreach ($list as $value) {
                $sum = bcadd($sum,$value['price'],2);
            }

            by::redis('core')->set($r_key,$sum);
            CUtil::ResetExpire($r_key,1800);
        }

        return floatval($sum);
    }
}
