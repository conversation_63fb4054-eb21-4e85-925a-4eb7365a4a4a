<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\PlatformModel;


class Gtype99Model extends CommModel
{

    public $tb_fields = [
        'gid', 'mprice', 'price', 'cover_image', 'market_image', 'images','is_recommend',
        'is_presale', 'presale_time', 'deposit', 'expand_price', 'start_payment', 'end_payment', 'surplus_time',
        'scheduled_number', 'coefficient', 'is_coupons', 'limit_num', 't_status', 't_time', 'detail', 'video',
        'introduce', 'parameters', 'platform', 'is_internal_purchase','sell_point'
    ];

    const DECIMAL_RANGE = "99999999.99"; //价格上限
    const ImageLimit    = 10; //商品图片数量上限

    const ATYPE = [
        'SPEC'  => 0, //统一规格
        'SPECS' => 1, //自定义
    ];

    const PLATFORM = [
        'WX'  => 1, // 微信小程序
        'APP' => 2, // APP/H5
        'PC'  => 3, // PC商城
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gtype_99`";
    }

    public static function tableName(): string
    {
        return  self::tbName();
    }

    /**
     * @param $gid
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getOneGtypeKey($gid): string
    {
        return AppCRedisKeys::getOneGtypeByGid($gid);
    }

    /**
     * @param $gid
     * @return int
     * 缓存清理
     * @throws \yii\db\Exception
     */
    private function __delCache($gid): int
    {
        $r_key1 = $this->__getOneGtypeKey($gid);

        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $gid
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 商品主表增改
     */
    public function SaveLog(int $gid, array $aData)
    {
        $id                = $aData['id']           ?? 0;
        $market_image      = $aData['market_image'] ?? "";//营销图
        $cover_image       = $aData['cover_image']  ?? "";
        $images            = $aData['images']       ?? "";
        $detail            = $aData['detail']       ?? "";
        $pc_cover_image    = $aData['pc_cover_image'] ?? ""; //PC简洁图
        $pc_images         = $aData['pc_images']    ?? "";   //PC商品图
        $pc_detail         = $aData['pc_detail']    ?? "";   //PC商品详情
        $id                = CUtil::uint($id);
        $platform_ids      = explode(',', $aData['platform_ids'] ?? ''); // 平台
        if (empty($cover_image)) {
            return [false, "请上传商品主图"];
        }
        if (empty($images)) {
            return [false, "请上传商品图"];
        }
        if (substr_count($images, "|") > self::ImageLimit) {
            return [false, "商品图片数量最多" . self::ImageLimit . "张"];
        }
        if (empty($platform_ids)) {
            return [false, "请选择平台"];
        }
        //平台选择PC，PC图片必填
        if (in_array(self::PLATFORM['PC'], $platform_ids)) { // 平台包含PC商城
            if (empty($pc_cover_image)) {
                return [false, "请上传PC封面图"];
            }
            if (empty($pc_images)) {
                return [false, "请上传PC主图"];
            }
            if (empty($pc_detail)) {
                return [false, "请上传PC商品详情"];
            }
            // 上传数量
            if (count(explode('|', $pc_cover_image)) != 1) {
                return [false, "PC封面图最多可上传1张"];
            }
            if (count(explode('|', $pc_images)) > 10) {
                return [false, "PC主图最多可上传10张"];
            }
        }


        //参数过滤
        $mprice            = $aData['mprice']           ?? 0;
        $price             = $aData['price']            ?? 0;
        $atype             = $aData['atype']            ?? 0;
        $is_coupons        = $aData['is_coupons']       ?? 0;
        $is_recommend      = $aData['is_recommend']     ?? 0;
        $platform          = $aData['platform']         ?? 99;
        $custom_tag        = $aData['custom_tag']       ?? '';
        $parameters        = $aData['parameters']       ?? '';
        $introduce         = $aData['introduce']        ?? '';
        $limit_num         = $aData['limit_num']        ?? 0;
        $t_status          = $aData['t_status']         ?? 0;
        $t_time            = $aData['t_time']           ?? 0;
        $tid               = $aData['tid']              ?? ""; //商品标签
        $video             = $aData['video']            ?? ""; //商品视频
        $is_internal_purchase = $aData['is_internal_purchase'] ?? 0; //是否参与内购
        $is_presale           = $aData['is_presale']           ?? 1; //是否参与预售
        $presale_time         = $aData['presale_time']         ?? 0; //预售截止时间
        $deposit              = $aData['deposit']              ?? 0; //定金价格
        $expand_price         = $aData['expand_price']         ?? 0; //膨胀价格
        $start_payment        = $aData['start_payment']        ?? 0; //尾款付款开始时间
        $end_payment          = $aData['end_payment']          ?? 0; //尾款付款结束时间
        $surplus_time         = $aData['surplus_time']         ?? 0; //距尾款时间推送
        $scheduled_number     = $aData['scheduled_number']     ?? 0; //预定人数
        $coefficient          = $aData['coefficient']          ?? 0; //预定增长系数
        $sellPoint            = $aData['sell_point']           ?? 0; //商品卖点

        $mprice            = sprintf("%.2f", $mprice);
        $price             = sprintf("%.2f", $price);
        $is_coupons        = CUtil::uint($is_coupons);
        $is_recommend      = CUtil::uint($is_recommend);
        $platform          = CUtil::uint($platform);
        $limit_num         = CUtil::uint($limit_num);
        $t_status          = CUtil::uint($t_status);
        $t_time            = CUtil::uint($t_time);
        $is_presale        = CUtil::uint($is_presale);
        $deposit           = sprintf("%.2f", $deposit);
        $expand_price      = sprintf("%.2f", $expand_price);
        $presale_time      = CUtil::uint($presale_time);
        $start_payment     = CUtil::uint($start_payment);
        $end_payment       = CUtil::uint($end_payment);
        $surplus_time      = sprintf('%.2f',$surplus_time);
        $scheduled_number  = CUtil::uint($scheduled_number);
        $coefficient       = CUtil::uint($coefficient);
        $is_internal_purchase = CUtil::uint($is_internal_purchase);

        //替换掉所有js标签
        $detail            = preg_replace("/<script[\s\S]*?<\/script>/i", "", $detail);
        $pc_detail         = preg_replace("/<script[\s\S]*?<\/script>/i", "", $pc_detail);

        $parameters        = preg_replace("/<script[\s\S]*?<\/script>/i", "", $parameters);
        $introduce         = preg_replace("/<script[\s\S]*?<\/script>/i", "", $introduce);

        if (bccomp($price, self::DECIMAL_RANGE, 2) > 0 || bccomp($price, 0, 2) <= 0) {
            return [false, "非法价格"];
        }

        if (bccomp($mprice, self::DECIMAL_RANGE, 2) > 0 || bccomp($mprice, 0, 2) <= 0) {
            return [false, "非法市场价"];
        }

        if (empty($tid)) {
            return [false, "请选择商品标签"];
        }


        if(mb_strlen($introduce)>250){
            return [false, "商品简介不能超过250个字！"];
        }

        if ($is_internal_purchase > 0 && empty($pcombines)) {
            return [false, "内购价格必填"];
        }

        if($is_internal_purchase && $is_presale == by::Gmain()::PTYPE['PRESALE']){
            return [false, "内购商品不允许参与预售！"];
        }

        if ($is_presale == by::Gmain()::PTYPE['PRESALE']) {

            // if ($atype == self::ATYPE['SPECS']) {
            //     return [false, "多规格商品不参与预售"];
            // }

            if (empty($deposit) || empty($expand_price)) {
                return [false, "预售的定金价格及膨胀价格必填"];
            }

            $price_half = bcdiv($price,2,2);
            if ($deposit > $price_half || $expand_price > $price_half){
                return [false, "预售的定金价格或膨胀价格不能超于日销价的50%"];
            }

            if ($presale_time < time()) {
                return [false, '请输入正确的预售的截止时间'];
            }

            if ($start_payment < $presale_time) {
                return [false, '请输入正确的尾款付款开始时间'];
            }

            if ($end_payment < $start_payment) {
                return [false, '请输入正确的尾款付款结束时间'];
            }

            if (empty($surplus_time)) {
                return [false, "距尾款推送时间必填"];
            }

            if (empty($coefficient)) {
                return [false, "预定增长系数必填"];
            }
        }

        $specsData = [];

        $sku            = $aData['sku'] ?? "";
        if (empty($sku)) {
            return [false, "商品编码不能为空(2)"];
        }

        $save = [
            'cover_image'          => $cover_image,
            'market_image'         => $market_image,
            'images'               => $images,
            'mprice'               => by::Gtype0()->totalFee($mprice),
            'price'                => by::Gtype0()->totalFee($price),
            'is_coupons'           => $is_coupons,
            'is_recommend'         => $is_recommend,
            'platform'             => $platform,
            'custom_tag'           => $custom_tag,
            'parameters'           => $parameters,
            'introduce'            => $introduce,
            'is_internal_purchase' => $is_internal_purchase,
            'limit_num'            => $limit_num,
            't_status'             => $t_status,
            't_time'               => $t_time,
            'detail'               => $detail,
            'video'                => $video,
            'is_presale'           => $is_presale,
            'presale_time'         => $presale_time,
            'deposit'              => by::Gtype0()->totalFee($deposit),
            'expand_price'         => by::Gtype0()->totalFee($expand_price),
            'start_payment'        => $start_payment,
            'end_payment'          => $end_payment,
            'surplus_time'         => $surplus_time,
            'scheduled_number'     => $scheduled_number,
            'sell_point'           => $sellPoint
        ];

        $tb    = $this->tbName();
        $db    = by::dbMaster();
        $trans = $db->beginTransaction();

        try {

            if ($id) {
                $aInfo = $this->GetOneBaseByGid($gid);
                if (empty($aInfo)) {
                    throw new MyExceptionModel('数据不存在');
                }
                //自定义价格生效中不改价格
                $isIni = $aInfo['is_ini'] ?? 0;
                if($isIni) unset($save['price']);

                if($isIni && $is_internal_purchase){
                    throw new MyExceptionModel('自定义商品不允许参与内购');
                }

                $db->createCommand()->update($tb, $save, "`gid`=:gid", [":gid" => $gid])->execute();

                $this->__delCache($gid); //todo 清空商品详情缓存

            } else {
                $save['gid'] = $gid;
                $db->createCommand()->insert($tb, $save)->execute();
            }
            //商品标签
            list($s, $m) = by::model('GtagModel', 'goods')->SaveLog($gid, $aData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //预售库存
            list($s, $m) = by::Gprestock()->SaveLog($gid, $aData, $specsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //商品库存
            list($s, $m) = by::GoodsStockModel()->SaveLog($gid, $aData, $specsData, by::GoodsStockModel()::SOURCE['MAIN']);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            // 商品平台
            $platformData = by::Gtype0()->getPlatformData($gid, $platform_ids, $market_image, $cover_image, $images, $detail, $pc_cover_image, $pc_images, $pc_detail);
            list($s, $m) = byNew::GoodsPlatformModel()->batchUpdateOrCreate($platformData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            $trans->commit();

            return [true, $save];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gtype99');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.gtype99');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $gid
     * @param bool $format_price 转化格式为元
     * @return array|false
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 获取指定详情信息
     */
    public function GetOneBaseByGid($gid, $format_price = true, $spriceType=0, $aMainsku='')
    {
        $gid = CUtil::uint($gid);
        if ($gid <= 0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneGtypeKey($gid);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson, true);

        if ($aJson === false) {
            // 关联查询平台数据
            $aData = self::find()
                ->with('platforms')
                ->where(['gid' => $gid])
                ->asArray()
                ->one();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        // 处理商品数据
        $aData = by::Gtype0()->handleGoods($aData, $format_price);

        // 待优化，放在handleGoods()中
        //获取当前的设置价格
        $aData['pcombines'] = by::Gsprice()->GetListBySku($aMainsku,$format_price);
        $isInternal = 0;
        if (($aData['is_internal_purchase'] ?? 0) && $aData['pcombines']) { // 内购商品，取内购价格
            $pcombines      = array_column($aData['pcombines'], 'sprice', 'sprice_type');
            $aData['price'] = floatval($pcombines[1] ?? 0) > 0 ? $pcombines[1] : $aData['price'];
            $isInternal     = 1;
        }
        //获取自定义价格
        $aData = by::Gini()->getGiniPriceBySku($aData,$aMainsku,$format_price,$isInternal);

        $aData['is_internal'] = $isInternal;


        //默认统一规格，便于前端处理
        $aData['atype'] = '0';

        //判断预售状态
        $time              = intval(START_TIME);
        $aData['d_status'] = 0;
        $presale_time      = $aData['presale_time'] ?? 0;
        $start_payment     = $aData['start_payment'] ?? 0;
        $end_payment       = $aData['end_payment'] ?? 0;
        if(!empty($presale_time) && !empty($start_payment) && !empty($end_payment)){
            if($time < $presale_time){
                $aData['d_status'] = 1;//预售中
            }elseif($time >= $start_payment && $time < $end_payment){
                $aData['d_status'] = 2;//支付尾款中
            }else{
                $aData['d_status'] = 3;//预售结束
            }
        }


        $scheduled_number_now          = by::Gtype0()->getScheduledNumber($aData['gid'], $aData['scheduled_number'] ?? 0, $aData['coefficient'] ?? 0);
        $aData['scheduled_number_now'] = $scheduled_number_now;

        return $aData;
    }


    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public function totalFee($price, $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }

    /**
     * @param $gid
     * @return array|false
     * @throws \yii\db\Exception
     * 获取指定详情信息
     */
    public function GetOneByGid($gid, $spriceType = 0,$aMainsku ='')
    {
        $aData = $this->GetOneBaseByGid($gid,true,$spriceType,$aMainsku);
        if (empty($aData)) {
            return [];
        }

        //库存
        $sku      = $aMainsku;
        $hasStock = empty($sku) ? 0 : by::GoodsStockModel()->OptStock($sku);
        $aData['stock'] = $hasStock;

        return $aData;
    }

    /**
     * @return bool
     * @throws \yii\db\Exception
     * 定时上下架
     */
    public function UpDown()
    {
        $db             = by::dbMaster();
        $tb             = self::tbName();
        $now_time       = intval(START_TIME);
        $gid            = 0;

        while (true) {

            $sql    = "SELECT `gid` FROM {$tb} WHERE `gid` > :gid AND `t_status`=1 AND `t_time` <= :now_time 
                       ORDER BY `gid` ASC LIMIT 100";

            $list   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':now_time' => $now_time])->queryAll();

            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $gid    = $end['gid'];

            foreach ($list as $v) {
                $g_info = by::Gmain()->GetOneByGid($v['gid']);
                if ($g_info['status'] == 0) {
                    $u_status = 1;
                } else {
                    $u_status = 0;
                }

                $trans  = $db->beginTransaction();

                try {
                    //修改上下架状态
                    list($s, $m) = by::Gmain()->UpdateData($v['gid'], ['status' => $u_status]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    //修改原数据完成上下架操作
                    list($s, $m) = $this->UpdateData($v['gid'], ['t_status' => 0]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    $trans->commit();
                } catch (\Exception $e) {

                    $trans->rollBack();

                    CUtil::debug($e->getMessage(), 'err.updown');
                }
            }

            usleep(1000);
        }

        return true;
    }

    /**
     * @param $gid
     * @param array $update
     * @return array
     * @throws \yii\db\Exception
     * 修改数据
     */
    public function UpdateData($gid, array $update)
    {
        $gid     = CUtil::uint($gid);
        //允许修改的字段
        if (empty($gid) || empty($update)) {
            return [false, '参数缺失'];
        }
        $allowed = ['t_status','is_presale'];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb      = self::tbName();

        by::dbMaster()->createCommand()->update(
            $tb,
            $update,
            ['gid' => $gid]
        )->execute();

        //强制清理缓存
        $this->__delCache($gid);

        return [true, 'ok'];
    }

    /**
     * @return bool
     * @throws \yii\db\Exception
     * 定时检测预售商品是否失效
     */
    public function presaleInvalid(): bool
    {
        $db             = by::dbMaster();
        $now_time       = intval(START_TIME);
        $tb             = self::tbName($now_time);

        while (true) {

            $sql    = "SELECT `gid` FROM {$tb} WHERE`is_presale`=:is_presale AND `presale_time`<:now_time 
                       ORDER BY `gid` ASC LIMIT 100";

            $list   = by::dbMaster()->createCommand($sql, [':is_presale'=>by::Gmain()::PTYPE['PRESALE'],':now_time' => $now_time])->queryAll();

            if (empty($list)) {
                break;
            }

            foreach ($list as $v) {
                $trans  = $db->beginTransaction();
                try {
                    //修改原数据完成恢复操作
                    list($s, $m) = $this->UpdateData($v['gid'], ['is_presale' => by::Gmain()::PTYPE['NO_PRESALE']]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    $trans->commit();
                } catch (\Exception $e) {

                    $trans->rollBack();

                    CUtil::debug($e->getMessage(), 'err.presaleinvalid');
                }
            }

            usleep(1000);
        }

        return true;
    }

    // 定义与 平台 的关联关系，一个商品对应多个平台
    public function getPlatforms()
    {
        return $this->hasMany(GoodsPlatformModel::class, ['gid' => 'gid'])
            ->select(['gid', 'platform_id', 'cover_image', 'images', 'detail', 'goods_type'])
            ->where(['goods_type' => GoodsPlatformModel::GOODS_TYPES['MALL']]); // 普通商品
    }
}
