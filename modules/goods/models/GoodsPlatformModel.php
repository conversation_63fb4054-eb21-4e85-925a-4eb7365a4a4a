<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;

class GoodsPlatformModel extends CommModel
{
    // 商品类型
    const GOODS_TYPES = [
        'MALL'          => 1, // 普通商品
        'POINTS'        => 2, // 积分商品
        'BUY_AFTER_PAY' => 3, // 先试后买
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_goods_platform`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function rules(): array
    {
        return [
            [['gid', 'platform_id', 'goods_type', 'ctime', 'utime'], 'integer'],
            [['cover_image'], 'string', 'max' => 255],
            [['images'], 'string', 'max' => 2000],
            [['detail'], 'string', 'max' => 10000],
        ];
    }

    /**
     * 更新或创建记录
     * @param int $id
     * @param array $data
     * @return int
     */
    public function updateOrCreate(int $id, array $data): int
    {
        if ($id) { // 更新
            $model = self::findOne($id);
            if (!$model) {
                return 0;
            }
            $model->setAttributes($data);
            $model->utime = time();
            if ($model->save()) {
                return $model->id;
            } else {
                return 0;
            }
        } else { // 创建
            $model = new GoodsPlatformModel();
            $data['ctime'] = time();
            $data['utime'] = time();
            $model->setAttributes($data);
            if ($model->save()) {
                return $model->id;
            } else {
                return 0;
            }
        }
    }


    /**
     * 批量更新或创建记录
     * @param array $items
     * @return array
     */
    public function batchUpdateOrCreate(array $items): array
    {
        $db = by::dbMaster();
        $transaction = $db->beginTransaction();
        try {
            $time = time();
            $gids = array_column($items, 'gid');
            $goods_types = array_column($items, 'goods_type');
            $platform_ids = array_column($items, 'platform_id');

            // 获取现有的记录
            $records = self::find()
                ->where(['gid' => $gids, 'goods_type' => $goods_types])
                ->all();

            $exist_data = [];
            foreach ($records as $record) {
                $exist_data[$record->gid][$record->goods_type][$record->platform_id] = $record;
            }

            // 更新、插入的数据
            $update_data = [];
            $insert_data = [];
            foreach ($items as $item) {
                if (isset($exist_data[$item['gid']][$item['goods_type']][$item['platform_id']])) {
                    // 更新记录
                    $record = $exist_data[$item['gid']][$item['goods_type']][$item['platform_id']];
                    $record->setAttributes($item);
                    $record->utime = $time;
                    $update_data[] = $record->getAttributes();
                } else {
                    // 插入记录
                    $item['ctime'] = $time;
                    $item['utime'] = $time;
                    $insert_data[] = $item;
                }
            }

            // 1、批量插入
            if (!empty($insert_data)) {
                $db->createCommand()
                    ->batchInsert(
                        self::tableName(),
                        ['gid', 'platform_id', 'cover_image', 'images', 'detail', 'goods_type', 'ctime', 'utime'],
                        $insert_data
                    )
                    ->execute();
            }

            // 2、批量更新
            if (!empty($update_data)) {
                foreach ($update_data as $record) {
                    $db->createCommand()
                        ->update(
                            self::tableName(),
                            [
                                'cover_image' => $record['cover_image'],
                                'images'      => $record['images'],
                                'detail'      => $record['detail'],
                                'utime'       => $record['utime']
                            ],
                            [
                                'gid'         => $record['gid'],
                                'goods_type'  => $record['goods_type'],
                                'platform_id' => $record['platform_id']
                            ]
                        )
                        ->execute();
                }
            }

            // 3、批量删除
            self::deleteAll([
                'AND',
                ['gid' => $gids],
                ['goods_type' => $goods_types],
                ['NOT IN', 'platform_id', $platform_ids]
            ]);

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
        return [true, 'ok'];
    }

    /**
     * 获取数据列表
     * @param array $fields
     * @param array $conditions
     * @param string $orderBy
     * @return array
     */
    public function getDataList(array $fields, array $conditions, string $orderBy = 'id ASC'): array
    {
        return self::find()
            ->select($fields)
            ->where($conditions)
            ->orderBy($orderBy)
            ->asArray()
            ->all();
    }

    /**
     * 商品是否属于此平台
     * @param array $gids
     * @param int $platform
     * @param int $goods_type
     * @return array
     */
    public function exist(array $gids, int $platform, int $goods_type = 1): array
    {
        $items = self::find()
            ->select(['gid'])
            ->where(['gid' => $gids, 'platform_id' => $platform, 'goods_type' => $goods_type])
            ->asArray()
            ->all();
        $status = array_column($items, 'gid');

        $data = [];
        foreach ($gids as $gid) {
            $s = true;
            if (!in_array($gid, $status)) {
                $s = false;
            }
            $data[$gid] = $s;
        }
        return $data;
    }

}