<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单主表
 */
namespace app\modules\goods\models;

use app\components\AliZhima;
use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\components\Crm;
use app\components\Erp;
use app\components\EventMsg;
use app\components\UserMsg;
use app\jobs\CancelOrderJob;
use app\jobs\CancelPayOrderJob;
use app\jobs\CancelTradeInOrderJob;
use app\jobs\SalesCommissionJob;
use app\jobs\UserShopMoneyJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use app\modules\goods\models\GroupPurchase\GroupPurchaseModel;
use app\modules\goods\services\DrawActivityService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\CommPayModel;
use app\modules\main\models\UserBindModel;
use app\modules\main\models\pay\MpPayModel;
use app\modules\main\services\AliPayService;
use app\modules\main\services\CashierService;
use app\modules\main\services\GroupPurchaseService;
use app\modules\main\models\UserEmployeeModel;
use app\modules\main\services\pay\Order;
use app\modules\main\services\GiftCardService;
use app\modules\main\services\TradeInService;
use app\modules\wares\models\GiftCardExpendRecordModel;
use app\modules\wares\models\GiftUserCardsModel;
use app\modules\wares\models\GoodsStockModel;
use RedisException;
use app\jobs\UserOrderTryJob;
use yii\db\DataReader;
use yii\db\Exception;
use yii\helpers\Json;


class OmainModel extends CommModel {

    public function test() {
        $tb_sql = "";
        for($i=0; $i<10; $i++) {

            $tb      = by::pointLog()::getTable($i);

//            $tb_sql = "CREATE TABLE IF NOT EXISTS {$tb} LIKE `db_bili_goods`.`t_orders_goods_0`";
            $tb_sql .= "ALTER TABLE {$tb} 
MODIFY COLUMN `detail` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细记录流水对应的时间' AFTER `ctime`,
ADD COLUMN `rpoint` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '已回滚的积分' AFTER `remark`;";

//            by::dbMaster()->createCommand($tb_sql)->execute();
        }

        echo $tb_sql;
    }

    CONST PAY_EXPIRE        = YII_ENV_PROD ? 1800 : 800; //可支付时间窗口
    CONST DB_TIMEZONE       = ['ST'=>2022,'ED'=>2030];//数据库的时间跨度
    const ERP_LIMIT_STOCK   = 100; //库存数小于多少查erp

    CONST PAY_BY_WX = 1;  // 微信JSAPI
    CONST PAY_BY_WX_H5 = 2; // 微信H5
    CONST PAY_BY_WX_APP = 3; //微信APP
    CONST PAY_BY_ALIPAY = 4; //支付宝支付
    CONST PAY_BY_ALI_ZHIMA = 6;
    CONST PAY_BY_MP_WX = 7; // 中台(微信支付)
    CONST PAY_BY_MP_ALIPAY = 8; // 中台(支付宝支付)
    CONST PAY_JD_BAITIAO=9; //中台（京东白条小程序)
    CONST PAY_BY_MP_WEB_WX_H5 = 10;    //中台(微信H5)
    CONST PAY_BY_MP_WEB_ALIPAY_H5 = 11;    //中台(支付宝H5)
    CONST PAY_JD_BAITIAO_APP=12; //中台(京东白条APP)
    CONST PAY_JD_BAITIAO_H5=13; //中台(京东白条H5)
    CONST PAY_JD_BAITIAO_PC=14; //中台(京东白条PC)


    CONST PAY_BY_NO_SET = 99; //未设置支付方式
    CONST LOCK_EXPIRE_TIME = 120;

    //取消原因
    const R_TYPE = [
        1 => '不想要了',
        2 => '价格有点贵',
        3 => '暂时不需要了',
        4 => '余额不足',
        5 => '拍错',
        6 => '忘记使用优惠券',
    ];

    CONST SOURCE = [
        0 => '普通下单',
        1 => '导购分销',
        2 => '推荐有礼',
        3 => '导购、推荐',
        4 => '多麦',
    ];

    const USER_ORDER_TYPE = [
            'COMMON'         => 1,  //普通订单
            'TAIL'           => 2,  //尾款订单 未用/实际为普通订单
            'DEPOSIT'        => 3,  //定金订单
            'POINT'          => 4,  //积分商城订单
            'OTN'            => 5,  //以旧换新订单
            'BAT'            => 6,  //先试后买订单
            'INTERNAL'       => 7,  //内购订单
            'GROUP_PURCHASE' => 8,  //拼团订单
    ];

    CONST USER_ORDER_TYPE_NAME = [
        1=> '普通订单',
        2=> '尾款订单',
        3=> '定金订单',
        4=> '积分订单',
        5=> '以旧换新订单',
        6=> '先试后买订单',
        7=> '内购订单',
        8=> '团购订单',
    ];

    const UNION_SOURCE = [
        'duomai'          => 4,
        'tencent_qz_gdt'  => 5, //腾讯-非微信流量
        'tencent_gdt_vid' => 6, //腾讯-微信流量
        'huolala'         => 7, //货拉拉
        'scrm'            => 8, //scrm
    ];

    /**
     * 订单关系类型（从那个页面下单）
     */
    const ORDER_RELATION_TYPE = [
            'COMMON'            => 1,   // 普通订单
            'GROUP_PURCHASE'    => 2,   // 拼团
            'MONEY_MAKING'      => 3,   // 赚钱花
            'ONE_YUAN_PURCHASE' => 4,   // 一元购
            'FIFTY_PERCENT'     => 5,   // 五折购
            'EIGHTY_PERCENT'    => 6,   // 八折购
            'POINT_SHOPPING'    => 7,   // 积分购物
            'HALF_PRICE'        => 8,   // 半价购
            'DEPOSIT_ORDER'     => 9,   // 预售订单
            'POINT_ORDER'       => 10,  // 积分商城订单
    ];

    CONST TAIL_ORDER_MSG_TYPE = [
      1,3
    ];


    /**
     * @param $order_no
     * @return mixed
     * 根据订单号获取user_id
     */
    private function __getUserIdKey($order_no) {
        return AppCRedisKeys::getUserIdByNo($order_no);
    }


    public $tb_fields = [
        'id', 'user_id', 'order_no', 'status', 'source', 'ctime', 'platform_source', 'end_payment', 'store', 'type'
    ];

    public static function tbName($time): string
    {
        $year = date("Y",intval($time));
        //防止出现超出时间范围的查询
        $year = max($year,self::DB_TIMEZONE['ST']);
        $year = min($year,self::DB_TIMEZONE['ED']);

        return  "`db_dreame_goods`.`t_om_{$year}`";
    }


    /**
     * @param $user_id
     * @param $order_no
     * @return string
     * 详情唯一缓存KEY
     */
    private function __getInfoByOrderNoKey($user_id, $order_no): string
    {
        return AppCRedisKeys::getOMInfoByOrderNo($user_id, $order_no);
    }

    /**
     * @param $user_id
     * @param $order_no
     * 清理详情缓存
     */
    public function delInfoCache($user_id, $order_no)
    {
        $r_key  = $this->__getInfoByOrderNoKey($user_id, $order_no);

        by::redis('core')->del($r_key);
    }

    private $__order_status = [
        'ALL'                       => -1,
        'WAIT_PAY'                  => 0,       //待支付
        'CANCELED'                  => 100,     //已取消
        'WAIT_SEND'                 => 300,     //待发货
        'WAIT_RECEIVE'              => 400,     //待收货
        'FINISHED'                  => 500,     //已完成
        'REFUNDING'                 => 10000000,//申请退款
        'REFUNDING_WAIT_SEND'       => 10000300,//申请退款&待发货
        'REFUNDING_WAIT_RECEIVE'    => 10000400,//申请退款&待收货
        'REFUNDING_FINISHED'        => 10000500,//申请退款&已完成
        'RERUNDED'                  => 20000000,//退款完成
        'RERUNDED_WAIT_SEND'        => 20000300,//退款完成&待发货
        'RERUNDED_WAIT_RECEIVE'     => 20000400,//退款完成&待收货
        'RERUNDED_FINISHED'         => 20000500,//退款完成&已完成
    ];

    CONST ORDER_STATUS      = [
        'ALL'                       => -1,
        'WAIT_PAY'                  => 0,       //待支付
        'CANCELED'                  => 100,     //已取消
        'WAIT_SEND'                 => 300,     //待发货
        'WAIT_RECEIVE'              => 400,     //待收货
        'FINISHED'                  => 500,     //已完成
        'REFUNDING'                 => 10000000,//申请退款
        'REFUNDING_WAIT_SEND'       => 10000300,//申请退款&待发货
        'REFUNDING_WAIT_RECEIVE'    => 10000400,//申请退款&待收货
        'REFUNDING_FINISHED'        => 10000500,//申请退款&已完成
        'RERUNDED'                  => 20000000,//退款完成
        'RERUNDED_WAIT_SEND'        => 20000300,//退款完成&待发货
        'RERUNDED_WAIT_RECEIVE'     => 20000400,//退款完成&待收货
        'RERUNDED_FINISHED'         => 20000500,//退款完成&已完成
    ];

    //订单状态中文-导出用
    CONST STATUS_NAME = [
        '0'         => '待支付',
        '100'       => '已取消',
        '300'       => '待发货',
        '400'       => '待收货',
        '500'       => '已完成',
        '10000000'  => '申请退款',
        '10000300'  => '申请退款&待发货',
        '10000400'  => '申请退款&待收货',
        '10000500'  => '申请退款&已完成',
        '20000000'  => '退款完成',
        '20000300'  => '退款完成&待发货',
        '20000400'  => '退款完成&待收货',
        '20000500'  => '退款完成&已完成',
    ];

    CONST ALL_REFUND         = 60000000;  //所有退款单
    CONST RERUND_DTS         = 30000000;  //驳回退款

    CONST ORDER_KEYS  = [
        '0'          => 'WAIT_PAY',
        '100'        => 'CANCELED',
        '300'        => 'WAIT_SEND',     //待发货
        '400'        => 'WAIT_RECEIVE',  //已发货(待收货)
        '500'        => 'FINISHED',      //已完成
        '10000000'   => 'REFUNDING',     //退款中
        '20000000'   => 'RERUNDED',      //退款完成
        '60000000'   => 'RERUND',        //所有退款单
    ];

    //已支付状态
    private $__payed_status  = [
        'WAIT_SEND'     => 300,  //待发货
        'WAIT_RECEIVE'  => 400,  //已发货(待收货)
        'FINISHED'      => 500,  //已完成
    ];

    private $__order_refund = [
        'REFUNDING'     => 10000000,  //申请退款
        'RERUNDED'      => 20000000,  //退款完成
    ];


    /**
     * @param int $type 退款、完成
     * @return array
     * 可操作状态
     */
    public function GetCanActStatus($type = self::ORDER_STATUS['REFUNDING'])
    {
        $code_arr = [];
        switch ($type) {
            case self::ORDER_STATUS['REFUNDING'] :
                foreach($this->__payed_status as $payed_status) {
                    $code_arr[]     = $payed_status;

                    foreach($this->__order_refund as $refund) {
                        $code_arr[]     = $refund + $payed_status;
                    }
                }
                break;

            case self::ORDER_STATUS['FINISHED'] :
                $code_arr[]         = self::ORDER_STATUS['WAIT_RECEIVE'];
                foreach($this->__order_refund as $refund) {
                    $code_arr[] = $refund + self::ORDER_STATUS['WAIT_RECEIVE'];
                }
                break;
        }

        return $code_arr;
    }

    /**
     * @param $key
     * @param bool $sql
     * @return array|mixed
     * 获取状态查询用
     */
    public function GetOrderStatus($key, $sql = false)
    {
        $return     = [];
        $index      = self::ORDER_KEYS[$key] ?? '';
        if (empty($index)) {
            if ($sql) {
                $return = ['', []];
            }

            return $return;
        }

        switch (true) {
            case $key < $this->__order_status['WAIT_SEND'] :

                if ($sql) {
                    $where              = " `status` = :status";
                    $params[':status']  = $this->__order_status[$index];

                    $return             = [$where, $params];
                } else {
                    $return             = [$this->__order_status[$index]];
                }
                break;

            case $key < $this->__order_status['REFUNDING'] :
                $code_arr           = [];
                $code_arr[]         = $wait = $this->__payed_status[$index];
                foreach($this->__order_refund as $refund) {
                    $code_arr[] = $refund + $wait;
                }

                if ($sql) {
                    $status             = implode(',', $code_arr);
                    $where              = " `status` IN ({$status})";
                    $return             = [$where, []];
                } else {
                    $return             = $code_arr;
                }
                break;

            case $key < self::ALL_REFUND :
                $st                 =  $this->__order_status[$index];
                $ed                 = $this->__order_status[$index] + $this->__order_status['REFUNDING'];
                break;

            default :
                $st                 = $this->__order_refund['REFUNDING'];
                $ed                 = $this->__order_refund['RERUNDED'] + $this->__order_refund['REFUNDING'];

        }

        if (isset($st, $ed)) {
            if ($sql) {
                $where              = " `status` BETWEEN :s_st AND :s_ed";
                $params[":s_st"]    = $st;
                $params[":s_ed"]    = $ed;
                $return             = [$where, $params];
            } else {
                $return             = [$st, $ed];
            }
        }

        return $return;

    }

    public function SplitOrderStatus($code): array
    {
        $fist   = 0;    //第一状态（退款中、退款完成、退款驳回）
        if ($code >= $this->__order_status['REFUNDING']) {
            $s_temp     = intval(substr($code, -3));
            $second     = $s_temp > 0 ? $s_temp : $code;
            $fist       = $code - $s_temp;
        } else {
            $second     = $code;
        }

        $fist       = (string)$fist;
        $second     = (string)$second;

        return [$fist, $second];
    }

    /**
     * @param $code
     * @param $target
     * @param bool $is_refund
     * @param bool $complete
     * @param int $ostatus
     * @return int
     * 状态转换
     * * *1、   一个商品退一个
     * 待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 待发货
     *2、 两个商品退一个
     * 300->申请退款->10000300->通过->20000300
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 300->申请退款->10000300->驳回->300
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改状态为 待发货
     *2.1、 两个商品继续退一个
     * 20000300 ->申请退款->10000000->通过->20000000
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 退款完成&待发货
     *3     三个商品退一个
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改状态为 待发货
     *3.1   三个商品继续退一个
     * 20000300 ->申请退款->10000300->通过->20000300
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 通过 ->改状态为 退款完成&待发货
     * 20000300 ->申请退款->10000300->驳回->20000300
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款&待发货 -> 驳回 ->改原状态为 退款完成&待发货
     *3.2   三个商品再继续退一个(都退)
     * 20000300 ->申请退款->10000000->通过->20000000
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 通过 ->改状态为 退款完成
     * 退款完成&待发货 ->申请退款 ->改状态为 申请退款 -> 驳回 ->改原状态为 退款完成&待发货
     */
    public function SetOrderStatus($code, $target, $is_refund=false, $complete=false, $ostatus=-1)
    {
        $c_code     = -1;
        if ($is_refund && $complete) {
            switch ($target) {
                case self::ORDER_STATUS['REFUNDING'] :
                case self::ORDER_STATUS['RERUNDED'] :
                    $c_code = $target;break;
                case self::RERUND_DTS :
                    $c_code = $ostatus;break;
            }

        } elseif ($is_refund && $complete ==false) {

            switch ($target) {
                //10000300->通过->20000300
                case self::ORDER_STATUS['REFUNDING'] :
                case self::ORDER_STATUS['RERUNDED'] :
                    list($fist, $second) = $this->SplitOrderStatus($code);
                    if ($fist > 0 && $second == 0) {
                        if ($target >= $this->__order_status['REFUNDING']) {
                            $c_code = $target;
                        } else {
                            $c_code = $fist + $target;
                        }
                    } else {
                        if ($second >= $this->__order_status['REFUNDING']) {
                            $c_code = $second;
                        } else if ($target >= $this->__order_status['REFUNDING']) {
                            $c_code = $second + $target;
                        } else {
                            $c_code = $target;
                        }
                    }
                    break;

                    //10000300->驳回->20000300 || 10000300
                case self::RERUND_DTS :
                    $c_code = $ostatus;break;
            }

        } else {
            list($fist, $second) = $this->SplitOrderStatus($code);

            if ($fist > 0 && $second == 0) {
                if ($target >= $this->__order_status['REFUNDING']) {
                    $c_code = $target;
                } else {
                    $c_code = $fist + $target;
                }
            } else if ($fist > 0) {
                if ($target >= $this->__order_status['REFUNDING']) {
                    $c_code = $second + $target;
                } else {
                    $c_code = $fist + $target;
                }
            } else {
                $c_code = $target;
            }
        }

        return (int)$c_code;
    }


    /**
     * @param $time
     * @return string
     * 生成唯一订单号
     */
    public function CreateOrderNo($time): string
    {
        $ym             = date('ym', intval($time));
        $date           = date("Ymd");
        $unix           = sprintf("%.3f",microtime(true));
        list($now,$mil) = explode('.',$unix);

        $config         = CUtil::getConfig('hostid','common',MAIN_MODULE);
        $hostid         = $config[HOST_NAME] ?? mt_rand(0, 99);
        $hostid         = sprintf("%02d", $hostid);//有些项目服务器数量可能两位数
        $rand           = mt_rand(10, 99);//防止时间回拨，减少碰撞概率

        $second         = $now - strtotime('today');
        $second         = sprintf("%05d", $second);
        $pid            = sprintf("%05d", getmypid());
        $oid            = "{$date}{$second}{$pid}{$hostid}{$rand}{$ym}{$mil}";
        usleep(1000);//保证1ms一个
        return $oid;
    }

    /**
     * @param string $order_no
     * @param bool $return_tb
     * @return string
     * 根据订单号找到数据表
     */
    public static function GetTbNameByOrderId($order_no='', bool $return_tb = true): string
    {
        if(empty($order_no)) {
            return "";
        }

        $ym     = substr($order_no,-7,4);//订单抽选时间所属年份
        $time   = strtotime(date("20{$ym}d"));

        if ($return_tb == false) {
            return $time;
        }

        return self::tbName($time);
    }



    /**
     * @param $order_no
     * @return int|string
     * @throws Exception
     * 根据订单号获取user_id
     */
    public function GetUserIdByNo($order_no)
    {
        if (empty($order_no)) {
            return '';
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getUserIdKey($order_no);
        $user_id     = $redis->get($redis_key);

        if ($user_id === false) {
            $tb         = $this->GetTbNameByOrderId($order_no);

            $sql        = "SELECT `user_id` FROM {$tb} WHERE `order_no` = :order_no LIMIT 1";

            $user_id    = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->queryScalar();

            $redis->set($redis_key, $user_id, ['EX' => empty($user_id) ? 10 : 600]);
        }

        return intval($user_id);
    }


    /**
     * @throws Exception
     */
    public function updateDataByOrderNo($order_no, $data)
    {
        if (empty($order_no)||empty($data)) {
            return '';
        }
        $updateData = [];
        $tb         = $this->GetTbNameByOrderId($order_no);
        //查询数据存不存在
        if(empty($this->GetUserIdByNo($order_no))){
            return '';
        }

        //订单类型更新
        if(isset($data['type'])){
            $updateData['type'] = intval($data['type']);
        }
        //进行数据更新
        if(isset($data['state'])){
            $updateData['state'] = intval($data['state']);
        }
        if(isset($data['data'][0]['status'])){
            $updateData['advance_status'] = json_encode(trim($data['data'][0]['status']));
        }
        if(isset($data['data'][0]['statusCode'])){
            $updateData['advance_status_code'] = intval($data['data'][0]['statusCode']);
        }
        if (isset($data['is_sync_erp'])){
            $updateData['is_sync_erp'] = intval($data['is_sync_erp']);
        }
        if($updateData){
            by::dbMaster()->createCommand()->update($tb, $updateData, ['order_no' => $order_no])->execute();
        }
        CUtil::debug('update_'.$tb.'|'.$order_no.'|'.json_encode($updateData),'update.express');
        return true;
    }

    /**
     * @param $user_id
     * @param $deposit_order_no
     * @return array
     * @throws Exception
     * @throws RedisException
     * 创建尾款订单
     */
    public function addDepositTailRecord($user_id, $deposit_order_no): array
    {
        //频率限制
        $unique_key = __FUNCTION__;

        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,5,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        //获取定金订单详情
        $dInfo    = by::Odeposit()->getInfoByDepositOrderNo($user_id,$deposit_order_no,false,true,true,false);
        if(empty($dInfo)){
            return [false, "不存在定金订单！"];
        }

        //获取定金订单扩展数据
        $deInfo = by::OdepositE()->GetInfoByOrderId($user_id,$deposit_order_no,false);
        //入表扩展数据
        $platform_source = $deInfo['platform_source'] ?? 0;
        $source          = $deInfo['source'] ?? 0;
        $r_id            = $deInfo['r_id'] ?? 0;
        $guide_id        = $deInfo['guide_id'] ?? 0;
        $guide_type      = $deInfo['guide_type'] ?? 1;
        $union           = $deInfo['union'] ?? '';
        $euid            = $deInfo['euid'] ?? '';
        $live_mark       = $deInfo['live_mark'] ?? '';
        $referer         = $deInfo['referer'] ?? '';

        if ($r_id){
            $osourcer = by::osourceR()->getInfoByOrderNo($user_id,$deposit_order_no);
        }


        //获取用户定金订单地址数据
        $addressInfo = by::Oad()->GetOneByOrderNo($deposit_order_no);
        $cid = $addressInfo['cid'] ?? 0;


        $data = [
            'note'  => $dInfo['deposit_note'] ?? '',
        ];


        $gcombines[0] = [
            'gid'       => $dInfo['gid'] ?? 0,
            'sid'       => $dInfo['sid'] ?? 0,
            'num'       => $dInfo['num'] ?? 0,
        ];


        //生成数据校验
        list($s, $gcombines)  = $this->__depositCheck($user_id, $gcombines, $dInfo, $deInfo);
        if (!$s) {
            return [false, $gcombines];
        }

        $goodsStockModel = by::GoodsStockModel();

        //addRecord 创建很多表
        $order_no = "";
        $status = false;
        $ctime     = intval(START_TIME);
        $db        = by::dbMaster();
        $trans     = $db->beginTransaction();

        try {
            //尝试三次创建订单
            for ($i=0;$i<3;$i++) {
                $order_no               = $this->CreateOrderNo($ctime);
                list($status,$order_no) = $this->__safeInsert($db, $order_no, $user_id, $ctime, $guide_id , $r_id , $ctime ,$source ,$platform_source,$deposit_order_no);
                if ($status) {
                    break;
                }
            }

            if(!$status) {
                throw new \Exception($order_no, 2001);
            }

            $oprice = 0; //总原价
            $nprice = 0; //实际总原价
            $exprice = 0; //膨胀价格
            $reduce = 0; //总优惠价
            $shippingData=[];
            foreach ($gcombines as $v) {
                //todo 更新商品库存
//                list($s)    = by::Gstock()->UpdatePreSaleStock($v['gid'], $v['sid'], $v['num']);
                list($s)    = $goodsStockModel->UpdatePreSaleStock($v['gid'], $v['sid'], $v['num'],$goodsStockModel::SOURCE['MAIN']);
                if (!$s) {
                    throw new MyExceptionModel('预售商品新增待支付库存失败~~', 2001);
                }

                $oprice    = bcadd($oprice, $v['tprice']);
                $nprice    = bcadd($nprice, $v['tnprice']);
                $exprice    = bcadd($exprice, $v['texprice']);

                $gInfo = by::Gmain()->GetOneByGidSid($v['gid'], $v['sid'], true, true);

                $shippingData[] = [
                        'gid' => $v['gid'],
                        'sid' => $v['sid'],
                        'num' => $v['num'],
                        'price' => $gInfo['spec']['price'] ?? $gInfo['price'],
                        'is_free_shipping' => $gInfo['is_free_shipping'] ?? 0,
                ];
            }

            $price      = bcsub($nprice, $reduce);

            //运费权益
            $fprice=    by::Ouser()->shippingPrice($shippingData, $user_id, 0, $freight_other ?? [], $price,'presale',$cid);

            $pay_price  = bcadd($price, $fprice);

            if (bccomp($pay_price, 0) <= 0) {
                throw new MyExceptionModel('实付金额不能为0');
            }


            //导购表数据
            if (!empty($guide_id)) {
                $sourceData = [
                    'user_id' => $user_id,
                    'price'   => $price,
                    'ctime'   => $ctime,
                    'type'    => $guide_type,//内购类型
                ];
                list($s, $m) = by::Osource()->SaveLog($order_no,$guide_id,$sourceData);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
            }

            //推荐表数据
            if (!empty($r_id) && $r_id != $user_id) {
                $sourceData = [
                    'user_id'     => $user_id,
                    'r_id'        => $r_id,
                    'order_no'    => $order_no,
                    'price'       => $price,
                    'pay_price'   => $pay_price,
                    'ctime'       => $ctime,
                    'r_type'      => $osourcer['r_type'] ?? 1
                ];

                list($status, $res) = by::osourceR()->SaveLog($sourceData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }

            //渠道来源表
            if(!empty($union)||!empty($live_mark)||!empty($referer)){
                $sourceMData= [
                    'user_id'     => $user_id,
                    'union'       => $union,
                    'euid'        => $euid,
                    'order_no'    => $order_no,
                    'price'       => $price,
                    'live_mark'   => trim($live_mark),
                    'referer'     => $referer,
                    'ctime'       => $ctime
                ];
                list($status, $res) = by::osourceM()->SaveLog($sourceMData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }


            //订单表数据
            $ouserData = [
                    'oprice'           => $oprice,
                    'price'            => $price,
                    'fprice'           => $fprice,
                    'exprice'          => $exprice,
                    'deposit_price'    => $dInfo['price'] ?? 0,
                    'deposit_order_no' => $deposit_order_no,
                    'coupon_id'        => 0,
                    'cprice'           => 0,
                    'coin'             => 0,
                    'coin_price'       => 0,
                    'coin_logid'       => 0,
                    'note'             => $data['note'] ?? '',
                    'ctime'            => $ctime,
                    'relation_type'    => self::ORDER_RELATION_TYPE['DEPOSIT_ORDER'],
                    'relation_id'      => 0,
            ];
            by::Ouser()->SaveLog($user_id, $order_no, $ouserData);

            //地址表数据

            if($addressInfo){
                $save    = [
                    'order_no'      => $order_no,
                    'address_id'    => $addressInfo['address_id'] ?? 0,
                    'nick'          => $addressInfo['nick'] ?? '',
                    'phone'         => $addressInfo['phone'] ?? '',
                    'pid'           => $addressInfo['pid'] ?? '',
                    'cid'           => $addressInfo['cid'] ?? '',
                    'aid'           => $addressInfo['aid'] ?? '',
                    'detail'        => $addressInfo['detail'] ?? '',
                    'address'       => json_encode($addressInfo['address'] ?? []),
                ];
                $tb = by::Oad()->tbName($order_no);
                by::dbMaster()->createCommand()->insert($tb, $save)->execute();
            }

            //订单商品表数据
            $oGoodsData = [
                'coupon_id'       => 0,
                'cprice'          => 0,
                'coin'            => 0,
                'coin_price'      => 0,
                'tprice'          => $price,
                'exprice'         => $exprice,
                'gift_card_value' => 0,
                'gift_card_type'  => 0,
            ];
            list($s, $m) = by::Ogoods()->SaveLog($user_id, $order_no, $gcombines, $oGoodsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //订单商品快照数据
            $cfgData = [
                'ctime'     => $ctime,
                'gcombines' => $gcombines,
            ];
            by::Ocfg()->SaveLog($user_id, $order_no, $cfgData, 0);

            // 当前购买的用户，有绑定的推荐人
            $this->saveUserBindOrder($user_id, $order_no, self::USER_ORDER_TYPE['TAIL']); // 尾款订单


            //保存拉起付款参数，重新支付用
            $payData = [
                'prepay_id' => '', 'price' => $pay_price, 'pay_type' => self::PAY_BY_NO_SET, 'h5_url' => ''];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

            $trans->commit();

            //todo 创建推送消息
            foreach ($gcombines as $gcombine){
                $gInfo = by::Gmain()->GetOneByGidSid($gcombine['gid'], $gcombine['sid'], false);
                $this->_createUserMsg($user_id,$order_no,$gInfo);
            }

            //todo 清理订单表缓存
            //清理订单列表缓存
            by::Ouser()->DelListCache($user_id);
            by::Ouser()->DelPointCache($user_id);

            //防止有刷缓存问题
            by::Ouser()->DelInfoCache($user_id,$order_no);
            by::Ouser()->DelInfoCacheByDeposit($user_id,$deposit_order_no);

            //todo 清理订单商品表缓存
            by::Ogoods()->DelListCache($order_no);
            by::Ogoods()->DelList1Cache($user_id);

            //todo 清理订单商品快照表缓存
            by::Ocfg()->DelCache($order_no);

            //todo 清理订单主表信息
            by::Omain()->delInfoCache($user_id,$order_no);

            //腾讯推送
            //todo 腾讯推送
            by::userAdv()->pushAdv($user_id, $order_no, 'COMPLETE_ORDER');

            return [true,$order_no];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $this->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['MALL']);

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            $this->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['MALL']);

            return [false, '网络繁忙,请稍候'];
        }

    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $gInfo
     * @return void
     * 创建用户推送消息
     */
    private function _createUserMsg($user_id,$order_no,$gInfo){
        //拼接数据
        $startPayment = CUtil::uint($gInfo['start_payment'] ?? 0); //尾款付款开始时间
        $endPayment = CUtil::uint($gInfo['end_payment'] ?? 0);//尾款付款结束时间
        $surplusTime = CUtil::uint($gInfo['surplus_time'] ?? 0);//距尾款时间推送（第二次推送）
        $good_name = $gInfo['name'] ?? '';

        $tailOrderMsgType = self::TAIL_ORDER_MSG_TYPE;

        $baseArr = [
            'user_id'=>$user_id,
            'need_send'=>1,
            'resource'=>[
                'order_no'=>$order_no,
                'good_name'=>$good_name,
            ]
        ];

        if($startPayment){
            $arr =[];
            $baseArr['send_time'] = $startPayment;
            foreach ($tailOrderMsgType as $item){
                $baseArr['type'] = $item;
                if($item == 1){//短信
                    $baseArr['resource']['sms_tpl'] = 'ORDER_PRESALE_START';
                    $baseArr['resource']['pay_remain_time'] = 0;
                    $arr[] = $baseArr;
                }

                if($item == 2){//公众号
                    $baseArr['resource']['notice_type'] = 'DEPOSIT_ORDER_START';
                    $baseArr['resource']['pay_remain_time'] = 0;
                    $arr[] = $baseArr;
                }

                if($item == 3){//APP
                    $baseArr['resource']['event'] = 'order_presale_start';
                    $baseArr['resource']['pay_remain_time'] = 0;
                    $arr[] = $baseArr;
                }
            }
            //todo 存入数据库
            UserMsg::factory()->push($user_id,'saveUserMsg',['user_id'=>$user_id,'send_time'=>$baseArr['send_time'],'arr'=>$arr]);
        }

        if($endPayment && $surplusTime){
            $arr =[];
            $baseArr['send_time'] = $endPayment - CUtil::uint($surplusTime*3600);
            foreach ($tailOrderMsgType as $item){
                $baseArr['type'] = $item;
                if($item == 1){//短信
                    $baseArr['resource']['sms_tpl'] = 'ORDER_PRESALE_END';
                    $baseArr['resource']['pay_remain_time'] = $surplusTime;
                    $arr[] = $baseArr;
                }

                if($item == 2){//公众号
                    $baseArr['resource']['notice_type'] = 'DEPOSIT_ORDER_END';
                    $baseArr['resource']['pay_remain_time'] = $surplusTime;
                    $arr[] = $baseArr;
                }

                if($item == 3){//APP
                    $baseArr['resource']['event'] = 'order_presale_end';
                    $baseArr['resource']['pay_remain_time'] = $surplusTime;
                    $arr[] = $baseArr;
                }
            }
            //todo 存入数据库
            UserMsg::factory()->push($user_id,'saveUserMsg',['user_id'=>$user_id,'send_time'=>$baseArr['send_time'],'arr'=>$arr]);
        }
    }


    /**
     * @param null $db
     * @param string $order_no
     * @param int $user_id
     * @param int $ctime
     * @param int $gudie_id
     * @param int $r_id
     * @param int $expire_time
     * @param int $source
     * @param int $platformSource
     * @param string $deposit_order_no
     * @return array
     * 安全保存订单主表记录
     */
    public function __safeInsert($db=null, $order_no='', $user_id=0, $ctime=0, $gudie_id=0, int $r_id = 0, int $expire_time = 0, int $source = 0, int $platformSource = 0, string $deposit_order_no = '', int $type = 1): array
    {
        try{
            $db = is_null($db) ? by::dbMaster() : $db;
            $tb = self::tbName($ctime);

            $save = ['order_no' => $order_no, 'user_id' => $user_id, 'ctime' => $ctime];

            $extend         = by::userExtend()->getUserExtendInfo($user_id, false);
            $save['source'] = $source;
            $save['store']  = $extend['store'] ?? '';

            //平台来源
            $save['platform_source'] = $platformSource;

            if(empty($save['source'])){
                !empty($gudie_id) && $save['source'] = 1;

                if (!empty($r_id) && $r_id != $user_id && !empty($expire_time) && $ctime <= $expire_time) {
                    if (empty($gudie_id)) {
                        $save['source'] = 2;
                    } else {
                        $save['source'] = 3;
                    }
                }
            }

            $save['type'] = $type;
            $save['end_payment'] = 0;
            if($deposit_order_no){
                //根据定金订单获取商品信息
                $depositInfo = by::Odeposit()->CommDepositPackageInfo($user_id,$deposit_order_no);
                $presaleEndPayment = $depositInfo['end_payment'] ?? 0;
                !YII_ENV_PROD  && CUtil::debug(json_encode($depositInfo).'|'.$user_id .'|'.$deposit_order_no,'kk.op');
                $save['type'] = self::USER_ORDER_TYPE['TAIL']; // 尾款订单
                $save['end_payment'] = $presaleEndPayment;
            }

            $db->createCommand()->insert($tb,$save)->execute();

            return [true,$order_no];

        } catch (\Exception $e) {

            return [false,$e->getMessage()];
        }
    }

    /**
     * @param $user_id
     * @param $gcombines
     * @param array $data
     * @return array
     * @throws Exception
     * 校验订单
     */
    private function __depositCheck($user_id, $gcombines, array $data, array $deInfo)
    {
        if (empty($gcombines) || $user_id <= 0) {
            return [false, '非法参数'];
        }

        $mGmain         = by::Gmain();

        $gNums          = count($gcombines);
        foreach($gcombines as $k => $gcombine) {

            $gid    = $gcombine['gid'];

            $num    = intval($gcombine['num'] ?? 0);

            if($num <= 0){
                return [false, '选择的商品数量不能为0！'];
            }

            $g_info = $deInfo['cfg'] ?? [];
            if(empty($g_info)){
                $g_info  = $mGmain->GetOneByGidSid($gid, $gcombine['sid'], false);
            }

            if (empty($g_info)) {
                return [false, ($gNums > 1 ? '部分' : '').'商品已下架，下单失败~'];
            }


            //计算尾款
            $yprice = $g_info['spec']['price'] ?? $g_info['price'];
            $yExpandPrice = $g_info['spec']['expand_price'] ?? ($g_info['expand_price'] ?? 0);
            $price = CUtil::uint($yprice) - CUtil::uint($yExpandPrice);

            $gcombines[$k]['tprice']     = bcmul($yprice, $gcombine['num']);
            $gcombines[$k]['texprice']     = bcmul($yExpandPrice, $gcombine['num']);
            $gcombines[$k]['tnprice']     = bcmul($price, $gcombine['num']);
            $gcombines[$k]['is_coupons'] = $g_info['is_coupons']    ?? 0;
            $gcombines[$k]['tids']       = $g_info['tids']          ?? [];
        }

        return [true, $gcombines];
    }



    /**
     * @param $user_id
     * @param $gcombines
     * @param array $data
     * @param int $spriceType
     * @return array
     * @throws Exception
     * 校验订单
     */
    private function __check($user_id, $gcombines, array $data, int $spriceType)
    {
        if (empty($gcombines) || $user_id <= 0) {
            return [false, '非法参数'];
        }
        //活动sn
        $preSn = $data['pre_sn'] ?? '';

        // 以旧换新活动
        $is_trade_in = $data['is_trade_in'] ?? 0;

        // 团购活动ID
        $groupActivityId = $data['group_activity_id'] ?? 0;
        // 团ID
        $groupPurchaseId = $data['group_purchase_id'] ?? 0;
        if ($groupPurchaseId) {
            $leader = byNew::GroupPurchaseModel()->getLeaderInfo($groupPurchaseId);
            if (empty($leader)) {
                return [false, '当前团不存在，请检查！'];
            }
        }
        $relationType = $data['relation_type'] ?? 1; // 关系类型


        //todo 收货地址校验
        $aid            = $data['aid'];
        if ($aid <= 0) {
            return [false, '请选择收货地址'];
        }
        $ad = by::Address()->GetOneAddress($user_id,$aid);
        $isDel = $ad['is_del'] ?? 0; // 删除状态
        if (empty($ad) || ($isDel == by::Address()::IS_DEL['yes'])) { // 地址不存在、或地址被删除
            return [false, '请添加或选择收货地址'];
        }

        //todo 判定该用户是否授权手机号
        $phone     = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            return [false, '请先授权手机号'];
        }

        //todo 限购区域
        list($s, $m) = by::model('OfreightModel', 'goods')->IsDis($user_id, $aid);
        if (!$s) {
            return [-1, $m];
        }

        //订单备注校验
        if (mb_strlen($data['note']) > 50) {
            return [false, '请输入50字内备注'];
        }

        $mGmain         = by::Gmain();

        $limit_gids     = [];
        $gNums          = count($gcombines);
        foreach($gcombines as $k => $gcombine) {

            $gid    = $gcombine['gid'];

            $num    = intval($gcombine['num'] ?? 0);

            if($num <= 0){
                return [false, '选择的商品数量不能为0！'];
            }

            $g_info  = $mGmain->GetOneByGidSid($gid, $gcombine['sid'], false, false,$spriceType);

            //校验自定义价格
            list($status,$data)= by::Ouser() -> CheckGoodsIniPrice($g_info,$gcombine);
            if(!$status){
                return [-12,$data];
            }

            if (empty($g_info)) {
                return [false, ($gNums > 1 ? '部分' : '').'商品已下架，下单失败~'];
            }

            if ($g_info['is_del'] != 0) {
                return [false, ($gNums > 1 ? '部分' : '').'商品不存在，下单失败~~'];
            }

            $is_presale = $g_info['is_presale'] ?? 0;
            if(CUtil::uint($is_presale) == 1){
                return [false,'预售商品不支持直接购买！'];
            }

            if ($g_info['status'] != 0) {
                return [false, ($gNums > 1 ? '部分' : '').'商品已下架，下单失败~~'];
            }

            if ($g_info['atype'] == by::Gtype0()::ATYPE['SPECS'] && empty($g_info['spec'])) {
                return [false, '多规格商品，请选择正确的属性'];
            }

            //第一次校验库存
            $sku   = $g_info['spec']['sku'] ?? ($g_info['sku'] ?? '');
            $stock = empty($sku) ? 0 : by::GoodsStockModel()->OptStock($sku);
            if ($gcombine['num'] > $stock) {
                return [false, ($gNums > 1 ? '部分' : '').'商品库存不够，下单失败~'];
            }

            //活动抵扣金额
            $gInfo['acdeprice'] = 0;
            if($preSn && $sku){
                //获取活动折扣价格
                $acdepriceInfo = by::GmainAcDepriceModel()->GetInfoBySnAndSku($preSn,$sku,false);
                $gInfo['acdeprice'] = $acdepriceInfo['deprice'] ?? 0;
                $gInfo['is_yjhx'] = 1;
            }


            //todo erp校验库存
            $lockOldErp = CUtil::omsLock(0,time());
            if (!$lockOldErp && $g_info['type'] != $mGmain::TYPE['Y_LINK'] && $stock < self::ERP_LIMIT_STOCK) {
                $sku                = $g_info['spec']['sku'] ?? $g_info['sku'];
                list($s, $stock1)   = Erp::factory()->getStockNew($sku, $g_info['ck_code']);

                if ( !$s ) {
                    return [false, '请稍后再试'];
                }

                if ($gcombine['num'] > $stock1) {
                    return [false, ($gNums > 1 ? '部分' : '').'商品库存不够，下单失败~~'];
                }
            }


            //  校验团购商品限购数量 **一个订单只支持一件商品**  如果是拼团商品 走自己的限购数 如果是普通商品 也走自己的 区分是不是拼团商品 有没有团活动ID
            if ($groupActivityId || $groupPurchaseId) {
                // 如果团活动ID为空 说明不是团长发起拼团 所有不需要校验活动ID
                $isCheckGroupActivityId = empty($groupActivityId) ? false : true;
                // 如果已成团直接取团活动ID
                if ($groupPurchaseId) {
                    $groupActivityId = byNew::GroupPurchaseModel()->getLeaderInfo($groupPurchaseId)['activity_id'];
                }
                if (empty($groupActivityId)) {
                    return [false, '拼团活动不存在，请检查！'];
                }

                $groupPurchaseData  = byNew::GroupPurchaseActivityModel()->getActivityGoodsDetail($groupActivityId,$gid);
                if (!$groupPurchaseData['status']){
                    return [false, '拼团活动不存在，请检查！'];
                }

                $groupGoods          = $groupPurchaseData['data']['goods'] ?? [];
                $groupGoodsLimitData = array_column($groupGoods, 'purchase_limit', 'gid');
                // 判断当前商品是否在拼团活动中
                if (!isset($groupGoodsLimitData[$gid])) {
                    return [false, '当前商品不在拼团活动中，请检查！'];
                }
                // 拼团商品限购数量
                $groupGoodsLimit = $groupGoodsLimitData[$gid];
                // 判断当前商品是否超过限购数量
                list($limitStatus, $limitData) = by::Ogoods()->CanLimitBuy($user_id, $gid, $gcombine['num'], $groupGoodsLimit, 1, $groupPurchaseId, $isCheckGroupActivityId);
                if (!$limitStatus) {
                    return [false, $limitData];
                }
            } else if ($g_info['limit_num'] > 0) {
                // 普通商品限购处理
                $limit_gids[$gid]['limit_num'] = $g_info['limit_num'];
                // 累加限购数量
                $limit_gids[$gid]['num'] = empty($limit_gids[$gid]['num'])
                        ? $gcombine['num']
                        : bcadd($limit_gids[$gid]['num'], $gcombine['num']);
            }

            $price = $g_info['spec']['price'] ?? $g_info['price'];
            if ($groupActivityId > 0){
                $rate = GroupPurchaseService::getInstance()->getDiscountRate($gid);
                $price    = bcmul($price, $rate, 2);
            }
//            $gcombines[$k]['price']      = $price;
            $gcombines[$k]['tprice']     = bcmul($price, $gcombine['num']);
            $gcombines[$k]['acdeprice']  = $gInfo['acdeprice'];
            $gcombines[$k]['is_yjhx']    = $gInfo['is_yjhx'] ?? 0;
            $gcombines[$k]['is_coupons'] = $g_info['is_coupons']    ?? 0;
            $gcombines[$k]['tids']       = $g_info['tids']          ?? [];
        }

        //todo 判断限购
        if (!empty($limit_gids)) {
            foreach ($limit_gids as $gid => $v) {
                // 如果是团购商品，跳过普通商品的限购验证
                if (($groupActivityId || $groupPurchaseId) && isset($groupGoodsLimitData[$gid])) {
                    continue;
                }
                list($s, $m) = by::Ogoods()->CanLimitBuy($user_id, $gid, $v['num'], $v['limit_num']);
                if (!$s) {
                    return [false, $m];
                }
            }
        }

        return [true, $gcombines];
    }

    /**
     * @param array $data
     * @throws Exception
     * 下单失败，redis库存回滚
     */
    private function __stockCacheRollback(array $data,$source)
    {
        $wxPayModel = by::wxPay();
        $goodsStockModel = by::GoodsStockModel();

        $sourceStock = ($source == $wxPayModel::SOURCE['POINTS']) ? by::GoodsStockModel()::SOURCE['WARES'] : by::GoodsStockModel()::SOURCE['MAIN'];
        foreach($data as $val) {
            $sku = $goodsStockModel->GetSkuByGidAndSid($val['gid'],$val['sid'],$sourceStock);
            $goodsStockModel->DelAllCache($sku);

            //如果是自定义价格，价格回滚
            $giniId = $val['gini_id'] ?? 0;
            if($giniId){
                by::Gini()->DelAllIniCache($giniId);
            }
        }
    }

    /**
     * @param array $data
     * @throws Exception
     * @throws RedisException
     * 预售下单失败，redis库存回滚
     */
    private function __presaleStockCacheRollback(array $data)
    {
        foreach($data as $val) {
            by::Gprestock()->DelAllCache($val['gid'], $val['sid']);
        }
    }

    /**
     * 创建订单
     * @param $user_id
     * @param $api
     * @param $arr
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function createOrder($user_id, $api, $arr): array
    {
        //频率限制
        $unique_key = __FUNCTION__;
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,5,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        // 校验订单
        list($status, $res) = $this->validateOrder($user_id, $api);
        if (!$status) {
            return [false, $res];
        }

        $ctime             = intval(START_TIME);
        $coupon_id         = $arr['coupon_id'] ?? 0;
        $consume_id        = $arr['consume_id'] ?? 0;
        $type              = $arr['type'] ?? 0;                    //资源类型
        $coin              = $arr['coin'] ?? 0;
        $giftCardIds       = $arr['gift_card_ids'] ?? "";          //礼品卡id 逗号分隔 "1,2,3"
        $gcombines         = $arr['gcombines'] ?? "";              //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]
        $payType           = $arr['pay_type'] ?? self::PAY_BY_WX;
        $payType           = CUtil::uint($payType);
        $group_activity_id = CUtil::uint($arr['group_activity_id'] ?? 0); //团购活动ID
        $groupPurchaseId   = CUtil::uint($arr['group_purchase_id'] ?? 0); //团ID
        $paymentPlan       = $arr['payment_plan'] ?? "NO_INST";
        $getChannel        = $arr['get_channel'] ?? 0;                     //优惠券来源控制
        $spriceType        = $arr['sprice_type'] ?? 0;                     //自定义设置价格类型
        $referer           = $arr['referer'] ?? '';                        //用户登录链接
        $union             = $arr['union'] ?? '';                          //渠道来源
        $euid              = $arr['euid'] ?? '';                           //渠道来源-标识参数
        $liveMark          = CUtil::removeXss($arr['live_mark'] ?? '');    //直播标识
        $oneYuanSeckillId  = $arr['one_yuan_seckill_id'] ?? 0;             //一元秒杀标识Id
        $shoppingPrice     = CUtil::totalFee($arr['shopping_price'] ?? 0); //使用购物金 转为分
        $consumeMoney      = CUtil::totalFee($arr['consume_money'] ?? 0);  //使用消费金 转为分
        $platformSource    = $arr['platform_source'] ?? 0;                 //平台来源
        $source            = self::UNION_SOURCE[$union] ?? 0;
        $guide_id          = by::userGuide()->getGuideByUid($user_id);

        // 追觅小店店长user_id
        $popularize_user_id = $arr['storekeeper'] ?? 0;


        $relationType = $arr['relation_type'] ?? 'COMMON';
        $relationType = self::ORDER_RELATION_TYPE[$relationType] ?? 1; // 业务类型
        $relationId   = $arr['relation_id'] ?? 0;                      // 业务ID

        $unionInfo = by::userAdv()->getLastUnionInfo($user_id);
        $referer = $unionInfo['referer'] ?? $referer;
        $union   = $unionInfo['union'] ?? $union;
        $euid    = $unionInfo['euid'] ?? $euid;

        $r_info      = by::userRecommend()->getInfoByUid($user_id);

        $r_id        = $r_info['r_id'] ?? 0;
        $expire_time = $r_info['expire_time'] ?? 0;
        $r_type = 1;
        // 如果推荐人为空，则去追觅大使的绑定表里去找数据
        if ($r_id == 0){
            $zinfo = byNew::UserBindModel()->getInfoByUserId($user_id);
            $r_id = $zinfo['user_id'] ?? 0;
            if ($r_id){
                $empRes = byNew::UserEmployeeModel()->getEmployeeInfo($r_id, 'user_id');
                if ($empRes){
                    $r_type = 2;
                    $expire_time = $zinfo['bind_time'] + 86400 * 90;
                }
            }
        }

        // 2025-08-10 邀请好友购买商品,赠送奖励金,一笔订单只给一次奖励方式
        $inviter_id = $arr['inviter_id'] ?? 0;
        if ($inviter_id) {
            $r_id = $inviter_id;
            $expire_time = time() + 86400;
            $r_type = 4;
        }

        $is_trade_in = $arr['is_trade_in'] ?? 0;
        $device_type = $arr['device_type'] ?? '';


        $acData            = $arr['ac_data'] ?? '';
        $acData            = (array)json_decode($acData, true);
        $sn                = $acData['sn'] ?? '';
        $image             = $acData['image'] ?? '';

        $gcombines = (array)json_decode($gcombines, true);

        // 校验商品
        list($status, $res) = $this->checkGoods($user_id, $gcombines,$payType);
        if (!$status) {
            return [false, $res];
        }

        // 单规格补偿机制
        $gcombines=$this->checkGidSid($gcombines,$res);

        //如果是活动进入不允许用优惠券
        $preSn = '';
        if($sn) {
            $coupon_id = 0;
            if(empty($image)) return [false,'旧机图片必传哟！'];
            $sn = CUtil::removeXss($sn);
            $remove_chars = array('\\', ';',',');
            $sn           = str_replace($remove_chars, '', $sn);
            //校验活动时间
            if(!ActivityConfigEnum::judgeActivityTime($user_id)){
                return [false,'不在活动时间内，不允许参加哟！'];
            }
            //获取产品注册信息
            $regInfo = by::productReg()->getPRegInfo($user_id, $sn);
            if (empty($regInfo)) return [false,'您不符合参与活动规定！'];
            $productId = CUtil::uint($regInfo['product_id'] ?? 0);
            $p_info    = by::product()->getOneById($productId);
            if (empty($p_info)) return  [false,'您不符合参与活动规定！'];
            //获取可兑换商品SKU
            $preSn         = $p_info['sn'] ?? '';
            if(empty($preSn)) return  [false,'您不符合参与活动规定！'];
            //单商品校验
            if(count($gcombines)>1){
                return  [false,'参与活动的商品只允许一种！'];
            }
            $checkNum = $gcombines[0]['num'] ?? 0;
            if($checkNum >1 ) return  [false,'以旧换新每笔订单仅可购买1件商品！'];
        };


        $data = [
                'aid'               => $arr['aid'] ?? 0,
                'note'              => $arr['note'] ?? '',
                'pre_sn'            => $preSn ?? '',
                'group_activity_id' => $group_activity_id,
                'group_purchase_id' => $groupPurchaseId,
                'is_trade_in'       => $is_trade_in,
                'relation_type'     => $relationType,
        ];
        list($s, $gcombines)  = $this->__check($user_id, $gcombines, $data, $spriceType);
        if (!$s || $s === -1 || $s === -12) {
            return ($s === -12)?[-12, $gcombines] :(($s === -1) ? [-1, $gcombines] : [false, $gcombines]);
        }

        $arr['ctime'] = $ctime;

        $order_no  = "";
        $status    = false;
        $db        = by::dbMaster();
        $trans     = $db->beginTransaction();

        try {
            //尝试三次创建订单
            $order_type = self::USER_ORDER_TYPE['COMMON']; //普通订单
            if($sn) $order_type = self::USER_ORDER_TYPE['OTN'];//以旧换新订单
            // 校验商品是否为内购商品
            $isInternalGoods = $this->isIncludeInternalGoods($gcombines);
            if ($isInternalGoods) {//包含内购商城商品，即为内购商城订单
                $order_type = self::USER_ORDER_TYPE['INTERNAL'];
            }
            if ($group_activity_id || $groupPurchaseId) {
                $order_type = self::USER_ORDER_TYPE['GROUP_PURCHASE'];
            }
            for ($i=0;$i<3;$i++) {
                $order_no               = $this->CreateOrderNo($ctime);
                list($status,$order_no) = $this->__safeInsert($db, $order_no, $user_id, $ctime, $guide_id, $r_id, $expire_time, $source, $platformSource, '', $order_type);
                if ($status) {
                    break;
                }
            }

            if(!$status) {
                throw new \Exception($order_no, 2001);
            }

            $oprice = 0; //总原价
            $reduce = 0; //总优惠价
            $card_type = $card_type ?? 0;
            // 如果使用购物金则不能使用其他优惠
            // 根据API类型确定是否启用购物金（仅指定API可使用）
            $isCanUseShoppingPrice = in_array($arr['api'], OuserModel::SHOPPING_PRICE_APIS);
            $shoppingPrice  = $isCanUseShoppingPrice ? $shoppingPrice : 0;
            if (bccomp($shoppingPrice, 0) > 0) {
                $userShoppingMoney = byNew::UserShopMoneyModel()->getInfoByUserId($user_id);
                if (empty($userShoppingMoney) || $userShoppingMoney < $shoppingPrice) {
                    throw new MyExceptionModel('购物金不足');
                }
                $reduce = $shoppingPrice;
            }else{
                //todo 消费券
                $consume_id = CUtil::uint($consume_id);
                $sub_price = 0;
                if($consume_id > 0){
                    $user_consume_info   = by::userCard()->getCardById($user_id, $consume_id, $getChannel);
                    $consume_type        = $user_consume_info['type'] ?? 0;
                    if($consume_type != by::userCard()::TYPE['consume']) return [false,'优惠券不存在！'];
                    list($s, $consume_price) = by::userCard()->useCard($user_id, $consume_type, $order_no, $gcombines, $consume_id, $trans, $getChannel, $spriceType);
                    if (!$s) {
                        CUtil::debug("{$user_id}|{$consume_id}|{$consume_price}".var_export($gcombines,1), 'warn.buy');
                        throw new MyExceptionModel('兑换或优惠券使用失败');
                    }
                    $consume_price  = by::Gtype0()->totalFee($consume_price);
                    $reduce        = bcadd($reduce, $consume_price);
                }

                //todo 使用兑换或优惠券
                $coupon_id = CUtil::uint($coupon_id);
                if ($coupon_id > 0) {
                    $user_card_info   = by::userCard()->getCardById($user_id, $coupon_id, $getChannel);
                    $card_type        = $user_card_info['type'] ?? 0;
                    if($card_type == by::userCard()::TYPE['consume']) return [false,'优惠券不存在！'];
                    list($s, $cprice) = by::userCard()->useCard($user_id, $card_type, $order_no, $gcombines, $coupon_id, $trans, $getChannel, $spriceType);
                    if (!$s) {
                        CUtil::debug("{$user_id}|{$coupon_id}|{$cprice}".var_export($gcombines,1), 'warn.buy');
                        throw new MyExceptionModel('兑换或优惠券使用失败');
                    }
                    $cprice     = by::Gtype0()->totalFee($cprice);
                    $reduce     = bcadd($reduce, $cprice);

                    $freight_other = [
                            'card_type' => $card_type,
                            'market_id' => $user_card_info['market_id'],
                    ];
                }

                //todo 使用积分
                if ($coin > 0) {
                    $point   = by::point()->get($user_id);
                    if ($point < $coin) {
                        CUtil::debug("{$user_id}|{$coin}", 'warn.buy');
                        throw new MyExceptionModel('积分使用失败');
                    }
                    $coin_price = by::point()->convert($coin,'RMB',$user_id);
                    $coin_price = by::Gtype0()->totalFee($coin_price);
                    $reduce     = bcadd($reduce, $coin_price);
                }

                //todo 活动扣减
                if($sn) {
                    $acdeprice = 0;
                    foreach ($gcombines as $item){
                        $acdeprice = bcadd($acdeprice,$item['acdeprice'] ?? 0);
                    }
                    $reduce     = bcadd($reduce, $acdeprice);
                }
            }

            //todo 购物车下单-删除购物车
            $cart_ids   = (array)json_decode($arr['cart_ids'] ?? '', true);
            !empty($cart_ids) && by::cart()->del($user_id, $cart_ids);

            $shippingData = [];
            //todo 非运费信息进行预减库存
            foreach ($gcombines as $v) {
                //todo 更新商品库存
                list($s)    = by::GoodsStockModel()->UpdateStock($v['gid'], $v['sid'], $v['num'],'ROLL',true,by::GoodsStockModel()::SOURCE['MAIN']);
                if (!$s) {
                    throw new MyExceptionModel('库存不够~~', 2001);
                }
                //todo 更新自定义价格库存
                $giniId = CUtil::uint($v['gini_id'] ?? 0);
                if($giniId){
                    list($sg)   = by::Gini()->UpdateStock($giniId,$v['num']);
                    if(!$sg){
                        throw new MyExceptionModel('优惠价格库存不够~~', 2001);
                    }
                }

                $gInfo = by::Gmain()->GetOneByGidSid($v['gid'], $v['sid'], true, true, $spriceType);
                if (empty($gInfo)) {
                    throw new MyExceptionModel('商品信息不存在 ~');
                }

                $shippingData[] = [
                        'gid' => $v['gid'],
                        'sid' => $v['sid'],
                        'num' => $v['num'],
                        'price' => $gInfo['spec']['price'] ?? $gInfo['price'],
                        'is_free_shipping' => $gInfo['is_free_shipping'] ?? 0,
                ];
                $oprice    = bcadd($oprice, $v['tprice']);
            }

            $price      = bcsub($oprice, $reduce);

            //运费权益
            $fprice=by::Ouser()->shippingPrice($shippingData, $user_id, $arr['aid'], $freight_other ?? [], CUtil::totalFee($price,1));
            $fprice = CUtil::totalFee($fprice);

            // 先计算邮费 再计算礼品卡
            // 使用礼品卡处理逻辑
            // 初始化参数
            $useCardType            = 0;               // 卡类型
            $useCardValue           = 0;              // 卡值
            $giftCardDiscountAmount = 0;    // 礼品卡抵扣金额
            $giftCardData           = [];             // 礼品卡数据
            $applyGiftCardInfo      = [];        // 礼品卡抵扣信息
            $subsidyPrice           = 0;              // 国补金额（仅APP支持）

            // 使用购物金时不使用礼品卡和国补（保持原逻辑）
            if (bccomp($shoppingPrice, 0) == 0) {
                // 处理礼品卡（若有指定礼品卡）
                if (!empty($giftCardIds)) {
                    // 判断是否使用了其他优惠（积分/优惠券/消费券）
                    $isDeducted = ($coin > 0 || $coupon_id > 0 || $consume_id > 0);

                    // 解析礼品卡ID列表
                    $giftCardIdsArray = explode(",", $giftCardIds);

                    // 应用礼品卡抵扣
                    list($applyStatus, $applyResult) = GiftCardService::getInstance()
                            ->orderApplyGiftCard($giftCardIdsArray, $reduce, $price, $gcombines, $db, $isDeducted);

                    // 礼品卡应用失败
                    if (!$applyStatus) {
                        return [false, $applyResult];
                    }

                    // 更新礼品卡相关数据
                    $useCardType            = $applyResult['useCardType'];
                    $useCardValue           = $applyResult['useCardValue'];
                    $giftCardDiscountAmount = $applyResult['giftCardDiscountAmount'];
                    $giftCardData           = $applyResult['giftCardData'];
                    $applyGiftCardInfo      = $applyResult['applyGiftCardInfo'];
                    $price                  = $applyResult['price']; // 更新价格（已扣礼品卡）
                }

                // 处理国补逻辑（仅指定API且非团购商品适用）
                $isSubsidyApplicable = in_array($arr['api'], OuserModel::SUBSIDY_APIS)
                        && $group_activity_id <= 0;

                if ($isSubsidyApplicable) {
                    // 调用国补计算接口
                    list($subsidyStatus, $subsidyMessage, $calculatedSubsidy) = by::Ouser()->applySubsidyDiscount($gcombines, $price);

                    // 国补应用失败时抛出异常
                    if (!$subsidyStatus) {
                        throw new MyExceptionModel($subsidyMessage);
                    }

                    // 国补金额向下取整（确保为整数）
                    $subsidyPrice = (int) floor($calculatedSubsidy);
                }

                // 团购商品强制清零国补（严格执行业务规则）
                if ($group_activity_id > 0) {
                    $subsidyPrice = 0;
                }

                // 计算最终实付金额（单位：分）
                // 公式：商品实付金额 = 商品总价 - 优惠券 - 消费券 - 积分 - 活动抵扣金额 - 礼品卡抵扣金额 - 国补
                // 使用bcmath确保精度，最终向下取整为整数
                $finalPrice = (int)floor(bcsub($price, $subsidyPrice, 2));

                // 用最终价格覆盖原价格变量（若需保留原始价格可跳过此步）
                $price = $finalPrice;
            }


            // 如果是app并且使用了购物金
            $isCanUseConsumeMoney = in_array($arr['api'], OuserModel::CONSUME_MONEY_APIS);
            $consumeMoney         = $isCanUseConsumeMoney ? $consumeMoney : 0;
            if ($isCanUseConsumeMoney && bccomp($consumeMoney, 0) > 0) {
                $userConsumeMoney = byNew::UserShopMoneyModel()->getInfoByUserId($user_id, 2);
                if (empty($userConsumeMoney) || $userConsumeMoney < $consumeMoney) {
                    throw new MyExceptionModel('消费金不足');
                }
                // 更新价格
                $price = bcsub($price, $consumeMoney);
            }


            //实付金额 = 商品实付金额 + 邮费金额
            $pay_price = bcadd($price, $fprice);

            if (empty($giftCardIds) && bccomp($pay_price, 0) <= 0 && $card_type != by::userCard()::TYPE['voucher']) {
                throw new MyExceptionModel('网络繁忙~');
            }


            //导购表数据
            if (!empty($guide_id)) {
                $sourceData = [
                    'user_id' => $user_id,
                    'price'   => $price,
                    'ctime'   => $ctime,
                    'type'    => ($spriceType == 1) ? 2 : 1,//内购类型
                ];
                list($s, $m) = by::Osource()->SaveLog($order_no,$guide_id,$sourceData);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
            }

            //推荐表数据
            if (!empty($r_id) && $r_id != $user_id) {
                $sourceData = [
                    'user_id'     => $user_id,
                    'r_id'        => $r_id,
                    'order_no'    => $order_no,
                    'price'       => $price,
                    'pay_price'   => $pay_price,
                    'expire_time' => $expire_time,
                    'ctime'       => $ctime,
                    'r_type'      => $r_type
                ];

                list($status, $res) = by::osourceR()->SaveLog($sourceData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }

            //渠道来源表
            if(!empty($union)||!empty($liveMark)||!empty($referer)){
                $sourceMData= [
                    'user_id'     => $user_id,
                    'union'       => $union,
                    'euid'        => $euid,
                    'order_no'    => $order_no,
                    'price'       => $price,
                    'live_mark'   => trim($liveMark),
                    'referer'     => $referer,
                    'ctime'       => $ctime
                ];
                list($status, $res) = by::osourceM()->SaveLog($sourceMData);
                if (!$status) {
                    throw new MyExceptionModel($res);
                }
            }


            // 如果是拼团订单,校验和处理拼团ID
            if ($order_type == self::USER_ORDER_TYPE['GROUP_PURCHASE']) {
                // 未传入拼团ID时,需要创建新的拼团
                if (!$groupPurchaseId) {
                    if (empty($group_activity_id)) {
                        throw new MyExceptionModel('拼团活动不存在，请检查！');
                    }
                    $gid = $gcombines[0]['gid'] ?? 0; // 获取拼团商品ID
                    if (!$gid) {
                        throw new MyExceptionModel('拼团商品ID不存在，请检查！');
                    }
                    // 创建新的拼团
                    list($group_status, $groupPurchase) = GroupPurchaseService::getInstance()->initiateGroupPurchase([
                            'user_id'     => $user_id,
                            'activity_id' => $group_activity_id,
                            'gid'         => $gid,
                    ], $user_id);
                    if (!$group_status && !isset($groupPurchase['id'])) {
                        throw new MyExceptionModel('创建拼团失败，请重试！');
                    }
                    $groupPurchaseId = $groupPurchase['id'];
                }
            }

            // 将礼品卡ID以逗号分隔存入表中
            $giftCardIdsStr = is_array($giftCardIds) ? implode(",", $giftCardIds) : $giftCardIds;

            // 设置订单表数据
            $ouserData = [
                    'oprice'            => $oprice,
                    'price'             => $price,
                    'fprice'            => $fprice,
                    'subsidy_price'     => $subsidyPrice, // 国补金额
                    'coupon_id'         => $coupon_id,
                    'coupon_market_id'  => $user_card_info['market_id'] ?? 0,
                    'cprice'            => $cprice ?? 0,
                    'consume_id'        => $consume_id,
                    'consume_market_id' => $user_consume_info['market_id'] ?? 0,
                    'consume_price'     => $consume_price ?? 0,
                    'shopping_price'    => $shoppingPrice ?? 0,
                    'consume_money'     => $consumeMoney ?? 0,
                    'coin'              => $coin,
                    'coin_price'        => $coin_price ?? 0,
                    'acdeprice'         => $acdeprice ?? 0,
                    'coin_logid'        => $log_id ?? 0,
                    'note'              => $arr['note'] ?? '',
                    'gift_card_ids'     => $giftCardIdsStr,
                    'gift_card_price'   => $giftCardDiscountAmount ?? 0,      // 确保有默认值
                    'group_purchase_id' => $groupPurchaseId ?? 0,             // 拼团ID
                    'relation_type'     => $relationType,
                    'relation_id'       => $relationId,
                    'ctime'             => $ctime,
            ];

            // 保存订单数据
            by::Ouser()->SaveLog($user_id, $order_no, $ouserData);

            //地址表数据
            by::Oad()->SaveLog($user_id, $order_no, $arr['aid']);

            //订单商品表数据
            $oGoodsData = [
                    'coupon_id'       => $coupon_id,
                    'cprice'          => $cprice ?? 0,
                    'consume_id'      => $consume_id,
                    'consume_price'   => $consume_price ?? 0,
                    'coin'            => $coin,
                    'coin_price'      => $coin_price ?? 0,
                    'shopping_price'  => $shoppingPrice ?? 0,
                    'consume_money'   => $consumeMoney ?? 0,
                    'acdeprice'       => $acdeprice ?? 0,
                    'tprice'          => $oprice,
                    'subsidy_price'   => $subsidyPrice, // 国补金额
                    'gift_card_type'  => $useCardType,
                    'gift_card_value' => $useCardValue,
            ];

            list($s, $m) = by::Ogoods()->SaveLog($user_id, $order_no, $gcombines, $oGoodsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //订单商品快照数据
            $cfgData = [
                'ctime'     => $ctime,
                'gcombines' => $gcombines,
            ];
            by::Ocfg()->SaveLog($user_id, $order_no, $cfgData, $spriceType);

            // 当前购买的用户，有绑定的推荐人
            $this->saveUserBindOrder($user_id, $order_no, $order_type);

            // 插入礼品卡消费记录表
            if (!empty($giftCardIds)){
                $insertExpendData = [];
                $columns          = [];
                foreach ($applyGiftCardInfo['giftCardDataAfterDeduction'] as $giftCardId => $giftCardValue) {
                    $giftCardInfo     = byNew::GiftUserCards()->userGiftCardInfo($giftCardId,false);
                    $type             = $giftCardInfo['type'] ?? 1;
                    $insertExpendInfo = [
                        'card_id'        => $giftCardInfo['card_id'] ?? '',
                        'user_card_id'   => $giftCardId,
                        'order_no'       => $order_no,
                        'expend_amount'  => $type == 2 ? 0 : bcsub($giftCardData[$giftCardId], $giftCardValue),
                        'current_amount' => $type == 2 ? 0 : $giftCardValue,
                        'type'           => GiftCardExpendRecordModel::TYPES['USE'],
                        'ctime'          => intval(START_TIME),
                        'utime'          => intval(START_TIME),
                    ];

                    $columns            = array_keys($insertExpendInfo);
                    $insertExpendData[] = $insertExpendInfo;
                }

                $giftCardExpendTb = byNew::GiftCardExpendRecord()::tbName();
                by::dbMaster()->createCommand()->batchInsert($giftCardExpendTb, $columns, $insertExpendData)->execute();
            }

            //订单以旧换新数据
            if($sn){
                $otnData = [
                    'ctime'     => $ctime,
                    'image'     => $image
                ];
                by::OtnModel()->SaveLog($user_id,$order_no,$otnData);
            }

            /**
             * 一元秒杀订单处理秒杀表中订单号
             * 取消订单时需要更新为空
             */
            if ($oneYuanSeckillId) {
                //助力成功以后才可以购买商品
                if (by::OneYuanSeckillModel()->checkCanBuy($oneYuanSeckillId,$user_id,$gcombines)){
                    by::OneYuanSeckillModel()->updateOrderNo($oneYuanSeckillId, $order_no);
                }else{
                   throw new MyExceptionModel('您不符合商品购买条件~');
                }
            }

            /**
             * 如果使用了购物金，插入使用记录
             */
            if (bccomp($shoppingPrice, 0) > 0) {
                \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'subtract', 'type' => 1, 'money' => $shoppingPrice, 'extend' => $order_no, 'remark' => '下单扣减']));
            }

            /**
             * 如果使用了消费金，插入使用记录
             */
            if (bccomp($consumeMoney, 0) > 0) {
                \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'subtract', 'type' => 2, 'money' => $consumeMoney, 'extend' => $order_no, 'remark' => '下单扣减']));
            }

            //todo 如果兑换券免邮，实付款为0，则无需走微信下单
            if (bccomp($pay_price, 0) == 0) {
                $payType = self::PAY_BY_WX; //无需支付直接算作小程序订单
                $ret = ['need_pay' => '0', 'order_no'=>$order_no];
            } else {
                if($payType == self::PAY_BY_WX || $payType == self::PAY_BY_WX_APP){
                    $other = [
                        'body'  =>'订单支付',
                        'ctime' =>$ctime,
                    ];
                    $attach = [
                        'source'  => by::WxPay()::SOURCE['MALL']
                    ];
                    list($status, $ret) = by::WxPay()->unifiedOrder($user_id, $api, $order_no, $pay_price, $other, $attach, $payType);

                }elseif($payType == self::PAY_BY_WX_H5){//H5支付
                    $other = [
                        'body'  =>'订单H5支付',
                        'ctime' =>$ctime,
                    ];
                    $attach = [
                        'source'  => by::wxH5Pay()::SOURCE['MALL']
                    ];
                    list($status, $ret) = by::wxH5Pay()->unifiedOrder($user_id, $api, $order_no, $pay_price, $other, $attach);
                }elseif($payType == self::PAY_BY_ALIPAY) {//支付宝支付
                    // 支付过期时间
                    list($status, $ret) = $this->getPayExpireTime($ctime);
                    if (!$status) {
                        throw new MyExceptionModel($ret);
                    }
                    $attach = [
                        'user_id'     => $user_id,
                        'time_expire' => $ret,
                        'order_type'  => Order::ORDER_TYPE['GOODS'], // 商品订单
                    ];
                    list($status, $alipay_body) = AliPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $alipay_body];
                }elseif ($payType == self::PAY_BY_MP_WX) {//中台微信支付
                    // 中台微信支付（pay_channel=1 && pay_method=1）
                    $attach = [
                        'user_id'     => $user_id,
                        'uid'         => by::Phone()->getUidByUserId($user_id),
                        'pay_channel' => MpPayModel::PAY_CHANNEL['WX'],
                        'pay_method'  => MpPayModel::PAY_METHOD['NATIVE'],
                        'order_type'  => Order::ORDER_TYPE['GOODS'], // 商品订单
                        'pay_type'    => $payType,                   // 支付方式
                    ];
                    list($status, $pay_body) = MpPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $pay_body['pay_url'] ?? '', 'ptime' => $pay_body['ptime'] ?? 0];
                }elseif ($payType == self::PAY_BY_MP_ALIPAY) {//中台支付宝支付
                    // 中台支付宝支付（pay_channel=2 && pay_method=1）
                    $attach = [
                        'user_id'     => $user_id,
                        'uid'         => by::Phone()->getUidByUserId($user_id),
                        'pay_channel' => MpPayModel::PAY_CHANNEL['ALIPAY'],
                        'pay_method'  => MpPayModel::PAY_METHOD['NATIVE'],
                        'order_type'  => Order::ORDER_TYPE['GOODS'], // 商品订单
                        'pay_type'    => $payType,                   // 支付方式
                    ];
                    list($status, $pay_body) = MpPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $pay_body['pay_url'] ?? '', 'ptime' => $pay_body['ptime'] ?? 0];
                }elseif ($payType == self::PAY_BY_MP_WEB_WX_H5) {//中台微信H5
                    // 中台微信支付（pay_channel=1 && pay_method=5）
                    $attach = [
                            'user_id'     => $user_id,
                            'uid'         => by::Phone()->getUidByUserId($user_id),
                            'pay_channel' => MpPayModel::PAY_CHANNEL['WX'],
                            'pay_method'  => MpPayModel::PAY_METHOD['H5'],
                            'order_type'  => Order::ORDER_TYPE['GOODS'], // 商品订单
                            'pay_type'    => $payType,                   // 支付方式
                            'device_type' => $device_type // 设备类型
                    ];
                    list($status, $pay_body) = MpPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $pay_body['pay_url'] ?? '', 'ptime' => $pay_body['ptime'] ?? 0];
                }elseif ($payType == self::PAY_BY_MP_WEB_ALIPAY_H5) {//中台支付宝支付
                    // 中台支付宝支付（pay_channel=2 && pay_method=5）
                    $attach = [
                            'user_id'     => $user_id,
                            'uid'         => by::Phone()->getUidByUserId($user_id),
                            'pay_channel' => MpPayModel::PAY_CHANNEL['ALIPAY'],
                            'pay_method'  => MpPayModel::PAY_METHOD['H5'],
                            'order_type'  => Order::ORDER_TYPE['GOODS'], // 商品订单
                            'pay_type'    => $payType,                   // 支付方式
                    ];
                    list($status, $pay_body) = MpPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $pay_body['pay_url'] ?? '', 'ptime' => $pay_body['ptime'] ?? 0];
                }elseif ($payType == self::PAY_JD_BAITIAO||$payType == self::PAY_JD_BAITIAO_APP||$payType == self::PAY_JD_BAITIAO_H5||$payType == self::PAY_JD_BAITIAO_PC) {//中台京东白条
                    // 白条分期检查
                    list($status, $jd_pay_info) = CashierService::getInstance()->getJDPayInfo($user_id, $order_no, bcdiv($pay_price, 100, 2), $paymentPlan);
                    if (!$status) {
                        throw new MyExceptionModel($jd_pay_info);
                    }

                    if ($payType == self::PAY_JD_BAITIAO_APP){
                        $jd_pay_info['pay_method'] = MpPayModel::PAY_METHOD['APP'];
                    }elseif ($payType == self::PAY_JD_BAITIAO_PC){
                        $jd_pay_info['pay_method'] = MpPayModel::PAY_METHOD['H5'];
                    }elseif ($payType == self::PAY_JD_BAITIAO_H5){
                        $jd_pay_info['pay_method'] = MpPayModel::PAY_METHOD['H5'];
                    } else{
                        // 中台京东白条微信（pay_channel=3 && pay_method=1）
                        $jd_pay_info['pay_method'] = MpPayModel::PAY_METHOD['NATIVE'];
                    }

                    $attach = [
                            'user_id'      => $user_id,
                            'uid'          => by::Phone()->getUidByUserId($user_id),
                            'pay_channel'  => MpPayModel::PAY_CHANNEL['JD_BAITIAO'],
                            'pay_method'   => MpPayModel::PAY_METHOD['NATIVE'],
                            'order_type'   => Order::ORDER_TYPE['GOODS'], // 商品订单
                            'pay_type'     => $payType,                   // 支付方式
                    ];
                    $attach = array_merge($attach, $jd_pay_info);

                    list($status, $pay_body) = MpPayModel::getInstance()->pay($order_no, bcdiv($pay_price, 100, 2), $attach);
                    $ret = ['need_pay' => '1', 'order_no' => $order_no, 'prepay_id' => $pay_body['pay_url'] ?? '', 'ptime' => $pay_body['ptime'] ?? 0];
                }else{//没有选择对应的平台直接返回订单信息，但是需要支付
                    $status = true;
                    $ret = ['need_pay' => '1', 'order_no'=>$order_no];
                }


                if(!$status){
                    throw new MyExceptionModel($ret);
                }
                $ret['need_pay'] = '1';
            }

            //保存拉起付款参数，重新支付用
            $payData = [
                'prepay_id' => $ret['prepay_id'] ?? '', 'price' => $pay_price, 'pay_type' => $payType, 'h5_url' => $ret['h5_url'] ?? '', 'expire_time' => $ret['expire_time'] ?? 0,'payment_plan' => $paymentPlan];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

            $trans->commit();

            //todo 异步处理订单
            if(bccomp($pay_price, 0) == 0){
                list($s,$msg) = by::BasePayModel()->afterPay($user_id, $order_no, [], [], by::wxPay()::SOURCE['MALL'],$payType);
                if (!$s) {
                    CUtil::debug($user_id.'|'.$order_no. '|msg:' . $msg,'err.not_pay.order');
                }
            }
            //To如果有推荐人 则记录到佣金表 Job
            if ($popularize_user_id) {
                //记一下日志，看是否调用
                CUtil::debug(sprintf("记录到佣金表：参数：%s-%s-%s-%s", $popularize_user_id,$order_no,$user_id,$price), 'Info.SalesCommissionJob.job');
                \Yii::$app->queue->push(new SalesCommissionJob(['order_no' => $order_no, 'user_id' => $user_id,'recomand_user_id' => $popularize_user_id, 'price' => $price, 'status' => 0]));
            }

            //todo 清理订单表缓存
            //清理订单列表缓存
            by::Ouser()->DelListCache($user_id);
            by::Ouser()->DelPointCache($user_id);

            //防止有刷缓存问题
            by::Ouser()->DelInfoCache($user_id,$order_no);

            //todo 清理订单地址表缓存
            by::Oad()->DelCache($order_no);

            //todo 清理订单商品不表缓存
            by::Ogoods()->DelListCache($order_no);
            by::Ogoods()->DelList1Cache($user_id);

            //todo 清理订单商品快照表缓存
            by::Ocfg()->DelCache($order_no);

            //todo 清理导购订单表缓存
            !empty($guide_id) && by::Osource()->DelCache($guide_id);

            //todo 清理渠道来源订单表缓存
            !empty($union) && by::osourceM()->delListCache($union);

            //todo 清理订单主表信息
            by::Omain()->delInfoCache($user_id,$order_no);

            self::ReqAntiConcurrency(0,$unique_key,0,'DEL');

            $r_price     = by::Gtype0()->totalFee($price, 1);
            $rate        = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
            $ret['coin'] = bcmul($r_price, $rate, 2);

            //TODO 绑定用户等级
            $ret['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id,$ret['coin'],time());

            //todo 推送广告
            by::userAdv()->pushAdv($user_id,$order_no,'COMPLETE_ORDER');

            // 取消支付单（异步处理，不影响主流程），订单关闭前5秒
            \Yii::$app->queue->delay(self::PAY_EXPIRE - 5)->push(new CancelPayOrderJob([
                'order_no' => $order_no
            ]));

            // 延时队列，异步取消订单
            \Yii::$app->queue->delay(self::PAY_EXPIRE)->push(new CancelOrderJob([
                'user_id'  => $user_id,
                'order_no' => $order_no,
            ]));

            return [true,$ret];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $this->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['MALL']);

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            $this->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['MALL']);

            return [false, '网络繁忙,请稍候'];
        }
    }

    /**
     * @param $e
     * @param $gcombines
     * @param $unique_key
     * @param $user_id
     * @param $source
     * @throws Exception
     * @throws RedisException
     * 添加订单返回
     */
    public function __addRecord($e, $gcombines, $unique_key, $user_id ,$source)
    {
        $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();

        switch ($source) {
            case  by::wxPay()::SOURCE['MALL'] :
                CUtil::debug($error, 'warn.order');

                break;
            case  by::wxPay()::SOURCE['PLUMBING'] :
                CUtil::debug($error, 'plumbing-order.err');

                break;

            case  by::wxPay()::SOURCE['DEPOSIT'] :
                CUtil::debug($error, 'deposit-order.err');
                break;

            case  by::wxPay()::SOURCE['POINTS'] :
                CUtil::debug($error, 'points-order.err');

                break;
            default:
                break;
        }

//       CUtil::sendMsgToUdp([
//            "**项目 :** dreame\n",
//            "**摘要 :** 下单失败\n",
//            "**详情 :** {$error}\n",
//            "**环境 :** ".(YII_ENV_PROD ? 'release' : 'test')."\n",
//            "**时间 :** ".date("Y-m-d H:i:s")."\n",
//       ],'','','p_1644549157');

        switch ($source) {
            case  by::wxPay()::SOURCE['MALL'] :
            case  by::wxPay()::SOURCE['POINTS'] :
                $e->getCode() != 2001 && $this->__stockCacheRollback($gcombines,$source);
                break;
            case  by::wxPay()::SOURCE['DEPOSIT'] :
                $e->getCode() != 2001 && $this->__presaleStockCacheRollback($gcombines);

                break;
            default:
                break;
        }
        self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
    }

    /**
     * @param $year
     * @param $order_no
     * @param $order_tid
     * @param $user_iden
     * @param $status
     * @param $source
     * @param $order_time
     * @param $p_sources
     * @param $live_mark
     * @param $order_type
     * @param $store
     * @param $source_code
     * @param $goods_name
     * @param $sku
     * @param $page
     * @param $page_size
     * @return array|DataReader
     * @throws Exception
     * 管理后台展示
     */
    public function GetList(
            $year = '',
            $order_no = '',
            $order_tid = '',
            $user_iden = '',
            $status = -1,
            $source = -1,
            $order_time = [],
            $p_sources = '-1',
            $live_mark = '',
            $order_type = 0,
            $store = '',
            $source_code = '',
            $goods_name = '',
            $sku = '',
            $is_sync_erp = -1,
            $page = 1,
            $page_size = 50
    )
    {
        // 处理分页
        $page = CUtil::uint($page, 1);
        list($offset) = CUtil::pagination($page, $page_size);
        // 获取订单号
        if ($order_tid) {
            $order_no = by::oPay()->GetOrderNoByTid($year, $order_tid);
        }

            // 获取表名
        if ($order_no) {
            // 无论 order_tid 是否为空，只要 order_no 存在，都根据订单号获取表名
            $tb = $this->GetTbNameByOrderId($order_no);
        } elseif ($order_tid && empty($order_no)) {
            // 如果有 order_tid 但无法获取到对应的 order_no，则直接返回空数组
            return [];
        } else {
            // 如果既没有 order_no 也没有有效的 order_tid，则根据年份或当前时间获取表名
            $tb = self::tbName(empty($year) ? time() : strtotime("{$year}0101"));
        }

        // 获取查询条件
        list($where, $params) = $this->__getCondition(
                $order_no,
                $user_iden,
                $status,
                $source,
                $order_time,
                $p_sources,
                $live_mark,
                $year,
                $order_type,
                $store,
                $source_code,
                $goods_name,
                $sku,
                $is_sync_erp
        );

        // 构建 SQL 查询
        $sql = "SELECT `order_no`,`user_id`,`source`,`platform_source`,`store`,`is_sync_erp`
            FROM {$tb} 
            WHERE {$where} 
            ORDER BY `id` DESC 
            LIMIT :offset, :page_size";

        // 执行查询
        $aData = by::dbMaster()
                ->createCommand($sql)
                ->bindValues(array_merge($params, [':offset' => $offset, ':page_size' => $page_size]))
                ->queryAll();

        return $aData;
    }


    /**
     * @param $user_id
     * @param $order_no
     * @return array|DataReader
     * @throws Exception
     */
    public function getInfoByOrderNo($user_id, $order_no)
    {
        $user_id = CUtil::uint($user_id);
        if($user_id == 0 || empty($order_no)) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByOrderNoKey($user_id, $order_no);
        $json       = $redis->get($redis_key);
        $info       = (array)json_decode($json,true);

        if($json === false){
            $tb              = $this->GetTbNameByOrderId($order_no);
            $fields = implode("`,`",$this->tb_fields);
            list($where, $param) = $this->__getCondition($order_no,$user_id);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql,$param)->queryOne();
            $info   = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX'=>empty($info) ? 10 : 600]);
        }

        return $info;
    }

    /**
     * @param string $year
     * @param string $order_no
     * @param string $order_tid
     * @param string $user_iden
     * @param int $status
     * @param int $source
     * @param array $order_time
     * @param string $p_sources
     * @param string $live_mark
     * @param int $order_type
     * @param string $store
     * @param string $source_code
     * @param string $goods_name
     * @param string $sku
     * @return int
     * @throws Exception
     * 订单总数
     */

    public function GetListCount(
             $year = '',
             $order_no = '',
             $order_tid = '',
             $user_iden = '',
             $status = -1,
             $source = -1,
             $order_time = [],
             $p_sources = '-1',
             $live_mark = '',
             $order_type = 0,
             $store = '',
             $source_code = '',
             $goods_name = '',
             $sku = '',
             $is_sync_erp = -1
    ): int
    {
        // 获取表名
        if ($order_no) {
            // 如果 order_no 存在，获取对应的表名
            $tb = $this->GetTbNameByOrderId($order_no);
        } elseif ($order_tid) {
            // 如果有 order_tid 但无法获取到对应的 order_no，返回 0
            return 0;
        } else {
            // 如果既没有 order_no 也没有有效的 order_tid，则根据年份或当前时间获取表名
            $tb = self::tbName(empty($year) ? time() : strtotime("{$year}0101"));
        }

        // 获取条件和参数
        list($where, $params) = $this->__getCondition(
                $order_no,
                $user_iden,
                $status,
                $source,
                $order_time,
                $p_sources,
                $live_mark,
                $year,
                $order_type,
                $store,
                $source_code,
                $goods_name,
                $sku,
                $is_sync_erp
        );

        // 执行 SQL 查询以获取计数
        $sql     = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $command = by::dbMaster()->createCommand($sql, $params);
        $count   = $command->queryScalar();

        // 返回计数结果
        return intval($count);
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param int $source
     * @param array $order_time
     * @return array
     * @throws Exception
     * 规范化查询条件
     */
    private function __getCondition($order_no = '', $user_iden = '', $status = -1, $source = -1, $order_time = [], $p_sources = '-1', $live_mark = '', $year = '', $order_type = 0, $store = '', $source_code = '', $goods_name = '', $sku = '',$is_sync_erp = -1): array

    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        if (!empty($goods_name) || !empty($sku)) {
            // 多规格找主sku
            $sku = by::Gspecs()->GetMainSkuBySpecsSku($sku);

            // 获取积分商品和上架/下架商品信息
            $waresGid = by::GoodsMainModel()->GetList(['name' => $goods_name, 'sku' => $sku], 1, 9999999);
            $gids = by::Gmain()->GetList(1, 9999999, '', -1, -1, $goods_name, $sku);

            // 合并订单号
            $orderNos = array_merge(
                by::Ogoods()->getAllOrderByGids($waresGid, 2),
                by::Ogoods()->getAllOrderByGids($gids)
            );

            // 如果有订单号，则构建条件
            if (!empty($orderNos)) {
                $orderNos = implode("','", $orderNos);
                $where .= " AND `order_no` IN ('{$orderNos}')";
            }
        }


        //注意范围
        if(!empty($order_no)) {
            $where               .= " AND `order_no`=:order_no";
            $params[":order_no"]  = $order_no;
        }

        if(!empty($user_iden)) {
            if (strlen($user_iden) == 11) {
                $uids = by::Phone()->GetUidsByPhone($user_iden);
                if(!empty($uids)) {
                    $uids    = implode(',',$uids);
                    $where  .= " AND `user_id` IN ({$uids})";
                }else{
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where  .= " AND `user_id` = -1";
                }
            } else {
                $where  .= " AND `user_id` = :user_id";
                $params[':user_id'] = $user_iden;
            }

        }

        if($status >= 0) {
            list($w, $p)        = $this->GetOrderStatus($status, true);

            if ($w) {
                $where             .= " AND {$w}";
                $params             = array_merge($params, $p);
            }
        }

        if ($source > -1) {
            $where              .= " AND `source` = :source";
            $params[":source"]   = $source;
        }
        if ($is_sync_erp  >= 0){
            $where              .= " AND `is_sync_erp` = :is_sync_erp";
            $params[":is_sync_erp"]   = $is_sync_erp;
        }

        if (!empty($order_type)){
            //如果order_type中包含逗号，那么改成in查询
            if (strpos($order_type, ',') !== false) {
                $order_type = explode(',', $order_type);
                $where .= " AND `type` in (".implode(',', $order_type).")";
            } else {
                $where .= " AND `type` = :type";
                $params[':type'] = $order_type;
            }
        }


        if (intval($p_sources) != '-1'){
            $p_sources = trim($p_sources);
            $p_sources = implode("','",explode(',',$p_sources));
            $where              .= " AND `platform_source` in ('{$p_sources}')";
        }

        if(!empty($order_time['st']) && !empty($order_time['ed'])) {
            $where               .= " AND `ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"]  = $order_time['st'];
            $params[":order_ed"]  = $order_time['ed'];
        }

        if(!empty($live_mark) && !empty($year)){
            //获取符合条件的order_no
            $liveMarkInfo = by::osourceM()->getList('',$year,['live_mark'=>$live_mark]);
            if($liveMarkInfo){
                $order_nos = array_filter(array_unique(array_column($liveMarkInfo,'order_no')));
                if($order_nos){
                    $order_nos = implode("','",$order_nos);
                    $where              .= " AND `order_no` in ('{$order_nos}')";
                }
            }
        }


        if (!empty($source_code) && !empty($year)) {
            //获取符合条件的order_no
            $sourceInfo = by::osourceM()->getList('', $year, ['euid' => $source_code]);
            $order_nos  = array_filter(array_unique(array_column($sourceInfo, 'order_no')));
            $order_nos  = implode("','", $order_nos);
            $where      .= " AND `order_no` in ('{$order_nos}')";
        }

        if (!empty($store)) {
            $where            .= " AND `store` =:store ";
            $params[':store'] = $store;
        }

        return [$where,$params];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $status
     * @param array $arr
     * @param array $main
     * @return array
     * @throws Exception
     * 订单status同步
     */
    public function SyncInfo($user_id, $order_no, $status, $arr=[], $main=[])
    {

        if($user_id <= 0 || empty($order_no) || !in_array($status,self::ORDER_STATUS)) {
            return [false,"非法参数(o)"];
        }

        $db       = by::dbMaster();
        $trans    = $db->beginTransaction();
        try{

            //订单主表
            $main['status'] = $status;
            $tb_main        = self::GetTbNameByOrderId($order_no);

            $ret = $db->createCommand()->update(
                $tb_main,
                $main,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new MyExceptionModel("无数据更新(01)");
            }

            $arr['status'] = $status;

            //订单表
            $tb_info = by::Ouser()::tbName($user_id);
            $ret     = $db->createCommand()->update(
                $tb_info,
                $arr,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new MyExceptionModel("无数据更新~~");
            }

            //todo 导购订单表
            $ouid = by::Osource()->getOuidByOrder($user_id, $order_no);
            if ($ouid) {
                $time       = by::Omain()->GetTbNameByOrderId($order_no, false);
                $tb_osource = by::Osource()::tbName($time);

                $db->createCommand()->update(
                    $tb_osource,
                    ['status' => $status],
                    ['order_no' => $order_no, 'user_id' => $user_id]
                )->execute();
            }

            //todo 推荐订单表
            $info = by::OsourceR()->getInfoByOrderNo($user_id, $order_no);
            if (!empty($info)) {
                //更新推荐订单表（1）
                $or_data['status'] = $status;

                if (!empty($arr['finish_time'])) {
                    $or_data['finish_time'] = $arr['finish_time'];
                }

                $time  = by::Omain()->GetTbNameByOrderId($order_no, false);
                $where = ['order_no' => $order_no, 'user_id' => $user_id];
                $db->createCommand()->update(by::OsourceR()::tbName($time), $or_data, $where)->execute();

                $ac_model = by::activityConfigModel();
                $ac_id    = $ac_model->getActivityIdsByType($ac_model::GRANT_TYPE['recommend_gift']);
                $ac_info  = $ac_model->getActivityOne($ac_id);
                if ($ac_info && $info['is_valid'] == 1 && $status == self::ORDER_STATUS['WAIT_SEND']) {
                    switch ($ac_info['reward_type']) {
                        case $ac_model::REWARD_TYPE['coupon'] :
                            list($s, $am_ids) = by::aM()->getIdsByAid($ac_id);
                            if ($s) {
                                $save = ['reward_coupon_num' => count($am_ids)];
                            }

                            break;
                        case $ac_model::REWARD_TYPE['point'] :
                            $save = ['reward_point' =>  by::Actype2()::ACTION['order']['reward_point']];

                            break;
                        default :
                            break;
                    }

                    //更新推荐订单表（2）
                    if (!empty($save)) {
                        $db->createCommand()->update(by::OsourceR()::tbName($time), $save, $where)->execute();
                    }
                }
            }

            //todo 订单来源表
            $ominfo = by::osourceM()->getInfoByOrderNo($user_id, $order_no);
            if(!empty($ominfo)){
                //更新订单来源表
                $or_data['status'] = $status;

                if (!empty($arr['finish_time'])) {
                    $or_data['finish_time'] = $arr['finish_time'];
                }

                $time  = by::Omain()->GetTbNameByOrderId($order_no, false);
                $where = ['order_no' => $order_no, 'user_id' => $user_id];
                $db->createCommand()->update(by::osourceM()::tbName($time), $or_data, $where)->execute();
            }


            $trans->commit();

            //清理订单列表缓存
            by::Ouser()->DelListCache($user_id);

            by::Ouser()->DelPointCache($user_id);

            //防止有刷缓存问题
            by::Ouser()->DelInfoCache($user_id, $order_no);

            //清理导购订单相关缓存
            if (!empty($ouid)) {
                by::Osource()->DelCache($ouid);
                by::Osource()->delInfoCache($ouid, $order_no);
                by::Osource()->DelSumCache($ouid, $status);
            }

            //清理推荐订单相关缓存
            if (!empty($info['r_id'])) {
                by::OsourceR()->delListCache($info['r_id']);
                by::OsourceR()->delInfoCache($user_id, $order_no);

                //抽奖邀请购买赠送抽奖次数
                $status == by::Omain()::ORDER_STATUS['WAIT_SEND'] && DrawActivityService::getInstance()->doActivityTask($info['r_id'], ['task_code' => 'INVITE_FRIEND','invitee_id'=>$user_id,'order_no'=>$order_no]);
            }

            //清理订单来源相关缓存
            if (!empty($ominfo)) {
                by::osourceM()->delListCache($ominfo['union']??0);
                by::osourceM()->delInfoCache($user_id, $order_no);
            }

            //清理订单主表相关缓存
            by::Omain()->delInfoCache($user_id, $order_no);

            //清理完成订单商品和列表缓存
            by::Ogoods()->__delGetOrderGidByUidKey($user_id);
            by::Ouser()->__delGetOrderListByUidKey($user_id);

            return [true, "OK"];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.omain');

            return [false,'操作失败'];
        }
    }

    /**
     * @param int $type
     * @return array
     * 获取退款、取消原因
     */
    public function GetRtypeList($type = 1)
    {
        if ($type == 1) {
            $list   = self::R_TYPE;
        } else {
            $list   = by::OrefundMain()::R_TYPE;
        }

        $return = [];
        foreach ($list as $rid=>$name) {
            $return[] = [
                'rid'  => (string)$rid,
                'name' => $name,
            ];
        }

        return $return;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param int $r_type 取消原因
     * @return array
     * @throws Exception
     * 回收订单
     */
    public function Recycle($user_id, $order_no, $r_type=0) :array
    {
        //频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,3,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $mOmain     = by::Omain();
        //可取消状态
        $can_status = [$mOmain::ORDER_STATUS['WAIT_PAY']];
        $stockModel = by::GoodsStockModel();

        $oInfo      = by::Ouser()->GetInfoByOrderId($user_id, $order_no);

        if (empty($oInfo)) {
            return [false, '无需处理'];
        }
        if ( !in_array($oInfo['status'], $can_status) ) {
            return [false, '该订单不可取消'];
        }

        //todo 查询支付方式参数
        $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
        if(empty($aLog)) {
            return [false, '无法获取支付流水，数据有误！'];
        }
        $payType = $aLog['pay_type']??'';
        if($payType == by::Omain()::PAY_BY_WX || $payType == by::Omain()::PAY_BY_WX_APP){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::WxPay()->wxOrderQuery($order_no, 1, $payType);
            if ($s) {
                return [false, '该订单已支付'];
            }
        }elseif ($payType == by::Omain()::PAY_BY_WX_H5){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::wxH5Pay()->wxH5OrderQuery($order_no, 1);
            if ($s) {
                return [false, '该订单已支付'];
            }
        }elseif ($payType == by::Omain()::PAY_BY_ALI_ZHIMA){
            // todo 查询芝麻信用是否支付
            list($s) = $this->CheckZhiMaOrderPayStatus($order_no,$aLog['tid']);
            if ($s) {
                return [false, '该订单已支付'];
            }
        }elseif ($payType == by::Omain()::PAY_BY_ALIPAY){

            // 从缓存中查询支付状态
            $orderKey = AppCRedisKeys::getAliPayStatus($order_no);
            list($s) = by::redis()->get($orderKey);
            if ($s) {
                return [false, '该订单已支付'];
            }

            // 查询支付宝中心此订单是否已付款
            if (AliPayService::getInstance()->isPaySuccess($order_no)) {
                return [false, '该订单已支付'];
            }
        }

        $oGoods     = by::Ogoods()->GetListByOrderNo($user_id, $order_no);

        $trans      = by::dbMaster()->beginTransaction();
        try {
            if ($r_type > 0) {
                $arr = ['cr_type' => $r_type];
            }
            list($s, $m) = $this->SyncInfo($user_id, $order_no, self::ORDER_STATUS['CANCELED'], $arr ?? []);
            if (!$s) {
                throw new \Exception($m);
            }

            //todo 回退优惠券
            if ($oInfo['coupon_id'] > 0) {
                list($s, $m) = by::userCard()->UnLockCard($user_id, $oInfo['coupon_id'],$trans);
                if (!$s) {
                    throw new \Exception($m);
                }
            }


            //todo 回退消费券
            $consumeId = $oInfo['consume_id'] ?? 0; 
            if ($consumeId) {
                list($s, $m) = by::userCard()->UnLockCard($user_id, $consumeId,$trans);
                if (!$s) {
                    throw new \Exception($m);
                }
            }



            //todo 退还礼品卡
            if (!empty($oInfo['gift_card_ids'])) { // 使用了礼品卡
                $order_no = $oInfo['order_no'];
                $gift_card_ids = explode(',', $oInfo['gift_card_ids']);
                list($s, $m) = byNew::GiftCardExpendRecord()->UnLockGiftCard($user_id,$order_no, $gift_card_ids);
                if (!$s) {
                    throw new \Exception($m);
                }
            }

            //todo 回收商品库存
            foreach($oGoods as $val) {
                $goodsType = $val['goods_type'] ?? '';
                $source    = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                    ? $stockModel::SOURCE['WARES']
                    : $stockModel::SOURCE['MAIN'];
                //todo 获取商品sku 如果是多规格商品，获取主sku
                $sku  = $stockModel->GetSkuByGidAndSid($val['gid'], $val['sid'], $source);
                $wait = $stockModel->OptWait($sku);
                if (intval($wait) >= intval($val['num'])) {
                    list($s, $m) = $stockModel->UpdateStock($val['gid'], $val['sid'], $val['num'], 'ROLL', false, $source);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                    $giniId = $val['gini_id'] ?? 0;
                    if ($giniId) {
                        list($sg, $mg) = by::Gini()->UpdateStock($giniId, $val['num'], 'ROLL', false);
                        if (!$sg) {
                            throw new \Exception($mg);
                        }
                    }
                }
            }
            // 查询是否是一元秒杀订单 是的话取消订单时更新一元秒杀表 订单号更新为空
            $oneYuanSeckillInfo = by::OneYuanSeckillModel()->getOneYuanSeckillInfo($order_no);
            if ($oneYuanSeckillInfo) {
                // 如果存在把表中订单号更新为空
                $oneYuanSeckillData  = [
                        'order_no' => NULL,
                ];
                $oneYuanSeckillWhere = [
                        'order_no' => $order_no,
                ];
                $oneYuanSeckillTb    = by::OneYuanSeckillModel()::tbName();
                by::dbMaster()->createCommand()->update(
                        $oneYuanSeckillTb,
                        $oneYuanSeckillData,
                        $oneYuanSeckillWhere
                )->execute();
            }

            $trans->commit();

            // 以旧换新订单（异步取消）
            \Yii::$app->queue->push(new CancelTradeInOrderJob([
                'user_id'  => $user_id,
                'order_no' => $order_no
            ]));

            if($payType == by::Omain()::PAY_BY_ALI_ZHIMA){
                AliZhima::factory()->CancelOrder($order_no, $aLog['tid'] ?? '');
            }

            // 取消订单返还购物金
            if ($oInfo['shopping_price']) {
                \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'add','type'=>1, 'money' => $oInfo['shopping_price'], 'extend' => $order_no, 'remark' => '订单取消返还购物金']));
            }

            // 取消订单发还消费金
            if (!empty($oInfo['consume_money']) && $oInfo['consume_money'] > 0) {
                \Yii::$app->queue->push(new UserShopMoneyJob(['user_id' => $user_id, 'money_type' => 'add','type'=>2, 'money' => $oInfo['consume_money'], 'extend' => $order_no, 'remark' => '订单退款返还消费金']));
            }
            // 修改分佣表
            \Yii::$app->queue->push(new SalesCommissionJob(['order_no' => $order_no, 'user_id' => $user_id,'recomand_user_id' => 0, 'price' => 0, 'status' => 100]));

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($order_no.'|'.$e->getMessage(), 'err.recycle');

            return [false, '取消失败！'];
        }

    }

    /**
     * 订单是否可取消
     * @param $user_id
     * @param $order_no
     * @return bool
     * @throws Exception
     */
    public function isCanCancelOrder($user_id, $order_no): bool
    {
        $canCancelStatus = [self::ORDER_STATUS['WAIT_PAY']];
        $orderInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        if (empty($orderInfo) || !in_array($orderInfo['status'], $canCancelStatus)) {
            return false;
        }
        return true;
    }

    /**
     * 礼品卡是否可退还：未支付、且使用了礼品卡
     * @param $user_id
     * @param $order_no
     * @return bool
     * @throws Exception
     */
    public function isCanRefundGiftCard($user_id, $order_no): bool
    {
        $canRefundStatus = [self::ORDER_STATUS['WAIT_PAY']]; // 待支付
        $orderInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        // 未支付、且使用了礼品卡
        if ($orderInfo && in_array($orderInfo['status'], $canRefundStatus) && $orderInfo['gift_card_ids']) {
            return true;
        }
        return false;
    }


    public function GueryOrderPayInfo($order_no,$user_id)
    {
        //todo 查询支付方式参数
        $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no);
        if(empty($aLog)) {
            return [false, '无法获取支付流水，数据有误！'];
        }
        $payType = $aLog['pay_type']??'';
        $s = false;
        if($payType == by::Omain()::PAY_BY_WX || $payType == by::Omain()::PAY_BY_WX_APP){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::WxPay()->wxOrderQuery($order_no, 1, $payType);
        }elseif ($payType == by::Omain()::PAY_BY_WX_H5){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::wxH5Pay()->wxH5OrderQuery($order_no, 1);
        }
        return [true,['order_no'=>$order_no,'status'=>$s?1:0]];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $finish_time
     * @param bool $force 强制完成
     * @return array
     * @throws Exception 确认收货
     * 确认收货
     */
    public function Finish($user_id, $order_no, $finish_time = null, bool $force = false): array
    {
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        //可操作状态
        $can_status = $this->GetCanActStatus(self::ORDER_STATUS['FINISHED']);

        $oInfo      = by::Ouser()->GetInfoByOrderId($user_id, $order_no,false);
        if (empty($oInfo)) {
            return [false, '无需处理'];
        }

        if ( !in_array($oInfo['status'], $can_status)&& !$force) {
            return [false, '该订单不可操作'];
        }

        $trans = by::dbMaster()->beginTransaction();
        try {

            $uData          = ['finish_time'   => $finish_time ?: intval(START_TIME)];

            $next_st        = $this->SetOrderStatus($oInfo['status'], self::ORDER_STATUS['FINISHED']);

            //修改订单状态
            list($s, $m)    = $this->SyncInfo($user_id, $order_no, $next_st, $uData);
            if (!$s) {
                throw new \Exception($m);
            }

            $trans->commit();
            // //todo 订单同步crm
            // Crm::factory()->push($user_id,'order',['user_id'=>$user_id,'order_no'=>$order_no]);
            // Crm::factory()->push($user_id,'orderLine',['user_id'=>$user_id,'order_no'=>$order_no]);

            // todo 更新先试后买订单状态
            \Yii::$app->queue->push(new UserOrderTryJob([
                'user_id'     => $user_id,
                'order_no'    => $order_no,
                'type'        => 'arrival_time', // 'arrival_time' => '签收时间
                'update_data' => ['user_id' => $user_id, 'order_no' => $order_no, 'arrival_time' => $finish_time ?: intval(START_TIME)],
            ]));

            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');

            // byNew::GroupPurchaseMemberModel()->update(['finish_time' => time(),'utime'=> time()], ['order_no' => $order_no]);
            byNew::SalesCommissionModel()->patch(['order_no'=>$order_no],['status'=>500,'finish_time'=>time()]);

            return [true, 'ok'];

        } catch (\Exception $e) {

            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.finish');

            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
            return [false, '操作失败'];
        }

    }

    /**
     * @return array
     * @throws Exception
     * 自动取消订单记录
     */
    public function Cancel(): array
    {
        $wait          = self::ORDER_STATUS['WAIT_PAY'];

        $etime         = intval(START_TIME) - self::PAY_EXPIRE + 20; //两小时前的数据置为已取消状态
        $id            = 0;

        $where         = " `status`={$wait} AND `ctime`<{$etime}";
        $where         .= " AND `type`= 1";

        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);
        foreach ($years as $year) {
            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_main   = self::tbName($ctime);

            while (true) {
                $sql    = "SELECT `id`,`order_no`,`user_id` FROM {$tb_main} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

                $list   = $db->createCommand($sql, [':id'=>$id])->queryAll();

                if (empty($list)) {
                    break;
                }

                $end    = end($list);
                $id     = $end['id'];

                foreach($list as $val) {
                    list($status,$ret) = $this->Recycle($val['user_id'], $val['order_no']);
                    if(!$status){
                        CUtil::debug($val['order_no'].'|'.$val['user_id'].'|'.json_encode($ret,320), 'err.recycle');
                    }

                }
            }

        }
        return [true,"OK"];
    }


    /**
     * @return array
     * @throws Exception
     * @throws RedisException
     * 取消订单前5分钟通知用户
     */
    public function cancelOrderNotifyUser(): array
    {
        $wait          = self::ORDER_STATUS['WAIT_PAY'];
        $etime         = intval(START_TIME) - self::PAY_EXPIRE + 320; //两小时前的数据置为已取消状态


        $where         = " `type`= 1 AND `status`={$wait} AND `ctime`<{$etime}";

        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);
        $now = time();
        foreach ($years as $year) {
            $id1            = 0;
            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_main   = self::tbName($ctime);

            while (1) {
                $sql    = "SELECT `id`,`order_no`,`user_id`,`ctime` FROM {$tb_main} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

                $list1   = $db->createCommand($sql, [':id'=>$id1])->queryAll();

                if (empty($list1)) {
                    break;
                }

                $end1    = end($list1);
                $id1     = $end1['id'];

                foreach($list1 as $item) {
                    if(empty($item['order_no']??'')) continue;

                    //有效拦截
                    $uniqueKey = CUtil::getAllParams(__FUNCTION__, $item['order_no']);
                    list($s)    = by::model("CommModel",MAIN_MODULE)->AccFrequency(0,$uniqueKey,900,"EX",2);
                    if (!$s) {
                        continue;
                    }

                    // todo 通知如果超过截止时间，不再通知
                    $ctime = $item['ctime'] ?? 0;
                    if($now - self::PAY_EXPIRE > $ctime) {
                        continue;
                    }

                    //todo 支付提醒
                    EventMsg::factory()->run('orderMsgSend',['event'=>'paid','order_no'=>$item['order_no'] ?? '']);
                }
            }

        }
        return [true,"OK"];
    }


    /**
     * 批量导出数据
     * @param string $year
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param int $source
     * @param int $order_st
     * @param int $order_ed
     * @param string $p_sources
     * @param false $viewSensitive
     * @return array|mixed
     * @throws Exception
     */
    public function batchExportData($year = '', $order_no = '', $user_iden = '', $status = -1, $source = -1, $order_st = 0, $order_ed = 0, $p_sources = '-1', $viewSensitive = false,$store='',$orderType = '', $order_tid = '')
    {
        // 设置脚本的最大执行时间为10分钟
        ini_set('max_execution_time', '600');
        $head = [
                '订单号', '交易流水号', '状态', '类型', '子类型', '订单平台来源', '付款时间', '订单总金额', '商品总金额', '运费', '优惠券名称', '优惠券金额', '消费券名称', '消费券金额', '积分抵扣金额', '实付金额',
                '用户昵称', '用户手机号', '用户来源', '用户标签', '收货人姓名', '收货人手机号', '收货人地址', '物流公司', '快递单号', '备注', '绑定人ID', '门店名称'
        ];

        $time   = empty($year) ? time() : strtotime("{$year}0101");
        $tb     = self::tbName($time);

        $order_time = [
            'st' => $order_st ?? 0,
            'ed' => $order_ed ?? 0,
        ];

        // 所有订单类型（不包括先试后买）
        $orderTypeArray = [
                by::Omain()::USER_ORDER_TYPE['COMMON'],
                by::Omain()::USER_ORDER_TYPE['TAIL'],
                by::Omain()::USER_ORDER_TYPE['DEPOSIT'],
                by::Omain()::USER_ORDER_TYPE['POINT'],
                by::Omain()::USER_ORDER_TYPE['OTN'],
                by::Omain()::USER_ORDER_TYPE['INTERNAL'],
                by::Omain()::USER_ORDER_TYPE['GROUP_PURCHASE'],
        ];

        // 将订单类型转换为逗号分隔的字符串
        $orderTypeString = implode(",", $orderTypeArray);

        $order_type = !empty($orderType) ? $orderType : $orderTypeString;

        //导出
        $db                = by::dbMaster();
        $orderUserModel    = by::Ouser();
        $userModel         = by::users();
        $marketConfigModel = by::marketConfig();
        $userCardModel     = by::userCard();
        $phoneModel        = by::Phone();
        $userExtendModel   = by::userExtend();
        $orderSourceModel  = by::osourceR();
        $orderAddressModel = by::Oad();
        $orderMainModel    = by::Omain();
        $goodsTypeModel    = by::Gtype0();
        $oPay              = by::oPay();

        if ($order_tid) {
            $order_no = $oPay->GetOrderNoByTid($year, $order_tid);
        }

        list($where, $params) = $this->__getCondition($order_no, $user_iden, $status, $source, $order_time, $p_sources, '', '', $order_type, $store);

        $id     = 0;
        $sql    = "SELECT `id`,`order_no`,`user_id`,`type`,`source`,`platform_source`,`store` FROM {$tb} WHERE `id` > :id AND {$where} 
                   ORDER BY `id` LIMIT 1000";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            // 订单列表
            $orderItems = $list;

            // 订单号集合
            $orderNos = array_column($list, 'order_no');

            // 用户集合
            $userIds = array_unique(array_column($list, 'user_id'));

            // 获取用户订单数据
            $userOrderItems = $orderUserModel->getListByUserIdsAndOrderNos($userIds, $orderNos);
            $userOrderItems = array_column($userOrderItems, null, 'order_no');

            // 获取用户手机号数据
            $userPhoneItems = $phoneModel->getListByUserIds($userIds);
            $userPhoneItems = array_column($userPhoneItems, null, 'user_id');

            // 获取用户来源数据
            $userExtendItems = $userExtendModel->getListByUserIds($userIds, ['user_id', 'source', 'tag']);
            $userExtendItems = array_column($userExtendItems, null, 'user_id');

            // 获取用户的数据
            $userItems = $userModel->getListByUserIds($userIds, ['user_id', 'nick']);
            $userItems = array_column($userItems, null, 'user_id');

            // 获取订单的地址
            $orderAddressItems = $orderAddressModel->getListByOrderNos($orderNos, ['order_no', 'nick', 'phone', 'address', 'detail', 'express_name', 'mail_no']);
            $orderAddressItems = array_column($orderAddressItems, null, 'order_no');

            // 获取优惠券名称
            $userCoupons = $this->getUserCoupon($userOrderItems, 100);
            $userCardItems = $userCardModel->getListByIds($userCoupons, ['id', 'user_id', 'market_id']);

            // 优惠券的名称
            $marketIds = [];
            foreach ($userCardItems as $userCardItem) {
                $ids = array_column($userCardItem, 'market_id');
                $marketIds = array_merge($marketIds, $ids);
            }

            $marketItems = $marketConfigModel->getListByIds($marketIds, ['id', 'name']);
            $marketItems = array_column($marketItems, 'name', 'id');

            // 订单号优惠券名称
            $orderMarketName = [];
            $orderCouponRelationItems = $this->getOrderCouponRelation($userOrderItems, 100);
            foreach ($orderCouponRelationItems as $orderNo => $orderCouponRelationItem) {
                $orderMarketName[$orderNo] = '';
                $index = $orderCouponRelationItem['index'];
                $coupon_id = $orderCouponRelationItem['coupon_id'];
                if (isset($userCardItems[$index][$coupon_id]['market_id'])) {
                    $market_id = $userCardItems[$index][$coupon_id]['market_id'];
                    $orderMarketName[$orderNo] = $marketItems[$market_id] ?? '';
                }
            }

            // 订单号消费券名称
            $orderConsumeName = [];
            $orderConsumeRelationItems = $this->getOrderConsumeRelation($userOrderItems, 100);
            foreach ($orderConsumeRelationItems as $orderNo => $orderConsumeRelationItem) {
                $orderConsumeName[$orderNo] = '';
                $index = $orderConsumeRelationItem['index'];
                $consume_id = $orderConsumeRelationItem['consume_id'];
                if (isset($userCardItems[$index][$consume_id]['market_id'])) {
                    $market_id = $userCardItems[$index][$consume_id]['market_id'];
                    $orderConsumeName[$orderNo] = $marketItems[$market_id] ?? '';
                }
            }


            // 订单的推荐人（推荐有礼）
            $orderSourceItems = $orderSourceModel->getListByOrderNos($orderNos, ['order_no', 'r_id']);
            $orderSourceItems = array_column($orderSourceItems, 'r_id', 'order_no');


            // 拼装数据
            foreach ($orderItems as $item) {
                // 订单号、用户ID
                $orderNo = $item['order_no'];
                $userId  = $item['user_id'];
                $store   = $item['store'] ?? '';
                $userOrderItem = $userOrderItems[$orderNo];
                // 状态
                list(, $status) = $orderMainModel->SplitOrderStatus($userOrderItem['status']);
                $status = self::STATUS_NAME[$status] ?? '未知';
                // 平台来源
                $platformSourceName = $userExtendModel->platformSourceConfig($item['platform_source']) ?? '未知';
                // 付款时间
                $payTime = empty($userOrderItem['pay_time']) ? '' : date('Y-m-d H:i:s', $userOrderItem['pay_time']);
                // 订单实际金额
                $orealPrice = bcadd($userOrderItem['oprice'], $userOrderItem['fprice'], 2);
                $orealPrice = bcsub($orealPrice, $userOrderItem['exprice'], 2);
                $orealPrice = bcadd($orealPrice, $userOrderItem['deposit_price'], 2);
                // 订单实付金额
                $realPrice = bcadd($userOrderItem['price'], $userOrderItem['fprice'], 2);
                if ($userOrderItem['status'] >= $orderMainModel::ORDER_STATUS['WAIT_SEND']) {
                    $realPrice = bcadd($realPrice, $userOrderItem['deposit_price'], 2);
                }
                // 用户昵称
                $userNick = $userItems[$userId]['nick'] ?? '';
                // 手机号
                $phone = $userPhoneItems[$userId]['phone'] ?? '';
                // 用户来源
                list($source)   = $userExtendModel->sourceConfig($userExtendItems[$userId]['source'] ?? '');
                // 收货地址
                $address = $orderAddressItems[$orderNo] ?? [];
                $address['address'] = (array)Json::decode($address['address'] ?? '');
                //交易流水号
                $orderTid =  byNew::OPayModel()->GetOneInfo($orderNo)['tid'] ?? '';
                // 结果集合
                $data[] = [
                        'order_no'             => $orderNo . "\t",
                        'order_tid'            => $orderTid . "\t",
                        'status'               => $status,
                        'type'                 => OmainModel::USER_ORDER_TYPE_NAME[$item['type']] ?? '未知',
                        'source'               => self::SOURCE[$item['source']] ?? '',
                        'platform_source_name' => $platformSourceName,
                        'pay_time'             => $payTime,
                        'oprice'               => $goodsTypeModel->totalFee($orealPrice, 1), // 金额类型转换
                        'gprice'               => $goodsTypeModel->totalFee($userOrderItem['oprice'], 1),
                        'fprice'               => $goodsTypeModel->totalFee($userOrderItem['fprice'], 1),
                        'cname'                => $orderMarketName[$orderNo] ?? '',
                        'cprice'               => $goodsTypeModel->totalFee($userOrderItem['cprice'], 1),
                        'consume_name'         => $orderConsumeName[$orderNo] ?? '',
                        'consume_price'        => $goodsTypeModel->totalFee($userOrderItem['consume_price'], 1),
                        'coin_price'           => $goodsTypeModel->totalFee($userOrderItem['coin_price'], 1),
                        'real_price'           => $goodsTypeModel->totalFee($realPrice, 1),
                        'nick'                 => '\'' . $userNick,
                        'phone'                => $phone,
                        'u_source'             => $source,
                        'tag'                  => $userExtendItems[$userId]['tag'] ?? '',
                        'ad_name'              => $address['nick'] ?? '',
                        'ad_phone'             => $address['phone'] ?? '',
                        'ad_detail'            => ($address['address']['province'] ?? '') . ($address['address']['city'] ?? '') . ($address['address']['area'] ?? '') . ($address['detail'] ?? ''),
                        'express_name'         => $address['express_name'] ?? '',
                        'mail_no'              => $address['mail_no'] ?? '',
                        'note'                 => $userOrderItem['note'],
                        'r_id'                 => $orderSourceItems[$orderNo] ?? 0,
                        'store'                => $store
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data, ['phone' => 'tm', 'ad_phone' => 'tm', 'ad_detail' => 'tm']);
        return $data;
    }


    public function batchExportGoodsData($year = '', $order_no = '', $user_iden = '', $status = -1, $source = -1, $order_st = 0, $order_ed = 0, $viewSensitive = false,$orderType = '', $order_tid = '')
    {
        // 设置脚本的最大执行时间为10分钟
        ini_set('max_execution_time', '600');
        $head = [
                '订单号', '交易流水号', '状态', '付款时间', '商品名', '商品类型', '商品编号', '规格', '单价', '数量', '应付金额', '优惠券名称', '优惠券金额', '消费券名称', '消费券金额', '积分抵扣金额',
                '实付金额', '用户昵称', '用户手机号', '用户来源', '用户标签'
        ];

        $time   = empty($year) ? time() : strtotime("{$year}0101");
        $tb     = self::tbName($time);

        $order_time = [
            'st' => $order_st ?? 0,
            'ed' => $order_ed ?? 0,
        ];

        // 所有订单类型（不包括先试后买）
        $orderTypeArray = [
                by::Omain()::USER_ORDER_TYPE['COMMON'],
                by::Omain()::USER_ORDER_TYPE['TAIL'],
                by::Omain()::USER_ORDER_TYPE['DEPOSIT'],
                by::Omain()::USER_ORDER_TYPE['POINT'],
                by::Omain()::USER_ORDER_TYPE['OTN'],
                by::Omain()::USER_ORDER_TYPE['INTERNAL'],
                by::Omain()::USER_ORDER_TYPE['GROUP_PURCHASE'],
        ];

        // 将订单类型转换为逗号分隔的字符串
        $orderTypeString = implode(",", $orderTypeArray);

        $order_type = !empty($orderType) ? $orderType : $orderTypeString;



        //导出
        $db                = by::dbMaster();
        $orderUserModel    = by::Ouser();
        $orderGoodsModel   = by::Ogoods();
        $orderConfigModel  = by::Ocfg();
        $userModel         = by::users();
        $marketConfigModel = by::marketConfig();
        $userCardModel     = by::userCard();
        $phoneModel        = by::Phone();
        $userExtendModel   = by::userExtend();
        $goodsTypeModel    = by::Gtype0();
        $oPay              = by::oPay();

        if ($order_tid) {
            $order_no = $oPay->GetOrderNoByTid($year, $order_tid);
        }

        list($where, $params) = $this->__getCondition($order_no, $user_iden, $status, $source, $order_time, '-1', '', $year, $order_type);

        // 标签
        $tagMap = by::Gtag()->GetTagNameMap();
        $tagNames  = $tagMap;

        $id     = 0;
        $sql    = "SELECT `id`,`order_no`,`user_id` FROM {$tb} WHERE `id` > :id AND {$where} 
                   ORDER BY `id` LIMIT 1000";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            // 订单号集合
            $orderNos = array_column($list, 'order_no');

            // 用户集合
            $userIds = array_unique(array_column($list, 'user_id'));

            // 获取用户订单数据
            $userOrderItems = $orderUserModel->getListByUserIdsAndOrderNos($userIds, $orderNos);
            $userOrderItems = array_column($userOrderItems, null, 'order_no');

            // 获取订单商品数据
            $columns = ['order_no', 'gid', 'sid', 'num', 'oprice', 'price', 'cprice', 'coin_price', 'consume_price', 'status'];
            $orderGoodsItems = $orderGoodsModel->getListByUserIdsAndOrderNos($userIds, $orderNos, $columns);

            // 获取订单配置数据
            $orderConfigItems = $orderConfigModel->getListByOrderNos($orderNos);

            // 获取属性值
            $orderAttributeItems = $orderConfigModel->getAttributeList($orderConfigItems);
            $orderAttributeItems = array_column($orderAttributeItems, 'at_val', 'id');

            // 订单配置数据格式化
            $orderConfigItems = $orderConfigModel->formatOrderConfigItems($orderConfigItems);

            // 获取用户的数据
            $userItems = $userModel->getListByUserIds($userIds, ['user_id', 'nick']);
            $userItems = array_column($userItems, null, 'user_id');

            // 获取用户手机号数据
            $userPhoneItems = $phoneModel->getListByUserIds($userIds);
            $userPhoneItems = array_column($userPhoneItems, null, 'user_id');

            // 获取用户来源数据
            $userExtendItems = $userExtendModel->getListByUserIds($userIds, ['user_id', 'source', 'tag']);
            $userExtendItems = array_column($userExtendItems, null, 'user_id');

            // 获取优惠券名称
            $userCoupons = $this->getUserCoupon($userOrderItems, 100);
            $userCardItems = $userCardModel->getListByIds($userCoupons, ['id', 'user_id', 'market_id']);

            // 优惠券的名称
            $marketIds = [];
            foreach ($userCardItems as $userCardItem) {
                $ids = array_column($userCardItem, 'market_id');
                $marketIds = array_merge($marketIds, $ids);
            }
            $marketItems = $marketConfigModel->getListByIds($marketIds, ['id', 'name']);
            $marketItems = array_column($marketItems, 'name', 'id');

            // 订单号优惠券名称
            $orderMarketName = [];
            $orderCouponRelationItems = $this->getOrderCouponRelation($userOrderItems, 100);
            foreach ($orderCouponRelationItems as $orderNo => $orderCouponRelationItem) {
                $orderMarketName[$orderNo] = '';
                $index = $orderCouponRelationItem['index'];
                $coupon_id = $orderCouponRelationItem['coupon_id'];
                if (isset($userCardItems[$index][$coupon_id]['market_id'])) {
                    $market_id = $userCardItems[$index][$coupon_id]['market_id'];
                    $orderMarketName[$orderNo] = $marketItems[$market_id] ?? '';
                }
            }

            // 订单号消费券名称
            $orderConsumeName = [];
            $orderConsumeRelationItems = $this->getOrderConsumeRelation($userOrderItems, 100);
            foreach ($orderConsumeRelationItems as $orderNo => $orderConsumeRelationItem) {
                $orderConsumeName[$orderNo] = '';
                $index = $orderConsumeRelationItem['index'];
                $consume_id = $orderConsumeRelationItem['consume_id'];
                if (isset($userCardItems[$index][$consume_id]['market_id'])) {
                    $market_id = $userCardItems[$index][$consume_id]['market_id'];
                    $orderConsumeName[$orderNo] = $marketItems[$market_id] ?? '';
                }
            }

            // 拼装数据
            foreach ($orderGoodsItems as $item) {
                // 订单商品的信息
                $order_no = $item['order_no'];
                $gid      = $item['gid'];
                $sid      = $item['sid'];

                // 用户订单信息
                $userOrderItem = $userOrderItems[$order_no];
                $userId = $userOrderItem['user_id'];

                // 订单状态
                $status = $item['status'];
                if ($item['status'] == $orderGoodsModel::STATUS['NORMAL']) {
                    $status = $userOrderItem['status'];
                }

                // 订单配置详情
                $orderConfigItem = $orderConfigItems[$order_no][$gid][$sid] ?? [];

                // 付款时间
                $pay_time = empty($userOrderItem['pay_time']) ? '' : date('Y-m-d H:i:s', $userOrderItem['pay_time']);

                // 商品标签名称
                $goods_tag_names = array_map(function ($tid) use ($tagNames) {
                    return $tagNames[$tid] ?? '';
                }, $orderConfigItem['tids'] ?? []);
                $goods_tag_names = implode('、', $goods_tag_names);

                // 商品属性值
                $attr = '';
                if (!empty($orderConfigItem['av_ids'])) {
                    $tmpAttr = [];
                    foreach ($orderConfigItem['av_ids'] as $av_id) {
                        $tmpAttr[] = $orderAttributeItems[$av_id] ?? '';
                    }
                    $attr = implode(',', $tmpAttr);
                }

                // 用户昵称
                $nick = $userItems[$userId]['nick'] ?? '';
                // 手机号
                $phone = $userPhoneItems[$userId]['phone'] ?? '';
                // 用户来源
                list($source)   = $userExtendModel->sourceConfig($userExtendItems[$userId]['source'] ?? '');
                //交易流水号
                $orderTid = byNew::OPayModel()->GetOneInfo($order_no)['tid'] ?? '';

                // 结果集合
                $data[] = [
                        'order_no'      => $order_no . "\t",
                        'order_tid'     => $orderTid . "\t",
                        'status'        => self::STATUS_NAME[$status] ?? '未知',
                        'pay_time'      => $pay_time,
                        'g_name'        => $orderConfigItem['name'],
                        'g_tids'        => $goods_tag_names,
                        'code'          => $orderConfigItem['sku'] ?? '',
                        'attr'          => $attr,
                        'g_uprice'      => $goodsTypeModel->totalFee($orderConfigItem['price'], 1),
                        'g_num'         => $item['num'],
                        'g_oprice'      => $goodsTypeModel->totalFee($item['oprice'] ?? 0, 1),
                        'cname'         => $orderMarketName[$order_no] ?? '',
                        'cprice'        => $goodsTypeModel->totalFee($item['cprice'] ?? 0, 1),
                        'consume_name'  => $orderConsumeName[$order_no] ?? '',
                        'consume_price' => $goodsTypeModel->totalFee($item['consume_price'] ?? 0, 1),
                        'coin_price'    => $goodsTypeModel->totalFee($item['coin_price'] ?? 0, 1),
                        'price'         => $goodsTypeModel->totalFee($item['price'] ?? 0, 1),
                        'nick'          => '\'' . $nick,
                        'phone'         => $phone,
                        'u_source'      => $source,
                        'tag'           => $userExtendItems[$userId]['tag'] ?? '',
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);
        return $data;
    }


    /**
     * @param $order_no
     * @return int|string
     * @throws Exception
     * 根据订单号获取source
     */
    public function getSourceByNo($order_no)
    {
        if (empty($order_no)) {
            return '';
        }

        $tb     = $this->GetTbNameByOrderId($order_no);
        $sql    = "SELECT `source` FROM {$tb} WHERE `order_no` = :order_no LIMIT 1";
        $source = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->queryScalar();

        return !empty($source) ? intval($source) : 0;
    }

    /**
     * @param $order_no
     * @return array|DataReader
     * @throws Exception
     * 根据订单号查询订单
     */
    public function getInfoByNo($order_no)
    {
        if(empty($order_no)) return [];
        $tb              = $this->GetTbNameByOrderId($order_no);
        $fields = implode("`,`",$this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `order_no` = :order_no LIMIT 1";
        $info   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no])->queryOne();
        return empty($info) ? [] : $info;
    }



    /**
     * @param $data
     * @return false|int|string|DataReader
     * @throws Exception
     * 根据定金订单号统计已支付的尾款订单
     */
    public function getOrderByDepositOrder($user_id , $data)
    {
        if(empty($data)) {
            return 0;
        }

        $time       = time();
        $tb         = $this->tbName($time);

        $where      = " `user_id` = {$user_id}";
        $where     .= " AND `deposit_order_no` = {$data}";
        $fields     = implode("`,`",$this->tb_fields);

        $sql     = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";
        $aData   = by::dbMaster()->createCommand($sql)->queryOne();

        return $aData ?? 0;
    }

    /**
     * 获取用户优惠券
     * @param array $userOrderItems
     * @param int $mod
     * @return array
     */
    public function getUserCoupon(array $userOrderItems, int $mod)
    {
        $data = [];
        foreach ($userOrderItems as $item) {
            // 用户ID、优惠券ID
            $userId = $item['user_id'];
            $couponId = $item['coupon_id'];
            // 有优惠券插入
            if ($couponId) {
                $index = intval($userId) % $mod;
                $data[$index][] = $couponId;
            }
        }
        return $data;
    }

    /**
     * 获取订单-优惠券关系
     * @param array $userOrderItems
     * @param int $mod
     * @return array
     */
    public function getOrderCouponRelation(array $userOrderItems, int $mod)
    {
        $data = [];
        foreach ($userOrderItems as $item) {
            // 用户ID、订单号、优惠券ID
            $userId   = $item['user_id'];
            $orderNo  = $item['order_no'];
            $couponId = $item['coupon_id'];
            // 有优惠券插入
            if ($couponId) {
                $index = intval($userId) % $mod;
                $data[$orderNo] = [
                    'index'     => $index,
                    'coupon_id' => $couponId
                ];
            }
        }
        return $data;
    }

    private function getOrderConsumeRelation(array $userOrderItems, int $mod)
    {
        $data = [];
        foreach ($userOrderItems as $item) {
            // 用户ID、订单号、消费券ID
            $userId   = $item['user_id'];
            $orderNo  = $item['order_no'];
            $consumeId = $item['consume_id'];
            // 有消费券插入
            if ($consumeId) {
                $index = intval($userId) % $mod;
                $data[$orderNo] = [
                    'index'     => $index,
                    'consume_id' => $consumeId
                ];
            }
        }
        return $data;
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function updateStoreData($unionId, $store): bool
    {
        // 获取用户 ID
        $user_id = by::users()->getUserIdByUnionId($unionId, 1);

        if (empty($user_id)) {
            // 处理用户不存在的情况，可以抛出异常或返回错误
            return false;
        }

        $endTime   = intval(START_TIME);
        $beginTime = $endTime - by::WeFocus()::UPDATE_STORE_EXPIRE;
        $endTime   = $endTime + by::WeFocus()::UPDATE_STORE_EXPIRE;
        $tb        = self::tbName($endTime);
        $fields    = implode("`,`", $this->tb_fields);
        // 使用参数化查询来避免 SQL 注入风险
        // 判断前后十分钟内 是会员 且 没门店信息
        $sql = "SELECT `{$fields}` FROM {$tb} WHERE `user_id` = :user_id AND `ctime` BETWEEN :beginTime AND :endTime AND `store` = ''";

        // 使用参数绑定来传递参数，以避免 SQL 注入
        $params = [":user_id" => $user_id, ":beginTime" => $beginTime, ":endTime" => $endTime];

        // 执行查询
        $orderDatas = by::dbMaster()->createCommand($sql, $params)->queryAll();

        // 如果没有需要更新的订单，直接返回
        if (empty($orderDatas)) {
            return false;
        }

        foreach ($orderDatas as $orderData) {
            if (isset($orderData['store']) && empty($orderData['store'])) {
                by::dbMaster()->createCommand()->update($tb, ['store' => $store], ['user_id' => $user_id, 'order_no' => $orderData['order_no'] ?? ''])->execute();
            }
        }
        return true;
    }

    /**
     * 校验订单
     * @param $user_id
     * @param $api
     * @return array
     * @throws Exception
     */
    private function validateOrder($user_id, $api): array
    {
        // 校验Api
        list($status, $security_key) = $this::getApiKey($api);
        if ($status == -1) {
            return [false, '无效的Api信息'];
        }
        // 校验用户
        $userInfo = by::users()->getUserMainInfo($user_id);
        if (empty($userInfo)) {
            return [false, '用户信息错误'];
        }
        return [true, ''];
    }

    /**
     * 支付过期时间
     * @param $ctime
     * @return array
     */
    private function getPayExpireTime($ctime)
    {
        $commPay = new CommPayModel();
        list($status, $ret) = $commPay->GetPayExpireTime(['ctime' => $ctime], 7100);
        if (!$status) {
            return [false, $ret];
        }

        return [true, date("Y-m-d H:i:s", $ret)];
    }

    /**
     * 根据订单号获取数据（无缓存）
     * @param array $orderNos
     * @param array|string[] $columns
     * @return array
     * @throws \yii\db\Exception
     */
    public function getListByOrderNos(array $orderNos, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $columns);
        // 分组查询
        $groupOrderNos = $this->groupOrderNo($orderNos);
        foreach ($groupOrderNos as $index => $nos) {
            $time = strtotime($index . '-01-01');
            $tb = self::tbName($time); // 获取表名称
            // 查询条件
            $nos = implode("','", $nos);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `order_no` IN ('{$nos}')";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 订单号分组
     * @param array $orderNos
     * @return array
     */
    private function groupOrderNo(array $orderNos): array
    {
        $data = [];
        foreach ($orderNos as $orderNo) {
            // 获取年份，分组
            $index = substr($orderNo, 0, 4); // 年份
            $data[$index][] = $orderNo;
        }
        return $data;
    }



    /**
     * 查询芝麻订单支付状态
     * @param $orderNo
     * @param $orderRequestNo
     * @return bool
     */
    public function CheckZhiMaOrderPayStatus($orderNo, $orderRequestNo): bool
    {
        $redisKey = $this->HasPayRedisKey($orderNo);
        if (by::Redis()->get($redisKey)) {
            return true;
        }
        list($sta, $data) = AliZhima::factory()->QueryOrder($orderNo, $orderRequestNo);
        $orderStatus = $data['order_status'] ?? '';
        if ($sta && $orderStatus && $orderStatus == 'AUTHORIZED') {
            return true;
        }
        return false;
    }


    public function HasPayRedisKey($order_no)
    {
        return 'zhima_has_pay_' . $order_no;
    }

    /**
     * 检查用户是否可以购买商品
     * @param int $user_id
     * @param array $gcombines
     * @return array
     * @throws Exception
     */
    public function checkGoods(int $user_id, array $gcombines,$payType=''): array
    {
        if (empty($gcombines)){
            return [false, '商品信息不能为空'];
        }

        // 获取商品ID
        $gids = array_column($gcombines, 'gid');

        // 获取商品信息
        $goods = by::Gtype0()->getListByGids($gids, ['gid', 'is_internal_purchase','jd_baitiao_support','atype']);

        // 京东白条支持检查
        if ($payType == self::PAY_JD_BAITIAO||$payType == self::PAY_JD_BAITIAO_APP||$payType == self::PAY_JD_BAITIAO_H5||$payType == self::PAY_JD_BAITIAO_PC) {
            if (Collection::make($goods)->where('jd_baitiao_support', 0)->isNotEmpty()) {
                return [false, '商品不支持京东白条支付'];
            }
        }

        // 校验商品是普通、内购商品
        $normalGoods = [];
        $internalPurchaseGoods = [];

        foreach ($goods as $good) {
            if ($good['is_internal_purchase'] == 1) {
                $internalPurchaseGoods[] = $good['gid'];
            } else {
                $normalGoods[] = $good['gid'];
            }
        }

        // 合并条件判断
        if (!empty($normalGoods) && !empty($internalPurchaseGoods)) {
            return [false, '内购商品不可与普通商品同时下单'];
        }

        if (empty($internalPurchaseGoods)) {
            return [true, $goods];
        }

        // 获取员工状态
        $uid = by::Phone()->getUidByUserId($user_id);
        $employee = byNew::UserEmployeeModel()->getEmployeeInfo($uid);

        // 检查员工状态
        if (empty($employee) || $employee['employee_status'] == UserEmployeeModel::EMPLOYEE_STATUS['STOP']) {
            return [false, '微笑大使身份失效，不可购买内购商品'];
        }

        if ($employee['is_blacklist'] == UserEmployeeModel::IS_BLACKLIST['YES']) {
            return [false, '微笑大使身份冻结中，不可购买内购商品'];
        }




        return [true, $goods];
    }

    /**
     * 检查商品规格
     * @param array $gcombines
     * @param $goods
     * @return array
     */
    private function checkGidSid(array $gcombines, $goods): array
    {
        $goodsType = array_column($goods, 'atype', 'gid');

        foreach ($gcombines as $index => $item) {
            $gid   = $item['gid'] ?? '';
            $sid   = $item['sid'] ?? 0;
            $atype = $goodsType[$gid] ?? false;

            // 对单规格不等于0的商品，补偿规格为0
            if ($atype!==false && $atype == Gtype0Model::ATYPE['SPEC'] && $sid != 0) {
                $gcombines[$index]['sid'] = 0;
            }
        }

        return $gcombines;
    }


    /**
     * 是否包含内购商城商品
     * @param array $gcombines
     * @return bool
     */
    private function isIncludeInternalGoods(array $gcombines): bool
    {
        // 获取商品ID
        $gids = array_column($gcombines, 'gid');
        // 获取商品信息
        $goods = by::Gtype0()->getListByGids($gids, ['gid', 'is_internal_purchase']);
        foreach ($goods as $good) {
            if ($good['is_internal_purchase'] == 1) {
                return true;
            }
        }
        return false;
    }

    /**
     * 保存用户绑定订单信息，（只绑定普通订单、内购订单、尾款订单）
     * @param $user_id
     * @param $order_no
     * @param $order_type
     * @return void
     * @throws Exception
     */
    private function saveUserBindOrder($user_id, $order_no, $order_type)
    {
        if (!in_array($order_type, [self::USER_ORDER_TYPE['COMMON'], self::USER_ORDER_TYPE['TAIL'], self::USER_ORDER_TYPE['OTN'], self::USER_ORDER_TYPE['INTERNAL']])) {
            return;
        }

        $uid = byNew::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return;
        }

        // 获取用户绑定信息
        $userBindParam = [
            'bound_uid'   => $uid,
            'bind_status' => UserBindModel::BIND_STATUS['BIND']
        ];
        $userBinds     = byNew::UserBindModel()->getBindList($userBindParam, 1, 1);

        if (empty($userBinds[0]) || ($uid == $userBinds[0]['uid'])) { // 当前购买的用户，没有绑定的推荐人 或 自己绑定自己
            return;
        }

        // 插入用户订单绑定信息
        $data = [
            'uid'       => $userBinds[0]['uid'],
            'bound_uid' => $uid,
            'order_no'  => $order_no,
            'score'     => 2000, // 固定给2000积分
        ];
        byNew::BoundUserOrderModel()->saveData($data);
    }
}