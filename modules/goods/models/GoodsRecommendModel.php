<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 商品推荐总表模型
 */
class GoodsRecommendModel extends CommModel
{
    public $tb_fields = [
            'id',
            'gid',
            'sid',
            'total_times'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`goods_recommend`";
    }

    /**
     * 获取推荐缓存键
     * @param string $gidSids
     * @return string
     */
    private function __getRecommendCacheKey(string $gidSids): string
    {
        return AppCRedisKeys::getGoodsRecommendKey($gidSids);
    }

    /**
     * 获取所有推荐缓从大到小排序
     * @param $page
     * @param $pageSize
     * @return string
     */
    private function __getRecommendAllListKey($page, $pageSize): string
    {
        return AppCRedisKeys::getRecommendAllListKey($page, $pageSize);
    }

    /**
     * 删除推荐缓存
     * @param int $gid
     * @param int $sid
     * @return int
     */
    private function __delCache(int $gid, int $sid): int
    {
        $redis = by::redis('core');
        $key   = $this->__getRecommendCacheKey("{$gid}_{$sid}");
        return $redis->del($key);
    }

    /**
     * 批量获取商品推荐次数
     * @param array $gidSids 格式：['gid_sid', ...]
     * @return array 格式：['gid_sid' => total_times, ...]
     * @throws Exception
     */
    public function getRecommendTimes(array $gidSids): array
    {
        if (empty($gidSids)) {
            return [];
        }

        $result    = [];
        $needQuery = [];

        // 检查缓存
        $redis = by::redis('core');
        foreach ($gidSids as $gidSid) {
            $cacheKey = $this->__getRecommendCacheKey($gidSid);
            $cached   = $redis->get($cacheKey);
            if ($cached !== false) {
                $result[$gidSid] = (int) $cached;
            } else {
                $needQuery[] = $gidSid;
            }
        }

        // 查询未缓存的数据
        if (!empty($needQuery)) {
            $conditions = [];
            $params     = [];
            foreach ($needQuery as $index => $gidSid) {
                list($gid, $sid) = explode('_', $gidSid);
                $conditions[]           = "(gid = :gid{$index} AND sid = :sid{$index})";
                $params[":gid{$index}"] = (int) $gid;
                $params[":sid{$index}"] = (int) $sid;
            }

            $tb   = $this->tbName();
            $sql  = "SELECT gid, sid, total_times FROM {$tb} WHERE " . implode(' OR ', $conditions);
            $data = by::dbMaster()->createCommand($sql, $params)->queryAll();

            // 处理查询结果并缓存
            $foundGidSids = [];
            foreach ($data as $row) {
                $gidSid          = $row['gid'] . '_' . $row['sid'];
                $totalTimes      = (int) $row['total_times'];
                $result[$gidSid] = $totalTimes;
                $foundGidSids[]  = $gidSid;

                // 缓存结果
                $cacheKey = $this->__getRecommendCacheKey($gidSid);
                $redis->setex($cacheKey, 3600, $totalTimes);
            }

            // 对于未找到的记录，设置为0并缓存
            foreach ($needQuery as $gidSid) {
                if (!in_array($gidSid, $foundGidSids)) {
                    $result[$gidSid] = 0;
                    $cacheKey        = $this->__getRecommendCacheKey($gidSid);
                    $redis->setex($cacheKey, 60, 0); // 未找到的记录缓存时间短一些
                }
            }
        }

        return $result;
    }

    /**
     * 增加推荐次数
     * @param int $gid
     * @param int $sid
     * @return bool
     * @throws Exception
     */
    public function incrementRecommendTimes(int $gid, int $sid): bool
    {
        $tb  = $this->tbName();
        $sql = "INSERT INTO {$tb} (gid, sid, total_times) VALUES (:gid, :sid, 1) 
                ON DUPLICATE KEY UPDATE total_times = total_times + 1";

        $params = [
                ':gid' => $gid,
                ':sid' => $sid
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->execute();

        if ($result) {
            // 清除缓存
            $this->__delCache($gid, $sid);
        }

        return $result > 0;
    }

    /**
     * 减少推荐次数
     * @param int $gid
     * @param int $sid
     * @return bool
     * @throws Exception
     */
    public function decrementRecommendTimes(int $gid, int $sid): bool
    {
        $tb  = $this->tbName();
        $sql = "UPDATE {$tb} SET total_times = GREATEST(total_times - 1, 0) 
                WHERE gid = :gid AND sid = :sid AND total_times > 0";

        $params = [
                ':gid' => $gid,
                ':sid' => $sid
        ];

        $result = by::dbMaster()->createCommand($sql, $params)->execute();

        if ($result) {
            // 清除缓存
            $this->__delCache($gid, $sid);
        }

        return $result > 0;
    }

    /**
     * 获取单个商品的推荐次数
     * @param int $gid
     * @param int $sid
     * @return int
     * @throws Exception
     */
    public function getGoodsRecommendTimes(int $gid, int $sid): int
    {
        $gidSid = "{$gid}_{$sid}";
        $result = $this->getRecommendTimes([$gidSid]);
        return $result[$gidSid] ?? 0;
    }

 // 获取推荐列表（按推荐次数从大到小排序）
 public function getRecommendAllList(int $page = 1, int $pageSize = 10): array
 {
     // 尝试从缓存获取数据
//     $redis = by::redis('core');
//     $cacheKey = $this->__getRecommendAllListKey($page, $pageSize);
//     $cached = $redis->get($cacheKey);
//
//     if ($cached !== false) {
//         return json_decode($cached, true);
//     }

     $tb     = $this->tbName();
     $offset = ($page - 1) * $pageSize;

     // 查询按 total_times 从大到小排序的商品
     $sql = "SELECT gid, sid, total_times FROM {$tb} 
          WHERE total_times > 0 
          ORDER BY total_times DESC, gid DESC 
          LIMIT :offset, :page_size";

     $params = [
             ':offset'    => $offset,
             ':page_size' => $pageSize
     ];

     $data = by::dbMaster()->createCommand($sql, $params)->queryAll();

     // 查询总数
     $countSql    = "SELECT COUNT(*) as total FROM {$tb} WHERE total_times > 0";
     $countResult = by::dbMaster()->createCommand($countSql)->queryOne();
     $total       = (int) ($countResult['total'] ?? 0);

     $result = [];
     if (!empty($data)) {
         foreach ($data as $row) {
             $result[] = [
                     'gid'         => $row['gid'],
                     'sid'         => $row['sid'],
                     'total_times' => $row['total_times']
             ];
         }
     }

     $response = [
             'list'      => $result,
             'total'     => $total,
             'page'      => $page,
             'page_size' => $pageSize,
             'pages'     => \app\models\CUtil::getPaginationPages($total, $pageSize)
     ];

     // 设置缓存，60秒过期
//     $redis->setex($cacheKey, 60, json_encode($response));

     return $response;
 }


} 