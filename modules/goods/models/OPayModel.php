<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/7
 * Time: 19:09
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class OPayModel extends CommModel {

    public $tb_fields = [
        'id','order_no','prepay_id','tid','price','ptime','pay_time','pay_type','h5_url','payment_plan'
    ];

    /**
     * 支付方案
     */
    const PAYMENT_PLAN = [
            'NO_INST' => '不分期',
            'INST_3' => '3期分期',
            'INST_6' => '6期分期',
            'INST_12' => '12期分期',
            'FREE_3' => '3期免息',
            'FREE_6' => '6期免息',
            'FREE_12' => '12期免息',
    ];

    /**
     *  分期期数
     */
    const  PAYMENT_PLAN_PERIOD = [
            'NO_INST' => 1,
            'INST_3'  => 3,
            'INST_6'  => 6,
            'INST_12' => 12,
            'FREE_3'  => 3,
            'FREE_6'  => 6,
            'FREE_12' => 12,
    ];

    /**
     * @param $order_no
     * @return string
     * 安全分表
     */
    public static function tbName($order_no): string
    {
        $time = by::Omain()->GetTbNameByOrderId($order_no, false);
        $year = date("Y",intval($time));

        return  "db_dreame_goods.t_opay_{$year}";
    }

    public static function GetTbNameByOrderYear($year): string
    {
        return  "db_dreame_goods.t_opay_{$year}";
    }

    /**
     * @param $order_no
     * @param $p_s
     * @return string
     * 支付流水详情
     */
    private function __getOrderPayInfoKey($order_no): string
    {
        return AppCRedisKeys::getOrderPayInfo($order_no);
    }

    /**
     * @param $order_no
     * @return int
     * 缓存清理
     */
    private function __delCache($order_no): int
    {
        $r_key1 = $this->__getOrderPayInfoKey($order_no);
        return by::redis('core')->del($r_key1);
    }

    /**
     * @param $order_no
     * @return array|false
     * @throws Exception
     * 付款流水详情
     */
    public function GetOneInfo($order_no,$cache=true)
    {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOrderPayInfoKey($order_no);
        $aJson       = $cache ? $redis->get($redis_key) : false;
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName($order_no);
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `order_no`=:order_no LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':order_no' => $order_no])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $cache && $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }


    /**
     * @param $order_no
     * @param array $data
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog($order_no, array $data = []): array
    {
        $tb   = self::tbName($order_no);

        $aLog = $this->GetOneInfo($order_no);

        if(empty($aLog)) {
            $price     = $data['price'] ?? 0;
            $tid       = $data['tid'] ?? '';
            $prepay_id = $data['prepay_id'] ?? '';
            $pay_type  = $data['pay_type'] ?? '';
            $h5_url    = $data['h5_url'] ?? '';
            $payment_plan = $data['payment_plan'] ?? 'NO_INST';

            $save = [
                'order_no'  => $order_no,
                'prepay_id' => $prepay_id,
                'tid'       => $tid,
                'price'     => $price,
                'ptime'     => intval(START_TIME),
                'pay_type'  => $pay_type,
                'h5_url'    => $h5_url,
                'payment_plan' => $payment_plan,
            ];

            $rows = by::dbMaster()->createCommand()->insert($tb,$save)->execute();

        } else {
            $allowed = ['pay_time','tid','prepay_id','ptime','h5_url','pay_type','price','payment_plan'];
            foreach ($data as $field => $val) {
                if ( !in_array($field, $allowed) ) {
                    unset($data[$field]);
                }
            }
            if (empty($data)) {
                return [false, '无数据更改'];
            }

            $rows = by::dbMaster()->createCommand()->update($tb, $data, ['order_no' => $order_no])->execute();
        }

        $this->__delCache($order_no); //todo 清空缓存

        return [true,$rows];
    }


    /**
     * @param int $year
     * @param $orderTid
     * @return string
     * 通过交易单号获取订单号
     */
    public function GetOrderNoByTid(int $year, $orderTid): string
    {
        // 根据年份获取对应的订单表名并设置动态表名
        $orderModel = self::find()->from(self::GetTbNameByOrderYear($year));

        // 使用 ORM 查询 tid 对应的订单号
        $orderNo = $orderModel->select('order_no')
                ->where(['tid' => $orderTid])
                ->scalar();

        // 如果结果为 false 或 null，返回空字符串
        return $orderNo !== false && $orderNo !== null ? (string) $orderNo : '';
    }


    public function getPayPlan($payment_plan): array
    {
        return [
                'name'  => OPayModel::PAYMENT_PLAN[$payment_plan] ?? OPayModel::PAYMENT_PLAN['NO_INST'],
                'value' => OPayModel::PAYMENT_PLAN_PERIOD[$payment_plan] ?? 0
        ];
    }

}
