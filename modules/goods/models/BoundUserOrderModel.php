<?php

namespace app\modules\goods\models;

use app\models\by;
use app\modules\main\models\CommModel;

/**
 * 被订单的用户订单号模型
 */
class BoundUserOrderModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_bound_user_order`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 1:待发放 2:已发放 3:已作废
    const SCORE_STATUS = [
        'WAIT'   => 1,
        'SEND'   => 2,
        'CANCEL' => 3
    ];

    const SCORE_STATUS_NAME = [
        1 => '未发放',
        2 => '已发放',
        3 => '已作废'
    ];

    // 获取订单列表
    public function getOrderList(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        // 创建查询构建器
        $query = self::find()->select(['*']);

        // 获取查询条件
        $condition = $this->getSearchCondition($params);

        // 应用查询条件
        if (!empty($condition)) {
            $query->where($condition);
        }

        // 分页和排序
        return $query->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
    }

    // 获取数量
    public function getOrderCount(array $params = []): int
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->where($condition)
            ->count();
    }

    // 获取订单集合
    public function getOrders(array $params = []): array
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->where($condition)
            ->asArray()
            ->all();
    }

    // 保存绑定用户订单数据
    public function saveData(array $data): bool
    {
        // 获取记录
        $order = self::findOne(['order_no' => $data['order_no']]);

        if ($order === null) {
            // 如果不存在，创建新的记录
            $order                    = new self();
            $order->uid               = $data['uid'];
            $order->bound_uid         = $data['bound_uid'];
            $order->order_no          = $data['order_no'];
            $order->score             = $data['score'];
            $order->score_status      = self::SCORE_STATUS['WAIT'];
            $order->order_create_time = $data['order_create_time'] ?? time(); // 订单创建时间
            $order->ctime             = time();                               // 设置创建时间
        }

        // 更新或设置积分状态
        if (isset($data['score_status'])) {
            $order->score_status = $data['score_status'];
        }

        $order->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$order->save(false)) {
            return false;
        }

        return true;
    }

    // 更新订单状态为：已发放
    public function updateStatusToSend($order_no): bool
    {
        $order = self::findOne(['order_no' => $order_no]);

        if ($order === null || $order->score_status != self::SCORE_STATUS['WAIT']) {
            return false;
        }

        // 更新积分状态
        $order->score_status = self::SCORE_STATUS['SEND'];
        $order->utime        = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$order->save(false)) {
            return false;
        }

        return true;
    }

    /**
     * 批量更新订单状态
     * @param $order_nos
     * @param int $status
     * @return int
     */
    public function updateStatus($order_nos, int $status): int
    {
        return self::updateAll(['score_status' => $status, 'utime' => time()], ['order_no' => $order_nos]);
    }

    /**
     * 获取订单列表（方法用于脚本，写的比较局限）
     * @param $id
     * @param $limit
     * @return array
     */
    public function getOrderListForUpdateStatus($id, $limit): array
    {
        return self::find()
            ->where(['>', 'id', $id])
            ->andWhere(['score_status' => self::SCORE_STATUS['WAIT']])
            ->limit($limit)
            ->asArray()
            ->all();
    }

    // 获取搜索条件
    private function getSearchCondition(array $params): array
    {
        $conditions = ['and'];

        if (!empty($params['uid'])) {
            $conditions[] = ['uid' => $params['uid']];
        }

        if (!empty($params['bound_uid'])) {
            $conditions[] = ['bound_uid' => $params['bound_uid']];
        }

        if (!empty($params['order_no'])) {
            $conditions[] = ['order_no' => $params['order_no']];
        }

        if (!empty($params['score_status'])) {
            $conditions[] = ['score_status' => $params['score_status']];
        }

        // 查询订单创建时间
        if (!empty($params['order_start_create_time']) && !empty($params['order_end_create_time'])) {
            $conditions[] = ['between', 'order_create_time', $params['order_start_create_time'], $params['order_end_create_time']];
        }

        // 移除数组中只有 'and' 的情况，即没有其他条件时
        if (count($conditions) === 1) {
            return [];
        }

        return $conditions;
    }

    //获取推荐购买数量
    public function getRecommendBuyCount(){
        $list = self::find()
            ->select(['count(id) as count','uid'])
            ->groupBy('uid')
            ->asArray()
            ->all();
        $res = [];
        foreach($list as $k=>$v){
            $res[$v['uid']] = $v['count'];
        }
        return $res;
    }

}