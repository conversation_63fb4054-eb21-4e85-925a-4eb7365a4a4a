<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\AppNRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\common\models\BaseModel;
use yii\db\ActiveQuery;

/**
 * 国补活动主表模型
 *
 * @property int $id 活动ID
 * @property string $name 活动名称
 * @property int $start_time 开始时间
 * @property int $end_time 结束时间
 * @property string $desc 活动描述
 * @property int $create_time 创建时间
 * @property int $update_time 更新时间
 * @property int $delete_time 删除时间
 * @property int $is_deleted 是否删除：0-未删除，1-已删除
 */
class SubsidyActivityModel extends BaseModel
{

    // 删除状态
    const IS_DELETED = [
            'NO'  => 0, // 未删除
            'YES' => 1, // 已删除
    ];

    public static function tableName(): string
    {
        return '`db_dreame_goods`.`subsidy_activity`';
    }

    public static function getDb()
    {
        return by::dbMaster();
    }

    public function rules()
    {
        return [
                [['name', 'start_time', 'end_time'], 'required'],
                [['start_time', 'end_time', 'create_time', 'update_time', 'delete_time', 'is_deleted'], 'integer'],
                [['name'], 'string', 'max' => 255],
                [['desc'], 'string'],
                [['is_deleted'], 'in', 'range' => array_keys(self::IS_DELETED)],
                ['start_time', 'compare', 'compareAttribute' => 'end_time', 'operator' => '<', 'message' => '开始时间必须小于结束时间'],
        ];
    }

    public function attributeLabels(): array
    {
        return [
                'id'          => '活动ID',
                'name'        => '活动名称',
                'start_time'  => '开始时间',
                'end_time'    => '结束时间',
                'desc'        => '活动描述',
                'create_time' => '创建时间',
                'update_time' => '更新时间',
                'delete_time' => '删除时间',
                'is_deleted'  => '是否删除',
        ];
    }

    /**
     * 关联商品表
     */
    public function getGoods()
    {
        return $this->hasMany(SubsidyActivityGoodsModel::class, ['activity_id' => 'id']);
    }

    /**
     * 获取活动列表
     * @param array $params
     * @return ActiveQuery
     */
    public function getActivityList(array $params = []): ActiveQuery
    {
        $query = self::find();

        // 基础条件
        $query->where(['is_deleted' => self::IS_DELETED['NO']]);

        // 搜索条件
        if (!empty($params['name'])) {
            $query->andWhere(['like', 'name', $params['name']]);
        }

        // 时间范围查询
        if (!empty($params['start_time'])) {
            $query->andWhere(['>=', 'start_time', $params['start_time']]);
        }

        if (!empty($params['end_time'])) {
            $query->andWhere(['<=', 'end_time', $params['end_time']]);
        }

        $query->orderBy(['id' => SORT_DESC]);

        return $query;
    }

    /**
     * 检查时间段冲突
     * @param int $start_time
     * @param int $end_time
     * @param int|null $exclude_id 排除的活动ID（用于编辑时）
     * @return bool
     */
    public function checkTimeConflict(int $start_time, int $end_time, int $exclude_id = null): bool
    {
        $query = self::find();
        $query->where(['is_deleted' => self::IS_DELETED['NO']]);

        // 排除指定ID
        if ($exclude_id) {
            $query->andWhere(['<>', 'id', $exclude_id]);
        }

        // 时间冲突检查：新活动的开始时间在现有活动时间段内，或新活动的结束时间在现有活动时间段内，或新活动完全包含现有活动
        $query->andWhere([
                'or',
                ['and', ['<=', 'start_time', $start_time], ['>=', 'end_time', $start_time]], // 新活动开始时间在现有活动时间段内
                ['and', ['<=', 'start_time', $end_time], ['>=', 'end_time', $end_time]], // 新活动结束时间在现有活动时间段内
                ['and', ['>=', 'start_time', $start_time], ['<=', 'end_time', $end_time]], // 现有活动在新活动时间段内
        ]);

        return $query->exists();
    }

    /**
     * 获取当前生效的活动
     * @throws \Throwable
     */
    public function getCurrentActivity(): array
    {
        $data= CUtil::rememberCache(AppCRedisKeys::currentSubsidyActivity(), function (&$options) {
            $current_time = time();

            $result = self::find()
                    ->where(['is_deleted' => self::IS_DELETED['NO']])
                    ->andWhere(['<=', 'start_time', $current_time])
                    ->andWhere(['>=', 'end_time', $current_time])
                    ->one();

            if (!empty($result)){
                // 设置缓存的过期时间为活动结束时间或默认过期时间
                $options['expire'] = CUtil::calculateCacheTtl([['start_time' => $result->start_time, 'end_time' => $result->end_time]]);
            }

            // 空值缓存时间10分钟
            $options['emptyExpire'] = 60;
            return empty($result) ? [] : $result->toArray();
        });

        return  empty($data)?[]:$data;
    }

    /**
     * 保存活动
     * @param array $data
     * @return array [bool, string|int]
     */
    public function saveActivity(array $data): array
    {
        $id         = $data['id'] ?? 0;
        $name       = trim($data['name'] ?? '');
        $start_time = (int) ($data['start_time'] ?? 0);
        $end_time   = (int) ($data['end_time'] ?? 0);
        $desc       = trim($data['desc'] ?? '');
        $goods      = $data['goods'] ?? [];

        // 参数验证
        if (empty($name)) {
            return [false, '活动名称不能为空'];
        }

        if ($start_time <= 0 || $end_time <= 0) {
            return [false, '请设置正确的活动时间'];
        }

        if ($start_time >= $end_time) {
            return [false, '开始时间必须小于结束时间'];
        }

        if (empty($goods)) {
            return [false, '请选择参与活动的商品'];
        }

        // 检查时间冲突
        if ($this->checkTimeConflict($start_time, $end_time, $id)) {
            return [false, '该时间段已有其他国补活动，请重新选择时间'];
        }

        $transaction = self::getDb()->beginTransaction();
        try {
            $current_time = time();

            if ($id) {
                // 更新
                $model = self::findOne($id);
                if (!$model || $model->is_deleted == self::IS_DELETED['YES']) {
                    return [false, '活动不存在'];
                }
                $model->update_time = $current_time;
            } else {
                // 新增
                $model              = new self();
                $model->create_time = $current_time;
                $model->update_time = $current_time;
                $model->is_deleted  = self::IS_DELETED['NO'];
            }

            $model->name       = $name;
            $model->start_time = $start_time;
            $model->end_time   = $end_time;
            $model->desc       = $desc;

            if (!$model->save()) {
                throw new \Exception('保存活动失败');
            }

            $activity_id = $model->id;

            // 保存商品关联
            $goodsModel = new SubsidyActivityGoodsModel();
            list($success, $message) = $goodsModel->saveActivityGoods($activity_id, $goods);
            if (!$success) {
                throw new \Exception($message);
            }

            $transaction->commit();
            $this->clearCache($activity_id);
            return [true, $activity_id];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
    }


    /**
     * 清除缓存
     * @param $activity_id
     * @return void
     */
    public function clearCache($activity_id)
    {
        CUtil::delCache(AppCRedisKeys::currentSubsidyActivity());
        CUtil::delCache(AppCRedisKeys::getSubsidyActivityGoods($activity_id, '*'), true);
    }


    /**
     * 删除活动
     * @param int $id
     * @return array
     */
    public function deleteActivity(int $id): array
    {
        $model = self::findOne($id);
        if (!$model || $model->is_deleted == self::IS_DELETED['YES']) {
            return [false, '活动不存在'];
        }

        $model->is_deleted  = self::IS_DELETED['YES'];
        $model->delete_time = time();
        $model->update_time = time();

        if ($model->save(false)) { // 跳过验证，因为删除操作不需要验证业务规则
            $this->clearCache($id);
            return [true, '删除成功'];
        }

        return [false, '删除失败'];
    }


}
