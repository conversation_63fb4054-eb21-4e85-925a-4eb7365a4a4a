<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 退款表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class OrefundDepositModel extends CommModel {

    CONST STATUS = [
        'audit'     => 1000,    //待审核
        'pass'      => 1010,    //审核通过
        'refusal'   => 1020,    //审核拒绝
        'Cancel'    => 100,     //取消
        'success'   => 20000000,//退款成功
    ];

    public $tb_fields   = [
        'id','refund_no','order_no','user_id','r_type','m_type','ctime','rtime','status','ostatus','describe',
        'images','a_reason','mail_no','express_code','express_name','price','rback'
    ];

    public static function tbName($user_id): string
    {
        $mod = intval($user_id) % 5;
        return  "`db_dreame_goods`.`t_orefund_d_{$mod}`";
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($user_id,$refund_no): string
    {
        return AppCRedisKeys::getOneDepositRefund($user_id,$refund_no);
    }


    /**
     * @param $user_id
     * @param $refund_no
     * @return int
     * 缓存清理
     * @throws RedisException
     */
    public function DelCache($user_id, $refund_no): int
    {
        $r_key1 = $this->__getOneInfoKey($user_id,$refund_no);
        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param int $user_id
     * @param array $arr
     * @return array
     * @throws Exception
     * 保存数据
     */
    public function SaveLog(int $user_id, array $arr): array
    {
        $order_no   = $arr['order_no']  ?? '';
        $refund_no          = $arr['refund_no'] ?? '';
        $m_type             = $arr['m_type']    ?? 0;
        $r_type             = $arr['r_type']    ?? 0;
        $describe           = $arr['describe']  ?? "";
        $images             = $arr['images']    ?? "";
        $ctime              = $arr['ctime']     ?? 0;
        $ostatus            = $arr['ostatus']   ?? 0;

        $save       = [
            'refund_no' => $refund_no,
            'order_no'  => $order_no,
            'user_id'   => $user_id,
            'r_type'    => $r_type,
            'm_type'    => $m_type,
            'ctime'     => $ctime,
            'ostatus'   => $ostatus,
            'describe'  => $describe,
            'images'    => $images,
        ];

        $tb = $this->tbName($user_id);
        by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $this->DelCache($user_id, $refund_no);

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array|false
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetInfoByRefundNo($user_id, $refund_no)
    {
        $user_id     = CUtil::uint($user_id);

        if(empty($user_id) || empty($refund_no)) {
            return [];
        }

        $redis       = by::redis('core');
        $r_key       = $this->__getOneInfoKey($user_id,$refund_no);
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb         = $this->tbName($user_id);
            $fields     = implode("`,`",$this->tb_fields);

            $sql        = "SELECT `{$fields}` FROM {$tb} WHERE `refund_no`=:refund_no AND `user_id`=:user_id LIMIT 1";
            $aData      = by::dbMaster()->createCommand($sql,[':refund_no' => $refund_no,':user_id'=>$user_id])->queryOne();
            $aData      = $aData ?: [];
            $redis->set($r_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 1800]);
        }

        return $aData;
    }
}
