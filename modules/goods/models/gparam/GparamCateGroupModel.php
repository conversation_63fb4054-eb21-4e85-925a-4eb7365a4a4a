<?php

namespace app\modules\goods\models\gparam;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\BaseModel;
use yii\db\ActiveQuery;

/**
 * 商品参数的二级类目分组，缓存改造完成！
 * the goods params's category group
 */
class GparamCateGroupModel extends BaseModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public static $tb_fields = [
        'id', 'pid', 'cate_id', 'name'
    ];

    // 表名称
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_gparam_categroup`";
    }

    /**
     * 获取列表
     * @param array $condition
     * @param $columns
     * @return array
     */
    public function getCateGroupList(array $condition, $columns): array
    {
        // 查询数据
        $items = $this->__getCateGroupList($condition);

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->select($columns);
    }

    /**
     * 根据分组ID， 获取分组
     * @param $id
     * @return array
     */
    public function getCateGroupById($id): array
    {
        // 获取数据
        $items = self::__getCateGroupById($id);
        if (empty($items)) {
            return [];
        }

        return $items;
    }

    /**
     * 根据 类目ID 获取分组信息
     * @param int $cId
     * @param $columns
     * @return array
     */
    public function getCateGroupByCateId(int $cId, $columns): array
    {
        // 获取数据
        $items = self::__getCateGroupByCateId($cId);
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->sortBy('id')
            ->select($columns);
    }

    /**
     * 分组是否存在
     * @param $cId
     * @param $name
     * @return bool
     */
    public function isExistCateGroup($cId, $name): bool
    {
        // 获取数据
        $items = self::__getCateGroupByCateId($cId);
        if (empty($items)) {
            return false;
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)->where('name', $name)->isNotEmpty();
    }

    /**
     * 根据类目ID， 获取分组
     *（后台业务，不处理缓存）
     * @param int $cateId
     * @param [] $columns
     * @param [] $with
     * @return array
     */
    public function getCateGroupByCateIdNoCache(int $cateId, $columns = ['*'], $with = []): array
    {
        return self::find()
            ->select($columns)
            ->with($with)
            ->where(['cate_id' => $cateId])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->all();
    }

    /**
     * 参数详情，一个分组有多个参数详情（1对多）
     * @return \yii\db\ActiveQuery
     */
    public function getDetail(): ActiveQuery
    {
        return $this->hasMany(GparamCateGroupDetailModel::class, ['categroup_id' => 'id'])
            ->select(['id', 'categroup_id', 'gparam_id'])
            ->where(['is_del' => self::IS_DEL['no']]);
    }

    /**
     * 保存数据
     * @param array $data
     * @return array
     * @throws \Throwable
     * @throws \yii\db\StaleObjectException
     */
    public function saveLog(array $data): array
    {
        $id = $data['id'] ?? 0;
        if ($id > 0) { // 更新
            $model = self::findOne($id);
            $model->name = $data['name'];
            $model->utime = $data['utime'] ?? time();
            $res = $model->update();
        } else { // 添加
            $model = new self();
            $model->pid = $data['pid'] ?? 0;
            $model->cate_id = $data['cate_id'];
            $model->name = $data['name'];
            $model->is_del = $data['is_del'] ?? 0;
            $model->ctime = $data['ctime'] ?? time();
            $model->utime = $data['utime'] ?? time();
            $res = $model->save(); // 成功返回 true
        }

        // 删除缓存
        $this->delCateGroupListCache();
        $this->delCateGroupByIdCache($model->id);
        $this->delCateGroupByCateIdCache($model->cate_id);

        // 返回id
        return [($res != false), $model->id];
    }

    /**
     * 删除数据
     * @param int $id
     * @return array
     * @throws \yii\db\Exception
     */
    public function DeleteLog(int $id): array
    {
        // 验证是否存在
        $model = self::findOne($id);
        if (!$model) {
            return [true, 'ok'];
        }

        // 软删除
        $ret = by::dbMaster()->createCommand()
            ->update(self::tableName(), ['is_del' => self::IS_DEL['yes'], 'dtime' => time()], ['id' => $id])
            ->execute();
        if (!$ret) {
            return [false, '未知原因，更新失败'];
        }

        // 删除缓存
        $this->delCateGroupListCache();
        $this->delCateGroupByIdCache($id);
        $this->delCateGroupByCateIdCache($model->cate_id);

        return [true, 'ok'];
    }

    /**
     * 根据 id 获取数据
     * @param int $id
     * @return array
     */
    private static function __getCateGroupById(int $id): array
    {
        // redis 缓存
        $redis = by::redis();
        $r_key = self::__getCateGroupByIdKey($id);

        // 获取，从缓存中获取数据
        $res = $redis->get($r_key);

        // 缓存中有数据，直接返回
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where(['id' => $id])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->one();

        // 空值转为数组
        $data = $data ?? [];

        // 插入，插入数据到缓存
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 根据 cate_id 获取数据
     * @param int $cId
     * @return array
     */
    private static function __getCateGroupByCateId(int $cId): array
    {
        // redis 缓存
        $redis = by::redis();
        $r_key = self::__getCateGroupByCateIdKey($cId);

        // 获取，从缓存中获取数据
        $res = $redis->get($r_key);

        // 缓存中有数据，直接返回
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields) // 查询全量数据
            ->where(['cate_id' => $cId])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 获取列表
     * @param array $condition
     * @return array
     */
    public function __getCateGroupList(array $condition): array
    {
        // 查找条件
        $condition = $this->__getCondition($condition);

        // 从 redis 中获取数据
        $redis = by::redis();
        $r_key = self::__getCateGroupListKey();
        // 序列化 id 的参数
        $hash_key = serialize($condition);
        $res = $redis->hGet($r_key, $hash_key);

        // 结果
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where($condition)
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存，并更新过期时间
        $redis->hSet($r_key, $hash_key, json_encode($data));
        CUtil::ResetExpire($r_key, self::EXPIRE_TIME);

        return $data;
    }

    /**
     * 请求条件
     * @param array $condition
     * @return array
     */
    private function __getCondition(array $condition): array
    {
        // 条件
        $where = null;

        // id 查询
        if (isset($condition['id']) && $condition['id']) {
            $where['id'] = $condition['id'];
        }

        // cate_id 查询
        if (isset($condition['cate_id']) && $condition['cate_id']) {
            $where['cate_id'] = $condition['cate_id'];
        }

        // name 查询
        if (isset($condition['name']) && $condition['name']) {
            $where['name'] = $condition['name'];
        }

        // 未删除
        $where['is_del'] = self::IS_DEL['no'];

        return $where;
    }

    /**
     * 获取 hash 的 key
     * @return string
     */
    private static function __getCateGroupListKey(): string
    {
        return AppCRedisKeys::getCateGroupList();
    }

    /**
     * 获取 redis key
     * @param int $id
     * @return string
     */
    private static function __getCateGroupByIdKey(int $id): string
    {
        return AppCRedisKeys::getCateGroupById($id);
    }

    /**
     * 获取 redis key
     * @param int $cId
     * @return string
     */
    private static function __getCateGroupByCateIdKey(int $cId): string
    {
        return AppCRedisKeys::getCateGroupByCateId($cId);
    }

    /**
     * 删除 redis 缓存
     */
    public function delCateGroupListCache()
    {
        $r_key = self::__getCateGroupListKey();
        by::redis('core')->del($r_key);
    }

    /**
     * 删除 redis 缓存
     */
    public function delCateGroupByIdCache($id)
    {
        $r_key = self::__getCateGroupByIdKey($id);
        by::redis('core')->del($r_key);
    }

    /**
     * 删除 redis 缓存
     */
    public function delCateGroupByCateIdCache($cId)
    {
        $r_key = self::__getCateGroupByCateIdKey($cId);
        by::redis('core')->del($r_key);
    }
}