<?php

namespace app\modules\goods\models\gparam;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\BaseModel;

/**
 * 商品的商品参数
 * the goods's goods params
 */
class GparamGoodsModel extends BaseModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public static $tb_fields = [
        'id', 'sku', 'categroup_detail_id', 'param_info', 'sort', 'is_show', 'is_del'
    ];

    // 表名称
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_gparam_goods`";
    }

    /**
     * 获取列表
     * @param array $condition
     * @param $columns
     * @return array
     */
    public function getGoodsParamList(array $condition, $columns): array
    {
        // 查询数据
        $items = $this->__getGoodsParamList($condition);

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->select($columns);
    }

    /**
     * 根据 sku 获取参数
     * @param string $sku
     * @param array|int[] $isShow
     * @param [] $columns
     * @return array
     */
    public function getParamsBySku(string $sku, array $isShow = [0, 1], $columns = ['id']): array
    {
        // 获取数据
        $items = self::__getParamsBySku($sku);
        if (empty($items)) {
            return [];
        }

        // 处理数据，包含条件：是否显示的状态、返回的结果
        return Collection::wrap($items)
            ->whereIn('is_show', $isShow)
            ->select($columns);
    }

    /**
     * 批量插入数据
     * @param $columns
     * @param $rows
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchInsertLog($columns, $rows): array
    {
        if (empty($rows)) {
            return [true, 'ok'];
        }

        $ret = by::dbMaster()->createCommand()->batchInsert(self::tableName(), $columns, $rows, 100)->execute();
        if (!$ret) {
            return [false, '未知原因，插入失败'];
        }

        // 删除缓存
        $skus = array_unique(array_column($rows, 'sku'));
        foreach ($skus as $sku) {
            // 删除缓存
            $this->__delParamsBySkuCache($sku);
        }
        $this->__delGoodsParamListCache();

        return [true, 'ok'];
    }

    /**
     * 批量更新数据
     * @param $rows
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchUpdateLog($rows): array
    {
        if (empty($rows)) {
            return [true, 'ok'];
        }

        // 当前时间戳
        $time = time();

        $ids = array_column($rows, 'id');
        $items = array_column($rows, null, 'id');
        $models = self::find()->where(['id' => $ids])->all();
        foreach ($models as $model) {
            $id = $model->id;
            if ($items[$id]) {
                $model->param_info = $items[$id]['param_info'] ?? '';
                $model->sort = $items[$id]['sort'] ?? 0;
                $model->is_show = $items[$id]['is_show'] ?? 0;
                $model->utime = $items[$id]['utime'] ?? $time;
            }
            $model->save();
            // 删除缓存
            $this->__delParamsBySkuCache($model->sku);
        }
        // 删除缓存
        $this->__delGoodsParamListCache();
        return [true, 'ok'];
    }

    /**
     * 批量删除，更新 is_del 的状态
     * @param $groupDetailIds
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchDeleteLog($groupDetailIds)
    {
        if (empty($groupDetailIds)) {
            return [true, 'ok'];
        }

        // 当前时间戳
        $time = time();

        // 软删除
        $models = self::find()->where(['categroup_detail_id' => $groupDetailIds])->all();
        foreach ($models as $model) {
            $model->is_del = self::IS_DEL['yes'];
            $model->dtime = $time;
            $model->save();
            // 删除缓存
            $this->__delParamsBySkuCache($model->sku);
        }
        // 删除缓存
        $this->__delGoodsParamListCache();
        return [true, 'ok'];
    }

    /**
     * 批量删除，更新 is_del 的状态
     * @param $ids
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchDeleteByIds($ids)
    {
        if (empty($ids)) {
            return [true, 'ok'];
        }

        // 当前时间戳
        $time = time();

        // 软删除
        $models = self::find()->where(['id' => $ids])->all();
        foreach ($models as $model) {
            $model->is_del = self::IS_DEL['yes'];
            $model->dtime = $time;
            $model->save();
            // 删除缓存
            $this->__delParamsBySkuCache($model->sku);
        }
        // 删除缓存
        $this->__delGoodsParamListCache();
        return [true, 'ok'];
    }

    /**
     * 保存商品参数
     * @param $sku
     * @param $c_id
     * @param $params
     * @return array
     */
    public function saveLog($sku, $c_id, $params): array
    {
        // 获取 sku 的存量参数
        $columns = ['id', 'sku', 'categroup_detail_id', 'param_info', 'sort', 'is_show'];
        $goodsParamItems = self::getParamsBySku($sku, [0, 1], $columns);
        // 处理数据
        $paramItems = array_column($params, null, 'categroup_detail_id');
        $existsItems = array_column($goodsParamItems, null, 'categroup_detail_id');

        // 数据分类（修改、删除、增加）
        $data = $this->__getHandleData($sku, $c_id, $paramItems, $existsItems);

        try {
            foreach ($data as $key => $value) {
                switch ($key) {
                    case 'INSERT':
                        $columns = ['sku', 'categroup_detail_id', 'param_info', 'sort', 'is_show', 'is_del', 'ctime', 'utime'];
                        self::BatchInsertLog($columns, $value);
                        break;
                    case 'DELETE':
                        self::BatchDeleteByIds($value);
                        break;
                    case 'UPDATE':
                        self::BatchUpdateLog($value);
                        break;
                }
            }
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
        return [true, 'ok'];
    }

    /**
     * 获取要处理的数据
     * @param $sku
     * @param $c_id
     * @param $paramItems
     * @param $existsItems
     * @return array
     */
    private function __getHandleData($sku, $c_id, $paramItems, $existsItems)
    {
        $data = [
            'INSERT' => [],
            'DELETE' => [],
            'UPDATE' => [],
        ];

        // 清除空数据
        foreach ($paramItems as $key => $item) {
            if (mb_strlen(trim($item['param_info'])) == 0) {
                unset($paramItems[$key]);
            }
        }

        // 过滤商品二级类目不一致问题
        $paramItems = $this->__filterCateUnequal($c_id, $paramItems);

        // 当前时间戳
        $time = time();
        foreach ($paramItems as $key => $item) {
            if (isset($existsItems[$key])) { // 修改
                // 判断条件
                $pData = sprintf('%s-%s-%s', $item['param_info'], $item['sort'], $item['is_show']);
                $dData = sprintf('%s-%s-%s', $existsItems[$key]['param_info'], $existsItems[$key]['sort'], $existsItems[$key]['is_show']);
                if ($pData != $dData) {
                    $id = $existsItems[$key]['id'];
                    $tmp = [
                        'id'         => $id,
                        'param_info' => $item['param_info'],
                        'sort'       => $item['sort'],
                        'is_show'    => $item['is_show'],
                        'utime'      => $time,
                    ];
                    $data['UPDATE'][$id] = $tmp;
                }
            } else { // 增加
                $tmp = [
                    'sku'                 => $sku,
                    'categroup_detail_id' => $item['categroup_detail_id'],
                    'param_info'          => $item['param_info'],
                    'sort'                => $item['sort'],
                    'is_show'             => $item['is_show'],
                    'is_del'              => self::IS_DEL['no'],
                    'ctime'               => $time,
                    'utime'               => $time,
                ];
                $data['INSERT'][] = $tmp;
            }
            unset($existsItems[$key]);
        }
        // 删除
        $data['DELETE'] = array_column($existsItems, 'id');
        return $data;
    }

    /**
     * 根据 sku 获取数据（元数据）
     * @param string $sku
     * @return array
     */
    private static function __getParamsBySku(string $sku): array
    {
        // redis 缓存
        $redis = by::redis();
        $r_key = self::__getParamsBySkuKey($sku);

        // 获取，从缓存中获取数据
        $res = $redis->get($r_key);

        // 缓存中有数据，直接返回
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields) // 查询全量数据
            ->where(['sku' => $sku])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->orderBy(['sort' => SORT_ASC])
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 获取分组详情列表
     * @param array $condition
     * @return array
     */
    private function __getGoodsParamList(array $condition): array
    {
        // 查找条件
        $condition = $this->__getCondition($condition);

        // 从 redis 中获取数据
        $redis = by::redis();
        $r_key = self::__getGoodsParamListKey();
        // 序列化 id 的参数
        $hash_key = serialize($condition);
        $res = $redis->hGet($r_key, $hash_key);

        // 结果
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where($condition)
            ->orderBy(['sort' => SORT_DESC])
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存，并更新过期时间
        $redis->hSet($r_key, $hash_key, json_encode($data));
        CUtil::ResetExpire($r_key, self::EXPIRE_TIME);

        return $data;
    }

    /**
     * 请求条件
     * @param array $condition
     * @return array
     */
    private function __getCondition(array $condition): array
    {
        // 条件
        $where = null;

        // id 查询
        if (isset($condition['id']) && $condition['id']) {
            $where['id'] = $condition['id'];
        }

        // sku 查询
        if (isset($condition['sku']) && $condition['sku']) {
            $where['sku'] = $condition['sku'];
        }

        // categroup_detail_id 查询
        if (isset($condition['categroup_detail_id']) && $condition['categroup_detail_id']) {
            $where['categroup_detail_id'] = $condition['categroup_detail_id'];
        }

        // is_show 查询
        if (isset($condition['is_show']) && $condition['is_show']) {
            $where['is_show'] = $condition['is_show'];
        }

        // 未删除
        $where['is_del'] = self::IS_DEL['no'];

        return $where;
    }

    /**
     * 获取 hash 的 key
     * @return string
     */
    private static function __getGoodsParamListKey(): string
    {
        return AppCRedisKeys::getGoodsParamList();
    }

    /**
     * 删除 redis cache
     * @param $sku
     * @return void
     */
    private static function __delGoodsParamListCache()
    {
        $r_key = self::__getGoodsParamListKey();
        by::redis('core')->del($r_key);
    }

    /**
     * 获取 redis key
     * @param $sku
     * @return string
     */
    private static function __getParamsBySkuKey($sku): string
    {
        return AppCRedisKeys::getParamsBySku($sku);
    }

    /**
     * 删除 redis cache
     * @param $sku
     * @return void
     */
    private static function __delParamsBySkuCache($sku)
    {
        $r_key = self::__getParamsBySkuKey($sku);
        by::redis('core')->del($r_key);
    }

    /**
     * 过滤分类不一致问题
     * @param $cId
     * @param $paramItems
     * @return mixed
     */
    private function __filterCateUnequal($cId, $paramItems)
    {
        // 获取分类信息
        $cate = by::cateModel()->getCateById($cId, ['*'], ['parent']);
        $cateId = $cate['parent']['id'] ?? 0; // 二级分类

        // 获取分组信息
        $groupIds = array_column($paramItems, 'group_id');
        $cateGroupItems = array_column(by::GparamCateGroupModel()->getCateGroupList(['id' => $groupIds], ['id', 'cate_id']), null, 'id');

        foreach ($paramItems as $key => $item) {
            if (($cateGroupItems[$item['group_id']]['cate_id'] ?? -1) != $cateId) {
                unset($paramItems[$key]);
            }
        }
        return $paramItems;
    }
}