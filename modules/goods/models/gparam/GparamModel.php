<?php

namespace app\modules\goods\models\gparam;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\modules\goods\models\BaseModel;

/**
 * 商品参数，缓存改造完成！
 * goods params
 */
class GparamModel extends BaseModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public static $tb_fields = [
        'id', 'name'
    ];

    // 表名称
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_gparam`";
    }

    /**
     * 获取全部
     * @param $columns
     * @return array
     */
    public function getParamList($columns): array
    {
        // 获取数据（从缓存中获取）
        $items = self::__getAllParams();
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)->select($columns);
    }

    /**
     * 获取多条（已经加入缓存）
     * @param array $ids
     * @param array|string[] $columns
     * @return array
     */
    public static function getParamByIds(array $ids, array $columns): array
    {
        // 获取数据（从缓存中获取）
        $items = self::__getAllParams();
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->whereIn('id', $ids)
            ->select($columns);
    }

    /**
     * 根据参数 name 获取参数
     * @param string $name
     * @param array|string[] $columns
     * @return array
     */
    public function getParamByName(string $name, array $columns): array
    {
        // 获取数据（从缓存中获取）
        $items = self::__getAllParams();
        if (empty($items)) {
            return [];
        }

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->where('name', 'like', $name)
            ->select($columns);
    }

    /**
     * 获取所有参数（Aside-Cache：旁路缓存）
     * @return array
     */
    private static function __getAllParams(): array
    {
        // redis 缓存
        $redis = by::redis();
        $r_key = self::__getParamsKey();

        // 获取，从缓存中获取数据
        $res = $redis->get($r_key);

        // 缓存中有数据，直接返回
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 获取 redis key
     * dreame|getParamsKey
     * @return string
     */
    private static function __getParamsKey(): string
    {
        return AppCRedisKeys::getParams();
    }

    /**
     * 删除 redis 缓存
     *（暂无没有删除逻辑）
     */
    public function delParamsCache()
    {
        $r_key = self::__getParamsKey();
        by::redis('core')->del($r_key);
    }
}