<?php

namespace app\modules\goods\models\gparam;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\BaseModel;
use yii\db\ActiveQuery;

/**
 * 商品参数的二级类目分组详情
 * the detail of goods params's category group
 */
class GparamCateGroupDetailModel extends BaseModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public static $tb_fields = [
        'id', 'categroup_id', 'gparam_id'
    ];

    // 表名称
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_gparam_categroup_detail`";
    }

    /**
     * 获取列表
     * @param array $condition
     * @param $columns
     * @return array
     */
    public function getCateGroupDetailList(array $condition, $columns): array
    {
        // 查询数据
        $items = $this->__getCateGroupDetailList($condition);

        // 处理数据，使用 Collect 处理数据
        return Collection::wrap($items)
            ->select($columns);
    }

    /**
     * 批量插入数据
     * @param $columns
     * @param $rows
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchInsertLog($columns, $rows): array
    {
        $ret = by::dbMaster()->createCommand()
            ->batchInsert(self::tableName(), $columns, $rows, 100)
            ->execute();
        if (!$ret) {
            return [false, '未知原因，插入失败'];
        }
        // 删除缓存
        $this->delCateGroupDetailListCache();
        return [true, 'ok'];
    }

    /**
     * 批量删除数据
     * @param array $ids
     * @return array
     * @throws \yii\db\Exception
     */
    public function BatchDeleteLog(array $ids): array
    {
        // 软删除
        $ret = by::dbMaster()->createCommand()
            ->update(self::tableName(), ['is_del' => self::IS_DEL['yes'], 'dtime' => time()], ['id' => $ids])
            ->execute();
        if (!$ret) {
            return [false, '未知原因，更新失败'];
        }
        // 删除缓存
        $this->delCateGroupDetailListCache();
        return [true, 'ok'];
    }


    /**
     * 根据 group_ids 获取分组详情
     * @param array $groupIds
     * @param [] $columns
     * @return array
     */
    public function getCateGroupDetailByGroupIds(array $groupIds, $columns = ['*']): array
    {
        return self::find()
            ->select($columns)
            ->where(['categroup_id' => $groupIds])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->all();
    }

    /**
     * 获取参数 id 、 name
     * @return ActiveQuery
     */
    public function getParam(): ActiveQuery
    {
        return $this->hasOne(GparamModel::class, ['id' => 'gparam_id'])
            ->select(['id', 'name'])
            ->where(['is_del' => self::IS_DEL['no']]);
    }

    /**
     * 获取分组详情列表
     * @param array $condition
     * @return array
     */
    private function __getCateGroupDetailList(array $condition): array
    {
        // 查找条件
        $condition = $this->__getCondition($condition);

        // 从 redis 中获取数据
        $redis = by::redis();
        $r_key = self::__getCateGroupDetailListKey();
        // 序列化 id 的参数
        $hash_key = serialize($condition);
        $res = $redis->hGet($r_key, $hash_key);

        // 结果
        if ($res !== false) {
            return json_decode($res, true);
        }

        // 查询，从库中查询数据
        $data = self::find()
            ->select(self::$tb_fields)
            ->where($condition)
            ->asArray(true)
            ->all();

        // 插入，插入数据到缓存，并更新过期时间
        $redis->hSet($r_key, $hash_key, json_encode($data));
        CUtil::ResetExpire($r_key, self::EXPIRE_TIME);

        return $data;
    }

    /**
     * 请求条件
     * @param array $condition
     * @return array
     */
    private function __getCondition(array $condition): array
    {
        // 条件
        $where = null;

        // id 查询
        if (isset($condition['id']) && $condition['id']) {
            $where['id'] = $condition['id'];
        }

        // categroup_id 查询
        if (isset($condition['categroup_id']) && $condition['categroup_id']) {
            $where['categroup_id'] = $condition['categroup_id'];
        }

        // gparam_id 查询
        if (isset($condition['gparam_id']) && $condition['gparam_id']) {
            $where['gparam_id'] = $condition['gparam_id'];
        }

        // 未删除
        $where['is_del'] = self::IS_DEL['no'];

        return $where;
    }

    /**
     * 获取 hash 的 key
     * @return string
     */
    private static function __getCateGroupDetailListKey(): string
    {
        return AppCRedisKeys::getCateGroupDetailList();
    }

    /**
     * 删除 redis 缓存
     */
    public function delCateGroupDetailListCache()
    {
        $r_key = self::__getCateGroupDetailListKey();
        by::redis('core')->del($r_key);
    }

}