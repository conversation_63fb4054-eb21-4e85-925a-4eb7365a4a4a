<?php
/*
* @Author: linze
* @Date: 2022-03-08 19:24:07
 * @LastEditors: linze
 * @LastEditTime: 2022-03-18 12:04:57
* @Description: 商品套餐相关处理
 * @FilePath: \dreame\modules\goods\models\GtcModel.php
*/

namespace app\modules\goods\models;

use app\components\Erp;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\models\by;
use app\components\AppCRedisKeys;

class GtcModel extends CommModel
{

    public static function tbName(): string
    {
        return '`db_dreame_goods`.`t_gtc`';
    }

    /**
     * @param $sn
     * @return string
     * 套餐sn唯一数据缓存KEY
     */
    private function __getListBySn($sn): string
    {
        return AppCRedisKeys::getTcSnGoodsKey($sn);
    }

    /**
     * @param $sn
     * @return int
     * 删除详情缓存
     */
    private function __delOneCache($sn)
    {
        $r_key  = $this->__getListBySn($sn);
        return by::redis()->del($r_key);
    }

    /**
     * @param $sku
     * @return array
     * @throws \yii\db\Exception
     * 根据sku检查是否为套餐
     */
    public function checkIsTc($sku)
    {
        list($s, $ret)  =  Erp::factory()->getTcList($sku);
        if (!$s) {
            return [false, $ret];
        }

        $tc_list        = $ret['data']['taocanListGet'][0] ?? [];
        if (empty($tc_list['guige_list'])) {
            return [false, '该套餐下无商品信息'];
        }
        if ($tc_list['status'] != 'enabled') {
            return [false, '该套餐状态不可用'];
        }

        $goods_list = array_column($tc_list['guige_list'], 'guige_goods_list');
        $time       = time();

        foreach ($goods_list as $list) {
            if (empty($list)) {
                continue;
            }
            foreach ($list as $goods) {
                $data[] = [
                    'tc_sn'  => $sku,
                    'tc_sku' => $goods['tc_sku'],
                    'sku'    => $goods['sku'],
                    'sl'     => $goods['sl'],
                    'time'   => $time
                ];
            }
        }

        $tb         = self::tbName();
        $db         = by::dbMaster();
        $trans      = $db->beginTransaction();

        try {
            $o_list = array_column($this->getListBySn($sku), 'sku');
            $n_list = array_column($data, 'sku');
            $d_list = array_diff($o_list, $n_list);
            $d_list && $db->createCommand()->delete($tb, ['tc_sn' => $sku, 'sku' => $d_list])->execute();

            $sql    = $db->createCommand()->batchInsert($tb, ['tc_sn', 'tc_sku', 'sku', 'sl', 'ctime'], $data)->getRawSql();
            $sql   .= " ON DUPLICATE KEY UPDATE `sl` = values(`sl`)";
            $db->createCommand($sql)->execute();

            $trans->commit();

            $this->__delOneCache($sku);

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gtc');

            return [false, '套餐写入失败'];
        }
    }

    /**
     * @param $sn
     * @return array
     * @throws \yii\db\Exception
     * 通过套餐sn获取其关联的商品sku数据
     */
    public function getListBySn($sn)
    {
        $redis      = by::redis('core');
        $r_key      = self::__getListBySn($sn);
        $aJson      = $redis->get($r_key);
        $aData      = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb         = self::tbName();
            $need_fid   = ['tc_sn', 'tc_sku', 'sku', 'sl'];
            $field      = implode("`,`", $need_fid);
            $sql        = "SELECT `{$field}` FROM {$tb} WHERE `tc_sn`=:sn ORDER BY `id`";
            $aData      = by::dbMaster()->createCommand($sql, [':sn' => $sn])->queryAll();

            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }
}
