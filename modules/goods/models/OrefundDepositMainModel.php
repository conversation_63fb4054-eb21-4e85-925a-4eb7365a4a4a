<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 退款表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\pay\MpPayModel;
use app\modules\main\services\refund\Order;
use yii\db\Exception;


class OrefundDepositMainModel extends CommModel {

    public $tb_fields   = [
        'id','refund_no','order_no','user_id','ctime','status'
    ];

    CONST STATUS = [
        'ALL'           => -1,
        'AUDIT'         => 1000,     //平台待审核
        'P_PASS'        => 1010,     //平台审核通过
        'P_REJECT'      => 1020,     //平台审核拒绝
        'CANCELED'      => 100,      //已取消
        'SUCCESS'       => 20000000, //退款成功
    ];


    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_orefund_d_main`";
    }

    /**
     * @param $refund_no
     * @return string
     * 订单详情唯一缓存KEY
     */
    private function __getOneInfoKey($refund_no): string
    {
        return AppCRedisKeys::getOneDepositRefundMain($refund_no);
    }


    /**
     * @param $refund_no
     * @return int
     * 缓存清理
     * @throws \RedisException
     */
    private function __delInfoCache($refund_no): int
    {
        $r_key = $this->__getOneInfoKey($refund_no);

        return  by::redis('core')->del($r_key);
    }

    /**
     * @param int $user_id
     * @param array $arr
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 申请退款
     */
    public function ApplyDepositRefund(int $user_id, array $arr)
    {
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        $ctime    = intval(START_TIME);
        $order_no = $arr['order_no'] ?? '';
        $m_type   = $arr['m_type'] ?? 0;
        $r_type   = $arr['r_type'] ?? 0;
        $describe = $arr['describe'] ?? "";
        $images   = $arr['images'] ?? "";


        $m_type     = CUtil::uint($m_type);
        $r_type     = CUtil::uint($r_type);

        //todo 判断所选数据是否正常
        $mOdeposit     = by::Odeposit();

        $oInfo      = $mOdeposit->getInfoByDepositOrderNo($user_id, $order_no);
        if (empty($oInfo)) {
            return [false, '订单信息异常.|'.$order_no];
        }

        //生成退款单号
        $refund_no  = 'D'.$mOdeposit->CreateOrderNo($ctime);
        $tb         = self::tbName();
        $db         = by::dbMaster();

        $next_st    = by::Odeposit()->SetOrderStatus($oInfo['status'], $mOdeposit::STATUS['REFUNDING'], true, true);

        $trans      = $db->beginTransaction();
        try {
            //插入定金退款主表数据
            $save       = [
                'refund_no'             => $refund_no,
                'order_no'              => $order_no,
                'user_id'               => $user_id,
                'm_type'                => $m_type,
                'ctime'                 => $ctime,
            ];
            $db->createCommand()->insert($tb, $save)->execute();

            //插入定金退款表数据
            $save = [
                'order_no'              => $order_no,
                'refund_no'             => $refund_no,
                'm_type'                => $m_type,
                'r_type'                => $r_type,
                'describe'              => $describe,
                'images'                => $images,
                'ctime'                 => $ctime,
                'ostatus'               => $oInfo['status'],
            ];
            by::OrefundDeposit()->SaveLog($user_id, $save);

            //更改订单状态
            if ($next_st != $oInfo['status']) {
                list($s, $m) = $mOdeposit->SyncInfo($user_id, $order_no, $next_st);
                if (!$s) {
                    throw new \Exception($m);
                }
            }

            $trans->commit();

            //todo 清缓存
            $this->__delInfoCache($refund_no);

            return [true, $refund_no];
        } catch (\Exception $e) {

            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.refund');

            return [false, '定金申请退款失败！'];
        }
    }


    /**
     * @param $user_id
     * @param $refund_no
     * @param $status
     * @param array $arr
     * @return array
     * @throws Exception
     * 状态结果同步
     */
    public function SyncInfo($user_id,$refund_no,$status,$arr=[])
    {

        if($user_id <= 0 || empty($refund_no) || !in_array($status,self::STATUS)) {
            return [false,"非法参数"];
        }

        $db       = by::dbMaster();
        $trans    = $db->beginTransaction();
        try{

            $tb_main = self::tbName();
            $ret     = $db->createCommand()->update(
                $tb_main,
                ['status'=>$status],
                ['refund_no' => $refund_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new \Exception("无数据更新(1)");
            }

            $arr['status'] = $status;

            $tb_info = by::OrefundDeposit()::tbName($user_id);

            $ret     = $db->createCommand()->update(
                $tb_info,
                $arr,
                ['refund_no' => $refund_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new \Exception("无数据更新(2)");
            }

            $trans->commit();

            //todo 清理退款表缓存
            by::OrefundDeposit()->DelCache($user_id, $refund_no);
            $this->__delInfoCache($refund_no);

            return [true,"OK"];

        } catch (\Exception $e) {
            $trans->rollBack();

            return [false,$e->getMessage()];
        }
    }

    /**
     * @param string $order_no
     * @param string $user_iden 用户身份（id或手机号）
     * @param int $status
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 管理后台展示
     */
    public function GetList($order_no='', $user_iden='', $status=-1,$order_time=[],$page=1, $page_size=50)
    {
        $page                = CUtil::uint($page,1);

        $tb                  = self::tbName();

        list($offset)        = CUtil::pagination($page,$page_size);
        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        $sql                 = "SELECT `user_id`,`order_no`,`refund_no` FROM {$tb} WHERE {$where} 
                                ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
        $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

        return $aData;
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param array $order_time
     * @return int
     * @throws Exception
     * 订单总数
     */
    public function GetListCount($order_no='', $user_iden='', $status=-1,$order_time=[])
    {
        $tb                  = self::tbName();

        list($where,$params) = $this->__getCondition($order_no, $user_iden, $status,$order_time);
        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $command             = by::dbMaster()->createCommand($sql,$params);
        $count               = $command->queryScalar();

        return intval($count);
    }

    /**
     * @param string $order_no
     * @param string $user_iden
     * @param int $status
     * @param array $order_time
     * @return array
     * @throws Exception
     * 规范化查询条件
     */
    private function __getCondition($order_no='', $user_iden='', $status=-1,$order_time=[]): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if(!empty($order_no)) {
            $where               .= " AND `order_no`=:order_no";
            $params[":order_no"]  = $order_no;
        }

        if(!empty($user_iden)) {
            if (strlen($user_iden) == 11) {
                $uids = by::Phone()->GetUidsByPhone($user_iden);
                if(!empty($uids)) {
                    $uids    = implode(',',$uids);
                    $where  .= " AND `user_id` IN ({$uids})";
                }else{
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where  .= " AND `user_id` = -1";
                }
            } else {
                $where  .= " AND `user_id` = {$user_iden}";
            }

        }

        if($status >= 0) {
            $where                 .= " AND `status` = :status";
            $params[":status"]      = $status;
        }

        if(!empty($order_time['st']) && !empty($order_time['ed'])) {
            $where               .= " AND `ctime` BETWEEN :order_st AND :order_ed";
            $params[":order_st"]  = $order_time['st'];
            $params[":order_ed"]  = $order_time['ed'];
        }
        return [$where,$params];
    }


    /**
     * @param $order_no
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 尾款退款
     */
    public function _judgeRefundDeposit($user_id,$order_no)
    {
        //1.判断是否有退款订单
        $refundNos = $this->GetList($order_no);
        if($refundNos){
            //执行退款
            foreach ($refundNos as $item){
                $arr =[
                    'refund_no' => $item['refund_no'],
                    'order_no'  => $item['order_no'],
                    'status'    => self::STATUS['P_PASS'],
                    'a_reason'  => '定金退款',
                ];
               list($s,$msg) = $this->Audit($arr);
               if(!$s){
                   return [false,$msg];
               }
            }
        }else{
            //创建退款订单
            list($s,$refund_no)=$this->ApplyDepositRefund($user_id,[
                'order_no' => $order_no,
                'm_type'   => 0,
                'r_type'   => 0,
                'describe' => '定金申请退款！',
                'images'   => '',
            ]);
            if(!$s){
                return [false,$refund_no];
            }
            //退款
            $arr =[
                'refund_no'  =>$refund_no,
                'order_no'  =>$order_no,
                'status'  =>self::STATUS['P_PASS'],
                'a_reason'  =>'定金退款',
            ];
            list($s,$msg) = $this->Audit($arr);
            if(!$s){
                return [false,$msg];
            }
        }
        return  [true,'OK'];
    }









    /**
     * @param $arr
     * @return array
     * @throws Exception
     * 退款单审核
     */
    public function Audit($arr): array
    {
        //定金订单退款
        $refund_no = $arr['refund_no'] ?? '';
        $order_no  = $arr['order_no']  ?? '';
        $status    = $arr['status']    ?? 0;
        $a_reason  = $arr['a_reason']  ?? '定金订单退款';

        if (empty($refund_no) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($refund_no, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if (mb_strlen($a_reason) > 500) {
            return [false, '拒绝原因最长500字'];
        }

        $status = CUtil::uint($status);
        if(!in_array($status,[self::STATUS['P_PASS'],self::STATUS['P_REJECT']])) {
            return [false, '参数错误(1)'];
        }

        $rm_info = $this->GetInfoByRefundNo($refund_no);
        if (empty($rm_info)) {
            return [false, '退款订单异常'];
        }

        $user_id = $rm_info['user_id'];
        $r_info  = by::OrefundDeposit()->GetInfoByRefundNo($user_id,$refund_no);
        if($r_info['status'] == by::Odeposit()::STATUS['RERUNDED']){
            return [true, '退款已完成！'];
        }

        if ($r_info['status'] != self::STATUS['AUDIT']) {
            return [false, '退款订单异常(2)'];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            list($s, $m) = $this->SyncInfo($user_id,$refund_no,$status, ['a_reason' => $a_reason]);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 更改订单状态
            list($s, $ret) = $this->__auditByStatus($user_id, $order_no, $refund_no, $status, $r_info['ostatus']);
            if (!$s) {
                throw new MyExceptionModel($ret);
            }

            if ($status == self::STATUS['P_PASS']) {
                //todo 获取最新支付方式，选择退款渠道
                $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
                if(empty($aLog)) {
                    throw new MyExceptionModel('无法获取支付流水，数据有误！');
                }
                $payType  = $aLog['pay_type'] ?? '';
                $trade_no = $aLog['tid'] ?? '';
                if($payType == by::Omain()::PAY_BY_WX || $payType == by::Omain()::PAY_BY_WX_APP){
                    //todo 微信执行退款
                    list($s, $m)  = by::wxPay()->refund($user_id, $order_no, $refund_no, false,by::WxPay()::SOURCE['DEPOSIT'], $payType);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }elseif ($payType == by::Omain()::PAY_BY_WX_H5){
                    //todo 微信H5执行退款
                    list($s, $m)  = by::wxH5Pay()->refund($user_id, $order_no, $refund_no, false,by::wxH5Pay()::SOURCE['DEPOSIT']);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }elseif($payType == by::Omain()::PAY_BY_ALIPAY) {//支付宝退款
                    // 获取退款金额（定金订单）
                    $refund_amount = $this->getRefundAmount($user_id, $order_no);
                    list($s, $m) = AliPayModel::getInstance()->refund($order_no, $refund_no, $refund_amount);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }elseif($payType == by::Omain()::PAY_BY_MP_WX || $payType == by::Omain()::PAY_BY_MP_ALIPAY|| $payType == by::Omain()::PAY_BY_MP_WEB_WX_H5|| $payType == by::Omain()::PAY_BY_MP_WEB_ALIPAY_H5) {//支付中台（微信/支付宝）
                    // 获取退款金额（定金订单）
                    $refund_amount = $this->getRefundAmount($user_id, $order_no);
                    $attach = [
                        'user_id'    => $user_id,
                        'uid'        => by::Phone()->getUidByUserId($user_id),
                        'order_no'   => $order_no,
                        'order_type' => Order::ORDER_TYPE['DEPOSIT'], // 预售订单
                        'pay_type'   => $payType,                     // 支付方式
                    ];
                    list($s, $m) = MpPayModel::getInstance()->refund($trade_no, $refund_no, $refund_amount, $attach);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }else{
                    throw new MyExceptionModel('支付渠道有误！无法退款');
                }
            }

            $trans->commit();

            return [true, 'ok'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit-deposit');

            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit-deposit');

            return [false, '定金退款失败'];
        }
    }


    /**
     * @throws Exception
     * @throws \RedisException
     */
    private function __auditByStatus($user_id, $order_no, $refund_no, $status, $ostatus)
    {
       $oDmain = by:: Odeposit();
       $o_info         = $oDmain->getInfoByDepositOrderNo($user_id, $order_no);

        switch ($status) {
            case self::STATUS['P_PASS'] :
                $next_st = $oDmain->SetOrderStatus($o_info['status'], $oDmain::STATUS['RERUNDED'], true, true);
                break;

            default :
                $next_st = $oDmain->SetOrderStatus($o_info['status'], $oDmain::RERUND_DTS, true, true, $ostatus);
        }

        //订单状态修改
        if ($next_st != $o_info['status']) {
            list($s, $m)    = $oDmain->SyncInfo($user_id, $order_no, $next_st);
            if (!$s) {
                return [false, $m];
            }
        }

        return [true, ['r_freight' => false, 'complete' => true]];
    }

    /**
     * @param $order_no
     * @param $refund_no
     * @param int $user_id
     * @return array|false
     * @throws Exception
     * 获取指定详情信息-只查退款单对应的user_id（慎用-微信退款查询专用）
     */
    public function GetInfoByRefundNo($refund_no)
    {
        if(empty($refund_no)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($refund_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb         = $this->tbName();

            $sql        = "SELECT `user_id` FROM {$tb} WHERE `refund_no`=:refund_no LIMIT 1";
            $aData      = by::dbMaster()->createCommand($sql,[':refund_no' => $refund_no])->queryOne();
            $aData      = $aData ?: [];
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * 获取退款金额（定金订单）
     * @param $user_id
     * @param string $order_no
     * @return mixed
     * @throws Exception
     * @throws \RedisException
     */
    private function getRefundAmount($user_id, string $order_no)
    {
        $order_info = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
        return $order_info['price'] ?? 0;  // 返回的为元，无需转换
    }


}
