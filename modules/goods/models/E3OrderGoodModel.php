<?php

namespace app\modules\goods\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * E3+订单表
 */
class E3OrderGoodModel extends CommModel
{
    
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_e3_order_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }
    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        // $trans  = $db->beginTransaction();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'E3OrderGoods.info');
            // $trans->rollBack();
            return [false, '操作失败'];
        }
    }

    public function getGoodsInfo($order_no,$sku){
        $item = self::find()
            ->where(['order_no' => $order_no,'sku'=>$sku])
            ->one();
        return $item ? $item->toArray() : [];
    }

    public function getGoodsListBySku($order_no,$sku_list){
        $list = self::find()->where(['order_no' => $order_no])->andWhere(['IN','sku',$sku_list])->asArray()->all();
        return $list ? $list : [];
    }
    public function getGoodsListByStatus($order_no,$status){
        $list = self::find()->where(['order_no' => $order_no,'status'=>$status])->asArray()->all();
        return $list ? $list : [];
    }

    public function batchUpdate($data){
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        foreach($data as $k=>$v){
            $db->createCommand()->update($tb, ['status'=>$v['status'],'utime'=>time(),'refund_score'=>$v['refund_score']], "`id`=:id", [":id" => $v['id']])->execute();
        }
        return true;
    }
}