<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use app\modules\main\models\CommModel;
use RedisException;
use Throwable;
use yii\db\DataReader;
use yii\db\Exception;

//活动奖品表
class DrawPrizeModel extends CommModel
{
    use ModelTrait;
    
    public static function getInstance(): DrawPrizeModel
    {
        return byNew::DrawPrize();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_draw_prize`";
    }

    public static $tb_fields = [
        'id', 'name', 'type', 'value', 'image', 'default_image', 'active_image', 'desc', 'remark', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'NO'  => 0,
        'YES' => 1
    ];

    // 奖品类型
    const PRIZE_TYPES = [
            'DEFAULT'         => 1, // 谢谢参与
            'POINT'           => 2, // 积分
            'COUPON'          => 3, // 优惠券
            'EXTERNAL_COUPON' => 4, // 兑换券（第三方券码）
            'PRICE'           => 5, // 现金红包
            'GOLD'            => 6, // 金币
            'FREE_AGAIN'      => 7, // 再来一次
            'CUSTOM_FORM'     => 8, // 自定义表单
    ];

    //通过奖品Id获取奖品详情

    /**
     * @return string
     * 奖品列表key
     */
    private function getPrizeByIdKey($prizeId): string
    {
        return AppCRedisKeys::getPrizeByIdKey($prizeId);
    }

    /**
     * @return void
     * 清除奖品列表redis
     * @throws RedisException
     */
    public function __delPrizeByIdKey($prizeId)
    {
        $redis    = by::redis();
        $redisKey = $this->getPrizeByIdKey($prizeId);
        $redis->del($redisKey);
    }

    /**
     * @return void
     * 清除奖品列表redis
     * @throws RedisException
     */
    public function __delPrizeListByIdKey($prizeIds)
    {
        $redis    = by::redis();

        $pipeline  = $redis->pipeline();
        foreach ($prizeIds as $prizeId) {
            $redis_key = $this->getPrizeByIdKey($prizeId);
            $redis->del($redis_key);
        }
        // 执行pipeline
        $pipeline->exec();
    }

    /**
     * @param $prizeIds
     * @return array
     * @throws Exception
     * @throws RedisException
     * 通过奖品Id获取奖品详情
     */
    public function getPrizeListByIds($prizeIds): array
    {
        $result = [];
        if (empty($prizeIds)) {
            return $result;
        }

        // 获取 Redis 实例和键
        $redis     = by::redis();
        $redisKeys = [];
        $pipeline  = $redis->pipeline();
        foreach ($prizeIds as $prizeId) {
            $redis_key           = $this->getPrizeByIdKey($prizeId);
            $redisKeys[$prizeId] = $redis_key;
            $redis->get($redis_key);
        }
        // 执行pipeline
        $responses = $pipeline->exec();

        //没有缓存的奖品数据
        $noDataPrize = [];

        foreach ($prizeIds as $index => $id) {
            $item = $responses[$index];
            if ($item === false) {
                $noDataPrize[] = $id;
            }
            $result[$id] = (array)json_decode($item, true);
        }

        //获取没有缓存的数据
        $values = $this->getPrizeByIds($noDataPrize);

        $pipeline = $redis->pipeline();

        //存入缓存
        foreach ($values as $id => $value) {
            $result[$id] = $value;
            $redis_key   = $redisKeys[$id];
            $pipeline->set($redis_key, json_encode($value), ['EX' => empty($value) ? 10 : rand(1800, 3600)]);
        }

        // 执行pipeline
        $pipeline->exec();

        return array_values($result);
    }


    /**
     * @param $prizeIds
     * @return array
     * @throws Exception
     * 批量查询奖品信息（查库）
     */
    public function getPrizeByIds($prizeIds): array
    {

        if (empty($prizeIds)) {
            return [];
        }
        // 从数据库获取奖品信息
        $tb          = self::tbName();
        $prizeIdsStr = implode("','", $prizeIds);

        $sql         = "SELECT `id`,`name`,`type`,`value`,`image`,`active_image`,`default_image`, `desc` FROM {$tb} WHERE `id` IN ('{$prizeIdsStr}') AND `is_del`=:is_del";
        $prizeValues = by::dbMaster()->createCommand($sql, ['is_del' => self::IS_DEL['NO']])->queryAll();
        $aData       = [];
        foreach ($prizeValues as $prizeValue) {
            $aData[$prizeValue['id']] = $prizeValue;
        }
        return $aData;
    }
    
    public function doCreate(array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('抽奖任务奖品添加失败1');
            }
            
            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 更新
     * @param int $id 抽奖奖品ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $data['utime'] = time();
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException(sprintf('%s抽奖奖品保存失败', $data['task_name'] ?? ''));
            }

            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 删除
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['dtime' => time(), 'is_del' => 1], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }

            if (empty($resp)) {
                throw new BusinessException('删除抽奖奖品失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 根据活动模块ID获取活动商品列表
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function getListByRelationId(int $moduleRelationId): array
    {
        $tb = self::tbName();
        return self::find()->from($tb)->where(['module_relation_id' => $moduleRelationId, 'dtime' => 0])->asArray()->all();
    }
    
    /**
     * 根据活动模块ID获取活动商品IDs
     * @param int $moduleRelationId
     * @return array
     */
    public function getIdsByRelationId(int $moduleRelationId): array
    {
        $res = $this->getListByRelationId($moduleRelationId);
        
        return array_column($res, 'id');
    }
}
