<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\exceptions\ProductMatrixException;
use app\models\by;
use app\modules\main\models\CommModel;

class ProductMatrixModel extends CommModel
{

    public static function tableName(): string
    {
        return '`db_dreame_goods`.`t_product_matrix`';
    }

    public static $tb_fields = [
            'category_id', 'main_product', 'sub_products', 'ctime', 'utime'
    ];

    private function getProductMatrixDetailKey($categoryId): string
    {
        return AppCRedisKeys::getProductMatrixDetailKey($categoryId);
    }

    public function __delProductMatrixDetailKey($categoryId)
    {
        $redis    = by::redis();
        $redisKey = $this->getProductMatrixDetailKey($categoryId);
        $redis->del($redisKey);
    }

    /**
     * @throws ProductMatrixException
     */
    public function saveProductMatrix($save): array
    {
        $categoryId    = $save['category_id'];
        $productMatrix = self::findOne($categoryId);
        if ($productMatrix) {
            // 编辑
            $save['utime'] = time();
        } else {
            // 新增
            $productMatrix = new self();
            $save['ctime'] = time();
        }

        // 设置属性并保存，使用修改器
        foreach ($save as $attribute => $value) {
            $productMatrix->$attribute = $value;
        }

        // 尝试保存数据，如果保存失败抛出异常
        if (!$productMatrix->save()) {
            // 抛出带有错误信息的异常
            throw new ProductMatrixException('保存失败: ' . implode(', ', $productMatrix->errors));
        }

        $this->__delProductMatrixDetailKey($categoryId);
        // 返回保存成功，并且将保存后的数据返回
        return [true, $productMatrix->attributes];
    }

    public function getDetail($categoryId): array
    {
        $redis    = by::redis();
        $redisKey = $this->getProductMatrixDetailKey($categoryId);

        // 尝试从 Redis 获取缓存数据
        $cachedData = $redis->get($redisKey);
        if ($cachedData) {
            // 如果缓存中存在数据，直接返回
            return json_decode($cachedData, true);
        }

        // 使用 select() 来限制查询的字段
        $productMatrix = self::find()
                ->select(['category_id', 'main_product', 'sub_products']) // 只选择特定字段
                ->where(['category_id' => $categoryId])                   // 根据 categoryId 查询
                ->one(); // 获取一条记录

        // 返回查询结果，如果没有找到返回空数组
        $productMatrix = $productMatrix ? $productMatrix->toArray() : [];

        // 将查询到的数据存入 Redis
        $redis->set($redisKey, json_encode($productMatrix, 320), ['EX' => empty($productMatrix) ? 10 : 7200]);

        return $productMatrix;
    }


    public function getDetailIdsByCategoryIds(array $categoryIds): array
    {
        // 执行查询，选择特定字段并按 category_id 筛选
        return self::find()
                ->select('category_id') // 只选择特定字段
                ->where(['IN', 'category_id', $categoryIds])                    // 使用正确的 IN 查询
                ->asArray()                                                     // 直接返回数组形式
                ->column();
    }

}
