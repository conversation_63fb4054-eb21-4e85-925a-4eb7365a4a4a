<?php

namespace app\modules\goods\models;

use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\ActiveQuery;

/**
 * 追觅小店 - model
 */
class DreameStoreModel extends CommModel
{

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`dreame_store`";
    }

    /**
     * 追觅小店 活动ID
     */
    public function getActivityId()
    {
        return CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
    }

    public function getPageList($params = []){
        $order = $params['order'] ?? 'ctime';
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        $query = self::find();

        $where = $params;
        $query = $this->handleSearch($query, $where);

        $list = $query->orderBy([$order => SORT_DESC])
            ->offset(($page - 1) * $pageSize)->limit($pageSize)
            ->asArray()->all();
        $total = $query->count();
        return ['list' => $list, 'total' => $total];
    }

    public function saveData(array $data): bool
    {
        // 获取用户记录
        $info = self::findOne(['user_id' => $data['user_id']]);

        if ($info === null) {
            // 如果用户不存在，创建新的记录
            $info              = new self();
            $info->user_id     = $data['user_id'];
            $info->ctime       = time(); // 设置创建时间
        }

        if (!$info ||  1 == $info['is_delete']) {
            $data += $this->initData();
        }

        // 更新或设置
        $info->setAttributes($data, false);

        $info->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$info->save(false)) {
            return false;
        }

        return true;
    }

    public function initData()
    {
        $data['status'] = 1;
        $data['max'] = 10;
        $data['reason'] = '';
        $data['is_delete'] = 0;
        $data['ctime'] = time();
        return $data;
    }

    public function doCreate($data)
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        $data['ctime'] = time();
        $data['utime'] = time();
        $resp = $db->createCommand()->insert($tb, $data)->execute();
        if (empty($resp)) {
            throw new BusinessException('创建失败');
        }
        $id = $db->getLastInsertID();
        return $id;
    }

    public function getInfoByUserId($user_id): array
    {
        return self::getInfo(['user_id' => $user_id]);
    }

    // 获取信息
    public function getInfo($where): array
    {
        $item = self::find()
            ->where($where)
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->one();

        return $item ? $item->toArray() : [];
    }

    public function getByInviterIdAndInviterFrom($inviterId, $inviterFrom): array
    {
        $item = self::find()
            ->where(['inviter_id' => $inviterId, 'inviter_from' => $inviterFrom, 'status' => 1, 'is_delete' => 0])
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->asArray()
            ->all();

        return $item ?? [];
    }

    public function getInfos($ids): array
    {
        $list = self::find()
            ->where(['id' => $ids])
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->asArray()
            ->all();

        $list = array_column($list, null, 'id');

        return $list ?: [];
    }

    public function updateInfo($where,array $params){
        $db = by::dbMaster();
        $tb = self::tbName();
        $params['utime'] = time();
        return $db->createCommand()->update($tb, $params, $where)->execute();
    }

    public function deleteInfo($where){
        $db = by::dbMaster();
        $tb = self::tbName();
        return $db->createCommand()->delete($tb, $where)->execute();
    }

    public function getInfoByField(string $uid,string $field = 'user_id'): array
    {
        $item = self::find()
            ->where([$field => $uid])
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->one();

        return $item ? $item->toArray() : [];
    }


    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        $params = array_map(function ($v) {return trim($v);}, $params);
        $params = array_filter($params, function ($v) {
            return (!empty($v) || $v === '0' || $v === 0 );
        });

        if ($params['store_name'] ?? '') {
            $query->andWhere(['like', 'store_name', $params['store_name']]);
            unset($params['store_name']);
        }

        $attrs = $this->getTbFields();
        $map = [];
        foreach ($attrs as  $field) {
            if (isset($params[$field])) {
                $map[$field] = $params[$field];
            }
        }

        $query->andWhere($map);
        return $query;
    }

    public function getTbFields(): array
    {
        return ['id', 'user_id', 'store_name','max', 'status','reason','is_delete','ctime','utime'];
    }

}