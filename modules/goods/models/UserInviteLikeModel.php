<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;
use yii\db\Exception;

/**
 * 用户邀请点赞模型
 *
 * @property int $id 主键
 * @property int $inviter_id 邀请人ID
 * @property int $invitee_id 被邀请人ID
 * @property int $invite_type 邀请类型 1=一元秒杀
 * @property int $relate_id 与邀请类型关联的外键ID，例如拼团ID、秒杀ID等
 * @property int $invited_at 邀请时间（秒时间戳）
 * @property string $remark 备注信息
 */
class UserInviteLikeModel extends CommModel
{
    // 邀请类型
    const INVITE_TYPE = [
            'ONE_YUAN_SECKILL' => 1,// 一元秒杀
            'FRIEND_BUY'       => 2,// 邀请好友半价购买
            'REG_MALL_INVITE'  => 3,// 商城注册邀请
            'CONSUME_MONEY'    => 5,// 消费金/赚钱花
    ];

    public $tb_fields = [
            'id',
            'inviter_id',
            'invitee_id',
            'invite_type',
            'relate_id',
            'invited_at',
            'device_id',
            'remark'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`user_invite_like`";
    }

    /**
     * 创建邀请点赞记录
     * @param int $inviterId 邀请人ID
     * @param int $inviteeId 被邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param string|null $deviceId 设备ID
     * @param string|null $remark 备注信息
     * @return array
     * @throws Exception
     */
    public function createInviteLike(int $inviterId, int $inviteeId, int $inviteType, int $relateId, string $deviceId = null, string $remark = null): array
    {
        $data = [
            'inviter_id'  => $inviterId,
            'invitee_id'  => $inviteeId,
            'invite_type' => $inviteType,
            'relate_id'   => $relateId,
            'invited_at'  => time(),
        ];

        // 添加设备ID（如果提供）
        if ($deviceId !== null) {
            $data['device_id'] = $deviceId;
        }

        // 添加备注信息（如果提供）
        if ($remark !== null) {
            $data['remark'] = $remark;
        }

        $fields       = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        $sql          = "INSERT INTO " . self::tbName() . " 
                ({$fields}) VALUES ({$placeholders})";

        try {
            $command = by::dbMaster()->createCommand($sql, $data);
            $result  = $command->execute();

            if ($result > 0) {
                return [
                        true,
                        '点赞成功'
                ];
            } else {
                return [
                        false,
                        '点赞记录创建失败'
                ];
            }
        } catch (\Exception $e) {
            // 处理唯一键冲突
            if (strpos($e->getMessage(), 'Duplicate entry') !== false) {
                return [
                        false,
                        '点赞记录已存在'
                ];
            }
            return [
                    false,
                    '点赞失败：' . $e->getMessage()
            ];
        }
    }
    
    /**
     * 获取邀请点赞统计
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param bool $todayOnly 是否只统计当天邀请，默认false保持兼容性
     * @return int
     * @throws Exception
     */
    public function getInviteLikeCount(int $inviterId, int $inviteType, int $relateId, bool $todayOnly = false): int
    {
        $sql = "SELECT COUNT(*) as count FROM " . self::tbName() . "
                WHERE inviter_id = :inviter_id
                AND invite_type = :invite_type
                AND relate_id = :relate_id";
        
        $params = [
            ':inviter_id'  => $inviterId,
            ':invite_type' => $inviteType,
            ':relate_id'   => $relateId,
        ];
        
        // 如果需要只统计当天邀请的用户，添加时间条件
        if ($todayOnly) {
            $startTime = strtotime(date('Y-m-d 00:00:00'));
            $endTime = strtotime(date('Y-m-d 23:59:59'));
            $sql .= " AND invited_at BETWEEN :start_time AND :end_time";
            $params[':start_time'] = $startTime;
            $params[':end_time'] = $endTime;
        }
        
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return (int) ($result['count'] ?? 0);
    }

    public function checkCanLike(int $ac_id, int $inviter_id, int $user_id): array
    {
        $redis = by::redis();
        
        $key = AppCRedisKeys::getInviteLikeCacheKey($ac_id, $inviter_id);

        // 检查是否点赞
        $isLike = $redis->sismember($key, $user_id);
        if (! empty($isLike)) {
            // 已点赞
            return [false, '您已点赞，请勿重复点赞'];
        } else {
            $sql = "SELECT count(*) as like_count FROM " . self::tbName() . "
                WHERE inviter_id = :inviter_id
                AND invitee_id = :invitee_id
                AND invite_type = :invite_type
                AND relate_id = :relate_id";

            $params = [
                ':inviter_id'  => $inviter_id,
                ':invitee_id'  => $user_id,
                ':invite_type' => 5,
                ':relate_id'   => 5,
            ];

            $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $like_count = (int) ($result['like_count'] ?? 0);

            if (! empty($like_count)) {
                $redis = by::redis();
                $key = AppCRedisKeys::getInviteLikeCacheKey($ac_id, $inviter_id);
                $redis->sAdd($key, $inviter_id);
                return [false, '您已点赞，请勿重复点赞'];
            }
        }

        // 未点赞
        return [true, 'ok'];
    }
} 