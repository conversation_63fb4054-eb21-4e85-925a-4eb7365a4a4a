<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\components\Erp;
use app\components\ErpNew;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\PlatformModel;


class Gtype0Model extends CommModel
{
    public $tb_fields = [
        'gid', 'mprice', 'price', 'cover_image', 'market_image', 'images', 'cover_image_3d', 'model_3d', 'atype','is_recommend',
        'is_presale', 'presale_time', 'deposit', 'expand_price', 'start_payment', 'end_payment', 'surplus_time',
        'scheduled_number', 'coefficient', 'is_coupons', 'limit_num', 't_status', 't_time', 'detail', 'video',
        'introduce', 'parameters', 'platform', 'is_internal_purchase','sell_point','is_free_shipping'
    ];

    const DECIMAL_RANGE = "99999999.99"; //价格上限
    const ImageLimit    = 10; //商品图片数量上限
    const Model3DLimit  = 4;  //商品3D模型图片数量上限
    const Model3DExt    = ['obj', 'glb', 'gltf']; //3D模型图扩展名

    const ATYPE = [
        'SPEC'  => 0, //统一规格
        'SPECS' => 1, //自定义
    ];

    // 平台
    const PLATFORM = [
        'WX'  => 1, // 微信小程序
        'APP' => 2, // APP/H5
        'PC'  => 3, // PC商城
    ];

    //是否预售 （0不参与预售，1参与预售）
    const IS_PRESALE = [
        'no'  => 0,
        'yes' => 1,
    ];
    //是否内购（0：否 1：是）
    const IS_INTERNAL_PURCHASE = [
        'no'  => 0,
        'yes' => 1,
    ];

    public static function tableName(): string
    {
        return  self::tbName();
    }

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_gtype_0`";
    }

    /**
     * @param $gid
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getOneGtypeKey($gid): string
    {
        return AppCRedisKeys::getOneGtypeByGid($gid);
    }

    /**
     * @param $gid
     * @return int
     * 缓存清理
     * @throws \yii\db\Exception
     */
    private function __delCache($gid): int
    {
        $r_key1 = $this->__getOneGtypeKey($gid);

        return  by::redis('core')->del($r_key1);
    }

    /**
     * @param $price
     * @param int $type
     * @return mixed
     * 货币单位转换
     */
    public function totalFee($price, $type = 0)
    {
        switch (true) {
            case $type == 0:
                $price = sprintf("%.2f", $price);
                $price = bcmul($price, 100);
                return CUtil::uint($price);
                break;

            default:
                $price = CUtil::uint($price);
                $price = bcdiv($price, 100, 2);
                return sprintf("%.2f", $price);
        }
    }

    /**
     * @param int $gid
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 商品主表增改
     */
    public function SaveLog(int $gid, array $aData)
    {
        $id                = $aData['id']           ?? 0;
        $market_image      = $aData['market_image'] ?? "";//营销图
        $cover_image       = $aData['cover_image']  ?? "";//简洁图
        $images            = $aData['images']       ?? "";
        $detail            = $aData['detail']       ?? "";
        $pc_cover_image    = $aData['pc_cover_image'] ?? ""; //PC简洁图
        $pc_images         = $aData['pc_images']    ?? "";   //PC商品图
        $pc_detail         = $aData['pc_detail']    ?? "";   //PC详情
        $cover_image_3d    = $aData['cover_image_3d'] ?? "";
        $model_3d          = $aData['model_3d']     ?? "";
        $ck_code           = $aData['ck_code']      ?? "";
        $sellPoint         = $aData['sell_point']   ?? '';//商品卖点
        $id                = CUtil::uint($id);
        $platform_ids      = explode(',', $aData['platform_ids'] ?? ''); // 平台
        $jd_baitiao_support = $aData['jd_baitiao_support'] ?? 0; //是否支持京东白条
        $is_free_shipping = $aData['is_free_shipping'] ?? 0; //是否包邮


        if (empty($cover_image)) {
            return [false, "请上传简洁版封面图"];
        }
        if (empty($market_image)) {
            return [false, "请上传营销版封面图"];
        }
        if (empty($images)) {
            return [false, "请上传商品图"];
        }
        if (substr_count($images, "|") > self::ImageLimit) {
            return [false, "商品图片数量最多" . self::ImageLimit . "张"];
        }
        if (strlen($sellPoint) > 500) {
            return [false, "卖点参数过长，请修改"];
        }
        //上传3D图
        list($status, $msg) = $this->__check3DImage($cover_image_3d, $model_3d);
        if (!$status) {
            return [false, $msg];
        }
        if (empty($platform_ids)) {
            return [false, "请选择平台"];
        }
        //平台选择PC，PC图片必填
        if (in_array(self::PLATFORM['PC'], $platform_ids)) { // 平台包含PC商城
            if (empty($pc_cover_image)) {
                return [false, "请上传PC封面图"];
            }
            if (empty($pc_images)) {
                return [false, "请上传PC主图"];
            }
            if (empty($pc_detail)) {
                return [false, "请上传PC详情"];
            }
            // 上传数量
            if (count(explode('|', $pc_cover_image)) != 1) {
                return [false, "PC封面图最多可上传1张"];
            }
            if (count(explode('|', $pc_images)) > 10) {
                return [false, "PC主图最多可上传10张"];
            }
        }
        //设置价格
        $pcombines = $aData['pcombines'] ?? ""; //设置价格组合 [{"sprice_type":1,"sprice":200.05}]
        $pcombines = (array)json_decode($pcombines, true);

        //参数过滤
        $mprice            = $aData['mprice']           ?? 0;
        $price             = $aData['price']            ?? 0;
        $atype             = $aData['atype']            ?? 0;
        $is_coupons        = $aData['is_coupons']       ?? 0;
        $is_recommend      = $aData['is_recommend']     ?? 0;
        $parameters        = $aData['parameters']       ?? '';
        $custom_tag        = $aData['custom_tag']       ?? '';
        $introduce         = $aData['introduce']        ?? '';
        $limit_num         = $aData['limit_num']        ?? 0;
        $t_status          = $aData['t_status']         ?? 0;
        $t_time            = $aData['t_time']           ?? 0;
        $attr_cnf          = $aData['attr_cnf']         ?? ""; //属性配置
        $specs             = $aData['specs']            ?? ""; //规格列表
        $tid               = $aData['tid']              ?? ""; //商品标签
        $video             = $aData['video']            ?? ""; //视频
        $is_internal_purchase = $aData['is_internal_purchase'] ?? 0; //是否参与内购
        $is_presale           = $aData['is_presale']           ?? 0; //是否参与预售
        $presale_time         = $aData['presale_time']         ?? 0; //预售截止时间
        $deposit              = $aData['deposit']              ?? 0; //定金价格
        $expand_price         = $aData['expand_price']         ?? 0; //膨胀价格
        $start_payment        = $aData['start_payment']        ?? 0; //尾款付款开始时间
        $end_payment          = $aData['end_payment']          ?? 0; //尾款付款结束时间
        $surplus_time         = $aData['surplus_time']         ?? 0; //距尾款时间推送
        $scheduled_number     = $aData['scheduled_number']     ?? 0; //预购商品数（虚拟）
        $coefficient          = $aData['coefficient']          ?? 0; //预定增长系数

        $mprice            = sprintf("%.2f", $mprice);
        $price             = sprintf("%.2f", $price);
        $atype             = CUtil::uint($atype);
        $is_coupons        = CUtil::uint($is_coupons);
        $is_recommend      = CUtil::uint($is_recommend);
        $limit_num         = CUtil::uint($limit_num);
        $t_status          = CUtil::uint($t_status);
        $t_time            = CUtil::uint($t_time);
        $is_presale        = CUtil::uint($is_presale);
        $deposit           = sprintf("%.2f", $deposit);
        $expand_price      = sprintf("%.2f", $expand_price);
        $presale_time      = CUtil::uint($presale_time);
        $start_payment     = CUtil::uint($start_payment);
        $end_payment       = CUtil::uint($end_payment);
        $surplus_time      = sprintf('%.2f',$surplus_time);
        $scheduled_number  = CUtil::uint($scheduled_number);
        $coefficient       = CUtil::uint($coefficient);

        $is_internal_purchase = CUtil::uint($is_internal_purchase);

        //替换掉所有js标签
        $detail            = preg_replace("/<script[\s\S]*?<\/script>/i", "", $detail); //防注入
        $pc_detail         = preg_replace("/<script[\s\S]*?<\/script>/i", "", $pc_detail); //防注入

        $parameters        = preg_replace("/<script[\s\S]*?<\/script>/i", "", $parameters);
        $introduce         = preg_replace("/<script[\s\S]*?<\/script>/i", "", $introduce);


        //todo 新旧ERP
        $lockOldErp = CUtil::omsLock(0,time());

        if (!in_array($atype, self::ATYPE)) {
            return [false, "非法商品规格类型"];
        }

        if (bccomp($price, self::DECIMAL_RANGE, 2) > 0 || bccomp($price, 0, 2) <= 0) {
            return [false, "非法价格"];
        }

        if (bccomp($mprice, self::DECIMAL_RANGE, 2) > 0 || bccomp($mprice, 0, 2) <= 0) {
            return [false, "非法市场价"];
        }

        if (empty($tid)) {
            return [false, "请选择商品标签"];
        }


        if(mb_strlen($introduce)>250){
            return [false, "商品简介不能超过250个字！"];
        }

        if ($is_internal_purchase > 0 && empty($pcombines)) {
            return [false, "内购价格必填"];
        }


        if($is_internal_purchase && $is_presale == by::Gmain()::PTYPE['PRESALE']){
            return [false, "内购商品不允许参与预售！"];
        }

        if ($is_presale == by::Gmain()::PTYPE['PRESALE']) {

            // if ($atype == self::ATYPE['SPECS']) {
            //     return [false, "多规格商品不参与预售"];
            // }

            if (empty($deposit) || empty($expand_price)) {
                return [false, "预售的定金价格及膨胀价格必填"];
            }

            $price_half = bcdiv($price,2,2);
            if ($deposit > $price_half || $expand_price > $price_half){
                return [false, "预售的定金价格或膨胀价格不能超于日销价的50%"];
            }

            if ($presale_time < time()) {
                return [false, '请输入正确的预售的截止时间'];
            }

            if ($start_payment < $presale_time) {
                return [false, '请输入正确的尾款付款开始时间'];
            }

            if ($end_payment < $start_payment) {
                return [false, '请输入正确的尾款付款结束时间'];
            }

            if (empty($surplus_time)) {
                return [false, "距尾款推送时间必填"];
            }

            if (empty($coefficient)) {
                return [false, "预定增长系数必填"];
            }
        }

        $attrData = $specsData = [];
        if ($atype == self::ATYPE['SPECS']) {
            //规格名-规格值 校验 [{"at_name":"颜色","at_val":["红色","白色"]}]
            list($status, $attrData) = by::Gak()->checkAttr($attr_cnf);
            if (!$status) {
                return [false, $attrData];
            }

            //[{"at_val":"颜色:红色,尺码:s","sku":"12211","price":"0.01","image":"http://abc.jpg"}]
            list($status, $specsData) = by::Gspecs()->checkSpecs($specs, $ck_code);
            if (!$status) {
                return [false, $specsData];
            }
        } else {
            $sku            = $aData['sku'] ?? "";
            if (empty($sku)) {
                return [false, "商品编码不能为空(2)"];
            }
            if(!$lockOldErp){
                list($s, $stock) = Erp::factory()->getStockNew($sku,$ck_code,true);
                if (!$s) {
                    $stock  = 0;
                }
                $aData['stock'] = $stock;
            }
        }

        $save = [
            'cover_image'          => $cover_image,
            'market_image'         => $market_image,
            'images'               => $images,
            'cover_image_3d'       => $cover_image_3d,
            'model_3d'             => $model_3d,
            'mprice'               => $this->totalFee($mprice),
            'price'                => $this->totalFee($price),
            'atype'                => $atype,
            'is_coupons'           => $is_coupons,
            'is_recommend'         => $is_recommend,
            'platform'             => 99,
            'custom_tag'           => $custom_tag,
            'parameters'           => $parameters,
            'introduce'            => $introduce,
            'is_internal_purchase' => $is_internal_purchase,
            'limit_num'            => $limit_num,
            't_status'             => $t_status,
            't_time'               => $t_time,
            'detail'               => $detail,
            'video'                => $video,
            'is_presale'           => $is_presale,
            'presale_time'         => $presale_time,
            'deposit'              => $this->totalFee($deposit),
            'expand_price'         => $this->totalFee($expand_price),
            'start_payment'        => $start_payment,
            'end_payment'          => $end_payment,
            'surplus_time'         => $surplus_time,
            'scheduled_number'     => $scheduled_number,
            'coefficient'          => $coefficient,
            'sell_point'           => $sellPoint,
            'jd_baitiao_support'   => $jd_baitiao_support,
            'is_free_shipping'    => $is_free_shipping
        ];

        $tb    = $this->tbName();
        $db    = by::dbMaster();
        $trans = $db->beginTransaction();

        try {

            if ($id) {
                $aInfo = $this->GetOneBaseByGid($gid);
                if (empty($aInfo)) {
                    throw new MyExceptionModel('数据不存在');
                }

                //自定义价格生效中不改价格
                $isIni = $aInfo['is_ini'] ?? 0;
                if($isIni) unset($save['price']);

                if($isIni && $is_internal_purchase){
                    throw new MyExceptionModel('自定义商品不允许参与内购');
                }

                $db->createCommand()->update($tb, $save, "`gid`=:gid", [":gid" => $gid])->execute();

                if ($atype == self::ATYPE['SPECS']) {
                    //编辑属性
                    list($status, $msg) = $this->__updateAttr($gid, $attrData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }

                    //编辑属性sku
                    list($status, $msg) = $this->__updateSpecs($gid, $specsData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }
                }

                $this->__delCache($gid); //todo 清空商品详情缓存

            } else {
                $save['gid'] = $gid;
                $db->createCommand()->insert($tb, $save)->execute();

                if ($atype == self::ATYPE['SPECS']) {
                    //新增属性
                    list($status, $msg) = $this->__insertAttr($gid, $attrData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }

                    //新增属性sku
                    list($status, $msg) = $this->__insertSpecs($gid, $specsData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }
                }
            }

            //设置价格保存
            if($pcombines && isset($aData['sku'])){
                list($s,$m) = $this->__updateSprice($aData['sku'],$pcombines);
                if(!$s){
                    throw new MyExceptionModel($m);
                }
            }

            //商品标签
            list($s, $m) = by::Gtag()->SaveLog($gid, $aData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //预售库存
            list($s, $m) = by::Gprestock()->SaveLog($gid, $aData, $specsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //商品库存
            list($s, $m) = by::GoodsStockModel()->SaveLog($gid, $aData, $specsData, by::GoodsStockModel()::SOURCE['MAIN']);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            // 商品平台
            $platformData = $this->getPlatformData($gid, $platform_ids, $market_image, $cover_image, $images, $detail, $pc_cover_image, $pc_images, $pc_detail);
            list($s, $m) = byNew::GoodsPlatformModel()->batchUpdateOrCreate($platformData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            $this->__delCache($gid); //todo 清空商品详情缓存

            $trans->commit();

            //todo 推送E+创建商品
            $lockOldErp && ErpNew::factory()->pushGoods($gid);


            return [true, $save];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.gtype0');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.gtype0');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $gid
     * @param bool $format_price 转化格式为元
     * @return array|false
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 获取指定详情信息
     */
    public function GetOneBaseByGid($gid, $format_price = true, $spriceType = 0, $aMainsku = 0)
    {
        $gid = CUtil::uint($gid);
        if ($gid <= 0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneGtypeKey($gid);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson, true);

        if ($aJson === false) {
            // 关联查询平台数据
            $aData = self::find()
                ->with('platforms')
                ->where(['gid' => $gid])
                ->asArray()
                ->one();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        // 处理商品数据
        $aData = $this->handleGoods($aData, $format_price);


        // 待优化，放在handleGoods()中
        $aData['pcombines'] = by::Gsprice()->GetListBySku($aMainsku, $format_price);
        $isInternal         = 0;
        if (($aData['is_internal_purchase'] ?? 0) && $aData['pcombines']) { // 内购商品，取内购价格
            $pcombines      = array_column($aData['pcombines'], 'sprice', 'sprice_type');
            $aData['price'] = floatval($pcombines[1] ?? 0) > 0 ? $pcombines[1] : $aData['price'];
            $isInternal     = 1;
        }
        //获取自定义价格
        $aData = by::Gini()->getGiniPriceBySku($aData,$aMainsku,$format_price,$isInternal);

        $aData['is_internal'] = $isInternal;

        //判断预售状态
        $time              = intval(START_TIME);
        $aData['d_status'] = 0;
        $presale_time      = $aData['presale_time'] ?? 0;
        $start_payment     = $aData['start_payment'] ?? 0;
        $end_payment       = $aData['end_payment'] ?? 0;
        if(!empty($presale_time) && !empty($start_payment) && !empty($end_payment)){
            if($time < $presale_time){
                $aData['d_status'] = 1;//预售中
            }elseif($time >= $start_payment && $time < $end_payment){
                $aData['d_status'] = 2;//支付尾款中
            }else{
                $aData['d_status'] = 3;//预售结束
            }
        }

        $scheduled_number_now          = by::Gtype0()->getScheduledNumber($aData['gid'], $aData['scheduled_number'] ?? 0, $aData['coefficient'] ?? 0);
        $aData['scheduled_number_now'] = $scheduled_number_now;

        return $aData;
    }

    /**
     * @param $gid
     * @param int $scheduled_number
     * @param int $coefficient
     * @return int|mixed|string
     * @throws \RedisException
     * @throws \yii\db\Exception
     * 计算预定人数
     */
    public function getScheduledNumber($gid, $scheduled_number = 0, $coefficient = 0)
    {
        $sale = by::Gprestock()->OptSales($gid);

        return bcadd(bcmul($coefficient,CUtil::uint($sale)),$scheduled_number);
    }

    /**
     * @param $gid
     * @return array|false
     * @throws \yii\db\Exception
     * 获取指定详情信息
     */
    public function GetOneByGid($gid,$spriceType=0,$aMainsku='')
    {
        $aData = $this->GetOneBaseByGid($gid,true,$spriceType,$aMainsku);
        if (empty($aData)) {
            return [];
        }

        $aData['attr_cnf']  = [];
        $aData['specs']     = [];
        switch ($aData['atype']) {
            case self::ATYPE['SPEC']:
                //库存
                $sku                = $aMainsku;
                $hasStock           = empty($sku) ? 0 : by::GoodsStockModel()->OptStock($sku);
                $aData['stock']     = $hasStock;
                $aData['pre_stock'] = by::Gprestock()->OptStock($gid, 0);
                break;
            case self::ATYPE['SPECS']:
                //商品属性
                $aData['attr_cnf']  = by::Gak()->GetListByGid($gid);
                //商品属性值
                foreach ($aData['attr_cnf'] as $k => $v) {
                    $aData['attr_cnf'][$k]['val'] = by::Gav()->GetListByAkId($v['id']);
                }
                break;
        }

        return $aData;
    }

    /**
     * 根据商品ID获取商品信息
     * @param array $gids
     * @param array $columns
     * @return array
     */
    public function getListByGids(array $gids, array $columns = ['*']): array
    {
        return self::find()
            ->select($columns)
            ->where(['gid' => $gids])
            ->asArray()
            ->all();
    }

    /**
     * @param $gid
     * @param $attr_cnf
     * @return array
     * @throws \yii\db\Exception
     * 新增商品属性、属性值
     */
    private function __insertAttr($gid, $attr_cnf)
    {

        if (empty($attr_cnf) && !is_array($attr_cnf)) {
            return [false, "属性参数异常"];
        }

        //规格处理
        foreach ($attr_cnf as $val) {

            $at_name    = $val['at_name']; //规格名 - 颜色
            $at_val     = $val['at_val'];  //规格值列表 - ["红色","白色"]
            $at_params  = [
                'gid'       => $gid,
                'at_name'   => $at_name,
            ];
            list($status, $ak_id) = by::Gak()->SaveLog($at_params);
            if (!$status) {
                return [false, "新增Attr失败!"];
            }
            $ak_id      = intval($ak_id); //新增成功的规格id

            //规格值
            foreach ($at_val as $v) {
                $atv_params = [
                    'ak_id'    => $ak_id,
                    'at_val'   => $v['av_name'] ?? '',
                    'at_image' => $v['av_image'] ?? '',
                ];
                list($status, $m) = by::Gav()->SaveLog($atv_params);
                if (!$status) {
                    return [false, "新增Attrvalue失败!"];
                }
            }
        }

        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param $attr_cnf
     * @return array
     * @throws \yii\db\Exception
     * 更新商品属性、属性值
     */
    private function __updateAttr($gid, $attr_cnf)
    {
        if (empty($attr_cnf) || !is_array($attr_cnf)) {
            return [false, "属性参数异常"];
        }
        // [{"at_name":"颜色","at_val":[{"av_name":"深灰色","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"},{"av_name":"白色","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"}]},{"at_name":"大小","at_val":[{"av_name":"1英寸","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"},{"av_name":"2英寸","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"}]},{"at_name":"质量","at_val":[{"av_name":"好","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"},{"av_name":"坏","av_image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg"}]}]
        //[{"id":"96","gid":"239","at_name":"1111","at_val":[{"id":"199","ak_id":"96","at_val":"11111","image":"https://wpm-cdn.dreame.tech/images/2023010/249795-1696831560313.jpg","val":"11111"}]},{"id":"97","gid":"239","at_name":"2222","at_val":[{"id":"200","ak_id":"97","at_val":"222","image":"https://wpm-cdn.dreame.tech/images/2023010/441222-1696831591150.png","val":"222"}]},{"id":"98","gid":"239","at_name":"333","at_val":[{"id":"201","ak_id":"98","at_val":"3333","image":"https://wpm-cdn.dreame.tech/images/2023010/602823-1696831579951.jpg","val":"3333"}]},{"id":"99","gid":"239","at_name":"444","at_val":[{"id":"202","ak_id":"99","at_val":"4444","image":"https://wpm-cdn.dreame.tech/images/2023010/285326-1696831604470.jpg","val":"4444"}]}]
        //编辑 - 规格
        //查修改之前的规格 和 新的规格
        $o_attr  = by::Gak()->GetListByGid($gid);
        $o_attr  = array_column($o_attr, 'at_name');
        $n_attr  = array_column($attr_cnf, 'at_name');

        //新增的规格
        $a_attr  = array_diff($n_attr, $o_attr);
        //删除的规格
        $d_attr  = array_diff($o_attr, $n_attr);
        //修改的规格
        $u_attr  = array_intersect($n_attr, $o_attr);

        //新增 规格
        foreach ($attr_cnf as $val) {
            $at_name    = $val['at_name']; //规格名 - 颜色
            $at_val     = $val['at_val'];  //规格值列表 - ["红色","白色"]
            $at_params  = [
                'gid'       => $gid,
                'at_name'   => $at_name,
            ];

            if (in_array($at_name, $a_attr)) {
                //新增
                list($status, $ak_id) = by::Gak()->SaveLog($at_params);
                if (!$status) {
                    return [false, "新增Attr失败!"];
                }
                $ak_id      = intval($ak_id); //新增成功的规格id

                //规格值
                foreach ($at_val as $v) {
                    $atv_params = [
                        'ak_id'    => $ak_id,
                        'at_val'   => $v['av_name'] ?? '',
                        'at_image' => $v['av_image'] ?? '',
                    ];
                    list($status) = by::Gav()->SaveLog($atv_params);
                    if (!$status) {
                        return [false, "新增Attrvalue失败(1)"];
                    }
                }
            } else if (in_array($at_name, $u_attr)) {
                //修改
                $at_name    = $val['at_name']; //规格名 - 颜色
                $aLog       = by::GaK()->GetOneByName($gid, $at_name);
                $ak_id      = $aLog['id'] ?? 0;
                $avList     = by::Gav()->GetListByAkId($ak_id) ?? [];

                //图片+规格值名称
                $at_val = array_column($at_val,'av_image','av_name');

                $o_av       = array_column($avList, 'at_image','at_val');
                //新增的规格值
                $a_av = array_diff_assoc($at_val,$o_av);//修改或添加 比较key和value是否相同  key相同value不同为修改 key不同为添加
                //删除的规格值
                $d_av = array_diff_key($o_av,$at_val);//删除 比较key是否相同

                // //新增的规格值
                // $a_av       = array_diff($at_val, $o_av);
                // //删除的规格值
                // $d_av       = array_diff($o_av, $at_val);

                //添加规格值
                foreach ($a_av as $k=>$v) {
                    $atv_params = [
                        'ak_id'    => $ak_id,
                        'at_val'   => $k,
                        'at_image' => $v,
                    ];
                    list($status) = by::Gav()->SaveLog($atv_params);
                    if (!$status) {
                        return [false, "新增Attrvalue失败(2)"];
                    }
                }
                //删除规格值
                foreach ($d_av as $k => $v) {
                    list($status) = by::Gav()->UpdateLog($ak_id, $k);
                    if (!$status) {
                        return [false, '删除attrValue失败!'];
                    }
                }
            }
        }

        //删除
        foreach ($d_attr as $at_name) {
            list($status, $ak_id) = by::Gak()->UpdateLog($gid, $at_name);
            if (!$status) {
                return [false, '删除attr失败!'];
            }
            list($status) = by::Gav()->UpdateLog($ak_id);
            if (!$status) {
                return [false, '删除attrvalue失败!'];
            }
        }
        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param $specsData
     * @return array
     * @throws \yii\db\Exception
     * 新增属性sku
     */
    private function __insertSpecs($gid, $specsData)
    {
        if (empty($specsData) && !is_array($specsData)) {
            return [false, "属性sku参数异常"];
        }

        $attrList = by::Gak()->GetListByGid($gid, false);
        foreach ($attrList as $k => $v) {
            $avList              = by::Gav()->GetListByAkId($v['id'], false);
            $attrList[$k]['val'] = array_column($avList, null, 'at_val');
        }
        $attrList = array_column($attrList, null, 'at_name');

        //[{"at_val":["颜色:红色","尺码:s"],"sku":"12211","price":"0.01","image":"http://abc.jpg"}]
        foreach ($specsData as $sval) {
            foreach ($sval['at_val'] as $k1 => $v1) {
                $specs_n    = explode(':', $v1);
                //                $specs_v[0] = $attrList[$specs_n[0]]['id'];
                //                $specs_v[1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'];

                //                $sval['at_val'][$k1] = implode(':',$specs_v);
                $sval['at_val'][$k1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'];
            }

            $save = [
                'gid'       => $gid,
                'av_ids'    => json_encode($sval['at_val']),
                'sku'       => $sval['sku'],
                'price'     => $sval['price'],
                'image'     => $sval['image'],
            ];

            list($status, $msg) = by::Gspecs()->SaveLog($save);
            if (!$status) {
                return [false, $msg];
            }

            if (isset($sval['pcombines']) && $sval['pcombines'] && $sval['sku']) {
                list($s, $m) = $this->__updateSprice($sval['sku'], $sval['pcombines']);
                if (!$s) {
                    return [false, $m];
                }
            }

        }

        return [true, 'ok'];
    }


    /**
     * @param $sku
     * @param $pcombines
     * @return array
     * @throws \yii\db\Exception
     * 编辑设置价格
     */
    private function __updateSprice($sku,$pcombines)
    {
        if (empty($pcombines) && !is_array($pcombines)) {
            return [false, "参数异常"];
        }

        $o_sprice = by::Gsprice()->GetListBySku($sku, true, false);
        $o_sprice = array_column($o_sprice, 'sprice_type');
        $n_sprice = array_column($pcombines, 'sprice_type');

        //新增的设置价格类型
        $a_sprice = array_diff($n_sprice, $o_sprice);
        //删除的设置价格类型
        $d_sprice = array_diff($o_sprice, $n_sprice);
        //修改的设置价格类型
        $u_sprice = array_intersect($n_sprice, $o_sprice);

        foreach ($pcombines as $pval) {
            if (in_array($pval['sprice_type'], $a_sprice)) {
                $save = [
                    'sku'       => $sku,
                    'sprice'     => $pval['sprice'],
                    'sprice_type'     => $pval['sprice_type'],
                    'ctime' => intval(START_TIME),
                    'utime' => intval(START_TIME),
                ];
                list($status, $msg) = by::Gsprice()->SaveLog($save);
                if (!$status) {
                    return [false, $msg];
                }
            } else if (in_array($pval['sprice_type'], $u_sprice)) {
                $save = [
                    'sprice'     => $pval['sprice'],
                    'utime' => intval(START_TIME),
                ];
                list($status) = by::Gsprice()->UpdateLog($sku, $pval['sprice_type'], $save);
                if (!$status) {
                    return [false, "修改设置价格失败!"];
                }
            }
        }

        //删除sku
        foreach ($d_sprice as $sp_type) {
            list($status) = by::Gsprice()->UpdateLog($sku, $sp_type, ['is_del' => 1]);
            if (!$status) {
                return [false, "删除设置价格失败!"];
            }
        }

        return [true, "ok"];
    }




    /**
     * @param $gid
     * @param $specsData
     * @return array
     * @throws \yii\db\Exception
     * 编辑商品属性sku
     */
    private function __updateSpecs($gid, $specsData)
    {
        if (empty($specsData) && !is_array($specsData)) {
            return [false, "参数异常"];
        }

        $o_specs = by::Gspecs()->GetListByGid($gid);

        //判断自定义价格
        $ini_arr = array_column($o_specs,'is_ini','sku');

        $o_specs = array_column($o_specs, 'sku');
        $n_specs = array_column($specsData, 'sku');

        //新增的sku
        $a_specs = array_diff($n_specs, $o_specs);
        //删除的sku
        $d_specs = array_diff($o_specs, $n_specs);
        //修改的sku
        $u_specs = array_intersect($n_specs, $o_specs);

        //所有商品组合

        $attrList = by::Gak()->GetListByGid($gid);
        foreach ($attrList as $k => $v) {
            $avList              = by::Gav()->GetListByAkId($v['id']);
            $attrList[$k]['val'] = array_column($avList, null, 'at_val');
        }
        $attrList = array_column($attrList, null, 'at_name');

        foreach ($specsData as $sval) {
            foreach ($sval['at_val'] as $k1 => $v1) {
                $specs_n    = explode(':', $v1);
                //                $specs_v[0] = $attrList[$specs_n[0]]['id'];
                //                $specs_v[1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'];

                //                $sval['at_val'][$k1] = implode(':',$specs_v);
                $sval['at_val'][$k1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'] ?? 0;
            }

            if (in_array($sval['sku'], $a_specs)) {
                $save = [
                    'gid'       => $gid,
                    'av_ids'    => json_encode($sval['at_val']),
                    'sku'       => $sval['sku'],
                    'price'     => $sval['price'],
                    'image'     => $sval['image'],
                ];
                list($status, $msg) = by::Gspecs()->SaveLog($save);
                if (!$status) {
                    return [false, $msg];
                }
            } else if (in_array($sval['sku'], $u_specs)) {
                $save = [
                    'av_ids'     => json_encode($sval['at_val']),
                    'price'      => $sval['price'],
                    'image'      => $sval['image'],
                ];
                //判断自定义价格是否生效中
                $is_ini = $ini_arr[$sval['sku']] ?? 0;
                if($is_ini) unset($save['price']);

                list($status) = by::Gspecs()->UpdateLog($gid, $sval['sku'], $save);
                if (!$status) {
                    return [false, "修改sku失败!"];
                }
            }

            if (isset($sval['pcombines']) && $sval['pcombines'] && $sval['sku']) {
                list($s, $m) = $this->__updateSprice($sval['sku'], $sval['pcombines']);
                if (!$s) {
                    return [false, $m];
                }
            }

        }

        //删除sku
        foreach ($d_specs as $sku) {
            list($status) = by::Gspecs()->UpdateLog($gid, $sku, ['is_del' => 1]);
            if (!$status) {
                return [false, "删除sku失败!"];
            }
        }

        return [true, "ok"];
    }

    /**
     * @return bool
     * @throws \yii\db\Exception
     * 定时上下架
     */
    public function UpDown()
    {
        $db             = by::dbMaster();
        $tb             = self::tbName();
        $now_time       = intval(START_TIME);
        $gid            = 0;

        while (true) {

            $sql    = "SELECT `gid` FROM {$tb} WHERE `gid` > :gid AND `t_status`=1 AND `t_time` <= :now_time 
                       ORDER BY `gid` ASC LIMIT 100";

            $list   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':now_time' => $now_time])->queryAll();

            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $gid    = $end['gid'];

            foreach ($list as $v) {
                $g_info = by::Gmain()->GetOneByGid($v['gid']);
                if ($g_info['status'] == 0) {
                    $u_status = 1;
                } else {
                    $u_status = 0;
                }

                $trans  = $db->beginTransaction();

                try {
                    //修改上下架状态
                    list($s, $m) = by::Gmain()->UpdateData($v['gid'], ['status' => $u_status]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    //修改原数据完成上下架操作
                    list($s, $m) = $this->UpdateData($v['gid'], ['t_status' => 0]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    $trans->commit();
                } catch (\Exception $e) {

                    $trans->rollBack();

                    CUtil::debug($e->getMessage(), 'err.updown');
                }
            }

            usleep(1000);
        }

        return true;
    }

    /**
     * @param $gid
     * @param array $update
     * @return array
     * @throws \yii\db\Exception
     * 修改数据
     */
    public function UpdateData($gid, array $update)
    {
        $gid     = CUtil::uint($gid);
        //允许修改的字段
        if (empty($gid) || empty($update)) {
            return [false, '参数缺失'];
        }
        $allowed = ['t_status','is_presale'];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb      = self::tbName();

        by::dbMaster()->createCommand()->update(
            $tb,
            $update,
            ['gid' => $gid]
        )->execute();

        //强制清理缓存
        $this->__delCache($gid);

        return [true, 'ok'];
    }

    /**
     * @return bool
     * @throws \yii\db\Exception
     * 定时检测预售商品是否失效
     */
    public function presaleInvalid(): bool
    {
        $db             = by::dbMaster();
        $now_time       = intval(START_TIME);
        $tb             = self::tbName($now_time);

        while (true) {

            $sql    = "SELECT `gid` FROM {$tb} WHERE`is_presale`=:is_presale AND `presale_time`<:now_time 
                       ORDER BY `gid` ASC LIMIT 100";

            $list   = by::dbMaster()->createCommand($sql, [':is_presale'=>by::Gmain()::PTYPE['PRESALE'],':now_time' => $now_time])->queryAll();

            if (empty($list)) {
                break;
            }

            foreach ($list as $v) {
                $trans  = $db->beginTransaction();
                try {
                    //修改原数据完成恢复操作
                    list($s, $m) = $this->UpdateData($v['gid'], ['is_presale' => by::Gmain()::PTYPE['NO_PRESALE']]);
                    if (!$s) {
                        throw new \Exception($m);
                    }

                    $trans->commit();
                } catch (\Exception $e) {

                    $trans->rollBack();

                    CUtil::debug($e->getMessage(), 'err.presaleinvalid');
                }
            }

            usleep(1000);
        }

        return true;
    }

    /**
     * 校验 3d 模型图
     * @param $cover_image_3d
     * @param $model_3d
     * @return array
     */
    private function __check3DImage($cover_image_3d, $model_3d): array
    {
        if (empty($cover_image_3d) && !empty($model_3d)) {
            return [false, "请上传3D封面图"];
        }
        if (!empty($cover_image_3d) && empty($model_3d)) {
            return [false, "请上传3D模型图"];
        }
        //验证图片个数
        $model_3d_files = explode('|', $model_3d);
        $model_3d_files = array_filter($model_3d_files);
        if (count($model_3d_files) > self::Model3DLimit) {
            return [false, "3D模型图数量最多" . self::Model3DLimit . "张"];
        }
        //验证3D格式
        foreach ($model_3d_files as $file) {
            $ext = pathinfo($file, PATHINFO_EXTENSION);
            if (!in_array($ext, self::Model3DExt)) {
                return [false, "上传的3D模型图格式错误"];
            }
        }
        return [true, 'ok'];
    }


    /**
     * 获取平台数据，用于插入或更新
     * @param $gid
     * @param $platform_ids
     * @param $market_image
     * @param $cover_image
     * @param $images
     * @param $pc_cover_image
     * @param $pc_images
     * @return array
     */
    public function getPlatformData($gid, $platform_ids, $market_image, $cover_image, $images, $detail, $pc_cover_image, $pc_images, $pc_detail): array
    {
        $data = [];
        foreach ($platform_ids as $platform_id) {
            switch ($platform_id) {
                case PlatformModel::PLATFORM['WX']: // 微信小程序
                    $data[] = [
                        'gid'         => $gid,
                        'platform_id' => $platform_id,
                        'cover_image' => $market_image,
                        'images'      => $images,
                        'detail'      => $detail,
                        'goods_type'  => GoodsPlatformModel::GOODS_TYPES['MALL'],
                    ];
                    break;
                case PlatformModel::PLATFORM['APP']: // APP/H5
                    $data[] = [
                        'gid'         => $gid,
                        'platform_id' => $platform_id,
                        'cover_image' => $cover_image,
                        'images'      => $images,
                        'detail'      => $detail,
                        'goods_type'  => GoodsPlatformModel::GOODS_TYPES['MALL'],
                    ];
                    break;
                case PlatformModel::PLATFORM['PC']:  // PC
                    $data[] = [
                        'gid'         => $gid,
                        'platform_id' => $platform_id,
                        'cover_image' => $pc_cover_image,
                        'images'      => $pc_images,
                        'detail'      => $pc_detail,
                        'goods_type'  => GoodsPlatformModel::GOODS_TYPES['MALL'],
                    ];
                    break;
                default:
                    break;
            }
        }
        return $data;
    }

    /**
     * 处理商品信息
     * @param array $goods
     * @param $format_price
     * @return array
     */
    public function handleGoods(array $goods, $format_price): array
    {
        // 删除id
        unset($goods['id']);

        // 更改类型
        $goods['atype'] = strval($goods['atype'] ?? 0);

        // 处理平台数据
        $platforms = $goods['platforms'] ?? [];
        $goods['platform_ids'] = array_column($platforms, 'platform_id');

        // 新增字段
        $goods['custom_tag'] = $goods['custom_tag'] ?? '';

        // 价格
        $goods['original_price'] = $goods['price'];

        //  格式化价格
        if ($format_price) {
            $goods['mprice']         = $this->totalFee($goods['mprice'], 1);
            $goods['price']          = $this->totalFee($goods['price'], 1);
            $goods['original_price'] = $this->totalFee($goods['price'], 1);
            $goods['deposit']        = $this->totalFee($goods['deposit'] ?? 0, 1);
            $goods['expand_price']   = $this->totalFee($goods['expand_price'] ?? 0, 1);
        }

        // 处理图片数据
        $goods['pc_cover_image'] = '';
        $goods['pc_images'] = '';
        $goods['pc_detail'] = '';
        foreach ($platforms as $platform) {
            // 平台id
            $platform_id = $platform['platform_id'];
            // 填充信息（兼容历史数据结构）
            switch ($platform_id) {
                case PlatformModel::PLATFORM['WX']:
                    $goods['market_image'] = $platform['cover_image'];
                    $goods['images'] = $platform['images'];
                    $goods['detail'] = $platform['detail'];
                    break;
                case PlatformModel::PLATFORM['APP']:
                    $goods['cover_image'] = $platform['cover_image'];
                    $goods['images'] = $platform['images'];
                    $goods['detail'] = $platform['detail'];
                    break;
                case PlatformModel::PLATFORM['PC']:
                    $goods['pc_cover_image'] = $platform['cover_image'];
                    $goods['pc_images'] = $platform['images'];
                    $goods['pc_detail'] = $platform['detail'];
                    break;
                default:
                    break;
            }
        }
        return $goods;
    }

    // 定义与 平台 的关联关系，一个商品对应多个平台
    public function getPlatforms()
    {
        return $this->hasMany(GoodsPlatformModel::class, ['gid' => 'gid'])
            ->select(['gid', 'platform_id', 'cover_image', 'images', 'detail', 'goods_type'])
            ->where(['goods_type' => GoodsPlatformModel::GOODS_TYPES['MALL']]); // 普通商品
    }
}
