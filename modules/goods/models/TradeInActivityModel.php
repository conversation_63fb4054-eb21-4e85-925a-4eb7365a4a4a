<?php

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 以旧换新活动表
 */
class TradeInActivityModel extends CommModel
{
    // 缓存时间 3600 秒
    const EXPIRE_TIME = 3600;

    // 表字段
    public $tb_fields = [
            'id', 'name', 'picture', 'products', 'start_time', 'end_time', 'extras', 'status', 'is_del', 'ctime', 'utime'
    ];

    /**
     * 表名称
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_trade_in_activity`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    const STATUS = [
            'ON'  => 1, // 开启
            'OFF' => 2  // 停止
    ];

    const IS_DEL = [
            'NO'  => 0,
            'YES' => 1
    ];


    /**
     * 有效活动key
     * @return string
     */
    public function getValidActivityKey($key): string
    {
        return AppCRedisKeys::getTradeInValidActivityKey($key);
    }

    /**
     * 删除前端缓存
     */
    public function delCacheList()
    {
        CUtil::delCache($this->getValidActivityKey('*'),true);
    }


    /**
     * 获取有效的活动
     */
    public function getValidActivity()
    {
        return CUtil::rememberCache($this->getValidActivityKey('info'), function () {
            // 从数据库获取数据
            $current = time();
            $data    = self::find()
                    ->where([
                            'and',
                            ['<=', 'start_time', $current],
                            ['>=', 'end_time', $current],
                            ['status' => self::STATUS['ON'], 'is_del' => self::IS_DEL['NO']]
                    ])->asArray()->one();

            if (empty($data)) {
                return [];
            }

            // 设置缓存的过期时间为活动结束时间或默认过期时间
            $data['set_expire'] = CUtil::calculateCacheTtl([$data]);
            return $data;
        });
    }







    public function getGoodsList(): \yii\db\ActiveQuery
    {
        return $this->hasMany(TradeInActivityGoodsModel::class, ['activity_id' => 'id'])->select(['id', 'gid', 'sid', 'activity_id', 'price', 'underline_price'])->where(['is_del' => 0]);
    }


}