<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单主表
 */
namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\jobs\CancelDepositOrderJob;
use app\jobs\CancelPayOrderJob;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\AddressModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class OdepositModel extends CommModel {

    CONST EXP          = 3600;

    CONST PAY_EXPIRE        = YII_ENV_PROD ? 1800 : 800; //可支付时间窗口

    CONST ListExpire  = 600;//列表缓存

    CONST DB_TIMEZONE       = ['ST'=>2022,'ED'=>2030];//数据库的时间跨度

    CONST TYPE      = [
        'ORDINARY'                  => 1,       //普通订单
        'DEPOSIT'                   => 2,       //预售订单
    ];

    CONST PAY_TYPE      = [
        'PAY_DEPOSIT'             => 1,       //支付定金
        'PAY_TAIL'                => 2,       //支付尾款
    ];

    CONST STATUS      = [
        'ALL'                       => -1,
        'WAIT_PAY'                  => 0,       //未付款
        'CANCELED'                  => 100,     //已取消
        'WAIT_SEND'                 => 300,     //已付款
        'WAIT_RECEIVE'              => 400,     //待收货
        'FINISHED'                  => 500,     //已完成
        'REFUNDING'                 => 10000000,//申请退款
        'REFUNDING_WAIT_SEND'       => 10000300,//申请退款&待发货
        'REFUNDING_WAIT_RECEIVE'    => 10000400,//申请退款&待收货
        'REFUNDING_FINISHED'        => 10000500,//申请退款&已完成
        'RERUNDED'                  => 20000000,//退款完成
        'RERUNDED_WAIT_SEND'        => 20000300,//退款完成&待发货
        'RERUNDED_WAIT_RECEIVE'     => 20000400,//退款完成&待收货
        'RERUNDED_FINISHED'         => 20000500,//退款完成&已完成
    ];

    private $__order_status = [
        'ALL'                       => -1,
        'WAIT_PAY'                  => 0,       //待支付
        'CANCELED'                  => 100,     //已取消
        'WAIT_SEND'                 => 300,     //待发货
        'WAIT_RECEIVE'              => 400,     //待收货
        'FINISHED'                  => 500,     //已完成
        'REFUNDING'                 => 10000000,//申请退款
        'REFUNDING_WAIT_SEND'       => 10000300,//申请退款&待发货
        'REFUNDING_WAIT_RECEIVE'    => 10000400,//申请退款&待收货
        'REFUNDING_FINISHED'        => 10000500,//申请退款&已完成
        'RERUNDED'                  => 20000000,//退款完成
        'RERUNDED_WAIT_SEND'        => 20000300,//退款完成&待发货
        'RERUNDED_WAIT_RECEIVE'     => 20000400,//退款完成&待收货
        'RERUNDED_FINISHED'         => 20000500,//退款完成&已完成
    ];

    CONST ALL_REFUND         = 60000000;  //所有退款单
    CONST RERUND_DTS         = 30000000;  //驳回退款

    //取消原因
    const R_TYPE = [
        1 => '不想要了',
        2 => '价格有点贵',
        3 => '暂时不需要了',
        4 => '余额不足',
        5 => '拍错',
    ];


    CONST PAY_BY_WX = 1;
    CONST PAY_BY_WX_H5 = 2;
    CONST PAY_BY_WX_APP = 3; // 微信APP
    CONST PAY_BY_ALIPAY = 4; // 支付宝支付
    CONST PAY_BY_MP_WX = 7; // 中台（微信支付）
    CONST PAY_BY_MP_ALIPAY = 8; // 中台（支付宝支付）

    const PAY_BY_MP_WEB_WX_H5     = 10;  //中台（微信H5）
    const PAY_BY_MP_WEB_ALIPAY_H5 = 11;  //中台（支付宝H5）

    CONST PAY_BY_NO_SET = 99;

    public static function tbName($time): string
    {
        $year = date("Y",intval($time));
        //防止出现超出时间范围的查询
        $year = max($year,self::DB_TIMEZONE['ST']);
        $year = min($year,self::DB_TIMEZONE['ED']);

        return  "`db_dreame_goods`.`t_odeposit_{$year}`";
    }

    public $tb_fields = [
        'id', 'user_id', 'cart_id', 'gid', 'sid', 'num', 'order_no', 'status', 'price', 'pay_time', 'deposit_note', 'finish_time', 'update_time', 'ctime', 'cr_type'
    ];


    /**
     * @param string $order_no
     * @param bool $return_tb
     * @return string
     * 根据订单号找到数据表
     */
    public static function GetTbNameByOrderId($order_no='', bool $return_tb = true): string
    {
        if(empty($order_no)) {
            return "";
        }

        $ym     = substr($order_no,-7,4);//订单抽选时间所属年份
        $time   = strtotime(date("20{$ym}d"));

        if ($return_tb == false) {
            return $time;
        }

        return self::tbName($time);
    }

    /**
     * @return string
     * 订单列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getDepositList();
    }

    /**
     * @param $user_id
     * @return string
     * 订单列表
     */
    private function __getDepositListKey($user_id): string
    {
        return AppCRedisKeys::getMyDepositList($user_id);
    }


    /**
     * @param string $user_id
     * 清理订单列表
     */
    public function delListCache(string $user_id='')
    {
        $r_key  = $this->__getDepositListKey($user_id);
        $r_key1  = $this->__getListKey();

        by::redis('core')->del($r_key,$r_key1);
    }


    /**
     * @param $order_no
     * @return string
     * 订单唯一数据缓存KEY
     */
    private function __getInfoByOrderNoKey($user_id,$order_no): string
    {
        return AppCRedisKeys::getDepositInfoByOrNo($user_id,$order_no);
    }

    /**
     * @param $user_id
     * @param $gid
     * @return string
     * 预售的订单数量缓存KEY
     */
    private function __getPaidNumKey($gid): string
    {
        return AppCRedisKeys::getPaidNumByGid($gid);
    }


    /**
     * @param string $user_id
     * @param string $order_nos
     * @return void
     * @throws \RedisException
     * 清理缓存
     */
    public function delCache(string $user_id = '', string $order_no = '')
    {
        $r_key2 = $this->__getInfoByOrderNoKey($user_id,$order_no);

        by::redis('core')->del($r_key2);
    }

    /**
     * @param string $user_id
     * @param string $order_nos
     * @return void
     * @throws \RedisException
     * 清理预定人数缓存
     */
    public function delScheduledCache($gid = '')
    {
        $r_key = $this->__getPaidNumKey($gid);

        by::redis('core')->del($r_key);
    }


    /**
     * @param string $year
     * @param string $order_no
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 定金订单管理后台展示
     */
    public function getList(string $year='', string $order_no='', int $status=-1, int $s_time=0, int $e_time=0, int $page=1, int $page_size=50): array
    {
        $page      = CUtil::uint($page,1);
        $redis     = by::redis('core');
        $r_key     = $this->__getListKey();
        $s_key     = CUtil::getAllParams(__FUNCTION__, $year, $order_no, $status, $s_time, $e_time, $page, $page_size);
        $aJson     = $redis->hGet($r_key, $s_key);
        $aData     = (array)json_decode($aJson, true);
        if ($aJson === false) {
            if ($order_no) {
                $tb = $this->getTbNameByOrderId($order_no);
            } else {
                $year = trim($year);
                $time            = empty($year) ? time() : strtotime("{$year}0101");
                $tb              = self::tbName($time);
            }

            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($order_no, $status, $s_time, $e_time);
            $sql = "SELECT `order_no`,`status`,`price`,`pay_time`,`ctime` FROM {$tb} WHERE {$where} 
                                ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $aData = !empty($aData) ? $aData : [];

            $redis->hSet($r_key, $s_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 0 : self::EXP);
        }
        return $aData;
    }


    /**
     * @param string $order_no
     * @param int $status
     * @param $s_time
     * @param $e_time
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(string $order_no='', int $status=-1, $s_time=0, $e_time=0, $user_id = 0): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //注意范围
        if($user_id > 0) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"]  = intval($user_id);
        }

        //注意范围
        if(!empty($order_no)) {
            $where               .= " AND `order_no`=:order_no";
            $params[":order_no"]  = $order_no;
        }

        if ($status > -1) {
            $where            .= " and `status`=:status";
            $params[':status'] = $status;
        }

        if (!empty($s_time) && !empty($e_time)) {
            $where            .= " AND `ctime` BETWEEN :s_time AND :e_time";
            $params[":s_time"] = $s_time;
            $params[":e_time"] = $e_time;
        }

        return [$where,$params];
    }

    /**
     * @param string $year
     * @param string $order_no
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @return int
     * @throws Exception
     * @throws \RedisException
     * 订单总数
     */
    public function getListCount(string $year='', string $order_no='', int $status=-1, int $s_time=0, int $e_time=0): int
    {
        $redis = by::redis('core');
        $r_key = $this->__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $year, $order_no, $status, $s_time, $e_time);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            if ($order_no) {
                $tb = $this->getTbNameByOrderId($order_no);
            } else {
                $time = empty($year) ? time() : strtotime("{$year}0101");
                $tb = self::tbName($time);
            }

            list($where, $params) = $this->__getCondition($order_no, $status, $s_time, $e_time);
            $sql = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $command = by::dbMaster()->createCommand($sql, $params);
            $count = $command->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }
        return intval($count);
    }

    /**
     * @param array $arr
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 定金订单详情页面
     */
    public function depositBuyInfo(array $arr): array
    {
        $gcombines  = $arr['gcombines'] ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]
        $gcombines  = (array)json_decode($gcombines, true);
        $user_id    = $arr['user_id'];
        $cart_ids   = $arr['cart_ids']  ?? ""; //购物车id ["1"]
        $aid        = $arr['aid']       ?? 0;  //地址id

        $count = count($gcombines);
        if ($count > 1){
            return [false,'单次只能支付一种类型商品的定金'];
        }

        $aData      = [];
        $tprice     = 0; //总价
        $tdPrice = 0;
        foreach ($gcombines as $key => $val) {
            $gid         = CUtil::uint($val['gid']);
            $sid         = CUtil::uint($val['sid']);
            $num         = CUtil::uint($val['num'] ?? 0);

            if (empty($gid)) {
                return [false, '没有商品ID'];
            }

            if ($num <= 0) {
                return [false, '商品数量不能为0'];
            }

            $gInfo = by::Gmain()->GetOneByGidSid($gid, $sid, true, true);
            $price              = $gInfo['spec']['price'] ?? $gInfo['price'];
            $uprice         = $gInfo['spec']['price'] ?? $gInfo['price'];
            if (empty($gInfo)) {
                return [false, '没有商品ID'];
            }

            //判断商品是否是预售
            $gInfo['is_presale'] = intval($gInfo['is_presale'] ?? 0);
            if(empty($gInfo['is_presale'])){
                return [false, '预售已经结束，请返回重新下单！'];
            }

            unset($gInfo['sort'], $gInfo['mprice'], $gInfo['images'], $gInfo['is_coupons'], $gInfo['t_status'], $gInfo['t_time'], $gInfo['detail'], $gInfo['tids']);


            $gInfo['num']     = $num;
            $gInfo['tprice']  = bcmul($price, $num, 2);
            $tprice           = bcadd($tprice, $gInfo['tprice'], 2);
            $gInfo['tdprice'] = bcmul($gInfo['deposit'], $num, 2);
            $tdPrice          = bcadd($tdPrice, $gInfo['tdprice'], 2);

            $gInfo['tail_price'] = sprintf('%.2f', bcsub($price, $gInfo['expand_price'], 2));


            $aData['cart_ids']   = json_decode($cart_ids, true);
            $oGoodsInfo    = [
                'presale_time'      =>$gInfo['presale_time'],
                'deposit'           =>$gInfo['deposit'],
                'expand_price'      =>$gInfo['expand_price'],
                'start_payment'     =>$gInfo['start_payment'],
                'end_payment'       =>$gInfo['end_payment'],
                'surplus_time'      =>$gInfo['surplus_time'],
                'scheduled_number'  =>$gInfo['scheduled_number'],
                'coefficient'       =>$gInfo['coefficient'],
                'tail_price'        =>$gInfo['tail_price'],
                'uprice'            =>$uprice,
                'deposit_status'    => 0,
                'tail_order_status' => 0,
            ];
            $gInfo['presale_info']  = by::Odeposit()->_judgePayOrder($oGoodsInfo);
            $gInfo['uprice']  = $uprice;
            $aData['list'][]        = $gInfo;
        }

        $price          = bcsub($tprice, 0, 2);//商品价格

        $real_price = $tdPrice;//实付价格
        if (bccomp($real_price, 0, 2) <= 0) {
            return [false, '网络繁忙~~'];
        }

        $aData['total']         = [
            'tprice'     => $tprice,
            'deposit'    => $tdPrice,
            'acdeprice'  => '0.00',
            'price'      => $tdPrice,
            'real_price' => $tdPrice,
        ];

        $aData['pay_style'] = self::PAY_TYPE['PAY_DEPOSIT'];

        $aData['gcombines'] = $gcombines;
        return [true, $aData];
    }

    /**
     * @param $user_id
     * @param $api
     * @param $arr
     * @return array
     * @throws Exception
     * @throws \Exception
     * 创建定金订单
     */
    public function addDepositRecord($user_id, $arr)
    {
        //频率限制
        $unique_key = __FUNCTION__;
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,5,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $gcombines      = $arr['gcombines']   ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]
        $gcombines      = (array)json_decode($gcombines, true);
        $payType        = $arr['pay_type']    ?? self::PAY_BY_NO_SET;
        $payType        = CUtil::uint($payType);

        $data = [
            'aid'           => $arr['aid']  ?? 0,
            'deposit_note'  => $arr['note'] ?? '',
            'cart_id'       => $arr['cart_ids'] ?? ''

        ];
        list($s, $gcombines)  = $this->__check($user_id, $gcombines, $data);
        if (!$s) {
            return  [false, $gcombines];
        }


        //订单扩展数据
        $getChannel = $arr['get_channel'] ?? 0;//优惠券来源控制
        $spriceType = $arr['sprice_type'] ?? 0;//自定义设置价格类型


        $referer = CUtil::removeXss($arr['referer'] ?? '');//用户登录链接
        $union = CUtil::removeXss($arr['union'] ?? ''); //渠道来源
        $euid  = CUtil::removeXss($arr['euid'] ?? ''); //渠道来源-标识参数
        $unionInfo = by::userAdv()->getLastUnionInfo($user_id);
        $referer   = $unionInfo['referer'] ?? $referer;
        $union     = $unionInfo['union'] ?? $union;
        $euid      = $unionInfo['euid'] ?? $euid;



        $liveMark = CUtil::removeXss($arr['live_mark'] ?? '');//直播标识
        $platformSource = $arr['platform_source'] ?? 0; //平台来源
        $source = by::Omain()::UNION_SOURCE[$union] ?? 0;
        $guide_id  = by::userGuide()->getGuideByUid($user_id);
        $r_info      = by::userRecommend()->getInfoByUid($user_id);
        $r_id        = $r_info['r_id']        ?? 0;
        $expire_time = $r_info['expire_time'] ?? 0;


        //数据判断 订单来源判断
        if(empty($source)){
            !empty($gudie_id) && $source = 1;
            if (!empty($r_id) && $r_id != $user_id && !empty($expire_time) && intval(START_TIME) <= $expire_time) {
                if (empty($gudie_id)) {
                    $source = 2;
                } else {
                    $source = 3;
                }
            }
        }

        $guide_type = ($spriceType == 1) ? 2 : 1;


        $db           = by::dbMaster();
        $trans        = $db->beginTransaction();
        $order_no     = "";
        $ctime        = intval(START_TIME);
        $pay_price    = $gcombines[0]['tprice'] ?? 0;//定金价格
        $deposit_note = $data['deposit_note'] ?? '';//定金订单备注
        $gid          = CUtil::uint($gcombines[0]['gid'] ?? 0);  //商品id
        $sid          = CUtil::uint($gcombines[0]['sid'] ?? 0);  //属性id
        $num          = CUtil::uint($gcombines[0]['num'] ?? 0);  //数量
        $cart_id      = CUtil::uint($gcombines[0]['cart_id'] ?? 0);//购物车id

        try {
            //定金订单表数据
            $depositData = [
                'user_id'      => $user_id,
                'cart_id'      => $cart_id,
                'gid'          => $gid,
                'sid'          => $sid,
                'num'          => $num,
                'order_no'     => $this->createOrderNo($ctime),
                'price'        => $pay_price,
                'deposit_note' => $deposit_note,
                'ctime'        => $ctime
            ];
            //尝试三次创建订单
            for ($i=0;$i<3;$i++) {
                list($s, $order_no) = $this->saveDeposit($depositData);
                if ($s) {
                    break;
                }
            }
            if(!$s) {
                throw new \Exception($order_no, 2001);
            }

            //todo 更新预售商品库存
            list($s)    = by::Gprestock()->UpdateStock($gid, $sid, $num);
            if (!$s) {
                throw new MyExceptionModel('库存不够~~', 2001);
            }

            //地址表数据
            by::Oad()->SaveLog($user_id, $order_no, $arr['aid']);

            //定金订单扩展表
            $aData = [
                'gid'             => $gid,
                'sid'             => $sid,
                'num'             => $num,
                'price'           => $pay_price,
                'r_id'            => $r_id,
                'guide_id'        => $guide_id,
                'guide_type'      => $guide_type,
                'union'           => $union,
                'euid'            => $euid,
                'live_mark'       => $liveMark,
                'referer'         => $referer,
                'source'          => $source,
                'platform_source' => $platformSource,
                'ctime'           => intval(START_TIME),
            ];
            by::OdepositE()->SaveLog($user_id,$order_no,$aData);


            $ret = ['need_pay' => '1', 'order_no'=>$order_no];

            //保存拉起付款参数，重新支付用
            $payData = [
                'prepay_id' => $ret['prepay_id'] ?? '', 'price' => $pay_price, 'pay_type' => $payType ?? self::PAY_BY_NO_SET, 'h5_url' => $ret['h5_url'] ?? ''];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

            //todo 购物车下单-删除购物车
            $cart_ids   = (array)json_decode($arr['cart_ids'] ?? '', true);
            !empty($cart_ids) && by::cart()->del($user_id, $cart_ids);

            $trans->commit();

            //todo 清理订单地址表缓存
            by::Oad()->DelCache($order_no);
            //todo 清理用户订单列表缓存
            $this->delListCache($user_id);

            //todo 腾讯推广
            by::userAdv()->pushAdv($user_id, $order_no, 'COMPLETE_ORDER');

            self::ReqAntiConcurrency(0,$unique_key,0,'DEL');

            // 取消支付单（异步处理，不影响主流程），订单关闭前5秒
            \Yii::$app->queue->delay(self::PAY_EXPIRE - 5)->push(new CancelPayOrderJob([
                'order_no' => $order_no
            ]));

            // 延时队列，异步取消订单
            \Yii::$app->queue->delay(self::PAY_EXPIRE)->push(new CancelDepositOrderJob([
                'user_id'  => $user_id,
                'order_no' => $order_no,
            ]));

            return [true,$ret];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            by::Omain()->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['DEPOSIT']);

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            by::Omain()->__addRecord($e, $gcombines, $unique_key, $user_id, by::wxPay()::SOURCE['DEPOSIT']);

            return [false, '网络繁忙,请稍候'];
        }
    }

    /**
     * @param $user_id
     * @param $cart_id
     * @param $gcombines
     * @param $data
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 验证订单参数
     */
    private function __check($user_id, $gcombines, $data): array
    {

        if (empty($gcombines) || $user_id <= 0) {
            return [false, '非法参数'];
        }

        //TODO 商品数据大于2
        $gNums          = count($gcombines);
        if ($gNums > 1){
            return [false, '单次只能付一件类型商品的定金'];
        }


        $cart_id        = (array)json_decode($data['cart_id'], true);
        $cart_num       = count($cart_id) ?? 0;
        if (!empty($cart_id) && $cart_num > 1){
            return [false, '预售商品请单独结算'];
        }
        $gcombines[0]['cart_id'] = $cart_id[0] ?? '';

        //todo 判定该用户是否授权手机号
        $phone     = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            return [false, '请先授权手机号'];
        }

        //收获信息校验
        $aid            = $data['aid'];
        if ($aid <= 0) {
            return [false, '请选择收货地址'];
        }

        $ad = by::Address()->GetOneAddress($user_id,$aid);
        $isDel = $ad['is_del'] ?? 0; // 删除状态
        if (empty($ad) || ($isDel == by::Address()::IS_DEL['yes'])) { // 地址不存在、或地址被删除
            return [false, '请添加或选择收货地址'];
        }

        //todo 限购区域
        list($s, $m) = by::model('OfreightModel', 'goods')->IsDis($user_id, $aid);
        if (!$s) {
            return [false, $m];
        }

        //订单备注校验
        if (mb_strlen($data['deposit_note']) > 50) {
            return [false, '请输入50字内备注'];
        }

        $mGmain         = by::Gmain();

        $limit_gids     = [];

        foreach($gcombines as $k => $gcombine) {

            $gid    = $gcombine['gid'];

            $num    = intval($gcombine['num'] ?? 0);

            if($num <= 0){
                return [false, '选择的商品数量不能为0！'];
            }

            $g_info  = $mGmain->GetOneByGidSid($gid, $gcombine['sid'], false);


            if (empty($g_info)) {
                return [false, '商品已下架，下单失败~'];
            }

            $is_presale = $g_info['is_presale'] ?? 0;
            $presale_time = $g_info['presale_time'] ?? 0;
            if ($presale_time < intval(START_TIME)) {
                return [false, '商品预售已结束！'];
            }


            if ($g_info['status'] != 0) {
                return [false, '商品已下架，下单失败~~'];
            }

            if ($g_info['atype'] == by::Gtype0()::ATYPE['SPECS'] && empty($g_info['spec'])) {
                return [false, '多规格商品，请选择正确的属性'];
            }

            // todo 商品限购
            if ($g_info['limit_num'] > 0) {
                $limit_gids[$gid]['limit_num'] = $g_info['limit_num'];
                $limit_gids[$gid]['num'] = empty($limit_gids[$gid]['num']) ? $gcombine['num'] :
                    bcadd($limit_gids[$gid]['num'], $gcombine['num']);
            }

            // TODO 第一次校验库存（预定人数）
            $pre_stock    = by::Gprestock()->OptStock($gid, $gcombine['sid']);
            if ($gcombine['num'] > $pre_stock) {
                return [false, '商品库存不够，下单失败~'];
            }

            $price                       = $g_info['deposit'] ?? 0;
            $gcombines[$k]['tprice']     = bcmul($price, $gcombine['num']);
            $gcombines[$k]['tids']       = $g_info['tids']          ?? [];  //标签
        }

        //todo 判断限购
        if (!empty($limit_gids)) {
            foreach ($limit_gids as $gid=>$v) {
                list($s, $m) = by::Ogoods()->CanLimitBuy($user_id, $gid, $v['num'], $v['limit_num']);
                if (!$s) {
                    return [false, $m];
                }
            }
        }

        return [true, $gcombines];
    }

    /**
     * @param $time
     * @return string
     * 生成唯一订单号
     */
    public function createOrderNo($time): string
    {
        $ym             = date('ym', intval($time));
        $date           = date("Ymd");
        $unix           = sprintf("%.3f",microtime(true));
        list($now,$mil) = explode('.',$unix);

        $config         = CUtil::getConfig('hostid','common',MAIN_MODULE);
        $hostid         = $config[HOST_NAME] ?? mt_rand(0, 99);
        $hostid         = sprintf("%02d", $hostid);//有些项目服务器数量可能两位数
        $rand           = mt_rand(200, 299);//防止时间回拨，减少碰撞概率

        $second         = $now - strtotime('today');
        $second         = sprintf("%05d", $second);
        $pid            = sprintf("%05d", getmypid());
        $oid            = "{$date}{$second}{$pid}{$hostid}{$rand}{$ym}{$mil}";
        usleep(1000);//保证1ms一个
        return $oid;
    }

    /**
     * @param $arr
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 保存定金订单
     */
    public function saveDeposit($arr): array
    {
        $user_id        = $arr['user_id']   ?? 0;
        $cart_id        = $arr['cart_id']   ?? 0;
        $gid            = $arr['gid']       ?? 0;
        $sid            = $arr['sid']       ?? 0;
        $num            = $arr['num']       ?? 0;
        $order_no       = $arr['order_no']  ?? 0;
        $price          = $arr['price']     ?? 0;
        $ctime          = $arr['ctime']     ?? 0;
        $status         = $arr['status']    ?? 0;
        $note           = $arr['deposit_note']      ?? '';

        if (empty($user_id) || empty($ctime)) {
            return [false, '参数错误(3)'];
        }

        $save = [
            'user_id'       => $user_id,
            'cart_id'       => $cart_id,
            'gid'           => $gid,
            'sid'           => $sid,
            'num'           => $num,
            'order_no'      => $order_no,
            'status'        => $status,
            'price'         => $price,
            'deposit_note'  => $note,
            'ctime'         => $ctime
        ];

        $tb     = self::tbName($ctime);
        by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $this->delCache($user_id,$order_no);
        $this->delListCache($user_id);

        return [true, $order_no];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $can_user
     * @param $can_attr
     * @param $can_ad
     * @param $can_remain
     * @param $cache
     * @param $isShortCode
     * @return array|void
     * @throws Exception
     * @throws \RedisException
     * 统一打包数据
     */
    public function CommDepositPackageInfo($user_id,$order_no,$can_user=false,$can_attr=false,$can_ad=false,$can_remain=false,$cache=true,$isShortCode = false)
    {
        $info          = $this->getInfoByDepositOrderNo($user_id,$order_no,$can_user,$can_attr,$cache);
        if(empty($info)) {
            return [];
        }

        $info['user_order_type'] = by::Omain()::USER_ORDER_TYPE['DEPOSIT'];

        //根据定金订单查询主订单
        (($info['status'] ?? 0) >= 300) && $oInfo        = by::Ouser()->GetInfoByDepositNo($user_id,$info['order_no']);

        //定金订单商品数据
        $extendData = by::OdepositE()->GetInfoByOrderId($user_id,$info['order_no'],false);
        $oGoods = $extendData['cfg'] ?? [];

        if(empty($oGoods)){
            $oGoods        = by::Gmain()->GetOneByGidSid($info['gid']?? 0, $info['sid']??0,false);
        }
        $uprice = $oGoods['spec']['price'] ?? $oGoods['price'];
        $oGoodsInfo = [
            'presale_time'      => $oGoods['presale_time'],
            'deposit'           => by::Gtype0()->totalFee($oGoods['deposit'],1),
            'expand_price'      => by::Gtype0()->totalFee($oGoods['expand_price'],1),
            'tail_price'        => by::Gtype0()->totalFee(bcsub(($oGoods['spec']['price'] ?? $oGoods['price']) ?? 0,$oGoods['expand_price'],2),1),
            'start_payment'     => $oGoods['start_payment'],
            'end_payment'       => $oGoods['end_payment'],
            'surplus_time'      => $oGoods['surplus_time'],
            'scheduled_number'  => $oGoods['scheduled_number'],
            'coefficient'       => $oGoods['coefficient'] ?? '',
            'deposit_status'    => (($info['status'] ?? 0) >= 300) ? 1 : 0,
            'tail_order_status' => (($oInfo['status'] ?? 0) >= 300) ? 1 : 0,
        ];

        $tail_price    = sprintf('%.2f',by::Gtype0()->totalFee(bcmul(bcsub(($oGoods['spec']['price'] ?? $oGoods['price']) ?? 0,$oGoods['expand_price'],2),$info['num'],2),1)) ?? 0;

        $oGoodsInfo = $this->_judgePayOrder($oGoodsInfo);

        $oGoods = [
            'id'                   => $oGoods['id'] ?? 0,
            'gid'                  => $oGoods['gid'] ?? 0,
            'sku'                  => $oGoods['sku'] ?? '',
            'name'                 => $oGoods['name'] ?? '',
            'cover_image'          => $oGoods['cover_image'] ?? '',
            'market_image'         => empty($oGoods['market_image'] ?? '') ? $oGoods['cover_image'] ?? '' : $oGoods['market_image'],
            'pc_image'             => empty($oGoods['pc_image'] ?? '') ? $oGoods['cover_image'] ?? '' : $oGoods['pc_image'],
            'tids'                 => $oGoods['tids'] ?? '',
            'is_internal_purchase' => $oGoods['is_internal_purchase'] ?? 0,
            'is_internal'          => $oGoods['is_internal'] ?? 0,
            'gini_id'              => $oGoods['gini_id'] ?? 0,
            'is_presale'           => $oGoods['is_presale'] ?? 0,
            'uprice'               => by::Gtype0()->totalFee($uprice, 1),
            'presale_info'         => $oGoodsInfo,
            'price'                => by::Gtype0()->totalFee($oGoods['price'], 1),
            'num'                  => $info['num'],
            'oprice'               => by::Gtype0()->totalFee(bcmul($oGoods['price'], $info['num'], 2), 1),
            'deposit'              => by::Gtype0()->totalFee(bcmul($oGoods['deposit'], $info['num'], 2), 1),
            'tail_price'           => $oGoodsInfo['tail_price'] ?? 0,
            'type'                 => $oGoods['type'] ?? '0',
            'note'                 => $info['deposit_note'] ?? '',
        ];

        if($isShortCode && $oGoods['sku']){
            $oGoods['short_code'] = by::Gmain()->GetOneBySku($oGoods['sku'])['short_code'] ?? '';
        }else{
            $oGoods['short_code'] = '';
        }
        //规格属性名
        if ($can_attr) {
            $av_ids         = $extendData['cfg']['spec']['av_ids'] ?? '';
            $attr_cnf       = [];
            if ($av_ids) {
                $av_ids = json_decode($av_ids,true);
                foreach ($av_ids as $k1 => $v1){
                    $cnf = by::Gav()->IdToName($v1);
                    $cnf && $attr_cnf[] = $cnf;
                }
            }
            $oGoods['attr_cnf'] = $attr_cnf;
        }

        $info['goods'][] = $oGoods;

        if($can_user) {
            $user         = by::users()->getOneByUid($user_id);
            $info['user'] = [
                'avatar' => $user['avatar'] ?? "",
                'nick'   => $user['nick']   ?? "",
            ];
        }

        //收货地址
        if ($can_ad) {
            $oAd               = by::Oad()->GetOneByOrderNo($order_no);
            // 是否默认地址
            $defaultAddress    = by::Address()->GetDefaultAddress($user_id, 0, false);
            $isDefaultAddress  = 0;
            if ($defaultAddress &&
                $defaultAddress['id'] == $oAd['address_id'] &&
                $defaultAddress['status'] == AddressModel::STATUS['YES']
            ) {
                $isDefaultAddress = 1;
            }

            $address           = [
                'id'        => $oAd['address_id'], // 收货地址ID
                'nick'      => $oAd['nick'],
                'phone'     => $oAd['phone'],
                'province'  => $oAd['address']['province'],
                'city'      => $oAd['address']['city'],
                'area'      => $oAd['address']['area'],
                'detail'    => $oAd['detail'],
                'pid'       => $oAd['pid'],
                'cid'       => $oAd['cid'],
                'aid'       => $oAd['aid'],
                'is_default_address' => $isDefaultAddress,
            ];
            $info['address']   = $address;
            $info['mail']      = [
                'mail_no'           => $oAd['mail_no'],
                'express_code'      => $oAd['express_code'],
                'express_name'      => $oAd['express_name'],
            ];
        }

        //收货地址
        if ($can_remain && $info['status'] == self::STATUS['WAIT_PAY']) {
            $now_time = intval(START_TIME);
            $remain_time            = $info['ctime'] + by::Omain()::PAY_EXPIRE - $now_time + 5;
            $info['remain_time']    = $remain_time < 0 ? 0 : $remain_time;

            //定金订单截止时间
            $endPayment = CUtil::uint($oGoodsInfo['presale_time'] ?? 0);
            if ($endPayment) {
                $remain_time         = min($info['remain_time'], $endPayment - $now_time);
                $info['remain_time'] = $remain_time < 0 ? 0 : $remain_time;
            }
        }

        $info['pay_style']        = self::PAY_TYPE['PAY_DEPOSIT'];
        $info['deposit']          = $info['price'] ?? 0;
        $info['acdeprice']        = '0.00';
        $info['tail_price']       = $tail_price;
        $info['real_price']       = $info['price'] ?? 0;
        $info['is_presale_order'] = by::Gmain()::PTYPE['PRESALE'];
        $info['end_payment']      = $oGoodsInfo['end_payment'] ?? 0;
        $info['note']             = $info['deposit_note'] ?? '';

        return $info;
    }


    public function _judgePayOrder($info)
    {
        //判断定金是否可支付
        //判断尾款是否可支付
        $can_pay_deposit = 1;
        $can_pay_tail = 0;
        $now = intval(START_TIME);
        $presaleTime = $info['presale_time'];
        $startPayment = $info['start_payment'];
        $endPayment = $info['end_payment'];
        if($now >= $presaleTime){
            $can_pay_deposit = 0;
        }
        if($now >= $startPayment && $now < $endPayment){
            $can_pay_tail = 1;
        }
        $info['can_pay_deposit'] = $can_pay_deposit;
        $info['can_pay_tail'] = $can_pay_tail;

        return $info;
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     * @throws \RedisException
     * 获取订单详情
     */
    public function getInfoByDepositOrderNo($user_id, $order_no,bool $can_user = false, bool $address = false,bool $cache=true, bool $format_price = true)
    {
        $user_id = CUtil::uint($user_id);
        if(empty($order_no) || empty($user_id)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getInfoByOrderNoKey($user_id,$order_no);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false || !$cache) {
            $time    = time();
            $tb      = $this->tbName($time);

            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `order_no`=:order_no AND `user_id`=:user_id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':order_no'=>$order_no, ':user_id'=>$user_id])->queryOne();
            $aData   = $aData ?: [];
            $cache == true && $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        if(!empty($aData)) {
            $format_price && $aData['price']   = by::Gtype0()->totalFee($aData['price'], 1);
            if ($can_user) {
                $user = by::users()->getOneByUid($aData['user_id']);
                $aData['user'] = [
                    'nick'   => $user['nick']   ?? "",
                    'phone'  => by::Phone()->GetPhoneByUid($aData['user_id']),
                ];
            }
            if($address){
                  $address = by::Oad()->GetOneByOrderNo($order_no);
                  $aData['address'] = $address ?? [];
            }
        }else{
            return [];
        }

        return $aData;
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $next_status
     * @param array $update_data
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 订单状态结果同步
     */
    public function syncInfo($user_id, $order_no, $next_status, array $update_data = []): array
    {
        if($user_id == 0 || empty($order_no) || !in_array($next_status,self::STATUS)) {
            return [false,"非法参数"];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try{
            $update_data['status'] = $next_status;
            $time       = $this->GetTbNameByOrderId($order_no, false);

            //todo 定金订单表更新状态
            $ret = $db->createCommand()->update(
                self::tbName($time),
                $update_data,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new MyExceptionModel("无数据更新(1)");
            }
            //todo 定金扩展表改变状态
            $extendInfo = by::OdepositE()->GetInfoByOrderId($user_id,$order_no);
            if($extendInfo){
                $extendRet = $db->createCommand()->update(
                    by::OdepositE()::tbName($user_id),
                    ['status'=>$next_status],
                    ['order_no' => $order_no, 'user_id' => $user_id]
                )->execute();
                if($extendRet <= 0) {
                    throw new MyExceptionModel("无数据更新(2)");
                }
            }

            $trans->commit();

            $this->delCache($user_id,$order_no);
            $this->delListCache($user_id);

            if($extendInfo){
                by::OdepositE()->DelInfoCache($user_id,$order_no);
            }

            return [true, "OK"];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $trans->rollBack();

            return [false,$e->getMessage()];
        }
    }


    /**
     * @param $user_id
     * @param $gid
     * @param bool $cache
     * @return array|int|mixed|\Redis|string|\yii\db\DataReader
     * @throws Exception
     * @throws \RedisException
     * 根据gid统计预售商品定金订单数量
     */
    public function getPaidNumByGid($gid, bool $cache=true)
    {
        $redis       = by::redis('core');
        $redis_key   = $this->__getPaidNumKey($gid);
        $aData       = $cache ? $redis->get($redis_key) :false;

        if($aData === false) {
            $this_year     = date("Y");
            $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
            $years         = array_unique($years);
            foreach ($years as $year) {
                if($year < 2022) {
                    continue;
                }
                $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
                $ctime     = strtotime($date);
                $tb_main   = self::tbName($ctime);
                $where              = "`gid` = :gid";
                $params[':gid']     = $gid;
                $status             = [
                    self::STATUS['WAIT_SEND'],self::STATUS['FINISHED'],self::STATUS['WAIT_RECEIVE']
                ];
                $in_status          = implode(',',$status);
                $where             .= " AND `status` IN ($in_status)";

                $sql     = "SELECT count(*) FROM {$tb_main} WHERE {$where}";
                $num   = by::dbMaster()->createCommand($sql,$params)->queryScalar() ?? 0;
                $aData += $num;
            }

            $redis->set($redis_key,$aData,['EX'=>empty($aData) ? 10 : self::EXP]);
        }

        return $aData;
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $r_type
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 回收定金订单
     */
    public function depositRecycle($user_id, $order_no = '', $r_type = 0) :array
    {
        //频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,3,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $oDeposit     = by::Odeposit();
        //可取消状态
        $can_status = [$oDeposit::STATUS['WAIT_PAY']];

        $oInfo      = $this->getInfoByDepositOrderNo($user_id, $order_no);
        if (empty($oInfo)) {
            return [false, '无需处理'];
        }
        if ( !in_array($oInfo['status'], $can_status) ) {
            return [false, '该订单不可取消'];
        }

        //todo 查询支付方式参数
        $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
        if(empty($aLog)) {
            return [false, '无法获取支付流水，数据有误！'];
        }
        $payType = $aLog['pay_type']??'';
        if($payType == $oDeposit::PAY_BY_WX || $payType == $oDeposit::PAY_BY_WX_APP){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::WxPay()->wxOrderQuery($order_no, 1, $payType);
            if ($s) {
                return [false, '该订单已支付'];
            }
        }elseif ($payType == $oDeposit::PAY_BY_WX_H5){
            // todo 查询微信中心此订单是否已付款
            list($s) = by::wxH5Pay()->wxH5OrderQuery($order_no, 1);
            if ($s) {
                return [false, '该订单已支付H5'];
            }
        }

        $trans      = by::dbMaster()->beginTransaction();
        try {
            $arr['cr_type'] = $r_type;
            list($s, $m) = $this->SyncInfo($user_id, $order_no, self::STATUS['CANCELED'], $arr ?? []);
            if (!$s) {
                throw new \Exception($m);
            }


            list($s, $m) = by::Gprestock()->UpdateStock($oInfo['gid'], $oInfo['sid'], $oInfo['num'], 'ROLL', false);
            if (!$s) {
                throw new \Exception($m);
            }



            $trans->commit();

            //清理预定人数缓存
            $this->delScheduledCache($oInfo['gid']);
            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($order_no.'|'.$e->getMessage(), 'err.depositRecycle');

            return [false, '取消失败！'];
        }
    }

    /**
     * 订单是否可取消
     * @param $user_id
     * @param $order_no
     * @return bool
     * @throws Exception
     * @throws \RedisException
     */
    public function isCanCancelOrder($user_id, $order_no): bool
    {
        $canCancelStatus = [self::STATUS['WAIT_PAY']];
        $orderInfo = $this->getInfoByDepositOrderNo($user_id, $order_no);
        if (empty($orderInfo) || !in_array($orderInfo['status'], $canCancelStatus)) {
            return false;
        }
        return true;
    }

    /**
     * @param $user_id
     * @param int $status
     * @param string $order_no
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws \yii\db\Exception
     * 订单列表
     */
    public function getDepositList($user_id,$status=-1,$order_no='',$page=1,$page_size=10)
    {
        $page        = CUtil::uint($page,1);
        $redis       = by::redis('core');
        $r_key       = $this->__getDepositListKey($user_id);
        $sub_key     = CUtil::getAllParams(__FUNCTION__,$status,$order_no,$page,$page_size);
        $aJson       = $redis->hGet($r_key,$sub_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $time                = intval(START_TIME);
            $tb                  = $this->tbName($time);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($order_no,$status,0,0,$user_id);
            $sql                 = "SELECT `order_no` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC 
                                    LIMIT {$offset},{$page_size}";

            $command             = by::dbMaster()->createCommand($sql,$params);
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return $aData;
    }


    /**
     * @param $user_id
     * @param $status
     * @param $order_no
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 获取定金订单
     */
    public function getDepositInfos($user_id, $status = -1, $order_no= '',$page=1, $page_size=10)
    {
        $deposits  = $this->getDepositList($user_id,$status,$order_no,$page, $page_size);

        $dInfos = [];
        if (!empty($deposits)){
            foreach ($deposits as $sit) {
                $dInfo = $this->CommDepositPackageInfo($user_id,$sit['order_no'],false,true);
                $dInfos[] = $dInfo;
            }
        }
        return $dInfos;
    }





    /**
     * @param array $goodIds
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 自动取消订单记录
     */
    public function Cancel(array $goodIds = []): array
    {
        $wait          = self::STATUS['WAIT_PAY'];

        $etime         = intval(START_TIME) - self::PAY_EXPIRE + 20; //两小时前的数据置为已取消状态

        $where         = " `status`={$wait} AND `ctime`<{$etime}";

        if($goodIds){
            $goodIds = implode("','",$goodIds);
            $where = " `status`={$wait} AND `gid` in ('{$goodIds}')";
        }

        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);
        foreach ($years as $year) {
            $id            = 0;

            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_main   = self::tbName($ctime);

            while (true) {
                $sql    = "SELECT `id`,`order_no`,`user_id` FROM {$tb_main} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

                $list   = $db->createCommand($sql, [':id'=>$id])->queryAll();

                if (empty($list)) {
                    break;
                }

                $end    = end($list);
                $id     = $end['id'];

                foreach($list as $val) {
                    list($status,$ret) = $this->depositRecycle($val['user_id'], $val['order_no']);
                    if(!$status){
                        CUtil::debug($val['order_no'].'|'.$val['user_id'].'|'.json_encode($ret,320), 'err.depositRecycle');
                    }
                    // 取消支付单，塞队列中去处理
                    \Yii::$app->queue->push(new CancelPayOrderJob([
                        'order_no' => $val['order_no']
                    ]));
                }
            }

        }
        return [true,"OK"];
    }


    /**
     * @param $code
     * @param $target
     * @param $is_refund
     * @param $complete
     * @param $ostatus
     * @return int
     * 获取订单状态
     */
    public function SetOrderStatus($code, $target, $is_refund=false, $complete=false, $ostatus=-1)
    {
        $c_code     = -1;
        if ($is_refund && $complete) {
            switch ($target) {
                case self::STATUS['REFUNDING'] :
                case self::STATUS['RERUNDED'] :
                    $c_code = $target;break;
                case self::RERUND_DTS :
                    $c_code = $ostatus;break;
            }

        } elseif ($is_refund && !$complete) {

            switch ($target) {
                //10000300->通过->20000300
                case self::STATUS['REFUNDING'] :
                case self::STATUS['RERUNDED'] :
                    list($fist, $second) = $this->SplitOrderStatus($code);
                    if ($fist > 0 && $second == 0) {
                        if ($target >= $this->__order_status['REFUNDING']) {
                            $c_code = $target;
                        } else {
                            $c_code = $fist + $target;
                        }
                    } else {
                        if ($second >= $this->__order_status['REFUNDING']) {
                            $c_code = $second;
                        } else if ($target >= $this->__order_status['REFUNDING']) {
                            $c_code = $second + $target;
                        } else {
                            $c_code = $target;
                        }
                    }
                    break;

                //10000300->驳回->20000300 || 10000300
                case self::RERUND_DTS :
                    $c_code = $ostatus;break;
            }

        } else {
            list($fist, $second) = $this->SplitOrderStatus($code);

            if ($fist > 0 && $second == 0) {
                if ($target >= $this->__order_status['REFUNDING']) {
                    $c_code = $target;
                } else {
                    $c_code = $fist + $target;
                }
            } else if ($fist > 0) {
                if ($target >= $this->__order_status['REFUNDING']) {
                    $c_code = $second + $target;
                } else {
                    $c_code = $fist + $target;
                }
            } else {
                $c_code = $target;
            }
        }

        return (int)$c_code;
    }


    public function SplitOrderStatus($code): array
    {
        $fist   = 0;    //第一状态（退款中、退款完成、退款驳回）
        if ($code >= $this->__order_status['REFUNDING']) {
            $s_temp     = intval(substr($code, -3));
            $second     = $s_temp > 0 ? $s_temp : $code;
            $fist       = $code - $s_temp;
        } else {
            $second     = $code;
        }

        $fist       = (string)$fist;
        $second     = (string)$second;

        return [$fist, $second];
    }
}
