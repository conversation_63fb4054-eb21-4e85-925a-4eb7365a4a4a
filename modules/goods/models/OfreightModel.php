<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 * 运费表
 */

namespace app\modules\goods\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\Exception;
use app\modules\main\models\CommModel;

class OfreightModel extends CommModel
{

    const TYPE = [
        'WITH_FREE' => 1, //满免运费
        'DEFAULT'   => 2, //默认运费
        'AREA'      => 3, //地区配置运费
        'DIS_AREA'  => 4, //禁用地区
        'EXPLORE'   => 5  //勘探服务地区
    ];

    public $tb_fields = [
        'id','type','cnf'
    ];

    public static function tbName()
    {
        return "`db_dreame_goods`.`t_ofreight`";
    }

    /**
     * @param $type
     * @return string
     * 通过type获取数据
     */
    private function __getOneByTypeKey($type): string
    {
        return AppCRedisKeys::getFreightByType($type);
    }

    /**
     * @param $id
     * @return string
     * 通过id获取数据
     */
    private function __getOneByIdKey($id): string
    {
        return AppCRedisKeys::getFreightById($id);
    }

    /**
     * @param $id
     * 删除id缓存数据
     */
    private function __delCacheByIdKey($id): string
    {
        $r_key  = $this->__getOneByIdKey($id);
        return by::redis()->del($r_key);
    }

    private function __delCache($type)
    {
        $r_key  = $this->__getOneByTypeKey($type);
        return by::redis()->del($r_key);
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog(array $aData): array
    {
        $id         = $aData['id']      ?? 0;
        $type       = $aData['type']    ?? 0;
        $cnf        = $aData['cnf']     ?? 0;
        $aids       = $aData['aids']    ?? ""; //[{"province":"110000","city":["110100","110100"]}]

        $type       = CUtil::uint($type);

        if ( !in_array($type, self::TYPE) ) {
            return [false, '类型错误'];
        }

        switch ($type) {
            case self::TYPE['WITH_FREE']:
            case self::TYPE['DEFAULT']:
            case self::TYPE['AREA']:
                $cnf    = by::Gtype0()->totalFee($cnf);

                break;
            case self::TYPE['EXPLORE']:
                if (empty($id)) {
                    $info = $this->GetOneByType($type);
                    if (!empty($info)) {
                        return [false, '勘探服务地区数据已存在'];
                    }
                }

                break;
        }

        $db         = by::dbMaster();

        $trans      = $db->beginTransaction();
        try {
            if ($id) {

                list($s, $m) = $this->__update($id, $type, $cnf, $aids);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }

            } else {

                list($s, $m) = $this->__insert($type, $cnf, $aids);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
            }

            $trans->commit();

            $this->__delCache($type);
            $id && $this->__delCacheByIdKey($id);
            return [true, 'ok'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            return [false, '操作失败'];
        }
    }

    /**
     * @param $type
     * @param $cnf
     * @param string $aids
     * @return array
     * @throws Exception
     * 插入数据
     */
    private function __insert($type, $cnf, string $aids)
    {
        $tb         = self::tbName();
        $db         = by::dbMaster();

        switch ($type) {
            case self::TYPE['WITH_FREE']:
            case self::TYPE['DEFAULT']:
                $aLog   = $this->GetOneByType($type);
                if (!empty($aLog)) {
                    return [false, '当前type只允许唯一数据'];
                }

                $save   = [
                    'type'      => $type,
                    'cnf'       => $cnf,
                ];
                $db->createCommand()->insert($tb, $save)->execute();
                break;

            default :
                $save   = [
                    'type'      => $type,
                    'cnf'       => $cnf,
                ];
                $db->createCommand()->insert($tb, $save)->execute();

                $id         = $db->getLastInsertID();

                list($s, $m) = by::model('OfCfgModel', 'goods')->SaveLog($id, $type, $aids);

                if (!$s) {
                    return [false, $m];
                }
        }

        return [true, 'ok'];
    }

    /**
     * @param $id
     * @param $type
     * @param $cnf
     * @param string $aids
     * @return array
     * @throws Exception
     * 更新数据
     */
    private function __update($id, $type, $cnf, string $aids)
    {
        $tb         = self::tbName();
        $db         = by::dbMaster();

        switch ($type) {
            case self::TYPE['WITH_FREE']:
            case self::TYPE['DEFAULT']:
                $db->createCommand()->update($tb, ['cnf'=>$cnf], ['id' => $id])->execute();
                break;

            default :
                $db->createCommand()->update($tb, ['cnf'=>$cnf], ['id' => $id])->execute();

                list($s, $m) = by::model('OfCfgModel', 'goods')->SaveLog($id, $type, $aids);

                if (!$s) {
                    return [false, $m];
                }
        }

        return [true, 'ok'];
    }

    /**
     * @param $id
     * @return array|false
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id)
    {
        $id      = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis   = by::redis('core');
        $r_key   = $this->__getOneByIdKey($id);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {

            $tb      = $this::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id'=>$id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($r_key, json_encode($aData), ['EX' => 1800]);
        }

        return $aData;
    }

    /**
     * @param $type
     * @return array|false
     * @throws \yii\db\Exception
     * 根据type获取数据（查满免及默认运费）
     */
    public function GetOneByType($type)
    {
        $type      = CUtil::uint($type);

        if ( $type == self::TYPE['AREA'] ) {
            return [];
        }

        $redis   = by::redis('core');
        $r_key   = $this->__getOneByTypeKey($type);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb      = $this::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `type`=:type LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':type'=>$type])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($r_key,json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param $tab
     * @return array
     * @throws Exception
     * 后台用-暂无缓存
     */
    public function GetList($tab)
    {
        $tb             = self::tbName();

        switch ($tab) {
            case 1  : $where = " `type` IN (1,2,3)";

                break;
            case 5  : $where = " `type` = 5";

                break;
            default : $where = " `type` = 4";

                break;
        }

        $fields         = implode("`,`",$this->tb_fields);
        $sql            = "SELECT `{$fields}` FROM {$tb} WHERE {$where} ORDER BY `type` LIMIT 200";

        $aData          = by::dbMaster()->createCommand($sql)->queryAll();

        $data           = [];
        $return         = [];
        foreach($aData as $k => $val) {
            $val['type'] < self::TYPE['DIS_AREA'] && $val['cnf']   = by::Gtype0()->totalFee($val['cnf'], 1);

            if($tab != 1 && $val['cnf'] === '0.00'){
                $val['cnf'] = '';
            }

            if ($val['type'] == self::TYPE['WITH_FREE']) {
                $return['with_free'] = $val;
                unset($aData[$k]);
                continue;
            }

            $aids = $temp = [];
            if ($val['type'] >= self::TYPE['AREA']) {
                $of_cfg               = by::model('OfCfgModel', 'goods')->GetListByOfId($val['id']);
                foreach ($of_cfg as $v) {
                    $temp[$v['pid']][] = $v;
                }

                $address = '';
                $pData   = by::model('AreaModel', MAIN_MODULE)->GetList();
                $pData   = array_column($pData, 'name', 'id');
                foreach($temp as $pid => $v1) {
                    $aids[] = [
                        'province' => $pid,
                        'city'     => array_column($v1, 'cid'),
                    ];

                    $address   .= $pData[$pid].'【';
                    $cData      = by::model('AreaModel', MAIN_MODULE)->GetList($pid);
                    $cData      = array_column($cData, 'name', 'id');
                    $cname      = array_map(function ($v2) use($cData) {
                        return $cData[$v2['cid']];
                    }, $v1);

                    $address    .= implode('、', $cname). '】';

                }

            }
            $val['aids']    = $aids;
            $val['address'] = $address ?? '';
            $data[]         = $val;
        }

        $return['list']   = $data;

        return $return;
    }

    /**
     * @param int $id
     * @return array
     * @throws Exception
     * 删除数据
     */
    public function Del(int $id)
    {
        $db         = by::dbMaster();
        $tb         = self::tbName();

        $aInfo      = $this->GetOneById($id);
        if (empty($aInfo)) {
            return [false, '数据不存在'];
        }

        $type       = $aInfo['type'];
        if ($type != self::TYPE['AREA'] && $type != self::TYPE['DIS_AREA'] && $type != self::TYPE['EXPLORE'] ) {
            return [false, '默认数据不可删除'];
        }

        $trans      = $db->beginTransaction();

        try {
            $db->createCommand()->delete($tb, ['id' => $id])->execute();

            by::model('OfCfgModel', 'goods')->Del($id, $aInfo['type']);

            $trans->commit();

            $this->__delCache($aInfo['type']);

            return [true, 'ok'];
        } catch (\Exception $e) {

            $trans->rollBack();
            return [false, $e->getMessage()];
        }

    }

    /**
     * @param $user_id
     * @param $address_id
     * @param $price :价格分
     * @param array $other
     * @return int
     * @throws Exception
     * 根据地址id获取运费
     */
    public function GetFreight($user_id, $address_id, $price, $other=[])
    {
        $freight    = 0;
        $cnf        = $this->GetOneByType(self::TYPE['WITH_FREE']);
        $free       = $cnf['cnf'] ?? 0;
        //免运费
        if ($free > 0 && bccomp($price, $free) >= 0) {
            return $freight;
        }

        //其它免邮参数-
        if (!empty($other)) {
            //todo 兑换券配置免邮
            $card_type = $other['card_type'] ?? 0;
            $market_id = $other['market_id'] ?? 0;

            $mMarketCg = by::marketConfig();

            if ($card_type == $mMarketCg::TYPE['voucher'] && $market_id > 0) {
                $mcInfo         = by::marketConfig()->getOneById($market_id, 1);
                $freight_free   = $mcInfo['freight_free'] ?? 0;

                if ($freight_free == 1) {
                    return 0;
                }
            }

        }

        if (empty($address_id)) {
            $cnf        = $this->GetOneByType(self::TYPE['DEFAULT']);
            return (int)$cnf['cnf'];
        }

        $address  = by::Address()->GetOneAddress($user_id, $address_id);

        $cid      = $address['cid'] ?? 0;

        $aData    = by::model('OfCfgModel', 'goods')->GetOneByCid(self::TYPE['AREA'], $cid);

        if (empty($aData)) {
            $cnf        = $this->GetOneByType(self::TYPE['DEFAULT']);

        } else {
            $cnf        = $this->GetOneById($aData['of_id']);
        }

        return (int)$cnf['cnf'];
    }

    /**
     * @param $user_id
     * @param $address_id
     * @return array
     * @throws Exception
     * 根据地址id判断是否可配送
     */
    public function IsDis($user_id, $address_id)
    {
        $address  = by::Address()->GetOneAddress($user_id, $address_id);

        $cid      = $address['cid'];

        $aData    = by::model('OfCfgModel', 'goods')->GetOneByCid(self::TYPE['DIS_AREA'], $cid);

        if (empty($aData)) {
            return [true, 'ok'];
        }

        $cnf  = $this->GetOneById($aData['of_id']);

        return [false, $cnf['cnf']];
    }


}
