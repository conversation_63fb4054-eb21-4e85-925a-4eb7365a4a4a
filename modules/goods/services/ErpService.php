<?php
/**
 * Created by CP
 * User: CP
 * Date: 2024/05/18
 * Time: 10:55
 * Erp 回调 接口文档  http://adaptor.open.baison.net/#/doc
 */
namespace app\modules\goods\services;


use app\components\Crm;
use app\components\ErpOms;
use app\components\ExpressTen;
use app\jobs\TryBuyUserPathJob;
use app\jobs\TryBuyUserTagJob;
use app\jobs\TryOrderCompleteJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\OgoodsModel;
use app\modules\main\services\DataService;
use yii\db\Exception;

class ErpService {

    /*******************************************oms回写START***********************************************************/

    const REWRITE_METHOD_LIST = ['adaptor.logistics.offline.send','adaptor.ag.sendgoods.cancel','adaptor.refund.returngoods.agree','adaptor.items.stock.update','adaptor.ag.logistics.warehouse.update'];


    /**
     * @throws Exception
     */
    public function rewrite($method, $data)
    {
        if(empty($method)|| !in_array($method,self::REWRITE_METHOD_LIST)){
            return [false,'METHOD:'.$method.'不正确！'];
        }
        list($s,$msg) = $this->run($method,$data);
        return [$s,$msg];
    }

    /**
     * @throws Exception
     */
    public function run($method, $data)
    {
        switch ($method){
            case 'adaptor.logistics.offline.send':
                return $this->orderSendUpdate($data);
            case 'adaptor.ag.sendgoods.cancel':
                return $this->cancelOrderUpdate($data);
            case 'adaptor.refund.returngoods.agree':
                return $this->refundOrderAgree($data);
            case 'adaptor.ag.logistics.warehouse.update':
                return $this->refundWarehouseAgree($data);
            case 'adaptor.items.stock.update':
                return $this->syncStock($data);
            default:
                return [false,'method 不存在'];
        }
    }



    /**
     * @param $data
     * @return array|void
     * @throws Exception
     * 发货回写
     * A101-发货回写
     */
    public function orderSendUpdate($data, $ogid = 0)
    {
        //订单信息校验
        $checkFields = ['deal_code','shipping_code','shipping_sn'];
        foreach ($checkFields as $field){
            if(empty($data[$field])){
                return [false,$field.'参数不存在'];
            }
        }

        $order_no = $data['deal_code'];
        $shipping_code = $data['shipping_code'];
        $shipping_sn = $data['shipping_sn'];

        $shippingInfo = [
            'shipping_code' => $shipping_code,
            'shipping_sn'   => $shipping_sn,
            'shipping_name' => $data['shipping_name'] ?? '',
        ];

        //订单信息
        $orderInfo = by::Omain()->getInfoByNo($order_no);
        if(empty($orderInfo)){
            return [false,'订单信息不存在'];
        }

        //更新数据
        list($s,$msg) = $this->_shellOrder($order_no,$orderInfo,$shippingInfo,$ogid);
        return [$s,$msg];

    }
    
    
    /**
     * @param $order_no
     * @param $orderInfo
     * @param $shippingInfo
     * @param int $ogid 订单商品ID，如果不为空必然后台手动发货传入
     * @return array
     * 发货回写方法
     */
    protected function _shellOrder($order_no,$orderInfo,$shippingInfo,$ogid=0): array
    {

        $status = $orderInfo['status'] ?? 0;
        $user_id = $orderInfo['user_id'] ?? 0;

        $mOmain = by::Omain();
        $mOad   = by::Oad();
        $db     = by::dbMaster();

        $shipping_time_fh = intval(START_TIME);
        $shipping_sn = $shippingInfo['shipping_sn'];
        $shipping_code = $shippingInfo['shipping_code'];
        $shipping_name = $shippingInfo['shipping_name'];

        $trans  = $db->beginTransaction();

        try {
            //更改订单状态，所有商品都发货完成订单才转为待收货，三方商品和品牌商品
            

            $save = [
                'mail_no'        => $shipping_sn,
                'express_code'   => $shipping_code,
                'express_name'   => $shipping_name,
            ];

            // 手动发货更新订单商品表发货数据
            if (! empty($ogid)) {
                by::Ogoods()->UpdateData($ogid, $user_id, $order_no, $save);
            } else {
                // 非手动发货才更新订单地址表数据
                $mOad->UpdateData($order_no, $save);
            }
            
            // 查询所有订单商品
            $oGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
            $can_next = true;
            $has_third = false;
            $third_count = 0; //优选订单商品数量
            foreach ($oGoods as $oGoodsItem) {
                $gid = (int) ($oGoodsItem['gid'] ?? 0);
                $goodsRes = by::Gmain()->GetOneByGid($gid);
                $isAuthDelivery = $goodsRes['is_auto_delivery'] ?? 0; //是否自动发货，0=否（不推送E3+，需要手动发货），1=是（推送E3+）
                if ($oGoodsItem['is_third'] == 1) {
                    $third_count++;
                }
                if ($oGoodsItem['is_third'] == 1 && $isAuthDelivery == 0) {//三方商品的，手动发货才检查
                    $has_third = true;
                    // 优选有一个没发货就不能继续更新订单状态
                    if (empty($oGoodsItem['mail_no']) || empty($oGoodsItem['express_code']) || empty($oGoodsItem['express_name'])) {
                        $can_next = false;
                    }
                }
            }

            // 1. 先手动发货
            //      1.1 订单中只有优选商品时，其他优选商品都没发货或部分没发货，此时不会更新订单状态，依然是待发货
            //      1.2 订单中只有优选商品时，其他优选商品也都已发货，此时会更新订单状态，改为已发货
            //      1.3 订单中普通和优选商品都存在时，其他商品都没发货或部分没发货，此时不会更新订单状态，依然是待发货
            //      1.4 订单中普通和优选商品都存在时，其他优选商品都发货了，此时需要判断普通商品是否发货，如果也发货了，则订单状态可以更新，如果没发货，订单状态不变

            // 订单中普通和优选商品都存在时，判断非优选商品是否发货
            if (! empty($ogid) && count($oGoods) > $third_count) {
                // 判断非优选商品是否也发货
                $mOadRes = $mOad->GetOneByOrderNo($order_no);
                if (! empty($mOadRes) && (! empty($mOadRes['mail_no']) && ! empty($mOadRes['express_code']) && ! empty($mOadRes['express_name']))) {
                    // 非优选也发货了，则可以更新订单状态
                    $can_next = true;
                } else {
                    $can_next = false;
                    $mOad->DelCache($order_no);
                }
            }

            // 2. 先E3+发货
            //      2.1 订单中只有普通商品时，按原逻辑走，E3+发货就会更新订单状态
            //      2.2 订单中存在优选商品时，判断其他优选商品是否都发货，如果没有都发货，此时不更新订单状态，否则可以更新订单状态

            // 将订单状态改为下一步操作
            if ($can_next) {
                //更改订单状态
                $next_st     = $mOmain->SetOrderStatus($status, $mOmain::ORDER_STATUS['WAIT_RECEIVE']);
                list($s, $m) = $mOmain->SyncInfo($user_id, $order_no, $next_st, ['stime'=>$shipping_time_fh], ['stime'=>$shipping_time_fh]);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();
                // //todo 订单同步crm
                // Crm::factory()->push($user_id,'order',['user_id'=>$user_id,'order_no'=>$order_no]);
                // Crm::factory()->push($user_id,'orderLine',['user_id'=>$user_id,'order_no'=>$order_no]);

                $shipping_code = ExpressTen::factory()->getKD100ExpressCode($shipping_code);


                //todo 加入快递100订阅
                //获取收货人电话
                $oAdInfo = $mOad->GetOneByOrderNo($order_no);
                $receiver_mobile = $oAdInfo['phone'] ?? '';
                
                for($i=0; $i<3; $i++) {
                    list($s, $m) = ExpressTen::factory()->poll($shipping_sn, $shipping_code, $order_no, $receiver_mobile);
                    if ($s) {
                        break;
                    }
                    sleep(1);
                }
                
                //todo 如果是先試後買商品，則从E3+获取SN号等信息
                $type = $orderInfo['type'] ?? 0;
                if($type == by::Omain()::USER_ORDER_TYPE['BAT']){
                    list($status,$snInfo) = ErpOms::factory()->run('querySn',['order_nos'=>[$order_no],'user_id'=>$user_id]);
                    $snArr = array_column($snInfo,'sn','dealCode');
                    $sn = $snArr[$order_no] ?? '';
                    if($sn){
                        $s = byNew::UserOrderTry()->SaveLog(['user_id'=>$user_id,'order_no'=>$order_no,'sn'=>$sn]);
                        if(!$s){
                            CUtil::debug($order_no.'|'.$sn.'|'.time().'|订单SN号保存失败！','err.detail.order.update');
                            return [false,'订单SN号保存失败！'];
                        }
                    }
                    
                    // 发货回写用户路径
                    \Yii::$app->queue->delay(DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                        'user_id'  => $user_id,
                        'scene'    => 'send',
                        'order_no' => $order_no,
                    ]));
                    
                    \Yii::$app->queue->push(new TryBuyUserTagJob([
                        'userId'   => $user_id,
                        'tagScene' => 'SEND',
                    ]));
                }
            } else {
                $trans->commit();
                
                $shipping_code = ExpressTen::factory()->getKD100ExpressCode($shipping_code);
                //todo 加入快递100订阅
                //获取收货人电话
                $oAdInfo = $mOad->GetOneByOrderNo($order_no);
                $receiver_mobile = $oAdInfo['phone'] ?? '';
                
                for($i=0; $i<3; $i++) {
                    list($s, $m) = ExpressTen::factory()->poll($shipping_sn, $shipping_code, $order_no, $receiver_mobile);
                    if ($s) {
                        break;
                    }
                    sleep(1);
                }
            }
            byNew::SalesCommissionModel()->patch(['order_no'=>$order_no],['status'=>400,'utime'=>time()]);
            
            return [true, 'OK'];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.detail.order.update');
            return [false, '发货回写失败！'];
        }

    }


    /**
     * @param $data
     * @return array
     * @throws Exception
     * 取消发货回写
     * A103-取消发货AG回写
     */
    public function cancelOrderUpdate($data)
    {
        //参数校验
        $checkFields = ['refund_id','deal_code','status'];
        foreach ($checkFields as $field){
            if(empty($data[$field])){
                return [false,$field.'参数不存在'];
            }
        }
        $refund_no = $data['refund_id'] ?? 0;
        //查出退款订单主信息
        if($data['status'] == 'SUCCESS'){
            list($s,$m) = $this->_rback($refund_no);
            return [$s,$m];
        }

        return [true,'OK'];
    }




    /**
     * @param $data
     * @return array
     * @throws Exception
     * A104-同意退货回写 当前业务直接返回正确
     */
    public function refundOrderAgree($data): array
    {
        //参数校验
        return [true,'ok'];

    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     * ( A105-退货入库AG回写（第三方商城按照接口标准进行订阅） )   
     */
    public function refundWarehouseAgree($data):array
    {
        //参数校验
        $checkFields = ['refund_id'];
        foreach ($checkFields as $field){
            if(empty($data[$field])){
                return [false,$field.'参数不存在'];
            }
        }
        $refund_no = $data['refund_id'] ?? 0;
        list($s,$m) = $this->_rback($refund_no);
        return [$s,$m];
    }




    /**
     * @param $refund_no
     * @return array
     * @throws Exception
     */
    protected function _rback($refund_no)
    {
        $refundMainInfo = by::OrefundMain()->GetInfoByRefundNo($refund_no);
        if(empty($refundMainInfo)){
            return [false,$refund_no.'退款订单不存在！'];
        }
        //保存当前退款单已退单
        $user_id = $refundMainInfo['user_id'] ?? 0;
        $save = ['rback' => 1];
        list($s, $m)    = by::Orefund()->UpdateData($user_id, $refund_no, $save);
        if (!$s) {
            return [false, $m];
        }

        //1.判断是否为先试后买订单（更新退货到货时间） 
        // 先试后买订单直接完结
        $tryOrderInfo = byNew::UserOrderTry()->GetOneInfo(
            [CUtil::buildCondition('refund_no', '=', $refund_no),CUtil::buildCondition('user_id', '=', $user_id)]
        );
        $noNeed = [byNew::UserOrderTry()::TRY_STATUS['FINISHED'],byNew::UserOrderTry()::TRY_STATUS['DEDUCTED'],byNew::UserOrderTry()::TRY_STATUS['DEDUCT_FAIL']];

        if($tryOrderInfo && !in_array($tryOrderInfo['try_status'],$noNeed)){
            //1.到货改为退机状态
            //2.退货成功异步直接完结
            \Yii::$app->queue->push(new TryOrderCompleteJob([
                'input' => [
                    'user_id' => $user_id,
                    'order_no' => $tryOrderInfo['order_no'],
                    'refund_no' => $refund_no,
                    'status' => by::OrefundMain()::STATUS['P_PASS'],
                    'a_reason' => 'E3+回调，先试后买订单退货成功直接完结~',
                    'force' => 1,
                    'is_complete' => 1,
                    'is_check' => false,
                ]
           ]));
        }
        return [true,'OK'];
    }


    /**
     * @param $data
     * @return array
     * @throws Exception
     * 回写同步库存
     * A109-库存同步
     */
    public function syncStock($data)
    {
        //参数校验
        $checkFields = ['data'];
        foreach ($checkFields as $field){
            if(empty($data[$field])){
                return [false,$field.'参数不存在'];
            }
        }

        $stockData = $data['data'];
        if(!is_array($stockData)){
            return [false,'库存数据格式不正确！'];
        }

        $return = [];
        foreach ($stockData as $stock) {
            $sku      = $stock['sku_id'] ?? 0;
            $quantity = $stock['quantity'] ?? 0;
            if ($sku) {
                //更新库存
                // list($s, $msg) = $this->_updateStock($sku, $quantity);
                //库存不受E3+控制 无需更改库存 直接返回true
                $return[] = [
                    'success'    => true,
                    'message'    => $sku,
                    'retry_flag' => false,
                    'num_iid'    => $stock['num_iid'] ?? '',
                    'sku_id'     => $stock['sku_id'] ?? '',
                    'quantity'   => $stock['quantity'] ?? '',
                    'is_item'    => $stock['is_item'] ?? 0,
                    'type'       => $stock['type'] ?? 1
                ];
            }
        }

        return [true,$return];
    }


    /**
     * @throws Exception
     */
    protected function _updateStock($sku, $stock): array
    {
        //判断是否是积分商品不发货的 过滤
        $gidInfos = by::GoodsPointsModel()->GetList(['is_send'=>2],1,0);
        $gids = array_column($gidInfos,'gid');
        $skus = by::GoodsPointsPriceModel()->GetSkusByGids($gids);
        if(in_array($sku,$skus)) return [true,'OK'];

        by::GoodsStockModel()->UpdateStock(0, 0, $stock, 'SET', true, by::GoodsStockModel()::SOURCE['OTHER'], $sku);
        //更新自定义价格库存
        by::Gini()->SyncIniStock($sku, $stock);

//        //根据sku获取商品ID
//        $mainInfo = by::Gmain()->GetOneBySku($sku);
//        if($mainInfo){
//            $gid = $mainInfo['id'] ?? 0;
//            $gData  = by::Gmain()->GetOneByGidSid($gid, 0, true,false, 0);
//            if($gData && isset($gData['is_presale']) && !empty($gData['is_presale'])) return [true,'ok'];
//            if($gid){
//                //更新库存
//                by::Gstock()->UpdateStock($gid, 0, $stock, 'SET');
//
//                //更新自定义价格库存
//                by::Gini()->SyncIniStock($sku,$stock);
//            }
//            $sid = 0;
//        }else{
//            //多规格判断
//            $gSpecs = by::Gspecs()->GetOneBySku($sku);
//            $gid = $gSpecs['gid'] ?? 0;
//            $sid = $gSpecs['id'] ?? 0;
//            $gData  = by::Gmain()->GetOneByGidSid($gid, $sid, true,false, 0);
//            if($gData && isset($gData['is_presale']) && !empty($gData['is_presale'])) return [true,'ok'];
//            if($gid && $sid){
//                //更新库存
//                by::Gstock()->UpdateStock($gid, $sid, $stock, 'SET');
//
//                //更新自定义价格库存
//                by::Gini()->SyncIniStock($sku,$stock);
//            }else{
//                return  [1,$sku];
//            }
//        }
        return [true,$sku];
    }



    /*******************************************oms回写END***********************************************************/


}
