<?php
/**
 * 订单统计类
 */
namespace app\modules\goods\services;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

class StatisticService {


    private function __statisticGoodsKey($gid): string
    {
        return AppCRedisKeys::statisticGoods($gid);
    }

    /**
     * @throws \yii\db\Exception
     */
    public function statisticGoods($gid,$cache=true): array
    {
        $redis       = by::redis('core');
        $r_key       = $this->__statisticGoodsKey($gid);
        $aJson       = $cache ? $redis->get($r_key) : false;
        $aData       = (array)json_decode($aJson,true);
        if($aJson === false) {
            $aData  = $this->_statisticOrdersByGid($gid);
            $aData1 = $this->_statisticDordersByGid($gid);
            $aData  = array_merge($aData, $aData1);
            $redis->set($r_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }
        if (empty($aData)) {
            return [];
        }
        return $aData;
    }


    /**
     * @throws \yii\db\Exception
     */
    private function _statisticOrdersByGid($gid): array
    {
        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);

        //构造返回数据
        $data = [
            'tail_paid_num'   => 0,
            'tail_refund_num' => 0,
        ];

        foreach ($years as $year) {
            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_ocfg   = by::Ocfg()::tbNameByTime($ctime);
            $tb_main   = by::Omain()::tbName($ctime);
            $wait_send = by::Omain()::ORDER_STATUS['WAIT_SEND'];
            $refund = by::Omain()::ORDER_STATUS['RERUNDED'];

            $sql = " SELECT COUNT(*) FROM {$tb_main} as `main` INNER JOIN ( SELECT  `order_no` FROM {$tb_ocfg} WHERE `gid` = {$gid}) 
             as `ocfg` ON `main`.`order_no` = `ocfg`.`order_no` WHERE `main`.`type`= 2 AND `main`.`status` >= {$wait_send}";


            $num = $db->createCommand($sql)->queryScalar();
            $data['tail_paid_num'] += intval($num);

            $sql1 = " SELECT COUNT(*) FROM {$tb_main} as `main` INNER JOIN ( SELECT  `order_no` FROM {$tb_ocfg} WHERE `gid` = {$gid}) 
             as `ocfg` ON `main`.`order_no` = `ocfg`.`order_no` WHERE `main`.`type`= 2 AND `main`.`status` = {$refund}";

            $num1 = $db->createCommand($sql1)->queryScalar();
            $data['tail_refund_num'] += intval($num1);

        }
        return $data;
    }


    /**
     * @param $gid
     * @return array
     * @throws \yii\db\Exception
     * 取出定金订单数据
     */
    private function _statisticDordersByGid($gid){

        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year-1,$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);

        //构造返回数据
        $data = [
            'deposit_paid_num'   => 0,
            'deposit_refund_num' => 0,
        ];
        foreach ($years as $year) {
            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_main   = by::Odeposit()::tbName($ctime);
            $wait_send = by::Odeposit()::STATUS['WAIT_SEND'];
            $refund = by::Odeposit()::STATUS['RERUNDED'];

            $sql = " SELECT COUNT(*) FROM {$tb_main}  WHERE `gid`={$gid}  AND `status` >= {$wait_send}";

            $num = $db->createCommand($sql)->queryScalar();
            $data['deposit_paid_num'] += intval($num);

            $sql1 = " SELECT COUNT(*) FROM {$tb_main} WHERE `gid`={$gid} AND `status` = {$refund}";
            $num1 = $db->createCommand($sql1)->queryScalar();
            $data['deposit_refund_num'] += intval($num1);

        }
        return $data;



    }


}
