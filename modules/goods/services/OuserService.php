<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 订单表
 */
namespace app\modules\goods\services;


use app\components\Crm;
use app\components\ExpressTen;
use app\jobs\RefundGiftCardJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\UserCardModel;
use app\modules\main\services\GiftCardService;
use app\modules\wares\models\GiftCardExpendRecordModel;
use app\modules\wares\models\GiftUserCardsModel;
use yii\db\Exception;

class OuserService {


    /**
     * @param $user_id
     * @param $info
     * @param $arr
     * @return array[]
     * @throws Exception
     * 获取用户可用的积分优惠券
     */
    public function getUserCanUse($user_id,$info,$arr)
    {
        $mPoint         = by::point();
        $aid = CUtil::uint($arr['aid'] ?? 0);
        !YII_ENV_PROD && CUtil::debug(json_encode($aid),'order_info');
        if(!empty($aid)){
            $address = by::Address()-> GetOneAddress($user_id,$aid);
            $info['address']    = $address;
        }

        list($s,$budgetInfo) = $this->_budgetOrderPrice($user_id,$info,$arr);
        if(!$s){
            return [false,$budgetInfo];
        }

        $coin_type            = $budgetInfo['coin_type'] ?? 0;
        $coin                 = $budgetInfo['coin'] ?? 0;
        $fprice               = $budgetInfo['fprice'] ?? 0;
        $real_price           = $budgetInfo['real_price'] ?? 0;
        $price                = $budgetInfo['price'] ?? 0;
        $tcoin                = $budgetInfo['tcoin'] ?? 0;
        $can_coin             = $budgetInfo['can_coin'] ?? 0;
        $coin_price           = $budgetInfo['coin_price'] ?? 0;
        $note                 = $budgetInfo['note'] ?? 0;
        $gift_card_price      = $budgetInfo['gift_card_price'] ?? 0;
        $gift_card_ids        = $budgetInfo['gift_card_ids'] ?? [];
        $giftCardCanUseNumber = $budgetInfo['gift_card_can_use_number'] ?? [];


        //封在$info
        $info['coin']            = $coin;
        $info['fprice']          = sprintf('%.2f', $fprice);
        $info['price']           = sprintf('%.2f', $price);
        $info['coin_price']      = sprintf('%.2f', $coin_price);
        //$info['gift_card_price'] = sprintf('%.2f', $gift_card_price);
        $info['real_price']      = $real_price;
        if ($info['status'] >= by::Omain()::ORDER_STATUS['WAIT_SEND']) {
            $info['real_price'] = bcadd($info['real_price'], $info['deposit_price'] ?? 0, 2);
        }
        !empty($note) && $info['note'] = $note;

        $rate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
        
        $info['use_coin'] = [
            'tcoin'      => $tcoin,
            'can_coin'   => $can_coin,
            'coin_price' => $coin_price,
            'rate'       => $rate,
            'coin_type'  => $coin_type,
            'coin'       => $coin
        ];

        $info['gift_card'] = [
            'can_number'    => $giftCardCanUseNumber,
            'use_amount'    => $gift_card_price,
            'gift_card_ids' => $gift_card_ids,
        ];
        return [true,$info];
    }
    /**
     * @param $canCoin
     * @param $price
     * @param $rate
     * @return mixed
     * 获取用户实际可使用积分
     */
    private function __canCoinByBenefit($canCoin, $price, $rate,$user_id)
    {
        // 积分中心返回比例  v1:10 v2:20 v3:30 诸如此类...
        $memberRate = bcdiv($rate, 100, 2); // 将比例转换为小数形式
        // 商城后台配置抵扣系数
        $deductionRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
        
        // 计算实际可使用的积分比例
        $rate = bcmul($memberRate, $deductionRate);
        // 1-100 积分抵扣比例  原本是100  现在改为10  原本逻辑为 100 抵1块钱  现在 10抵1块钱 原本概率为 30
        // 计算允许的最大可使用积分额
        $deductionAmount = CUtil::uint(floatval($price) * $rate);

        // 返回可用的最小抵扣积分值
        return min($canCoin, $deductionAmount);
    }


    /**
     * @param $user_id
     * @param $info
     * @param $arr
     * @return array|void
     * @throws Exception
     *
     */
    public function _budgetOrderPrice($user_id,$info,$arr)
    {
        $aid            = CUtil::uint($arr['aid'] ?? 0);  //地址id
        $note           = $arr['note'] ?? '';             //订单备注
        $coupon_id      = intval($arr['coupon_id'] ?? -1);
        $coin           = CUtil::uint($arr['coin'] ?? 0);
        $coin_type      = intval($arr['coin_type'] ?? 0);       //回传给客户端（0默认 1使用 2不使用）
        $getChannel     = CUtil::uint($arr['get_channel'] ?? 0);//优惠券来源控制
        $order_no       = $arr['order_no'] ?? 0;                //优惠券来源控制
        $giftCardIdsStr = $arr['gift_card_ids'] ?? '';
        $isUseGiftCard  = $arr['is_use_gift_card'] ?? 1;
        $giftCardIds    = empty(trim($giftCardIdsStr)) ? [] : explode(',', $giftCardIdsStr);//使用礼品卡ID


        //根据传值获取卡券类型
        $coupon_id = CUtil::uint($coupon_id);
        if ($coupon_id > 0) {
            $user_card_info = by::userCard()->getCardById($user_id, $coupon_id, $getChannel);
        }

        $card_type  = $user_card_info['type'] ?? 0;

        $tprice     = 0;
        $than_price = 0;    //订单最少付款额
        $expand_price = 0;

        $goods = $info['goods'] ?? [];
        if(empty($goods)){
            return [false,'商品不能为空！'];
        }

        foreach ($goods as $key => $val) {
            $num                = CUtil::uint($val['num'] ?? 0);
            if($num <= 0){
                return [false,'商品数量不能为0'];
            }

            if ($card_type == UserCardModel::TYPE['voucher']) {
                if ($num != 1) {
                    return [false,'兑换券仅可兑换单个商品'];

                }
            }
            $expand_price = (isset($val['is_presale']) && $val['is_presale']) ? bcadd($expand_price, ($val['presale_info']['expand_price'] ?? 0) * $num, 2) : 0;

            $tprice             = bcadd($tprice, $val['uprice']*$num, 2);
            $than_price         = bcadd($than_price, bcmul($num, 0.01, 2), 2);

        }

        $price = bcsub($tprice,$expand_price,2);

        $mPoint         = by::point();
        $tcoin          = $mPoint->get($user_id); //总积分
        //判断当前订单的所用积分
        $userCoin = by::Ouser()->GetInfoByOrderId($user_id,$order_no);
        $userCanCoin = $userCoin['coin'];
        $tcoin = bcadd($tcoin,$userCanCoin);


        $can_coin = $this->_canCoin($user_id,$tcoin,$price,$than_price,$card_type);

        $coin_price = 0;
        if($coin_type == 1 && $coin > 0){//使用积分
            if ($coin > $can_coin) {
                return [false, '积分使用超限'];
            }

            $coin_price = $mPoint->convert($coin,'RMB',$user_id);
            $price      = bcsub($price, $coin_price, 2);

        }else{//不使用积分
            $price      = $info['price'];
            if($coin_type != 1){
                if($coin_type == 2){
                    $infocoin = CUtil::uint($info['coin'] ?? 0);
                    if($infocoin > 0 ){
                        $price      = bcadd($price, $mPoint->convert($infocoin,'RMB',$user_id), 2);
                    }
                    $coin       = 0;
                    $coin_price = 0;
                }else{
                    $coin       = $info['coin'] ?? 0;
                    $coin_price = $info['coin_price'] ?? 0;
                    $price      = bcsub($price, $coin_price, 2);
                }
            }
        }

        if($aid >0){
            //地址校验
            $ad = by::Address()->GetOneAddress($user_id,$aid);
            if (empty($ad)) {
                return [false, '请添加或选择收货地址'];
            }

            $shippingData = [];
            foreach ($info['goods']??[] as $key => $v) {
                $gInfo          = by::Gmain()->GetOneByGidSid($v['gid'], $v['sid'], true, true);
                $shippingData[] = [
                        'gid'              => $v['gid'],
                        'sid'              => $v['sid'],
                        'num'              => $v['num'],
                        'price'            => $gInfo['spec']['price'] ?? $gInfo['price'],
                        'is_free_shipping' => $gInfo['is_free_shipping'] ?? 0,
                ];
            }

            $otprice= by::Gtype0()->totalFee($price);
            //使用了礼品卡并且金额支付金额为0免邮费
            if( !empty($giftCardIds) && $otprice == 0){
                $fprice = 0;
            }else{
                //运费权益
                $fprice = by::Ouser()->shippingPrice($shippingData, $user_id, $aid, $freight_other ?? [], $price);
            }
        }else{
            $fprice = $info['fprice'] ?? 0;
        }

        $useAmount    = 0;
        $giftCardType = 0;
        //使用礼品卡
        if ($isUseGiftCard == 1  && !empty($giftCardIds)) {
            if ($giftCardIds) {
                foreach ($giftCardIds as $val) {
                    $giftCardInfo = byNew::GiftUserCards()->userGiftCardInfo($val);

                    // 如果卡不存在则跳过
                    if (empty($giftCardInfo)) {
                        continue;
                    }

                    // 检查礼品卡状态和过期时间
                    if ($giftCardInfo['expire_time'] < time() || in_array($giftCardInfo['status'], [GiftUserCardsModel::STATUS['FROZEN'], GiftUserCardsModel::STATUS['SHARED'], GiftUserCardsModel::STATUS['USED']])) {
                        return [false, '礼品卡已过期或已使用，请检查'];
                    }

                    // 金额转为元
                    $giftCardInfo['amount'] = bcdiv($giftCardInfo['amount'], 100, 2);

                    if ($giftCardInfo['type'] == 2) {
                        $giftCardType = 2;
                        return [false, '尾款订单不允许使用指定兑换卡~~'];
                    } else {
                        $useAmount    = bcadd($useAmount, $giftCardInfo['amount'], 2);
                        $giftCardType = 1;
                    }
                }

                //优惠金额
                $useAmount = min($price, $useAmount);
                //实付金额
                $price = bcsub($price, $useAmount, 2);
            }

        } elseif($isUseGiftCard == 0 && !empty($giftCardIds)) {
            //不使用礼品卡
            $price     = $info['price'];
            $useAmount = $info['gift_card_price'];
        }


        //礼品卡可用张数  不可使用指定兑换卡
        $gids = array_column($goods, 'gid');
        list($s, $ret) = GiftCardService::getInstance()->getUserCardList($user_id, 1, $gids,1);
        $giftCardList         = $ret['list'] ?? [];
        $giftCardCanUseNumber = 0;
        //返回可用列表 count 张数 尾款订单不可用指定兑换卡 所以不算在可用张数内
        foreach ($giftCardList ?? [] as $item) {
            if ($item['back'] == 1) {
                $giftCardCanUseNumber = bcadd($giftCardCanUseNumber, 1);
            }
        }



        $real_price = bcadd($price, $fprice, 2);

        if (empty($giftCardIds) && bccomp($real_price, 0, 2) <= 0 && $card_type != by::userCard()::TYPE['voucher']) {
            return [false, '网络繁忙~~'];
        }

        return [true, [
            'price'                    => $price,
            'tprice'                   => $tprice,
            'aid'                      => $aid,
            'than_price'               => $than_price,
            'expand_price'             => $expand_price,
            'coin_price'               => $coin_price,
            'gift_card_price'          => $useAmount,
            'gift_card_ids'            => $giftCardIds,
            'gift_card_type'           => $giftCardType,
            'gift_card_can_use_number' => $giftCardCanUseNumber,
            'coin'                     => $coin,
            'tcoin'                    => $tcoin,
            'can_coin'                 => $can_coin,
            'coin_type'                => $coin_type,
            'real_price'               => $real_price,
            'fprice'                   => $fprice,
            'note'                     => $note,
        ]];
    }

    /**
     * @param $user_id
     * @param $tcoin
     * @param $price
     * @param $than_price
     * @param $card_type
     * @return int|mixed|string
     * 可使用积分
     */
    private function _canCoin($user_id,$tcoin,$price,$than_price,$card_type=0){
        $mPoint =  by::point();
        //积分权益
        $pointRate = by::memberCenterModel()->getPointCrash($user_id);
        
        //可用积分必须小于商品数*num
        $can_price = bccomp($price, $than_price, 2) == 1 ? bcsub($price, $than_price, 2) : 0;
        $can_coin  = $mPoint->convert($can_price, 'POINT',$user_id);
        $can_coin  = bccomp($tcoin, $can_coin) >= 0 ? $can_coin : $tcoin;
        if ($card_type == by::userCard()::TYPE['voucher']) {
            $can_coin = 0; //使用兑换券可用积分为0
        }
        //可使用的积分
        // $lockCrmSend     = CUtil::getConfig('lockCrmSend','member',MAIN_MODULE) ?? false;
        // $lockCrmSend && $can_coin = $this->__canCoinByBenefit($can_coin,$price,$pointRate);
        $can_coin = $this->__canCoinByBenefit($can_coin,$price,$pointRate,$user_id);
        return $can_coin;
    }


    /**
     * @param $user_id
     * @param $arr
     * @return array|void
     * 更新订单信息
     * @throws Exception
     */
    public function updateOrderRecord($user_id, $arr)
    {
        //频率限制
        $unique_key = __FUNCTION__;
        list($anti) = by::Ouser()->ReqAntiConcurrency($user_id,$unique_key,5,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $order_no = $arr['order_no'] ?? '';
        if(empty($order_no)){
            return [false,"订单不存在！"];
        }


        //获取订单信息
        $info     = by::Ouser()->CommPackageInfo($user_id,$order_no,false,true,true,true);
        //订单状态
        $status = $info['status'] ?? -1;
        if(intval($status) !== by::Omain()::ORDER_STATUS['WAIT_PAY']){
            return [false,'订单状态不正确，请返回重试！'];
        }

        //判断是否为尾款订单，非尾款订单不予修改
        $deposit_order_no = $info['deposit_order_no'] ?? '';
        if(empty($deposit_order_no)){
            return [false,'非尾款订单，禁止修改！'];
        }


        //商品详情
        $goods = $info['goods'] ?? [];
        if(empty($goods)){
            return [false,'商品不能为空！'];
        }

        $endPayment   = $goods[0]['presale_info']['end_payment'] ?? 0;
        $startPayment = $goods[0]['presale_info']['start_payment'] ?? 0;
        $isPresale    = $goods[0]['is_presale'] ?? 0;
        $gid = $goods[0]['gid'] ?? 0;
        $sid = $goods[0]['sid'] ?? 0;
        if($isPresale == 1 && $startPayment > intval(START_TIME)){
            return [false,'还未到支付尾款时间，请耐心等待！'];
        }

        if($isPresale == 1 && $endPayment < intval(START_TIME)){
            return [false,'已超过支付尾款时间，禁止修改！'];
        }

        if(empty($gid)){
            return [false,'商品不能为空~'];
        }

        //订单状态判断
        $ouserInfoStatus = $info['status'] ?? -1;
        if(CUtil::uint($ouserInfoStatus) !== by::Omain()::ORDER_STATUS['WAIT_PAY']){
            return [false,"订单状态错误！不允许修改"];
        }


        //todo 判断默认地址是否可以配送
        $aid = CUtil::uint($arr['aid'] ?? 0);
        if($aid){
            list($s, $m) = by::model('OfreightModel', 'goods')->IsDis($user_id, $arr['aid']??0);
            if (!$s) {
                return [false, $m];
            }
        }


        list($s,$budgetInfo) = $this->_budgetOrderPrice($user_id,$info,$arr);
        if(!$s){
            return [false,$budgetInfo];
        }
        $coin_type      = $budgetInfo['coin_type'] ?? 0;
        $coin           = $budgetInfo['coin'] ?? 0;
        $fprice         = by::Gtype0()->totalFee($budgetInfo['fprice'] ?? 0);
        $real_price     = by::Gtype0()->totalFee($budgetInfo['real_price'] ?? 0);
        $tcoin          = $budgetInfo['tcoin'] ?? 0;
        $can_coin       = $budgetInfo['can_coin'] ?? 0;
        $coin_price     = by::Gtype0()->totalFee($budgetInfo['coin_price'] ?? 0);
        $note           = $budgetInfo['note'] ?? 0;
        $price          = by::Gtype0()->totalFee($budgetInfo['price'] ?? 0);
        $tprice         = by::Gtype0()->totalFee($budgetInfo['tprice'] ?? 0);
        $aid            = $budgetInfo['aid'] ?? 0;
        $expend_price   = by::Gtype0()->totalFee($budgetInfo['expand_price'] ?? 0);
        $giftCardIds    = $budgetInfo['gift_card_ids'] ?? [];
        $giftCardIdsStr = empty($giftCardIds) ? '' : implode(',', $giftCardIds);



        //todo 使用礼品卡
        // 初始化参数
        $useCardType            = 0;
        $useCardValue           = 0;
        $giftCardDiscountAmount = 0;
        $giftCardData           = [];
        $applyGiftCardInfo      = [];

        if (!empty($giftCardIds)) {

            // 获取礼品卡数据
            list($giftCardStatus, $giftCardData, $useCardType) = GiftCardService::getInstance()->getGiftCardData($giftCardIds, $goods);

            if (!$giftCardStatus) {
                return [false, $giftCardData];
            }

            // 取扣除礼品卡ID的顺序
            $giftCardIds = array_keys($giftCardData);

            // 计算订单当前应付金额 （总金额 - 积分优惠金额 - 膨胀金额）
            $orderPrice = $tprice - $coin_price - $expend_price;

            // 应用礼品卡抵扣
            $applyGiftCardInfo = GiftCardService::getInstance()->applyGiftCards($orderPrice, $giftCardIds, $giftCardData);

            // 抵扣金额
            $giftCardDiscountAmount = $applyGiftCardInfo['allDiscountAmount'];

            // 使用礼品卡类型  如果是指定兑换卡那么value为ID 如果是储值金额卡那么value为金额
            $useCardValue = ($useCardType == 2) ? implode(",", $giftCardIds) : $giftCardDiscountAmount;

            // 更新礼品卡余额和状态
            foreach ($applyGiftCardInfo['giftCardDataAfterDeduction'] as $giftCardId => $giftCardValue) {
                $giftCardInfo = byNew::GiftUserCards()->userGiftCardInfo($giftCardId);
                $updateData   = ['amount' => ($giftCardInfo['type'] == 2) ? 0 : $giftCardValue];

                if ($giftCardValue == 0 || $useCardType == 2) {
                    $updateData['status'] = GiftUserCardsModel::STATUS['USED']; // 更新卡状态为不可使用
                }

                byNew::GiftUserCards()->saveData($updateData, $giftCardId);
            }

        }


        $ctime     = intval(START_TIME);
        $db        = by::dbMaster();
        $trans     = $db->beginTransaction();
        try {
            //更新数据


            // todo 插入卡消费记录
            if (!empty($giftCardIds)){
                $insertExpendData = [];
                $columns          = [];
                foreach ($applyGiftCardInfo['giftCardDataAfterDeduction'] as $giftCardId => $giftCardValue) {
                    $giftCardInfo     = byNew::GiftUserCards()->userGiftCardInfo($giftCardId,false);
                    $type             = $giftCardInfo['type'] ?? 1;
                    $insertExpendInfo = [
                        'card_id'        => $giftCardInfo['card_id'] ?? '',
                        'user_card_id'   => $giftCardId,
                        'order_no'       => $order_no,
                        'expend_amount'  => $type == 2 ? 0 : bcsub($giftCardData[$giftCardId], $giftCardValue),
                        'current_amount' => $type == 2 ? 0 : $giftCardValue,
                        'type'           => GiftCardExpendRecordModel::TYPES['USE'],
                        'ctime'          => intval(START_TIME),
                        'utime'          => intval(START_TIME),
                    ];

                    $columns            = array_keys($insertExpendInfo);
                    $insertExpendData[] = $insertExpendInfo;
                }

                $giftCardExpendTb = byNew::GiftCardExpendRecord()::tbName();
                by::dbMaster()->createCommand()->batchInsert($giftCardExpendTb, $columns, $insertExpendData)->execute();
            }

            //判断是否需要支付
            if (bccomp($real_price, 0) == 0) {
                $ret = ['need_pay' => '0', 'order_no' => $order_no, 'coin' => 0];
            } else {
                $ret = ['need_pay' => '1', 'order_no' => $order_no, 'coin' => 0];
            }

            //todo 更新订单流水表
            //更新支付流水表
            $payData = [
                'price'=> $real_price,
            ];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);


            //todo 导购订单表
            $ouid = by::Osource()->getOuidByOrder($user_id, $order_no);
            if ($ouid) {
                $time       = by::Omain()->GetTbNameByOrderId($order_no, false);
                $tb_osource = by::Osource()::tbName($time);

                $db->createCommand()->update(
                    $tb_osource,
                    ['price' => $price],
                    ['order_no' => $order_no, 'user_id' => $user_id]
                )->execute();
            }

            //todo 推荐订单表
            $rinfo = by::OsourceR()->getInfoByOrderNo($user_id, $order_no);
            if($rinfo){
                $time  = by::Omain()->GetTbNameByOrderId($order_no, false);
                $db->createCommand()->update(by::OsourceR()::tbName($time), [
                    'price'       => $price
                ], ['order_no' => $order_no, 'user_id' => $user_id])->execute();
            }

            //todo 订单来源表
            $ominfo = by::osourceM()->getInfoByOrderNo($user_id, $order_no);
            if(!empty($ominfo)){
                $time  = by::Omain()->GetTbNameByOrderId($order_no, false);
                $where = ['order_no' => $order_no, 'user_id' => $user_id];
                $db->createCommand()->update(by::osourceM()::tbName($time), [
                    'price'       => $price
                ], $where)->execute();
            }


            //todo 订单表数据
            $ouserInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
            if($ouserInfo){
                $where = ['order_no' => $order_no, 'user_id' => $user_id];
                $db->createCommand()->update(by::Ouser()::tbName($user_id), [
                    'price'           => $price,
                    'fprice'          => $fprice,
                    'coin'            => $coin,
                    'coin_price'      => $coin_price ?? 0,
                    'coin_logid'      => $log_id ?? 0,
                    'note'            => $note,
                    'gift_card_ids'   => $giftCardIdsStr,
                    'gift_card_price' => $giftCardDiscountAmount,
                ], $where)->execute();
            }

            //todo 地址表数据
            $oadInfo = by::Oad()->GetOneByOrderNo($order_no);
            if($aid){
                if($oadInfo){
                    $aData = by::Address()->GetOneAddress($user_id,$aid);
                    $address = by::Address()->SafeInfo($aData);
                    $save    = [
                        'nick'          => $aData['nick'] ?? '',
                        'phone'         => $aData['phone'] ?? '',
                        'pid'           => $aData['pid'] ?? '',
                        'cid'           => $aData['cid'] ?? '',
                        'aid'           => $aData['aid'] ?? '',
                        'detail'        => $aData['detail'] ?? '',
                        'address'       => json_encode($address),
                    ];
                    $where = ['order_no' => $order_no];
                    $db->createCommand()->update(by::Oad()::tbName($order_no),$save, $where)->execute();
                }else{
                    //地址表数据
                    by::Oad()->SaveLog($user_id, $order_no, $aid);
                }
            }


            //todo 订单-商品表
            //查询商品表是否存在
            $goodInfo = by::Ogoods()->GetInfoByOrderNo($user_id,$order_no,$gid,$sid);
            if($goodInfo){
                $tb         = by::Ogoods()::tbName($user_id);
                $save = [
                    'coin_price'      => $coin_price,
                    'price'           => $price,
                    'coin'            => $coin,
                    'gift_card_type'  => $useCardType,//尾款订单只可以使用储值金额卡
                    'gift_card_value' => $useCardValue,
                ];
                !YII_ENV_PROD && CUtil::debug(json_encode($save),'goods-order');
                $where = ['order_no' => $order_no,'user_id'=>$user_id,'gid'=>$gid,'sid'=>$sid];
                //进行更新
                $db->createCommand()->update($tb,$save, $where)->execute();
            }


            $trans->commit();

            //todo 实付金额为0 异步处理订单
            if(bccomp($real_price, 0) == 0){
                list($s,$msg) = by::BasePayModel()->afterPay($user_id, $order_no, [], [], by::wxPay()::SOURCE['MALL'],by::Omain()::PAY_BY_WX);
                if (!$s) {
                    CUtil::debug($user_id.'|'.$order_no. '|msg:' . $msg,'err.not_pay.order');
                }
            }

            // 尾款订单使用了礼品卡 防止预售结束不支付，自动退换礼品卡金额
            if (bccomp($real_price, 0) > 0 && !empty($giftCardIds)) {
                \Yii::$app->queue->delay($endPayment - time())->push(new RefundGiftCardJob([
                    'user_id'  => $user_id,
                    'order_no' => $order_no,
                ]));
            }

            //清理订单列表缓存
            by::Ouser()->DelListCache($user_id);

            by::Ouser()->DelPointCache($user_id);

            //防止有刷缓存问题
            by::Ouser()->DelInfoCache($user_id, $order_no);

            // 清除t_uo_g_的缓存
            by::Ogoods()->DelListCache($order_no);

            //清理导购订单相关缓存
            if (!empty($ouid)) {
                by::Osource()->DelCache($ouid);
                by::Osource()->delInfoCache($ouid, $order_no);
            }

            //清理推荐订单相关缓存
            if (!empty($rinfo)) {
                by::OsourceR()->delListCache($rinfo['r_id'] ?? 0);
                by::OsourceR()->delInfoCache($user_id, $order_no);
            }

            //清理订单来源相关缓存
            if (!empty($ominfo)) {
                by::osourceM()->delListCache($ominfo['union'] ?? '');
                by::osourceM()->delInfoCache($user_id, $order_no);
            }

            //清理订单表数据缓存
            if(!empty($ouserInfo)){
                by::Ouser()->DelInfoCache($user_id,$order_no);
            }

            //清理地址表数据
            if(!empty($oadInfo)){
                by::Oad()->DelCache($order_no);
            }
            //清理订单商品数据
            if(!empty($goodInfo)){
                by::Ogoods()->DelInfoCache($user_id,$order_no,$gid,$sid);
            }


            // //todo 订单同步crm
            // Crm::factory()->push($user_id,'order',['user_id'=>$user_id,'order_no'=>$order_no]);
            // Crm::factory()->push($user_id,'orderLine',['user_id'=>$user_id,'order_no'=>$order_no]);

            return [true,$ret];
       } catch (MyExceptionModel $e) {
            $trans->rollBack();
            $this->__addRecord($e, $unique_key, $user_id);
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();
            $this->__addRecord($e, $unique_key, $user_id);
            return [false, '网络繁忙,请稍候'];
        }
    }


    /**
     * @param $e
     * @param $unique_key
     * @param $user_id
     * @return void
     * 更新订单信息错误
     */
    public function __addRecord($e,$unique_key, $user_id)
    {
        $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
        CUtil::debug($error, 'err.update-order-ouserService');
        by::Ouser()->ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
    }


}
