<?php
namespace app\modules\goods\services;


use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\services\goods\IndexGoodsMainService;

class OcfgService{


    /**
     * @param int $user_id
     * @param string $order_no
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 保存新商品模块
     */
    public function SaveWaresLog(int $user_id, string $order_no, array $aData): array
    {
        $gcombines      = $aData['gcombines']   ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]
        $platformIds    = $aData['platformIds'] ?? [];
        if (empty($gcombines)) {
            return [false, '参数错误(6)'];
        }

        // 是否员工
        $uid        = by::Phone()->getUidByUserId($user_id);
        $isEmployee = byNew::UserEmployeeModel()->isEffectiveEmployee($uid);

        $save           = [];
        $indexGoodsMainService = new IndexGoodsMainService();

        foreach($gcombines as $gcombine) {
            $gData  = $indexGoodsMainService->GetOneByGidSid($gcombine['gid'], $gcombine['sid'], $platformIds,false);
            if(empty($gData)){
                return [false,'商品不存在（6）'];
            }
            //保存关键商品信息
            unset($gData['introduce'],$gData['notice'],$gData['video'],$gData['cover_image_3d'],$gData['model_3d'],$gData['detail'],$gData['images']);
            $cfg    = $gData;
            $cfg['is_employee'] = $isEmployee ? 1 : 0;
            $save[] = [
                'order_no'      => $order_no,
                'user_id'       => $user_id,
                'gid'           => $gcombine['gid'],
                'sid'           => $gcombine['sid'],
                'goods_type'    => by::Ogoods()::GOODS_TYPE['WARES'],
                'cfg'           => json_encode($cfg)
            ];
        }

        $tb             = by::Ocfg()::tbName($order_no);
        by::dbMaster()->createCommand()->batchInsert($tb, ['order_no','user_id','gid','sid','goods_type','cfg'], $save)->execute();

        return [true, 'ok'];
    }


}
