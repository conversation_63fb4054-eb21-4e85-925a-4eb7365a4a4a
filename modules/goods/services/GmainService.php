<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */
namespace app\modules\goods\services;


use app\models\by;
use app\models\CUtil;

class GmainService{


    /**
     * @return bool
     * @throws \yii\db\Exception
     * @throws \RedisException
     *
     */
    public function changePresaleGoodAndOrder(): bool
    {
        //查出所有预售订单
        $db             = by::dbMaster();
        $tb             = by::Gtype0()::tbName();
        $now_time       = intval(START_TIME);
        $gid            = 0;
        $goodIds = [];

        while (true) {

            $sql    = "SELECT `gid` FROM {$tb} WHERE `gid` > :gid AND `is_presale`=1 AND `presale_time` <= :now_time 
                       ORDER BY `gid` ASC LIMIT 100";

            $list   = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':now_time' => $now_time])->queryAll();

            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $gid    = $end['gid'];

            foreach ($list as $v) {
                $trans  = $db->beginTransaction();
                try {
                    //将预售商品改为常规商品
                    list($s, $m) = by::Gtype0()->UpdateData($v['gid'], ['is_presale' => 0]);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                    $trans->commit();
                    $goodIds[]=$v['gid'];
                } catch (\Exception $e) {

                    $trans->rollBack();
                    CUtil::debug($e->getMessage(), 'err.presale_good');
                }
            }

            usleep(1000);
        }
        !empty($goodIds) &&   $this->cancelDepositOrder($goodIds);

        return true;
    }


    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     */
    public function cancelDepositOrder($goodIds)
    {
        by::Odeposit()->cancel($goodIds);
    }



}
