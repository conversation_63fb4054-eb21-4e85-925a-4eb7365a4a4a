<?php

namespace app\modules\goods\services;


use app\components\AliYunSms;
use app\components\AppCRedisKeys;
use app\components\ErpOms;
use app\components\IotDs;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\SmsService;
use RedisException;
use yii\db\Exception;

class UserOrderTryService
{

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    const CONVERSION_TIME = YII_ENV_PROD ? 86400 * 365  : 86400 * 1;

    public static function getInstance()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new self();
        }

        return self::$_instance[__CLASS__];
    }

    public function SaveSnsByOrders()
    {
        //1.查出所有的try_status 为 1 且 sn 为 空的订单
        //2.分为50个为一组，查询sn数据并且保存
        $db = by::dbMaster();
        $tb = byNew::UserOrderTry()::tbName();
        $id = 0;

        while (true) {

            $sql = "SELECT * FROM {$tb} WHERE `id` > :id AND `try_status`= :try_status AND `sn` = '' 
                       ORDER BY `id` ASC LIMIT 50";

            $list = by::dbMaster()->createCommand($sql, [':id' => $id, ':try_status'=>byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY']])->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];

            $orderNos = array_unique(array_filter(array_column($list, 'order_no')));
            //3.批量获取sn  ErpOms获取
            list($status, $data) = ErpOms::factory()->run('querySn', ['order_nos' => $orderNos, 'user_id' => 0]);
            if ($status) {
                $snsArr = array_column($data, 'sn', 'dealCode');
                // 批量更新
                $updateData = [];
                foreach ($list as $v) {
                    if (isset($snsArr[$v['order_no']])) {
                        $updateData[] = [
                            'id' => $v['id'],
                            'sn' => $snsArr[$v['order_no']] ?? '',
                        ];
                    }
                }
                if (!empty($updateData)) {
                    $updateSql = CUtil::batchUpdate($updateData, "id", $tb);
                    $s         = $db->createCommand($updateSql)->execute();
                    if (!$s) {
                        CUtil::debug("更新失败", 'err.save_sns_by_orders');
                    }
                }
            }
            usleep(1000);
        }
        return true;
    }

    public function SaveActiveTimeBySns()
    {
        //1.查出所有register_time 为0，且sn不为空的数据
        //2.分为50个为一组，查询register_time数据并且保存
        $db     = by::dbMaster();
        $tb     = byNew::UserOrderTry()::tbName();
        $tbUser = byNew::UserTryModel()::tableName();
        $id     = 0;

        while (true) {
            $sql = "SELECT `a`.*,`b`.`uid` FROM {$tb} as `a` INNER JOIN {$tbUser} as `b` ON `a`.`user_id`= `b`.`user_id` WHERE `a`.`id` > :id AND `a`.`register_time` = 0 AND `a`.`sn` != '' 
                       ORDER BY `a`.`id` ASC LIMIT 50";

            $list = by::dbMaster()->createCommand($sql, [':id' => $id])->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];

            $sns = array_unique(array_filter(array_column($list, 'sn')));
            //3.批量获取sn  ErpOms获取
            list($status, $data) = IotDs::factory()->run('activelist', ['sns' => $sns]);

            if ($status) {
                $dataAssoc = array_combine(
                    array_map(function($item) { return $item['uid'] . '|' . $item['sn']; }, $data),
                    $data
                );

                $result = array_values(array_map(function($item) use ($dataAssoc) {
                    $key = $item['uid'] . '|' . $item['sn'];
                    if (isset($dataAssoc[$key])) {
                        return [
                            'id'            => $item['id'],
                            'register_time' => $dataAssoc[$key]['activeTime'] / 1000,
                            'order_no'      => $item['order_no'] ?? '',
                            'user_id'       => $item['user_id'] ?? ''
                        ];
                    }
                    return null;
                }, $list));

                $result = array_filter($result);

                $updateData = [];
                if($result){
                    foreach ($result as $k => $v) {
                        //自动审核通过
                        by::Omain()->Finish($v['user_id'], $v['order_no']);
                        $updateData[] = [
                            'id' => $v['id'],
                            'register_time' => $v['register_time']
                        ];
                    }
                }

                if (!empty($updateData)) {
                    $updateSql = CUtil::batchUpdate($updateData, "id", $tb);
                    $s         = $db->createCommand($updateSql)->execute();
                    if (!$s) {
                        CUtil::debug("更新失败|" . json_encode($updateData), 'err.user_order_try.save_active_time_by_sns');
                    }
                }


            }
            usleep(1000);
        }
        return true;
    }


    /**
     * @return bool
     * @throws RedisException
     * @throws Exception
     * 用户收到货2天未激活
     * 短信通知用户、飞书通知运营
     */
    public function activateInform(): bool
    {
        $day = 2;
        // 获取未激活的订单
        $orders = byNew::UserOrderTry()->GetUnActivateOrder('activateInform', ['day' => $day]);
        if (empty($orders)) {
            return true;
        }

        // 连接 Redis
        $redis         = by::redis();
        $data['title'] = sprintf('%s 先试后买未激活提醒', date('Y-m-d'));
        $redisKey      = AppCRedisKeys::sentNotificationsSet();
        $phones        = [];
        foreach ($orders as $order) {
            $userId  = $order['user_id'];
            $orderNo = $order['order_no'];
            // 查询电话号码
            $phone  = by::Phone()->GetPhoneByUid($userId);
            $subKey = CUtil::getAllParams(__FUNCTION__, $userId, $orderNo);

            //构造飞书数据
            $data['contents'][] = sprintf('超过 **%s** 天未激活订单号 **%s** 用户手机号 **%s**，请及时关注！', $day, $orderNo, $phone);

            // 使用 SISMEMBER 检查键是否已存在于集合中
            $isMember = $redis->sIsMember($redisKey, $subKey);

            if (!$isMember) {
                // 如果不存在，则添加到集合中
                $redis->sAdd($redisKey, $subKey);
                $phones[$phone] = $phone;
            }
        }

        //  通知飞书
        if (!empty($data['contents'])) {
            CUtil::sendMsgToFs($data, 'activateNotice', 'interactive');
        }

        // 发送短信
        if (!empty($phones) && YII_ENV_PROD) {
            SmsService::getInstance()->batchSendSms(AliYunSms::TEMPLATE_CODE['activate_inform'], $phones, false);
        }

        return true;
    }


    /**
     * @return bool
     * @throws Exception
     * 未到货异常通知
     * 飞书通知运营
     */
    public function unArriveInform(): bool
    {
        $orders = byNew::UserOrderTry()->GetUnActivateOrder('unArriveInform', ['try_status' => byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY']]);
        if (empty($orders)) {
            return true;
        }

        $data['title'] = sprintf('%s 先试后买未到货提醒', date('Y-m-d'));
        foreach ($orders as $order) {
            $activity           = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($order['ac_id']);
            $payTime            = $order['pay_time'] ?? 0;
            $deliveryPeriod     = $activity['delivery_period'] ?? 0;
            $deliveryPeriodTime = $deliveryPeriod * byNew::UserOrderTry()::TRY_TIME_PERIOD;
            //超过收货有效期的数据
            if (time() - $payTime > $deliveryPeriodTime) {
                $userId             = $order['user_id'];
                $orderNo            = $order['order_no'];
                $phone              = by::Phone()->GetPhoneByUid($userId);
                $data['contents'][] = sprintf('订单号 **%s** 用户手机号 **%s** 超过发货有效期 **%s** 天未到货，请及时关注！', $orderNo, $phone, $deliveryPeriod);
            }
        }

        //  通知飞书
        if (!empty($data['contents'])) {
            CUtil::sendMsgToFs($data, 'activateNotice', 'interactive');
        }

        return true;
    }


    /**
     * @return bool
     * @throws Exception
     * @throws RedisException
     *  试用结束提醒
     *  提前2天通知
     *  短信通知用户
     */
    public function tryEndInform(): bool
    {
        // 试用结束前通知天数
        $day    = 2;
        $orders = byNew::UserOrderTry()->GetUnActivateOrder('tryEndInform', ['try_status' => byNew::UserOrderTry()::TRY_STATUS['ON_TRY']]);
        if (empty($orders)) {
            return true;
        }

        $redis    = by::redis();
        $redisKey = AppCRedisKeys::sentNotificationsSet();
        //发送短信手机号集合
        $phones = [];
        foreach ($orders as $order) {
            $arrivalTime  = $order['arrival_time'] ?? 0;
            $registerTime = $order['register_time'] ?? 0;
            // 确保不为空的较早时间戳作为体验开始时间
            if ($arrivalTime != 0 && $registerTime != 0) {
                $beginTryTimeStamp = min($arrivalTime, $registerTime);
            } elseif ($arrivalTime != 0) {
                $beginTryTimeStamp = $arrivalTime;
            } elseif ($registerTime != 0) {
                $beginTryTimeStamp = $registerTime;
            } else {
                continue; // 如果两个时间戳都为空，跳过当前订单
            }

            // 获取活动信息
            $activity = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($order['ac_id']);
            $validity = $activity['validity'] ?? 0;
            // 通知时间日期 = 体验开始时间 + 有效期 - 提前通知天数
            $informDate = date('Y-m-d', ($beginTryTimeStamp + ($validity - $day) * byNew::UserOrderTry()::TRY_TIME_PERIOD));
            // 当前日期
            $currentDate = date('Y-m-d', time());
            // 判断是否需要通知用户
            if ($currentDate >= $informDate) {
                $userId  = $order['user_id'];
                $orderNo = $order['order_no'];
                $subKey  = CUtil::getAllParams(__FUNCTION__, $userId, $orderNo);

                // 使用 SISMEMBER 检查键是否已存在于集合中
                if (!$redis->sIsMember($redisKey, $subKey)) {
                    // 如果不存在，则添加到集合中
                    $redis->sAdd($redisKey, $subKey);
                    $phone = by::Phone()->GetPhoneByUid($userId);
                    // 获取用户电话并发送短信
                    if ($phone) {
                        $phones[$phone] = $phone;
                    }
                }
            }
        }

        // 发送短信
        if (!empty($phones) && YII_ENV_PROD) {
            SmsService::getInstance()->batchSendSms(AliYunSms::TEMPLATE_CODE['try_end_inform'], $phones, false);
        }

        return true;
    }


    /**
     * @return bool
     * @throws Exception
     * @throws RedisException
     * 退机有效期剩余1天
     * 短信通知用户
     */
    public function returnProductInform(): bool
    {
        $day    = 1;
        $orders = byNew::UserOrderTry()->GetUnActivateOrder('returnProductInform', ['try_status' => byNew::UserOrderTry()::TRY_STATUS['WAIT_BACK']]);
        if (empty($orders)) {
            return true;
        }
        $redis    = by::redis();
        $redisKey = AppCRedisKeys::sentNotificationsSet();

        //发送短信手机号集合
        $phones = [];

        foreach ($orders as $order) {
            $arrivalTime  = $order['arrival_time'] ?? 0;
            $registerTime = $order['register_time'] ?? 0;
            // 确保不为空的较早时间戳作为体验开始时间
            if ($arrivalTime != 0 && $registerTime != 0) {
                $beginTryTimeStamp = min($arrivalTime, $registerTime);
            } elseif ($arrivalTime != 0) {
                $beginTryTimeStamp = $arrivalTime;
            } elseif ($registerTime != 0) {
                $beginTryTimeStamp = $registerTime;
            } else {
                continue; // 如果两个时间戳都为空，跳过当前订单
            }

            // 获取活动信息
            $activity     = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($order['ac_id']);
            $validity     = $activity['validity'] ?? 0;
            $returnPeriod = $activity['return_period'] ?? 0;

            // 通知时间节点
            $informTimeStamp =date('Y-m-d', ($beginTryTimeStamp + ($validity + $returnPeriod - $day) * byNew::UserOrderTry()::TRY_TIME_PERIOD));
            // 当前日期
            $currentDate = date('Y-m-d', time());
            // 判断是否需要通知用户    取整天
            if ($currentDate >= $informTimeStamp) {
                $userId  = $order['user_id'];
                $orderNo = $order['order_no'];
                $subKey  = CUtil::getAllParams(__FUNCTION__, $userId, $orderNo);

                // 使用 SISMEMBER 检查键是否已存在于集合中
                if (!$redis->sIsMember($redisKey, $subKey)) {
                    // 如果不存在，则添加到集合中
                    $redis->sAdd($redisKey, $subKey);
                    $phone = by::Phone()->GetPhoneByUid($userId);
                    // 获取用户电话并发送短信
                    if ($phone) {
                        $phones[$phone] = $phone;
                    }
                }
            }
        }

        // 发送短信
        if (!empty($phones) && YII_ENV_PROD) {
            SmsService::getInstance()->batchSendSms(AliYunSms::TEMPLATE_CODE['return_product_inform'], $phones, false);
        }

        return true;
    }


    /**
     * @param $input
     * @param $format_price
     * @param $is_ac
     * @return array|\yii\db\DataReader
     * 根据查询条件查询试用订单详情
     */
    public function GetUserOrderTryInfo($input, $format_price = true, $is_ac = true)
    {
        $info   = byNew::UserOrderTry()->GetOneInfo($input, $format_price);
        $acInfo = [];
        if ($is_ac) {
            $acId = $info['ac_id'] ?? 0;
            if ($acId) {
                $acInfo = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($acId);
                if ($acInfo) {
                    unset($acInfo['id'], $acInfo['create_time'], $acInfo['update_time'], $acInfo['rule']);
                }
            }
        }
        return array_merge($info, $acInfo);
    }


    /**
     * 将过期试用中的订单状态改为待退机
     * @return bool
     */
    public function EndTryOrders()
    {
        $db   = by::dbMaster();
        $tb   = byNew::UserOrderTry()::tbName();
        $tbAc = byNew::ActivityTypeModel()::tableName();
        $id   = 0;

        while (true) {
            $sql = "SELECT `try`.* 
                    FROM {$tb} AS `try` 
                    INNER JOIN {$tbAc} as `ac` ON `ac`.`ac_id` = `try`.`ac_id` 
                    WHERE `try`.`id` > :id 
                    AND `try`.`arrival_time` != 0 
                    AND `try`.`try_status` = :try_status
                    AND `try`.`arrival_time` + `ac`.`validity` * :time_period < UNIX_TIMESTAMP() 
                    ORDER BY `try`.`id` ASC 
                    LIMIT 50 ";


            $params = [
                ':id'         => $id,
                ':time_period' => byNew::UserOrderTry()::TRY_TIME_PERIOD,
                ':try_status' => byNew::UserOrderTry()::TRY_STATUS['ON_TRY'],
            ];

            $list = by::dbMaster()->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];


            //更新状态
            $updateData = [];
            foreach ($list as $v) {
                $updateData[] = [
                    'id'         => $v['id'],
                    'try_status' => byNew::UserOrderTry()::TRY_STATUS['WAIT_BACK'],
                ];
            }
            if (!empty($updateData)) {
                $updateSql = CUtil::batchUpdate($updateData, "id", $tb);
                $s         = $db->createCommand($updateSql)->execute();
                if (!$s) {
                    CUtil::debug("更新失败|" . json_encode($updateData), 'err.end_try_orders');
                }
            }
            usleep(1000);
        }
        return true;
    }


    /**
     * 将过期待退机订单状态改为待扣款
     * @return bool
     */
    public function WaitDeductOrders()
    {
        $db   = by::dbMaster();
        $tb   = byNew::UserOrderTry()::tbName();
        $tbAc = byNew::ActivityTypeModel()::tableName();
        $id   = 0;

        while (true) {
            $sql = "SELECT `try`.* 
                    FROM {$tb} AS `try` 
                    INNER JOIN {$tbAc} as `ac` ON `ac`.`ac_id` = `try`.`ac_id` 
                    WHERE `try`.`id` > :id 
                    AND `try`.`arrival_time` != 0 
                    AND `try`.`back_time` = 0
                    AND `try`.`try_status` = :try_status
                    AND `try`.`arrival_time` + `ac`.`validity` * :time_period1 + `ac`.`return_period` * :time_period2 < UNIX_TIMESTAMP() 
                    ORDER BY `try`.`id` ASC 
                    LIMIT 50 ";


            $params = [
                ':id'         => $id,
                ':time_period1' => byNew::UserOrderTry()::TRY_TIME_PERIOD,
                ':time_period2' => byNew::UserOrderTry()::TRY_TIME_PERIOD,
                ':try_status' => byNew::UserOrderTry()::TRY_STATUS['WAIT_BACK'],
            ];

            $list = by::dbMaster()->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];


            //更新状态
            $updateData = [];
            foreach ($list as $v) {
                $updateData[] = [
                    'id'         => $v['id'],
                    'try_status' => byNew::UserOrderTry()::TRY_STATUS['WAIT_DEDUCT'],
                ];
            }
            if (!empty($updateData)) {
                $updateSql = CUtil::batchUpdate($updateData, "id", $tb);
                $s         = $db->createCommand($updateSql)->execute();
                if (!$s) {
                    CUtil::debug("更新失败|" . json_encode($updateData), 'err.end_try_orders');
                }
            }
            usleep(1000);
        }
        return true;
    }


    /**
     * @return true
     * @throws Exception
     * 
     * 保存用户试用订单转化
     */
    public function SaveUserOrderTryConversion()
    {
        $db        = by::dbMaster();
        $tb        = byNew::UserOrderTry()::tbName();
        $tbUserTry = byNew::UserTryModel()::tableName();
        $userOrderTryConversion = byNew::UserOrderTryConversion();
        $userOrderTryConversionTb = $userOrderTryConversion::tbName();
        // 找出$tb中最大的值
        $sql = "SELECT MAX(`id`) as `id` FROM {$tb}";
        $id  = $db->createCommand($sql)->queryScalar();
        $ctime     = time();

        // 机器标识
        $iotTagS = by::Gtag()::TAG_IOT_MODEL;


        while (true) {
            $sql = "SELECT `try`.*,`user`.`uid` 
                    FROM {$tb} AS `try` 
                    INNER JOIN {$tbUserTry} as `user` ON `user`.`user_id` = `try`.`user_id` 
                    WHERE `try`.`id` < :id 
                    AND `try`.`over_time` != 0
                    AND (UNIX_TIMESTAMP() BETWEEN `try`.`over_time` AND `try`.`over_time` + :conversion_time)
                    ORDER BY `try`.`id` DESC 
                    LIMIT 50 ";


            $params = [
                ':id' => $id,
                ':conversion_time' => self::CONVERSION_TIME
            ];

            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];

            // 获取uids 
            $uids = array_values(array_unique(array_filter(array_column($list, 'uid'))));


            // 获取用户绑定机器信息
            list($status, $data) = IotDs::factory()->run('activelist', ['uids' => $uids]);
            if (!$status || empty($data)) {
                continue;
            }

            // data 中加入 $list 的 user_id 字段
            foreach ($data as &$item0) {
                $item0['user_id'] = $list[array_search($item0['uid'], array_column($list, 'uid'))]['user_id'] ?? 0;
            }

            // 过滤data中数据库已存在的数据
            // 收集所有的 user_id 和 sn
            $userIds = array_column($data, 'user_id');
            $sns = array_column($data, 'sn');

            // 一次性查询数据库，获取所有已存在的数据
            $existingData = $userOrderTryConversion->GetList([
                CUtil::buildCondition('user_id', 'IN', $userIds),
                CUtil::buildCondition('sn', 'IN', $sns)
            ]);

            // 将已存在的数据转换为一个关联数组，方便后续查找
            $existingDataAssoc = [];
            foreach ($existingData as $dataValue) {
                $existingDataAssoc[$dataValue['user_id'] . '|' . $dataValue['sn']] = true;
            }

            // 收集所有需要插入的数据
            $dataToInsert = [];
            foreach ($data as $set){
                // 已存在的数据不做处理
                if (isset($existingDataAssoc[$set['user_id'] . '|' . ($set['sn'] ?? '')])) {
                    continue;
                }
                $dataToInsert[] = $set;
            }


            //查找不同的uid和label，只保留over_time最大的一条数据数据
            $needData = [];
            $seen     = [];

            // $list 二维数组 按照over_time 倒序处理
            usort($list, function ($a, $b) {
                return $b['over_time'] - $a['over_time']; // 按照 over_time 倒序排序
            });


            $exceptionData = [];
            foreach ($list as $v) {
                $uid      = $v['uid'] ?? '';
                $label    = $v['label'] ?? 0;
                $tagModel = $iotTagS[$label] ?? '';
                $sn = $v['sn'] ?? 0;

                if (!isset($seen[$uid . '--' . $label]) && $uid && $label && $tagModel && $sn) {
                    $v['tag_model']             = $tagModel;
                    $v['over_time_unix']        = $v['over_time'] * 1000;
                    $needData[]                 = $v;
                    $seen[$uid . '--' . $label] = true;
                }
                if(empty($sn)){
                    $exceptionData[] = $v;
                }
            }

            if($exceptionData){
                CUtil::debug("这些试用订单已经完成，但是没有试用机器sn，请关注E3+查询！，exceptionData:".json_encode($exceptionData), 'warn.orderTry.exceptionData');
            }


            $saveData = [];
            // 获取每个用户的绑定机器信息 以uid为key，把每个用户的绑定机器信息放到一个数组中
            $uidData = [];
            foreach ($dataToInsert as $item1) {
                $uid = $item1['uid'] ?? '';
                if ($uid) {
                    $uidData[$uid][] = $item1;
                }
            }
            // 每个用户匹配
            if($needData && $uidData){
                foreach ($needData as $v1) {
                    $uid      = $v1['uid'] ?? '';
                    $sn       = $v1['sn'] ?? 0;
                    $overTime = $v1['over_time_unix'] ?? 0;
                    $tagModel = $v1['tag_model'] ?? '';
                    if (isset($uidData[$uid]) && $tagModel && $sn && $uid) {
                        unset($item);
                        foreach ($uidData[$uid] as $item) {
                            if (strpos($item['model'], $tagModel) !== false && $overTime < ($item['activeTime'] ?? 0) && $sn != $item['sn']) {
                                $saveData[] = array_merge($v1, $item);
                                break;
                            }
                        }
                    }
                }
            }


            CUtil::debug("savedata:".json_encode($saveData)."||"."needdata:".json_encode($needData)."||"."data:".json_encode($dataToInsert), 'saveData.try');

            if ($saveData) {
                $setData = [];
                foreach ($saveData as $item) {
                    $setData[] = [
                        'user_id'     => $item['user_id'],
                        'model'       => $item['model'],
                        'order_no'    => $item['order_no'],
                        'label'       => $item['label'],
                        'ac_id'       => $item['ac_id'],
                        'sn'          => $item['sn'] ?? 0,
                        'active_time' => ($item['activeTime'] ?? 0) / 1000,
                        'ctime'       => $ctime,
                        'utime'       => $ctime,
                    ];
                }

                $setData = array_filter($setData);

                // 批量插入数据
                if ($setData) {
                    list($s,$msg) = $userOrderTryConversion->BatchInsert($setData);
                    if(!$s){
                        CUtil::debug("批量插入试用订单转化失败，msg:".$msg, 'err.save_user_order_try_conversion');
                    }
                }

            }

            usleep(1000);
        }
        return true;
    }


    /**
     * @param $order_no
     * @return array
     * 取消先试后买订单(回调处理)
     */
    public function CancelTryOrder($order_no)
    {
        if(empty($order_no)){
            return [false, "订单号不存在"];
        }
        $tryOrder = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no)]);
        if(empty($tryOrder)){
            return [false, "先试后买订单不存在"];
        }
        $tryStatus = $tryOrder['try_status'] ?? -1;
        if($tryStatus == byNew::UserOrderTry()::TRY_STATUS['HAS_CANCEL']){
            return [true, "先试后买订单已取消"];
        }
        if($tryStatus != byNew::UserOrderTry()::TRY_STATUS['WAIT_PAY']){
            return [false, "先试后买订单不是待支付状态，无法取消"];
        }
        $save = ['user_id' => $tryOrder['user_id'], 'order_no' => $order_no, 'try_status' => byNew::UserOrderTry()::TRY_STATUS['HAS_CANCEL'], 'utime'=>time()];
        list($s, $m) = byNew::UserOrderTry()->SaveLog($save);
        if (!$s) {
            return [false, "先试后买订单取消失败~订单状态更新失败~"];
        }
        return [true, '取消成功'];
    }
}
