<?php

namespace app\modules\goods\services;

use app\components\AppCRedisKeys;
use app\components\AppNRedisKeys;
use app\components\LockRedis;
use app\components\MemberCenter;
use app\jobs\HandleDrawActivityJob;
use app\jobs\HandleNewDrawActivityJob;
use app\jobs\UpdateDrawTimesJob;
use app\exceptions\DrawActivityException;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\asset\models\PointConfigModel;
use app\modules\back\services\MemberActivityService;
use app\modules\goods\models\DrawActivityModel;
use app\modules\goods\models\DrawActivityPrizeRecordModel;
use app\modules\goods\models\DrawActivityTaskRecordModel;
use app\modules\goods\models\DrawExternalCouponModel;
use app\modules\goods\models\DrawPrizeModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\DrawCustomFormRecordModel;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\MemberActivityModuleRelationModel;
use app\modules\main\models\MemberActivityModuleResourceModel;
use RedisException;
use yii\db\Exception;

class DrawActivityService
{

    private static $_instance = NULL;


    private function __construct()
    {

    }


    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $acId
     * @return array
     * 活动详情+奖品列表
     */
    public function getActivityDetail($acId): array
    {
        // 参数验证
        if (empty($acId) || !is_numeric($acId)) {
            return [false, '参数有误，请检查！'];
        }

        try {
            // 获取活动信息
            $activityDetail = byNew::DrawActivity()->getActivityDetail($acId);

            // 获取奖品信息(列表)
            $prizeList = byNew::DrawActivityPrize()->getPrizeListByAcId($acId);

            //获取奖品信息(ID)
            $prizeIds = array_column($prizeList, 'prize_id');
            // 将奖品信息加入活动详情
            $activityDetail['prize_list'] = byNew::DrawPrize()->getPrizeListByIds($prizeIds);

            return [true, $activityDetail];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.draw_activity');
            // 异常处理
            return [false, '获取活动详情失败'];
        }
    }


    /**
     * @param $userId
     * @param $acId
     * @return array
     * 获取用户剩余抽奖次数
     */
    public function getUserDrawTimes($userId, $acId): array
    {
        // 参数验证
        if (!is_numeric($acId) || !is_numeric($userId)) {
            return [false, '参数错误，请检查！'];
        }

        try {
            // 获取用户抽奖次数
            $drawTimes = byNew::DrawActivityTaskRecord()->getUserDrawTimes($userId, $acId);

            return [true, intval($drawTimes)];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.draw_times');
            // 异常处理
            return [false, '获取用户抽奖次数失败'];
        }
    }


    /**
     * @param $userId
     * @param $post
     * @return array
     * 完成任务刷取次数
     */
    public function doActivityTask($userId, $post): array
    {
        //校验活动时间
        // if(!ActivityConfigEnum::judgeActivityTime($userId,'DRAW')){
        //     return [true,'不在活动时间内，不允许参加哟！'];
        // }

        try {
            // 确保 post 数据中提供了 task_code
            if (!isset($post['task_code'])) {
                throw new DrawActivityException('参数有误，请检查！');
            }

            $taskCode = $post['task_code'];

            //是否校验任务状态  0不校验  1校验  默认校验
            $checkTask = $post['check_task'] ?? 1;
            //被邀请人ID
            $inviteeId = $post['invitee_id'] ?? 0;
            //订单号
            $order_no =  $post['order_no'] ?? '';

            if (empty($taskCode)) {
                throw new DrawActivityException('参数有误，请检查！');
            }

            // 添加心享官事件特殊处理
            if ($taskCode === 'ADD_CUSTOMER_SERVICE' && $checkTask) {
                list($status, $ret) = $this->handleCustomerServiceTask($userId);
                if (!$status) {
                    return [true, $ret];
                }
            }

            // 区分邀请有礼奖励
            if ($taskCode === 'INVITE_FRIEND' && $checkTask) {
               if($inviteeId && $order_no){
                   $mainInfo = by::Omain()->getInfoByOrderNo($inviteeId,$order_no);
                   $user_order_type = CUtil::uint($mainInfo['type'] ?? by::Omain()::USER_ORDER_TYPE['COMMON']);//订单类型-普通订单
                   if($user_order_type !== 1) return [true,'OK'];
               }
            }


            // 获取任务信息
            $taskInfo = byNew::DrawTask()->getTaskInfoByTaskCode($taskCode);

            // 检查任务信息是否有效
            if (empty($taskInfo) || !isset($taskInfo['id'], $taskInfo['type'])) {
                throw new DrawActivityException('获取任务信息失败');
            }

            $taskId   = $taskInfo['id'];
            $taskType = $taskInfo['type'];

            // 获取与任务关联的活动抽奖次数
            $activityDrawTimes = byNew::DrawActivityTask()->getActivityId($taskId);

            // 更新用户在每个关联活动中的抽奖次数
            foreach ($activityDrawTimes as $k => $activityDrawTime) {
                //活动ID
                $acId = $activityDrawTime['activity_id'] ?? 0;
                //抽奖次数
                $drawTimes = $activityDrawTime['draw_times'];
                //活动详情
                $activityDetail = byNew::DrawActivity()->getActivityDetail($acId);
                //活动结束时间
                $endTime = $activityDetail['end_time'] ?? 0;
                //当前时间
                $currenTime = intval(START_TIME);
                //如果活动信息是空 或者 当前时间 < 活动开始时间  或者 当前时间 > 活动结束时间 则不继续执行
                if (empty($activityDetail) || $currenTime < $activityDetail['start_time'] || $currenTime > $endTime) continue;

                //任务限制周期类型
                $taskLimitType = $activityDrawTime['task_limit_type'] ?? '';
                $phone = by::Phone()->GetPhoneByUid($userId);
                switch ($taskLimitType) {
                    case byNew::DrawActivityTask()::TASK_LIMIT_TYPE['everyday']:
                        //任务限制周期类型为 每天 代表 免费赠送次数 过期时间为当天晚上
                        $endTime = mktime(23, 59, 59);

                        //任务完成记录表中查询完成次数
                        $completeCount = byNew::DrawActivityTaskRecord()->getTaskCompleteCount($acId, $taskId, $phone, $endTime);
                        break;
                    case byNew::DrawActivityTask()::TASK_LIMIT_TYPE['activity_period']:
                        //任务限制周期类型为 活动期间 过期时间为活动结束时间
                        //任务完成记录表中查询完成次数
                        $completeCount = byNew::DrawActivityTaskRecord()->getTaskCompleteCount($acId, $taskId, $phone, $endTime);
                        break;
                    default:
                        $completeCount = 0;
                }
                //任务限制次数
                $taskLimit = $activityDrawTime['task_limit'] ?? 0;

                //如果超过限制次数 则跳过次条数据
                if ($completeCount >= $taskLimit) continue;

                if ($taskCode === 'INVITE_NEW_USER') {
                    // $completeCount 完成次数+本次次数 如果是三的倍数 +6次机会否则+1次机会
                    $extra = empty($activityDrawTime['extra']) ? [] : json_decode($activityDrawTime['extra'], true);
                    if (empty($extra)) {
                        // 默认值
                        $drawTimes = (($completeCount + 1) % 3) == 0 ? 6 : 1;
                    } else {
                        // 计算邀请人抽奖次数
                        $defaultDrawTimes = (int) ($extra['extra']['gift_times'][0]['gift_times'] ?? 1);
                        $specialInvitedNum = (int) ($extra['extra']['gift_times'][1]['invite_person'] ?? 3);
                        $extraDrawTimes = (int) ($extra['extra']['gift_times'][1]['gift_times'] ?? 5);
                        $drawTimes = (($completeCount + 1) % $specialInvitedNum) == 0 ? ($extraDrawTimes + 1) : $defaultDrawTimes;
                    }
                }

                if ($taskCode === 'INVITE_FRIEND' && !self::CheckRecommendTask($acId, $activityDetail['end_time'] ?? 0, $userId, $inviteeId)) continue;

                //更新用户在每个关联活动中的抽奖次数
                byNew::DrawActivityTaskRecord()->updateDrawTimes($userId, $acId, $drawTimes);

                // 将任务插入队列以更新抽奖次数（type 为 1 表示增加次数）
                \Yii::$app->queue->push(new UpdateDrawTimesJob([
                    'activity'   => $activityDrawTime,
                    'task_code'  => $taskCode,
                    'task_type'  => $taskLimitType,
                    'user_id'    => $userId,
                    'type'       => 1,
                    'draw_times' => $drawTimes,
                ]));
            }

            return [true, ['status' => 1]];
        } catch (DrawActivityException $e) {
            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.do_task_activity');
            return [false, '获取任务信息失败'];
        }
    }


    /**
     * @throws RedisException
     * 校验邀请好友任务限制
     */
    private function CheckRecommendTask($acId, $acEndTime, $userId, $inviteeId): bool
    {
        $redis    = by::redis();
        $redisKey = AppCRedisKeys::recommendTask($acId, $userId);

        if (!$redis->sIsMember($redisKey, $inviteeId)) {
            $ttl = $acEndTime - time();
            $redis->sAdd($redisKey, $inviteeId);
            CUtil::ResetExpire($redisKey, $ttl);
            return true;
        }

        return false;
    }




    /**
     * 处理心享官事件任务
     * @throws Exception
     * @throws RedisException
     */
    private function handleCustomerServiceTask($userId)
    {
        $user    = by::users()->getRealMainUserById($userId);
        $unionId = $user['unionid'] ?? '';

        // 如果有unionId，则获取oaInfo
        $oaInfo = $unionId ? by::WeFocus()->getOaInfoByUnionId($unionId) : null;

        // 判断$oaInfo是否为空，并返回不同的结果
        return empty($oaInfo) ? [false, ['status' => 0]] : [true, ['status' => 1]];
    }


    /**
     * 抽奖
     * @param int $userId
     * @param int $acId
     * @return array
     * @throws DrawActivityException
     * @throws Exception
     * @throws RedisException
     */
    public function doDrawActivity(int $acId, int $userId): array
    {
        // 0、奖品数量，默认中1个奖品
        $num = 1;

        // 0、获取用户手机号
        $phone = by::Phone()->GetPhoneByUid($userId);
        if (empty($phone)) {
            throw new DrawActivityException('手机号未授权，不可抽奖');
        }

        // 1、活动是否过期
        $drawActivity = byNew::DrawActivity();
        $activity     = $drawActivity->getActivityDetail($acId);
        if (empty($activity) || ($activity['status'] != $drawActivity::STATUS['ON'])) {
            throw new DrawActivityException('活动不存在/已下架');
        }
        $time = time();
        if ($activity['start_time'] > $time) {
            throw new DrawActivityException('活动未开始，敬请期待');
        }
        if ($activity['end_time'] < $time) {
            throw new DrawActivityException('活动已结束，期待下次');
        }

        // 2、用户抽奖次数
        $drawTimes = byNew::DrawActivityTaskRecord()->getUserDrawTimes($userId, $acId);
        if ($drawTimes <= 0) {
            throw new DrawActivityException('抽奖次数为0，不可抽奖');
        }

        // 默认奖品ID
        $defaultPrizeId = $this->getDefaultDrawPrize($acId)['prize_id'];

        // 3、抽中奖品ID
        $prizeId = $this->getDrawPrizeId($acId, $phone, $num) ?: $defaultPrizeId;

        // 4、处理抽奖：扣减缓存数据
        $prizeId = $this->updateCachedPrizeNum($acId, $prizeId, $defaultPrizeId, $num) ?: $prizeId;

        byNew::DrawActivityTaskRecord()->updateDrawTimes($userId, $acId, -$num);

        // 5、更新中奖数量
        $this->updateCachedPrizeRecordCount($acId, $phone, $prizeId, $num);

        // 6、异步处理抽奖
        \Yii::$app->queue->push(new HandleDrawActivityJob([
            'user_id'     => $userId,
            'user_phone'  => $phone,
            'activity_id' => $acId,
            'prize_id'    => $prizeId,
            'consume_type' => 1,
            'consume_gold' => 0,
        ]));

        return [
            'ac_id'    => $acId,
            'prize_id' => $prizeId
        ];
    }
    
    /**
     * 获取抽奖记录
     * @param int $acId
     * @param int $userId
     * @param int $page
     * @param int $pageSize
     * @param string $type
     * @return array
     * @throws Exception
     */
    public function getDrawRecord(int $acId, int $userId, int $page = 1, int $pageSize = 20, string $type = ''): array
    {
        $record = byNew::DrawActivityPrizeRecord();

        if (strtolower($type) == 'draw_gold') {
            // 金币抽奖需要看到所有，即使是谢谢参与
            $prizeType = [
                    $record::PRIZE_TYPES['DEFAULT'],
                    $record::PRIZE_TYPES['POINT'],
                    $record::PRIZE_TYPES['COUPON'],
                    $record::PRIZE_TYPES['EXTERNAL_COUPON'],
                    $record::PRIZE_TYPES['PRICE'],
                    $record::PRIZE_TYPES['GOLD'],
                    $record::PRIZE_TYPES['FREE_AGAIN'],
                    $record::PRIZE_TYPES['CUSTOM_FORM'],
            ];
        } else {
            // 查询条件
            $prizeType = [
                    $record::PRIZE_TYPES['POINT'],
                    $record::PRIZE_TYPES['COUPON'],
                    $record::PRIZE_TYPES['EXTERNAL_COUPON'],
                    $record::PRIZE_TYPES['PRICE'],
                    $record::PRIZE_TYPES['GOLD'],
            ];
        }
        $params = [
                'activity_id' => $acId,
                'user_id'     => $userId,
                'prize_type'  => $prizeType,
        ];

        $items = $record->getPrizeRecordList($params, $page, $pageSize);
        // 总数
        $count = $record->getGoldPrizeRecordCount($params);
        // 获取奖品信息
        $prizes = $this->getPrizeListByIds(array_column($items, 'prize_id'));

        $list = array_map(function ($item) use ($prizes) {
            // 奖品信息
            $prize = $prizes[$item['prize_id']] ?? [];
            return [
                    'id'           => $item['id'],
                    'activity_id'  => $item['activity_id'],
                    'prize_image'  => $prize['image'] ?? '',
                    'prize_name'   => $item['prize_name'],
                    'prize_type'   => $item['prize_type'],
                    'prize_value'  => $item['prize_value'],
                    'prize_num'    => $item['prize_num'],
                    'prize_desc'   => $prize['desc'] ?? '',
                    'draw_time'    => date('Y-m-d H:i', $item['draw_time']),
                    'consume_gold' => (int) ($item['consume_gold'] ?? 0),
            ];
        }, $items);

        return [
                'total' => CUtil::getPaginationPages($count, $pageSize),
                'list'  => $list
        ];
    }

    /**
     * 获取奖品ID
     * 逻辑：
     *     1. 抽中的奖品数量大于等于num，则判断是否达到中奖次数上限；
     *     2. 未达到上限，返回抽中的奖品；
     *     3. 其他返回0
     * @param int $acId
     * @param int $phone
     * @param int $num
     * @return int
     * @throws DrawActivityException
     * @throws RedisException
     */
    public function getDrawPrizeId(int $acId, int $phone, int $num): int
    {
        // 1、奖品
        $prize = $this->getDrawPrize($acId);

        // 2、中奖数量 有缓存
        $prizeRecordCount = byNew::DrawActivityPrizeRecord()->getPrizeRecordCount($acId, $phone);

        // 如果中奖次数限制为不限制，直接返回奖品ID
        if (empty($prize['prize_limit']) && $prize['prize_num'] >= $num) {
            return $prize['prize_id'] ?? 0;
        }

        if (($prize['prize_num'] >= $num) && ($prize['prize_limit'] >= ($prizeRecordCount[$prize['prize_id']] ?? 0) + $num)) {
            return $prize['prize_id'];
        }

        return 0;
    }

    /**
     * 获取奖品ID
     * @param int $acId
     * @return array
     * @throws DrawActivityException
     * @throws RedisException
     */
    private function getDrawPrize(int $acId): array
    {
        // 1、获取奖品列表 有缓存
        $prizes = byNew::DrawActivityPrize()->getPrizeListByAcId($acId);
        if (empty($prizes)) {
            throw new DrawActivityException('暂无奖品-1');
        }

        // 2、验证中奖概率
        $totalRate = array_reduce($prizes, function ($carry, $prize) {
            return $carry + ($prize['rate'] * 10000);
        }, 0);

        if ($totalRate != 1000000) {
            throw new DrawActivityException('中奖概率有误-1');
        }

        // 3、抽奖
        $rand            = mt_rand(1, 1000000);
        $accumulatedRate = 0;

        foreach ($prizes as $prize) {
            $accumulatedRate += $prize['rate'] * 10000;
            if ($rand <= $accumulatedRate) {
                return $prize;
            }
        }

        throw new DrawActivityException('中奖概率有误-2');
    }

    /**
     * 获取抽奖活动中的默认奖品[谢谢参与]
     * @param int $acId
     * @return array
     * @throws DrawActivityException
     * @throws RedisException
     */
    private function getDefaultDrawPrize(int $acId): array
    {
        // 奖品列表
        $prizeList = byNew::DrawActivityPrize()->getPrizeListByAcId($acId);

        $defaultPrizes = array_filter($prizeList, function ($prize) {
            return $prize['is_default'] == 1;
        });

        if (empty($defaultPrizes)) {
            throw new DrawActivityException('暂无奖品-2');
        }

        // 获取第一个数组
        $defaultPrize = reset($defaultPrizes);

        if ($defaultPrize['prize_num'] <= 0) {
            throw new DrawActivityException('暂无奖品-3');
        }

        return $defaultPrize;
    }


    /**
     * @param $userId
     * @param int $acId
     * @return array
     * @throws RedisException
     * 获取任务列表
     */
    public function getTaskList($userId, int $acId): array
    {
        $taskList = [];

        try {
            // 任务ID列表
            $taskIdList = byNew::DrawActivityTask()->getTaskIdsByAcId($acId);

            // 活动详情
            $activityDetail = byNew::DrawActivity()->getActivityDetail($acId);
            $endTime        = $activityDetail['end_time'] ?? 0;
            $phone = by::Phone()->GetPhoneByUid($userId);
            foreach ($taskIdList as $item) {
                $taskId        = $item['task_id'];
                $taskLimitType = $item['task_limit_type'];

                switch ($taskLimitType) {
                    case byNew::DrawActivityTask()::TASK_LIMIT_TYPE['everyday']:
                        $endTime       = mktime(23, 59, 59);
                        $completeCount = byNew::DrawActivityTaskRecord()->getTaskCompleteCount($acId, $taskId, $phone, $endTime);
                        break;
                    case byNew::DrawActivityTask()::TASK_LIMIT_TYPE['activity_period']:
                        $completeCount = byNew::DrawActivityTaskRecord()->getTaskCompleteCount($acId, $taskId, $phone, $endTime);
                        break;
                    default:
                        $completeCount = 0;
                }
                //任务限制条数
                $taskLimit = $item['task_limit'] ?? 0;

                $task = byNew::DrawTask()->getTaskListById($taskId);
                if (empty($task)) continue;
                //如果完成大于等于限制条数 则为已完成 否则为未完成
                $task['status'] = ($completeCount >= $taskLimit) ? 1 : 0;
                // 完成数量 只针对邀请新用户（INVITE_NEW_USER）生效
                $task['number'] = $completeCount;

                if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
                    $task['status'] = 0;
                    $task['number'] = 0;
                }

                $taskList[] = $task;
            }
            return [true, $taskList];
        } catch (Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.do_task_activity');
            return [false, '获取任务列表失败'];
        }
    }

    /**
     * 更新缓存中的奖品数量，使用Lua脚本，保证原子性
     * @param int $acId
     * @param int $prizeId
     * @param int $defaultPrizeId
     * @param int $num
     * @return int
     * @throws RedisException
     */
    private function updateCachedPrizeNum(int $acId, int $prizeId, int $defaultPrizeId, int $num): int
    {
        $prizeListKey = byNew::DrawActivityPrize()->getPrizeListCacheKey($acId);

        $redis = by::redis('core');

        $luaScript = <<<LUA
        local cachedData = redis.call('HGET', KEYS[1], ARGV[1])
        
        local num = tonumber(ARGV[3])
    
        if cachedData 
        then
            local prize = cjson.decode(cachedData)
            if tonumber(prize['prize_num']) >= num 
            then
                prize['prize_num'] = prize['prize_num'] - num
                prize['prize_issue_num'] = prize['prize_issue_num'] + num
                redis.call('HSET', KEYS[1], ARGV[1], cjson.encode(prize))
                return ARGV[1]
            else
                local cachedDefaultData = redis.call('HGET', KEYS[1], ARGV[2])
                local defaultPrize = cjson.decode(cachedDefaultData)
                defaultPrize['prize_num'] = defaultPrize['prize_num'] - num
                defaultPrize['prize_issue_num'] = defaultPrize['prize_issue_num'] + num
                redis.call('HSET', KEYS[1], ARGV[2], cjson.encode(defaultPrize))
                return ARGV[2]
            end
        end
        return 0
LUA;

        return $redis->eval($luaScript, [$prizeListKey, $prizeId, $defaultPrizeId, $num], 1);
    }


    /**
     * 更新缓存中的中奖数量，使用Lua脚本，保证原子性
     * @param int $phone
     * @param int $acId
     * @param int $prizeId
     * @param int $num
     * @return void
     * @throws RedisException
     */
    private function updateCachedPrizeRecordCount(int $acId, int $phone, int $prizeId, int $num)
    {
        // 缓存KEY
        $prizeRecordKey = byNew::DrawActivityPrizeRecord()->getPrizeRecordCountCacheKey($acId, $phone);

        $redis = by::redis('core');

        $luaScript = <<<LUA
        local cachedData = redis.call('HGET', KEYS[1], ARGV[1])
        
        local num = tonumber(ARGV[2])
        
        if cachedData then
            cachedData = cachedData + num
        else
            cachedData = num
        end
        redis.call('HSET', KEYS[1], ARGV[1], cachedData)
        redis.call('EXPIRE', KEYS[1], 1800)
LUA;

        $redis->eval($luaScript, [$prizeRecordKey, $prizeId, $num], 1);
    }


    /**
     * 获取奖品信息
     * @param array $prizeIds
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    private function getPrizeListByIds(array $prizeIds): array
    {
        $prizes = byNew::DrawPrize()->getPrizeListByIds($prizeIds);
        return array_column($prizes, null, 'id');
    }




    const SUCCESS = 1000;         // 成功
    const ACTIVITY_NOT_STARTED = 1001; // 活动未开始
    const ACTIVITY_ENDED = 1002;    // 活动已结束
    const ACTIVITY_INVALID = 1003;   // 活动不存在/已下架
    const PRIZES_EXHAUSTED = 1004;   // 奖品已抽完
    const PHONE_NOT_AUTHORIZED = 1005; // 手机号未授权
    const ALREADY_DRAWN = 1006;      // 已抽过奖
    const INVALID_PRIZE = 1007;      // 奖品已抽完
    const PRIZE_DATA_ERROR = 1008;   // 奖品数据有误
    const NOT_NEW_USER = 1009; // 不是新用户
    const SYSTEM_ERROR = 1010;       // 系统错误

    /**
     * @param int $acRelationId 此ID为 member_activity_module_relation表中主键ID
     * @param int $userId
     * @return int[]
     * 领红包活动处理
     */
    public function doNewDrawActivity(int $acRelationId,  int $userId): array
    {
        try {
            // 奖品队列
            $key = AppNRedisKeys::envelopeActivity($acRelationId);
            // 奖品库存
            $stockKey = AppNRedisKeys::envelopeActivityStock($acRelationId);
            // 抽奖集合 抽过的放在redis中
            $lotteryRedisKe = AppNRedisKeys::lotteryUsers($acRelationId);

            $redis = by::redis();  // 修正组件调用方式

            // 1.校验用户是否已抽过奖
            if ($redis->sIsMember($lotteryRedisKe, $userId)) {
                return $this->buildResponse(self::ALREADY_DRAWN, '您已抽过奖，请勿重复抽奖');
            }

            // 2.活动是否过期
            $activity = MemberActivityModuleRelationModel::getInstance()->getOneByCondition($acRelationId, MemberActivityService::MODULE_RED_ID['RED_ENVELOPE']['id']);
            $extra    = json_decode($activity['extra'], true) ?? [];
            // 校验活动状态
            if (empty($activity) || ($activity['delete_time'] > 0) || empty($extra)) {
                return $this->buildResponse(self::ACTIVITY_INVALID, '活动不存在/已下架');
            }

            $time  = time();
            if ($extra['collect_start_time'] > $time) {
                return $this->buildResponse(self::ACTIVITY_NOT_STARTED, '活动未开始，敬请期待');
            }
            if ($extra['collect_end_time'] < $time) {
                return $this->buildResponse(self::ACTIVITY_ENDED, '活动已结束，期待下次');
            }

            // 3.检查活动库存
            $listLength = $redis->lLen($key);
            if ($listLength <= 0) {
                return $this->buildResponse(self::PRIZES_EXHAUSTED, '奖品已抽完，谢谢参与');
            }

            // 4.获取用户手机号
            $phone = by::Phone()->GetPhoneByUid($userId);
            if (empty($phone)) {
                return $this->buildResponse(self::PHONE_NOT_AUTHORIZED, '手机号未授权，不可抽奖');
            }

            // 4.1 校验用户是否为新用户
            $isCheckUser = $extra['is_check_user'] ?? 0; // 是否校验新用户  1：全部用户可以参加  2：新用户
            if ($isCheckUser == MemberActivityService::USER_TYPE['NEW_USER']) {
                list($status, $message, $data) = $this->isNewUser($userId);
                if (!$status) {
                    return $this->buildResponse(self::NOT_NEW_USER, '当前用户不符合新用户条件，谢谢参与');
                }
            }

            // 5.获取奖品数据
            $prizeJson = $redis->rPop($key);

            if (empty($prizeJson)) {
                return $this->buildResponse(self::INVALID_PRIZE, '奖品已抽完，谢谢参与');
            }

            $prizeData = json_decode($prizeJson, true);

            if (empty($prizeData)) {
                return $this->buildResponse(self::PRIZE_DATA_ERROR, '奖品数据有误，请稍后再试');
            }

            // 6.异步处理抽奖结果
            \Yii::$app->queue->push(new HandleNewDrawActivityJob([
                    'user_id'              => $userId,
                    'user_phone'           => $phone,
                    'activity_relation_id' => $acRelationId,
                    'prize'                => $prizeData,
            ]));

            // 7.记录抽奖用户
            $redis->sAdd($lotteryRedisKe, $userId);

            // 8. 构建返回数据
            $allStock     = (int) $redis->get($stockKey);
            $surplusStock = max(0, $listLength - 1);
            $data         = [
                    'prize_name'    => $prizeData['name'],
                    'prize_value'   => $prizeData['link'] ?? 0,
                    'prize_image'   => $prizeData['image'] ?? '',
                    'all_stock'     => $allStock,    //总库存
                    'surplus_stock' => $surplusStock,//当前剩余库存
            ];

            // 返回中奖信息
            return $this->buildResponse(self::SUCCESS, '抽奖成功', $data);
        } catch (\Exception $e) {
            // 记录异常日志
            \Yii::error('抽奖活动异常: ' . $e->getMessage(), 'err.draw_activity');

            // 返回统一的错误响应
            return $this->buildResponse(self::SYSTEM_ERROR, '系统繁忙，请稍后再试');
        }
    }

    /**
     * 构建标准响应结构
     * @param int $code 错误码
     * @param string $message 提示信息
     * @param array $data 数据
     * @return array
     */
    private function buildResponse(int $code, string $message, array $data = []): array
    {
        $status = $code === self::SYSTEM_ERROR ? 0 : 1;
        return [
                'status'  => $status,
                'code'    => $code,
                'message' => $message,
                'data'    => $data
        ];
    }

    public function getEnvelopeRecord(int $acRelationId, int $userId, int $page = 1, int $pageSize = 20): array
    {
        $record = byNew::EnvelopeRecord();

        // 查询条件
        $params = [
                'activity_relation_id' => $acRelationId,
                'user_id'              => $userId,
        ];

        $items = $record->getEnvelopeRecordList($params, $page, $pageSize);

        return array_map(function ($item) {
            // 奖品信息
            return [
                    'id'          => $item['id'],
                    'activity_id' => $item['activity_relation_id'],
                    'prize_name'  => $item['prize_name'],
                    'prize_value' => $item['prize_value'],
                    'prize_num'   => $item['prize_num'],
                    'draw_time'   => date('Y-m-d', $item['collect_time']),
            ];
        }, $items);
    }


    public function isNewUser($user_id): array
    {
        try {
            if (!YII_ENV_PROD) {
                $dic = CUtil::dictData('is_new_user');
                $user_ids=array_column($dic, 'value');
                if (in_array($user_id, $user_ids)) {
                    return [true, '新用户', ['is_new_user' => true]];
                }
            }
            // 检查用户是否为新用户
            $phone = by::Phone()->GetPhoneByUid($user_id);

            if (empty($phone)) {
                return [false, '手机号未授权', ['is_new_user' => false]];
            }

            $userIds = by::Phone()->GetUidsByPhone($phone, 2);

            if (count($userIds) > 1) {
                return [false, '用户不是新用户', ['is_new_user' => false]];
            }

            // 用户注册时间
            $userInfo    = by::users()->getRealMainUserById($user_id);
            $userRegTime = $userInfo['reg_time'] ?? '';

            if (empty($userRegTime)) {
                return [false, '用户不是新用户', ['is_new_user' => false]];
            }

            // 获取当天日期
            $today = date('Y-m-d');
            // 获取用户注册日期
            $userRegDate = date('Y-m-d', $userRegTime);

            // 检查用户注册日期是否为今天
            if ($userRegDate !== $today) {
                return [false, '用户不是新用户', ['is_new_user' => false]];
            }

            return [true, '新用户', ['is_new_user' => true]];
        } catch (\Exception $e) {
            // 记录错误日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.is_new_user');

            // 返回通用错误信息，避免暴露系统细节
            return [false, '系统繁忙，请稍后再试', ['is_new_user' => false]];
        }
    }
    
    /**
     * 金币抽奖
     * @param int $user_id
     * @param int $acId
     * @return array
     * @throws DrawActivityException
     * @throws Exception
     * @throws RedisException|BusinessException
     */
    public function doGoldDraw(int $acId, int $user_id): array
    {
        // 默认抽中的奖品数量
        $defaultPrizeNum = 1;

        // 获取用户手机号
        $phone = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            throw new DrawActivityException('手机号未授权，不可抽奖');
        }

        // 主活动是否过期
        // $memberActivity = MemberActivityModel::getInstance()->getOne($acId, ['id', 'start_time', 'end_time', 'delete_time']);
        $memberActivity = MemberActivityService::getInstance()->getDetail($acId, true);
        if (empty($memberActivity) || ! empty($memberActivity['delete_time'])) {
            throw new DrawActivityException('活动不存在/已下架');
        }

        $time = time();
        if ($memberActivity['start_time'] > $time) {
            throw new DrawActivityException('活动未开始，敬请期待');
        }
        if ($memberActivity['end_time'] < $time) {
            throw new DrawActivityException('活动已结束，期待下次');
        }

        // $memberActivityRelation = MemberActivityModuleRelationModel::getInstance()->getModuleListByActivityId($acId);
        $memberActivityRelation = $memberActivity['modules'];
        if (empty($memberActivityRelation)) {
            throw new DrawActivityException('抽奖活动未配置1');
        }

        $activityRelation = $memberActivityRelation[0];
        if ($activityRelation['module_res_id'] != MemberActivityModuleResourceModel::DRAW) {
            throw new DrawActivityException('抽奖活动未配置2');
        }
        
        $draw_start_time = $activityRelation['module_data']['draw_start_time'];
        $draw_end_time = $activityRelation['module_data']['draw_end_time'];
        if ($draw_start_time > $time) {
            throw new DrawActivityException('抽奖未开始，敬请期待');
        }
        if ($draw_end_time < $time) {
            throw new DrawActivityException('抽奖已结束，期待下次');
        }

        // $relation_id = $activityRelation['module_relation_id'];
        // $drawActivityRes = DrawActivityModel::find()->where(['module_relation_id' => $relation_id, 'dtime' => 0])->asArray()->one();
        $drawActivityRes = $activityRelation['module_data']['draw_task'][0] ?? [];
        if (empty($drawActivityRes)) {
            throw new DrawActivityException('抽奖活动未配置3');
        }
        $drawActivityId = $drawActivityRes['draw_activity_id'];

        $mux = new LockRedis();

        // 设置分布式锁key
        $unique_key = 'memberCenter|scoreGet|lock|'.$user_id;
        // 设置当前请求的唯一值，用于当前请求只能释放自己的锁
        $unique_val = md5($unique_key.microtime(true).uniqid());
        try {
            // 金币抽奖配置
            $drawConfig = $this->getDrawConfig();
            $drawUnlockConfig = $this->getDrawUnlockConfig();
            
            // 每次消耗金币
            // $everytime_consume_gold = (int) ($drawConfig['everytime_consume_gold']['value'] ?? 0);
            $everytime_consume_gold = $drawActivityRes['consume_type'] == 2 ? ($drawActivityRes['consume_gold'] ?? 0) : 0;
            // 元转分
            $everytime_consume_gold = (int) bcmul($everytime_consume_gold, 100);

            // 解锁抽奖所需总金币
            $min_draw_gold = (int) ($drawUnlockConfig['min_draw_gold']['value'] ?? 0);
            
            if (empty($everytime_consume_gold)) {
                throw new DrawActivityException('抽奖失败，未配置抽奖所需的消耗金');
            }

            // if (empty($min_draw_gold)) {
            //     throw new DrawActivityException('抽奖失败，未配置解锁抽奖所需总金币');
            // }

            // 加锁
            $lock = $mux->lock($unique_key, $unique_val, 10);
            if (! $lock) {
                throw new DrawActivityException('抽奖中，请勿重复操作');
            }

            // // 查询用户剩余金币
            // list($status, $data) = MemberCenter::factory()->scoreGet($user_id);
            // if (! $status) {
            //     CUtil::debug('用户金币查询失败: ' . $data, 'err.doGoldDraw.scoreGet');
            //     throw new DrawActivityException('金币查询失败，请稍后再试~');
            // }

            // 剩余消费金
            $userGold = byNew::UserShopMoneyModel()->getInfoByUserId($user_id, 2,2);
            // 元转分
            $userGold = (int) bcmul($userGold, 100);
            // // 累计获得总金币
            // $totalGoldSum = intval($data['totalGoldSum'] ?? 0);

            // // 是否解锁抽奖 判断总金币是否为空或者小于配置
            // if (empty($totalGoldSum) || $min_draw_gold > $totalGoldSum) {
            //     throw new DrawActivityException('未解锁抽奖，不可抽奖');
            // }

            // 判断剩余金币是否不满足每次消耗
            if (empty($userGold) || $everytime_consume_gold > $userGold) {
                throw new DrawActivityException('消费金不足，不可抽奖');
            }

            // 默认奖品ID
            $defaultPrizeId = (int) ($this->getDefaultDrawPrize($drawActivityId)['prize_id'] ?? 0);

            // 抽中奖品ID
            $prizeId = $this->getDrawPrizeId($drawActivityId, $phone, $defaultPrizeNum) ?: $defaultPrizeId;

            $prizeData = byNew::DrawPrize()->getPrizeListByIds([$prizeId])[0] ?? null;
            if (empty($prizeData)) {
                throw new DrawActivityException(sprintf('抽奖失败，奖品ID：%s 不存在', $prizeId));
            }

            // 抽中非再来一次的奖品，则消耗金币
            $consume_type = 1;
            if ($prizeData['type'] != DrawPrizeModel::PRIZE_TYPES['FREE_AGAIN']) {
                // 先判断是否有抽奖次数
                // $drawTimes = byNew::DrawActivityTaskRecord()->getUserDrawTimes($user_id, $drawActivityId);
                // if ($drawTimes <= 0) {
                    // 无抽奖次数则消耗金币
                    // list($status, $data) = MemberCenter::factory()->syncReduceGold($user_id, $everytime_consume_gold);
                    $status = byNew::UserShopMoneyModel()->AddOrSubtract($user_id, 'subtract', 2, $everytime_consume_gold, $user_id, '盲盒抽奖');
                    if (! $status) {
                        CUtil::debug('用户盲盒抽奖失败:' , 'err.doGoldDraw.AddOrSubtract');
                        throw new DrawActivityException('抽奖失败，请稍后再试');
                    }
                    $consume_type = 2;
                // }
            } else {
                // 再来一次不消耗金币
                $everytime_consume_gold = 0;
                $consume_type = 2;
            }

            // 处理抽奖：扣减缓存数据
            $prizeId = $this->updateCachedPrizeNum($drawActivityId, $prizeId, $defaultPrizeId, $defaultPrizeNum) ?: $prizeId;

            byNew::DrawActivityTaskRecord()->updateDrawTimes($user_id, $drawActivityId, -$defaultPrizeNum);

            // 更新中奖数量
            $this->updateCachedPrizeRecordCount($drawActivityId, $phone, $prizeId, $defaultPrizeNum);
            
            // 异步处理抽奖
            \Yii::$app->queue->push(new HandleDrawActivityJob([
                'user_id'     => $user_id,
                'user_phone'  => $phone,
                'activity_id' => $drawActivityId,
                'prize_id'    => $prizeId,
                'consume_type' => $consume_type,
                'consume_gold' => $everytime_consume_gold,
            ]));
        } catch (\Throwable $e) {
            throw new DrawActivityException($e->getMessage());
        } finally {
            $mux->freed($unique_key, $unique_val);
        }
        
        return [
            'ac_id'    => $acId,
            'prize_id' => $prizeId
        ];
    }

    /**
     * 金币兑换积分
     * @param int $user_id 用户ID
     * @param int $use_gold 使用金币数
     * @return bool
     * @throws DrawActivityException
     */
    public function goldExchangePoint(int $user_id, int $use_gold): bool
    {
        if (empty($use_gold)) {
            throw new DrawActivityException('金币不能为空');
        }

        if (! CUtil::isNumeric($use_gold)) {
            throw new DrawActivityException('金币只能为整数');
        }

        if ($use_gold < 0) {
            throw new DrawActivityException('金币不能为负数');
        }

        $mux = new LockRedis();
        
        // 设置分布式锁key
        $unique_key = 'memberCenter|scoreGet|lock|'.$user_id;
        // 设置当前请求的唯一值，用于当前请求只能释放自己的锁
        $unique_val = md5($unique_key.microtime(true).uniqid());
        try {
            // 金币抽奖配置
            // $config = $this->getDrawConfig();

            // 金币比例
            $exchange_gold_rate = (new PointConfigModel())->getExchangeGoldRate();
            // 积分比例
            // $exchange_point_rate = (int) ($config['exchange_point_rate']['value'] ?? 0);
            $exchange_point_rate = 1;
            if (empty($exchange_gold_rate) || empty($exchange_point_rate)) {
                throw new DrawActivityException('兑换失败，金币或积分比例未配置');
            }
            
            // 获取金币抽奖解锁配置
            $config = $this->getDrawUnlockConfig();
            // 解锁抽奖所需总金币
            $min_exchange_gold = (int) ($config['min_exchange_gold']['value'] ?? 0);
            
            // 判断兑换金币是否小于配置的金币比例
            if ($exchange_gold_rate > $use_gold) {
                throw new DrawActivityException('兑换失败，兑换金币不可小于当前配置的金币比例');
            }
            
            // 转换兑换的金币可兑换的积分
            $point = (int) bcmul((string) $use_gold, (bcdiv((string) $exchange_point_rate, (string) $exchange_gold_rate, strlen($exchange_gold_rate))));
            if (empty($point)) {
                throw new DrawActivityException('兑换失败，比例配置不正确');
            }

            // 加锁
            $lock = $mux->lock($unique_key, $unique_val, 10);
            if (! $lock) {
                throw new DrawActivityException('兑换中，请勿重复操作');
            }

            // 查询用户剩余金币
            list($status, $data) = MemberCenter::factory()->scoreGet($user_id);
            if (! $status) {
                CUtil::debug('用户金币查询失败: ' . $data, 'err.doGoldDraw.scoreGet');
                throw new DrawActivityException('金币查询失败，请稍后再试~');
            }

            // 剩余金币
            $userGold = intval($data['totalGold'] ?? 0);
            // 累计获得总金币
            $totalGoldSum = intval($data['totalGoldSum'] ?? 0);
            
            // 是否解锁兑换 判断总金币是否为空或者小于配置
            if (empty($totalGoldSum) || $min_exchange_gold > $totalGoldSum) {
                throw new DrawActivityException('未解锁兑换，不可兑换');
            }
            
            // 判断剩余金币是否不满足兑换
            if (empty($userGold) || $use_gold > $userGold) {
                throw new DrawActivityException('金币不足，不可兑换');
            }
            
            // 金币兑换积分
            list($status, $data) = MemberCenter::factory()->syncGoldExchangePoint($user_id, $use_gold, $point);
            if (! $status) {
                CUtil::debug('用户金币抽奖失败: ' . $data, 'err.goldExchangePoint.goldExchangePoint');
                throw new DrawActivityException('抽奖失败，请稍后再试');
            }
        } catch (\Throwable $e) {
            throw new DrawActivityException($e->getMessage());
        } finally {
            $mux->freed($unique_key, $unique_val);
        }

        return true;
    }

    /**
     * 获取金币抽奖配置
     * @return array
     * @throws BusinessException
     * @throws DrawActivityException
     */
    public function getDrawConfig(): array
    {
        $config = CUtil::dictData('draw_gold_config');
        if (empty($config)) {
            throw new DrawActivityException('金币抽奖未配置');
        }
        return array_column($config, null, 'label');
    }
    
    /**
     * 获取金币抽奖解锁配置
     * @return array
     * @throws BusinessException
     * @throws DrawActivityException
     */
    public function getDrawUnlockConfig(): array
    {
        $config = CUtil::dictData('draw_gold_unlock_config');
        if (empty($config)) {
            throw new DrawActivityException('金币抽奖解锁路径未配置');
        }
        $unlock_config = [];
        foreach ($config as $item) {
            $label = $item['label'] ?? '';
            $label = explode(':', (string) $label);
            if (count($label) < 2) {
                throw new DrawActivityException('金币抽奖解锁路径配置错误');
            }
            $key = $label[0];
            $name = $label[1];
            $unlock_config[] = [
                'name' => $name,
                'key' => $key,
                'value' => (int) ($item['value'] ?? 0),
            ];
        }
        return array_column($unlock_config, null, 'key');
    }
    
    public function submitGoldDraw(array $data): array
    {
        $user_id = (int) ($data['user_id'] ?? 0);
        $dpr_id = (int) ($data['dpr_id'] ?? 0);
        $realname = htmlspecialchars(trim($data['realname'] ?? ''));
        $phone = htmlspecialchars(trim($data['phone'] ?? ''));
        $address = htmlspecialchars(trim($data['address'] ?? ''));
        
        if (empty($user_id)) {
            return [false, '用户ID不能为空'];
        }
        
        if (empty($dpr_id)) {
            return [false, '中奖记录ID不能为空'];
        }
        
        if (empty($realname)) {
            return [false, '姓名不能为空'];
        }
        
        if (empty($phone)) {
            return [false, '手机号不能为空'];
        }
        
        if (! CUtil::reg_valid($phone, 0)) {
            return [false, '手机号格式错误'];
        }

        $prizeRecord = byNew::DrawActivityPrizeRecord()->getOneById($dpr_id);
        if (empty($prizeRecord)) {
            return [false, '中奖记录不存在1'];
        }

        if ($prizeRecord['user_id'] != $user_id) {
            return [false, '中奖记录不存在2'];
        }

        if ($prizeRecord['prize_type'] != 8) {
            return [false, '此记录不支持提交信息'];
        }

        $model = DrawCustomFormRecordModel::getInstance();
        $submitRecord = $model->getSubmitRecordByDprId($dpr_id);
        if (! empty($submitRecord)) {
            return [false, '已提交过中奖信息，请勿重复提交'];
        }

        $data['address'] = $address;
        list($status, $id, $msg) = $model->doCreate($data);

        if (! $status) {
            return [false, $msg];
        }
        
        return [true, 'success'];
    }


    /**
     * 公共方法：抽奖记录列表二次加工（处理提交状态、商品信息等）
     * @param array $list 原始抽奖记录列表
     * @return array 加工后的列表
     */
    public function processDrawRecordList(array $list)
    {
        // 1. 提取抽奖记录ID，查询提交记录
        $dprIds = array_unique(array_column($list, 'id'));
        $submitRecords = DrawCustomFormRecordModel::getInstance()->getListByDprIds($dprIds);
        $submitRecordMap = array_column($submitRecords, null, 'dpr_id');

        // 2. 遍历加工列表（用引用减少数组赋值）
        foreach ($list as &$item) {
            $item['is_submit'] = -1; // 默认：未定义提交状态

            // 处理奖品类型8（需提交收货信息）
            if ($item['prize_type'] == 8) {
                $item['submit_info'] = [];
                if (isset($submitRecordMap[$item['id']])) {
                    $submitRecord = $submitRecordMap[$item['id']];
                    $item['is_submit'] = 1;
                    $item['submit_info'] = [
                            'id' => $submitRecord['id'] ?? 0,
                            'realname' => $submitRecord['realname'] ?? '',
                            'phone' => $submitRecord['phone'] ?? '',
                            'address' => $submitRecord['address'] ?? '',
                    ];
                } else {
                    $item['is_submit'] = 0;
                }
            }
            // 处理奖品类型3（商品类奖品）
            elseif ($item['prize_type'] == 3) {
                $marketConfig = by::marketConfig()->getOneById($item['prize_value']);
                $cGoodsVal = $marketConfig['c_goods_val'] ?? '';
                $goodsIds = empty($cGoodsVal) ? [] : explode(',', $cGoodsVal);
                // 简化赋值：是否单个商品ID
                $item['c_goods_val_single'] = (count($goodsIds) === 1) ? 1 : 0;
                $item['c_goods_val'] = $cGoodsVal;
            }
        }
        unset($item); // 解除引用，避免后续意外修改

        return $list;
    }
}
