<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 商品主表
 */
namespace app\modules\goods\services;


use app\jobs\CancelPayOrderJob;
use app\models\by;
use app\models\CUtil;

class OmainService{


    /**
     * @return array
     * @throws \yii\db\Exception
     * 自动取消订单记录
     */
    public function CancelTailOrder(): array
    {
        $wait          = by::Omain()::ORDER_STATUS['WAIT_PAY'];

        $etime         = intval(START_TIME); //两小时前的数据置为已取消状态
        $id            = 0;

        $where         = " `status`={$wait} AND `ctime`<{$etime}";
        $where         .= " AND `type`= 2 AND `end_payment`< {$etime} AND `end_payment` > 0";

        $db            = by::dbMaster();

        //分表可能跨年
        $this_year     = date("Y");
        $years         = [$this_year,date("Y",strtotime("-3 days"))];
        $years         = array_unique($years);
        foreach ($years as $year) {
            if($year < 2022) {
                continue;
            }

            //确定分表
            $date      = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime     = strtotime($date);
            $tb_main   = by::Omain()::tbName($ctime);

            while (true) {
                $sql    = "SELECT `id`,`order_no`,`user_id` FROM {$tb_main} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

                $list   = $db->createCommand($sql, [':id'=>$id])->queryAll();

                if (empty($list)) {
                    break;
                }

                $end    = end($list);
                $id     = $end['id'];

                foreach($list as $val) {
                    list($status,$ret) = by::Omain()->Recycle($val['user_id'], $val['order_no']);
                    if(!$status){
                        CUtil::debug($val['order_no'].'|'.$val['user_id'].'|'.json_encode($ret,320), 'err.recycle');
                    }
                    // 取消支付单，塞队列中去处理
                    \Yii::$app->queue->push(new CancelPayOrderJob([
                        'order_no' => $val['order_no']
                    ]));
                }
            }

        }
        return [true,"OK"];
    }



}
