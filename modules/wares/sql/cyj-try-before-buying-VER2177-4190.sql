-- db_dreame_wares库

-- 商品详情信息表
CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_goods_try_buying`
(
    `id`          int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `name`        varchar(100)  NOT NULL DEFAULT '' COMMENT '商品名',
    `cover_image` varchar(2000) NOT NULL DEFAULT '' COMMENT '封面图（1：小程序，2：APP）json数据类型',
    `video`       varchar(255)  NOT NULL DEFAULT '' COMMENT '视频',
    `images`      varchar(2000) NOT NULL DEFAULT '' COMMENT '图片集（最多10张）',
    `mprice`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '市场价（原价：分）',
    `introduce`   varchar(255)  NOT NULL DEFAULT '' COMMENT '简介',
    `label`       varchar(50)   NOT NULL DEFAULT '' COMMENT '商品标签',
    `limit_num`   tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '限购数',
    `detail`      varchar(5000) NOT NULL DEFAULT '' COMMENT '商品详情',
    `platform`    tinyint(3) unsigned NOT NULL DEFAULT '99' COMMENT '平台（1:微信小程序;2:app/H5平台;99:全平台）',
    `online_time` varchar(100)  NOT NULL DEFAULT '' COMMENT '上架时间',
    `shipping`    tinyint(3) unsigned NOT NULL DEFAULT '2' COMMENT '是否包邮（1:否;2:是）',
    `is_del`      tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime`       int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`       int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`       int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_gid` (`gid`) USING BTREE,
    KEY           `idx_name` (`name`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='先试后买商品表';

-- 商品价格信息表
CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_goods_try_buying_price`
(
    `id`     int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '多规格id',
    `sku`    varchar(50)  NOT NULL DEFAULT '' COMMENT '商品编码',
    `price`  int(11) unsigned NOT NULL DEFAULT '0' COMMENT '价格（分）',
    `image`  varchar(500) NOT NULL DEFAULT '' COMMENT '小图（多规格）',
    `is_del` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime`  int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`  int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`  int(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY      `idx_gid_sid` (`gid`,`sid`) USING BTREE
    )DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='先试后买商品价格表';

-- 活动配置表
CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_activity`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `name`           varchar(255)  NOT NULL default '' COMMENT '活动名称',
    `grant_type`     tinyint(1) unsigned NOT NULL default '1' COMMENT '活动类型 1 先用后买',
    `start_time`     int(11) unsigned NOT NULL default '0' COMMENT '开始时间',
    `end_time`       int(11) unsigned NOT NULL default '0' COMMENT '结束时间',
    `status`         tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否下架 0否 1是',
    `is_delete`      tinyint(1) unsigned NOT NULL default '0' COMMENT '是否删除 0 未删除 1删除',
    `create_time`    int(11) unsigned NOT NULL default '0' COMMENT '添加时间',
    `update_time`    int(11) unsigned NOT NULL default '0' COMMENT '修改时间',
    `delete_time`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`)
    )default CHARSET = utf8mb4 COMMENT = '活动配置表';

-- 先试后买活动配置表
CREATE TABLE if NOT EXISTS  `db_dreame_wares`.`t_activity_type`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `ac_id`          int(10) NOT NULL DEFAULT '0' COMMENT '活动id',
    `poster_image`   varchar(255)  NOT NULL DEFAULT '' COMMENT '活动海报',
    `share_image`    varchar(255)  NOT NULL DEFAULT '' COMMENT '分享海报',
    `try_goods_ids`  varchar(100)  NOT NULL DEFAULT '0' COMMENT '试用商品ID',
    `try_quota`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '试用名额',
    `apply_number`   int(11) unsigned NOT NULL DEFAULT '0' COMMENT '已申请人数',
    `validity`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '有效期x天',
    `survey_key`     varchar(200)  NOT NULL DEFAULT '' COMMENT '问卷key',
    `pass_mark`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '及格分数',
    `join_condition` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '参与条件 0：不限制 1：申请过活动的 2：通过过问卷的活动 3：体验过机器的活动',
    `ac_ids`         varchar(100)  NOT NULL DEFAULT '' COMMENT '限制条件活动ID',
    `rule`           varchar(2000) NOT NULL DEFAULT '' COMMENT '活动规则',
    `create_time`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
    `update_time`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_ac` (`ac_id`) USING BTREE
    )DEFAULT CHARSET=utf8mb4 COMMENT='活动配置表(先用后买)';

-- 问卷记录表
CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_survey_record`
(
    `id`         int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`    int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `uid`        varchar(50) NOT NULL DEFAULT '' COMMENT 'IOT平台UID',
    `ac_id`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `form_key`   varchar(50) NOT NULL DEFAULT '0' COMMENT '问卷key',
    `score`      int(11) NOT NULL DEFAULT '0' COMMENT '分数',
    `pass_score` int(11) NOT NULL DEFAULT '0' COMMENT '当前活动通过分数',
    `ctime`      int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`      int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_user_ac_id` (`user_id`, `ac_id`) USING BTREE
    )DEFAULT CHARSET = utf8mb4 COMMENT = '用户问卷记录表';

-- 支付宝口令表
CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_zfb_password`
(
    `id`                   INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `zfb_password`         VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '支付宝口令',
    `password_expire_time` INT(11) NOT NULL DEFAULT '0' COMMENT '支付宝口令过期时间',
    `qr_code`              VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '二维码链接',
    `zfb_link`             varchar(255)  NOT NULL COMMENT '支付宝链接',
    `link_expire_time`     INT(11) NOT NULL DEFAULT '0' COMMENT '支付宝链接过期时间',
    `ctime`                INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`                INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 COMMENT='支付宝芝麻日志表';


-- db_dreame库
-- 创建小程序用户SQL
CREATE TABLE if NOT EXISTS `db_dreame`.`t_users_platform`
(
    `id`             int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`        int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
    `openudid` varchar(50) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识',
    `unionid` varchar(200) NOT NULL DEFAULT '' COMMENT '用户在开放平台的唯一标识符',
    `reg_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
    `user_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户类型 20小程序用户',
    `uid` varchar(50) NOT NULL DEFAULT '' COMMENT 'IOT平台UID',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `openudid` (`openudid`) USING BTREE
    )default CHARSET = utf8mb4 COMMENT = '平台用户表';

-- 先试后买用户表
CREATE TABLE if NOT EXISTS `db_dreame`.`t_user_try`
(
    `id`      int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `uid`     varchar(50)   NOT NULL DEFAULT '' COMMENT '其他平台 UID',
    `phone`   varchar(20)   NOT NULL DEFAULT '' COMMENT '手机号码',
    `status`  tinyint(3) NOT NULL DEFAULT '0' COMMENT '用户状态 0：正常、1：拉黑',
    `reason`  varchar(500) NOT NULL DEFAULT '' COMMENT '拉黑原因',
    `ctime`   int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`   int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unq_user_id` (`user_id`) USING BTREE
    )DEFAULT CHARSET = utf8mb4 COMMENT = '先试后买用户表';

-- 用户链路表
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_users_path`
(
    `id`                INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `uid`               varchar(18)  NOT NULL DEFAULT '' COMMENT '用户 UID',
    `user_id`           int(10) NOT NULL DEFAULT '0' COMMENT '用户 商城ID',
    `ac_id`             int(10) NOT NULL DEFAULT '0' COMMENT '活动id',
    `phone`             varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号码',
    `nick_name`         varchar(50)  NOT NULL DEFAULT '' COMMENT '企业微信昵称',
    `avatar`            varchar(200) NOT NULL DEFAULT '' COMMENT '头像URL',
    `source`            varchar(100) NOT NULL DEFAULT '' COMMENT '推广来源',
    `is_auth`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否微信授权（0：未授权，1：已授权）',
    `is_sign_agreement` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否签署协议（0：未签署，1：已签署）',
    `is_enter_zfb`      tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否进入支付宝（0：未进入，1：已进入）',
    `is_enter_survey`   tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否进入问卷（0：未进入，1：已进入）',
    `is_submit_survey`  tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否提交问卷（0：未提交，1：已提交）',
    `is_pass_survey`    tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否通过问卷（0：未通过，1：已通过）',
    `is_send`           tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否发货（0：未发货，1：已发货）',
    `ctime`             INT (11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`             INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unq_u_a` (`user_id`,`ac_id`) USING BTREE,
    KEY                 `idx_phone` (`phone`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 COMMENT='用户链路表';


-- db_dreame_goods库

-- 用户先试后买订单表
CREATE TABLE if NOT EXISTS `db_dreame_goods`.`t_uo_try`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `order_no`      varchar(50)  NOT NULL DEFAULT '' COMMENT '订单号',
    `auth_no`       varchar(100) NOT NULL DEFAULT '' COMMENT '支付宝资金授权订单号',
    `ac_id`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `amount`        int(11) unsigned NOT NULL DEFAULT '0' COMMENT '抵扣金额（分）',
    `try_status`    int unsigned NOT NULL DEFAULT '0' COMMENT '试用状态',
    `goods_name`    varchar(100) NOT NULL DEFAULT '' COMMENT '商品名',
    `label`         varchar(50)  NOT NULL DEFAULT '' COMMENT '商品标签',
    `sn`            varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `back_time`     int(11) NOT NULL DEFAULT '0' COMMENT '归还时间',
    `arrival_time`  int(11) NOT NULL DEFAULT '0' COMMENT '用户到货时间',
    `register_time` int(11) NOT NULL DEFAULT '0' COMMENT '用户产品注册时间',
    `ctime`         int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`         int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unq_o` (`order_no`) USING BTREE,
    UNIQUE KEY `unq_u_o` (`user_id`,`order_no`) USING BTREE
    )DEFAULT CHARSET = utf8mb4 COMMENT = '用户先试后买订单';

-- 先试后买订单转化表
CREATE TABLE if NOT EXISTS `db_dreame_goods`.`t_uo_try_conversion`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `order_no`      varchar(50)  NOT NULL DEFAULT '' COMMENT '订单号',
    `ac_id`         int(11) unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `model`         varchar(50)  NOT NULL DEFAULT '' COMMENT '产品model',
    `label`         varchar(50)  NOT NULL DEFAULT '' COMMENT '商品标签',
    `ctime`         int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`         int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unq_u_m` (`user_id`,`model`) USING BTREE
    ) DEFAULT CHARSET = utf8mb4 COMMENT = '用户先试后买订单转化表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_iot_product`
(
    `id`        INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `model`  VARCHAR(50) NOT NULL DEFAULT '' COMMENT '产品model',
    `name`      VARCHAR(50) NOT NULL DEFAULT '' COMMENT 'model对应产品名称',
    `ctime`     INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`     INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `unq_model` (`model`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 COMMENT='IOT产品表';


-- db_dreame_log库
-- 支付宝芝麻日志表
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_zhima_log`
(
    `id`        INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `order_no`  VARCHAR(50) NOT NULL DEFAULT '' COMMENT '订单号',
    `type`      VARCHAR(50) NOT NULL DEFAULT '' COMMENT '回调类型',
    `resources` TEXT        NOT NULL COMMENT '回调json数据',
    `ctime`     INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`     INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX       `idx_order_no` (`order_no`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 COMMENT='支付宝芝麻日志表';





-- 商品相关新增/修改字段
ALTER TABLE `db_dreame_goods`.`t_uo_try`
  ADD `pay_time` int(11) NOT NULL DEFAULT '0' COMMENT '支付时间' AFTER `sn`,
  ADD `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '最新退货订单号',
  ADD `over_time` int(11) NOT NULL DEFAULT '0' COMMENT '完结时间' AFTER `back_time`,
  ADD `fund_amount` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '自有资金金额',
  ADD `credit_amount` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '信用金额',
  ADD `phone_str` varchar(50) NOT NULL DEFAULT '' COMMENT '手机号加密字符串';

ALTER TABLE `db_dreame_goods`.`t_uo_try_conversion`
    ADD `active_time` int(11) NOT NULL DEFAULT '0' COMMENT '激活时间' AFTER `label`,
    ADD `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码' AFTER `label`,
    DROP INDEX `unq_u_m`,
    ADD UNIQUE KEY `unq_u_m_s` (`user_id`, `model`, `sn`) USING BTREE;



-- 用户相关新增/修改字段
ALTER TABLE `db_dreame`.`t_users_platform` modify `openudid` varchar(200) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识';
-- 增加企业微信用户推广来源
ALTER TABLE `db_dreame`.`t_we_focus`
    ADD `source` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '推广来源' AFTER `store`,
    MODIFY `resources` TEXT NOT NULL COMMENT '资源内容 json数据';



-- 先试后买商品/活动相关 新增/修改字段
ALTER TABLE `db_dreame_wares`.`t_goods_try_buying` ADD `try_price` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '市场价（原价：分）';
ALTER TABLE `db_dreame_wares`.`t_survey_record` ADD `audit_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '审核状态（0待审核 1审核通过 2审核不通过）' AFTER `pass_score`;
ALTER TABLE `db_dreame_wares`.`t_activity_type`
    ADD `return_period` int(11) NOT NULL DEFAULT '0' COMMENT '退机有效期 单位天',
    ADD `delivery_period` int(11) NOT NULL DEFAULT '0' COMMENT '收货有效期 单位天',
    ADD `is_audit` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否人工审核（0否 1是）' AFTER `pass_mark`;




-- 修改字符集
ALTER TABLE `db_dreame_wares`.`t_activity_type` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_wares`.`t_activity` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_uo_try` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2023` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2024` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2025` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2026` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2027` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2028` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2029` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_om_2030` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame`.`t_user_try` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_goods`.`t_orefund_main` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci;
ALTER TABLE `db_dreame_admin`.`t_sys_log`
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
    MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备注';
ALTER TABLE `db_dreame_admin`.`t_sys_log_2024`
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
    MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备注';
ALTER TABLE `db_dreame_admin`.`t_sys_log_2025`
    CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
    MODIFY COLUMN `text` TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '备注';







-- 后台权限
-- 商品管理
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (605, 'tryBeforeBuy', 0, '', '先用后付', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (606, 'TryGoodsListManager', 605, 'try-goods/list', '商品管理', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (607, 'TryGoodsList', 606, 'back/try-goods/list', '商品列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (608, 'WaresGoodsSale', 606, 'back/try-goods/sale', '商品上下架', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (609, 'WaresGoodsInfo', 606, 'back/try-goods/info', '商品详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (610, 'WaresGoodsDel', 606, 'back/try-goods/del', '商品删除', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (611, 'WaresGoodsSave', 606, 'back/try-goods/save', '商品创建/编辑', 1711934843, 0, 1, 0, 0, '');

-- 活动管理
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (612, 'ActivityWaresManager', 605, 'activity/list', '活动列表', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (613, 'ActivityWaresList', 612, 'back/activity/list', '活动列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (614, 'ActivityWaresSave', 613, 'back/activity/modify', '活动创建/编辑', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (615, 'ActivityWaresDetail', 613, 'back/activity/detail', '活动详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (616, 'ActivityWaresStatus', 613, 'back/activity/status', '活动上下架', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (617, 'ActivityWaresDel', 613, 'back/activity/del', '活动删除', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (629, 'ActivitySurveyKeyList', 613, 'back/activity/survey-key', '活动问卷列表', 1711934843, 0, 1, 0, 0, '');

-- 订单管理
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (618, 'tryOrderManager', 605, 'tryOrder/list', '订单管理', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (619, 'tryOrderList', 618, 'back/try-orders/list', '订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (620, 'tryRefundList', 618, 'back/try-orders/refund-list', '退款订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (621, 'tryExceptionList', 618, 'back/try-orders/exception-list', '异常订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (622, 'tryOrderInfo', 618, 'back/try-orders/info', '订单详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (623, 'tryOrderRefundInfo', 618, 'back/try-orders/refund-info', '退款订单详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (624, 'tryOrderBegin', 618, 'back/try-orders/exception-begin', '订单开始试用', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (645, 'tryOrderRaudit', 618, 'back/try-orders/exception-raudit', '订单完结/强制扣款', 1711934843, 0, 1, 0, 0, '');


-- 用户管理
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (625, 'tryUserManager', 605, 'tryUser/list', '用户管理', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (626, 'tryUserList', 625, 'back/try-user/list', '用户列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (627, 'tryUserInfo', 625, 'back/try-user/info', '用户详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (628, 'tryUserShield', 625, 'back/try-user/list', '用户-拉黑/移除黑名单', 1711934843, 0, 1, 0, 0, '');

--支付宝口令维护
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (632, 'zfbPassword', 605, 'tryPassword/list', '支付宝口令维护', 1716187970, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (633, 'zfbPasswordInfo', 632, 'back/data/zfb-password-info', '支付宝口令维护详情', 1716187970, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (634, 'zfbPasswordSave', 632, 'back/data/zfb-password', '支付宝口令维护保存', 1716187970, 0, 1, 0, 0, '');

-- 人工审核
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (635, 'tryAuditManager', 605, 'tryUser/list', '人工审核', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (636, 'tryAuditList', 635, 'back/data/survey-audit-list', '人工审核列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (637, 'tryAudit', 635, 'back/data/survey-audit', '审核', 1711934843, 0, 1, 0, 0, '');

-- 用户链路
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (640, 'tryUserPathManager', 605, 'tryUser/list', '用户链路', 1711934843, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (641, 'tryUserPathList', 640, 'back/try-user/user-path-list', '用户链路列表', 1711934843, 0, 1, 0, 0, '');







-- 插入iot产品数据
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2009', '追觅灵图D9 扫拖机器人', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2218', 'vestel-2218', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2029', '追觅L10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2027', '追觅W10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2104', '追觅W10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2028a', '追觅L10 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2028', '追觅Z10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2008', '追觅慧目F9 扫拖机器人', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2259', 'Dreame Bot D9 Max', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2205', '追觅 D10 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.p2275', '追觅R2275', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2228', '追觅S10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2204', 'dreame.vacuum.r2204', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2206', 'dreame.vacuum.r2206', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2212', 'MOVA G30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2215', '追觅X10 Pro（原尊享版）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2225', 'dreame.vacuum.r2225', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2232', '追觅W10s Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2233', '追觅S10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2228o', 'L10s Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2232a', '追觅W10s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2246', '追觅S10 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2247', '追觅S10 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2239', '追觅L10S PLUS', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2235', '追觅X10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2216o', 'DreameBot L10s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2250', '追觅D10s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2240', 'DreameBot D10s Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2243', 'DreameBot D10s', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2251o', '追觅W10s', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2251a', 'DreameBot L10 Prime ', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2257o', 'DreameBot L10 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2255', '追觅R2255', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253', '追觅X20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2260', 'DreameBot C9', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2312', 'DreameBot D9 Max(White)', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2314', '追觅W20 Pro_u', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2316', '追觅S20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2315', '追觅X20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2313', '追觅S20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2235a', 'DreameBot X10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2317', '追觅W20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2315a', 'r2315a', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2319', '追觅X10 曜金黑', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2320', '追觅X10 Pro（813）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2312a', 'DreameBot D9 Max', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2232b', 'DreameBot L10s Prime', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2322', '追觅D9 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2263', '追觅X20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2216', 'W2216', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2215a', 'DreameBot L12 Beta', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2328', 'DreameBot F9 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2322b', '追觅D9 Plus（black）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2316o', '追觅S20 S', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2316p', '追觅S20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2298', 'R2298', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2311', 'Dreame H40 Pro Plus Mix', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306', 'H30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2345', '追觅W20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2316', '洗地机w2316', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2317', 'Dreame H40 Pro plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2318', 'Dreame H40 Mix', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2319', 'w2319', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2313p', '追觅S20 Pro Plus（清洁液版）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2228d', 'DreameBot L10s Ultra Special Edition', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2345a', '追觅W20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2216b', 'w2216', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2332', '追觅S20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2273', '追觅X20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253b', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253c', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253t', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253w', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2335', '追觅X20t Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2345h', '追觅W20 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306a', 'w2306a', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.ow2306', '同方2306', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2356', '追觅S20 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2355', '追觅S20 Pro热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2342', '追觅W20 Plus_u', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253m', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2338', '追觅S20 Pro 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2350', 'MOVA G20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2273a', '追觅X20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.r0000', 'mower_demo', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2322', 'Dreame H40 Station', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2360', '追觅S10 Pro Plus 热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2370', '追觅S20 热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2362', '追觅L10s Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2263b', '追觅X20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2361a', 'DreameBot L30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2361g', '暂停项目', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2367', 'DreameBot L10s Ultra Heat-tech', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.ow2316', '同方2316', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2368', '追觅W20s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2369', '追觅W20s Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253u', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2353', '追觅S20s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2354', '追觅S30 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2253d', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310', '追觅Master Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2372', '追觅X20s', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2375', '追觅X30', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2373', '追觅X30-2373', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2336', 'r2336', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2363', 'L10s Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2387', 'MOVA全能10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2388', 'MOVA扫拖10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2386', 'MOVA免洗10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2311a', 'Dreame H40 Pro Plus Mix', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2390', '追觅X20p Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2391', 'r2391', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394', '追觅X20p Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2395', 'r2395', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2398', '追觅S20 Plus 热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.r9527', 'mower', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2381', 'MOVA G10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394f', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394l', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394a', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394j', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9301', '追觅X30 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9302', '追觅S20 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9304', '追觅S10 Pro Ultra 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9305', '追觅S10 Pro Plus机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394s', 'DreameBot L20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394k', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2394u', 'DreameBot L20 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2390a', 'DreameBot L30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2338a', 'L10s Pro Ultra Heat', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2377', 'L10s Pro Ultra Heat', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2380', 'Dreame MOVA M1', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9308', 'S20 Pro 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2382', 'Dreame Mova S10（2382）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310a', '追觅Master One', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9310', '追觅S20 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9311', '追觅S10 Pro 机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9309a', 'L10s Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9312', 'S10 Pro Ultra 双滚刷机械臂版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2415', 'DreameBot X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9313', 'DreameBot L20 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9314', 'DreameBot L30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.p2255', 'A1', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2235c', '追觅X20C', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2257a', 'DreameBot L10 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9314h', 'DreameBot L20 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9313h', 'DreameBot L30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9315h', 'DreameBot X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9316h', 'X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9317h', 'L30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2380r', 'Dreame TROUVER M1', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2416', '追觅X40 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2413', 'Dreame Mova S20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2412', '追觅S30 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9314a', 'DreameBot L20 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9313a', 'DreameBot L30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9315a', 'X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9316a', 'X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9317a', 'L30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2421', '追觅S10 Pro热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9316k', 'X30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2401', 'X40 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2414', 'Dreame Mova S20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310b', 'S10 Pro Ultra超薄嵌入版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2316a', 'H30 躺平版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9315k', 'X30 Ultra-黑', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.p3255', 'p2255_test', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('p2255t', 'p2255t', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2364', 'L10s Pro Gen 2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2420', 'MOVA P10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.swbot.y2301', '泳池机器人600s', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2422', 'Dreame D9 Max Gen 2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2423', 'Dreame D10 Plus Gen 2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2385', 'MOVA G20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2383', 'Dreame Trourver S10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2382a', 'Dreame Mova S10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2382r', 'Dreame Trouver S10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2383a', 'Dreame Mova S10 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2384', 'MOVA G10 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2424', '追觅S30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2425', '追觅S40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2360w', 'S10 Pro Max 热水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2297', 'MR527', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2440', '追觅X40 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2228z', 'L10s Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2435', 'MOVA G30', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2363a', 'L10s Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2364a', 'L10s Pro Gen 2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2431', 'J40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2432', 'MOVA CX1 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310f', 'X30 Pro 超薄嵌入版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310g', 'S20 Pro 超薄嵌入版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306b', '2306B', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2455', 'MOVA G30 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306c', 'H30 Combo Station', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306d', 'H14 Pro(美版）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2316d', 'H20 Ultra 系列', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2437', '追觅S30 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310e', 'X40 Pro 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310d', 'S30 Pro Ultra 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2310c', 'S30 Pro 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2416a', 'X40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2450a', 'Master One 欧版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2450m', 'X30 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2410', 'r2410', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2445', 'X40 Pro Ultra 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2416n', 'X40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2449a', 'X40 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2449k', 'X40 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2462', 'MOVA P10s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2385a', 'S20 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2419', 'X50', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2416c', 'X40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2433', 'r2433', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2416h', 'X40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2464', 'r2464', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2458', '无限动力LDS单机', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2459', '无限动力LDS单集尘', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2427', 'MOVA P10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2422a', 'Dreame D9 Max Gen 2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2468', 'X30 Ultra Complete', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2426', '追觅X40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2316b', 'H14', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2422', 'H20 Ultra 旋锋版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2430', 'G40履带+外摆', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2439', 'H20 Ultra Mix', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2436', 'G40 平板拖+外摆', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2447', 'D40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2461', '免洗J40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2480', '追觅S30 Pro Ultra双线激光', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306h', 'H14 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2438r', 'Dreame Trouver E10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2438a', 'Dreame Mova E10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2438i', 'Dreame Mova E10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2482', '追觅清洁大师Zenith One', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2472', 'P10s Pro 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2413b', 'Dreame Trouver S20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2414b', 'Dreame Trouver S20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2306e', 'H14 Pro (欧版)', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2467', '追觅X40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2483', '追觅X40 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2465', 'X40 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2438b', 'Dreame Mova E10', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2473', 'r2473', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.p3255d', 'p2255_dev', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.mower.p3254', 'p2255_dev1', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2465a', 'X40 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2465b', 'X40 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2465m', 'X40 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2470a', 'L40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2465h', 'X40 Master', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2484', 'r2484', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2318e', 'H14 Dual', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2436', 'H20 Ultra Station', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2419s', 'r2419s', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2485', '追觅S30 铂金版  ', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2486', 'MOVA P10s Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2489', 'r2489', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2491', 'MOVA P10s Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2488', '追觅X30s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2453', 'X50 Pro 单滚', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2473s', 'X50 Pro 超薄上下水 双滚', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2453s', 'X50 Pro 超薄上下水 单滚', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2492a', 'L40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2469a', 'L10s Ultra Gen2', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2478c', 'Dreame Trouver E20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2478v', 'Dreame Trouver E20 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2463k', 'Dreame Trouver E20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2463r', 'Dreame Trouver E20 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2495', 'X40 Pro Ultra 超薄上下水版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2316c', 'H14（台版）', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2492b', 'L40 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2458a', 'Dreame Mova E20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2458h', 'Dreame Mova E20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2459a', 'Dreame Mova E20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2459h', 'Dreame Mova E20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2459r', 'Dreame Mova E20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2422p', 'H30 Ultra 旋锋版', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.hold.w2439p', 'H30 Ultra Mix', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2487', '追觅S40 Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9401', '追觅S40', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2471', 'L10 Prime', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2427a', 'Mova E30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2427c', 'Trouver E30 Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('ahd6a', 'Dreame Hair Glory ', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2490', 'r2490', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2493', '追觅X40 Pro Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9408', '追觅X30s Pro', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9406', 'MOVA P10 Pro Ultra', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r9420', 'r9420', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2413c', 'Dreame Trouver S20', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);
INSERT INTO `t_iot_product` (`model`, `name`, `ctime`, `utime`) VALUES ('dreame.vacuum.r2414c', 'Dreame Trouver S20 Plus', UNIX_TIMESTAMP(),UNIX_TIMESTAMP())ON DUPLICATE KEY UPDATE `name` = VALUES(`name`), `utime` = VALUES(`utime`);




-- 客服申请订单退款权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (646, 'tryOrderRefund', 618, 'back/try-orders/exception-refund', '客服申请退款', 1711934843, 0, 1, 0, 0, '');

-- 用户链路新增人工审核节点
ALTER TABLE `db_dreame`.`t_users_path` ADD `is_manual_audit`  tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否人工审核通过（0：未通过，1：已通过）';