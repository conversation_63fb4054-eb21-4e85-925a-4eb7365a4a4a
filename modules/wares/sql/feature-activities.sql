CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_activity` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL DEFAULT '' COMMENT '活动名称',
    `grant_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '发放方式 1 先用后买',
    `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
    `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除 1删除',
    `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COMMENT='活动配置表';
