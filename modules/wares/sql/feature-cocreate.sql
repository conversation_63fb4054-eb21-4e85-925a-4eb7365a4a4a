CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_cocreate_user` (
    `id` int(10)  unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户ID',
    `uid` varchar(50) NOT NULL DEFAULT '' COMMENT 'UID',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号码',
    `nick` varchar(10) NOT NULL DEFAULT '' COMMENT '昵称',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 正常 1 已注销',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建事件',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY  `idx_user_id` (`user_id`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='共创人员权限表';


CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_cocreate_material` (
    `id` int(10)  unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `name` varchar(100) NOT NULL DEFAULT '' COMMENT '素材名称',
    `topic` varchar(50) NOT NULL DEFAULT '' COMMENT '素材主题',
    `url` varchar(500) NOT NULL DEFAULT '' COMMENT '素材URL',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建事件',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='共创素材表';


CREATE TABLE if NOT EXISTS `db_dreame_wares`.`t_cocreate_statistic` (
    `id` int(10)  unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户ID',
    `material_id` int(10) NOT NULL DEFAULT '0' COMMENT '素材ID',
    `enjoy` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0:未知 1:喜欢 2：不喜欢',
    `remark` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建事件',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY  `idx_user_id` (`user_id`) USING BTREE,
    UNIQUE KEY  `uniq_user_material` (`user_id`,`material_id`) USING BTREE
 ) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='共创数据表';


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (550, 'CoCreateManager', 0, '', '共创管理', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (551, 'CoCreateUser', 550, 'co-create-user/record', '共创人员管理', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (552, 'CoCreateUserList', 551, 'back/co-create-user/list', '共创人员列表', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (553, 'CoCreateUserAdd', 551, 'back/co-create-user/add', '共创人员添加', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (554, 'CoCreateUserDel', 551, 'back/co-create-user/del', '共创人员批量删除', 1644579242, 0, 1, 0, 0, '');



INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (560, 'CoCreateMaterial', 550, 'co-create-material/record', '共创素材管理', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (561, 'CoCreateMaterialList', 560, 'back/co-create-material/list', '共创素材列表', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (562, 'CoCreateMaterialSave', 560, 'back/co-create-material/save', '共创素材添加', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (565, 'CoCreateMaterialEdit', 560, 'back/co-create-material/edit', '共创素材编辑', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (563, 'CoCreateMaterialDel', 560, 'back/co-create-material/del', '共创素材批量删除', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (564, 'CoCreateMaterialDetail', 560, 'back/co-create-material/detail', '共创素材详情', 1644579242, 0, 1, 0, 0, '');


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (570, 'CoCreateStatistic', 550, 'co-create-statistic/record', '数据统计管理', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (571, 'CoCreateStatisticList', 570, 'back/co-create-statistic/list', '共创数据列表', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (572, 'CoCreateStatisticDetail', 570, 'back/co-create-statistic/detail', '共创数据详情', 1644579242, 0, 1, 0, 0, '');



CREATE INDEX idx_statistic_user_enjoy ON db_dreame_wares.t_cocreate_statistic(material_id, user_id, enjoy);
CREATE INDEX idx_material_topic_isdel ON db_dreame_wares.t_cocreate_material(id, topic, is_del);

ALTER TABLE `db_dreame_wares`.`t_cocreate_statistic` MODIFY COLUMN `enjoy` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0:未知 1:喜欢 2：不喜欢 3:超级喜欢';
