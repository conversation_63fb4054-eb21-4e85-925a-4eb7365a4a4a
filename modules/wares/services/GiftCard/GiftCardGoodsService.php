<?php

namespace app\modules\wares\services\GiftCard;

use app\exceptions\GiftCardGoodsException;
use app\models\byNew;
use app\modules\wares\models\GiftCardGoodsModel;

class GiftCardGoodsService
{
    private static $_instance = NULL;

    public function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取卡片列表
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array
     * @throws \yii\db\Exception
     */
    public function getCardGoodsList(array $params, int $page = 1, int $pageSize = 20): array
    {
        // 获取商品列表
        $goodsList = byNew::GiftCardGoods()->getGoodsList($params, $page, $pageSize);
        $list = [];
        $typeName = GiftCardGoodsModel::TYPE_NAME;
        $statusName = GiftCardGoodsModel::STATUS_NAME;

        // 激活量
        $cardGoodsIds = array_column($goodsList['list'], 'id');
        $actNums = byNew::GiftCardResources()->getActNumByCardGoodsIds($cardGoodsIds);

        // 处理数据
        foreach ($goodsList['list'] as $goods) {
            $list[] = [
                'id'          => $goods['id'],
                'sku'         => $goods['sku'],
                'name'        => $goods['name'],
                'price'       => bcdiv($goods['price'], 100, 2),
                'stock'       => $goods['stock'],
                'act_num'     => $actNums[$goods['id']] ?? 0,
                'type'        => $goods['type'],
                'type_name'   => $typeName[$goods['type']] ?? '',
                'status'      => $goods['status'],
                'status_name' => $statusName[$goods['status']] ?? '',
            ];
        }
        return [
            'list'      => $list,
            'page'      => $page,
            'page_size' => $pageSize,
            'total'     => $goodsList['total']
        ];
    }

    /**
     * 获取卡商品详情
     * @param int $id
     * @return array
     */
    public function getCardGoodsDetail(int $id): array
    {
        $goods = byNew::GiftCardGoods()->getGoodsById($id);
        if (!$goods) {
            return [];
        }

        // 卡资源
        $resources = json_decode($goods['card_resource'], true);

        $goods['price'] = bcdiv($goods['price'], 100, 2);
        $goods['images'] = $goods['images'] ? explode(',', $goods['images']) : [];
        $goods['card_resource'] = array_values($resources);

        $typeName = GiftCardGoodsModel::TYPE_NAME;
        $statusName = GiftCardGoodsModel::STATUS_NAME;

        return array_merge($goods, ['type_name' => $typeName[$goods['type']] ?? '', 'status_name' => $statusName[$goods['status']] ?? '']);
    }

    /**
     * 添加或编辑商品
     * @param array $params
     * @return void
     * @throws GiftCardGoodsException
     * @throws \yii\db\Exception
     */
    public function saveCardGoods(array $params)
    {
        $id = $params['id'] ?? null;
        $this->validateCardGoods($id, $params);

        // 准备共通的数据字段
        $data = [
            'sku'           => $params['sku'],
            'name'          => $params['name'],
            'web_name'      => $params['web_name'],
            'cover_image'   => $params['cover_image'],
            'images'        => $params['images'],
            'start_time'    => $params['start_time'],
            'end_time'      => $params['end_time'],
            'card_resource' => $params['card_resource'],
            'price'         => bcmul($params['price'], 100),
            'status'        => $params['status'],
            'utime'         => time(),
        ];

        if (!$id) { // 新增
            $data['type'] = $params['type'];
            $data['stock'] = 0;
            $data['ctime'] = time();
        }

        byNew::GiftCardGoods()->store($id, $data);
    }

    /**
     * 校验
     * @param $id
     * @param array $params
     * @return void
     * @throws GiftCardGoodsException
     */
    private function validateCardGoods($id, array $params)
    {
        if ($id) { // 编辑
            $goods = byNew::GiftCardGoods()->getGoodsById($id);
            if (!$goods) {
                throw new GiftCardGoodsException("卡商品不存在");
            }
        } else { // 新增
            $goods = byNew::GiftCardGoods()->getGoodsBySku($params['sku']);
            if ($goods) {
                throw new GiftCardGoodsException("SKU已存在");
            }
        }
        $this->validateNameAndWebName($id, $params['name'], $params['web_name']);
        // 卡资源是否重复
        if ($params['card_resource']) {
            $cardResources = json_decode($params['card_resource'], true);
            $cardIds = array_column($cardResources, 'card_id');
            $cardIds = array_unique($cardIds);
            if (count($cardIds)!= count($cardResources)) {
                throw new GiftCardGoodsException("卡资源重复");
            }
            $cards = byNew::GiftCard()->getDataListByIds($cardIds);
            if (count($cards)!= count($cardIds)) {
                throw new GiftCardGoodsException("卡资源不存在");
            }
        }

    }

    /**
     * 校验名称
     * @param $id
     * @param $name
     * @param $webName
     * @return void
     * @throws GiftCardGoodsException
     */
    private function validateNameAndWebName($id, $name, $webName)
    {
        $goodsByName = byNew::GiftCardGoods()->getGoodsByName($name);
        if ($goodsByName && ($goodsByName['id'] != $id)) {
            throw new GiftCardGoodsException("内显名称已存在");
        }
        $goodsByWebName = byNew::GiftCardGoods()->getGoodsByWebName($webName);
        if ($goodsByWebName && ($goodsByWebName['id'] != $id)) {
            throw new GiftCardGoodsException("外显名称已存在");
        }
    }
}