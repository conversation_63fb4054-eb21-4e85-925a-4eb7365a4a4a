<?php

namespace app\modules\wares\services\GiftCard;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class GiftCardResourcesService
{
    private static $_instance = NULL;



    public function __construct()
    {
    }



    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $params
     * @param $page
     * @param $pageSize
     * @return array
     * 卡资源列表
     */
    public function getCardResourcesList($params, $page, $pageSize): array
    {
        //查询参数
        $param = [
            'begin_time'    => $params['begin_time'] ?? '',
            'end_time'      => $params['end_time'] ?? '',
            'card_no'       => $params['card_no'] ?? '',
            'card_goods_id' => $params['card_goods_id'] ?? '',
            'act_user_id'   => $params['act_user_id'] ?? '',
            'type'          => $params['type'] ?? '',
            'status'        => $params['status'] ?? '',
        ];
        $list  = byNew::GiftCardResources()->getCardResourcesList($param, $page, $pageSize);
        foreach ($list['list'] as &$value){
            $goods = byNew::GiftCardGoods()->getGoodsById($value['card_goods_id']);
            $value['type'] = $goods['type'] ?? '';
        }
        //总页数
        $pages = CUtil::getPaginationPages($list['total'], $pageSize);
        return [true, ['list' => $list['list'], 'page' => $pages]];
    }


    public function discardResource($id,$status): array
    {
        $resourcesStatus = byNew::GiftCardResources()::STATUS;
        if (!in_array($status,$resourcesStatus)){
            return [false,'作废失败'];
        }
        return byNew::GiftCardResources()->discardResource($id,$status);
    }
}
