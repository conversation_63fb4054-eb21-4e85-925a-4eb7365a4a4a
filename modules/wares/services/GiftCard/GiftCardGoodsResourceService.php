<?php

namespace app\modules\wares\services\GiftCard;

use app\exceptions\GiftCardGoodsException;
use app\models\by;
use app\models\byNew;
use app\modules\wares\models\GiftCardGoodsModel;
use app\modules\wares\models\GiftCardResourcesModel;

class GiftCardGoodsResourceService
{
    private static $_instance = NULL;

    public function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 添加礼品卡
     *
     * @param int $goodsId 商品ID
     * @param array $cards 卡片信息数组
     * @throws GiftCardGoodsException
     */
    public function addCards(int $goodsId, array $cards)
    {
        // 1. 校验卡号是否重复
        $cardNos = array_column($cards, 0);
        if (count(array_unique($cardNos)) != count($cards)) {
            throw new GiftCardGoodsException('卡号存在重复');
        }

        // 2. 校验卡密是否重复
        $passwords = array_column($cards, 1);
        if (count(array_unique($passwords)) != count($cards)) {
            throw new GiftCardGoodsException('卡密存在重复');
        }

        // 3. 校验商品是否存在
        $goods = byNew::GiftCardGoods()->getGoodsById($goodsId);
        if (!$goods) {
            throw new GiftCardGoodsException('卡商品不存在');
        }

        // 4. DB校验卡号是否重复
        $resources = byNew::GiftCardResources()->getResourcesByCardNos($cardNos);
        if (!empty($resources)) {
            $cardNosStr = implode(",", array_column($resources, 'card_no'));
            throw new GiftCardGoodsException(sprintf('卡号已存在：%s', $cardNosStr));
        }

        // 5. DB校验卡密是否重复
        $resources = byNew::GiftCardResources()->getResourcesByPasswords($passwords);
        if (!empty($resources)) {
            $passwordsStr = implode(",", array_column($resources, 'card_password'));
            throw new GiftCardGoodsException(sprintf('卡密已存在：%s', $passwordsStr));
        }

        $data = $this->prepareCardData($goodsId, $cards);

        $this->insertCardData($goodsId, $data);
    }

    /**
     * 卡数据
     * @param int $goodsId
     * @param array $cards
     * @return array
     */
    private function prepareCardData(int $goodsId, array $cards): array
    {
        $data = [];
        $now = time();
        foreach ($cards as $card) {
            $data[] = [
                'card_goods_id' => $goodsId,
                'card_no'       => $card[0],
                'card_password' => $card[1],
                'status'        => 1,
                'ctime'         => $now,
                'utime'         => $now,
            ];
        }
        return $data;
    }

    /**
     * @param int $goodsId
     * @param array $data
     * @return void
     * @throws GiftCardGoodsException
     */
    private function insertCardData(int $goodsId, array $data)
    {
        $db = by::dbMaster();
        $tran = $db->beginTransaction();
        try {
            $goods = byNew::GiftCardGoods()->getGoodsById($goodsId);
            $stock = $goods['stock'] + count($data);

            $db->createCommand()->update(
                GiftCardGoodsModel::tableName(),
                ['stock' => $stock],
                ['id' => $goodsId]
            )->execute();

            $db->createCommand()->batchInsert(
                GiftCardResourcesModel::tableName(),
                ['card_goods_id', 'card_no', 'card_password', 'status', 'ctime', 'utime'],
                $data
            )->execute();

            $tran->commit();
        } catch (\Exception $e) {
            $tran->rollBack();
            throw new GiftCardGoodsException('添加失败');
        }
    }

    /**
     * 生成卡资源
     *
     * @param string $prefix
     * @param int $amount
     * @return array
     * @throws \Exception
     */
    public function generateCardResources(string $prefix = 'DM', int $amount = 1): array
    {
        // 卡号、卡密
        $cards = [];
        $passwords = [];
        $datePrefix = $prefix . date('ymd');

        $batchSize = 1000;

        while (count($cards) < $amount || count($passwords) < $amount) {

            // 1. 生成卡号
            $batchCards = [];
            while (count($batchCards) < $batchSize && count($cards) + count($batchCards) < $amount) {
                $card = $datePrefix . $this->generateRandomStr(12);
                if (!isset($cards[$card])) {
                    $batchCards[$card] = true;
                }
            }
            // 1.2 去重
            if ($batchCards) {
                $exitsCards = byNew::GiftCardResources()->getResourcesByCardNos(array_keys($batchCards));
                $exitsCards = array_column($exitsCards, 'card_no');
                foreach ($batchCards as $card => $v) {
                    if (!in_array($card, $exitsCards)) {
                        $cards[$card] = true;
                    }
                }
            }


            // 2. 生成卡密
            $batchPasswords = [];
            while (count($batchPasswords) < $batchSize && count($passwords) + count($batchPasswords) < $amount) {
                $password = wordwrap($this->generateRandomStr(16), 4, '-', true);
                if (!isset($passwords[$password])) {
                    $batchPasswords[$password] = true;
                }
            }
            // 2.2 去重
            if ($batchPasswords) {
                $exitsPasswords = byNew::GiftCardResources()->getResourcesByPasswords(array_keys($batchPasswords));
                $exitsPasswords = array_column($exitsPasswords, 'card_password');
                foreach ($batchPasswords as $password => $v) {
                    if (!in_array($password, $exitsPasswords)) {
                        $passwords[$password] = true;
                    }
                }
            }
        }

        // 3. 返回
        return array_map(null, array_keys($cards), array_keys($passwords));
    }

    /**
     * 生成随机字符串
     * @param int $length
     * @return string
     * @throws \Exception
     */
    private function generateRandomStr(int $length = 16): string
    {
        if ($length < 1) {
            return '';
        }
        $str = strtoupper(bin2hex(random_bytes($length)));
        return substr($str, 0, $length);
    }
}