<?php

namespace app\modules\wares\services\GiftCard;


use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class GiftCardsService
{
    private static $_instance = NULL;

    protected $giftCardModel;

    public function __construct()
    {
        $this->giftCardModel = byNew::GiftCard();
    }



    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $post
     * @return array
     * 保存礼品卡 -- 卡表
     */
    public function save($post): array
    {
        try {
            //1、解析参数
            $name       = $post['name'] ?? '';
            $webName    = $post['web_name'] ?? '';
            $image      = $post['image'] ?? '';
            $amount     = CUtil::totalFee($post['amount'] ?? 0);
            $goodsIds   = $post['goods_ids'] ?? '';
            $goodsIds   = str_replace("，", ",", $goodsIds);
            $sku        = $post['sku'] ?? '';
            $expireDays = intval($post['expire_days'] ?? '');
            $type       = intval($post['type'] ?? '');
            $desc       = $post['desc'] ?? '';
            $id         = $post['id'] ?? '';

            //2、校验参数
            if (empty($name)) {
                return [false, '对内名称不能为空'];
            }

            list($s, $nameInfo) = $this->giftCardModel->getGiftCardInfo('name', $name);
            if ($nameInfo && $nameInfo['id'] != $id) {
                return [false, '对内名称已存在'];
            }

            if (empty($webName)) {
                return [false, '对外名称不能为空'];
            }

            list($s, $webNameInfo) = $this->giftCardModel->getGiftCardInfo('web_name', $webName);
            if ($webNameInfo && $webNameInfo['id'] != $id) {
                return [false, '对外名称已存在'];
            }

            if (empty($image)) {
                return [false, '请上传礼品卡封面图'];
            }

            if (empty($type)) {
                return [false, '请选择礼品卡类型'];
            }

            if ($type == $this->giftCardModel::TYPE['ASSIGN_EXCHANGE'] && empty($goodsIds)) {
                return [false, '指定兑换卡必须配置适用商品'];
            }

            if ($type == $this->giftCardModel::TYPE['STORAGE_CASH'] && empty($amount)) {
                return [false, '储值现金卡必须配置礼品卡金额'];
            }

            if (empty($expireDays)) {
                return [false, '请配置使用有效期'];
            }
            if ($type == 2 && strlen($sku) == 0) {
                return [false, '请配置兑换商品SKU'];
            }
            $input = [
                'name'        => $name,
                'web_name'    => $webName,
                'image'       => $image,
                'amount'      => $amount,
                'goods_ids'   => $goodsIds,
                'sku'         => $sku,
                'expire_days' => $expireDays,
                'type'        => $type,
                'desc'        => $desc,
                'utime'       => intval(START_TIME),
            ];

            //3、入库
            if ($id) {
                //编辑  删除不允许编辑数据
                unset($input['amount'], $input['type'], $input['expire_days']);
            } else {
                //添加
                $input['ctime'] = intval(START_TIME);
            }
            return $this->giftCardModel->saveData($input, $id);
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.save_gift_card');
            return [false, '保存失败'];
        }
    }


    /**
     * @param $post
     * @param $page
     * @param int $page_size
     * @return array
     * 卡列表
     */
    public function getGiftCardList($post, $page, int $page_size): array
    {
        try {
            $list = $this->giftCardModel->getCardList($post, $page, $page_size);

            //总页数
            $pages = CUtil::getPaginationPages($list['total'], $page_size);
            foreach ($list['list'] as &$value) {
                $value['amount'] = bcdiv($value['amount'], 100, 2);
            }
            return [true, ['list' => $list['list'], 'page' => $pages]];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.get_gift_card_list');
            return [false, '列表获取失败'];
        }
    }

    /**
     * 获取礼品卡
     * @return array
     */
    public function getGiftCards(): array
    {
        $list = $this->giftCardModel->getCards(['id', 'name']);
        return [true, $list];
    }


    /**
     * @param $id
     * @return array
     * 卡详情
     */
    public function getGiftCardInfo($id): array
    {
        try {
            if (empty($id)) {
                return [false, '礼品卡ID不能为空'];
            }
            return $this->giftCardModel->getGiftCardInfo('id', $id);
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.get_card_info');
            return [false, '详情获取失败'];
        }
    }


    /**
     * @param $id
     * @param $status
     * @return array
     * 审核礼品卡
     */
    public function audit($id, $status): array
    {
        try {
            if (empty($id)) {
                return [false, '请选择审核礼品卡'];
            }

            if (!in_array($status, array_keys($this->giftCardModel::STATUS))) {
                return [false, '审核状态不存在'];
            }

            $status = $this->giftCardModel::STATUS[$status];
            return $this->giftCardModel->audit($id, $status);
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.audit_card_list');
            return [false, '审核失败'];
        }
    }



}
