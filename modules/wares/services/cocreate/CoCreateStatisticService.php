<?php

namespace app\modules\wares\services\cocreate;

use app\components\AliYunOss;
use app\jobs\InsertUserCoCreateMaterialJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class CoCreateStatisticService
{
    private static $_instance = NULL;

    protected $coCreateStatisticModel;
    protected $coCreateMaterialModel;


    public function __construct()
    {
        $this->coCreateStatisticModel = byNew::CoCreateStatisticModel();
        $this->coCreateMaterialModel = byNew::CoCreateMaterialModel();

    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * 后台获取列表
     * @throws \yii\db\Exception
     */
    public function GetList($input, $page, $page_size)
    {
        $return = ['list' => [], 'pages' => 1];
        $count  = $this->coCreateStatisticModel->GetListCount($input);
        if (empty($count)) {
            return $return;
        }
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        //获取图片ID
        $ids = $this->coCreateStatisticModel->GetListByInput($input, $page, $page_size);

        //数据总数
        $countSumList = $this->coCreateStatisticModel->GetCountByInput(['material_ids'=>$ids]);
        $countSumArr = array_column($countSumList,'num','material_id');

        $countEnjoyList = $this->coCreateStatisticModel->GetCountByInput(['material_ids'=>$ids,'enjoy'=>1]);
        $countEnjoyArr = array_column($countEnjoyList,'num','material_id');

        $topicArr        = array_column($this->coCreateMaterialModel::TOPIC_LIST, 'value', 'key');
        foreach ($ids as $id) {
            $info                  = $this->coCreateMaterialModel->GetOneById($id);
            $info['topic_name']    = $topicArr[$info['topic']] ?? '';
            $info['sum']           = $countSumArr[$id] ?? 0;
            $info['enjoy']         = $countEnjoyArr[$id] ?? 0;
            $info['no_enjoy']      = bcsub($info['sum'], $info['enjoy']);
            $info['enjoy_rate']    = round($this->safe_divide($info['enjoy'], $info['sum'],5) * 100);
            $info['no_enjoy_rate'] = round($this->safe_divide($info['no_enjoy'], $info['sum'],5) * 100);
            $return['list'][]      = $info;
        }
        return $return;
    }

    private function safe_divide($dividend, $divisor, $precision = 2) {
        return ($divisor > 0) ? bcdiv($dividend, $divisor, $precision) : 0;
    }

    /**
     * @throws \yii\db\Exception
     */
    public function GetDetailList($material_id, $input, $page, $page_size): array
    {
        $return = ['list' => [], 'pages' => 1];
        $count  = $this->coCreateStatisticModel->GetDetailListCount($material_id,$input);
        if (empty($count)) {
            return $return;
        }
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        //获取图片ID
        $return['list'] = $this->coCreateStatisticModel->GetDetailListByInput($material_id,$input, $page, $page_size);
        return $return;
    }

    public function GetIfUseByUserId($user_id): bool
    {
        $user_id = intval($user_id);
        $key = $this->coCreateStatisticModel->GetIFUseByUser($user_id);
        $redis = by::redis();
        $value = $redis->get($key);
        return !empty($value);
    }

    public function GetUserStatisticList($user_id,$input,$page,$page_size)
    {
        $return = ['list'=>[],'pages'=>1];
        if(empty($user_id)||strlen($user_id)>12){
            return $return;
        }
        $count = $this->coCreateStatisticModel->GetListCountByUser($user_id,$input);
        if(empty($count)){
            return $return;
        }
        $return['pages']    = CUtil::getPaginationPages($count, $page_size);
        //获取图片ID
        $list       = $this->coCreateStatisticModel->GetListByUser($user_id,$input,$page,$page_size);
        if($list && empty($input['topic'])){
            foreach ($list as $k => $v){
                $material_info= $this->coCreateMaterialModel->GetOneById($v['material_id']);
                $list[$k]['name'] = $material_info['name'];
                $list[$k]['url'] = $material_info['url'];
            }
        }
        $return['list'] = $list;
        return $return;
    }

}
