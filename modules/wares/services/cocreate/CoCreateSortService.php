<?php

namespace app\modules\wares\services\cocreate;

use app\jobs\UpdateCoCreateUserJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\enums\cocreate\SortEnum;

class CoCreateSortService
{
    private static $_instance = NULL;

    protected $coCreateUserModel;
    protected $coCreateMaterialModel;

    public function __construct()
    {
        $this->coCreateUserModel     = byNew::CoCreateUserModel();
        $this->coCreateMaterialModel = byNew::CoCreateMaterialModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    public function GetSortByTopic($topic)
    {
        $list = [];
        $redis = by::redis();
        $r_key = SortEnum::IMAGE_SORT_REDIS_KEY.'|'.$topic;
        $allPhotosScores = $redis->zrevrange($r_key, 0, -1,true);
        if(empty($allPhotosScores)) {
            return $list;
        }

        // 输出前10张评分最高的图片
        $i = 0;
        foreach($allPhotosScores as $photoId => $score) {
            if($i >= 10) {
                break;
            }
            $i++;
            $info = $this->coCreateMaterialModel->GetOneById($photoId);
            $info['score'] = $score;
            $list[]= $info;
        }
        return $list;
    }

}








