<?php

namespace app\modules\wares\services\cocreate;

use app\components\AliYunOss;
use app\components\AppWRedisKeys;
use app\jobs\InsertUserCoCreateMaterialJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\enums\cocreate\SortEnum;

class CoCreateMaterialService
{
    private static $_instance = NULL;

    protected $coCreateMaterialModel;
    protected $coCreateStatisticModel;


    public function __construct()
    {
        $this->coCreateMaterialModel = byNew::CoCreateMaterialModel();
        $this->coCreateStatisticModel = byNew::CoCreateStatisticModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * 后台获取列表
     * @throws \yii\db\Exception
     */
    public function GetList($input, $page, $page_size)
    {
        $return = ['list' => [], 'pages' => 1];
        $count  = $this->coCreateMaterialModel->GetListCount($input);
        if (empty($count)) {
            return $return;
        }
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $topicArr        = array_column($this->coCreateMaterialModel::TOPIC_LIST, 'value', 'key');
        //获取图片ID
        $ids = $this->coCreateMaterialModel->GetListByInput($input, $page, $page_size);
        foreach ($ids as $id) {
            $info               = $this->coCreateMaterialModel->GetOneById($id);
            $info['topic_name'] = $topicArr[$info['topic']] ?? '';
            $return['list'][]   = $info;
        }
        return $return;
    }




    public function Del($ids)
    {
        return $this->coCreateMaterialModel->Del($ids);
    }


    public function Edit($post)
    {
        $id    = intval($post['id'] ?? 0);
        $topic = trim($post['topic'] ?? '');
        $name  = trim($post['name'] ?? '');

        if (empty($id) || empty($topic) || empty($name)) {
            return [false, '缺少必填参数！'];
        }
        if (strlen($name) > 100) return [false, '图片名过长'];
//        //名称校验
//        $count  = $this->coCreateMaterialModel->GetListCount(['check_name'=>$name,'nid'=>$id]);
//        if($count>0) return [false,'素材名称已存在，不允许重复'];

        $res = $this->coCreateMaterialModel->UpdateInfo($id, ['topic' => $topic, 'name' => $name]);
        return [$res, empty($res) ? '编辑失败' : 'ok'];
    }

    public function Save($topic, $file, $backUserId)
    {
        @ini_set('post_max_size', '20M');
        @ini_set('upload_max_filesize', '20M');
        $fileName = $file['name'];
        if (strlen($fileName) > 100) return [false, '图片名称不能大于100个字'];

        $time = time();
        list($s, $url) = AliYunOss::factory()->uploadFileToOss($file, [], false, 2);
        if (!$s) return [false, $url];

        //文件名不能重复校验
        $info = $this->coCreateMaterialModel->GetOneByName($fileName,$topic);
        if($info){
            $id = $info['id'];
            $arr = [
                'topic' => $topic,
                'url'   => $url['bigImg'] ?? '',
                'name'  => $fileName,
                'utime' => time()
            ];
            $res = $this->coCreateMaterialModel->UpdateInfo($id,$arr);
            return [$res, empty($res) ? '更新成功' : 'ok'];
        }

        $arr = [
            'topic' => $topic,
            'url'   => $url['bigImg'] ?? '',
            'name'  => $fileName,
            'ctime' => $time,
            'utime' => $time
        ];
        $res = $this->coCreateMaterialModel->SaveInfo($arr);
        return [$res, empty($res) ? '保存失败' : 'ok'];
    }

    public function GetRandListByUserId($user_id,$page,$page_size=20)
    {
        $return = [
            'page' => $page,
            'page_size' => $page_size,
            'total_pages' => 0,
            'list' => [],
        ];

        $list = $this->coCreateMaterialModel->GetMainListByUserId($user_id);


        $positionKey = $this->coCreateMaterialModel->UserEnjoyPositionKey($user_id);

        $redis = by::redis('core');
        $position = intval($redis->get($positionKey) ?? 0);

        // 如果找到键，则从该位置开始截取数组，否则返回空数组
        $list = array_slice($list, $position + 1, null, true);

        if (empty($list)){
            return  $return;
        }

        // 计算总页面数
        $totalPages = ceil(count($list) / $page_size);

        // 计算当前页的第一条数据的索引
        $pageStart = ($page - 1) * $page_size;

        // 提取当前页的数据
        $pageData = array_slice($list, $pageStart, $page_size);


        foreach ($pageData as $k => $v) {
            $pageData[$k]['position'] = $pageStart + $k +1;
        }

        // 返回分页数据和相关信息
        $return['total_pages'] = $totalPages;
        $return['list'] = $pageData;
        return $return;
    }


    public function GetNowListByUserId($user_id,$topic,$inputPage,$page_size=20)
    {

        $return = [
            'page' => 1,
            'page_size' => $page_size,
            'total_pages' => 2,
            'list' => [],
        ];

        if($inputPage >= 3){
            return $return;
        }

        $positionKey = $this->coCreateMaterialModel->UserEnjoyPositionKey($user_id,$topic);

        $redis = by::redis('core');
        $position = intval($redis->get($positionKey) ?? 0);

        $list = $this->coCreateMaterialModel->GetListByRange($topic,$position,$page_size);
//        if(empty($list)){ //补偿措施直查数据库
//            $list = $this->coCreateMaterialModel->GetNowList($topic,$position, 1, $page_size);
//        }

        $count = count($list);
        if ($count < $page_size) {
            $return['page'] = 2;
        } elseif (empty($count)) {
            $return['page'] = 3;
            return $return;
        }

        // 返回分页数据和相关信息
        $return['list'] = $list;
        return $return;
    }

    public function Enjoy($user_id,$post)
    {
        if(strlen($user_id)>12) $user_id = 0;
        $user_id = intval($user_id);
        if(empty($user_id)) return [false,'请先授权后重试！'];
        $materialId = intval($post['material_id'] ?? 0);
        $enjoy = intval($post['enjoy'] ?? 0);
        $position = intval($post['position']?? 0);
        $topic = trim($post['topic']?? '');
        if(empty($materialId)) return [false,'素材必须选择！'];
        if(empty($enjoy)) return [false,'必须选择喜好！'];
        if(empty($position)) return [false,'打标位置必须填写！'];
        if(empty($topic)) return [false,'主题必须填写！'];

        $redis = by::redis('core');
        //频率限制 每100ms一次
        $cmtime = floor(START_TIME*10);
        $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id, $cmtime);
        $s = $redis->set($unique_key, $user_id, ['nx', 'ex'=>1]);
        if(!$s){
            return [false,'请仔细评价图片哟！'];
        }

        $key = $this->coCreateStatisticModel->GetIFUseByUser($user_id);
        $redis->set($key, $user_id, ['nx', 'ex'=>24*3600*30]);

        //1.用户存储读取位置
        $positionKey = byNew::CoCreateMaterialModel()->UserEnjoyPositionKey($user_id,$topic);
        $redis->set($positionKey,$position);

        //异步处理
        \Yii::$app->queue->push(new InsertUserCoCreateMaterialJob([
            'user_id'     => $user_id,
            'material_id' => $materialId,
            'enjoy'       => $enjoy,
            'position'    => $position,
            'remark'      => CUtil::removeXss($post['remark'] ?? ''),
        ]));


        //2.图片计分
          $this->AddImageSort($materialId,$topic,$enjoy);

//        byNew::CoCreateStatisticModel()->InsertUserEnjoyData($user_id,$materialId,$enjoy,$post['remark']);
        return [true,'OK！'];
    }


    public function AddImageSort($materialId,$topic,$enjoy)
    {
        $redis = by::redis('core');
        // 定义评分映射，这将简化你的if-else结构
        $scoreMapping = [
            1 => SortEnum::IMAGE_LIKE_SCORE,
            3 => SortEnum::IMAGE_SUPER_LIKE_SCORE,
            0 => SortEnum::IMAGE_DISLIKE_SCORE, // 似乎为0时无操作，假设默认值为0，如果不是这个逻辑，相应调整
        ];

        // 使用 isset 来检查$enjoy对应的分数是否存在，不存在则使用默认分数0
        $score = $scoreMapping[$enjoy] ?? 0;

        // 只有当分数大于0时才将评分和图片ID存入Redis
        if ($score > 0) {
            $r_key = SortEnum::IMAGE_SORT_REDIS_KEY.'|'.$topic;
            $redis->zIncrBy($r_key, $score, $materialId);
        }

    }

}
