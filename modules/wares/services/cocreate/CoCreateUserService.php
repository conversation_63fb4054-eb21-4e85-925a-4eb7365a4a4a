<?php

namespace app\modules\wares\services\cocreate;

use app\jobs\UpdateCoCreateUserJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class CoCreateUserService
{
    private static $_instance = NULL;

    protected $coCreateUserModel;

    public function __construct()
    {
        $this->coCreateUserModel = byNew::CoCreateUserModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * 后台获取列表
     * @throws \yii\db\Exception
     */
    public function GetList($input,$page,$page_size)
    {
        $return = ['list'=>[],'pages'=>1];
        $count = $this->coCreateUserModel->GetListCount($input);
        if(empty($count)){
            return $return;
        }
        $return['pages']    = CUtil::getPaginationPages($count, $page_size);
        //获取图片ID
        $ids       = $this->coCreateUserModel->GetListByInput($input,$page,$page_size);
        foreach ($ids as $id) {
            $return['list'][] = $this->coCreateUserModel->GetOneById($id);
        }
        return $return;
    }


    public function GetInfoByUserId($user_id)
    {
        strlen($user_id)>12 && $user_id = 0;
        if(empty($user_id)) return true;
        $user_id = intval($user_id);

        $info = $this->coCreateUserModel->GetOneByUserId($user_id);
        if (!empty($info) && $info['is_del'] == 0) {
            return true;
        }

        //查出是否有权限
        $phone = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)){
            return false;
        }
        //查表
        $count = $this->coCreateUserModel->GetListCount(['phone' => $phone]);
        if($count>0){
            //异步更新用户信息
            \Yii::$app->queue->push(new UpdateCoCreateUserJob(['user_id'=>$user_id,'type'=>"bind_user"]));
            return true;
        }

        return false;
    }

    public function Del($ids)
    {
        return $this->coCreateUserModel->Del($ids);
    }


    public function batchAdd($phone,$file,$backUserId)
    {
        //防止多重点击上传，单人限制3s
        $unique_key = CUtil::getAllParams(__FUNCTION__, $backUserId);
        list($anti) = $this->coCreateUserModel->ReqAntiConcurrency($backUserId, $unique_key, 3, 'EX');
        if (!$anti) {
            return [false, '3s内请勿重复点击！'];
        }
        //参数校验
        if (empty($phone) && empty($file)) {
            return [false, '用户手机号或上传文件至少填写一条！'];
        }
        //获取填入的电话号码
        $userData = array_unique(array_filter(explode(',', $phone)));
        if (count($userData) > 20) {
            return [false, '用户数据大于20条建议采用批量上传方式'];
        }
        //用户批量上传数据处理
        $data = [];
        if ($file) {
            $filename = $file['tmp_name'] ?? '';
            if (!$filename) {
                return [false, '请选择文件'];
            }

            $name = explode('.', $file['name']);
            if (array_pop($name) != 'csv') {
                return [false, '请上传csv类型文件'];
            }

            $handle = fopen($filename, 'r');
            list($s, $data) = $this->__anCsv($handle);
            if (!$s) {
                return [false, $data];
            }

            if (!count($data)) {
                return [false, '没有任何数据'];
            }

            //明显重复项校验
            $memberNos = array_column($data, 'memberNo');
            $realMemberNos = array_filter($memberNos);
            if (count($realMemberNos) !== count(array_unique($realMemberNos))) {
                return [false, '表格存在明显相同数据行，请校验后重新上传！'];
            }
            if (count($realMemberNos) > 2000) {
                return [false, '文件数据超过2000行，为防止操作时间过长，请分批上传！'];
            }
        }
        $data = empty($data) ? [] : array_column($data, 'memberNo');
        $data = array_unique(array_merge($data,$userData));
        //找出已存在的用户
        $hasIds = $this->coCreateUserModel->GetListByInput(['phones'=>$data],1,2000);
        if(count($hasIds)>500) return [false,'该表格数据存在过量已存在的数据'];
        $filterUser =[];
        if($hasIds){
            foreach ($hasIds as $id){
                $info = $this->coCreateUserModel->GetOneById($id);
                $filterUser[]=$info['phone'];
            }
        }
        //插入用户
        $insertUser = array_diff($data,$filterUser);
        //拼接插入数据
        $iData = [];
        $time = time();
        if($insertUser){
            $setPhones = [];
            foreach ($insertUser as $phone){
                $userInfo = by::users()->getWxUserByPhone($phone);
                if($userInfo){
                    $user_id = $userInfo['user_id']??0;
                    $uid = by::Phone()->getUidByUserId($user_id);
                    $info = by::users()->getOneByUid($user_id);
                    $iData[] = [
                        'user_id' => $user_id,
                        'phone'   => $phone,
                        'uid'     => $uid,
                        'nick'    => $info['nick']??'',
                        'ctime'   => $time,
                        'utime'   => $time
                    ];
                    $setPhones[] = $phone;
                }
            }


            $noUserPhone = array_diff($insertUser, $setPhones);
            if($noUserPhone){
                foreach ($noUserPhone as $phone){
                    $iData[] = [
                        'user_id' => 0,
                        'phone'   => $phone,
                        'uid'     => 0,
                        'nick'    => '',
                        'ctime'   => $time,
                        'utime'   => $time
                    ];
                }
            }

            if($iData){
               $s = $this->coCreateUserModel->batchInsert($iData);
               if(!$s){
                   return [false,'用户导入失败！'];
               }
            }
        }
        return [true,'ok'];
    }



    private function __anCsv($handle): array
    {
        $out = [];
        $n = 1;

        while ($data = fgetcsv($handle, 50)) {
            if ($n>1 && !empty($data[0])){
                $out[]=[
                    'memberNo'        => trim($data[0]),
                ];
            }
            $n++;
        }

        return [true, array_values($out)];
    }




}
