<?php

namespace app\modules\wares\services\goods;


use app\models\by;


class GoodsStockService
{
    private static $_instance = NULL;

    protected $goodsAtkModel;
    protected $goodsAvModel;
    protected $goodsSpecsModel;
    protected $goodsStockModel;


    public function __construct()
    {
        $this->goodsAtkModel = by::GoodsAtkModel();
        $this->goodsAvModel  = by::GoodsAvModel();
        $this->goodsSpecsModel = by::GoodsSpecsModel();
        $this->goodsStockModel = by::GoodsStockModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $gid
     * @param string $av_ids
     * @return array
     * @throws \yii\db\Exception
     * 多规格判断库存（小程序置灰）
     */
    public function InStock($gid, $av_ids = '', $source = '')
    {
        if(empty($source)){
            $source = $this->goodsStockModel::SOURCE['WARES'];
        }
        if ($source == $this->goodsStockModel::SOURCE['WARES']) {
            return $this->processSource($gid, $av_ids, $source, $this->goodsAtkModel, $this->goodsAvModel, $this->goodsSpecsModel);
        } elseif ($source == $this->goodsStockModel::SOURCE['MAIN']) {
            return $this->processSource($gid, $av_ids, $source, by::Gak(), by::Gav(), by::Gspecs());
        } else {
            return [];
        }
    }


    /**
     * @throws \yii\db\Exception
     */
    private function processSource($gid, $av_ids, $source, $akModel, $avModel, $specsModel) {
        //所有属性名
        $ak = $akModel->GetListByGid($gid);
        //规格列表
        if($source == $this->goodsStockModel::SOURCE['WARES']){
            $specs = $specsModel->GetSpecsListByGid($gid);
        }else{
            $specs = $specsModel->GetListByGid($gid);
        }

        $ak = array_column($ak, null, 'id');
        $av_ids = empty($av_ids) ? [] : json_decode($av_ids, true);

        //没选属性值，判断第一级
        if (empty($av_ids)) {
            $fist_ak = reset($ak);
        } else {
            foreach ($av_ids as $av_id) {
                $aLog = $avModel->GetOneById($av_id);
                unset($ak[$aLog['ak_id']]);
            }
            $fist_ak = empty($ak) ? [] : reset($ak);
        }

        $ak_id = $fist_ak['id'] ?? 0;

        if (empty($ak_id)) {
            return [];
        }
        $av = $avModel->GetListByAkId($ak_id);

        foreach ($av as $k => $v) {
            $av[$k]['in_stock'] = 0;
            foreach ($specs as $v1) {
                $av1_ids = json_decode($v1['av_ids'], true);
                if (!array_diff([$v['id']], $av1_ids) && !array_diff($av_ids,$av1_ids)) {
                    //判断库存
                    $sku = $this->goodsStockModel->GetSkuByGidAndSid($gid, $v1['id'], $source);
                    $stock = $this->goodsStockModel->OptStock($sku);
                    if ($stock > 0) {
                        $av[$k]['in_stock'] = 1;
                        break;
                    }
                }
            }
        }

        $fist_ak['val'] = $av;
        return $fist_ak;
    }

    /**
     * 获取库存告警商品
     * @param int $safety_stock 安全库存
     * @return array
     */
    public function getStockOutGoodsList($safety_stock = 5)
    {
        // 查询库存小于10的sku
        $skuList = by::GoodsStockModel()->getSkuByStock($safety_stock);

        $goodsList = [];
        if (!empty($skuList)) {
            $skuArr = array_column($skuList, "sku");
            $goodsSingleList = by::Gmain()->getGoodsBySku($skuArr); // 单规格
            $goodsSpecList = by::Gmain()->getGoodsBySku($skuArr, 1); // 多规格
            $goodsList = array_merge($goodsSingleList, $goodsSpecList);

            $skuMap = array_column($skuList, null, "sku");
            array_walk($goodsList, function (&$item) use ($skuMap) {
                $item["stock"] = $skuMap[$item["sku"]]["stock"];
            });
        }

        return $goodsList;
    }
}
