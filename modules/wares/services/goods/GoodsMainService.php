<?php

namespace app\modules\wares\services\goods;

use app\components\ErpNew;
use app\jobs\WaresPointsJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\back\services\system\SystemDictDataService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\wares\forms\goods\GoodsMainForm;
use app\modules\wares\forms\goods\GoodsPointsForm;
use app\modules\wares\forms\goods\GoodsTryBeforeBuyForm;
use RedisException;
use yii\db\Exception;

class GoodsMainService
{
    private static $_instance = NULL;

    protected $goodsMainModel;
    protected $goodsStockModel;
    protected $goodsSpecsModel;
    protected $goodsPointsModel;
    protected $goodsPointsPriceModel;
    protected $goodsAtkModel;
    protected $goodsAvModel;
    protected $goodsTryBuyingModel;
    protected $goodsTryBuyingPriceModel;

    public function __construct()
    {
        $this->goodsMainModel           = by::GoodsMainModel();
        $this->goodsStockModel          = by::GoodsStockModel();
        $this->goodsSpecsModel          = by::GoodsSpecsModel();
        $this->goodsPointsModel         = by::GoodsPointsModel();
        $this->goodsPointsPriceModel    = by::GoodsPointsPriceModel();
        $this->goodsAtkModel            = by::GoodsAtkModel();
        $this->goodsAvModel             = by::GoodsAvModel();
        $this->goodsTryBuyingModel      = byNew::GoodsTryBuyingModel();
        $this->goodsTryBuyingPriceModel = byNew::GoodsTryBuyingPriceModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * 后台获取列表
     * @throws Exception
     * @throws RedisException
     */
    public function GetList($input, $page, $page_size): array
    {
        $return = ['list' => [], 'pages' => 1];
        $count  = $this->goodsMainModel->GetListCount($input, $page, $page_size);
        if (empty($count)) {
            return $return;
        }
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        //获取图片ID
        $gids = $this->goodsMainModel->GetList($input, $page, $page_size);
        foreach ($gids as $gid) {
            $return['list'][] = $this->GetMainInfoByGid($gid);
        }
        return $return;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 基础数据
     */
    public function GetMainInfoByGid($gid, $is_stock = true, $is_price = true)
    {
        $aGoods = $this->goodsMainModel->GetOneByGid($gid);
        if (empty($aGoods)) return [];

        //获取积分商品信息
        $source = $aGoods['source'] ?? 0;
        switch ($source) {
            case $this->goodsMainModel::SOURCE['POINTS']:
                $points = $this->goodsPointsModel->GetOneById($gid);
                $aGoods = array_merge($points, $aGoods);
                break;
            case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                $tryGoods = $this->goodsTryBuyingModel->GetOneById($gid);
                $aGoods   = array_merge($tryGoods, $aGoods);
        }

        //获取库存信息
        if ($is_stock) {
            list($stock, $sales) = $this->GetSumByGid($gid, $this->goodsStockModel::SOURCE['WARES'], false);
            $aGoods['stock'] = $stock;
            $aGoods['sales'] = $sales;
        }

        if ($is_price) {
            switch ($source) {
                case $this->goodsMainModel::SOURCE['POINTS']:
                    $priceGood = $this->goodsPointsPriceModel->GetOneById($gid, 0);
                    $aGoods    = array_merge($priceGood, $aGoods);
                    break;
                case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                    $tryGoodsPrice = $this->goodsTryBuyingPriceModel->GetOneById($gid);
                    $aGoods        = array_merge($tryGoodsPrice, $aGoods);
            }
        }

        $atype = $aGoods['atype'] ?? -1;
        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            //规格列表
            $specs = $this->goodsSpecsModel->GetSpecsListByGid($gid);
            if ($is_price) {
                foreach ($specs as &$spec) {
                    $specPrice = $this->goodsPointsPriceModel->GetOneById($gid, $spec['id']);
                    $spec      = array_merge($specPrice, $spec);
                }
            }

            $aGoods['specs'] = $this->goodsSpecsModel->AttrToName($gid, $specs);
        }

        return $aGoods;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 后台详情数据
     */
    public function GetOneByGid($gid, $is_stock = true, $is_price = true)
    {
        $aData = $this->GetMainInfoByGid($gid, $is_stock, $is_price);
        if (empty($aData)) {
            return [];
        }

        $aData['attr_cnf'] = [];
        switch ($aData['atype']) {
            case $this->goodsMainModel::ATYPE['SPEC']:
                break;
            case $this->goodsMainModel::ATYPE['SPECS']:
                //商品属性
                $aData['attr_cnf'] = $this->goodsAtkModel->GetListByGid($gid);
                //商品属性值
                foreach ($aData['attr_cnf'] as $k => $v) {
                    $atVal = $this->goodsAvModel->GetListByAkId($v['id']);
                    foreach ($atVal as &$item) {
                        $item['val'] = $item['at_val'] ?? '';
                    }
                    $aData['attr_cnf'][$k]['at_val'] = $atVal;
                }
                break;
        }

        return $aData;
    }


    /**
     * @param $data
     * @return array
     * 创建/编辑新商品
     */
    public function modify($data)
    {
        //参数校验
        //a.主表校验
        $form = new GoodsMainForm();
        $form->load($data, '');
        if (!$form->validate()) {
            // 错误信息
            $errors = $form->firstErrors;
            return [false, array_shift($errors)];
        }

        //b.多规格校验
        list($s, $specsData) = $this->__validateSpecs($data);
        if (!$s) {
            return [false, $specsData];
        }

        //c.商品来源判断
        $source = $data['source'] ?? 1;

        switch ($source) {
            case $this->goodsMainModel::SOURCE['POINTS']:
                list($status, $pointsData) = $this->__validatePointsGoods($data);
                if (!$status) {
                    return [false, $pointsData];
                }
                break;
            case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                list($status, $pointsData) = $this->__validateTryBeforeBuyGoods($data);
                if (!$status) {
                    return [false, $pointsData];
                }
                break;
            default:
                return [false, '目前不支持其他类型商品！'];
        }

        //事务处理(保存/更新)数据
        list($s, $msg) = $this->_saveData($data);
        if (!$s) {
            return [false, $msg];
        }
        return [true, $msg];
    }


    /**
     * @param $aData
     * @return array
     * 事务保存新商品数据
     */
    private function _saveData($aData): array
    {
        $attrData = $specsData = [];

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $id     = CUtil::uint($aData['id'] ?? 0);
            $atype  = CUtil::uint($aData['atype'] ?? 0);
            $source = CUtil::uint($aData['source'] ?? 0);

            //存改主表
            list($s, $gid) = $this->goodsMainModel->SaveLog($aData);
            if (!$s) {
                throw new MyExceptionModel($gid);
            }


            switch ($source) {
                //存改积分表
                case $source == $this->goodsMainModel::SOURCE['POINTS']:
                    if ($id) {//更新积分表
                        list($s, $pointsGoods) = $this->goodsPointsModel->UpdateLog($id, $aData);
                    } else {//创建积分表
                        $aData['gid'] = $gid;
                        list($s, $pointsGoods) = $this->goodsPointsModel->SaveLog($aData);
                    }
                    if (!$s) {
                        throw new MyExceptionModel($pointsGoods);
                    }
                    break;
                case $source == $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                    $aData['mprice'] = $aData['price'];
                    if ($id) {//更新先试后买表
                        list($s, $tryBuyingGoods) = $this->goodsTryBuyingModel->UpdateLog($id, $aData);
                    } else {//创建先试后买表
                        $aData['gid'] = $gid;
                        list($s, $tryBuyingGoods) = $this->goodsTryBuyingModel->SaveLog($aData);
                    }
                    if (!$s) {
                        throw new MyExceptionModel($tryBuyingGoods);
                    }
                    break;
            }

            //多规格存储
            if ($id) {//编辑
                if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
                    //编辑属性
                    $attr_cnf = $aData['attr_cnf'] ?? ""; //属性配置
                    $attrData = json_decode($attr_cnf, true);

                    // 编辑属性
                    list($status, $msg) = $this->__updateAttr($gid, $attrData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }

                    $specs     = $aData['specs'] ?? ""; //规格列表
                    $specsData = json_decode($specs, true);

                    // 编辑属性sku
                    list($status, $msg) = $this->__updateSpecs($gid, $specsData, $source);
                } else {
                    //根据规则更新
                    list($status, $msg) = $this->OptGoodsPrice($source, $gid, 0, $aData, 'UPDATE');
                }
                if (!$status) {
                    throw new MyExceptionModel($msg);
                }
                $this->goodsMainModel->__delCache($gid); //todo 清空商品详情缓存

            } else {//新增
                if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
                    // 新增属性
                    $attr_cnf = $aData['attr_cnf'] ?? ""; //属性配置
                    $attrData = json_decode($attr_cnf, true);

                    // 新增属性
                    list($status, $msg) = $this->__insertAttr($gid, $attrData);
                    if (!$status) {
                        throw new MyExceptionModel($msg);
                    }

                    $specs     = $aData['specs'] ?? ""; //规格列表
                    $specsData = json_decode($specs, true);

                    // 新增属性sku
                    list($status, $msg) = $this->__insertSpecs($gid, $specsData, $source);
                } else {
                    //根据规则创建
                    list($status, $msg) = $this->OptGoodsPrice($source, $gid, 0, $aData, 'SAVE');
                }
                if (!$status) {
                    throw new MyExceptionModel($msg);
                }
            }

            //TODO 商品库存
            list($s, $m) = $this->goodsStockModel->SaveLog($gid, $aData, $specsData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }


            $trans->commit();

            //判断是否发货
            $isSend = CUtil::uint($aData['is_send'] ?? 0);
            if ($isSend == 1) {
                //同步通知E3+
                ErpNew::factory()->pushWaresGoods($gid);
            }

            //TODO 异步同步积分商城数据
            \Yii::$app->queue->push(new WaresPointsJob(['source' => $this->goodsMainModel::SOURCE['POINTS']]));

            return [true, 'OK'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.goods-main');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.goods-main');

            return [false, '操作失败'];
        }


    }


    /**
     * @param $data
     * @return array
     * 多规格基础校验
     */
    private function __validateSpecs($data): array
    {
        //商品多规格校验
        $attr_cnf = $data['attr_cnf'] ?? ""; //属性配置
        $specs    = $data['specs'] ?? "";    //规格列表
        $atype    = CUtil::uint($data['atype'] ?? 0);

        if (!in_array($atype, $this->goodsMainModel::ATYPE)) {
            return [false, "非法商品规格类型"];
        }

        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            //规格名-规格值 校验 [{"at_name":"颜色","at_val":["红色","白色"]}]
            list($status, $attrData) = by::GoodsAtkModel()->checkAttr($attr_cnf);
            if (!$status) {
                return [false, $attrData];
            }

            //[{"at_val":"颜色:红色,尺码:s","sku":"12211","price":"0.01","points":"500","exchange":"1","image":"http://abc.jpg"}]
            list($status, $specsData) = $this->goodsSpecsModel->checkSpecs($specs);
            if (!$status) {
                return [false, $specsData];
            }

            // 确保 $attr_cnf 中的 at_name 值与 $specs 中的 at_val 值的键相匹配
            list($status, $res) = by::GoodsAtkModel()->checkAttrSpecs($attrData, $specsData);
            if (!$status) {
                return [false, $res];
            }
        }

        return [true, 'OK'];
    }


    /**
     * @param $data
     * @return array
     * 校验积分商城商品数据
     */
    private function __validatePointsGoods($data): array
    {
        //1.积分商品校验
        $pointsForm = new GoodsPointsForm();
        $pointsForm->load($data, '');
        if (!$pointsForm->validate()) {
            // 错误信息
            $errors = $pointsForm->firstErrors;
            return [false, array_shift($errors)];
        }

        list($status, $pointsData) = $this->goodsPointsModel->checkPoints($data);
        if (!$status) {
            return [false, $pointsData];
        }

        //2.单规格校验
        $atype = CUtil::uint($data['atype'] ?? 0);
        $model = $this->goodsPointsPriceModel;
        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            $specs = $data['specs'] ?? ""; //规格列表
            $arr   = json_decode($specs, true);
            if (!is_array($arr)) {
                return [false, "多规格sku参数有误"];
            }

            foreach ($arr as $k => $v) {
                list($status, $priceData) = $model->checkExchange($v);
                if (!$status) {
                    return [false, $priceData];
                }
            }
        } else {
            list($status, $priceData) = $model->checkExchange($data);
            if (!$status) {
                return [false, $priceData];
            }
        }




        return [true, 'OK'];
    }


    /**
     * @param $gid
     * @param $attr_cnf
     * @return array
     * @throws Exception
     * 新增商品属性、属性值
     */
    private function __insertAttr($gid, $attr_cnf): array
    {
        if (empty($attr_cnf) && !is_array($attr_cnf)) {
            return [false, "属性参数异常"];
        }

        $goodsAtkModel = by::GoodsAtkModel();
        $goodsAvModel  = by::GoodsAvModel();


//  attr_cnf: [{"at_name":"口味","at_val":[{"val":"甜","image":"http:\/\/abc1.jpg"},{"val":"酸","image":"http:\/\/abc2.jpg"}]},
//  {"at_name":"颜色","at_val":[{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]}]


        //规格处理
        foreach ($attr_cnf as $val) {
            $at_name   = $val['at_name']; //规格名 - 颜色
            $at_val    = $val['at_val'];  //规格值列表 - [{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]
            $at_params = [
                    'gid'     => $gid,
                    'at_name' => $at_name,
            ];
            list($status, $ak_id) = $goodsAtkModel->SaveLog($at_params);
            if (!$status) {
                return [false, "新增Attr失败!"];
            }
            $ak_id = intval($ak_id); //新增成功的规格id

            //规格值
            foreach ($at_val as $v) {
                $atv_params = [
                        'ak_id'  => $ak_id,
                        'at_val' => $v['val'],
                        'image'  => $v['image'] ?? '',
                ];
                list($status, $m) = $goodsAvModel->SaveLog($atv_params);
                if (!$status) {
                    return [false, "新增Attrvalue失败!"];
                }
            }
        }

        return [true, 'ok'];
    }


    /**
     * @param $gid
     * @param $attr_cnf
     * @return array
     * @throws Exception
     * 更新商品属性、属性值
     */
    private function __updateAttr($gid, $attr_cnf): array
    {
        if (empty($attr_cnf) || !is_array($attr_cnf)) {
            return [false, "属性参数异常"];
        }
        $goodsAtkModel = by::GoodsAtkModel();
        $goodsAvModel  = by::GoodsAvModel();

        //编辑 - 规格
        //查修改之前的规格 和 新的规格
        $o_attr = $goodsAtkModel->GetListByGid($gid);
        $o_attr = array_column($o_attr, 'at_name');
        $n_attr = array_column($attr_cnf, 'at_name');

        //新增的规格
        $a_attr = array_diff($n_attr, $o_attr);
        //删除的规格
        $d_attr = array_diff($o_attr, $n_attr);
        //修改的规格
        $u_attr = array_intersect($n_attr, $o_attr);

        //新增 规格
        foreach ($attr_cnf as $val) {
            $at_name   = $val['at_name']; //规格名 - 颜色
            $at_val    = $val['at_val'];  //规格值列表 - [{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]
            $at_params = [
                    'gid'     => $gid,
                    'at_name' => $at_name,
            ];

            if (in_array($at_name, $a_attr)) {
                //新增
                list($status, $ak_id) = $goodsAtkModel->SaveLog($at_params);
                if (!$status) {
                    return [false, "新增Attr失败!"];
                }
                $ak_id = intval($ak_id); //新增成功的规格id

                //规格值
                foreach ($at_val as $v) {
                    $atv_params = [
                            'ak_id'  => $ak_id,
                            'at_val' => $v['val'],
                            'image'  => $v['image'] ?? '',
                    ];
                    list($status) = $goodsAvModel->SaveLog($atv_params);
                    if (!$status) {
                        return [false, "新增Attrvalue失败(1)"];
                    }
                }
            } else if (in_array($at_name, $u_attr)) {
                //修改
                $at_name = $val['at_name']; //规格名 - 颜色
                $aLog    = $goodsAtkModel->GetOneByName($gid, $at_name);
                $ak_id   = $aLog['id'] ?? 0;
                $avList  = $goodsAvModel->GetListByAkId($ak_id) ?? [];

                //$at_val 规格值列表 - [{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]

                $o_av = array_column($avList, 'at_val');
                //新增的规格值
                $a_av = array_diff(array_column($at_val, 'val'), $o_av);
                //删除的规格值
                $d_av = array_diff($o_av, array_column($at_val, 'val'));

                $atValImage = array_column($at_val, 'image', 'val');

                //添加规格值
                foreach ($a_av as $v) {
                    $atv_params = [
                            'ak_id'  => $ak_id,
                            'at_val' => $v,
                            'image'  => $atValImage[$v] ?? '',
                    ];
                    list($status, $ret) = $goodsAvModel->SaveLog($atv_params);
                    !YII_ENV_PROD && CUtil::debug(json_encode($atv_params) . '|' . $status . '|' . json_encode($ret, 320), 'goods-av');
                    if (!$status) {
                        return [false, "新增Attrvalue失败(2)"];
                    }
                }
                //删除规格值
                foreach ($d_av as $k => $v) {
                    list($status) = $goodsAvModel->UpdateLog($ak_id, $v);
                    if (!$status) {
                        return [false, '删除attrValue失败!'];
                    }
                }
            }
        }

        //删除
        foreach ($d_attr as $at_name) {
            list($status, $ak_id) = $goodsAtkModel->UpdateLog($gid, $at_name, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
            if (!$status) {
                return [false, '删除attr失败!'];
            }
            list($status) = $goodsAvModel->UpdateLog($ak_id);
            if (!$status) {
                return [false, '删除attrvalue失败!'];
            }
        }
        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param $specsData
     * @param $source
     * @return array
     * @throws Exception
     * 新增属性sku
     */
    private function __insertSpecs($gid, $specsData, $source): array
    {
        if (empty($specsData) && !is_array($specsData)) {
            return [false, "属性sku参数异常"];
        }
        $goodsAtkModel   = by::GoodsAtkModel();
        $goodsAvModel    = by::GoodsAvModel();
        $goodsSpecsModel = $this->goodsSpecsModel;

        $attrList = $goodsAtkModel->GetListByGid($gid, false);
        foreach ($attrList as $k => $v) {
            $avList              = $goodsAvModel->GetListByAkId($v['id'], false);
            $attrList[$k]['val'] = array_column($avList, null, 'at_val');
        }
        $attrList = array_column($attrList, null, 'at_name');

        //[{"at_val":["颜色:红色","尺码:s"],"sku":"12211","price":"0.01","image":"http://abc.jpg"}]
        foreach ($specsData as $sval) {
            foreach ($sval['at_val'] as $k1 => $v1) {
                $specs_n             = explode(':', $v1);
                $sval['at_val'][$k1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'];
            }

            $save = [
                    'gid'    => $gid,
                    'av_ids' => json_encode($sval['at_val']),
                    'sku'    => $sval['sku'],
            ];

            list($status, $sid) = $goodsSpecsModel->SaveLog($save);
            if (!$status) {
                return [false, $sid];
            }

            //根据规则创建
            list($status, $msg) = $this->OptGoodsPrice($source, $gid, $sid, $sval, 'SAVE');
            if (!$status) {
                return [false, $msg];
            }

        }

        return [true, 'ok'];
    }


    /**
     * @param $gid
     * @param $specsData
     * @param $source
     * @return array
     * @throws Exception
     * 编辑商品属性sku
     *
     */
    private function __updateSpecs($gid, $specsData, $source): array
    {
        if (empty($specsData) && !is_array($specsData)) {
            return [false, "参数异常"];
        }

        $goodsAtkModel   = by::GoodsAtkModel();
        $goodsAvModel    = by::GoodsAvModel();
        $goodsSpecsModel = $this->goodsSpecsModel;

        $o_specs = $goodsSpecsModel->GetSpecsListByGid($gid);

        $o_specs = array_column($o_specs, 'sku');
        $n_specs = array_column($specsData, 'sku');

        //新增的sku
        $a_specs = array_diff($n_specs, $o_specs);
        //删除的sku
        $d_specs = array_diff($o_specs, $n_specs);
        //修改的sku
        $u_specs = array_intersect($n_specs, $o_specs);

        //所有商品组合
        $attrList = $goodsAtkModel->GetListByGid($gid);
        foreach ($attrList as $k => $v) {
            $avList              = $goodsAvModel->GetListByAkId($v['id']);
            $attrList[$k]['val'] = array_column($avList, null, 'at_val');
        }
        $attrList = array_column($attrList, null, 'at_name');

        foreach ($specsData as $sval) {
            foreach ($sval['at_val'] as $k1 => $v1) {
                $specs_n             = explode(':', $v1);
                $sval['at_val'][$k1] = $attrList[$specs_n[0]]['val'][$specs_n[1]]['id'];
            }

            if (in_array($sval['sku'], $a_specs)) {
                $save = [
                        'gid'    => $gid,
                        'av_ids' => json_encode($sval['at_val']),
                        'sku'    => $sval['sku'],
                ];
                list($status, $sid) = $goodsSpecsModel->SaveLog($save);
                if (!$status) {
                    return [false, $sid];
                }
                //根据规则创建
                list($status, $msg) = $this->OptGoodsPrice($source, $gid, $sid, $sval, 'SAVE');
                if (!$status) {
                    return [false, $msg];
                }

            } else if (in_array($sval['sku'], $u_specs)) {
                $save = [
                        'av_ids' => json_encode($sval['at_val']),
                        'utime'  => intval(START_TIME),
                ];
                list($status, $sid) = $goodsSpecsModel->UpdateLog($gid, $sval['sku'], $save);
                if (!$status) {
                    return [false, "修改sku失败!"];
                }
                //根据规则更新
                list($status, $msg) = $this->OptGoodsPrice($source, $gid, $sid, $sval, 'UPDATE');
                if (!$status) {
                    return [false, $msg];
                }

            }
        }

        //删除sku
        foreach ($d_specs as $sku) {
            list($status, $sid) = $goodsSpecsModel->UpdateLog($gid, $sku, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
            if (!$status) {
                return [false, "删除sku失败!"];
            }
            //根据规则删除
            list($status, $msg) = $this->OptGoodsPrice($source, $gid, $sid, [], 'DEL');
            if (!$status) {
                return [false, $msg];
            }
        }

        return [true, "ok"];
    }


    /**
     * @param $source
     * @param $gid
     * @param $sid
     * @param $aData
     * @param $opt
     * @return array
     * @throws Exception
     * 处理商品价格
     */
    private function OptGoodsPrice($source, $gid, $sid, $aData, $opt = 'SAVE'): array
    {
        if (empty($source) || empty($gid)) {
            !YII_ENV_PROD && CUtil::debug(json_encode($source) . $gid . json_encode($aData) . $opt, 'oooolll.ppp');
            return [false, '主商品价格数据有误，无法创建相关数据！'];
        }

        switch ($source) {
            case $this->goodsMainModel::SOURCE['POINTS']:
                $model = $this->goodsPointsPriceModel;
                break;
            case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                $model = $this->goodsTryBuyingPriceModel;
                break;
            default:
                return [false, '暂不支持其他类型的商品'];
        }

        switch ($opt) {
            case 'SAVE':
                $aData['ctime'] = intval(START_TIME);
                $aData['gid']   = $gid;
                $aData['sid']   = $sid;
                list($s, $msg) = $model->SaveLog($aData);
                break;
            case 'UPDATE':
                $aData['utime'] = intval(START_TIME);
                list($s, $msg) = $model->UpdateLog($gid, $sid, $aData);
                break;
            case 'DEL':
                $aData['dtime']  = intval(START_TIME);
                $aData['is_del'] = 1;
                list($s, $msg) = $model->UpdateLog($gid, $sid, $aData);
                break;
            default:
                list($s, $msg) = [false, '商品价格-没有相关的操作方式！'];
        }

        return [$s, $msg];
    }


    /**
     * @param $post
     * @return array
     * 获取积分商品枚举列表
     */
    public function getPointsEnum($post): array
    {
        $keyword = trim($post['keyword'] ?? '');
        if (empty($keyword)) {
            return [false, '枚举标签必传'];
        }

        $arr = $this->getArrayByField($keyword);

        if (empty($arr)) {
            return [false, '找不到对应的枚举列表'];
        }

        if ($keyword == 'tab') {
            $prefix = 'point_category';
            $return_data = [];
            // $user_id = $post['user_id'] ?? 0;
            // $platformIds = $post['platformIds'] ?? [];
            // 由于数据字典不适合层级类配置，所以积分类目只支持三级分类
            foreach ($arr as $kid => $v) {
                $tmp = [
                    'name' => $v,
                    'value' => $kid,
                    'children' => [],
                ];
                
                // $input = [
                //     'user_id'     => $user_id,
                //     'source'      => 2,
                //     'tab'         => intval($kid ?? 0),
                //     'position'    => intval($post['position'] ?? 0),
                //     'sort_rule'   => trim($post['sort_rule'] ?? ''),
                //     'is_point'    => CUtil::uint($post['is_point'] ?? 0),
                //     'is_my'       => CUtil::uint($post['is_my'] ?? 0),
                //     'platformIds'    => $platformIds
                // ];
                //
                // $indexGoodsMainService = new IndexGoodsMainService();
                // list($status,$ret) = $indexGoodsMainService->GoodsList($input,1,1);
                // $count = 0;
                // if ($status) {
                //     $count = $ret['count'] ?? 0;
                // }
                //
                // if (empty($count)) {
                //     continue;
                // }

                $subprefix = $prefix . '_' . $kid;
                $subdata = CUtil::dictData($subprefix);
                $subdata = array_column($subdata, 'label', 'value');
                foreach ($subdata as $skid => $vv) {
                    $subtmp = [
                        'name' => $vv,
                        'value' => $skid,
                        'children' => [],
                    ];
                    $ssubprefix = $prefix . '_' . $kid . '_' . $skid;
                    $ssubdata = CUtil::dictData($ssubprefix);
                    $ssubdata = array_column($ssubdata, 'label', 'value');
                    foreach ($ssubdata as $sskid => $vvv) {
                        $subtmp['children'][] = [
                            'name' => $vvv,
                            'value' => $sskid,
                        ];
                    }
                    $tmp['children'][] = $subtmp;
                }
                $return_data[] = $tmp;
            }
            return [true, $return_data];
        }
        
        $res = [];
        foreach ($arr as $key => $item) {
            $res[] = [
                    'value' => $key,
                    'name'  => $item
            ];
        }
        return [true, $res];
    }


    public function getArrayByField($field)
    {
        switch ($field) {
            case 'type':
                return $this->goodsPointsModel::TYPE;
            case 'label':
                return $this->goodsPointsModel::LABEL;
            case 'tab':
                try {
                    $prefix = 'point_category';
                    $data = CUtil::dictData($prefix);
                    return array_column($data, 'label', 'value');
                } catch (\Throwable $e) {
                    return $this->goodsPointsModel::TAB;
                }
            case 'position':
                return $this->goodsPointsModel::POSITION;
            case 'levels':
                return by::users()::USER_LEVEL;
            case 'online_time':
                return $this->goodsPointsModel::WEEK;
            case 'activity':
                return ActivityConfigEnum::POINTS_ACTIVITY_NAME;
            default:
                return null;
        }
    }

    /**
     * @param $gid
     * @return array
     * @throws Exception
     * 根据商品ID获取SKUS
     */
    public function GetSkusByGid($gid): array
    {
        $aMain = $this->goodsMainModel->GetOneByGid($gid);
        if (empty($aMain)) return [];
        $atype = $aMain['atype'] ?? 0;
        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            $aData = $this->goodsSpecsModel->GetSpecsListByGid($gid);
            $skus  = array_column($aData, 'sku');
        } else {
            $skus = empty($aMain['sku'] ?? '') ? [] : [$aMain['sku']];
        }
        return $skus;
    }


    /**
     * @param $id
     * @param array $update
     * @return array
     * @throws Exception
     * 强制更新数据
     */
    public function UpdateData($id, array $update)
    {
        $s = $this->goodsMainModel->UpdateData($id, $update);
        \Yii::$app->queue->push(new WaresPointsJob(['source' => $this->goodsMainModel::SOURCE['POINTS']]));
        return $s;
    }


    /**
     * @throws Exception
     * 删除商品
     */
    public function Del($id)
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [false, '参数错误'];
        }

        $aLog = $this->goodsMainModel->GetOneByGid($id);;
        if (empty($aLog)) {
            return [false, '参数错误(1)'];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();

        try {
            //主表删除
            list($s, $msg) = $this->goodsMainModel->UpdateData($id, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
            if (!$s) {
                throw new MyExceptionModel($msg);
            }


            switch ($aLog['source']) {
                //积分商品表删除
                case $this->goodsMainModel::SOURCE['POINTS']:
                    list($s, $msg) = $this->goodsPointsModel->UpdateLog($id, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
                    if (!$s) {
                        throw new MyExceptionModel($msg);
                    }
                    break;
                //先试后买商品表删除
                case $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']:
                    list($s, $msg) = $this->goodsTryBuyingModel->UpdateLog($id, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
                    if (!$s) {
                        throw new MyExceptionModel($msg);
                    }
                    break;
                default:
                    throw new MyExceptionModel('暂不支持其他类型的商品');
            }

            //积分商品价格删除
            //1.查找是否存在
            $pointPrice = $this->goodsPointsPriceModel->GetOneById($id, 0);
            if ($pointPrice) {
                $this->goodsPointsPriceModel->UpdateLog($id, 0, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
            }
            //先试后买价格删除
            $tryPrice = $this->goodsTryBuyingPriceModel->GetOneById($id, 0);
            if ($tryPrice) {
                $this->goodsTryBuyingPriceModel->UpdateLog($id, 0, ['is_del' => 1, 'dtime' => intval(START_TIME)]);
            }

            //多规格删除
            if ($aLog['atype'] != $this->goodsMainModel::ATYPE['SPEC']) {
                $specs = $this->goodsSpecsModel->GetSpecsListByGid($id);
                if ($specs) {
                    foreach ($specs as $spec) {
                        $this->goodsSpecsModel->UpdateLog($spec['gid'], $spec['sku'], ['is_del' => 1, 'dtime' => intval(START_TIME)]);
                        $pointPrice = $this->goodsPointsPriceModel->GetOneById($id, $spec['id']);
                        $pointPrice && $this->goodsPointsPriceModel->UpdateLog($id, $spec['id'], ['is_del' => 1, 'dtime' => intval(START_TIME)]);
                        $tryPrice = $this->goodsTryBuyingPriceModel->GetOneById($id, $spec['id']);
                        $tryPrice && $this->goodsTryBuyingPriceModel->UpdateLog($id, $spec['id'], ['is_del' => 1, 'dtime' => intval(START_TIME)]);
                    }
                }
            }

            $trans->commit();

            \Yii::$app->queue->push(new WaresPointsJob(['source' => $this->goodsMainModel::SOURCE['POINTS']]));

            return [true, 'ok'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.goods-del');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.goods-del');
            return [false, '删除失败'];
        }

    }


    /**
     * @param $gid
     * @return array
     * @throws Exception
     * 同步库存
     */
    public function SyncWaresStock($gid)
    {
        $gid = CUtil::uint($gid);
        if (empty($gid)) {
            return [false, '参数错误'];
        }
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $gid);
        list($anti) = $this->goodsMainModel->ReqAntiConcurrency(0, $unique_key, 10);
        if (!$anti) {
            return [false, '10s内只能同步一次'];
        }

        return ErpNew::factory()->pushWaresGoods($gid);
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function GetSumByGid($gid, $source = '', $cache = true): array
    {
        if ($source == $this->goodsStockModel::SOURCE['MAIN']) {
            $skus = by::Gmain()->GetSkusByGid($gid);
        } elseif ($source == $this->goodsStockModel::SOURCE['WARES']) {
            $skus = $this->GetSkusByGid($gid);
        } else {
            return [0, 0];
        }
        $stock = 0;
        $sales = 0;
        foreach ($skus as $sku) {
            $stock += $this->goodsStockModel->OptStock($sku);
            $sales += $this->goodsStockModel->OptSales($sku);
        }

        return [$stock, $sales];
    }

    /**
     * 获取会员商品的库存信息
     */
    public function GetMainStockByGid($gid): array
    {
        $skus = by::Gmain()->GetSkusByGid($gid);

        return array_map(function ($sku) {
            return [
                    'stock' => $this->goodsStockModel->OptStock($sku),
                    'sku'   => $sku
            ];
        }, $skus);
    }


    private function __validateTryBeforeBuyGoods($data): array
    {
        //1.先试后买商品校验
        $pointsForm = new GoodsTryBeforeBuyForm();
        $pointsForm->load($data, '');
        if (!$pointsForm->validate()) {
            // 错误信息
            $errors = $pointsForm->firstErrors;
            return [false, array_shift($errors)];
        }

        $price = $data['price'] ?? 0;
        if (empty($price) && $price <= 0) {
            return [false, "价格不能为空"];
        }
        $price = sprintf("%.2f", $price);
        if (bccomp($price, by::GoodsPointsPriceModel()::DECIMAL_RANGE, 2) > 0 || bccomp($price, 0, 2) < 0) {
            return [false, "非法价格"];
        }


        return [true, 'OK'];
    }

}
