<?php

namespace app\modules\wares\services\goods;


use app\models\by;
use app\models\CUtil;
use app\modules\main\enums\activity\ActivityConfigEnum;

class IndexGoodsMainService extends GoodsMainService
{


    /**
     * @throws \yii\db\Exception
     * 获取所有商品
     *
     */
    public function GetAllMainList($source)
    {
        return $this->goodsMainModel->GetAllMainList($source);
    }


    /**
     * @param $gid
     * @param $platformIds
     * @param $is_name
     * @param $is_stock
     * @param $is_attr
     * @return array|mixed|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取首页商品详情
     */
    public function IndexMainInfoByGid($gid,$platformIds=[],$is_name = true, $is_stock = true, $is_attr=true, $user_id =0)
    {
        $aGoods = $this->GetAllOneByGid($gid, $platformIds, $is_stock,true,$is_attr);
        $atype  = $aGoods['atype'] ?? -1;
        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            $aGoods = $this->__getMinSpecsPrice($aGoods);
        }
        if (!empty($user_id)){
            // 判断是否可以兑换
            list($status, $msg) = by::activityConfigModel()->activityFreePointsRule($user_id,$gid);
            $aGoods['activity'] = $msg;
            $aGoods['activity_tag'] = ActivityConfigEnum::POINTS_ACTIVITY_RESULT[$msg] ?? '不可以兑换';
        }


        if ($is_name) {
            $arr = ['label', 'tab', 'levels', 'online_time', 'position'];
            foreach ($arr as $i) {
                $aGoods = $this->__transferName($i, $aGoods);
            }
        }

        return $aGoods;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function AlertGoodsInfo($user_id, $aGoods): array
    {
        $alertInfo = [
            'can_purchase' => 1,
            'check_tab'    => '',
            'button_msg'   => '立即兑换',
            'alert_msg'    => '',
        ];
        $arr       = ['online_time','status','stock', 'level', 'point','activity'];
        $user_id   = strlen($user_id) < 13 ? CUtil::uint($user_id) : 0;
        if (empty($user_id)) {
            $alertInfo = [
                'can_purchase' => 1,
                'check_tab'    => 'user',
                'button_msg'   => '用户未授权',
                'alert_msg'    => '请先授权！',
            ];
        } else {
            foreach ($arr as $item) {
                switch ($item) {
                    case 'status':
                        if (CUtil::uint($aGoods['status']) > 0) {
                            $alertInfo['can_purchase'] = 0;
                            $alertInfo['check_tab']    = 'status';
                            $alertInfo['button_msg']   = '商品已下架';
                        }
                        break;
                    case 'stock':
                        if ($aGoods['stock'] <= 0) {
                            $alertInfo['can_purchase'] = 0;
                            $alertInfo['check_tab']    = 'stock';
                            $alertInfo['button_msg']   = '待补货';
                        }
                        break;
                    case 'level':
                        $myLevel = by::memberCenterModel()->GetUserLevel($user_id);
                        $levels  = $aGoods['levels'] ?? '';
                        if ($levels) {
                            $levelArr = explode('|', $levels);
                            if (!in_array($myLevel, $levelArr)) {
                                $alertInfo['can_purchase'] = 0;
                                $alertInfo['check_tab']    = 'level';
                                $alertInfo['button_msg']   = '等级未达标';
                                $alertInfo['alert_msg']    = '该商品为' . ($aGoods['levels_tag'] ?? '钻石及以上用户专享') . '，去看看其他商品吧';
                            }
                        }
                        break;
                    case 'point':
                        $myPoint = by::point()->get($user_id);
                        $point   = CUtil::uint($aGoods['point'] ?? 0);
                        if ($myPoint < $point) {
                            $alertInfo['can_purchase'] = 0;
                            $alertInfo['check_tab']    = 'point';
                            $alertInfo['button_msg']   = '积分不足';
                            $alertInfo['alert_msg']    = '您现有积分' . ($myPoint) . '，暂不足以支付该商品所用积分';
                        }
                        break;
                    case 'online_time':
                        $value = trim($aGoods['online_time'] ?? 0);
                        $dataW = date('w');
                        if(empty($dataW)) $dataW = 7;
                        if(strlen($value) < 5 || strpos($value, '|') !== false){
                            $valueArr = explode('|',$value) ?? [];
                            if (!in_array($dataW,$valueArr)) {
                                $alertInfo['can_purchase'] = 0;
                                $alertInfo['check_tab']    = 'online_time';
//                                $alertInfo['alert_msg']    = '仅'.$this->convertValuesToNames($value,$arr,'|','、').'可买';
                            }
                        }else{
                            $value = CUtil::uint($value);
                            if(!empty($value) && $value > intval(START_TIME)){
                                $alertInfo['can_purchase'] = 0;
                                $alertInfo['check_tab']    = 'online_time';
//                                $alertInfo['alert_msg']    = date('m月d日 H:i',$value).'上架';
                            }
                        }
                        break;
                    case 'activity':
                        $activity = $aGoods['activity'] ?? 0;
                        if( $activity > 0 ){
                            $alertInfo['can_purchase'] = 0;
                            $alertInfo['check_tab']    = 'activity';
                            $alertInfo['alert_msg'] = $alertInfo['button_msg']  = ActivityConfigEnum::POINTS_ACTIVITY_RESULT[$aGoods['activity']] ?? '不可以兑换';
                        }
                        break;
                    default:
                        break;
                }
                if ($alertInfo['can_purchase'] !== 1) {
                    break;
                }
            }
        }

        return $alertInfo;
    }

    /**
     * @throws \yii\db\Exception
     * 根据gid ,sid 获取商品详情
     */

    public function GetOneByGidSid($gid, $sid = 0, $platformIds=[], $format_price = true, $attr_to_name = false)
    {
        $aMain = $this->goodsMainModel->GetOneByGid($gid);
        if (empty($aMain)) {
            return [];
        }
        if($aMain['is_del'] == 1){
            return [];
        }

        $source = $aMain['source'] ?? 0;
        if ($source == $this->goodsMainModel::SOURCE['POINTS']) {
            $aMain = $this->handleGoods($gid, $sid, $platformIds, $format_price, $attr_to_name, $aMain, 'Points');
        } elseif ($source == $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']) {
            $aMain = $this->handleGoods($gid, $sid, $platformIds, $format_price, $attr_to_name, $aMain, 'TryBuying');
        }

        return $aMain;
    }

    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     */
    private function handleGoods($gid, $sid, $platformIds, $format_price, $attr_to_name, $aMain, $type)
    {
        $goodsModel = "goods{$type}Model";
        $goodsPriceModel = "goods{$type}PriceModel";

        $goodsInfo = $this->$goodsModel->GetOneById($gid, $platformIds, $format_price);
        $aMain = array_merge($goodsInfo, $aMain);
        $aMain['sid'] = $sid;

        if ($sid == 0) {
            $priceGood = $this->$goodsPriceModel->GetOneById($gid, 0, $format_price);
            $aMain = array_merge($priceGood, $aMain);
        }

        $aMain['spec'] = [];
        if ($sid > 0) {
            $spec = $this->goodsSpecsModel->GetOneById($gid, $sid);
            $specPrice = $this->$goodsPriceModel->GetOneById($gid, $sid, $format_price);
            if (empty($spec) || empty($specPrice)) return [];
            $spec = array_merge($specPrice, $spec);
            if ($attr_to_name) {
                $av_ids = $spec['av_ids'] ?? '';
                $attr_cnf = [];
                if ($av_ids) {
                    $av_ids = json_decode($av_ids, true);
                    foreach ($av_ids as $k1 => $v1) {
                        $cnf = $this->goodsAvModel->IdToName($v1);
                        !empty($cnf) && $attr_cnf[] = $cnf;
                    }
                    $spec['attr_cnf'] = $attr_cnf;
                }
            }
            $aMain['spec'] = $spec;
        }

        return $aMain;
    }



    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 根据gid获取商品详情
     */
    public function GetAllOneByGid($gid, $platformIds=[], $is_stock=true, $is_price=true, $is_attr=true, $is_specs=true)
    {
        $aGoods = $this->goodsMainModel->GetOneByGid($gid);
        if(empty($aGoods)) return [];

        $source = $aGoods['source'] ?? 0;
        $goodsModel = $source == $this->goodsMainModel::SOURCE['POINTS'] ? 'goodsPointsModel' : 'goodsTryBuyingModel';
        $priceModel = $source == $this->goodsMainModel::SOURCE['POINTS'] ? 'goodsPointsPriceModel' : 'goodsTryBuyingPriceModel';

        if($source == $this->goodsMainModel::SOURCE['POINTS'] || $source == $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY']){
            $goodsInfo = $this->$goodsModel->GetOneById($gid,$platformIds);
            $aGoods = array_merge($goodsInfo,$aGoods);
        }

        if($is_stock){
            list($stock, $sales) = $this->GetSumByGid($gid,$this->goodsStockModel::SOURCE['WARES']);
            $aGoods['stock'] = $stock;
            $aGoods['sales'] = $sales;
        }

        $atype = $aGoods['atype'] ?? -1;
        if ($atype == $this->goodsMainModel::ATYPE['SPECS']) {
            if($is_attr){
                $aGoods['attr_cnf']  = by::GoodsAtkModel()->GetListByGid($gid);
                foreach ($aGoods['attr_cnf'] as $k => $v) {
                    $aGoods['attr_cnf'][$k]['val'] = by::GoodsAvModel()->GetListByAkId($v['id']);
                }
            }

            if($is_specs){
                $specs = $this->goodsSpecsModel->GetSpecsListByGid($gid);
                if($is_price && ($source == $this->goodsMainModel::SOURCE['POINTS'] || $source == $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY'])){
                    foreach ($specs as &$spec){
                        $specPrice = $this->$priceModel->GetOneById($gid,$spec['id']);
                        $spec = array_merge($specPrice,$spec);
                    }
                }
                $aGoods['specs'] = $specs;
            }
        } else {
            if($is_price && ($source == $this->goodsMainModel::SOURCE['POINTS'] || $source == $this->goodsMainModel::SOURCE['TRY_BEFORE_BUY'])){
                $priceGood = $this->$priceModel->GetOneById($gid,0);
                $aGoods = array_merge($priceGood,$aGoods);
            }
        }

        return $aGoods;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function GoodsList($input, $page = 1, $page_size = 20)
    {
        $user_id = $input['user_id'] ?? 0;
        $user_id = strlen($user_id) > 11 ? 0 : CUtil::uint($user_id);
        $is_my = $input['is_my'] ?? 0;
        $cache = true;
        //is_my 判断-我能兑换
        if($user_id && $is_my){
            //1.用户等级控制
            $level = strtolower(by::memberCenterModel()->GetUserLevel($user_id));
            $input['level'] = $level;
            //2.获取用户积分
            $oPoint  = by::point()->get($user_id);//当前数额
            $input['me_point'] = CUtil::uint($oPoint);
            //3.过滤当前不可买商品
            $input['me_time'] = time();
            //4.我可以购买不做缓存
            $cache = false;
        }

        return $this->SyncGoodsList($input,$page,$page_size,$cache);
    }




    /**
     * @throws \yii\db\Exception
     */
    public function SyncGoodsList($input, $page, $page_size, $cache = true): array
    {
        //获取所有数据
        $return = [
            'list'  => [],
            'count' => 0
        ];

        $r_key = $this->goodsMainModel->GetIndexGoodsQueryListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size, 'gids');
        $redis = by::redis('core');
        $aJson = $cache ? $redis->hGet($r_key, $sub_key) : false;
        $gids = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $source      = CUtil::uint($input['source'] ?? 0);
            $simpleInfos = $this->GetAllGoodsSimpleInfo($source);
            $gids        = $this->GetGidsByInput($input, $simpleInfos);
            $redis->hSet($r_key, $sub_key, json_encode($gids));
            CUtil::ResetExpire($r_key, empty($gids) ? 1 : 7200);
        }

        if(empty($gids)) return  [true, $return];

        //3.获取符合条件的商品条数
        $return['count'] = count($gids);

        //4.分页
        $gids = array_slice($gids, ($page - 1) * $page_size, $page_size);
        if (empty($gids)) {
            return [true, $return];
        }

        //5.查出商品详情
        $data = [];
        foreach ($gids as $gid) {
            $dataItem = $this->IndexMainInfoByGid($gid,$input['platformIds'] ?? [],true,true,false);
            //过滤参数
            unset($dataItem['detail'],$dataItem['notice'],$dataItem['specs'],$dataItem['image'],$dataItem['images']);
            $data[]=$dataItem;
        }
        $return['list'] = $data;

        return [true, $return];
    }


    /**
     * @param $input
     * @param $simpleInfos
     * @return array
     * 根据排序等获取用户数据
     */
    public function GetGidsByInput($input,$simpleInfos)
    {
        if (empty($simpleInfos)) return [];
        $tab      = CUtil::uint($input['tab'] ?? 0);
        $is_point = CUtil::uint($input['is_point'] ?? 0);
        $position = CUtil::uint($input['position'] ?? 0);
        $me_point = intval($input['me_point'] ?? '-1');
        $level    = trim($input['level'] ?? 0);
        $me_time  = CUtil::uint($input['me_time'] ?? 0);
        $platform = $input['platformIds'] ?? [];
        $sort_rule = empty($input['sort_rule']) ? [] : (json_decode($input['sort_rule'], true) ?? []);


        if(!empty($tab)){
            foreach ($simpleInfos as $key=>$info){
                if(!in_array((string)$tab,$info['tab'])){
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if(!empty($is_point)){
            foreach ($simpleInfos as $key=>$info){
                if($info['exchange'] !== $this->goodsPointsPriceModel::EXCHANGE['AP']){
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if ($me_point != -1) {
            foreach ($simpleInfos as $key => $info) {
                if ($info['point'] > $me_point) {
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if(!empty($me_time)){
            $dataW = CUtil::uint(date('w'));
            if($dataW == 0) $dataW = 7;
            foreach ($simpleInfos as $key=>$info){
                if(empty($info['online_time'])){
                    continue;
                }
                if (strlen($info['online_time']) >= 9 && !strpos($info['online_time'], '|') && $me_time < intval($info['online_time'])) {
                    unset($simpleInfos[$key]);
                } elseif (strlen($info['online_time']) >= 1) {
                    $onlineArr = explode('|', $info['online_time']);
                    if (strlen($info['online_time']) < 9 || strpos($info['online_time'], '|')) {
                        if (!in_array($dataW, $onlineArr)) {
                            unset($simpleInfos[$key]);
                        }
                    }
                }

            }
            $simpleInfos = array_values($simpleInfos);
        }


        if(!empty($position)){
            foreach ($simpleInfos as $key=>$info){
                if(!in_array((string)$position,$info['position'])){
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if(!empty($level)){
            foreach ($simpleInfos as $key=>$info){
                if(!in_array($level,$info['levels'])){
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if(!empty($platform)){
            foreach ($simpleInfos as $key=>$info){
                if(!in_array($info['platform'],$platform)){
                    unset($simpleInfos[$key]);
                }
            }
            $simpleInfos = array_values($simpleInfos);
        }

        if(!empty($sort_rule)){
            $type = $sort_rule['type'] ?? '';
            $act  = $sort_rule['act'] ?? '';
            $sort = SORT_ASC;
            if(in_array($type,['price','point']) && in_array($act,$this->goodsMainModel::ACT)){
                $sort = ($act == $this->goodsMainModel::ACT['0']) ? SORT_ASC : SORT_DESC;
            }
            $fieldColumn = array_column($simpleInfos,$type);
            $fieldSort = array_column($simpleInfos,'sort');
            $fieldId   = array_column($simpleInfos,'gid');

            array_multisort($fieldColumn, $sort, $fieldSort, SORT_DESC, $fieldId, SORT_DESC, $simpleInfos);
        }

        return empty($simpleInfos) ? [] : array_column($simpleInfos,'gid');

    }


    /**
     * @throws \yii\db\Exception
     */
    public function GetAllGoodsSimpleInfo($source,$cache = true): array
    {
        $allGoods    = $this->GetAllMainList($source);
        $r_key = $this->goodsMainModel->GetAllGoodsSimpleInfoKey();
        $aJson = $cache ? by::redis('core')->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $gids        = array_column($allGoods, 'id');
            $aData = [];
            if ($gids) {
                foreach ($gids as $gid) {
                    $info     = $this->IndexMainInfoByGid($gid, [], false, false, false);
                    $label    = $info['label'] ?? '';
                    $tab      = $info['tab'] ?? '';
                    $position = $info['position'] ?? '';
                    $levels   = $info['levels'] ?? '';

                    $aData[] = [
                        'gid'         => $gid,
                        'price'       => $info['price'] ?? 0,
                        'point'       => CUtil::uint($info['point'] ?? 0),
                        'status'      => CUtil::uint($info['status'] ?? 0),
                        'label'       => empty($label) ? [] : explode('|', $label),
                        'tab'         => empty($tab) ? [] : explode('|', $tab),
                        'position'    => empty($position) ? [] : explode('|', $position),
                        'levels'      => empty($levels) ? [] : explode('|', $levels),
                        'coupon_id'   => $info['coupon_id'] ?? '',
                        'platform'    => CUtil::uint($info['platform'] ?? ''),
                        'sort'        => $info['sort'] ?? '',
                        'exchange'    => CUtil::uint($info['exchange'] ?? ''),
                        'online_time' => trim($info['online_time'] ?? ''),
                    ];
                }
            }
            by::redis('core')->set($r_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 6000);
        }

        return $aData;
    }


    /**
     * @param $aGoods
     * @return mixed
     */
    private function __getMinSpecsPrice($aGoods){
        $specs = $aGoods['specs'] ?? [];
        if($specs){
            $priceColumn = array_column($specs,'price');
            $pointsColumn = array_column($specs,'point');
            array_multisort($priceColumn, SORT_ASC, $pointsColumn, SORT_ASC , $specs);
            $aGoods['price']    = $specs[0]['price'] ?? 0;
            $aGoods['point']    = $specs[0]['point'] ?? 0;
            $aGoods['exchange'] = $specs[0]['exchange'] ?? 0;
        }
        return $aGoods;
    }


    /**
     * @param $field
     * @param $aData
     * @return mixed
     * 获取积分商品字段名
     */
    private function __transferName($field, $aData)
    {
        if(empty($aData)) return $aData;
        $value = $aData[$field] ?? '';
        $aData[$field . '_tag'] = '';
        if ($value) {
            $arr = $this->getArrayByField($field);
            !empty($arr) && $aData[$field . '_tag'] = $this->convertValuesToTag($field,$value, $arr);
        }

        return $aData;
    }


    /**
     * @param $field
     * @param $value
     * @param $arr
     * @return string
     * 按照UI输出字段名
     */
    private function convertValuesToTag($field,$value,$arr): string
    {
        $str = '';
        switch ($field){
            case 'label':
            case 'tab':
            case 'position':
                $str .= $this->convertValuesToNames($value, $arr);break;
            case 'levels':
                $valueArr = array_unique(array_filter(explode('|', $value)));
                $count = count($valueArr);
                if ($count == 1) {
                    $str .= '仅' . $arr[$valueArr[0]] . '专享';
                } elseif ($count == count($arr)) {
                    $str .= '';
                } else {
                    $str .= $arr[$valueArr[0]] . '及以上专享';
                }
                break;
            case 'online_time':
                if(strlen($value) < 5 || strpos($value, '|') !== false){
                    $str.= '仅'.$this->convertValuesToNames($value,$arr,'|','、').'可买';
                }else{
                    $value = CUtil::uint($value);
                    !empty($value) && $value > intval(START_TIME)&& $str.= date('m月d日 H:i',$value).'上架';
                }
                break;
            default:;
        }
        return $str;
    }

    /**
     * @param $value
     * @param $arr
     * @param string $sep
     * @param string $osp
     * @return string
     * 根据分割符转换字段名称
     */
    private function convertValuesToNames($value, $arr, string $sep = '|', string $osp= '|'): string
    {
        $valueArr = explode($sep, $value);
        $strArr = array_map(function ($item) use ($arr) {
            return $arr[$item] ?? null;
        }, $valueArr);
        $filteredStrArr = array_filter($strArr);
        return implode($osp, $filteredStrArr);
    }
}
