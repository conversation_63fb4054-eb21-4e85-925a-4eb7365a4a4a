<?php

namespace app\modules\wares\services\goods;


use app\models\by;
use app\models\CUtil;
use yii\db\Exception;


class GoodsSpecsService
{
    private static $_instance = NULL;

    protected $goodsAtkModel;
    protected $goodsAvModel;
    protected $goodsSpecsModel;
    protected $goodsStockModel;
    protected $goodsMainModel;


    public function __construct()
    {
        $this->goodsAtkModel   = by::GoodsAtkModel();
        $this->goodsAvModel    = by::GoodsAvModel();
        $this->goodsSpecsModel = by::GoodsSpecsModel();
        $this->goodsStockModel = by::GoodsStockModel();
        $this->goodsMainModel  = by::GoodsMainModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @throws Exception
     */
    public function GetOneByAvIds($gid, $av_ids, $num, $platformIds=[]): array
    {
        $gid        = CUtil::uint($gid);
        $num        = CUtil::uint($num);
        $av_ids     = (array)json_decode($av_ids, true);
        if (empty($gid) || empty($num)) {
            return [false, '参数错误'];
        }

        $indexGMainService = new IndexGoodsMainService();

        $gInfo      = $indexGMainService->GetAllOneByGid($gid, $platformIds,false, true, false,true);

        if (empty($gInfo)) {
            return [false, '参数错误(2)'];
        }

        switch ($gInfo['atype']) {
            case $this->goodsMainModel::ATYPE['SPEC']:
                return [true, [
                    'gid'       => $gid,
                    'sku'       => '',
                    'price'     => $gInfo['price'],
                    'point'     => $gInfo['point'] ?? 0,
                    'coupon_id' => $gInfo['coupon_id'] ?? 0,
                    'av_ids'    => '',
                    'image'     => $gInfo['cover_image'],
                    'num'       => $num,
                ]];
                break;
            default:
                if (empty($av_ids)) {
                    return [false, '参数错误'];
                }
                $aLog       = $gInfo['specs'] ?? [];
                if(empty($aLog)){
                    return [false,'参数错误'];
                }
                foreach ($aLog as $val) {
                    $av1_ids = json_decode($val['av_ids'], true);
                    if (!array_diff($av1_ids, $av_ids)) {
                        if (isset($val['price'])) {
                            $val['num']     = $num;
                        }
                        return [true, $val];
                    }
                }
                return [false, '该商品没有此属性'];
        }
    }

    /**
     * @param int $gid
     * @return array
     * @throws Exception
     * sid查属性
     */
    public function GetAttrByGid(int $gid): array
    {
        $gid        = CUtil::uint($gid);
        if (empty($gid)) {
            return [false, '参数错误'];
        }

        $aData      = $this->goodsAtkModel->GetListByGid($gid);
        //商品属性值
        foreach ($aData as $k => $v) {
            $aData[$k]['val'] = $this->goodsAvModel->GetListByAkId($v['id']);
        }

        return [true, $aData];
    }
}
