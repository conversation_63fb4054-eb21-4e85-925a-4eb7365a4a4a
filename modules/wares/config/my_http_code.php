<?php

$config['code'] = [
    'common_error'    => -1,
    'no_bind_phone'   => -2,//未绑定手机号
    'frequent'        => -10,
    'session_expired' => -100,
    'friends_limit'   => -200,
    'network_busy'    => -1000,
    'account_frozen'  => -1001,//被冻结
    'account_seal'    => -1002,//被封禁
    'account_del'     => -1003,//被删除
    'action_error'    => -10000,
];

$config['code_msg'] = [
    '-1000' => '网络繁忙',
    '-1001' => '该用户已被冻结',
    '-1002' => '该用户已被封号',
    '-1003' => '该用户不存在',
];


$config['status'] = [
    'ok'          => 200,//（成功）  服务器已成功处理了请求。 通常，这表示服务器提供了请求的网页。
    'request_err' => 400,//（错误请求） 服务器不理解请求的语法。
    'inter_err'   => 500,//(服务器内部错误）  服务器遇到错误，无法完成请求。
];