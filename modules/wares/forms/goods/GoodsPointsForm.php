<?php

namespace app\modules\wares\forms\goods;

use app\models\by;
use app\models\byNew;
use app\modules\back\forms\BaseModel;
use app\modules\back\services\CouponService;

class GoodsPointsForm extends BaseModel
{
    public $id;
    public $name;
    public $type;
    public $cover_image;
    public $images;
    public $mprice;
    public $is_send;
    public $detail;
    public $levels;
    public $notice;
    public $online;
    public $shipping;
    public $sku;
    public $subtype;


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
                ['id', 'number', 'message' => 'id必须为数字'],
                ['subtype', 'number', 'message' => 'id必须为数字'],
                ['name', 'required', 'message' => '请填写商品名称'],
                ['sku', 'required', 'message' => '请填写商品编号'],
                ['type', 'required', 'message' => '请填写商品类型'],
                ['cover_image', 'required', 'message' => '请上传封面图'],
                ['images', 'required', 'message' => '请上传图片集'],
                ['mprice', 'required', 'message' => '请填写市场价'],
                ['is_send', 'number', 'min' => 1, 'max' => 2, 'message' => '请确认是否发货'],
                ['detail', 'required', 'message' => '请填写商品详情'],
                ['notice', 'required', 'message' => '兑换须知必填'],
                ['online', 'number', 'min' => 1, 'max' => 2, 'message' => '上架类型必传'],
                ['shipping', 'number', 'min' => 1, 'max' => 2, 'message' => '是否包邮必选'],
                ['type', 'validateTypeAndShipping'],
        ];
    }

    public function validateTypeAndShipping(): bool
    {
        if ($this->type == 2) {
            if (empty($this->subtype)){
                $this->addError('subtype', '请选择子商品类型');
                return false;
            }

            $query = by::GoodsMainModel()::find()
                    ->leftJoin(by::GoodsPointsModel()::tbName(), 't_goods_points.gid = t_goods_main.id')
                    ->where(['sku' => $this->sku, 'subtype' => $this->subtype]);

            if (!empty($this->id)) {
                $query->andWhere(['<>', 't_goods_main.id', $this->id]);
            }

            $exists = $query->exists();
            $productType = $this->subtype == 1 ? '优惠券' : '停车券';

            if ($exists) {
                $this->addError('sku', "{$productType}商品编号已存在");
                return false;
            }

            list($coupon_status, $msg) = CouponService::getInstance()->check($this->subtype, $this->sku);
            if (!$coupon_status){
                $this->addError('sku', $msg);
                return false;
            }
        }

        if ($this->type == 2 && $this->shipping != 2) {
            $this->addError('shipping', '虚拟商品，必须选择包邮');
            return false;
        }

        // 虚拟商品智能选择不发货
        if ($this->type == 2 && $this->is_send != 2) {
            $this->addError('is_send', '虚拟商品，必须选择不发货');
            return false;
        }

        return true;
    }

    /**
     * 从新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
                'id'          => 'id',
                'name'        => '商品名称',
                'type'        => '商品类型',
                'cover_image' => '封面图',
                'images'      => '图片集',
                'mprice'      => '市场价',
                'is_send'     => '是否发货',
                'detail'      => '商品详情',
                'levels'      => '用户等级',
                'notice'      => '兑换须知',
                'online'      => '上架方式',
                'shipping'    => '是否包邮',
        ];
    }
}
