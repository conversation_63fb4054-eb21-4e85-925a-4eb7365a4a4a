<?php

namespace app\modules\wares\forms\goods;

use app\modules\back\forms\BaseModel;

class GoodsTryBeforeBuyForm extends BaseModel
{
    public $name;
    public $label;
    public $cover_image;
    public $images;
//    public $mprice;
    public $try_price;
    public $price;
    public $detail;
    public $stock;
    public $notice;
    public $online;


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            ['name', 'required', 'message' => '请填写商品名称'],
            ['label', 'required', 'message' => '请选择商品标签'],
            ['cover_image', 'required', 'message' => '请上传封面图'],
            ['images', 'required', 'message' => '请上传图片集'],
//            ['mprice', 'required', 'message' => '请填写商品原价'],
            ['try_price', 'required', 'message' => '请填写试用价格'],
            ['price', 'required', 'message' => '请填写商品售价'],
            ['stock', 'required', 'message' => '请填写商品库存'],
            ['detail', 'required', 'message' => '请填写商品详情'],
            ['online', 'number', 'min' => 1, 'max' => 2, 'message' => '上架类型必传']
        ];
    }

    /**
     * 从新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'name'        => '商品名称',
            'cover_image' => '封面图',
            'label'       => '商品标签',
            'images'      => '图片集',
//            'mprice'      => '市场价',
            'try_price'   => '试用价格',
            'price'       => '商品售价',
            'stock'       => '商品库存',
            'is_send'     => '是否发货',
            'detail'      => '商品详情',
            'notice'      => '兑换须知',
            'online'      => '上架方式'
        ];
    }
}
