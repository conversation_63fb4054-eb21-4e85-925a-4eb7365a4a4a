<?php

namespace app\modules\wares\forms\goods;

use app\modules\back\forms\BaseModel;

class GoodsMainForm extends BaseModel
{
    // 属性
    public $name;         // 商品名称
    public $sku;   // 商品编码
    public $source;   // 商品来源
    public $atype;   // 商品规格


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            ['name', 'required', 'message' => '请填写商品名称'],
            ['sku', 'required', 'message' => '请填写商品编码'],
            ['source', 'number','max' => 3, 'message' => '请区分商品来源'],
            ['atype', 'number', 'min'=>0, 'max'=>1, 'message' => '请选择商品规格'],
        ];
    }

    /**
     * 从新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'name'   => '商品名称',
            'sku'    => '商品编码',
            'source' => '商品来源',
            'atype' => '商品规格',
        ];
    }
}
