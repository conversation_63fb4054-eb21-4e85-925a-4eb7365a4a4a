<?php

namespace app\modules\wares\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class GiftUserCardsModel extends CommModel
{

    public $tb_fields = [
        'id', 'user_id', 'sharer_id', 'card_id', 'card_no', 'amount', 'type', 'status', 'content', 'expire_time', 'ctime', 'utime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_gift_user_cards`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    const STATUS = [
        'RECEIVED' => 1,  //已领取
        'FROZEN'   => 2,  //已冻结
        'SHARED'   => 3,  //已分享
        'USED'     => 4,  //已使用
    ];

    const TYPE = [
        'STORAGE_CASH'    => 1, //储值现金
        'ASSIGN_EXCHANGE' => 2, //指定兑换
    ];

    const FROZEN_TIME = YII_ENV_PROD ? 3600 : 300; //冻结时间
    /**
     * @param $userId
     * @param $status
     * @return string
     * 礼品卡列表key
     */
    private function userGiftCardListRedisKey($userId, $status): string
    {
        return AppCRedisKeys::userGiftCardListRedisKey($userId, $status);
    }

    /**
     * @param $userId
     * @return void
     * @throws RedisException 删除礼品卡列表key
     * ----激活时候删除
     */
    public function __delUserGiftCardListRedisKey($userId)
    {
        $redis     = by::redis();
        $redisKey1 = $this->userGiftCardListRedisKey($userId, 1);
        $redisKey2 = $this->userGiftCardListRedisKey($userId, 2);
        $redis->del($redisKey1, $redisKey2);
    }

    /**
     * @param $id
     * @return string
     * 礼品卡详情key
     */
    private function userGiftCardInfoRedisKey($id): string
    {
        return AppCRedisKeys::userGiftCardInfoRedisKey($id);
    }


    /**
     * 分享卡片记录
     * @param int $sharerId
     * @return string
     */
    private function getUserCardsByShareIdCacheKey(int $sharerId): string
    {
        return AppCRedisKeys::getUserCardsByShareIdCacheKey($sharerId);
    }

    /**
     * @param $id
     * @return void
     * @throws RedisException
     * 删除礼品卡详情key
     */
    public function __delUserGiftCardInfoRedisKey($id)
    {
        $redis    = by::redis();
        $redisKey = $this->userGiftCardInfoRedisKey($id);
        $redis->del($redisKey);
    }


    /**
     * @param $userId
     * @param $status 1可用 2不可用
     * @return array
     * @throws Exception
     * @throws RedisException
     * 查询礼品卡列表
     */
    public function giftCardList($userId, $status): array
    {
        $redis      = by::redis();
        $redisKey   = $this->userGiftCardListRedisKey($userId, $status);
        $cachedData = $redis->get($redisKey);

        // 如果缓存中有数据，直接返回
        if (!empty($cachedData)) {
            return json_decode($cachedData, true) ? : [];
        }
        // 查询数据库
        $tb          = self::tbName();
        $currentTime = time();
        switch ($status) {
            // 可用：有金额 且 没过期
            case 1:
                $sql = "SELECT * FROM {$tb} WHERE `user_id` = :userId AND `status` = 1 AND `expire_time` >= :expireTime ORDER BY `id` DESC";
                break;
            // 不可用：已过期 或 没金额 或 分享冻结状态
            case 2:
                $sql = "SELECT * FROM {$tb} WHERE `user_id` = :userId and (`status` IN(2, 3, 4) or `expire_time` <= :expireTime ) ORDER BY `utime` DESC";
                break;
            default:
                return [];
        }

        $params = [':userId' => $userId, ':expireTime' => $currentTime];
        $result = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $result = empty($result) ? [] : $result;

        // 将查询结果缓存，并设置过期时间
        $expirationTime = empty($result) ? 10 : 1800;
        $redis->set($redisKey, json_encode($result), ['EX' => $expirationTime]);

        return $result;
    }


    /**
     * @param $id
     * @param bool $isCache
     * @return array|mixed|DataReader
     * @throws Exception
     * @throws RedisException 获取卡详情
     */
    public function userGiftCardInfo($id, bool $isCache = true)
    {
        $result = [];
        $redis = by::redis();
        $redisKey = $this->userGiftCardInfoRedisKey($id);

        // 查询缓存
        $cachedData = $redis->get($redisKey);
        if ($isCache && !empty($cachedData)) {
            return json_decode($cachedData, true) ?: [];
        }

        // 查询数据库
        $tb = self::tbName();
        $sql = "SELECT * FROM {$tb} WHERE id = :id";
        $params = [':id' => $id];
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();

        // 将查询结果缓存，并设置过期时间
        if ($isCache) {
            $expirationTime = empty($result) ? 10 : 1800;
            $redis->set($redisKey, json_encode($result), ['EX' => $expirationTime]);
        }

        return empty($result) ? [] : $result;
    }


    public function getUserCardsByIds(array $ids)
    {
        return self::find()
            ->where([
                'IN', 'id', $ids
            ])
            ->asArray()
            ->all();
    }

    public function getUserCardsByCardNos(array $cardNos)
    {
        return self::find()
            ->where([
                'IN', 'card_no', $cardNos
            ])
            ->asArray()
            ->all();
    }

    /**
     * 获取分享人的卡片
     * @param int $sharerId
     * @return array|mixed|void
     * @throws RedisException
     */
    public function getUserCardsByShareId(int $sharerId)
    {
//        $redisKey = $this->getUserCardsByShareIdCacheKey($sharerId);
//
//        // 查询缓存
//        $redis = by::redis();
//        $cachedData = $redis->get($redisKey);
//        if (!empty($cachedData)) {
//            return json_decode($cachedData, true) ?: [];
//        }

        // 查询数据库
        $result = self::find()
            ->where(['sharer_id' => $sharerId])
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();

//        // 将查询结果缓存，并设置过期时间
//        $expirationTime = empty($result) ? 10 : 1800;
//        $redis->set($redisKey, json_encode($result), ['EX' => $expirationTime]);

        return $result;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 修改卡状态
     */
    public function updateUserCard($id, $user_id, $status = 2): array
    {
        $tb = self::tbName();

        $save = [];
        if ($status) {
            $save = [
                'status' => $status,
                'utime'  => intval(START_TIME)
            ];
        }

        by::dbMaster()->createCommand()->update($tb, $save, ['id' => $id])->execute();
        $this->__delUserGiftCardInfoRedisKey($id);
        $this->__delUserGiftCardListRedisKey($user_id);
        return [true, 'ok'];
    }

    /**
     * 添加、编辑数据（清除缓存）
     * @param $input
     * @param int $id
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function saveData($input, int $id = 0, $tran = null): array
    {
        $db = $tran ?: by::dbMaster();
        $tb = self::tbName();
        if ($id) {
            $db->createCommand()->update($tb, $input, "`id`=:id", [":id" => $id])->execute();
        } else {
            $db->createCommand()->insert($tb, $input)->execute();
            $id = $db->getLastInsertID();
        }
        // 清除缓存
        $card = self::find()->where(['id' => $id])->asArray()->one();
        if ($card) {
            $this->__delUserGiftCardInfoRedisKey($id);
            $this->__delUserGiftCardListRedisKey($card['user_id']);
        }
        return [true, $input];
    }
}
