<?php

namespace app\modules\wares\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class GoodsTryBuyingModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'name', 'cover_image', 'images', 'mprice', 'try_price', 'limit_num', 'introduce', 'label', 'detail',
        'platform', 'online_time', 'shipping', 'video', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_goods_try_buying`";
    }


    private function __getGoodsTryByIdKey($gid): string
    {
        return AppCRedisKeys::getGoodsTryByIdKey($gid);
    }

    /**
     * @throws RedisException
     */
    public function delGoodsTryByIdKey($gid)
    {
        $redisKey = $this->__getGoodsTryByIdKey($gid);
        by::redis()->del($redisKey);
    }


    public function UpdateLog($gid, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'name', 'cover_image', 'images', 'mprice', 'limit_num', 'introduce', 'label', 'detail',
            'platform', 'online_time', 'shipping', 'video', 'is_del', 'utime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['mprice'])) {
            $update['mprice'] = by::Gtype0()->totalFee($update['mprice']);
        }

        $gid  = CUtil::uint($gid);
        $aLog = $this->GetOneById($gid);

        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tableName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid])->execute();

        $this->delGoodsTryByIdKey($gid);
        byNew::ActivityModel()->delWAListCacheKey();
        return [true, "成功"];
    }

    public function saveLog($data): array
    {
        // 1. 参数检查与优化
        $gid      = CUtil::uint($data['gid'] ?? 0);
        $mprice   = sprintf("%.2f", $data['mprice'] ?? 0);
        $tryPrice = sprintf("%.2f", $data['try_price'] ?? 0);

        if (empty($gid)) {
            return [false, "增加先试后买商品-缺少必要参数"];
        }

        //2.替换掉所有js标签
        $detail = $data['detail'] ?? "";
        $detail = preg_replace("/<script[\s\S]*?<\/script>/i", "", $detail); //防注入

        // 3. 数据整理
        $save = [
            'gid'         => $gid,
            'name'        => $data['name'] ?? "",
            'cover_image' => $data['cover_image'] ?? "",
            'images'      => $data['images'] ?? "",
            'mprice'      => by::Gtype0()->totalFee($mprice),
            'try_price'   => by::Gtype0()->totalFee($tryPrice),
            'limit_num'   => $data['limit_num'] ?? 0,
            'label'       => $data['label'] ?? "",
            'detail'      => $detail,
            'platform'    => $data['platform'] ?? 99,
            'online_time' => time(),
            'shipping'    => $data['shipping'] ?? 2,
            'video'       => $data['video'] ?? "",
            'ctime'       => time(),
            'utime'       => time(),
        ];

        // 4. 使用预处理语句进行数据库插入
        $tb      = self::tableName();
        $command = by::dbMaster()->createCommand()->insert($tb, $save);
        $ret     = $command->execute();
        $id      = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，先试后买商品新增失败"];
        }

        return [true, $id];
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function GetOneById($gid, $platformIds = [], $format_price = true, bool $cache = true,$is_del = true): array
    {
        $gid = CUtil::uint($gid);

        if (empty($gid)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getGoodsTryByIdKey($gid);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb             = $this::tableName();
            $fields         = implode(",", $this->tb_fields);
            $isDelCondition = $is_del ? ' AND `is_del` = 0 ' : '';
            $sql            = "SELECT {$fields} FROM {$tb} WHERE `gid` = :gid {$isDelCondition} LIMIT 1";
            $aData          = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryOne();
            $aData          = $aData ?: [];
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if ($format_price) {
            $aData['mprice']    = CUtil::totalFee($aData['mprice'], 1);
            $aData['try_price'] = CUtil::totalFee($aData['try_price'], 1);
        }


        return $aData;
    }


}
