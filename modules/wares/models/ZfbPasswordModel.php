<?php

namespace app\modules\wares\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class ZfbPasswordModel extends CommModel
{

    public $tb_fields = [
        'id', 'zfb_password', 'password_expire_time', 'qr_code', 'zfb_link', 'link_expire_time', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_zfb_password`";
    }

    //二维码落地前缀
    const QRCODE = 'alipays://platformapi/startapp';

    private function getZfwPasswordKey(): string
    {
        return AppCRedisKeys::getZfwPasswordKey();
    }

    public function __delZfwPasswordKey()
    {
        $redis = by::redis();
        $redis->del($this->getZfwPasswordKey());
    }

    /**
     * @throws Exception
     * 保存支付宝口令
     */
    public function saveLog($id, $data): array
    {
        $db             = by::dbMaster();
        $tb             = self::tableName();
        $successMessage = '保存成功';
        $failureMessage = '保存失败';

        // 检查表中是否已经存在记录
        $existingCount = $db->createCommand("SELECT COUNT(*) FROM {$tb}")->queryScalar();

        if ($id) {
            // 更新操作
            $updatedRows = $db->createCommand()
                ->update($tb, $data, ['id' => $id])
                ->execute();
            $result      = $updatedRows > 0;
        } else {
            if ($existingCount > 0) {
                // 如果已经存在记录，则不允许插入新记录
                return [false, '已经存在一条记录，不能插入新的记录'];
            }
            // 插入操作
            $data['ctime'] = time();
            $insertedRows  = $db->createCommand()
                ->insert($tb, $data)
                ->execute();
            $result        = $insertedRows > 0;
        }

        $this->__delZfwPasswordKey();
        return $result ? [true, $successMessage] : [false, $failureMessage];
    }

    /**
     * @param bool $isCache
     * @return array
     * @throws RedisException
     * 获取支付宝口令
     */
    public function getLatestInfo(bool $isCache = false): array
    {
        $redis    = by::redis();
        $redisKey = $this->getZfwPasswordKey();

        // 尝试从缓存中获取记录
        $record = $isCache ? json_decode($redis->get($redisKey), true) : null;

        // 如果缓存不存在或者缓存的记录为空，则从数据库中获取最新记录
        if ($record === null) {
            $record = self::find()->orderBy(['id' => SORT_DESC])->limit(1)->one();

            if ($record) {
                $record = $record->toArray();
            }
            // 如果启用了缓存，并且数据库中有记录，则将记录存入缓存
            $isCache && $redis->set($redisKey, json_encode($record), ['ex' => empty($aData) ? 10 : 3600]);
        }

        // 返回查询结果
        return $record !== null ? [true, $record] : [true, []];
    }


}
