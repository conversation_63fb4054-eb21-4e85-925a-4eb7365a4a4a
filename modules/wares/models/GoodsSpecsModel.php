<?php
/**
 * Author :CP
 */

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class GoodsSpecsModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'sku', 'av_ids', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_specs`";
    }

    /**
     * @param $gid
     * @return string
     * 根据gid获取属性列表
     */
    private function __getGoodsSpecsListByGid($gid): string
    {
        return AppWRedisKeys::getGoodsSpecsListByGid($gid);
    }


    /**
     * @param $gid
     * @return void
     * 列表缓存清理
     */
    private function __delListCache($gid)
    {
        $r_key = $this->__getGoodsSpecsListByGid($gid);
        by::redis('core')->del($r_key);
    }


    /**
     * @param $gid
     * @param $id
     * @return string
     * 根据id获取数据
     */
    private function __getGoodsSpecsByIdKey($gid, $id): string
    {
        return AppWRedisKeys::getGoodsSpecsById($gid, $id);
    }

    /**
     * @param $gid
     * @param $id
     * 单条数据清理
     */
    private function __delIdCache($gid, $id)
    {
        $r_key = $this->__getGoodsSpecsByIdKey($gid, $id);
        by::redis('core')->del($r_key);
    }


    /**
     * @param $gid
     * @param $sku
     * @return string
     * 根据sku 获取数据
     */
    private function __getSpecsByGidSkuKey($gid,$sku): string
    {
        return AppWRedisKeys::getSpecsByGidSku($gid,$sku);
    }

    /**
     * @param $gid
     * @param $sku
     * @return int|\Redis
     */
    private function __delSkuCache($gid,$sku)
    {
        $r_key = $this->__getSpecsByGidSkuKey($gid,$sku);
        return by::redis('core')->del($r_key);
    }


    /**
     * @param $gid
     * @param bool $cache
     * @return array
     * @throws Exception
     * 获取多规格列表
     */
    public function GetSpecsListByGid($gid, bool $cache = true, $isDelete = false): array
    {
        $gid = CUtil::uint($gid);
        if (empty($gid)) return [];

        $redis = by::redis('core');
        $r_key = $this->__getGoodsSpecsListByGid($gid);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array) json_decode($aJson, true);

        if ($aJson === false) {
            $tb = $this::tbName();
            if ($isDelete) {
                $sql = "SELECT `id` FROM {$tb} WHERE `gid` = :gid ";
            } else {
                $sql = "SELECT `id` FROM {$tb} WHERE `gid` = :gid AND `is_del` = 0";
            }
            $aData = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryAll();
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }
        if (empty($aData)) {
            return [];
        }

        $return = [];
        foreach ($aData as $val) {
            $return[] = $this->GetOneById($gid, $val['id']);
        }

        return $return;
    }


    /**
     * @param $gid
     * @param $id
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     * 获取单条数据
     */
    public function GetOneById($gid, $id, bool $cache = true)
    {
        $gid = CUtil::uint($gid);
        $id  = CUtil::uint($id);

        if (empty($gid) || empty($id)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getGoodsSpecsByIdKey($gid, $id);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(",", $this->tb_fields);
            $sql    = "SELECT {$fields} FROM  {$tb} WHERE `id`=:id AND `gid`=:gid LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id, ':gid' => $gid])->queryOne();
            $aData  = $aData ?: [];
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $aData;
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 增加商品sku（多规格）
     */
    public function SaveLog(array $aData): array
    {
        $gid    = $aData['gid'] ?? 0;
        $av_ids = $aData['av_ids'] ?? "";
        $sku    = $aData['sku'] ?? "";

        if (empty($gid) || empty($av_ids) || empty($sku)) {
            return [false, "增加属性sku失败-缺少必要参数"];
        }

        $save = [
            'gid'    => $gid,
            'av_ids' => $av_ids,
            'sku'    => $sku,
            'ctime'  => intval(START_TIME)
        ];

        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id  = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，属性sku新增失败"];
        }

        $this->__delIdCache($gid, $id);
        $this->__delListCache($gid);
        return [true, $id];
    }


    /**
     * @param $gid
     * @param $sku
     * @param array $update
     * @return array
     * @throws Exception
     * 更新多规格
     */
    public function UpdateLog($gid, $sku, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'av_ids', 'is_del', 'utime', 'dtime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $gid  = CUtil::uint($gid);
        $sku   = trim($sku);
        $aLog = $this->GetOneByGidSku($gid, $sku);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid, 'id' => $aLog['id']])->execute();

        $this->__delSkuCache($gid,$sku);
        $this->__delIdCache($gid, $aLog['id']);
        $this->__delListCache($gid);
        return [true, $aLog['id']];
    }


    /**
     * @throws Exception
     */
    public function GetOneByGidSku($gid, string $sku, $cache = true): array
    {
        $gid = CUtil::uint($gid);
        if (empty($gid) || empty($sku)) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = $this->__getSpecsByGidSkuKey($gid,$sku);

        $aJson      = $cache ? $redis->get($r_key) : false;
        $aData      = (array)json_decode($aJson, true);

        if ($aJson  === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id`,`gid` FROM  {$tb} WHERE `gid`= :gid AND `sku`=:sku  AND`is_del`=0 LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':gid'=>$gid ,':sku' => $sku])->queryOne();
            $aData   = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $this->GetOneById($aData['gid'], $aData['id']);
    }



    /**
     * @param string $str:json [{"at_val":"颜色:红色,尺码:s","sku":"12211","price":"0.01","points":"500","exchange":"1","image":"http://abc.jpg"}]
     * @return array
     *
     */
    public function checkSpecs(string $str): array
    {
        $arr = json_decode($str, true);
        if (!is_array($arr)) {
            return [false, "属性sku参数有误"];
        }

        foreach ($arr as $k => $v) {
            $at_val     = $v['at_val'] ?? "";
            $sku        = $v['sku']     ?? "";
            if (empty($at_val) || empty($sku)) {
                return [false, "属性sku参数有误-缺少必要参数"];
            }
        }

        return [true, $arr];
    }


    /**
     * @param int $gid
     * @param array $aData
     * @return array
     * @throws Exception
     * 属性名转换
     */
    public function AttrToName(int $gid, array $aData)
    {
        foreach ($aData as $key => $val) {
            $av_ids = json_decode($val['av_ids'], true);

            foreach ($av_ids as $k => $v) {
                $cnf = by::GoodsAvModel()->IdToName($v);
                !empty($cnf) &&  $aData[$key]['attr_cnf'][$k] = $cnf;
            }

            $aData[$key]['stock']   = by::GoodsStockModel()->OptStock($val['sku']??0);
        }

        return $aData;
    }
}
