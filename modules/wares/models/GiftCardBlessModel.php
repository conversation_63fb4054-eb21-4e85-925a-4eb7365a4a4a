<?php

namespace app\modules\wares\models;


use app\modules\main\models\CommModel;

class GiftCardBlessModel extends CommModel
{

    public $tb_fields = [
        'id', 'encode', 'bless_content', 'ctime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_gift_card_bless`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function getBlessContent($enCode)
    {
        $blessData = self::find()->
            where(['encode' => $enCode])
            ->asArray()
            ->one();
        return empty($blessData) ? [] : $blessData;
    }

}
