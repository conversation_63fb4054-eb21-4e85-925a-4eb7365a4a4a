<?php

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GoodsStockModel extends CommModel
{

    public $tb_fields = [
        'id', 'sku', 'stock', 'sales', 'wait',
        'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_stock`";
    }

    public static function tableName(): string
    {
        return static::tbName();
    }


    const SOURCE = [
        'MAIN'  => 1,//会员商品模块来源
        'WARES' => 2,//积分（新）商品模块来源
        'OTHER' => 99 //其他来源
    ];

    /**
     * @param $sku
     * @return string
     * 库存
     */
    private function __getGoodsStockKey($sku): string
    {
        return AppWRedisKeys::goodsStock($sku);
    }

    /**
     * @param $sku
     * @return string
     * 销量缓存KEY
     */
    private function __getGoodsSalesKey($sku): string
    {
        return AppWRedisKeys::goodsSales($sku);
    }

    /**
     * @param $sku
     * @return string
     * 未付款数缓存KEY
     */
    private function __getGoodsWaitsKey($sku): string
    {
        return AppWRedisKeys::goodsWait($sku);
    }

    /**
     * @param $skus
     * @return string
     * 获取商品的库存列表
     */
    private function __getGoodsListStockKey($skus): string
    {
        if(is_array($skus)) $skus=serialize($skus);
        return AppWRedisKeys::goodsListStock($skus);
    }

    /**
     * @param $skus
     * @return void
     * 删除商品的库存列表
     */
    public function __delCache($skus)
    {
        if(is_array($skus)) $skus=serialize($skus);
        $r_key = $this->__getGoodsListStockKey($skus);
        by::redis('core')->del($r_key);
    }

    /**
     * @param $skus
     * @throws \yii\db\Exception
     * 删除所有sid缓存
     */
    private function __delOptCache($skus)
    {
        if(is_array($skus) && $skus){
            foreach ($skus as $sku) {
                $this->OptStock($sku, 'DEL');
            }
        }else{
            $this->OptStock($skus, 'DEL');
        }
    }

    /**
     * @param $sku
     * 删除库存、销量、未付款数缓存
     */
    public function DelAllCache($sku)
    {
        $r_key1 = $this->__getGoodsStockKey($sku);
        $r_key2 = $this->__getGoodsSalesKey($sku);
        $r_key3 = $this->__getGoodsWaitsKey($sku);
        $r_key = $this->__getGoodsListStockKey($sku);

        by::redis()->del($r_key1, $r_key2, $r_key3, $r_key);
    }

    /**
     * @param $gid
     * @param array $data
     * @param array $specsData
     * @return array
     * @throws \yii\db\Exception
     * 数据增改
     */
    public function SaveLog($gid, array $data = [], array $specsData = [], string $source = self::SOURCE['WARES'])
    {
        $tb        = self::tbName();
        $goodsMain = by::GoodsMainModel();
        $aData     = [];
        $need_fid  = ['sku', 'stock', 'ctime','utime'];

        $atype = $data['atype'] ?? -1;
        $isSend = $data['is_send'] ?? 1;


        if ($atype == $goodsMain::ATYPE['SPECS']) {
            switch ($source){
                case self::SOURCE['MAIN']:
                    $aData = by::Gspecs()->getListByGid($gid);
                    break;
                case self::SOURCE['WARES']:
                    $aData = by::GoodsSpecsModel()->GetSpecsListByGid($gid);
                    break;
                default:break;
            }
            $aData = array_column($aData, 'sku');
        }

        //保存数据
        $values = '';
        $ctime     = intval(START_TIME);
        $utime     = time();

        $saveData = [];

        if (!empty($aData)) {
            //添加子商品库存
            $specsData = array_column($specsData, null, 'sku');
            foreach ($aData as $key => $sku) {
                $stock  = $specsData[$sku]['stock'] ?? 0;
                $stock  = CUtil::uint($stock);
                $saveData[] = [
                    'sku'   => $sku,
                    'stock' => $stock,
                ];
            }
            $skus = $aData;
        } else {
            $stock  = $data['stock'] ?? 0;
            $stock  = CUtil::uint($stock);
            $sku = trim($data['sku'] ?? '');
            $saveData[] = [
                'sku'   => $sku,
                'stock' => $stock,
            ];
            $skus = $sku;
        }
        if(empty($saveData)) return [true,'ok'];

        //已有的数据不予更新 (库存可以)
//        foreach ($saveData as $key => $v) {
//            //查询是否已有sku
//            $sku   = $v['sku'];
//            $stock = $v['stock'];
//            if (empty($stock) && $this->getStockListBySkus($sku,true) && $isSend == 1) {
//                unset($saveData[$key]);
//            }
//        }
        $saveData = array_values($saveData);

        if($saveData){
            foreach ($saveData as $item){
                $values .= "('{$item['sku']}', {$item['stock']}, {$ctime},{$utime}),";
            }
            $values = rtrim($values, ',');
        }else{
            return [true, 'ok'];
        }

        $field = implode(',', $need_fid);
        $sql   = "INSERT INTO {$tb} ({$field})  
                        VALUES {$values} 
                        ON DUPLICATE KEY UPDATE `stock` = values(`stock`), `utime` = values(`utime`)";
        by::dbMaster()->createCommand($sql)->execute();

        //删除所有sid缓存
        $this->__delOptCache($skus);


        return [true, 'ok'];
    }

    /**
     * @param $gid
     * @param $sid
     * @param $num
     * @param $opt
     * @param bool $positive
     * @param string $source
     * @return array
     * @throws \yii\db\Exception
     */
    public function UpdateStock($gid, $sid = 0, $num = 0, $opt = 'ROLL', bool $positive = true,string $source = self::SOURCE['WARES'],string $sku = '')
    {
        if($source != self::SOURCE['OTHER']){
            $sku = $this->GetSkuByGidAndSid($gid,$sid,$source);
            !YII_ENV_PROD && CUtil::debug($sku.'__'.$gid.'__'.$sid.'__'.$num.'__'.$opt,'update-stock');
            if(empty($sku)){
                return [false,'SKU不存在'];
            }
        }else{
            if(empty($sku)){
                return [true,'OK'];
            }
        }

        $num = abs($num);
        $num = $positive ? $num : -$num;

        $tb = self::tbName();

        switch ($opt) {
            case 'ROLL':
                //1、先更新redis库存数据-第二次校验库存
                list($s, $m) = $this->OptStock($sku, 'INCR', -$num);
                if (!$s) {
                    return [false, $m];
                }

                //2、更新数据库数据-第三次数据库校验库存-库存字段非负
                $sql   = "UPDATE {$tb} SET `stock`=`stock`-(:num),`sales`=`sales`+(:num),`wait`=`wait`+(:num)
                           WHERE `sku` = :sku  AND `stock`-(:num) >= 0 LIMIT 1";
                $u_num = by::dbMaster()->createCommand($sql, [':num' => $num, ':sku' => $sku])->execute();

                if ($u_num == 0) {
                    return [false, '库存不够(3)'];
                }

                //更新销量缓存
                $this->OptSales($sku, 'INCR', $num, $positive);
                //更新未付款缓存
                $this->OptWait($sku, 'INCR', $num, $positive);

                break;

            case 'SET':
                $num  = max($num, 0);
                $wait = $this->OptWait($sku);

                //如果erp库存比未付款数还小，则更新库存为0
                if ($wait >= $num) {
                    $num     = 0;
                    $set_str = " :num";
                } else {
                    $set_str = " :num - `wait`";
                }

                $sql   = "UPDATE {$tb} SET `stock` = {$set_str} WHERE `sku` = :sku LIMIT 1";
                $u_num = by::dbMaster()->createCommand($sql, [':num' => $num, ':sku' => $sku])->execute();

                if ($u_num > 0) {
                    $this->optStock($sku, 'DEL');
                }

                break;

            case 'SALE':
                $sql = "UPDATE {$tb} SET `sales` = `sales` + (:num) WHERE `sku` = :sku  LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':sku' => $sku])->execute();
                //删除销量缓存
                $this->OptSales($sku, 'DEL');
                break;

            case 'WAIT':
                $wait = $this->OptWait($sku);

                if ($wait <= $num) {
                    $set_str = " :num";
                    $num     = 0;
                } else {
                    $set_str = " `wait` - :num";
                }

                $sql = "UPDATE {$tb} SET `wait` = {$set_str} WHERE `sku` = :sku LIMIT 1";
                by::dbMaster()->createCommand($sql, [':num' => $num, ':sku' => $sku])->execute();
                //删除未付款缓存
                $this->OptWait($sku, 'DEL');
                break;
        }

        return [true, $sku];
    }


    /**
     * @param $sku
     * @param string $opt
     * @param int $num
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品库存
     */
    public function OptStock($sku, string $opt = 'GET', int $num = 0)
    {
        $sku   = trim($sku);
        $r_key = $this->__getGoodsStockKey($sku);
        $redis = by::redis();

        switch ($opt) {
            case 'GET':
                $stock = $redis->get($r_key);
                if ($stock === false) {
                    $tb   = self::tbName();
                    $sql  = "SELECT `stock` FROM {$tb} WHERE `sku`=:sku LIMIT 1";
                    $aLog = by::dbMaster()->createCommand($sql, [':sku' => $sku])->queryOne();
                    $stock = $aLog['stock'] ?? 0;
                    $redis->set($r_key, $stock, ['NX', 'EX' => 600]);
                }
                return intval($stock);
                break;

            case 'INCR':
                !$redis->exists($r_key) && $this->OptStock($sku);
                $s_num = $redis->incrBy($r_key, $num);
                if ($s_num < 0) {
                    $num < -1 && $this->OptStock($sku, 'DEL');
                    return [false, '库存不足(6)'];
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param $sku
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品销量
     */
    public function OptSales($sku, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $sku   = trim($sku);
        $num   = abs($num);
        $num   = $positive ? $num : -$num;
        $r_key = $this->__getGoodsSalesKey($sku);
        $redis = by::redis();

        switch ($opt) {
            case 'GET':
                $sales = $redis->get($r_key);

                if ($sales === false) {
                    $tb   = self::tbName();
                    $sql  = "SELECT `sales` FROM {$tb} WHERE `sku`=:sku LIMIT 1";
                    $aLog = by::dbMaster()->createCommand($sql, [':sku' => $sku])->queryOne();
                    $sales = $aLog['sales'] ?? 0;
                    $redis->set($r_key, $sales, ['NX', 'EX' => 600]);
                }

                return intval($sales);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }

    /**
     * @param $sku
     * @param string $opt
     * @param int $num
     * @param bool $positive 是否正数
     * @return array|int
     * @throws \yii\db\Exception
     * 获取设置商品未付款数
     */
    public function OptWait($sku, string $opt = 'GET', int $num = 0, bool $positive = true)
    {
        $sku   = trim($sku);
        $num   = abs($num);
        $num   = $positive ? $num : -$num;
        $r_key = $this->__getGoodsWaitsKey($sku);
        $redis = by::redis();

        switch ($opt) {
            case 'GET':
                $wait = $redis->get($r_key);

                if ($wait === false) {
                    $tb   = self::tbName();
                    $sql  = "SELECT `wait` FROM {$tb} WHERE `sku`=:sku LIMIT 1";
                    $aLog = by::dbMaster()->createCommand($sql, [':sku' => $sku])->queryOne();

                    $wait = $aLog['wait'] ?? 0;
                    $redis->set($r_key, $wait, ['NX', 'EX' => 600]);
                }

                return intval($wait);
                break;

            case 'INCR':
                if ($redis->exists($r_key)) {
                    $redis->incrBy($r_key, $num);
                }
                return [true, 'ok'];
                break;

            case 'DEL':
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }



    /**
     * @param $skus
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取商品库存列表
     */
    public function getStockListBySkus($skus, bool $cache = true)
    {
        $r_key = $this->__getGoodsListStockKey($skus);
        $redis = by::redis();
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            if(!is_array($skus)) $skus = explode(',',$skus);
            $skus   = implode("','", $skus);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `sku` IN ('{$skus}')";
            $aData  = by::dbMaster()->createCommand($sql)->queryAll();
            $redis->set($r_key, json_encode($aData), ['ex' => empty($aData) ? 10 : 600]);
        }
        return $aData;
    }



    /**
     * @throws \yii\db\Exception
     */
    public function GetSkuByGidAndSid($gid, $sid = 0, $source = self::SOURCE['WARES'])
    {
        $gData = ($source == self::SOURCE['MAIN'])
            ? by::Gmain()->GetOneByGidSid($gid, $sid)
            : (by::GoodsPointsPriceModel()->GetOneById($gid, $sid));

        if(empty($gData)){
            $gData = byNew::GoodsTryBuyingPriceModel()->GetOneById($gid,$sid);
        }

        return $gData['spec']['sku'] ?? ($gData['sku'] ?? '');
    }


    /**
     * @param $gid
     * @param int $sid
     * @param int $num
     * @param int $source
     * @return array
     * @throws \yii\db\Exception
     */
    public function UpdatePreSaleStock($gid, int $sid=0, int $num=0, int $source = self::SOURCE['WARES']): array
    {
        $sku = $this->GetSkuByGidAndSid($gid,$sid,$source);
        $tb     = self::tbName();
        $sql    = "UPDATE {$tb} SET `sales`=`sales`+(:num),`wait`=`wait`+(:num) WHERE `sku` = :sku LIMIT 1";
        $u_num  = by::dbMaster()->createCommand($sql, [':num' => $num, ':sku' => $sku])->execute();
        if ($u_num == 0) {
            return [false, '新增预售订单失败'];
        }
        return [true,'OK'];
    }

    /**
     * 根据库存获取sku
     * @param int    $stock
     * @param string $op
     * @return array
     */
    public function getSkuByStock($stock, $op = "<=")
    {
        return self::find()->where(["=", "is_del", 0])
            ->andWhere([$op, "stock", $stock])
            ->select(["sku", "stock"])
            ->asArray()
            ->all();
    }
}
