<?php

namespace app\modules\wares\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\ActiveQuery;

class GiftCardGoodsModel extends CommModel
{

    public $tb_fields = [
        'id', 'sku', 'name', 'web_name', 'cover_image', 'images', 'card_resource', 'price', 'stock', 'type', 'start_time', 'end_time', 'status', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_gift_card_goods`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 是否删除
    const IS_DEL = [
        'no'  => 0,
        'yes' => 1
    ];

    // 类型名称
    const TYPE_NAME = [
        1 => '实体卡',
        2 => '虚拟卡'
    ];

    // 状态名称
    const STATUS_NAME = [
        1 => '上架',
        2 => '下架'
    ];

    /**
     * 通过ID 获取卡商品
     * @param int $id
     * @return array
     */
    public function getGoodsById(int $id)
    {
        return self::find()
            ->where([  // 过滤删除的数据
               'id'     => $id,
               'is_del' => self::IS_DEL['no']
            ])
            ->asArray()
            ->one();
    }

    /**
     * 通过名字 获取卡商品
     * @param string $name
     * @return array|null
     */
    public function getGoodsByName(string $name)
    {
        return self::find()
            ->where([  // 过滤删除的数据
               'name'   => $name,
               'is_del' => self::IS_DEL['no']
            ])
            ->asArray()
            ->one();
    }

    /**
     * 通过外显名字 获取卡商品
     * @param string $webName
     * @return array|null
     */
    public function getGoodsByWebName(string $webName)
    {
        return self::find()
            ->where([  // 过滤删除的数据
               'web_name' => $webName,
               'is_del' => self::IS_DEL['no']
            ])
            ->asArray()
            ->one();
    }

    /**
     * 通过sku 获取卡商品
     * @param string $sku
     * @return array|null
     */
    public function getGoodsBySku(string $sku)
    {
        return self::find()
            ->where([  // 过滤删除的数据
               'sku'   => $sku,
               'is_del' => self::IS_DEL['no']
            ])
            ->asArray()
            ->one();
    }

    /**
     * 添加、更新数据
     * @param int $id
     * @param array $data
     * @return void
     * @throws \yii\db\Exception
     */
    public function store(int $id, array $data)
    {
        if ($id) {
            by::dbMaster()->createCommand()->update(self::tableName(), $data, ['id' => $id])->execute();
        } else {
            by::dbMaster()->createCommand()->insert(self::tableName(), $data)->execute();
        }
    }

    /**
     * 获取卡商品列表
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function getGoodsList(array $params, int $page = 1, int $pageSize = 20): array
    {
        // 分页信息
        list($offset, $limit) = CUtil::pagination($page, $pageSize);

        // 查询构造器
        $activeQuery = $this->getActiveQuery($params);
        // 数据
        $list = $activeQuery->limit($limit)->offset($offset)
            ->orderBy('`id` DESC')
            ->asArray()
            ->all();
        // 总数
        $total = $activeQuery->count();
        return ['list' => $list, 'total' => $total];
    }
    /**
     * 获取卡商品列表
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    public function getGoodsListByIds(array $ids): array
    {
        $activeQuery = self::find()
            ->andWhere(['is_del' => self::IS_DEL['no']]); // 过滤删除的数据
        // 查询构造器
        
        $activeQuery->andWhere(['in', 'id', $ids]);
        
        // 数据
        $list = $activeQuery
            ->orderBy('`id` DESC')
            ->asArray()
            ->all();
        
        return $list;
    }

    /**
     * 创建查询构造器
     * @param array $params
     * @return ActiveQuery
     */
    private function getActiveQuery(array $params): ActiveQuery
    {
        $activeQuery = self::find()
            ->andWhere(['is_del' => self::IS_DEL['no']]); // 过滤删除的数据

        if (!empty($params['name'])) {
            // 将name字段改为模糊查询
            $activeQuery->andWhere(['like', 'name', $params['name']]);
        }
        if (isset($params['ids']) && count($params['ids']) > 0) {
            $activeQuery->andWhere(['in', 'id', $params['ids']]);
        }

        if (!empty($params['sku'])) {
            $activeQuery->andWhere(['sku' => $params['sku']]);
        }

        if (!empty($params['type'])) {
            $activeQuery->andWhere(['type' => $params['type']]);
        }

        if (!empty($params['status'])) {
            $activeQuery->andWhere(['status' => $params['status']]);
        }

        return $activeQuery;
    }

    /**
     * 导出
     * @param $name
     * @param $sku
     * @param $type
     * @param $status
     * @param $viewSensitive
     * @return array
     */
    public function exportData($name = '', $sku = '', $status = '', $type = '', $viewSensitive = false): array
    {
        $head = ['商品ID', '商品编号', '商品名称', '商品外显名称', '状态', '类型', '售卖价格', '库存', '激活量'];

        $params = [
            'name'   => $name,
            'sku'    => $sku,
            'type'   => $type,
            'status' => $status,
        ];

        $activeQuery = $this->getActiveQuery($params);
        $list = $activeQuery->asArray()->all();
        $data = [];
        $data[] = $head;

        // 激活量
        $cardGoodsIds = array_column($list, 'id');
        $actNums = byNew::GiftCardResources()->getActNumByCardGoodsIds($cardGoodsIds);

        foreach ($list as $value) {
            $data[] = [
                'id'       => $value['id'],
                'sku'      => $value['sku'],
                'name'     => $value['name'],
                'web_name' => $value['web_name'],
                'status'   => $value['status'] == 1 ? '上架' : '下架',
                'type'     => $value['type'] == 1 ? '实体卡' : '虚拟卡',
                'price'    => bcdiv($value['price'], 100, 2),
                'stock'    => $value['stock'],
                'act_num'  => $actNums[$value['id']] ?? 0, // 激活数量
            ];
        }
        // 结果
        return $data;
    }
}
