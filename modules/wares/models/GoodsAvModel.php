<?php

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GoodsAvModel extends CommModel
{

    public $tb_fields = [
        'id', 'ak_id', 'at_val', 'image'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_av`";
    }

    /**
     * @param $ak_id
     * @param $at_val
     * @return string
     * 通过attrid 获取规格
     */
    private function __getGoodsAvKey($ak_id, $at_val): string
    {
        return AppWRedisKeys::getGoodsAvByAtkId($ak_id, $at_val);
    }

    /**
     * @param $id
     * @return string
     * 通过id获取数据
     */
    private function __getGoodsAvByIdKey($id): string
    {
        return AppWRedisKeys::getGoodsAvById($id);
    }

    /**
     * @param $ak_id
     * @return mixed
     * 属性值列表
     */
    private function __getGoodsAvListKey($ak_id)
    {
        return AppWRedisKeys::getGoodsAvList($ak_id);
    }

    /**
     * @param $id
     * @return void
     * 缓存清理
     */
    private function __delIdCache($id)
    {
        $r_key = $this->__getGoodsAvByIdKey($id);
        by::redis('core')->del($r_key);
    }

    /**
     * @param $ak_id
     * @param $at_val
     * @return void
     * 缓存清理
     */
    private function __delCache($ak_id, $at_val)
    {
        $r_key = $this->__getGoodsAvKey($ak_id, $at_val);
        by::redis('core')->del($r_key);
    }

    /**
     * @param $ak_id
     * @return void
     * 列表缓存清理
     */
    private function __delListCache($ak_id)
    {
        $r_key = $this->__getGoodsAvListKey($ak_id);
        by::redis('core')->del($r_key);
    }

    /**
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 增加规格增改
     */
    public function SaveLog(array $aData): array
    {
        $ak_id  = CUtil::uint($aData['ak_id'] ?? ""); //规格id
        $at_val = trim($aData['at_val'] ?? "");  //属性值
        $image  = trim($aData['image'] ?? "");  //属性值
        if (empty($ak_id) || empty($at_val)) {
            return [false, "参数缺失"];
        }

        $db = by::dbMaster();
        $tb = self::tbName();

        $aInfo = $this->GetOneByAkVal($ak_id, $at_val, false);
        if ($aInfo) {
            return [true, $aInfo['id']]; //存在，直接返回规格属性值id
        }

        $save = [
            'ak_id'  => $ak_id,
            'at_val' => $at_val,
            'image'  => $image,
        ];

        $ret = $db->createCommand()->insert($tb, $save)->execute();

        if (!$ret) {
            return [false, '规格属性值添加失败'];
        }

        $av_id = $db->getLastInsertID(); //新增成功的规格属性值id

        $this->__delIdCache($av_id);
        $this->__delCache($ak_id, $at_val);
        $this->__delListCache($ak_id);

        return [true, $av_id];
    }


    /**
     * @param int $ak_id
     * @param string $at_val
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 查询单条
     */
    public function GetOneByAkVal(int $ak_id = 0, string $at_val = '', bool $cache = true): array
    {
        $ak_id = CUtil::uint($ak_id);

        $redis = by::redis('core');
        $r_key = $this->__getGoodsAvKey($ak_id, $at_val);

        $id = $cache ? $redis->get($r_key) : false;

        if ($id === false) {
            $tb = $this::tbName();

            list($where, $params) = $this->__getCondition($ak_id, $at_val);

            $sql   = "SELECT `id` FROM  {$tb} WHERE {$where}  LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $id    = $aData['id'] ?? 0;
            $cache && $redis->set($r_key, $id, 1800);
        }

        if (!$id) {
            return [];
        }

        return $this->GetOneById($id);
    }

    /**
     * @param int $ak_id
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 根据ak_id查询列表
     */
    public function GetListByAkId(int $ak_id = 0, $cache = true): array
    {
        $ak_id = CUtil::uint($ak_id);
        $redis = by::redis('core');
        $r_key = $this->__getGoodsAvListKey($ak_id);


        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(',', $this->tb_fields);

            $sql   = "SELECT {$fields} FROM  {$tb} WHERE `ak_id` = :ak_id AND `is_del` = 0";
            $aData = by::dbMaster()->createCommand($sql, [':ak_id' => $ak_id])->queryAll();
            $cache && $redis->set($r_key, json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $ak_id
     * @param string $at_val
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $ak_id = 0, string $at_val = ''): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        if ($ak_id) {
            $where            .= " AND `ak_id` =:ak_id";
            $params[":ak_id"] = $ak_id;
        }

        if ($at_val) {
            $where             .= " AND `at_val` = :at_val";
            $params[":at_val"] = $at_val;

            $where             .= " AND `is_del`=:is_del";
            $params[':is_del'] = 0;
        }

        return [$where, $params];
    }

    /**
     * @param int $ak_id
     * @param string $at_val
     * @return array
     * @throws \yii\db\Exception
     * 编辑规格属性值
     */
    public function UpdateLog(int $ak_id = 0, string $at_val = ''): array
    {
        $ak_id = CUtil::uint($ak_id);

        $aLog = $this->GetOneByAkVal($ak_id, $at_val);

        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->update($tb, ['is_del' => 1, 'dtime' => intval(START_TIME)], ['id' => $aLog['id']])->execute();

        if (!$ret) {
            return [false, '未知原因，规格属性值修改失败'];
        }

        $this->__delIdCache($aLog['id']);
        $this->__delCache($ak_id, $at_val);
        $this->__delListCache($ak_id);
        return [true, 'ok'];
    }

    /**
     * @param $id
     * @return array|false
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id, $cache = true)
    {
        $id = CUtil::uint($id);

        $redis = by::redis('core');
        $r_key = $this->__getGoodsAvByIdKey($id);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $cache && $redis->set($r_key, json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $av_id
     * @return array
     * @throws \yii\db\Exception
     * 根据id转换属性配置
     */
    public function IdToName(int $av_id): array
    {
        $av_id  = CUtil::uint($av_id);
        $avData = $this->GetOneById($av_id);
        if (empty($avData)) return [];

        $akData = by::GoodsAtkModel()->GetOneById($avData['ak_id']);

        return [
            'at_name' => $akData['at_name'],
            'at_val'  => $avData['at_val'],
        ];
    }


    /**
     * 获取商品属性值（无缓存）
     * @param array $ids
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public function getListByIds(array $ids)
    {
        if (empty($ids)) {
            return [];
        }
        $tb     = $this::tbName();
        $fields = implode("`,`", $this->tb_fields);
        $ids    = implode(',', $ids);
        $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id` IN ({$ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }


}
