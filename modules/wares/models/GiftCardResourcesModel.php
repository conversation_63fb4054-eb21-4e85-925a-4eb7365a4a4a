<?php

namespace app\modules\wares\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\main\models\CommModel;
use yii\db\ActiveQuery;

class GiftCardResourcesModel extends CommModel
{

    public $tb_fields = [
        'id', 'card_goods_id', 'card_no', 'card_password', 'status', 'act_user_id', 'act_time', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_gift_card_goods_resource`";
    }

    const STATUS = [
        'INACTIVE'  => 1, //未激活
        'ACTIVATED' => 2, //已激活
        'INVALID'   => 3, //已作废
    ];
    /**
     * 根据卡号，获取数据
     * @param array $cardNos
     * @return array
     */
    public function getResourcesByCardNos(array $cardNos): array
    {
        return self::find()
            ->where([
                'IN', 'card_no', $cardNos
            ])
            ->asArray()
            ->all();
    }

    /**
     * 根据卡密，获取数据
     * @param array $passwords
     * @return array
     */
    public function getResourcesByPasswords(array $passwords): array
    {
        return self::find()
            ->where([
                'IN', 'card_password', $passwords
            ])
            ->asArray()
            ->all();
    }

    /**
     * 根据卡密，获取数据
     * @param string $cardCardPassword
     * @return null | array
     */
    public function getResourceByCardPassword(string $cardCardPassword)
    {
        return self::find()
            ->where(['card_password' => $cardCardPassword])
            ->asArray()
            ->one();
    }


    /**
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array
     * 分页获取卡资源列表
     */
    public function getCardResourcesList(array $params, int $page = 1, int $pageSize = 20): array
    {
        // 分页信息
        list($offset, $limit) = CUtil::pagination($page, $pageSize);

        // 查询构造器
        $activeQuery = $this->getActiveQuery($params);
        // 数据
        $list = $activeQuery->limit($limit)->offset($offset)
            ->orderBy('`id` DESC')
            ->asArray()
            ->all();
        // 总数
        $total = $activeQuery->count();
        return ['list' => $list, 'total' => $total];
    }


    private function getActiveQuery(array $params): ActiveQuery
    {
        $activeQuery = self::find();

        //激活时间
        if (!empty($params['begin_time']) && !empty($params['end_time']))
        {
            $activeQuery->andWhere(['between', 'act_time', $params['begin_time'], $params['end_time']]);
        }
        //卡号
        if (!empty($params['card_no'])) {
            $activeQuery->andWhere(['card_no' => $params['card_no']]);
        }
        //商品ID，可能存在card_goods_id=0
        if (strlen($params['card_goods_id']) > 0) {
            $activeQuery->andWhere(['card_goods_id' => $params['card_goods_id']]);
        }
        //激活人ID
        if (!empty($params['act_user_id'])) {
            $activeQuery->andWhere(['act_user_id' => $params['act_user_id']]);
        }
        //状态
        if (!empty($params['status']) && $params['status'] > -1) {
            $activeQuery->andWhere(['status' => $params['status']]);
        }
        //卡商品类型
        if (!empty($params['type'])){
            $tb = byNew::GiftCardGoods()::tbName();
            $sql = "SELECT `id` FROM {$tb} WHERE `type` =:type";
            $card_goods_id = by::dbMaster()->createCommand($sql,[":type"=>$params['type']])->queryColumn();
            $activeQuery->andWhere(['IN','card_goods_id',$card_goods_id]);
        }

        return $activeQuery;
    }

    /**
     * @param $id
     * @param $status
     * @return array
     * 作废卡资源
     */
    public function discardResource($id, $status): array
    {
        // 这是一个try-catch块，它尝试执行一些可能会失败的操作，如果出现错误，会被catch块捕获并处理。
        try {
            // 通过自类的静态方法，在数据库中找到一个记录，记录的id是$id。
            $record = self::findOne(['id' => $id]);

            // 判断找到的记录是否存在。
            if (!$record) {
                // 如果记录不存在，返回一个错误信息，并停止执行后续代码。
                return [false, '数据不存在'];
            }

            // 创建一个数据库命令，并使用该命令更新找到的记录的状态。更新的状态是$status，更新的条件是id等于$id。
            by::dbMaster()->createCommand()->update(self::tableName(), ['status' => $status], ['id' => $id])->execute();

            // 如果成功更新了记录，返回一个成功的消息。
            return [true, '作废成功'];
        } catch (\Exception $e) {
            // 如果在执行过程中出现异常，捕获该异常，并获取异常的信息和堆栈信息。
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            // 将错误信息写入到debug日志中。
            CUtil::debug($error, 'err.discard_resource');
            // 返回一个失败的消息。
            return [false, '作废失败'];
        }

    }


    //导出卡资源列表
    public function exportData($begin_time = '', $end_time = '', $card_no = '', $card_goods_id = '', $act_user_id = '', $status = '', $viewSensitive = false): array
    {
        $head = ['卡资源ID', '卡商品ID', '卡号', '卡密', '状态', '激活用户ID', '激活时间'];

        $params = [
            'begin_time'    => $begin_time,
            'end_time'      => $end_time,
            'card_no'       => $card_no,
            'card_goods_id' => $card_goods_id,
            'act_user_id'   => $act_user_id,
            'status'        => $status,
        ];

        $activeQuery = $this->getActiveQuery($params);
        $list        = $activeQuery->asArray()->all();
        $data        = [];
        $data[]      = $head;
        foreach ($list as $value) {
            $data[] = [
                'id'            => $value['id'],
                'card_goods_id' => $value['card_goods_id'],
                'card_no'       => $value['card_no'],
                'card_password' => $value['card_password'], // 这里将加密后的密码返回
                'status'        => ($value['status'] == 1 ? '未激活' : $value['status']) == 2 ? '已激活' : '作废',
                'act_user_id'   => $value['act_user_id'],
                'act_time'      => empty($value['act_time']) ? '' : date('Y-m-d H:i:s', $value['act_time']),
            ];
        }
        //卡密加密
        return Response::responseList($data, ['card_password' => 'tm']);
    }

    /**
     * 获取卡片的激活量
     * 后端使用 无缓存
     * @param array $cardGoodsIds
     * @return array
     * @throws \yii\db\Exception
     */
    public function getActNumByCardGoodsIds(array $cardGoodsIds): array
    {
        if (empty($cardGoodsIds)) {
            return [];
        }
        $cardGoodsIds = implode(',', $cardGoodsIds);

        $sql = "SELECT `card_goods_id`, COUNT(`id`) AS `num` FROM " . self::tableName() . " WHERE `card_goods_id` IN (" . $cardGoodsIds . ") AND `status` = " . self::STATUS['ACTIVATED'] . " GROUP BY `card_goods_id`";
        $list = by::dbMaster()->createCommand($sql)->queryAll();
        $data = [];
        foreach ($list as $value) {
            $data[$value['card_goods_id']] = $value['num'];
        }
        return $data;
    }

    /**
     * 获取一条没有使用的卡资源
     */
    public function getOneUnusedCardResource($cardGoodsId,$outIds = []){
        $query = self::find()->where(['card_goods_id' => $cardGoodsId, 'status' => 1]);
        if (count($outIds) >0){
            $query->andWhere(['not in','id',$outIds]);
        }
        return $query->asArray()->one();
    }

}
