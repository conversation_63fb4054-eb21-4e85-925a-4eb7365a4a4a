<?php

namespace app\modules\wares\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class ActivityModel extends CommModel
{

    public $tb_fields = [
        'id', 'name', 'grant_type', 'start_time', 'end_time', 'status', 'is_del', 'create_time', 'update_time', 'delete_time'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_activity`";
    }

    const GRANT_TYPE = [
        'tryBeforeBuy' => 1,  //先试后买
    ];

    const STATUS = [
        'UP'   => 0,//上架
        'DOWN' => 1,//下架
    ];


    private function getListCacheKey(): string
    {
        return AppCRedisKeys::getWActivityListCacheKey();
    }

    public function delWAListCacheKey()
    {
        $redisKey = $this->getListCacheKey();
        by::redis()->del($redisKey);
    }

    private function getWActivityInfoById($id): string
    {
        return AppCRedisKeys::getWActivityInfoById($id);
    }

    public function delWActivityInfoById($id)
    {
        $redisKey = $this->getWActivityInfoById($id);
        by::redis()->del($redisKey);
    }


    /**
     * @param $id
     * @param $data
     * @return mixed|string
     * @throws Exception
     */
    public function saveLog($id, $data)
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        if ($id) {
            //update
            $result = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
        } else {
            //insert
            $result = $db->createCommand()->insert($tb, $data)->execute();
            $id     = $db->getLastInsertID();
        }

        // 清理缓存
        $this->delWActivityInfoById($id);
        $this->delWAListCacheKey();
        return $id;
    }

    /**
     * @param $id
     * @param array $update
     * @return array
     * @throws Exception
     * 修改活动配置（装态、删除）
     */
    public function updateData($id, array $update): array
    {
        $id = CUtil::uint($id);

        $record = self::findOne($id);
        if (!$record) {
            return [false, '活动不存在'];
        }

        //允许修改的字段
        if (empty($id) || empty($update)) {
            return [false, '参数缺失'];
        }

        $allowed = ['status', 'update_time', 'is_delete', 'delete_time'];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb = self::tableName();

        by::dbMaster()->createCommand()->update($tb,
            $update,
            ['id' => $id]
        )->execute();

        // 清理缓存
        $this->delWActivityInfoById($id);
        $this->delWAListCacheKey();

        return [true, 'ok'];
    }


    /**
     * @param array $input
     * @param $page
     * @param int $pageSize
     * @param bool $isCache
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getList(array $input, $page, int $pageSize, bool $isCache = false): array
    {
        $redis = by::redis();
        $cacheKey = $this->getListCacheKey();
        $subKey   = CUtil::getAllParams(__FUNCTION__, $input, $page, $pageSize);

        if ($isCache && $cache = $redis->hGet($cacheKey, $subKey)) {
            return json_decode($cache, true);
        }

        $limit = CUtil::pagination($page, $pageSize);
        $query = $this->__getCondition($input);

        $list = $query->orderBy('id DESC')
            ->offset($limit[0])
            ->limit($limit[1])
            ->asArray();

        //是否需要数量
        $need_count = $input['need_count'] ?? true;
        if ($need_count) {
            $total = (clone $query)->count();
            $pages = CUtil::getPaginationPages($total, $pageSize);
        }

        //是否需要详情
        $need_detail = $input['need_detail'] ?? true;
        if ($need_detail) {
            $list = $list->all();
            foreach ($list as &$value) {
                $detail = $this->getActivityDetail($value['id'], $value);
                $value  = $detail;
            }
            unset($value);
        } else {
            $list = $list->all();
        }

        $result = ['total' => intval($total ?? 0), 'pages' => $pages ?? 1, 'list' => array_values(array_filter($list))];

        if ($isCache) {
            $redis->hSet($cacheKey, $subKey, json_encode($result));
            CUtil::ResetExpire($cacheKey);
        }

        return $result;
    }

    private function __getCondition($input): \yii\db\ActiveQuery
    {
        $query = self::find();
        $query->where(['is_delete' => 0]);

        if (!empty($input['id'])) {
            $query->andWhere(['id' => $input['id']]);
        }

        if (!empty($input['name'])) {
            $query->andWhere(['like', 'name', $input['name']]);
        }

        if (isset($input['status']) && $input['status'] > -1) {
            $query->andWhere(['status' => $input['status']]);
        }

        if (!empty($input['start_time'])) {
            $query->andWhere(['>=', 'start_time', $input['start_time']]);
        }

        if (!empty($input['end_time'])) {
            $query->andWhere(['<=', 'end_time', $input['end_time']]);
        }

        if (!empty($input['goods_name'])) {
            $goodsIds = by::GoodsMainModel()::find()
                ->where(['like', 'name', $input['goods_name']])
                ->andWhere(['source' => 3])
                ->select('id')
                ->column();
            $acId     = byNew::ActivityTypeModel()::find()
                ->where(['in', 'try_goods_ids', $goodsIds])
                ->select('ac_id')
                ->column();

            $query->andWhere(['in', 'id', $acId]);
        }

        return $query;
    }


    public function getOneById($id, $isCache = true)
    {
        if (empty($id)) {
            return [];
        }

        $redis    = by::redis();
        $redisKey = $this->getWActivityInfoById($id);

        if ($isCache) {
            $aDataJson = $redis->get($redisKey);
            if (!empty($aDataJson)) {
                return json_decode($aDataJson, true);
            }
        }

        $aData = self::findOne(['id' => $id]);
        if (empty($aData)) {
            return [];
        }

        $aData = $aData->toArray();
        if ($isCache) {
            $redis->set($redisKey, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getActivityDetail($acId, $activityMain = [], $isCache = false): array
    {
        if (empty($acId)) {
            return []; // 如果活动不存在，返回 null 或抛出异常，根据实际情况决定
        }

        if (empty($activityMain)) {
            $activityMain = $this->getOneById($acId, $isCache);
        }

        if (empty($activityMain)) {
            return [];
        }

        // 获取活动详情
        $detail = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($acId, $isCache);

        // 获取商品信息
        $goods       = by::GoodsMainModel()->GetOneByGid($detail['try_goods_ids']);
        $goodsDetail = byNew::GoodsTryBuyingModel()->GetOneById($detail['try_goods_ids'], [],true, $isCache,false);
        $goodsPrice  = byNew::GoodsTryBuyingPriceModel()->GetOneById($detail['try_goods_ids'], 0, true, $isCache,false);

        // 检查商品信息是否完整
        if (empty($goods) || empty($goodsDetail) || empty($goodsPrice)) {
            return [];
        }

        // 构建返回结果
        return [
            'id'              => $activityMain['id'],
            'name'            => $activityMain['name'],
            'grant_type'      => $activityMain['grant_type'],
            'status'          => $activityMain['status'],
            'start_time'      => $activityMain['start_time'],
            'end_time'        => $activityMain['end_time'],
            'poster_image'    => $detail['poster_image'],
            'share_image'     => $detail['share_image'],
            'try_quota'       => $detail['try_quota'],
            'apply_number'    => $detail['apply_number'],
            'validity'        => $detail['validity'],
            'return_period'   => $detail['return_period'],
            'delivery_period' => $detail['delivery_period'],
            'survey_key'      => $detail['survey_key'],
            'pass_mark'       => $detail['pass_mark'],
            'join_condition'  => $detail['join_condition'],
            'ac_ids'          => $detail['ac_ids'],
            'rule'            => $detail['rule'],
            'is_audit'        => $detail['is_audit'],
            'goods'           => [
                'goods_name'  => $goods['name'],
                'goods_id'    => $goods['id'],
                'sid'         => 0,
                'sku'         => $goods['sku'],
                'mprice'      => $goodsDetail['mprice'],
                'try_price'   => $goodsDetail['try_price'],
                'detail'      => $goodsDetail['detail'],
                'label'       => $goodsDetail['label'],
                'price'       => $goodsPrice['price'],
                'cover_image' => $goodsDetail['cover_image'],
                'images'      => $goodsDetail['images'],
                'status'      => $goods['status'],
                'is_del'      => $goods['is_del']
            ],
        ];
    }


}
