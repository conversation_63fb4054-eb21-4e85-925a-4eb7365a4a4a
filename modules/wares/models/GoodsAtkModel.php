<?php

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GoodsAtkModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'at_name'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_atk`";
    }

    /**
     * @param $id
     * @return string
     * 通过id获取数据
     */
    private function __getOneAtkByIdKey($id): string
    {
        return AppWRedisKeys::getGoodsOneAtkById($id);
    }

    /**
     * @param $gid
     * @param $at_name
     * @return string
     * 属性名缓存
     */
    private function __getOneAtkByNameKey($gid, $at_name): string
    {
        return AppWRedisKeys::getGoodsOneAtkByName($gid, $at_name);
    }

    /**
     * @param $gid
     * @return string
     * 属性列表缓存
     */
    private function __getGoodsAtkListByGidKey($gid): string
    {
        return AppWRedisKeys::getGoodsAtkListByGid($gid);
    }

    /**
     * @param $id
     * @return void
     * 缓存清理
     */
    private function __delIdCache($id)
    {
        $r_key = $this->__getOneAtkByIdKey($id);
        by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @param $at_name
     * @return void
     * 缓存清理
     */
    private function __delNameCache($gid, $at_name)
    {
        $r_key = $this->__getOneAtkByNameKey($gid, $at_name);
        by::redis('core')->del($r_key);
    }

    /**
     * @param $gid
     * @return void
     * 列表缓存清理
     */
    private function __delListCache($gid)
    {
        $r_key = $this->__getGoodsAtkListByGidKey($gid);
        by::redis('core')->del($r_key);
    }

    /**
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 增加规格增改
     */
    public function SaveLog(array $aData): array
    {
        $gid     = CUtil::uint($aData['gid'] ?? 0);
        $at_name = trim($aData['at_name'] ?? '');

        if (empty($gid) || empty($at_name)) {
            return [false, "参数缺失"];
        }

        $aInfo = $this->GetOneByName($gid, $at_name, false);
        if ($aInfo) {
            return [true, $aInfo['id']]; //存在，直接返回规格id
        }

        $save = [
            'gid'     => $gid,
            'at_name' => $at_name,
        ];

        $db  = by::dbMaster();
        $tb  = self::tbName();
        $ret = $db->createCommand()->insert($tb, $save)->execute();
        if (!$ret) {
            return [false, "规格新增失败"];
        }

        $ak_id = $db->getLastInsertID();
        $this->__delIdCache($ak_id);
        $this->__delNameCache($gid, $at_name);
        $this->__delListCache($gid);

        return [true, $ak_id];
    }

    /**
     * @param int $gid
     * @param string $at_name
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 通过name 查询规格
     */
    public function GetOneByName(int $gid, string $at_name, bool $cache = true): array
    {
        $gid = CUtil::uint($gid);

        if (empty($gid) || empty($at_name)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getOneAtkByNameKey($gid, $at_name);

        $id = $cache ? $redis->get($r_key) : false;

        if ($id === false) {
            $tb    = $this::tbName();
            $sql   = "SELECT `id` FROM  {$tb} WHERE `gid`=:gid AND `at_name`=:at_name AND `is_del`= 0 LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [':gid' => $gid, ':at_name' => $at_name])->queryOne();
            $id    = $aData['id'] ?? 0;

            $cache && $redis->set($r_key, $id, 1800);
        }

        if (!$id) {
            return [];
        }

        return $this->GetOneById($id);
    }

    /**
     * @param string $attrCnf
     * @return array
     * 规格名-规格值 [{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]
     * 商品规格校验
     */
    public function checkAttr(string $attrCnf): array
    {
        $attrCnfArr = json_decode($attrCnf, true);
        if (!is_array($attrCnfArr)) {
            return [false, "规格参数有误(1)"];
        }

        foreach ($attrCnfArr as $k => $v) {
            if (!isset($v['at_name'])) {
                return [false, "规格参数有误(2)"];
            }
            if (!isset($v['at_val']) || !is_array($v['at_val'])) {
                return [false, "规格参数有误(3)"];
            }
            foreach ($v['at_val'] as $k1=>$v1){
                if (empty($v1['val'])) {
                    return [false, "规格参数有误(4)"];
                }
            }
        }
        return [true, $attrCnfArr];
    }

    /**
     * 确保 $attrData 中的 at_name 值与 $specsData 中的 at_val 值的键相匹配
     * @param array $attrData
     * @param array $specsData
     * @return array
     */
    public function checkAttrSpecs(array $attrData, array $specsData): array
    {
        // 将 $attrData 的 at_name 提取出来并存储在关联数组中
        $atNameArr = array_flip(array_column($attrData, 'at_name')); // ['颜色' => 0, '尺码' => 0]

        foreach ($specsData as $spec) {
            $atValKeys = [];
            foreach ($spec['at_val'] as $at_val) {
                list($key,) = explode(':', $at_val);
                $atValKeys[$key] = 0;
            }

            // 比较键是否相同
            if (array_diff_key($atNameArr, $atValKeys) || array_diff_key($atValKeys, $atNameArr)) {
                return [false, "规格名与规格值不匹配"];
            }
        }
        return [true, ''];
    }

    /**
     * @param int $gid
     * @param bool $cache
     * @return array
     * @throws \yii\db\Exception
     * 商品规格列表
     */
    public function GetListByGid(int $gid, bool $cache = true): array
    {
        if (empty($gid)) {
            return [];
        }
        $gid = CUtil::uint($gid);

        $redis = by::redis('core');
        $r_key = $this->__getGoodsAtkListByGidKey($gid);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `gid`=:gid AND `is_del`= 0 ORDER BY `id` asc";
            $aData  = by::dbMaster()->createCommand($sql, [':gid' => $gid])->queryAll();
            $cache && $redis->set($r_key, json_encode($aData), 1800);
        }

        return $aData;
    }

    /**
     * @param int $gid
     * @param string $at_name
     * @param array $update
     * @return array
     * @throws \yii\db\Exception
     * 编辑商品属性名 暂时只改is_del
     *
     */
    public function UpdateLog(int $gid, string $at_name, array $update): array
    {
        $gid = CUtil::uint($gid);

        if (empty($gid) || empty($at_name)) {
            return [false, '缺少参数'];
        }

        $aLog = $this->GetOneByName($gid, $at_name);
        if (empty($aLog)) {
            return [false, "规格-{$at_name}不存在！"];
        }

        //允许修改的字段
        $allowed = [
             'is_del', 'dtime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb = self::tbName();
        $ret = by::dbMaster()->createCommand()->update($tb, $update, ['id' => $aLog['id']])->execute();

        if (!$ret) {
            return [false, "未知原因，规格修改失败"];
        }

        $this->__delIdCache($aLog['id']);
        $this->__delNameCache($gid, $at_name);
        $this->__delListCache($gid);

        return [true, $aLog['id']];
    }


    /**
     * @param $id
     * @param $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id, $cache = true)
    {
        $id = CUtil::uint($id);

        $redis = by::redis('core');
        $r_key = $this->__getOneAtkByIdKey($id);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $cache && $redis->set($r_key, json_encode($aData), 1800);
        }

        return $aData;
    }

}
