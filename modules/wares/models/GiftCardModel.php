<?php

namespace app\modules\wares\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\ActiveQuery;
use yii\db\Exception;

class GiftCardModel extends CommModel
{

    public $tb_fields = [
        'id', 'name', 'web_name', 'image', 'amount', 'goods_ids', 'sku', 'expire_days', 'type', 'status', 'desc', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_gift_card`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    const TYPE = [
        'STORAGE_CASH'    => 1, //储值现金
        'ASSIGN_EXCHANGE' => 2, //指定兑换
    ];

    const STATUS = [
        'AWAIT_AUDIT'  => 1,//待审核
        'PASS_AUDIT'   => 2,//审核通过
        'REFUSE_AUDIT' => 3,//审核拒绝
    ];

    //卡详情 前台
    private function getCardInfoKey($id): string
    {
        return AppCRedisKeys::getCardInfoKey($id);
    }

    //删除卡详情缓存
    public function __delCardInfoKey($id)
    {
        $redis = by::redis();
        $redis->del($this->getCardInfoKey($id));
    }

    public function saveData($input, $id = ''): array
    {
        $db = by::dbMaster();
        $tb = self::tbName();
        if ($id) {
            $db->createCommand()->update($tb, $input, "`id`=:id", [":id" => $id])->execute();
        } else {
            $db->createCommand()->insert($tb, $input)->execute();
            $id = $db->getLastInsertID();
        }
        $this->__delCardInfoKey($id); //清除前台卡详情缓存
        return [true, $input];
    }


    /**
     * @param array $params
     * @param int $page
     * @param int $pageSize
     * @return array
     * 卡列表
     */
    public function getCardList(array $params, int $page = 1, int $pageSize = 20): array
    {
        // 分页信息
        list($offset, $limit) = CUtil::pagination($page, $pageSize);

        // 查询构造器
        $activeQuery = $this->getActiveQuery($params);
        // 数据
        $list = $activeQuery->limit($limit)->offset($offset)
            ->orderBy('`id` DESC')
            ->asArray()
            ->all();
        // 总数
        $total = $activeQuery->count();
        return ['list' => $list, 'total' => $total];
    }


    private function getActiveQuery(array $params): ActiveQuery
    {
        $activeQuery = self::find()
            ->andWhere(['is_del' => self::IS_DEL['no']]); // 过滤删除的数据

        //礼品卡名称
        if (!empty($params['name'])) {
            $activeQuery->andWhere(['like', 'name', $params['name']]);
        }
        //类型
        if (!empty($params['type']) && $params['type'] > -1) {
            $activeQuery->andWhere(['type' => $params['type']]);
        }
        //状态
        if (!empty($params['status']) && $params['status'] > -1) {
            $activeQuery->andWhere(['status' => $params['status']]);
        }

        return $activeQuery;
    }

    /**
     * 获取审核通过的卡列表
     * @param array $ids
     * @return array
     */
    public function getCardsByIds(array $ids): array
    {
        return self::find()
            ->where(['IN', 'id', $ids])
            ->asArray()
            ->all();
    }

    /**
     * 获取审核通过的卡列表
     * @param array $columns
     * @return array
     */
    public function getCards(array $columns): array
    {
        return self::find()
            ->select($columns)
            ->where(['status' => self::STATUS['PASS_AUDIT'], 'is_del' => self::IS_DEL['no']])
            ->orderBy('`id` DESC')
            ->asArray()
            ->all();
    }


    /**
     * @param $id
     * @param $status
     * @return array
     * @throws Exception
     * 礼品卡审核
     */
    public function audit($id, $status): array
    {
        $tb  = self::tbName();
        $row = by::dbMaster()->createCommand()->update($tb, ['status' => $status], "`id`=:id", [":id" => $id])->execute();
        if (!$row) {
            return [true, '审核失败'];
        }
        return [true, '审核成功'];
    }

    /**
     * @param $column
     * @param $value
     * @return array
     * 礼品卡详情
     */
    public function getGiftCardInfo($column, $value): array
    {
        if (empty($column) || empty($value)) {
            return [false, '参数能为空'];
        }

        $info = self::find()
            ->where([$column => $value])
            ->asArray()
            ->one();
        $info = empty($info) ? [] : $info;
        $info && $info['amount'] = bcdiv($info['amount'], 100);
        return [true, $info];
    }

    /**
     * 批量获取，有缓存
     * 避免循环单条查询，再单条缓存
     * @param array $ids
     * @return array
     * @throws RedisException
     */
    public function getDataListByIds(array $ids): array
    {
        $results = [];
        // 0、判空
        if (empty($ids)) {
            return [];
        }
        // 1、从缓存中获取商品信息
        $redis = by::redis('core');
        // gid的key集合
        $redisKeys = [];
        // 开始pipeline事务
        $pipeline = $redis->pipeline();
        // 添加多个查询命令到pipeline
        foreach ($ids as $id) {
            $redis_key = $this->getCardInfoKey($id);
            $redisKeys[$id] = $redis_key;
            // 查询数据
            $redis->get($redis_key);
        }
        // 执行pipeline
        $responses = $pipeline->exec();
        // 没有缓存数据的gid
        $noDataIds = [];
        foreach ($ids as $index => $id) {
            $value = $responses[$index];
            if ($value === false) {
                $noDataIds[] = $id;
            }
            $results[$id] = (array)json_decode($value, true);
        }

        // 2、不在缓存中的数据，批量查询数据库
        // 3、批量同步缓存
        $items = $this->getDataByIds($noDataIds);
        $pipeline = $redis->pipeline();
        foreach ($items as $id => $item) {
            // 更新到结果集
            $results[$id] = $item;
            // 更新到缓存
            $redis_key = $redisKeys[$id];
            $pipeline->set($redis_key, json_encode($item), ['EX' => empty($item) ? 10 : rand(3000, 3600)]);
        }
        // 执行pipeline
        $pipeline->exec();

        return $results;
    }


    /**
     * 根据ID获取数据
     * @param array $ids
     * @return array
     */
    private function getDataByIds(array $ids): array
    {
        if (empty($ids)) {
            return [];
        }
        // 查询数据
        $items = self::find()
            ->where([
                'IN', 'id', $ids
            ])
            ->andWhere([  // 过滤删除的数据
                          'is_del' => self::IS_DEL['no']
            ])
            ->asArray()
            ->all();
        return array_column($items, null, 'id');
    }

    /**
     * @param string $name
     * @param $type
     * @param $status
     * @param bool $viewSensitive
     * @return array
     */
    public function exportData(string $name = '', $type = '', $status = '', bool $viewSensitive = false): array
    {
        $head = ['卡ID', '名称', '外显名称', '图片', '金额（分）', '商品ID', '有效期：天', '类型', '审核状态', '规则描述'];

        $params      = [
            'name'   => $name,
            'type'   => $type,
            'status' => $status
        ];
        $activeQuery = $this->getActiveQuery($params);
        $list        = $activeQuery->asArray()->all();

        $data = []; // 初始化为空数组

        $data[] = $head;

        foreach ($list as $value) {
            $data[] = [
                'id'          => $value['id'] ?? '',
                'name'        => $value['name'] ?? '',
                'web_name'    => $value['web_name'] ?? '',
                'image'       => $value['image'] ?? '',
                'amount'      => $value['amount'] ?? '',
                'goods_ids'   => $value['goods_ids'] ?? '',
                'expire_days' => $value['expire_days'] ?? '',
                'type'        => ($value['type'] ?? '') == 1 ? '储值现金卡' : '指定兑换卡',
                'status'      => ($value['status'] ?? '') == 1 ? '待审核' : (($value['status'] ?? '') == 2 ? '审核通过' : '审核拒绝'),
                'desc'        => $value['desc'] ?? '',
            ];
        }
        return $data;
    }


    /**
     * @param $id
     * @return array
     * @throws RedisException
     * 卡详情 前台 （有缓存）
     */
    public function getCardInfo($id): array
    {
        if (empty($id)) {
            return [];
        }
        $redis    = by::redis();
        $redisKey = $this->getCardInfoKey($id);
        $aJson    = $redis->get($redisKey);
        $aData    = (array)json_decode($aJson, true);
        if (empty($aData)) {
            $aData = self::find()
                ->where(['id' => $id])
                ->asArray()
                ->one();
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redisKey, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }


}
