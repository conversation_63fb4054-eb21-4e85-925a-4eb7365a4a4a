<?php

namespace app\modules\wares\models\cocreate;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class CoCreateUserModel extends CommModel
{

    public $tb_fields = [
        'id', 'user_id', 'uid', 'phone', 'nick', 'is_del', 'status','ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_cocreate_user`";
    }

    private function __getInfoKey($id): string
    {
        return AppWRedisKeys::getCoCreateUserId($id);
    }

    private function __getInfoByUserId($user_id): string
    {
        return AppWRedisKeys::getCoCreateUserByUserId($user_id);
    }


    private function __getListKey(): string
    {
        return AppWRedisKeys::getCoCreateUserList();
    }
    /**
     * @param $id
     * @return void
     * 缓存清理
     */
    private function __delIdCache($id)
    {
        $r_key = $this->__getInfoKey($id);
        by::redis('core')->del($r_key);
    }

    private function __delIdByUserIdCache($user_id)
    {
        $r_key = $this->__getInfoByUserId($user_id);
        by::redis('core')->del($r_key);
    }

    private function __delListCache()
    {
        $r_key = $this->__getListKey();
        by::redis('core')->del($r_key);
    }


    public function GetOneById($id, $cache = true)
    {
        $id = CUtil::uint($id);
        $redis = by::redis('core');
        $r_key = $this->__getInfoKey($id);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $cache && $redis->set($r_key, json_encode($aData), empty($aData) ? 10 : 1800);
        }

        return $aData;
    }

    public function GetOneByUserId($user_id,$cache=true)
    {
        $user_id = CUtil::uint($user_id);
        $redis = by::redis('core');
        $r_key = $this->__getInfoByUserId($user_id);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `user_id`=:user_id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $cache && $redis->set($r_key, json_encode($aData), empty($aData) ? 10 : 1800);
        }

        return $aData;
    }


    public function GetListByInput($input, $page, $page_size = 50): array
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,serialize($input),$page,$page_size);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb                  = $this->tbName();
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($input);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC 
                    LIMIT {$offset},{$page_size}";

            $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,empty($aData) ? 10 : 1800);
        }

        return empty($aData) ? [] : array_column($aData,"id");
    }


    private function __getCondition($input): array
    {
        //SQL初始化条件
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;

        if(!empty($input['is_del']) && $input['is_del'] == -1){
            unset($params[':is_del']);
            $where             = "1=1";
        }

        if(!empty($input['nick'])){
            $where                .= " AND `nick` LIKE :nick";
            $params[":nick"]       = "%{$input['nick']}%";
        }

        if(!empty($input['uid'])){
            $where                .= " AND `uid` LIKE :uid";
            $params[":uid"]       = "%{$input['uid']}%";
        }

        if(!empty($input['phone'])){
            $where                .= " AND `phone` LIKE :phone";
            $params[":phone"]       = "%{$input['phone']}%";
        }

        if(!empty($input['phones']) && is_array($input['phones'])){
            $phoneString = implode("','",$input['phones']);
            $where              .= " AND `phone` in ('{$phoneString}')";
            $where                .= " AND `status` = 0";
        }

        return [$where,$params];
    }


    public function GetListCount($input)
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,serialize($input));
        $count   = by::redis('core')->hGet($r_key,$sub_key);
        if($count === false) {
            $tb                  = $this->tbName();
            list($where,$params) = $this->__getCondition($input);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
            $count               = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,empty($count) ? 10 : 1800);
        }

        return intval($count);
    }

    public function Del($ids)
    {
        $tb                  = $this->tbName();
        if(!is_array($ids)) $ids=explode(',',$ids);
        $time = time();
        $res = by::dbMaster()->createCommand()->update($tb,
            ['is_del'=>1,'dtime'=>$time],
            ['id' => $ids]
        )->execute();
        foreach ($ids as $id){
            $info = $this->GetOneById($id);
            if($info){
                $this->__delIdByUserIdCache($info['user_id']??0);
            }
            $this->__delIdCache($id);
        }
        $this->__delListCache();
        return $res;
    }

    public function UpdateInfo($user_id,$arr)
    {
        $user_id = CUtil::uint($user_id);
        $info = $this->GetOneByUserId($user_id);
        if(empty($info)) return true;
        $tb                  = $this->tbName();
        $arr['utime'] =  time();
        $res = by::dbMaster()->createCommand()->update($tb,
            $arr,
            ['user_id' => $user_id]
        )->execute();
        $this->__delIdCache($info['id']??0);
        $this->__delListCache();
        $this->__delIdByUserIdCache($user_id);
        return  $res;
    }

    public function UpdateInfoByPhone($user_id)
    {
        $user_id = CUtil::uint($user_id);
        //1.获取手机号码信息
        $phone = by::Phone()->GetPhoneByUid($user_id);
        $uid = by::Phone()->getUidByUserId($user_id);
        $info = by::users()->getOneByUid($user_id);

        $arr = [
            'phone' => $phone,
            'nick' => $info['nick'] ?? '',
            'user_id' => $user_id,
            'uid' => $uid,
            'utime' =>  time(),
        ];

        $tb                  = $this->tbName();
        $res = by::dbMaster()->createCommand()->update($tb,
            $arr,
            ['phone' => $phone,'user_id'=> 0,'uid' => 0,'is_del'=> 0]
        )->execute();

        if($res){
            $sql = "SELECT `id` FROM {$tb} WHERE `phone`=:phone  AND `is_del`=:is_del ORDER BY `id` DESC 
                    LIMIT 1";
            $aData               = by::dbMaster()->createCommand($sql,[':phone' => $phone,':is_del'=> 0])->queryOne();
            $id = $aData['id']??0;
            if($id){
                $this->__delIdCache($id);
            }
        }
        $this->__delIdByUserIdCache($user_id);
        $this->__delListCache();
        return  $res;
    }

    /**
     * @throws \yii\db\Exception
     */
    public function UpdateByMainUserType($user_id, $type, $query)
    {
        if($type == "bind_user"){
            return  $this->UpdateInfoByPhone($user_id);
        }

        //查询共创人员有没有
        $info = $this->GetOneByUserId($user_id);
        if(empty($info)) return true;

        $arr = [];
        switch ($type){
            case "change_nick":
              //获取用户Nick
              $userDetail =   by::users()->getOneByUid($user_id);
              $arr['nick'] = $userDetail['nick'] ?? '';
              break;
            case "change_phone":
              //获取用户电话号码
              $userPhone = by::Phone()->GetPhoneByUid($user_id);
              $arr['phone'] = $userPhone;
               break;
            case  "change_status":
                $arr['status'] = 1;
                break;
        }
        if($arr){
            $s = $this->UpdateInfo($user_id,$arr);
            //清除缓存
            $this->__delIdCache($info['id']??0);
            $this->__delListCache();
            $this->__delIdByUserIdCache($user_id);
            return  $s;
        }

        return true;
    }

    public function batchInsert($data)
    {
        $tb                  = $this->tbName();
        $s = by::dbMaster()->createCommand()->batchInsert($tb, ['user_id','phone','uid','nick','ctime','utime'],$data)->execute();
        $this->__delListCache();
        return $s;
    }

}
