<?php

namespace app\modules\wares\models\cocreate;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class CoCreateMaterialModel extends CommModel
{

    public $tb_fields = [
        'id', 'name', 'topic', 'url', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_cocreate_material`";
    }

    const TOPIC_LIST = [
        ['key' => 'car', 'value' => "汽车"],
        ['key' => 'clothing', 'value' => "服装"]
    ];

    private function __getInfoKey($id): string
    {
        return AppWRedisKeys::getCoCreateMaterialId($id);
    }


    private function __getListKey(): string
    {
        return AppWRedisKeys::getCoCreateMaterialList();
    }

    private function __getMainListByUserIdKey($user_id): string
    {
        return AppWRedisKeys::getCoCreateMaterialListByUserId($user_id);
    }

    private function __getNowListKey(): string
    {
        return AppWRedisKeys::getNowCoCreateMaterialList();
    }

    public function GetAllCoCreateMaterialListKey($topic = ''): string
    {
        return AppWRedisKeys::getAllCoCreateMaterialList($topic);
    }

    public function UserEnjoyPositionKey($user_id, $topic = ''): string
    {
        return AppWRedisKeys::userEnjoyPositionKey($user_id, $topic);
    }

    /**
     * @param $id
     * @return void
     * 缓存清理
     */
    private function __delIdCache($id)
    {
        $r_key = $this->__getInfoKey($id);
        by::redis('core')->del($r_key);
    }


    private function __delListCache()
    {
        $r_key = $this->__getListKey();
        by::redis('core')->del($r_key);
    }

    private function __delNowListCache()
    {
        $r_key = $this->__getNowListKey();
        by::redis('core')->del($r_key);
    }


    public function GetOneById($id, $cache = true)
    {
        $id    = CUtil::uint($id);
        $redis = by::redis('core');
        $r_key = $this->__getInfoKey($id);
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $cache && $redis->set($r_key, json_encode($aData), empty($aData) ? 10 : 1800);
        }

        return $aData;
    }

    public function GetOneByName($name,$topic)
    {
        $tb     = $this::tbName();
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `name`=:name and `topic` = :topic LIMIT 1";
        $aData  = by::dbMaster()->createCommand($sql, [':name' => $name,'topic'=> $topic])->queryOne();
        return empty($aData) ? [] : $aData;
    }


    public function GetListByInput($input, $page, $page_size = 50): array
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC 
                    LIMIT {$offset},{$page_size}";

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 1800);
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }


    private function __getCondition($input): array
    {
        //SQL初始化条件
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;


        if (!empty($input['name'])) {
            $where           .= " AND `name` LIKE :name";
            $params[":name"] = "%{$input['name']}%";
        }

        if (!empty($input['id'])) {
            $where         .= " AND `id` = :id";
            $params[":id"] = $input['id'];
        }

        if (!empty($input['topic'])) {
            $where            .= " AND `topic` LIKE :topic";
            $params[":topic"] = "%{$input['topic']}%";
        }

        if (!empty($input['check_name'])) {
            $where           .= " AND `name` = :name";
            $params[":name"] = $input['check_name'];
        }

        if (!empty($input['nid'])) {
            $where          .= " AND `id` != :nid";
            $params[":nid"] = $input['nid'];
        }


        return [$where, $params];
    }


    public function GetListCount($input)
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 10 : 1800);
        }

        return intval($count);
    }

    public function Del($ids)
    {
        $tb = $this->tbName();
        if (!is_array($ids)) $ids = explode(',', $ids);
        $time = time();
        $res  = by::dbMaster()->createCommand()->update($tb,
            ['is_del' => 1, 'dtime' => $time],
            ['id' => $ids]
        )->execute();
        foreach ($ids as $id) {
            $info = $this->GetOneById($id, false);
            if (!empty($info)) {
                $this->DelListRange($info);
            }
            $this->__delIdCache($id);
        }
        $this->__delListCache();
        $this->__delNowListCache();
        return $res;
    }

    public function ChangeInfoTopic($info, $topic)
    {
        $otopic = $info['topic'];
        $id     = $info['id'];
        if ($otopic != $topic) {
            $this->DelListRange($info);
        }
        $r_key = $this->GetAllCoCreateMaterialListKey($topic);
        $redis = by::redis('core');
        $value = [
            'id'       => $id,
            'url'      => $info['url'],
            'position' => $id,
            'topic'    => $topic,
        ];
        $redis->zAdd($r_key, $value['position'], json_encode($value));
    }

    public function UpdateInfo($id, $arr)
    {
        $id   = CUtil::uint($id);
        $info = $this->GetOneById($id);
        if (empty($info)) return true;
        $tb           = $this->tbName();
        $arr['utime'] = time();
        $res          = by::dbMaster()->createCommand()->update($tb,
            $arr,
            ['id' => $id]
        )->execute();
        $this->__delIdCache($id);
        $this->__delListCache();
        $this->__delNowListCache();
        $this->ChangeInfoTopic($info, $arr['topic'] ?? '');
        return $res;
    }

    public function SaveInfo($save)
    {
        $tb = $this->tbName();
        $db = by::dbMaster();
        $s  = $db->createCommand()->insert($tb, $save)->execute();
        $id = $db->getLastInsertID();
        $this->__delListCache();
        $r_key = $this->GetAllCoCreateMaterialListKey($save['topic'] ?? '');
        $redis = by::redis('core');
        $value = [
            'id'       => $id,
            'url'      => $save['url'],
            'position' => $id,
            'topic'    => $save['topic'] ?? '',
        ];
        $redis->zAdd($r_key, $value['position'], json_encode($value));
        return $s;
    }

    public function GetMainListByUserId($user_id, $pageSize = 500)
    {

        $r_key       = $this->__getMainListByUserIdKey($user_id);
        $positionKey = $this->UserEnjoyPositionKey($user_id);
        $redis       = by::redis('core');
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson, true);

        //获取图片数量

        $expire = intval($this->GetListCount([]));
        $expire = max($expire, 250);
        $expire = min($expire, 300);

        if ($aJson === false) {
            $tb          = $this->tbName();
            $statisticTb = byNew::CoCreateStatisticModel()::tbName();
            $sql         = "select a.id,a.url from {$tb} as a LEFT JOIN {$statisticTb} as b ON a.id = b.material_id  and b.user_id = :user_id where a.is_del = 0  and  b.id is null
                             ORDER BY RAND() limit {$pageSize}";
            $aData       = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryAll();
            $aData       = empty($aData) ? [] : $aData;
            $redis->set($r_key, json_encode($aData), empty($aData) ? 10 : $expire);
            $redis->set($positionKey, 0, empty($aData) ? 10 : $expire);
        }

        return $aData;
    }


    //实时获取当前用户判断的列表
    public function GetNowList($topic, $position, $page = 1, $page_size = 20)
    {
        $r_key   = $this->__getNowListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $topic, $position, $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            $sql   = "select `id`,`url`,`id` as `position`,`topic` from {$tb} where is_del = 0   and  id > :position and topic = :topic   
                         ORDER BY id ASC LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, [':position' => $position, ':topic' => $topic])->queryAll();
            $aData = empty($aData) ? [] : $aData;

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 500 : 1800);
        }

        return $aData;
    }

    public function GetNowListCount($position)
    {
        $tb    = $this->tbName();
        $sql   = "select COUNT(*) from {$tb} where is_del = 0  and  id > :position";
        $count = by::dbMaster()->createCommand($sql, [':position' => $position])->queryScalar();
        return empty($count) ? 0 : $count;
    }


    //任务防止topic数据丢失
    public function GetAllMaterialList($topic = "")
    {
        $r_key = $this->GetAllCoCreateMaterialListKey($topic);
        $redis = by::redis('core');
        $tb    = $this->tbName();
        $sql   = "select `id`,`url`,`id` as `position`,`topic` from {$tb} where is_del = 0 and topic = :topic ORDER BY id ASC";
        $aData = by::dbMaster()->createCommand($sql, [':topic' => $topic])->queryAll();
        if (!empty($aData) && !$redis->exists($r_key)) {
            foreach ($aData as $key => $value) {
                $redis->zAdd($r_key, $value['position'], json_encode($value));
            }
        }
        return true;
    }


    public function GetListByRange($topic, $position, $page_size = 20)
    {
        $r_key = $this->GetAllCoCreateMaterialListKey($topic);
        $redis = by::redis('core');
        $list  = $redis->zRangeByScore($r_key, $position + 1, 1000000, ['limit' => [0, $page_size]]);
        if ($list) {
            foreach ($list as $key => $value) {
                $list[$key] = json_decode($value, true);
            }
        }
        return $list;
    }

    public function DelListRange($info)
    {
        $topic = $info['topic'];
        $value = [
            'id'       => $info['id'] ?? 0,
            'url'      => $info['url'],
            'position' => $info['id'] ?? 0,
            'topic'    => $info['topic'] ?? '',
        ];
        $r_key = $this->GetAllCoCreateMaterialListKey($topic);
        $redis = by::redis('core');
        return $redis->zRem($r_key, json_encode($value));
    }

//数据量大小调试方案
//select a.id,a.url from t_cocreate_material as a LEFT JOIN t_cocreate_statistic as b ON a.id = b.material_id  and b.user_id = 5315 where a.is_del = 0  and  b.id is null
//ORDER BY RAND() limit 10
//
//
//
//SELECT a.id, a.url
//FROM (
//SELECT id
//FROM t_cocreate_material
//WHERE is_del = 0
//AND id >= (SELECT FLOOR(MIN(id) + (MAX(id) - MIN(id)) * RAND()) FROM t_cocreate_material)
//LIMIT 10
//) AS random_material
//INNER JOIN t_cocreate_material AS a ON a.id = random_material.id
//LEFT JOIN t_cocreate_statistic AS b ON a.id = b.material_id AND b.user_id = 5315
//WHERE b.id IS NULL;

}
