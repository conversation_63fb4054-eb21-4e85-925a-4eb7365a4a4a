<?php

namespace app\modules\wares\models\cocreate;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class CoCreateStatisticModel extends CommModel
{

    public $tb_fields = [
        'id', 'material_id','user_id', 'enjoy', 'remark', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_cocreate_statistic`";
    }

    private function __getListKey(): string
    {
        return AppWRedisKeys::getCoCreateStatisticList();
    }

    private function __getDetailListKey($material_id):string
    {
        return AppWRedisKeys::getCoCreateStatisticDetailList($material_id);
    }

    private function __getInfoByUserKey($user_id, $material_id): string
    {
        return AppWRedisKeys::getCoCreateStatisticInfoByUser($user_id, $material_id);
    }

    private function __getStaticsListByUserKey($user_id): string
    {
        return AppWRedisKeys::getCoCreateStatisticListByUser($user_id);
    }

    public function GetIFUseByUser($user_id): string
    {
        return AppWRedisKeys::getIFUseByUser($user_id);
    }




    private function __delListCache()
    {
        $r_key = $this->__getListKey();
        by::redis('core')->del($r_key);
    }

    private function __delDetailListCache($material_id)
    {
        $r_key = $this->__getDetailListKey($material_id);
        by::redis('core')->del($r_key);
    }

    private function __delInfoByUserCache($user_id, $material_id)
    {
        $r_key = $this->__getInfoByUserKey($user_id, $material_id);
        by::redis('core')->del($r_key);
    }

    private function __delListByUserCache($user_id)
    {
        $r_key = $this->__getStaticsListByUserKey($user_id);
        by::redis('core')->del($r_key);
    }

    public function GetOneByUser($user_id, $material_id, $cache = true)
    {
        $user_id     = CUtil::uint($user_id);
        $material_id = CUtil::uint($material_id);
        $redis       = by::redis('core');
        $r_key       = $this->__getInfoByUserKey($user_id, $material_id);
        $aJson       = $cache ? $redis->get($r_key) : false;
        $aData       = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE `user_id`=:user_id  and `material_id`=:material_id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':user_id' => $user_id, ':material_id' => $material_id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $cache && $redis->set($r_key, json_encode($aData), empty($aData) ? 10 : 3600);
        }

        return $aData;
    }


    public function InsertUserEnjoyData($user_id,$material_id,$enjoy=0,$remark='',$position='')
    {
        //1.判断这个素材有没有（没有直接返回）
        if(!$material_id) return false;
        if(!$user_id) return false;
        if(!$enjoy) return false;
        $materialInfo = byNew::CoCreateMaterialModel()->GetOneById($material_id);
        if(empty($materialInfo)) return false;

        //2.查找用户是否已经评价过 （评价过直接返回）
        $info = $this->GetOneByUser($user_id,$material_id,false);
        if($info) return true;


        //3.用户没有评价过（存入）
        $time = time();
        $arr = [
            'user_id'=>$user_id,
            'material_id'=>$material_id,
            'enjoy'=>$enjoy,
            'remark'=>$remark,
            'ctime'=>$time,
            'utime'=>$time,
        ];
        return $this->SaveInfo($arr);
    }


    public function SaveInfo($save)
    {
        $tb = $this->tbName();
        $s  = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $this->__delListCache();
        $this->__delDetailListCache($save['material_id'] ?? 0);
        $this->__delInfoByUserCache($save['user_id']?? 0, $save['material_id']?? 0);
        $this->__delListByUserCache($save['user_id']?? 0);
        return $s;
    }


    public function GetListByInput($input, $page, $page_size = 50): array
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            $materialDb = byNew::CoCreateMaterialModel()::tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql  = "select a.id from {$materialDb} as a INNER JOIN {$tb} as b ON a.id = b.material_id WHERE {$where}  GROUP BY a.id ORDER BY a.id ASC LIMIT {$offset},{$page_size}";

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 1800);
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }


    private function __getCondition($input): array
    {
        //SQL初始化条件
        $where             = "a.is_del=:is_del";
        $params[':is_del'] = 0;


        if (!empty($input['name'])) {
            $where           .= " AND a.`name` LIKE :name";
            $params[":name"] = "%{$input['name']}%";
        }

        if (!empty($input['id'])) {
            $where           .= " AND a.`id` = :id";
            $params[":id"] = $input['id'];
        }

        if (!empty($input['topic'])) {
            $where            .= " AND a.`topic` LIKE :topic";
            $params[":topic"] = "%{$input['topic']}%";
        }

        return [$where, $params];
    }


    public function GetListCount($input)
    {
        $r_key   = $this->__getListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            $materialDb = byNew::CoCreateMaterialModel()::tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM (select a.id from {$materialDb} as a INNER JOIN {$tb} as b ON a.id = b.material_id WHERE {$where}  GROUP BY a.id) as e ";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 10 : 1800);
        }

        return intval($count);
    }

    public function GetCountByInput($input)
    {
        $tb = $this->tbName();
        list($where, $params) = $this->__getConditionLike($input);
        $sql   = "SELECT `material_id`, COUNT(*) as num FROM {$tb} WHERE {$where} GROUP BY `material_id`";
        return by::dbMaster()->createCommand($sql, $params)->queryAll();
    }

    private function __getConditionLike($input): array
    {
        //SQL初始化条件
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;


        if (!empty($input['enjoy'])) {
            $where           .= " AND `enjoy` = :enjoy";
            $params[":enjoy"] = $input['enjoy'];
        }

        if (!empty($input['material_id'])) {
            $where           .= " AND `material_id` = :material_id";
            $params[":material_id"] = $input['material_id'];
        }

        if (!empty($input['material_ids']) && is_array($input['material_ids'])){
            $materialIds = $input['material_ids'];
            $materialIds = implode("','",$materialIds);
            $where .= " AND `material_id` in ('{$materialIds}')";
        }

        return [$where, $params];
    }


    /**
     * @param $material_id
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * @throws \yii\db\Exception
     * 获取详情列表
     */
    public function GetDetailListByInput($material_id,$input, $page, $page_size = 50): array
    {
        $r_key   = $this->__getDetailListKey($material_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getDetailCondition($material_id,$input);

            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} WHERE {$where} LIMIT {$offset},{$page_size}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 1800);
        }
        return $aData;
    }


    private function __getDetailCondition($material_id,$input): array
    {
        //SQL初始化条件
        $where                  = "material_id=:material_id";
        $params[':material_id'] = $material_id;

        if (!empty($input['user_id'])) {
            $userId = $input['user_id'];
            if(strlen($userId)>10){//手机号
                $str = $this->__getQueryUserIds("phone",$userId);
                if($str) $where           .= " AND `user_id` in '{$str}' ";
            }elseif (intval($userId)==0){//uid
                $str = $this->__getQueryUserIds("uid",$userId);
                if($str) $where           .= " AND `user_id` in '{$str}' ";
            }else{
                $where           .= " AND `user_id` = :user_id";
                $params[":user_id"] = $input['user_id'];
            }
        }

        if (!empty($input['enjoy'])){
            $where           .= " AND `enjoy` = :enjoy";
            $params[":enjoy"] = $input['enjoy'];
        }

        return [$where, $params];
    }

    private function __getQueryUserIds($field,$userId): string
    {
        $users = byNew::CoCreateUserModel()->GetListByInput(['is_del'=>-1,$field=>$userId],1,10000);
        $user_ids = [];
        $str = "";
        if($users){
            foreach ($users as $user){
                $info = byNew::CoCreateUserModel()->GetOneById($user);
                $user_ids[]=$info['user_id'];
            }
            $str = implode("','",$user_ids);
        }
        return $str;
    }


    public function GetDetailListCount($material_id,$input): int
    {
        $r_key   = $this->__getDetailListKey($material_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getDetailCondition($material_id,$input);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 10 : 1800);
        }

        return intval($count);
    }

    public function GetListByUser($user_id,$input,$page,$page_size)
    {
        $r_key   = $this->__getStaticsListByUserKey($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input), $page, $page_size);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getUserCondition($user_id,$input);

            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM  {$tb} as `cs` WHERE {$where} LIMIT {$offset},{$page_size}";
            if (!empty($input['topic'])){
                 $topic = $input['topic'];
                 $materialDb = byNew::CoCreateMaterialModel()::tbName();
                 $sql = "SELECT `cs`.`id`,`cs`.`material_id`,`cs`.`user_id`,`cs`.`enjoy`,`cs`.`remark`,`cm`.`name`,`cm`.`url`,`cm`.`topic` FROM  {$tb} AS `cs` JOIN {$materialDb} AS `cm` ON `cs`.`material_id` = `cm`.`id` WHERE {$where} AND `cm`.`topic` = '{$topic}' AND `cm`.`is_del` = 0 ORDER BY `cs`.`id` DESC LIMIT {$offset},{$page_size}";
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : 1800);
        }
        return $aData;
    }

    public function GetListCountByUser($user_id,$input)
    {
        $r_key   = $this->__getStaticsListByUserKey($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getUserCondition($user_id,$input);
            $sql   = "SELECT COUNT(*) FROM {$tb} as  `cs` WHERE {$where}";
            if (!empty($input['topic'])){
                $topic = $input['topic'];
                $materialDb = byNew::CoCreateMaterialModel()::tbName();
                $sql = "SELECT COUNT(*) FROM {$tb} AS `cs` JOIN {$materialDb} AS `cm` ON `cs`.`material_id` = `cm`.`id` WHERE {$where} AND `cm`.`topic` = '{$topic}' AND `cm`.`is_del` = 0";
            }
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 10 : 1800);
        }

        return intval($count);
    }


    private function __getUserCondition($user_id,$input): array
    {
        //SQL初始化条件
        $where                  = "`cs`.`user_id`=:user_id";
        $params[':user_id'] = $user_id;

        if (!empty($input['enjoy'])){
            $where           .= " AND `cs`.`enjoy` = :enjoy";
            $params[":enjoy"] = $input['enjoy'];
        }

        return [$where, $params];
    }
}
