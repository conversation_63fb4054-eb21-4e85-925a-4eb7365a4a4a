<?php

namespace app\modules\wares\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class GoodsTryBuyingPriceModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'sku', 'price', 'image', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_try_buying_price`";
    }

    private function __getGoodsTryPriceByIdKey($gid, $sid): string
    {
        return AppCRedisKeys::getGoodsTryPriceByIdKey($gid, $sid);
    }

    public function delGoodsTryPriceByIdKey($gid, $sid)
    {
        $redisKey = $this->__getGoodsTryPriceByIdKey($gid, $sid);
        by::redis()->del($redisKey);
    }


    public function SaveLog(array $aData): array
    {
        $gid   = CUtil::uint($aData['gid'] ?? 0);
        $sid   = CUtil::uint($aData['sid'] ?? 0);
        $price = $aData['price'] ?? 0;
        $price = sprintf("%.2f", $price);

        if (empty($gid)) {
            return [false, "增加先试后买商品规则-缺少必要参数"];
        }

        $aLog = $this->GetOneById($gid, $sid, true, false);
        if (!empty($aLog)) {
            return [false, '该先试后买商品规则已存在！'];
        }

        $save = [
            'gid'   => $gid,
            'sid'   => $sid,
            'sku'   => trim($aData['sku'] ?? ''),
            'price' => by::Gtype0()->totalFee($price),
            'image' => trim($aData['image'] ?? ''),
            'ctime' => intval(START_TIME),
            'utime' => intval(START_TIME),
        ];

        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id  = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，先试后买商品规则新增失败"];
        }

        return [true, 'OK'];
    }

    public function GetOneById($gid, $sid = 0, $format_price = true, bool $cache = true, $is_del = true)
    {
        $gid = CUtil::uint($gid);
        $sid = CUtil::uint($sid);

        if (empty($gid)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getGoodsTryPriceByIdKey($gid, $sid);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb             = self::tbName();
            $fields         = implode(",", $this->tb_fields);
            $isDelCondition = $is_del ? ' AND `is_del` = 0 ' : '';
            $sql            = "SELECT {$fields} FROM  {$tb} WHERE `sid`=:sid AND `gid`=:gid $isDelCondition LIMIT 1";
            $aData          = by::dbMaster()->createCommand($sql, [':sid' => $sid, ':gid' => $gid])->queryOne();
            $aData          = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if ($format_price) {
            $aData['price'] = CUtil::totalFee($aData['price'], 1);
        }

        return $aData;
    }


    public function UpdateLog($gid, $sid, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'price', 'image', 'is_del', 'utime', 'dtime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['price'])) {
            $update['price'] = by::Gtype0()->totalFee($update['price']);
        }

        $gid  = CUtil::uint($gid);
        $sid  = CUtil::uint($sid);
        $aLog = $this->GetOneById($gid, $sid);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid, 'sid' => $sid])->execute();

        $this->delGoodsTryPriceByIdKey($gid, $sid);

        return [true, "成功"];
    }


}
