<?php
/**
 * Author :CP
 */

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class GoodsPointsPriceModel extends CommModel
{

    public $tb_fields = [
        'id', 'gid', 'sid', 'sku','exchange','price','point','coupon_id','image',
        'is_del','ctime', 'utime', 'dtime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_points_price`";
    }

    const DECIMAL_RANGE = "99999999.99"; //价格上限

    const EXCHANGE = [
            'PAM'    => 1, //积分+钱
            'AP'     => 2, //全积分
            'COUPON' => 3, //兑换券
            'PRICE'  => 4, //钱
    ];

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 根据gid,sid获取数据
     */
    private function __getGoodsPointsPriceByIdKey($gid, $sid): string
    {
        return AppWRedisKeys::getGoodsPointsPriceById($gid, $sid);
    }

    private function __getSkusByGids($gids)
    {
        return AppWRedisKeys::getSkusByGids($gids);
    }

    /**
     * @param $gid
     * @param $sid
     * 单条数据清理
     */
    private function __delIdCache($gid, $sid)
    {
        $r_key = $this->__getGoodsPointsPriceByIdKey($gid, $sid);
        by::redis('core')->del($r_key);
    }


    /**
     * @param $gid
     * @param $sid
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     * 获取单条数据
     */
    public function GetOneById($gid, $sid, $format_price = true,  bool $cache = true)
    {
        $gid = CUtil::uint($gid);
        $sid  = CUtil::uint($sid);

        if (empty($gid)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getGoodsPointsPriceByIdKey($gid, $sid);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(",", $this->tb_fields);
            $sql    = "SELECT {$fields} FROM  {$tb} WHERE `sid`=:sid AND `gid`=:gid AND `is_del` = 0 LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':sid' => $sid, ':gid' => $gid])->queryOne();
            $aData  = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }
        //格式化价格
        if($format_price){
            $aData['price']         = CUtil::totalFee($aData['price'], 1);
        }

        return $aData;
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 增加积分商品规则
     */
    public function SaveLog(array $aData): array
    {
        $gid    = CUtil::uint($aData['gid'] ?? 0);
        $sid    = CUtil::uint($aData['sid'] ?? 0);
        $price = $aData['price'] ?? 0;
        $price = sprintf("%.2f", $price);
        $point = CUtil::uint($aData['point'] ?? 0);
        $exchange = CUtil::uint($aData['exchange'] ?? '0');
        $coupon_id = CUtil::uint($aData['coupon_id'] ?? '0');

        if(empty($exchange)){
            return [false, "没有指定对应的兑换价"];
        }

        switch ($exchange) {
            case self::EXCHANGE['PAM']:
                $coupon_id = 0;
                break;
            case self::EXCHANGE['PRICE']:
                $coupon_id = 0;
                $point     = 0;
                break;
            case self::EXCHANGE['AP']:
                $price = 0;
                break;
            case self::EXCHANGE['COUPON']:
                $point = 0;
                $price = 0;
                break;
            default:
                break;
        }

        if (empty($gid)) {
            return [false, "增加积分商品规则-缺少必要参数"];
        }

        $aLog = $this->GetOneById($gid, $sid);
        if (!empty($aLog)) {
            return [false, '该积分商品规则已存在！'];
        }

        $save = [
            'gid'         => $gid,
            'sid'         => $sid,
            'sku'         => trim($aData['sku'] ?? ''),
            'exchange'    => $exchange,
            'price'       => by::Gtype0()->totalFee($price),
            'point'       => $point,
            'coupon_id'   => $coupon_id,
            'image'   => trim($aData['image'] ?? ''),
            'ctime'       => intval(START_TIME),
            'utime'       => intval(START_TIME),
        ];

        $tb  = self::tbName();
        $ret = by::dbMaster()->createCommand()->insert($tb, $save)->execute();
        $id  = by::dbMaster()->getLastInsertID();

        if (!$ret) {
            return [false, "未知原因，积分商品规则新增失败"];
        }

        $this->__delIdCache($gid, $sid);

        return [true, 'OK'];
    }


    /**
     * @param $gid
     * @param $sid
     * @param array $update
     * @return array
     * @throws Exception
     * 更新积分商品详情页
     */
    public function UpdateLog($gid, $sid, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'exchange','price','point','coupon_id','image',
            'is_del', 'utime', 'dtime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        if (isset($update['price'])) {
            $update['price'] = by::Gtype0()->totalFee($update['price']);
        }

        $gid  = CUtil::uint($gid);
        $sid   = CUtil::uint($sid);
        $aLog = $this->GetOneById($gid, $sid);
        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tbName();

        by::dbMaster()->createCommand()->update($tb, $update, ['gid' => $gid, 'sid' => $sid])->execute();

        $this->__delIdCache($gid, $sid);
        return [true, "成功"];
    }


    /**
     * @param $data
     * @return array
     * 检验参数
     */
    public function checkExchange($data): array
    {
        $exchange = CUtil::uint($data['exchange'] ?? 0);
        if(!in_array($exchange,self::EXCHANGE)){
            return [false,'没有选择兑换类型'];
        }
        $price      = $data['price']   ?? 0;
        $price = sprintf("%.2f", $price);
        if (bccomp($price, self::DECIMAL_RANGE, 2) > 0 || bccomp($price, 0, 2) < 0) {
            return [false, "非法价格"];
        }
        $point = CUtil::uint($data['point'] ?? 0);
        $coupon_id = CUtil::uint($data['coupon_id'] ?? 0);
        $priceF = CUtil::uint($price*100);//价格（分）

        switch ($exchange) {
            case self::EXCHANGE['PAM']:
                if (empty($priceF) || empty($point)) {
                    return [false, '积分和钱有一个为空！'];
                }
                break;
            case self::EXCHANGE['PRICE']:
                if (empty($priceF)) {
                    return [false, '金额不能为空！'];
                }
                break;
            case self::EXCHANGE['AP']:
                if (empty($point)) {
                    return [false, '积分不能为空！'];
                }
                if ($priceF || $coupon_id) {
                    return [false, '全积分不能填写金额或优惠券ID！'];
                }
                break;
            case self::EXCHANGE['COUPON']:
                if (empty($coupon_id)) {
                    return [false, '兑换券不能为空！'];
                }
                if ($priceF || $point) {
                    return [false, '全积分不能填写金额或优惠券ID！'];
                }
                break;
            default:
                break;
        }
        return [true, $data];
    }


    public function GetSkusByGids($gids,$cache=true): array
    {
        if(!is_array($gids)) $gids = explode(',',$gids);
        $redis = by::redis('core');
        $r_key = $this->__getSkusByGids(serialize($gids));
        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $gids = implode("','",$gids);
            $sql    = "SELECT `sku` FROM  {$tb} WHERE `gid` in ('{$gids}') AND `is_del` = 0";
            $aData  = by::dbMaster()->createCommand($sql)->queryAll();
            $aData  = $aData ?: [];
            $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return array_column($aData,'sku');
    }
}
