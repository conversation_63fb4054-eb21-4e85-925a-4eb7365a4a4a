<?php

namespace app\modules\wares\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class ActivityTypeModel extends CommModel
{

    public $tb_fields = [
        'id', 'ac_id', 'poster_image', 'share_image', 'try_goods_ids', 'try_quota', 'apply_number', 'validity', 'return_period', 'delivery_period'
        , 'survey_key', 'pass_mark', 'is_audit', 'join_condition', 'ac_ids', 'rule', 'create_time', 'update_time'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_activity_type`";
    }

    const IS_AUDIT = [
        'no_audit' => 0,
        'audit'    => 1,
    ];

    private function getWActivityTypeInfoByAcId($acId): string
    {
        return AppCRedisKeys::getWActivityTypeInfoByAcId($acId);
    }

    public function delWActivityTypeInfoByAcId($acId)
    {
        $redisKey = $this->getWActivityTypeInfoByAcId($acId);
        by::redis()->del($redisKey);
    }

    /**
     * @param $ac_id
     * @param $data
     * @return void
     * @throws Exception
     */
    public function saveLog($ac_id, $data)
    {
        $tb = self::tableName();
        if ($ac_id) { // 更新
            by::dbMaster()->createCommand()->update($tb, $data, ['ac_id' => $ac_id])->execute();
        } else {      // 添加
            by::dbMaster()->createCommand()->insert($tb, $data)->execute();
        }
        // 删除缓存
        $this->delWActivityTypeInfoByAcId($ac_id);
        byNew::ActivityModel()->delWAListCacheKey();
    }


    public function getActivityTypeInfoByAcId($acId, $isCache = true)
    {
        if (empty($acId)) {
            return [];
        }

        $redis    = by::redis();
        $redisKey = $this->getWActivityTypeInfoByAcId($acId);

        if ($isCache) {
            $aDataJson = $redis->get($redisKey);
            if (!empty($aDataJson)) {
                return json_decode($aDataJson, true);
            }
        }

        $aData = self::findOne(['ac_id'=>$acId]);
        if (empty($aData)) {
            return [];
        }

        $aData = $aData->toArray();
        if ($isCache) {
            $redis->set($redisKey, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }


}
