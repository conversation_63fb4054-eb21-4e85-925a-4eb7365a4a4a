<?php
/**
 * Author :CP
 */

namespace app\modules\wares\models;

use app\components\AppWRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class GoodsMainModel extends CommModel
{

    public $tb_fields = [
        'id', 'sku', 'short_code', 'name', 'status', 'sort', 'atype', 'source', 'version', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const SOURCE = [
        'COMMON'         => 1,  //普通商品
        'POINTS'         => 2,  //积分商品
        'TRY_BEFORE_BUY' => 3,  //先试后买商品
    ];

    const ATYPE = [
        'SPEC'  => 0, //统一规格
        'SPECS' => 1, //自定义
    ];

    const STATUS = [
        'ON_SALE' => 0,     //在售
        'OFF_SALE' => 1,    //下架
    ];


    const ACT = ['0'=>'up','1'=>'down'];

    const INDEX_SIZE = 2; //首页查询数据

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_goods_main`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }
    /**
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getOneGoodsMainByGid($gid): string
    {
        return AppWRedisKeys::getOneGoodsMainByGid($gid);
    }

    /**
     * @return void
     * 缓存清理
     */
    public function __delCache($gid)
    {
        $r_key = $this->__getOneGoodsMainByGid($gid);
        by::redis('core')->del($r_key);
    }

    /**
     * @return string
     * 商品哈希列表
     */
    private function __getGoodsMainListKey(): string
    {
        return AppWRedisKeys::getGoodsMainList();
    }

    /**
     * @return string
     * 商品哈希列表
     */
    private function __getAllMainListKey(): string
    {
        return AppWRedisKeys::getAllMainList();
    }

    /**
     * @return string
     * 获取商品所有数据
     */
    public function GetAllGoodsSimpleInfoKey(): string
    {
        return AppWRedisKeys::getAllGoodsSimpleInfo();
    }

    /**
     * @return string
     * 获取查询数据
     */
    public function GetIndexGoodsQueryListKey(): string
    {
        return AppWRedisKeys::getIndexGoodsQueryList();
    }

    /**
     * @return void
     * 清理缓存列表(每种类型都要清理)
     */
    private function __delListCache()
    {
        $r_key = $this->__getGoodsMainListKey();
        $r_key1 = $this->__getAllMainListKey();
        $r_key2 = $this->GetIndexGoodsQueryListKey();
        by::redis('core')->del($r_key,$r_key1,$r_key2);
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 商品主表增改
     */
    public function SaveLog(array $aData): array
    {
        //校验主表参数
        list($s, $aData) = $this->__check($aData);
        if (!$s) {
            return [false, $aData];
        }

        $save = [
            'sku'        => $aData['sku'],
            'short_code' => $aData['short_code'],
            'name'       => $aData['name'],
            'atype'      => $aData['atype'],
            'source'     => $aData['source'],
            'version'    => $aData['version'],
            'sort'       => $aData['sort'],
            'status'     => $aData['status'],
        ];

        $id = $aData['id'];

        if ($id) {
            $aGoods = $this->GetOneByGid($id);
            if (empty($aGoods)) {
                return [false, '参数错误-主商品表'];
            }

            if($aGoods['status'] == self::STATUS['ON_SALE']){
                return [false, '上架状态不允许修改！'];
            }

            if($aData['source'] !== CUtil::uint($aGoods['source'])){
                return [false, '积分商品类型不能改动！'];
            }
            unset($save['sku'], $save['source']); //不能修改的参数 不能由积分商品变成普通商品

        } else {
            //新增
            $aGoodsCount = $this->GetListCount(['sku'=>$aData['sku'],'source'=>$aData['source']]);
//            if (!empty($aGoodsCount)) {
//                return [false, "商品编码对应商品已存在"];
//            }

            $save['ctime'] = intval(START_TIME);
        }

        $db    = by::dbMaster();
        $tb    = $this->tbName();

        if ($id) {
            $db->createCommand()->update($tb, $save, "`id`=:id", [":id" => $id])->execute();
            $this->__delCache($id); //todo 清空详情缓存
        } else {
            $db->createCommand()->insert($tb, $save)->execute();
            $id = $db->getLastInsertID();
        }

        $this->__delListCache();

        return [true, $id];
    }


    public function GetOneByGid($gid)
    {
        $gid = CUtil::uint($gid);
        if ($gid <= 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOneGoodsMainByGid($gid);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $gid])->queryOne();
            $aData  = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        $aData['version'] = CUtil::long2version($aData['version']);

        return $aData;
    }


    /**
     * @param $input
     * @param $page
     * @param $page_size
     * @return array
     * @throws Exception
     * 获取列表
     */
    public function GetList($input, $page, $page_size = 50)
    {
        $r_key   = $this->__getGoodsMainListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,serialize($input),$page,$page_size);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb                  = $this->tbName();
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($input,$page,$page_size);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `sort` DESC,`id` DESC 
                    LIMIT {$offset},{$page_size}";

            $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,empty($aData) ? 10 : 1800);
        }

        return empty($aData) ? [] : array_column($aData,"id");

    }


    /**
     * @param int $source
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     */
    public function GetAllMainList(int $source = 1 , bool $cache = true)
    {
        $source = CUtil::uint($source);
        if(empty($source)) return [];
        $r_key = $this->__getAllMainListKey();
        $aJson = $cache ? by::redis('core')->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb    = $this->tbName();
            $sql   = "SELECT `id`,`name`,`sku` FROM {$tb} where `is_del` = 0  AND `status`= 0  AND `source` = {$source} ORDER BY `sort` DESC,`id` DESC ";
            $aData = by::dbMaster()->createCommand($sql)->queryAll();
            $cache && by::redis('core')->set($r_key, json_encode($aData));
            $cache && CUtil::ResetExpire($r_key, empty($aData) ? 10 : 6000);
        }
        return $aData ?? [];
    }


    /**
     * @param $input
     * @return int
     * @throws Exception
     * 获取总量
     */
    public function GetListCount($input,$page=1,$page_size=50)
    {
        $r_key   = $this->__getGoodsMainListKey();
        $sub_key = CUtil::getAllParams(__FUNCTION__,serialize($input));
        $count   = by::redis('core')->hGet($r_key,$sub_key);
        if($count === false) {
            $tb                  = $this->tbName();
            list($where,$params) = $this->__getCondition($input,$page,$page_size);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
            $count               = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,empty($count) ? 10 : 1800);
        }

        return intval($count);
    }


    /**
     * @param $input
     * @return array
     * 查询条件约束
     * @throws Exception
     */
    private function __getCondition($input,$page,$page_size)
    {
        //SQL初始化条件
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;


        if(!empty($input['name'])){
            $where                .= " AND `name` LIKE :name";
            $params[":name"]       = "%{$input['name']}%";
        }

        if(!empty($input['sku'])) {
            $where                .= " AND `sku` LIKE :sku";
            $params[":sku"]        = "%{$input['sku']}%";
        }

        $status = $input['status'] ?? -1;
        if($status >= 0) {
            $where                .= " AND `status`=:status";
            $params[":status"]     = intval(!!$status);
        }

        if(!empty($input['source'])) {
            $where                .= " AND `source`=:source";
            $params[":source"]     = CUtil::uint($input['source']);
        }

        if(!empty($input['id'])) {
            $where                .= " AND `id`=:id";
            $params[":id"]     = CUtil::uint($input['id']);
        }

        if (!empty($input['source']) && $input['source'] == self::SOURCE['TRY_BEFORE_BUY'] && !empty($input['label'])) {
            $ids   = GoodsTryBuyingModel::find()
                ->select('gid')
                ->where(['label' => $input['label']])
                ->column();
            $idArr = implode("','", $ids);
            $where .= " AND `id` in ('{$idArr}')";
        }

        $type = $input['type'] ?? -1;//是否是实体商品(商品类型)
        if($type >0){
            //查找积分商品
            $pointsModel = by::GoodsPointsModel();
            //获取列表
            $pointsGoods = $pointsModel->GetList($input,1,0);
            //where in
            $gids = array_column($pointsGoods,'gid');
            $gids    = implode("','", $gids);
            $where .= " AND `id` in ('{$gids}') ";
        }

        //版本号控制
        $versions  = $this->GetAccessVersions($input['version'] ?? 0);
        $versions  = implode(',',$versions);
        $where    .= " AND `version` IN ({$versions})";

        return [$where,$params];
    }


    /**
     * @param $version
     * @return array
     * 获取可访问商品版本号
     */
    public function GetAccessVersions($version): array
    {
        $version  = CUtil::version2long($version);
        $version = $version === '' ? 0 : $version;
        return array_unique([0,$version]);
    }

    /**
     * @param array $aData
     * @return array
     * 校验主表参数
     */
    private function __check(array $aData): array
    {
        $id        = CUtil::uint($aData['id'] ?? 0);
        $sku       = trim($aData['sku'] ?? "");
        $name      = trim($aData['name'] ?? "");
        $short_code = trim($aData['short_code'] ?? "");
        $atype     = CUtil::uint($aData['atype'] ?? 0);
        $source    = CUtil::uint($aData['source'] ?? 0);
        $version   = CUtil::version2long($aData['g_version'] ?? 0);
        $sort      = CUtil::uint($aData['sort'] ?? 0);
        $status    = CUtil::uint($aData['status'] ?? 0);

        if (empty($name)) {
            return [false, "商品名称不能为空"];
        }

        if (empty($sku)) {
            return [false, "商品编码不能为空"];
        }

        if (empty($source)){
            return [false, "商品类型不能为空"];
        }

        $aData['id']        = $id;
        $aData['sku']       = $sku;
        $aData['name']      = $name;
        $aData['short_code'] = $short_code;
        $aData['atype']     = $atype;
        $aData['source']    = $source;
        $aData['version']   = intval($version);
        $aData['sort']      = $sort;
        $aData['status']    = $status;


        return [true, $aData];
    }


    /**
     * @param $id
     * @param array $update
     * @return array
     * @throws Exception
     * 编辑数据（允许的必要数据）
     */
    public function UpdateData($id, array $update)
    {
        $id     = CUtil::uint($id);
        //允许修改的字段
        if (empty($id) || empty($update)) {
            return [false, '参数缺失'];
        }
        $allowed = ['sort','status','utime','is_del','dtime'];

        foreach ($update as $field => $val) {
            if ( !in_array($field, $allowed) ) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $tb      = self::tbName();

        by::dbMaster()->createCommand()->update($tb,
            $update,
            ['id' => $id]
        )->execute();

        //强制清理缓存
        $this->__delCache($id);
        $this->__delListCache();

        return [true, 'ok'];
    }

    public function getDataByIds(array $ids, array $columns = ['*'])
    {
        // 判空
        if (empty($ids)) {
            return [];
        }

        // 查询
        return self::find()->select($columns)->where(['id' => $ids])->asArray()->all();
    }

}
