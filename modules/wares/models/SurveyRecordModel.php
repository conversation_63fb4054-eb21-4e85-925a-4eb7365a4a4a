<?php

namespace app\modules\wares\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;
use function GuzzleHttp\Psr7\str;


class SurveyRecordModel extends CommModel
{

    public $tb_fields = [
        'id', 'user_id', 'uid', 'ac_id', 'form_key', 'score', 'pass_score', 'audit_status', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return "`db_dreame_wares`.`t_survey_record`";
    }

    //审核状态
    const AUDIT_STATUS = [
        'WAIT_AUDIT'    => 0, //待审核
        'AUDIT_PASS'    => 1, //审核通过
        'AUDIT_NO_PASS' => 2, //审核不通过
    ];

    const STATUS = [
        0 => '待审核',
        1 => '审核通过',
        2 => '审核失败',
    ];
    /**
     * @throws Exception
     * 保存问卷记录
     */
    public function saveLog($userId, $passMark, $auditStatus, $data): array
    {
        $save = [
            'user_id'      => $userId,
            'uid'          => $data['dreameUid'] ?? '',
            'ac_id'        => $data['activityId'] ?? 0,
            'form_key'     => $data['formKey'] ?? '',
            'score'        => $data['totalScore'] ?? 0,
            'pass_score'   => $passMark,
            'audit_status' => $auditStatus,
            'ctime'        => START_TIME,
            'utime'        => START_TIME,
        ];

        // 检查数据是否存在
        $exists = self::find()
            ->where(['user_id' => $userId, 'ac_id' => $save['ac_id'], 'form_key' => $save['form_key']])
            ->exists();

        // 数据已存在，执行更新操作
        if ($exists) {
            $updatedRows = by::dbMaster()
                ->createCommand()
                ->update(self::tableName(), $save, ['user_id' => $userId, 'ac_id' => $save['ac_id'], 'form_key' => $save['form_key']])
                ->execute();

            // 检查更新是否成功
            if (!$updatedRows) {
                return [false, '更新失败'];
            }

            return [true, '更新成功'];
        }

        // 数据不存在，执行插入操作
        $insertedRows = by::dbMaster()
            ->createCommand()
            ->insert(self::tableName(), $save)
            ->execute();

        // 检查插入是否成功
        if (!$insertedRows) {
            return [false, '插入失败'];
        }

        return [true, '插入成功'];
    }


    public function getSurveyRecord($where = [])
    {
        return self::find()
            ->where($where)
            ->asArray()
            ->one();
    }

    public function getSurveyList($input = [], $page = null, $pageSize = null): array
    {
        // 创建查询对象
        $query = self::find()->orderBy(['id' => SORT_DESC]);

        // 获取并应用条件
        $this->applyConditions($query, $input);

        // 判断是否进行分页
        if ($page !== null && $pageSize !== null && $page > 0 && $pageSize > 0) {
            // 计算偏移量
            list($offset) = CUtil::pagination($page, $pageSize);

            // 设置分页参数
            $query->offset($offset)->limit($pageSize);
        }

        // 执行查询并获取结果，直接返回结果数组
        return $query->asArray()->all();
    }


    public function getCount($input = [])
    {
        // 创建查询对象
        $query = self::find()->orderBy(['id' => SORT_DESC]);

        // 获取并应用条件
        $this->applyConditions($query, $input);

        // 获取总数
        return (clone $query)->count();
    }

    private function applyConditions($query, $input): void
    {
        if (isset($input['audit_status']) && $input['audit_status'] > -1) {
            $query->andWhere(['audit_status' => $input['audit_status']]);
        }

        if (isset($input['uid']) && strlen($input['uid']) > 0) {
            $query->andWhere(['uid' => $input['uid']]);
        }

        if (isset($input['user_id']) && strlen($input['user_id']) > 0) {
            $query->andWhere(['user_id' => $input['user_id']]);
        }

        if (isset($input['ac_id']) && strlen($input['ac_id']) > 0) {
            $query->andWhere(['ac_id' => $input['ac_id']]);
        }

        if (isset($input['name']) && strlen($input['name']) > 0) {
            $acIds = byNew::ActivityModel()::find()
                ->select('id')
                ->where(['like', 'name', $input['name']])
                ->asArray()
                ->column();

            $query->andWhere(['IN', 'ac_id', $acIds]);
        }

        if (isset($input['phone']) && strlen($input['phone']) > 0) {
            $userIds = by::Phone()::find()
                ->select('user_id')
                ->where(['phone' => $input['phone']])
                ->asArray()
                ->column();

            $query->andWhere(['IN', 'user_id', $userIds]);
        }

        if (isset($input['label']) && $input['label'] > -1) {
            // 获取符合条件的商品ID列表
            $goodsIds = byNew::GoodsTryBuyingModel()::find()
                ->select('gid')
                ->where(['label' => $input['label']])
                ->asArray()
                ->column();

            // 检查是否有商品ID
            if ($goodsIds) {
                // 获取符合条件的活动ID列表
                $acIds = byNew::ActivityTypeModel()::find()
                    ->select('ac_id')
                    ->where(['IN', 'try_goods_ids', $goodsIds])
                    ->asArray()
                    ->column();

                // 检查是否有活动ID，并将其应用到主查询
                if ($acIds) {
                    $query->andWhere(['IN', 'ac_id', $acIds]);
                }
            }
        }

        if (isset($input['goods_name']) && strlen($input['goods_name']) > 0) {
            // 获取符合条件的商品ID列表
            $goodsIds = byNew::GoodsMainModel()::find()
                ->select('id')
                ->where(['like', 'name', $input['goods_name']])
                ->asArray()
                ->column();

            // 获取符合条件的活动ID列表
            $acIds = byNew::ActivityTypeModel()::find()
                ->select('ac_id')
                ->where(['IN', 'try_goods_ids', $goodsIds])
                ->asArray()
                ->column();

            // 检查是否有活动ID，并将其应用到主查询
            $query->andWhere(['IN', 'ac_id', $acIds]);
        }


        if (isset($input['start_time'], $input['end_time']) && $input['start_time'] > 0 && $input['end_time'] > 0) {
            $query->andWhere(['between', 'ctime', $input['start_time'], $input['end_time']]);
        }

        if (isset($input['is_audit']) && $input['is_audit'] > -1) {
            $acIds = byNew::ActivityTypeModel()::find()
                ->select('ac_id')
                ->where(['is_audit' => $input['is_audit']])
                ->asArray()
                ->column();

            $query->andWhere(['IN', 'ac_id', $acIds]);
        }



    }

    /**
     * @throws Exception
     * 更新审核状态
     */
    public function UpdateAuditStatus($id, $auditStatus): bool
    {
        $affectedRows = by::dbMaster()
            ->createCommand()
            ->update(self::tableName(), ['audit_status' => $auditStatus], ['id' => $id])
            ->execute();

        return $affectedRows > 0;
    }


    public function exportData($uid = '', $phone = '', $ac_id = '', $name = '', $label = '', $goods_name = '', $start_time = '', $end_time = '', $viewSensitive = false)
    {
        $head = ['用户UID', '手机号', '活动ID', '活动名称', '试用商品类型', '试用机器名称', '申请时间', '审核状态'];

        $params = [
            'uid'           => $uid,
            'phone'         => $phone,
            'ac_id'         => $ac_id,
            'name'          => $name,
            'label'         => $label,
            'goods_name'    => $goods_name,
            'start_time'    => $start_time,
            'end_time'      => $end_time,
            'viewSensitive' => $viewSensitive
        ];

        $list = $this->getSurveyList($params);

        $data   = [];
        $data[] = $head;
        $tagMap = by::Gtag()->GetTagNameMap();
        foreach ($list as $value) {
            $activityInfo = byNew::ActivityModel()->getActivityDetail($value['ac_id']);
            $data[]       = [
                'uid'          => $value['uid'],
                'phone'        => by::Phone()->GetPhoneByUid($value['user_id']) ?? '',
                'ac_id'        => $value['ac_id'],
                'name'         => $activityInfo['name'],
                'label'        => $tagMap[$activityInfo['goods']['label']],
                'goods_name'   => $activityInfo['goods']['goods_name'],
                'ctime'        => date('Y-m-d H:i:s', $value['ctime']),
                'audit_status' => self::STATUS[$value['audit_status']],
            ];
        }

        // 结果
        return $data;
    }
}
