<?php

namespace app\modules\wares\models;

use app\models\by;
use app\models\byNew;
use app\modules\main\models\CommModel;

class GiftCardExpendRecordModel extends CommModel
{

    public $tb_fields = [
        'id', 'card_id', 'user_card_id', 'order_no', 'expend_amount', 'current_amount', 'type', 'ctime', 'utime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_wares`.`t_gift_card_expend_record`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 类型
    const TYPES = [
        'USE'    => 1, // 使用
        'RETURN' => 2, // 退还
    ];


    /**
     * 获取消费记录
     * @param int $user_card_id
     * @return array
     */
    public function getRecordsByUserCardId(int $user_card_id): array
    {
        return self::find()
            ->where(['user_card_id' => $user_card_id])
            ->orderBy('id DESC')
            ->asArray()
            ->all();
    }


    /**
     * 退还礼品卡（取消订单、整单退款）
     * @param $order_no
     * @param array $user_card_ids
     * @return array
     * @throws \yii\db\Exception
     */
    public function UnLockGiftCard($user_id, $order_no, array $user_card_ids): array
    {
        $db = by::dbMaster();
        // 1.获取消费记录
        $tbName = self::tbName();
        $ids = implode(',', $user_card_ids);
        $sql = "SELECT * FROM {$tbName} WHERE `order_no`=:order_no AND `user_card_id` IN ({$ids}) ORDER BY `id` DESC";
        $records = $db->createCommand($sql, [':order_no' => $order_no])->queryAll();

        // 2.获取礼品卡余额
        $tbName = GiftUserCardsModel::tbName();
        $sql = "SELECT * FROM {$tbName} WHERE `id` IN ({$ids})";
        $cards = $db->createCommand($sql)->queryAll();
        $amounts = array_column($cards, 'amount', 'id');

        // 3.消费记录（退还）数据
        $insertData = [];
        $time = time();
        foreach ($records as $record) {
            $current_amount = $amounts[$record['user_card_id']] + intval($record['expend_amount']);
            $amounts[$record['user_card_id']] = $current_amount;

            $insertData[] = [
                'card_id'        => $record['card_id'],
                'user_card_id'   => $record['user_card_id'],
                'order_no'       => $record['order_no'],
                'expend_amount'  => -$record['expend_amount'],
                'current_amount' => $current_amount,
                'type'           => self::TYPES['RETURN'],
                'ctime'          => $time,
                'utime'          => $time,
            ];
        }

        // 4.插入消费记录
        $this->insertExpendRecords($db, $insertData);
        // 5.更新礼品卡余额
        $this->updateCardAmounts($db, $user_id, $amounts);

        // 6.清除缓存
        byNew::GiftUserCards()->__delUserGiftCardListRedisKey($user_id);
        foreach ($user_card_ids as $user_card_id) {
            byNew::GiftUserCards()->__delUserGiftCardInfoRedisKey($user_card_id);
        }
        return [true, '退还礼品卡成功'];
    }

    /**
     * 插入消费记录
     * @param $db
     * @param $insertData
     * @return void
     */
    private function insertExpendRecords($db, $insertData)
    {
        if ($insertData) {
            $db->createCommand()->batchInsert(
                self::tbName(),
                ['card_id', 'user_card_id', 'order_no', 'expend_amount', 'current_amount', 'type', 'ctime', 'utime'],
                $insertData
            )->execute();
        }
    }

    /**
     * 更新礼品卡余额
     * @param $db
     * @param $amounts
     * @return void
     */
    private function updateCardAmounts($db, $user_id, $amounts)
    {
        foreach ($amounts as $id => $amount) {
            $db->createCommand()->update(
                GiftUserCardsModel::tbName(),
                ['amount' => $amount, 'status' => 1, 'utime' => time()],
                ['id' => $id]
            )->execute();

            // 清除卡详情缓存
            byNew::GiftUserCards()->__delUserGiftCardInfoRedisKey($id);
        }
        // 清除卡列表缓存
        byNew::GiftUserCards()->__delUserGiftCardListRedisKey($user_id);
    }

    /**
     * 退礼品卡
     */
    public function refund($user_id, $order_no, $refund_no)
    {
        $refundGoodsIds = $this->getRefundGoodsIds($user_id, $refund_no, $order_no);
        if (empty($refundGoodsIds)) {
            return [true, 'ok'];
        }

        $giftCards = $this->getGiftCardUsage($user_id, $order_no, $refundGoodsIds);

        $db = by::dbMaster();
        foreach ($giftCards as $type => $cards) {
            $this->processGiftCardRefund($user_id, $type, $order_no, $cards, $db);
        }

        return [true, 'ok'];
    }

    /**
     * 获取退款商品ID
     * @param $user_id
     * @param $refund_no
     * @param $order_no
     * @return array
     * @throws \yii\db\Exception
     */
    private function getRefundGoodsIds($user_id, $refund_no, $order_no): array
    {
        $refundGoods = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        return array_column($refundGoods, 'og_id');
    }

    /**
     * 获取此退款商品里，礼品卡的使用信息
     * @param $user_id
     * @param $order_no
     * @param $refundGoodsIds
     * @return array
     * @throws \yii\db\Exception
     */
    private function getGiftCardUsage($user_id, $order_no, $refundGoodsIds): array
    {
        $giftCards = [];
        $goods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
        foreach ($goods as $val) {
            if (in_array($val['id'], $refundGoodsIds)) {
                $giftCards[$val['gift_card_type']][] = $val['gift_card_value'];
            }
        }
        return $giftCards;
    }

    private function processGiftCardRefund($user_id, $type, $order_no, $cards, $db)
    {
        switch ($type) {
            case 1: // 退卡金额
                if (array_sum($cards)) {
                    $this->refundCashCard($user_id, $order_no, array_sum($cards), $db);
                }
                break;
            case 2: // 退兑换卡
                if (!empty($cards)) {
                    $this->refundExchangeCard($user_id, $order_no, $cards, $db);
                }
                break;
        }
    }

    /**
     * 退还现金卡
     * @param $order_no
     * @param $amount
     * @param $db
     * @return void
     */
    private function refundCashCard($user_id, $order_no, $amount, $db)
    {
        // 消费金额的数据
        $records = $this->getExpendCashRecords($order_no);

        // 计算剩余消费金额
        $totalExpendAmounts = $this->calculateTotalExpendAmounts($records);

        // 卡信息
        $userCards = byNew::GiftUserCards()->getUserCardsByIds(array_keys($totalExpendAmounts));
        // 根据过期时间倒序
        usort($userCards, function ($a, $b) {
            return $b['expire_time'] - $a['expire_time'];
        });

        list($newRecords, $amounts) = $this->prepareRefundRecords($userCards, $totalExpendAmounts, $amount, $order_no);

        // 插入和更新数据
        $this->insertExpendRecords($db, $newRecords);
        $this->updateCardAmounts($db, $user_id, $amounts);
    }


    /**
     * 取兑换卡
     * @param $user_id
     * @param $order_no
     * @param $cards
     * @param $db
     * @return void
     */
    private function refundExchangeCard($user_id, $order_no, $cards, $db)
    {
        // 消费记录
        $records = self::find()
            ->where(['order_no' => $order_no, 'type' => 1, 'expend_amount' => 0])
            ->andWhere(['in', 'user_card_id', $cards])
            ->asArray()
            ->all();

        $newRecords = [];
        $amounts = [];
        foreach ($records as $record) {
            $newRecords[] = [
                'card_id'        => $record['card_id'],
                'user_card_id'   => $record['user_card_id'],
                'order_no'       => $order_no,
                'expend_amount'  => 0,
                'current_amount' => 0,
                'type'           => self::TYPES['RETURN'],
                'ctime'          => time(),
                'utime'          => time(),
            ];
            $amounts[$record['user_card_id']] = 0;
        }
        // 4.插入消费记录
        $this->insertExpendRecords($db, $newRecords);
        $this->updateCardAmounts($db, $user_id, $amounts);
    }

    /**
     * 消费的金额数据
     * @param $order_no
     * @return array|\yii\db\ActiveRecord[]
     */
    private function getExpendCashRecords($order_no)
    {
        return self::find()
            ->where(['order_no' => $order_no])
            ->andWhere(['in', 'type', [1, 2]])
            ->andWhere(['<>', 'expend_amount', 0])
            ->asArray()
            ->all();
    }

    /**
     * 消费的剩余金额
     * @param $records
     * @return array
     */
    private function calculateTotalExpendAmounts($records): array
    {
        $totalExpenditures = [];
        foreach ($records as $record) {
            $cardId = $record['user_card_id'];
            $totalExpenditures[$cardId] = ($totalExpenditures[$cardId] ?? 0) + $record['expend_amount'];
        }
        return $totalExpenditures;
    }

    /**
     * 准备退款记录
     * @param $userCards
     * @param $totalExpendAmounts
     * @param $amount
     * @param $order_no
     * @return array
     */
    private function prepareRefundRecords($userCards, $totalExpendAmounts, $amount, $order_no): array
    {
        $newRecords = [];
        $amounts = [];

        foreach ($userCards as $userCard) {
            $cardId = $userCard['id'];
            $refundAmount = min($amount, $totalExpendAmounts[$cardId]);

            if ($refundAmount > 0) {
                $newRecords[] = [
                    'card_id'        => $userCard['card_id'],
                    'user_card_id'   => $userCard['id'],
                    'order_no'       => $order_no,
                    'expend_amount'  => -1 * $refundAmount,
                    'current_amount' => $userCard['amount'] + $refundAmount,
                    'type'           => self::TYPES['RETURN'],
                    'ctime'          => time(),
                    'utime'          => time(),
                ];
                $amounts[$cardId] = $userCard['amount'] + $refundAmount;
                $amount -= $refundAmount;
            }

            if ($amount <= 0) {
                break;
            }
        }

        return [$newRecords, $amounts];
    }

}
