<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\components\RuiYun;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use app\modules\forms\plumbing\ServiceOrderSaveModel;
use app\modules\main\models\ProductModel;
use yii\db\Exception;
use app\modules\main\models\CommModel;

/**
 * Class PlumbingOrderModel
 * @package app\modules\plumbing\models
 * 上下水服务模型
 */
class PlumbingOrderModel extends CommModel
{
    CONST EXP          = 3600;

    CONST PAGE_SIZE    = YII_ENV_PROD ? 20 : 3;

    CONST IMAGE_LIMIT  = 5; //图片数量上限

    CONST EXPORT_LIMIT = YII_ENV_PROD ? 200 : 5;

    CONST SOURCE = [
        'INDEX' => 1,
        'ADMIN' => 2
    ];

    CONST SOURCE_PAGE = [
        'ALL_ORDER_PAGE' => 1,
        'PLUMBING_PAGE'  => 2
    ];

    CONST TYPE = [
        'EXPLORE' => 1,
        'INSTALL' => 2, // 上门安装
        'SERVICE' => 3, // 上门服务
    ];

    CONST TYPE_NAME = [
        1 => '勘探工单',
        2 => '安装工单',
        3 => '服务工单',
    ];

    // 产品类型
    const PRODUCT_TYPE = [
        'SWEEPER' => 1, // 扫地机
        'WASHING' => 2, // 洗地机
    ];

    // 产品类型名
    const PRODUCT_TYPE_NAME = [
        1 => '扫地机',
        2 => '洗地机'
    ];

    //租户
    const TENANT = [
        'NORMAL' => '000000',
        'MOVA'  => '000002',
    ];

    CONST STATUS = [
        'WAIT_SERVICE' => 0, //待服务
        'CANCEL'       => 1, //已取消
        'FINISH'       => 2, //已完成
        'IN_SERVICE'   => 3, //服务中
        'WAIT_PAY'     => 4, //待支付
    ];

    CONST STATUS_NAME = [
        0 => '待服务',
        1 => '已取消',
        2 => '已完成',
        3 => '服务中',
        4 => '待支付',
    ];

    CONST REFUND_STATUS = [
        'WAIT_AUDIT'     => 1000,
        'AUDIT_PASS'     => 1010,
        'AUDIT_REFUSE'   => 1020,
        'REFUND_SUCCESS' => 20000000
    ];

    CONST REFUND_STATUS_NAME = [
        1000     => '待审核',
        1010     => '审核通过',
        1020     => '审核拒绝',
        20000000 => '退款成功',
    ];

    CONST CONTEXT_TYPE = [
        'BLANK'  => 1,
        'FINISH' => 2,
        'CHECK'  => 3
    ];

    CONST CONTEXT_TYPE_NAME = [
        1 => '毛坯房',
        2 => '装修中',
        3 => '已入住'
    ];

    CONST EXPLORE_CASE = [
        'WAIT_FOLLOW'     => 1,
        'EXPLORE_FAIL'    => 2,
        'EXPLORE_SUCCESS' => 3,
        'NO_EXPLORE'      => 4
    ];

    CONST EXPLORE_CASE_NAME = [
        1 => '待跟进',
        2 => '勘探失败',
        3 => '勘探成功',
        4 => '未勘探'
    ];

    CONST INSTALL_CASE = [
        'NO_INSTALL'      => 1,
        'INSTALL_FAIL'    => 2,
        'INSTALL_SUCCESS' => 3
    ];

    CONST INSTALL_CASE_NAME = [
        1 => '未安装',
        2 => '安装失败',
        3 => '已安装'
    ];

    CONST IS_EXPORT = [
        'NOT_EXPORT'     => 0,
        'ALREADY_EXPORT' => 1
    ];

    CONST IS_EXPORT_NAME = [
        0 => '未导出',
        1 => '已导出'
    ];

    CONST UPDATE_FIELDS = [
        'STATUS'       => 'status',
        'EXPLORE_CASE' => 'explore_case',
        'IS_EXPORT'    => 'is_export',
        'INSTALL_CASE' => 'install_case'
    ];

    CONST UPDATE_FIELDS_NAME = [
        'status'       => '状态',
        'explore_case' => '勘探情况',
        'is_export'    => '是否导出',
        'install_case' => '安装情况'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_plumbing_order`";
    }

    public $tb_fields = [
            'id', 'order_no', 'sn', 'sn_alias', 'user_id', 'name', 'phone', 'pid', 'cid', 'aid', 'detail', 'address', 'price', 'images', 'explore_case', 'install_case', 'ruiyun_id', 'ruiyun_detail_id',
            'type', 'product_type', 'status', 'refund_status', 'context_type', 'is_buy', 'is_export', 'is_explore', 'expect_time', 'pay_time', 'finish_time', 'update_time', 'ctime', 'tenant', 'sku'
    ];

    /**
     * @return string
     * 订单列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getPlumbingList();
    }

    /**
     * @param $order_no
     * @return string
     * 订单唯一数据缓存KEY
     */
    private function __getInfoByOrderNoKey($order_no): string
    {
        return AppCRedisKeys::getPlumbingInfoByOrNo($order_no);
    }


    /**
     * 清理缓存
     */
    public function delCache($order_nos = '')
    {
        $r_key1 = $this->__getListKey();

        if (is_array($order_nos)) {
            foreach($order_nos as $order_no) {
                $r_key2 = $this->__getInfoByOrderNoKey($order_no);
            }
        } else {
            $r_key2 = $this->__getInfoByOrderNoKey($order_nos);
        }

        by::redis('core')->del($r_key1, $r_key2);
    }


    /**
     * @param $id
     * @param $order_no
     * @param $user_msg
     * @param $type
     * @param $status
     * @param $s_time
     * @param $e_time
     * @param $refund_status
     * @param $is_export
     * @param $phones
     * @param $source_page
     * @param $pay_check
     * @param $product_type
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(
        $id = 0, $order_no = '', $user_msg = '', $type = 0, $status = -1, $s_time = 0, $e_time = 0, $refund_status = '',
        $is_export = -1, $phones = '', $source_page = 1, $pay_check = false, $product_type = 0
    ): array
    {
        $where  = "1 = 1";
        $params = [];

        $id = CUtil::uint($id);
        if ($id > 0) {
            $where        .= " and `id`=:id";
            $params[':id'] = $id;
        }

        if (!empty($order_no)) {
            $where              .= " and `order_no`=:order_no";
            $params[':order_no'] = $order_no;
        }

        if (!empty($user_msg)) {
            $user_id            = CUtil::uint($user_msg);
            $where             .= " AND `user_id` = :user_id";
            $params[':user_id'] = $user_id;
        }

        if ($type > 0) {
            $where          .= " and `type`=:type";
            $params[':type'] = $type;
        }

        /*switch ($source_page) {
            case self::SOURCE_PAGE['PLUMBING_PAGE'] :
                switch ($type) {
                    case self::TYPE['EXPLORE'] :
                    case self::TYPE['INSTALL'] :
                        $status_str = self::STATUS['WAIT_SERVICE'].",".self::STATUS['IN_SERVICE'];
                        $where     .= " AND `status` IN ({$status_str})";

                        break;
                    default :
                        break;
                }

                break;
            default :
                if (!$pay_check) {
                    $where .= " and `status` != 4";
                }

                if ($status > -1) {
                    $where            .= " and `status`=:status";
                    $params[':status'] = $status;
                }

                break;
        }*/

        if (!$pay_check) {
            $where .= " and `status` != 4";
        }

        if ($status > -1) {
            $where            .= " and `status`=:status";
            $params[':status'] = $status;
        }

        // 产品类型
        if ($product_type > 0) {
            $where            .= " and `product_type`=:product_type";
            $params[':product_type'] = $product_type;
        }

        if (!empty($s_time) && !empty($e_time)) {
            $where            .= " AND `ctime` BETWEEN :s_time AND :e_time";
            $params[":s_time"] = $s_time;
            $params[":e_time"] = $e_time;
        }

        $refund_status_str = '';
        $refund_status_arr = explode('|', stripslashes(strval($refund_status)));
        foreach ($refund_status_arr as $refund) {
            if ($refund == '') {
                continue;
            }
            $refund_status_str .= CUtil::uint($refund).',';
        }

        if (!empty($refund_status_str)) {
            $refund_status_str = rtrim($refund_status_str, ',');

            $where .= " AND `refund_status` IN ({$refund_status_str})";
        }

        if ($is_export > -1) {
            $where               .= " and `is_export`=:is_export";
            $params[':is_export'] = $is_export;
        }

        if (!empty($phones)) {
            $phones_arr = explode(',', stripslashes($phones));

            $phones_str = '';
            foreach ($phones_arr as $phone) {
                $phones_str .= CUtil::uint($phone).',';
            }

            $phones_str = rtrim($phones_str, ',');

            $where .= " AND `phone` IN ({$phones_str})";
        }

        return [$where, $params];
    }

    /**
     * @param $source
     * @param int $page
     * @param int $page_size
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @return array
     * @throws Exception
     * 上下水服务订单列表
     */
    public function getListBack($source, int $page = 1, int $page_size = self::PAGE_SIZE, string $order_no = '', string $user_msg = '', int $type = 0, int $status = -1, int $s_time = 0, int $e_time = 0): array
    {
        $redis  = by::redis('core');
        $r_key  = self::__getListKey();
        $s_key  = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $order_no, $user_msg, $type, $status, $s_time, $e_time);

        $aJson  = $redis->hGet($r_key, $s_key);
        $ids    = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition(0, $order_no, $user_msg, $type, $status, $s_time, $e_time);

            if ($source == 1) {
                $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
            } else {
                list($offset) = CUtil::pagination($page, $page_size);
                $sql          = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size} ";
            }

            $ids = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $ids = !empty($ids) ? $ids : [];

            $redis->hSet($r_key, $s_key, json_encode($ids));
            CUtil::ResetExpire($r_key, empty($ids) ? 0 : self::EXP);
        }

        $list = [];

        foreach ($ids as $v)
        {
            $info = by::plumbingOrder()->getInfoById($v['id']);
            if(empty($info) || $info['status'] == 3 || $info['status'] == 4){
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param $source
     * @param $type
     * @param $source_page
     * @param $page
     * @param $page_size
     * @param $order_no
     * @param $user_msg
     * @param $status
     * @param $s_time
     * @param $e_time
     * @param $refund_status
     * @param $is_export
     * @param $phones
     * @param $id
     * @return array
     * @throws Exception
     * 订单列表
     */
    public function getList(
        $source, $type, $source_page = self::SOURCE_PAGE['ALL_ORDER_PAGE'], $page = 1, $page_size = self::PAGE_SIZE,
        $order_no = '', $user_msg = '', $status = -1,  $s_time = 0, $e_time = 0, $refund_status = '', $is_export = -1, $phones = '', $id = 0, $product_type = 0
    ): array
    {
        $source = CUtil::uint($source);
        $type   = CUtil::uint($type);
        if (empty($source) || empty($type)) {
            return [];
        }

        $redis     = by::redis('core');
        $r_key     = self::__getListKey();
        $s_key     = CUtil::getAllParams(__FUNCTION__, $source, $type, $source_page, $page, $page_size, $order_no, $user_msg, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id, $product_type);
        $aJson     = $redis->hGet($r_key, $s_key);
        $order_nos = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition($id, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $source_page, false, $product_type);

            if ($source == 1) {
                $sql = "SELECT `order_no` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
            } else {
                list($offset) = CUtil::pagination($page, $page_size);
                $sql          = "SELECT `order_no` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size} ";
            }

            $order_nos = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $order_nos = !empty($order_nos) ? $order_nos : [];

            $redis->hSet($r_key, $s_key, json_encode($order_nos));
            CUtil::ResetExpire($r_key, empty($order_nos) ? 0 : self::EXP);
        }

        if ($source == 1) {
            $can_user = false;
        } else {
            $can_user = true;
        }

        $list           = [];
        $plumbing_order = by::plumbingOrder();

        foreach ($order_nos as $v)
        {
            $info = $plumbing_order->getInfoByOrderNo($v['order_no'], $can_user);
            if(empty($info)){
                continue;
            }

            if (
                $source == 1 && ($info['refund_status'] == $plumbing_order::REFUND_STATUS['WAIT_AUDIT']
                    || $info['status'] == $plumbing_order::REFUND_STATUS['AUDIT_PASS'])
            ) {
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param $order_no
     * @param bool $can_user
     * @param bool $pay_check
     * @return array
     * @throws Exception
     * 订单唯一数据
     */
    public function getInfoByOrderNo($order_no, bool $can_user = false, bool $pay_check = false, bool $cache = true): array
    {
        if (empty($order_no)) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByOrderNoKey($order_no);
        $json       = $cache ? $redis->get($redis_key) : false;
        $info       = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $param) = $this->__getCondition(0, $order_no, '', 0, -1, 0, 0, '', -1, '', 1, $pay_check);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql,$param)->queryOne();

            $info   = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
       }

        if(!empty($info)) {
            $info['address'] = (array)json_decode($info['address'], true);
            $info['price']   = by::Gtype0()->totalFee($info['price'], 1);

            if ($can_user) {
                $user = by::users()->getOneByUid($info['user_id']);
                $info['user'] = [
                    'nick'   => $user['nick']   ?? "",
                    'phone'  => by::Phone()->GetPhoneByUid($info['user_id']),
                ];
            }
        }

        return $info;
    }


    /**
     * @param $order_no
     * @param $data
     * @return bool
     * @throws Exception
     * 保存数据
     */
    public function savePlumbingOrder($order_no, $data): bool
    {
        CUtil::debug(by::dbMaster()->createCommand()->update(self::tbName(), $data, ['order_no' => $order_no])->getRawSql());
        $res = by::dbMaster()->createCommand()->update(self::tbName(), $data, ['order_no' => $order_no])->execute();
        //删除列表缓存
        $this->delCache($order_no);

        return $res;
    }


    /**
     * @param $id
     * @return array
     * @throws Exception
     * 订单唯一数据
     */
    public function getInfoById($id): array
    {
        $id = CUtil::uint($id);
        if ($id == 0) {
            return [];
        }

        $tb                  = self::tbName();
        $fields              = implode("`,`", $this->tb_fields);
        list($where, $param) = $this->__getCondition($id);
        $sql  = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
        $info = by::dbMaster()->createCommand($sql,$param)->queryOne();
        $info = empty($info) ? [] : $info;

        if(!empty($info)) {
            $info['address'] = (array)json_decode($info['address'], true);
            $info['price']   = by::Gtype0()->totalFee($info['price'], 1);
        }

        return $info;
    }

    /**
     * @param $phone
     * @param $type
     * @return array
     * @throws Exception
     * 通过手机号获取数据
     */
    public function getIdsByPhone($phone, $type): array
    {
        if (empty($phone)) {
            return [];
        }

        $tb                  = self::tbName();
        list($where, $param) = $this->__getCondition(0, '', '', $type, -1, 0, 0, '', -1, $phone);
        $sql  = "SELECT `id` FROM {$tb} WHERE {$where} LIMIT 1";
        $info = by::dbMaster()->createCommand($sql,$param)->queryColumn();

        return empty($info) ? [] : $info;
    }

    /**
     * @param $type
     * @param $order_no
     * @param $user_msg
     * @param $status
     * @param $s_time
     * @param $e_time
     * @param $refund_status
     * @param $is_export
     * @param $phones
     * @param $id
     * @return int
     * @throws Exception
     * 订单数量
     */
    public function getCount(
        $type, $order_no = '', $user_msg = '', $status = -1, $s_time = 0, $e_time = 0, $refund_status = '', $is_export = -1, $phones = '', $id = 0, $product_type = 0
    ): int
    {
        $id   = CUtil::uint($id);

        $type = CUtil::uint($type);
        if (empty($type)) {
            return 0;
        }

        $id = CUtil::uint($id);

        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id, $product_type);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition($id, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phones, 1, false, $product_type);
            $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }

        return intval($count);
    }

    /**
     * @param $user_id
     * @param $data
     * @return array
     * @throws Exception
     * 订单保存
     */
    public function plumbingSaveBack($user_id, $data): array
    {
        if (empty($data)) {
            return [false,"参数错误"];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if (empty($data['name'])) {
            return [false, "请填写联系人姓名"];
        }

        if (empty($data['phone'])) {
            return [false,"请填写联系人手机号"];
        }

        //短信码验证
        $verify_code = $data['verify_code'] ?? 0;
        list($status, $res) = by::model("SmsModel",MAIN_MODULE)->verifyCode($data['phone'], "CODE", $verify_code);
        if(!$status){
            return [$status, $res];
        }

        $pid = $data['pid'] ?? 0;
        $cid = $data['cid'] ?? 0;
        $aid = $data['aid'] ?? 0;

        // 省市区 瑞云名称
        $res = [
                'province' => $data['province'] ?? '未知省',
                'city'     => $data['city'] ?? '未知市',
                'area'     => $data['county'] ?? '未知区',
        ];

        if (empty($data['detail'])) {
            return [false, "请填写详情地址"];
        }

        $expect_time= isset($data['expect_time']) ? CUtil::uint($data['expect_time']) : 0;
        if ($expect_time == 0) {
            return [false, "请选择勘探日期"];
        }

        $context_type = isset($data['context_type']) ? CUtil::uint($data['context_type']) : 0;
        if (!in_array($context_type, self::CONTEXT_TYPE)) {
            return [false, "请选择合法家庭情况"];
        }

        $type = isset($data['type']) ? CUtil::uint($data['type']) : 0;
        if (!in_array($type, self::TYPE)) {
            return [false, "类型不合法"];
        }

        //sn验证
        $sn = $data['sn'] ?? '';
        list($status, $ret) = by::product()->match($sn,$user_id);
        if (!$status) {
            return [false, $ret];
        }

        $save = [
            'user_id'      => $user_id,
            'order_no'     => $this->createOrderNo(time()),
            'sn'           => $data['sn'],
            'name'         => $data['name'],
            'phone'        => $data['phone'],
            'pid'          => $pid,
            'cid'          => $cid,
            'aid'          => $aid,
            'detail'       => $data['detail'],
            'address'      => json_encode($res),
            'status'       => by::plumbingOrder()::STATUS['WAIT_SERVICE'],
            'type'         => $data['type'],
            'context_type' => $data['context_type'],
            'expect_time'  => $expect_time,
            'update_time'  => time(),
            'ctime'        => time()
        ];

        //todo 插入下单信息
        $ret = by::dbMaster()->createCommand()->insert(self::tbName(), $save)->execute();
        if (!$ret) {
            return [false, "下单失败"];
        }

        //上下水推送瑞云
        RuiYun::factory()->push($user_id,'save',['order_no'=>$save['order_no']??'','user_id'=>$user_id]);

        //下单完成 异步存入

        $this->delCache();

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $api
     * @param $data
     * @return array
     * @throws Exception
     * 订单保存
     */
    public function plumbingSave($user_id, $api, $data): array
    {
        if (empty($data)) {
            return [false,"参数错误"];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        $type = isset($data['type']) ? CUtil::uint($data['type']) : 0;
        if (!in_array($type, self::TYPE)) {
            return [false, "类型不合法"];
        }

        if (empty($data['name'])) {
            return [false, "请填写联系人姓名"];
        }

        if (empty($data['phone'])) {
            return [false,"请填写联系人手机号"];
        }

        $pid = $data['pid'] ?? 0;
        $cid = $data['cid'] ?? 0;
        $aid = $data['aid'] ?? 0;

        // 省市区 瑞云名称
        $res = [
                'province' => $data['province'] ?? '未知省',
                'city'     => $data['city'] ?? '未知市',
                'area'     => $data['county'] ?? '未知区',
        ];

        //判断支付方式
        $pay_type = $data['pay_type']??by::Omain()::PAY_BY_WX;


        if (empty($data['detail'])) {
            return [false, "请填写详情地址"];
        }

        $expect_time= isset($data['expect_time']) ? CUtil::uint($data['expect_time']) : 0;
        if ($expect_time == 0) {
            return [false, "请选择期望日期"];
        }

        $context_type = isset($data['context_type']) ? CUtil::uint($data['context_type']) : 0;
        if (!in_array($context_type, self::CONTEXT_TYPE)) {
            return [false, "请选择合法家庭情况"];
        }

        $ctime     = time();
        $pay_price = 0;

        $save = [
                'user_id'      => $user_id,
                'name'         => $data['name'],
                'phone'        => $data['phone'],
                'pid'          => $pid,
                'cid'          => $cid,
                'aid'          => $aid,
                'detail'       => $data['detail'],
                'address'      => json_encode($res),
                'type'         => $type,
                'context_type' => $context_type,
                'expect_time'  => $expect_time,
                'update_time'  => $ctime,
                'ctime'        => $ctime
        ];

        switch ($type) {
            case self::TYPE['EXPLORE'] :
                if (!isset($data['is_buy'])) {
                    return [false, "请选择是否购买追觅产品"];
                }

                $is_buy         = CUtil::uint($data['is_buy']);
                $save['is_buy'] = $is_buy;

                if ($is_buy == 1) {
                    $sn = isset($data['sn']) ? trim(strval($data['sn'])) : '' ;
                    list($status, $ret) = by::plumbingSn()->match($type, $sn);
                    if (!$status) {
                        return [false, $ret];
                    }

                    $save['sn']     = $sn;
                    $save['status'] = by::plumbingOrder()::STATUS['WAIT_SERVICE'];
                } else {
                    if (!isset($data['price'])) {
                        return [false, '价格不存在'];
                    }

                    $pay_price    = by::Gtype0()->totalFee($data['price']);
                    $price_config = by::plumbingPrice()->getPriceConfig();
                    $price        = by::Gtype0()->totalFee($price_config['price'] ?? 0);
                    if ($pay_price != $price) {
                        return [false, '支付金额有误'];
                    }

                    $save['price'] = $pay_price;
                }

                break;
            case self::TYPE['INSTALL'] :
                if (empty($data['sn_config'])) {
                    return [false, '请选择产品信息'];
                }

                $sn_arr = (array)json_decode($data['sn_config'], true);
                if (empty($sn_arr['sn_alias'])) {
                    return [false, '请选择产品信息'];
                }

                $sn = isset($data['sn']) ? trim(strval($data['sn'])) : '' ;
                if (empty($sn)){
                    return [false,'请填写sn编码'];
                }

                if(!preg_match("/^[a-zA-Z0-9-\/]+$/u",$sn)){
                    return [false,'sn编码不符合规则'];
                }

                //截取不同的位数 循环查询
                $p_sn   = $sn_arr['p_sn'] ?? '';
                $digits = by::plumbingSn()::SN_FIX_DIGITS;
                for ($i = $digits['MIN']; $i <= $digits['MAX']; $i++) {
                    $sn_fix = substr($sn, 0, $i);
                    if ($sn_fix == $p_sn) {
                        $sn_check = 1;

                        break;
                    }
                }

                if (empty($sn_check)) {
                    return [false, '该sn编码与机型不匹配'];
                }

                $save['sn']       = $sn;
                $save['sn_alias'] = $sn_arr['sn_alias'];
                // 通过 $p_sn sn前缀找到对应sku
                $save['sku']      = ProductModel::find()
                        ->select('m_name')
                        ->where(['sn' => $p_sn])
                        ->scalar() ?: '';

                if (!isset($data['is_explore'])) {
                    return [false, "请选择是否已上门勘测"];
                }

                $is_explore         = CUtil::uint($data['is_explore']);
                $save['is_explore'] = $is_explore;

                if ($is_explore == 0) {
                    if (empty($data['images'])) {
                        return [false, "请上传图片"];
                    }

                    if(substr_count($data['images'], "|") > self::IMAGE_LIMIT) {
                        return [false,"图片数量最多".self::IMAGE_LIMIT."张"];
                    }

                    $save['images'] = $data['images'];
                }

                $save['status']  = by::plumbingOrder()::STATUS['WAIT_SERVICE'];

                break;
            default :
                return [false, "类型不合法"];
        }

        //短信码验证
        $verify_code = $data['verify_code'] ?? 0;
        list($status, $res) = by::model("SmsModel",MAIN_MODULE)->verifyCode($data['phone'], "CODE", $verify_code);
        if(!$status){
            return [$status, $res];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            //尝试三次创建订单
            for ($i=0;$i<3;$i++) {
                $save['order_no'] = $this->createOrderNo($ctime);

                //todo 插入下单信息
                list($status, $order_no) = $this->__safeInsert($db, $save);
                if ($status) {
                    break;
                }
            }

            if(!$status) {
                throw new \Exception($order_no, 2001);
            }

            switch ($type) {
                case by::plumbingOrder()::TYPE['EXPLORE'] :
                    //todo 实付款为0，则无需走微信下单
                    if (bccomp($pay_price, 0) == 0) {
                        if (isset($is_buy) && $is_buy == 0) {
                            list($s,$ret) = by::BasePayModel()->afterPay($user_id, $order_no, [], [], by::wxPay()::SOURCE['PLUMBING'],$pay_type);
                            if (!$s) {
                                throw new \Exception($ret);
                            }
                        }

                        $ret = ['need_pay' => '0', 'order_no' => $order_no];
                    } else {
                        $other  = ['body' =>'工单支付', 'ctime' => $ctime];
                        $attach = ['source' => by::wxPay()::SOURCE['PLUMBING']];

                        if($pay_type == by::Omain()::PAY_BY_WX){
                            list($status, $ret) = by::WxPay()->unifiedOrder($user_id, $api, $order_no, $pay_price, $other, $attach, $pay_type);
                            if(!$status){
                                throw new MyExceptionModel($ret);
                            }
                        }elseif ($pay_type == by::Omain()::PAY_BY_WX_H5){
                            list($status, $ret) = by::wxH5Pay()->unifiedOrder($user_id, $api, $order_no, $pay_price, $other, $attach);
                            if(!$status){
                                throw new MyExceptionModel($ret);
                            }
                        }else{//仅仅返回对应的订单信息
                            $ret = ['order_no' => $order_no];
                        }
                        $ret['need_pay'] = '1';
                        $ret['pay_type'] = $pay_type;
                        $ret['h5_url']   = $ret['h5_url'] ??'';
                    }

                    //todo 下单流水
                    if (isset($is_buy) && $is_buy == 0) {
                        $pay_data = ['prepay_id' => $ret['prepay_id'] ?? '', 'price' => $pay_price,'ptime'=>time(),'pay_type'=>$pay_type,'h5_url'=>$ret['h5_url']??''];
                        $res = by::model('OPayModel','goods')->SaveLog($order_no, $pay_data);
                        if($res <= 0) {
                            throw new Exception("更新失败");
                        }
                    }

                    break;
                case by::plumbingOrder()::TYPE['INSTALL'] :
                    $ret = ['need_pay' => '0', 'order_no' => $order_no];

                    break;
                default :
                    return [false, "类型不合法"];
            }


            $trans->commit();
            $this->delCache();

            //上下水推送瑞云
            RuiYun::factory()->push($user_id,'save',['order_no'=>$order_no,'user_id'=>$user_id]);

            self::ReqAntiConcurrency($user_id, $unique_key, 0,'DEL');

            return [true, $ret];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            by::Omain()->__addRecord($e, [], $unique_key, $user_id, by::wxPay()::SOURCE['PLUMBING']);

            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $trans->rollBack();

            by::Omain()->__addRecord($e, [], $unique_key, $user_id, by::wxPay()::SOURCE['PLUMBING']);

            return [false, '网络繁忙,请稍候'];
        }
    }

    /**
     * 保存订单信息
     * @param array $data
     * @return array
     */
    public function saveOrder(array $data): array
    {
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 尝试三次创建订单
            for ($i = 0; $i < 3; $i++) {
                $data['order_no'] = $this->createOrderNo(time());
                // 插入下单信息
                list($status, $order_no) = $this->__safeInsert($db, $data);
                if ($status) {
                    break;
                }
            }
            if (!$status) {
                throw new \Exception($order_no, 2001);
            }
            $ret = ['need_pay' => '0', 'order_no' => $order_no];
            $trans->commit();
            $this->delCache();

            // 推送瑞云
            $random = $data['user_id'];
            // 获取随机整数（0-100）
            if(empty($random)) $random = rand(0, 100);
            RuiYun::factory()->push($random, 'save', ['order_no' => $order_no, 'user_id' => $data['user_id']]);
            return [true, $ret];
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug(sprintf('服务订单信息：%s。异常信息：%s', implode(',', $data), $e->getMessage()));
            return [false, '网络繁忙,请稍候'];
        }
    }

    /**
     * @param $ids_arr
     * @param $field
     * @param $val
     * @return array
     * @throws Exception
     * 修改字段
     */
    public function updateFields($ids_arr, $field, $val): array
    {
        if (empty($field) || ($field != self::UPDATE_FIELDS['IS_EXPORT'] && $field != self::UPDATE_FIELDS['STATUS'] && empty($val))) {
            return [false, '参数错误'];
        }

        if (empty($ids_arr)) {
            return [false, '请选择工单'];
        }

        switch ($field) {
            case self::UPDATE_FIELDS['STATUS'] :
                if (!in_array($val,self::STATUS)) {
                    return [false,"状态不合法"];
                }

                $val_name = by::plumbingOrder()::STATUS_NAME[$val];


                break;
            case self::UPDATE_FIELDS['EXPLORE_CASE'] :
                if (!in_array($val,self::EXPLORE_CASE)) {
                    return [false,"勘探情况不合法"];
                }

                $val_name = by::plumbingOrder()::EXPLORE_CASE_NAME[$val];

                break;
            case self::UPDATE_FIELDS['IS_EXPORT'] :
                if (!in_array($val,self::IS_EXPORT)) {
                    return [false,"导出结果不合法"];
                }

                $val_name = by::plumbingOrder()::IS_EXPORT_NAME[$val];

                break;
            case self::UPDATE_FIELDS['INSTALL_CASE'] :
                if (!in_array($val,self::INSTALL_CASE)) {
                    return [false,"安装情况不合法"];
                }

                $val_name  = by::plumbingOrder()::INSTALL_CASE_NAME[$val];

                break;
            default :
                return [false,"{$field}不存在"];
        }

        $field_name = by::plumbingOrder()::UPDATE_FIELDS_NAME[$field];
        $b_val_name = "";

        foreach ($ids_arr as $id) {
            $po_info = $this->getInfoById($id);
            if(empty($po_info)){
                return [false,"订单信息不存在"];
            }

            switch ($field) {
                case self::UPDATE_FIELDS['STATUS'] :
                    $b_val_name .= by::plumbingOrder()::STATUS_NAME[$po_info[$field]].",";

                    break;
                case self::UPDATE_FIELDS['EXPLORE_CASE'] :
                    $b_val_name .= "未设置,";
                    if ($po_info[$field] != 0) {
                        $b_val_name .= by::plumbingOrder()::EXPLORE_CASE_NAME[$po_info[$field]].",";
                    }

                    break;
                case self::UPDATE_FIELDS['IS_EXPORT'] :
                    $b_val_name .= by::plumbingOrder()::IS_EXPORT_NAME[$po_info[$field]].',';

                    break;
                case self::UPDATE_FIELDS['INSTALL_CASE'] :
                    $b_val_name .= "未设置,";
                    if ($po_info[$field] != 0) {
                        $b_val_name .= by::plumbingOrder()::INSTALL_CASE_NAME[$po_info[$field]].",";
                    }

                    break;
                default :
                    break;
            }

            /*if ($info[$field] == $val) {
                return [false,"{$field}信息已更新"];
            }*/
        }

        $b_val_name = trim($b_val_name, ",");

        $time = time();
        $save = [$field => $val, 'update_time' =>$time];
        if ($field == self::UPDATE_FIELDS['STATUS']) {
            $save['finish_time'] = $val == self::STATUS['FINISH'] ? $time : 0;
        }

        $res  = by::dbMaster()->createCommand()->update(self::tbName(), $save, ['id' => $ids_arr])->execute();
        if ($res <= 0) {
            return [true, '更新失败'];
        }

        foreach ($ids_arr as $id) {
            //todo 推送公众号消息推送队列
            $info = $this->getInfoById($id);
            if ($field == self::UPDATE_FIELDS['STATUS'] && $val == self::STATUS['FINISH']) {
                $push_data = [
                    'type'        => by::WxNotice()::TYPE['PLUMBING_COMMENT'],
                    'order_type'  => $info['type'],
                    'user_id'     => $info['user_id'],
                    'order_no'    => $info['order_no'],
                    'finish_time' => $save['finish_time']
                ];

                by::WxNotice()->AddPush($push_data);
            }

            $this->delCache($info['order_no'] ?? '');
        }

        return [true, ['field_name' => $field_name, 'b_val_name' => $b_val_name, 'val_name' => $val_name]];
    }

    /**
     * @param $order_no
     * @param $user_msg
     * @param $status
     * @param $s_time
     * @param $e_time
     * @param $refund_status
     * @param $is_export
     * @param $phones
     * @param $id
     * 勘测订单导出
     */
    public function exploreExport(
        $order_no = '', $user_msg = '', $status = -1, $s_time = 0, $e_time = 0, $refund_status = '', $is_export = -1, $phones = '', $id = 0, $viewSensitive=false
    )
    {
        $head = [
            '工单ID', '工单编号', '工单类型','用户ID','用户手机号','产品SN编码', '勘测情况', '工单状态', '退款状态', '导出结果', '创建时间',
            '完成时间', '买家姓名', '联系人', '联系电话', '省', '市', '区', '联系地址', '期望时间', '家庭环境'
        ];

        $f_name = '勘测工单列表' . date('Ymd') . mt_rand(1000, 9999);
        $limit  = self::EXPORT_LIMIT;
        $tb     = self::tbName();
        $type   = self::TYPE['EXPLORE'];
        list($where,$params) = $this->__getCondition(0, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phones);

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params, $id, $limit, $viewSensitive)
        {
            $db       = by::dbMaster();
            $id       = $id ?? 0;
            $id_where = $id > 0 ? " `id` = :id " :  " `id` > :id ";
            $fields   = implode("`,`", $this->tb_fields);
            $sql      = "SELECT `{$fields}` FROM {$tb} WHERE {$id_where} AND {$where} ORDER BY `id` LIMIT {$limit}";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val)
                {
                    if (empty($val)) {
                        continue;
                    }

                    $address = (array)json_decode($val['address'], true);

                    $data[] = [
                        'id'            => $val['id'],
                        'order_no'      => $val['order_no']."\t",
                        'type'          => self::TYPE_NAME[$val['type']] ?? '勘探服务',
                        'user_id'       => $val['user_id'],
                        'phone'         => by::Phone()->GetPhoneByUid($val['user_id']) ?? '',
                        'sn'            => $val['sn'],
                        'explore_case'  => self::EXPLORE_CASE_NAME[$val['explore_case']] ?? '',
                        'status'        => self::STATUS_NAME[$val['status']] ?? '',
                        'refund_status' => self::REFUND_STATUS_NAME[$val['refund_status']] ?? '',
                        'is_export'     => self::IS_EXPORT_NAME[$val['is_export']] ?? '',
                        'ctime'         => empty($val['ctime']) ? '' : date('Y-m-d H:i:s', $val['ctime']),
                        'finish_time'   => empty($val['finish_time']) ? '' : date('Y-m-d H:i:s', $val['finish_time']),
                        'nick'          => by::users()->getOneByUid($val['user_id'])['nick'] ?? '',
                        'name'          => $val['name'],
                        'mobile'        => $val['phone'],
                        'province'      => $address['province'] ?? '',
                        'city'          => $address['city'] ?? '',
                        'area'          => $address['area'] ?? '',
                        'detail'        => $val['detail'],
                        'expect_time'   => empty($val['expect_time']) ? '' : date('Y-m-d', $val['expect_time']),
                        'context_type'  => self::CONTEXT_TYPE_NAME[$val['context_type']] ?? '',
                    ];
                }
                !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm','detail'=>'tm','mobile'=>'tm']);

                yield $data;

                if (count($list) < $limit) {
                    break;
                }
            }

        }, $f_name);
    }

    public function exploreExportData($order_no = '', $user_msg = '', $status = -1, $s_time = 0, $e_time = 0, $refund_status = '', $is_export = -1, $phones = '', $id = 0, $viewSensitive=false)
    {
        $head = [
            '工单ID', '工单编号', '工单类型','用户ID','用户手机号','产品SN编码', '勘测情况', '工单状态', '退款状态', '导出结果', '创建时间',
            '完成时间', '买家姓名', '联系人', '联系电话', '省', '市', '区', '联系地址', '期望时间', '家庭环境'
        ];

        $limit  = self::EXPORT_LIMIT;
        $tb     = self::tbName();
        $type   = self::TYPE['EXPLORE'];
        list($where,$params) = $this->__getCondition(0, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phones);

        //导出
        $db       = by::dbMaster();
        $id       = $id ?? 0;
        $id_where = $id > 0 ? " `id` = :id " :  " `id` > :id ";
        $fields   = implode("`,`", $this->tb_fields);
        $sql      = "SELECT `{$fields}` FROM {$tb} WHERE {$id_where} AND {$where} ORDER BY `id` LIMIT {$limit}";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val)
            {
                if (empty($val)) {
                    continue;
                }

                $address = (array)json_decode($val['address'], true);

                $data[] = [
                    'id'            => $val['id'],
                    'order_no'      => $val['order_no']."\t",
                    'type'          => self::TYPE_NAME[$val['type']] ?? '勘探服务',
                    'user_id'       => $val['user_id'],
                    'phone'         => by::Phone()->GetPhoneByUid($val['user_id']) ?? '',
                    'sn'            => $val['sn'],
                    'explore_case'  => self::EXPLORE_CASE_NAME[$val['explore_case']] ?? '',
                    'status'        => self::STATUS_NAME[$val['status']] ?? '',
                    'refund_status' => self::REFUND_STATUS_NAME[$val['refund_status']] ?? '',
                    'is_export'     => self::IS_EXPORT_NAME[$val['is_export']] ?? '',
                    'ctime'         => empty($val['ctime']) ? '' : date('Y-m-d H:i:s', $val['ctime']),
                    'finish_time'   => empty($val['finish_time']) ? '' : date('Y-m-d H:i:s', $val['finish_time']),
                    'nick'          => '\''.(by::users()->getOneByUid($val['user_id'])['nick'] ?? ''),
                    'name'          => '\''.$val['name'],
                    'mobile'        => $val['phone'],
                    'province'      => $address['province'] ?? '',
                    'city'          => $address['city'] ?? '',
                    'area'          => $address['area'] ?? '',
                    'detail'        => $val['detail'],
                    'expect_time'   => empty($val['expect_time']) ? '' : date('Y-m-d', $val['expect_time']),
                    'context_type'  => self::CONTEXT_TYPE_NAME[$val['context_type']] ?? '',
                ];
            }

            if (count($list) < $limit) {
                break;
            }
        }
        !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm','detail'=>'tm','mobile'=>'tm']);
        return $data;
    }

    /**
     * @param $order_no
     * @param $user_msg
     * @param $status
     * @param $s_time
     * @param $e_time
     * @param $refund_status
     * @param $is_export
     * @param $phone
     * @param int $id
     * 安装订单导出
     */
    public function installExport(
        $order_no = '', $user_msg = '', $status = -1, $s_time = 0, $e_time = 0, $refund_status = -1, $is_export = -1, $phone = '', $id = 0, $viewSensitive = false
    )
    {
        $head = [
            '工单ID', '工单编号', '工单类型','用户ID','用户手机号','产品SN编码', '安装情况', '工单状态', '导出结果', '创建时间',
            '完成时间', '买家姓名', '联系人', '联系电话', '省', '市', '区', '联系地址', '期望时间', '家庭环境'
        ];

        $f_name = '安装工单列表' . date('Ymd') . mt_rand(1000, 9999);
        $tb     = self::tbName();
        $limit  = self::EXPORT_LIMIT;
        $type   = self::TYPE['INSTALL'];
        list($where, $params) = $this->__getCondition(0, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phone, $viewSensitive);

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params, $id, $limit, $viewSensitive)
        {
            $db       = by::dbMaster();
            $id       = $id ?? 0;
            $id_where = $id > 0 ? " `id` = :id " :  " `id` > :id ";
            $fields   = implode("`,`", $this->tb_fields);
            $sql      = "SELECT `{$fields}` FROM {$tb} WHERE {$id_where} AND {$where} ORDER BY `id` LIMIT {$limit}";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val)
                {
                    if (empty($val)) {
                        continue;
                    }

                    $address = (array)json_decode($val['address'], true);

                    $data[] = [
                        'id'            => $val['id'],
                        'order_no'      => $val['order_no']."\t",
                        'type'          => self::TYPE_NAME[$val['type']] ?? '勘探服务',
                        'user_id'       => $val['user_id'],
                        'phone'         => by::Phone()->GetPhoneByUid($val['user_id']) ?? '',
                        'sn'            => $val['sn'],
                        'install_case'  => self::INSTALL_CASE_NAME[$val['install_case']] ?? '',
                        'status'        => self::STATUS_NAME[$val['status']] ?? '',
                        'is_export'     => self::IS_EXPORT_NAME[$val['is_export']] ?? '',
                        'ctime'         => empty($val['ctime']) ? '' : date('Y-m-d H:i:s', $val['ctime']),
                        'finish_time'   => empty($val['finish_time']) ? '' : date('Y-m-d H:i:s', $val['finish_time']),
                        'nick'          => by::users()->getOneByUid($val['user_id'])['nick'] ?? '',
                        'name'          => $val['name'],
                        'mobile'        => $val['phone'],
                        'province'      => $address['province'] ?? '',
                        'city'          => $address['city'] ?? '',
                        'area'          => $address['area'] ?? '',
                        'detail'        => $val['detail'],
                        'expect_time'   => empty($val['expect_time']) ? '' : date('Y-m-d', $val['expect_time']),
                        'context_type'  => self::CONTEXT_TYPE_NAME[$val['context_type']] ?? '',
                    ];
                }

                !$viewSensitive &&  $data = Response::responseList($data,['phone'=>'tm','detail'=>'tm']);
                yield $data;

                if (count($list) < $limit) {
                    break;
                }
            }

        }, $f_name);
    }

    public function installExportData($order_no = '', $user_msg = '', $status = -1, $s_time = 0, $e_time = 0, $refund_status = -1, $is_export = -1, $phone = '', $id = 0, $viewSensitive = false)
    {
        $head = [
            '工单ID', '工单编号', '工单类型','用户ID','用户手机号','产品SN编码', '安装情况', '工单状态', '导出结果', '创建时间',
            '完成时间', '买家姓名', '联系人', '联系电话', '省', '市', '区', '联系地址', '期望时间', '家庭环境'
        ];

        $tb     = self::tbName();
        $limit  = self::EXPORT_LIMIT;
        $type   = self::TYPE['INSTALL'];
        list($where, $params) = $this->__getCondition(0, $order_no, $user_msg, $type, $status, $s_time, $e_time, $refund_status, $is_export, $phone, $viewSensitive);

        //导出
        $db       = by::dbMaster();
        $id       = $id ?? 0;
        $id_where = $id > 0 ? " `id` = :id " :  " `id` > :id ";
        $fields   = implode("`,`", $this->tb_fields);
        $sql      = "SELECT `{$fields}` FROM {$tb} WHERE {$id_where} AND {$where} ORDER BY `id` LIMIT {$limit}";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val)
            {
                if (empty($val)) {
                    continue;
                }

                $address = (array)json_decode($val['address'], true);

                $data[] = [
                    'id'            => $val['id'],
                    'order_no'      => $val['order_no']."\t",
                    'type'          => self::TYPE_NAME[$val['type']] ?? '勘探服务',
                    'user_id'       => $val['user_id'],
                    'phone'         => by::Phone()->GetPhoneByUid($val['user_id']) ?? '',
                    'sn'            => $val['sn'],
                    'install_case'  => self::INSTALL_CASE_NAME[$val['install_case']] ?? '',
                    'status'        => self::STATUS_NAME[$val['status']] ?? '',
                    'is_export'     => self::IS_EXPORT_NAME[$val['is_export']] ?? '',
                    'ctime'         => empty($val['ctime']) ? '' : date('Y-m-d H:i:s', $val['ctime']),
                    'finish_time'   => empty($val['finish_time']) ? '' : date('Y-m-d H:i:s', $val['finish_time']),
                    'nick'          => '\''.(by::users()->getOneByUid($val['user_id'])['nick'] ?? ''),
                    'name'          => '\''.$val['name'],
                    'mobile'        => $val['phone'],
                    'province'      => $address['province'] ?? '',
                    'city'          => $address['city'] ?? '',
                    'area'          => $address['area'] ?? '',
                    'detail'        => $val['detail'],
                    'expect_time'   => empty($val['expect_time']) ? '' : date('Y-m-d', $val['expect_time']),
                    'context_type'  => self::CONTEXT_TYPE_NAME[$val['context_type']] ?? '',
                ];
            }
            if (count($list) < $limit) {
                break;
            }
        }
        !$viewSensitive &&  $data = Response::responseList($data,['phone'=>'tm','detail'=>'tm']);
        return $data;

    }

    /**
     * @param string $pid
     * @param string $cid
     * @param string $aid
     * @return array
     * 获取地址
     */
    public function getAddress($pid = 0, $cid = 0, $aid = 0): array
    {
        if ($pid == 0) {
            return [false, "请填写省"];
        }

        if ($cid == 0) {
            return [false, "请填写市"];
        }

        if ($aid == 0) {
            return [false, "请填写区"];
        }

        $list1 = by::model('AreaModel', MAIN_MODULE)->GetList();
        $list2 = by::model('AreaModel', MAIN_MODULE)->GetList($pid);
        $list3 = by::model('AreaModel', MAIN_MODULE)->GetList($cid);

        $list1 = array_column($list1, 'name', 'id');
        $list2 = array_column($list2, 'name', 'id');
        $list3 = array_column($list3, 'name', 'id');

        $data['province'] = $list1[$pid] ?? '';
        $data['city']     = $list2[$cid] ?? '';
        $data['area']     = $list3[$aid] ?? '';

        return [true, $data];
    }

    /**
     * @param $db
     * @param $save
     * @return array
     * 安全保存工单表记录
     */
    private function __safeInsert($db, $save): array
    {
        try{
            if (empty($save)) {
                throw new \Exception("下单失败");
            }

            //todo 插入下单信息
            $db = is_null($db) ? by::dbMaster() : $db;
            $db->createCommand()->insert(self::tbName(), $save)->execute();

            return [true, $save['order_no']];

        } catch (\Exception $e) {

            return [false,$e->getMessage()];
        }
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $next_status
     * @param array $update_data
     * @return array
     * @throws Exception
     * 订单状态结果同步
     */
    public function syncInfo($user_id, $order_no, $next_status, array $update_data = []): array
    {

        if($user_id == 0 || empty($order_no) || !in_array($next_status,self::STATUS)) {
            return [false,"非法参数"];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try{
            $update_data['status'] = $next_status;

            //todo 工单表更新状态
            $ret = $db->createCommand()->update(
                self::tbName(),
                $update_data,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($ret <= 0) {
                throw new MyExceptionModel("无数据更新(1)");
            }

            $trans->commit();

            $this->delCache($order_no);

            return [true, "OK"];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $trans->rollBack();

            return [false,'操作失败'];
        }
    }

    /**
     * @param $time
     * @return string
     * 生成唯一订单号
     */
    public function createOrderNo($time): string
    {
        $ym               = date('ym', intval($time));
        $date            = date("Ymd");
        $unix            = sprintf("%.3f",microtime(true));
        list($now, $mil) = explode('.', $unix);
        $config          = CUtil::getConfig('hostid','common',MAIN_MODULE);
        $hostid          = $config[HOST_NAME] ?? mt_rand(0, 99);
        $hostid          = sprintf("%02d", $hostid);//有些项目服务器数量可能两位数
        $rand            = mt_rand(100, 199);//防止时间回拨，减少碰撞概率
        $second          = $now - strtotime('today');
        $second          = sprintf("%05d", $second);
        $pid             = sprintf("%05d", getmypid());

        $oid             = "{$date}{$second}{$pid}{$hostid}{$rand}{$ym}{$mil}";

        usleep(1000);//保证1ms一个

        return $oid;
    }
}
