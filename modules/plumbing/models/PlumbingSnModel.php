<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\Exception;
use app\modules\main\models\CommModel;

/**
 * Class PlumbingSnModel
 * @package app\modules\plumbing\models
 * 上下水服务sn编码配置
 */
class PlumbingSnModel extends CommModel
{
    CONST EXP = 3600;

    CONST IMAGE_LIMIT = 1;  //图片数量上限

    CONST COUNT_LIMIT = YII_ENV_PROD ? 100 : 100; //sn配置数量上限

    CONST TYPE = [
        'EXPLORE' => 1,
        'INSTALL' => 2,
        'SERVICE' => 3, // 上门服务
    ];

    //sn编码前缀位数
    CONST SN_FIX_DIGITS = [
        'MIN' => 3,
        'MAX' => 12
    ];

    public static function tbName(): string
    {
        return  "`db_dreame`.`t_plumbing_sn`";
    }

    public $tb_fields = [
        'id', 'p_id', 'sn', 'name', 'alias', 'images', 'type', 'product_type', 'ctime'
    ];

    /**
     * @return string
     * sn编码配置列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getPlumbingSnList();
    }

    /**
     * @return string
     * sn集合key
     */
    private function __getSnConfigKey($type): string
    {
        return AppCRedisKeys::getPlumbingSnConfig($type);
    }

    /**
     * @param $id
     * @return string
     * sn编码配置唯一数据缓存KEY
     */
    private function __getInfoByIdKey($id): string
    {
        return AppCRedisKeys::getPlumbingSnInfoById($id);
    }

    /**
     * @param int $id
     * 清理缓存
     */
    private function __delCache(int $id = 0)
    {
        $r_key1 = $this->__getListKey();
        $r_key2 = $this->__getInfoByIdKey($id);

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param int $type
     * 清理sn集合缓存
     */
    private function __delSnConfigCache($type)
    {
        $r_key = $this->__getSnConfigKey($type);

        by::redis('core')->del($r_key);
    }

    /**
     * @param int $id
     * @param int $type
     * @param string $sn
     * @param int $product_type
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $id = 0, int $type = 0, string $sn = '', int $product_type = 0): array
    {
        $where  = "1 = 1";
        $params = [];

        if ($id > 0) {
            $where        .= " and `id`=:id";
            $params[':id'] = $id;
        }

        if ($type > 0) {
            $where          .= " and `type`=:type";
            $params[':type'] = $type;
        }

        if (!empty($sn)) {
            $where        .= " and `sn`=:sn";
            $params[':sn'] = $sn;
        }

        if ($product_type > 0) {
            $where         .= " and `product_type`=:product_type";
            $params[':product_type'] = $product_type;
        }

        return [$where, $params];
    }

    /**
     * @param $type
     * @param string $sn
     * @param int $product_type
     * @return array
     * @throws Exception
     * sn编码配置列表
     */
    public function getConfig($type, string $sn = '',  int $product_type = 0): array
    {
        $type = CUtil::uint($type);
        if($type == 0) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $type, $sn, $product_type);
        $json  = $redis->hGet($r_key, $s_key);
        $ids   = (array)json_decode($json,true);

        if ($json === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition(0, $type, $sn, $product_type);
            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
            $ids = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $ids = !empty($ids) ? $ids : [];

            $redis->hSet($r_key, $s_key, json_encode($ids));
            CUtil::ResetExpire($r_key, empty($ids) ? 0 : self::EXP);
        }

        $list = [];
        foreach ($ids as $v)
        {
            $info = $this->getInfoById($v['id']);
            if(empty($info)){
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param $type
     * @return int
     * @throws Exception
     * sn配置数量
     */
    public function getCount($type): int
    {
        if (empty($type)) {
            return 0;
        }

        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $type);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            $tb                   = self::tbName();
            list($where, $params) = $this->__getCondition(0, $type);
            $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }

        return intval($count);
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 上下水服务sn编码设置唯一数据
     */
    public function getInfoById($id): array
    {
        $id = CUtil::uint($id);
        if($id == 0) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByIdKey($id);
        $json       = $redis->get($redis_key);
        $info       = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $params) = $this->__getCondition($id);
            $sql  = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $info = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        return $info;
    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     * 保存sn配置
     */
    public function snSave($data): array
    {
        if (empty($data)) {
            return [false, "参数错误"];
        }

        if (!in_array($data['type'], self::TYPE)) {
            return [false, "类型不合法"];
        }

        if (empty($data['sn_config'])){
            return [false,'请选择产品'];
        }

        $sn_config = (array)json_decode($data['sn_config'], true);
        foreach ($sn_config as $config) {
            $p_info = by::product()->getOneById($config['p_id']);
            if (empty($p_info)) {
                return [false,'产品信息不存在'];
            }

            $info = [
                'p_id'  => $config['p_id'],
                'sn'    => $p_info['sn'],
                'name'  => $p_info['name'],
                'type'  => $data['type'],
                'ctime' => time()
            ];

            switch ($data['type']) {
                case self::TYPE['INSTALL'] :
                    if (empty($config['alias'])){
                        return [false,'请输入产品名称'];
                    }

                    if (empty($config['images'])){
                        return [false,'请上传产品图片'];
                    }

                    if(substr_count($config['images'],"|") > self::IMAGE_LIMIT) {
                        return [false,"图片数量最多".self::IMAGE_LIMIT."张"];
                    }

                    $info['alias']  = $config['alias'];
                    $info['images'] = $config['images'];

                    break;
                default :
                    break;
            }

            $save[] = $info;
        }

        $columns = array_keys(reset($save));
        $res     = by::dbMaster()->createCommand()->batchInsert(self::tbName(), $columns, $save)->execute();
        if ($res <= 0) {
            return [false, '添加失败'];
        }

        $this->__delCache();
        $this->__delSnConfigCache($data['type']);

        return [true, 'ok'];
    }

    /**
     * @param $type
     * @param $product_type
     * @param $id
     * @param $p_id
     * @param $alias
     * @param $images
     * @return array
     * @throws Exception
     * sn配置编辑
     */
    public function edit($type, $product_type, $id, $p_id, $alias, $images): array
    {
        $p_id = CUtil::uint($p_id);
        if (empty($p_id)){
            return [false,'请选择产品'];
        }

        $p_info = by::product()->getOneById($p_id);
        if (empty($p_info)) {
            return [false,'产品信息不存在'];
        }

        $type = CUtil::uint($type);
        $save = [
            'p_id' => $p_id,
            'sn'   => $p_info['sn'],
            'name' => $p_info['name'],
            'type' => $type,
            'product_type' => $product_type,
        ];

        switch ($type) {
            case self::TYPE['EXPLORE'] :
                break;
            case self::TYPE['INSTALL'] :
            case self::TYPE['SERVICE'] :
                if (empty($alias)){
                    return [false,'请输入产品名称'];
                }

                if (empty($images)){
                    return [false,'请上传产品图片'];
                }

                if(substr_count($images,"|") > self::IMAGE_LIMIT) {
                    return [false,"图片数量最多".self::IMAGE_LIMIT."张"];
                }

                $save['alias']  = $alias;
                $save['images'] = $images;

                break;
            default :
                return [false, "类型不合法"];
        }

        try {
            $id = CUtil::uint($id);
            if ($id) {
                $sn_info = $this->getInfoById($id);
                if (empty($sn_info)){
                    return [false,'数据不存在'];
                }

                $save['utime'] = time();

                $res = by::dbMaster()->createCommand()->update(self::tbName(), $save, ['id' => $id])->execute();
                if ($res <= 0) {
                    throw new MyExceptionModel('修改sn配置失败，请检查选择产品是否重复');
                }

                $this->__delCache($id);
            } else {
                $count = $this->getCount($type);
                if ($count >= self::COUNT_LIMIT) {
                    return [false,"sn配置数量最多".self::COUNT_LIMIT."个"];
                }

                $save['ctime'] = time();

                $res = by::dbMaster()->createCommand()->insert(self::tbName(), $save)->execute();
                if ($res <= 0) {
                    throw new MyExceptionModel('添加sn配置失败，请检查选择产品是否重复');
                }

                $this->__delCache();
                $this->__delSnConfigCache($type);
            }

            return [true, 'ok'];
        } catch (MyExceptionModel $e) {

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            return [false, 'sn配置失败，请检查选择产品是否重复'];
        }
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除sn配置
     */
    public function del($id): array
    {
        $id = CUtil::uint($id);
        if ($id == 0) {
            return [false, '参数错误'];
        }

        $info = $this->getInfoById($id);
        if(empty($info)){
            return [false, 'sn配置信息不存在'];
        }

        $tb                   = self::tbName();
        list($where, $params) = self::__getCondition($id);
        $sql = "DELETE FROM  {$tb} WHERE {$where} LIMIT 1";
        $res = by::dbMaster()->createCommand($sql, $params)->execute();
        if ($res <= 0) {
            return [false, '删除失败'];
        }

        $this->__delCache();
        $this->__delSnConfigCache($info['type']);

        return [true, 'ok'];
    }

    /**
     * @param $type
     * @param $sn
     * @return array
     * @throws Exception
     * sn编码匹配产品
     */
    public function match($type, $sn): array
    {
        $type = CUtil::uint($type);
        if(!in_array($type, by::plumbingOrder()::TYPE)) {
            return [false, '工单类型不合法'];
        }

        if (empty($sn)) {
            return [false, '请填写sn编码'];
        }

        $sn = trim(strval($sn));
        if (!preg_match("/^[a-zA-Z0-9-\/]+$/u", $sn)) {
            return [false, 'sn编码不符合规则'];
        }

        $redis  = by::redis('core');
        $r_key  = $this->__getSnConfig($type); //拿缓存key之时确保缓存中有数据
        $digits = self::SN_FIX_DIGITS;
        $c_list = [];

        //截取不同的位数 循环在缓存中查询
        for ($i = $digits['MIN']; $i <= $digits['MAX']; $i++) {
            $sn_fix = substr($sn, 0, $i);
            $is_m   = $redis->sIsMember($r_key, $sn_fix);

            if ($is_m) {
                $config_list = $this->getConfig($type, $sn_fix);
                $c_list      = array_merge($c_list, $config_list);
            }
        }

        $ids = array_column($c_list,'id','id');

        //根据查询结果返回三种状态：未查询到 查询到一个 查询到多个
        switch (true) {
            case empty($ids) :
                return [false,'该机型暂不支持此服务'];
            case count($ids) > 1 :
                $list = [];

                foreach ($ids as $id){
                    $info = $this->getInfoById($id);
                    !empty($info) && $list[] = $info;
                }

                if (empty($list)) {
                    return [false, '该机型暂不支持此服务'];
                }

                return [true, $list];
            default :
                $id = current($ids);
                if (empty($id)){
                    return [false, '该机型暂不支持此服务'];
                }

                return [true, ['id' => $id]];
        }
    }

    /**
     * @param $type
     * @return string
     * @throws Exception
     * 获取集合key(确保集合中有数据)
     */
    private function __getSnConfig($type): string
    {
        $page_size = 50;
        $redis     = by::redis('core');
        $r_key     = $this->__getSnConfigKey($type);
        $sn_card   = by::redis('core')->SCARD($r_key);

        if($sn_card <= 0) {
            //保证只有一次db查询
            $unique_key = CUtil::getAllParams(__FUNCTION__, $type);
            list($free) = self::ReqAntiConcurrency(0, $unique_key, 5, 'EX');
            if ($free) {
                $tb = self::tbName();
                list($where, $params) = $this->__getCondition(0, $type);

                //sn配置数量
                $count = $this->getCount($type);

                //分页刷入缓存
                $pages    = CUtil::getPaginationPages($count, $page_size);
                $last_id  = 0;
                $sql_temp = "SELECT `sn`,`id` FROM {$tb} WHERE {$where}";
                for ($page = 1; $page <= $pages; $page++) {
                    $list_sql = sprintf($sql_temp, $last_id);
                    $rows     = by::dbMaster()->createCommand($list_sql, $params)->queryAll();
                    if (empty($rows)) {
                        break;
                    }

                    $end     = end($rows);
                    $last_id = $end['id'];

                    $sn = array_column($rows,"sn");
                    $redis->sAdd($r_key,...$sn);
                }

                $redis->expire($r_key, 86400*7);
            } else {
                usleep(200000);
            }
        }

        return $r_key;
    }
}
