<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use yii\db\Exception;
use app\modules\main\models\CommModel;

/**
 * Class PlumbingSnModel
 * @package app\modules\plumbing\models
 * 上下水评价
 */
class PlumbingCommentModel extends CommModel
{
    CONST EXP = 3600;

    CONST PAGE_SIZE = YII_ENV_PROD ? 20 : 3;

    CONST IMAGE_LIMIT = 5;  //图片数量上限

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_plumbing_comment`";
    }

    public $tb_fields = [
        'id', 'order_no', 'user_id', 'phone', 'tags', 'overall_grade', 'speed_grade', 'manner_grade', 'specialty_grade'
        , 'brand_grade', 'content', 'images', 'type', 'ctime', 'manner_grade'
    ];

    /**
     * @return string
     * 评价列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getPlumbingCommentList();
    }

    /**
     * @param $order_no
     * @return string
     * 评价唯一数据缓存KEY
     */
    private function __getInfoByOrderNoKey($order_no): string
    {
        return AppCRedisKeys::getPlumbingCoInfoByOrNo($order_no);
    }

    /**
     * @param string $order_no
     * 清理缓存
     */
    private function __delCache(string $order_no = '')
    {
        $r_key1 = $this->__getListKey();
        $r_key2 = $this->__getInfoByOrderNoKey($order_no);

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param string $order_no
     * @param string $user_mag
     * @param int $type
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(string $order_no = '', string $user_mag = '', int $type = 0): array
    {
        $where  = "1 = 1";
        $params = [];

        if (!empty($order_no)) {
            $where              .= " AND `order_no`=:order_no";
            $params[':order_no'] = $order_no;
        }

        if(!empty($user_mag)) {
            if (strlen($user_mag) == 11) {
                $where           .= " AND `phone`=:phone";
                $params[':phone'] = $user_mag;
            } else {
                $where             .= " AND `user_id`=:user_id";
                $params[':user_id'] = $user_mag;
            }
        }

        if ($type > 0) {
            $where          .= " and `type`=:type";
            $params[':type'] = $type;
        }

        return [$where, $params];
    }

    /**
     * @param $user_id
     * @param $data
     * @return array
     * @throws Exception
     * 评价记录
     */
    public function commentSave($user_id, $data): array
    {
        if (empty($data)) {
            return [false,"参数错误"];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if (empty($data['order_no'])) {
            return [false, "工单编号不能为空"];
        }

        $pc_info= by::plumbingComment()->getInfoByOrderNo($data['order_no']);
        if (!empty($pc_info)) {
            return [false, "该工单已评价"];
        }

        $type = isset($data['type']) ? CUtil::uint($data['type']) : 0;
        if (!in_array($type, by::plumbingOrder()::TYPE)) {
            return [false, "类型不合法"];
        }

        $po_info= by::plumbingOrder()->getInfoByOrderNo($data['order_no']);
        if (empty($po_info)) {
            return [false, "工单信息不存在"];
        }

        if ($po_info['status'] != by::plumbingOrder()::STATUS['FINISH']) {
            return [false, "工单状态异常"];
        }

        $overall_grade = isset($data['overall_grade']) ? CUtil::uint($data['overall_grade']) : 0;
        if ($overall_grade == 0) {
            return [false, "请对本次服务总体评价"];
        }

        $speed_grade = isset($data['speed_grade']) ? CUtil::uint($data['speed_grade']) : 0;
        if ($speed_grade == 0) {
            return [false, "请对服务人员上门速度评价"];
        }

        $manner_grade = isset($data['manner_grade']) ? CUtil::uint($data['manner_grade']) : 0;
        if ($manner_grade == 0) {
            return [false, "请对服务人员服务态度评价"];
        }

        $specialty_grade = isset($data['specialty_grade']) ? CUtil::uint($data['specialty_grade']) : 0;
        if ($specialty_grade == 0) {
            return [false, "请对服务人员专业能力评价"];
        }

        if (empty($data['brand_grade'])) {
            return [false, "请对品牌服务打分"];
        }

        $tags     = explode("|", $data['tags'] ?? '');
        $tag_name = [];
        foreach ($tags as $id) {
            $ct_info = by::commentTag()->getInfoById($id);
            if (empty($ct_info)) {
                continue;
            }

            $tag_name[] = $ct_info['name'];
        }

        $tag_name = implode('，', $tag_name);

        $save = [
            'order_no'        => $data['order_no'],
            'user_id'         => $user_id,
            'phone'           => $po_info['phone'],
            'tags'            => $tag_name,
            'overall_grade'   => $data['overall_grade'],
            'speed_grade'     => $data['speed_grade'],
            'manner_grade'    => $data['manner_grade'],
            'specialty_grade' => $data['specialty_grade'],
            'brand_grade'     => $data['brand_grade'],
            'content'         => $data['content'] ?? '',
            'images'          => $data['images'] ?? '',
            'type'            => $data['type'],
            'ctime'           => time()
        ];

        try {
            //todo 记录评价信息
            $ret = by::dbMaster()->createCommand()->insert(self::tbName(), $save)->execute();
            if ($ret <= 0) {
                throw new MyExceptionModel('评价失败，请检查该订单是否已评价');
            }

            self::ReqAntiConcurrency($user_id, $unique_key, 0,'DEL');

            $this->__delCache();

            return [true, 'ok'];
        } catch (MyExceptionModel $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'plumbing-comment.err.');

            return [false, $e->getMessage()];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'plumbing-comment.err.');

            return [false, '评价失败，请检查该订单是否已评价'];
        }
    }

    /**
     * @param $page
     * @param $page_size
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @return array
     * @throws Exception
     * 评价列表
     */
    public function getList($page, $page_size, string $order_no = '', string $user_msg = '', int $type = 0): array
    {
        $redis     = by::redis('core');
        $r_key     = self::__getListKey();
        $s_key     = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $order_no, $user_msg, $type);
        $json      = $redis->hGet($r_key, $s_key);
        $order_nos = (array)json_decode($json,true);

        if ($json === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition($order_no, $user_msg, $type);
            list($offset) = CUtil::pagination($page, $page_size);
            $sql          = "SELECT `order_no` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size} ";
            $order_nos    = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $order_nos    = !empty($order_nos) ? $order_nos : [];

            $redis->hSet($r_key, $s_key, json_encode($order_nos));
            CUtil::ResetExpire($r_key, empty($order_nos) ? 0 : self::EXP);
        }

        $list = [];
        foreach ($order_nos as $v)
        {
            $info = $this->getInfoByOrderNo($v['order_no']);
            if(empty($info)){
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @return int
     * @throws Exception
     * 评价数量
     */
    public function getCount(string $order_no = '', string $user_msg = '', int $type = 0): int
    {
        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $order_no, $user_msg, $type);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition($order_no, $user_msg, $type);
            $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }

        return intval($count);
    }

    /**
     * @param $order_no
     * @param bool $can_user
     * @return array
     * @throws Exception
     * 评价唯一数据
     */
    public function getInfoByOrderNo($order_no, bool $can_user = false): array
    {
        if(empty($order_no)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getInfoByOrderNOKey($order_no);
        $json  = $redis->get($r_key);
        $info  = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $params) = $this->__getCondition($order_no);
            $sql  = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info = by::dbMaster()->createCommand($sql, $params)->queryOne();
            $info = empty($info) ? [] : $info;

            $redis->set($r_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        if(!empty($info)) {
            if ($can_user) {
                $info['nick'] = by::users()->getOneByUid($info['user_id'])['nick']               ?? "";
                $info['name'] = by::plumbingOrder()->getInfoByOrderNo($info['order_no'])['name'] ?? "";
            }
        }

        return $info;
    }

    public function export(string $order_no = '', string $user_msg = '', int $type = 0, $viewSensitive = false)
    {
        $head = [
            '工单编号', '工单类型','本次服务总体评价','服务人员上门速度','服务人员服务态度', '服务人员专业能力', '品牌服务', '标签', '评价',
            '用户ID', '用户昵称', '联系人姓名', '联系人手机号'
        ];

        $f_name = '评论中心列表' . date('Ymd') . mt_rand(1000, 9999);
        $tb     = self::tbName();
        list($where, $params) = $this->__getCondition($order_no, $user_msg, $type);

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params, $viewSensitive)
        {
            $db     = by::dbMaster();
            $id     = 0;
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val)
                {
                    if (empty($val)) {
                        continue;
                    }

                    $data[] = [
                        'order_no'        => $val['order_no']."\t",
                        'type'            => by::plumbingOrder()::TYPE_NAME[$val['type']],
                        'overall_grade'   => $val['overall_grade'],
                        'speed_grade'     => $val['speed_grade'],
                        'manner_grade'    => $val['manner_grade'],
                        'specialty_grade' => $val['specialty_grade'],
                        'brand_grade'     => $val['brand_grade'],
                        'tags_name'       => $val['tags'],
                        'content'         => $val['content'],
                        'user_id'         => $val['user_id'],
                        'nick'            => by::users()->getOneByUid($val['user_id'])['nick'] ?? '',
                        'name'            => by::plumbingOrder()->getInfoByOrderNo($val['order_no'])['name'] ?? '',
                        'phone'           => $val['phone'],
                    ];
                }
                !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);

                yield $data;
            }

        }, $f_name);
    }

    public function exportData(string $order_no = '', string $user_msg = '', int $type = 0, $viewSensitive = false)
    {
        $head = [
            '工单编号', '工单类型','本次服务总体评价','服务人员上门速度','服务人员服务态度', '服务人员专业能力', '品牌服务', '标签', '评价',
            '用户ID', '用户昵称', '联系人姓名', '联系人手机号'
        ];

        $tb     = self::tbName();
        list($where, $params) = $this->__getCondition($order_no, $user_msg, $type);

        //导出
        $db     = by::dbMaster();
        $id     = 0;
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val)
            {
                if (empty($val)) {
                    continue;
                }

                $data[] = [
                    'order_no'        => $val['order_no']."\t",
                    'type'            => by::plumbingOrder()::TYPE_NAME[$val['type']],
                    'overall_grade'   => $val['overall_grade'],
                    'speed_grade'     => $val['speed_grade'],
                    'manner_grade'    => $val['manner_grade'],
                    'specialty_grade' => $val['specialty_grade'],
                    'brand_grade'     => $val['brand_grade'],
                    'tags_name'       => $val['tags'],
                    'content'         => $val['content'],
                    'user_id'         => $val['user_id'],
                    'nick'            => '\''.(by::users()->getOneByUid($val['user_id'])['nick'] ?? ''),
                    'name'            => '\''.(by::plumbingOrder()->getInfoByOrderNo($val['order_no'])['name'] ?? ''),
                    'phone'           => $val['phone'],
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);

        return $data;
    }
}
