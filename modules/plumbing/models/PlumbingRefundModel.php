<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use yii\db\Exception;
use app\modules\main\models\CommModel;

/**
 * Class PlumbingRefundModel
 * @package app\modules\plumbing\models
 * 上下水服务退款模型
 */
class PlumbingRefundModel extends CommModel
{
    CONST EXP         = 3600;

    CONST IMAGE_LIMIT = 5; //图片数量上限

    CONST PAGE_SIZE   = YII_ENV_PROD ? 20 : 3;

    CONST PASS_EXPIRE = 60; //可同意退款时间窗口

    CONST TYPE = [
        'CANCEL'              => 1,
        'BUY_REFUND'          => 2,
        'EXPLORE_FAIL_REFUND' => 3
    ];

    CONST TYPE_NAME = [
        1 => '我要取消',
        2 => '已购机退款',
        3 => '勘测失败退款'
    ];

    CONST SOURCE = [
        'INDEX' => 1,
        'ADMIN' => 2
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_goods`.`t_plumbing_refund`";
    }

    public $tb_fields = [
        'id', 'refund_no', 'order_no', 'user_id', 'name', 'phone', 'price', 'describe', 'images', 'sn', 'buy_order_no', 'channel', 'type',
        'order_type', 'ostatus', 'status', 'refuse_reason', 'rtime', 'utime', 'ctime'
    ];

    /**
     * @return string
     * 退款订单列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getPlumbingRefundList();
    }

    /**
     * @param $refund_no
     * @return string
     * 退款订单唯一数据缓存KEY
     */
    private function __getInfoByReNoKey($refund_no): string
    {
        return AppCRedisKeys::getPlumbingReInfoByReNo($refund_no);
    }

    /**
     * 清理缓存
     */
    public function delCache($refund_nos = '')
    {
        $r_key1 = $this->__getListKey();

        if (is_array($refund_nos)) {
            foreach($refund_nos as $refund_no) {
                $r_key2 = $this->__getInfoByReNoKey($refund_no);
            }
        } else {
            $r_key2 = $this->__getInfoByReNoKey($refund_nos);
        }

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param string $refund_no
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @param int $id
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(string $refund_no = '', string $order_no = '', string $user_msg = '', int $type = 0, int $status = 0, int $s_time = 0, int $e_time = 0, int $id = 0): array
    {
        $where  = "1 = 1";
        $params = [];

        if (!empty($refund_no)) {
            $where        .= " and `refund_no`=:refund_no";
            $params[':refund_no'] = $refund_no;
        }

        if (!empty($order_no)) {
            $where              .= " and `order_no`=:order_no";
            $params[':order_no'] = $order_no;
        }

        if (!empty($user_msg)) {
            if (strlen($user_msg) == 11) {
                $where           .= " AND `phone` = :phone";
                $params[':phone'] = $user_msg;
            } else {
                $user_id            = CUtil::uint($user_msg);
                $where             .= " AND `user_id` = :user_id";
                $params[':user_id'] = $user_id;
            }

        }

        if ($type > 0) {
            $where          .= " and `type`=:type";
            $params[':type'] = $type;
        }

        if ($status > 0) {
            $where            .= " and `status`=:status";
            $params[':status'] = $status;
        }

        if (!empty($s_time) && !empty($e_time)) {
            $where            .= " AND `ctime` BETWEEN :s_time AND :e_time";
            $params[":s_time"] = $s_time;
            $params[":e_time"] = $e_time;

            /*$end_time = strtotime(date("Y-m-d", $e_time));
            if ($e_time == $end_time) {
                $params[":e_time"] = $e_time + 86399;
            }*/
        }

        return [$where, $params];
    }

    /**
     * @param $source
     * @param int $page
     * @param int $page_size
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @return array
     * @throws Exception
     * 退款订单列表
     */
    public function getList(
        $source, int $page = 1, int $page_size = self::PAGE_SIZE, string $order_no = '', string $user_msg = '',
        int $type = 0, int $status = -1, int $s_time = 0, int $e_time = 0
    ): array
    {
        $source = CUtil::uint($source);
        if ($source == 0) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = self::__getListKey();
        $s_key      = CUtil::getAllParams(__FUNCTION__, $source, $page, $page_size, $order_no, $user_msg, $type, $status, $s_time, $e_time);

        $aJson      = $redis->hGet($r_key, $s_key);
        $refund_nos = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition('', $order_no, $user_msg, $type, $status, $s_time, $e_time);

            if ($source == 1) {
                $sql = "SELECT `refund_no` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
            } else {
                list($offset) = CUtil::pagination($page, $page_size);
                $sql          = "SELECT `refund_no` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size} ";
            }

            $refund_nos = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $refund_nos = !empty($refund_nos) ? $refund_nos : [];

            $redis->hSet($r_key, $s_key, json_encode($refund_nos));
            CUtil::ResetExpire($r_key, empty($refund_nos) ? 0 : self::EXP);
        }

        $list = [];
        foreach ($refund_nos as $v)
        {
            $info = by::plumbingRefund()->getInfoByRefundNo($v['refund_no']);
            if(empty($info)){
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param $refund_no
     * @return array
     * @throws Exception
     * 退款订单唯一数据
     */
    public function getInfoByRefundNo($refund_no): array
    {
        if (empty($refund_no)) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByReNoKey($refund_no);
        $json       = $redis->get($redis_key);
        $info       = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $param) = $this->__getCondition($refund_no);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql,$param)->queryOne();
            $info   = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        if(!empty($info)) {
            $po_info              = by::plumbingOrder()->getInfoByOrderNo($info['order_no']);
            $info['price']        = $po_info['price']        ?? '';
            $info['address']      = $po_info['address']      ?? '';
            $info['detail']       = $po_info['detail']       ?? '';
            $info['explore_case'] = $po_info['explore_case'] ?? '';
            $info['order_status'] = $po_info['status']       ?? '';
            $info['refund_status']= $po_info['refund_status']?? '';
        }

        return $info;
    }

    /**
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * @return int
     * @throws Exception
     * 退款订单数量
     */
    public function getCount(string $order_no = '', string $user_msg = '', int $type = 0, int $status = 0, int $s_time = 0, int $e_time = 0): int
    {
        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $order_no, $user_msg, $type, $status, $s_time, $e_time);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition('', $order_no, $user_msg, $type, $status, $s_time, $e_time);
            $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }

        return intval($count);
    }

    /**
     * @param int $user_id
     * @param array $data
     * @return array
     * @throws Exception
     * 申请退款
     */
    public function applyRefund(int $user_id, array $data): array
    {
        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        $type = isset($data['type']) ? CUtil::uint($data['type']) : 0;
        if (!in_array($type, self::TYPE) ) {
            return [false, '退款类型不合法'];
        }

        $images = $data['images'] ?? "";
        if(substr_count($images,"|") > self::IMAGE_LIMIT) {
            return [false,"图片数量最多".self::IMAGE_LIMIT."张"];
        }

        $describe = $data['describe'] ?? "";
        if (mb_strlen($describe) > 100) {
            return [false, '描述最长100字'];
        }


        $plumbing_order = by::plumbingOrder();
        $order_no      = $data['order_no'] ?? '';
        $p_o_info      = $plumbing_order->getInfoByOrderNo($order_no);
        if (empty($p_o_info)) {
            return [false, '工单信息异常'];
        }

        $ostatus = $p_o_info['status'] ?? 0;
        if (!empty($ostatus) && !in_array($ostatus, $plumbing_order::STATUS)) {
            return [false, '状态不合法'];
        }

        $refund_status = $p_o_info['refund_status'] ?? 0;
        if ($refund_status == $plumbing_order::REFUND_STATUS['WAIT_AUDIT']) {
            return [false, '审核中'];
        }

        if ($refund_status == $plumbing_order::REFUND_STATUS['AUDIT_PASS']) {
            return [false, '退款中'];
        }

        if ($refund_status == $plumbing_order::REFUND_STATUS['REFUND_SUCCESS']
        ) {
            return [false, '已退款成功'];
        }

        //生成退款单号
        $ctime     = intval(START_TIME);
        $refund_no = 'T'.by::plumbingOrder()->createOrderNo($ctime);

        //插入退款表数据
        $save = [
            'refund_no'  => $refund_no,
            'order_no'   => $order_no,
            'user_id'    => $user_id,
            'name'       => $p_o_info['name']  ?? '',
            'phone'      => $p_o_info['phone'] ?? '',
            'type'       => $type,
            'order_type' => $p_o_info['type']  ?? 0,
            'describe'   => $describe,
            'images'     => $images,
            'ostatus'    => $ostatus,
            'utime'      => $ctime,
            'ctime'      => $ctime
        ];

        switch ($type) {
            case self::TYPE['EXPLORE_FAIL_REFUND']:
            case self::TYPE['CANCEL'] :
                break;
            case self::TYPE['BUY_REFUND'] :
                $sn = isset($data['sn']) ? trim(strval($data['sn'])) : '' ;
                list($status, $ret) = by::plumbingSn()->match(by::plumbingOrder()::TYPE['EXPLORE'], $sn);
                if (!$status) {
                    return [false, $ret];
                }

                $save['sn']           = $sn;
                $save['buy_order_no'] = $data['buy_order_no'] ?? '';
                $save['channel']      = $data['channel']      ?? '';

                break;
            default :
                return [false, '退款类型不合法'];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $res = $db->createCommand()->insert(self::tbName(), $save)->execute();
            if($res <= 0) {
                throw new Exception("无数据更新（1）");
            }

            //更新工单表退款状态
            $update_data = [
                'refund_status' => $plumbing_order::REFUND_STATUS['WAIT_AUDIT'],
                'update_time'   => $ctime,
            ];
            $res = $db->createCommand()->update(
                by::plumbingOrder()::tbName(),
                $update_data,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($res <= 0) {
                throw new Exception("无数据更新（2）");
            }

            $trans->commit();

            //todo 清缓存
            by::plumbingOrder()->delCache($order_no);
            $this->delCache();

            return [true, $refund_no];
        } catch (\Exception $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'plumbing-refund-apply.err.');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     * 退款审核
     */
    public function audit($data): array
    {
        $plumbing_order = by::plumbingOrder();
        $refund_no      = $data['refund_no'] ?? '';
        $order_no       = $data['order_no']  ?? '';

        if (empty($refund_no) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = self::ReqAntiConcurrency($refund_no, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        $refuse_reason = $data['refuse_reason'] ?? '';
        if (mb_strlen($refuse_reason) > 100) {
            return [false, '拒绝原因最长100字'];
        }

        $status = isset($data['status']) ? CUtil::uint($data['status']) : 0;
        if (!in_array($status, [$plumbing_order::REFUND_STATUS['AUDIT_PASS'], $plumbing_order::REFUND_STATUS['AUDIT_REFUSE']])) {
            return [false, '退款状态不合法'];
        }

        $refund_info = $this->getInfoByRefundNo($refund_no);
        if (empty($refund_info)) {
            return [false, '退款工单异常'];
        }

        $p_o_info = $plumbing_order->getInfoByOrderNo($order_no);
        if (empty($p_o_info)) {
            return [false, '工单异常'];
        }

        if ($p_o_info['refund_status'] != $plumbing_order::REFUND_STATUS['WAIT_AUDIT']) {
            return [false, '工单状态异常'];
        }

        if ($p_o_info['refund_status'] == $plumbing_order::REFUND_STATUS['AUDIT_PASS']) {
            return [false, '退款中'];
        }

        if ($p_o_info['refund_status'] == $plumbing_order::REFUND_STATUS['REFUND_SUCCESS']
        ) {
            return [false, '已退款成功'];
        }

        //记录生成后1分钟内无法操作同意退款
        $edit_time = $refund_info['ctime'] + self::PASS_EXPIRE - time();
        if ($status == $plumbing_order::REFUND_STATUS['AUDIT_PASS'] && $edit_time > 0) {
            return [false, "请{$edit_time}秒后再操作"];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $user_id     = $refund_info['user_id'];
            $refund_fee  = by::Gtype0()->totalFee($p_o_info['price'] ?? 0);
            $next_st     = $status;
            $ctime       = time();
            $update_data = ['utime' => $ctime];

            if ($status == $plumbing_order::REFUND_STATUS['AUDIT_PASS']) {
                if (bccomp($refund_fee, 0) == 0) {
                    $ctime                = time();
                    $next_st              = by::plumbingOrder()::REFUND_STATUS['REFUND_SUCCESS'];
                    $update_data['rtime'] = $ctime;
                    $update_data['price'] = $refund_fee;
                }

                $type = $refund_info['type'] ?? 0;
                if ($type == self::TYPE['CANCEL']) {
                    $update_data['ostatus'] = $plumbing_order::STATUS['CANCEL'];
                }
            } else {
                $update_data['refuse_reason'] = $refuse_reason;
            }

            list($s, $m) = $this->syncInfo($user_id, $refund_no, $order_no, $next_st, $update_data);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 微信执行退款
            if (bccomp($refund_fee, 0) != 0 && $status == $plumbing_order::REFUND_STATUS['AUDIT_PASS']) {
                //todo 获取最新支付方式，选择退款渠道
                $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
                if(empty($aLog)) {
                    throw new MyExceptionModel('无法获取支付流水，数据有误！');
                }
                $payType = $aLog['pay_type']??'';
                if($payType == by::Omain()::PAY_BY_WX || $payType == by::Omain()::PAY_BY_WX_APP){
                    //todo 微信执行退款
                    list($s, $m) = by::WxPay()->refund($user_id, $order_no, $refund_no, false, by::WxPay()::SOURCE['PLUMBING'], $payType);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }elseif ($payType == by::Omain()::PAY_BY_WX_H5){
                    //todo 微信H5执行退款
                    list($s, $m) = by::wxH5Pay()->refund($user_id, $order_no, $refund_no, false, by::wxH5Pay()::SOURCE['PLUMBING']);
                    if (!$s) {
                        throw new MyExceptionModel($m);
                    }
                }else{
                    throw new MyExceptionModel('支付渠道有误！无法退款');
                }
            }

            $trans->commit();

            return [true, $p_o_info['price']];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'plumbing-refund-audit.err');

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'plumbing-refund-audit.err');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @param $order_no
     * @param $next_st
     * @param array $update_data
     * @return array
     * @throws Exception
     * 退款订单状态结果同步
     */
    public function syncInfo($user_id, $refund_no, $order_no, $next_st, array $update_data = []): array
    {
        if(empty($user_id) || empty($refund_no) || !in_array($next_st, by::plumbingOrder()::REFUND_STATUS)) {
            return [false,"非法参数"];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try{
            //更新工单表退款状态
            $po_data = ['refund_status' => $next_st, 'update_time' => $update_data['utime']];
            !empty($update_data['ostatus']) && $po_data['status'] = $update_data['ostatus'];

            $res = $db->createCommand()->update(
                by::plumbingOrder()::tbName(),
                $po_data,
                ['order_no' => $order_no, 'user_id' => $user_id]
            )->execute();
            if($res <= 0) {
                throw new \Exception("无数据更新(1)");
            }

            //更新退款表状态
            unset($update_data['ostatus']);
            $update_data['status'] = $next_st;

            $res = $db->createCommand()->update(
                self::tbName(),
                $update_data,
                ['refund_no' => $refund_no, 'user_id' => $user_id]
            )->execute();
            if($res <= 0) {
                throw new \Exception("无数据更新(2)");
            }

            $trans->commit();

            //todo 清理退款表缓存
            by::plumbingOrder()->delCache($order_no);
            $this->delCache($refund_no);

            return [true,"OK"];
        } catch (\Exception $e) {
            $trans->rollBack();

            return [false,$e->getMessage()];
        }
    }

    /**
     * @param string $order_no
     * @param string $user_msg
     * @param int $type
     * @param int $status
     * @param int $s_time
     * @param int $e_time
     * 退款订单导出
     */
    public function export(string $order_no = '', string $user_msg = '', int $type = 0, int $status = 0, int $s_time = 0, int $e_time = 0,$viewSensitive = false)
    {
        $head = ['工单编号', '退款编号','订单类型','退款金额','用户ID', '用户手机号', '退款时间', '退款前状态', '退款状态', '退款原因'];

        $f_name = '退款工单列表' . date('Ymd') . mt_rand(1000, 9999);
        $tb     = self::tbName();
        list($where,$params) = $this->__getCondition('', $order_no, $user_msg, $type, $status, $s_time, $e_time);

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params, $viewSensitive)
        {
            $db     = by::dbMaster();
            $id     = 0;
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val)
                {
                    if (empty($val)) {
                        continue;
                    }

                    $data[] = [
                        'order_no'   => $val['order_no']."\t",
                        'refund_no'  => $val['refund_no']."\t",
                        'order_type' => by::plumbingOrder()::TYPE_NAME[$val['order_type']],
                        'price'      => by::Gtype0()->totalFee($val['price'], 1),
                        'user_id'    => $val['user_id'],
                        'phone'      => $val['phone'],
                        'rtime'      => empty($val['rtime']) ? '' : date('Y-m-d', $val['rtime']),
                        'ostatus'    => by::plumbingOrder()::STATUS_NAME[$val['ostatus']],
                        'status'     => by::plumbingOrder()::REFUND_STATUS_NAME[$val['status']],
                        'type'       => by::plumbingRefund()::TYPE_NAME[$val['type']]
                    ];
                }
                !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);

                yield $data;
            }

        }, $f_name);
    }

    public function exportData(string $order_no = '', string $user_msg = '', int $type = 0, int $status = 0, int $s_time = 0, int $e_time = 0,$viewSensitive = false)
    {
        $head = ['工单编号', '退款编号','订单类型','退款金额','用户ID', '用户手机号', '退款时间', '退款前状态', '退款状态', '退款原因'];

        $tb     = self::tbName();
        list($where,$params) = $this->__getCondition('', $order_no, $user_msg, $type, $status, $s_time, $e_time);

        //导出
        $db     = by::dbMaster();
        $id     = 0;
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val)
            {
                if (empty($val)) {
                    continue;
                }

                $data[] = [
                    'order_no'   => $val['order_no']."\t",
                    'refund_no'  => $val['refund_no']."\t",
                    'order_type' => by::plumbingOrder()::TYPE_NAME[$val['order_type']],
                    'price'      => by::Gtype0()->totalFee($val['price'], 1),
                    'user_id'    => $val['user_id'],
                    'phone'      => $val['phone'],
                    'rtime'      => empty($val['rtime']) ? '' : date('Y-m-d', $val['rtime']),
                    'ostatus'    => by::plumbingOrder()::STATUS_NAME[$val['ostatus']],
                    'status'     => by::plumbingOrder()::REFUND_STATUS_NAME[$val['status']],
                    'type'       => by::plumbingRefund()::TYPE_NAME[$val['type']]
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data,['phone'=>'tm']);
        return $data;
    }
}
