<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\Exception;
use app\modules\main\models\CommModel;

/**
 * Class PlumbingSnModel
 * @package app\modules\plumbing\models
 * 上下水评价标签配置模型
 */
class CommentTagModel extends CommModel
{
    CONST EXP = 3600;

    CONST COUNT_LIMIT = 10; //标签配置数量上限

    public static function tbName(): string
    {
        return  "`db_dreame`.`t_comment_tag`";
    }

    public $tb_fields = [
        'id', 'name', 'ctime'
    ];

    /**
     * @return string
     * 标签配置列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getCommentTagList();
    }

    /**
     * @param $id
     * @return string
     * 标签配置唯一数据缓存KEY
     */
    private function __getInfoByIdKey($id): string
    {
        return AppCRedisKeys::getCommentTagInfoById($id);
    }

    /**
     * @param int $id
     * 清理缓存
     */
    private function __delCache(int $id = 0)
    {
        $r_key1 = $this->__getListKey();
        $r_key2 = $this->__getInfoByIdKey($id);

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param int $id
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $id = 0): array
    {
        $where  = "1 = 1";
        $params = [];

        if ($id > 0) {
            $where        .= " and `id`=:id";
            $params[':id'] = $id;
        }

        return [$where, $params];
    }

    /**
     * @return array
     * @throws Exception
     * 标签配置列表
     */
    public function getConfig(): array
    {
        $tb                   = self::tbName();
        list($where, $params) = self::__getCondition();
        $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
        $ids = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $ids = !empty($ids) ? $ids : [];

        $list = [];
        foreach ($ids as $v)
        {
            $info = $this->getInfoById($v['id']);
            if(empty($info)){
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @return int
     * @throws Exception
     * 标签配置数量
     */
    public function getCount(): int
    {
        $tb                   = self::tbName();
        list($where, $params) = $this->__getCondition();
        $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
        $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();

        return empty($count) ? 0 : intval($count);
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 标签配置唯一数据
     */
    public function getInfoById($id): array
    {
        $id = CUtil::uint($id);
        if($id == 0) {
            return [];
        }

        $tb                  = self::tbName();
        $fields              = implode("`,`", $this->tb_fields);
        list($where, $params) = $this->__getCondition($id);
        $sql  = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
        $info = by::dbMaster()->createCommand($sql, $params)->queryOne();

        return empty($info) ? [] : $info;
    }

    /**
     * @param $id
     * @param $name
     * @return array
     * 标签配置编辑
     */
    public function edit($id, $name): array
    {
        if (empty($name)){
            return [false,'请输入标签名称'];
        }

        try {
            $save = ['name' => $name, 'ctime' => time()];

            $id   = CUtil::uint($id);
            if ($id) {
                $tag_info = $this->getInfoById($id);
                if (empty($tag_info)){
                    return [false,'数据不存在'];
                }

                $res = by::dbMaster()->createCommand()->update(self::tbName(), $save, ['id' => $id])->execute();
                if ($res <= 0) {
                    throw new MyExceptionModel('修改标签配置失败');
                }

                $this->__delCache($id);
            } else {
                $count = $this->getCount();
                if ($count >= self::COUNT_LIMIT) {
                    return [false, "标签配置数量最多".self::COUNT_LIMIT."个"];
                }

                $res = by::dbMaster()->createCommand()->insert(self::tbName(), $save)->execute();
                if ($res <= 0) {
                    throw new MyExceptionModel('添加标签配置失败，请检查标签名称是否重复');
                }

                $this->__delCache();
            }

            return [true, 'ok'];
        } catch (MyExceptionModel $e) {

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            return [false, '标签配置失败，请检查标签名称是否重复'];
        }
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除标签配置
     */
    public function del($id): array
    {
        $id = CUtil::uint($id);
        if ($id == 0) {
            return [false, '参数错误'];
        }

        $info = $this->getInfoById($id);
        if(empty($info)){
            return [false, '标签配置信息不存在'];
        }

        $tb                   = self::tbName();
        list($where, $params) = self::__getCondition($id);
        $sql = "DELETE FROM  {$tb} WHERE {$where} LIMIT 1";
        $res = by::dbMaster()->createCommand($sql, $params)->execute();
        if ($res <= 0) {
            return [false, '删除失败'];
        }

        $this->__delCache();

        return [true, 'ok'];
    }
}