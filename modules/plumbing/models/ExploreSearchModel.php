<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class ExploreSearchModel extends CommModel
{
    CONST EXP       = 3600;

    CONST PAGE_SIZE = 25;

    CONST IS_AREA = [
        0 => '否',
        1 => '是'
    ];

    CONST TYPE = [
        'EXPLORE' => 1,
        'INSTALL' => 2,
        'SERVICE' => 3,
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_log`.`t_explore_search`";
    }

    public $tb_fields = [
        'id', 'pid', 'cid', 'cname', 'count', 'update_time', 'ctime'
    ];

    /**
     * @return string
     * 勘探服务地区搜索列表
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getExploreList();
    }

    /**
     * @param $cid
     * @return string
     * 勘探服务地区搜索唯一数据
     */
    private function __getInfoByIdKey($cid): string
    {
        return AppCRedisKeys::getExploreInfoById($cid);
    }


    /**
     * @param int $cid
     * 清理缓存
     */
    public function delCache(int $cid = 0)
    {
        $r_key1 = $this->__getListKey();
        $r_key2 = $this->__getInfoByIdKey($cid);

        by::redis('core')->del($r_key1, $r_key2);
    }

    /**
     * @param int $cid
     * @param string $cname
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $cid = 0, string $cname = ''): array
    {
        $where  = "1 = 1";
        $params = [];

        if ($cid > 0) {
            $where          .= " and `cid`=:cid";
            $params[':cid'] = $cid;
        }

        if (!empty($cname)) {
            $where           .= " AND (`cname` LIKE :cname)";
            $params[":cname"] = "%{$cname}%";
        }

        return [$where, $params];
    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     * 保存查询记录
     */
    public function searchSave($data): array
    {
        if (empty($data)) {
            return [false,"参数错误"];
        }

        $pid = isset($data['pid']) ? CUtil::uint($data['pid']) : 0;
        $cid = isset($data['cid']) ? CUtil::uint($data['cid']) : 0;
        $aid = isset($data['aid']) ? CUtil::uint($data['aid']) : 0;
        list($status, $res) = by::plumbingOrder()->getAddress($pid, $cid, $aid);
        if (!$status) {
            return [$status, $res];
        }

        $save = [
            'pid'         => $pid,
            'cid'         => $cid,
            'cname'       => $res['city'],
            'count'       => 1,
            'update_time' => time(),
            'ctime'       => time()
        ];

        $fields = array_keys($save);
        $fields = implode("`,`", $fields);
        $rows   = implode("','", $save);

        $upd = [];
        foreach ($save as $k => $v){
            if ($k == 'count') {
                $upd[] = "`{$k}` = `count` + 1";
            } else {
                $upd[] = "`{$k}` = '{$v}'";
            }
        }
        $upd = implode(' , ', $upd);

        $tb_ur = self::tbName();
        $sql1  = "INSERT INTO {$tb_ur} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$upd}";
        $sql2  = "ALTER TABLE {$tb_ur} AUTO_INCREMENT=1";

        //todo 插入查询记录信息
        by::dbMaster()->createCommand($sql1)->execute();
        by::dbMaster()->createCommand($sql2)->execute();

        $this->delCache($cid);

        $type = isset($data['type']) ? CUtil::uint($data['type']) : 0;
        $ret = $this->isCanService($cid, $type);

        return [true, $ret];
    }

    /**
     * @param $cid
     * @param int $type
     * @return array
     * 是否在服务区
     */
    public function isCanService($cid, int $type = self::TYPE['EXPLORE']): array
    {
        switch ($type) {
            case self::TYPE['INSTALL'] :
                $msg_name = '安装服务';

                break;
            case self::TYPE['SERVICE'] :
                $msg_name = '上门服务';

                break;
            default :
                $msg_name = '勘测预约';

                break;
        }

        $data = [
            'is_can_service' => 0,
            'msg' => "您的城市尚未开通{$msg_name}\r\n请耐心等待"
        ];

        $order_type = by::model('OfreightModel', 'goods')::TYPE['EXPLORE'];
        $info       = by::model('OfCfgModel', 'goods')->GetOneByCid($order_type, $cid);
        if (!empty($info)) {
            $data = [
                'is_can_service' => 1,
                'msg'            => ''
            ];
        }

        return $data;
    }

    /**
     * @param $source 1：小程序列表 2：后台列表
     * @param int $page
     * @param int $page_size
     * @param string $cname
     * @return array
     * @throws Exception
     * 勘探服务地区搜索列表
     */
    public function getList($source, int $page = 1, int $page_size = self::PAGE_SIZE, string $cname = ''): array
    {
        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $cname);
        $aJson = $redis->hGet($r_key, $s_key);
        $cids  = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition(0, $cname);

            if ($source == 1) {
                $sql = "SELECT `cid` FROM {$tb} WHERE {$where} ORDER BY `id` DESC ";
            } else {
                list($offset) = CUtil::pagination($page, $page_size);
                $sql          = "SELECT `cid` FROM {$tb} WHERE {$where} ORDER BY `count` DESC LIMIT {$offset},{$page_size}";
            }

            $cids = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $cids = !empty($cids) ? $cids : [];

            $redis->hSet($r_key, $s_key, json_encode($cids));
            CUtil::ResetExpire($r_key, empty($cids) ? 0 : self::EXP);
        }

        $list      = [];
        $p_data    = by::model('AreaModel', MAIN_MODULE)->GetList();
        $name_data = array_column($p_data, 'name', 'id');

        foreach ($cids as $v)
        {
            $info = by::exploreSearch()->getInfoById($v['cid']);
            if(empty($info)){
                continue;
            }

            $info['address'] = $name_data[$info['pid']].' '.$info['cname'];

            $info['is_area'] = by::exploreSearch()->isCanService($v['cid'])['is_can_service'] ?? 0;

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param $cid
     * @return array
     * @throws Exception
     * 勘探服务地区搜索唯一数据
     */
    public function getInfoById($cid): array
    {
        $cid = CUtil::uint($cid);
        if($cid == 0) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getInfoByIdKey($cid);
        $json       = $redis->get($redis_key);
        $info       = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $param) = $this->__getCondition($cid);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql,$param)->queryOne();
            $info   = empty($info) ? [] : $info;

            $redis->set($redis_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        return $info;
    }

    /**
     * @param string $cname
     * @return int
     * @throws Exception
     * 勘探服务地区搜索记录数量
     */
    public function getCount(string $cname = ''): int
    {
        $redis = by::redis('core');
        $r_key = self::__getListKey();
        $s_key = CUtil::getAllParams(__FUNCTION__, $cname);
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            $tb                   = self::tbName();
            list($where, $params) = self::__getCondition(0, $cname);
            $sql                  = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count                = !empty($count) ? $count : 0;

            $redis->hSet($r_key, $s_key, $count);
            CUtil::ResetExpire($r_key, empty($count) ? 0 : self::EXP);
        }

        return intval($count);
    }

    /**
     * @param string $cname
     * 勘探服务地区搜索记录导出
     */
    public function export(string $cname = '')
    {
        $head                 = ['省', '市', '查询次数','是否可服务'];
        $f_name               = '查询记录列表' . date('Ymd') . mt_rand(1000, 9999);
        $tb                   = self::tbName();
        list($where, $params) = $this->__getCondition(0, $cname);

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params)
        {
            $db     = by::dbMaster();
            $id     = 0;
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

            $p_data    = by::model('AreaModel', MAIN_MODULE)->GetList();
            $name_data = array_column($p_data, 'name', 'id');

            while (true) {
                $params['id'] = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end  = end($list);
                $id   = $end['id'];
                $data = [];

                foreach ($list as $val) {
                    if (empty($val)) {
                        continue;
                    }

                    $is_can_service = by::exploreSearch()->isCanService($val['cid'])['is_can_service'];

                    $data[] = [
                        'pid'     => $name_data[$val['pid']],
                        'cid'     => $val['cname'],
                        'count'   => $val['count'],
                        'is_area' =>  isset($is_can_service) ? self::IS_AREA[$is_can_service] : ''
                    ];
                }

                yield $data;
            }

        }, $f_name);
    }

    public function exportData(string $cname = '')
    {
        $head                 = ['省', '市', '查询次数','是否可服务'];
        $tb                   = self::tbName();
        list($where, $params) = $this->__getCondition(0, $cname);

        //导出
        $db     = by::dbMaster();
        $id     = 0;
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $p_data    = by::model('AreaModel', MAIN_MODULE)->GetList();
        $name_data = array_column($p_data, 'name', 'id');

        $data[] = $head;
        while (true) {
            $params['id'] = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end  = end($list);
            $id   = $end['id'];

            foreach ($list as $val) {
                if (empty($val)) {
                    continue;
                }

                $is_can_service = by::exploreSearch()->isCanService($val['cid'])['is_can_service'];

                $data[] = [
                    'pid'     => $name_data[$val['pid']],
                    'cid'     => $val['cname'],
                    'count'   => $val['count'],
                    'is_area' =>  isset($is_can_service) ? self::IS_AREA[$is_can_service] : ''
                ];
            }

        }
        return $data;
    }
}