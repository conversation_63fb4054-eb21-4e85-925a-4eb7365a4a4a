<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 19:45
 */

namespace app\modules\plumbing\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;
use app\modules\main\models\CommModel;

class PlumbingPriceModel extends CommModel
{
    CONST EXP = 3600;

    public static function tbName(): string
    {
        return  "`db_dreame`.`t_plumbing_price`";
    }

    public $tb_fields = [
        'id', 'oprice', 'price', 'note', 'ctime'
    ];

    /**
     * @return string
     * 上下水服务价格配置KEY
     */
    private function __getPlumbingPriceKey(): string
    {
        return AppCRedisKeys::getPlumbingPrice();
    }


    /**
     * 清理缓存
     */
    private function __delCache()
    {
        $r_key = $this->__getPlumbingPriceKey();

        by::redis('core')->del($r_key);
    }


    /**
     * @param int $id
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $id = 0): array
    {
        $where  = "1 = 1";
        $params = [];

        if ($id > 0) {
            $where        .= " and `id`=:id";
            $params[':id'] = $id;
        }

        return [$where, $params];
    }

    /**
     * @return array
     * @throws Exception
     * 上下水服务价格配置
     */
    public function getPriceConfig(): array
    {
        $redis = by::redis('core');
        $r_key = $this->__getPlumbingPriceKey();
        $json  = $redis->get($r_key);
        $info  = (array)json_decode($json,true);

        if($json === false){
            $tb                  = self::tbName();
            $fields              = implode("`,`", $this->tb_fields);
            list($where, $param) = $this->__getCondition(1);
            $sql  = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";
            $info = by::dbMaster()->createCommand($sql,$param)->queryOne();
            $info = empty($info) ? [] : $info;

            $redis->set($r_key, json_encode($info), ['EX' => empty($info) ? 10 : self::EXP]);
        }

        if (!empty($info)) {
            $info['oprice'] = by::Gtype0()->totalFee($info['oprice'], 1);
            $info['price']  = by::Gtype0()->totalFee($info['price'], 1);
        }

        return $info;
    }

    /**
     * @param $data
     * @return array
     * @throws Exception
     * 保存价格配置
     */
    public function priceSave($data): array
    {
        if (empty($data)) {
            return [false, "参数错误"];
        }

        if (empty($data['price'])) {
            return [false, "价格不能为空"];
        }

        $note = $data['note'] ?? 0;
        if (mb_strlen($note) > 50) {
            return [false, '描述最长50字'];
        }

        $save = [
            'id'     => 1,
            'oprice' => by::Gtype0()->totalFee($data['oprice'] ?? 0),
            'price'  => by::Gtype0()->totalFee($data['price']),
            'note'   => $note,
            'ctime'  => time()
        ];

        $fields = array_keys($save);
        $fields = implode("`,`", $fields);
        $rows   = implode("','", $save);

        $upd = [];
        foreach ($save as $k => $v){
            $upd[] = "`{$k}` = '{$v}'";
        }
        $upd = implode(' , ', $upd);

        //todo 插入查询记录信息
        $tb  = self::tbName();
        $sql = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$upd}";
        $res = by::dbMaster()->createCommand($sql)->execute();
        if ($res <= 0) {
            return [false, '保存失败'];
        }

        $this->__delCache();

        return [true, 'ok'];
    }
}