<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\SubsidyActivityService;
use app\modules\goods\models\WishlistModel;
use app\modules\main\models\UserEmployeeModel;
use yii\db\Exception;

class GoodsService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 优惠券状态
    const MARKET_RECEIVED     = 1; // 已领取
    const MARKET_USED         = 2; // 已使用
    const MARKET_AVAILABLE    = 3; // 可领取
    const MARKET_OUT_OF_STOCK = 4; // 已领光

    /**
     * 获取优惠券列表
     * @param int $userId
     * @param int $gid
     * @param string $level
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getMarketList(int $userId, int $gid, string $level = '')
    {
        // 1. 获取活动
        $activityConfig = by::activityConfigModel();
        $activities     = $activityConfig->getActivityByType($activityConfig::GRANT_TYPE['goods_detail']);

        if (empty($activities)) {
            return [];
        }
        $activity_id = $activities[0]['id'] ?? 0;

        // 2. 获取优惠券
        $markets   = $this->getMarkets($gid, $level, $activity_id);
        $marketIds = array_column($markets, 'market_id');

        // 与此商品有关的优惠券
        $receivedMarkets         = by::userCard()->getCardListByUserId($userId, by::userCard()::GET_CHANNEL['activity'], $activity_id);
        $relevantReceivedMarkets = array_filter($receivedMarkets, function ($item) use ($marketIds) {
            return in_array($item['market_id'], $marketIds);
        });

        $receivedMarketsById = [];
        foreach ($relevantReceivedMarkets as $market) {
            $receivedMarketsById[$market['market_id']][] = $market;
        }

        $now = time();
        foreach ($markets as $key => &$market) {
            $marketId         = $market['market_id'];
            $market['status'] = intval($market['stock']) > 0 ? self::MARKET_AVAILABLE : self::MARKET_OUT_OF_STOCK;
            if (isset($receivedMarketsById[$marketId])) {
                $hasValidCard = false;
                foreach ($receivedMarketsById[$marketId] as $card) {
                    if ($card['expire_time'] >= $now) {
                        $hasValidCard     = true;
                        $market['status'] = $card['status'] == 0 ? self::MARKET_RECEIVED : self::MARKET_USED;
                        break;
                    }
                }
                if (!$hasValidCard) {
                    unset($markets[$key]);
                }
            }
        }

        return array_values($markets);
    }

    /**
     * 获取可用优惠券列表
     * @param int $gid
     * @param string $level
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getMarkets(int $gid, string $level = '', $activity_id = 0): array
    {
        // 0. 商品不存在，或不可使用优惠券，则返回空
        $goods = by::Gmain()->GetAllOneByGid($gid, false, false, 0, false);

        if (empty($goods) || $goods['is_coupons'] == 0) {
            return [];
        }

        // 1. 获取活动
        if (empty($activity_id)) {
            return [];
        }

        // 2. 获取活动的优惠券，同一个时间，只能有一条活动
        $markets = by::aM()->getCouponListByAcId($activity_id);

        // 3. 过滤优惠券
        $markets = array_filter($markets, function ($item) use ($gid, $level) {
            if (strpos($item['level'], $level) === false) {
                return false;
            }
            // 优惠券的过期时间
            if ($item['expire_time'] < time()) {
                return false;
            }
            $goodsIds = explode(',', $item['goods_val']);
            if ($item['goods_type'] == 1) { // 包含
                // goods_val 为空代表所有商品都可用
                $filteredGoodsIds = array_filter($goodsIds);
                return empty($filteredGoodsIds) || in_array($gid, $filteredGoodsIds);
            } else { // 不包含
                return !in_array($gid, $goodsIds);
            }
        });

        usort($markets, function ($a, $b) {
            return $a['expire_time'] - $b['expire_time'];
        });
        return $markets;
    }

    /**
     * 获取商品多规格数据
     * @param int $gid
     * @param int $sprice_type
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getSpecList(int $gid, int $sprice_type): array
    {
        // 获取商品信息
        $goods = by::Gmain()->GetAllOneByGid($gid, true, false, $sprice_type);
        if (!$goods || by::Gtype0()::ATYPE['SPEC'] == $goods['atype']) { // 统一规格
            return [];
        }

        // 多规格
        $items = by::Gspecs()->GetListByGid($gid, $sprice_type);

        // 商品属性
        $items = $this->setGoodsAttr($items, $gid);

        // 商品库存
        $items = $this->setGoodsStock($items, $goods['limit_num'] ?? 0);

        // 按照价格进行升序
        usort($items, function ($a, $b) {
            return $a['price'] <=> $b['price'];
        });


        return $items;
    }

    /**
     * 设置商品规格属性
     * @param array $items
     * @param int $gid
     * @return array
     * @throws \yii\db\Exception
     */
    private function setGoodsAttr(array $items, int $gid): array
    {
        // 规格属性
        $attr_keys = by::Gak()->GetListByGid($gid);
        $attr_keys = array_column($attr_keys, 'at_name', 'id');

        // 规格属性值
        $attr_value_ids = [];
        foreach ($items as $item) {
            $attr_value_ids = array_merge($attr_value_ids, json_decode($item['av_ids'], true));
        }
        $attr_value_ids = array_unique($attr_value_ids);
        $attr_values    = by::Gav()->getListByIds($attr_value_ids);
        $attr_values    = array_column($attr_values, null, 'id');

        foreach ($items as $index => $item) {
            $av_ids                = json_decode($item['av_ids'], true);
            $items[$index]['attr'] = array_map(function ($av_id) use ($attr_values, $attr_keys) {
                return [
                        'key'   => $attr_keys[$attr_values[$av_id]['ak_id'] ?? 0] ?? '',
                        'value' => $attr_values[$av_id]['at_val'] ?? ''
                ];
            }, $av_ids);
        }

        return $items;
    }

    /**
     * 设置商品库存
     * @param array $items
     * @param int $limit_num
     * @return array
     * @throws \yii\db\Exception
     */
    private function setGoodsStock(array $items, int $limit_num): array
    {
        $stocks     = byNew::GoodsStockModel()->getStockListBySkus(array_column($items, 'sku'), false);
        $stock_nums = array_column($stocks, 'stock', 'sku');
        foreach ($items as $k => $v) {
            $items[$k]['num']       = 1;
            $items[$k]['in_stock']  = 0;
            $items[$k]['store']     = 0;
            $items[$k]['limit_num'] = $limit_num;
            // 判断库存（小程序置灰）
            if (($stock_nums[$v['sku']] ?? 0) > 0) {
                $items[$k]['in_stock']  = 1;
                $items[$k]['store']     = $stock_nums[$v['sku']];
                $items[$k]['limit_num'] = $limit_num;
            }
        }
        return $items;
    }

    /**
     * 检查用户是否有权限查看内购商品的详情
     * @param $user_id
     * @param $gid
     * @return array
     * @throws \yii\db\Exception
     */
    public function checkInternalPurchase($user_id, $gid): array
    {
        $goods = by::Gtype0()->getListByGids([$gid]);
        if (empty($goods[0]) || $goods[0]['is_internal_purchase'] != 1) { // 非内购，不校验
            return [true, ''];
        }

        $uid      = by::Phone()->getUidByUserId($user_id);
        $employee = byNew::UserEmployeeModel()->getEmployeeInfo($uid);

        if (empty($employee) || $employee['employee_status'] == UserEmployeeModel::EMPLOYEE_STATUS['STOP']) {
            return [false, '非微笑大使身份，不可查看内购商品'];
        }

        if ($employee['is_blacklist'] == UserEmployeeModel::IS_BLACKLIST['YES']) {
            return [false, '微笑大使身份冻结中，不可查看内购商品'];
        }

        return [true, ''];
    }

    /**
     * @param $userId
     * @param array $params
     * @return array
     * 心愿单操作：添加或移除商品
     */
    public function wish($userId, array $params = []): array
    {
        $opt     = $params['opt'] ?? 'add';
        $goodsId = (int) ($params['goods_id'] ?? 0);
        $specsId = (int) ($params['specs_id'] ?? 0);
        $remark  = trim($params['remark'] ?? '');

        // 冗余检查移除：前面已判断 $goodsId <= 0，此处无需重复
        if ($goodsId <= 0) {
            return [false, '商品ID不能为空！'];
        }
        if ($specsId < 0) {
            $specsId = 0;
        }

        if (!in_array($opt, ['add', 'remove'])) {
            return [false, '非法操作！'];
        }

        // 查询记录时，需包含规格ID（同一商品不同规格应视为不同心愿项）
        // 原查询漏了 specs_id，导致同一商品不同规格可能被误判，此处补充
        $wishRecord = WishlistModel::findOne([
                'user_id'  => $userId,
                'goods_id' => $goodsId,
                'specs_id' => $specsId  // 关键补充：区分商品规格
        ]);

        $time = time();
        try {
            if ($opt === 'add') {
                if ($wishRecord && $wishRecord->is_deleted === 0) {
                    return [false, '已存在心愿单中！'];
                }

                if (!$wishRecord) {
                    $wishRecord = new WishlistModel();
                    $wishRecord->setAttributes([
                            'user_id'      => $userId,
                            'goods_id'     => $goodsId,
                            'specs_id'     => $specsId,
                            'is_deleted'   => 0,
                            'is_notified'  => 1,
                            'remark'       => $remark,
                            'expire_time'  => 0,
                            'add_time'     => $time,
                            'created_time' => $time,
                    ]);
                } else {
                    $wishRecord->is_deleted = 0;
                    $wishRecord->add_time   = $time;
                    // 可选：更新时是否刷新创建时间？根据业务需求决定，此处保持原逻辑
                }

                if (!$wishRecord->save()) {
                    throw new Exception('数据保存失败：' . json_encode($wishRecord->errors));
                }
                WishlistModel::delMyWishListKey($userId);
                return [true, '已添加到心愿单！'];

            } elseif ($opt === 'remove') {
                if (!$wishRecord || $wishRecord->is_deleted === 1) {
                    return [false, '心愿单中不存在此商品！'];
                }

                $wishRecord->is_deleted = 1;
                if (!$wishRecord->save()) {
                    throw new Exception('数据保存失败：' . json_encode($wishRecord->errors));
                }
                WishlistModel::delMyWishListKey($userId);
                return [true, '已从心愿单中移除！'];
            } else {
                return [false, '未知操作类型！'];
            }

        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wish');
            return [false, '操作失败，请稍后再试！'];
        }
    }


    /**
     * 获取用户积分信息
     * @param $userId
     * @return array
     * @throws Exception
     */
    public function getPointInfo($userId): array
    {
        // todo 优化点 结合用户等级比例+商品金额+是否为追觅合伙人做计算
        // 获取当前用户总计分数
        $mPoint = by::point();
        $totalPoint = $mPoint->get($userId); // 总积分
        // 获取当前用户积分比例
        $deductionRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 10;
        return array(
                $totalPoint,
                $deductionRate
        );
    }

    /**
     * @param $userId
     * @return array
     * 获取我的心愿单
     */
    public function wishList($userId): array
    {
        try {
            $wishlist = byNew::WishlistModel()->getMyWishlist($userId);
            if (empty($wishlist)) {
                return [true, []];
            }

            $result = [];
            list($totalPoint, $deductionRate) = $this->getPointInfo($userId);

            foreach ($wishlist as $item) {
                $item['specs_id'] = $item['specs_id'] ?? 0;

                $goods = by::Gmain()->GetAllOneByGid($item['goods_id'], true, false, 0, false);
                if ($goods && is_array($goods)) {
                    $item['goods_name']     = $goods['name'] ?? '';
                    $item['cover_image']    = $goods['cover_image'] ?? '';
                    $item['price']          = $goods['price'] ?? 0;
                    $item['mprice']         = $goods['mprice'] ?? 0;
                    $item['total_point']    = $totalPoint;    // 添加总积分
                    $item['deduction_rate'] = $deductionRate; // 添加积分比例
                    //国补价格
                    $item['subsidy_price']  = SubsidyActivityService::getInstance()->getGoodsSubsidyAmount($item['goods_id'], $item['price']);
                    $result[]               = $item;
                }
                // 商品不存在则跳过
            }

            return [true, $result];

        } catch (\Throwable $e) {
            // 错误记录日志，方便排查
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.wish_list');
            return [false, '操作失败，请稍后再试！'];
        }
    }


    /**
     * 检查此商品是否在我的心愿列表中
     */
    /**
     * 检查一个或多个商品是否在用户的心愿单中
     *
     * @param int $userId 用户ID
     * @param array $goodsIds 商品ID或商品ID数组
     * @param int $specsId 规格ID，默认为0
     * @return bool|array 如果传入单个商品ID，返回布尔值；如果传入数组，返回关联数组[goodsId => bool]
     */
    public function getGoodsIsWish($userId, $goodsIds, $specsId = 0)
    {
        // 验证用户ID
        if (empty($userId) || !is_numeric($userId) || strlen((string)$userId) >= 11) {
            return is_array($goodsIds) ? array_fill_keys($goodsIds, false) : false;
        }

        // 处理规格ID
        $specsId = max(0, (int)$specsId);

        // 标准化商品ID为数组
        $singleGoodsId = false;
        if (!is_array($goodsIds)) {
            $singleGoodsId = true;
            $goodsIds = [$goodsIds];
        }

        // 过滤空的商品ID
        $goodsIds = array_filter($goodsIds, 'is_numeric');
        if (empty($goodsIds)) {
            return $singleGoodsId ? false : [];
        }

        // 查询心愿单记录
        $wishRecords = WishlistModel::find()
                ->select('goods_id')
                ->where([
                        'user_id'    => $userId,
                        'goods_id'   => $goodsIds,
                        'specs_id'   => $specsId,
                        'is_deleted' => 0
                ])
                ->asArray()
                ->all();

        // 构建结果数组
        $result = array_fill_keys($goodsIds, false);
        foreach ($wishRecords as $record) {
            $result[$record['goods_id']] = true;
        }

        // 如果是单个商品ID查询，返回布尔值
        return $singleGoodsId ? reset($result) : $result;
    }

    /**
     * 批量获取商品的收藏数量
     *
     * @param array $goodsIds 商品ID数组
     * @return array 商品ID到收藏数量的映射数组 [商品ID => 收藏数]
     */
    public function getGoodsWishNumber(array $goodsIds): array
    {
        // 过滤无效的商品ID，确保每个ID都是正整数
        $validIds = array_filter($goodsIds, function ($id) {
            return is_numeric($id) && (int) $id > 0;
        });

        // 如果没有有效ID，直接返回空数组
        if (empty($validIds)) {
            return [];
        }

        // 使用GROUP BY和索引优化查询
        $query = WishlistModel::find()
                ->select(['goods_id', 'COUNT(*) as count'])
                ->where([
                        'goods_id'   => $validIds,
                        'is_deleted' => 0
                ])
                ->groupBy('goods_id')
                ->indexBy('goods_id'); // 使用goods_id作为结果数组的键

        // 执行查询并获取结果
        $counts = $query->asArray()->all();

        // 构建完整的结果数组，确保包含所有请求的商品ID
        $result = [];
        foreach ($goodsIds as $id) {
            $result[$id] = isset($counts[$id]) ? (int) $counts[$id]['count'] : 0;
        }

        return $result;
    }
    
    public function goodsTagFallbackContent(int $tid)
    {
        $data = CUtil::dictData('goods_tag_fallback_content');

        $dataMap = CUtil::groupBy($data, 'label');
        $default = $dataMap[-1] ?? [];

        if (isset($dataMap[$tid])) {
            return [true, $dataMap[$tid]];
        } else {
            return [true, $default];
        }
    }
}