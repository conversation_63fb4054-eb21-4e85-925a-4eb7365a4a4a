<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class OmsService
{

    //OMS 回写限制 **************  *************
    const API_CONFIG = YII_ENV_PROD ? [
        'IPS'      => ['***************', '***************', '*************', '*************', '***************', '**************', '*************'],
        'LIMIT'    => 1000,
        'API_LIST' => ['rewrite'],
    ] : [
        'IPS'      => [],
        'LIMIT'    => 1000,
        'API_LIST' => ['rewrite'],
    ];

    //构造参数

    protected $_ips      = [];                           //ip白名单
    protected $_limit    = 0;                            //频率限制
    protected $_api_list = [];                           //接口权限


    public function __construct()
    {
        $apiConfig       = self::API_CONFIG;
        $this->_ips      = $apiConfig['IPS'] ?? [];
        $this->_limit    = $apiConfig['LIMIT'] ?? 10;
        $this->_api_list = $apiConfig['API_LIST'] ?? [];
    }


    /**
     * ip白名单校验
     */
    protected function _isValidIp(): array
    {
        if (!empty($this->_ips)) {
            $client_ip = CUtil::get_client_ip();
            if (!in_array($client_ip, $this->_ips)) {
                CUtil::debug("{$client_ip}", 'oms.ip.white');
                return [false, '请联系管理员添加白名单'];
            }
        }
        return [true, 'OK'];
    }


    /**
     * @param $method
     * @return array
     * 访问频率限制
     */
    protected function _isValidLimit($method): array
    {
        $unique_key = CUtil::getAllParams(__FUNCTION__, implode(',', $this->_ips));
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($method, $unique_key, 1, "EX", $this->_limit);

        if (!$s) {
            return [false, '频繁请求，请稍候~|每秒访问超过：' . $this->_limit];
        }
        return [true, 'OK'];
    }


    /**
     * @param $data
     * @return array
     */
    protected function _isValidSign($data): array
    {
        $sign = $data['sign'] ?? '';
        if (empty($sign)) {
            return [false, '签名不能为空！'];
        }

        unset($data['sign']);

        $filterFields = by::oms()::OMS_SIGN_FILTER_FIELDS;
        foreach ($filterFields as $field) {
            unset($data[$field]);
        }
        $s = by::oms()::verifyOmsSign($data, $sign);
        if (!$s) {
            return [false, '验签失败！'];
        }
        return [true, 'OK'];
    }


    /**
     * @param $post
     * @return array
     * @throws Exception
     * oms 回写
     */
    public function rewriteOmsData($post): array
    {
        //IP限制
        list($s, $msg) = $this->_isValidIp();
        if (!$s) {
            return [false, $msg];
        }

        //参数处理
        parse_str(str_replace('+', '%2B', urldecode($post)), $arr);

        //请求日志
        CUtil::debug("post:{$post}|arr:" . json_encode($arr, 320), "oms.rewrite");

        //参数校验
        foreach (by::oms()::OMS_REWRITE_KEYS as $item) {
            if (empty($arr[$item])) {
                return [false, $item . '公共参数错误'];
            }
        }

        //时钟偏差5分钟内有效
        if (YII_ENV_PROD && abs(time() - strtotime($arr['timestamp'])) > 60 * 5) {
            return [false, '签名时间已过期！'];
        }

        list($s, $msg) = $this->_isValidSign($arr);
        if (!$s) {
            return [false, $msg];
        }

        list($s, $msg) = by::oms()->rewriteOmsData($arr);
        if (!$s) {
            return [false, $msg];
        }
        return [true, $msg];
    }


}