<?php

namespace app\modules\main\services;

use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\system\SystemDictDataService;
use app\modules\goods\models\GmainModel;
use app\modules\goods\models\OmainModel;
use app\modules\goods\services\ErpService;
use RedisException;
use yii\db\Exception;

class OrderService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * 获取订单支付状态
     *
     * @param int $userId 用户ID
     * @param string $orderNo 订单号
     * @param int $orderType 订单类型
     * @return array 返回订单支付状态及相关信息
     * @throws Exception
     * @throws RedisException
     *
     * response:
     *   pay_status 1: 已支付
     *              2: 未支付
     *              3: 已取消
     */
    public function getOrderStatus(int $userId, string $orderNo, int $orderType): array
    {
        // 校验订单号是否为空
        if (empty($orderNo)) {
            return [false, '订单号不能为空'];
        }

        // 根据订单类型查询订单支付状态
        if ($orderType == by::Odeposit()::TYPE['ORDINARY']) {
            // 查询普通、尾款订单支付状态
            $orderInfo = by::Omain()->getInfoByNo($orderNo);
        } elseif ($orderType == by::Odeposit()::TYPE['DEPOSIT']) {
            // 查询预售定金订单支付状态
            $orderInfo = by::Odeposit()->getInfoByDepositOrderNo($userId, $orderNo);
        } else {
            return [false, '未知的订单类型'];
        }

        // 如果订单信息不存在，返回错误信息
        if (!$orderInfo) {
            return [false, '订单信息不存在'];
        }

        // 获取订单状态和状态枚举
        $status          = intval($orderInfo['status']);
        $orderStatusEnum = by::Omain()::ORDER_STATUS;

        // 判断订单支付状态并返回相应的信息
        if ($status > $orderStatusEnum['CANCELED']) {
            return [true, [
                'pay_status' => 1,
                'msg'        => '订单已支付'
            ]];
        } elseif ($status == $orderStatusEnum['CANCELED']) {
            return [true, [
                'pay_status' => 3,
                'msg'        => '订单已取消'
            ]];
        }

        return [true, [
            'pay_status' => 2,
            'msg'        => '订单未支付'
        ]];
    }
    
    /**
     * 手动发货
     * @param array $data
     * @return true
     * @throws BusinessException
     * @throws Exception
     */
    public function manualDelivery(array $data): bool
    {
        // 订单号
        $order_no = $data['order_no'] ?? '';
        // 订单商品ID
        $ogid = $data['ogid'] ?? 0;
        // 发货类型 1=物流发货
        $delivery_type = $data['delivery_type'] ?? 0;
        // 物流单号
        $express_no = $data['express_no'] ?? '';
        $express_no = htmlspecialchars(trim($express_no));
        // 物流公司编码
        $express_code = $data['express_code'] ?? '';
        $express_code = htmlspecialchars(trim($express_code));
        // 物流名称
        $express_name = $data['express_name'] ?? '';
        $express_name = htmlspecialchars(trim($express_name));

        if (empty($order_no)) {
            throw new BusinessException('订单号不能为空');
        }

        if (empty($ogid)) {
            throw new BusinessException('订单商品ID不能为空');
        }

        if (empty($delivery_type)) {
            throw new BusinessException('请选择发货类型');
        }

        if (empty($express_no)) {
            throw new BusinessException('物流单号不能为空');
        }

        if (empty($express_code)) {
            throw new BusinessException('物流公司编码不能为空');
        }
        
        if (empty($express_name)) {
            throw new BusinessException('物流名称不能为空');
        }
        
        $orderInfo = by::Omain()->getInfoByNo($order_no);

        if (empty($orderInfo)) {
            throw new BusinessException('订单数据不存在');
        }

        if ($orderInfo['status'] != OmainModel::ORDER_STATUS['WAIT_SEND']) {
            throw new BusinessException('仅待发货才能手动发货');
        }

        // 物流发货
        if ($delivery_type == 1) {
            $data = [
                'deal_code' => $order_no,
                'shipping_code' => $express_code,
                'shipping_name' => $express_name,
                'shipping_sn' => $express_no,
            ];

            $service = new ErpService();
            list($status, $msg) = $service->orderSendUpdate($data,$ogid);
            if (! $status) {
                throw new BusinessException($msg);
            }
        } else {
            throw new BusinessException('暂不支持该发货类型');
        }
        
        return true;
    }
}
