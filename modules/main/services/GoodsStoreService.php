<?php

namespace app\modules\main\services;

use app\models\by;
use app\modules\goods\models\StoreGoodsModel;
use app\modules\goods\models\StoreGoodsRelationModel;
use yii\db\Exception;

class GoodsStoreService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $user_id
     * @param $goods_ids
     * @return array
     */
    public function existingGoods($user_id,$goods_ids):array
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();
        $existingGoods = $StoreGoodsRelationModel->existingGoods($user_id,$goods_ids);
        return $existingGoods;
    }


    public function batchAdd(array $rows)
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();
        $status = $StoreGoodsRelationModel->batchAdd($rows);
        return $status;
    }


    /**
     * @param $page
     * @param $page_size
     * @param $version  : 当前版本号
     * @param $type     : 商品类型
     * @param $status   : 0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 标签
     * @return array
     * @throws Exception
     *
     */
    public function GetStoreGoods(
        $user_id=0, $page=1, $page_size=50, $version='',$name,$tid ,$platformId,$status = -1,$orderType = 1
    ): array
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();

        $gids = $StoreGoodsRelationModel->GetStoreGoods($user_id,$page, $page_size, $version,$name, $tid, $platformId,$status,$orderType);
        return $gids;
    }
    public function GetStoreGoodsCount(
        $user_id=0, $version='',$name,$tid ,$platformId,$status
    )
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();

        $count = $StoreGoodsRelationModel->GetStoreGoodsCount($user_id, $version,$name, $tid, $platformId,$status);
        return $count;
    }

    public function GetStoreList(
        $page=1,    $page_size=50, $version='', $name,$tid ,$platformId
    ): array
    {
        $StoreGoodsRelationModel = StoreGoodsModel::instance();
        $gids = $StoreGoodsRelationModel->GetGoodsList($page, $page_size, $version, $name, $tid, $platformId);
        return $gids;
    }
    public function GetStoreListCount(
        $version='', $name,$tid ,$platformId
    )
    {
        $StoreGoodsRelationModel = StoreGoodsModel::instance();
        $count = $StoreGoodsRelationModel->GetGoodsListCount($version, $name, $tid, $platformId);
        return $count;
    }

    public function GetStoreGoodsOld(
        $user_id=0,
        $page=1,    $page_size=50, $version='', $type=-1,
        $status=-1, $name='',      $sku='',     $tid=-1,
        $detailData=[], $sort=[]
    ): array
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();

        $gids = $StoreGoodsRelationModel->GetStoreGoods($user_id,$page, $page_size, $version, $type, $status, '', '', $tid, $detailData, $sort);
        return $gids;
    }

    /**
     * @param $page
     * @param $page_size
     * @param $version  : 当前版本号
     * @param $type     : 商品类型
     * @param $status   : 0上架1下架
     * @param $name     : 商品名称
     * @param $sku      : 商品编码
     * @param $tid      : 标签
     * @return array
     * @throws Exception
     *
     */
    public function GetStoreListOld(
        $user_id=0,
        $page=1,    $page_size=50, $version='', $type=-1,
        $status=-1, $name='',      $sku='',     $tid=-1,
        $detailData=[], $sort=[]
    ): array
    {
        $StoreGoodsRelationModel = StoreGoodsRelationModel::instance();
        $gids = $StoreGoodsRelationModel->GetStore($user_id,$page, $page_size, $version, $type, $status, '', '', $tid, $detailData, $sort);
        return $gids;
    }


}