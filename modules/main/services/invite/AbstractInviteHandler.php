<?php

namespace app\modules\main\services\invite;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

/**
 * 抽象邀请处理器基类
 * 提供通用的邀请逻辑实现
 */
abstract class AbstractInviteHandler implements InviteHandlerInterface
{
    /**
     * 基础邀请资格验证
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array [bool $isValid, string $message, array $userInfo]
     */
    protected function validateBasicEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        $userInviteModel = by::UserInviteModel();

        // 基础验证：检查是否为自己邀请
        if ($userInviteModel->isSelfInvite($inviterId, $inviteeId)) {
            return [false, '不能邀请自己', []];
        }

        // 检查邀请人是否为有效用户
        $inviterInfo = by::users()->getOneByUid($inviterId);
        if (empty($inviterInfo)) {
            return [false, '邀请人信息无效', []];
        }

        // 检查被邀请人是否为有效用户
        $inviteeInfo = by::users()->getOneByUid($inviteeId);
        if (empty($inviteeInfo)) {
            return [false, '被邀请人信息无效', []];
        }

        return [true, '', compact('inviterInfo', 'inviteeInfo')];
    }

    /**
     * 检查新用户条件
     * @param int $inviteeId 被邀请人ID
     * @param array $config 配置信息
     * @return array [bool $isValid, string $message]
     */
    protected function validateNewUser(int $inviteeId, array $config): array
    {
        if ($config['check_new_user'] ?? true) {
            $drawActivityService = \app\modules\goods\services\DrawActivityService::getInstance();
            list($status, $message, $data) = $drawActivityService->isNewUser($inviteeId);
            if (!$status) {
                return [false, '被邀请用户不符合新用户条件'];
            }
        }
        return [true, ''];
    }

    /**
     * 检查邀请频率限制
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param array $config 配置信息
     * @return array [bool $isValid, string $message]
     */
    protected function validateInviteFrequency(int $inviterId, int $inviteType, array $config): array
    {
        if ($config['frequency_limit']['enabled'] ?? false) {
            $limitCount = $config['frequency_limit']['max_invites_per_day'] ?? 10;
            $userInviteModel = by::UserInviteModel();
            $todayCount = $userInviteModel->getTodayInviteCount($inviterId, $inviteType);
            if ($todayCount >= $limitCount) {
                return [false, "每日最多邀请{$limitCount}人"];
            }
        }
        return [true, ''];
    }

    /**
     * 检查是否已被全局邀请过
     * @param int $inviteeId 被邀请人ID
     * @return array [bool $isValid, string $message]
     */
    protected function validateGlobalInvite(int $inviteeId): array
    {
        $userInviteModel = by::UserInviteModel();
        if ($userInviteModel->hasGlobalInvited($inviteeId)) {
            return [false, '该用户已被邀请过了'];
        }
        return [true, ''];
    }

    /**
     * 检查用户对规则的最大领取次数限制
     * @param int $inviterId 邀请人ID
     * @param int $relateId 关联ID
     * @return array [bool $isValid, string $message]
     */
    protected function validateRuleClaimLimit(int $inviterId, int $relateId): array
    {
        if ($relateId > 0) {
            $inviteGiftRulesModel = by::InviteGiftRulesModel();
            $userInviteGiftsModel = by::UserInviteGiftsModel();

            $rule = $inviteGiftRulesModel->getRuleById($relateId);
            if (!empty($rule) && isset($rule['max_claims']) && $rule['max_claims'] > 0) {
                $userClaimCount = $userInviteGiftsModel->getUserRuleClaimCount($inviterId, $relateId);
                if ($userClaimCount >= $rule['max_claims']) {
                    return [false, "该奖励规则最多只能领取{$rule['max_claims']}次，您已达到上限"];
                }
            }
        }
        return [true, ''];
    }

    /**
     * 获取处理器配置
     * @return array
     * @throws Exception
     */
    protected function getHandlerConfig(): array
    {
        $reflection = new \ReflectionClass($this);
        $handlerClass = $reflection->getShortName();
        $handler= InviteService::CONFIG[$handlerClass] ?? [];
        if (empty($handler)) {
            CUtil::debug("邀请处理器 {$handlerClass} 没有对应的配置", 'invite_handler_config');
            throw new Exception( "邀请处理器 {$handlerClass} 没有对应的配置");
        }

        return $handler;
    }

    /**
     * 构建成功返回数据
     * @param array $inviterInfo 邀请人信息
     * @param array $inviteeInfo 被邀请人信息
     * @param array $additionalData 额外数据
     * @return array
     */
    protected function buildSuccessData(array $inviterInfo, array $inviteeInfo, array $additionalData = []): array
    {
        $baseData = [
            'nick'           => $inviterInfo['nick'] ?? '邀请人',
            'avatar'         => CUtil::avatar($inviterInfo),
            'invitee_nick'   => $inviteeInfo['nick'] ?? '被邀请人',
            'invitee_avatar' => CUtil::avatar($inviteeInfo),
        ];

        return array_merge($baseData, $additionalData);
    }

    /**
     * 记录错误日志
     * @param string $method 方法名
     * @param string $error 错误信息
     * @param string $logType 日志类型
     */
    protected function logError(string $method, string $error, string $logType = 'invite_error')
    {
        $handlerClass = get_class($this);
        CUtil::debug("{$handlerClass} {$method} error: {$error}", $logType);
    }

    /**
     * 处理邀请业务逻辑的通用部分
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    protected function processCommonBusiness(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        $userInviteModel = by::UserInviteModel();
        
        // 获取邀请人数统计
        $newInviteCount = $userInviteModel->getInviteCount($inviterId, $inviteType, $relateId);

        $result = [
            'message'              => '邀请成功',
            'current_invite_count' => $newInviteCount,
            'inviter_info'         => [
                'user_id' => $inviterId,
                'nick'    => by::users()->getOneByUid($inviterId)['nick'] ?? '邀请人',
            ],
            'invitee_info'         => [
                'user_id' => $inviteeId,
                'nick'    => by::users()->getOneByUid($inviteeId)['nick'] ?? '被邀请人',
            ],
        ];

        return [true, $result];
    }

    /**
     * 处理通用邀请奖励逻辑
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @return array
     */
    protected function processCommonInviteGifts(int $inviterId, int $inviteType, int $relateId): array
    {
        try {
            $inviteGiftService = InviteGiftService::getInstance();
            list($status, $message, $gifts) = $inviteGiftService->processInviteGifts($inviterId, $inviteType, $relateId);

            if ($status) {
                CUtil::debug("用户 {$inviterId} 获得新的邀请奖励：" . json_encode($gifts), 'invite_gift_' . $inviterId);
                return [$status, $message];
            }

            return [false, $message];

        } catch (\Exception $e) {
            $this->logError('processInviteGifts', $e->getMessage(), 'invite_gift_error');
        }

        return [false, '处理邀请奖励失败,请联系客服'];
    }

    /**
     * 生成备注信息 - 子类可重写此方法
     * @param int $inviteType 邀请类型
     * @return string|null
     */
    public function generateRemark(int $inviteType)
    {
        return null;
    }
} 