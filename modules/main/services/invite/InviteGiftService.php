<?php

namespace app\modules\main\services\invite;

use app\components\EventMsg;
use app\components\PointCenter;
use app\jobs\UserShopMoneyJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\goods\models\UserInviteGiftsModel;
use app\modules\main\services\GiftCardService;
use app\modules\main\services\MemberActivityService;
use yii\db\Exception;

/**
 * 邀请奖励服务类
 * 处理邀请有礼的业务逻辑
 */
class InviteGiftService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 检查并处理邀请奖励
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int|null $relateId 关联ID，对应user_invite_gift_rules.id，为null时处理所有规则
     * @return array
     */
    public function processInviteGifts(int $inviterId, int $inviteType, int $relateId = null): array
    {
        try {
            $userInviteModel          = by::UserInviteModel();
            $inviteGiftRulesModel     = by::InviteGiftRulesModel();
            $inviteRuleGiftItemsModel = by::InviteRuleGiftItemsModel();
            $userInviteGiftsModel     = by::UserInviteGiftsModel();

            // 获取当前邀请人数
            $inviteCount = $userInviteModel->getInviteCount($inviterId, $inviteType, $relateId);
            // 根据relateId获取规则
            if ($relateId !== null) {
                // 如果指定了relateId，获取指定的规则
                $rule = $inviteGiftRulesModel->getRuleById($relateId);
                if (empty($rule) || $rule['invite_type'] != $inviteType) {
                    return [
                            false,
                            '指定的规则不存在或邀请类型不匹配',
                            []
                    ];
                }
                // 检查邀请人数是否满足要求
                if ($inviteCount < $rule['required_count']) {
                    return [
                            true,
                            '邀请人数未达到要求，请继续邀请',
                            []
                    ];
                }
                $rules = [$rule];
            } else {
                // 获取符合条件的规则
                $rules = $inviteGiftRulesModel->getRulesByInviteCount($inviteType, $inviteCount);
            }

            if (empty($rules)) {
                return [
                        true,
                        '暂无可领取的奖励',
                        []
                ];
            }

            $db          = by::dbMaster();
            $transaction = $db->beginTransaction();

            try {
                $newGifts = [];

                foreach ($rules as $rule) {
                    $ruleId    = $rule['id'];
                    $grantMode = $rule['grant_mode'];
                    $maxClaims = $rule['max_claims'] ?? 1; // 获取最大领取次数，默认为1

                    // 检查用户对该规则的领取次数是否已达到上限
                    if ($maxClaims > 0) { // 0表示不限制
                        $userClaimCount = $userInviteGiftsModel->getUserRuleClaimCount($inviterId, $ruleId);
                        if ($userClaimCount >= $maxClaims) {
                            CUtil::debug("用户 {$inviterId} 对规则 {$ruleId} 的领取次数已达到上限 {$maxClaims}", 'invite_gift_limit');
                            continue;
                        }
                    }

                    // 获取下一个领取序列号
                    $nextClaimSequence = $userInviteGiftsModel->getNextClaimSequence($inviterId, $ruleId);

                    // 获取奖品项
                    $giftItems = $inviteRuleGiftItemsModel->getGiftItemsByRuleId($ruleId);
                    if (empty($giftItems)) {
                        continue;
                    }

                    // 根据发放模式处理奖励
                    switch ($grantMode) {
                        case $inviteGiftRulesModel::GRANT_MODE['AUTO']:
                            // 自动发放：立即创建并发放奖励
                            list($status, $message, $ruleNewGifts) = $this->processAutoGrant($inviterId, $rule, $giftItems, $userInviteGiftsModel, $nextClaimSequence);
                            break;

                        case $inviteGiftRulesModel::GRANT_MODE['MANUAL']:
                            // 手动领取：只创建奖励记录，状态为待发放
                            list($status, $message, $ruleNewGifts) = $this->processManualGrant($inviterId, $rule, $giftItems, $userInviteGiftsModel, $nextClaimSequence);
                            break;

                        case $inviteGiftRulesModel::GRANT_MODE['SCHEDULED']:
                            // 定时发放：创建奖励记录，等待定时任务处理
                            list($status, $message, $ruleNewGifts) = $this->processScheduledGrant($inviterId, $rule, $giftItems, $userInviteGiftsModel, $nextClaimSequence);
                            break;

                        default:
                            CUtil::debug('无效的发放模式: ' . $grantMode . ' 规则ID: ' . $ruleId, 'invite_gift_error');
                            continue 2;
                    }

                    if (!$status) {
                        CUtil::debug('处理奖励失败: ' . (is_string($message) ? $message : '未知错误') . ' 规则ID: ' . $ruleId, 'invite_gift_error');
                    } else {
                        // 合并该规则的新奖励到总的新奖励数组
                        $newGifts = array_merge($newGifts, $ruleNewGifts);
                    }
                }

                $transaction->commit();
                return [
                        true,
                        '奖励处理成功',
                        $newGifts
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                CUtil::debug('处理邀请奖励事务失败: ' . $e->getMessage(), 'invite_gift_error');
                return [
                        false,
                        '奖励处理失败',
                        []
                ];
            }

        } catch (\Exception $e) {
            CUtil::debug('processInviteGifts error: ' . $e->getMessage(), 'invite_gift_error');
            return [
                    false,
                    '系统异常，请稍后重试',
                    []
            ];
        }
    }

    /**
     * 处理自动发放逻辑
     * @param int $inviterId 邀请人ID
     * @param array $rule 规则信息
     * @param array $giftItems 奖品项列表
     * @param UserInviteGiftsModel $userInviteGiftsModel 用户奖励模型
     * @param int $claimSequence 领取序列号
     * @return array
     */
    public function processAutoGrant(int $inviterId, array $rule, array $giftItems, UserInviteGiftsModel $userInviteGiftsModel, int $claimSequence): array
    {
        try {
            $now      = time();
            $newGifts = [];

            foreach ($giftItems as $giftItem) {
                // 创建奖励记录
                $giftData = [
                        'inviter_id'        => $inviterId,
                        'rule_id'           => $rule['id'],
                        'rule_gift_item_id' => $giftItem['id'],
                        'gift_id'           => $giftItem['gift_id'],
                        'gift_type'         => $giftItem['gift_type'],
                        'quantity'          => $giftItem['quantity'],
                        'claim_sequence'    => $claimSequence,
                        'issued_at'         => $now,
                        'status'            => $userInviteGiftsModel::STATUS['ISSUED'],
                        // 自动发放直接设为已发放
                ];

                list($success, $result) = $userInviteGiftsModel->createGift($giftData);
                if ($success) {
                    // 执行奖品发放
                    if ($this->executeGiftDelivery($giftData, $inviterId)) {
                        $newGifts[] = $giftData;
                    } else {
                        CUtil::debug('自动发放执行失败，礼品ID: ' . $giftData['gift_id'], 'invite_gift_error');
                    }
                } else {
                    CUtil::debug('自动发放创建奖励记录失败: ' . $result, 'invite_gift_error');
                }
            }

            return [
                    true,
                    '自动发放成功',
                    $newGifts
            ];
        } catch (\Exception $e) {
            CUtil::debug('processAutoGrant error: ' . $e->getMessage(), 'invite_gift_error');
            return [
                    false,
                    '自动发放处理失败',
                    []
            ];
        }
    }

    /**
     * 处理手动领取逻辑
     * @param int $inviterId 邀请人ID
     * @param array $rule 规则信息
     * @param array $giftItems 奖品项列表
     * @param UserInviteGiftsModel $userInviteGiftsModel 用户奖励模型
     * @param int $claimSequence 领取序列号
     * @return array
     */
    private function processManualGrant(int $inviterId, array $rule, array $giftItems, UserInviteGiftsModel $userInviteGiftsModel, int $claimSequence): array
    {
        try {
            $newGifts = [];

            foreach ($giftItems as $giftItem) {
                // 创建奖励记录，状态为待发放
                $giftData = [
                        'inviter_id'        => $inviterId,
                        'rule_id'           => $rule['id'],
                        'rule_gift_item_id' => $giftItem['id'],
                        'gift_id'           => $giftItem['gift_id'],
                        'gift_type'         => $giftItem['gift_type'],
                        'quantity'          => $giftItem['quantity'],
                        'claim_sequence'    => $claimSequence,
                        'issued_at'         => null,
                        'status'            => $userInviteGiftsModel::STATUS['PENDING'],
                        // 待发放状态
                ];

                list($success, $result) = $userInviteGiftsModel->createGift($giftData);
                if ($success) {
                    $newGifts[] = $giftData;
                } else {
                    CUtil::debug('手动领取创建奖励记录失败: ' . $result, 'invite_gift_error');
                }
            }

            return [
                    true,
                    '手动领取处理成功',
                    $newGifts
            ];
        } catch (\Exception $e) {
            CUtil::debug('processManualGrant error: ' . $e->getMessage(), 'invite_gift_error');
            return [
                    false,
                    '手动领取处理失败',
                    []
            ];
        }
    }

    /**
     * 处理定时发放逻辑
     * @param int $inviterId 邀请人ID
     * @param array $rule 规则信息
     * @param array $giftItems 奖品项列表
     * @param UserInviteGiftsModel $userInviteGiftsModel 用户奖励模型
     * @param int $claimSequence 领取序列号
     * @return array
     */
    private function processScheduledGrant(int $inviterId, array $rule, array $giftItems, USerInviteGiftsModel $userInviteGiftsModel, int $claimSequence): array
    {
        try {
            $newGifts = [];

            foreach ($giftItems as $giftItem) {
                // 创建奖励记录，状态为待发放，等待定时任务处理
                $giftData = [
                        'inviter_id'        => $inviterId,
                        'rule_id'           => $rule['id'],
                        'rule_gift_item_id' => $giftItem['id'],
                        'gift_id'           => $giftItem['gift_id'],
                        'gift_type'         => $giftItem['gift_type'],
                        'quantity'          => $giftItem['quantity'],
                        'claim_sequence'    => $claimSequence,
                        'issued_at'         => null,
                        'status'            => $userInviteGiftsModel::STATUS['PENDING'],
                        // 待发放状态
                ];

                list($success, $result) = $userInviteGiftsModel->createGift($giftData);
                if ($success) {
                    $newGifts[] = $giftData;
                } else {
                    CUtil::debug('定时发放创建奖励记录失败: ' . $result, 'invite_gift_error');
                }
            }

            return [
                    true,
                    '定时发放处理成功',
                    $newGifts
            ];
        } catch (\Exception $e) {
            CUtil::debug('processScheduledGrant error: ' . $e->getMessage(), 'invite_gift_error');
            return [
                    false,
                    '定时发放处理失败',
                    []
            ];
        }
    }

    /**
     * 执行礼品发放
     * @param array $giftData 礼品数据
     * @param $inviterId
     * @return bool
     */
    private function executeGiftDelivery(array $giftData, $inviterId): bool
    {
        try {
            // TODO: 根据 gift_type 调用不同的发放接口
            // 例如：
            // - 礼品卡：调用礼品卡发放接口
            // - 优惠券：调用优惠券发放接口
            // - 积分：调用积分发放接口
            // - 商品：调用商品发放接口
            // - 购物金：调用购物金发放接口

            switch ($giftData['gift_type']) {
                case UserInviteGiftsModel::GIFT_TYPE['GIFT_CARD']: // 礼品卡
//                    $this->deliverGiftCard($giftData);
                    break;
                case UserInviteGiftsModel::GIFT_TYPE['COUPON']: // 优惠券
                    // $this->deliverCoupon($giftData);
                    break;
                case UserInviteGiftsModel::GIFT_TYPE['POINTS']: // 积分
                    $this->deliverPoints($giftData);
                    break;
                case UserInviteGiftsModel::GIFT_TYPE['GOODS']: // 商品
                    // $this->deliverGoods($giftData);
                case UserInviteGiftsModel::GIFT_TYPE['SHOPPING_VOUCHER']: // 购物金
                    $this->deliverShoppingVoucher($giftData, $inviterId);
                    break;
                case  UserInviteGiftsModel::GIFT_TYPE['CONSUME_MONEY']: // 消费金
                    $this->deliverConsumeMoney($giftData, $inviterId);
                    break;
                case UserInviteGiftsModel::GIFT_TYPE['GOLD']: // 金币
                    $this->deliverGold($giftData, $inviterId);
                    break;
                default:
                    CUtil::debug('未知的奖品类型: ' . $giftData['gift_type'], 'invite_gift_error');
                    return false;
            }

            return true;
        } catch (\Exception $e) {
            CUtil::debug('executeGiftDelivery error: ' . $e->getMessage(), 'invite_gift_error');
            return false;
        }
    }

    public function deliverShoppingVoucher($giftData, $inviterId)
    {
        $user_id = \Yii::$app->request->post('user_id');
        \Yii::$app->queue->push(new UserShopMoneyJob([
                'user_id'    => $inviterId,
                'money_type' => 'add',
                'type'       => 1,
                'money'      => 10000,
                'extend'     => $user_id,
                'remark'     => '邀请好友注册奖励'
        ]));
    }
    //CONSUME_MONEY
    public function deliverConsumeMoney($giftData, $inviterId)
    {
        // $halfPriceBuyId = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
        // // 获取活动信息以验证时间
        // list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo((int)$halfPriceBuyId);
        // $amount = 1;
        // if ($activityStatus) {
        //     $amount = $activityData['modules'][0]['extra']['consume_money_return'] ?? 1;
        // }
        // $user_id = \Yii::$app->request->post('user_id');
        // \Yii::$app->queue->push(new UserShopMoneyJob([
        //         'user_id'    => $inviterId,
        //         'money_type' => 'add',
        //         'money'      => round($amount *100),
        //         'type'       => 2,
        //         'extend'     => $user_id,
        //         'remark'     => '邀请好友注册奖励'
        // ]));
        $user_id = \Yii::$app->request->post('user_id');
        EventMsg::factory()->run('appInviteRegMoney', ['inviter_id' => (int) $inviterId, 'invitee_id' => (int) $user_id]);
    }

    /**
     * 发送邀请点赞奖励（消费金）
     * @param array $giftData 礼品数据
     * @param int $inviterId 邀请人ID
     * @throws MyExceptionModel
     */
    public function sendInviteLikeConsumeMoney(array $giftData, int $inviterId)
    {
        // $consumeMoneyLikeReturn = $giftData['consume_money_like_return'] ?? 0;
        //
        // if (! empty($consumeMoneyLikeReturn)) {
        //     // 获取被邀请人ID
        //     $user_id = \Yii::$app->request->post('user_id') ?? (int) ($giftData['invitee_id'] ?? 0);
        //     \Yii::$app->queue->push(new UserShopMoneyJob([
        //         'user_id'    => $inviterId,
        //         'money_type' => 'add',  // add增加
        //         'money'      => round($consumeMoneyLikeReturn * 100),   // 转换成分
        //         'type'       => 2, // 金额类型 2=消费金
        //         'extend'     => $user_id,
        //         'remark'     => $giftData['remark'] ?? '赚钱花点赞奖励'
        //     ]));
        // }

        $user_id = \Yii::$app->request->post('user_id');
        $invitee_id = empty($user_id) ? ($giftData['invitee_id'] ?? 0) : $user_id;
        EventMsg::factory()->run('appInviteFriendLike', ['inviter_id' => (int) $inviterId, 'invitee_id' => (int) $invitee_id]);
    }




    /**
     * 定时发放奖励（供定时任务调用）
     * @param int $limit 每次处理的最大记录数
     * @return array
     */
    public function processScheduledGifts(int $limit = 100): array
    {
        try {
            $userInviteGiftsModel = by::UserInviteGiftsModel();
            $inviteGiftRulesModel = by::InviteGiftRulesModel();

            // 获取定时发放模式的规则
            $scheduledRules = $inviteGiftRulesModel->getRulesByGrantMode($inviteGiftRulesModel::GRANT_MODE['SCHEDULED']);
            if (empty($scheduledRules)) {
                return [
                        true,
                        '无定时发放规则'
                ];
            }

            $ruleIds = array_column($scheduledRules, 'id');

            // 获取待发放的奖励记录
            $pendingGifts = $userInviteGiftsModel->getPendingGiftsByRuleIds($ruleIds, $limit);
            if (empty($pendingGifts)) {
                return [
                        true,
                        '无待发放奖励'
                ];
            }

            $successCount = 0;
            $failCount    = 0;

            foreach ($pendingGifts as $gift) {
                try {
                    // 执行发放
                    $inviterId = $gift['inviter_id'];
                    if ($this->executeGiftDelivery($gift, $inviterId)) {
                        // 更新状态为已发放
                        $updateData = [
                                'status'    => $userInviteGiftsModel::STATUS['ISSUED'],
                                'issued_at' => time(),
                        ];
                        $userInviteGiftsModel->updateGift($gift['id'], $updateData);
                        $successCount++;
                    } else {
                        $failCount++;
                        CUtil::debug('定时发放失败，礼品ID: ' . $gift['id'], 'invite_gift_scheduled_error');
                    }
                } catch (\Exception $e) {
                    $failCount++;
                    CUtil::debug('定时发放异常，礼品ID: ' . $gift['id'] . ' 错误: ' . $e->getMessage(), 'invite_gift_scheduled_error');
                }
            }

            return [
                    true,
                    "定时发放完成，成功：{$successCount}，失败：{$failCount}"
            ];

        } catch (\Exception $e) {
            CUtil::debug('processScheduledGifts error: ' . $e->getMessage(), 'invite_gift_scheduled_error');
            return [
                    false,
                    '定时发放处理失败',
                    []
            ];
        }
    }

    /**
     * @param array $giftData
     * @return void
     */
    private function deliverPoints(array $giftData)
    {
        EventMsg::factory()->run('appInviteReg', ['user_id' =>$giftData['inviter_id']]);
    }


    public function deliverGold($giftData, $inviterId)
    {
        EventMsg::factory()->run('appInviteRegGold', ['user_id' => $inviterId]);
    }

}