<?php

namespace app\modules\main\services\invite;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\MemberActivityService;
use http\Env;

/**
 * 通用邀请服务类
 * 使用工厂模式根据不同的invite_type实现不同的业务逻辑
 */
class InviteService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 邀请类型常量
    const INVITE_TYPE = [
            'ONE_YUAN_SECKILL' => 1,
        // 一元秒杀
            'FRIEND_BUY'       => 2,
            // 邀请好友一起购
            'REG_MALL_INVITE'  => 3,
            // 商城注册邀请
            'POINT_BUY_INVITE' => 4,
            // 积分购买邀请
            'CONSUME_MONEY_INVITE' => 5,
            // ... 可以继续添加其他邀请类型
            'GOLD_INVITE'      => 7,
            // 金币邀请
    ];

    /*
     * 活动配置
     */
    const CONFIG = [
        // 好友购买邀请处理器配置
            'FriendBuyInviteHandler' => [
                // 是否检查新用户条件
                    'check_new_user'   => YII_ENV_PROD,
                // 邀请频率限制
                    'frequency_limit'  => [
                            'enabled'             => false,
                            'max_invites_per_day' => 10,
                        // 每日最多邀请10人
                    ],
                // 奖励集成配置
                    'gift_integration' => [
                            'enabled'      => true,
                            'auto_process' => true,
                        // 是否自动处理奖励
                    ],
            ],
        // 商城注册邀请处理器配置
            'RegMallInviteHandler'   => [
                // 是否检查新用户条件
                    'check_new_user'   => YII_ENV_PROD,
                // 邀请频率限制
                    'frequency_limit'  => [
                            'enabled'             => false,
                            'max_invites_per_day' => 10,
                        // 每日最多邀请10人
                    ],
                // 奖励集成配置
                    'gift_integration' => [
                            'enabled'      => true,
                            'auto_process' => true,
                        // 是否自动处理奖励

            ],
        ],
        // 商城注册邀请处理器配置
        'GoldInviteHandler' => [
            // 是否检查新用户条件
                'check_new_user'   => YII_ENV_PROD,
            // 邀请频率限制
                'frequency_limit'  => [
                        'enabled'             => false,
                        'max_invites_per_day' => 10,
                    // 每日最多邀请10人
                ],
            // 奖励集成配置
                'gift_integration' => [
                        'enabled'      => true,
                        'auto_process' => true,
                    // 是否自动处理奖励

                    ],
            ],
        // 消费金邀请处理器配置
            'ConsumeMoneyInviteHandler'   => [
                // 是否检查新用户条件
                    'check_new_user'   => YII_ENV_PROD,
                // 邀请频率限制
                    'frequency_limit'  => [
                            'enabled'             => false,
                            'max_invites_per_day' => 10,
                        // 每日最多邀请10人
                    ],
                // 奖励集成配置
                    'gift_integration' => [
                            'enabled'      => true,
                            'auto_process' => true,
                        // 是否自动处理奖励

                    ],
            ],
    ];

    /**
     * 通用邀请方法
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @param string|null $deviceId 设备ID
     * @param string|null $remark 备注信息
     * @return array
     */
    public function invite(int $inviterId, int $inviteType, int $relateId, int $inviteeId, string $deviceId = null, string $remark = null): array
    {
        try {
            // 根据邀请类型获取对应的处理器
            $handler = $this->getInviteHandler($inviteType);

            if (!$handler) {
                return [
                        false,
                        '不支持的邀请类型'
                ];
            }

            // 执行邀请前的验证
            list($checkStatus, $checkMessage) = $handler->checkInviteEligibility($inviterId, $inviteType, $relateId, $inviteeId);
            if (!$checkStatus) {
                return [
                        false,
                        $checkMessage
                ];
            }

            // 开始事务
            $db          = by::dbMaster();
            $transaction = $db->beginTransaction();

            try {
                // 如果没有提供remark且handler支持生成remark，则生成默认remark
                if ($remark === null && method_exists($handler, 'generateRemark')) {
                    $remark = $handler->generateRemark($inviteType);
                }

                // 创建邀请记录
                $userInviteModel = by::UserInviteModel();
                list($inviteStatus, $inviteMsg) = $userInviteModel->createInvite(
                        $inviterId,
                        $inviteeId,
                        $inviteType,
                        $relateId,
                        $deviceId,
                        $remark
                );

                if (!$inviteStatus) {
                    $transaction->rollBack();
                    return [
                            false,
                            $inviteMsg
                    ];
                }

                // 执行业务逻辑
                list($businessStatus, $businessResult) = $handler->processInviteBusiness($inviterId, $inviteType, $relateId, $inviteeId);
                if (!$businessStatus) {
                    throw new \Exception($businessResult);
                }

                // 更新邀请统计
                $this->updateInviteStatistics($inviterId);

                // 处理邀请奖励
                list($status_gift, $message) = $handler->processInviteGifts($inviterId, $inviteType, $relateId);
                if (!$status_gift) {
                    $transaction->rollBack();
                    return [
                            false,
                            $message
                    ];
                }

                $transaction->commit();

                return [
                        true,
                        $businessResult
                ];

            } catch (\Exception $e) {
                $transaction->rollBack();
                throw $e;
            }

        } catch (\Exception $e) {
            CUtil::debug('invite error: ' . $e->getMessage(), 'invite_error');
            return [
                    false,
                    '邀请失败'
            ];
        }
    }

    /**
     * 检查用户是否可以被邀请
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function checkInviteEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            // 根据邀请类型获取对应的处理器
            $handler = $this->getInviteHandler($inviteType);
            if (!$handler) {
                return [
                        false,
                        '不支持的邀请类型'
                ];
            }

            // 执行邀请前的验证
            return $handler->checkInviteEligibility($inviterId, $inviteType, $relateId, $inviteeId);

        } catch (\Exception $e) {
            CUtil::debug('checkInviteEligibility error: ' . $e->getMessage(), 'invite_error');
            return [
                    false,
                    '检查失败'
            ];
        }
    }


    /**
     * 获取邀请信息详情
     * @param int $userId 用户ID
     * @param int $inviteType 邀请类型
     * @param int|null $relateId 关联规则ID，为null时返回所有规则
     * @return array
     */
    public function getInviteInfo(int $userId, int $inviteType, int $relateId = 0): array
    {
        try {
            $userInviteModel      = by::UserInviteModel();
            $userInviteLikeModel  = by::UserInviteLikeModel();
            $inviteGiftRulesModel = by::InviteGiftRulesModel();
            $userInviteGiftsModel = by::UserInviteGiftsModel();

            // 获取当前用户的邀请人数（invite_type为该类型，relate_id为0表示总数）
            $inviteCount = $userInviteModel->getInviteCount($userId, $inviteType, $relateId);
            $likeCount = $userInviteLikeModel->getInviteLikeCount($userId, $inviteType, $relateId);

            // 根据是否传入relate_id获取规则
            if ($relateId !== null) {
                // 获取指定的规则
                $rule = $inviteGiftRulesModel->getRuleById($relateId);
                if (empty($rule) || $rule['invite_type'] != $inviteType) {
                    return [
                            false,
                            '指定的规则不存在或邀请类型不匹配'
                    ];
                }
                $allRules = [$rule];
            } else {
                // 获取所有激活的邀请规则
                $allRules = $inviteGiftRulesModel->getActiveRulesByType($inviteType);
            }


            // 处理规则信息
            $inviteRules = [];
            foreach ($allRules as $rule) {
                $ruleId = $rule['id'];


                $inviteRules[] = [
                        'rule_id'        => (int) $ruleId,
                        'rule_name'      => $rule['rule_name'] ?: '',
                        'rule_details'   => $rule['rule_details'] ?: '',
                        'required_count' => (int) $rule['required_count'],
                    // 'grant_mode_name' => $this->getGrantModeName($rule['grant_mode']),
                    // 'gift_items'     => $formattedGiftItems,
                ];
            }

            // 根据是否指定了relate_id决定返回格式
            if ($relateId !== null) {
                // 返回单个规则对象
                $inviteRule = !empty($inviteRules) ? $inviteRules[0] : null;
                $result     = [
                        'invite_count'   => $inviteCount,
                        'like_count'     => $likeCount,
                        // 'pending_amount' => byNew::UserShopMoneyModel()->getInfoByUserId($userId, 1,2),
                        'pending_amount' => byNew::UserShopMoneyModel()->getInfoByUserId($userId, $inviteType == 5 ? 2 : 1,2),
                        'invite_rules'   => $inviteRule,
                ];
            } else {
                // 返回规则数组（保持向后兼容）
                $result = [
                        'invite_count'   => $inviteCount,
                        'like_count'     => $likeCount,
                        'pending_amount' => 0,
                        'invite_rules'   => $inviteRules,
                ];
            }

            return [
                    true,
                    $result
            ];

        } catch (\Exception $e) {
            CUtil::debug('getInviteInfo error: ' . $e->getMessage(), 'invite_error');
            return [
                    false,
                    '获取邀请信息失败'
            ];
        }
    }


    /**
     * 根据邀请奖励获取用户礼品卡ID
     * @param int $userId 用户ID
     * @param $userInviteGiftsModel
     * @return array
     */
    private function getUserCardIdsByInviteGifts(int $userId, $userInviteGiftsModel): array
    {
        try {
            // 获取用户所有已发放的邀请奖励
            $inviteGifts = $userInviteGiftsModel->getGiftsByInviterId($userId, 1); // status=1 已发放

            $giftCardIds = [];
            foreach ($inviteGifts as $gift) {
                // 只处理礼品卡类型的奖励
                if ($gift['gift_type'] == 1) { // GIFT_CARD
                    $giftCardIds[] = $gift['gift_id'];
                }
            }

            if (empty($giftCardIds)) {
                return [];
            }

            // 根据您的需求，查询 user_id=$userId, type=3, card_id in $giftCardIds 的记录
            $giftUserCardsModel = byNew::GiftUserCards();
            $userCardIds        = $giftUserCardsModel::find()
                    ->where([
                            'user_id' => $userId,
                            'type'    => 3
                    ])
                    ->andWhere([
                            'IN',
                            'card_id',
                            $giftCardIds
                    ])
                    ->select('id')
                    ->column(); // 使用column()更高效地获取ID数组

            return $userCardIds;

        } catch (\Exception $e) {
            CUtil::debug('getUserCardIdsByInviteGifts error: ' . $e->getMessage(), 'invite_error');
            return [];
        }
    }

    /**
     * 根据邀请类型获取对应的处理器
     * @param int $inviteType
     * @return InviteHandlerInterface|null
     */
    private function getInviteHandler(int $inviteType)
    {
        // 使用工厂模式创建处理器
        return InviteHandlerFactory::createHandler($inviteType);
    }

    /**
     * 更新邀请统计
     * @param int $inviterId 邀请人ID
     * @return bool
     */
    private function updateInviteStatistics(int $inviterId): bool
    {
        try {
            $userInviteRankModel = by::UserInviteRankModel();
            return $userInviteRankModel->updateInviteCount($inviterId, 1);
        } catch (\Exception $e) {
            CUtil::debug('updateInviteStatistics error: ' . $e->getMessage(), 'invite_error');
            return false;
        }
    }

    /**
     * 通用邀请点赞方法
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @param string|null $deviceId 设备ID
     * @param string|null $remark 备注信息
     * @return array
     */
    public function inviteLike(int $inviterId, int $inviteType, int $relateId, int $inviteeId, string $deviceId = null, string $remark = null): array
    {
        if ($inviterId == $inviteeId) {
            return [false, '您不能给自己点赞', 0];
        }

        $userInviteModel = by::UserInviteLikeModel();
        $consume_money_id = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id) ?? 0;
        if (empty($consume_money_id)) {
            return [false, '赚钱花活动未配置', 0];
        }

        // 判断是否能点赞
        list($status, $msg) = $userInviteModel->checkCanLike($consume_money_id, $inviterId, $inviteeId);
        if (! $status) {
            return [false, $msg, 0];
        }

        // 开始事务
        $db = by::dbMaster();
        $transaction = $db->beginTransaction();
        try {
            $service = InviteGiftService::getInstance();
            // 获取活动信息以验证时间
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo((int) $consume_money_id);
            $consumeMoneyLikeReturn = 0;
            if ($activityStatus) {
                $consumeMoneyLikeReturn = $activityData['modules'][0]['extra']['consume_money_like_return'] ?? 0;
            }

            // 创建点赞记录
            list($inviteStatus, $inviteMsg) = $userInviteModel->createInviteLike(
                $inviterId,
                $inviteeId,
                $inviteType,
                $relateId,
                $deviceId,
                $remark
            );

            if (! $inviteStatus) {
                throw new BusinessException($inviteMsg);
            }

            $redis = by::redis();
            $key = AppCRedisKeys::getInviteLikeCacheKey($consume_money_id, $inviterId);
            $redis->sAdd($key, $inviteeId);

            $service->sendInviteLikeConsumeMoney(['inviter_id' => $inviterId, 'relate_id' => $relateId, 'remark' => $remark, 'consume_money_like_return' => $consumeMoneyLikeReturn, 'invitee_id' => $inviteeId], $inviterId);

            $transaction->commit();
            return [
                true,
                '点赞成功',
                $consumeMoneyLikeReturn
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            CUtil::debug('invite like error: ' . $e->getMessage(), 'invite_like_error');
            return [
                false,
                '点赞失败',
                0
            ];
        }
    }
}

