<?php

namespace app\modules\main\services\invite;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\DreamAmbassadorService;
use app\modules\main\models\UserPopupModel;

/**
 * 商城注册邀请处理器
 * 处理 invite_type=3 的邀请业务逻辑
 */
class RegMallInviteHandler extends AbstractInviteHandler
{
    /**
     * 检查邀请资格
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID（对于好友购买，可能是商品ID或订单ID）
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function checkInviteEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            // 基础验证
            list($isValid, $message, $userInfo) = $this->validateBasicEligibility($inviterId, $inviteType, $relateId, $inviteeId);
            if (!$isValid) {
                return [false, $message, []];
            }

            $config = $this->getHandlerConfig();

            // 检查新用户条件
            list($isValid, $message) = $this->validateNewUser($inviteeId, $config);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查邀请频率限制
            list($isValid, $message) = $this->validateInviteFrequency($inviterId, $inviteType, $config);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查是否已被邀请过（全局检查）
            list($isValid, $message) = $this->validateGlobalInvite($inviteeId);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查用户对相关规则的最大领取次数限制
            list($isValid, $message) = $this->validateRuleClaimLimit($inviterId, $relateId);
            if (!$isValid) {
                return [false, $message, []];
            }

            return [
                true,
                '可以参与邀请',
                $this->buildSuccessData($userInfo['inviterInfo'], $userInfo['inviteeInfo'])
            ];

        } catch (\Exception $e) {
            $this->logError('checkInviteEligibility', $e->getMessage());
            return [false, '检查失败', []];
        }
    }

    /**
     * 处理邀请业务逻辑
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function processInviteBusiness(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            list($status, $result) = $this->processCommonBusiness($inviterId, $inviteType, $relateId, $inviteeId);
            if (!$status) {
                return [false, $result];
            }

            $inviteCount = $result['current_invite_count']??0;
            // 成为追觅推广大使
            if ($inviteCount == DreamAmbassadorService::BECOME_AMBASSADOR_INVITE_COUNT){

                // 1. 在 user_invite_rank 表中标记字段
                by::UserInviteRankModel()->updateAmbassadorStatus($inviterId, 1);
                
                // 2. 添加弹窗记录
                byNew::UserPopupModel()->savePopup(
                    $inviterId, 
                    byNew::UserPopupModel()::POPUP_TYPE['BECOME_AMBASSADOR'],
                    '成为追觅推广大使',
                    true // 只添加一次弹窗
                );

                CUtil::debug( '成为追觅推广大使|inviter_id:' . $inviterId, 'dream_ambassador_become');
            }

        } catch (\Exception $e) {
            $this->logError('processInviteBusiness', $e->getMessage());
            return [false, '处理失败：' . $e->getMessage()];
        }
        return [true, $result];
    }

    /**
     * 处理邀请奖励
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     */
    public function processInviteGifts(int $inviterId, int $inviteType, int $relateId): array
    {
        try {
            $inviteGiftService = InviteGiftService::getInstance();
            list($status, $message, $gifts) = $inviteGiftService->processInviteGifts($inviterId, $inviteType, $relateId);

            if ($status && !empty($gifts)) {
                $quantities = array_column($gifts, 'quantity');
                $total_point = !empty($quantities) ? array_sum($quantities) : 0;

                // 会员商城邀请好友注册记录弹窗
                byNew::UserPopupModel()->savePopup($inviterId, UserPopupModel::POPUP_TYPE['MEMBER_MALL_INVITE'], $total_point);
                CUtil::debug("用户 {$inviterId} 获得新的邀请奖励：" . json_encode($gifts), 'invite_gift_' . $inviteType);

                return [$status, $message];
            }

        } catch (\Exception $e) {
            $this->logError('processInviteGifts', $e->getMessage(), 'invite_gift_error');
        }

        return [false, '处理邀请奖励失败,请联系客服'];
    }

    /**
     * 生成REG_MALL_INVITE类型的备注信息
     * @param int $inviteType 邀请类型
     * @return string|null
     */
    public function generateRemark(int $inviteType)
    {
        if ($inviteType === InviteService::INVITE_TYPE['REG_MALL_INVITE']) {
            return "新用户邀请注册";
        }
        return null;
    }
} 