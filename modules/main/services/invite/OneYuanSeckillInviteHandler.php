<?php

namespace app\modules\main\services\invite;

use app\components\EventMsg;
use app\models\by;
use app\models\CUtil;
use app\modules\main\services\MemberActivityService;
use app\modules\main\services\OneYuanSeckillService;
use yii\db\Exception;

/**
 * 一元秒杀邀请处理器
 */
class OneYuanSeckillInviteHandler extends AbstractInviteHandler
{


    /**
     * 获取不通过邀请新用户完成的活动ID列表
     * @return array 不允许邀请新用户的活动ID数组
     * @throws Exception
     */
    public static function canNotInviteNewUserActivity(): array
    {
        $excludedActivities = [];
        $allDiscountThreeActivityId = CUtil::dictValue('user_invite_activity.all_discount_three_activity_id');

        if ($allDiscountThreeActivityId > 0) {
            $excludedActivities[] = $allDiscountThreeActivityId;
        } else {
            throw new Exception('未配置全场三折购活动ID');
        }

        return $excludedActivities;
    }

    /**
     * 检查邀请资格
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 秒杀ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function checkInviteEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            $oneYuanSeckillModel = by::OneYuanSeckillModel();
            $userInviteModel = by::UserInviteModel();


            // 获取秒杀记录
            $seckillRecord = $oneYuanSeckillModel->getSeckillById($relateId);
            if (empty($seckillRecord)) {
                return [false, '助力活动不存在', []];
            }

            // 此活动不能通过邀请新用户完成
            if (in_array($seckillRecord['activity_id'], $this->canNotInviteNewUserActivity())) {
                return [false, '此活动不能通过邀请新用户完成', []];
            }

            // 检查助力状态
            if ($seckillRecord['status'] != OneYuanSeckillService::STATUS['IN_PROGRESS']) {
                return [false, '助力活动已结束', ['type' => 'help_is_end']];
            }

            // 验证邀请人权限
            if ($seckillRecord['user_id'] != $inviterId) {
                return [false, '无权限进行此邀请', []];
            }

            // 检查是否为自己助力
            if ($userInviteModel->isSelfInvite($inviterId, $inviteeId)) {
                return [false, '不能邀请自己助力', []];
            }

            // 检查是否已经助力过
            if ($userInviteModel->hasInvited($inviteeId, $inviteType, $relateId)) {
                return [false, '用户已经为该活动助力过了', []];
            }

            // 检查新用户条件（与原有逻辑保持一致）
            $drawActivityService = \app\modules\goods\services\DrawActivityService::getInstance();
            list($status, $message, $data) = $drawActivityService->isNewUser($inviteeId);
            if (!$status) {
                return [false, '用户不符合助力条件', []];
            }

            // 获取活动信息以验证时间
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($seckillRecord['activity_id'], $inviteeId);
            if (!$activityStatus) {
                return [false, '活动信息获取失败', []];
            }

            // 验证活动时间
            $now = time();
            $startTime = $activityData['base_info']['start_time'] ?? 0;
            $endTime = $activityData['base_info']['end_time'] ?? 0;

            if ($now < $startTime || $now > $endTime) {
                return [false, '活动已结束，无法助力', []];
            }

            return [true, '可以参与邀请', $this->buildSeckillRetData($inviterId, $startTime, $endTime)];

        } catch (\Exception $e) {
            $this->logError('checkInviteEligibility', $e->getMessage());
            return [false, '检查失败', []];
        }
    }

    /**
     * 构建秒杀特有的返回数据
     * @param int $inviterId 邀请人ID
     * @param int $startTime 活动开始时间
     * @param int $endTime 活动结束时间
     * @return array
     */
    private function buildSeckillRetData(int $inviterId, int $startTime, int $endTime): array
    {
        // 用户信息
        $inviterInfo = by::users()->getOneByUid($inviterId);
        return [
            'nick' => $inviterInfo['nick'] ?? '匿名用户',
            'avatar' => CUtil::avatar($inviterInfo),
            'active_start_time' => $startTime,
            'active_end_time' => $endTime,
        ];
    }

    /**
     * 处理邀请业务逻辑
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 秒杀ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function processInviteBusiness(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            $oneYuanSeckillModel = by::OneYuanSeckillModel();
            $userInviteModel = by::UserInviteModel();

            // 获取秒杀记录
            $seckillRecord = $oneYuanSeckillModel->getSeckillById($relateId);
            if (empty($seckillRecord)) {
                throw new \Exception('秒杀记录不存在');
            }

            // 获取活动信息以检查是否达到助力要求
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($seckillRecord['activity_id'], $inviteeId);
            if (!$activityStatus) {
                throw new \Exception('活动信息获取失败');
            }

            // 重新计算邀请人数（在邀请记录创建后）
            $newInviteCount = $userInviteModel->getInviteCount(
                $inviterId,
                $inviteType,
                $relateId,
                false // 统计所有有效邀请，不限制当天
            );

            // 更新秒杀表中的邀请人数
            $updateResult = $oneYuanSeckillModel->updateInviteMembers($relateId, $newInviteCount);
            if (!$updateResult) {
                $this->logError('processInviteBusiness', "更新邀请人数失败: seckillId={$relateId}, count={$newInviteCount}");
            }

            // 获取所需邀请人数
            $requiredInviteNumber = $this->getRequiredInviteNumberFromActivity($activityData, $seckillRecord['gid']);
            
            // 检查是否达到助力要求
            $isSuccess = $newInviteCount >= $requiredInviteNumber;

            if ($isSuccess) {
                // 助力成功，更新状态
                $statusUpdateResult = $oneYuanSeckillModel->updateStatus($relateId, OneYuanSeckillService::STATUS['SUCCESS']);
                if (!$statusUpdateResult) {
                    $this->logError('processInviteBusiness', "更新助力状态失败: seckillId={$relateId}");
                    // 状态更新失败不应该影响整个流程，记录日志即可
                }
            }

            $result = [
                'message' => '助力成功',
                'current_invite_count' => $newInviteCount,
                'required_invite_count' => $requiredInviteNumber,
                'is_success' => $isSuccess,
            ];

            return [true, $result];
        } catch (\Exception $e) {
            $this->logError('processInviteBusiness', $e->getMessage());
            return [false, '处理失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理邀请奖励
     * 一元秒杀邀请类型不需要发放奖励
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @throws Exception
     */
    public function processInviteGifts(int $inviterId, int $inviteType, int $relateId): array
    {
        // 一元秒杀邀请类型(type=1)不需要发放奖励，这里为空实现
        $seckillRecord = by::OneYuanSeckillModel()->getSeckillById($relateId);
        $activityId    = $seckillRecord['activity_id'] ?? 0;

        // 三折购活动的特殊处理
        if ($activityId && $activityId == CUtil::dictValue('user_invite_activity.discount_three_activity_id')) {
            EventMsg::factory()->run('addThreeBuy', ['user_id' => $inviterId]);
            return [true, '邀请参与3折购'];
        }

        // 助力发奖励金
        if ($activityId && $activityId == CUtil::dictValue('user_invite_activity.discount_one_yuan_activity_id')){
            EventMsg::factory()->run('oneYuanFriendSupport', ['user_id' => $inviterId]);
        }

        return [true, '不需要发放奖励'];
    }

    /**
     * 从活动数据中获取需要的助力人数
     * @param array $activityData
     * @param int $gid
     * @return int|mixed
     * @throws \Exception
     */
    public function getRequiredInviteNumberFromActivity(array $activityData, int $gid)
    {
        $modules = $activityData['modules'] ?? [];
        foreach ($modules as $module) {
            if (($module['module_code'] ?? '') === 'SNAP_UP') {
                $extra = $module['extra'] ?? [];
                foreach ($extra as $item) {
                    if (($item['goods_id'] ?? 0) == $gid) {
                        return $item['extra']['invite_number'] ?? 3;
                    }
                }
            }
        }

        // 异常处理
        throw new \Exception('活动数据中未找到对应商品的助力人数配置');
    }
} 