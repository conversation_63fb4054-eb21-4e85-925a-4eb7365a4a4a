<?php

namespace app\modules\main\services\invite;

/**
 * 邀请处理器接口
 */
interface InviteHandlerInterface
{
    /**
     * 检查邀请资格
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function checkInviteEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array;

    /**
     * 处理邀请业务逻辑
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function processInviteBusiness(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array;

    /**
     * 处理邀请奖励
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @return void
     */
    public function processInviteGifts(int $inviterId, int $inviteType, int $relateId);
} 