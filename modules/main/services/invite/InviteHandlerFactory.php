<?php

namespace app\modules\main\services\invite;

use app\models\CUtil;

/**
 * 邀请处理器工厂类
 * 提供可配置的处理器创建机制
 */
class InviteHandlerFactory
{
    // 缓存处理器实例
    private static $handlers = [];
    
    // 默认处理器映射
    private static $defaultHandlers = [
        InviteService::INVITE_TYPE['ONE_YUAN_SECKILL'] => OneYuanSeckillInviteHandler::class, // 一元秒杀
        InviteService::INVITE_TYPE['FRIEND_BUY'] => FriendBuyInviteHandler::class,      // 邀请好友一起购
        InviteService::INVITE_TYPE['REG_MALL_INVITE'] => RegMallInviteHandler::class, // 商城注册邀请
        InviteService::INVITE_TYPE['CONSUME_MONEY_INVITE'] => ConsumeMoneyInviteHandler::class, // 消费金
        InviteService::INVITE_TYPE['GOLD_INVITE'] => GoldInviteHandler::class, // 金币邀请
    ];

    /**
     * 根据邀请类型获取对应的处理器
     * @param int $inviteType 邀请类型
     * @return InviteHandlerInterface|null
     */
    public static function createHandler(int $inviteType)
    {
        // 如果已缓存，直接返回
        if (isset(self::$handlers[$inviteType])) {
            return self::$handlers[$inviteType];
        }

        // 从配置获取处理器映射
        $handlerClass = self::$defaultHandlers[$inviteType] ?? null;

        if (!$handlerClass) {
            CUtil::debug("邀请类型 {$inviteType} 没有对应的处理器", 'invite_handler_factory');
            return null;
        }

        // 检查处理器类是否存在
        if (!class_exists($handlerClass)) {
            CUtil::debug("处理器类 {$handlerClass} 不存在", 'invite_handler_factory');
            return null;
        }

        // 检查是否实现了接口
        if (!in_array(InviteHandlerInterface::class, class_implements($handlerClass))) {
            CUtil::debug("处理器类 {$handlerClass} 没有实现 InviteHandlerInterface 接口", 'invite_handler_factory');
            return null;
        }

        try {
            // 创建处理器实例
            $handler = new $handlerClass();
            
            // 缓存处理器实例
            self::$handlers[$inviteType] = $handler;
            
            return $handler;
        } catch (\Exception $e) {
            CUtil::debug("创建处理器实例失败: {$handlerClass}, 错误: " . $e->getMessage(), 'invite_handler_factory');
            return null;
        }
    }

}