<?php

namespace app\modules\main\services\invite;

use app\models\by;
use app\models\CUtil;
use app\modules\main\services\MemberActivityService;

/**
 * 好友购买邀请处理器
 * 处理 invite_type=2 的邀请业务逻辑
 */
class GoldInviteHandler extends AbstractInviteHandler
{
    /**
     * 检查邀请资格
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID（对于好友购买，可能是商品ID或订单ID）
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function checkInviteEligibility(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            // 基础验证
            list($isValid, $message, $userInfo) = $this->validateBasicEligibility($inviterId, $inviteType, $relateId, $inviteeId);
            if (!$isValid) {
                return [false, $message, []];
            }

            $config = $this->getHandlerConfig();

            // 检查新用户条件
            list($isValid, $message) = $this->validateNewUser($inviteeId, $config);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查邀请频率限制
            list($isValid, $message) = $this->validateInviteFrequency($inviterId, $inviteType, $config);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查是否已被邀请过（全局检查）
            list($isValid, $message) = $this->validateGlobalInvite($inviteeId);
            if (!$isValid) {
                return [false, $message, []];
            }

            // 检查用户对相关规则的最大领取次数限制
            list($isValid, $message) = $this->validateRuleClaimLimit($inviterId, $relateId);
            if (!$isValid) {
                return [false, $message, []];
            }

            return [
                    true,
                    '可以参与邀请',
                    $this->buildSuccessData($userInfo['inviterInfo'], $userInfo['inviteeInfo'])
            ];

        } catch (\Exception $e) {
            $this->logError('checkInviteEligibility', $e->getMessage());
            return [false, '检查失败', []];
        }
    }

    /**
     * 处理邀请业务逻辑
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     * @param int $inviteeId 被邀请人ID
     * @return array
     */
    public function processInviteBusiness(int $inviterId, int $inviteType, int $relateId, int $inviteeId): array
    {
        try {
            return $this->processCommonBusiness($inviterId, $inviteType, $relateId, $inviteeId);
        } catch (\Exception $e) {
            $this->logError('processInviteBusiness', $e->getMessage());
            return [false, '处理失败：' . $e->getMessage()];
        }
    }

    /**
     * 处理邀请奖励
     * @param int $inviterId 邀请人ID
     * @param int $inviteType 邀请类型
     * @param int $relateId 关联ID
     */
    public function processInviteGifts(int $inviterId, int $inviteType, int $relateId): array
    {
        return $this->processCommonInviteGifts($inviterId, $inviteType, $relateId);
    }

    /**
     * 生成FRIEND_BUY类型的备注信息
     * @param int $inviteType 邀请类型
     * @return string|null
     */
    public function generateRemark(int $inviteType)
    {
        if ($inviteType == InviteService::INVITE_TYPE['GOLD_INVITE']) {
            return "金币邀请";
        }
        return null;
    }


} 