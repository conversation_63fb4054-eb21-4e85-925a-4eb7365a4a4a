<?php

namespace app\modules\main\services;


use app\models\byNew;
use app\modules\goods\models\OfficialIntroModel;

class ProductMatrixService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function categoryList(): array
    {
        // 获取所有分类及其对应的详细信息
        $categories = byNew::GoodsCategoryModel()->getList();

        // 获取所有分类的 ID
        $categoryIds = array_column($categories, 'id');

        // 如果没有分类ID，直接返回空数组
        if (empty($categoryIds)) {
            return [];
        }

        // 批量查询所有分类的详细信息，返回分类ID列表
        $detailIds = byNew::ProductMatrixModel()->getDetailIdsByCategoryIds($categoryIds);

        // 如果没有详细信息ID，直接返回空数组
        if (empty($detailIds)) {
            return [];
        }

        // 将分类和对应的详情合并，返回包含分类详情的数组
        foreach ($categories as $key => $category) {
            // 如果 当前品类没有配置详情
            if (!in_array($category['id'], $detailIds)) {
                unset($categories[$key]);
            }
        }

        // 返回更新后的分类列表
        return array_values($categories);
    }

    public function getDetail($categoryId): array
    {
        $detail = byNew::ProductMatrixModel()->getDetail($categoryId);
        // 如果数据为空，返回空数组
        if (empty($detail)) {
            return [];
        }
        // 顶部卡片数据
        if (!empty($detail['main_product'])) {
            $detail['main_product'] = json_decode($detail['main_product'], true);
        }
        // 底部卡片数据
        if (!empty($detail['sub_products'])) {
            $detail['sub_products'] = json_decode($detail['sub_products'], true);
        }
        return $detail;
    }


    public function introDetail($id): array
    {
        $detail = byNew::OfficialIntroModel()->getDetail($id);

        // 直接判断状态并返回结果，减少额外变量和分支
        return ($detail['status'] ?? 0) === OfficialIntroModel::STATUS['DOWN'] ? [] : $detail;
    }




}