<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\CommPayModel;
use app\modules\main\models\pay\PayModel;

/**
 * 支付宝支付
 */
class AliPayService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    //下单来源
    CONST SOURCE = [
        'MALL'     => 1,
        'PLUMBING' => 2,
        'DEPOSIT'  => 3,
        'POINTS'   => 4,//积分商城
    ];

    public function AgainPay($user_id, $order_no, $order_type = 1) // 默认普通订单
    {
        if (empty($user_id) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //是否需要重新创建订单
        $needReunified = 0;
        $endPayment = 0;


        if ($order_type == by::Odeposit()::TYPE['DEPOSIT']){
            $oInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }

            $oeInfo = by::OdepositE()->GetInfoByOrderId($user_id,$order_no);
            $cfg = $oeInfo['cfg'] ?? [];

            //定金订单
            $endPayment = $cfg['presale_time'] ?? 0;
            $needReunified = 1;

        }else{
            $oInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
            if(isset($oInfo['deposit_order_no']) && $oInfo['deposit_order_no']){
                $now = intval(START_TIME);
                $oInfo['ctime'] = $now;
                $oMainInfo = by::Omain()->getInfoByOrderNo($user_id,$order_no);

                //尾款订单校验库存
                $commPay = new CommPayModel();
                list($s,$msg) = $commPay->CheckTailOrderStock($user_id,$oInfo['deposit_order_no']);
                if(!$s){
                    return [$s,$msg];
                }

                //定金订单
                $endPayment = $oMainInfo['end_payment'] ?? 0;
                $needReunified = 2;
            }

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] != by::Omain()::ORDER_STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }
        }

        //查支付宝订单是否已付款
        if ($this->isPaySuccess($order_no)) {
            return [false, '该订单已支付'];
        }

        //付款流水
        $aOpay      = by::model('OPayModel','goods')->GetOneInfo($order_no,false);
        if (empty($aOpay)) {
            return [false, '订单不存在(2)'];
        }

        // 重新创建
        $rebuild = false;
        // 切换支付方式
        if ($aOpay['pay_type'] != by::Omain()::PAY_BY_ALIPAY) {
            // 保存支付信息
            // 保存支付信息
            if ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET) {
                PayModel::getInstance()->saveTradeOrder($order_no, $aOpay['pay_type'], $aOpay['prepay_id'], $aOpay['h5_url'], $aOpay['ptime']);
            }

            // 新支付方式的支付信息
            $currentPayData = PayModel::getInstance()->getTradeOrder($order_no, by::Omain()::PAY_BY_ALIPAY);

            if ($currentPayData && ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET)) {
                // 更新支付流水表
                $payData = [
                    'prepay_id' => $currentPayData['prepay_id'], 'ptime' => $currentPayData['ptime'], 'h5_url' => $currentPayData['h5_url'], 'pay_type' => by::Omain()::PAY_BY_ALIPAY,
                ];
                by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);
                // 更新数据
                $aOpay['prepay_id'] = $currentPayData['prepay_id'];
                $aOpay['ptime'] = $currentPayData['ptime'];
            } else {
                // 新支付方式的支付信息不存在，重新生成
                $rebuild = true;
            }
        }

        $now        = time();
        if ($rebuild || (empty($aOpay['prepay_id']) && $aOpay['price']) || bcsub($now, $aOpay['ptime']) > 7100 ) {
            $other              = [
                'body'          => '订单支付',
                'ctime'         => $oInfo['ctime'],
                'needReunified' => $needReunified,
                'endPayment'    => $endPayment,
            ];

            // 支付过期时间
            list($status, $time_expire) = $this->getPayExpireTime($other, 7100);
            if (!$status) {
                return [false, $time_expire];
            }
            $params = [
                'user_id'     => $user_id,
                'time_expire' => $time_expire,
                'order_type'  => $order_type, // 商品订单
            ];
            list($status, $alipay_body) = AliPayModel::getInstance()->pay($order_no, bcdiv($aOpay['price'], 100, 2), $params);
            if (!$status) {
                return [false, $alipay_body];
            }

            $PayReq = [
                'order_no'  => $order_no,
                'prepay_id' => $alipay_body,
            ];

            //更新支付流水表
            $payData = [
                'prepay_id' => $PayReq['prepay_id'], 'ptime' => $now, 'h5_url' => '', 'pay_type'=>by::Omain()::PAY_BY_ALIPAY,
            ];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

        } else {
            $PayReq = [
                'order_no'  => $order_no,
                'prepay_id' => $aOpay['prepay_id']
            ];
        }

        $PayReq['pay_type'] = by::Omain()::PAY_BY_ALIPAY;
        $r_price            = bcsub($aOpay['price'], $oInfo['fprice'] ?? 0);
        $r_price            = by::Gtype0()->totalFee($r_price, 1);
        $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
        $PayReq['coin']     = bcmul($r_price, $rate, 2);

        // 绑定用户等级
        $PayReq['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id,$PayReq['coin'],time());

        if (isset($PayReq['appId'])) {
            unset($PayReq['appId']);
        }

        return [true, $PayReq];
    }

    /**
     * 是否支付成功
     * @param $order_no
     * @return bool
     */
    public function isPaySuccess($order_no): bool
    {
        // 查支付宝订单是否已付款
        list($status, $res) = AliPayModel::getInstance()->query($order_no, false);
        if ($status) {
            $res = json_decode($res, true);

            // 检查 JSON 解析是否成功
            if (json_last_error() === JSON_ERROR_NONE && isset($res['alipay_trade_query_response'])) {
                $response = $res['alipay_trade_query_response'];

                // 检查接口返回的 code 是否为 10000
                if ($response['code'] === '10000' &&
                    ($response['trade_status'] == 'TRADE_SUCCESS' || $response['trade_status'] == 'TRADE_FINISHED')) {
                    return true;
                }
            }
        }
        return false;
    }

    private function getPayExpireTime($params, $limit)
    {
        $commPay = new CommPayModel();
        list($s, $time_expire) = $commPay->GetPayExpireTime($params, $limit);
        if (!$s) {
            return [false, $time_expire];
        }

        return [true, date("Y-m-d H:i:s", $time_expire)];
    }
}