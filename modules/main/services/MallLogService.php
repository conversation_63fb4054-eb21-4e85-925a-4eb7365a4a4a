<?php

namespace app\modules\main\services;

use app\models\by;

class MallLogService
{
    // 默认的uid
    const DEFAULT_UID = 'AA000000';

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 交换uid
     * @param $params
     * @return false|mixed|string
     * @throws \yii\db\Exception
     */
    public function exchangeUid($params)
    {
        // 解析数据
        $data = json_decode($params, true);

        // 数据异常不处理
        if (!isset($data['metadata']['uid'])) {
            return $params;
        }

        // 获取 uid
        $userId = $data['metadata']['uid'];
        $uid = by::Phone()->getUidByUserId($userId);

        // 处理数据
        $data['metadata']['uid'] = ($uid ?: self::DEFAULT_UID);

        return json_encode($data);
    }
}