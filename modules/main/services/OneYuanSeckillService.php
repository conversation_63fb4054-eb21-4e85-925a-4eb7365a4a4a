<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\OneYuanSeckillModel;
use app\modules\main\models\SalesCommissionModel;
use app\modules\main\services\invite\OneYuanSeckillInviteHandler;
use yii\db\Exception;

/**
 * 一元秒杀服务类
 */
class OneYuanSeckillService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    /**
     * @param $activity_id
     * @param $startTime
     * @param $endTime
     * @param int $userId
     * @return array
     * @throws Exception
     */
    public function getShopInfo(int $activityId, int $startTime, int $endTime, int $userId): array
    {
        // 统一的安全默认返回
        $default = [
            'has_order'  => false,
            'has_store'  => false,
            'order_info' => [
                'order_user_id' => 0,
                'nick_name'     => '',
                'avatar'        => '',
            ],
        ];

        // 基础参数校验
        if ($userId <= 0) {
            return $default;
        }

        // 活动校验（严格类型比较，防止类型混淆）
        $allowedActivityIds = array_map('intval', OneYuanSeckillInviteHandler::canNotInviteNewUserActivity());
        if ($activityId <= 0 || !in_array($activityId, $allowedActivityIds, true)) {
            return $default;
        }

        // 时间窗口严格校验：必须有效且跨度限制（最大31天），避免大时间窗导致的扫描/压测
        if ($startTime <= 0 || $endTime <= 0 || $endTime <= $startTime) {
            return $default;
        }

        // 初始化变量
        $shopHasOrder = false;
        $orderUserId = 0;
        $orderUserInfo = [];

        // 获取小店订单信息（限定时间窗内）
        try {
            $salesCommissionModel = new SalesCommissionModel();
            $shopOrder = $salesCommissionModel->getOrderInfoInPeriod($userId, $startTime, $endTime);
            if (!empty($shopOrder)) {
                $shopHasOrder = true;
                $orderUserId = intval($shopOrder['user_id'] ?? 0);
                if ($orderUserId > 0) {
                    // 仅提取必要字段，避免过多信息暴露
                    $orderUserInfo = by::users()->getOneByUid($orderUserId) ?: [];
                }
            }
        } catch (\Exception $e) {
            // 日志记录，不向上抛具体错误，避免信息泄露
            CUtil::debug('getShopInfo order_fetch_error: ' . ($e->getCode() ?: 0), 'one_yuan_seckill_error');
        }

        // 获取用户小店信息（容错）
        $hasStore = false;
        try {
            $shopInfo = DreameStoreService::getInstance()->getInfo($userId);
            $hasStore = !empty($shopInfo);
        } catch (\Exception $e) {
            CUtil::debug('getShopInfo store_fetch_error: ' . ($e->getCode() ?: 0), 'one_yuan_seckill_error');
        }

        return [
            'has_order'  => $shopHasOrder,
            'has_store'  => $hasStore,
            'order_info' => [
                'order_user_id' => $orderUserId,
                'nick_name'     => $orderUserInfo['nick'] ?? '',
                'avatar'        => CUtil::avatar($orderUserInfo),
            ],
        ];
    }

    private function __clone()
    {
    }


    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 助力状态
    const STATUS = [
        'IN_PROGRESS' => 0,
        // 进行中
        'SUCCESS'     => 1,
        // 助力成功
    ];

    // 邀请类型
    const INVITE_TYPE = [
        'ONE_YUAN_SECKILL' => 1,
        // 一元秒杀
    ];

    // 是否允许同时发起多个助力活动的配置
    const ALLOW_MULTIPLE_ACTIVITIES = true;

    /**
     * 获取当前用户助力详情
     * @param int $userId 用户ID
     * @param int $seckillId 秒杀ID
     * @return array
     */
    public function getSeckillDetail(int $userId, int $seckillId): array
    {
        try {

            $oneYuanSeckillModel = by:: OneYuanSeckillModel();

            // 获取秒杀记录
            $seckillRecord = $oneYuanSeckillModel->getSeckillById($seckillId);
            if (empty($seckillRecord)) {
                return [
                        false,
                        '助力活动不存在'
                ];
            }

            // 检查权限：只有发起人可以查看详情
            if ($seckillRecord['user_id'] != $userId) {
                return [
                        false,
                        '无权限查看此助力活动'
                ];
            }

            // 获取活动信息
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($seckillRecord['activity_id'], $userId);
            if (!$activityStatus) {
                return [
                        false,
                        '活动信息获取失败'
                ];
            }

            // 获取活动商品信息
            $goodsInfo = $this->getGoodsInfoFromActivity($activityData, $seckillRecord['gid']);

            // 获取助力好友列表（只包含当天邀请的用户）
            $helpFriends = $this->getHelpFriendsList($userId, $seckillId, $seckillRecord['activity_id']);

            // 获取需要的助力人数
            $requiredInviteNumber = $this->getRequiredInviteNumber($activityData, $seckillRecord['gid']);

            // 获取当天的邀请人数统计
            $userInviteModel = by::UserInviteModel();
            $todayInviteCount = $userInviteModel->getInviteCount(
                $userId,
                self::INVITE_TYPE['ONE_YUAN_SECKILL'],
                $seckillId,
                false // 只统计当天的邀请
            );
            $userInfo = by::users()->getOneByUid($seckillRecord['user_id']);

            $base_info = $activityData['base_info'] ?? [];
            $startTime = intval($base_info['start_time'] ?? 0);
            $endTime = intval($base_info['end_time'] ?? 0);

            $shopPurchase = $this->getShopInfo($seckillRecord['activity_id'], $startTime, $endTime, $userId);

            $result = [
                'seckill_id'             => $seckillId,
                'activity_id'            => $seckillRecord['activity_id'],
                'gid'                    => $seckillRecord['gid'],
                'status'                 => $seckillRecord['status'],
                'status_text'            => $seckillRecord['status'] == self::STATUS['SUCCESS'] ? '助力成功' : '助力中',
                'invite_members'         => $todayInviteCount, // 使用当天的邀请人数
                'required_invite_number' => $requiredInviteNumber,
                'order_no'               => $seckillRecord['order_no'] ?? '',
                'created_at'             => $seckillRecord['created_at'],
                'goods_info'             => $goodsInfo,
                'help_friends'           => $helpFriends,
                'activity_info'          => $activityData['base_info'] ?? [],
                'leader_info'            => [
                    'nick_name' => $userInfo['nick'] ?? '',
                    'avatar'    => CUtil::avatar($userInfo),
                    'leader_id' => $userInfo['user_id'] ?? 0,
                ],
                'shop_purchase'          => $shopPurchase,
            ];

            return [
                true,
                $result
            ];
        } catch (\Exception $e) {
            CUtil::debug('getSeckillDetail error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [
                false,
                '获取助力详情失败'
            ];
        }
    }


    /**
     * 获取助力好友列表
     * @param int $userId 用户ID
     * @param int $seckillId 秒杀ID
     * @param int $activity_id 活动ID
     * @return array
     * @throws Exception
     */
    public function getHelpFriendsList(int $userId, int $seckillId, int $activity_id): array
    {
        if ($this->isAllThreeDiscount($activity_id)) {
            $inviteList = byNew::DreameStoreModel()->getByInviterIdAndInviterFrom($userId, 'ACTIVITY_' . $activity_id . '_' . $seckillId);
            $inviteList = array_map(function ($item) {
                return [
                    'invitee_id' => $item['user_id'],
                    'invited_at' => $item['ctime'],
                ];
            }, $inviteList);
        } else {
            $userInviteModel = by::UserInviteModel();
            // 只获取当天邀请的用户列表，隔天的用户不在团里显示
            $inviteList = $userInviteModel->getInviteList(
                $userId,
                self::INVITE_TYPE['ONE_YUAN_SECKILL'],
                $seckillId,
                false // 只获取当天邀请的用户
            );
        }


        $helpFriends = [];
        foreach ($inviteList as $invite) {
            $inviteeInfo = by::users()->getOneByUid($invite['invitee_id']);
            $helpFriends[] = [
                'user_id'        => $invite['invitee_id'],
                'nick' => $inviteeInfo['nick'] ?? '匿名用户',
                'avatar'         => CUtil::avatar($inviteeInfo),
                'help_time'      => $invite['invited_at'],
                'help_time_text' => date('Y-m-d H:i:s', $invite['invited_at']),
            ];
        }

        return $helpFriends;
    }

    /**
     * 发起秒杀助力
     * @param int $userId 用户ID
     * @param int $activityId 活动ID
     * @param int $gid 商品ID
     * @return array
     */
    public function startSeckill(int $userId, int $activityId, int $gid): array
    {
        try {
            $oneYuanSeckillModel = new OneYuanSeckillModel();

            // 验证活动是否有效
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($activityId, $userId);
            if (!$activityStatus) {
                return [
                    false,
                    '活动不存在或已结束'
                ];
            }

            // 验证活动时间
            $now = time();
            $startTime = $activityData['base_info']['start_time'] ?? 0;
            $endTime = $activityData['base_info']['end_time'] ?? 0;

            if ($now < $startTime) {
                return [false, '活动尚未开始'];
            }
            if ($now > $endTime) {
                return [false, '活动已结束'];
            }

            // 验证商品信息
            $goodsInfo = $this->getGoodsInfoFromActivity($activityData, $gid);
            if (empty($goodsInfo)) {
                return [false, '商品不在活动范围内'];
            }

            // 验证商品库存
            if (!$this->checkGoodsStock($gid)) {
                return [false, '商品库存不足'];
            }

            // 只能全场三折购，只能发起一次商品的助力活动
            if ($this->isAllThreeDiscount($activityId)) {
                $existingSeckill = $oneYuanSeckillModel->getSeckillByUserActivityOne($userId, $activityId);
                if (!empty($existingSeckill)) {
                    return [false, '全场三折购活动只能发起一次，请勿重复发起'];
                }
            }

            // 检查是否已经发起过助力
            if ($oneYuanSeckillModel->hasInProgressSeckill($userId, $activityId, $gid)) {
                return [
                    false,
                    '您已经发起过此商品的助力活动'
                ];
            }

            // 检查用户是否有其他进行中的助力活动（根据配置决定）
            if (!self::ALLOW_MULTIPLE_ACTIVITIES) {
                $inProgressSeckills = $oneYuanSeckillModel->getUserInProgressSeckills($userId);
                if (!empty($inProgressSeckills)) {
                    return [
                        false,
                        '您有其他助力活动正在进行中，请先完成后再发起新的助力'
                    ];
                }
            }

            // 创建秒杀记录
            list($createStatus, $seckillIdOrMsg) = $oneYuanSeckillModel->createSeckill($userId, $activityId, $gid);
            if (!$createStatus) {
                return [
                    false,
                    $seckillIdOrMsg
                ];
            }

            return [
                true,
                [
                    'seckill_id' => $seckillIdOrMsg,
                    'message'    => '助力活动发起成功',
                    'goods_info' => $goodsInfo,
                ]
            ];

        } catch (\Exception $e) {
            CUtil::debug('startSeckill error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [
                false,
                '发起助力失败'
            ];
        }
    }

    /**
     * 检查是否为全场三折购活动
     * @param int $activityId 活动ID
     * @return bool
     */
    private function isAllThreeDiscount($activityId): bool
    {
        $allDiscountThreeActivityId = CUtil::dictValue('user_invite_activity.all_discount_three_activity_id');
        return $activityId == $allDiscountThreeActivityId;
    }

    public static function getAllDiscountThreeActivityId(): int
    {
        return CUtil::dictValue('user_invite_activity.all_discount_three_activity_id');
    }

    public function getAllDiscountThreeInvite($inviter_from, $inviterId, $inviteeId): array
    {
        $inviter_from_arr = explode('_', $inviter_from);
        $type = $inviter_from_arr[0] ?? '';
        $activity_id = $inviter_from_arr[1] ?? 0;
        $seckill_id = $inviter_from_arr[2] ?? 0;

        if ($type == 'ACTIVITY' && $activity_id == $this->getAllDiscountThreeActivityId() && $seckill_id) {
            try {
                $oneYuanSeckillModel = by::OneYuanSeckillModel();

                // 获取秒杀记录
                $seckillRecord = $oneYuanSeckillModel->getSeckillById($seckill_id);
                if (empty($seckillRecord)) {
                    throw new \Exception('活动记录不存在');
                }

                // 获取活动信息以检查是否达到助力要求
                list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($seckillRecord['activity_id'], $inviteeId);
                if (!$activityStatus) {
                    throw new \Exception('活动信息获取失败');
                }

                // 重新计算邀请人数（在邀请记录创建后）
                $newInviteCount = DreameStoreService::getInstance()->getActivityUserCount($inviterId, $inviter_from);

                // 更新秒杀表中的邀请人数
                $updateResult = $oneYuanSeckillModel->updateInviteMembers($seckill_id, $newInviteCount);
                if (!$updateResult) {
                    CUtil::debug('getAllDiscountThreeInvite error: 更新助力人数失败: seckillId=' . $seckill_id, 'one_yuan_seckill_error');
                }

                // 获取所需邀请人数
                $requiredInviteNumber = $this->getRequiredInviteNumber($activityData, $seckillRecord['gid']);

                // 检查是否达到助力要求
                $isSuccess = $newInviteCount >= $requiredInviteNumber;
                if ($isSuccess) {
                    // 助力成功，更新状态
                    $statusUpdateResult = $oneYuanSeckillModel->updateStatus($seckill_id, OneYuanSeckillService::STATUS['SUCCESS']);
                    if (!$statusUpdateResult) {
                        CUtil::debug('getAllDiscountThreeInvite error: 更新助力状态失败: seckillId=' . $seckill_id, 'one_yuan_seckill_error');
                        // 状态更新失败不应该影响整个流程，记录日志即可
                    }
                }
                return [true, '全场三折购助力成功'];
            } catch (\Exception $e) {
                CUtil::debug('getAllDiscountThreeInvite error: ' . $e->getMessage(), 'one_yuan_seckill_error');
                return [false, '处理失败：' . $e->getMessage()];
            }
        }
        return [false, '不符合全场三折购条件'];
    }

    /**
     * 获取商品信息
     * @param int $gid
     * @return array
     */
    private function getGoodsInfo(int $gid): array
    {
        try {

            $goodsData = by::Gmain()->GetOneByGidSid($gid);
            if (empty($goodsData)) {
                return [];
            }

            return [
                    'gid'         => $goodsData['gid'],
                    'name'        => $goodsData['name'],
                    'mprice'      => $goodsData['mprice'] ?? 0,
                    'cover_image' => $goodsData['cover_image'] ?? '',
                    'price'       => $goodsData['price'] ?? 0,
                    'sku'         => $goodsData['sku'] ?? '',
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * 从活动数据中获取指定商品信息
     * @param array $activityData
     * @param int $gid
     * @return array
     */
    private function getGoodsInfoFromActivity(array $activityData, int $gid): array
    {
        $modules = $activityData['modules'] ?? [];
        foreach ($modules as $module) {
            if (($module['module_code'] ?? '') === 'SNAP_UP') {
                $extra = $module['extra'] ?? [];
                foreach ($extra as $item) {
                    if (($item['goods_id'] ?? 0) == $gid) {
                        // 获取库存信息
                        $stock = $this->getGoodsStock($gid);
                        
                        return [
                                'gid'              => $item['goods_id'],
                                'name'             => $item['goods_name'] ?? '',
                                'cover_image'      => $item['goods_image'] ?? '',
                                'price'            => $item['sale_price'] ?? 0,
                                'mprice'           => $item['mprice'] ?? 0,
                                'sku'              => $item['goods_sku'] ?? '',
                                'invite_number'    => $item['extra']['invite_number'] ?? 3,
                                'buy_goods_number' => $item['extra']['buy_goods_number'] ?? 1,
                                'show_name'        => $item['show_name'] ?? '',
                                'stock'            => $stock,
                        ];
                    }
                }
            }
        }
        return [];
    }

    /**
     * 获取需要的助力人数
     * @param array $activityData
     * @param int $gid
     * @return int
     */
    private function getRequiredInviteNumber(array $activityData, int $gid): int
    {
        if ($gid == 0) {
            return $activityData['modules'][0]['extra'][0]['invite_number'] ?? 100;
        }
        $goodsInfo = $this->getGoodsInfoFromActivity($activityData, $gid);
        return $goodsInfo['invite_number'] ?? 3;
    }

    /**
     * 检查商品库存
     * @param int $gid
     * @return bool
     */
    private function checkGoodsStock(int $gid): bool
    {
        try {
            $goodsData = by::Gmain()->GetOneByGid($gid);
            if (empty($goodsData)) {
                return false;
            }

            $sku = $goodsData['sku'] ?? '';
            if (empty($sku)) {
                return false;
            }

            $stock = by::GoodsStockModel()->OptStock($sku);
            return $stock > 0;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * 批量获取用户在指定活动中的进行中秒杀活动
     * @param int $userId 用户ID
     * @param int $activityId 活动ID
     * @return array 返回秒杀活动数据，键为商品ID，值为秒杀活动信息
     */
    public function getUserInProgressSeckillsByActivity(int $userId, int $activityId): array
    {
        try {
            $oneYuanSeckillModel = by::OneYuanSeckillModel();

            // 获取用户在指定活动中的所有进行中秒杀记录
            $seckillRecords = $oneYuanSeckillModel->getUserInProgressSeckillsByActivity($userId, $activityId);

            if (empty($seckillRecords)) {
                return [];
            }

            $userInviteModel = by::UserInviteModel();
            $result = [];
            foreach ($seckillRecords as $record) {
                $gid       = $record['gid'];
                $seckillId = $record['id'];

                // 获取助力好友列表（只包含当天邀请的用户）
                $helpFriends = $this->getHelpFriendsList($userId, $seckillId, $activityId);

                // 获取当天的邀请人数统计
                $todayInviteCount = $userInviteModel->getInviteCount(
                    $userId,
                    self::INVITE_TYPE['ONE_YUAN_SECKILL'],
                    $seckillId,
                    false // 只统计当天的邀请
                );

                $result[$gid] = [
                        'seckill_id'     => $seckillId,
                        'status'         => $record['status'],
                        'invite_members' => $todayInviteCount, // 使用当天的邀请人数
                        'created_at'     => $record['created_at'],
                        'help_friends'   => $helpFriends,
                        'order_no'       => $record['order_no'] ?? '',
                ];
            }

            return $result;
        } catch (\Exception $e) {
            CUtil::debug('getUserInProgressSeckillsByActivity error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [];
        }
    }

    /**
     * 获取商品库存
     * @param int $gid 商品ID
     * @return int 库存数量
     */
    private function getGoodsStock(int $gid): int
    {
        try {
            $gstockModel = by::Gstock();
            list($stock, $sales) = $gstockModel->GetSumData($gid);
            return $stock;
        } catch (\Exception $e) {
            CUtil::debug('getGoodsStock error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return 0;
        }
    }

    /**
     * 获取或创建一元秒杀记录
     * @param int $userId 用户ID
     * @param int $activityId 活动ID
     * @param int $gid 商品ID
     * @return array
     */
    public function getOrCreateSeckill(int $userId, int $activityId, int $gid): array
    {
        try {
            $oneYuanSeckillModel = new OneYuanSeckillModel();

            // 如果 gid 为 0，则不校验商品信息，直接按活动维度生成/获取记录
            if ($gid === 0) {
                // 非全场三折购，则不允许 gid 为 0
                if (!$this->isAllThreeDiscount($activityId)) {
                    return [
                        false,
                        '商品ID必须大于0'
                    ];
                }

                // 只完成一次
                $existingSeckillOne = $oneYuanSeckillModel->getSeckillByUserActivityOne($userId, $activityId);
                if (!empty($existingSeckillOne)) {
                    return [
                        true,
                        [
                            'seckill_id' => $existingSeckillOne['id'],
                            'message'    => '获取秒杀记录成功',
                            'is_new'     => false
                        ]
                    ];
                }
                list($createStatus, $seckillIdOrMsg) = $oneYuanSeckillModel->createSeckill($userId, $activityId, 0);
                if (!$createStatus) {
                    return [
                        false,
                        $seckillIdOrMsg
                    ];
                }
                return [
                    true,
                    [
                        'seckill_id' => $seckillIdOrMsg,
                        'message'    => '创建秒杀记录成功',
                        'is_new'     => true,
                    ]
                ];
            }

            // gid > 0 的场景按原有校验逻辑处理
            $existingSeckill = $oneYuanSeckillModel->getSeckillByUserActivityGid($userId, $activityId, $gid);
            if (!empty($existingSeckill)) {
                return [
                    true,
                    [
                        'seckill_id' => $existingSeckill['id'],
                        'message' => '获取秒杀记录成功',
                        'is_new' => false
                    ]
                ];
            }

            list($status, $create) = $this->startSeckill($userId, $activityId, $gid);
            if (!$status) {
                return [
                    false,
                    $create
                ];
            }

            return [
                true,
                [
                    'seckill_id' => $create['seckill_id'],
                    'message'    => '创建秒杀记录成功',
                    'is_new'     => true,
                ]
            ];

        } catch (\Exception $e) {
            CUtil::debug('getOrCreateSeckill error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [
                false,
                '获取或创建助力失败'
            ];
        }
    }

    /**
     * 绑定/切换商品（仅在助力成功且已有订单号的记录上允许）
     * @param int $userId
     * @param int $seckillId
     * @param int $gid
     * @return array
     */
    public function selectGidBind(int $userId, int $seckillId, int $gid): array
    {
        try {
            $oneYuanSeckillModel = new OneYuanSeckillModel();
            $record = $oneYuanSeckillModel->getSeckillById($seckillId);

            if (empty($record)) {
                return [false, '未找到可绑定的助力记录'];
            }

            if ($record['gid'] == $gid) {
                return [true, [
                    'id'      => intval($record['id']),
                    'old_gid' => intval($record['gid']),
                    'new_gid' => intval($gid),
                ]];
            }

            // 只支持仅支持全场三折购活动的绑定
            if (!$this->isAllThreeDiscount($record['activity_id'])) {
                return [false, '仅支持全场三折购活动的绑定'];
            }

            if ($userId != $record['user_id']) {
                return [false, '无权限操作此助力记录'];
            }

            // 获取活动信息
            list($activityStatus, $activityData) = MemberActivityService::getInstance()->getInfo($record['activity_id'], $userId);
            if (!$activityStatus) {
                return [
                    false,
                    '活动信息获取失败'
                ];
            }

            // 获取活动商品信息
            $goodsInfo = $this->getGoodsInfoFromActivity($activityData, $gid);
            if (empty($goodsInfo)) {
                return [false, '商品不在活动范围内'];
            }

            $ok = $oneYuanSeckillModel->updateGid(intval($record['id']), $gid);
            if (!$ok) {
                return [false, '绑定失败'];
            }

            return [true, [
                'id'      => intval($record['id']),
                'old_gid' => intval($record['gid']),
                'new_gid' => intval($gid),
            ]];
        } catch (\Exception $e) {
            CUtil::debug('selectGidBind error: ' . $e->getMessage(), 'one_yuan_seckill_error');
            return [false, '绑定失败'];
        }
    }
} 