<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\common\Singleton;
use app\modules\main\models\UserBindModel;

class SalesCommissionService
{
    use Singleton;
    CONST STATUS_NAME = [
        '0'         => '待支付',
        '100'       => '已取消',
        '300'       => '待发货',
        '400'       => '待收货',
        '500'       => '已完成',
        '600'       => '已发放',
        '10000000'  => '申请退款',
        '10000300'  => '申请退款&待发货',
        '10000400'  => '申请退款&待收货',
        '10000500'  => '申请退款&已完成',
        '20000000'  => '退款完成',
        '20000300'  => '退款完成&待发货',
        '20000400'  => '退款完成&待收货',
        '20000500'  => '退款完成&已完成',
    ];

    public function pageList($user_id, $page,$pageSize,$activity_id): array
    {
        // $params['referrer'] = $user_id;
        if (!$page){
            $page = 1;
        }
        if (!$pageSize){
            $pageSize = 10;
        }
        $pageList = byNew::SalesCommissionModel()->getList($user_id,$page,$pageSize,$activity_id);
        $items = $pageList['list'];
        // 查询用户信息
        // $userIds  = array_column($items, 'user_id');
        // $users = by::users()->getListByUserIds($userIds, ['user_id', 'nick', 'avatar']);
        // $users = array_column($users, null, 'user_id');
        // 整合数据
        $list = [];
        foreach ($items as $item) {
            $info  = by::Ouser()->CommPackageInfo($item['user_id'],$item['order_no'],false,true,false,false,true,false,true,true);
            if (!$info){
                continue;
            }
            $user =  by::phone()->getDataByUserId($item['user_id']);
            $rinfo = [
                'order_no'         => $info['order_no'],
                'deposit_order_no' => $info['deposit_order_no'] ?? '',
                'status'           => $item['status'],
                'oprice'           => $info['oprice'],
                'exprice'          => $info['exprice'] ?? 0,
                'deposit_price'    => $info['deposit_price'] ?? 0,
                'price'            => CUtil::totalFee($item['price'], 1),
                'dprice'           => $info['dprice'],
                'real_price'       => $info['real_price'],
                'subsidy_price'    => $info['subsidy_price'],
                'coin'             => $info['coin'] ?? 0,
                'coin_price'       => $info['coin_price'] ?? 0,
                'cprice'           => $info['cprice'] ?? 0,
                'goods'            => $info['goods'],
                'user_order_type'  => $info['user_order_type'] ?? 0,  //订单类型
                'ctime'            => $info['ctime'] ?? 0,            //下单时间
            ];
            $rinfo['uid'] = $user['uid'] ?? ''; //用户ID

            $rinfo['commission'] = CUtil::totalFee($item['commission'], 1); //佣金 价格转化为元
            $rinfo['rate'] = $item['rate']; //佣金比例
            $statusName = self::STATUS_NAME[$item['status']] ?? '未知';
            $rinfo['status_name'] = $statusName;
            $list[] = $rinfo;
        }
        $pages = CUtil::getPaginationPages($pageList['total'], $pageSize);
        return ['list' => $list, 'total' => $pageList['total'],'pages'=>$pages];
    }

    public function statistics($user_id,$activity_id){
        $data = byNew::SalesCommissionModel()->statistics($user_id,$activity_id);
        return $data;
    }
}