<?php

namespace app\modules\main\services;

use app\components\Crm;
use app\components\E3MemberCenter;
use app\components\MemberCenter;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;
use app\modules\main\enums\activity\ActivityConfigEnum;
use DateTime;
use Faker\Provider\Uuid;

class E3Service
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function getMemberInfo($data){
        $phone = $data['phone'] ?? '';
        if (!$phone) return ['',false,'手机号必传'];

        // 通过手机号获取用户信息
        $info = by::Phone()->GetInfoByPhone($phone);
        $result = [];
        if (!$info) {
            $result['is_member'] = 0;
            // $result['uid'] = 0;
            $result['score'] = 0;
            return [$result,true,''];
        }
        $result['is_member'] = 1;
        // $result['uid'] = $info['user_id'];

        // 获取积分
       
        list($s, $data) = MemberCenter::factory()->run('scoreGet', ['user_id'=>$info['user_id']]);
        if (!$s) {
            return ['',false,'获取积分失败'];
        }
        $point = $data['totalPoints'] ?? 0;
        
        if ($point <= 0) {
            $result['score'] = 0;
        }else{
            //小程序订单未同步到crm的积分
            $oPoint = by::Ouser()->getUsePoint($info['user_id']);
            $result['score'] = intval(bcsub($point,$oPoint));
        }
        return [$result,true,''];
    }

    public function getBuyInfo($data){
        $phone = $data['phone'] ?? 0;
        $amount = $data['amount'] ?? 0;
        if ((!$phone) || (!$amount)){
            return ['',false,'手机号必传'];
        }
        $info = by::Phone()->GetInfoByPhone($phone);
        if (!$info) return ['',false,'用户不存在'];
        $uid = $info['user_id'];
        
        $result = [
            'uid' => $uid,
            'can_use_score' => 0,
            'real_price' => $amount,
            'deduction_price' => 0,
            'use_coupon' => []
        ];
        // 根据金额获取可使用积分
        $is_use_score = $data['is_use_score'] ?? 0;
        if ($is_use_score){
            list($can_coin, $deduction_price,$real_price) = $this->__canUseScore($uid, $amount);
            // 计算实际支付金额
            $result['real_price'] = $real_price;
            $result['can_use_score'] = $can_coin;
            $result['deduction_price'] = $deduction_price;
        }

        // 根据金额和商品获取可使用优惠券，暂时不加

        return [$result,true,''];
    }
    private function __canUseScore($uid, $amount){
        // 积分权益
        $pointRate = by::memberCenterModel()->getPointCrash($uid);
        $mPoint = by::point();
        $tcoin  = $mPoint->get($uid); //总积分
        // 计算金额转换的积分
        $can_coin  = $mPoint->convert($amount, 'POINT');
        // 如果积分大于总积分，则使用全部积分
        $can_coin  = bccomp($tcoin, $can_coin) >= 0 ? $can_coin : $tcoin;
        
        // 计算实际可使用积分
        $can_coin = $this->__canCoinByBenefit($can_coin, $amount, $pointRate,$uid);
        // 计算实际抵扣金额
        $deduction_price = $can_coin > 0 ? $mPoint->convert($can_coin) : 0;

        $real_price = bcsub($amount, $deduction_price,2);
        return [$can_coin, $deduction_price,$real_price];
    }

    /**
     * @param $canCoin
     * @param $price
     * @param $rate
     * @param $userId
     * @return mixed
     * 获取用户实际可使用积分
     */
    private function __canCoinByBenefit($canCoin, $price, $rate, $userId)
    {
        // 校验活动时间，若符合条件则设置最低比率为25
        if (ActivityConfigEnum::judgeActivityTime($userId, 'POINT_DEDUCTION')) {
            $rate = max($rate, ActivityConfigEnum::POINT_DEDUCTION_MULTIPLE);
        }

        // 计算允许的最大可使用积分额
        $deductionAmount = CUtil::uint(floatval($price) * $rate);

        // 返回可用的最小抵扣积分值
        return min($canCoin, $deductionAmount);
    }
    /**
     * 保存订单
     *
     * @param [type] $data
     * @return void
     */
    public function saveOrder($data){
        $phone = $data['phone'] ?? 0;
        $order_price = $data['order_price'] ?? '';
        $discount_price = $data['discount_price'] ?? 0;
        $is_use_score = $data['is_use_score'] ?? 0;
        $use_score = $data['use_score'] ?? 0;
        $real_price = $data['real_price'] ?? '';
        $order_no = $data['order_no'] ?? '';
        $store_name = $data['store_name'] ?? '';
        $store_code = $data['store_code'] ?? '';
        $goods_list = $data['goods_list'] ?? [];
        if (!$phone || !$order_price || !$real_price || !$store_name || !$store_code || !$order_no){
            return ['',false,'缺少参数'];
        }
        $info = by::Phone()->GetInfoByPhone($phone);
        if (!$info) return ['',false,'用户不存在'];
        $uid = $info['user_id'];

        // 订单号唯一验证
        $order = by::E3OrderModel()->getOrderByNo($order_no);
        if ($order) return ['',false,'订单号已存在'];

        // 不使用积分的话，把积分置为0
        if ($is_use_score == 0){
            $use_score = 0;
        }else{
            // 使用积分时，校验一下积分和实际可使用积分是否一致
            list($can_coin, $deduction_price,$new_real_price) = $this->__canUseScore($uid, $order_price);
            if ($use_score > $can_coin){
                return ['',false,'使用积分大于实际可使用积分'];
            }
        }
        $uName = by::Phone()->getUidByUserId($uid);
        if (empty($uName)) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $uid . '|用户uid为空', 'warn.uid');
            return ['',false,'用户不存在，请联系管理员确认'];
        }
        // 金额全部转成分保存
        $order = [
            'order_no' => $order_no,
            'uid' => $uid,
            'order_price' => round($order_price*100),
            'discount_price' => round($discount_price*100),
            'real_price' => round($real_price*100),
            'is_use_score' => $is_use_score,
            'use_score' => $use_score,
            'store_name' => $store_name,
            'store_code' => $store_code,
            'ctime' => time(),
            'utime' => time(),
            'status' => 0,
        ];
        $gData = [];
        $surplus_score = $use_score;
        foreach ($goods_list as $k=>$v){
            // 校验一下参数
            if (!isset($v['sku']) || !isset($v['name']) || !isset($v['num']) || !isset($v['price'])){
                return ['',false,'商品参数错误'];
            }
            // 计算每个商品的积分
            $good_total = bcmul($v['num'],$v['price'],2);
            if ($k+1 == count($goods_list)){
                $child_use_score = $surplus_score;
            }else{
                $child_use_score = bcmul(($good_total/$order_price),$use_score,0);
                $surplus_score = bcsub($surplus_score,$child_use_score,0);
            }
            
            $gData[] = [
                'order_no' => $order_no,
                'sku' => $v['sku'],
                'name' => $v['name'],
                'num' => $v['num'] ?? 0,
                'price' => round($v['price']*100),
                'use_score'=> $child_use_score,
                'status' => 1,
                'ctime' => time(),
                'utime' => time()
            ];
        }
        list($status,$msg) = by::E3OrderModel()->SaveLog($order,$gData);
        if (!$status){
            return ['',false,$msg];
        }
        // 调IoT接口扣除积分
        if ($use_score > 0){
            $dateTime = new DateTime();
            $milliseconds = $dateTime->format('Uv');
            $iot_data = [
                'uid' => $uName,
                'timestamp'=>$milliseconds,
                'point'=>$use_score,
                'operType'=>-1,
                'grow'=>0,
                'ext'=>'线下订单扣减积分',
                'serialNo'=> Uuid::uuid()
            ];
            list($status, $iotData) = E3MemberCenter::factory()->run('pointGrowSave', ['event' => 'deduct','data' => $iot_data]);
            // 写日志
            if ($iotData['code'] != 0){
                return ['',false,'积分扣减失败，请重试或者联系管理员处理'];
            }
        }
        return [['order_no'=>$order_no],true,''];
    }

    public function orderReward($data){
        $order_no = $data['order_no'] ?? '';
        // $phone = $data['phone'] ?? 0;
        if (!$order_no){
            return ['',false,'缺少参数'];
        }
        // $info = by::Phone()->GetInfoByPhone($phone);
        // if (!$info) return ['',false,'用户不存在'];
        
        $order = by::E3OrderModel()->getOrderByNo($order_no);
        if (!$order){
            return ['',false,'订单不存在'];
        }
        if ($order['status'] != 0){
            return ['',false,'订单已处理'];
        }
        $uid = $order['uid'];
        // 积分增加成功后，修改订单状态 todo 方法名修改
        $status = by::E3OrderModel()->updateStatusByOrderNo($order_no,1);
        if (!$status){
            return ['',false,'订单状态修改失败'];
        }
        // 根据实际支付金额奖励积分,如果又部分退款，仅发放剩余金额对应的积分
        $uName = by::Phone()->getUidByUserId($uid);
        if (empty($uName)) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $uid . '|用户uid为空', 'warn.uid');
            return ['',false,'用户不存在，请联系管理员确认'];
        }
        // 获取退款的商品
        $order_goods = by::E3OrderGoodModel()->getGoodsListByStatus($order_no,2);
        $refund_price = 0;
        foreach ($order_goods as $k=>$v){
            $refund_price = bcadd($refund_price,$v['price'],0);
        }
        // 计算实际应该发放奖励的金额
        $reward_score = bcsub($order['real_price'],$refund_price,0);

        if ($order['real_price'] > 0 && $reward_score > 0){
            $dateTime = new DateTime();
            $milliseconds = $dateTime->format('Uv');
            $iot_data = [
                'uid' => $uName,
                'timestamp'=>$milliseconds,
                'point'=>round($reward_score/100,2),
                'operType'=>1,
                'grow'=>0,
                'ext'=>'线下购买奖励积分',
                'serialNo'=> Uuid::uuid()
            ];
            list($status, $iotData) = E3MemberCenter::factory()->run('pointGrowSave', ['event' => 'reward','data' => $iot_data]);
            if ($iotData['code'] != 0){
                return ['',false,'积分奖励失败，请重试或者联系管理员处理'];
            }
        }
        return ['success',true,''];

    }

    public function orderRefund($data){
        $order_no = $data['order_no'] ?? '';
        $phone = $data['phone'] ?? 0;
        if (!$order_no || !$phone){
            return ['',false,'缺少参数'];
        }
        $info = by::Phone()->GetInfoByPhone($phone);
        if (!$info) return ['',false,'用户不存在'];
        
        $order = by::E3OrderModel()->getOrderByNo($order_no);
        if (!$order){
            return ['',false,'订单不存在'];
        }
        $uid = $order['uid'];
        $goods_list = $data['goods_list'] ?? [];
        $refund_score = 0;
        $update_data = [];
        $uName = by::Phone()->getUidByUserId($uid);
        if (empty($uName)) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $uid . '|用户uid为空', 'warn.uid');
            return ['',false,'用户不存在，请联系管理员确认'];
        }
        // todo 统一获取 循环计算
        if (count($goods_list) == 0){
            return ['',false,'请选择要退还的商品'];
        }
        $sku_list = array_column($goods_list,'sku');
        $order_goods = by::E3OrderGoodModel()->getGoodsListBySku($order_no,$sku_list);
        if (empty($order_goods)) {
            return ['',false,'订单商品不存在'];
        }
        
        $new_list = [];
        foreach ($order_goods as $k1=>$v1){
            $new_list[$v1['sku']] = $v1;
        }
        
        foreach ($goods_list as $k=>$v){
            $gInfo = $new_list[$v['sku']] ?? [];
            if (empty($gInfo)) {
                continue;
            }
            // 已退款的商品，不再退款
            if ($gInfo['status'] == 2) {
                continue;
            }
            $rscore = bcmul($v['num']/$gInfo['num'],$gInfo['use_score'],0);

            if ($rscore > $gInfo['use_score'] - $gInfo['refund_score']){
                return ['',false,'商品'.$v['num'].'积分退还失败，积分不足'];
            }
            $refund_score = bcadd($refund_score,$rscore,0);
            $item = [];
            $item['id'] = $gInfo['id'];
            $item['refund_score'] = $gInfo['refund_score'] + $rscore;
            if ($item['refund_score'] == $gInfo['use_score']){
                $item['status'] = 2;
            }else{
                $item['status'] = 3;
            }
            
            $update_data[] = $item;
        }
        $status = by::E3OrderGoodModel()->batchUpdate($update_data);
        if (!$status){
            return ['',false,'商品退还积分失败'];
        }
        // 调用IoT接口退还积分，加积分
        if ($refund_score > 0){
            $dateTime = new DateTime();
            $milliseconds = $dateTime->format('Uv');
            $iot_data = [
                'uid' => $uName,
                'timestamp'=>$milliseconds,
                'point'=>$refund_score,
                'operType'=>1,
                'grow'=>0,
                'ext'=>'线下退款退还积分',
                'serialNo'=> Uuid::uuid()
            ];
            list($status, $iotData) = E3MemberCenter::factory()->run('pointGrowSave', ['event' => 'refund','data' => $iot_data]);
            if ($iotData['code'] != 0){
                return ['',false,'积分退还失败，请重试或者联系管理员处理'];
            }
        }

        return ['success',true,''];
    }
}