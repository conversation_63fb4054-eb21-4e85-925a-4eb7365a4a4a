<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\WeiXinPc;
use app\exceptions\LoginException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\enums\user\UserTypeEnum;
use RedisException;
use yii\db\Exception;

class LoginService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }




    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param $id
     * @return array
     * @throws RedisException
     * @throws RandomException
     * 获取微信扫码登录的 URL参数
     */
    public function getPcUrl($id): array
    {
        // 获取配置参数
        $configArr = CUtil::getConfig('pc-weixin', 'common', MAIN_MODULE);

        if (!isset($configArr[$id])) {
            return [false, '获取失败'];
        }

        $config = $configArr[$id];

        // 生成 state 参数（可以使用 UUID 或其他唯一标识符）
        $state = bin2hex(random_bytes(16));

        // 保存 state 到 Redis 后续校验
        $redis    = by::redis();
        $redisKey = AppCRedisKeys::getQrConnectState($state);
        $redis->setex($redisKey, 43200, $state);

        // 获取配置信息
        $appId        = $config['appId'] ?? '';
        $scope        = $config['scope'] ?? 'snsapi_login'; // 默认值为 snsapi_login
        $redirectUri  = urlencode($config['redirect_uri'] ?? '');
        $id           = $config['id'] ?? '';
        $responseType = $config['response_type'] ?? '';

        // 准备返回数据
        return [true, [
            'appid'         => $appId,
            'scope'         => $scope,
            'redirect_uri'  => $redirectUri,
            'state'         => $state,
            'id'            => $id,
            'response_type' => $responseType,
        ]];
    }


    /**
     * @param $code
     * @param $state
     * @param $userType
     * @return array
     * @throws RedisException
     * @throws Exception
     * 获取用户信息
     */
    public function getUserWxInfo($code, $state, $userType): array
    {
        // 校验 state 参数的有效性
        if (!$this->isValidState($state)) {
            return [false, '无效的 state 参数'];
        }

        // 获取 access token 和 openid
        list($accessStatus, $accessInfo) = WeiXinPc::factory()->getAccessInfo($code);
        if (!$accessStatus) {
            return [false, $accessInfo];
        }

        // 提取 access token 和 openid
        $accessToken = $accessInfo['access_token'] ?? '';
        $openid      = $accessInfo['openid'] ?? '';

        // 判断用户是否已经是会员
        $userId = by::UsersPlatformModeModel()->GetUserIdByOpenUdId($openid, $userType);

        if (!empty($userId) && strlen($userId) < 15) {
            // 用户已存在，获取用户信息
            $userInfo = by::users()->getOneByUid($userId);
            $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
            $result   = [
                'nickname' => $userInfo['nick'] ?? '',
                'avatar'   => $userInfo['avatar'] ?? '',
                'exists'   => 1,
                'user_id'  => $userId,
                'uid'      => $mallInfo['uid'] ?? '',
                'phone'    => $mallInfo['phone'] ?? '',
            ];
        } else {
            // 用户不存在，获取微信用户信息
            list($userStatus, $userInfo) = WeiXinPc::factory()->getUserInfo($accessToken, $openid);
            if (!$userStatus) {
                return [false, $userInfo];
            }
            $result = [
                'nickname' => $userInfo['nickname'] ?? '',
                'avatar'   => $userInfo['headimgurl'] ?? '',
                'exists'   => 0,
                'user_id'  => 0,
            ];
        }
        $result['openudid']  = $openid;
        $result['user_type'] = $userType;
        $result['unionid']   = $accessInfo['unionid'] ?? '';
        // 返回结果
        return [true, $result];
    }

    /**
     * @param $state
     * @return bool
     * @throws RedisException
     * 校验state是否有效
     */
    private function isValidState($state): bool
    {
        $redis    = by::redis();
        $redisKey = AppCRedisKeys::getQrConnectState($state);

        // 检查 Redis 中的 state 是否存在
        if ($redis->exists($redisKey)) {
            return true;
        }

        return false;
    }


    /**
     * @param string $jwtToken
     * @return array
     * @throws Exception
     * 解析jwtToken
     */
    public function loginByJwtToken(string $jwtToken): array
    {
        // 尝试通过 JWT Token 登录
        list($status, $data) = by::login()->loginByApp($jwtToken);
        return [$status, $data];
    }

    /**
     * @param array $post
     * @param array $data
     * @return array
     * @throws Exception
     * @throws RedisException
     * 确定场景来源并获取用户信息
     */
    public static function determineSceneSource(array $post, array &$data): array
    {
        $userMainInfo = [];

        // 默认来源app
        $source = by::userExtend()::SOURCE_FROM_APP;
        // PC 端登录
        if ($post['api'] === 'p_1712037068') {
            // PC 扫码登录
            if (!empty($post['scan_code']) && !empty($post['state'])) {
                // 校验手机号是否被他人绑定
                $userData = by::UsersPlatformModeModel()->GetOneByPhone($data['phone'] ?? '', UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
                if (isset($userData['status']) && $userData['status'] == by::UsersPlatformModeModel()::STATUS['BIND']) {
                    return [false, '手机号已被绑定', $userMainInfo];
                }

                // 获取用户微信信息
                list($status, $userMainInfo) = self::getInstance()->getUserWxInfo($post['scan_code'], $post['state'], UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
//                if ($status) {
//                    $data['nick_name'] = $userMainInfo['nickname'] ?? '';
//                    $data['avatar']    = $userMainInfo['avatar'] ?? '';
//                }
            }
            $source = by::userExtend()::SOURCE_FROM_COMMON_PC;

        }

        // 门店登录
        if (!empty($post['shop_code'])) {
            list($user_shop_code) = by::retailers()->decrypt($post['shop_code']);
            $data['shop_code'] = trim($user_shop_code ?? '');
            $source            = by::userExtend()::SOURCE_FROM_SHOP_CODE_PC;
        }

        // 默认来源
        return [true, $source, $userMainInfo];
    }



    /**
     * @param int $userId
     * @param array $sessionData
     * @return array
     * 初始化用户session
     */
    public function initUserSession(int $userId, array $sessionData): array
    {
        return by::login()->initUserLoginTarget($userId, $sessionData);
    }


    /**
     * 解绑微信
     *
     * @param  $userId
     * @return array 解绑结果
     */
    public function unbindUser($userId): array
    {
        // 校验用户ID是否为空
        if (empty($userId)) {
            return [false, '用户ID无效'];
        }

        // 调用模型层方法进行解绑操作
        $status = by::UsersPlatformModeModel()->updateLog($userId, ['status' => 0]);


        // 返回解绑结果和消息
        return [$status, $status ? "解绑成功" : '解绑失败'];
    }


    /**
     * 绑定微信
     *
     * @param int   $userId 用户ID
     * @param array $post   POST数据
     * @return array 绑定结果，包含成功与否标志及消息
     */
    public function bindUser(int $userId, array $post): array
    {
        try {
            $code  = $post['code'] ?? '';
            $state = $post['state'] ?? '';

            // 获取 openid 和 unionid
            list($status, $data) = LoginService::getInstance()->getUserWxInfo($code, $state, UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
            if (!$status) {
                throw new LoginException('获取微信信息失败');
            }

            $openid  = $data['openudid'] ?? '';
            $unionid = $data['unionid'] ?? '';

            // 校验微信信息是否完整
            if (empty($openid) || empty($unionid)) {
                throw new LoginException('微信信息不完整');
            }

            // 校验 unionid 是否已绑定其他账号
            $userPlatformData = by::UsersPlatformModeModel()->GetOneByUnionId($unionid, UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
            if ($this->isWechatBoundToAnotherAccount($userPlatformData)) {
                return [false, '当前微信已绑定其他账号'];
            }

            // 绑定用户
            if (empty($userPlatformData)) {
                // 1.没有数据
                list($status, $result) = DataService::getInstance()->bindExistingUser($userId, ['openudid' => $openid, 'unionid' => $unionid], UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
            } elseif (!$this->isWechatBoundToAnotherAccount($userPlatformData)) {
                // 2.注销了
                list($status, $result) = DataService::getInstance()->bindExistingUser($userId, ['openudid' => $openid, 'unionid' => $unionid], UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
            } else {
                // 3.已经绑定修改状态
                $status = by::UsersPlatformModeModel()->updateLog($userId, ['status' => byNew::UsersPlatformModeModel()::STATUS['BIND']]);
            }

            if (!$status) {
                // 构建详细的错误信息
                $errorMessage = sprintf(
                    '绑定失败，用户ID：%d，微信信息：openudid=%s, unionid=%s，绑定微信失败',
                    $userId,
                    $openid,
                    $unionid
                );
                throw new LoginException($errorMessage);
            }

            // 更新昵称头像
            // by::users()->updateMembersInfo($userId, ['nick' => $data['nickname'], 'avatar' => $data['avatar']]);

            return [true, '绑定成功'];
        } catch (\Exception $exception) {
            // 记录异常日志（可以使用框架的日志功能或自定义日志记录）
            $error = sprintf(
                '绑定失败，用户ID：%d，错误信息：%s | 文件：%s | 行：%d',
                $userId,
                $exception->getMessage(),
                $exception->getFile(),
                $exception->getLine()
            );

            CUtil::debug($error, 'err.bind_wechat_user');
            // 捕获异常并返回错误信息
            return [false, "绑定失败"];
        }
    }

    /**
     * 获取绑定信息
     *
     * @param int $userId
     * @return array
     */
    public function binds(int $userId): array
    {
        try {
            // 获取手机号
            $phone = by::Phone()->GetPhoneByUid($userId);

            // 检查手机号是否存在
            if (!$phone) {
                throw new Exception('用户手机号不存在');
            }

            // 查询是否绑定微信
            $user = by::UsersPlatformModeModel()->GetOneByPhone($phone, UserTypeEnum::USER_TYPE['WECHAT_SCAN']);

            // 返回成功结果
            return [
                true,
                [
                    'bindPhone'     => $phone,
                    'canBindWechat' => empty($user['status']), // 能否绑定微信：未绑定 (true)，已绑定 (false)
                ]
            ];
        } catch (Exception $e) {
            // 记录错误信息
            $error = sprintf("%s | %s:%d", $e->getMessage(), $e->getFile(), $e->getLine());
            CUtil::debug($error, 'err.bind_wechat_user');

            // 返回失败结果
            return [false, '绑定失败'];
        }
    }


    /**
     * @param array $post
     * @return array
     * @throws RedisException 校验是否绑定过微信
     */
    public function checkBind(array $post): array
    {
        $code  = $post['code'] ?? null;
        $state = $post['state'] ?? null;

        // 校验 state 参数的有效性
        if (!$this->isValidState($state)) {
            return [false, '无效的 state 参数'];
        }

        // 获取 access token 和 openid
        list($accessStatus, $accessInfo) = WeiXinPc::factory()->getAccessInfo($code);

        if (!$accessStatus) {
            return [false, $accessInfo];
        }

        $unionId = $accessInfo['unionid'] ?? '';

        // 判断用户ID是否已经存在，表示微信已绑定其他账号
        $userPlatformData = by::UsersPlatformModeModel()->getOneByUnionId($unionId, UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
        if ($this->isWechatBoundToAnotherAccount($userPlatformData)) {
            return [false, '当前微信已绑定其他账号'];
        }

        return [true, 'OK'];
    }

    /**
     * @param array $userPlatformData
     * @return bool
     * 校验微信是否绑定其他账号
     */
    private function isWechatBoundToAnotherAccount(array $userPlatformData): bool
    {
        return isset($userPlatformData['status']) &&
            $userPlatformData['status'] == by::UsersPlatformModeModel()::STATUS['BIND'] &&
            !strpos($userPlatformData['openudid'], '|');
    }


    public function CheckMainUserByUnionID($unionId, int $userType)
    {
        return by::users()->getUserIdByUnionId($unionId, $userType) ?? '';
    }


    public function CheckPlatUserByUnionID($unionId,$userType)
    {
        return by::UsersPlatformModeModel()->GetOneByUnionId($unionId,$userType);
    }


    /**
     * 获取用户登录类型
     * 1: 会员态
     * 2: 会员退出态
     * 3: 游客态（包括注销）
     *
     * @param string $userId 用户ID
     * @return int 用户登录类型
     */
    public function getUserLoginType(string $userId): int
    {
        // 判断是否为游客（UUID 格式检查）
        if (CUtil::checkUuid($userId)) {
            // 查询游客表，获取主用户 ID
            $userData   = by::RuserExtend()->getUserExtendInfo($userId, false);
            $mainUserId = $userData['main_user_id'] ?? '';

            // 判断游客数据是否存在
            if (empty($userData) || empty($mainUserId)) {
                return 3; // 未绑定用户，仍为游客
            }

            // 查询主用户信息
            $main = by::users()->getUserMainInfo($mainUserId);
            return !empty($main['openudid']) && strpos($main['openudid'], '|') !== false ? 3 : 2;
        }

        // 查询用户表数据（非 UUID 格式，直接视为会员）
        $main = by::users()->getUserMainInfo($userId);
        return !empty($main['openudid']) && strpos($main['openudid'], '|') !== false ? 3 : 1;
    }
}