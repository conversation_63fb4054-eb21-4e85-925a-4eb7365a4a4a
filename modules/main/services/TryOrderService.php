<?php

namespace app\modules\main\services;

use app\models\byNew;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

class TryOrderService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }
    

    /**
     * @param $userId
     * @param $tryStatus
     * @return array
     * @throws RedisException
     * @throws Exception
     * 我的试用列表
     */
    public function GetTryOrderList($userId, $tryStatus): array
    {
        $return            = [];
        $userOrderTryModel = byNew::UserOrderTry();
        $surveyModel       = byNew::SurveyRecordModel();
        $activityModel     = byNew::ActivityModel();
        $activityTypeModel = byNew::ActivityTypeModel();
        $userTryStatus     = $userOrderTryModel::USER_TRY_STATUS[$tryStatus] ?? [];

        switch ($tryStatus) {
            // 获取申请中状态
            case $userOrderTryModel::USER_APPLY_STATUS['APPLY']:
                $list = $surveyModel->getSurveyList(['user_id' => $userId]);
                $list = $this->processApply($list, $tryStatus);
                break;
            // 已失效：通过过问卷+没有支付过订单+活动已经结束
            case $userOrderTryModel::USER_APPLY_STATUS['APPLY_EXPIRED']:
                $list = $surveyModel->getSurveyList(['user_id' => $userId, 'audit_status' => $surveyModel::AUDIT_STATUS['AUDIT_PASS']]);
                $list = $this->processApplyExpired($list, $userId,$tryStatus, $userOrderTryModel);
                break;
            // 获取用户试用订单的状态数组
            default:
                $tryStatusArray = array_intersect_key($userOrderTryModel::TRY_STATUS, array_flip($userTryStatus));

                if (empty($tryStatusArray)) {
                    return [true, $return];
                }

                $list = $userOrderTryModel->GetList([
                    CUtil::buildCondition('user_id', '=', $userId),
                    CUtil::buildCondition('ac_id', '!=', ''),
                    CUtil::buildCondition('try_status', 'IN', $tryStatusArray),
                ]);

                $list = $this->processOrderList($list, $tryStatus, $userOrderTryModel, $activityModel, $activityTypeModel);
        }
        $return['list'] = array_values($list);
        return [true, $return];
    }

    /**
     * @param $list
     * @param $tryStatus
     * @return mixed
     * 申请中
     */
    private function processApply($list, $tryStatus)
    {
        foreach ($list as &$value) {
            $activityMain          = byNew::ActivityModel()->getOneById($value['ac_id']);
            $activityDetail        = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($value['ac_id']);
            $value['poster_image'] = $activityDetail['poster_image'] ?? '';
            $value['name']         = $activityMain['name'] ?? '';
            $value['try_status']   = $tryStatus;
            unset($value['uid'], $value['score'], $value['pass_score'], $value['survey_info'], $value['utime']);
        }
        return $list;
    }


    /**
     * @param $list
     * @param $userId
     * @param $tryStatus
     * @param $userOrderTryModel
     * @return mixed
     * @throws Exception
     * @throws RedisException
     * 申请失效
     */
    private function processApplyExpired($list, $userId,$tryStatus, $userOrderTryModel)
    {
        foreach ($list as $key => &$value) {
            $activityInfo = byNew::ActivityModel()->getActivityDetail($value['ac_id'], [], true);
            $orderInfo    = $userOrderTryModel->GetOneInfo([
                CUtil::buildCondition('user_id', '=', $userId),
                CUtil::buildCondition('ac_id', '=', $value['ac_id']),
                CUtil::buildCondition('try_status', '>', $userOrderTryModel::TRY_STATUS['HAS_CANCEL']),
            ]);

            if (time() > $activityInfo['end_time'] && empty($orderInfo)) {
                unset($value['uid'], $value['score'], $value['pass_score'], $value['survey_info'], $value['utime']);
            } else {
                unset($list[$key]);
            }

            $value['poster_image'] = $activityInfo['poster_image'] ?? '';
            $value['name']         = $activityInfo['name'] ?? '';
            $value['try_status']   = $tryStatus;
        }
        return $list;
    }

    /**
     * @param $list
     * @param $tryStatus
     * @param $userOrderTryModel
     * @param $activityModel
     * @param $activityTypeModel
     * @return mixed
     * 已下单、体验中、体验完成
     */
    private function processOrderList($list, $tryStatus, $userOrderTryModel, $activityModel, $activityTypeModel)
    {
        foreach ($list as &$value) {
            $activityMain          = $activityModel->getOneById($value['ac_id']);
            $activityDetail        = $activityTypeModel->getActivityTypeInfoByAcId($value['ac_id']);
            $value['poster_image'] = $activityDetail['poster_image'] ?? '';
            $value['name']         = $activityMain['name'] ?? '';

            $tryBeginTime = null;
            $tryEndTime   = null;
            if (in_array($tryStatus, [$userOrderTryModel::USER_APPLY_STATUS['TRYING'], $userOrderTryModel::USER_APPLY_STATUS['TRY_FINISH']])) {
                $tryBeginTime = $this->getTryBeginTime($value);
                if ($tryBeginTime !== null) {
                    $tryEndTime = $tryBeginTime + ($activityDetail['validity'] * $userOrderTryModel::TRY_TIME_PERIOD);
                }
            }
            $value['try_begin_time'] = $tryBeginTime;
            $value['try_end_time']   = $tryEndTime;

            unset($value['utime'], $value['auth_no'], $value['sn'], $value['label'], $value['amount'], $value['register_time'], $value['arrival_time'], $value['back_time']);
        }
        return $list;
    }

    private function getTryBeginTime($value)
    {
        if (!empty($value['arrival_time']) && !empty($value['register_time'])) {
            return min($value['arrival_time'], $value['register_time']);
        } elseif (!empty($value['arrival_time'])) {
            return $value['arrival_time'];
        } elseif (!empty($value['register_time'])) {
            return $value['register_time'];
        }
        return null;
    }


    public function GetTryInfo($acId, $userId): array
    {
        if (empty($acId)) {
            return [false, "活动ID不能为空"];
        }

        //获取活动信息
        $activityInfo = byNew::ActivityModel()->getActivityDetail($acId, [], true);

        //判断活动状态
        $activityStatus                  = WaresActivityService::getInstance()->CheckActivityStatus($activityInfo['start_time'], $activityInfo['end_time'],$activityInfo['status']);
        $activityInfo['activity_status'] = $activityStatus;

        if (empty($activityInfo)) {
            return [false, "活动不存在"];
        }
        return [true, $activityInfo];
    }

}