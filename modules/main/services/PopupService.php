<?php

namespace app\modules\main\services;

use app\models\byNew;
use app\modules\main\models\UserPopupModel;

class PopupService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    public function getPopupData($userId): array
    {
        // 获取弹窗数据
        return byNew::UserPopupModel()->GetPopupData([
                'user_id'    => $userId,
                'popup_type' => UserPopupModel::POPUP_TYPE['MEMBER_MALL_INVITE']
        ]);
    }
}