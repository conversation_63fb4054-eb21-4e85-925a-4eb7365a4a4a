<?php

namespace app\modules\main\services;

use app\components\Employee;
use app\components\EventMsg;
use app\jobs\EmployeeInviteJob;
use app\jobs\EmployeeStatisticsJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\UserBindModel;

class UserBindService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 新注册的用户列表
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public function getRegisterList(int $user_id, array $params): array
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return [false, '用户不存在'];
        }

        // 获取列表
        $param = [
            'uid'             => $uid,
            'bind_start_time' => $params['bind_start_time'] ?? '',
            'bind_end_time'   => $params['bind_end_time'] ?? '',
            'is_new_user'     => UserBindModel::IS_NEW_USER['YES']
        ];
        $items = byNew::UserBindModel()->getBindList($param, $params['page'], $params['page_size']);
        // 总数
        $total = byNew::UserBindModel()->getBindCount($param);

        // 查询用户信息
        $uids  = array_column($items, 'bound_uid');
        $users = $this->getUserList($uids);

        // 整合数据
        $list = [];
        foreach ($items as $item) {
            $list[] = [
                'nickname'    => $users[$item['bound_uid']]['nick'] ?? '',
                'create_time' => date('Y-m-d', $item['ctime'])
            ];
        }
        return [true, ['list' => $list, 'total' => $total]];
    }

    /**
     * 绑定用户
     * @param $boundUid
     * @param $employeeUid
     * @param $isNew
     * @param $error
     * @return bool|int
     */
    public function bindEmployee($userId,$employeeUserId,$boundUid, $employeeUid,$phone, $isNew, &$error,$isForceUpdate = 0, $activity_id = 1)
    {
        $status = byNew::UserBindModel()->bindEmployee($userId,$employeeUserId,$boundUid, $employeeUid,$phone, $isNew, $error,$isForceUpdate, $activity_id);
        if ($status && $isNew) { // 绑定成功 且是新用户

        }
        return $status;
    }

    public function newBind($user_id,$inviter_id, $isNew){
        // 当前用户uid
        // $uid = $this->user_id;
        $userInfo = by::Phone()->getDataByUserId($user_id);
        $user =  by::phone()->getDataByUserId($inviter_id);
        $employeeUid = $user['uid'] ?? '';
        //处理绑定操作
        $error = '';
        $res = UserBindService::getInstance()->bindEmployee($user_id,$inviter_id,$userInfo['uid'], $employeeUid,$userInfo['phone'], $isNew, $error);
        if(!$res){
            // 绑定失败，记录日志
            CUtil::debug('用户绑定员工失败:'.$user_id.'-'.$employeeUid.'-'.$error,'user_bind.info');
        }
        
        // 统计埋点，推荐人数加一
        \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$inviter_id,'order_no' => '','type'=>1,'field'=>'recommend_num','number'=>1,'addOrsubtract'=>'+']));

        EventMsg::factory()->run('addRichPlan', ['user_id' => $inviter_id]); // 邀请好友给合伙人发奖励金
        return true;
    }

    /**
     * 查询用户信息
     * @param $uids
     * @return array
     * @throws \yii\db\Exception
     */
    private function getUserList($uids): array
    {
        // 查询用户ID
        $user_ids = by::Phone()->getUserIdsByUids($uids);

        // 查询用户信息
        $users = by::users()->getListByUserIds($user_ids, ['user_id', 'nick', 'avatar']);
        $users = array_column($users, null, 'user_id');

        // 整合数据
        $data = [];
        foreach ($user_ids as $uid => $user_id) {
            $data[$uid] = $users[$user_id] ?? [];
        }
        return $data;
    }

    /**
     * 微笑大使-已关联的用户列表
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public function getSmileList(int $user_id, array $params): array
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return [false, '用户不存在'];
        }

        // 获取列表
        $param = [
            'uid'             => $uid,
            'bind_start_time' => $params['bind_start_time'] ?? '',
            'bind_end_time'   => $params['bind_end_time'] ?? '',
            'activity_id'   => UserBindModel::ACTIVITY_ID['smile'],
        ];
        $items = byNew::UserBindModel()->getBindList($param, $params['page'], $params['page_size']);
        // 总数
        $total = byNew::UserBindModel()->getBindCount($param);

        // 查询用户信息
        $uids  = array_column($items, 'bound_uid');
        $users = $this->getUserList($uids);

        // 整合数据
        $list = [];
        foreach ($items as $item) {
            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);
            $avatar = $config['cdnAddr'] . '/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png';
            $list[] = [
                'nickname'  => $users[$item['bound_uid']]['nick'] ?? '',
                'avatar'    => $users[$item['bound_uid']]['avatar'] ?? $avatar,
                'create_time' => date('Y-m-d', $item['ctime'])
            ];
        }
        return [true, ['list' => $list, 'total' => $total]];
    }
}