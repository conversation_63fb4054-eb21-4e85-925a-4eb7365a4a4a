<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\UserCardModel;
use yii\db\Exception;

class MemberCenterService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $arr
     * @return array
     * 获取用户权益
     */
    public function userBenefit($arr): array
    {
        list($s, $centerData) = MemberCenter::factory()->run('levelBenefit', $arr);
        if (!$s) {
            return [false, $centerData];
        }

        // 会员中心服务
        $memberCenterModel = by::memberCenterModel();

        $level = $arr['level'] ?? '';
        if (empty($level)) {
            //没有level时查询获取对应用户的level
            list($s, $basicInfo) = MemberCenter::factory()->run('basicInfo', $arr);
            $levelInfo = $basicInfo['currentLevelInfo'] ?? [];
            $level     = $levelInfo['level']['level'] ?? 'v1';
            $arr['level'] = $level;
        }

        $r_key      = $memberCenterModel->__getMemberCenterInfoKey('userBenefit', ['level'=>$level]);
        $redis      = by::redis();
        $aJson      = $redis->get($r_key);
        $resultData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            //1.找出所属等级的数据
            foreach ($centerData as $center) {
                $centerLevel = $center['level']['level'] ?? '';
                if ($centerLevel == $level) {
                    $resultData = $center;
                    break;
                }
            }

            $resBenefit  = $resultData['benefitRuleList'] ?? [];
            $lastLevel   = $centerData[count($centerData) - 1] ?? [];
            $lastBenefit = $lastLevel['benefitRuleList'] ?? [];

            if ($resBenefit && $lastBenefit) {
                foreach ($resBenefit as $key => $res) {
                    $resBenefit[$key]['is_use'] = 1;
                }
                $resTypeCodes = array_column($resBenefit, 'typeCode');
                foreach ($lastBenefit as $last) {
                    $ltypeCode = $last['typeCode'] ?? '';
                    if ($ltypeCode && !in_array($ltypeCode, $resTypeCodes)) {
                        $last['is_use'] = 0;
                        $resBenefit[]   = $last;
                    }
                }
            }

            $resultData['benefitRuleList'] = $resBenefit;

            $resultData['rightLists'] = $memberCenterModel->__getRightListByData($centerData,$level,$arr);

            $redis->set($r_key, json_encode($resultData), ['ex' => $memberCenterModel->expire_time]);
        }

        if($resultData['benefitRuleList']) {
            $benefitRuleList = $resultData['benefitRuleList'];
            //按照平台过滤数据
            $benefitRuleList = $memberCenterModel->__filterBenefit($benefitRuleList,$arr);
            //有类型且有标签，那么就会单独输出，否则输出全部数据
            $typeCode = trim($arr['typeCode'] ?? '');
            $single = CUtil::uint($arr['single'] ?? 0);
            if($single){
                $typeCode = empty($typeCode) ? 'free_shipping' : $typeCode;
            }
            //返回本地数据
            foreach ($benefitRuleList as $key=>$valueData){
                $benefitRuleList[$key]['rightList'] = $resultData['rightLists'][$valueData['typeCode'] ?? ''] ?? [];
                if ($typeCode && $typeCode !== ($valueData['typeCode'] ?? '')) {
                    $vd = $valueData['data'] ?? [];
                } else {
                    $vd = $this->__getLocalDataByBenefit($valueData['typeCode'] ?? '', $valueData['data'] ?? [], $arr);
                }
                $benefitRuleList[$key]['data']= $vd;
            }
            $resultData['benefitRuleList'] = $benefitRuleList;
        }
        unset($resultData['rightLists']);

        return [true,$resultData];

    }

    /**
     * 获取权益
     * @param int $userId
     * @return array
     */
    public function getBenefitInfo(int $userId): array
    {
        // 要展示的服务
        $typeCodes = [
            'quality_assurance', // 质保
            'free_shipping',     // 满N元包邮
            'return_exchange',   // N无理由退
            'expensive_pay',     // N天保价
            'points_double',     // 购物返积分
        ];

        // 一、获取会员服务权益
        list($status, $ret) = by::model("MemberCenterModel", MAIN_MODULE)->CenterMessage('basicInfo', ['user_id' => $userId]);
        if (!$status) {
            return [
                'name'     => '',
                'benefits' => []
            ];
        }

        // 1、权益集合
        $benefits = [];
        $benefitRuleList = $ret['currentLevelInfo']['benefitRuleList'] ?? [];
        foreach ($benefitRuleList as $item) {
            $typeCode = $item['typeCode'];
            if (in_array($typeCode, $typeCodes)) {
                $benefits[$typeCode] = $item;
            }
        }
        // 2、用户等级名，例如：铜牌觅享家
        $name = $ret['currentLevelInfo']['level']['name'] ?? '';

        // 二、获取基础权益
        $basicBenefits = $this->__getBasicBenefitInfo();
        $items = array_merge($basicBenefits, $benefits);

        // 按顺序排序
        $data = [];
        foreach ($typeCodes as $typeCode) {
            if (isset($items[$typeCode])) {
                $data[] = $items[$typeCode];
            }
        }

        // 3、返回结果
        return [
            'name'     => $name,
            'benefits' => $data
        ];
    }

    /**
     * 获取任务列表
     * @param int $userId
     * @param $platform
     * @param int $type
     * @return array
     */
    public function getTaskList(int $userId, $platform, $type = 1,$version = ''): array
    {
        // 获取任务列表
        list($status, $ret) = by::memberCenterModel()->CenterMessage('taskList', ['user_id' => $userId, 'platformSource' => $platform, 'type' => $type,'version'=>$version]);

        if (!$status) {
            return [false, $ret];
        }
        // 版本号
        // $version = \Yii::$app->request->post('version', '0.0.0');
        if (version_compare($version, '2.1.3', '<=')) {
            // 适配新版本任务
            $ret = array_filter($ret, function ($item) {
                return !in_array($item['code'], ['post', 'share_goods_money', 'invite_buy_goods_money']);
            });
        }


        // 包装任务列表
        $ret = $this->adapterTask($ret);
        return [$status, array_values($ret)];
    }
    
    /**
     * 获取任务列表
     * @param $platform
     * @param int $type
     * @return array
     */
    public function getNoAuthTaskList($platform, $type = 1, $limit = 3,$version): array
    {
        // 获取任务列表
        list($status, $ret) = by::memberCenterModel()->CenterMessage('noAuthTaskList', ['platformSource' => $platform, 'type' => $type, 'limit' => $limit,'version'=>$version]);
        
        if (!$status) {
            return [false, $ret];
        }
        
        // 包装任务列表
        $ret = $this->adapterTask($ret);
        return [$status, array_values($ret)];
    }

    /**
     * （暂时代码）
     * 获取基础权益，例如：两年质保，全国联保
     * @return array
     */
    private function __getBasicBenefitInfo(): array
    {
        $data = [];
        $qualityAssurance = [
                "name"               => "质保",
                "code"               => "",
                "typeCode"           => "quality_assurance",
                "groupCode"          => "oper",
                "ord"                => 1,
                "lockedIcon"         => "",
                "title"              => "官方质保，全国联保",
                "normalIcon"         => "",
                "descri"             => '',
                "levelBenefitDescri" => "官方质保，全国联保",
                "serviceIcon"        => "https://wpm-cdn.dreame.tech/images/202411/673bf9696b1a14390091933.png",
                "serviceTitle"       => "官方质保，全国联保",
                "serviceDescribe"    => '<!DOCTYPE html><html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/></head><body style="text-align:center;"><img src="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202503/67c69ccaed6e49736282828.png"/></body></html>',
        ];
        $data['quality_assurance'] = $qualityAssurance;
        return $data;
    }


    /**
     * 获取权益方法
     * @param $typeCode
     * @param $data
     * @param $arr
     * @return mixed
     */
    private function __getLocalDataByBenefit($typeCode,$data,$arr){
        switch ($typeCode){
            case 'new_user_gift':
            case 'exclusive':
            case 'birth_benefits':
            case 'product_register':
            case 'tick_month':
                return $this->$typeCode($data,$arr);
            default:return $data;
        }
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * @throws Exception
     * 新版本
     * 获取新人大礼包数据
     */
    private function new_user_gift($data, $arr) {
        // 锁定新人大礼包
        $lock      = CUtil::getConfig('lockCoupon', 'member', MAIN_MODULE) ?? false;
        $testUsers = CUtil::getConfig('testCouponUser', 'member', MAIN_MODULE) ?? [];
        $user_id   = $arr['user_id'] ?? '';
        $phone     = by::Phone()->GetPhoneByUid($user_id); // 不可删除

        if ($lock && !in_array($phone, $testUsers)) {
            return [];
        }

        if (empty($user_id)) return $data;

        // 获取用户的新人大礼包状态
        $userInfo               = by::users()->getOneByUid($user_id);
        $isNewGift              = intval($userInfo['is_new_gift'] ?? 0);
        $data['receive_status'] = $isNewGift;  // 领取状态
        $data['coupon_status']  = $isNewGift;  // 领取状态

        // 初始化活动信息
        $activityInfo = [
                'coupon' => [],
                'point'  => [],
        ];

        if ($isNewGift == 1) {
            // 如果已经领取过新人大礼包
            $drawInfo = by::AcDrawLogModel()->GetDetail($phone, ['grant_type' => 1]);
            $acId     = $drawInfo['ac_id'] ?? 0;

            if (!$acId) {
                $acInfos = by::activityConfigModel()->getList([
                        'grant_type'  => 1,
                        'is_del'      => 1,
                        'need_count'  => false,
                        'need_detail' => false
                ], 0, 20);
                $acId    = array_column($acInfos['list'], 'id');
            }

            // 获取已领取的新人大礼包数据
            $marketConfig = by::userCard()->getListByGetRelation($user_id, UserCardModel::GET_CHANNEL['activity'], $acId, true);

            if (!empty($drawInfo['market_ids']) || !empty($marketConfig)) {
                foreach ($marketConfig as $mcId) {
                    $market = by::marketConfig()->couponCondition($mcId);
                    if ($market) {
                        $activityInfo['coupon'][] = $market;
                    }
                }
            }

            if (!empty($drawInfo['point'])) {
                $rate            = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
                $activityInfo['point'] = [
                        'point_value'      => $drawInfo['point'],
                        'deductible_price' => bcdiv($drawInfo['point'], $rate, 2),
                ];
            }
        } else {
            // 如果未领取新人大礼包
            $activityGiftData       = by::activityConfigModel()->getActivity(1, $user_id, 1, false);
            $activityInfo['coupon'] = $activityGiftData['coupon'] ?? [];
            $activityInfo['point']  = $activityGiftData['point'] ?? [];

            if (empty($activityInfo['coupon']) && empty($activityInfo['point'])) {
                $data['coupon_status'] = 3; // 已过期或没配置
            } else {
                $activityInfo['coupon'] = array_filter($activityInfo['coupon'], function ($item) {
                    $stock      = $item['surplus_num'] ?? 0;
                    $expireTime = $item['expire_time'] ?? 0;
                    return $stock > 0 && $expireTime > time();
                });

                if (empty($activityInfo['coupon']) && empty($activityInfo['point'])) {
                    $data['coupon_status'] = 2; // 已领光
                }
            }
        }

        // 按照 market_id 排序
        usort($activityInfo['coupon'], function ($a, $b) {
            return $a['market_id'] - $b['market_id'];
        });

        // 处理优惠券状态
        foreach ($activityInfo['coupon'] as &$info) {
            $info['usage_status'] = 0;
            $marketId             = $info['market_id'] ?? 0;
            if ($isNewGift && $marketId) {
                $userCardInfo         = by::userCard()->getCardById($user_id, $marketId);
                $info['usage_status'] = $userCardInfo['status'] ?? 0;
            }
        }

        return array_merge($data, $activityInfo);
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * 星享官
     */
    private function exclusive($data,$arr){
        $erweima     = CUtil::getConfig('xinShareCode','member',MAIN_MODULE);
        $data['erweima']= $erweima;
        return $data;
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * @throws Exception
     * 生日福利
     *
     */
    private function birth_benefits($data,$arr){
        //获取用户生日信息
        $userId = CUtil::uint($arr['user_id'] ?? 0);
        $userId && $userInfo = by::users()->getOneByUid($userId);
        $data['birthday'] = intval($userInfo['birthday'] ?? 0);
        //增加领券的状态值
        $data['status'] = ActivityConfigService::getInstance()->getBirthdayCardStatus($userId, $arr['level']);
        //生日天，生日月
        if ($data['birthday']) {
            $day = date('md');
            $month = date('m');
            $birthDay = date('md', $data['birthday']);
            $birthMonth = date('m', $data['birthday']);
            $data['is_birth_day'] = ($day == $birthDay) ? 1 : 0;
            $data['is_birth_month'] = ($month == $birthMonth) ? 1 : 0;
        } else {
            $data['is_birth_day'] = 0;
            $data['is_birth_month'] = 0;
        }
        return $data;
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * 产品注册
     */
    private function product_register($data,$arr){
        $platformSource = $arr['platformSource'] ?? 0;
        $data['is_open'] = 1;
        in_array($platformSource,[5,6]) && $data['is_open'] = 0;
        return $data;
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * 每月领卷
     * @throws Exception
     */
    private function tick_month($data,$arr){
        return ActivityConfigService::getInstance()->UserMonthCoupon($data,$arr);
    }

    /**
     * @param $data
     * @param $arr
     * @return mixed
     * @throws Exception
     * 旧版本新人礼包
     */
    private function old_new_user_gift($data, $arr)
    {
        $user_id = $arr['user_id'] ?? '';
        if (empty($user_id)) return $data;
        //获取用户的新人大礼包状态
        $userInfo               = by::users()->getOneByUid($user_id);
        $isNewGift              = $userInfo['is_new_gift'] ?? 0;
        $data['receive_status'] = intval($isNewGift); //领取状态
        $data['usage_status']   = 0;                  //未使用
        //礼包信息
        $data['coupon_status'] = intval($isNewGift);  //领取状态
        $activityInfo          = by::activityConfigModel()->getActivity(1, $user_id, 1, false);

        $surplus_num = $activityInfo[0]['surplus_num'] ?? 0;
        if (empty(CUtil::uint($surplus_num)) && $data['coupon_status'] !== 2) {
            $data['coupon_status'] = 4;//已领光
        }

        $data = array_merge($activityInfo[0] ?? [], $data);
        unset($data['surplus_num']);
        $marketId = $activityInfo[0]['market_id'] ?? 0;
        if ($isNewGift && $marketId) {
            //判断使用状态
            $userCardInfo         = by::userCard()->getCardById($user_id, $marketId);
            $data['usage_status'] = $userCardInfo['status'] ?? 0;
        }
        return $data;
    }

    /**
     * 适配任务列表
     * @param array $items
     * @return array
     */
    private function adapterTask(array $items): array
    {
        // 文案描述
        $descriptions = $this->getMemberDescriptions();
        foreach ($items as $index => $item) {
            // 文案
            if (isset($descriptions[$item['code']])) {
                $items[$index]['descri'] = $descriptions[$item['code']];
            }

            // 过滤任务
            if (in_array($item['code'], ['employees_buy_inner_main_machine', 'employees_buy_inner_parts', 'employees_invite_register', 'employees_invite_buy'])) {
                unset($items[$index]);
            }
        }
        return $items;
    }

    /**
     * 获取普通会员的任务描述
     * @return array
     */
    private function getMemberDescriptions(): array
    {
        return [
                'buy_main_machine' => '发放积分值=实际付款金额*等级倍数(普通1倍，银牌1倍，金牌1.2倍，钻石1.5倍，黑金2倍)
发放觅享分=实际付款金额（确认收货后第15天发放）',
                'buy_parts'        => '发放积分值=实际付款金额*等级倍数(普通1倍，银牌1倍，金牌1.2倍，钻石1.5倍，黑金2倍)
发放觅享分=实际付款金额*10（将在确认收货后第15天发放）',
        ];
    }


    /**
     * @param $userId
     * @param $level
     * @param $platformSource
     * @param $single
     * @return array
     * app调用方法 获取会员信息
     */
    public function appGrowthCenter($userId, $level, $platformSource, $single): array
    {
        // 定义等级映射表
        $levelMapping = ['v1', 'v2', 'v3', 'v4', 'v5'];

        // 获取当前等级的索引
        $levelIndex = array_search($level, $levelMapping);
        if ($levelIndex === false) {
            return [false, []]; // 如果找不到该等级，返回空数据
        }

        // 调用 'CenterMessage' 获取数据
        list($status, $data) = MemberCenter::factory()->run(
                'growthCenter',
                ['user_id' => $userId, 'single' => $single]
        );

        // 如果返回数据无效，直接返回空数据
        if (!$status || empty($data['allLevelInfo'])) {
            return [false, []];
        }

        // 获取当前等级的数据
        $allLevelInfo     = $data['allLevelInfo'];
        $currentLevelInfo = $allLevelInfo[$levelIndex] ?? [];

        // 如果当前等级信息存在 'benefitRuleList'，则进行处理
        if (!empty($currentLevelInfo['benefitRuleList'])) {
            // 过滤 'benefitRuleList' 数据
            $currentLevelInfo['benefitRuleList'] = by::memberCenterModel()->__filterBenefit(
                    $currentLevelInfo['benefitRuleList'],
                    ['platformSource' => $platformSource]
            );

            // 清理不需要的字段
            foreach ($currentLevelInfo['benefitRuleList'] as &$benefitRule) {
                unset($benefitRule['descri'], $benefitRule['serviceDescribe']);
            }
        }

        // 返回当前等级的数据
        return [true, $currentLevelInfo];
    }

    const BROWSE_TASK_CODE = [
            'oneYuanPurchase',      // 一元购
            'richPlan',             // 暴富计划
            'fiveDiscountPurchase', // 五折购
            'halfPriceBuy',         // 半价购
            'pointsShopping',       // 积分购物
            'appShareGoods',        // app分享商品
            'appInviteRegGold',     // app邀请注册
            'viewGoodsMoney',       // 浏览商品60秒获得消费金
            'postFriend',           // 完成朋友板块1次发帖，获得消费金奖励
            'viewGoods',            // 浏览商品15s
            'shareGoodsMoney',      // 分享商城商品
            'addBlindPrizeDraw',    // 添加盲盒抽奖
            'viewGoodsThreeBuy',
            'viewGoodsOneYuanMoney',
            'shareGoodsFriendMoney',
            'viewGoodsGroup',
            'shareGroupGoods',
            'shareShopGoodsFriend',
            'viewGoodsRichPlan',
            'shareRichGoodsFriend',
            'viewGoodsRichPlanMoney',
    ];

    /**
     * 处理浏览任务
     * @param int    $userId 用户ID
     * @param string $type   任务类型
     * @param array  $params 任务类型
     * @return array [是否成功, 消息/结果]
     */
    public function handleBrowseTask(int $userId, string $type, $params = []): array
    {
        try {
            // 验证任务类型非空
            if (empty($type)) {
                return [false, '任务类型不能为空'];
            }

            // 清理输入并验证类型合法性
            $taskCode = trim($type);
            if (!in_array($taskCode, self::BROWSE_TASK_CODE)) {
                return [false, '任务类型错误'];
            }

            // 执行任务时可能抛出异常，统一捕获
            $params["user_id"] = $userId;
            list($status, $result) = EventMsg::factory()->run($taskCode, $params);

            return $status ? [true, 'success'] : [false, $result];
        } catch (\Exception $e) {
            // 记录异常详情（包含堆栈信息便于调试）
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.browse_task');
            // 向调用方返回通用错误信息（避免暴露敏感信息）
            return [false, '任务处理失败，请稍后重试'];
        }
    }

}
