<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

class TurboModeService
{

    private static $_instance = NULL;

    private $cache_key_prefix = 'turbo_mode_cache:';

    /**
     * 是否开启极速模式
     * @var false|mixed
     */
    private $is_open;

    private $cache_time;

    private function __construct()
    {
        $this->is_open = CUtil::getConfig('turbo_mode', 'config', \Yii::$app->id)['is_open'] ?? false;
        $this->cache_time = CUtil::getConfig('turbo_mode', 'config', \Yii::$app->id)['cache_time'] ?? 3600;

    }

    public function cacheList()
    {
        return [
                'goods-recommend/list',
                'goods-recommend/goods-tag'
        ];
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 根据极速模式状态执行缓存或原方法
     * @param string $cacheKey 缓存键
     * @param callable $callback 原方法回调
     * @param array $params 方法参数
     * @return mixed
     * @throws RedisException
     */
    public function executeWithCache($cacheKey, $callback, $params = [], $cacheTime = null)
    {
        if (!is_callable($callback)) {
            throw new \InvalidArgumentException('callback 不是可调用类型');
        }

        if (!$this->is_open) {
            // 极速模式未开启，直接执行原方法
            return call_user_func_array($callback, $params);
        }

        $cacheTime = $cacheTime ?? $this->cache_time;
        try {
            // 尝试从缓存获取数据
            $cacheData = by::redis()->get($cacheKey);
            if ($cacheData !== false) {
                try {
                    $decoded = json_decode($cacheData, true);
                } catch (\Throwable $e) {
                    CUtil::debug('TurboModeService json_decode 异常: ' . $e->getMessage(), __METHOD__);
                    $decoded = null;
                }
                // 缓存命中，返回缓存数据（支持缓存空值）
                return $decoded;
            }

            // 缓存未命中，执行原方法
            $result = call_user_func_array($callback, $params);

            // 缓存穿透保护：空值也缓存（可自定义标记）
            $cacheValue = json_encode($result);
            if ($cacheValue === false) {
                CUtil::debug('TurboModeService json_encode 异常', __METHOD__);
            } else {
                by::redis()->setex($cacheKey, $cacheTime, $cacheValue);
            }

            return $result;
        } catch (RedisException $e) {
            // Redis异常时，直接执行原方法
            CUtil::debug('TurboModeService Redis缓存异常: ' . $e->getMessage() . ' cacheKey: ' . $cacheKey, __METHOD__);
            return call_user_func_array($callback, $params);
        }
    }

    /**
     * 清除指定缓存
     * @param string $cacheKey 缓存键
     * @return bool
     */
    public function clearCache(string $cacheKey): bool
    {
        try {
            return by::redis()->del($cacheKey) > 0;
        } catch (RedisException $e) {
            CUtil::debug('TurboModeService 清除缓存异常: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    // 清除所有前缀缓存
    public function clearAllCache(): bool
    {
        try {
            $keys = by::redis()->keys($this->cache_key_prefix . '*');
            return by::redis()->del($keys) > 0;
        } catch (RedisException $e) {
            CUtil::debug('TurboModeService 清除缓存异常: ' . $e->getMessage(), __METHOD__);
            return false;
        }
    }

    /**
     * 获取极速模式开启状态
     * @return bool
     */
    public function isOpen(): bool
    {
        return $this->is_open;
    }

    /**
     * 设置缓存时间
     * @param int $time 缓存时间（秒）
     */
    public function setCacheTime(int $time)
    {
        $this->cache_time = $time;
    }


    public function getCacheKey($key)
    {
        return $this->cache_key = $this->cache_key_prefix . $key;
    }

    public function check()
    {
        $result = [];

        try {
            // 模糊查询所有以cache_key_prefix开头的键
            $keys = by::redis()->keys($this->cache_key_prefix . '*');

            foreach ($keys as $fullCacheKey) {
                // 提取原始的缓存键名（去掉前缀）
                $cacheKey = str_replace($this->cache_key_prefix, '', $fullCacheKey);

                try {
                    // 获取TTL
                    $ttl = by::redis()->ttl($fullCacheKey);

                    // 获取缓存值
                    $cacheValue   = by::redis()->get($fullCacheKey);
                    $decodedValue = null;
                    if ($cacheValue !== false) {
                        try {
                            $decodedValue = json_decode($cacheValue, true);
                        } catch (\Throwable $e) {
                            $decodedValue = $cacheValue; // 如果解码失败，保留原始值
                        }
                    }

                    $result[$cacheKey] = [
                            'cache_key'   => $fullCacheKey,
                            'ttl'         => $ttl,
                            'status'      => $this->getTtlStatus($ttl),
                            'expire_time' => $ttl > 0 ? date('Y-m-d H:i:s', time() + $ttl) : null,
                            'cache_value' => $decodedValue,
                    ];
                } catch (RedisException $e) {
                    $result[$cacheKey] = [
                            'cache_key'   => $fullCacheKey,
                            'ttl'         => null,
                            'status'      => 'redis_error',
                            'error'       => $e->getMessage(),
                            'expire_time' => null,
                            'cache_value' => null,
                    ];
                }
            }

        } catch (RedisException $e) {
            return [
                    'error' => 'Redis查询异常: ' . $e->getMessage(),
                    'data'  => []
            ];
        }

        return [
                'total_count' => count($result),
                'list'        => $result
        ];
    }

    /**
     * 获取TTL状态描述
     * @param int $ttl
     * @return string
     */
    private function getTtlStatus($ttl)
    {
        if ($ttl === -2) {
            return 'not_exist'; // 键不存在
        } elseif ($ttl === -1) {
            return 'no_expire'; // 键存在但没有设置过期时间
        } elseif ($ttl === 0) {
            return 'expired'; // 键已过期
        } else {
            return 'active'; // 键存在且有效
        }
    }

}