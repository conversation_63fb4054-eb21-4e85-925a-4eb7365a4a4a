<?php

namespace app\modules\main\services;

use app\modules\wares\services\goods\IndexGoodsMainService;

class SearchService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @throws \yii\db\Exception
     * @throws \RedisException
     */
    public function GetWaresByName($keyword, $limit, $page=1, $pageSize=10, $source=2, $platformIds=[1,99]): array
    {
        $return = [
            'list'  => [],
            'count' => 0
        ];

        $indexGoodsMainService = new IndexGoodsMainService();

        //1.获取所有的商品
        $allGoods = $indexGoodsMainService->GetAllMainList($source);
        if(!$allGoods) {
            return [true, $return];
        }

        //2.取出所有符合条件商品的IDs（IDs>$limit直接报搜索条数过多）
        $goodsData = array_column($allGoods, 'name', 'id');
        $gids = array_keys(array_filter($goodsData, function ($name) use ($keyword) {
            return strpos(strtolower($name), strtolower($keyword)) !== false;
        }));

        //3.获取符合条件的商品条数
        $return['count'] = count($gids);
        if (!empty($gids) && $return['count'] > $limit) {
            return [false, '查询参数过于模糊，请精确搜索'];
        }

        //4.分页
        $gids = array_slice($gids, ($page - 1) * $pageSize, $pageSize);
        if (empty($gids)) {
            return [true, $return];
        }


        //5.查出商品详情
        $data = [];
        foreach ($gids as $gid) {
            $dataItem = $indexGoodsMainService->IndexMainInfoByGid($gid, $platformIds, true, true, false);
            //过滤参数
            unset($dataItem['detail'], $dataItem['notice'], $dataItem['specs'], $dataItem['image'], $dataItem['images']);
            if ($platformIds && in_array($dataItem['platform'] ?? 0, $platformIds)) {
                $data[] = $dataItem;
            }
        }
        $return['list'] = $data;

        return [true, $return];
    }
}
