<?php

namespace app\modules\main\services;

use app\components\AliZhima;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use yii\db\Exception;

class ZhimaService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function AgainPay($user_id, $post)
    {
        if (empty($post['order_no']) || empty($user_id)) {
            return [false, '参数错误'];
        }
        $order_no = $post['order_no'];
        $ac_id    = $post['ac_id'] ?? 0;
        $payType  = $post['pay_type'] ?? 0;

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = by::Omain()->ReqAntiConcurrency($user_id, $unique_key, 3);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        //查询订单信息
        $order_info = by::Ouser()->CommPackageInfo($user_id, $order_no, false, false, false, false, false, false, true);
        if (empty($order_info)) {
            return [false, '订单不存在'];
        }
        $order_info['out_request_no'] = $order_no . '_' . time();

        //活动信息校验
        if ($ac_id) {
            $activityInfo = byNew::ActivityModel()->getOneById($ac_id);
            if (empty($activityInfo)) {
                return [false, "活动不存在"];
            }

            if (time() < $activityInfo['start_time']) {
                return [false, "活动未开始"];
            }

            if (time() > $activityInfo['end_time']) {
                return [false, "活动已结束"];
            }
        }

        // 查看支付流水
        $aOpay = by::model('OPayModel', 'goods')->GetOneInfo($order_no);
        if (!empty($aOpay['pay_time'])) {
            return [false, '订单已支付'];
        }

        //查看芝麻信用支付状态，如果已经支付直接返回报错
        if (!empty($aOpay['tid'])) {
            $status = by::Omain()->CheckZhiMaOrderPayStatus($order_no, $aOpay['tid'] ?? '');
            if ($status) {
                return [false, "订单已支付"];
            }
        }

        // 取消原有支付类型的订单
        $b = by::BasePayModel()->CancelNoPayOrder($payType, $aOpay);
        if (!$b) {
            return [false, '取消原有支付类型的订单失败'];
        }

        // 查看ptime距今是否超过两小时，超过两小时就重新创建订单
        if ((time() - $aOpay['ptime'] > 7200) || empty($aOpay['prepay_id']) || $aOpay['pay_type'] == by::Omain()::PAY_BY_NO_SET) {
            // 创建芝麻订单
            return $this->CreateZhimaOrder($user_id, $ac_id, $order_info);
        }

        // 直接返回存量数据
        $result = [
            'pay_type'  => by::Omain()::PAY_BY_ALI_ZHIMA,
            'order_str' => $aOpay['prepay_id'],
        ];
        return [true, $result];
    }


    /**
     * 创建芝麻订单
     * @param $user_id
     * @param $ac_id
     * @param $order_info
     * @return array
     */
    public function CreateZhimaOrder($user_id, $ac_id, $order_info)
    {
        if (empty($user_id) || empty($order_info)) {
            return [false, '参数错误'];
        }
        $order_no = $order_info['order_no'];
        $label = implode(',', array_column($order_info['goods'], 'label'));
        $goodsName = implode(',', array_column($order_info['goods'], 'name'));
        // 事务处理
        $transaction = by::dbMaster()->beginTransaction();
        try {
            //1.成功拉起芝麻支付，否则回滚
            list($sta, $orderStr) = AliZhima::factory()->FreezeOrder($order_info);
            if (!$sta) {
                throw new \Exception("拉起芝麻支付失败~");
            }

            //2.更新支付流水表
            $now     = time();
            $payData = [
                'prepay_id' => $orderStr,
                'ptime'     => $now,
                'tid'       => $order_info['out_request_no'],
                'h5_url'    => '',
                'pay_type'  => by::Omain()::PAY_BY_ALI_ZHIMA,
            ];
            list($s, $msg) = by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);
            if (!$s) {
                throw new \Exception($msg);
            }

            $transaction->commit();

            $result = [
                'pay_type'  => by::Omain()::PAY_BY_ALI_ZHIMA,
                'order_str' => $orderStr,
            ];
            return [true, $result];
        } catch (\Exception $e) {
            $transaction->rollBack();
            //打印日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.zhima.pay');
            return [false, $e->getMessage()];
        }
    }



    public function ZhiMaFreezeNotify($data)
    {
        // 如果回调就说明已支付，不能再进行创建订单
        $redisKey = by::Omain()->HasPayRedisKey($data['out_order_no']);
        $redis = by::Redis();
        $redis->set($redisKey, 1, 3600);
        $orderNo = $data['out_order_no'];
        $authNo  = $data['auth_no'];//支付宝资金授权订单号   ----> 用于解冻(完结订单)
        $amount  = $data['amount'];
        //1.鉴定orderNo是否存在
        $orderInfo = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $orderNo)]);
        if (empty($orderInfo)) {
            return [false, '订单不存在'];
        }
        $user_id = CUtil::uint($orderInfo['user_id']);
        if (empty($user_id)) {
            return [false, '用户不存在'];
        }
        $mOuser = by::Ouser();
        $mOmain = by::Omain();
        //todo 订单状态检测
        $order = $mOuser->CommPackageInfo($user_id, $orderNo, false, false, false, false, true, false, false);
        if (empty($order)) {
            CUtil::debug("订单不存在|{$user_id}|{$orderNo}", 'err.n.pay');
            return [false, '订单不存在'];
        }

        if ($order['status'] == $mOmain::ORDER_STATUS['WAIT_SEND']) {
            CUtil::debug("订单状态已更新:" . json_encode($order), 'err.n.pay');
            return [true, 'ok'];
        }

        if ($order['status'] != $mOmain::ORDER_STATUS['WAIT_PAY']) {
            CUtil::debug("无效订单:" . json_encode($order), 'err.n.pay');
            return [false, '无效订单'];
        }

        $pay_price = bcadd($order['price'], $order['fprice'], 2);
        if (bccomp($pay_price, $amount, 2) != 0) {
            CUtil::debug("支付金额与商品实际金额不一致:" . json_encode($data), 'err.n.pay');
            return [false, '支付金额与商品实际金额不一致'];
        }

        // 订单通知处理
        $source = by::wxPay()::SOURCE['BUY_AFTER_PAY'];
        return by::BasePayModel()->afterPay($user_id, $orderNo, $data, $order, $source, by::Omain()::PAY_BY_ALI_ZHIMA);
    }


    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 保存参加先试后买用户
     */
    public function SaveTryUser($userId): array
    {
        //新增
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);

        $save = [
            'user_id' => $userId,
            'uid'     => $mallInfo['uid'] ?? '',
            'phone'   => $mallInfo['phone'] ?? ''
        ];
        return byNew::UserTryModel()->SaveUser($save);
    }


}
