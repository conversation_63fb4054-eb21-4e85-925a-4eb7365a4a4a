<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;

/**
 * 商品服务
 */
class GmainService
{
    // 设置 redis 缓存的过期时间 1s
    const EXPIRE_TIME = 1;

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * 根据商品类目获取商品列表
     * @param int $cateId
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function GetGoodsListByCateId(int $cateId, int $platformId): array
    {
        // 缓存的前缀：商品列表
        $r_key = AppCRedisKeys::getGoodsParamGoodsList($cateId);

        // 缓存：从 redis 获取数据
        $redis = by::redis();
        $cachedData = $redis->get($r_key);
        if ($cachedData) {
            return json_decode($cachedData, true);
        }

        // 获取商品分类
        $cateData = by::cateModel()->getOneInfoByPid($cateId);
        if (empty($cateData)) {
            return [];
        }
        $cateIds = array_column($cateData, 'id');

        // 获取商品信息，且商品（没删除、没下架）
        $goodsData = by::Gmain()->getDataByCateIds($cateIds);
        if (empty($goodsData)) {
            return [];
        }

        // 获取当前平台的商品gid
        $gids = array_column($goodsData, 'gid');
        $effectiveGids = GoodsPlatformService::getInstance()->getEffectiveGids($gids, $platformId);


        $skus = array_column($goodsData, 'sku');

        // 获取配置了参数的商品（不允许service层互调）
        $items = GparamGoodsService::getInstance()->getParamsBySkus($skus, [0, 1]);
        $skus = array_unique(array_column($items, 'sku'));

        // 返回参数
        $data = [];
        foreach ($goodsData as $item) {
            if (in_array($item['sku'], $skus) && in_array($item['gid'], $effectiveGids)) {
                $data[] = $item;
            }
        }

        // 按 gid 倒序
        $data = Collection::wrap($data)->sortByDesc('gid')->all();

        $data = array_values($data);

        // 缓存：向 redis 插入数据
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 商品销售状态
     * @param $sku
     * @return int
     * @throws \yii\db\Exception
     */
    public function getSaleStatus($sku): int
    {
        $model = by::Gmain();
        $item = $model->GetOneBySku($sku);
        if (empty($item) || ($item['status'] == $model::STATUS['OFF_SALE'])) { // 下架
            return $model::STATUS['OFF_SALE'];
        }
        return $model::STATUS['ON_SALE'];
    }

    /**
     * 获取商品标签
     * @param int $gid
     * @return array
     * @throws \yii\db\Exception
     */
    public function getTagsByGid(int $gid): array
    {
        $tags = by::Gmain()->getProductTags($gid);
        return $tags['tids_name'];
    }

    /**
     * 获取商品名称
     * @param int $gid
     * @return string
     * @throws \yii\db\Exception
     */
    public function getGoodsNameByGid(int $gid): string
    {
        $goods = by::Gmain()->getOneByGid($gid);
        return $goods['name'] ?? '';
    }
}