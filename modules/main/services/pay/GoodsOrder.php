<?php

namespace app\modules\main\services\pay;

use app\components\Crm;
use app\components\ErpNew;
use app\components\EventMsg;
use app\components\PointCenter;
use app\jobs\AutoCouponActivityPushJob;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\FinishTaskJob;
use app\jobs\GroupPurchaseJob;
use app\jobs\SyncPointGrowJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\goods\models\OmainModel;
use Throwable;
use yii\db\Exception;

/**
 * 商品订单
 */
class GoodsOrder extends Order
{
    // 订单信息
    protected $order;

    /**
     * 是否需要处理
     * @return bool
     * @throws \yii\db\Exception
     */
    public function isNeedHandle(): bool
    {
        // 退款单
        $this->order = by::Ouser()->GetInfoByOrderId($this->user_id, $this->order_no, false);
        // 已支付成功（待发货）：无需处理
        if (isset($this->order['status']) && $this->order['status'] == OmainModel::ORDER_STATUS['WAIT_SEND']) {
            return false;
        }
        return true;
    }

    /**
     * 校验订单
     */
    public function validate()
    {
        // 1. 订单不存在
        if (empty($this->order)) {
            throw new MyExceptionModel('订单不存在');
        }

        // 2. 订单状态不符
        if ($this->order['status'] != OmainModel::ORDER_STATUS['WAIT_PAY']) {
            throw new MyExceptionModel('订单状态不符，无法支付');
        }

        // 单位：分
        $pay_price = bcadd($this->order['price'], $this->order['fprice'], 2); // 支付金额=商品金额+运费
        if (bccomp($pay_price, $this->payment_amount, 2) != 0) {
            throw new MyExceptionModel('支付金额与商品实际金额不一致');
        }
    }

    /**
     * 支付操作
     * @throws Throwable
     */
    public function handle()
    {
        // 开启事务
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 1.同步支付流水
            $this->syncPayLog($this->order_no, $this->trade_no, $this->payment_time, $this->pay_type);

            // 2.更新订单状态：待发货
            $this->updateStatus($this->user_id, $this->order_no, OmainModel::ORDER_STATUS['WAIT_SEND'], $this->payment_time);

            // 3.更新库存
            $skus = $this->updateStock($this->user_id, $this->order_no);

            // 4.提交事务
            $trans->commit();

            // 5.同步crm\oms等操作
            $this->syncOperations($this->user_id, $this->order_no);

            // 6. 自动发放优惠券
            \Yii::$app->queue->push(new AutoCouponActivityPushJob(['user_id' => $this->user_id, 'order_no' => $this->order_no, 'skus' => $skus]));

            // 7. 团购订单回调
            if (!empty($this->order['group_purchase_id'] ?? 0)){
                \Yii::$app->queue->push(new GroupPurchaseJob(['user_id' => $this->user_id, 'order_no' => $this->order_no, 'group_purchase_id' => $this->order['group_purchase_id']]));
            }
            $commissionInfo = byNew::SalesCommissionModel()->getInfo($this->order_no);
            if ($commissionInfo){
                byNew::SalesCommissionModel()->patch(['order_no'=>$this->order_no],['status'=>300,'utime'=>time()]);
                // 如果是小店的订单，则给奖励
                $avt = YII_ENV_PROD ? 32 : 170;
                if ($commissionInfo['activity_id'] == $avt){
                    EventMsg::factory()->run('friendBuyShopGoods', ['user_id' => $commissionInfo['referrer']]);
                }
            }
            // 追觅合伙人埋点
            \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$this->user_id,'order_no' => $this->order_no,'type'=>2,'field'=>'','number'=>0,'addOrsubtract'=>'+']));

        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录日志
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.goods.pay');
            throw new MyExceptionModel('支付失败');
        }
    }

    /**
     * 更新订单状态
     * @param $user_id
     * @param $order_no
     * @param $status
     * @param $payment_time
     * @return void
     * @throws MyExceptionModel
     * @throws \yii\db\Exception
     */
    private function updateStatus($user_id, $order_no, $status, $payment_time)
    {
        list($status, $ret) = by::Omain()->SyncInfo($user_id, $order_no, $status, ['pay_time' => $payment_time]);
        if (!$status) {
            CUtil::debug('订单状态修改失败：' . $ret, 'err.goods.pay');
            throw new MyExceptionModel('订单状态修改失败');
        }
    }

    /**
     * 更新库存
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws MyExceptionModel
     * @throws Exception
     */
    private function updateStock($user_id, $order_no)
    {
        $skus=[];
        // 订单商品
        $orderGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
        foreach ($orderGoods as $v) {
            // 1.更新退款商品的库存
            $goodsType = $v['goods_type'] ?? '';
            $goodsSource = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                ? by::GoodsStockModel()::SOURCE['WARES']
                : by::GoodsStockModel()::SOURCE['MAIN'];
            list($stockStatus,$sku) = by::GoodsStockModel()->UpdateStock($v['gid'], $v['sid'], $v['num'], 'WAIT', true, $goodsSource);
            if ($stockStatus) {
                $skus[] = $sku;
            }
            // 2.更新自定义价格的商品库存
            if ($giniId = $v['gini_id'] ?? 0) {
                list($status, $msg) = by::Gini()->UpdateStock($giniId, $v['num'], 'WAIT');
                if (!$status) {
                    CUtil::debug('更新自定义价格的商品库存异常：' . $msg, 'err.goods.pay');
                    throw new MyExceptionModel($msg);
                }
            }
        }
        return $skus;
    }

    /**
     * 同步crm\oms等操作
     * @param $user_id
     * @param $order_no
     * @return void
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function syncOperations($user_id, $order_no)
    {
        // 1. 新旧erp锁
        $lockOldErp = CUtil::omsLock($user_id, $order_no);
        if ($lockOldErp) {
            //团购订单不推送E3
            if (empty($this->order['group_purchase_id'] ?? 0)) {
                ErpNew::factory()->synErp('addOrder', ['user_id' => $user_id, 'order_no' => $order_no]);
            }
        } else {
            by::Ouser()->ErpAddOrder($user_id, $order_no, 0, [], false);
        }

        // // 2. 订单同步crm
        // Crm::factory()->push($user_id, 'order', ['user_id' => $user_id, 'order_no' => $order_no]);
        // Crm::factory()->push($user_id, 'orderLine', ['user_id' => $user_id, 'order_no' => $order_no]);
        PointCenter::factory()->orderPush($user_id, $order_no);

        // 3. 消息通知处理
        by::UserMsg()->changeTailOrderMsgStatus($user_id, $order_no);
        // 4. 同步腾讯信息
        by::userAdv()->pushAdv($user_id, $order_no, 'PURCHASE');
        // 5. 插入抽奖购买任务队列
        \Yii::$app->queue->push(new FinishTaskJob(['user_id' => $user_id, 'order_no' => $order_no]));
    }

    /**
     * 同步支付流水
     * @param $order_no
     * @param $trade_no
     * @param $payment_time
     * @param $pay_type
     * @return void
     */
    private function syncPayLog($order_no, $trade_no, $payment_time, $pay_type)
    {
        by::model('OPayModel', 'goods')->SaveLog(
            $order_no,
            ['tid' => $trade_no, 'pay_time' => $payment_time, 'pay_type' => $pay_type]
        );
    }

}

