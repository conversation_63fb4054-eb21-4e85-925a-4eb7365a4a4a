<?php

namespace app\modules\main\services\pay;

use app\components\EventMsg;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\FinishTaskJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\goods\models\OdepositModel;

/**
 * 定金订单
 */
class DepositOrder extends Order
{
    // 订单信息
    protected $order;

    /**
     * 是否需要处理
     * @return bool
     * @throws \yii\db\Exception
     */
    public function isNeedHandle(): bool
    {
        // 退款单
        $this->order = by::Odeposit()->getInfoByDepositOrderNo($this->user_id, $this->order_no);
        // 已支付成功（待发货）：无需处理
        if (isset($this->order['status']) && $this->order['status'] == OdepositModel::STATUS['WAIT_SEND']) {
            return false;
        }
        return true;
    }

    /**
     * 校验订单
     */
    public function validate()
    {
        // 参数
        $payment_amount = $this->payment_amount;

        // 1. 订单不存在
        if (empty($this->order)) {
            throw new MyExceptionModel('定金订单不存在');
        }

        // 2. 订单状态不符
        if ($this->order['status'] != OdepositModel::STATUS['WAIT_PAY']) {
            throw new MyExceptionModel('订单状态不符，无法支付');
        }

        // 单位：分
        $pay_price = bcmul($this->order['price'], 100, 2); // 在取出order的price时，已经转为了元，此处再还原成分
        if (bccomp($pay_price, $payment_amount, 2) != 0) {
            throw new MyExceptionModel('支付金额与实际金额不一致');
        }
    }

    public function handle()
    {
        // 开启事务
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 1.同步支付流水
            $this->syncPayLog($this->order_no, $this->trade_no, $this->payment_time, $this->pay_type);

            // 2.更新订单状态：待发货
            $this->updateStatus($this->user_id, $this->order_no, OdepositModel::STATUS['WAIT_SEND']);

            // 3.更新库存
            $this->updateStock($this->user_id, $this->order_no);

            // 4.创建尾款订单
            $this->createTailOrder($this->user_id, $this->order_no);

            // 5.提交事务
            $trans->commit();
            $commissionInfo = byNew::SalesCommissionModel()->getInfo($this->order_no);
            if ($commissionInfo){
                byNew::SalesCommissionModel()->patch(['order_no'=>$this->order_no],['status'=>300,'utime'=>time()]);
                // 如果是小店的订单，则给奖励
                $avt = YII_ENV_PROD ? 32 : 170;
                if ($commissionInfo['activity_id'] == $avt){
                    EventMsg::factory()->run('friendBuyShopGoods', ['user_id' => $commissionInfo['referrer']]);
                }
            }
            // 追觅合伙人埋点
            \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$this->user_id,'order_no' => $this->order_no,'type'=>2,'field'=>'','number'=>0,'addOrsubtract'=>'+']));
            // 5.同步crm\oms等操作
            $this->syncOperations($this->user_id, $this->order_no);
        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录日志
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.deposit.pay');
            throw new MyExceptionModel('支付失败');
        }
    }

    /**
     * 更新订单状态
     * @param $user_id
     * @param $order_no
     * @param $status
     * @return void
     * @throws MyExceptionModel
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function updateStatus($user_id, $order_no, $status)
    {
        //同步支付结果,调整订单状态
        list($status, $ret) = by::Odeposit()->SyncInfo($user_id, $order_no, $status);
        if (!$status) {
            CUtil::debug('定金订单状态修改失败：' . $ret . ' order_no：' . $order_no, 'err.deposit.pay');
            throw new MyExceptionModel('定金订单状态修改失败');
        }
    }

    /**
     * 修改库存
     * @param $user_id
     * @param $order_no
     * @return void
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function updateStock($user_id, $order_no)
    {
        // 获取定金订单详情
        $order = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
        by::Gprestock()->UpdateStock($order['gid'], $order['sid'], $order['num'], 'WAIT');
    }

    /**
     * 创建尾款订单
     * @param $user_id
     * @param $order_no
     * @return void
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function createTailOrder($user_id, $order_no)
    {
        //创建尾款订单
        list($status, $ret) = by::Omain()->addDepositTailRecord($user_id, $order_no);
        if (!$status) {
            CUtil::debug('尾款订单创建失败：' . $ret . ' order_no：' . $order_no, 'err.deposit.pay');
            throw new \Exception('尾款订单创建失败');
        }
    }

    /**
     * 同步操作
     * @param $user_id
     * @param $order_no
     * @return void
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function syncOperations($user_id, $order_no)
    {
        // 同步腾讯信息
        by::userAdv()->pushAdv($user_id, $order_no, 'PURCHASE');
        // 插入抽奖购买任务队列
        \Yii::$app->queue->push(new FinishTaskJob(['user_id' => $user_id, 'order_no' => $order_no]));
    }

    /**
     * 同步支付流水
     * @param $order_no
     * @param $trade_no
     * @param $payment_time
     * @param $pay_type
     * @return void
     */
    private function syncPayLog($order_no, $trade_no, $payment_time, $pay_type)
    {
        by::model('OPayModel', 'goods')->SaveLog(
            $order_no,
            ['tid' => $trade_no, 'pay_time' => $payment_time, 'pay_type' => $pay_type]
        );
    }
}