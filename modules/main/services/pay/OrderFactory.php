<?php

namespace app\modules\main\services\pay;

/**
 * 订单工厂
 */
class OrderFactory
{
    /**
     * 创建服务
     * @param $order_type
     * @param $pay_type
     * @param $user_id
     * @param $order_no
     * @param $payment_amount
     * @param $payment_time
     * @param $trade_no
     * @return Order
     * @throws \Exception
     */
    public static function createService($order_type, $pay_type, $user_id, $order_no, $payment_amount, $payment_time, $trade_no): Order
    {
        switch ($order_type) {
            case Order::ORDER_TYPE['GOODS']:
                return new GoodsOrder($user_id, $order_no, $payment_amount, $payment_time, $trade_no, $pay_type);
            case Order::ORDER_TYPE['DEPOSIT']:
                return new DepositOrder($user_id, $order_no, $payment_amount, $payment_time, $trade_no, $pay_type);
            default:
                throw new \Exception("Unsupported product type");
        }
    }
}