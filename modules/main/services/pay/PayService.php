<?php

namespace app\modules\main\services\pay;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\pay\MpPayModel;

/**
 * 支付服务
 */
class PayService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 支付方式
    const PAY_TYPE = [
        'WECHAT'    => 1,
        'WECHAT_H5' => 2,
        'ALIPAY'    => 4,
        'MP_PAY'    => 5, // 中台支付
    ];

    /**
     * 处理支付通知
     * @param $pay_type
     * @param $params
     * @return array|void
     */
    public function handleNotify($pay_type, $params)
    {
        switch ($pay_type) {
            case self::PAY_TYPE['WECHAT']:
                // TODO 微信支付
                break;
            case self::PAY_TYPE['WECHAT_H5']:
                // TODO 微信H5支付
                break;
            case self::PAY_TYPE['ALIPAY']:
                // 支付宝支付
                return $this->alipayNotify($params);
            case self::PAY_TYPE['MP_PAY']:
                // 中台支付
                return $this->mpNotify($params);
            default:
                return [true, ''];
        }
    }

    /**
     * 支付宝支付
     * @param array $params
     * @return array
     */
    private function alipayNotify(array $params): array
    {
        // 该笔会先收到 TRADE_SUCCESS 交易状态，然后超过 交易有效退款时间 该笔交易会再次收到 TRADE_FINISHED 状态，实际该笔交易只支付了一次，切勿认为该笔交易支付两次
        if (isset($params['trade_status']) && $params['trade_status'] == 'TRADE_FINISHED') {
            return [true, '交易完成：满一年不处理'];
        }

        // 交易状态：非交易成功、交易结束不处理
        if (!in_array($params['trade_status'], ['TRADE_SUCCESS', 'TRADE_FINISHED'])) {
            return [false, '交易失败：交易状态异常'];
        }

        // 订单参数：用户、订单号、订单来源
        parse_str(urldecode($params['passback_params']), $order_params);
        if (!isset($order_params['user_id'], $order_params['order_no'], $order_params['order_type'])) {
            return [false, '交易失败：订单参数异常'];
        }

        // 缓存支付成功的状态
        $redis = by::redis();
        $orderKey = AppCRedisKeys::getAliPayStatus($order_params['order_no']);
        $redis->set($orderKey, 1, 1800);

        // 处理支付后的回调
        try {
            $payment_amount = bcmul($params['total_amount'], 100); // 转为分
            $payment_time = strtotime($params['gmt_payment']); // 转为时间戳
            $service = OrderFactory::createService(
                $order_params['order_type'],
                self::PAY_TYPE['ALIPAY'],
                $order_params['user_id'],
                $order_params['order_no'],
                $payment_amount,
                $payment_time,
                $params['trade_no']
            );
            $service->pay();
            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 中台支付
     * @param array $params
     * @return array
     */
    private function mpNotify(array $params): array
    {
        // 订单参数：用户、订单号、订单来源
        parse_str(urldecode($params['passback']), $order_params);
        if (!MpPayModel::getInstance()->checkPassback($order_params)) {
            return [false, '交易失败：订单参数异常'];
        }

        // 处理支付后的回调
        try {
            $payment_amount = bcmul($params['total_amount'], 100); // 转为分
            $payment_time = $params['pay_time'];
            $service = OrderFactory::createService(
                $order_params['order_type'],
                $order_params['pay_type'],
                $order_params['user_id'],
                $order_params['order_no'],
                $payment_amount,
                $payment_time,
                $params['bill_id'] // 交易单号
            );
            $service->pay();
            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }
}