<?php

namespace app\modules\main\services;



use app\models\by;
use RedisException;
use yii\db\Exception;

/**
 * 商品服务
 */
class GtiedSaleService
{

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $sku
     * @param $platformId
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取主机搭配购买的配件信息
     */
    public function getTiedSaleInfo($sku, $platformId): array
    {
        // 验证SKU是否存在
        if (!$sku) {
            return [false, []];
        }

        // 获取主要部件信息
        $mainPartInfo = by::mainPartModel()->getInfoByMainSku($sku);
        $tiedSale     = json_decode($mainPartInfo['tied_sale'] ?? '', true) ?? [];

        // 验证捆绑销售信息是否存在
        if (!$tiedSale) {
            return [true, []];
        }

        // 过滤和处理搭配售卖信息
        $parts = array_filter(array_map(function ($tied) use ($platformId) {
            if (!($tied['is_tied'] ?? '')) {
                return null;
            }

            // 获取配件信息
            $goods = by::gCateModel()->getMachineTypeByPartSku($tied['part_sku'] ?? '') ?? [];
            if (empty($goods)) {
                return null;
            }

            // 检查平台ID是否匹配
            $platformIds = $goods['platform_ids'] ?? [];
            if (!empty($platformIds) && !in_array($platformId, $platformIds)) {
                return null;
            }

            // 获取平台商品信息
            $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platformId, $goods);

            // 如果配件删除了则跳过
            if (empty($platformInfo)) {
                return null;
            }

            $part = [
                'id'           => $goods['id'] ?? '',
                'gid'          => $goods['gid'] ?? '',
                'sku'          => $goods['sku'] ?? '',
                'price'        => $goods['price'] ?? 0,
                'av_ids'       => $goods['av_ids'] ?? '',
                'gini_id'      => $goods['gini_id'] ?? 0,
                'name'         => $goods['name'] ?? '',
                'limit_num'    => $goods['limit_num'] ?? 0,
                'stock'        => $goods['stock'] ?? 0,
                'machine_type' => $goods['machine_type'] ?? '',
                'images'       => $platformInfo['images'] ?? '',
                'cover_image'  => $platformInfo['cover_image'] ?? '',
                'num'          => "0",// 前端需要的字段 默认数量为0 加减数量时再修改
            ];

            return ($goods['is_stock'] && $goods['status'] == 0) ? $part : null;
        }, $tiedSale));

        // 返回结果
        return [true, array_values($parts)];
    }


}