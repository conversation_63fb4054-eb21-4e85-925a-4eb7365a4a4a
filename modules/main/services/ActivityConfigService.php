<?php

namespace app\modules\main\services;

use app\components\Device;
use app\models\by;
use app\models\byNew;
use app\models\CodeModel;
use app\models\CUtil;
use app\modules\log\services\AcDrawLogService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\enums\user\UserInfoEnum;
use app\modules\main\models\ActivityConfigModel;
use app\modules\main\models\AcType1Model;
use app\modules\main\models\ErpLogModel;
use RedisException;
use yii\db\Exception;

class ActivityConfigService
{
    private static $_instance = NULL;
    protected $activityConfigModel;

    private function __construct()
    {
        $this->activityConfigModel = by::activityConfigModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 生日活动状态
    const BIRTHDAY_CARD_STATUS = [
        'UN_FILL_BIRTHDAY' => 0, // 未填写生日
        'ENABLE_DRAW'      => 1, // 生日所在月：可领取
        'HAS_DRAW'         => 2, // 生日所在月：已经领取
        'UNABLE_DRAW'      => 3, // 生日所在月：不可领取，卡券已被领取完
        'NOT_ARRIVE_MONTH' => 4, // 未到生日所在月
        'PASS_MONTH'       => 5, // 已过生日所在月
    ];

    /**
     * @throws Exception
     */
    public function UserDrawNew($user_id, $ids, $marketIds, $r_id,$grant_type): array
    {
        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__, $ids);
        list($anti) = $this->activityConfigModel->ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试~'];
        }

        // 判断活动参数是否正确
        $ids = array_filter(array_unique(explode(',', CUtil::trimTags($ids))));
        if (empty($ids)) {
            return [false, '参数错误~'];
        }

        // 判断活动状态
        $aData = $this->activityConfigModel->getActivityOne($ids[0]);

        if (isset($aData['end_time']) && $aData['end_time'] < intval(START_TIME) &&
            ($aData['grant_type'] != $this->activityConfigModel::GRANT_TYPE['birthday'])) { // 生日活动，排除有效期概念
            return [false, '活动已结束~'];
        }

        // 变量用于存储是否需要检查优惠券的结果
        $checkCoupons = true;
        $amList       = [];
        $acType1Data  = [];
        // 如果是新人礼包，只有 has_coupon为 1才校验  为0时不校验
        if ($grant_type == ActivityConfigModel::GRANT_TYPE['newUser']) {
            $acType1Data = byNew::AcType1Model()->getInfoByAcId($ids[0]);
            // 判断是否需要检查优惠券
            if (isset($acType1Data['has_coupon']) && $acType1Data['has_coupon'] == AcType1Model::HAS_POINT_OR_COUPON['NO']) {
                $checkCoupons = false;
            }
        }

        // 如果需要检查优惠券，执行检查逻辑
        if ($checkCoupons) {
            $amList = by::aM()->getListByAid($ids[0]);
            if (empty($amList)) {
                return [false, '优惠券已经抢光~'];
            }
        }

        //过滤已过期的优惠券
        $amList = array_filter($amList, function ($value) {
            $couponInfo = by::marketConfig()->couponCondition($value['mc_id']);
            return ($couponInfo['expire_time'] ?? 0) >= time();
        });
        $amList = array_values($amList);

        //罗列所有符合的优惠券
        $market_ids = [];

        //校验领取状态
        if (in_array($aData['grant_type'], [$this->activityConfigModel::GRANT_TYPE['birthday'], $this->activityConfigModel::GRANT_TYPE['monthly']])) {
            // 判断生日情况
            if ($aData['grant_type'] == $this->activityConfigModel::GRANT_TYPE['birthday'] && (by::users()->UserReachBirthday($user_id) != UserInfoEnum::BIRTHDAY_STATUS['REACH'])) {
                return [false, '您尚未达到生日活动领取条件~'];
            }
            // 判断当月是否领过
            list($drawStatus, $info) = AcDrawLogService::getInstance()->UserDrawStatus($user_id, $aData['grant_type']);
            if ($drawStatus) {
                return [false, '您已领取过该活动优惠券~'];
            }
            $market_ids = $this->__getMarketIds($aData['grant_type'], $user_id, $amList, $ids);
        } elseif ($aData['grant_type'] == $this->activityConfigModel::GRANT_TYPE['newUser']) {
            $userInfo = by::users()->getOneByUid($user_id, false);
            if ($userInfo['is_new_gift'] != 0) {
                return [false, '新人礼包只能领取一次哟!'];
            }
            $market_ids = $this->__getMarketIds($aData['grant_type'], $user_id, $amList, $ids);
            // 新人礼包没有优惠券，不需要检查  只配置了优惠券没配置积分要给出提示
            if (empty($market_ids) && isset($acType1Data['has_point']) && $acType1Data['has_point'] == AcType1Model::HAS_POINT_OR_COUPON['YES']) {
                $checkCoupons = false;
            }
        } elseif ($aData['grant_type'] == $this->activityConfigModel::GRANT_TYPE['goods_detail']) {
            $market_ids = $this->__getMarketIds($aData['grant_type'], $user_id, $amList, $ids);
            $market_ids = array_intersect(explode(',', $marketIds), $market_ids);
            // 判断是否领过
            foreach ($ids as $acId) {
                list($drawStatus, $info) = AcDrawLogService::getInstance()->UserDrawStatus($user_id, $aData['grant_type'], $acId, $market_ids);
                if ($drawStatus) {
                    return [false, '您已领取过该活动优惠券~'];
                }
            }
        } else {
            return [false, '活动类型不正确！'];
        }

        if (empty($market_ids) && $checkCoupons) {
            return [false, '优惠券已经抢光~~'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        //todo 领月券/生日券
        try {
            //发放奖励
            list($status, $msg) = $this->activityConfigModel->activityDraw($user_id, $aData['grant_type'], $ids, $market_ids, $r_id);
            if (!$status) {
                throw new Exception($msg);
            }
            $transaction->commit();
            $this->activityConfigModel->ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
            by::userCard()->__delCache($user_id);
            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();
            //删除并发标识
            $this->activityConfigModel->ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
//            trigger_error($error);
            CUtil::debug($error, 'activity_receive_err');
            return [false, '领取失败'];
        }
    }


    /**
     * @param $grant_type
     * @param $user_id
     * @param $amList
     * @param $ids
     * @return array
     * 获取用户可领优惠券(加锁防止并发领取)
     */
    private function __getMarketIds($grant_type, $user_id, $amList, $ids): array
    {
        $market_ids = [];
        //优惠券锁
        $redis = by::redis('core');
        foreach ($amList as $am) {
            $m_key = CUtil::getAllParams(__FUNCTION__, $ids[0], $am['mc_id']);
            if ($redis->get($m_key)) continue;
            if (in_array($grant_type, [$this->activityConfigModel::GRANT_TYPE['birthday'], $this->activityConfigModel::GRANT_TYPE['monthly'], $this->activityConfigModel::GRANT_TYPE['goods_detail']])) {
                //获取用户level
                $level = strtolower(by::memberCenterModel()->GetUserLevel($user_id));
                if ($this->isIncludeLevel($am['level'], $level) && $am['stock'] > 0) {
                    $market_ids[] = $am['mc_id'];
                }
            } elseif ($grant_type == $this->activityConfigModel::GRANT_TYPE['newUser']) {
                if ($am['stock'] > 0) {
                    $market_ids[] = $am['mc_id'];
                }
            }
            if ($am['stock'] == 1) $redis->set($m_key, 1, 5);
        }
        return $market_ids;
    }


    /**
     * @throws Exception
     */
    public function UserMonthCoupon($data, $arr)
    {
        $status         = ActivityConfigEnum::MONTHLY_CARD_STATUS['can_draw'];
        $coupon         = [];
        $marketIds      = [];
        $activityCoupon = [];
        $acInfo         = [];
        //获取用户领券状态
        $user_id = CUtil::uint($arr['user_id'] ?? 0);

        list($drawStatus, $info) = AcDrawLogService::getInstance()->UserDrawStatus($user_id, $this->activityConfigModel::GRANT_TYPE['monthly']);
        if ($info) {
            $status = ActivityConfigEnum::MONTHLY_CARD_STATUS['has_draw'];
            //获取活动信息
            $acInfo    = by::activityConfigModel()->getActivityOne($info['ac_id'] ?? 0);
            $marketIds = explode(',', $info['market_ids']);
        } else {
            //获取当前券信息
            $activityCoupon = by::activityConfigModel()->getActivity($this->activityConfigModel::GRANT_TYPE['monthly'], $user_id, 1);
            if (empty($activityCoupon)) {
                $activityCoupon = by::activityConfigModel()->getActivity($this->activityConfigModel::NO_ARR_MONTHLY, $user_id, 1);
                if ($activityCoupon) {
                    $status = ActivityConfigEnum::MONTHLY_CARD_STATUS['no_draw'];//不可领取
                } else {
                    $status = ActivityConfigEnum::MONTHLY_CARD_STATUS['no_activity'];
                }
            }
        }
        if ($marketIds) {
            //已领取，显示领取的优惠券
            foreach ($marketIds as $mc_id) {
                $couponInfo = by::marketConfig()->couponCondition($mc_id);
                if (empty($couponInfo)) continue;
                $couponInfo['surplus_num'] = 1;
                if ($acInfo) {
                    $couponInfo['ac_s_time'] = $acInfo['start_time'];
                    $couponInfo['ac_e_time'] = $acInfo['end_time'];
                }
                $coupon[] = $couponInfo;
            }
        }

        if ($activityCoupon) {
            //如果有一张有库存只显示一张
            //如果全部没有库存则显示所有张并且状态为已领光
            $totalNum  = 0;
            $couponAll = [];
            $couponHas = [];

            foreach ($activityCoupon as $couponInfo) {
                $couponAll[] = $couponInfo;
                $stock       = CUtil::uint($couponInfo['surplus_num']);
                if ($stock > 0) {
                    $couponHas[] = $couponInfo;
                }
                $totalNum += $stock;
            }
            if ($totalNum == 0) {
                $status = ActivityConfigEnum::MONTHLY_CARD_STATUS['no_stock'];
                $coupon = $couponAll;
            } else {
                $coupon = $couponHas;
            }
        }

        //用户使用状态
        $data['status']    = $status;
        $data['coupon']    = $coupon;
        $data['ac_s_time'] = empty($coupon[0]['ac_s_time'] ?? 0) ? '' : date('Y.m.d', $coupon[0]['ac_s_time']);
        $data['ac_e_time'] = empty($coupon[0]['ac_e_time'] ?? 0) ? '' : date('Y.m.d', $coupon[0]['ac_e_time']);

        return $data;
    }

    /**
     * 是否包含此等级
     * @param $levels
     * @param $level
     * @return bool
     */
    private function isIncludeLevel($levels, $level): bool
    {
        $levels = explode(',', $levels);
        return in_array($level, $levels);
    }

    /**
     * @param $grant_type
     * @param $user_id
     * @return array
     * @throws Exception
     * 礼包列表
     */
    public function activityList($grant_type = 1, $user_id = 0): array {
        // 获取活动列表
        $list = by::activityConfigModel()->getActivity($grant_type, $user_id, 1);

        // 如果list为空，直接返回空数组
        if (empty($list)) {
            return [];
        }

        // 初始化优惠券和积分标记
        $has_coupon = $list['has_coupon'] ?? 0;
        $has_point  = $list['has_point'] ?? 0;
        $coupon     = $list['coupon'] ?? [];

        // 检查优惠券是否为空，如果为空直接返回空数组
        if (empty($coupon)) {
            return $list;
        }

        // 过滤无库存和已过期的优惠券
        $filteredCoupons = array_filter($coupon, function ($item) use ($grant_type, $has_coupon) {
            // 检查item是否为空，避免空值处理
            if (empty($item)) {
                return false;
            }

            // 判断是否为新人礼包且未配置优惠券
            $isNewUserGiftWithoutCoupon = ($grant_type == ActivityConfigModel::GRANT_TYPE['newUser']) &&
                    ($has_coupon == byNew::AcType1Model()::HAS_POINT_OR_COUPON['NO']);

            // 保留新人礼包未配置优惠券的项，或保留有库存且未过期的项
            return $isNewUserGiftWithoutCoupon || ($item['surplus_num'] > 0 && $item['expire_time'] >= START_TIME);
        });

        // 重建优惠券数组的索引
        $list['coupon'] = array_values($filteredCoupons);

        // 返回处理后的活动列表
        return $list;
    }



    /**
     * 获取生日活动，领取状态
     * @param $userId
     * @param $level
     * @return int
     * @throws \yii\db\Exception
     */
    public function getBirthdayCardStatus($userId, $level): int
    {
        // 一、生日填写状态
        $user = by::users()->getOneByUid($userId);
        if (empty($user) || empty($user['birthday'])) {
            return self::BIRTHDAY_CARD_STATUS['UN_FILL_BIRTHDAY'];
        }
        // 二、生日所在月份的状态
        // 当前月
        $currentMonth = date('m');
        // 生日月
        $birthMonth = date('m', $user['birthday']);
        // 未到生日所在月
        if ($birthMonth > $currentMonth) {
            return self::BIRTHDAY_CARD_STATUS['NOT_ARRIVE_MONTH'];
        }
        // 已过生日所在月
        if ($birthMonth < $currentMonth) {
            return self::BIRTHDAY_CARD_STATUS['PASS_MONTH'];
        }
        // 三、生日所在月：可领取、已经领取、不可领取，卡券已被领取完
        // 判断是否已经领取
        list($drawStatus, $info) = AcDrawLogService::getInstance()->UserDrawStatus($userId, ActivityConfigModel::GRANT_TYPE['birthday']);
        if ($drawStatus) {
            return self::BIRTHDAY_CARD_STATUS['HAS_DRAW'];
        }
        // 判断是否可领取
        $time   = time(); // 当前时间
        $acInfo = by::activityConfigModel()->getInfoByType(ActivityConfigModel::GRANT_TYPE['birthday']);
        if (empty($acInfo)) { // 活动不存在
            return self::BIRTHDAY_CARD_STATUS['UNABLE_DRAW'];
        }
        // 可领取的库存
        $stock   = 0;
        $coupons = by::aM()->getCouponListByAcId($acInfo['id']);
        foreach ($coupons as $coupon) {
            // 验证等级是否符合、优惠券是否过期
            $levels = explode(',', $coupon['level'] ?? '');
            if (in_array($level, $levels) && $time <= $coupon['expire_time']) {
                $stock += $coupon['stock'];
            }
        }
        if ($stock >= 1) {
            return self::BIRTHDAY_CARD_STATUS['ENABLE_DRAW'];
        }
        return self::BIRTHDAY_CARD_STATUS['UNABLE_DRAW'];
    }


    /**
     * @throws Exception
     */
    public function activityGoodsList($sn, $user_id)
    {
        $list = [];
        //判断用户
        if (strlen($user_id) > 11 || empty($user_id)) return $list;
        //获取产品注册信息
        $regInfo = by::productReg()->getPRegInfo($user_id, $sn);
        if (empty($regInfo)) return $list;
        $productId = CUtil::uint($regInfo['product_id'] ?? 0);
        $p_info    = by::product()->getOneById($productId);
        if (empty($p_info)) return $list;
        //获取可兑换商品SKU
        $preSn         = $p_info['sn'] ?? '';
        $acDepriceList = by::GmainAcDepriceModel()->GetListBySn($preSn);
        $gmainModel    = by::Gmain();
        //根据sku获取商品ID
        if ($acDepriceList) {
            foreach ($acDepriceList as $item) {
                $goodInfos = $gmainModel->GetGidAndSidBySku($item['sku'] ?? 0);
                $gid       = $goodInfos['gid'] ?? 0;
                $sid       = $goodInfos['sid'] ?? 0;
                if ($gid) {
                    $aGoods = $gmainModel->GetAllOneByGid($gid, true, false, 0, false);
                    $aGoods = [
                        'gid'         => $gid,
                        'sid'         => $sid,
                        'name'        => $aGoods['name'],
                        'cover_image' => $aGoods['cover_image'],
                        'mprice'      => $aGoods['mprice'],
                        'price'       => $aGoods['price'],
                        'deprice'     => $item['deprice'] ?? 0,
                        'newprice'    => bcsub($aGoods['price'], $item['deprice'] ?? 0, 2),
                    ];
                    $list[] = $aGoods;
                }
            }
        }
        return $list;
    }


    /**
     * 获取用户打卡活动详情（包含每日状态）
     *
     * @param int $userId 用户ID
     * @param array $extra 活动扩展配置（需包含打卡开始和结束时间）
     *
     * @return array 活动打卡状态数据结构：
     *         [
     *             'code' => 1, // 返回状态码（1为正常）
     *             'list' => [  // 每日打卡信息
     *                 [
     *                     'day'       => '2025-04-18',
     *                     'dayView'   => '04.18' 或 '打卡/已打卡',
     *                     'status'    => 0|1, // 是否打卡
     *                     'statusTag' => 0|1, // 同status
     *                     'numValue'  => 10,  // 每日可获得积分
     *                     'canCheck'  => 0|1  // 是否可打卡（今天未打可打，历史或已打不可）
     *                 ]
     *             ]
     *         ]
     * @throws RedisException
     */
    public function activityCheckDetail(int $userId, array $extra): array
    {
        $startTime = $extra['checkin_start_time']; // 活动起始时间
        $endTime   = $extra['checkin_end_time'];   // 活动结束时间
        $today     = date('Y-m-d');                // 当前日期（格式 Y-m-d）

        // 返回结构初始化
        $result = [
                'code' => 1,
                'list' => []
        ];

        // 获取整个活动期间的日期列表
        $checkinDays = $this->GetAllDaysByTime($startTime, $endTime);
        if (empty($checkinDays)) {
            return $result;
        }

        // 获取今日是否打卡的 Redis 缓存
        $todayCheckKey   = by::memberCenterModel()->GetTodayCheckInInKey($userId, $today);
        $hasCheckedToday = by::redis()->get($todayCheckKey); // 非空即为已打卡

        // 遍历打卡日期，构建打卡状态信息
        foreach ($checkinDays as $date) {
            $dateParts   = explode('-', $date);
            // 获取日期显示（月.日）
            $dateDisplay = ($dateParts[1] ?? '') . '.' . ($dateParts[2] ?? '');

            $isToday = ($date === $today);
            // 今日是否打卡 true：已打卡 false：未打卡
            $checked = $isToday && !empty($hasCheckedToday); // 今日且已打卡

            // 如果是今天，根据打卡状态显示文字
            if ($isToday) {
                $dateDisplay = empty($hasCheckedToday) ? '打卡' : '已打卡';
            }

            $result['list'][] = [
                    'day'       => $date,
                    'dayView'   => $dateDisplay,
                    'status'    => $checked ? 1 : 0,
                    'statusTag' => $checked ? 1 : 0,
                    'numValue'  => $extra['checkin_daily_points'],         // 每天固定积分值
                    'canCheck'  => ($date < $today || $checked) ? 0 : 1,   // 只能今天打，历史或已打卡不可重复打
            ];
        }

        // 非会员处理
        if (empty($userId) || strlen((string) $userId) > 11) {
            $result['code'] = CodeModel::STATUS['NOT_MEMBER'];
            $userId         = 0;
        }

        // 整合用户打卡记录（将已打记录标记进返回数组）
        $result['list'] = $this->BindWithCheckData($userId, $extra, $result['list']);

        return $result;
    }


    /**
     * 绑定用户实际打卡数据到签到列表中
     *
     * @param int $user_id 用户ID
     * @param array $extra   活动配置信息（包含签到时间、每日积分等）
     * @param array $list    原始签到天列表（含状态、积分等信息）
     *
     * @return array 返回绑定后的签到天数据（含实际打卡记录）
     */
    public function BindWithCheckData(int $user_id, array $extra, array $list): array
    {
        // 获取当前日期
        $currentDate = date('Y-m-d');

        // 如果是会员用户才进行打卡数据绑定
        if ($user_id) {
            // 签到周期时间范围
            $periodTime = [
                    $extra['checkin_start_time'],
                    $extra['checkin_end_time']
            ];

            // 拉取用户签到明细数据（打卡记录）
            list($status, $ret) = by::memberCenterModel()->CenterMessage('checkInInDetail', [
                    'user_id'               => intval($user_id),
                    'period_time'           => $periodTime,
                    'member_center_save_id' => $extra['member_center_save_id']
            ]);

            // 数据拉取失败则直接进入累计签到绑定逻辑
            if (!$status) {
                return $this->BindSumCheck($list, $extra);
            }

            // 获取签到数据（以 createTime 为索引）
            $checkData = $ret[0] ?? [];
            if (empty($checkData) || !is_array($checkData)) {
                return $this->BindSumCheck($list, $extra);
            }

            $checkDataIndexed = array_column($checkData, null, 'createTime');
            $checkDetail      = [];

            // 格式化数据，按日期（Y-m-d）重组数据（以日期为 key）
            foreach ($checkDataIndexed as $timestamp => $value) {
                $checkDay = date('Y-m-d', intval($timestamp / 1000)); // 转换毫秒为秒
                if ($checkDay < '2023-01-01') continue;               // 异常数据过滤
                $checkDetail[$checkDay] = $value;
            }

            // 将打卡记录绑定到原始签到列表中
            if (!empty($checkDetail)) {
                foreach ($list as &$dayItem) {
                    $day      = $dayItem['day'];
                    $checkRec = $checkDetail[$day] ?? [];

                    // 如果有打卡数据则使用，否则使用默认状态
                    $statusChecked = $checkRec['status'] ?? $dayItem['status'];

                    // 设置签到状态和显示标签
                    $dayItem['status']    = $statusChecked;
                    $dayItem['statusTag'] = $statusChecked;

                    // 设置积分值（如果打卡记录中有指定则使用记录中的）
                    $dayItem['numValue'] = $checkRec['numValue'] ?? $extra['checkin_daily_points'];

                    // 判断是否还能打卡（今天未打卡可打，其它已打不可打）
                    $dayItem['canCheck'] = ($statusChecked == 1 || $day < $currentDate) ? 0 : 1;

                    // 更新今天的 dayView 显示文字
                    if ($day == $currentDate && $statusChecked == 1) {
                        $dayItem['dayView'] = '已打卡';
                    }
                }
                unset($dayItem); // 释放引用
            }
        }

        // 无论是否绑定数据，最后都调用累计签到绑定逻辑
        return $this->BindSumCheck($list, $extra);
    }



    /**
     * 累计签到奖励数据绑定
     *
     * 根据传入的签到列表和活动设定，累计已签到次数，
     * 当签到次数达到活动要求时，将对应项标记为可获得奖励状态，
     * 若未满足则在当前日期对应项附近标记补签到奖励。
     *
     * @param array $list 已签到记录列表，每一项包含如下字段：
     *                     - 'day'      => 签到日期 (Y-m-d 格式)
     *                     - 'status'   => 签到状态（1 为已签到，0 为未签到）
     *                     - 其他字段可能存在...
     * @param array $extra 活动设定信息，包含：
     *                     - 'checkin_days'          => 活动要求签到天数
     *                     - 'checkin_reward_points' => 签到奖励积分数
     *
     * @return array 返回重构后的签到记录列表（索引从 0 开始）
     */
    public function BindSumCheck(array $list, array $extra): array
    {
        // 已签到次数初始值
        $checkedCount = 0;

        // 当前日期，格式为 'Y-m-d'
        $currentDate = date('Y-m-d');

        // 活动要求的签到天数
        $requiredCheckDays = $extra['checkin_days'];

        // 遍历签到列表，统计已签到天数
        foreach ($list as $key => $check) {
            // 累加已签到次数（status 为 1 表示已签到）
            if ($check['status'] == 1) {
                $checkedCount++;
            }
            // 当累计签到次数达到要求时
            if ($checkedCount == $requiredCheckDays) {
                // 在当前项标记奖励状态和奖励积分
                $list[$key]['statusTag'] = 2;
                $list[$key]['numValue']  = $extra['checkin_reward_points'];
                // 提前结束循环
                break;
            }
        }

        // 如果累计签到天数不足，则在当前日期附近找到合适的位置补充奖励标记
        if ($checkedCount < $requiredCheckDays) {
            // 计算还需签到的天数差额
            $remainingCheckDays = $requiredCheckDays - $checkedCount;

            // 在签到列表中查找当前日期对应的项
            foreach ($list as $k => $v) {
                if ($v['day'] == $currentDate) {
                    /*
                     * 计算目标标记的索引：
                     * 起始索引为当前日期所在项 $k，加上剩余天数减 1，
                     * 若当前日期已签到（status 为 1），则目标索引往后移动一位。
                     */
                    $targetIndex = $k + $remainingCheckDays - 1;
                    if ($v['status'] == 1) {
                        $targetIndex = $k + $remainingCheckDays;
                    }

                    // 如果目标索引在列表范围内，则标记该项为奖励状态，并赋予奖励积分
                    if (isset($list[$targetIndex])) {
                        $list[$targetIndex]['statusTag'] = 2;
                        $list[$targetIndex]['numValue']  = $extra['checkin_reward_points'];
                    }
                }
            }
        }

        // 返回重构后的列表，重新索引数组
        return array_values($list);
    }


    /**
     * @param $startTime
     * @param $endTime
     * @return array
     * 获取时间范围内的所有数据
     */
    public function GetAllDaysByTime($startTime, $endTime): array
    {
        $arr = [];
        while ($startTime <= $endTime) {
            $arr[]     = date('Y-m-d', $startTime);
            $startTime = strtotime('+1 day', $startTime);
        }

        return $arr;
    }

    // 设置秒杀商品状态
    public function setPointGoodsStatus($user_id, array $goods)
    {
        // TODO
        $goods              = array_values($goods);
        if($goods){
            foreach ($goods as &$goodsItem){
                $gid = $goodsItem['gid'] ?? null;
                list($s, $msg) = by::activityConfigModel()->checkPointsGoodsRule($user_id,$gid);
                $goodsItem['status'] = $msg;
                $goodsItem['status_tag'] = ActivityConfigEnum::POINTS_ACTIVITY_RESULT[$msg] ?? '不可兑换';
            }
        }
        return $goods;
    }

    // 设置秒杀商品状态
    public function setSecKillGoodsStatus(array $goods): array
    {
        // 商品状态映射
        $statusMap = [
                1 => '抢',
                2 => '未开始',
                3 => '今日售罄',
                4 => '已结束',
        ];

        $currentTime = START_TIME;
        $currentHour = (int) date('H', $currentTime);

        // 判断活动是否已开始
        if ($currentTime < strtotime("2025-05-18 10:00:00")) {
            foreach ($goods as &$arr) {
                foreach ($arr['arr'] as &$goodsItem) {
                    $goodsItem['status']     = 2;
                    $goodsItem['status_tag'] = $statusMap[2];
                }
            }
            return $goods;
        }

        // 判断活动是否已结束
        if ($currentTime > strtotime("2025-05-20 23:59:59")) {
            foreach ($goods as &$arr) {
                foreach ($arr['arr'] as &$goodsItem) {
                    $goodsItem['status']     = 4;
                    $goodsItem['status_tag'] = $statusMap[4];
                }
            }
            return $goods;
        }

        // 判断是否在00:00-10:00时间段
        if ($currentHour >= 0 && $currentHour < 10) {
            foreach ($goods as &$arr) {
                foreach ($arr['arr'] as &$goodsItem) {
                    $goodsItem['status']     = 2;
                    $goodsItem['status_tag'] = $statusMap[2];
                }
            }
            return $goods;
        }

        // 根据库存设置商品状态
        $stockModel = by::GoodsStockModel();
        foreach ($goods as &$arr) {
            foreach ($arr['arr'] as &$goodsItem) {
                $sku                     = $goodsItem['sku'] ?? 0;
                $stock                   = $stockModel->OptStock($sku);
                $status                  = $stock < 1 ? 3 : 1;
                $goodsItem['status']     = $status;
                $goodsItem['status_tag'] = $statusMap[$status];
            }
        }

        return $goods;
    }
//    public function setSecKillGoodsStatus($user_id, array $goods)
//    {
//        $statusArr = [
//                -1 => '即将开始',
//                1  => '立即秒杀',
//                2  => '即将开始',
//                3  => '已秒光',
//                4  => '已结束',
//                5  => '已秒杀',
//        ];
//        $mGmain    = by::Gmain();
//
//        foreach ($goods as &$goodsItem) {
//            $gid                 = $goodsItem['gid'] ?? null;
//            $sid                 = $goodsItem['sid'] ?? null;
//            $goodsItem['status'] = -1;
//            if (isset($gid)) {
//                $g_info = $mGmain->GetOneByGidSid($gid, $sid, false, false, 0);
//                if (isset($g_info) && !empty($g_info) && $g_info['status'] == 0) {
//                    //看是否购买过
//                    list($s, $m) = by::Ogoods()->CanLimitBuy($user_id, $gid, 1, 1);
//                    if (!$s) {
//                        $goodsItem['status'] = 5;
//                        $goodsItem['status_tag'] = $statusArr[$goodsItem['status']];
//                        continue;
//                    }
//                    $phone = by::Phone()->GetPhoneByUid($user_id);
//                    $userIds = by::Phone()->GetUidsByPhone($phone, YII_ENV_PROD ? 30 : 100);
//                    if(count($userIds) > 1){
//                        if (($key = array_search($user_id, $userIds)) !== false) {
//                            unset($userIds[$key]);
//                        }
//                        $userIds = array_filter(array_values($userIds));
//                        foreach ($userIds as $uid){
//                            list($s, $m) = by::Ogoods()->CanLimitBuy($uid, $gid, 1, 1);
//                            if (!$s) {
//                                $goodsItem['status'] = 5;
//                                $goodsItem['status_tag'] = $statusArr[$goodsItem['status']];
//                                break;
//                            }
//                        }
//                    }
//                    //获取时间
//                    $gini_info = $g_info['gini_info'] ?? [];
//                    $stime     = $gini_info['stime'] ?? 0;
//                    $etime     = $gini_info['etime'] ?? 0;
//                    $giInfos   = by::Gini()->GetList(['sku' => $goodsItem['sku']], 1, 100, false);
//                    $ginid    = $giInfos[0] ?? 0;
//                    $giInfo = by::Gini()->GetOneById($ginid);
//                    if (empty($stime) && empty($etime)) {
//                        $stime = $giInfo['stime'] ?? 0;
//                        $etime = $giInfo['etime'] ?? 0;
//                    }
//                    // 判断时间范围和库存
//                    if(empty($stime) && empty($etime)){
//                        $goodsItem['status'] = 2;
//                    }elseif ($stime > time()) {
//                        $goodsItem['status'] = 2;
//                    } elseif ($etime < time()) {
//                        $goodsItem['status'] = 4;
//                    } else {
//                        //库存判断
//                        $stock = by::GoodsStockModel()->OptStock($goodsItem['sku'] ?? 0);
//                        $goodsItem['status'] = $stock < 1 ? 3 : 1;
//                    }
//                }
//            }
//            $goodsItem['status_tag'] = $statusArr[$goodsItem['status']];
//        }
//
//        return $goods;
//    }


    public function queryGoodsInfo($goods)
    {
        if (empty($goods)){
            return null;
        }
        foreach ($goods as &$value) {
            $info            = by::Gmain()->GetOneByGidSid($value['gid'], $value['sid'], false, false, 1);
            $value['mprice'] = bcdiv($info['mprice'] ?? 0, 100);
            $value['price']  = bcdiv($info['price'] ?? 0, 100);
        }
        return $goods;
    }


    public function assembleMainData($userId, array $mains): array
    {
        $groupedData = [];

        // 判断是否需要计算积分
        if ($userId && strlen($userId) < 11) {
            // 获取积分相关数据
            $pointRate = by::memberCenterModel()->getPointCrash($userId);
            $mPoint    = by::point();
            $totalCoin = $mPoint->get($userId); // 用户总积分

            foreach ($mains as &$main) {
                $itemPrice = bcsub($main['price'],$main['coupon_price'],2);

                // 计算可用积分和对应金额
                $requiredCoin  = $mPoint->convert($itemPrice, 'POINT',$userId);
                $availableCoin = min($totalCoin, $requiredCoin);
                $finalCoin     = by::Ouser()->__canCoinByBenefit($availableCoin, $itemPrice, $pointRate, $userId);

                $main['can_point']   = $finalCoin;
                $main['point_price'] = $mPoint->convert($finalCoin,'RMB',$userId);
            }
        }

        // 计算每期价格并按分类分组
        foreach ($mains as &$main) {
            // 计算每期价格
            $price                     = bcsub($main['price'], $main['point_price'], 2);  // price(原价) - point_price(积分抵扣金额)
            $price                     = bcsub($price, $main['coupon_price'], 2);         // (price - point_price) - coupon_price(优惠券抵扣金额)
            $main['every_issue_price'] = bcdiv($price, $main['free_issue'], 2);           // (price - point_price - coupon_price) / free_issue(期数)

            // 按分类分组
            $groupedData[$main['category']][] = $main;
        }

        // 重新组织数据结构，移除不需要的字段
        return array_map(function ($category, $items) {
            return [
                    'name' => $category,
                    'arr'  => array_map(function ($item) {
                        unset($item['category']); // 移除不需要的字段
                        return $item;
                    }, (array) $items),
            ];
        }, array_keys($groupedData), $groupedData);
    }

    /**
     * 获取我的机器数据+配件信息
     * @param $userId
     * @param $category
     * @return array
     */
    public function checkMyMachine($userId, $category): array
    {
        try {
            // 1. 获取IOT设备信息
            list($status, $ret) = Device::factory()->run('list_v3', ['user_id' => $userId, 'lang' => 'zh', 'sharedStatus' => 1]);
            $deviceRecords = $ret['page']['records'] ?? [];

            $deviceItems = array_map(function ($deviceRecord) {
                // 截取 model 字段，区分是哪个产品类型
                $modelParts = explode('.', $deviceRecord['model']);
                $type = $modelParts[1] ?? '';
                if (!in_array($type,array_keys(by::Gtag()::TAG_IOT))){
                    return [];
                }
                $tid        = by::Gtag()::TAG_IOT[$modelParts[1] ?? ''] ?? '';
                return [
                        'sn'          => $deviceRecord['sn'],
                        'tid'         => $tid,
                        'active_time' => bcdiv($deviceRecord['devBindTime'] ?? 0, 1000),    // 转为毫秒
                        'name'        => $deviceRecord['deviceInfo']['displayName'],
                ];
            }, $deviceRecords);
            $deviceItems = array_filter($deviceItems);

            // 2. 获取用户产品注册列表
            $ids = by::productReg()->getRegList($userId);

            $productItems = array_map(function ($id) use ($userId) {
                $regInfo = by::productReg()->getOneById($userId, $id);
                if (!$regInfo) {
                    throw new Exception("找不到产品注册信息，ID: {$id}" . "| user_id: {$userId}");
                }
                $productInfo = by::product()->getOneById($regInfo['product_id'] ?? '');

                $tid = $productInfo['tid'] ?? '';
                if (!in_array($tid, by::Gtag()::TAG_IOT)) {
                    return [];
                }

                return [
                        'sn'          => $regInfo['sn'],
                        'tid'         => $tid,
                        'active_time' => $regInfo['create_time'],
                        'name'        => $productInfo['name'] ?? '',
                ];
            }, $ids);
            $productItems = array_filter($productItems);

            // 无数据直接返回
            if (empty($deviceItems) && empty($productItems)) {
                return [true, []];
            }

            // 3. 组装两个数据源数据，如果 sn 一样，则取 IOT 设备信息
            $deviceSnList       = array_column($deviceItems, 'sn');
            $additionalProducts = array_filter($productItems, function ($productItem) use ($deviceSnList) {
                return !in_array($productItem['sn'], $deviceSnList); // 排除 sn 已存在于 deviceItems 的项
            });

            $result = array_merge($deviceItems, $additionalProducts); // 合并结果

            // 4. 获取相对应数据的配件信息  考虑没有此数据的情况
            $deviceData = array_map(function ($item) use ($category) {
                $item['use_day']   = $item['active_time'] ? intval((time() - $item['active_time']) / 86400) : 0;
                $item['cate_data'] = $category[$item['tid']] ?? []; // 防止不存在 tid 的情况
                // 移除不需要的字段
                unset($item['active_time'],  $item['sn']);
                return $item;
            }, $result);
            return [true, $deviceData];
        } catch (Exception $e) {
            // 捕获异常并返回错误信息
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.checkMyMachine');
            return [
                    false, "获取我的机器数据失败",
            ];
        }
    }


    /**
     * @param $userId
     * @param array $points
     * @return array
     * @throws Exception
     * 组装积分数据 （积分商品+当前可用积分数）
     */
    public function assemblePointData($userId, array $points): array
    {
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            return [
                    'goods' => $points,
                    'point' => 0,
            ];
        }
        // 获取用户总积分
        $totalCoin = by::point()->get($userId); // 用户总积分
        return [
                'goods' => $points,
                'point' => $totalCoin,
        ];
    }


}
