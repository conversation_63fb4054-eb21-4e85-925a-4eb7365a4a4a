<?php

namespace app\modules\main\services\refund;

use app\modules\main\models\pay\MpPayModel;

/**
 * 退款服务
 */
class RefundService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 退款通知方式
    const REFUND_TYPE = [
        'WECHAT'    => 1,
        'WECHAT_H5' => 2,
        'ALIPAY'    => 4,
        'MP_PAY'    => 5, // 中台支付
    ];

    // 支付宝交易状态：关闭
    const ALIPAY_TRADE_CLOSED = 'TRADE_CLOSED';

    /**
     * 处理退款通知
     * @param $pay_type
     * @param $params
     * @return array|void
     */
    public function handleNotify($pay_type, $params)
    {
        switch ($pay_type) {
            case self::REFUND_TYPE['WECHAT']:
                // TODO 微信退款
                break;
            case self::REFUND_TYPE['WECHAT_H5']:
                // TODO 微信H5退款
                break;
            case self::REFUND_TYPE['ALIPAY']:
                // 支付宝退款
                return $this->alipayNotify($params);
            case self::REFUND_TYPE['MP_PAY']:
                // 中台退款
                return $this->mpNotify($params);
            default:
                return [true, ''];
        }
    }

    /**
     * 支付宝退款
     * @param array $params
     * @return array
     */
    private function alipayNotify(array $params): array
    {
        // 该笔会先收到 TRADE_SUCCESS 交易状态，然后超过 交易有效退款时间 该笔交易会再次收到 TRADE_FINISHED 状态，实际该笔交易只支付了一次，切勿认为该笔交易支付两次
        if (isset($params['trade_status']) && $params['trade_status'] == 'TRADE_FINISHED') {
            return [true, '交易完成：满一年不处理'];
        }

        // 交易状态：非交易成功、交易完成关闭不处理
        if (!in_array($params['trade_status'], ['TRADE_SUCCESS', 'TRADE_CLOSED'])) {
            return [false, '退款失败：交易状态异常'];
        }

        // 订单参数：用户、订单号、订单来源
        parse_str(urldecode($params['passback_params']), $order_params);
        if (!isset($order_params['user_id'], $order_params['order_no'], $order_params['order_type'])) {
            return [false, '退款失败：订单参数异常'];
        }

        // 退款费用：不为0
        if ($params['refund_fee'] == 0) {
            return [false, '退款失败：退款金额为0'];
        }

        // 通用处理
        try {
            $service = OrderFactory::createService(
                $order_params['order_type'],
                self::REFUND_TYPE['ALIPAY'],
                $order_params['user_id'],
                $order_params['order_no'],
                $params['out_biz_no'], // 退款单号
                bcmul($params['refund_fee'], 100), // 退款金额，单位：分
                strtotime($params['gmt_refund']), // 退款时间
                $params['trade_no'] // 交易单号
            );
            $service->refund();
            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 中台退款
     * @param array $params
     * @return array
     */
    private function mpNotify(array $params): array
    {
        // 订单参数：用户、订单号、订单来源
        parse_str(urldecode($params['passback']), $order_params);
        if (!MpPayModel::getInstance()->checkPassback($order_params)) {
            return [false, '退款失败：订单参数异常'];
        }

        // 退款费用：不为0
        if ($params['refund_amount'] == 0) {
            return [false, '退款失败：退款金额为0'];
        }

        // 通用处理
        try {
            $service = OrderFactory::createService(
                $order_params['order_type'],
                $order_params['pay_type'],
                $order_params['user_id'],
                $order_params['order_no'],
                $params['refund_id'], // 退款单号
                bcmul($params['refund_amount'], 100), // 退款金额，单位：分
                $params['refund_time'],
                $params['bill_id'] // 交易单号
            );
            $service->refund();
            return [true, ''];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }
}