<?php

namespace app\modules\main\services\refund;

abstract class Order
{
    // 用户ID
    protected $user_id;
    // 订单号
    protected $order_no;
    // 退款单号
    protected $refund_no;
    // 退款金额
    protected $refund_amount;
    // 退款时间
    protected $refund_time;

    // 交易单号
    protected $trade_no;
    // 退款类型：微信、微信H5、支付宝WAP
    protected $pay_type;

    // 订单类型
    const ORDER_TYPE = [
        'GOODS'    => 1, // 商品
        'DEPOSIT'  => 2, // 预售
    ];

    public function __construct($user_id, $order_no, $refund_no, $refund_amount, $refund_time, $trade_no, $pay_type)
    {
        $this->user_id = $user_id;
        $this->order_no = $order_no;
        $this->refund_no = $refund_no;
        $this->refund_amount = $refund_amount;
        $this->refund_time = $refund_time;
        $this->trade_no = $trade_no;
        $this->pay_type = $pay_type;
    }

    // 是否需要处理
    public abstract function isNeedHandle(): bool;

    public abstract function validate();

    public abstract function handle();

    public function refund()
    {
        if ($this->isNeedHandle()) {
            // 校验订单
            $this->validate();
            // 处理订单
            $this->handle();
        }
    }
}