<?php

namespace app\modules\main\services\refund;

use app\components\Crm;
use app\components\PointCenter;
use app\components\EventMsg;
use app\jobs\SyncPointGrowJob;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\back\services\GroupPurchaseService;
use app\modules\goods\models\OrefundMainModel;

/**
 * 普通商品退款
 */
class GoodsOrder extends Order
{
    // 退款单信息
    protected $refund;

    /**
     * 是否需要处理
     * @return bool
     * @throws \yii\db\Exception
     */
    public function isNeedHandle(): bool
    {
        // 退款单
        $this->refund = by::Orefund()->GetInfoByRefundNo($this->user_id, $this->refund_no);
        // 已退款成功：无需处理
        if (isset($this->refund['status']) && $this->refund['status'] == OrefundMainModel::STATUS['SUCCESS']) {
            return false;
        }
        return true;
    }

    /**
     * 校验订单
     */
    public function validate()
    {
        // 1. 退款单不存在
        if (empty($this->refund)) {
            throw new MyExceptionModel('退款单不存在');
        }

        // 2. 退款单状态不符
        if ($this->refund['status'] != OrefundMainModel::STATUS['P_PASS']) {
            throw new MyExceptionModel('退款单状态不符，无法退款');
        }
    }

    /**
     * 退款操作
     */
    public function handle()
    {
        // 开启事务
        $trans = by::dbMaster()->beginTransaction();
        try {

            // 1.更新库存
            $this->updateStock($this->user_id, $this->order_no, $this->refund_no);

            // 2.退还优惠券
            $this->returnCard($this->user_id, $this->order_no, $this->refund_no, $trans);

            // 3.退还消费券
            $this->returnCard($this->user_id, $this->order_no, $this->refund_no, $trans, 'consume_id');

            // 4.更新退款单状态：退款成功
            $this->updateStatus($this->user_id, $this->refund_no, OrefundMainModel::STATUS['SUCCESS'], $this->refund_amount, $this->refund_time);

            // 5.提交事务
            $trans->commit();

            // 6.同步crm\oms、推送消息等操作
            $this->syncOperations($this->user_id, $this->order_no, $this->refund_no, $this->refund['m_type']);

            // 团购订单退款消息推送
            GroupPurchaseService::getInstance()->sendRefundMessage($this->order_no);
        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录日志
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.goods.refund');
            throw new MyExceptionModel('退款失败');
        }
    }

    /**
     * 更新库存
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @return void
     * @throws MyExceptionModel
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function updateStock($user_id, $order_no, $refund_no)
    {
        // 订单商品
        $orderGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
        // 退款商品
        $refundGoods = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        // 退款商品id
        $refundGoodsIds = array_column($refundGoods, 'og_id');
        // 回退库存
        foreach ($orderGoods as $val) {
            // 1.更新退款商品的库存
            if (in_array($val['id'], $refundGoodsIds)) {
                //判断商品类型
                $goodsType = $val['goods_type'] ?? '';
                $goodsSource = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                    ? by::GoodsStockModel()::SOURCE['WARES']
                    : by::GoodsStockModel()::SOURCE['MAIN'];
                by::GoodsStockModel()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'SALE', false, $goodsSource);
            }

            // 2.更新自定义价格的商品库存
            if ($giniId = $val['gini_id'] ?? 0) {
                list($status, $msg) = by::Gini()->UpdateStock($giniId, $val['num'], 'SALE', false);
                if (!$status) {
                    CUtil::debug('更新自定义价格的商品库存异常：' . $msg, 'err.goods.refund');
                    throw new MyExceptionModel($msg);
                }
            }
        }
    }

    /**
     * 退还优惠券
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param $trans
     * @return void
     * @throws MyExceptionModel
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function returnCard($user_id, $order_no, $refund_no, $trans, $field = 'coupon_id', $limitVoucher = false)
    {
        // 1.无优惠券可退
        $order = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        if (empty($order[$field])) {
            return;
        }


        // 订单商品
        $orderGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
        // 退款商品
        $refundGoods = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        // 已退款商品数量
        $hasRefundGoodsNum = by::OrefundMain()->getCountByOrderNo($user_id, $order_no);

        // 2.单笔订单，存在尚未退的商品，不可退优惠券
        if (count($orderGoods) != (count($refundGoods) + $hasRefundGoodsNum)) {
            return;
        }

        // 3.退还优惠券
        list($status, $msg) = by::userCard()->UnLockCard($user_id, $order[$field], $trans, $limitVoucher);
        if (!$status) {
            CUtil::debug('退还优惠券异常：' . $msg, 'err.goods.refund');
            throw new MyExceptionModel($msg);
        }
    }

    /**
     * 更新退款单状态
     * @param $user_id
     * @param $refund_no
     * @param $status
     * @param $refund_amount
     * @param $refund_time
     * @return void
     * @throws MyExceptionModel
     * @throws \yii\db\Exception
     */
    private function updateStatus($user_id, $refund_no, $status, $refund_amount, $refund_time)
    {
        $save = [
            'rtime' => $refund_time,
            'price' => $refund_amount,
        ];
        list($status, $msg) = by::OrefundMain()->SyncInfo($user_id, $refund_no, $status, $save);
        if (!$status) {
            throw new MyExceptionModel($msg);
        }
    }

    /**
     * 同步crm\oms、推送消息等操作
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param $m_type
     * @return void
     * @throws \yii\db\Exception
     */
    private function syncOperations($user_id, $order_no, $refund_no, $m_type)
    {
        // 退款提醒
        EventMsg::factory()->run('orderMsgSend', ['event' => 'refund', 'order_no' => $order_no, 'refund_no' => $refund_no]);

        // 订单同步crm
        // Crm::factory()->push($user_id, 'order', ['user_id' => $user_id, 'order_no' => $order_no]);
        // Crm::factory()->push($user_id, 'orderLine', ['user_id' => $user_id, 'order_no' => $order_no]);

        // 退款crm
        // Crm::factory()->push($user_id, 'refund', ['user_id' => $user_id, 'refund_no' => $refund_no]);
        // Crm::factory()->push($user_id, 'refundLine', ['user_id' => $user_id, 'refund_no' => $refund_no]);
        // 订单同步IOT
        PointCenter::factory()->refundPush($user_id, $refund_no);

        // oms退款推送
        by::OrefundMain()->refundPushOms($m_type, $user_id, $refund_no, $order_no);
    }

}