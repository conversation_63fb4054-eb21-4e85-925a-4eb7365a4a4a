<?php

namespace app\modules\main\services\refund;

use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\goods\models\OrefundDepositModel;

/**
 * 预售商品退款
 */
class DepositOrder extends Order
{
    // 退款单信息
    protected $refund;
    
    /**
     * 是否需要处理
     * @return bool
     * @throws \yii\db\Exception
     */
    public function isNeedHandle(): bool
    {
        // 退款单
        $this->refund = by::OrefundDeposit()->GetInfoByRefundNo($this->user_id, $this->refund_no);
        // 已退款成功：无需处理
        if (isset($this->refund['status']) && $this->refund['status'] == OrefundDepositModel::STATUS['success']) {
            return false;
        }
        return true;
    }

    /**
     * 校验订单
     */
    public function validate()
    {
        // 1. 退款单不存在
        if (empty($this->refund)) {
            throw new MyExceptionModel('退款单不存在');
        }

        // 2. 退款单状态不符
        if ($this->refund['status'] != OrefundDepositModel::STATUS['pass']) {
            throw new MyExceptionModel('退款单状态不符，无法退款');
        }
    }

    /**
     * 退款操作
     */
    public function handle()
    {
        // 开启事务
        $trans = by::dbMaster()->beginTransaction();
        try {
            // 1.更新退款单状态：退款成功
            $this->updateStatus($this->user_id, $this->refund_no, OrefundDepositModel::STATUS['success'], $this->refund_amount, $this->refund_time);

            // 2.更新库存
            $this->updateStock($this->user_id, $this->order_no);

            // 3.提交事务
            $trans->commit();
        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录日志
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.deposit.refund');
            throw new MyExceptionModel($e->getMessage());
        }
    }

    /**
     * 更新退款单状态
     * @param $user_id
     * @param $refund_no
     * @param $status
     * @param $refund_amount
     * @param $refund_time
     * @return void
     * @throws MyExceptionModel
     * @throws \yii\db\Exception
     */
    private function updateStatus($user_id, $refund_no, $status, $refund_amount, $refund_time)
    {
        //更改退款申请表为退款成功
        $save = [
            'rtime' => $refund_time,
            'price' => $refund_amount,
        ];
        list($status, $msg) = by::OrefundDepositMain()->SyncInfo($user_id, $refund_no, $status, $save);
        if (!$status) {
            CUtil::debug('更新退款单状态异常：' . $msg, 'err.deposit.refund');
            throw new MyExceptionModel($msg);
        }
    }

    /**
     * 更新库存
     * @param $user_id
     * @param $order_no
     * @return void
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function updateStock($user_id, $order_no)
    {
        //回退销量
        $deposit_order = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
        if (isset($deposit_order['gid']) && $deposit_order['gid']) {
            by::Gprestock()->UpdateStock($deposit_order['gid'], $deposit_order['sid'] ?? 0, $deposit_order['num'] ?? 1, 'SALE', false);
        }
    }
}