<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\models\by;

/**
 * 商品的商品参数服务
 */
class GparamGoodsService
{
    const EXPIRE_TIME = 1; // 设置缓存的过期时间 1s

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 根据 sku 获取单个商品的参数
     * @param string $sku
     * @return array
     */
    public function GetParamsInfoBySku(string $sku): array
    {
        // 返回结果
        $data = [
            'sku'   => $sku,
            'items' => []
        ];

        // 判空
        if (empty($sku)) {
            return $data;
        }

        // 缓存：从 redis 获取数据，（缓存的目的：1时防止用户频繁访问，2是商品参数非强实时性业务，所以可短时间缓存）
        $redis = by::redis();
        $r_key = AppCRedisKeys::getOneGoodsParamInfo($sku);
        $cachedData = $redis->get($r_key);
        if ($cachedData) {
            return json_decode($cachedData, true);
        }

        // 获取参数值
        $items = $this->getParamsBySkus([$sku], [1]);

        // 获取参数名
        $paramIds = array_column($items, 'param_id');
        $paramName = $this->GetParamNameByIds($paramIds);

        // 处理返回数据的格式
        foreach ($items as $item) {
            $param = [
                'categroup_id'   => $item['categroup_id'],
                'categroup_name' => $item['categroup_name'],
                'param_id'       => $item['param_id'],
                'param_name'     => $paramName[$item['param_id']] ?? '',
                'param_info'     => $item['param_info']
            ];
            $data['items'][] = $param;
        }

        // 缓存：向 redis 插入数据
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return $data;
    }

    /**
     * 根据 skus 获取多个商品的参数（存了 redis 缓存）
     * 按参数类型的并集，展示参数集。
     * @param array $skus
     * @param bool $isGroup
     * @param int $platformId
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function GetParamsListBySkus(array $skus, bool $isGroup, int $platformId): array
    {
        // 缓存的前缀
        $r_key = AppCRedisKeys::getParamsListBySkus($skus, $isGroup);
        // 缓存：从 redis 获取数据
        $redis = by::redis();
        $cachedData = $redis->get($r_key);

        if ($cachedData) {
            return [true, json_decode($cachedData, true)];
        }

        // 校验 skus 属于同一个商品分类
        $isSameCate = $this->__isSameCate($skus);
        if (!$isSameCate) {
            return [false, '商品分类不同'];
        }

        // 校验 商品有效性
        $goods = $this->__getGoodsBySkus($skus);

        // 校验 商品上架状态
        $status = array_sum(array_column($goods, 'status'));
        if (count($goods) != count($skus) || $status > 0) {
            return [false, '商品已下架'];
        }

        // 校验 商品平台
        $gids = array_column($goods, 'gid');
        $status = GoodsPlatformService::getInstance()->checkPlatform($gids, $platformId);
        if (!$status) {
            return [false, '当前平台商品已下架'];
        }

        // 获取 skus 的参数信息
        $items = $this->getParamsBySkus($skus, [0, 1]);

        // 如果对比的 skus 没有设置参数，则返回空
        $compareSkus = array_column($items, 'sku');
        $compareSkus = array_unique($compareSkus);
        if (array_diff($skus, $compareSkus)) {
            return [false, '商品未配置参数'];
        }

        // 获取商品参数的并集，且按参数的顺序排序
        $params = $this->__getParamKinds($items);

        // 格式化参数信息结果集
        $formatItems = $this->__formatParamItems($items);

        if (!$isGroup) { // 正常展示
            $data = $this->__formatNormalParamItems($formatItems, $goods, $params);
        } else { // 分组展示
            $data = $this->__formatGroupParamItems($formatItems, $goods, $params);
        }

        // 缓存：向 redis 插入数据
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));

        return [true, $data];
    }


    /**
     * 根据 pids 获取分类列表
     * @param array $pids
     * @param int $platformId
     * @return array
     */
    public function GetCateListByPids(array $pids, int $platformId): array
    {
        // 缓存的前缀
        $r_key = AppCRedisKeys::getCateListByPids($pids);
        // 缓存：从 redis 获取数据
        $redis = by::redis();
        $cachedData = $redis->get($r_key);

        if ($cachedData) {
            return json_decode($cachedData, true);
        }

        // 判空
        if (empty($pids)) {
            return [];
        }

        // 获取二级分类
        $cateItems = by::cateModel()->getCateByPids($pids);
        if (empty($cateItems)) {
            return [];
        }

        // 根据二级分类，获取商品
        $cateIds = array_column($cateItems, 'id');
        $goodsItems = $this->__getGoodsBySecondCateIds($cateIds);
        if (empty($goodsItems)) {
            return [];
        }

        // 校验 商品平台
        $goodsItems = array_column($goodsItems, null, 'gid');
        $effectiveGids = GoodsPlatformService::getInstance()->getEffectiveGids(array_keys($goodsItems), $platformId);

        // 获取有效的商品
        $effectiveGoodsItems = [];
        foreach ($effectiveGids as $gid) {
            if (isset($goodsItems[$gid])) {
                $effectiveGoodsItems[] = $goodsItems[$gid];
            }
        }

        // 获取配置了参数的商品
        $skus = array_column($effectiveGoodsItems, 'sku');
        $goodsParamItems = $this->getParamsBySkus($skus, [0, 1]);

        // 处理数据
        $cateGoods = [];
        foreach ($goodsParamItems as $item) {
            $cateGoods[$item['cate_id']][$item['sku']] = 1;
        }

        // 有效产品数量大于等于2
        $data = [];
        foreach ($cateItems as $item) {
            if (isset($cateGoods[$item['id']]) && count($cateGoods[$item['id']]) >= 2) {
                $data[] = [
                    'c_id' => $item['id'],
                    'name' => sprintf("%s对比", $item['name']),
                    'icon' => $item['icon'],
                ];
            }
        }

        // 缓存：向 redis 插入数据
        $redis->setex($r_key, self::EXPIRE_TIME, json_encode($data));
        return $data;
    }

    /**
     * 根据参数 id 获取参数名
     * @param array $ids
     * @return array
     */
    public function GetParamNameByIds(array $ids): array
    {
        // 要获取的值
        $columns = ['id', 'name'];
        $params = by::GparamModel()->getParamByIds($ids, $columns);
        return array_column($params, 'name', 'id');
    }


    /**
     * 根据sku获取参数，（缓存查询）
     * @param array $skus
     * @param array|int[] $isShow
     * @return array
     */
    public static function getParamsBySkus(array $skus, array $isShow = [0, 1]): array
    {
        // 1、获取 t_gparam_goods 的列表
        $goodsParamItems = by::GparamGoodsModel()->getGoodsParamList(['sku' => $skus, 'is_show' => $isShow], ['id', 'sku', 'categroup_detail_id', 'param_info', 'sort', 'is_show']);
        $goodsParamItems = array_column($goodsParamItems, null, 'id');
        // 获取 categroup_detail_ids
        $categroupDetailIds = array_column($goodsParamItems, 'categroup_detail_id');

        // 2、获取 t_gparam_categroup_detail 的列表
        $cateGroupDetailItems = by::GparamCateGroupDetailModel()->getCateGroupDetailList(['id' => $categroupDetailIds], ['id', 'categroup_id', 'gparam_id']);
        $cateGroupDetailItems = array_column($cateGroupDetailItems, null, 'id');
        // 获取 categroup_ids
        $cateGroupIds = array_column($cateGroupDetailItems, 'categroup_id');

        // 3、获取 t_gparam_categroup 的列表
        $cateGroupItems = by::GparamCateGroupModel()->getCateGroupList(['id' => $cateGroupIds], ['id', 'pid', 'cate_id', 'name']);
        $cateGroupItems = array_column($cateGroupItems, null, 'id');

        // 拼装数据
        $cateGroupData = [];
        foreach ($cateGroupDetailItems as $key => $item) {
            // 条件：在参数分组中存在。（数据没有被删除）
            $categroupId = $item['categroup_id'];
            if (!isset($cateGroupItems[$categroupId])) {
                continue;
            }
            $cateGroup = $cateGroupItems[$categroupId];
            // 赋值
            $cateGroupData[$key] = [
                'id'           => $item['id'],
                'categroup_id' => $item['categroup_id'],
                'param_id'     => $item['gparam_id'],
                'cate_id'      => $cateGroup['cate_id'],
                'name'         => $cateGroup['name'],
            ];
        }

        $data = [];
        foreach ($goodsParamItems as $key => $item) {
            // 条件：在参数分组详情中存在。（数据没有被删除）
            $cateGroupDetailId = $item['categroup_detail_id'];
            if (!isset($cateGroupData[$cateGroupDetailId])) {
                continue;
            }
            $cateGroupDetail = $cateGroupData[$cateGroupDetailId];
            $data[$key] = [
                'id'                  => $item['id'],
                'sku'                 => $item['sku'],
                'categroup_detail_id' => $item['categroup_detail_id'],
                'param_info'          => $item['param_info'],
                'sort'                => $item['sort'],
                'is_show'             => $item['is_show'],
                'categroup_id'        => $cateGroupDetail['categroup_id'],
                'param_id'            => $cateGroupDetail['param_id'],
                'cate_id'             => $cateGroupDetail['cate_id'],
                'categroup_name'      => $cateGroupDetail['name'],
            ];
        }
        return $data;
    }

    /**
     * 是否为相同的（二级）分类
     * @param array $skus
     * @return bool
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    private function __isSameCate(array $skus): bool
    {
        // 判空
        if (empty($skus)) {
            return false;
        }

        // 获取商品类目
        $items = [];
        foreach ($skus as $sku) {
            $item = by::gCateModel()->getCateInfoBySku($sku);
            if (!empty($item)) {
                $items[] = $item;
            }
        }
        // 返回个数错误
        if (count($items) != count($skus)) {
            return false;
        }

        // 获取 cate_ids
        $cateIds = array_column($items, 'c_id');

        // 获取商品类目
        $cateItems = [];
        foreach ($cateIds as $id) {
            $item = by::cateModel()->getOneInfoById($id);
            if (!empty($item)) {
                $cateItems[] = $item;
            }
        }
        // 返回个数错误
        if (count($cateItems) != count($cateIds)) {
            return false;
        }

        // 判断 pid 和 level 是否相等
        for ($i = 1; $i < count($cateItems); $i++) {
            if (($cateItems[0]['pid'] != $cateItems[$i]['pid']) || ($cateItems[0]['level'] != $cateItems[$i]['level'])) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取参数种类
     * @param array $items
     * @return array
     */
    private function __getParamKinds(array $items): array
    {
        // 获取 参数名
        $paramIds = array_column($items, 'param_id');
        $paramName = $this->GetParamNameByIds($paramIds);

        // redis 获取参数的 sort
        $redis = by::redis('core');

        // 格式化 参数集
        $params = [];
        foreach ($items as $item) {
            $cateId = $item['cate_id'];
            $categroupId = $item['categroup_id'];
            $paramId = $item['param_id'];

            // 获取 sort 值
            $key = AppCRedisKeys::getGoodsParamSort($cateId, $categroupId);
            $sort = $redis->zscore($key, $paramId);
            if (!$sort) {
                $sort = 0;
            }

            if (!isset($params[$categroupId])) {
                $params[$categroupId]['categroup_id'] = $categroupId;
                $params[$categroupId]['categroup_name'] = $item['categroup_name'];
            }
            $params[$categroupId]['params'][$paramId] = [
                'param_id'   => $item['param_id'],
                'param_name' => $paramName[$item['param_id']] ?? '',
                'sort'       => $sort,
            ];
        }

        // 排序
        sort($params);
        $params = array_column($params, null, 'categroup_id');

        $data = [];
        foreach ($params as $key => $val) {
            array_multisort(array_column($val['params'], 'sort'), SORT_ASC, $val['params']);
            $data[$key] = $val;
        }
        return $data;
    }

    /**
     *  格式化参数信息结果集
     * @param array $items
     * @return array
     */
    private function __formatParamItems(array $items): array
    {
        $data = [];
        foreach ($items as $item) {
            $sku = $item['sku'];
            $categroupId = $item['categroup_id'];
            $paramId = $item['param_id'];
            $data[$sku][$categroupId][$paramId] = [
                'param_info' => $item['param_info']
            ];
        }
        return $data;
    }

    /**
     * 根据 skus 获取商品信息
     * @param array $skus
     * @return array
     * @throws \yii\db\Exception
     */
    public function __getGoodsBySkus(array $skus): array
    {
        // 商品基本信息
        $items = [];
        foreach ($skus as $sku) {
            $item = by::Gmain()->GetOneBySku($sku);
            if (!empty($item)) {
                $items[] = [
                    'gid'    => $item['id'],
                    'sku'    => $item['sku'],
                    'name'   => $item['name'],
                    'status' => $item['status'],
                ];
            }
        }
        $gids = array_column($items, 'gid');

        // 商品详情信息
        $gTypeItems = [];
        foreach ($gids as $gid) {
            $item = by::Gtype0()->GetOneByGid($gid, 0);
            if (!empty($item)) {
                $gTypeItems[] = [
                    'gid'          => $item['gid'],
                    'price'        => $item['price'],
                    'image'        => $item['cover_image'],
                    'pc_image'     => $item['pc_cover_image'] ?? '',
                    'market_image' => empty($item['market_image'] ?? '') ? $item['cover_image'] : $item['market_image'] ?? '',
                ];
            }
        }
        $gTypeItems = array_column($gTypeItems, null, 'gid');

        // 拼装数据
        $data = [];
        foreach ($items as $item) {
            $gid = $item['gid'];
            if (!isset($gTypeItems[$gid])) {
                continue;
            }
            $gTypeItem = $gTypeItems[$gid];
            $data[$item['sku']] = [
                'gid'          => $item['gid'],
                'sku'          => $item['sku'],
                'name'         => $item['name'],
                'status'       => $item['status'],
                'price'        => $gTypeItem['price'],
                'image'        => $gTypeItem['image'],
                'pc_image'     => $gTypeItem['pc_image'],
                'market_image' => $gTypeItem['market_image'],
            ];
        }
        return $data;
    }

    /**
     * 根据商品的二级分类，获取商品（没删除、没下架）
     * @param $cateIds
     * @return array
     */
    private function __getGoodsBySecondCateIds($cateIds): array
    {
        // 三级分类
        $cateItems = by::cateModel()->getCateByPids($cateIds);
        if (empty($cateItems)) {
            return [];
        }
        // 获取商品
        $cateIds = array_column($cateItems, 'id');
        return by::Gmain()->getDataByCateIds($cateIds);
    }

    /**
     * 分组输出
     * @param array $items
     * @param array $params
     * @return array
     */
    private function __formatGroupParamItems(array $items, array $goods, array $params): array
    {
        $data = [];
        foreach ($items as $sku => $item) {
            // 商品信息
            $itemData = [
                'sku'          => $sku,
                'gid'          => $goods[$sku]['gid'] ?? '',
                'name'         => $goods[$sku]['name'] ?? '',
                'price'        => $goods[$sku]['price'] ?? '',
                'image'        => $goods[$sku]['image'] ?? '',
                'pc_image'     => $goods[$sku]['pc_image'] ?? '',
                'market_image' => $goods[$sku]['market_image'] ?? '',
            ];
            $itemData['params'] = [];
            // 参数信息
            foreach ($params as $param) {
                $tmp = [
                    'categroup_id'   => $param['categroup_id'],
                    'categroup_name' => $param['categroup_name'],
                ];
                foreach ($param['params'] as $value) {
                    $tmp['items'][] = [
                        'param_id'   => $value['param_id'],
                        'param_name' => $value['param_name'],
                        'param_info' => $item[$param['categroup_id']][$value['param_id']]['param_info'] ?? '',
                    ];
                }
                $itemData['params'][] = $tmp;
            }
            $data[] = $itemData;
        }
        return $data;
    }

    /**
     * 正常输出
     * @param array $items
     * @param array $params
     * @return array
     */
    private function __formatNormalParamItems(array $items, array $goods, array $params): array
    {
        $data = [];
        foreach ($items as $sku => $item) {
            // 商品信息
            $itemData = [
                'sku'          => $sku,
                'gid'          => $goods[$sku]['gid'] ?? '',
                'name'         => $goods[$sku]['name'] ?? '',
                'price'        => $goods[$sku]['price'] ?? '',
                'image'        => $goods[$sku]['image'] ?? '',
                'pc_image'     => $goods[$sku]['pc_image'] ?? '',
                'market_image' => $goods[$sku]['market_image'] ?? '',
            ];
            $itemData['params'] = [];
            // 参数信息
            foreach ($params as $param) {
                foreach ($param['params'] as $value) {
                    $tmp = [
                        'categroup_id'   => $param['categroup_id'],
                        'categroup_name' => $param['categroup_name'],
                        'param_id'       => $value['param_id'],
                        'param_name'     => $value['param_name'],
                        'param_info'     => $item[$param['categroup_id']][$value['param_id']]['param_info'] ?? '',
                    ];
                    $itemData['params'][] = $tmp;
                }
            }
            $data[] = $itemData;
        }
        return $data;
    }
}