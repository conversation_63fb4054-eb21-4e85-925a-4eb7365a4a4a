<?php

namespace app\modules\main\services;

use app\components\collection\Collection;
use app\components\MessagePush;
use app\exceptions\ActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\GtagModel;
use app\modules\main\models\UserPopupModel;
use app\modules\wares\services\goods\GoodsMainService;
use yii\db\Exception;
use yii\db\Expression;


/**
 * 拼团服务
 */
class GroupPurchaseService
{


    private static $_instance = NULL;

    private function __construct()
    {
    }

    //折扣率
    public function getDiscountRate($gid): float
    {
        // 默认值
        $rate = 0.6;

        $dic     = CUtil::dictData('group_purchase');
        $subdata = array_column($dic, 'value', 'label');
        if (isset($subdata['activity_id'])) {
            $activity_id = $subdata['activity_id'];
            return byNew::GroupPurchaseActivityGoodsModel()->getInfoByGIdAndAid($gid, $activity_id)['rate']?? $rate;
        } else {
            return $rate;
        }
    }

    public function getDiscountPrice($gid,$price){
        $rate = $this->getDiscountRate($gid);
        return bcmul($price, $rate, 2);
    }


    /**
     * @param $activity
     * @param $goods_name
     * @param $name
     * @param array $filterGids
     * @param array $getGids
     * @return array
     */
    public function getGoodsByGids($activity, $goods_name, $name, array $filterGids = [], array $getGids = [], $filter_is_del = 0, $tag_id = 0): array
    {
        $activityId = $activity['id'] ?? 0;
        // 1. 查询商品信息

        if (empty($goods_name)) {
            list($goods, $pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList($activityId, 'page', $goods_name, $filterGids, $getGids, $filter_is_del, $tag_id);
        }else{
            list($goods, $pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList($activityId, 'all', $goods_name, $filterGids, $getGids, $filter_is_del, $tag_id);
        }


        // 2. 查询团长已发起的拼团
//        $groups = $GroupPurchaseModel->getGroupPurchaseByUserId($userId, $activityId,$gids);

        // 2.查询团长是否有带支付订单
//        $unPayOrderNo = $GroupPurchaseModel->getLeaderUnPayOrder($userId, array_column($groups, 'id'));

        // 4. 组装数据
//        $groupList    = Collection::make($groups)->sortByDesc('id')->values();
        $goodsList = Collection::make($goods);
        $goodsList = $goodsList->map(function ($goods) use ($activity) {
            $stock                  = array_sum(array_column(GoodsMainService::getInstance()->GetMainStockByGid($goods['gid']), 'stock'));
            $buttonStatus           = $this->getButtonStatus($activity, '', false, 0, $stock, $goods['status'], $group['total_members'] ?? 0, $goods['max_members']);
            $goods['button_status'] = $buttonStatus;
//            unset($goods['min_members'], $goods['max_members'], $goods['min_group_qty'], $goods['purchase_limit']);
            return $goods;
        });

//        $unPayOrderNo = Collection::make($unPayOrderNo);

        // 已发起的拼团
//       $groupList = $groupList->map(function ($group) use ($unPayOrderNo, $goodsList) {
//           $goods = $goodsList->where('gid', $group['gid'])->first();
//           return $this->groupPurchaseFormat($group, $unPayOrderNo, $goods);
//       });


        return [
                'title' => $name,
                //                'group' => $groupList->toArray(),
                'goods' => $goodsList,
                'total_page' => $pages
        ];
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 分享落地页
    const SHARE_PATH = YII_ENV_PROD ? 'pages/goodsDetail/goodsDetail' : 'pages/goodsDetail/goodsDetail';

    // 团购资格验证错误码
    const QUALIFICATION_ERROR_CODES = [
        'NOT_QUALIFIED' => 120113,
        'SUCCESS' => 1,
    ];
    
    // 团购资格验证消息
    const QUALIFICATION_MESSAGES = [
        'NOT_QUALIFIED' => '抱歉，您未注册追觅产品暂时不能发起团购。如您已经购买使用追觅产品可到"我的--注册有礼"添加产品后再发起团购；或扫描二维码添加心享官给您发送参与拼团邀请链接',
        'SUCCESS' => '恭喜您成为团长！',
    ];

    // 校验资格
    public function verifyQualification($userId): array
    {
            return [true, self::QUALIFICATION_MESSAGES['SUCCESS'], self::QUALIFICATION_ERROR_CODES['SUCCESS']];

//        // 1. 校验用户是否注册过产品
//        if ($this->hasRegisteredProducts($userId)) {
//            return [true, self::QUALIFICATION_MESSAGES['SUCCESS'], self::QUALIFICATION_ERROR_CODES['SUCCESS']];
//        }
//
//        // 2. 校验用户是否有购买记录
//        if ($this->hasPurchaseRecords($userId)) {
//            return [true, self::QUALIFICATION_MESSAGES['SUCCESS'], self::QUALIFICATION_ERROR_CODES['SUCCESS']];
//        }
//
//        // 用户既没有注册产品，也没有购买记录
//        return [
//                false,
//                self::QUALIFICATION_MESSAGES['NOT_QUALIFIED'],
//                self::QUALIFICATION_ERROR_CODES['NOT_QUALIFIED']
//        ];
    }

    /**
     * 检查用户是否注册过产品
     * @param int $userId
     * @return bool
     */
    private function hasRegisteredProducts(int $userId): bool
    {
        $registeredIds = by::productReg()->getRegList($userId);
        return !empty($registeredIds);
    }

    /**
     * 检查用户是否有购买记录
     * @param int $userId
     * @return bool
     */
    private function hasPurchaseRecords(int $userId): bool
    {
        $page = 1;
        $pageSize = 20;

        do {
            try {
                $orderList = by::Ouser()->GetList($userId, 500, '', '', $page, $pageSize);

                if (empty($orderList)) {
                    break;
                }

                // 获取订单号列表
                $orderNos = array_column($orderList, 'order_no');
                $orderConfigs = by::Ocfg()->getListByOrderNos($orderNos);

                // 检查是否有包含商品的订单
                foreach ($orderConfigs as $config) {
                    $tids = $config['tids'] ?? [];
                    if (!empty($tids) && !empty(array_intersect(GtagModel::MAIN_TAG, $tids))) {
                        return true;
                    }
                }

                $page++;
            } catch (\Exception $e) {
                // 如果GetList报错，直接返回false
                return false;
            }
        } while (true);

        return false;
    }

    // 获取弹窗数据
    public function getPopupData($userId): array
    {
        // 获取弹窗数据
        $popupData = byNew::UserPopupModel()->GetPopupData([
                'user_id'    => $userId,
                'popup_type' => UserPopupModel::POPUP_TYPE['GROUP_PURCHASES']
        ]);

        // 简化判断逻辑
        return [
                'value' => (!empty($popupData) && $popupData['is_displayed'] == UserPopupModel::IS_DISPLAYED['YES']) ? 1 : 0
        ];
    }


    // 保存弹窗记录
    public function savePopup($userId, $popupType): bool
    {
        // 参数检查
        if (empty($userId) || empty($popupType)) {
            return false;
        }

        // 调用模型层更新弹窗
        return byNew::UserPopupModel()->savePopup($userId, $popupType);
    }


    /**
     * 发起拼团（创建团购活动）(主要)
     *
     * 本方法用于处理用户发起拼团的请求，包括以下主要步骤：
     * 1. 验证用户参与团购的资格
     * 2. 验证活动的有效性
     * 3. 验证商品是否在活动范围内
     * 4. 检查用户是否已发起过相同拼团
     * 5. 创建新的拼团记录
     *
     * @param array $params 请求参数数组
     *      - activity_id: int 活动ID
     *      - gid: int 商品ID
     * @param int $userId 用户ID
     *
     * @return array 返回数组包含两个元素：
     *      - bool 表示操作是否成功
     *      - array 包含消息和拼团ID的数组
     *          - message: string 操作结果消息
     *          - id: int 成功时返回拼团ID，失败时返回0
     *
     * @throws ActivityException 当验证失败时抛出，包含以下错误码：
     *      - 120003: 活动不存在
     *      - 120004: 活动未开始或已结束
     *      - 120005: 商品不在活动范围内
     *      - 120006: 用户已发起过拼团
     *      - 120007: 保存拼团信息失败
     */
    public function initiateGroupPurchase($params, $userId): array
    {
        $activityId  = $params['activity_id'] ?? 0;
        $gid         = $params['gid'] ?? 0;
        $currentTime = time();

        //        $groupPurchaseModel = byNew::GroupPurchaseModel();

        try {
            // 1. 校验资格
            list($status, $msg, $code) = $this->verifyQualification($userId);
            if (!$status) {
                throw new ActivityException($msg, $code);
            }


            // 2. 获取活动详情，验证活动是否存在及有效性
            $activityData = byNew::GroupPurchaseActivityModel()->getActivityGoodsDetail($activityId,$gid);
            if (empty($activityData) || !$activityData['status']) {
                throw new ActivityException("活动不存在", 120003);
            }

            // 活动商品已被删除
            $goods = $activityData['data']['goods']??[];
            if (empty($goods)||!in_array($gid, array_column($goods, 'gid'))) {
                throw new ActivityException("活动商品不存在", 120003);
            }

            if ($activityData['data']['start_time'] > $currentTime || $activityData['data']['end_time'] < $currentTime) {
                throw new ActivityException("活动未开始或已结束", 120004);
            }

            // 3. 校验商品是否在拼团商品范围内
            $activityGoodsIds = array_column($activityData['data']['goods'], 'gid');
            if (!in_array($gid, $activityGoodsIds)) {
                throw new ActivityException("商品不在活动范围内", 120005);
            }

            // 4. 查询用户是否已经发起过拼团
            //            $groupPurchase = $groupPurchaseModel->getLastGroupPurchaseRecord([
            //                    'activity_id' => $activityId,
            //                    'gid'         => $gid,
            //                    'user_id'     => $userId,
            //            ]);
            //
            //            if (!empty($groupPurchase) && $groupPurchase['status'] == $groupPurchaseModel::STATUS_IN_PROGRESS) {
            //                throw new ActivityException("您已经发起过拼团", 120006);
            //            }

            // 5. 创建拼团记录
            $groupPurchaseData = $this->createGroupLeader($activityId, $gid, $userId);
            if (!$groupPurchaseData['status']) {
                throw new ActivityException("保存拼团信息失败", 120007);
            }
            return [
                    true,
                    ['message' => '操作成功', 'id' => $groupPurchaseData['data']['id']]
            ];

        } catch (ActivityException $activityException) {
            // 捕获自定义异常并返回自定义错误信息
            return [
                    false,
                    ['message' => $activityException->getMessage(), 'id' => 0]
            ];
        } catch (\Exception $e) {
            // 捕获其他通用异常，提供友好的错误信息
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.group_purchase');
            return [
                    false,
                    ['message' => '发起拼团时出现错误，请稍后再试。' . $e->getMessage(), 'id' => 0]
            ];
        }
    }


    /**
     * 创建拼团团长
     * @param int $activityId 活动ID
     * @param int $gid 商品ID
     * @param int $userId 用户ID
     * @return array ['status' => bool, 'data' => array] 创建结果
     * @throws Exception
     */
    public function createGroupLeader($activityId,  $gid,  $userId): array
    {
        // 获取用户uid
        $uid = by::usersMall()->getInfoByUserId($userId)['uid'] ?? '';

        // 创建团长数据
        $save = [
                'activity_id' => $activityId,
                'gid'         => $gid,
                'user_id'     => $userId,
                'uid'         => $uid,
                'status'      => byNew::GroupPurchaseModel()::STATUS_IN_PROGRESS,
        ];

        return CUtil::mSave(byNew::GroupPurchaseModel(), $save);
    }

    private static $validActivityInstance = [];

    /**
     * 获取有效的活动信息（单例模式）
     * @param int $activityId 活动ID
     * @return array|null 活动信息
     */
    private function getValidActivity(int $activityId)
    {
        if (!isset(self::$validActivityInstance[$activityId])) {
            self::$validActivityInstance[$activityId] = byNew::GroupPurchaseActivityModel()->find()
                    ->where(['id' => $activityId])
                    ->andWhere(['<=', 'start_time', time()])
                    ->andWhere(['>=', 'end_time', time()])
                    ->andWhere(['is_del' => 0])
                    ->one();
        }
        return self::$validActivityInstance[$activityId];
    }

    /**
     *  获取拼团列表
     *  权限：已发起某个拼团的用户
     *  返回：商品标题、商品图片、商品价格、原始价格、拼团状态、拼团人数、库存、un_pay_order_no(待支付订单号)
     */
    public function getGroupPurchaseList($userId, $activityId, $goods_name, $tag_id = 0): array
    {
        $activity = $this->getValidActivity($activityId);
        if (empty($activity)) {
            return [];
        }

//        $groups = is_numeric($userId)?$this->getMyGroupPurchase($userId, $activityId):[];
//
//        // 只保留正在行进中的
//        $groups = array_filter($groups, function ($group) {
//            return $group['status'] == 0;
//        });

//        $gids = array_column($groups, 'gid');
        return $this->getGoodsByGids($activity, $goods_name, $activity['name'], [], [], 1, $tag_id);
    }


    /**
     *  按钮状态 0：抢 1：已抢光 2：邀请好友 3：已下架 4：团员已满 5：拼团已结束
     *
     *  拼团成功：购买台数达标
     *  团员已满: 坑位已满
     *  团已拼满：所有人都已达到购买上限，可再次发起拼团
     *  团结束：团已拼满，团员已满
     *
     *  按钮状态
     *  1.团长尚未发起拼团时显示「抢」
     *  2.当商品剩余库存为0时,按钮需要变为「已抢光」
     *  3.团长已发起拼团时当商品下架时,按钮需要变为「已下架」
     *  4.当团长已针对某个商品发起拼团,仍然可以对其他商品发起拼团,只是页面上需要做区分，将已发起拼团的商品放在页面上方。
     *  5.当发起拼团但尚未拼满时,按钮变为「邀请好友参团」,可生成该商品的小程序链接卡片,并可唤起微信好友列表进行发送。
     *  6.当发起拼团且拼团成功时,按钮变为「拼团成功」。
     *  7.当发起拼团但拼团失败时,按钮变为「拼团失败」。
     *  8.当发起拼团但团员已满时,按钮变为「团员已满」。
     *  9.当发起拼团但拼团已结束时,按钮变为「拼团已结束」。
     *  已结束的团还要显示「
     */
    public function getButtonStatus($activity, $unPayOrderNo, $isGroup, $groupStatus, $stock, $goodStatus, $totalMembers, $maxMembers): int
    {

        $STATUS_GET        = 0; //抢（拼团中）
        $STATUS_SOLD_OUT   = 1; //已抢光 （无库存）
        $STATUS_INVITE     = 2; //参与拼团 （已发起拼团 可邀请好友）
        $STATUS_OFF_SHELF  = 3; //已下架 （商品已下架）
        $STATUS_GROUP_FULL = 4; //团员已满（拼团成功）
        $STATUS_END        = 5; //团未拼满时间结束（拼团失败）


        /**
         * 团长发起拼团时
         */
        if ($isGroup) {
            if ($unPayOrderNo != '' && $totalMembers == 0) {
                return $STATUS_GET;
            }

            /**
             * 9.当发起拼团但拼团已结束时。
             */
            if ($groupStatus != byNew::GroupPurchaseModel()::STATUS_SUCCESS && $activity['end_time'] < time()) {
                return $STATUS_END;
            }

            /**
             * 8.当发起拼团但团员已满时,按钮变为「拼团成功」。
             */
            if ($totalMembers >= $maxMembers) {
                return $STATUS_GROUP_FULL;
            }

            /**
             * 5.当发起拼团但尚未拼满时,按钮变为「邀请好友参团」
             */
            return $STATUS_INVITE;
        } else {
            /**
             * 3.团长已发起拼团时当商品下架时,按钮需要变为「已下架」
             */
            if ($goodStatus == by::Gmain()::STATUS['OFF_SALE']) {
                return $STATUS_OFF_SHELF;
            }

            /**
             * 2.当商品剩余库存为0时,按钮需要变为「已抢光」
             */
            if ($stock == 0) {
                return $STATUS_SOLD_OUT;
            }

            /**
             * 团长尚未发起拼团时显示「抢」
             */
            return $STATUS_GET;
        }

    }


    // 获取拼团成员是否已满
    public function checkGoodsDetail($groupPurchaseId, $userId): array
    {
        $groupPurchase = byNew::GroupPurchaseModel()->find()->where(['id' => $groupPurchaseId])->one();
        if (empty($groupPurchase)) {
            return ['status' => false, 'message' => '拼团不存在'];
        }

        // 1. 检查活动是否已过期
        $activity = $this->getValidActivity($groupPurchase->activity_id);
        if (empty($activity)) {
            return ['status' => false, 'message' => '活动不存在'];
        }
        if ($activity['end_time'] < time()) {
            return ['status' => false, 'message' => '活动已结束'];
        }

        // 2. 检查商品是否是团员
        $isGroupMember = ByNew::GroupPurchaseMemberModel()->find()->where(['group_purchase_id' => $groupPurchaseId, 'user_id' => $userId])->one();

        if (!$isGroupMember) {
            $maxMembersGroup = $groupPurchase->total_members ?? 0;
            $goods           = ByNew::GroupPurchaseActivityGoodsModel()->find()->where(['activity_id' => $groupPurchase->activity_id, 'gid' => $groupPurchase->gid])->one();

            if (empty($goods)) {
                return ['status' => false, 'message' => '商品不存在'];
            }

            // 3. 检查拼团是否已满
            if ($maxMembersGroup >= $goods->max_members) {
                return ['status' => false, 'message' => '抱歉，本团已满员，不可参加'];
            }
        }

        return ['status' => true, 'message' => '可参团', 'data' => ['gid' => $groupPurchase->gid]];
    }

    /**
     * 单例获取活动信息
     * @param int $activityId
     * @return array|null
     */
    private static $activityCache = [];

    private function getActivityById(int $activityId)
    {
        if (!isset(self::$activityCache[$activityId])) {
            self::$activityCache[$activityId] = byNew::GroupPurchaseActivityModel()->find()
                    ->where(['id' => $activityId])
                    ->andWhere(['is_del' => 0])
                    ->one();
        }
        return self::$activityCache[$activityId];
    }


    /**
     * 获取活动关联的商品GID和SID映射关系
     *
     * @param int $activityId 活动ID
     * @return array 返回格式为 ['gid' => ['sid1', 'sid2', ...]] 的关联数组
     */
    public function getGidByActivityId(int $activityId): array
    {
        // 检查活动是否存在
        $activity = $this->getActivityById($activityId);
        if (empty($activity)) {
            return [];
        }

        // 直接查询活动商品并按gid分组
        $goods = byNew::GroupPurchaseActivityGoodsModel()
                ->find()
                ->select([
                        'gid',
                ])
                ->where(['activity_id' => $activityId])
                ->asArray()
                ->all();

        return array_column($goods, 'gid');

    }

    // 获取拼团成员详情
    public function getGroupMembers($groupPurchaseId, $userId): array
    {
        $group = byNew::GroupPurchaseModel()->find()->where(['id' => $groupPurchaseId])->one();
        if (empty($group)) {
            return ['status' => false, 'message' => '获取团组失败'];
        }

        $activity = $this->getActivityById($group->activity_id);
        if (empty($activity)) {
            return ['status' => false, 'message' => '活动已不存在'];
        }

        $goodsList = $this->getGoodsByGids($activity, '', $activity['name'], [], [$group->gid]);
        $goods     = $goodsList['goods']->first() ?? [];

        if (empty($goods)) {
            return ['status' => false, 'message' => '获取商品失败'];
        }

        $members = byNew::GroupPurchaseMemberModel()->getGroupMembers($group->activity_id, $groupPurchaseId);
        $order   = \app\modules\back\services\GroupPurchaseService::getInstance()->getOrderData($members);

        // key 作为条件
        $user_order = Collection::make($order)->keyBy('user_id')->toArray();

        $groupSuccess = $goods['min_group_qty'] - $group->total_items;
        $groupFull    = $goods['max_members'] * $goods['purchase_limit'] - $group->total_items;
        // 当前用户 learder 团长  member 团员  tourist 游客
        $user_type = 'tourist';
        if ($group->user_id == $userId) {
            $user_type = 'leader';
        } else if (in_array($userId, array_column($members, 'user_id'))) {
            $user_type = 'member';
        }
        $BuyCount = byNew::GroupPurchaseMemberModel()->find()->where(['group_purchase_id' => $groupPurchaseId, 'user_id' => $userId])->sum('items_qty');
        $members  = Collection::make($members)->map(function ($member) use ($group, $goods, $user_order) {
            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);
            return [
                    'order_type'          => $user_order[$member['user_id']]['type'] ?? '',
                    'order_no'           => $user_order[$member['user_id']]['order_no'] ?? '',
                    'order_status'       => $user_order[$member['user_id']]['status'] ?? '',
                    'order_status_value' => $user_order[$member['user_id']]['status_value'] ?? '',
                    'is_leader'          => $member['user_id'] == $group->user_id ? 1 : 0, //是否团长 1:是 0:否
                    'avatar'             => $member['avatar'] == "" ? $config['cdnAddr'] . '/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png' : $member['avatar'],
                    'id'                 => $member['id'],
                    'user_id'            => $member['user_id'],
                    'is_reward'          => $member['is_reward'],
                    'nick_name'           => $member['nick_name']??'未知昵称'
            ];
        })->sortBy('id')->sortByDesc('is_leader')->values()->toArray();

        // 使用通用方法计算团结束时间
        $group_end_time = $this->calculateGroupEndTime($group->ctime);
        $data = [
                'status'                   => $group->status,
                'members'                  => $members,
                'end_time'                 => $activity['end_time'],
                'group_end_time'           => $group_end_time, // 添加结束时间+24小时的时间戳
                'max_members'              => (int) $goods['max_members'],
                // 再购买多少件商品拼团成功
                'need_items_group_success' => ($groupSuccess) > 0 ? $groupSuccess : 0,
                // 再购买多少件商品团满
                'need_items_group_full'    => ($groupFull) > 0 ? $groupFull : 0,
                // 团长是否可购买 1:是 0:否
                'purchase_limit'           => (int) $goods['purchase_limit'], // 限购数量
                'buy_count'                => (int) $BuyCount, // 已买台数
                'gid'                      => $group->gid,
                'user_type'                => $user_type,
                'goods'                    => $goods
        ];

        return ['status' => true, 'message' => '获取成功', 'data' => $data];
    }


    /**
     * 分享拼团邀请链接
     *
     * @param int $groupPurchaseId
     * @return array [bool, string] 返回布尔值和链接结果或错误信息
     * @throws Exception
     */
    public function shareInvitationLink(int $groupPurchaseId): array
    {
        $groupInfo = byNew::GroupPurchaseModel()->getLeaderInfo($groupPurchaseId);
        if (empty($groupInfo)) {
            return [false, '参数有误！'];
        }
        $params = [
                'path'  => self::SHARE_PATH,
                'query' => http_build_query(['group_purchase_id' => $groupPurchaseId, 'gid' => $groupInfo['gid']]), // 使用 http_build_query 构建查询字符串
                'type'  => 2,                                                                                       // 自有长链
        ];

        // 调用生成链接方法
        list($success, $result) = by::WxUlinkModel()->CreateLink($params);

        // 提前返回错误结果
        if (!$success) {
            return [false, $result];
        }

        // 返回成功结果
        return [true, $result];
    }


    /**
     * 检查用户是否是拼团的团长
     *
     * @param  $userId
     * @param  $params
     * @return array 返回结果数组，其中 'is_leader' 为 0 表示不是团长，1 表示是团长
     * @throws Exception 如果发生异常
     */
    public function checkLeader($userId, $params): array
    {
        // 提取订单号和团ID
        $orderNo         = $params['order_no'] ?? '';          // 订单号
        $groupPurchaseId = $params['group_purchase_id'] ?? 0;  // 团ID

        // 参数校验：订单号和团ID至少需要提供一个
        if (empty($orderNo) && empty($groupPurchaseId)) {
            return [false, '参数错误！'];
        }

        // 如果没有提供团ID，则通过订单号获取订单信息来查找团ID
        if (empty($groupPurchaseId)) {
            $orderInfo       = by::Ouser()->GetInfoByOrderId($userId, $orderNo);
            $groupPurchaseId = $orderInfo['group_purchase_id'] ?? ''; // 从订单信息中获取团ID

            // 如果订单信息中也没有团ID，则用户不是团长
            if (empty($groupPurchaseId)) {
                return [true, [
                        'is_leader'         => 0,
                        'group_purchase_id' => $groupPurchaseId // 返回空的团ID
                ]];
            }
        }

        // 根据团ID获取团长信息
        $leader = byNew::GroupPurchaseModel()->getLeaderInfo($groupPurchaseId);

        // 如果没有找到团长信息，表示用户不是团长
        if (empty($leader)) {
            return [true, [
                    'is_leader'         => 0,
                    'group_purchase_id' => $groupPurchaseId // 返回团ID，尽管没有团长
            ]];
        }

        // 判断当前用户是否是团长
        $isLeader = ($leader['user_id'] == $userId) ? 1 : 0;

        // 返回结果：包含是否为团长和团ID
        return [true, [
                'is_leader'         => $isLeader,
                'group_purchase_id' => $groupPurchaseId
        ]];
    }

    public function getGroupPurchaseDetail($activity_id, $group_purchase_id): array
    {

        if (!empty($group_purchase_id)) {
            $group = byNew::GroupPurchaseModel()->find()->where(['id' => $group_purchase_id])->one();
            if (empty($group)) {
                return ['status' => false, 'message' => '拼团不存在'];
            }
            $activity_id = $group->activity_id;
        }

        $data = $this->getValidActivity($activity_id);
        if (empty($data)) {
            return ['status' => false, 'message' => '抱歉，本团已结束，不可参加'];
        }

        return ['status' => true, 'message' => '获取成功', 'data' => ["details" => $data['details'] ?? ""]];
    }


    public function checkOrder($order_no): array
    {
        $order = byNew::GroupPurchaseMemberModel()->find()->where(['order_no' => $order_no])->one();
        if (empty($order)) {
            return ['status' => false, 'message' => '订单不存在'];
        }
        return ['status' => true, 'message' => '订单存在'];
    }

    /**
     * 更新拼团商品的参团人数
     *
     * 此方法用于更新指定活动和商品的拼团信息中的成员数量。
     * 它会查询当前拼团活动的商品对应的成员数量，
     * 并将结果更新到相应的数据库记录中。
     *
     * @param int $activityId 活动ID，标识所属的拼团活动
     * @param int $gid 商品ID，标识需要更新成员数量的商品
     * @return bool 返回布尔值，表示更新操作是否成功
     */
    public function updateGroupPurchaseGoodsMember(int $activityId, int $gid): bool
    {
        try {
            // 更新 t_group_purchase_goods 表中的 member 字段
            $result = byNew::GroupPurchaseActivityGoodsModel()->updateAll(
                    ['member' => new \yii\db\Expression('member+1')],
                    [
                            'activity_id' => $activityId,
                            'gid'         => $gid
                    ]
            );

            return $result != false;
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.group-purchase');
            return false;
        }
    }

    /**
     * 获取拼团进行中列表
     * @param $userId
     * @param int $activityId 活动ID
     * @param int $type 1:正常列表 2:随机模式
     * @param string $goods_name 商品名称，用于模糊查询
     * @param int|null $gid 商品ID，用于过滤特定商品
     * @return array
     */
    public function getInProgressGroupList($userId, int $activityId, int $type, string $goods_name = '',$gid = null): array
    {
        try {
        // 1. 验证活动是否存在
        $activity = $this->getValidActivity($activityId);
        if (empty($activity)) {
            return ['status' => true, 'message' => '未开始或已结束', 'data' => []];
        }

        // 2. 获取活动商品列表
        $filterGids = empty($gid)?[]:[$gid];
        list($goods,$pages) = byNew::GroupPurchaseActivityGoodsModel()->getActivityGoodsList($activityId, 'all', $goods_name,[],$filterGids,1);
        if (empty($goods)) {
            return ['status' => true, 'message' => '商品不存在', 'data' => []];
        }


        $goods = array_filter($goods, function ($item) {
            // 过滤已下架
            if ($item['status'] == by::Gmain()::STATUS['ON_SALE']) {
                // 过滤库存为0
                $stock = array_sum(array_column(
                        GoodsMainService::getInstance()->GetMainStockByGid($item['gid']),
                        'stock'
                ));
                if ($stock > 0) {
                    return true;
                }
            }
            return false;
        });

        // 过滤后的可用gid
        $gids = empty($gid)? array_column($goods, 'gid'):$filterGids;

            // 3. 构建拼团查询
            $query = byNew::GroupPurchaseModel()->find()
                    ->where([
                            'status'      => byNew::GroupPurchaseModel()::STATUS_IN_PROGRESS,
                            'activity_id' => $activityId,
                            'gid'         => $gids
                    ])
                    ->andWhere([
                            '>',
                            'ctime',
                            time() - 86400
                    ]); // 过滤24小时前创建的拼团

         if ($type==1){
             $query=$query->andWhere(['>','total_members',0]);
         }


        // 4. 根据类型设置排序方式
        if ($type == 2) {
            $limit = CUtil::getRequestParam('post', 'page_size', 3);
            // 获取总记录数
            $count = $query->count();
            // 如果记录数大于等于limit条,随机取limit条
            if ($count >= $limit) {
                // 计算最大可用的偏移量，确保能取到足够的记录
                $maxOffset = max(0, $count - $limit);
                // 随机生成偏移量
                $randomOffset = $maxOffset > 0 ? rand(0, $maxOffset) : 0;
                // 使用随机偏移量获取记录
                $query->offset($randomOffset)
                        ->limit($limit);
            } else {
                // 如果总记录数小于limit，则取所有记录
                $query->limit($count);
            }
            $result['list'] = $query->asArray()->all();
        } else {
            // 优化排序：让自己发起的拼团排在前面
            if ($userId>0){
                $query->orderBy([
                        new Expression("CASE WHEN user_id = " . (int)$userId . " THEN 1 ELSE 0 END DESC"),
                        'id' => SORT_DESC
                ]);
            }

            // 5. 分页处理
            $result = CUtil::Pg($query);
        }

        $result['list'] = array_map(function ($group) use ($goods, $activity) {
            return $this->getGroupPurchaseFormat($goods, $activity, $group);
        }, $result['list']);

        // 过滤掉空值（null）
        $result['list'] = array_filter($result['list'], function ($item) {
            return $item !== null;
        });

        // 重新索引数组
        $result['list'] = array_values($result['list']);

        // 6. 添加活动标题
        $result['title'] = $activity['name'] ?? '';

        return ['status' => true, 'message' => '获取成功', 'data' => $result];

        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.group-purchase');
            return ['status' => false, 'message' => '获取拼团列表失败'];
        }
    }

    private function getGroupPurchaseFormat($goods, $activity, $group)
    {
        // 5.1 获取商品信息
        $goodsInfo = array_filter($goods, function ($item) use ($group) {
            return $item['gid'] == $group['gid'];
        });

        if (empty($goodsInfo)) {
            return null; // 返回null而不是空数组
        }

        $goodsInfo = reset($goodsInfo);

        // 5.2 获取成员信息
        $members = byNew::GroupPurchaseMemberModel()->find()
                ->where(['group_purchase_id' => $group['id']])
                ->groupBy('user_id')
                ->all();

        $currentMemberCount = count($members);

        // 5.3 获取成员头像列表
        $memberAvatars = $this->getMemberAvatars($members);

        // 5.4 计算还差多少人成团
        $remainingMembers = $goodsInfo['max_members'] - $currentMemberCount;

        // 5.5 获取库存
        $stock = array_sum(array_column(
                GoodsMainService::getInstance()->GetMainStockByGid($goodsInfo['gid']),
                'stock'
        ));

        // 过滤无库存
        if ($stock == 0) {
            return null;
        }

        // 5.6 获取按钮状态
        $buttonStatus = $this->getButtonStatus(
                $activity,
                '',
                true,
                $group['status'],
                $stock,
                $goodsInfo['status'],
                $currentMemberCount,
                $goodsInfo['max_members']
        );

        // 5.7 计算活动结束时间
        $endTime = $activity['end_time'] - time();

        // 5.8 计算团结束时间
        $groupEndTime = $this->calculateGroupEndTime($group['ctime']);

        return [
                'gid'               => $goodsInfo['gid'],
                'name'              => $goodsInfo['name'],
                'cover_image'       => $goodsInfo['cover_image'],
                'price'             => $goodsInfo['price'],
                'mprice'            => $goodsInfo['mprice'],
                'status'            => $goodsInfo['status'],
                'rate'              => $goodsInfo['rate'],
                'button_status'     => $buttonStatus,
                'remaining_members' => max($remainingMembers, 0),
                'group_purchase_id' => $group['id'],
                'leader_id'         => $group['user_id'], // 添加团长的user_id
                'end_time'          => $endTime,
                'group_end_time'    => $groupEndTime,
                'member_avatars'    => $memberAvatars,
                'member'            => $goodsInfo['member'],
                'max_members'       => $goodsInfo['max_members']
        ];
    }

    /**
     * 获取成员头像和昵称列表
     * @param array $members 成员列表
     * @return array
     * @throws Exception
     */
    private function getMemberAvatars(array $members): array
    {
        $memberInfo    = [];

        foreach ($members as $member) {
            $userInfo     = by::usersMall()->getInfoByUserId($member->user_id);
            $memberInfo[] = [
                    'avatar'   => CUtil::avatar($userInfo),
                    'nick_name' => !empty($userInfo['nick_name']) ? $userInfo['nick_name'] : '未知用户'
            ];
        }

        return $memberInfo;
    }

    public function checkGroupPurchase($userId, $activityId, $gid): array
    {
        // 1.检查活动是否有效
        $activity = $this->checkActivity($activityId);
        if (!$activity['status']) {
            return $activity;
        }

        // 1. 校验资格
        list($status, $msg, $code) = $this->verifyQualification($userId);
        if (!$status) {
            return ['status' => false, 'message' => $msg, 'data' => ['code' => $code]];
        }

        // 2.检查团长是否有效
        $group = $this->getMyGroupPurchase($userId, $activityId, $gid);

        foreach ($group as $item) {
            if ($item['gid'] == $gid && $item['status'] == byNew::GroupPurchaseModel()::STATUS_IN_PROGRESS) {
                return ['status' => false, 'message' => '您已发起拼团，请勿重复发起', 'data' => ['code' => 120008]];
            }
        }

        // 3.查询团长是否有带支付订单
//        $unPayOrderNo = byNew::GroupPurchaseModel()->instance()->getUnPayOrder($userId);
//
//        if (!empty($unPayOrderNo)) {
//            return ['status' => false, 'message' => '当前存在一笔待支付订单，需先完成支付或取消订单后才可进行下一步', 'data' => ['un_pay_orders' => $unPayOrderNo]];
//        }

        return ['status' => true, 'message' => '检测通过', 'data' => []];
    }

    /**
     * 获取我发起的正在进行中的拼团信息
     *
     * @param int $userId 用户ID
     * @param int $activityId 活动ID
     * @param int $gid 商品ID（可选，为空时返回所有拼团，非空时按商品过滤）
     * @return array 返回查询结果数组
     */
    public function getMyGroupPurchase(int $userId, int $activityId, $gid = ''): array
    {
        $GroupPurchaseModel = byNew::GroupPurchaseModel()->instance();

        // 构建基础查询条件：用户和活动
        $query = $GroupPurchaseModel->find()
                ->where(['user_id' => $userId, 'activity_id' => $activityId])->andWhere(['status' => [0, 1]]);

        // 如果提供了商品ID，则添加商品过滤条件
        if (!empty($gid)) {
            $query->andWhere(['gid' => $gid]);
        }

        // 返回查询结果
        return $query->asArray()->all();
    }


    /**
     * 检查活动是否有效
     * @param $activityId
     * @return array
     */
    private function checkActivity($activityId): array
    {
        $GroupPurchaseModel = byNew::GroupPurchaseActivityModel()->instance();
        $activity           = $this->getValidActivity($activityId);
        if (empty($activity)) {
            return ['status' => false, 'message' => '请等待下次团购活动', 'data' => ['code' => '120001']];
        }

        return ['status' => true, 'message' => '活动有效', 'data' => $activity];
    }

    /**
     * 检查团长是否有效
     */
    private function checkGroup($activityId, $groupPurchaseId): array
    {
        $GroupPurchaseModel = byNew::GroupPurchaseModel()->instance();

        $group = $GroupPurchaseModel->find()->where(['id' => $groupPurchaseId, 'activity_id' => $activityId])->one();
        if (empty($group)) {
            return ['status' => false, 'message' => '团信息不存在', 'data' => []];
        }

        return ['status' => true, 'message' => '团有效', 'data' => $group];
    }

    /**
     * 我发起的拼团
     * @param $userId
     * @param int $type
     * @param  $goods_name
     * @return array
     */
    public function getMyGroupPurchaseList($userId, int $type = 1, $goods_name = ''): array
    {
        // 第一步：查询拼团基本信息和活动信息
        $groupQuery = byNew::GroupPurchaseModel()->find()
                ->select(['t_group_purchases.id as group_purchases_id', 't_group_purchases.activity_id', 't_group_purchases.gid', 't_group_purchases.total_members', 't_group_purchases.status as group_status', 't_group_purchases.ctime', 'start_time', 'end_time'])
                ->innerJoin('`db_dreame_goods`.t_group_purchase_activities', 't_group_purchase_activities.id=t_group_purchases.activity_id')
                ->andWhere(['t_group_purchase_activities.is_del' => 0]);

        // 1:我参与的团购 2:我发起的团购 3:我的正在拼的团
        if ($type == 2) {
            $groupQuery->where(['t_group_purchases.user_id' => $userId]);
            $groupQuery->orderBy(['t_group_purchases.id' => SORT_DESC]);
        } elseif ($type == 3) {
            // 我的正在拼的团：包含我参与的和我发起的所有正在进行中的拼团
            $groupQuery->leftJoin('`db_dreame_goods`.t_group_purchase_members', 't_group_purchase_members.group_purchase_id=t_group_purchases.id')
                    ->where(['or',
                        ['t_group_purchases.user_id' => $userId],
                        ['t_group_purchase_members.user_id' => $userId]
                    ])
                    ->andWhere(['t_group_purchases.status' => 0]); // 只查询正在进行中的拼团
            $groupQuery->orderBy(['t_group_purchases.id' => SORT_DESC]);
        } else {
            $groupQuery->leftJoin('`db_dreame_goods`.t_group_purchase_members', 't_group_purchase_members.group_purchase_id=t_group_purchases.id')
                    ->where(['t_group_purchase_members.user_id' => $userId]);

            $groupQuery->orderBy('t_group_purchase_members.ctime desc');
        }

        // 获取拼团基本信息
        $groupData = $groupQuery->asArray()->all();

        if (empty($groupData)) {
            return ['status' => true, 'message' => '', 'data' => ['list' => [], 'total' => 0, 'page' => 1, 'size' => 20]];
        }

        // 提取所有的gid用于后续查询
        $gids        = array_unique(array_column($groupData, 'gid'));
        $activityIds = array_unique(array_column($groupData, 'activity_id'));

        // 第二步：查询拼团商品信息
        $goodsQuery = byNew::GroupPurchaseActivityGoodsModel()->find()
                ->select(['activity_id', 'gid', 'member', 'max_members'])
                ->where(['activity_id' => $activityIds, 'gid' => $gids]);

        $goodsData = $goodsQuery->asArray()->all();

        // 建立商品数据的索引
        $goodsIndex = [];
        foreach ($goodsData as $goods) {
            $key              = $goods['activity_id'] . '_' . $goods['gid'];
            $goodsIndex[$key] = $goods;
        }

        // 第三步：查询商品详细信息
        $mainGoodsQuery = by::Gmain()->find()
                ->select(['t_gmain.id', 't_gmain.name', 't_gmain.status', 't_gtype_0.atype', 'mprice', 'price' => 'COALESCE(t_gtype_0.price)', 't_gtype_0.cover_image'])
                ->innerJoin('`db_dreame_goods`.t_gtype_0', 't_gmain.id = t_gtype_0.gid')
                ->where(['t_gmain.id' => $gids]);

        // 如果有商品名称过滤条件
        if (!empty($goods_name)) {
            $mainGoodsQuery->andFilterWhere(['like', 't_gmain.name', $goods_name]);
            $mainGoodsQuery->andWhere(['t_gmain.status' => 1]);
        }

        $mainGoodsData = $mainGoodsQuery->asArray()->all();

        // 建立商品详情的索引
        $mainGoodsIndex = [];
        foreach ($mainGoodsData as $mainGoods) {
            $mainGoodsIndex[$mainGoods['id']] = $mainGoods;
        }

        // 第四步：组装数据并分页处理
        $processedData = [];
        foreach ($groupData as $group) {
            $gid        = $group['gid'];
            $activityId = $group['activity_id'];

            // 如果商品不存在（可能被商品名称过滤掉了），则跳过
            if (!isset($mainGoodsIndex[$gid])) {
                continue;
            }

            $goodsKey  = $activityId . '_' . $gid;
            $goodsInfo = $goodsIndex[$goodsKey] ?? [];
            $mainGoods = $mainGoodsIndex[$gid];

            // 获取库存
            $stock = 0;
            if (!empty($gid)) {
                $stock = array_sum(array_column(GoodsMainService::getInstance()->GetMainStockByGid($gid), 'stock'));
            }

            $activity = [
                    'id'         => $activityId,
                    'start_time' => $group ['start_time'],
                    'end_time'   => $group ['end_time'],
            ];

            // 拼团状态
            $groupStatus = $group['group_status'];
            // 按钮状态
            $buttonStatus = $this->getButtonStatus($activity, '', true, $groupStatus, $stock, $mainGoods['status'] ?? 0, $group['total_members'] ?? 0, $goodsInfo['max_members'] ?? 0);

            // 计算团结束时间
            $groupEndTime = $this->calculateGroupEndTime($group['ctime']);

            $processedData[] = [
                    'id'                 => $mainGoods['id'],
                    'gid'                => $gid,
                    'name'               => $mainGoods['name'],
                    'price'              => CUtil::totalFee($mainGoods['price'], 1),
                    'mprice'             => CUtil::totalFee($mainGoods['mprice'], 1),
                    'status'             => $mainGoods['status'],
                    'cover_image'        => $mainGoods['cover_image'],
                    'atype'              => $mainGoods['atype'],
                    'button_status'      => $buttonStatus,
                    'member'             => $goodsInfo['member'] ?? 0,
                    'group_purchases_id' => $group['group_purchases_id'],
                    'activity_id'        => $activityId,
                    'max_members'        => $goodsInfo['max_members'] ?? 0,
                    'group_end_time'     => $groupEndTime,
            ];
        }

        // 手动分页处理
        $page   = intval($_POST['page'] ?? 1);
        $size   = intval($_POST['page_size'] ?? 20);
        $total  = count($processedData);
        $offset = ($page - 1) * $size;
        $list   = array_slice($processedData, $offset, $size);

        $result = [
                'list'  => $list,
                'total' => $total,
                'page'  => $page,
                'size'  => $size
        ];

        return ['status' => true, 'message' => '', 'data' => $result];
    }


    /**
     * 计算团购结束时间
     * @param int $createTime 团创建时间戳
     * @return int 团结束时间戳（创建时间+24小时）
     */
    public function calculateGroupEndTime(int $createTime): int
    {
        // 团结束时间为创建时间+24小时（86400秒）
        return $createTime + 86400;
    }

    /**
     * 团购消息推送
     * @param $msgConfigId
     * @param array $uids
     * @param $link_url
     * @return array
     */
    public function sendPush($msgConfigId, array $uids, $link_url): array
    {
        // 根据 $uid 每人每日收到拼团团购推送不超过4条,每人每次推送的间隙超过30分钟
        $successUids = [];
        $failedUids = [];

        foreach ($uids as $uid) {
            // 检查推送限制
            if ($this->checkPushLimit($uid)) {
                $successUids[] = $uid;
                // 记录推送
                $this->recordPush($uid);
            } else {
                $failedUids[] = $uid;
                CUtil::debug("用户 {$uid} 推送限制，跳过推送", 'group_purchase.push_limit');
            }
        }

        // 如果没有可推送的用户，返回失败
        if (empty($successUids)) {
            return [false, '所有用户均达到推送限制'];
        }

        $msg = [
            'msgConfigId'  => intval($msgConfigId),
            'pushStrategy' => 'crowd_push',
            'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
            'uids'         => $successUids,
            'ext'          => json_encode(['link_url' => $link_url], 320)
        ];

        list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
        // 记录推送结果
        if (!empty($failedUids)) {
            CUtil::debug("以下用户因推送限制被跳过：" . implode(',', $failedUids), 'group_purchase.push_limit');
        }
        
        return [$status, $data];
    }

    /**
     * 检查用户推送限制
     * @param string $uid 用户uid
     * @return bool 是否可以推送
     */
    private function checkPushLimit(string $uid): bool
    {
        $redis = by::redis();
        $today = date('Ymd');

        // 每日推送次数key
        $dailyKey = "group_purchase:push:daily:{$today}:{$uid}";
        // 最后推送时间key
        $lastPushKey = "group_purchase:push:last_time:{$uid}";

        // 检查每日推送次数（不超过4条）
        $dailyCount = $redis->get($dailyKey);
        if ($dailyCount !== false && intval($dailyCount) >= 4) {
            return false;
        }

        // 检查推送间隔（超过30分钟）
        $lastPushTime = $redis->get($lastPushKey);
        if ($lastPushTime !== false) {
            $currentTime = time();
            $timeDiff = $currentTime - intval($lastPushTime);
            // 间隔时间
            $time_sec = YII_ENV_PROD ? 1800 : 60; // 生产环境30分钟，测试环境1分钟
            if ($timeDiff < $time_sec) { // 30分钟 = 1800秒
                return false;
            }
        }

        return true;
    }

    /**
     * 记录用户推送
     * @param string $uid 用户uid
     * @return void
     */
    private function recordPush(string $uid)
    {
        $redis = by::redis();
        $today = date('Ymd');
        $currentTime = time();

        // 每日推送次数key
        $dailyKey = "group_purchase:push:daily:{$today}:{$uid}";
        // 最后推送时间key
        $lastPushKey = "group_purchase:push:last_time:{$uid}";

        // 增加每日推送次数
        if ($redis->exists($dailyKey)) {
            $redis->incr($dailyKey);
        } else {
            $redis->set($dailyKey, 1);
            // 设置过期时间为当天结束
            $expireTime = strtotime('tomorrow') - $currentTime;
            $redis->expire($dailyKey, $expireTime);
        }

        // 记录最后推送时间
        $redis->set($lastPushKey, $currentTime);
        // 设置过期时间为24小时
        $redis->expire($lastPushKey, 86400);
    }

    /**
     * 检查是否有团购的待支付订单
     * @param $userId
     * @return array
     */
    public function checkUnPayOrder($userId,$group_purchase_id): array
    {
        $unPayOrder = byNew::GroupPurchaseModel()->instance()->getUnPayOrder($userId,$group_purchase_id);
        if (empty($unPayOrder)) {
            return ['status' => true, 'message' => '没有待支付订单', 'data' => []];
        }
        return ['status' => false, 'message' => '有待支付订单', 'data' => ['un_pay_orders' => $unPayOrder]];
    }

    /**
     * 检查用户限购数量
     * @param int $activity_id 活动ID
     * @param int $gid 商品ID
     * @param int $user_id 用户ID
     * @return int 剩余可购买数量
     */
    public function limitBuy($activity_id, $gid, $user_id): int
    {
        // 查询商品限购数量
        $limitBuy = byNew::GroupPurchaseActivityGoodsModel()
                ->instance()
                ->find()
                ->select('purchase_limit')
                ->where([
                        'activity_id' => $activity_id,
                        'gid'         => $gid
                ])
                ->scalar();

        if (empty($limitBuy)) {
            return 0;
        }


        // 查询用户已购买数量
        $userBuy = byNew::GroupPurchaseMemberModel()
                ->instance()
                ->find()
                ->select('sum(items_qty)')
                ->leftJoin( byNew::GroupPurchaseModel()->tableName(), '`t_group_purchase_members`.group_purchase_id = `t_group_purchases`.id')
                ->where([
                        't_group_purchase_members.user_id'     => $user_id,
                        't_group_purchase_members.activity_id' => $activity_id,
                        'gid'         => $gid
                ])
                ->scalar();

        return max(0, (int) $limitBuy - (int) $userBuy);
    }


    /**
     * 获取商品团购信息
     * @param int $gid 商品ID
     * @return array
     */
    public function getGroupPurchaseInfo(int $gid): array
    {
        // 默认返回值
        $groupPurchaseInfo = [
            'activity_id'          => 0, // 当前团购活动ID，如果没有可用活动返回0
            'is_group_goods'       => 0, // 当前商品是否是拼团商品 1 是 0 不是
            'discount_activity_id' => 0, // 折扣活动ID
            'discount_gid'         => 0, // 折扣商品ID
            'group_rate'           => 0, // 折扣率
            'price'                => 0
        ];

        try {
            // 查找当前有效的团购活动
            $validActivity = byNew::GroupPurchaseActivityModel()->getCurrentActivity();

            if (!empty($validActivity)) {
                // 检查商品是否在当前活动中
                $activityGoods = byNew::GroupPurchaseActivityGoodsModel()->getInfoByGIdAndAid($gid, $validActivity['id']);
                if (!empty($activityGoods)) {
                    $groupPurchaseInfo['activity_id']          = (int)$validActivity['id'];
                    $groupPurchaseInfo['discount_activity_id'] = (int)$activityGoods['discount_activity_id'];
                    $groupPurchaseInfo['discount_gid']         = (int)$activityGoods['discount_gid'];
                    $groupPurchaseInfo['group_rate']           = (float)$activityGoods['rate'];
                    $groupPurchaseInfo['is_group_goods']       = 1;
                }
            }
        } catch (\Exception $e) {
            // 发生异常时记录日志但继续返回默认值
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.group-purchase-info');
        }

        return $groupPurchaseInfo;
    }

    /**
     * 获取团购实时榜单
     *
     * @param int $activityId 活动ID
     * @return array
     * @throws \Throwable
     */
    public function getRealTimeLeaderboard(int $activityId): array
    {
        try {
            // 直接实时从数据库查询三类榜单并返回
            $leaderboard = $this->getLeaderboardRankings($activityId);
            return ['status' => true, 'message' => '获取成功', 'data' => $leaderboard];

        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.group-purchase-leaderboard');
            return ['status' => false, 'message' => '获取榜单失败', 'data' => []];
        }
    }


    /**
     * 获取团购榜单排名数据
     * @param int $activityId 活动ID
     * @return array 榜单数据列表
     */
    private function getLeaderboardRankings(int $activityId): array
    {
        // 定义榜单配置
        $rankings = [
                [
                        'key'   => 'group_count',
                        'title' => '好拼榜'
                ],
                [
                        'key'   => 'sales_count',
                        'title' => '好评榜'
                ],
                [
                        'key'   => 'discount_amount',
                        'title' => '省钱榜'
                ]
        ];

        // 获取所有榜单数据
        $allResults = [];
        $allGids    = [];
        foreach ($rankings as $ranking) {
            $result = $this->getRankingByGroupKey($activityId, $ranking['title'], $ranking['key']);
            if (!empty($result['gid'])) {
                $allGids[]                   = $result['gid'];
                $allResults[$ranking['key']] = $result;
            } else {
                // 获取活动商品列表,排除已经在其他榜单出现的商品
                list($activityGoods, $pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList(
                        $activityId,
                        "page",
                        null,
                        $allGids,
                        []
                );

                // 如果没有可用商品,直接返回空数组
                if (empty($activityGoods)) {
                    return [];
                }

                // 取第一个未使用的商品
                $result    = reset($activityGoods);
                $allGids[] = $result['gid']; // 记录使用的商品ID

                $allResults[$ranking['key']] = [
                        'gid'           => $result['gid'],
                        $ranking['key'] => '0'
                    // 设置为0表示这是补偿数据
                ];
            }
        }

        if (empty($allGids)) {
            return [];
        }

        // 获取商品列表并建立索引
        list($activityGoods, $pages) = byNew::GroupPurchaseActivityGoodsModel()::getActivityGoodsList($activityId, "all", null, [], $allGids);
        $goodsMap = array_column($activityGoods, null, 'gid');

        // 构建结果数组
        $result = [];
        foreach ($rankings as $ranking) {
            $rankResult = $allResults[$ranking['key']] ?? null;
            if (isset($rankResult['gid']) && isset($goodsMap[$rankResult['gid']])) {
                $goods    = $goodsMap[$rankResult['gid']];
                $result[] = [
                        'gid'         => (string) $goods['gid'],
                        'name'        => $goods['name'],
                        'cover_image' => $goods['cover_image'],
                        'price'       => $goods['price'],
                        'mprice'      => $goods['mprice'],
                        'status'      => (string) $goods['status'],
                        'type'        => $ranking['key'],
                        'value'       => (int) $rankResult[$ranking['key']]
                ];
            }
        }
        return $result;
    }

    private function getRankingByGroupKey(int $activityId, string $groupKey, string $fieldAlias)
    {
        return byNew::GroupPurchaseActivityGoodsModel()
                ->find()
                ->select([
                        'gid',
                        $fieldAlias => 'COUNT(DISTINCT id)'
                ])
                ->where(['activity_id' => $activityId])
                ->andWhere(['is_del' => 0])
                ->andWhere(['group_key' => $groupKey])
                ->asArray()
                ->one();
    }
}