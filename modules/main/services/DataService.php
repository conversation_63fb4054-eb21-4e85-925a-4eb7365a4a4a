<?php

namespace app\modules\main\services;

use app\components\Dwz;
use app\jobs\TryBuyUserPathJob;
use app\jobs\TryBuyUserTagJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\components\Mall;
use app\components\AliApplet;
use app\models\MyExceptionModel;
use RedisException;
use yii\db\Exception;

class DataService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    const PAY_EXPIRE = YII_ENV_PROD ? 300 : 60;

    public function getYearReport($userId): array
    {
        //整体数据
        $userYearReportInfo = byNew::YearReportModel()->getYearReport($userId);
        if (empty($userYearReportInfo)) {
            return [true, [
                'code' => -1,
                'msg'  => '您暂无年终报告~',
            ]];
        }
        // 1、用户名
        $user                       = by::users()->getOneByUid($userId);
        $userYearReportInfo['nick'] = $user['nick'] ?? '';

        // 2、相伴时间
        $regTime                             = $userYearReportInfo['register_time'] ?? 0;
        $togetherTime                        = time() - $regTime;
        $togetherTime                        = bcdiv($togetherTime, 86400, 2);       // 计算天数和小数部分
        $days                                = floor($togetherTime);                 // 提取整数部分作为天数
        $hoursDecimal                        = ($togetherTime - $days) * 24;         // 计算小时的小数部分
        $hours                               = floor($hoursDecimal);                 // 提取整数部分作为小时
        $minutes                             = round(($hoursDecimal - $hours) * 60); // 计算分钟
        $togetherTime                        = "{$days}天 {$hours}小时";
        $userYearReportInfo['together_time'] = $togetherTime;
        unset($userYearReportInfo['register_time']);

        // 3、下单数据处理   x杯奶茶，金额转为元
        $userYearReportInfo['purchase_sum_price']      = bcdiv($userYearReportInfo['purchase_sum_price'], 100, 2);
        $userYearReportInfo['purchase_discount_price'] = bcdiv($userYearReportInfo['purchase_discount_price'], 100, 2);
        $userYearReportInfo['milk_tea']                = bcdiv($userYearReportInfo['purchase_discount_price'], 10);

        // 4、双十一数据处理  跑赢%多少的玩家、秒杀商品名
        $productNames = explode(',', $userYearReportInfo['double11_flash_goods']);
        // 去除重复商品名称并去除空格
        $productNames       = array_filter(array_map('trim', $productNames));
        $uniqueProductNames = array_unique($productNames);
        // 使用数组统计每个商品的数量
        $productCounts = array_count_values($productNames);

        // 格式化输出
        $outputString = '';
        foreach ($uniqueProductNames as $productName) {
            $count        = $productCounts[$productName];
            $outputString .= "{$productName}×{$count}，";
        }

        // 去除最后一个逗号
        $userYearReportInfo['double11_flash_goods'] = rtrim($outputString, '，');
        $userYearReportInfo['checkin_rate']         = $userYearReportInfo['checkin_rate'] . "%";

        // 5、加购数据处理
        $max_cart_infos                        = $userYearReportInfo['max_cart_infos'] ?? '';
        $max_cart_infos                        = json_decode($max_cart_infos, true);
        $userYearReportInfo['add_cart_date']   = $max_cart_infos['date'] ?? '';
        $userYearReportInfo['add_cart_number'] = $max_cart_infos['number'] ?? '';
        unset($userYearReportInfo['max_cart_infos']);

        // 6、最晚下单时间
        $userYearReportInfo['latest_purchase_time'] = empty($userYearReportInfo['latest_purchase_time']) ? null : date('Y-m-d H:i:s', $userYearReportInfo['latest_purchase_time']);

        // 7、人物性格
        if (!empty($userYearReportInfo['ctMaxYearCt'])) {
            $character = $this->getCharacter($userYearReportInfo['product_master'], $userYearReportInfo['ctMaxYearCtTop30'], $userYearReportInfo['totalActiveNumGte2'], $userYearReportInfo['ctMaxYearCsTop30']);
            unset($userYearReportInfo['product_master'], $userYearReportInfo['ctMaxYearCtTop30'], $userYearReportInfo['totalActiveNumGte2'], $userYearReportInfo['ctMaxYearCsTop30']);
            $key                                 = $userId % 5;
            $userYearReportInfo['keyword']       = $character['keyword'][$key];
            $userYearReportInfo['serial_number'] = $character['serial_number'];
        } else {
            $keyword = [
                '顺遂无虞，皆得所愿',
                '清澈明朗，一往无前',
                '日富一日，年富一年',
                '四时平安，万物可爱',
                '好运常在，财运当头'
            ];

            $userYearReportInfo['keyword']       = $keyword[$userId % 5];
            $userYearReportInfo['serial_number'] = 6;
        }

        // 8、处理IOT时间数据  0时区 + 8小时
        $dateFieldsWithTime    = ['firstCleanTime', 'activeTime', 'earliestCleanTime', 'latestCleanTime', 'createTime'];
        $dateFieldsWithoutTime = ['dayCtLargeTime', 'dayCsLargeTime'];

        foreach ($dateFieldsWithTime as $field) {
            if (!empty($userYearReportInfo[$field])) {
                $userYearReportInfo[$field] = date("Y-m-d H:i:s", strtotime($userYearReportInfo[$field]) + 8 * 3600);
            }
        }

        foreach ($dateFieldsWithoutTime as $field) {
            if (!empty($userYearReportInfo[$field])) {
                $userYearReportInfo[$field] = date("Y-m-d", strtotime($userYearReportInfo[$field]) + 8 * 3600);
            }
        }

        // 9、处理IOT数据 为您节省了X天（总使用时间）家务时间 每天按照8小时计算
        $userYearReportInfo['economize_time'] = round(bcdiv($userYearReportInfo['yearCt'], 8 * 60, 2));

        // 10、检验最早最晚时间
        $userYearReportInfo['earliestCleanTime'] = empty($userYearReportInfo['isEarliest']) ? null : $userYearReportInfo['earliestCleanTime'];
        $userYearReportInfo['latestCleanTime']   = empty($userYearReportInfo['isLatest']) ? null : $userYearReportInfo['latestCleanTime'];

        // 11、获取用户等级
        list($status, $ret) = by::memberCenterModel()->CenterMessage('basicInfo', ['user_id' => $userId]);
        $userYearReportInfo['level_name'] = $ret['currentLevelInfo']['level']['name'] ?? '';

        // 12、产品注册品类，换为、
        $userYearReportInfo['product_reg_name'] = str_replace(",", "、", $userYearReportInfo['product_reg_name']);

        return [true, $userYearReportInfo];
    }


    public function getCharacter($productMaster, $ctMaxYearCtTop30, $totalActiveNumGte2, $ctMaxYearCsTop30): array
    {
        switch (true) {
            case $productMaster:
                return [
                    'title'         => '开放的炫彩孔雀：探索世界的艺术家，用奇思妙想绘制生活画卷！',
                    'content'       => '您的扫地机器人对环境的感知更加敏锐，更具有聪慧、开放的思考力。愿意尝试新的清洁方法或使用新技术来保持环境整洁。',
                    'keyword'       => [
                        '眉目舒展 时时开怀',
                        '旧愿已偿 新梦将成',
                        '顺遂无虞 皆得所愿',
                        '哆来咪发 恭喜发财',
                        '勇往直前 喜乐连年',
                    ],
                    'serial_number' => 1
                ];
            case $ctMaxYearCtTop30:
                return [
                    'title'         => '尽责的勤劳蜜蜂：用勤劳与秩序建构着完美的生活之家！',
                    'content'       => '您的扫地机器人在清洁方面更有规律性和频率，更倾向于按部就班地定期扫拖，更注重细节，会花更多时间在每个区域确保彻底的清洁，清洁度要求高！',
                    'keyword'       => [
                        '欢天喜地 万事胜意',
                        '万事随想 所爱如山',
                        '山高水长 终有回甘',
                        '日富一日 年富一年',
                        '清澈明亮 一往无前'
                    ],
                    'serial_number' => 2
                ];
            case $totalActiveNumGte2:
                return [
                    'title'         => '慈爱的温暖熊猫：有一种踏实叫做“有我在”！',
                    'content'       => '您的扫地机器人就像一位清洁小组长，向外散发着能量，关注着整个环境的舒适度。不仅注重地面的常规清洁，也更愿意和伙伴一起完成清洁任务，相互帮助、井井有条。',
                    'keyword'       => [
                        '风和日丽 热爱无边',
                        '四时平安 万物可爱',
                        '山河如初 热爱未央',
                        '大吉大利 日进斗金',
                        '百思无忧 万般称心'
                    ],
                    'serial_number' => 3
                ];
            case $ctMaxYearCsTop30:
                return [
                    'title'         => '快乐的翩翩蝴蝶：积极阳光的心态让平凡生活也有鲜花盛开！',
                    'content'       => '您的扫地机器人属于外向性E人，更喜欢在有朋友或家人来访之前进行清洁，在意社交场合整洁度！更享受在社交氛围下展示自身的清洁本领，热衷于与人互动！',
                    'keyword'       => [
                        '春风拂面 美梦成真',
                        '喜上眉梢 好戏开场',
                        '有趣有盼 快乐无边',
                        '人间富贵 财运当头',
                        '开门见喜 风生水起',
                    ],
                    'serial_number' => 4
                ];
            default:
                return [
                    'title'         => '平和的卡皮巴拉：湖水般平静的外表下是不动声色的自我提升！',
                    'content'       => '您的扫地机器人属于内敛型I人，更多的力量源自于内在的学习、反思和积累。情绪稳定、更擅长进行长时间的清洁任务。对TA来说，清洁可能是缓解压力、舒缓情绪、自我提升的一种方式。',
                    'keyword'       => [
                        '清风朗月 如愿以偿',
                        '天上人间 岁岁欢颜',
                        '目有繁星 沐光而行',
                        '好运常在 发财被爱',
                        '霁风朗月 福至心灵',
                    ],
                    'serial_number' => 5
                ];
        }
    }


    /**
     * 用户授权方法
     *
     * @param string $userId 用户ID
     * @param string $userType 用户类型
     * @param string $phone 用户手机号
     * @return array 返回一个包含状态和消息的数组
     */
    public function AlipayUserAuth($userId, $userType, $phone, $openudid)
    {
        //1.判断手机号是否符合条件
        if (!preg_match("/^1[3456789]\d{9}$/", $phone)) {
            return [false, '手机号不属于国内~'];
        }
        //2.查找用户信息
        if(empty(CUtil::checkUuid($userId))||empty($phone)){
            return [false, '参数不正确'];
        }
        //3.查询用户是否已经授权
        $alipayUser = by::UsersPlatformModeModel()->GetOneByPhone($phone,$userType);
        if($alipayUser){
            return [false,'您已经进行过授权~'];
        }
    
        //4.查询是否已有该用户 (已有用户则直接绑定) 
        $userMainInfo = by::Rusers()->getUserMainInfo($userId);
        $userDetail = by::Rusers()->getOneByUid($userId);
        if(empty($userMainInfo)){
            return [false, '用户不存在'];
        }

        //5.查询用户信息 (这边接口需要传过来的openudid (即authCode 前端配置为auth_user才有权限获取用户信息) )
        if(!empty($openudid)){
            list($status, $alipayUser) = AliApplet::GetUserAuthToken($openudid);
            if (!$status) {
                return [false, $alipayUser];
            }
            $accessToken = $alipayUser['access_token'] ??'';
            if(empty($accessToken)){
                return [false, '授权失败~'];
            }
            list($status, $alipayUserInfo) = AliApplet::GetUserInfoByAccessToken($accessToken);
            if ($status){
                $gender = $alipayUserInfo['gender'] ??'';
//                $userDetail['nick'] = $alipayUserInfo['nick_name'] ?? $userDetail['nick'];
//                $userDetail['avatar'] = $alipayUserInfo['avatar'] ?? $userDetail['avatar'];
                $userDetail['sex'] = empty($gender) ? 0 : ($gender == 'm' ? 1 : 2);
            }
        }

        $wxUserInfo = by::users()->getWxUserByPhone($phone,false);
        $wxUserId = $wxUserInfo['user_id'] ?? 0;
        if($wxUserId){ //已经有微信用户
            return $this->bindExistingUser($wxUserId, $userMainInfo, $userType);
        }
    
        //6.没有用户信息则注册用户
        return $this->registerNewUser($userDetail, $userMainInfo, $phone, $userType);
    }
    
    /**
     * 绑定已存在的用户
     *
     * @param string $wxUserId 微信用户ID
     * @param array $userMainInfo 用户主要信息
     * @param string $userType 用户类型
     * @return array 返回一个包含状态和消息的数组
     */
    public function bindExistingUser($wxUserId, $userMainInfo, $userType)
    {
        $mallInfo = by::usersMall()->getInfoByUserId($wxUserId);

        $uid            = $mallInfo['uid'] ?? 0;
        $alipayUserSave = [
            'user_id'   => $wxUserId,
            'status'    => 1,
            'openudid'  => $userMainInfo['openudid'],
            'unionid'   => $userMainInfo['unionid'],
            'reg_time'  => time(),
            'user_type' => $userType,
            'uid'       => $uid
        ];
        list($status, $data) = by::UsersPlatformModeModel()->SaveLog($alipayUserSave);
        if (!$status) {
            return [false, '授权失败'];
        }
        return [true, array_merge($alipayUserSave, ['phone' => $mallInfo['phone'] ?? ''])];
    }
    
    /**
     * 注册新用户
     *
     * @param array $userDetail 用户详细信息
     * @param array $userMainInfo 用户主要信息
     * @param string $phone 用户手机号
     * @param string $userType 用户类型
     * @return array 返回一个包含状态和消息的数组
     */
    public function registerNewUser($userDetail, $userMainInfo, $phone, $userType)
    {
        $pdo = by::dbMaster()->beginTransaction();
        $body = $this->prepareBody($userDetail, $userMainInfo, $phone);
        //开始事务
        try{
            //a. iot 平台注册
            list($status,$data) = Mall::factory()->centerAlipayRegister($body);
            if(empty($status)){
                throw new MyExceptionModel('IOT授权失败~');
            }
            $userId = $data['data']['userId'] ?? null;
            $uid = $data['data']['uid'] ?? null;
            
            if(empty($userId)||empty($uid)){
                throw new MyExceptionModel('IOT授权失败~~');
            }
    
            // 保存支付宝用户数据
            $alipayUserSave =[
                'user_id'=>$userId,
                'openudid'=>$userMainInfo['openudid'],
                'unionid'=>$userMainInfo['unionid'],
                'reg_time'=>time(),
                'user_type'=>$userType,
                'uid'=>$uid       
            ];
            list($status,$data) = by::UsersPlatformModeModel()->SaveLog($alipayUserSave);
            if(!$status){
                throw new MyExceptionModel('授权失败~');
            }

            //更新用户扩展表
            $extendData['source'] = by::userExtend()::SOURCE_FROM_ALIPAY;
            $msg = by::userExtend()->saveUserExtend($userId, $extendData);
            if ($msg === false) {
                throw new MyExceptionModel('用户扩展表更新失败~');
            }
        
            // 提交事务
            $pdo->commit();
        
            return [true, $alipayUserSave];
        } catch (\exception $e) {
            // 回滚事务
            $pdo->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.alipayUserAuth');
            return [false, $e->getMessage()];
        }
    }

    /**
     * 准备请求体
     *
     * @param array $userDetail 用户详细信息
     * @param array $userMainInfo 用户主要信息
     * @param string $phone 用户手机号
     * @return array 返回一个过滤后的请求体数组
     */
    private function prepareBody($userDetail, $userMainInfo, $phone)
    {
        $body = [
            'nickName' => $userDetail['nick'] ?? null,
            'name' => $userDetail['nick'] ?? null,
            'phone' => $phone,
            'phoneCode' => 86,
            'sex' => empty($userDetail['sex']) ? 3 : CUtil::uint($userDetail['sex']),
            'realName' => $userDetail['real_name'] ?? $userMainInfo['user_id'],
            'status' => $userDetail['status'] ?? 0,
            'age' => $userDetail['age'] ?? 0,
            'avatar' => $userDetail['avatar'] ?? null,
            'birthday' => !empty($userDetail['birthday']) ? intval($userDetail['birthday'])*1000 : null,
            'openid' => $userMainInfo['openudid'] ?? null,
            'unionid' => $userMainInfo['unionid'] ?? null,
            'time' => !empty($userMainInfo['ctime']) ? date('Y-m-d', $userMainInfo['ctime']) : date('Y-m-d'),
            'source'=> by::userExtend()::SOURCE_FROM_ALIPAY
        ];
    
        return array_filter($body);
    }

    /**
     * @return array
     * @throws RedisException
     * 获取支付宝口令
     */
    public function GetZfbPassword($userId): array
    {
        // 获取最新的支付宝口令信息
        list($status, $zfbPassword) = byNew::ZfbPasswordModel()->getLatestInfo(true);

        // 检查状态和支付宝口令信息
        if (!$status || empty($zfbPassword)) {
            return [false, '暂无支付宝口令~'];
        }

        // 获取配置和二维码信息
        $qrCode = $zfbPassword['qr_code'] ?? '';
        $config = CUtil::getConfig('alipay_applet', 'alipay', MAIN_MODULE);
        $appId  = $config['appId'] ?? '';

        // 组合二维码落地页
        $zfbPassword['qr_code'] = byNew::ZfbPasswordModel()::QRCODE . '?appId=' . $appId . ($qrCode ? '&page=' . $qrCode : '');

        // 异步记录用户链路（手机号、是否授权、昵称、头像、uid） 延时5分钟确保企业微信回调成功，数据落库
        if ($userId && strlen($userId) < 11) {
            \Yii::$app->queue->delay(self::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $userId,
                'scene'   => ''
            ]));
        }

        return [true, $zfbPassword];
    }


    /**
     * @param $userId
     * @param $scene
     * @param  $orderNo
     * @return array
     * 修改节点状态
     */
    public function UpdateUserPath($userId, $scene, $orderNo = null): array
    {
        try {
            // 1.查询当前生效活动ID
            $currentTime = time();

            //如果有订单号则说明为发货节点
            if ($orderNo) {
                $tryOrderInfo = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $orderNo)]);
                $acId         = $tryOrderInfo['ac_id'] ?? 0;
            } else {
                $acId = byNew::ActivityModel()::find()
                    ->select(['id'])
                    ->where(['<', 'start_time', $currentTime])
                    ->andWhere(['>', 'end_time', $currentTime])
                    ->andWhere([
                        'grant_type' => 1,
                        'is_delete'  => 0,
                        'status'     => 0
                    ])
                    ->orderBy(['id' => SORT_DESC])
                    ->scalar();
            }

            if (!$acId) {
                throw new \Exception("未找到当前生效的活动");
            }

            // 2.获取当前节点
            $type = byNew::UserTryPathModel()::SCENE[$scene] ?? '';
            if (empty($type)) {
                throw new \Exception("更新节点不存在");
            }

            $update = [
                $type   => 1,
                'utime' => time()
            ];

            list($status, $data) = byNew::UserTryPathModel()->updateUserPath($userId, $acId, $update);

            if (!$status) {
                return [false, $data];
            }

            // 异步打标签--提交问卷
            $type == 'is_submit_survey' && \Yii::$app->queue->push(new TryBuyUserTagJob([
                'userId'   => $userId,
                'tagScene' => 'SUBMIT_SURVEY'
            ]));

            return [true, $data];
        } catch (\Exception $e) {
            // 记录异常日志
            $error = $e->getMessage() . " | " . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'warn.update_user_path');
            // 返回错误信息
            return [false, '系统错误，更新用户路径失败'];
        }
    }


    public function getShortLink($longLink): array
    {
        try {
            if (empty($longLink)) {
                return [false, '参数错误：长链接不能为空'];
            }

            // 验证URL格式
            if (!filter_var($longLink, FILTER_VALIDATE_URL)) {
                return [false, '参数错误：无效的URL格式'];
            }

            // 准备请求数据（虽然当前未使用，但保留以备将来扩展）
            $data = [
                    [
                            'LongUrl'        => $longLink,
                            'TermOfValidity' => 'long-term',
                    ]
            ];

            // 生成短网址
            list($status, $result) = Dwz::factory()->getDwz($longLink);

            if (!$status) {
                return [false, "生成短链接失败：{$result}"];
            }

            return [true, $result['ShortUrls'] ?? []];
        } catch (\Exception $e) {
            $error = $e->getMessage() . " | " . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.get_short_link');
            // 捕获其他所有异常
            return [false, "系统错误"];
        }
    }

}
