<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\SubsidyActivityService;
use app\modules\goods\models\GoodsRecommendModel;
use app\modules\goods\models\GoodsRecommendUsersModel;
use yii\db\Exception;

/**
 * 商品推荐服务类
 */
class GoodsRecommendService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 用户推荐商品
     * @param int $userId
     * @param int $gid
     * @param int $type 1: 推荐, 0: 取消推荐
     * @param string $uid 用户唯一标识
     * @return array
     * @throws Exception
     */
    public function userRecommendGoods(int $userId, int $gid, int $type, string $uid = ''): array
    {
        // 参数验证
        if (empty($userId) || empty($gid) || !in_array($type, [0, 1], true)) {
            return [false, '参数错误'];
        }

        // 获取商品的sid
        $sid = $this->getGoodsSid($gid);

        $goodsRecommendUsersModel = byNew::GoodsRecommendUsersModel();

        // 添加重复操作检查锁
        $lockKey = "recommend_lock:{$userId}:{$gid}:{$sid}";
        $redis = by::redis('core');
        
        if (!$redis->set($lockKey, 1, ['nx', 'ex' => 10])) {
            return [false, '操作进行中，请稍后再试'];
        }

        try {
            if ($type == 1) {
                // 推荐商品
                $result = $goodsRecommendUsersModel->recommendGoods($userId, $gid, $sid, $uid);
            } else {
                // 取消推荐 - 使用改进版本避免唯一键冲突
                $result = $goodsRecommendUsersModel->cancelRecommendGoodsV2($userId, $gid, $sid);
            }
        } finally {
            // 释放锁
            $redis->del($lockKey);
        }

        return $result;
    }

    /**
     * 获取用户推荐商品列表
     * @param int $userId
     * @param int $page
     * @param int $pageSize
     * @param array $platformIds 平台ID列表
     * @param int $spriceType 价格类型
     * @return array
     * @throws Exception
     */
    public function getUserRecommendList(int $userId, int $page = 1, int $pageSize = 10, array $platformIds = [1], int $spriceType = 0): array
    {
        if (empty($userId) || $userId <= 0) {
            return [false, '用户ID不能为空'];
        }

        $goodsRecommendUsersModel = byNew::GoodsRecommendUsersModel();
        $recommendResult = $goodsRecommendUsersModel->getUserRecommendList($userId, $page, $pageSize);

        if (empty($recommendResult['list'])) {
            return [true, $recommendResult];
        }

        $recommendResult['list'] = $this->__getGoodsInfo($userId,$recommendResult['list'], $spriceType, $platformIds);
        return [true, $recommendResult];
    }


    /**
     * 获取商品信息
     * @param $userId
     * @param $list
     * @param $spriceType
     * @param $platformIds
     * @return array
     * @throws Exception
     */
    private function __getGoodsInfo($userId,$list, $spriceType, $platformIds): array
    {
        if (!is_array($list) || empty($list)) {
            return [];
        }

        // 限制单次处理的商品数量，防止内存溢出
        $maxGoodsCount = 100;
        if (count($list) > $maxGoodsCount) {
            $list = array_slice($list, 0, $maxGoodsCount);
        }

        // 获取商品详细信息
        $gids = array_column($list, 'gid');
        
        // 验证gid数组
        $gids = array_filter($gids, function($gid) {
            return is_numeric($gid) && $gid > 0 && $gid <= 2147483647;
        });

        if (empty($gids)) {
            return [];
        }

        list($totalPoint, $deductionRate) = GoodsService::getInstance()->getPointInfo($userId);

        $goodsList = [];

        foreach ($gids as $gid) {
            try {
                $goodsDetail = by::Gmain()->GetAllOneByGid($gid, true, true, $spriceType, false);
                if (empty($goodsDetail) || $goodsDetail['status'] != 0) {
                    continue;
                }

                // 检查平台权限
                if (!empty($platformIds)) {
                    $goodsPlatformIds = $goodsDetail['platform_ids'] ?? [];
                    if (empty(array_intersect($platformIds, $goodsPlatformIds))) {
                        continue;
                    }
                }

                // 获取平台商品信息
                $platformId = $platformIds[0] ?? 1;
                $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platformId, $goodsDetail);

                // 获取商品推荐次数
//                $sid = $this->getGoodsSid($gid);
//                $gidSid = "{$gid}_{$sid}";
//                $goodsRecommendModel = byNew::GoodsRecommendModel();
//                $recommendTimes = $goodsRecommendModel->getRecommendTimes([$gidSid]);

                // 组装商品信息 - 只返回必要字段，避免敏感信息泄露
                $goodsData = [
                    'gid' => intval($goodsDetail['gid']),
                    'sku' => strval($goodsDetail['sku'] ?? ''),
                    'name' => strval($goodsDetail['name'] ?? ''),
                    'cover_image' => strval($platformInfo['cover_image'] ?? $goodsDetail['cover_image'] ?? ''),
                    'market_image' => strval(empty($goodsDetail['market_image']) ? $goodsDetail['cover_image'] ?? '' : $goodsDetail['market_image']),
                    'mprice' => floatval($goodsDetail['mprice'] ?? 0),
                    'price' => floatval($goodsDetail['price'] ?? 0),
                    'total_point' => floatval($totalPoint),
                    'deduction_rate' => floatval($deductionRate),
                    'subsidy_price' => SubsidyActivityService::getInstance()->getGoodsSubsidyAmount($gid, $goodsDetail['price'] ?? 0)
                    //                    'is_presale' => intval($goodsDetail['is_presale'] ?? 0),
//                    'presale_time' => intval($goodsDetail['presale_time'] ?? 0),
//                    'tids' => is_array($goodsDetail['tids'] ?? []) ? $goodsDetail['tids'] : [],
//                    'is_internal' => intval($goodsDetail['is_internal'] ?? 0),
//                    'is_internal_purchase' => intval($goodsDetail['is_internal_purchase'] ?? 0),
//                    'is_ini' => intval($goodsDetail['is_ini'] ?? 0),
//                    'gini_id' => intval($goodsDetail['gini_id'] ?? 0),
//                    'is_trade_in' => intval($goodsDetail['is_trade_in'] ?? 0),
//                    'goods_recommend_times' => intval($recommendTimes[$gidSid] ?? 0),
                ];

                $goodsList[] = $goodsData;
            } catch (\Exception $e) {
                // 记录错误但继续处理其他商品
                CUtil::writeLog('get_goods_info_error', 0, "gid: {$gid}, error: " . $e->getMessage(), 0, 'error', __METHOD__);
                continue;
            }

        }

        return $goodsList;
    }

    /**
     * 获取商品推荐次数（批量）
     * @param array $gidSids 格式：['gid_sid', ...]
     * @return array 格式：['gid_sid' => total_times, ...]
     * @throws Exception
     */
    public function getGoodsRecommend(array $gidSids): array
    {
        if (empty($gidSids)) {
            return [];
        }

        $goodsRecommendModel = byNew::GoodsRecommendModel();
        return $goodsRecommendModel->getRecommendTimes($gidSids);
    }

    /**
     * 获取单个商品的推荐次数
     * @param int $gid
     * @param int $sid
     * @return int
     * @throws Exception
     */
    public function getSingleGoodsRecommendTimes(int $gid, int $sid = 0): int
    {
        $gidSid = "{$gid}_{$sid}";
        $result = $this->getGoodsRecommend([$gidSid]);
        return $result[$gidSid] ?? 0;
    }

    /**
     * 获取用户对指定商品的推荐状态
     * @param int $userId
     * @param int $gid
     * @return bool
     * @throws Exception
     */
    public function getUserRecommendStatus(int $userId, int $gid): bool
    {
        $sid = $this->getGoodsSid($gid);
        $goodsRecommendUsersModel = byNew::GoodsRecommendUsersModel();
        return $goodsRecommendUsersModel->getUserRecommendStatus($userId, $gid, $sid);
    }

    /**
     * 批量获取用户推荐状态
     * @param int $userId
     * @param array $gids
     * @return array 格式：['gid' => bool, ...]
     * @throws Exception
     */
    public function batchGetUserRecommendStatus(int $userId, array $gids): array
    {
        if (empty($userId) || empty($gids)) {
            return [];
        }

        $goodsList = [];
        foreach ($gids as $gid) {
            $sid = $this->getGoodsSid($gid);
            $goodsList[] = ['gid' => $gid, 'sid' => $sid];
        }

        $goodsRecommendUsersModel = byNew::GoodsRecommendUsersModel();
        $result = $goodsRecommendUsersModel->batchGetUserRecommendStatus($userId, $goodsList);

        // 转换为以gid为键的数组
        $finalResult = [];
        foreach ($gids as $gid) {
            $sid = $this->getGoodsSid($gid);
            $gidSid = "{$gid}_{$sid}";
            $finalResult[$gid] = $result[$gidSid] ?? false;
        }

        return $finalResult;
    }

    /**
     * 为商品列表添加推荐次数字段
     * @param array $goodsList
     * @return array
     * @throws Exception
     */
    public function addRecommendTimesToGoodsList(array $goodsList): array
    {
        if (empty($goodsList)) {
            return $goodsList;
        }
        // 收集所有gid_sid
        $gidSids = [];
        foreach ($goodsList as $goods) {
            $gid = $goods['gid'] ?? $goods['id'] ?? 0;
            $sid = $this->getGoodsSid($gid);
            $gidSids[] = "{$gid}_{$sid}";
        }

        // 批量获取推荐次数
        $recommendTimes = $this->getGoodsRecommend($gidSids);

        // 为每个商品添加推荐次数字段
        foreach ($goodsList as $index => $goods) {
            $gid = $goods['gid'] ?? $goods['id'] ?? 0;
            $sid = $this->getGoodsSid($gid);
            $gidSid = "{$gid}_{$sid}";
            $goodsList[$index]['goods_recommend_times'] = $recommendTimes[$gidSid] ?? 0;
        }

        return $goodsList;
    }

    /**
     * 获取商品的规格ID，暂时默认为0
     * 后续如果需要支持多规格推荐，可以修改这个方法
     * @param int $gid
     * @return int
     */
    private function getGoodsSid(int $gid): int
    {
        // 目前默认返回0，表示商品本身
        // 如果后续需要支持按规格推荐，可以在这里实现获取sid的逻辑
        return 0;
    }

    /**
     * 获取推荐列表（按推荐次数从大到小排序）
     * @param $userId
     * @param int $page
     * @param int $pageSize
     * @param array $platformIds 平台ID列表
     * @param int $spriceType 价格类型
     * @return array
     */
    public function getFriendRecommendList($userId,int $page = 1, int $pageSize = 10, array $platformIds = [1], int $spriceType = 0): array
    {
        $goodsRecommendModel = byNew::GoodsRecommendModel();
        
        // 获取按推荐次数排序的商品推荐数据
        $recommendResult = $goodsRecommendModel->getRecommendAllList($page, $pageSize);

        // 获取商品详细信息
        $recommendResult['list'] = $this->__getGoodsInfo($userId,$recommendResult['list'], $spriceType, $platformIds);

        return [true, $recommendResult];
    }

    /**
     * 验证商品是否可以被推荐
     * @param int $gid
     * @return array
     * @throws Exception
     */
    public function validateGoodsForRecommend(int $gid): array
    {
        $goodsInfo = by::Gmain()->GetOneByGid($gid);
        if (empty($goodsInfo)) {
            return [false, '商品不存在'];
        }

        if ($goodsInfo['status'] != 0) {
            return [false, '商品已下架，无法推荐'];
        }

        if ($goodsInfo['is_del'] == 1) {
            return [false, '商品已删除，无法推荐'];
        }

        return [true, '商品可以被推荐'];
    }
} 