<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\modules\goods\models\BoundUserOrderModel;
use app\modules\main\models\UserBindModel;

/**
 * 被绑定用户订单服务
 */
class BoundUserOrderService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 订单列表
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public function getOrderList(int $user_id, array $params): array
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return [false, '用户不存在'];
        }

        // 获取列表
        $param = [
            'uid'                     => $uid,
            'score_status'            => $params['score_status'] ?? '',
            'order_start_create_time' => $params['order_start_create_time'] ?? '',
            'order_end_create_time'   => $params['order_end_create_time'] ?? ''
        ];
        $items = byNew::BoundUserOrderModel()->getOrderList($param, $params['page'], $params['page_size']);
        // 总数
        $total = byNew::BoundUserOrderModel()->getOrderCount($param);

        // 查询用户信息
        $uids  = array_column($items, 'bound_uid');
        $users = $this->getUserList($uids);

        // 整合数据
        $list = [];
        foreach ($items as $item) {
            $list[] = [
                'nickname'          => $users[$item['bound_uid']]['nick'] ?? '',
                'score'             => $item['score'],
                'score_status'      => $item['score_status'],
                'score_status_name' => $this->getScoreStatusName($item['score_status']),
                'order_create_time' => date('Y-m-d', $item['order_create_time'])
            ];
        }
        return [true, ['list' => $list, 'total' => $total]];
    }

    /**
     * 查询用户信息
     * @param $uids
     * @return array
     * @throws \yii\db\Exception
     */
    private function getUserList($uids): array
    {
        // 查询用户ID
        $user_ids = by::Phone()->getUserIdsByUids($uids);

        // 查询用户信息
        $users = by::users()->getListByUserIds($user_ids, ['user_id', 'nick', 'avatar']);
        $users = array_column($users, null, 'user_id');

        // 整合数据
        $data = [];
        foreach ($user_ids as $uid => $user_id) {
            $data[$uid] = $users[$user_id] ?? [];
        }
        return $data;
    }

    /**
     * 获取积分状态名称
     * @param $score_status
     * @return string
     */
    private function getScoreStatusName($score_status): string
    {
        $status = BoundUserOrderModel::SCORE_STATUS_NAME;
        return $status[$score_status] ?? '';
    }
}