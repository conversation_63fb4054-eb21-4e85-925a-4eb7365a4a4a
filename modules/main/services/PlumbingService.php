<?php

namespace app\modules\main\services;

use app\components\RuiYun;
use app\models\by;
use app\modules\plumbing\models\PlumbingOrderModel;
use yii\db\Exception;

/**
 * 预约服务
 */
class PlumbingService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 保存上门服务的订单
     * @param int $user_id
     * @param array $data
     * @return array
     * @throws Exception
     */
    public function saveServiceOrder(int $user_id, array $data): array
    {
        // 短信码验证
        list($status, $res) = by::model("SmsModel", MAIN_MODULE)->verifyCode($data['phone'], "CODE", $data['verify_code']);
        if (!$status) {
            return [$status, $res];
        }

        // 省市区 瑞云ID
        $address = [
                'province' => $data['province'] ?? '未知省',
                'city'     => $data['city'] ?? '未知市',
                'area'     => $data['county'] ?? '未知区',
        ];

        $plumbingOrder = by::plumbingOrder();
        // 产品信息
        $sn_config = json_decode($data['sn_config'], true);
        list($status, $res) = $this->__checkSn($data['sn'], $sn_config);
        if (!$status) {
            return [false, $res];
        }

        // sn验证
        list($status, $res) = by::product()->match($data['sn'],$user_id);
        if (!$status) {
            return [false, $res];
        }

        $save = [
                'user_id'      => $user_id,
                'order_no'     => $plumbingOrder->createOrderNo(time()),
                'sn'           => $data['sn'],
                'name'         => $data['name'],
                'phone'        => $data['phone'],
                'pid'          => $data['pid'],
                'cid'          => $data['cid'],
                'aid'          => $data['aid'],
                'detail'       => $data['detail'],
                'address'      => json_encode($address),
                'sn_alias'     => $sn_config['sn_alias'],
                'status'       => $plumbingOrder::STATUS['WAIT_SERVICE'],
                'type'         => $data['type'],
                'product_type' => $data['product_type'] ?? 0,
                'context_type' => $data['context_type'],
                'tenant'       => $data['tenant'] ?? PlumbingOrderModel::TENANT['NORMAL'],
                'expect_time'  => $data['expect_time'],
                'update_time'  => time(),
                'ctime'        => time()
        ];

        // 保存数据
        return $plumbingOrder->saveOrder($save);
    }

    /**
     * 验证SN
     * @param $sn
     * @param $sn_arr
     * @return array
     */
    private function __checkSn($sn, $sn_arr): array
    {
        // 产品信息
        if (empty($sn_arr['sn_alias']) || empty($sn_arr['p_sn'])) {
            return [false, '请选择产品信息'];
        }

        // 验证
        $p_sn = $sn_arr['p_sn'];
        $p_sn_len = strlen($p_sn);
        $digits = by::plumbingSn()::SN_FIX_DIGITS;
        if ($p_sn_len < $digits['MIN'] || $p_sn_len > $digits['MAX']) {
            return [false, '该产品信息错误'];
        }

        if (substr($sn, 0, $p_sn_len) != $p_sn) {
            return [false, '该sn编码与机型不匹配'];
        }

        return [true, ''];
    }

    public function serviceAddress(array $arr): array
    {
        $event      = $arr['event'] ?? '';
        $provinceId = $arr['province_id'] ?? '';
        $cityId     = $arr['city_id'] ?? '';

        $params = [
                'province_id' => $provinceId,
                'city_id'     => $cityId,
        ];

        list($success, $data) = RuiYun::factory()->getAddress($event, $params);

        if (!$success) {
            return [false, $data];
        }

        return [true, $data];
    }

}