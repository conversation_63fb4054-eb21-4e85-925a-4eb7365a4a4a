<?php

namespace app\modules\main\services;

use app\models\byNew;
use app\modules\goods\models\GoodsPlatformModel;

/**
 * 商品平台服务
 */
class GoodsPlatformService
{
    const EXPIRE_TIME = 1;

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 查询商品gids是否都在此平台
    public function checkPlatform(array $gids, int $platform): bool
    {
        $status = byNew::GoodsPlatformModel()->exist($gids, $platform);
        return !in_array(false, $status);
    }

    // 获取有效的gids
    public function getEffectiveGids($gids, $platform): array
    {
        if (empty($gids)) {
            return [];
        }

        $result = [];
        $items = byNew::GoodsPlatformModel()->exist($gids, $platform);
        foreach ($items as $gid => $status) {
            if ($status) {
                $result[] = $gid;
            }
        }
        return $result;
    }

    private function groupArrayByKey($array, $key): array
    {
        if (empty($array) || empty($key)) {
            return [];
        }

        $result = [];
        foreach ($array as $item) {
            if (array_key_exists($key, $item)) {
                $groupKey          = $item[$key];
                $result[$groupKey] = $item;
            } else {
                // 处理 $item 中不存在指定 $key 的情况
                continue;
            }
        }
        return $result;
    }

    // 获取当前平台信息（图片）
    public function getCurrentPlatform($platform_id, $goodsData = [])
    {
        if (empty($goodsData) || !isset($goodsData['platforms'])) {
            return [];
        }

        // 获取平台商品封面图
        $platforms = $goodsData['platforms'];
        $platforms = $this->groupArrayByKey($platforms, 'platform_id');

        return $platforms[$platform_id] ?? [];
    }




}