<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use RedisException;
use yii\db\Exception;

/**
 * 商品服务
 */
class GrecommendService
{
    //前台商品推荐service
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $post
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getRecommendList($post): array
    {
        $platformId = $post['platform_id'] ?? 0;
        $userId     = $post['user_id'] ?? 0;
        // 获取推荐的分类ID列表
        $cateIds = by::GoodsRecommend()->getList();
        $result  = [];

        foreach ($cateIds as $cateId) {
            // 获取推荐分类信息
            $aData     = by::GoodsRecommend()->getRecommendInfoByCateId($cateId) ?? [];

            $pCombines = explode(',', $aData['pcombines'] ?? '');
            $gid       = $aData['gid'] ?? 0;
            $oGoods    = by::Gmain()->GetAllOneByGid($gid);

            // 检查平台ID是否匹配  不匹配直接跳过
            $platformIds = $oGoods['platform_ids'] ?? [];
            if (!empty($platformIds) && !in_array($platformId, $platformIds)) {
                continue;
            }

            // 初始化结果数组
            $ret = [
                    'title'     => $aData['title'] ?? '',
                    'tid'       => intval($aData['tid'] ?? ''),
                    'primary'   => [],
                    'secondary' => []
            ];
            $primaryGoodsId = $oGoods['id'] ?? 0;
            // 主推+次推商品ID集合
            $goodsIds = array_merge([$primaryGoodsId],$pCombines);
            $wishRet        = GoodsService::getInstance()->getGoodsIsWish($userId,$goodsIds);

            // 处理主商品信息
            if ($oGoods['status'] == 0) {
                $ret['primary'] = [
                        'id'           => $primaryGoodsId,
                        'name'         => $oGoods['name'] ?? '',
                        'sku'          => $oGoods['sku'] ?? '',
                        'price'        => $oGoods['price'] ?? 0,
                        'tids'         => $oGoods['tids'] ?? [],
                        'is_presale'   => $oGoods['is_presale'] ?? 0,
                        'gini_id'      => $oGoods['gini_id'] ?? 0,
                        'is_ini'       => $oGoods['is_ini'] ?? 0,
                        'gini_info'    => $oGoods['gini_info'] ?? 0,
                        'gini_tag'     => $oGoods['gini_tag'] ?? 0,
                        'gini_etime'   => $oGoods['gini_etime'] ?? 0,
                        'deposit'      => $oGoods['deposit'] ?? 0,
                        'expand_price' => $oGoods['expand_price'] ?? 0,
                        'image'        => $aData['image'] ?? '',
                        'pc_image'     => $aData['pc_image'] ?? '',
                        'h5_image'     => $aData['h5_image'] ?? '',
                        'is_wish'      => $wishRet[$primaryGoodsId], // 是否在心愿单
                ];
            }

            // 处理副商品信息
            foreach ($pCombines as $pCombine) {
                $info = by::Gmain()->GetAllOneByGid(intval($pCombine), true, true, $post['sprice_type'] ?? 0, false);

                if (!$info || $info['status'] ?? 0) {
                    continue;
                }

                // 检查平台ID是否匹配
                $platformIds = $info['platform_ids'] ?? [];
                if (!empty($platformIds) && !in_array($platformId, $platformIds)) {
                    continue;
                }

                $secondaryGoodsId = $info['id'] ?? 0;
                // 获取平台商品信息
                $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platformId, $info);

                $tag        = array_column(by::Gtag()->GetListByGid(intval($pCombine)), 'tid');
                $is_presale = $info['is_presale'] ?? 0;

                $ret['secondary'][] = [
                        'id'           => $secondaryGoodsId,
                        'name'         => $info['name'] ?? '',
                        'sku'          => $info['sku'] ?? '',
                        'price'        => $info['price'] ?? 0,
                        'tids'         => $info['tids'] ?? [],
                        'is_presale'   => $info['is_presale'] ?? 0,
                        'gini_id'      => $info['gini_id'] ?? 0,
                        'is_ini'       => $info['is_ini'] ?? 0,
                        'gini_info'    => $info['gini_info'] ?? 0,
                        'gini_tag'     => $info['gini_tag'] ?? 0,
                        'gini_etime'   => $info['gini_etime'] ?? 0,
                        'deposit'      => $info['deposit'] ?? 0,
                        'expand_price' => $info['expand_price'] ?? 0,
                        'custom_tag'   => $info['custom_tag'] ?? '',
                        'pc_images'    => $platformInfo['images'] ?? '',
                        'image'        => $platformInfo['cover_image'] ?? '',
                        'type'         => $is_presale ?: (in_array(2, $tag) ? 2 : 0), //1预售 2热销
                        'is_wish'      => $wishRet[$secondaryGoodsId]
                ];
            }

            $result[] = $ret;
        }

        return [true, $result];
    }



    /**
     * @param $post
     * @param $page
     * @param $pageSize
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取默认商品列表（PC商城加入购物车推荐+搜索默认推荐）
     */
    public function getDefaultGoodsList($post, $page, $pageSize): array
    {
        $return = [];
        $gIds   = by::Gmain()->GetDefaultGoodsList($page, $pageSize, $post['version'], 0, 0, true, $post['platform_id'], 1);

        foreach ($gIds as $gid) {
            $aGoods = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);

            // 获取平台商品信息
            $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($post['platform_id'], $aGoods);

            // 获取 SKU
            $sku = $aGoods['sku'] ?? '';

            // 获取规格价格信息
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoods, $post['sprice_type'] ?? 0);

            // 组装商品信息
            $return[] = [
                'gid'                  => $aGoods['gid'],
                'sku'                  => $sku,
                'name'                 => $aGoods['name'],
                'images'               => $platformInfo['images'] ?? '',
                'cover_image'          => $platformInfo['cover_image'],
                'mprice'               => $aGoods['mprice'],
                'price'                => $aGoods['price'],
                'is_presale'           => $aGoods['is_presale'] ?? 0,
                'presale_time'         => $aGoods['presale_time'] ?? 0,
                'tids'                 => $aGoods['tids'] ?? [],
                'is_internal'          => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase' => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'               => $aGoods['is_ini'] ?? 0,
                'gini_id'              => $aGoods['gini_id'] ?? 0,
                'is_trade_in'          => $aGoods['is_trade_in'] ?? 0,
            ];
        }

        return $return;
    }

    /**
     * 格式化商品详情，添加推荐次数
     * @param array $gIds
     * @param array $post
     * @return array
     * @throws Exception
     */
    public function formatGoodsDetail(array $gIds, array $post): array
    {
        if (empty($gIds)) {
            return [];
        }

        $return = [];
        
        // 收集所有gid_sid用于批量获取推荐次数
        $gidSids = [];
        foreach ($gIds as $gid) {
            $gidSids[] = "{$gid}_0"; // 默认sid为0
        }
        
        // 批量获取推荐次数
        $recommendTimes = [];
        try {
            $goodsRecommendService = GoodsRecommendService::getInstance();
            $recommendTimes = $goodsRecommendService->getGoodsRecommend($gidSids);
        } catch (\Exception $e) {
            // 获取失败时，推荐次数默认为0
        }

        foreach ($gIds as $gid) {
            $aGoods = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);

            if (empty($aGoods) || $aGoods['status'] != 0) {
                continue;
            }

            // 获取平台商品信息
            $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($post['platform_id'], $aGoods);

            // 获取 SKU
            $sku = $aGoods['sku'] ?? '';

            // 获取规格价格信息
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoods, $post['sprice_type'] ?? 0);

            $gidSid = "{$gid}_0";
            // 组装商品信息
            $return[] = [
                'gid'                  => $aGoods['gid'],
                'sku'                  => $sku,
                'name'                 => $aGoods['name'],
                'images'               => $platformInfo['images'] ?? '',
                'cover_image'          => $platformInfo['cover_image'],
                'mprice'               => $aGoods['mprice'],
                'price'                => $aGoods['price'],
                'is_presale'           => $aGoods['is_presale'] ?? 0,
                'presale_time'         => $aGoods['presale_time'] ?? 0,
                'tids'                 => $aGoods['tids'] ?? [],
                'is_internal'          => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase' => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'               => $aGoods['is_ini'] ?? 0,
                'gini_id'              => $aGoods['gini_id'] ?? 0,
                'is_trade_in'          => $aGoods['is_trade_in'] ?? 0,
                'goods_recommend_times' => $recommendTimes[$gidSid] ?? 0, // 商品推荐次数
            ];
        }

        return $return;
    }

}
