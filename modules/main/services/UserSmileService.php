<?php

namespace app\modules\main\services;

use app\components\Employee;
use app\jobs\EmployeeInviteJob;
use app\models\by;
use app\models\byNew;
use app\modules\common\Singleton;
use app\modules\main\models\UserBindModel;

class UserSmileService
{
    use Singleton;

    public function getList()
    {

    }

    public function getDetail($user_id){
        // 获取用户数据
        $user_data = by::Phone()->getDataByUserId($user_id);
        if (empty($user_data)) {
            return [false, '用户不存在'];
        }

        // 更新uid
        $data = [];
        $data['uid'] = $user_data['uid'];
        $data['user_id'] = $user_id;

        // 获取用户员工详情
        $user = byNew::UserSmileModel()->getInfo($user_data['uid']);
        if ($user){
            $data['is_wxds'] = 1;
            $data['is_tip'] = $user['is_tip'];
            $data['level'] = $user['level'];
            $data['ctime'] = $user['ctime'];
            $data['utime'] = $user['utime'];
        }else{
            $data['is_wxds'] = 0;
        }
        return [true, $data];
    }


    public function apply($user_id){
        // 初始化返回数据
        $data = [
            'level' => 1,
            'is_tip' => 1,
            'ctime' => time(),
            'utime' => time(),
        ];

        // 获取用户数据
        $user_data = by::Phone()->getDataByUserId($user_id);
        if (empty($user_data)) {
            return [false, '用户不存在', []];
        }

        // 更新uid
        $data['uid'] = $user_data['uid'];
        $data['user_id'] = $user_id;
        // 获取用户员工详情
        $user = byNew::UserSmileModel()->getInfo($user_data['uid']);
        if($user){
            return [false, '您已经是微笑大使了', []];
        }
        // 没有数据时，创建追觅大使，先验一下是不是在职员工
        list($status, $employee) = Employee::factory()->employeeInfo(['phone' => $user_data['phone']]);
        if ($status && !empty($employee) && $employee['uid'] != "" && $employee['status'] == Employee::EMPLOYEE_STATUS['NORMAL']){
            
        }else{
            return [false, '您无法直接申请，需完成邀请任务', []];
        }
        
        $status = byNew::UserSmileModel()->saveData($data);
        if(!$status){
            return [false, '创建失败', []];
        }
        
        return [true, '申请成功', []];
    }

    /**
     * 已收到提示
     * @param int $userId
     * @return void
     * @throws Exception
     */
    public function closeTips(int $userId)
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($userId);
        if (empty($uid)) {
            return;
        }

        // 获取用户员工详情
        $user = byNew::UserSmileModel()->getInfo($uid);
        if ($user) {
            $updateData = [
                'is_tip' => 0,
                'utime'  => time()
            ];
            byNew::UserSmileModel()->updateInfo($user['id'], 'id', $updateData);
        }
    }
}