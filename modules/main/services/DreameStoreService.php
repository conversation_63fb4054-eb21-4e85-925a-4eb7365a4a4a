<?php

namespace app\modules\main\services;

use app\components\EventMsg;
use app\components\MemberCenter;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\modules\common\Singleton;
use yii\db\Exception;

/**
 * 追觅小店 - 服务层 - APP端
 */
class DreameStoreService
{
    use Singleton;


    /**
     * 详情
     */
    public function info($user_id, $store_user_id)
    {
        // 获取详情
        $info = $this->getInfo($store_user_id);
        if ($info) {
            if (!$info['store_photo']){
                $info['store_photo'] = by::users()->getDefaultAvatar();
            }
            // 如果不是店主且店铺状态不为1，则返回空数据
            if ($store_user_id != $user_id && 1 != $info['status']) {
                $info = [];
            } else {
                // 积分补偿机制有效期检查
                $deadline = strtotime('2025-09-10');
                if (time() < $deadline) {
                    // 获取开店任务信息
                    list($status, $task) = MemberCenter::factory()->taskInfo($user_id, 'mall/dreame/open_shop');
                    // 任务有效且未完成时触发积分补偿
                    if ($status && isset($task['completed']) && !$task['completed']) {
                        EventMsg::factory()->run('openShop', ['user_id' => $user_id, 'order_no' => '']);
                    }
                }
            }
        }
        return $info;
    }

    /**
     * 申请开店
     * @throws BusinessException
     * @throws Exception
     */
    public function apply($user_id, $params)
    {
        $model = byNew::DreameStoreModel();

        // 开启事务
        $transaction = $model->getDb()->beginTransaction();
        try {
            // 检查是否已申请
            if ($this->getInfo($user_id)) {
                throw new BusinessException('你已经申请过！');
            }

            $data['user_id'] = $user_id;
            $user = by::users()->getOneByUid($user_id);
            $data['store_name'] = ($user['nick'] ?? '') . '的小店';
            $data['store_photo'] = ($user['avatar'] ?: by::users()->getDefaultAvatar());
            $data['status'] = 1;
            $data['max'] = 10;
            $data['reason'] = '';
            $data['is_delete'] = 0;
            $data['ctime'] = time();

            // 处理邀请信息
            if (!empty($params['inviter_id']) && !empty($params['inviter_from'])) {
                $data['inviter_id'] = $params['inviter_id'];
                $data['inviter_from'] = $params['inviter_from'];
            }

            $res = $model->saveData($data);
            if (!$res) {
                throw new BusinessException('店铺创建失败');
            }

            // 处理邀请奖励
            if (!empty($data['inviter_id']) && !empty($data['inviter_from'])) {
                OneYuanSeckillService::getInstance()->getAllDiscountThreeInvite(
                    $data['inviter_from'],
                    $data['inviter_id'],
                    $user_id
                );
            }
            $transaction->commit();
            EventMsg::factory()->run('openShop', ['user_id' => $user_id]);//申请追觅小店得消费金
            return true;
        } catch (\Exception $e) {
            $transaction->rollBack();
            throw $e;
        }
    }

    /**
     * 修改
     */
    public function update($user_id, $params)
    {
        // 获取详情
        $info = $this->getInfo($user_id);

        $model = byNew::DreameStoreModel();
        if(!$info){
            throw new BusinessException('你还没有申请过！');
        }
        if ($info['status'] == 3) {
            throw new BusinessException('店铺被禁用，无法修改');
        }
        $where = ['user_id' => $user_id, 'is_delete' => 0];
        $safeAttributes = ['store_name', 'store_photo'];
        $data = array_intersect_key($params, array_flip($safeAttributes));
        $res = $model->updateInfo($where, $data);
        return $res;
    }

    public function getInfo($user_id)
    {
        $model = byNew::DreameStoreModel();
        $where = ['user_id' => $user_id, 'is_delete' => 0];
        $info =  $model->getInfo($where);
        if ($info) {
            $user = by::users()->getOneByUid($user_id);
            $info['nickname'] = $user['nick'] ?? '';
        }
        return $info;
    }

    public function getActivityUserCount($inviter_id, $inviter_from)
    {
        return byNew::DreameStoreModel()::find()->where(['inviter_id' => $inviter_id, 'is_delete' => 0, 'inviter_from' => $inviter_from])->count();
    }
}