<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\components\XzTradeIn;
use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\SmsService;
use app\modules\back\services\TradeInActivityService;
use app\modules\goods\models\TradeInOrderModel;

/**
 * 以旧换新服务
 */
class TradeInService
{
    private static $instance = null;
    // 以旧换新服务
    private $service;

    private function __construct()
    {
        $this->service = XzTradeIn::factory();
    }

    const EXPIRE_TIME = 3600; // 1h

    public static function getInstance(): self
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 活动
     * @return array
     * @throws \RedisException
     */
    public function getActivity(): array
    {
        // 获取有效的活动
        $activity = byNew::TradeInActivityModel()->getValidActivity();

        if (empty($activity)) {
            return [];
        }

        $extras = json_decode($activity['extras'], true);

        return [
                'name'       => $activity['name'],
                'picture'    => $activity['picture'] ?? '',
                'rules'      => $extras['rules'] ?? [],
                'questions'  => $extras['questions'] ?? [],
                'start_time' => date('Y-m-d H:i:s', $activity['start_time']),
                'end_time'   => date('Y-m-d H:i:s', $activity['end_time']),
        ];
    }

    /**
     * 获取商品列表
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getProductList(): array
    {
        $data = [];

        // 获取商品信息
        $products = $this->getProducts();

        $products = array_column($products, null, 'id');

        $goodsList = by::Gmain()->GetListByGids(array_keys($products));
        foreach ($goodsList as $goods) {
            if ($goods['status'] == 1 || $goods['is_presale'] == 1) { // 下架 或 预售 不展示
                continue;
            }
            $product = [
                    'id'      => $goods['gid'],
                    'sku'     => $goods['sku'],
                    'name'    => $goods['name'],
                    'price'   => $goods['price'],
                    'picture' => $goods['cover_image']
            ];

            $data[] = $product;
        }

        return $data;
    }


    /**
     * 是否为以旧换新商品
     * @param $gid
     * @param int $is_presale
     * @return int
     */
    public function isTradeInProduct($gid, int $is_presale = 0): int
    {
        // 预售商品不参与以旧换新
        if ($is_presale) {
            return 0;
        }

        $goodsList = byNew::TradeInActivityGoodsModel()->getActivityGoodsByGid($gid);

        if (empty($goodsList)) {
            return 0;
        }

        $goodsList = array_column($goodsList, 'gid');
        return in_array($gid, $goodsList) ? 1 : 0;
    }


    // 以旧换新商品价格缓存
    private $tradeInPriceCache = [];

    /**
     * 以旧换新商品价格
     */
    public function tradeInPrice($gInfo, $gid, $sid, $format_price = true): array
    {
        try {
            // 以旧换新商品价格缓存避免重复查询
            $key = $gid . '_' . $sid . '_' . (int) $format_price;
            if (isset($this->tradeInPriceCache[$key])) {
                return [true, $this->tradeInPriceCache[$key]];
            }

            $goodsList = byNew::TradeInActivityGoodsModel()->getActivityGoodsByGid($gid);
            if (empty($goodsList)) {
                return [false, '以旧换新商品不存在'];
            }

            $trade_in_goods = Collection::make($goodsList)->where('sid', $sid)->first();
            if (is_numeric($trade_in_goods['price']) && $trade_in_goods['price'] > 0) {
                $trade_in_price = $format_price ? $trade_in_goods['price'] : by::Gtype0()->totalFee($trade_in_goods['price']);
                if ($gInfo['atype'] == by::Gtype0()::ATYPE['SPECS']) {
                    $gInfo['spec']['price'] = $trade_in_price;
                } else {
                    $gInfo['price'] = $trade_in_price;
                }
                $this->tradeInPriceCache[$key] = $gInfo;
                return [true, $gInfo];
            } else {
                return [false, '商品价格错误'];
            }

        } catch (\Exception $e) {
            CUtil::debug('以旧换新商品价格修改失败：' . $e->getMessage(), 'err.trade_in_change_price');
            return $gInfo;
        }
    }

    // 获取订单信息
    public function getOrderInfo($order_no): array
    {
        $order = byNew::TradeInOrderModel()->getOrderByOrderNo($order_no);
        if ($order) {

            return [
                    'trade_in_order_no' => $order['trade_in_order_no'],
                    'product'           => json_decode($order['product'], true),
                    'status'            => $order['status'],
                    'status_name'       => TradeInOrderModel::STATUS_NAME[$order['status']] ?? ''
            ];
        }
        return [];
    }

    /**
     * 查询回收类目
     * @return array
     * @throws \RedisException
     */
    public function getCateList(): array
    {
        // Redis 缓存实例
        $redis = by::redis();
        $r_key = AppCRedisKeys::getTradeInCateListKey();

        // 尝试从缓存获取数据
        if ($cachedData = $redis->get($r_key)) {
            return json_decode($cachedData, true);
        }

        // 调用服务接口
        $data = $this->service->getCateList();

        $expire_time = $data[0] ? self::EXPIRE_TIME : 3; // 调用失败则3秒后重试
        if (isset($data[1])) {
            $data[1] = $this->filterCategory($data[1]);
        }

        // 缓存数据
        $redis->setex($r_key, $expire_time, json_encode($data));
        return $data;
    }

    /**
     * 查询型号模型
     * @param int $cate_id
     * @return array
     */
    public function getModelList(int $cate_id): array
    {
        list($status, $list) = $this->service->getModelList(['cate_id' => $cate_id]);
        // 处理数据
        if (!$status) {
            return [$status, $list];
        }
        if (in_array($list['type'], ["brand", "none"]) && empty($list['child'])) {
            $list['child'] = [
                    [
                            "id"          => $list['id'],
                            "type"        => "model",
                            "parent_id"   => $list['id'],
                            "name"        => "无具体型号",
                            "image"       => "0",
                            "service_id"  => 0,
                            "has_child"   => "false",
                            "update_time" => $list['update_time']
                    ]
            ];
        }
        return [$status, $list];
    }

    /**
     * 查询模型报价
     * @param int $cate_id
     * @param array $config
     * @return array
     */
    public function getModelPrice(int $cate_id, array $config): array
    {
        list($status, $list) = $this->service->getModelPrice(['cate_id' => $cate_id, 'config' => $config]);
        // 处理数据
        if (!$status) {
            return [$status, $list];
        }
        // 增加线稿图：从配置中取出
        $config             = CUtil::getConfig('trade_in', 'common', MAIN_MODULE);
        $list['host_image'] = $config['host_image'] ?? '';
        return [$status, $list];
    }

    /**
     * 创建回收订单
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public function createOrder(int $user_id, array $params): array
    {
        // 参数验证
        list($status, $res) = $this->validateParamsCreateOrder($params);
        if (!$status) {
            return [false, $res];
        }

        // 使用悲观锁或乐观锁来处理并发，例如：
        // 检查是否已存在相同订单
        if (byNew::TradeInOrderModel()->getOrderByOrderNo($params['order_no'])) {
            return [true, '回收单已创建'];
        }

        // 获取参数
        list($status, $orderData) = $this->getParamsCreateOrder($user_id, $params);
        if (!$status) {
            return [false, $orderData];
        }

        // 开始数据库事务
        $db   = by::dbMaster();
        $tran = $db->beginTransaction();
        try {
            list($status, $ret) = $this->service->createOrder($orderData);
            if (!$status || !isset($ret['order_code'])) {
                return [false, '回收单创建失败'];
            }

            // 商品名
            $product = json_encode([
                    'brand' => $params['item_brand'], // 品牌
                    'cate'  => $params['item_cates'], // 类目
                    'model' => $params['item_model']  // 型号
            ]);

            // 保存订单信息到数据库
            $data = [
                    'order_no'          => $params['order_no'],
                    'trade_in_order_no' => $ret['order_code'],
                    'product'           => $product,
                    'status'            => TradeInOrderModel::STATUS['AWAITING_CONFIRMATION'], // 等待确认
                    'ctime'             => time(),
                    'utime'             => time()
            ];
            byNew::TradeInOrderModel()->store(0, $data);

            //更新订单状态 type=5 以旧换新
            by::Omain()->updateDataByOrderNo($params['order_no'], ['type' => 5]);

            // 提交事务
            $tran->commit();
            return [true, '回收单创建成功'];
        } catch (\Exception $e) {
            // 出现异常，回滚事务
            $tran->rollback();
            return [false, '回收单创建失败: ' . $e->getMessage()];
        }
    }

    /**
     * 查询订单详情
     * @param string $order_no
     * @return array
     */
    public function orderInfo(string $order_no): array
    {
        if (empty($order_no)) {
            return [false, '订单号不能为空'];
        }

        $order = byNew::TradeInOrderModel()->getOrderByOrderNo($order_no);
        if (empty($order)) {
            return [false, '回收单不存在'];
        }

        list($status, $item) = $this->service->orderInfo(['order_code' => $order['trade_in_order_no']]);
        if (!$status) {
            return [$status, $item];
        }

        $config                             = CUtil::getConfig('trade_in', 'common', MAIN_MODULE);
        $item['recover_item']['item_image'] = $config['host_image'] ?? '';
        return [true, $item];
    }

    /**
     * 订单确认
     * @param string $order_code
     * @param int $is_confirm
     * @return array
     */
    public function orderConfirm(string $order_code, int $is_confirm): array
    {
        list($status, $res) = $this->service->orderConfirm(['order_code' => $order_code, 'is_confirm' => $is_confirm]);
        if (!$status) {
            return [false, '订单确认失败'];
        }
        return [true, '订单确认成功'];
    }

    /**
     * 取消回收单
     * @param string $order_no
     * @return array
     * @throws \yii\db\Exception
     */
    public function cancelOrder(string $order_no): array
    {
        if (empty($order_no)) {
            return [false, '订单号不能为空'];
        }

        // 是否可取消订单
        $order = byNew::TradeInOrderModel()->getOrderByOrderNo($order_no);
        if (empty($order) ||
                in_array($order['status'], [
                        TradeInOrderModel::STATUS['ORDER_CLOSED'],
                        TradeInOrderModel::STATUS['ORDER_CANCELED']
                ])) {
            return [true, '无需取消回收单'];
        }

        if (!in_array($order['status'], [
                TradeInOrderModel::STATUS['AWAITING_CONFIRMATION'],
                TradeInOrderModel::STATUS['AWAITING_RETURN'],
                TradeInOrderModel::STATUS['AWAITING_RECEIPT'],
                TradeInOrderModel::STATUS['PENDING_INSPECTION'],
                TradeInOrderModel::STATUS['AWAITING_QUOTE_CONFIRMATION'],
        ])) {
            return [true, '无法取消回收单'];
        }

        // 调用服务接口
        list($status, $res) = $this->service->cancelOrder(['order_code' => $order['trade_in_order_no']]);
        if (!$status) {
            return [false, '回收单取消失败'];
        }

        // 保存订单信息到数据库
        $data = [
                'status' => TradeInOrderModel::STATUS['ORDER_CANCELED'], // 已取消
                'utime'  => time()
        ];
        byNew::TradeInOrderModel()->store($order['id'], $data);

        return [true, '回收单取消成功'];
    }


    /**
     * 修改预约时间
     * @param string $order_code
     * @param string $rese_time
     * @return array
     */
    public function updateReseTime(string $order_code, string $rese_time): array
    {
        list($status, $res) = $this->service->updateReseTime($order_code, $rese_time);

        if (!$status) {
            return [false, '预约时间修改失败'];
        }


        return [true, '预约时间修改成功'];
    }

    /**
     * 更新订单状态
     * @param string $order_no
     * @param string $order_code
     * @param string $status
     * @return array
     * @throws \yii\db\Exception
     */
    public function updateStatus(string $order_no, string $order_code, string $status, string $remark = ''): array
    {
        $order = byNew::TradeInOrderModel()->getOrderByOrderNoAndTradeInOrderNo($order_no, $order_code);
        if (empty($order)) {
            return [false, '回收单不存在'];
        }

        if ($order['status'] == $status) {
            return [true, '回收单状态已同步'];
        }

        $data = [
                'status' => $status,
                'remark' => $remark,
                'utime'  => time()
        ];
        byNew::TradeInOrderModel()->store($order['id'], $data);
        return [true, '回收单状态同步成功'];
    }

    /**
     * 短信通知
     * @param int $id
     * @param int $status
     * @param string $sms_code
     * @param int $limit
     * @return int
     * @throws \yii\db\Exception
     */
    public function tradeInNotify(int $id, int $status, string $sms_code, int $limit): int
    {
        // 通知次数限制
        $notify_limit = 3;
        // 获取订单列表
        $orderList = byNew::TradeInOrderModel()->getOrderListByStatus($id, $status, $notify_limit, $limit);
        if (empty($orderList)) {
            return $id;
        }

        // 获取订单号，并从订单列表中提取手机号
        $orderNos   = array_column($orderList, 'order_no');
        $userIdList = by::Omain()->getListByOrderNos($orderNos, ['user_id']);
        $phoneList  = by::Phone()->getListByUserIds(array_column($userIdList, 'user_id'));
        $phones     = array_unique(array_column($phoneList, 'phone'));

        // 如果没有有效的手机号，则不执行短信发送
        if (empty($phones)) {
            return end($orderList)['id'];
        }

        // 准备短信发送数据
        $data = array_fill_keys($phones, null);

        // 批量发送短信
        SmsService::getInstance()->batchSendSms($sms_code, $data);

        // 更新订单通知次数
        byNew::TradeInOrderModel()->updateNotify($orderNos);

        // 返回最后一个订单的ID
        return end($orderList)['id'];
    }

    /**
     * 设置以旧换新标签
     * @param array $list
     * @return array
     */
    public function setTradeInTag(array $list): array
    {
        if (empty($list)) {
            return [];
        }

        // 获取订单号
        $order_nos = array_column($list, 'order_no');
        // 批量获取以旧换新订单
        $order_list = byNew::TradeInOrderModel()->getOrderListByOrderNos($order_nos);
        // 取出订单号并转换为键值对的形式
        $trade_in_orders = array_flip(array_column($order_list, 'order_no'));

        foreach ($list as $index => $item) {
            $list[$index]['is_trade_in'] = isset($trade_in_orders[$item['order_no']]) ? 1 : 0;
        }
        return $list;
    }

    /**
     * 发放优惠券
     * @param int $userId
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function distributeCoupon(int $userId): array
    {
        // 获取优惠券
        $coupon = $this->getCoupons();
        if (!$coupon) {
            return [
                    'status' => false,
                    'msg'    => '无可发放的优惠券'
            ];
        }

        $current = time();
        if ($current < $coupon['start_time'] || $current > $coupon['end_time']) {
            return [
                    'status' => false,
                    'msg'    => '不在发放时间范围内'
            ];
        }

        $distributedIds = [];
        foreach ($coupon['ids'] as $id) {
            if (!by::userCard()->isReceive($userId, $id)) {
                list($status, $ret) = by::userCard()->backSend($userId, $id, 1);
                if ($status) {
                    $distributedIds[] = $id;
                } else {
                    return [
                            'status' => false,
                            'msg'    => '发放优惠券失败'
                    ];
                }
            }
        }

        if (!empty($distributedIds)) {
            return [
                    'status' => true,
                    'msg'    => $coupon['tips'] ?? '优惠券发放成功'
            ];
        } else {
            return [
                    'status' => false,
                    'msg'    => '优惠券已发放'
            ];
        }
    }

    // 验证参数
    private function validateParamsCreateOrder(array $params): array
    {
        $requiredFields = [
                'cate_code', 'order_no', 'item_brand', 'item_cates', 'item_model',
                'in_express_time', 'aid'
        ];

        foreach ($requiredFields as $field) {
            if (!isset($params[$field]) || $params[$field] == '') {
                return [false, "缺少必要的参数: {$field}"];
            }
        }

        return [true, ''];
    }

    /**
     * 获取参数
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    private function getParamsCreateOrder(int $user_id, array $params): array
    {
        // 获取地址
        $addr = by::Address()->GetOneAddress($user_id, $params['aid'] ?? 0, true);
        if (empty($addr)) {
            return [false, '地址不存在'];
        }
        $data = [
                'cate_code'       => $params['cate_code'],
                'order_no'        => $params['order_no'],
                'name'            => $addr['nick'],
                'prov_name'       => $addr['province'],
                'city_name'       => $addr['city'],
                'area_name'       => $addr['area'],
                'address'         => $addr['detail'],
                'mobile'          => $addr['phone'],
                'remark'          => $params['trade_in_remark'],
                'item_brand'      => $params['item_brand'],
                'item_cates'      => $params['item_cates'],
                'item_model'      => $params['item_model'],
                'in_express_time' => date('Y-m-d H:i:s', $params['in_express_time']),
                'in_express'      => 0, // 默认0
                'enter_time'      => date('Y-m-d H:i:s'), // 下单时间
                'item_picture'    => '',
        ];
        return [true, $data];
    }

    /**
     * 获取商品信息
     * @return array
     * @throws \RedisException
     */
    private function getProducts(): array
    {
        // 获取有效的活动
        $activity = byNew::TradeInActivityModel()->getValidActivity();

        if (empty($activity)) {
            return [];
        }

        $products  = json_decode($activity['products'] ?? '', true);
        $goodsList = $activity['goodsList'] ?? [];
        // 活动商品
        return !empty($products) ? $products : $goodsList;
    }

    /**
     * 获取优惠券信息
     * @return array
     * @throws \RedisException
     */
    private function getCoupons(): array
    {
        // 获取有效的活动
        $activity = byNew::TradeInActivityModel()->getValidActivity();

        if (empty($activity)) {
            return [];
        }

        // 活动优惠券
        return json_decode($activity['coupons'] ?? '[]' , true);
    }

    /**
     * 分类列表
     * @return mixed|string
     */
    public function getTabList()
    {
        return byNew::TradeInActivityGoodsModel()->getTabList();
    }

    /**
     * 以旧换新列表
     * @param $categoryId
     * @return array
     */

    public function getList($categoryId): array
    {

        try {
            $page     = \Yii::$app->request->post('page', 1);
            $pageSize = \Yii::$app->request->post('page_size', 10);

            $page_cache_key = $categoryId . '_' . $page . '_' . $pageSize;
            $data           = byNew::TradeInActivityGoodsModel()->getActivityGoodsByCateId($page_cache_key, $categoryId);

            $data = $data['data'] ?? [];
            // 过滤goods.status= 1下架的商品
            $data['list'] = Collection::make($data['list'] ?? [])->filter(function ($item) {
                return $item['goods']['status'] == 0;
            })->values()->all();

            return $data;
        } catch (\Exception $e) {
            return [];
        }
    }


    /**
     * 分类过滤
     */

    public function filterCategory($data): array
    {
        try {
            // 分类过滤
            $map = ['清洁电器' => ['扫地机器人', '洗地机', '吸尘器'], '个护健康' => ['电吹风']];
            return Collection::make($data)->map(function ($item) use ($map) {
                if (isset($map[$item['name']])) {
                    $item['child'] = Collection::make($item['child'])->map(function ($goods) use ($map, $item) {
                        return in_array($goods['name'], $map[$item['name']]) ? $goods : [];
                    })->filter()->values()->all();
                    return $item;
                }
                return [];
            })->filter()->values()->all();
        } catch (\Exception $e) {
            return $data;
        }
    }

}