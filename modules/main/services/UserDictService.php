<?php

namespace app\modules\main\services;

use app\models\byNew;
use app\modules\main\models\UserDictModel;

class UserDictService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    /**
     * 保存用户自定义数据
     * @param int    $user_id
     * @param string $dict_key
     * @param string $content
     * @return bool
     */
    public function saveDictData($user_id, $dict_key, $content = "")
    {
        $dictModel = byNew::userDictModel();
        $info = $dictModel->getByUnique($user_id, $dict_key);

        if (empty($info)) {
            $info = new UserDictModel();
            $info->create_time = time();
        }

        $info->setAttributes([
            "user_id"     => (int)$user_id,
            "dict_key"    => $dict_key,
            "content"     => $content ?? "",
            "update_time" => time()
        ], false);

        return $info->save();
    }

    /**
     * 获取用户自定义数据
     * @param int    $user_id
     * @param string $dict_key
     * @return string
     */
    public function getDictContent($user_id, $dict_key)
    {
        $dictModel = byNew::userDictModel();
        $info = $dictModel->getByUnique($user_id, $dict_key);
        return $info["content"] ?? "";
    }

}