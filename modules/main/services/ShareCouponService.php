<?php

namespace app\modules\main\services;

use app\components\AppWRedisKeys;
use app\components\WeiXin;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\enums\shareCoupon\ShareCouponEnum;
use yii\db\Exception;

class ShareCouponService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    private function __getShareCouponCodeKey($code):string
    {
        return AppWRedisKeys::GetShareCouponCode($code);
    }

    private function __getShareUrlLinkKey($path,$query):string
    {
        return AppWRedisKeys::GetShareCouponUrlLink($path,$query);
    }

    CONST CODE_KEY = 'share_coupon';
    CONST PHONE_ENCRYPT_KEY ='%dreame_coupon%';
    CONST SHARE_PATH ='pagesA/coupon/share';


    public function ShareList($user_id,$config,$drawLog = true)
    {
        $acId = $config['ac_id']?? '';
        if(empty($acId)) return [false,'活动不存在！'];
        list($s,$list) = by::activityConfigModel()->getActivityDetail($acId);
        if(!$s) return [false,$list];
        $list['btn_status'] = by::activityConfigModel()::COUPON_BTN_STATUS['NO_AUTH']; //领取按钮状态（不可领取）
        //获取限制的sn数据
        $share_sns = $config['coupon_share_sns'] ?? [];
        $productIds = [];
        if($share_sns){
            foreach ($share_sns as $share_sn) {
                $share_sn = trim($share_sn);
                $productInfos = by::product()->GetListBySn($share_sn) ?? [];
                $productIds = array_merge($productIds,array_column($productInfos,'id'));
            }
        }
        if(empty($productIds)) return [false,'没有可分享的产品！'];

        //初始化某些数据
        $list['share_status'] = ShareCouponEnum::SHARE_STATUS['CAN_SHARE'];
        $list['share_remain_num'] = 0;
        $list['draw_log'] = [];

        if(strlen($user_id)>12 || empty(intval($user_id))){
            $list['share_status'] = ShareCouponEnum::SHARE_STATUS['NO_AUTH'];
            return [true,$list];
        }
        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = by::activityConfigModel()->ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }
        $list = $this->BindUserData($user_id, $list, $productIds, $config);
        $drawLog && $list = $this->BindUserDrawLog($user_id, $list, $config);
        //删除并发标识
        by::activityConfigModel()->ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
        return [true, $list];
    }

    //绑定用户数据
    private function BindUserData($user_id,$list,$productIds,$config)
    {
        $acId = $config['ac_id']?? '';
        $limitNum = $config['coupon_share_num']?? 3;

        //判断是否有权限分享，没有权限则转入产品注册页面
        $count = by::productReg()->GetRegCountByProductId($user_id,$productIds);
        if ($count <= 0) {
            $list['share_status'] = ShareCouponEnum::SHARE_STATUS['NO_REGISTER'];
            return $list;
        }
        //分享次数
        $list['share_remain_num'] = $this->__checkShareNum($user_id,$acId,$limitNum);
        if ($list['share_remain_num'] <= 0) {
            $list['share_status'] = ShareCouponEnum::SHARE_STATUS['NO_SHARE'];
            $list['share_remain_num'] = 0;
            return $list;
        }

        return $list;
    }


    public function GetShareCouponCode($user_id,$config,$remark='')
    {
        list($s,$list) = $this->ShareList($user_id,$config,false);
        if(!$s) return [false,$list];
        if($list['share_status'] != ShareCouponEnum::SHARE_STATUS['CAN_SHARE'] || empty($list['share_remain_num'])){
            return [false,'信息错误，无法生成分享券！'];
        }
        //生成分享券
        $code = CUtil::encrypt('U'.$user_id.'A'.$config['ac_id'],self::CODE_KEY);
        $r_key = $this->__getShareCouponCodeKey($code);
        $data = [
            'share_id'         => $user_id,
            'ac_id'            => $config['ac_id'],
            'remark'           => $remark,
            'coupon_share_num' => $config['coupon_share_num'] ?? 3,
            'share_time'       => time(),
        ];
        $redis = by::redis();
        $redis->set($r_key, json_encode($data));
        CUtil::ResetExpire($r_key,24*60*60);
        $path = self::SHARE_PATH;
        $query['share-coupon-code'] = $code;
        $url = $this->__createUrlLink($path, $query);
        $data['share-coupon-code'] = $code;
        $data['url'] = $url;
        return [true, $data];
    }


    private function __createUrlLink($path, $query): string
    {
        $redis    = by::redis();
        $key      = $this->__getShareUrlLinkKey($path, serialize($query));
        $queryStr = http_build_query($query, '', '&', PHP_QUERY_RFC3986);
        $url      = $redis->get($key);
        if (!$url) {
            //生产分享链接
            list($s, $url) = WeiXin::factory()->UrlLink($path, $queryStr);
            $redis->set($key, $url);
            CUtil::ResetExpire($key, !$s ? 10 : 24 * 60 * 60);
        }
        return $url;
    }

    //绑定用户领取记录
    private function BindUserDrawLog($user_id,$list,$config)
    {
        $acId = $config['ac_id']?? '';
        $drawLogModel = byNew::ShareCouponDrawLogModel();
        //获取分享者手机号
        $phone = by::phone()->GetPhoneByUid($user_id);
        $md5phone = CUtil::encrypt($phone,self::PHONE_ENCRYPT_KEY);
        $list['draw_log'] = $drawLogModel->GetDrawLogList(['md5phone' => $md5phone,'ac_id' => $acId], 1, 100);
        if($list['draw_log']){
            $userModel = by::users();
            foreach ($list['draw_log'] as $k => $v) {
                $list['draw_log'][$k]['user_info'] = $userModel->getOneByUid($v['user_id'] ?? 0) ?? [];
            }
        }

        return $list;
    }


    //分享券领取记录
    public function DrawList($user_id,$share_code): array
    {
        $r_key = $this->__getShareCouponCodeKey($share_code);
        $json = by::redis()->get($r_key);
        $config = (array)json_decode($json,true);
        if(empty($config)) return [false,'分享链接已失效！'];
        $acId = $config['ac_id']?? '';
        $share_id = $config['share_id']?? '';
        $share_time = $config['share_time']?? 0;
        $coupon_share_num = $config['coupon_share_num']?? 3;
        if(empty($acId) || empty($share_id)){
            return [false,'分享链接已失效！'];
        }
        list($s,$list) = by::activityConfigModel()->getActivityDetail($acId,$user_id);
        if(!$s) return [false,$list];

        //初始化某些数据
        $list['share_remain_num'] = 0;
        $list['share_id'] = $share_id;
        $list['share_time'] = $share_time;
        $list['share_info'] = by::users()->getOneByUid($share_id);

        if(strlen($user_id)>12 || empty(intval($user_id))){
            $list['btn_status'] = by::activityConfigModel()::COUPON_BTN_STATUS['NO_AUTH'];
            return [true,$list];
        }


        if($user_id == $share_id) {
            $list['btn_status'] = by::activityConfigModel()::COUPON_BTN_STATUS['SAME_USER'];
            return [true,$list];
        }

        //判断分享数量是否超限
        if ($this->__checkShareNum( $share_id, $acId, $coupon_share_num) <= 0) {
            $list['btn_status'] = by::activityConfigModel()::COUPON_BTN_STATUS['OVER_TIMES']; //领取超限
        }
        return [true, $list];
    }

    public function Draw($user_id,$share_code): array
    {
        list($s,$list) = $this->DrawList($user_id,$share_code);
        if(!$s) return [false,$list];

        //领取券
        $ids = $list['id'] ?? 0;
        $r_id = $list['share_id']?? 0;

        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__,$ids,$r_id);
        list($anti) = by::activityConfigModel()->ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }

        if($list['btn_status'] == by::activityConfigModel()::COUPON_BTN_STATUS['SAME_USER']){
            return [false,'您不能领取自己分享的优惠券！'];
        }

        if($list['btn_status'] == by::activityConfigModel()::COUPON_BTN_STATUS['OVER_TIMES']){
            return [false,'抱歉，优惠券已被其他人领光，无法领取！'];
        }

        if($list['btn_status']!= by::activityConfigModel()::COUPON_BTN_STATUS['ONCE_DRAW']){
            return [false,'您已领取过该优惠券，无法重复领取！'];
        }


        //判断是否满足活动条件
        $restrictionResult = by::activityConfigModel()->activityRestriction($ids, $user_id);
        if (!$restrictionResult) {
            return [false,'您不满足活动条件！'];
        }
        $coupon = $list['coupon']?? [];
        if(empty($coupon)) return [false,'优惠券已被领光'];
        $market_ids = array_column($coupon,'market_id');
        $market_ids = implode(',', $market_ids);
        if (empty($market_ids)) {
            return [false,'优惠券已被领光！'];
        }

        //领取校验
        if(!$this->__checkDrawNum($user_id,$ids)){
            return [false,'您已领取过该优惠券，无法重复领取！'];
        }

        $db        = by::dbMaster();
        $trans     = $db->beginTransaction();
        try {
            //领取优惠券
            list($status, $msg) = by::activityConfigModel()->userCommonDraw($user_id, $ids, $market_ids, $r_id);
            if(!$status) {
                throw new Exception($msg);
            }
            //领取记录
            $phone = by::Phone()->GetPhoneByUid($r_id);
            $md5phone = CUtil::encrypt($phone,self::PHONE_ENCRYPT_KEY);
            $arr = [
                'user_id'    => $user_id,
                'ac_id'      => $ids,
                'share_id'   => $r_id,
                'md5phone'   => $md5phone,
                'share_time' => $list['share_time'] ?? 0,
            ];
            $s = byNew::ShareCouponDrawLogModel()->SaveDrawLog($arr);
            if(!$s) {
                throw new Exception('领取记录保存失败！');
            }
            $trans->commit();
        }catch  (Exception $e){
            $trans->rollBack();
            CUtil::debug($e->getMessage(),'err.draw_share_coupon');
            return [false,"领取失败！"];
        }
        //删除并发标识
        by::activityConfigModel()->ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');
        return [true, 'ok'];
    }

    private function __checkDrawNum($user_id,$ac_id): bool
    {
        $model = byNew::ShareCouponDrawLogModel();
        $num = $model->GetDrawLogListCount(['user_id' => $user_id,'ac_id' => $ac_id]);
        if($num > 0){
            return false;
        }
        return true;
    }

    private function __checkShareNum($share_id,$ac_id,$limit_num): string
    {
        $model = byNew::ShareCouponDrawLogModel();
        $phone = by::Phone()->GetPhoneByUid($share_id);
        $md5phone = CUtil::encrypt($phone,self::PHONE_ENCRYPT_KEY);
        $num = $model->GetDrawLogListCount(['md5phone' => $md5phone,'ac_id' => $ac_id]);
        $remain = bcsub($limit_num, $num);
        return intval($remain);
    }


}
