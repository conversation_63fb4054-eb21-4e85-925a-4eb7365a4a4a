<?php

namespace app\modules\main\services;

use app\models\by;
use yii\db\Exception;

class WikiService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $pid
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 获取话题下任何五条数据
     */
    public function getRandomData($pid): array
    {
        if (empty($pid)) {
            return [false, '参数错误'];
        }
        $data = by::Wdtopic()->GetDidByTid($pid);
        $ret  = [];
        foreach ($data as $datum) {
            $info  = by::Wdynamic()->GetInfoByDid($datum['id'], true);
            $ninfo = [
                'clicks'      => $datum['clicks'] ?? 0,
                'cover_image' => $info['cover_image'] ?? '',
                'title'       => $info['title'] ?? '',
                'did'         => $info['did'] ?? '',
            ];
            $ret[] = $ninfo;
        }
        return [true, $ret];
    }
}