<?php

namespace app\modules\main\services;

use app\components\IotDs;
use app\components\Survey;
use app\jobs\SurveyRecordJob;
use app\jobs\TryBuyUserPathJob;
use app\jobs\TryBuyUserTagJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\enums\tryBeforeBuying\ActivityConfigEnum;
use Exception;
use RedisException;

class WaresActivityService
{
    private static $_instance = NULL;

    public $activityModel;

    const QUERY_CLEAN_MSG_TIME = 3600*24*30;

    private function __construct()
    {
        $this->activityModel = byNew::ActivityModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @throws Exception
     * 活动列表
     */
    public function GetActivityList(array $array, $page, int $page_size): array
    {
        try {
            $data = $this->activityModel->getList($array, $page, $page_size, true);

            if (!empty($data['list'])) {
                foreach ($data['list'] as &$value) {
                    $activityStatus           = $this->CheckActivityStatus($value['start_time'], $value['end_time'],$value['status']);
                    $value['activity_status'] = $activityStatus;
                }
            }

            return [true, $data];
        } catch (Exception $e) {
            // 记录错误信息
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'get_wares_activity_list_error');

            return [false, "获取活动列表失败"];
        }
    }

    //检验活动状态
    public function CheckActivityStatus($startTime, $endTime, $status = 0): int
    {
        $currentTime = time();

        // 检查当前时间是否早于活动开始时间。
        if ($currentTime < $startTime) {
            return 1; // 活动尚未开始。
        }

        // 检查当前时间是否早于活动结束时间。
        if ($currentTime < $endTime) {
            // 如果状态下架，认为活动已结束。
            return $status ? 3 : 2; // 如果状态下架，则返回活动已结束，否则返回活动进行中。
        }

        // 如果以上条件都不满足，那么活动已结束。
        return 3;
    }



    //获取活动tips
    private function GetActivityTips($activityStatus, $startTime, $endTime): array
    {
        $buttonText = ActivityConfigEnum::ACTIVITY_BUTTON_TEXT[$activityStatus] ?? '招募已结束';
        switch ($activityStatus) {
            case 1:
                $tipsText = "将在" . date("Y年m月d日H:i", $startTime) . "开启招募";
                break;
            case 2:
            case 4:
                $timeDiff = $endTime - time();
                if ($timeDiff < 0) {
                    $tipsText = "试用活动申请已结束";
                } else {
                    $days          = floor($timeDiff / (60 * 60 * 24));
                    $hours         = floor(($timeDiff % (60 * 60 * 24)) / (60 * 60));
                    $minutes       = floor(($timeDiff % (60 * 60)) / 60);
                    $seconds       = sprintf('%02d', $timeDiff % 60);
                    $formattedDays = ($days == 1) ? '1' : $days;

                    $tipsText = $timeDiff;//"距离申请结束时间仅剩{$formattedDays}天{$hours}:{$minutes}:{$seconds}";
                }
                break;
            case 3:
                $tipsText = "试用活动申请已结束";
                break;
            case 5:
                $tipsText = "体验中，享受您的体验之旅吧~";
                break;
            case 6:
                $tipsText = "正在审核您的申请，请耐心等待";
                break;
            case 7:
                $tipsText = "很遗憾，您未满足本次活动条件";
                break;
            default:
                $tipsText = '试用活动申请已结束';
                break;
        }

        return [
            'button_text' => $buttonText,
            'tips_text'   => $tipsText
        ];
    }


    /**
     * @param $acId
     * @param $userId
     * @return array
     * @throws RedisException
     * @throws \yii\db\Exception
     * 活动详情
     */
    public function GetActivityInfo($acId, $userId): array
    {
        if (empty($acId)) {
            return [false, "活动ID不能为空"];
        }

        //获取活动信息
        $activityInfo = $this->activityModel->getActivityDetail($acId, [], true);

        if (empty($activityInfo)) {
            return [false, "活动不存在"];
        }

        //判断活动状态
        $activityStatus                  = $this->CheckActivityStatus($activityInfo['start_time'], $activityInfo['end_time'],$activityInfo['status']);
        $activityInfo['activity_status'] = $activityStatus;

        //  判断是否已经参加活动 优先级高于招募名额是否已满  考虑游客模式下的情况 游客模式下默认展示立即申请按钮
        if (strlen($userId) < 11 || !empty(CUtil::uint($userId))) {
            //  判断是否下过先试后买订单 根据$userId和$acId查询订单表
            $bool = byNew::UserOrderTry()->CheckUserHasTry($userId,$acId);
            if ($bool) {
                $activityStatus = 5;//活动状态 已申请
            }
        }

        // 判断问卷是否人工审核状态
        $surveyData = byNew::SurveyRecordModel()->getSurveyRecord(['user_id' => $userId, 'ac_id' => $acId]);
        //如果人工审核 才会有审核状态
        if ($surveyData) {
            $auditStatus   = $surveyData['audit_status'] ?? 0;
            $auditMessages = [
                byNew::SurveyRecordModel()::AUDIT_STATUS['WAIT_AUDIT']    => 6,
                byNew::SurveyRecordModel()::AUDIT_STATUS['AUDIT_NO_PASS'] => 7
            ];

            if (isset($auditMessages[$auditStatus])) {
                $activityStatus = $auditMessages[$auditStatus];
            }
        }
        //个人状态
        $activityInfo['my_status']      = $activityStatus;
        $texts                          = $this->GetActivityTips($activityStatus, $activityInfo['start_time'], $activityInfo['end_time']);
        $activityInfo['button_text']    = $texts['button_text'];
        $activityInfo['tips_text']      = $texts['tips_text'];
        $activityInfo['is_fill_survey'] = empty($surveyData) ? 1 : 0; //是否填写问卷 0不填写 1填写



        // 异步更新用户链路 进入支付宝
        if ($userId && strlen($userId) < 11) {
            \Yii::$app->queue->delay(DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $userId,
                'scene'   => 'enter_zfb'
            ]));
        }

        return [true, $activityInfo];
    }

    /**
     * @throws \yii\db\Exception
     * @throws RedisException
     */
    public function ActivityApply($acId, $userId): array
    {
        // 1.判断活动是否存在
        $activityInfo = $this->activityModel->getActivityDetail($acId, [], true);
        if (empty($activityInfo)) {
            return [false, "活动不存在"];
        }

        // 2.判断商品状态
        if ($activityInfo['goods']['status'] ?? '' == 1 || $activityInfo['goods']['is_del'] ?? '' == 1){
            return [false,'商品已下架'];
        }

        // 3.判断用户是否是黑名单
        $userDetail = byNew::UserTryModel()->getInfoByUserId($userId);
        if (!empty($userDetail) && $userDetail['status'] == 1) {
            return [false, '抱歉，您暂不可申请试用'];
        }

        // 4.判断是否是老用户
        if($this->InterceptActivityUser($userId,$activityInfo['goods']['label'] ?? 11)){
            return [false, '您不符合活动条件'];
        }

        // 5.验证是否申请过
        $bool = byNew::UserOrderTry()->CheckUserHasTry($userId,$acId);
        if ($bool) {
            return [false, '抱歉，您暂不可申请试用'];
        }

        // 6. 判断问卷是否人工审核
        $surveyData = byNew::SurveyRecordModel()->getSurveyRecord(['user_id' => $userId, 'ac_id' => $acId]);
        if ($surveyData) {
            $auditStatus   = $surveyData['audit_status'] ?? 0;
            $auditMessages = [
                byNew::SurveyRecordModel()::AUDIT_STATUS['WAIT_AUDIT']    => '正在审核您的申请，请耐心等待',
                byNew::SurveyRecordModel()::AUDIT_STATUS['AUDIT_NO_PASS'] => '很遗憾，您未满足本次活动条件'
            ];

            if (isset($auditMessages[$auditStatus])) {
                return [false, $auditMessages[$auditStatus]];
            }
        }

        // 7.判断是否拥有参加活动的权限
        $joinCondition = $activityInfo['join_condition'] ?? 0;
        $acIds         = explode(",", $activityInfo['ac_ids'] ?? '');
        switch ($joinCondition) {
            case 0: // 无限制
                $isPass = true;
                break;
            case 1: // 申请过活动的 不可参加
                $isPass = !$this->hasAppliedActivities($userId, $acIds);
                break;
            case 2: // 通过过问卷的活动 不可参加
                $isPass = !$this->hasPassedSurveys($userId, $acIds);
                break;
            case 3: // 体验过机器的活动 不可参加
                $isPass = !$this->hasTriedMachines($userId, $acIds);
                break;
            default:
                $isPass = false;
                break;
        }

        return $isPass ? [true, "申请成功"] : [false, "您不符合本次活动"];
    }

    /**
     * @param $userId
     * @param $acIds
     * @return bool
     * 申请过活动的 不可参加
     */
    private function hasAppliedActivities($userId, $acIds): bool
    {
        $surveyRecordModel = byNew::SurveyRecordModel();
        foreach ($acIds as $ac) {
            $surveyData = $surveyRecordModel->getSurveyRecord(['user_id' => $userId, 'ac_id' => $ac]);
            if ($surveyData) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $userId
     * @param $acIds
     * @return bool true 有问卷记录 false 无问卷记录
     * 通过过问卷的活动 不可参加
     */
    private function hasPassedSurveys($userId, $acIds): bool
    {
        $surveyRecordModel = byNew::SurveyRecordModel();
        foreach ($acIds as $ac) {
            $surveyData = $surveyRecordModel->getSurveyRecord(['user_id' => $userId, 'ac_id' => $ac]);
            if ($surveyData && $surveyData['score'] >= $surveyData['pass_score']) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $userId
     * @param $acIds
     * @return bool
     * @throws \yii\db\Exception
     * 体验过机器的活动 不可参加
     */
    private function hasTriedMachines($userId, $acIds): bool
    {
        $userOrderTryModel = byNew::UserOrderTry();
        foreach ($acIds as $ac) {
            if ($userOrderTryModel->CheckUserHasTry($userId, $ac)) {
                return true;
            }
        }
        return false;
    }



    /**
     * @param $userId
     * @param $formKey
     * @param $activityId
     * @return array
     * @throws \yii\db\Exception
     * 查询用户问卷是否达标
     */
    public function QuerySurveyResult($userId, $formKey, $activityId): array
    {
        // 获取活动信息
        $activityData = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($activityId);
        if (empty($activityData)) {
            return [false, "活动不存在"];
        }

        $passMark = $activityData['pass_mark'] ?? 0;

        // 查询问卷结果
        list($status, $data) = Survey::factory()->querySurveyResult($userId, $formKey, $activityId);
        if (!$status || empty($data)) {
            return [false, "查询失败"];
        }

        $myMark = $data['totalScore'] ?? 0;
        // 是否通过 1通过 0未通过
        $isPass = ($myMark >= $passMark) ? 1 : 0;

        // 审核状态
        $auditStatus = byNew::ActivityTypeModel()::IS_AUDIT['no_audit'] ?? 0;
        //不需要人工审核
        if ($activityData['is_audit'] == $auditStatus) {
            $auditStatus = $isPass ? 1 : 2; // 1: 审核通过, 2: 审核不通过
        } else {
            //需要人工审核
            $auditStatus = $isPass ? 0 : 2; // 0: 待审核, 2: 审核不通过
        }

        //插入问卷记录
        list($status, $result) = byNew::SurveyRecordModel()->SaveLog($userId, $passMark, $auditStatus, $data);
        if (!$status) {
            return [false, '查询失败'];
        }

        //通过问卷
        if ($isPass) {
            // 异步更新用户链路 通过问卷
            \Yii::$app->queue->delay(DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $userId,
                'scene'   => 'pass_survey'
            ]));
        }

        // 没有配置人工审核并且通过问卷 才会更新节点 企业微信打标签 否则 在人工审核时操作
        if ($activityData['is_audit'] == byNew::ActivityTypeModel()::IS_AUDIT['no_audit']) {
            // 异步更新用户链路 人工审核通过（因为没有配置人工审核）
            \Yii::$app->queue->delay(DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $userId,
                'scene'   => 'manual_audit'
            ]));
            // 异步打标签
            $tagScene = ($myMark >= $passMark) ? 'PASS_SURVEY' : 'NO_PASS_SURVEY';
            \Yii::$app->queue->push(new TryBuyUserTagJob([
                'userId'   => $userId,
                'tagScene' => $tagScene,
            ]));
        }

        return [true, [
            'my_score'  => $myMark,
            'pass_mark' => $passMark,
            'is_pass'   => $isPass
        ]];
    }


    /**
     * @param $userId
     * @param $label
     * @return bool
     * @throws \yii\db\Exception
     * 判断是否要拦截试用用户
     */
    public function InterceptActivityUser($userId,$label = 11)
    {
        if($label == 11){
            $s = $this->queryCleanRecordByUserId($userId);
            if($s) return true;
        }
        return false;
    }

    /**
     * @param $userId
     * @return bool
     * @throws \yii\db\Exception
     * 判断用户一个月内是否有清扫记录  by::Gtag()::TAG_IOT_MODEL
     */
    public function queryCleanRecordByUserId($userId): bool
    {
        $st        = false;
        $endTime   = time();
        $startTime = $endTime - self::QUERY_CLEAN_MSG_TIME;

        // 获取用户UID
        $userInfo = by::usersMall()->getMallInfoByUserId($userId);

        $uid = $userInfo['uid'] ?? "";
        if ($uid) {
            list($status, $data) = IotDs::factory()->run('cleanmodel', ['uids' => [$uid], 'startTime' => $startTime, 'endTime' => $endTime]);
            if ($status && isset($data[0])) {
                $st = true;
            }
        }
        return $st;
    }


}
