<?php

namespace app\modules\main\services;

use app\components\ExpressTen;

class ExpressService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 查找物流信息
     * @param $number
     * @param $company
     * @return array
     */
    public function find($number, $company): array
    {
        list($status, $express) = ExpressTen::factory()->GetInfo($number, $company);
        if (!$status) {
            return [false, $express];
        }
        // 处理查询的信息
        $res = [];
        if (isset($express['data'])) {
            $res = $express['data'];
        }

        return [true, $res];
    }

}