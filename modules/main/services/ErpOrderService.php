<?php

namespace app\modules\main\services;

use app\components\Crm;
use app\components\E3MemberCenter;
use app\components\MemberCenter;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;
use app\modules\main\enums\activity\ActivityConfigEnum;
use DateTime;
use Faker\Provider\Uuid;

class ErpOrderService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 保存订单
     *
     * @param [type] $data
     * @return void
     */
    public function saveOrder($data){
        $phone = $data['phone'] ?? 0;
        $amount = $data['amount'] ?? '';
        $pay_time = $data['pay_time'] ?? 0;
        $order_no = $data['order_no'] ?? '';
        $status = $data['status'] ?? 0;
        $province = $data['province'] ?? '';
        $city = $data['city'] ?? '';
        $district = $data['district'] ?? '';
        $store_name = $data['store_name'] ?? '';
        $store_code = $data['store_code'] ?? '';
        $product_list = $data['product_list'] ?? [];
        if (!$phone || !$amount || !$product_list || !$pay_time || !$store_code || !$status){
            return ['',false,'缺少参数'];
        }
        
        $info = by::Phone()->GetInfoByPhone($phone);
        if (!$info) {
            $uName = '';
        }else{
            $uid = $info['user_id'];
            $uName = by::Phone()->getUidByUserId($uid);
        }
        
        // 订单号唯一验证
        $order = by::ErpOrderModel()->getOrderByNo($order_no);
        
        // 订单存在则更新，不存在则新增
        if($order){
            $updateData = [];
            $updateData['utime'] = time();
            $gData = [];
            // 退款时，只改订单状态
            if ($status == 2){
                $updateData['status'] = 2;
            }elseif ($status == 3){
                // 退换货时，更改金额和子表数据，不修改订单状态
                $updateData['amount'] = round($amount*100);
                foreach ($product_list as $key => $value) {
                    $gData[] = [
                        'order_no' => $order_no,
                        'amount' => round($value['amount']*100),
                        'num' => $value['new_salerecord_details_num'],
                        'product_type' => $value['new_dg_producttype'],
                        'applet_name' => $value['new_appletname'],
                        'project_number' => $value['new_project_number'],
                        'product_number' => $value['productnumber'],
                        'sn' => $value['sn'],
                        'ctime' => time(),
                        'utime' => time()
                    ];
                }
            }
            list($status,$msg) = by::ErpOrderModel()->updateAllLog($order_no,$updateData,$gData,$order['pay_time']);
            if (!$status){
                return ['',false,$msg];
            }
        }else{
            // 金额全部转成分保存
            if ($status == 3){
                $status = 1; // 未支付
            }
            $newOrder = [
                'phone' => $phone,
                'pay_time' => $pay_time,
                'order_no' => $order_no,
                'uid' => $uName,
                'amount' => round($amount*100),
                'store_name' => $store_name,
                'store_code' => $store_code,
                'province' => $province,
                'city' => $city,
                'district' => $district,
                'ctime' => time(),
                'utime' => time(),
                'status' => $status,
            ];
            $gData = [];
            foreach ($product_list as $key => $value) {
                $gData[] = [
                    'order_no' => $order_no,
                    'amount' => round($value['amount']*100),
                    'num' => $value['new_salerecord_details_num'],
                    'product_type' => $value['new_dg_producttype'],
                    'applet_name' => $value['new_appletname'],
                    'project_number' => $value['new_project_number'],
                    'product_number' => $value['productnumber'],
                    'sn' => $value['sn'],
                    'ctime' => time(),
                    'utime' => time()
                ];
            }

            list($status,$msg) = by::ErpOrderModel()->SaveLog($newOrder,$gData);
            if (!$status){
                return ['',false,$msg];
            }
        }
        return [['order_no'=>$order_no],true,''];
    }
    /**
     * 定时发放积分奖励
     *
     * @return void
     */
    public function cronOrderReward(){
        //获取需要发送积分奖励的订单
        $params = [];
        $params['status'] = 1; // 已支付
        $params['pay_time'] = time() - 15 * 86400; // 支付之后超过15天的
        $params['points_grant_status'] = 0; // 未发放奖励
        $params['uid'] = 1;
        $list = by::ErpOrderModel()->getList($params);
        foreach ($list as $key => $value) {
            $this->grantPoints($value);
        }
    }
    /**
     * 定时重试发放积分奖励
     *
     * @return void
     */
    public function cronRetryOrderReward(){
        //获取需要发送积分奖励的订单
        $params = [];
        $params['status'] = 1; // 已支付
        $params['pay_time'] = time() - 15 * 86400; // 支付之后超过15天的
        $params['points_grant_status'] = 2; // 失败重试
        
        $list = by::ErpOrderModel()->getList($params);
        foreach ($list as $key => $value) {
            $this->grantPoints($value);
        }
    }
    public function grantPoints($order){
        // 准备更新数据
        $updateData = [
            'points_grant_status' => 0,
            'granted_points' => 0,
            'grant_fail_reason' =>'',
            'points_grant_time' => time(),
            'utime' => time()
        ];

        $dateTime = new DateTime();
        $milliseconds = $dateTime->format('Uv');
        $iot_data = [
            'uid' => $order['uid'],
            'timestamp'=>$milliseconds,
            'point'=>round($order['amount']/100,2),
            'operType'=>1,
            'grow'=>0,
            'ext'=>'线下购买奖励积分',
            'serialNo'=> Uuid::uuid()
        ];
        list($status, $iotData) = E3MemberCenter::factory()->run('pointGrowSave', ['event' => 'reward','data' => $iot_data]);
        if ($iotData['code'] != 0 || !isset($iotData['data']) || !$iotData['data']){
            $updateData['grant_fail_reason'] = $iotData['msg']??'积分发放失败，原因未知';
            if ($order['points_grant_status'] == 0){
                $updateData['points_grant_status'] = 2; // 等待重试一次
            } elseif ($order['points_grant_status'] == 2){
                $updateData['points_grant_status'] = 3; // 已失败
            }
        }else{
            // 发放成功，更新订单状态
            $updateData['points_grant_status'] = 1; // 已发放
            $updateData['granted_points'] = $iotData['data']['point']; // 已发放积分数量
        }
        by::ErpOrderModel()->updateAllLog($order['order_no'],$updateData);
        
    }
}