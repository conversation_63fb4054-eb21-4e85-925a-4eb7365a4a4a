<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\CUtil;

class GuideService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }



    /**
     * @param $user_id
     * @param $job_no
     * @return array
     * @throws \yii\db\Exception
     */
    public function SaveGuideJobNo($user_id,$job_no): array
    {
        $guideModel = by::guide();
        //判断工号是否存在
        $jobNoInfo = $guideModel->getGuideInfoByJobNo($job_no);
        $userJobId = $jobNoInfo['user_id'] ?? 0;
        if($userJobId && intval($userJobId) !== intval($user_id)){
            return [false,'用户工号已存在！'];
        }
        //保存工号
        list($s,$data) = by::guide()->saveGuide($user_id,['job_no'=>$job_no]);
        if(!$s){
            return [false,'工号保存失败！'];
        }

        return [true,'OK'];
    }

}
