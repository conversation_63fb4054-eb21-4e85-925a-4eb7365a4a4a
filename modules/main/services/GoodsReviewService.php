<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\components\Review;
use app\exceptions\ReviewException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\GtagModel;
use app\modules\goods\models\OmainModel;
use app\modules\back\services\GoodsReviewService as BackGoodsReviewService;

/**
 * 商品评价服务
 */
class GoodsReviewService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 评价状态
    const REVIEW_STATUS = [
        'CAN_REVIEW'     => 1, // 可评价
        'CAN_APPEND'     => 2, // 可追评
        'CAN_NOT_REVIEW' => 3  // 不可评价
    ];

    // 详情的评价状态
    const DETAIL_REVIEW_STATUS = [
        'ALL'        => 0, // 不限制
        'AUDIT_PASS' => 2, // 审核通过
    ];

    // 评价类型：1主评、2追评
    const REVIEW_TYPE = [
        'MAIN'   => 1, // 主评
        'APPEND' => 2, // 追评
    ];

    // 我的评价类型：1主评、2追评
    const MY_REVIEW_TYPE = [
        'WAIT_REVIEW' => 1, // 待评价
        'REVIEW_LIST' => 2, // 已评价（评价列表）
        'CAN_APPEND'  => 3, // 可追评
    ];

    // 创建评价
    public function createReview(int $userId, array $params): array
    {
        try {
            // 校验订单
            $order = by::Omain()->getInfoByOrderNo($userId, $params['order_no']);
            // if (!$order) {
            //     return [false, '该订单不可评价'];
            // }

            // 校验用户
            $uid = by::Phone()->getUidByUserId($userId);
            if (!$uid) {
                return [false, '用户未授权'];
            }

            // 校验是否可以评价
            $status = $this->isCanReview($params['order_no'], $params['sku'], $params['type']);
            if (!$status) {
                return [false, '提交失败，请刷新重试'];
            }

            // 请求参数
            $data = [
                'uid'          => $uid,
                'review_id'    => $params['review_id'],
                'order_no'     => $params['order_no'],
                'sku'          => $params['sku'],
                'label'        => $params['label'],
                'content'      => $params['content'],
                'image_url'    => $params['image_url'],
                'video_url'    => $params['video_url'],
                'rating'       => $params['rating'],
                'is_anonymous' => $params['is_anonymous'],
                'type'         => $params['type'],
            ];
            list($status, $res) = Review::factory()->create($data);
            if (!$status) {
                return [false, '创建失败，请稍后重试'];
            }

            if ($order){
                // 增加积分：创建主评
                if (empty($params['review_id']) && $params['type'] == self::REVIEW_TYPE['MAIN']) {
                    // 获取商品类型：1主机、2配件
                    $goods_type = $this->getGoodsType($order['order_no'], $params['sku']);
                    if ($goods_type == 1) { // 评价主机
                        EventMsg::factory()->run('evaluateMachine', ['user_id' => $uid, 'user_type' => 2, 'review_id' => $res['id'], 'machine_type' => 1, 'oper_type' => 1]);
                    } else { // 评价配件
                        EventMsg::factory()->run('evaluateMachine', ['user_id' => $uid, 'user_type' => 2, 'review_id' => $res['id'], 'machine_type' => 2, 'oper_type' => 1]);
                    }
                }
            }
            return [true, '创建成功'];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '创建失败，请稍后重试'];
        }
    }

    // 删除评价
    public function deleteReview(int $userId, $order_no, $sku): array
    {
        // TODO 校验是否可以删除

        // 调用评价中台，删除评价
        try {
            list($status, $res) = Review::factory()->delete($order_no, $sku);
            if (!$status) {
                return [false, $res];
            }
            return [true, '删除成功'];
        } catch (\Exception $e) {
            return [false, '删除失败'];
        }
    }

    // 创建评价举报
    public function createReport(int $userId, array $params): array
    {
        try {
            // 校验用户
            $uid = by::Phone()->getUidByUserId($userId);
            if (!$uid) {
                return [false, '获取追觅ID失败'];
            }
            // 举报人ID
            $params['uid'] = $uid;

            list($status, $res) = Review::factory()->report($params);
            if (!$status) {
                return [false, '举报失败'];
            }
            return [true, '举报成功'];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '举报失败'];
        }
    }

    // 获取举报原因列表
    public function getReasonList(): array
    {
        $redis = by::redis('core');
        $cacheKey = AppCRedisKeys::getReportReasonListCacheKey();
        $cachedData = $redis->get(AppCRedisKeys::getReportReasonListCacheKey());

        if ($cachedData === false) {

            // 业务获取举报原因
            list($status, $res) = Review::factory()->reasonList();
            // 处理数据
            $data = array_map(function ($item) {
                return [
                    'id'   => $item['reason_id'],
                    'name' => $item['description']
                ];
            }, $res);

            // 存入缓存
            $expiration = empty($data) ? 10 : 3600;
            $redis->set($cacheKey, json_encode($data), ['EX' => $expiration]);
        } else {
            $data = json_decode($cachedData, true);
        }
        return $data;
    }

    /**
     * 获取评价列表
     * @param array $params
     * @return array
     */
    public function getReviewList(array $params): array
    {
        if (empty($params['gid'])) {
            return [true, ['list' => [], 'total' => 0]];
        }

        try {
            // 预设商品ID与SKU的映射关系
            // 键格式: 主商品ID,二人团商品ID,三人团商品ID
            // 值格式: [主商品编码, 二人团SKU, 三人团SKU]
            $presetSkuMap = [
                    '646,1140,1141'                               => ['011101AA000177', 'HY20250801005000', 'HY20250801006000'],
                    '700,1142,1143'                               => ['011104AA000013', 'HY20250801027000', 'HY20250801028000'],
                    '496,1146,1147'                               => ['011101AA000087', 'HY20250801031000', 'HY20250801032000'],
                    '697,1149,1150'                               => ['010101AA000495', 'HY20250801011000', 'HY20250801012000'],
                    '818,1151,1152'                               => ['010101AA000515', 'HY20250801009000', 'HY20250801010000'],
                    '804,1153,1154'                               => ['010401AA000252', 'HY20250801003000', 'HY20250801004000'],
                    '823,1128,1129'                               => ['010401AB000242', 'HY20250801001000', 'HY20250801002000'],
                    '608,1156,1157'                               => ['010204AA000498', 'HY20250801019000', 'HY20250801020000'],
                    '706,1182,1185,1212,1213,1214,1215,1216,1217' => [
                            '010204AA000701', '010204AA000836', '010204AA000809', '010204AA000711', '010204AA000756',
                            'HY20250801013000', 'HY20250801015000', 'HY20250801017000', 'HY20250801033000', 'HY20250801041000',
                            'HY20250801014000', 'HY20250801016000', 'HY20250801018000', 'HY20250801034000', 'HY20250801042000',
                            'HY20250802085000', 'HY20250802087000', 'HY20250802089000', 'HY20250802086000', 'HY20250802088000', 'HY20250802090000'
                    ],
                    '729,1160,1161'                               => ['010204AA000712', 'HY20250801035000', 'HY20250801036000'],
                    '817,1162,1163'                               => ['010101AC000466', 'HY20250801037000', 'HY20250801038000'],
                    '167,1164,1166'                               => ['010401AE000218', 'HY20250801039000', 'HY20250801040000'],
            ];

            // 初始化SKU列表
            $skus      = [];
            $targetGid = $params['gid'];

            // 从预设映射中查找匹配的SKU
            foreach ($presetSkuMap as $gidGroup => $skuList) {
                // 将逗号分隔的商品ID字符串转换为数组并检查是否包含目标ID
                if (in_array($targetGid, explode(',', $gidGroup))) {
                    $skus = $skuList;
                    break; // 找到匹配项后立即退出循环
                }
            }

            // 如果预设映射中未找到，则通过服务方法获取
            if (empty($skus)) {
                // 获取有效SKU列表
                $skus = by::Gmain()->getEffectiveSkuByGid(
                        $targetGid,
                        $params['goods_type'] ?? 1
                );

                // 如果仍然没有SKU，返回空结果集
                if (empty($skus)) {
                    return [
                            true,
                            [
                                    'list'      => [],
                                    'total'     => 0,
                                    'page'      => intval($params['page'] ?? 1),
                                    'page_size' => intval($params['page_size'] ?? 10)
                            ]
                    ];
                }
            }


            // 评价列表
            $reviews = [];
            // 请求参数
            $data = [
                'sku'        => $skus,
                'has_media'  => $params['has_media'],
                'has_append' => $params['has_append'],
                'page'       => $params['page'],
                'page_size'  => $params['page_size'],
            ];

            list($status, $res) = Review::factory()->list($data);
//            if ($params['has_append']==0&&$params['goods_type']==1){
//                $res = GoodsCommentService::get($params['gid'],$res,intval($params['page']),intval($params['page_size']),$params['has_media']);
//            }

            if (!$status || empty($res['list'])) {
                return [true, ['list' => [], 'total' => 0, 'page' => intval($params['page']), 'page_size' => intval($params['page_size'])]];
            }



            foreach ($res['list'] as $item) {
                // 格式化数据
                $review = $this->formatReview($item);

                // 用户信息
                $review['avatar'] = '';
                $review['nickname'] = '';
                if (!$review['is_anonymous']) {
                    $user = by::usersMall()->getMallInfoByUidWithDeleted($review['uid'], true, true);
                    // 查询用户昵称和头像
                    $review['avatar'] = $user['avatar'] ?? '';
                    $review['nickname'] = $user['nick_name'] ?? '';
                }
                $reviews[] = $review;
            }

            return [true, ['list' => $reviews, 'total' => $res['total'] ?? 0, 'page' => intval($params['page']), 'page_size' => intval($params['page_size'])]];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '获取评价列表失败'];
        }
    }

    /**
     * 获取评价详情
     * @param int $review_id
     * @param int $audit_status
     * @return array
     */
    public function getReviewDetail(int $review_id, int $audit_status = 0): array
    {
        if (!$review_id) {
            return [false, 'ID不能为空'];
        }

        try {
            // 获取评价详情
            list($status, $res) = Review::factory()->detail($review_id);
            if (!$status || !$res) {
                return [true, []];
            }

            // 校验评价状态
            $reviews = [];
            if (self::DETAIL_REVIEW_STATUS['ALL'] == $audit_status) {
                $reviews = $res;
            } else {
                foreach ($res as $item) {
                    if ($item['status'] == $audit_status) {
                        $reviews[] = $item;
                    }
                }
            }

            // 处理评价
            $data = $this->formatReview($reviews);

            // 用户信息
            $data['nickname'] = '';
            $data['avatar'] = '';

            // 是否匿名
            $is_anonymous = $data['is_anonymous'] ?? 0;
            if (!$is_anonymous) {
                $user = by::usersMall()->getMallInfoByUidWithDeleted($data['uid'], true, false);
                // 查询用户昵称和头像
                $data['nickname'] = $user['nick_name'] ?? '';
                $data['avatar'] = $user['avatar'] ?? '';
            }
            return [true, $data];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '获取评价详情失败'];
        }
    }

    /**
     * 格式化评价数据
     * @param array $reviews
     * @return array
     * @throws ReviewException
     */
    private function formatReview(array $reviews): array
    {
        $data = ['main' => [], 'append' => []];

        foreach ($reviews as $item) {
            if ($item['types'] == self::REVIEW_TYPE['MAIN']) {
                empty($data['main']) && $data['main'] = $this->formatReviewItem($item);
            } elseif ($item['types'] == self::REVIEW_TYPE['APPEND']) {
                empty($data['append']) && $data['append'] = $this->formatReviewItem($item);
            } else {
                throw new ReviewException('评价类型异常');
            }
        }

        // 追评天数
        if ($data['append']) {
            $main_date = (new \DateTime($data['main']['create_time']))->setTime(0, 0, 0);
            $append_date = (new \DateTime($data['append']['create_time']))->setTime(0, 0, 0);
            $interval = $main_date->diff($append_date);
            $days = $interval->format('%a');
            $data['append']['append_days'] = $days;
        }

        // 如果数据是空数组，返回空对象
        if (empty($data['main'])) {
            $data['main'] = new \stdClass();
        }

        // 如果数据是空数组，返回空对象
        if (empty($data['append'])) {
            $data['append'] = new \stdClass();
        }

        // 处理返回数据
        $result = $data['main'];
        $result['append'] = $data['append'];

        return $result;
    }

    // 格式化评价数据
    private function formatReviewItem($item): array
    {
        // 最大评分
        $rating = $item['rating'] ?: [];
        $max_rating = $rating ? max($rating) : 0;

        return [
            'review_id'    => $item['id'],
            'sku'          => $item['entity_id'],
            'uid'          => $item['reviewer_id'],
            'order_no'     => $item['entity_relation_id'],
            'label'        => $item['label'] ?: [],
            'content'      => $item['content'],
            'image_url'    => $item['image_url'] ?: [],
            'video_url'    => $item['video_url'] ?: [],
            'rating'       => $rating,
            'max_rating'   => $max_rating,
            'is_anonymous' => $item['is_anonymous'],
            'status'       => $item['status'],
            'refuse_reason'=> $item['refuse_reason'] ?? '', // 拒绝原因
            'create_time'  => date('Y-m-d', strtotime($item['created_at'])),
            'update_time'  => date('Y-m-d', strtotime($item['updated_at'])), // 更新（编辑）时间
            'reply'        => $this->formatReply($item['reply']),
        ];
    }

    // 格式化回复数据
    private function formatReply($reply)
    {
        $data = [];
        if (!empty($item = $reply[0])) {
            $data = [
                'reply_id'    => $item['id'],
                'content'     => $item['content'],
                'create_time' => $item['created_at'],
            ];
        }
        // 空数组转空对象
        if (empty($data)) {
            $data = new \stdClass();
        }
        return $data;
    }

    /**
     * 获取评价状态，首评订单完成30天，追评是首评完成30内
     * @param string $orderNo
     * @param array $skus
     * @param int $status
     * @param int $finishTime
     * @param array $reviewItems 评价数据
     * @return array
     */
    public function getReviewStatus(string $orderNo, array $skus, int $status, int $finishTime, array $reviewItems = []): array
    {
        if (empty($skus)) {
            return [];
        }

        // 订单状态不是已完成，不可评价
        if ($status != OmainModel::ORDER_STATUS['FINISHED']) {
            return array_fill_keys($skus, self::REVIEW_STATUS['CAN_NOT_REVIEW']);
        }

        // 获取评价数据
        if (empty($reviewItems)) {
            $params = [
                'order_nos' => [$orderNo],
            ];
            list($status, $reviewItems) = Review::factory()->status($params);
        }

        // 分组
        $group = [];
        foreach ($reviewItems ?? [] as $item) {
            $order_no = $item['entity_relation_id'];
            $sku = $item['entity_id'];

            if (!isset($group[$order_no][$sku])) {
                $group[$order_no][$sku] = [];
            }
            $group[$order_no][$sku][] = $item;
        }

        // 判断评价状态
        $data = [];
        foreach ($skus as $sku) {
            if (!isset($group[$orderNo][$sku])) { // 不存在则判断完成时间
                if ($finishTime >= strtotime('-30 days')) {  // 完成时间在30天内，可评价
                    $data[$sku] = self::REVIEW_STATUS['CAN_REVIEW'];
                } else {    // 完成时间超过30天，不可评价
                    $data[$sku] = self::REVIEW_STATUS['CAN_NOT_REVIEW'];
                }
            } else {
                $reviews = $group[$orderNo][$sku];
                $data[$sku] = $this->__getReviewStatus($reviews);
            }
        }
        return $data;
    }

    /**
     * 批量获取评价状态
     * @param array $items
     * @return array
     */
    public function batchGetReviewStatus(array $items): array
    {
        $data = [];

        // 获取评价数据
        list($status, $reviewItems) = Review::factory()->status(['order_nos' => array_column($items, 'order_no')]);
        if (!$status || empty($reviewItems)) {
            // 查询不到给默认值，避免在for循环中再查询
            $reviewItems = [
                [
                    'entity_relation_id' => -1,
                    'entity_id'          => -1,
                ]
            ];
        }

        foreach ($items as $item) {
            $data[$item['order_no']] = $this->getReviewStatus($item['order_no'], $item['skus'], $item['status'], $item['finish_time'], $reviewItems);
        }
        return $data;
    }

    /**
     * 批量设置评价状态
     * @param array $list
     * @param array $status
     * @return array
     */
    public function batchSetReviewStatus(array $list, array $status): array
    {
        foreach ($list as $key => $item) {
            $goods = $item['goods'];
            foreach ($goods as $k => $good) {
                $list[$key]['goods'][$k]['review_status'] = $status[$item['order_no']][$good['sku']];

            }
        }
        return $list;
    }


    /**
     * 获取可评价的订单列表
     * @param int $userId
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getCanReviewOrderList(int $userId): array
    {
        $data = [];

        // 1、30天内已完成的订单
        $order_nos = by::Ouser()->getOrders($userId, ['user_id' => $userId, 'status' => OmainModel::ORDER_STATUS['FINISHED'], 'finish_time' => ['>=', strtotime('-30 days')]], ['order_no']);

        // 去掉先试后买订单号
        $order_nos = $this->diffTryOrder($order_nos, $userId);

        // 2、获取订单下的商品
        $list = [];
        $reviewOrderGoods = [];
        foreach ($order_nos as $order_no) {
            $item = by::Ouser()->CommPackageInfo($userId, $order_no, false, true, false, false, true, false, true, true);
            if (!$item || ($item['user_order_type'] == OmainModel::USER_ORDER_TYPE['INTERNAL'])) { // 内购的订单去掉
                continue;
            }
            $list[] = $item;
            $reviewOrderGoods[] = [
                'order_no'    => $item['order_no'],
                'skus'        => array_column($item['goods'], 'sku'),
                'status'      => $item['status'],
                'finish_time' => $item['finish_time'],
            ];
        }

        // 3、 调用评价中台，评价状态
        $reviewStatus = [];
        if ($reviewOrderGoods) {
            $reviewStatus = $this->batchGetReviewStatus($reviewOrderGoods);
        }

        // 4、处理数据
        foreach ($list as $item) {
            $goods = [];
            foreach ($item['goods'] as $good) {
                // 评价状态
                $status = self::REVIEW_STATUS['CAN_REVIEW'];
                if ($reviewStatus[$item['order_no']][$good['sku']] <> $status) {
                    continue;
                }
                $good['review_status'] = $status;
                $goods[] = $good;
            }
            if ($goods) {
                $data[] = [
                    // 订单信息
                    'id'              => $item['id'],
                    'user_order_type' => $item['user_order_type'],
                    'price'           => $item['price'],
                    'coin'            => $item['coin'],
                    'deposit_price'   => $item['deposit_price'],
                    'fprice'          => $item['fprice'],
                    'order_no'        => $item['order_no'],
                    // 商品信息
                    'goods'           => $goods,
                ];
            }
        }
        return [true, ['list' => $data]];
    }

    /**
     * 获取可追评的订单列表
     * @param $userId
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getCanAppendOrderList($userId): array
    {
        $list = $this->getCanAppendList($userId);
        if (empty($list)) {
            return [true, ['list' => []]];
        }

        $orderNoSku = [];
        foreach ($list as $item) {
            $orderNoSku[$item['order_no']][$item['sku']] = 1;
        }

        $order_nos = array_unique(array_column($list, 'order_no'));

        // 2、获取订单下的商品
        $order_list = [];
        foreach ($order_nos as $order_no) {
            $item = by::Ouser()->CommPackageInfo($userId, $order_no, false, true, false, false, true, false, true, true);
            if (!$item) {
                continue;
            }
            $order_list[] = $item;
        }

        // 3、处理数据
        $data = [];
        foreach ($order_list as $order) {
            $goods = [];
            foreach ($order['goods'] as $good) {
                // 不在可评追评列表中
                if (!isset($orderNoSku[$order['order_no']][$good['sku']])) {
                    continue;
                }
                $good['review_status'] = self::REVIEW_STATUS['CAN_APPEND'];
                $goods[] = $good;
            }
            if ($goods) {
                $data[] = [
                    'id'              => $order['id'],
                    'user_order_type' => $order['user_order_type'],
                    'price'           => $order['price'],
                    'coin'            => $order['coin'],
                    'deposit_price'   => $order['deposit_price'],
                    'fprice'          => $order['fprice'],
                    'order_no'        => $order['order_no'],
                    'goods'           => $goods,
                ];
            }
        }
        return [true, ['list' => $data]];
    }

    /**
     * 获取可追评列表
     * @param $userId
     * @return array
     * @throws \yii\db\Exception
     */
    private function getCanAppendList($userId): array
    {
        // 校验用户
        $uid = by::Phone()->getUidByUserId($userId);
        if (!$uid) {
            return [];
        }

        $current_time = time(); // 当前时间戳
        $params = [
            'uid'        => $uid,
            'start_time' => strtotime('-30 days', $current_time),
            'end_time'   => $current_time,
        ];

        list($status, $items) = Review::factory()->status($params);
        if (!$status) {
            return [];
        }

        // 分组
        $group = [];
        foreach ($items as $item) {
            $order_no = $item['entity_relation_id'];
            $sku = $item['entity_id'];
            if (!isset($group[$order_no][$sku])) {
                $group[$order_no][$sku] = [];
            }
            $group[$order_no][$sku][] = $item;
        }

        // 过滤出可追评的订单
        $data = [];
        foreach ($group as $order_no => $skus) {
            foreach ($skus as $sku => $items) {

                if (count($items) != 1) {
                    continue;
                }

                if ($items[0]['types'] != self::REVIEW_TYPE['MAIN'] || $items[0]['is_deleted'] == 1) {
                    continue;
                }

                $data[] = [
                    'order_no'   => $order_no,
                    'sku'        => $sku,
                    'created_at' => $items[0]['created_at'],
                ];
            }
        }

        // $data按照created_at降序排序
        usort($data, function ($a, $b) {
            return $b['created_at'] <=> $a['created_at'];
        });

        return $data;
    }

    /**
     * 获取我的评价列表
     * @param int $userId
     * @param int $page
     * @param int $page_size
     * @return array
     */
    public function getMyReviewList(int $userId, int $page, int $page_size): array
    {
        try {
            // 校验用户
            $uid = by::Phone()->getUidByUserId($userId);
            if (!$uid) {
                return [false, '用户不存在'];
            }

            // 请求参数
            $data = [
                'uid'       => $uid,
                'page'      => $page,
                'page_size' => $page_size,
            ];
            list($status, $res) = Review::factory()->userReviewList($data);
            if (!$status) {
                return [false, '获取评价失败'];
            }

            $reviews = [];
            foreach ($res['list'] ?? [] as $item) {

                $review = $this->formatReview($item);

                // 用户信息
                $review['avatar'] = '';
                $review['nickname'] = '';
                if (!$review['is_anonymous']) {
                    $user = by::usersMall()->getMallInfoByUidWithDeleted($review['uid'], true, true);
                    // 查询用户昵称和头像
                    $review['avatar'] = $user['avatar'] ?? '';
                    $review['nickname'] = $user['nick_name'] ?? '';
                }
                $reviews[] = $review;
            }

            // 处理商品数据
            foreach ($reviews as $key => $review) {
                $item = by::Ouser()->CommPackageInfo($userId, $review['order_no'], false, true, false, false, true, false, true, true);
                if (!$item) {
                    continue;
                }
                // 订单信息
                $reviews[$key]['user_order_type'] = $item['user_order_type'];
                $reviews[$key]['price'] = $item['price'];
                $reviews[$key]['coin'] = $item['coin'];
                $reviews[$key]['deposit_price'] = $item['deposit_price'];
                $reviews[$key]['fprice'] = $item['fprice'];
                // 商品信息
                foreach ($item['goods'] as $good) {
                    if ($good['sku'] == $review['sku']) {
                        $reviews[$key]['goods'] = $good;
                    }
                }
            }
            return [true, ['list' => $reviews, 'total' => $res['total'], 'page' => intval($page), 'page_size' => intval($page_size)]];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '获取评价列表失败'];
        }
    }

    /**
     * 可追评、不可评价
     * @param array $reviews
     * @return int
     */
    private function __getReviewStatus(array $reviews): int
    {
        // 按照主评和追评分组
        $reviews = array_column($reviews, null, 'types');

        $main = $reviews[1] ?? [];
        $append = $reviews[2] ?? [];

        // 可追评
        if (!empty($main) && $main['is_deleted'] == 0 && empty($append) && strtotime($main['created_at']) >= strtotime('-30 days')) {
            return self::REVIEW_STATUS['CAN_APPEND'];
        }

        // 不可评价
        return self::REVIEW_STATUS['CAN_NOT_REVIEW'];
    }

    /**
     * 过滤先试后买订单号
     * @param array $orderNos
     * @param int $userId
     * @return array
     */
    private function diffTryOrder(array $orderNos, int $userId): array
    {
        // 判空
        if (empty($orderNos)) {
            return [];
        }

        // 先试后买订单
        $tryOrderNos = byNew::UserOrderTry()->GetList([
            CUtil::buildCondition('user_id', '=', $userId),
        ], false);

        return array_diff($orderNos, array_column($tryOrderNos, 'order_no'));
    }

    /**
     * 获取商品类型
     * @param $order_no
     * @param $sku
     * @returnint 1主机 2配件
     * @throws \yii\db\Exception
     */
    public function getGoodsType($order_no, $sku): int
    {
        // 获取订单商品信息
        $items = by::Ocfg()->getListByOrderNos([$order_no]);

        // 构建订单号和SKU到商品ID的映射关系
        $orderSkuMainMap = [];
        foreach ($items as $item) {
            $currentSku = ($item['atype'] == 1) ? $item['spec']['sku'] : $item['sku'];
            $orderSkuMainMap[$item['order_no']][$currentSku]['tids'] = $item['tids'] ?? [];
        }

        // 判断商品类型
        $mainTag = by::Gtag()->GetMainTag();
        // if (empty($orderSkuMainMap[$order_no][$sku]['tids']) || empty(array_intersect(GtagModel::MAIN_TAG, $orderSkuMainMap[$order_no][$sku]['tids']))) {
        if (empty($orderSkuMainMap[$order_no][$sku]['tids']) || empty(array_intersect($mainTag, $orderSkuMainMap[$order_no][$sku]['tids']))) {
            return 2; // 配件
        }

        return 1; // 主机
    }

    private function isCanReview($order_no, $sku, $type): bool
    {
        // 如果评价不存在，可以评价
        list($status, $reviewItems) = Review::factory()->status(['order_nos' => [$order_no]]);
        if (!$status) {
            return false;
        }

        if (empty($reviewItems)) {
            return true;
        }

        // 分组
        $group = [];
        foreach ($reviewItems ?? [] as $item) {
            $order_no_1 = $item['entity_relation_id'];
            $sku_1 = $item['entity_id'];

            if (!isset($group[$order_no_1][$sku_1])) {
                $group[$order_no_1][$sku_1] = [];
            }
            $group[$order_no_1][$sku_1][] = $item;
        }

        $reviews = $group[$order_no][$sku] ?? [];
        if (empty($reviews)) { // 没有数据可以评价
            return true;
        }

        // 如果评价存在，且状态为审核不通过，则可以评价
        $reviews = array_column($reviews, null, 'types');

        if ($type == self::REVIEW_TYPE['MAIN']) { // 主评
            if (empty($reviews[$type])) {
                return true;
            } else {
                if ($reviews[self::REVIEW_TYPE['MAIN']]['is_deleted'] == 0 && $reviews[self::REVIEW_TYPE['MAIN']]['status'] == 3) { // 未被删除且审核拒绝（可编辑）
                    return true;
                } else {
                    return false;
                }
            }
        } else { // 追评
            if (!empty($reviews[self::REVIEW_TYPE['MAIN']]) && $reviews[self::REVIEW_TYPE['MAIN']]['is_deleted'] == 0) { // 有主评、且未被删除
                if (empty($reviews[self::REVIEW_TYPE['APPEND']])) {
                    return true;
                } else if ($reviews[self::REVIEW_TYPE['APPEND']]['is_deleted'] == 0 && $reviews[self::REVIEW_TYPE['APPEND']]['status'] == 3) { // 未被删除且审核拒绝（可编辑）
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    }


    public function getMyReviewIds()
    {

    }
}