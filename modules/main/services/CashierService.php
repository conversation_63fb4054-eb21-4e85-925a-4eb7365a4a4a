<?php

namespace app\modules\main\services;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\OmainModel;
use app\modules\goods\models\OPayModel;
use app\modules\main\models\pay\MpPayModel;
use RedisException;
use yii\db\Exception;


class CashierService
{


    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     *  获取支付方式
     * @param $userId
     * @param $orderNo
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getPayment($userId, $orderNo): array
    {
        if (empty($orderNo)) {
            return ['status' => false, 'message' => '订单号不能为空'];
        }

        $orderInfo = by::Ouser()->CommPackageInfo($userId, $orderNo, false, false, true, true);

        if (empty($orderInfo) || $orderInfo['status'] != OmainModel::ORDER_STATUS['WAIT_PAY']) {
            return ['status' => false, 'message' => '订单不存在或已支付'];
        }

        $amount  = $orderInfo['price'];
        $gids    = array_column($orderInfo['goods'], 'gid');
        $goods   = by::Gtype0()->getListByGids($gids, ['gid', 'jd_baitiao_support']);
        $payList = $this->getPayList($amount, $goods);

        return ['status' => true, 'data' => ['pay_list' => $payList, 'price' => $amount, 'remain_time' => $orderInfo['remain_time']]];
    }

    /**
     *  获取支付方式
     * @param $userId
     * @param $orderNo
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getPaymentPC($userId, $orderNo): array
    {
        if (empty($orderNo)) {
            return ['status' => false, 'message' => '订单号不能为空'];
        }

        $orderInfo = by::Ouser()->CommPackageInfo($userId, $orderNo, false, false, true, true);

        if (empty($orderInfo)) {
            return ['status' => false, 'message' => '订单不存在或已支付'];
        }

        $amount  = $orderInfo['price'];
        $gids    = array_column($orderInfo['goods'], 'gid');
        $goods   = by::Gtype0()->getListByGids($gids, ['gid', 'jd_baitiao_support']);
        $payList = $this->getPayList($amount, $goods);

        // 只返回京东的支付方式
        $payList = array_filter($payList, function ($item) {
            return $item['pay_type'] == OmainModel::PAY_JD_BAITIAO;
        });
        $payList = array_values($payList);

        return ['status' => true, 'data' => ['pay_list' => $payList, 'price' => $amount, 'remain_time' => $orderInfo['remain_time']??0]];
    }


    /**
     * 获取支付方式
     * @param $amount
     * @param $goods
     * @return array[]
     */
    public function getPayList($amount, $goods): array
    {
        // 微信支付
        $payList = [
                ['pay_type' => OmainModel::PAY_BY_WX, 'name' => '微信支付'],
        ];

        // 京东白条
        $JDBaiTiao = self::payJDBaiTiao($goods, $amount);
        if (!empty($JDBaiTiao)) {
            $payList[] = ['pay_type' => OmainModel::PAY_JD_BAITIAO, 'name' => '京东白条支付', 'interests' => $JDBaiTiao];
        }

        return $payList;
    }

    /**
     * 京东白条支付
     * @param $goods
     * @param $amount
     * @return array
     */
    public function payJDBaiTiao($goods, $amount): array
    {
        // 检查是否有不支持白条商品
        $baiTiaoGoods = Collection::make($goods);
        if ($baiTiaoGoods->where('jd_baitiao_support', 1)->isEmpty()) {
            return [];
        }
        $gids = $baiTiaoGoods->pluck('gid')->toArray();
        return $this->getBaiTiaoGoodsInst($gids, $amount);
    }

    /**
     * 获取白条分期
     * @param $baiTiaoGids
     * @param $amount
     * @return array
     */
    private function getBaiTiaoGoodsInst($baiTiaoGids, $amount): array
    {
        $no_inst [] = self::setPeriodData("NO_INST", $amount);

        // 如果没有白条商品或者总金额小于10元，不显示分期
        if (empty($baiTiaoGids) || $amount < 10) {
            return $no_inst;
        }

        // 是否所有商品都支持免息分期
        $free = $this->getFreePeriod($baiTiaoGids, $amount);

        // 计算分期手续费
        $service_fee_data = MpPayModel::getInstance()->getInstFee(MpPayModel::PAY_CHANNEL['JD_BAITIAO'], $amount);

        // 按服务费从小到大排序
        $service_fee = Collection::make($service_fee_data)->filter(function ($item) use ($free) {
            // 如果有免息分期，不显示服务费分期
            if (empty($free)) {
                return true;
            }
            return !Collection::make($free)->where('key', 'FREE_' . $item['period'])->isNotEmpty();
        })->sortBy('period')->map(function ($item) {
            return self::setPeriodData("INST_" . $item['period'], $item['per_installment'], $item['service_fee']);
        })->values()->toArray();

        return array_merge($no_inst, $free, $service_fee);
    }


    /**
     * 检查白条分期
     * @param $userId
     * @param $orderNo
     * @param $amount
     * @param $paymentPlan
     * @param array $gcombines
     * @return array
     * @throws Exception
     */
    public function checkBaiTiaoInst($userId, $orderNo, $amount, $paymentPlan, array $gcombines = []): array
    {
        // 分期数
        $period      = 0;
        $sku         = '';
        $baiTiaoGids = [];
        if (!isset(OPayModel::PAYMENT_PLAN[$paymentPlan])) {
            return [false, '分期参数错误', $period, $sku];
        }

        if (!empty($gcombines))
            $baiTiaoGids = array_column($gcombines, 'gid');


        if (!empty($orderNo)) {
            $baiTiaoGids = $this->getGidByOrderNo($userId, $orderNo);
        }

        if (empty($baiTiaoGids)) {
            return [false, '商品不存在', $period, $sku];
        }

        $status = Collection::make(self::getBaiTiaoGoodsInst($baiTiaoGids, $amount))
                ->contains('key', $paymentPlan);

        if (!$status) {
            return [false, '请选择正确的分期', $period, $sku];
        }

        $period = OPayModel::PAYMENT_PLAN_PERIOD[$paymentPlan];

        // 如果是免息分期
        if (strpos($paymentPlan, 'FREE') !== false) {
            // 获取免息分期SKU
            $BAITIAO_FREE_SKU = CUtil::getConfig('jd_free_sku', 'baitiao', MAIN_MODULE) ?? [];
            $sku              = $BAITIAO_FREE_SKU[$paymentPlan] ?? '';
            if (empty($sku)) {
                return [false, '未配置京东白条免息分期SKU', $period, $sku];
            }
        }

        return [true, '', $period, $sku];

    }

    /**
     * 获取分期数据
     * @param $key
     * @param float $service_fee
     * @param $per_installment
     * @return array
     */
    public static function setPeriodData($key, $per_installment, float $service_fee = 0): array
    {
        $keys   = explode('_', $key);
        $period = $keys[1] ?? 0;
        $type   = $keys[0] ?? 'INST';

        $per_installment = sprintf("%.2f", $per_installment);

        if ($type == 'FREE') {
            return ['key' => $key, 'name' => $period . '期免息', 'service_fee' => $service_fee, 'per_installment' => $per_installment];
        }

        if ($type == 'INST') {
            return ['key' => $key, 'name' => $period . '期分期', 'service_fee' => $service_fee, 'per_installment' => $per_installment];
        }

        return ['key' => 'NO_INST', 'name' => '不分期', 'service_fee' => 0, 'per_installment' => $per_installment];
    }


    /**
     * 获取免息分期信息
     * @param array $baiTiaoGids
     * @param $amount
     * @return array
     */
    public function getFreePeriod(array $baiTiaoGids, $amount): array
    {
        $list = [];
        // 获取商品免息分期活动
        $baiTiaoInst = byNew::PaymentActivityModel()->getBaiTiaoInst($baiTiaoGids, 'min');
        $periods     = [3 => 'FREE_3', 6 => 'FREE_6', 12 => 'FREE_12'];
        foreach ($periods as $period => $key) {
            if ($baiTiaoInst >= $period) {
                $per_installment = $amount < 0 ? $amount : round($amount / $period, 2);
                $list[]          = self::setPeriodData($key, $per_installment);
            }
        }

        return $list;
    }

    /**
     * @param $userId
     * @param $orderNo
     * @return array
     * @throws Exception
     */
    public function getGidByOrderNo($userId, $orderNo): array
    {
        $goods = by::Ogoods()->GetListByOrderNo($userId, $orderNo);
        return array_column($goods, 'gid');
    }

    /**
     * 获取JD支付需要信息
     * @param $user_id
     * @param $order_no
     * @param $amount float
     * @param $paymentPlan
     * @return array
     * @throws Exception
     */
    public function getJDPayInfo($user_id, $order_no, float $amount, $paymentPlan): array
    {
        $data = [];
        list($statusC, $messageC, $period, $freeSku) = CashierService::getInstance()->checkBaiTiaoInst($user_id, $order_no, $amount, $paymentPlan);
        if (!$statusC) {
            return [false, $messageC];
        } else {
            $data['lock_plan']             = $period;
            $data['mini_program_redirect'] = 1;
        }

        $info = by::Ouser()->CommPackageInfo($user_id, $order_no, false, true, true, true, true, false, true, true);
        /**
         * 收货人姓名    name    String(100)
         * 收货地址    address    String(500)
         * 收货手机号    mobile    String(30)
         */
        $name     = $info['address']['nick'] ?? '';
        $province = $info['address']['province'] ?? '';
        $city     = $info['address']['city'] ?? '';
        if (strpos($city, '市辖区') !== false) {
            $city = $province . '市';
        }
        $area    = $info['address']['area'] ?? '';
        $detail  = $info['address']['detail'] ?? '';
        $address = $province . $city . $area . $detail;
        $mobile  = $info['address']['phone'] ?? '';

        $data['receiver_info'] = [
                'name'    => (string) substr($name, 0, 100),
                'address' => (string) substr($address, 0, 500),
                'mobile'  => (string) substr($mobile, 0, 30),
        ];


        /**
         * 商品编号    id    String(32)
         * 商品类型    type    String(20)    GT01:实物,GT02:虚拟
         * 商品单价    price    String(32) 是 单位：分, 整数大于0
         * 商品数量    num    String(32)
         */

        foreach ($info['goods'] as $good) {
            $num   = $good ['num'];
            $price = CUtil::totalFee($good['price']);

            $data['goods_detail'][] = [
                    'id'    => $freeSku,
                    'type'  => 'GT01',
                    'price' => (string) $price,
                    'num'   => (string) $num,
                    'name'  => (string) $good['name']
            ];
        }


        /**
         * 商品一级类目    catDescrip1    String(10)    文字版（与cat1码值对应）
         * 收货省    province    String(200)
         * 收货市    city    String(20)
         * 商品品牌    goodsBrand    String    3C商户必传
         * 下单账号    orderAccount    String    用户在商户侧唯一标识
         */
        if (strpos($name, '配件') !== false) {
            $cat = '主机';
        } else {
            $cat = '配件';
        }
        $data['risk_info'] = [
                'catDescrip1'  => (string) substr($cat, 0, 20),
                'province'     => (string) $province,
                'city'         => (string) substr($city, 0, 20),
                'goodsBrand'   => '追觅',
                'orderAccount' => (string) substr($user_id, 0, 32),
        ];
        return [true, $data];
    }
}