<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\OmainModel;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\CommPayModel;
use app\modules\main\models\pay\MpPayModel;
use app\modules\main\models\pay\PayModel;

/**
 * 支付服务
 */
class PayService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    //下单来源
    const SOURCE = [
        'MALL'     => 1,
        'PLUMBING' => 2,
        'DEPOSIT'  => 3,
        'POINTS'   => 4,//积分商城
    ];

    /**
     * 重新支付
     * @param $user_id
     * @param $order_no
     * @param $order_type
     * @param $pay_type
     * @param $attach
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function againPay($user_id, $order_no, $order_type, $pay_type, $attach = []): array // 默认普通订单
    {
        if (empty($user_id) || empty($order_no) || empty($order_type) || empty($pay_type)) {
            return [false, '参数错误'];
        }

        //是否需要重新创建订单
        $needReunified = 0;
        $endPayment    = 0;
        $paymentPlan   = $attach['payment_plan'] ?? 'NO_INST';

        if ($order_type == by::Odeposit()::TYPE['DEPOSIT']) {
            $oInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Odeposit()::STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }

            $oeInfo = by::OdepositE()->GetInfoByOrderId($user_id, $order_no);
            $cfg    = $oeInfo['cfg'] ?? [];

            //定金订单
            $endPayment    = $cfg['presale_time'] ?? 0;
            $needReunified = 1;

        } else {
            $oInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);

            if (isset($oInfo['deposit_order_no']) && $oInfo['deposit_order_no']) {
                $now            = intval(START_TIME);
                $oInfo['ctime'] = $now;
                $oMainInfo      = by::Omain()->getInfoByOrderNo($user_id, $order_no);

                //尾款订单校验库存
                $commPay = new CommPayModel();
                list($s, $msg) = $commPay->CheckTailOrderStock($user_id, $oInfo['deposit_order_no']);
                if (!$s) {
                    return [$s, $msg];
                }

                //定金订单
                $endPayment    = $oMainInfo['end_payment'] ?? 0;
                $needReunified = 2;
            }

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Omain()::ORDER_STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Omain()::ORDER_STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }
        }

        //付款流水
        $aOpay = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);

        if (empty($aOpay)) {
            return [false, '订单不存在(2)'];
        }

        $aOpayPaymentPlan=$aOpay['payment_plan']??'NO_INST';
        // 重新创建
        $rebuild = false;
        // 切换支付方式
        if ($aOpay['pay_type'].$aOpayPaymentPlan != $pay_type. $paymentPlan) {
            // 保存支付信息
            if ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET) {
                PayModel::getInstance()->saveTradeOrder($order_no, $aOpay['pay_type'], $aOpay['prepay_id'], $aOpay['h5_url'], $aOpay['ptime'],$aOpay['payment_plan']);
            }

            // 新支付方式的支付信息
            $currentPayData = PayModel::getInstance()->getTradeOrder($order_no, $pay_type, $paymentPlan);

            if ($currentPayData && ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET)) {
                // 更新支付流水表
                $payData = [
                    'prepay_id' => $currentPayData['prepay_id'], 'ptime' => $currentPayData['ptime'], 'h5_url' => $currentPayData['h5_url'], 'pay_type' => $pay_type,'payment_plan'=>$paymentPlan
                ];
                by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);
                // 更新数据
                $aOpay['prepay_id'] = $currentPayData['prepay_id'];
                $aOpay['ptime']     = $currentPayData['ptime'];
                $aOpay['h5_url']    = $currentPayData['h5_url'];
            } else {
                // 新支付方式的支付信息不存在，重新生成
                $rebuild = true;
            }
        }

        $now = time();
        if ($rebuild || (empty($aOpay['prepay_id']) && $aOpay['price']) || bcsub($now, $aOpay['ptime']) > 7100) {
            $other = [
                'body'          => '订单支付',
                'ctime'         => $oInfo['ctime'],
                'needReunified' => $needReunified,
                'endPayment'    => $endPayment,
            ];

            // 支付过期时间
            list($status, $time_expire) = $this->getPayExpireTime($other, 7100);
            if (!$status) {
                return [false, $time_expire];
            }
            $params = [
                'user_id'     => $user_id,
                'time_expire' => $time_expire,
                'order_type'  => $order_type, // 商品订单
            ];

            $amount=bcdiv($aOpay['price'], 100, 2);

            // 白条分期检查
            if ($pay_type == OmainModel::PAY_JD_BAITIAO||$pay_type == OmainModel::PAY_JD_BAITIAO_APP||$pay_type == OmainModel::PAY_JD_BAITIAO_H5||$pay_type == OmainModel::PAY_JD_BAITIAO_PC) {
                list($status, $jd_pay_info) = CashierService::getInstance()->getJDPayInfo($user_id, $order_no, $amount, $paymentPlan);
                if (!$status) {
                    return [false, $jd_pay_info];
                }
                $params = array_merge($params, $jd_pay_info);
            }
            list($status, $prepay_id) = $this->getPrePayId($order_no, $amount, $params, $pay_type);
            if (!$status) {
                return [false, $prepay_id];
            }

            $PayReq = [
                'order_no'  => $order_no,
                'prepay_id' => $prepay_id,
            ];

            //更新支付流水表
            $payData = [
                'prepay_id' => $PayReq['prepay_id'], 'ptime' => $now, 'h5_url' => $PayReq['h5_url'] ?? '', 'pay_type' => $pay_type,'payment_plan'=>$paymentPlan
            ];
            by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);

        } else {
            $PayReq = [
                'order_no'  => $order_no,
                'prepay_id' => $aOpay['prepay_id']
            ];
        }

        $PayReq['pay_type'] = $pay_type;
        $r_price            = bcsub($aOpay['price'], $oInfo['fprice'] ?? 0);
        $r_price            = by::Gtype0()->totalFee($r_price, 1);
        $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
        $PayReq['coin']     = bcmul($r_price, $rate, 2);

        // 绑定用户等级
        $PayReq['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id, $PayReq['coin'], time());

        if (isset($PayReq['appId'])) {
            unset($PayReq['appId']);
        }

        return [true, $PayReq];
    }

    private function getPayExpireTime($params, $limit)
    {
        $commPay = new CommPayModel();
        list($s, $time_expire) = $commPay->GetPayExpireTime($params, $limit);
        if (!$s) {
            return [false, $time_expire];
        }

        return [true, date("Y-m-d H:i:s", $time_expire)];
    }

    /**
     * 预支付ID
     * @param string $order_no
     * @param string $amount
     * @param array $order_params
     * @param int $pay_type
     * @return array
     * @throws \yii\db\Exception
     */
    private function getPrePayId(string $order_no, string $amount, array $order_params, int $pay_type): array
    {
        switch ($pay_type) {
            case OmainModel::PAY_BY_ALIPAY: // 支付宝
                return AliPayModel::getInstance()->pay($order_no, $amount, $order_params);
            case OmainModel::PAY_BY_MP_WX: // 中台（微信）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['WX'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['NATIVE'];
                $order_params['pay_type']    = $pay_type;
                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_BY_MP_ALIPAY: // 中台（支付宝）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['ALIPAY'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['NATIVE'];
                $order_params['pay_type']    = $pay_type;
                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_JD_BAITIAO: // 中台（京东白条）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['JD_BAITIAO'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['NATIVE'];
                $order_params['pay_type']    = $pay_type;

                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_JD_BAITIAO_APP: // 中台（京东白条）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['JD_BAITIAO'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['APP'];
                $order_params['pay_type']    = $pay_type;

                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params,true);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_JD_BAITIAO_PC: // 中台（京东白条）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['JD_BAITIAO'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['H5'];
                $order_params['pay_type']    = $pay_type;

                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params,true);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_JD_BAITIAO_H5: // 中台（京东白条）
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['JD_BAITIAO'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['H5'];
                $order_params['pay_type']    = $pay_type;

                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params,true);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_BY_MP_WEB_WX_H5:
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['WX'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['H5'];
                $order_params['pay_type']    = $pay_type;
                $order_params['device_type']    =  \Yii::$app->request->post('device_type', '');
                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params);
                CUtil::debug(json_encode($res),'pay_test');
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            case OmainModel::PAY_BY_MP_WEB_ALIPAY_H5:
                $order_params['uid']         = by::Phone()->getUidByUserId($order_params['user_id']);
                $order_params['pay_channel'] = MpPayModel::PAY_CHANNEL['ALIPAY'];
                $order_params['pay_method']  = MpPayModel::PAY_METHOD['H5'];
                $order_params['pay_type']    = $pay_type;
                list($status, $res) = MpPayModel::getInstance()->pay($order_no, $amount, $order_params);
                if ($status) {
                    return [true, $res['pay_url']];
                }
                return [false, $res];
            default:
                return [false, '不支持此支付方式'];
        }
    }
}