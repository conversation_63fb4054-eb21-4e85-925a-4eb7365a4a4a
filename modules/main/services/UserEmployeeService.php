<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\Device;
use app\components\Employee;
use app\components\EventMsg;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\UserBindJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\UserEmployeeModel;
use yii\db\Exception;

class UserEmployeeService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 员工等级
    const EMPLOYEE_LEVEL = [
        'NORMAL' => 1,  // 普通微笑大使
        'SUPER'  => 2   // 超级微笑大使
    ];

    // 员工状态
    const EMPLOYEE_STATUS = [
        'NORMAL' => 1, // 在职
        'STOP'   => 2  // 离职
    ];

    // 是否是员工
    const IS_EMPLOYEE = [
        'NO'  => 0,
        'YES' => 1
    ];

    // 是否黑名单
    const IS_BLACKLIST = [
        'YES' => 1,
        'NO'  => 2
    ];

    // 是否提示
    const IS_TIP = [
        'NO'  => 0,
        'YES' => 1
    ];

    // 积分类型
    const SCORE_TYPE = [
        'SCORE' => 1, // 积分
        'ENJOY' => 2, // 觅享分
        'SMILE' => 3  // 微笑分
    ];

    /**
     * 获取员工详情
     * @param $user_id
     * @return array
     * @throws \yii\db\Exception
     */
    public function getDetail($user_id): array
    {
        // 初始化返回数据
        $data = $this->initializeEmployeeData();

        // 获取用户数据
        $user_data = by::Phone()->getDataByUserId($user_id);
        if (empty($user_data)) {
            return [false, '用户不存在'];
        }

        // 更新uid
        $data['uid'] = $user_data['uid'];
        $data['user_id'] = $user_id;

        // 获取用户员工详情
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($user_data['uid']);
        if ($user){
            $this->populateEmployeeData($data, $user);
        }else{
            $data['is_zmds'] = self::IS_EMPLOYEE['NO'];
        }

        // 如果用户是员工，且当前绑定的员工未离职
        if ($user && $user['employee_status'] == self::EMPLOYEE_STATUS['NORMAL']) {
            $this->populateEmployeeData($data, $user);
        } else { // 员工未绑定
            list($status, $employee) = Employee::factory()->employeeInfo(['phone' => $user_data['phone']]);
            // 手机号可以查询到员工信息，且员工状态为在职
            if ($status && !empty($employee) && $employee['status'] == Employee::EMPLOYEE_STATUS['NORMAL']) { // status=1 在职员工
                // 绑定员工、先判断员工是否被其他用户绑定
                $oldUser = byNew::UserEmployeeModel()->getEmployeeInfo($employee['uid'],'employee_id');
                if ($oldUser && $oldUser['uid'] != $user_data['uid']) {
                    return [true, $data];
                }
                if ($this->bindEmployee($user_data['uid'], $employee['uid'], $employee['employee_no'], $employee['status'],$user_id)) {
                    // 初始化员工数据
                    $initEmployee = [
                        'level'              => self::EMPLOYEE_LEVEL['NORMAL'],  // 普通
                        'employee_status'    => self::EMPLOYEE_STATUS['NORMAL'], // 在职
                        'is_blacklist'       => self::IS_BLACKLIST['NO'],        // 非黑名单
                        'score'              => $employee['score'],              // 微笑分
                        'four_average_score' => 0,                               // 3个月平均分
                        'is_tip'             => 0,             // 是否提示
                        'type'               => 1,                               // 员工
                    ];
                    $this->populateEmployeeData($data, $initEmployee);
                }
            }
        }

        // 任务提示
        // $data['task_tips'] = $this->taskTips($data['level'], $employee['current_month_score']??0);

        return [true, $data];
    }

    public function apply($user_id){
        // 初始化返回数据
        $data = [
            'type' => 2,
            'level' => 1,
            'employee_status' => 2,
            'employee_id' => '',
            'ctime' => time(),
            'utime' => time(),
        ];

        // 获取用户数据
        $user_data = by::Phone()->getDataByUserId($user_id);
        if (empty($user_data)) {
            return [false, '用户不存在', []];
        }

        // 更新uid
        $data['uid'] = $user_data['uid'];
        $data['user_id'] = $user_id;
        // 获取用户员工详情
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($user_data['uid']);
        if($user){
            return [false, '您已经是追觅大使了', []];
        }
        // 没有数据时，创建追觅大使，先验一下是不是在职员工
        list($status, $employee) = Employee::factory()->employeeInfo(['phone' => $user_data['phone']]);
        if ($status && !empty($employee) && $employee['uid'] != "" && $employee['status'] == Employee::EMPLOYEE_STATUS['NORMAL']){
            // 是我们的员工，先判断员工是否被其他用户绑定
            $oldUser = byNew::UserEmployeeModel()->getEmployeeInfo($employee['uid'],'employee_id');
            if (empty($oldUser)){
                $data['employee_id'] = $employee['uid'];
                $data['employee_no'] = $employee['employee_no'];
                $data['employee_status'] = $employee['status'];
                $data['type'] = 1;
            }
        }
        $status = byNew::UserEmployeeModel()->saveData($data);
        if(!$status){
            return [false, '创建失败', []];
        }
        // 开启发奖励金
        EventMsg::factory()->run('openRichPlan', ['user_id' => $user_id]);

        // 判断一下他的父级是不是追觅大使
        $zinfo = byNew::UserBindModel()->getInfoByUserId($user_id);
        $r_id = $zinfo['user_id'] ?? 0;
        if ($r_id){
            $empRes = byNew::UserEmployeeModel()->getEmployeeInfo($user_id, 'user_id');
            if ($empRes){
                \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$empRes['user_id'],'order_no' => '','type'=>1,'field'=>'zmds_num','number'=>1,'addOrsubtract'=>'+']));
            }
        }
        
        return [true, '申请成功', []];
    }

    /**
     * 获取微笑分列表
     * @param int $user_id
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    public function getScoreList(int $user_id, array $params): array
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return [false, '用户不存在'];
        }

        // 查询员工管理表
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
        if (empty($user)) {
            return [false, '用户不存在'];
        }

        // 获取微笑分列表
        $param = [
            'uid'        => $user['employee_id'],
            'score_type' => $params['score_type'],
            'page'       => $params['page'],
            'page_size'  => $params['page_size'],
        ];
        list($status, $res) = Employee::factory()->scoreRecord($param);
        // 未获取到数据时，直接返回
        if (!$status || empty($res['list'])) {
            return [true, []];
        }

        // 整合数据
        $list = [];
        foreach ($res['list'] as $item) {
            $list[] = [
                'score'       => $item['handle_type'] == 1 ? $item['score'] : -$item['score'],
                'source'      => $item['source'],
                'create_time' => date('Y-m-d H:i:s', $item['create_time'])
            ];
        }
        return [true, ['list' => $list, 'total' => $res['total']]];
    }

    /**
     * 绑定员工
     * @param $uid
     * @param $employee_id
     * @param $employee_status
     * @return bool
     */
    public function bindEmployee($uid, $employee_id, $employee_no, $employee_status,$user_id): bool
    {
        // 保存员工信息
        return byNew::UserEmployeeModel()->saveData([
            'uid'             => $uid,
            'user_id'         => $user_id,
            'level'           => self::EMPLOYEE_LEVEL['NORMAL'],
            'employee_id'     => $employee_id,
            'employee_no'     => $employee_no,
            'employee_status' => $employee_status
        ]);
    }

    /**
     * 获取任务列表
     * @param int $userId
     * @param $platform
     * @return array
     * @throws \RedisException
     */
    public function getTaskList(int $userId, $platform,$version): array
    {
        // 获取任务列表
        list($status, $ret) = by::memberCenterModel()->CenterMessage('taskList', ['user_id' => $userId, 'platformSource' => $platform, 'type' => '','version' => $version]);


        if (!$status) {
            return [false, $ret];
        }

        // 包装任务列表
        $ret = $this->adapterTask($userId, $ret);
        return [$status, $ret];
    }

    /**
     * 已收到提示
     * @param int $userId
     * @return void
     * @throws Exception
     */
    public function closeTips(int $userId)
    {
        // 获取用户数据
        $uid = by::Phone()->getUidByUserId($userId);
        if (empty($uid)) {
            return;
        }

        // 获取用户员工详情
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
        if ($user) {
            $updateData = [
                'is_tip' => self::IS_TIP['NO'],
                'utime'  => time()
            ];
            byNew::UserEmployeeModel()->updateEmployeeInfo($user['id'], 'id', $updateData);
        }
    }
    
    /**
     * 检查非员工用户是否满足申请条件，即用户是否绑定了设备或注册了产品
     * @param int $user_id 用户ID
     * @return array
     */
    public function canApplyOld(int $user_id): array
    {
        try {
            $post = \Yii::$app->request->post();
            $offset       = $post['offset'] ?? '';
            $limit        = $post['limit'] ?? '';
            $sharedStatus = $post['sharedStatus'] ?? 1;
            $lang         = $post['lang'] ?? 'zh';
            $is_act = (int) ($post['is_act'] ?? 0);

            // 产品注册
            $reg = by::productReg()->userPList($user_id,$is_act);
            if (! empty($reg)) {
                return [true, 'success'];
            }
            
            // 设备列表
            list($status, $ret) = Device::factory()->run('list_v2', ['user_id' => $user_id, 'sharedStatus' => $sharedStatus, 'lang' => $lang, 'offset' => $offset, 'limit' => $limit]);
            if (! $status) {
                return [false, $ret];
            }
            
            $deviceTotal = $ret['page']['total'] ?? 0;
            if (empty($deviceTotal)) {
                return [false, '很抱歉，您不符合开通条件，请点击完成进行设备绑定或产品注册！'];
            }

            return [true, 'success'];
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('申请或获取追觅大使异常：user_id=%s error=%s', $user_id, $e->getMessage()));
            return [false, '操作失败，请稍后再试'];
        }
    }
    /**
     * 检查非员工用户是否满足申请条件，即用户是否绑定了设备或注册了产品
     * @param int $user_id 用户ID
     * @return array
     */
    public function canApply(int $user_id): array
    {
        try {
            // 最新规则，要求邀请人数为10人以上，先写1人，用于测试
            $inviteCount = byNew::UserBindModel()->getInviteCount($user_id);
            if ($inviteCount < 1) {
                return [false, '很抱歉，您不符合开通条件，请邀请更多好友加入追觅！'];
            }

            return [true, 'success'];
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('申请或获取追觅大使异常：user_id=%s error=%s', $user_id, $e->getMessage()));
            return [false, '操作失败，请稍后再试'];
        }
    }

    /**
     * 初始化员工数据
     * @return array
     */
    private function initializeEmployeeData(): array
    {
        return [
            'uid'                  => '',
            'level'                => self::EMPLOYEE_LEVEL['NORMAL'],
            'level_name'           => $this->getLevelName(self::EMPLOYEE_LEVEL['NORMAL']),
            'employee_status'      => self::EMPLOYEE_STATUS['NORMAL'],
            'employee_status_name' => $this->getEmployeeStatusName(self::EMPLOYEE_STATUS['NORMAL']),
            'is_blacklist'         => self::IS_BLACKLIST['NO'],
            'is_employee'          => self::IS_EMPLOYEE['NO'], // 是否是员工：0否，1是
            'score'                => 0,
            'type'                 => 0,   // 类型：1员工，2代理商
            'avg_score_3_months'   => 0,
            'is_tip'               => 0,    // 是否提示：0否，1是
            'tips'                 => '',   // 提示内容
        ];
    }

    /**
     * 填充员工数据
     * @param array &$data
     * @param array $employee
     * @return void
     */
    private function populateEmployeeData(array &$data, array $employee)
    {
        $data['level']                = $employee['level'] ?? self::EMPLOYEE_LEVEL['NORMAL'];
        $data['level_name']           = $this->getLevelName($data['level']);
        $data['employee_status']      = $employee['employee_status'] ?? self::EMPLOYEE_STATUS['NORMAL'];
        $data['employee_status_name'] = $this->getEmployeeStatusName($data['employee_status']);
        $data['is_blacklist']         = $employee['is_blacklist'] ?? self::IS_BLACKLIST['NO'];
        if ($data['employee_status'] == 1 ){
            $data['is_employee'] = self::IS_EMPLOYEE['YES'];
        }else{
            $data['is_employee'] = 0;
        }
        
        // 员工默认追觅大使
        $data['is_zmds']              = self::IS_EMPLOYEE['YES'];
        $data['score']                = $employee['score'] ?? 0;
        $data['avg_score_3_months']   = $employee['four_average_score'] ?? 0;
        $data['is_tip']               = $employee['is_tip'] ?? 0;
        $data['type']                 = $employee['type'] ?? 0;
        $data['tips']                 = $this->getTips($data['is_tip'], $data['level']);
    }

    /**
     * 获取等级名称
     * @param $level
     * @return string
     */
    private function getLevelName($level): string
    {
        switch ($level) {
            case self::EMPLOYEE_LEVEL['NORMAL']:
                $level_name = '普通微笑大使';
                break;
            case self::EMPLOYEE_LEVEL['SUPER']:
                $level_name = '超级员工';
                break;
            default:
                $level_name = '未知等级';
        }
        return $level_name;
    }

    /**
     * 员工状态
     * @param $employee_status
     * @return string
     */
    private function getEmployeeStatusName($employee_status): string
    {
        switch ($employee_status) {
            case self::EMPLOYEE_STATUS['NORMAL']:
                $employee_status_name = '有效';
                break;
            case self::EMPLOYEE_STATUS['STOP']:
                $employee_status_name = '离职';
                break;
            default:
                $employee_status_name = '未知状态';
        }
        return $employee_status_name;
    }

    /**
     * 适配任务列表
     * @param int $userId
     * @param array $items
     * @return array
     * @throws \RedisException
     */
    private function adapterTask(int $userId, array $items): array
    {
        // 任务描述映射
        $descriptions = $this->getEmployeeDescriptions();
        $scores       = $this->getEmployeeScores();

        // 添加新任务
        $this->addNewTasks($items);

        $data = [];
        foreach ($items as $index => $item) {
            if (!in_array($item['code'],
                [
                    'buy_main_machine',                          // 购买主机
                    'buy_parts',                                 // 购买配件
                    'employees_buy_inner_main_machine',          // 内购主机
                    'employees_buy_inner_parts',                 // 内购配件
                    'employees_invite_register',                 // 邀请注册
                    'employees_invite_buy',                      // 邀请购买
                    'buy_point_mall_goods',                      // 购买积分商城商品
                    'share_employee_poster',                     // 生成&分享“微笑大使”个人专属二维码海报
                    'write_inner_original_content',              // 撰写内部原创内容奖励
                    'special_activity_award',                    // 特殊活动奖励
                ])) {
                continue;
            }

            // 初始化微笑分
            $items[$index]['score'] = 0;
            if (isset($scores[$item['code']])) {
                $items[$index]['score'] = $scores[$item['code']];
            }

            // 任务描述
            if (isset($descriptions[$item['code']])) {
                $items[$index]['descri'] = $descriptions[$item['code']];
            }

            // 完成状态：生成&分享“微笑大使”个人专属二维码海报
            if ($item['code'] == 'share_employee_poster') {
                $redis                      = by::redis('core');
                $session_key                = AppCRedisKeys::getEmployeeShareCard('bind_employee', $userId);
                $items[$index]['completed'] = $redis->exists($session_key);
            }

            $data[] = $items[$index];
        }

        return $data;
    }

    /**
     * 获取微笑大使的任务描述
     * @return array
     */
    private function getEmployeeDescriptions(): array
    {
        return [
            'buy_main_machine'                 => '发放微笑分值=实际付款金额
发放积分值=实际付款金额*10
发放觅享分值=实际付款金额
(将在确认收货后第15天发放; 特殊活动期间，按「较高」发放标准结算）',
            'buy_parts'                        => '发放微笑分值=实际付款金额*10
发放积分值=实际付款金额*等级倍数(铜牌1倍，银牌1倍，金牌1.2倍，钻石1.5倍，黑金2倍)
发放觅享分值=实际付款金额*10
(将在确认收货后第15天发放; 特殊活动期间，按「较高」发放标准结算）',
            'employees_buy_inner_main_machine' => '发放微笑分值=实际付款金额
发放积分值=实际付款金额*10
发放觅享分值=实际付款金额
(将在确认收货后第15天发放; 特殊活动期间，按「较高」发放标准结算）',
            'employees_buy_inner_parts'        => '发放微笑分值=实际付款金额*10
发放积分值=实际付款金额*10
发放觅享分值=实际付款金额*10
(将在确认收货后第15天发放; 特殊活动期间，按「较高」发放标准结算）',
            'employees_invite_register'        => '单次完成任务奖励600微笑分，每位新用户成功注册后可获得奖励',
            'employees_invite_buy'             => '下单亲友需与“微笑大使”存在邀请绑定关系；每笔订单将在确认收货后第15天发放奖励',
        ];
    }

    /**
     * 添加新任务
     * @param array &$items
     */
    private function addNewTasks(array &$items): void
    {
        $newTasks = [
            [
                "name"                  => "购买积分商城商品",
                "code"                  => "buy_point_mall_goods",
                "timePeriodUnit"        => "every_time",
                "pointCollectType"      => "1",
                "timePeriodValue"       => 0,
                "eventLimitNum"         => 0,
                "period"                => 1,
                "awardType"             => 3,
                "descri"                => "发放微笑分值=实际付款金额
将在确认收货后第15天发放",
                "point"                 => 0,
                "grow"                  => 0,
                "score"                 => 0,
                "completed"             => false,
                "currentPeriodEventNum" => 0
            ],
            [
                "name"                  => "生成&分享“微笑大使”个人专属二维码海报",
                "code"                  => "share_employee_poster",
                "timePeriodUnit"        => "every_week",
                "pointCollectType"      => "1",
                "timePeriodValue"       => 0,
                "eventLimitNum"         => 0,
                "period"                => 1,
                "awardType"             => 3,
                "descri"                => "单次完成任务奖励200微笑分，限每周1次",
                "point"                 => 0,
                "grow"                  => 0,
                "score"                 => 200,
                "completed"             => false,
                "currentPeriodEventNum" => 0
            ],
            [
                "name"                  => "撰写内部原创内容奖励",
                "code"                  => "write_inner_original_content",
                "timePeriodUnit"        => "",
                "pointCollectType"      => "1",
                "timePeriodValue"       => 0,
                "eventLimitNum"         => 0,
                "period"                => 1,
                "awardType"             => 3,
                "descri"                => "根据具体项目/活动，按次发放",
                "point"                 => 0,
                "grow"                  => 0,
                "score"                 => 4000,
                "completed"             => false,
                "currentPeriodEventNum" => 0
            ],
            [
                "name"                  => "特殊活动奖励",
                "code"                  => "special_activity_award",
                "timePeriodUnit"        => "",
                "pointCollectType"      => "1",
                "timePeriodValue"       => 0,
                "eventLimitNum"         => 0,
                "period"                => 1,
                "awardType"             => 3,
                "descri"                => "以活动实际规则为准",
                "point"                 => 0,
                "grow"                  => 0,
                "score"                 => 0,
                "completed"             => false,
                "currentPeriodEventNum" => 0
            ],
        ];

        $items = array_merge($items, $newTasks);
    }

    private function getEmployeeScores(): array
    {
        return [
            'employees_invite_register'    => 600,
            'employees_invite_buy'         => 2000,
            'share_employee_poster'        => 200,
            'write_inner_original_content' => 4000,
        ];
    }

    /**
     * 获取提示内容
     * @param $is_tip
     * @param $level
     * @return string
     */
    private function getTips($is_tip, $level): string
    {
        if ($is_tip) {
            if ($level == self::EMPLOYEE_LEVEL['NORMAL']) {
                return '恭喜！|您已成为微笑大使|积极完成任务可以成为超级员工哦~';
            } elseif ($level == self::EMPLOYEE_LEVEL['SUPER']) {
                return '恭喜！|您已成为超级员工|不愧是你！再接再厉，解锁更多福利~';
            }
        }
        return '';
    }

    /**
     * 任务提示
     * 在身份为超级员工的当月25日，若微笑分值还未到达保级条件，将在微笑大使卡片下新增tips提示「微笑分较低，可能会失去超级员工身份，快去做任务提升微笑分吧～」，点击可定位到任务列表。
     * @param $level
     * @param $current_month_score
     * @return string
     */
    private function taskTips($level, $current_month_score): string
    {
        // 定义提示时间：当月25号
        $day = YII_ENV_PROD ? 25 : 10;

        // 是否达到当月的25号
        if (date('d') < $day) {
            return '';
        }

        // 是否是超级员工
        if ($level != self::EMPLOYEE_LEVEL['SUPER']) {
            return '';
        }

        // 是否达到保级条件
        if ($current_month_score >= 2000) { // 保级条件：当月微笑分值达到2000
            return '';
        }

        return '微笑分较低，可能会失去超级员工身份，快去做任务提升微笑分吧～';
    }
}
