<?php

namespace app\modules\main\services;

use app\models\by;
use app\models\Response;
use RedisException;
use yii\db\Exception;

class CateService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取分类名
     * @param $cateId
     * @return string
     * @throws RedisException
     * @throws \yii\db\Exception
     */
    public function GetCateNameByCateId($cateId): string
    {
        // 获取二级分类
        $item = by::cateModel()->getOneInfoById($cateId);
        if (empty($item)) {
            return '';
        }
        return $item['name'] ?? '';
    }


    /**
     * @param $c_id
     * @param $fds
     * @param $sort
     * @param $platformId
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取类目下商品信息、价格排序、筛选
     */
    public function getPartList($c_id, $fds = [], $sort = 1, $platformId = 1, $is_internal_purchase = 0): array
    {
        // 过滤和去重功能域数组   适配类目
        $fds = array_filter(array_unique($fds));

        // 检查分类ID是否为数字
        if (!is_numeric($c_id)) {
            return [false, '参数有误'];
        }

        // 获取分类信息
        $part_info = by::gCateModel()->getCateInfoByCid($c_id);

        // 如果没有分类信息，返回空数组
        if (!$part_info) {
            return [true, []];
        }

        $aData = [];

        foreach ($part_info as $value) {
            // 如果配件下架或已删除，跳过此条商品
            if ($value['status'] == 1 || $value['is_del'] == 1) {
                continue;
            }

            $sku = $value['sku'] ?? '';

            // 获取配件的主信息
            $aMain = by::Gmain()->GetOneBySku($sku);
            if (!$aMain) {
                continue;
            }

            // 获取完整的主信息
            $aMain = by::Gmain()->GetAllOneByGid($aMain['id'] ?? '', false);
            if (!$aMain || !in_array($platformId, $aMain['platform_ids']) || $aMain['status'] == 1 || $aMain['is_internal_purchase'] != $is_internal_purchase) { // 增加判断内购商品的条件
                continue;
            }

            // 获取配件关联的主SKU信息
            $main_skus    = by::mainPartModel()->getMainByPartSku($sku);
            $main_sku_str = implode(',', array_column($main_skus, 'main_sku'));

            // 获取主SKU关联的分类信息
            $cidInfos = by::gCateModel()->getCidInfosBySkus($main_sku_str);
            $cids     = array_column($cidInfos, 'id');

            // 检查功能域和分类ID是否有交集
            if (!empty($fds) && !array_intersect($fds, $cids)) {
                continue;
            }

            // 生成适配机型名称
            $mainCidNames          = implode('、', array_unique(array_filter(array_column($cidInfos, 'name'))));
            $aMain['machine_type'] = $mainCidNames;
            $aData[]               = $aMain;
        }

        // 按照排序规则对数据进行排序
        if ($aData) {
            usort($aData, function ($a, $b) use ($sort) {
                if ($sort == 1) {
                    return $a['price'] <=> $b['price'];
                } elseif ($sort == 2) {
                    return $b['price'] <=> $a['price'];
                } else {
                    return $b['sort'] <=> $a['sort'];
                }
            });
        }

        return [true, $aData];
    }



    /**
     * @param int $type （0全部、1主机、2配件）
     * @param bool $children 是否需要子集
     * @param bool $isCheckStatus 是否检查状态
     * @param int $platformId 平台id
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取类目列表
     */
    public function getList(int $type = 0, bool $children = true, bool $isCheckStatus = true, int $platformId = 1): array
    {
        $aData = by::cateModel()->getList($type);

        if (!$children || empty($aData)) {
            return [true, $aData];
        }

        // 当前平台下所有商品 GID
        $platformGids = by::Gmain()->GetList(1, 9999999, '', -1, -1, '', '', -1, ['platformIds' => [$platformId]]);

        if ($type == by::cateModel()::CATE_TYPE['part']) {
            foreach ($aData as $k => $v) {
                $goods = by::gCateModel()->getCateInfoByCid($v['id']);

                // 过滤出在售的商品并重建 SKU 到商品的映射
                $cateGoods = array_filter($goods, function ($good) {
                    return $good['status'] == by::Gmain()::STATUS['ON_SALE'];
                });

                $cateGoods = array_column($cateGoods, null, 'sku');

                // 当前类目下所有 SKU
                $skuArr = array_keys($cateGoods);

                // 当前类目下所有 GID
                $gIdArr = by::Gmain()->GetOneBySkus($skuArr);

                //当前平台售卖商品ID
                $currentPlatformGids = array_intersect($gIdArr, $platformGids);

                // 如果需要检查状态，并且当前类别等级为 3 且当前平台没有在售商品，移除该类别
                if ($isCheckStatus && $v['level'] == 3 && empty($currentPlatformGids)) {
                    unset($aData[$k]);
                }
            }
        }

        // 处理子级分类
        $aData = $this->childrenLevel($aData);

        // 应用额外条件
        $aData = $this->__getCondition($aData, $type);

        return [true, $aData];
    }



    /**
     * @param $data
     * @param $pid
     * @return array
     * 递归处理子集
     */
    public function childrenLevel($data, $pid = 0): array
    {
        $arr = [];
        foreach ($data as $key => $val) {
            $val = Response::responseList($val, ['ctime' => 'int', 'utime' => 'int', 'dtime' => 'int']);
            //等于0的时候代表是顶级类目
            if ($val['pid'] == $pid && $val['pid'] != $val['id']) {
                //递归处理子集
                $children = $this->childrenLevel($data, $val['id']) ?? [];
                $children && $val['children'] = $children;
                $arr[] = $val;
            }
        }

        return $arr;
    }


    /**
     * @param $data
     * @param int $type
     * @return mixed
     * 取指定数据,如果没有就取所有
     */
    public function __getCondition($data, int $type = 0)
    {
        foreach ($data as $value) {
            if ($value['tag_name'] == (by::cateModel()::MACHINE_TYPE[$type] ?? '')) {
                return $value;
            }
        }
        return $data;
    }
}