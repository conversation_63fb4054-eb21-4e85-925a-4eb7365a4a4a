<?php

namespace app\modules\main\services;

use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\components\Device;
use app\components\WeiXin;
use app\exceptions\GiftCardGoodsException;
use app\jobs\FreezeCardJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\models\GiftCardExpendRecordModel;
use app\modules\wares\models\GiftCardResourcesModel;
use app\modules\wares\models\GiftUserCardsModel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use RedisException;
use rmrevin\yii\fontawesome\FA;
use yii\db\Exception;

class GiftCardService
{
    private static $_instance = NULL;


    private function __construct()
    {

    }


    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    const SHARE_KEY = 'DREAME_GIFT_CARD';
    const SHARE_PATH = YII_ENV_PROD ? 'pagesB/giftCard/getCard' : 'pagesB/giftCard/getCard';

    /**
     * 激活卡片
     * @param int $userId
     * @param string $cardPassword
     * @return void
     * @throws GiftCardGoodsException
     */
    public function activateCard(int $userId, string $cardPassword)
    {
        // 0、卡密格式化
        $cardPassword = $this->formatCardPassword($cardPassword);

        // 1、卡片校验
        $resource = $this->validateCard($cardPassword);

        // 2、激活
        $db    = by::dbMaster();
        $trans = $db->beginTransaction();

        try {
            $cardGoods = byNew::GiftCardGoods()->getGoodsById($resource['card_goods_id']);
            if (empty($cardGoods)) {
                throw new GiftCardGoodsException("该卡密异常，卡商品不存在");
            }

            // 卡资源
            $cardResources = json_decode($cardGoods['card_resource'], true);
            // 生产要插入的数据
            $data = $this->getCardData($userId, $cardResources);

            by::dbMaster()->createCommand()->batchInsert(GiftUserCardsModel::tableName(),
                ['user_id', 'card_id', 'card_no', 'amount', 'type', 'expire_time', 'ctime', 'utime']
                , $data)
                ->execute();

            $update = [
                'status'      => GiftCardResourcesModel::STATUS['ACTIVATED'],
                'act_user_id' => $userId,
                'act_time'    => time(),
                'utime'       => time()
            ];

            by::dbMaster()->createCommand()->update(GiftCardResourcesModel::tableName(), $update, ['card_password' => $cardPassword])->execute();

            $trans->commit();

            byNew::GiftUserCards()->__delUserGiftCardListRedisKey($userId);
        } catch (GiftCardGoodsException $e) {
            $trans->rollBack();
            throw new GiftCardGoodsException($e->getMessage());
        } catch (\Exception $e) {
            $trans->rollBack();
            // 记录日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug('ERROR|' . $error, 'err.giftcard.msg');
            throw new GiftCardGoodsException('绑定卡片失败');
        }

    }

    /**
     * 校验卡片
     * @param string $cardPassword
     * @return array
     * @throws GiftCardGoodsException
     */
    public function validateCard(string $cardPassword): array
    {
        // 0、卡密格式化
        $cardPassword = $this->formatCardPassword($cardPassword);

        $resource = byNew::GiftCardResources()->getResourceByCardPassword($cardPassword);
        if (empty($resource)) {
            throw new GiftCardGoodsException("该卡密不存在，请重新输入");
        }

        if ($resource['status'] == GiftCardResourcesModel::STATUS['ACTIVATED']) {
            throw new GiftCardGoodsException("该卡密已绑定，请重新输入");
        }

        if ($resource['status'] == GiftCardResourcesModel::STATUS['INVALID']) {
            throw new GiftCardGoodsException("该卡密已废弃，请重新输入");
        }

        // 校验卡商品
        $cardGoods = byNew::GiftCardGoods()->getGoodsById($resource['card_goods_id']);
        if (empty($cardGoods)) {
            throw new GiftCardGoodsException("该卡密异常，卡商品不存在");
        }

        // 校验卡商品有效期
        if ($cardGoods['end_time'] < time()) {
            throw new GiftCardGoodsException("无法绑定，已超过礼品卡绑定时间");
        }

        // 校验卡商品有效期
        if ($cardGoods['start_time'] > time()) {
            throw new GiftCardGoodsException("无法绑定，尚未到达礼品卡绑定时间");
        }

        return $resource;
    }


    /**
     * 生成卡数据
     * @param int $userId
     * @param array $resources
     * @return array
     * @throws GiftCardGoodsException
     * @throws \Exception
     */
    private function getCardData(int $userId, array $resources): array
    {
        // 获取卡片ID
        $cardIds = array_unique(array_column($resources, 'card_id'));

        $cards = byNew::GiftCard()->getCardsByIds($cardIds);
        if (empty($cards) || (count($cards) != count($cardIds))) {
            throw new GiftCardGoodsException('卡资源异常');
        }

        $cards = array_column($cards, null, 'id');

        // 生成唯一卡号
        $total   = array_sum(array_column($resources, 'card_num'));
        $cardNos = $this->generateUniqueCardNos($total);

        // 准备数据
        $data = [];
        $now  = time();
        foreach ($resources as $resource) {
            $card       = $cards[$resource['card_id']];
            $expireTime = strtotime(date('Y-m-d')) + ($card['expire_days'] + 1) * 86400 - 1;

            foreach (array_slice($cardNos, 0, $resource['card_num']) as $cardNo) {
                $data[] = [
                    'user_id'     => $userId,
                    'card_id'     => $card['id'],
                    'card_no'     => $cardNo,
                    'amount'      => $card['amount'],
                    'type'        => $card['type'],
                    'expire_time' => $expireTime,
                    'ctime'       => $now,
                    'utime'       => $now,
                ];
            }

            $cardNos = array_slice($cardNos, $resource['card_num']);
        }

        return $data;
    }

    /**
     * 生成唯一卡号
     * @param int $total
     * @return array
     * @throws \Exception
     */
    private function generateUniqueCardNos(int $total): array
    {
        $cardNos   = [];
        $batchSize = 100;

        while (count($cardNos) < $total) {
            $batchCardNos = [];
            while (count($batchCardNos) < $batchSize && count($cardNos) + count($batchCardNos) < $total) {
                $batchCardNos[] = $this->createCardNo('DME');
            }

            $uniqueCardNos = byNew::GiftUserCards()->getUserCardsByCardNos($batchCardNos);
            $uniqueCardNos = array_column($uniqueCardNos, 'card_no');

            foreach ($batchCardNos as $cardNo) {
                if (!in_array($cardNo, $uniqueCardNos)) {
                    $cardNos[] = $cardNo;
                }
            }
        }

        return $cardNos;
    }

    /**
     * 生成卡号
     * e.g. DME231204C525DD6FD421
     * @param string $prefix
     * @return string
     * @throws \Exception
     */
    private function createCardNo(string $prefix = 'DME'): string
    {
        $date = date("ymd");

        $random = bin2hex(random_bytes(6));

        return strtoupper($prefix . $date . $random);

    }

    /**
     * 分享卡片列表
     * @param int $userId
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getShareCardList(int $userId): array
    {
        $userCards = byNew::GiftUserCards()->getUserCardsByShareId($userId);
        if (empty($userCards)) {
            return [];
        }

        // 获取用户信息
        $userIds = array_column($userCards, 'user_id');
        $users   = by::users()->getListByUserIds($userIds, ['user_id', 'nick', 'avatar']);
        $users   = array_column($users, null, 'user_id');

        // 获取卡的信息
        $cardIds = array_column($userCards, 'card_id');
        $cards   = byNew::GiftCard()->getCardsByIds($cardIds);
        $cards   = array_column($cards, null, 'id');

        $data = [];
        foreach ($userCards as $userCard) {
            // 用户信息
            $user_id = $userCard['user_id'];
            $user    = $users[$user_id] ?? [];
            // 卡信息
            $card_id = $userCard['card_id'];
            $card    = $cards[$card_id] ?? [];
            $data[]  = [
                'id'       => $userCard['id'],
                'user_id'  => $userCard['user_id'],
                'nickname' => $user['nick'] ?? '',
                'avatar'   => $user['avatar'] ?? '',
                'card_id'  => $userCard['card_id'],
                'card_no'  => $userCard['card_no'],
                'card'     => [
                    'name'      => $card['name'] ?? '',
                    'web_name'  => $card['web_name'] ?? '',
                    'image'     => $card['image'] ?? '',
                    'type'      => $card['type'] ?? '',
                    'amount'    => bcdiv($card['amount'] ?? '', 100, 2),
                    'goods_ids' => $card['goods_ids'] ?? '',
                ],
                'ctime'    => date('Y.m.d H:i:s', $userCard['ctime']),
            ];
        }
        return $data;
    }


    /**
     * @param $userId
     * @param $status
     * @param array $goodsId
     * @param int $type
     * @return array
     * @throws RedisException 获取用户卡列表
     */
    public function getUserCardList($userId, $status, $goodsId = [], $type = 0): array
    {
        try {
            if (!is_array($goodsId)) {
                $goodsId = explode(',', $goodsId);
            }

            $list = byNew::GiftUserCards()->giftCardList($userId, $status);
            $totalAmount = 0;

            $processedList = array_map(function ($item) use ($goodsId, $status, &$totalAmount, $type) {
                if (empty($item['sharer_id'])) {
                    $item['content'] = '';
                }
                // back1：可用 2：不可用
                $item['back'] = 1;

                $item['card_status'] = $this->getCardStatus($item);
                $item['amount'] = bcdiv($item['amount'], 100, 2);

                $cardInfo = byNew::GiftCard()->getCardInfo($item['card_id']);
                ['web_name' => $webName, 'image' => $image, 'type' => $cardType,'sku'=>$sku] = $cardInfo;

                $item['web_name'] = $webName;
                $item['image'] = $image;
                $item['type'] = $cardType;
                $item['sku'] = $sku;

                unset($item['ctime'], $item['utime'], $item['status']);

                $goodsIds = explode(',', $cardInfo['goods_ids']);
                $item['goods_ids'] = $goodsIds;
                if ($status == 1) {
                    $totalAmount = bcadd($totalAmount, $item['amount'], 2);
                    $goodsId = array_filter($goodsId);
                    if ($cardType == 2 && (count($goodsId) > 1 || !empty(array_diff($goodsId, $goodsIds)))) {
                        $item['back'] = 2;
                    }
                }

                //预售尾款订单 type == 1 普通订单type == 2
                if ($type == 1) {
                    $cardType == 2 && $item['back'] = 2;
                }

                return $item;
            }, $list);



            $processedList = array_values(array_filter($processedList));

            //订单中点击 不可用卡的在最后面
            if ($type) {
                usort($processedList, function ($a, $b) {
                    // 将back值为1的元素排在前面，值为2的元素排在后面
                    return $a['back'] - $b['back'];
                });
            }


            return [true, ['list' => $processedList, 'totalAmount' => $totalAmount]];
        } catch (Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.user_card_list');
            return [false, '获取礼品卡列表失败'];
        }
    }



    /**
     * @param $id
     * @return array
     * 获取用户卡详情信息
     */
    public function getUserCardInfo($id): array
    {
        try {
            //用户卡信息
            $userCardInfo = byNew::GiftUserCards()->userGiftCardInfo($id);
            //卡资源信息
            $cardInfo = byNew::GiftCard()->getCardInfo($userCardInfo['card_id']);

            //非分享得到的不展示祝福语
            if (empty($userCardInfo['sharer_id'])) {
                $userCardInfo['content'] = '';
            }
            // 获取卡状态
            $cardStatus                  = $this->getCardStatus($userCardInfo);
            $userCardInfo['card_status'] = $cardStatus;
            //金额转为元
            $userCardInfo['amount'] = bcdiv($userCardInfo['amount'],100,2);
            //面值
            $userCardInfo['face_value'] = bcdiv($cardInfo['amount'],100,2);
            // 使用解构赋值提取数组元素
            ['web_name' => $webName, 'image' => $image, 'desc' => $desc, 'type' => $type,'sku'=>$sku] = $cardInfo;
            $goodsIds = explode(',', $cardInfo['goods_ids']);

            // 更新 $userCardInfo 中的字段
            $userCardInfo = array_merge($userCardInfo, [
                'web_name' => $webName,
                'image'    => $image,
                'desc'     => $desc,
                'goods_ids'=> $goodsIds,
                'sku'      => $sku,
                'type'     => $type
            ]);

            //没消费
            $userCardInfo['has_expend'] = 0;
            //使用记录
            $expendRecord = byNew::GiftCardExpendRecord()->getRecordsByUserCardId($id);
            if ($expendRecord) {
                $userCardInfo['has_expend'] = 1;
            }


            unset($userCardInfo['ctime'], $userCardInfo['utime'], $userCardInfo['status']);

            return [true, $userCardInfo];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.user_card_info');
            return [false, '获取卡信息失败'];
        }
    }


    /**
     * @param array $userCardInfo
     * @return int
     * 获取卡状态
     */
    private function getCardStatus(array $userCardInfo): int
    {
        if ($userCardInfo['expire_time'] < time()) {
            return 2; // 过期状态
        } elseif ($userCardInfo['status'] == 4) {
            return 3; // 已使用状态
        } elseif ($userCardInfo['sharer_id'] != 0) {
            return 1; // 分享状态
        } elseif ($userCardInfo['status'] == 2) {
            return 4; // 分享待领取状态
        } elseif ($userCardInfo['status'] == 3) {
            return 5; // 分享已领取状态
        } else {
            return 0; // 默认状态为未使用
        }
    }


    public function CheckContent($user_id, $content): array
    {
        // 正则校验祝福语是否含链接
        if (preg_match('/https?:\/\/[^\s]+/', $content)) {
            return [false, '您输入的内容含链接，请删除后重试'];
        }

        //敏感词校验
        list($status, $ret) = Device::factory()->run('aligreen_content', ['user_id' => $user_id, 'content' => $content]);
        if ($ret['passed'] != 1) {
            return [false, '您输入的内容含敏感词，请删除后重试'];
        }

        if (strlen($content) > 150) {
            return [false, '您输入的内容过长，请删除后重试'];
        }

        return [true, []];
    }


    /**
     * @param $userId
     * @param $id
     * @param $content
     * @return array
     * @throws Exception
     * @throws RedisException
     * 分享卡片
     * 先查卡是否存在于有效的分享链接
     * 存在：删除有效链接 重新生成
     * 不存在：生成一个小时有效链接
     * 冻结 防止持卡人使用卡
     * 异步插入延时队列，解冻卡，修改卡状态
     */
    public function ShareGiftCard($userId, $id, $content): array
    {
        $transaction = by::dbMaster()->beginTransaction();

        try {
            $redis    = by::redis();
            $redisKey = AppCRedisKeys::userShareCardUniqueKey($userId, $id);

            // 判断是否存在其他分享链接
            $isExists = $redis->exists($redisKey);

            // 存在删除key 生成新的key（使前一个链接失效，确保只有最后分享的链接有效）
            if ($isExists) {
                $redis->del($redisKey);
            }

            //用户卡信息
            $cardInfo = byNew::GiftUserCards()->userGiftCardInfo($id);

            //使用记录
            $expendRecord = byNew::GiftCardExpendRecord()->getRecordsByUserCardId($id);
            if ($expendRecord) {
                throw new GiftCardGoodsException('卡已使用，不可分享');
            }

            if (empty($cardInfo)) {
                throw new GiftCardGoodsException('卡不存在，不可分享');
            }

            if ($cardInfo['user_id'] != $userId || $cardInfo['status'] == GiftUserCardsModel::STATUS['SHARED']) {
                throw new GiftCardGoodsException('卡不属于你，不可分享');
            }

            $params = [
                'user_id' => $userId,
                'id'      => $id,
                'time'    => intval(START_TIME),
            ];

            // 返回前端加密卡ID + 用户ID + 时间戳
            $encode = CUtil::encrypt(json_encode($params, 320), self::SHARE_KEY);

            // 存入redis，防止重复分享
            $redis->set($redisKey, $encode, ['EX' => GiftUserCardsModel::FROZEN_TIME]);

            $path            = self::SHARE_PATH;
            $query['encode'] = $encode;
            list($s, $url) = $this->__createUrlLink($path, $query);
            if (!$s) {
                throw new GiftCardGoodsException($url);
            }

            // $url = 'https://www.sojson.com/';
            // 返回前端参数
            $data['encode'] = $encode;
            $data['url']    = $url;

            // 冻结卡 防止自己使用卡
            byNew::GiftUserCards()->updateUserCard($id, $userId, GiftUserCardsModel::STATUS['FROZEN']);

            // 插入祝福语数据
            by::dbMaster()->createCommand()->insert(byNew::GiftCardsBless()::tbName(), [
                'encode'        => $encode,
                'bless_content' => $content,
                'ctime'         => time(),
            ])->execute();

            // 异步插入队列，解冻卡，修改卡状态
            \Yii::$app->queue->delay(GiftUserCardsModel::FROZEN_TIME)->push(new FreezeCardJob([
                'user_id' => $userId,
                'id'      => $id,
            ]));

            $transaction->commit();

            return [true, $data];
        } catch (GiftCardGoodsException $giftCardGoodsException) {
            $transaction->rollBack();
            return [false, $giftCardGoodsException->getMessage()];
        }
    }


    private function __createUrlLink($path, $query): array
    {
        $queryStr = http_build_query($query, '', '&', PHP_QUERY_RFC3986);
        //生产分享链接
        list($s, $url) = WeiXin::factory()->UrlLink($path, $queryStr);
        if (!$s) {
            return [false, $url];
        }

        return [true, $url];
    }

    /**
     * @param $enCode
     * @param $user_id
     * @return array
     * 领取人查看卡详情
     */
    public function getShareGiftCardInfo($enCode, $user_id): array
    {
        try {
            // 解密参数
            $enCode      = urldecode($enCode);
            $deCode      = CUtil::decrypt($enCode, self::SHARE_KEY);
            $param       = json_decode($deCode, true);
            $shareUserId = $param['user_id'] ?? '';
            $id          = $param['id'] ?? '';

            //默认0为可领取
            $cardStatus = 0;

            // 校验链接是否有效
            $redis       = by::redis();
            $redisKey    = AppCRedisKeys::userShareCardUniqueKey($shareUserId, $id);
            $redisEnCode = $redis->get($redisKey);
            if ($enCode != $redisEnCode) {
                $cardStatus = 2;//链接已失效
            }

            // 用户信息
            $user = by::users()->getOneByUid($shareUserId);

            // 用户卡信息
            $userCardInfo = byNew::GiftUserCards()->userGiftCardInfo($id);
            if (empty($userCardInfo)) {
                return [false, '链接已失效'];
            }

            //祝福语信息
            $blessData = byNew::GiftCardsBless()->getBlessContent($enCode);

            // 卡资源信息
            $cardInfo = byNew::GiftCard()->getCardInfo($userCardInfo['card_id']);

            //获取当前用户Uid
            $mallInfo = by::usersMall()->getInfoByUserId($user_id);

            //校验用户卡状态是已经领取还是没有被领取
            //已领取是被我自己领取还是他人领取
            if ($userCardInfo['status'] == GiftUserCardsModel::STATUS['SHARED']) {
                //如果是当前用户领取的状态为已领取 否则 已被其他人领取
                $drawData = byNew::GiftUserCards()::find()->where(['user_id' => $user_id, 'sharer_id' => $shareUserId, 'card_no' => $userCardInfo['card_no']])->one();
                if (empty($drawData)) {
                    $cardStatus = 3;//已被其他人领取
                } else {
                    $cardStatus = 1;//当前用户已领取
                }
            }

            // 分享卡详情信息
            $cardDetailInfo = [
                'content'  => $blessData['bless_content'] ?? '',
                'amount'   => bcdiv($userCardInfo['amount'], 100, 2),
                'image'    => $cardInfo['image'] ?? '',
                'nick'     => $user['nick'] ?? '',
                'uid'      => $mallInfo['uid'] ?? '',
                'avatar'   => $user['avatar'] ?? '',
                'type'     => $cardInfo['type'] ?? '',
                'web_name' => $cardInfo['web_name'] ?? '',
                'status'   => $cardStatus
            ];

            return [true, $cardDetailInfo];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.share_card_info');
            return [false, '链接已失效'];
        }
    }


    /**
     * 领取卡
     * @param $userId
     * @param $enCode
     * @throws Exception
     * @throws GiftCardGoodsException
     * @throws RedisException
     */
    public function receiveGiftCard($userId, $enCode)
    {
        // 签名解码
        $enCode = urldecode($enCode);
        $deCode = CUtil::decrypt($enCode, self::SHARE_KEY);
        $param  = json_decode($deCode, true);
        if (!isset($param['id'], $param['user_id'])) {
            throw new GiftCardGoodsException('已失效');
        }

        // 分享人ID
        $id       = $param['id'];
        $sharerId = $param['user_id'];

        // 校验信息
        $redis       = by::redis();
        $redisKey    = AppCRedisKeys::userShareCardUniqueKey($sharerId, $id);
        $redisEnCode = $redis->get($redisKey);
        if ($enCode != $redisEnCode) {
            throw new GiftCardGoodsException('已失效');
        }

        // 校验信息
        $userCard = byNew::GiftUserCards()->userGiftCardInfo($id);
        if (in_array($userCard['status'], [GiftUserCardsModel::STATUS['SHARED'], GiftUserCardsModel::STATUS['USED']])) {
            if ($userId == $userCard['user_id']) {
                throw new GiftCardGoodsException('已领取');
            } else {
                throw new GiftCardGoodsException('已被其他人领取');
            }
        }

        // 领取卡片
        $db   = by::dbMaster();
        $tran = $db->beginTransaction();
        try {
            // 1. 更新记录
            $updateData = [
                'status'  => GiftUserCardsModel::STATUS['SHARED'],
                'content' => '',
                'utime'   => time(),
            ];
            byNew::GiftUserCards()->saveData($updateData, $id, $db);

            // 生日祝福语
            $content = byNew::GiftCardsBless()->getBlessContent($enCode);

            // 2. 插入新记录
            $newData = [
                'user_id'     => $userId,
                'sharer_id'   => $sharerId,
                'card_id'     => $userCard['card_id'],
                'card_no'     => $userCard['card_no'],
                'amount'      => $userCard['amount'],
                'type'        => $userCard['type'],
                'status'      => GiftUserCardsModel::STATUS['RECEIVED'],
                'content'     => $content['bless_content'] ?? '',
                'expire_time' => $userCard['expire_time'],
                'ctime'       => time(),
                'utime'       => time(),
            ];
            byNew::GiftUserCards()->saveData($newData, 0, $db);

            $tran->commit();
            // 清除缓存
            $redis->del($redisKey);
        } catch (\Exception $e) {
            $tran->rollback();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.receive-gift-card');
            throw new GiftCardGoodsException('领取失败');
        }
    }

    /**
     * 礼品卡的消费记录
     * @param $userId
     * @param $userCardId
     * @return array
     * @throws Exception
     * @throws GiftCardGoodsException
     * @throws RedisException
     */
    public function getExpendRecord($userId, $userCardId): array
    {
        // 校验卡片是否属于该用户
        $userCard = byNew::GiftUserCards()->userGiftCardInfo($userCardId);
        if ($userCard['user_id'] != $userId) {
            throw new GiftCardGoodsException('卡片不存在');
        }

        $card_no = $userCard['card_no'];
        $card_type = $userCard['type'];

        // 消费记录
        $records = byNew::GiftCardExpendRecord()->getRecordsByUserCardId($userCardId);
        // 返回数据
        $list = [];
        $type_use = GiftCardExpendRecordModel::TYPES['USE'];
        foreach ($records as $record) {
            $list[] = [
                'order_no'       => $record['order_no'],
                'expend_amount'  => ($record['type'] == $type_use ? '-' : '+') . '￥' . abs(bcdiv($record['expend_amount'], 100, 2)),
                'current_amount' => bcdiv($record['current_amount'], 100, 2),
                'type'           => $record['type'],
                'create_time'    => date('Y.m.d H:i:s', $record['ctime']),
            ];
        }

        return compact('card_no', 'card_type', 'list');
    }



    //---------------礼品卡使用------------------------------------

    /**
     * @param $giftCardIds
     * @param $gcombines
     * @return array
     * @throws Exception
     * @throws RedisException 组装礼品卡使用数据
     */
    public function getGiftCardData($giftCardIds, $gcombines): array
    {
        $count         = count($giftCardIds);
        $goodsCount    = count($gcombines);
        $giftCardInfos = [];
        $useCardType   = 1; // 默认使用金额卡
        $giftCardData  = [];

        foreach ($giftCardIds as $giftCardId) {
            $giftCardInfos[] = byNew::GiftUserCards()->userGiftCardInfo($giftCardId);
        }

        // 按照礼品卡 expire_time 升序排列
        usort($giftCardInfos, function ($a, $b) {
            return $a['expire_time'] - $b['expire_time'];
        });

        // 修改抵扣商品的顺序  优先扣除要过期的
        foreach ($giftCardInfos as $giftCardInfo) {
            if ($giftCardInfo['expire_time'] < time()) {
                return [false, '礼品卡已过期，请检查', $useCardType];
            }

            if (in_array($giftCardInfo['status'], [GiftUserCardsModel::STATUS['FROZEN'], GiftUserCardsModel::STATUS['SHARED'], GiftUserCardsModel::STATUS['USED']])) {
                return [false, '礼品卡已使用，请检查', $useCardType];
            }

            if ($giftCardInfo['type'] == 2) {
                $useCardType = 2;
                // 抵扣卡只能单张使用
                if ($count > 1 || $goodsCount > 1 || array_sum(array_column($gcombines, 'num')) > 1) {
                    return [false, '指定兑换卡只可以购买单件商品时使用', $useCardType];
                }

                // 卡资源信息
                $cardInfo = byNew::GiftCard()->getCardInfo($giftCardInfo['card_id']);

                // 获取商品ID
                $goodsId = ($goodsCount == 1) ? implode(",", array_column($gcombines, 'gid')) : '';

                // 判断抵扣卡是否适用此商品
                $goods_ids = explode(",", $cardInfo['goods_ids']);
                if (!in_array($goodsId, $goods_ids)) {
                    return [false, '指定兑换卡不适用此商品', $useCardType];
                }

                // 抵扣卡的金额为商品总价
                $giftCardData[$giftCardInfo['id']] = $gcombines[0]['tprice'];
            } else {
                // 其他类型的礼品卡
                $giftCardData[$giftCardInfo['id']] = $giftCardInfo['amount'];
            }
        }

        return [true, $giftCardData, $useCardType];
    }



    /**
     * 应用礼品卡
     * @param $orderAmount
     * @param $selectedCardIds
     * @param $giftCardData
     * @return array 包含订单金额、礼品卡数据和实际抵扣金额的数组
     */
    public function applyGiftCards($orderAmount = 0, $selectedCardIds = [], $giftCardData = [])
    {
        // 计算选中的礼品卡总金额
        $selectedCardTotal = 0;
        foreach ($selectedCardIds as $selectedCardId) {
            if (isset($giftCardData[$selectedCardId])) {
                $selectedCardTotal += $giftCardData[$selectedCardId];
            }
        }

        // 计算实际抵扣金额
        $discountAmount       = min($selectedCardTotal, $orderAmount);
        $actualDiscountAmount = $orderAmount - $discountAmount;

        // 更新礼品卡余额
        foreach ($selectedCardIds as $selectedCardId) {
            if (isset($giftCardData[$selectedCardId])) {
                $amountToDeduct                = min($giftCardData[$selectedCardId], $discountAmount);
                $giftCardData[$selectedCardId] -= $amountToDeduct;
                $discountAmount                -= $amountToDeduct;
            }
        }

        // 返回抵扣后的余额信息
        return [
            'orderAmountAfterDiscount'   => max(0, $orderAmount - $discountAmount),//订单金额
            'giftCardDataAfterDeduction' => $giftCardData,                         //礼品卡 ID=>剩余金额
            'actualDiscountAmount'       => $actualDiscountAmount,                 //抵扣后金额
            'allDiscountAmount'          => $orderAmount - $actualDiscountAmount,  //抵扣金额
        ];
    }
    //--------------------------------------------------


    /**
     * @param $giftCardIds
     * @param $reduce
     * @param $price
     * @param $gcombines
     * @param $db
     * @return array
     * @throws Exception
     * @throws RedisException
     * 普通订单使用礼品卡
     */
    public function orderApplyGiftCard($giftCardIds,$reduce,$price,$gcombines,$db,$isDeducted = false)
    {

        $useCardType            = 0;
        $useCardValue           = 0;
        $giftCardDiscountAmount = 0;
        $giftCardData           = [];
        $applyGiftCardInfo      = [];

        // 获取礼品卡数据
        list($giftCardStatus, $giftCardData, $useCardType) = GiftCardService::getInstance()->getGiftCardData($giftCardIds, $gcombines);

        if (!$giftCardStatus) {
            return [false, $giftCardData];
        }

        if ($useCardType == 2 && $isDeducted) {
            return [false, '指定兑换卡不可同积分优惠券一起使用~'];
        }

        // 取扣除礼品卡ID的顺序
        $giftCardIds = array_keys($giftCardData);

        // 计算订单当前应付金额
        $orderPrice = array_sum(array_column($gcombines, 'tprice')) - $reduce;

        // 应用礼品卡抵扣
        $applyGiftCardInfo = GiftCardService::getInstance()->applyGiftCards($orderPrice, $giftCardIds, $giftCardData);

        // 抵扣金额
        $giftCardDiscountAmount = $applyGiftCardInfo['allDiscountAmount'];

        // 使用礼品卡类型  如果是指定兑换卡那么value为ID 如果是储值金额卡那么value为金额
        $useCardValue = ($useCardType == 2) ? implode(",", $giftCardIds) : $giftCardDiscountAmount;

        // 更新礼品卡余额和状态
        foreach ($applyGiftCardInfo['giftCardDataAfterDeduction'] as $giftCardId => $giftCardValue) {
            $giftCardInfo = byNew::GiftUserCards()->userGiftCardInfo($giftCardId);
            $updateData   = ['amount' => ($giftCardInfo['type'] == 2) ? 0 : $giftCardValue];

            if ($giftCardValue == 0 || $useCardType == 2) {
                $updateData['status'] = GiftUserCardsModel::STATUS['USED']; // 更新卡状态为不可使用
            }
            $updateData['utime'] = time();
            byNew::GiftUserCards()->saveData($updateData, $giftCardId, $db);
        }

        // 更新商品实付金额
        $price = bcsub($price, $giftCardDiscountAmount);
        return [
            true,
            [
                'useCardType'            => $useCardType,
                'useCardValue'           => $useCardValue,
                'giftCardDiscountAmount' => $giftCardDiscountAmount,
                'price'                  => $price,
                'giftCardData'           => $giftCardData,
                'applyGiftCardInfo'      => $applyGiftCardInfo
            ]
        ];
    }

    /**
     * 格式化卡密
     * A5309E369D93F180 转为 A530-9E36-9D93-F180
     * @param string $cardPassword
     * @return string
     */
    public function formatCardPassword(string $cardPassword): string
    {
        // 格式化卡密
        $cardPassword = trim($cardPassword);
        if (strpos($cardPassword, '-') === false) {
            $cardPassword = rtrim(chunk_split($cardPassword, 4, '-'), '-');
        }
        return $cardPassword;
    }

    // batchActivate
    public function batchActivate($list,$adminUid)
    {
        //准备数据
        $successNum = 0;
        $failNum = 0;
        $failData = [];
        $failUrl = '';
        // 循环处理数据
        foreach ($list as $k=>$item) {
            $userId = $item[0] ?? 0;
            $cardPassword = $item[1] ?? '';
            if (empty($cardPassword) || empty($userId)){
                $failData[] = [
                    'user_id' => $userId,
                    'card_password' => $cardPassword,
                    'error_message' => '第'.($k+2).'行错误：卡密不能为空或用户ID不能为0'
                ];
                $failNum++;
                continue;
            }
            // 验证userid是否存在
            if (!is_numeric($userId)){
                $failData[] = [
                    'user_id' => $userId,
                    'card_password' => $cardPassword,
                    'error_message' => '第'.($k+2).'行错误：用户ID必须是数字'
                ];
                $failNum++;
                continue;
            }
            $user_info = by::Phone()->getDataByUserId($userId);
            if (empty($user_info['uid'])){
                $failData[] = [
                    'user_id' => $userId,
                    'card_password' => $cardPassword,
                    'error_message' => '第'.($k+2).'行错误：用户ID不存在'
                ];
                $failNum++;
                continue;
            }

            try {
                $this->activateCard($userId,$cardPassword);
                $successNum++;
            } catch (\Exception $e) {
                $failData[] = [
                    'user_id' => $userId,
                    'card_password' => $cardPassword,
                    'error_message' => '第'.($k+2).'行错误：'.$e->getMessage()
                ];
                $failNum++;
            }
        }
        // 如果有错误的话，记录错误信息
        if ($failNum != 0){
            // 生成excel文件导出错误数据
            $newData = array_merge([['用户ID','卡密','错误信息']],$failData);
            $filename = sprintf('%s-%s-%d.xlsx', "批量激活失败数据", date('Ymd'), mt_rand(1000, 9999));
            $spreadsheet = new Spreadsheet();
            $activeWorksheet = $spreadsheet->getActiveSheet();
            $activeWorksheet->fromArray($newData);
            $writer = new Xlsx($spreadsheet);
            $writer->save($filename);
            list($status, $ret) = AliYunOss::factory()->uploadFile($filename);
            if (!$status){
                CUtil::debug("上传OSS失败，原因:" . $ret, 'err.aliyunoss');
            }
            unlink($filename);
            $failUrl = $ret;
        }
        // 记录日志
        byNew::SendGiftCardLogModel()->saveLog([
            'creator' => $adminUid,
            'success_num' => $successNum,
            'fail_num' => $failNum,
            'fail_excel' => $failUrl,
            'ctime' => time(),
            'utime' => time()
        ]);
        return [
            'success_num' => $successNum,
            'fail_num' => $failNum,
            'fail_data' => $failData,
            'fail_url' => $failUrl
        ];
    }

    public function batchActivateLog($page = 1,$limit = 20){
        $list = byNew::SendGiftCardLogModel()->getList($page,$limit);
        $count = byNew::SendGiftCardLogModel()->getCount();
        return [true,['list'=>$list,'count'=>$count]];
    }



}