<?php

namespace app\modules\main\services;

use app\components\AppCRedisKeys;
use app\components\Device;
use app\components\Employee;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\UserBindJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\UserEmployeeModel;
use yii\db\Exception;

class UserWithdrawRecordService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function apply($userId, $amount){
        // 先查询用户有多少余额
        $statistics = byNew::EmployeeStatisticsModel()->getInfo($userId,1);
        $balance = $statistics['balance'] ?? 0;
        if ($amount <= 0){
            return [false, '提现金额不能小于等于0'];
        }
        $amount = round($amount*100);
        if ($amount > $balance){
            return [false, '余额不足'];
        }
        // 写入提现记录
        $save = [
            'user_id' => $userId,
            'amount' => $amount,
            'status' => 0, // 未处理
            'ctime' => time(),
            'utime' => time()
        ];
        //开启事务
        $db     = by::dbMaster();
        $trans  = $db->beginTransaction();
        $res = byNew::UserWithdrawRecordModel()->SaveLog($save);
        if ($res){
            list($status,$msg) = byNew::EmployeeStatisticsModel()->patch($userId,'balance',$amount,'-');
            if ($status){
                $trans->commit();
                return [true, '提现申请成功，等待审核'];
            }else{
                $trans->rollBack();
                return [false, $msg];
            }
        }else{
            return [false, '提现申请失败'];
        }
    }

    public function getList($user_id,$status,$page,$pageSize){
        $result = [];
        $list = byNew::UserWithdrawRecordModel()->getList($user_id,$status,$page,$pageSize);

        // foreach ($list as &$item) {
        //     $item['amount'] = CUtil::totalFee($item['amount']);
        // }
        $result['list'] = $list;
        $result['total'] = byNew::UserWithdrawRecordModel()->getCount($user_id,$status);
        $result['pages'] = CUtil::getPaginationPages($result['total'], $pageSize);

        return [true, $result];
    }

    public function audit($id,$status,$remark = ''){
        $info = byNew::UserWithdrawRecordModel()->getInfo($id);
        if (!$info){
            return [false, '提现记录不存在'];
        }
        if ($status == 1){ // 通过审核
            $res = byNew::UserWithdrawRecordModel()->patch($id,'status',1);
            if ($res){
                return [true, '提现审核成功'];
            }else{
                return [false, '提现审核失败'];
            }
        }
        if ($status == 2){ // 不通过审核
            $res = byNew::UserWithdrawRecordModel()->patch($id,'status',2);
            $res = byNew::UserWithdrawRecordModel()->patch($id,'reason',$remark);
            if ($res){
                byNew::EmployeeStatisticsModel()->patch($info['user_id'],'balance',$info['amount'],'+');
                return [true, '提现审核成功'];
            }else{
                return [false, '提现审核失败'];
            }
        }
    }

}