<?php

namespace app\modules\main\v1\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class RuserGuideModel extends CommModel
{

    CONST CONTINUED = YII_ENV_PROD ? 7 :1; //绑定关系持续时间 (单位天)

    /**
     * @param $user_id
     * @return string
     * 根据用户id获得导购
     */
    private function __getGuideByUser($user_id) {
        return AppCRedisKeys::getGuideByUser($user_id);
    }

    /**
     * @param $user_id
     * @return int
     */
    public function __delCache($user_id) {
        $r_key = $this->__getGuideByUser($user_id);
        return by::redis('core')->del($r_key);
    }

    public static function tbName(): string
    {
        return '`db_dreame_no_auth`.`t_user_guide`';
    }

    /**
     * @param $code
     * @return array
     * 解密导购信息
     */
    public function decrypt($code){
        $decode     = CUtil::decrypt($code, by::Gmain()::SHARE_KEY);
        return explode('|', $decode);
    }

    /**
     * @param $user_id
     * @param $guide_id
     * @throws Exception
     * 绑定导购
     */
    public function bound($user_id, $guide_id)
    {
        if (empty(CUtil::checkUuid($user_id)) || empty(CUtil::checkUuid($guide_id))){
            return [false,'缺少参数'];
        }

        $guide = by::guide()->getGuideInfo($guide_id);
        if (empty($guide)){
            return [false,'请确认导购身份'];
        }
        $data=[
            'user_id' => $user_id,
            'guide_id'=> $guide_id,
            'ctime'   => CUtil::uint(START_TIME)
        ];

        $fields = array_keys($data);
        $fields = implode("`,`",$fields);

        $rows   = implode("','",$data);

        $dup    = [];
        foreach ($data as $key => $value){
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup    = implode(' , ',$dup);
        $tb     = self::tbName();
        $sql    = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";

        by::dbMaster()->createCommand($sql)->execute();

        //删除列表缓存
        $this->__delCache($user_id);

        return [true,'ok'];
    }

    /**
     * @param $user_id
     * @return int
     * @throws Exception
     */
    public function getGuideByUid($user_id){
        $user_id     = CUtil::uint($user_id);
        if($user_id <=0) {
            return 0;
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getGuideByUser($user_id);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `guide_id`,`ctime` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : 3600]);
        }

        $day = self::CONTINUED;
        if (empty($aData['guide_id']) || START_TIME > strtotime("+{$day} days",$aData['ctime'])){
            return 0;
        }

        return $aData['guide_id'];
    }

    public function getExpire(){
        $page_size  = 50;
        $day        = self::CONTINUED;
        $tb         = self::tbName();
        $ctime      = strtotime("-{$day} days",time());

        $sql        = "SELECT * FROM {$tb} WHERE `ctime`<:ctime LIMIT 1";

        $count      = by::dbMaster()->createCommand($sql, [':ctime' => $ctime])->queryScalar();

        $pages    = CUtil::getPaginationPages($count, $page_size);

        $id         = 0;

        for($page=1;$page<=$pages;$page++) {
            //分页查询
            $ret_sql = "SELECT `id`,`user_id` FROM {$tb} WHERE `ctime`<:ctime AND `id` > {$id} ORDER BY `id` ASC LIMIT {$page_size}";
            $list    = by::dbMaster()->createCommand($ret_sql,[':ctime' => $ctime])->queryAll();
            foreach ($list as $value){
                by::dbMaster()->createCommand()->delete($tb, ['id'=>$value['id']])->execute();
                $this->__delCache($value['user_id']);
            }

            $end     = end($rets);
            $id      = $end['id'];

            usleep(10000);
        }
    }

    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }
}
