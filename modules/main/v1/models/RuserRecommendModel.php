<?php

namespace app\modules\main\v1\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\EventMsg;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\DataReader;
use yii\db\Exception;

class RuserRecommendModel extends CommModel
{
    CONST EXPIRE_DAY = 30; //绑定关系持续时间 (单位天)
    CONST       EXP  = 3600;

    //行为
    const ACTION  = [
        'reg_reward'          => 1, //注册奖励
        'bind_reward'         => 2, //绑定奖励
        'order_coupon_reward' => 3, //下单优惠券奖励
        'order_point_reward'  => 4  //下单积分奖励
    ];

    //统计类型
    const CENSUS_TYPE = [
        'USER_ID' => 1, //邀请发起人数
        'R_ID'    => 2  //绑定人数
    ];

    public static function tbName(): string
    {
        return '`db_dreame_no_auth`.`t_user_recommend`';
    }

    public $tb_fields = [
        'id', 'user_id', 'r_id', 'bind_reward_point', 'reward_count', 'popup_count', 'ctime', 'expire_time'
    ];

    /**
     * @return string
     * 列表缓存
     */
    private function __getListKey(): string
    {
        return AppCRedisKeys::getUserRList();
    }

    /**
     * @param $user_id
     * @return string
     * 根据用户id获得推荐详情
     */
    private function __getInfoByUidKey($user_id): string
    {
        return AppCRedisKeys::getUserRInfoByUid($user_id);
    }

    /**
     * 清理缓存
     */
    private function __delListCache()
    {
        $r_key = self::__getListKey();

        by::redis('core')->del($r_key);
    }

    /**
     * @param $user_id
     */
    public function __delInfoCache($user_id)
    {
        $r_key = $this->__getInfoByUidKey($user_id);

        by::redis('core')->del($r_key);
    }

    /**
     * @param int $user_id
     * @param int $r_id
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $user_id = 0, int $r_id = 0): array
    {
        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        //用户ID
        if ($user_id > 0) {
            $where        .= " AND `user_id`=:user_id ";
            $params[':user_id'] = $user_id;
        }

        //推荐人ID
        if ($r_id > 0) {
            $where          .= " AND `r_id`=:r_id ";
            $params[':r_id'] = $r_id;
        }

        return [$where, $params];
    }

    /**
     * @param $user_id
     * @return array
     * 推荐人加密串
     */
    public function encrypt($user_id): array
    {
        $user_id = CUtil::uint($user_id);
        $rand    = CUtil::createVerifyCode(3, 1);
        $params  = CUtil::getAllParams($user_id, $rand);
        $encode  = CUtil::encrypt($params, by::Gmain()::SHARE_KEY);

        return [true, ['code' => $encode]];

    }

    /**
     * @param $user_id
     * @param $r_id
     * @param $phone
     * @param int $is_register
     * @return array
     * @throws Exception
     * 绑定推荐人
     */
    public function bind($user_id, $r_id, $phone, int $is_register = 0): array
    {
        $user_id = CUtil::uint($user_id);
        $r_id    = CUtil::uint($r_id);
        if ($user_id == 0 || $r_id == 0) {
            return [false,'缺少参数'];
        }

        $r_m_info = by::users()->getUserMainInfo($r_id);
        $r_phone  = by::Phone()->GetPhoneByUid($r_id);
        if (empty($r_m_info) || empty($r_phone)){
            return [false,'推荐人不存在'];
        }

        $main = by::users()->getUserMainInfo($r_id);
        if (strpos($main['openudid'], '|')) {
            return [false, '该账号已注销不能绑定'];
        }

        if ($user_id == $r_id || $r_phone == $phone) {
            return [false, '不能绑定自己为推荐人'];
        }

        $ur_info = $this->getInfoByUid($user_id);
        if (empty($ur_info) || (!empty($ur_info['expire_time']) && START_TIME > $ur_info['expire_time'])) {
            $start_time = intval(START_TIME);
            $days       = self::EXPIRE_DAY;
            $data       = [
                'user_id'     => $user_id,
                'r_id'        => $r_id,
                'ctime'       => $start_time,
                'expire_time' => YII_ENV_PROD ? strtotime("+{$days} days") : strtotime("+1 hours")
            ];

            $fields = array_keys($data);
            $fields = implode("`,`",$fields);
            $rows   = implode("','",$data);

            $dup = [];
            foreach ($data as $k => $v){
                $dup[] = "`{$k}` = '{$v}'";
            }
            $dup = implode(' , ',$dup);
            $db    = by::dbMaster();
            $tb_ur = self::tbName();
            $sql1  = "INSERT INTO {$tb_ur} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";
            $sql2  = "ALTER TABLE {$tb_ur} AUTO_INCREMENT=1";
            $res   = $db->createCommand($sql1)->execute();
            $db->createCommand($sql2)->execute();
            if ($res <= 0) {
                CUtil::debug("推荐人绑定失败, user_id:{$user_id}, r_id:{$r_id}, sql:{$sql1}", 'rid-bind.err');
                return [false, '绑定失败'];
            }

            //删除缓存
            by::users()->deleteRedisCache($user_id);
            
            // 获取被邀请人奖励配置
            list($status, $rewardData) = MemberCenter::factory()->ruleFineDetail('mall', 'dreame', 'be_invited_reg');
            if (! $status) {
                CUtil::debug('被邀请人获得奖励失败：user_id = '.$user_id,'warn.reward_user');
                return [false, '被邀请人获得奖励失败'];
            }
            
            $bindRewardPoint = $rewardData['gold'] ?? 50000;

            $type_model = by::Actype2();
            $ac_model   = by::activityConfigModel();
            $ac_info    = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift']);
            if (!empty($ac_info)) {
                //受邀请人绑定奖励积分和发放次数
                $reward_count = $ur_info['reward_count'] ?? 0;
                if (
                    $ac_info['is_bind_reward'] == $type_model::ACTION['bind']['is_reward']
                    && $ac_info['invitee_bind_limit'] > 0 && $reward_count < $ac_info['invitee_bind_limit']
                ) {
                    //更新发放次数
                    list($where, $params)    = $this->__getCondition($user_id);
                    $ur_set                  = "`reward_count` = `reward_count`+(:reward_count)";
                    $where                  .= " AND `reward_count`+(:reward_count) <= {$ac_info['invitee_bind_limit']}";
                    $params[':reward_count'] = 1;

                    //更新奖励积分
                    $ur_set                      .= ",`bind_reward_point` = `bind_reward_point`+(:bind_reward_point)";
                    $params[':bind_reward_point'] = $bindRewardPoint;

                    $tb_ur = self::tbName();
                    $sql   = "UPDATE {$tb_ur} SET {$ur_set} WHERE {$where} LIMIT 1";
                    $ret   = by::dbMaster()->createCommand($sql, $params)->execute();
                    if ($ret <= 0) {
                        CUtil::debug("更新绑定积分或发放次数失败, sql:{$sql}, 失败原因:{$ret}", 'bind-count.err');
                    }

                    //crm发放受邀请人绑定奖励积分
                    // Crm::factory()->push(
                    //     $user_id,
                    //     'behavior',
                    //     [
                    //         'type'    => Crm::factory()::POINT_ACTION['BIND'],
                    //         'user_id' => $user_id,
                    //         'phone'   => $phone,
                    //         'desc'    => '受邀请人绑定奖励'
                    //     ]
                    // );
                    EventMsg::factory()->run('beInviteReg',['user_id' => $user_id]);
                }

                if (!empty($is_register)) {
                    //写入扩展表 推荐人id（只是邀请绑定，并非实际推荐关系）
                    $extendData['r_id'] = $r_id;
                    by::userExtend()->saveUserExtend($user_id, $extendData);
                    
                    // 获取邀请人奖励配置
                    list($status, $rewardData) = MemberCenter::factory()->ruleFineDetail('mall', 'dreame', 'invite_reg');
                    if (! $status) {
                        CUtil::debug('邀请人获得奖励失败：r_id = '.$r_id,'warn.reward_user');
                        return [false, '邀请人获得奖励失败'];
                    }
                    
                    $rewardPoint = $rewardData['gold'] ?? 10000;

                    //邀请人注册奖励积分和发放次数
                    $reg_reward_count = by::users()->getOneByUid($r_id)['reg_reward_count'] ?? 0;
                    if (
                        $ac_info['is_reg_reward'] == $type_model::ACTION['reg']['is_reward']
                        && $ac_info['inviter_reg_limit'] > 0 && $reg_reward_count < $ac_info['inviter_reg_limit']
                    ) {
                        //更新邀请人注册发放次数
                        list($where, $params) = $this->__getCondition($r_id);
                        $where .= " AND `reg_reward_count`+(:reg_reward_count) <= {$ac_info['inviter_reg_limit']}";
                        $u_set = "`reg_reward_count` = `reg_reward_count`+(:reg_reward_count)";
                        $params[':reg_reward_count'] = 1;

                        //更新邀请人注册奖励积分
                        $u_set .= ",`reg_reward_point` = `reg_reward_point`+(:reg_reward_point)";
                        $params[':reg_reward_point'] = $rewardPoint;

                        $tb_u = by::users()::userTb($r_id);
                        $sql = "UPDATE {$tb_u} SET {$u_set} WHERE {$where} LIMIT 1";
                        $ret = by::dbMaster()->createCommand($sql, $params)->execute();
                        if ($ret <= 0) {
                            CUtil::debug("更新注册积分或发放次数失败, sql:{$sql}, 失败原因:{$ret}", 'bind-count.err');
                        }

                        //crm发放注册奖励积分
                        // Crm::factory()->push(
                        //     $r_id,
                        //     'behavior',
                        //     [
                        //         'type' => Crm::factory()::POINT_ACTION['REG'],
                        //         'user_id' => $r_id,
                        //         'phone'   => $phone,
                        //         'desc'    => '邀请人注册奖励'
                        //     ]
                        // );
                        EventMsg::factory()->run('inviteReg',['user_id'=>$r_id]);
                    }
                }

                $rate   = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
                $money1 = floor(sprintf("%.2f", $bindRewardPoint / $rate) * 100) / 100;
                $num1   = $num2 = by::am()->getListCount($ac_info['id']);
                $money2 = 100;
                if (empty($ur_info)) {
                    //受邀请人短信提醒
                    $bind_ext = ['money' => $money1];
                    list($status, $ret) = by::model("SmsModel",MAIN_MODULE)->smsSend($phone, "BIND", $bind_ext);
                    if (!$status) {
                        CUtil::debug("受邀请人绑定短信发送失败, user_id:{$user_id}, 失败原因:{$ret}", 'bind-sms.warn');
                    }

                    //邀请人首次短信提醒
                    $reg_ext = ['money1' => $money1, 'num1' => $num1, 'money2' => $money2, 'num2' => $num2];
                    list($status, $ret) = by::model("SmsModel",MAIN_MODULE)->smsSend($r_phone, "REG", $reg_ext);
                    if (!$status) {
                        CUtil::debug("推荐人首次发送短信失败, r_id:{$r_id}, 失败原因:{$ret}", 'bind-sms.warn');
                    }
                } else {
                    //邀请人重绑短信提醒
                    $again_ext = ['num1' => $num1, 'money' => $money2, 'num2' => $num2];
                    list($status, $ret) = by::model("SmsModel",MAIN_MODULE)->smsSend($r_phone, "AGAIN_BIND", $again_ext);
                    if (!$status) {
                        CUtil::debug("推荐人重新绑定短信发送失败, r_id:{$r_id}, 失败原因:{$ret}", 'bind-sms.warn');
                    }
                }

                //删除缓存
                $this->__delListCache();
                $this->__delInfoCache($user_id);
            }
        }

        return [true, 'ok'];
    }

    /**
     * @return array
     * @throws Exception
     * 绑定推荐人列表
     */
    public function getList(): array
    {
        $redis = by::redis('core');
        $r_key = $this->__getListKey();
        $aJson = $redis->get($r_key);
        $data  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb   = self::tbName();
            $sql  = "SELECT `user_id` FROM {$tb} WHERE ORDER BY `ctime` DESC";
            $data = by::dbMaster()->createCommand($sql)->queryAll();
            $data = !empty($data) ? $data : [];

            by::redis('core')->set($r_key, json_encode($data), empty($data) ? 10 : self::EXP);
        }

        $list = [];
        foreach ($data as $v) {
            $ur_info = $this->getInfoByUid($v['user_id']);
            if (empty($ur_info)) {
                continue;
            }

            $list[] = $ur_info;
        }

        return $list;
    }

    /**
     * @param $user_id
     * @return array|DataReader
     * @throws Exception
     * 获取绑定推荐人详情
     */
    public function getInfoByUid($user_id)
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id == 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getInfoByUidKey($user_id);
        $aJson     = $redis->get($redis_key);
        $info      = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb     = self::tbName();
            $fields = implode("`,`",$this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";
            $info   = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $info   = !empty($info) ? $info : [];
            $redis->set($redis_key, json_encode($info), ['EX'=> empty($info) ? 10 : self::EXP]);
        }

        return $info;
    }

    /**
     * @param $user_id
     * @param $action
     * @return array
     * @throws Exception
     * 更新弹窗次数
     */
    public function updatePopupCount($user_id, $action): array
    {
        $user_id = CUtil::uint($user_id);
        $action  = CUtil::uint($action);
        if ($user_id == 0 || $action == 0) {
            return [false, '参数错误'];
        }

        list($where, $params) = $this->__getCondition($user_id);
        switch ($action) {
            case self::ACTION['reg_reward'] :
                $tb     = by::users()::userTb($user_id);
                $u_info = by::users()->getOneByUid($user_id);
                if (isset($u_info['reg_reward_count']) && $u_info['reg_reward_count'] > 0) {
                    $where                     .= " AND `reg_popup_count`+(:reg_popup_count) <= {$u_info['reg_reward_count']}";
                    $set                        = "`reg_popup_count` = `reg_popup_count`+(:reg_popup_count)";
                    $params[':reg_popup_count'] = 1;
                }

                break;
            case self::ACTION['bind_reward'] :
                $tb      = self::tbName();
                $ur_info = by::userRecommend()->getInfoByUid($user_id);
                if (isset($ur_info['reward_count']) && $ur_info['reward_count'] > 0) {
                    $where                 .= " AND `popup_count`+(:popup_count) <= {$ur_info['reward_count']}";
                    $set                    = "`popup_count` = `popup_count`+(:popup_count)";
                    $params[':popup_count'] = 1;
                }

                break;
            case self::ACTION['order_coupon_reward'] :
                $tb     = by::users()::userTb($user_id);
                $u_info = by::users()->getOneByUid($user_id);
                if (isset($u_info['coupon_reward_count']) && $u_info['coupon_reward_count'] > 0) {
                    $where                        .= " AND `coupon_popup_count`+(:coupon_popup_count) <= {$u_info['coupon_reward_count']}";
                    $set                           = "`coupon_popup_count` = `coupon_popup_count`+(:coupon_popup_count)";
                    $params[':coupon_popup_count'] = 1;
                }

                break;
            case self::ACTION['order_point_reward'] :
                $tb     = by::users()::userTb($user_id);
                $u_info = by::users()->getOneByUid($user_id);
                if (isset($u_info['point_reward_count']) && $u_info['point_reward_count'] > 0) {
                    $where                       .= " AND `point_popup_count`+(:point_popup_count) <= {$u_info['point_reward_count']}";
                    $set                          = "`point_popup_count` = `point_popup_count`+(:point_popup_count)";
                    $params[':point_popup_count'] = 1;
                }

                break;
            default :
                return [false, '类型不合法'];
        }

        if (!empty($set)) {
            $sql = "UPDATE {$tb} SET {$set} WHERE {$where} LIMIT 1";
            $ret = by::dbMaster()->createCommand($sql, $params)->execute();
            if ($ret <= 0) {
                return [false, '更新弹窗次数失败'];
            }

            //删除缓存
            switch ($action) {
                case self::ACTION['order_coupon_reward']:
                case self::ACTION['order_point_reward']:
                case self::ACTION['reg_reward'] :
                    by::users()->deleteRedisCache($user_id);

                    break;
                case self::ACTION['bind_reward'] :
                    $this->__delListCache();
                    $this->__delInfoCache($user_id);

                    break;
                default :
                    return [false, '类型不合法'];
            }
        }

        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param int $r_id
     * @return array
     * @throws Exception
     * 获取弹窗次数相关数据
     */
    public function getPopupCount($user_id, int $r_id = 0): array
    {
        $user_id = CUtil::uint($user_id);
        $r_id    = CUtil::uint($r_id);
        if ($user_id == 0) {
            return [];
        }

        $ur_info = by::userRecommend()->getInfoByUid($user_id);
        if (!empty($ur_info)) {
            //首次绑定奖励需弹窗次数
            $popup_data['bind_popup_count'] = $ur_info['reward_count'] - $ur_info['popup_count'];

            //其他人邀请绑定是否弹窗
            $popup_data['other_bind_popup'] = 0;
            if (
                !empty($ur_info['reward_count']) && empty($popup_data['bind_popup_count']) && $r_id > 0
                && $user_id != $r_id && !empty($ur_info['expire_time'])
            ) {
                $popup_data['other_bind_popup'] = 1;
            }
        }

        $u_info = by::Users()->getOneByUid($user_id);
        if (!empty($u_info)) {
            //注册奖励需弹窗次数
            $popup_data['reg_popup_count']    = $u_info['reg_reward_count']    - $u_info['reg_popup_count'];

            //下单优惠券奖励需弹窗次数
            $popup_data['coupon_popup_count'] = $u_info['coupon_reward_count'] - $u_info['coupon_popup_count'];

            //下单积分奖励需弹窗次数
            $popup_data['point_popup_count']  = $u_info['point_reward_count']  - $u_info['point_popup_count'];
        }

        return $popup_data ?? [];
    }

    /**
     * @param $census_type
     * @return int
     * @throws Exception
     * 获取统计数量
     */
    public function getCensusTotal($census_type): int
    {
        $redis = by::redis('core');
        $r_key = $this->__getListKey();
        $h_key = CUtil::getAllParams(__FUNCTION__, $census_type);
        $total = $redis->hGet($r_key, $h_key);

        if ($total === false) {
            $tb  = $this->tbName();

            switch ($census_type) {
                case 1 :
                    $sql = "SELECT COUNT(`user_id`) FROM  {$tb}";

                    break;
                case 2 :
                    $sql = "SELECT COUNT(DISTINCT(`r_id`)) FROM  {$tb} where `r_id` > 0";

                    break;
                default :
                    return 0;
            }

            $total = by::dbMaster()->createCommand($sql)->queryScalar();
            $total = !empty($total) ? $total : 0;

            $redis->hSet($r_key, $h_key, $total);
            CUtil::ResetExpire($r_key,empty($total) ? 10 : self::EXP);
        }

        return CUtil::uint($total);
    }

    /**
     * @return array
     * @throws Exception
     * 推荐有礼统计
     */
    public function census(): array
    {
        $ac_model = by::activityConfigModel();
        $ac_info  = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift']);

        //前端需要返回一个二维数组
        $list[] = [
            'name'         => $ac_info['name']       ?? '',                                 //活动名称
            'grant_type'   => $ac_info['grant_type'] ?? 0,                                  //活动类型
            'bind_total'   => $this->getCensusTotal(self::CENSUS_TYPE['USER_ID']),          //绑定人数
            'invite_total' => $this->getCensusTotal(self::CENSUS_TYPE['R_ID']),             //邀请发起人数
            'reg_total'    => by::UserExtend()->getInviteRegTotal(),                        //新用户注册数
            'gmv'          => by::Gtype0()->totalFee(by::OsourceR()->getTotalGmv(), 1) //产生GMV
        ];

        return $list;
    }

    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }
}
