<?php

namespace app\modules\main\v1\models;

use app\components\AppCRedisKeys;
use app\components\WeWork;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\DataReader;
use yii\db\Exception;

class RguideModel extends CommModel
{
    public static $expire = 3600;

    const TIME = !YII_ENV_PROD ? 43200 : 60*86400;


    public static function tbName(): string
    {
        return '`db_dreame_no_auth`.`t_guide`';
    }

    /**
     * @param $user_id
     * 根据id获取信息
     */
    private function __getGuideByUidKey($user_id){
        return AppCRedisKeys::getGuideByUidKey($user_id);
    }

    /**
     * 列表缓存
     */
    private function __adminGuideKey(){
        return AppCRedisKeys::adminGuideKey();
    }

    /**
     * @param $user_id
     * 缓存清理
     */
    public function __delCache($user_id)
    {
        $r_key1 = $this->__getGuideByUidKey($user_id);
        $r_key2 = $this->__adminGuideKey();
        by::redis('core')->del($r_key1,$r_key2);
    }



    /**
     * @param $user_id
     * @return array|DataReader|false
     * @throws Exception
     * 根据用户ID获取扩展字段
     */
    public function getGuideInfo($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getGuideByUidKey($user_id);
        $aJson        = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `user_id`,`name`,`store`,`status` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param $data
     * @throws Exception
     */
    public function saveGuide($user_id, $data)
    {
        $data['user_id'] = empty(CUtil::checkUuid($user_id))?0:$user_id;

        $fields = array_keys($data);
        $fields = implode("`,`",$fields);

        $rows   = implode("','",$data);

        $dup    = [];
        foreach ($data as $key => $value){
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup    = implode(' , ',$dup);
        $tb     = self::tbName();
        $sql    = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";

        by::dbMaster()->createCommand($sql)->execute();

        //删除列表缓存
        $this->__delCache($user_id);

        return [true,'ok'];
    }


    /**
     * @param $page
     * @param $page_size
     * @param int $user_id
     * @param string $name
     * @param int $status
     * @return array|DataReader
     * @throws Exception
     */
    public function getList($page,$page_size,$user_id=0,$name='',$status=0)
    {
        $redis_key  = $this->__adminGuideKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$user_id,$name,$status);
        $redis      = by::redis();
        $aJson      = $redis->hGet($redis_key, $h_key);
        $list       = (array)json_decode($aJson,true);
        if($aJson === false){
            $tb = self::tbName();
            list($where,$params) = $this->_condition($user_id,$name,$status);
            list($offset) = CUtil::pagination($page,$page_size);
            $sql  = "select `user_id` from {$tb} where {$where} order by `user_id` desc limit {$offset},{$page_size}";
            $list = by::dbMaster()->createCommand($sql,$params)->queryAll();
            $redis->hSet($redis_key,$h_key,json_encode($list));
            CUtil::ResetExpire($redis_key,self::$expire);
        }

        return $list;
    }





    /**
     * @param $user_id
     * @param $name
     * @param $status
     * @return array
     * @throws Exception
     */
    protected function _condition($user_id,$name,$status)
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if(!empty($user_id)) {
            if (strlen($user_id) == 11) {
                $uids = by::Phone()->GetUidsByPhone($user_id);
                if(!empty($uids)) {
                    $uids    = implode(',',$uids);
                    $where  .= " AND `user_id` IN ({$uids})";
                }else{
                    //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                    $where  .= " AND `user_id` = -1";
                }
            } else {
                $where  .= " AND `user_id` = {$user_id}";
            }
        }

        if(!empty($name)) {
            $where              .= " AND `name` LIKE :name";
            $params[":name"]  = $name;
        }


        if($status>-1) {
            $where              .= " AND `status` = :status";
            $params[":status"]  = $status;
        }

        return [$where, $params];
    }

    /**
     * @param  $user_id
     * @param  $name
     * @param  $status
     * @param  $phone
     * @throws Exception
     * 导购列表人员导出
     */
    public function export($user_id = 0, $name = '', $status = 0)
    {
        $head   = [
            '用户ID', '导购员昵称', '手机号', '注册时间', '所属门店', '状态'
        ];

        $f_name = '导购列表' . date('Ymd') . mt_rand(1000, 9999);

        $tb = self::tbName();
        list($where, $params) = $this->_condition($user_id, $name, $status);

        //导出
        CUtil::export_csv_new($head, function () use ($tb, $where, $params) {

            $db      = by::dbMaster();
            $muser   = by::users();
            $mphone  = by::Phone();

            $id     = 0;
            $sql  = "select `id`,`user_id` from {$tb} where `id` > :id AND {$where} order by `id` limit 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $data       = [];

                foreach ($list as $value) {
                    $user   = $muser->getOneByUid($value['user_id']);
                    $main   = $muser->getUserMainInfo($value['user_id']);
                    $phone  = $mphone->GetPhoneByUid($value['user_id']);
                    $guide  = $this->getGuideInfo($value['user_id']);

                    if (empty($user)) {
                        $status = '注销';
                    } else {
                        $status = RuserModel::STATUS[$user['status']];
                    }
                    $data[] = [
                        'user_id'   => $value['user_id'],
                        'name'      => $guide['name'] ?? '',
                        'phone'     => $phone ?? '',
                        'reg_time'  => isset($main['reg_time']) ? date("Y-m-d H:i:s", $main['reg_time']) : 0,
                        'store'     => $guide['store'] ?? '',
                        'status'    => $status,
                    ];
                }

                yield $data;
            }
        }, $f_name);
    }

    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }
}
