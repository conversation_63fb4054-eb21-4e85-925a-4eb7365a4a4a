<?php

namespace app\modules\main\v1\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;
use function GuzzleHttp\debug_resource;

class RuserExtendModel extends CommModel
{
    public static $expire = 3600;

    const TIME = !YII_ENV_PROD ? 43200 : 60*86400;

    //可修改字段
    CONST MODIFY =  ['source', 'ctime','tag','guide_id'];

    public static function tbName(): string
    {
        return '`db_dreame_no_auth`.`t_user_extend`';
    }

    /**
     * @param $user_id
     * 根据id获取信息
     */
    private function __getExtendByUidKey($user_id): string
    {
        return AppCRedisKeys::getExtendByUseridKey($user_id);
    }

    /**
     * @param $card
     * 根据crm卡号获取用户id
     */
    private function __getUidByCard($card){
        return AppCRedisKeys::getUidByCard($card);
    }


    /**
     * 列表缓存
     */
    private function __adminUserKey(){
        return AppCRedisKeys::adminUserKey();
    }

    /**
     * @param $r_id
     * @return string
     * 邀请注册列表
     */
    private function __getInviteRegList($r_id): string
    {
        return AppCRedisKeys::getInviteRegList($r_id);
    }

    /**
     * @param $user_id
     * @param string $card
     * @param int $r_id
     * 缓存清理
     */
    private function __delCache($user_id, string $card='', int $r_id=0)
    {
        $r_key1 = $this->__getExtendByUidKey($user_id);
        $r_key2 = $this->__adminUserKey();
        by::redis('core')->del($r_key1,$r_key2);

        if ($card){
            $r_key3 = $this->__getUidByCard($card);
            by::redis('core')->del($r_key3);
        }

        if (!empty($r_id)){
            $r_key4 = $this->__getInviteRegList($r_id);
            by::redis('core')->del($r_key4);
        }
    }

    public function deleteRedisCache($user_id, string $card='', int $r_id=0)
    {
        $this->__delCache($user_id, $card, $r_id);
    }


    /**
     * @param $user_id
     * @param bool $cache
     * @return array|false
     * @throws Exception
     * 根据用户ID获取扩展字段
     */
    public function getUserExtendInfo($user_id, $cache=true)
    {
        if(empty(CUtil::checkUuid($user_id))) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getExtendByUidKey($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `source`,`ctime`,`guide_id`,`r_id`,`tag`,`card`,`main_user_id`,`store` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $aData;
    }


    /**
     * @param $user_id
     * @param $data
     * @return bool
     * @throws Exception
     * 保存数据
     */
    public function saveUserExtend($user_id, $data): bool
    {
        $data['user_id'] = CUtil::checkUuid($user_id);
        $fields = array_keys($data);
        $fields = implode("`,`",$fields);

        $rows   = implode("','",$data);

        $dup    = [];
        foreach ($data as $key => $value){
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup    = implode(' , ',$dup);
        $tb     = self::tbName();
        $sql    = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";

        $res    = by::dbMaster()->createCommand($sql)->execute();

        //删除列表缓存
        $this->__delCache($user_id,$data['card']??'', $data['r_id'] ?? 0);

        return $res;
    }


    /**
     * @param $source
     * @return array
     * 来源code说明
     */
    public function sourceConfig($source)
    {
        switch ($source){
            case 1001:case 1005:case 1006:case 1010:case 1024:case 1026:case 1027:case 1089:case 1106:
            $str = '微信首页'; $pv = 'source_1'; break;
            case 1007:case 1008:
            $str = '好友分享'; $pv = 'source_2'; break;
            case 1045:case 1046:case 1084:
            $str = '朋友圈广告'; $pv = 'source_3'; break;
            case 1067:
                $str = '公众号文章广告'; $pv = 'source_4'; break;
            case 1095:
                $str = '小程序广告'; $pv = 'source_5'; break;
            case 1035:case 1102:
            $str = '公众号菜单栏'; $pv = 'source_6'; break;
            case 1058:case 1144:
            $str = '公众号文章'; $pv = 'source_7'; break;
            case 1074:case 1081:case 1082:
            $str = '公众号消息'; $pv = 'source_8'; break;
            case 1175:case 1176:case 1177:case 1191:case 1195:
            $str = '视频号'; $pv = 'source_9'; break;
            case 1011:case 1012:case 1013:
            $str = '扫描二维码'; $pv = 'source_10'; break;
            case 1065:
                $str = '微信外部打开'; $pv = 'source_11'; break;
            default  :
                $str = '其他'; $pv = ''; break;
        }
        return [$str,$pv];
    }

    const SOURCE_TXT = [
        'source_1' => [1001,1005,1006,1010,1024,1026,1027,1089,1106],
        'source_2' => [1007,1008],
        'source_3' => [1045,1046,1084],
        'source_4' => [1067],
        'source_5' => [1095],
        'source_6' => [1035,1102],
        'source_7' => [1058,1144],
        'source_8' => [1074,1081,1082],
        'source_9' => [1175,1176,1177,1191,1195],
        'source_10' => [1011,1012,1013],
        'source_11' => [1065],
    ];

    /**
     * @param $page
     * @param $page_size
     * @param $user_id
     * @param $phone
     * @param $source
     * @param int $s_time
     * @param int $e_time
     * @param int $vip_s_time
     * @param int $vip_e_time
     * @return array|DataReader
     * @throws Exception
     * 获取用户列表
     */
    public function getUserList($page, $page_size, $user_id, $phone, $source, int $s_time = 0, int $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0)
    {
        $redis_key  = $this->__adminUserKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time);
        $redis      = by::redis();
        $aJson      = $redis->hGet($redis_key, $h_key);
        $list       = (array)json_decode($aJson,true);

        if($aJson === false){
            $tb           = self::tbName();
            $tb_phone     = by::Phone()::tbName();
            $where        = $this->_condition($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time);
            list($offset) = CUtil::pagination($page,$page_size);
            $sql          = "select e.user_id from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id 
                                where {$where} order by `user_id` desc limit {$offset},{$page_size}";
            $list         = by::dbMaster()->createCommand($sql)->queryAll();

            $redis->hSet($redis_key,$h_key,json_encode($list));
            CUtil::ResetExpire($redis_key,self::$expire);
        }

       return $list;
    }

    public function getUserData($list): array
    {
        $return = [];
        foreach ($list as $value) {
            $user     = by::users()->getOneByUid($value['user_id']);
            $phone    = by::Phone()->GetPhoneByUid($value['user_id']);
            $main     = by::users()->getUserMainInfo($value['user_id']);
            $extend   = $this->getUserExtendInfo($value['user_id']);
            $vip_time = by::Phone()->GetCtimeByUid($value['user_id']);

            if (!empty($extend['source'])){
                list($source) = $this->sourceConfig($extend['source']);

            }
            $return[] = [
                'user_id'     => $value['user_id'],
                'card'        => $extend['card']                        ??'',
                'nick'        => $user['nick']                          ??'',
                'phone'       => $phone                                 ?? '',
                'source'      => $source                                ?? '其他',
                'is_vip'      => $phone                                 ? 1 :0,
                'is_r_invite' => $extend['r_id']                        ? 1 :0,
                'is_invite'   => $extend['guide_id']                    ? 1 :0,
                'tag'         => $extend['tag']                         ?? '',
                'vip_time'    => $vip_time                              ?? '',
                'status'      => strpos($main['openudid'], '|')  ? 1 : 0,
            ];
        }

        return $return;
    }


    /**
     * @param $s_user_id
     * @param $s_phone
     * @param $s_source
     * @param $s_time
     * @param $e_time
     * @param int $vip_s_time
     * @param int $vip_e_time
     * @param int $is_export_point
     * @return array
     * 用户列表导出
     */
    public function export($s_user_id = 0, $s_phone = 0, $s_source = 0, $s_time = 0, $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0, int $is_export_point = 0): array
    {
//        $total      = $this->getUserTotal($s_user_id,$s_phone,$s_source,$s_time,$e_time);
//        $page_size  = 100;
//        $pages      = CUtil::getPaginationPages($total,$page_size);
        $headList = [
            '用户id',
            '会员卡号',
            '姓名',
            '手机号',
            '生日',
            '积分',
            '注册时间',
            '活跃时间',
            '注册会员时间',
            '来源',
            '标签',
            '性别',
            '地区',
            '默认收货地址',
            '是否注销',
            '是否邀请人邀请注册',
            '是否导购邀请注册',
            '是否绑定导购员',
            '是否领取新人礼包',
//            '累计支付金额',
//            '累计支付订单数',
//            '客单价',
//            '累计退款金额',
//            '累计退款订单数',
//            '注册30天支付金额',
//            '今年支付金额',
        ];
        $fileName   = '用户列表' . date('Ymd') . mt_rand(1000, 9999);
        //导出
        CUtil::export_csv_new ($headList, function () use($s_user_id, $s_phone, $s_source, $s_time, $e_time, $vip_s_time, $vip_e_time, $is_export_point)
        {
            $db       = by::dbMaster();
            $tb       = self::tbName();
            $tb_phone = by::Phone()::tbName();
            $last_uid = 0;
            $where    = $this->_condition($s_user_id, $s_phone, $s_source, $s_time, $e_time, $vip_s_time, $vip_e_time);

            while (true) {
                $sql  = "select e.user_id from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id 
                            where e.user_id > :user_id AND {$where} order by e.user_id ASC limit 100";

                $list = $db->createCommand($sql, [':user_id'=>$last_uid])->queryAll();


                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $last_uid   = $end['user_id'];
                $dataList   = [];

                foreach ($list as $v) {
                    $user               = by::users()->getOneByUid($v['user_id']);
                    $main               = by::users()->getUserMainInfo($v['user_id']);
                    $address            = by::Address()->GetDefaultAddress($v['user_id']);
                    $phone              = by::Phone()->GetPhoneByUid($v['user_id']);
                    $extend             = $this->getUserExtendInfo($v['user_id']);
                    $p_time             = by::Phone()->GetCtimeByUid($v['user_id']);
                    if (!empty($extend['source'])){
                        list($source) = $this->sourceConfig($extend['source']);
                    }
                    $u_guide    = by::userGuide()->getGuideByUid($v['user_id']);

                    //获取积分
                    if (!empty($is_export_point)) {
                        try {
                            $score = by::point()->get($v['user_id']);
                        } catch (\Exception $e) {
                            $score = '';
                        }
                    }

                    $dataList[] = [
                        'user_id'       => $v['user_id'],
                        'card'          => $extend['card'] ?? '',
                        'real_name'     => $user['real_name'] ?? '',
                        'phone'         => $phone,
                        'birthday'      => !empty($user['birthday']) ? date('Y-m-d',$user['birthday']):'',
                        'score'         => $score ?? '',
                        'reg_time'      => !empty($main['reg_time']) ? date('Y-m-d H:i:s',$main['reg_time']) : '',
                        'active_time'   => !empty($user['active_time']) ? date('Y-m-d H:i:s',$user['active_time']) : '',
                        'vip_time'      => !empty($p_time) ? date('Y-m-d H:i:s',$p_time) :'',
                        'source'        => $source ?? '',
                        'tag'           => $extend['tag']??'',
                        'sex'           => by::users()::USER_SEX_TXT[$user['sex']],
                        'area'          => empty($user['area']) ? '' : sprintf('%s，%s，%s',$user['area']['province']??'',$user['area']['city']??'',$user['area']['area']??''),
                        'address'       => empty($address) ? '' : sprintf('%s，%s，%s，%s',$address['province']??'',$address['city']??'',$address['area']??'',$address['detail']??''),
                        'status'        => strpos($main['openudid'], '|') ? '注销' : '正常',
                        'is_r_invite'   => !empty($extend['r_id'])     ? '是' : '否',
                        'is_invite'     => !empty($extend['guide_id']) ? '是' : '否',
                        'guide'         => $u_guide? '是':'否',
                        'is_new_gift'   => $user['is_new_gift'] ? '是':'否',
                    ];
                }

                yield $dataList;

            }
        },$fileName);

        return [true,'成功'];
    }

    /**
     * @param $user_id
     * @throws \yii\db\Exception
     * @return array
     * 获取用户公众号标签
     */
    public function getTag($user_id)
    {
        if (!YII_ENV_PROD){
            return [false,'无标签'];
        }
        $uMain   = by::Rusers()->getUserMainInfo($user_id);
        if (empty($uMain['unionid'])){
            return [false,'用户信息不存在'];
        }
        $aLog    = by::OaFocus()->GetALogByUnionid($uMain['unionid']);
        if(!empty($aLog['oa_openid'])) {
            list($state, $tagIds) = WeiXin::factory()->getTagsByUid($aLog['oa_openid'],empty(CUtil::wxOaLock())?WeiXin::UQ_TOKEN['OA']:WeiXin::UQ_TOKEN['OANEW']);
            if($state && !empty($tagIds['tagid_list'])){
                list($state, $tagList) = WeiXin::factory()->getTags(empty(CUtil::wxOaLock())?WeiXin::UQ_TOKEN['OA']:WeiXin::UQ_TOKEN['OANEW']);

                if (empty($tagList['tags'])) {
                    return [true, ''];
                }

                $tags = $arr = [];
                foreach ($tagList['tags'] as $v) {
                    $arr[$v['id']] = $v['name'];
                }
                foreach ($tagIds['tagid_list'] as $v) {
                    $tags[] = $arr[$v];
                }
                $tag = implode('，', $tags);
                return [true,$tag];
            }
        }
        return [false,'无标签'];
    }

    /**
     * @param $user_id
     * @param $phone
     * @param $source
     * @param $s_time
     * @param $e_time
     * @param $vip_s_time
     * @param $vip_e_time
     * @return string
     * @throws Exception
     */
    protected function _condition($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time): string
    {
        $where = "1=1";
        if(!empty($user_id)){
            $where .= " and e.user_id = {$user_id}";
        }

        if(!empty($phone)) {
            $uids = by::Phone()->GetUidsByPhone($phone);
            if(!empty($uids)) {
                $uids    = implode(',',$uids);
                $where  .= " AND e.user_id IN ({$uids})";
            }else{
                //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                $where  .= " AND e.user_id = -1";
            }
        }

        if(!empty($source)){
            $arr = self::SOURCE_TXT[$source] ?? [];
            if(!empty($arr)){
                $sources = implode(',',$arr);
                $where  .= " and e.source in ({$sources})";
            }
        }

        if($s_time && $e_time) {
            $s_time = CUtil::uint($s_time);
            $e_time = CUtil::uint($e_time) + 86399;
            $where .= " AND (e.ctime BETWEEN {$s_time} AND {$e_time})";
        }

        //vip注册时间
        if($vip_s_time && $vip_e_time) {
            $vip_s_time   = CUtil::uint($vip_s_time);
            $vip_e_time   = CUtil::uint($vip_e_time) + 86399;
            $vip_end_time = strtotime("+3 month", $vip_s_time) + 86399;
            if ($vip_e_time > $vip_end_time) $vip_e_time = $vip_end_time;
            $where       .= " AND (p.ctime BETWEEN {$vip_s_time} AND {$vip_e_time})";
        }

        return $where;
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 用户详情（note：限后台使用）
     */
    public function getUserInfo($user_id)
    {
        $main       = by::users()->getUserMainInfo($user_id);
        if (!$main){
            return [false,'用户不存在'];
        }

        $user    = by::users()->getOneByUid($user_id);
        $extend  = $this->getUserExtendInfo($user_id);
        $u_guide = by::userGuide()->getGuideByUid($user_id);
        $phone   = by::Phone()->GetPhoneByUid($user_id);
        $p_time  = by::Phone()->GetCtimeByUid($user_id);
        if (!empty($extend['source'])){
            list($source) = $this->sourceConfig($extend['source']);
        }

        $point      = by::point()->get($user_id);

        $addressArr = by::Address()->GetDefaultAddress($user_id);
        if (!empty($addressArr['id'])){
            $address = by::Address()->GetOneAddress($user_id,$addressArr['id']);
        }

        $r_info = by::userRecommend()->getInfoByUid($user_id);
        $r_id   = $r_info['r_id'] ?? 0;
        if (!empty($r_info['expire_time']) && START_TIME > $r_info['expire_time']) {
            $r_id = 0;
        }

        $data = [
            'user_id'       => $user_id,
            'avatar'        => $user['avatar'] ?? '',
            'nick'          => $user['nick'] ?? '',
            'sex'           => $user['sex'] ?? '',
            'phone'         => $phone,
            'active_time'   => $user['active_time'] ?? '',
            'reg_time'      => $main['reg_time'],
            'vip_time'      => $p_time ?? '',
            'source'        => $source ?? '其他',
            'status'        => strpos($main['openudid'], '|') ? '注销' : '正常',
            'point'         => $point ?? 0,
            'address'       => $address ?? [],
            'birthday'      => $user['birthday'] ?? 0,
            'area'          => $user['area'] ?? '',
            'real_name'     => $user['real_name'] ?? '',
            'is_invite'     => !empty($extend['guide_id']) ? 1:0,
            'guide'         => $u_guide,
            'r_id'          => $r_id,
            'tag'           => $extend['tag'] ??'',
            'is_new_gift'   => $user['is_new_gift'] ?? '0',
            'is_vip'        => $phone ? 1 :0,
        ];

        return [true,$data];
    }


    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }


    /**
     * @throws Exception
     * 更新门店信息
     */
    public function updateStoreData($unionId, $store): bool
    {
        // 获取游客表中的用户ID
        $noAutUserId = by::Rusers()->getUserIdByUnionId($unionId, 1);

        if (!empty($noAutUserId)) {
            // 获取游客表中的用户扩展信息
            $noAutUserExtend = by::RuserExtend()->getUserExtendInfo($noAutUserId, false);
            $currentTime = intval(START_TIME);

            if (!empty($noAutUserExtend) && empty($noAutUserExtend['store']) && abs($currentTime - $noAutUserExtend['ctime']) < by::WeFocus()::UPDATE_STORE_EXPIRE) {
                // 更新游客表的 store 字段
                $affectedRows = by::dbMaster()->createCommand()
                    ->update(self::tbName(), ['store' => $store], ['user_id' => $noAutUserId])
                    ->execute();

                // 返回更新是否成功
                return $affectedRows > 0;
            }
        }

        // 如果没有执行更新，返回 false
        return false;
    }


}
