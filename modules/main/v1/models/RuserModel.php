<?php

namespace app\modules\main\v1\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\main\models\UserModel;
use Faker\Provider\Uuid;

class RuserModel extends CommModel
{

    public static $expire = 1800;

    //0：保密，1：男，2：女  PHP MYSQL 枚举类型问题较多
    const USER_SEX = ['S' => 0, 'M' => 1, 'W' => 2];
    const USER_SEX_TXT = [0 => '保密', 1 => '男', 2 => '女'];


    //1 QQ, 99游客
    const USER_TYPE = [
        'QQ' => 1, 'VISITOR' => 99
    ];

    //针对不同场景 授权可修改字段
    CONST SCENARIOS = [
        'LOGIN'  => [ 'sex', 'nick', 'age', 'avatar', 'active_time'],
        'MODIFY' => [ 'sex', 'nick', 'age', 'avatar','real_name','birthday','area','is_new_gift'],
        'AUTH'   => [ 'oa_openid'],

    ];

    CONST STATUS = [
        0   =>'正常',
        1   =>'冻结',
        2   =>'平台禁用'
    ];

    public static function userMainTb(): string
    {
        return '`db_dreame_no_auth`.`t_users_main`';
    }

    /**
     * @param string $user_id
     * @return string
     * session key 标准
     */
    private function __sessionKey($user_id=''): string
    {
        return AppCRedisKeys::sessionKey($user_id);
    }


    private function __getRealRMainUserByIdKey($user_id)
    {
        return AppCRedisKeys::getRealRMainUserByIdKey($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 分表user tb
     */
    public static function userTb($user_id): string
    {
        //user_id 是uuid 所以要查询对应的id
        $tb         = self::userMainTb();
        $sql        = "SELECT `id` From {$tb} WHERE `user_id`=:user_id  LIMIT 1";
        $aData      = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id])->queryOne();
        $user_id    = $aData['id'] ?? 0;
        $mod     = $user_id % 100;

        return "`db_dreame_no_auth`.`t_user_{$mod}`";
    }


    /**
     * @param $nick
     * @param int $length
     * @return string
     * 昵称截取
     */
    public function validateNick($nick, $length = 32): string
    {
        return mb_substr(trim($nick), 0, $length, 'utf-8');
    }

    /**
     * 根据 $openudid  $user_type 获取 user_id
     * @param string $openudid
     * @param string $user_type
     * @return string
     * @throws \yii\db\Exception
     */
    public function getUserIdByOpenUdId(string $openudid = '', string $user_type = '', bool $cache = true): string
    {
        $loginType   = 1;//游客
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::getUserIdByOpenUdId($openudid, $user_type, $loginType);
        $user_id     = $cache ? $redis->get($session_key) : false;
        if (empty($user_id)) {
            $tb      = self::userMainTb();
            $sql     = "SELECT `user_id` From {$tb} WHERE `openudid`=:openudid  LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':openudid' => $openudid])->queryOne();
            $user_id = $aData['user_id'] ?? 0;
            $redis->set($session_key, $user_id, ['EX' => $user_id ? self::$expire : 10]);
        }
        return CUtil::checkUuid($user_id);
    }

    /**
     * @param string $user_id
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据user_id 获取用户信息
     */
    public function getOneByUid(string $user_id)
    {
        if (empty(CUtil::checkUuid($user_id))) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = AppCRedisKeys::getOneByUid($user_id);
        $aJson      = $redis->get($redis_key);
        $user_info  = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $user_tb = $this->userTb($user_id);
            $sql     = "SELECT * FROM {$user_tb} WHERE `user_id`=:user_id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);

            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            !empty($user_info) && $redis->set($redis_key, json_encode($user_info), ['EX' => self::$expire]);
        }
        if(!empty($user_info['area'])){
            $user_info['area'] = json_decode($user_info['area'],true);
        }
        return $user_info;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function getRealMainUserById(string $user_id , int $user_type = 1, bool $cache = true)
    {
        if (empty(CUtil::checkUuid($user_id))) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = $this->__getRealRMainUserByIdKey($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = $this -> userMainTb();
            $sql     = "SELECT * FROM {$tb} WHERE `user_id`=:user_id AND `user_type`= :user_type AND locate('|',`openudid`)=0 LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);
            $command->bindParam(":user_type", $user_type);
            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            !empty($user_info) && $redis->set($redis_key, json_encode($user_info), ['EX' => self::$expire]);
        }

        return $user_info;
    }

    /**
     * @param string $user_id
     * @return bool
     * @throws \yii\db\Exception
     * 删除 各类redis缓存
     */
    public function deleteRedisCache(string $user_id = '',$logOut= false): bool
    {
        if($user_id){
            $user       = $this->getUserMainInfo($user_id);
            $openudid   = $user['openudid']  ?? '';
            $user_type  = $user['user_type'] ?? 0;
            $this->delUserMainCacheByOpenudid($openudid, $user_type);

            $this->delUserMainCache($user_id);
            $this->delUserCache($user_id);
            $redis_key = $this->__getRealRMainUserByIdKey($user_id);
            by::redis('core')->del($redis_key);

            if($logOut){
               $session_key = $this->__sessionKey($user_id);
               by::redis('core')->del($session_key);
            }
        }
        return true;
    }

    /**
     * @param $openudid
     * @param $user_type
     * @return array
     * @throws \yii\db\Exception
     * 获取用户信息
     */
    public function getOneByOpenUdId($openudid, $user_type): array
    {
        $user_id = $this->getUserIdByOpenUdId($openudid, $user_type);
        if(empty(CUtil::checkUuid($user_id))){
            return [];
        }
        return $this->getOneByUid($user_id);
    }


    /**
     * @param $userInfo
     * @return array
     * @throws \yii\db\Exception
     * 新用户注册
     */
    public function register($userInfo): array
    {
        $user_type      = isset($userInfo['user_type']) ? intval($userInfo['user_type']) : 0;
        $loginControl   = CUtil::getConfig('loginControl', 'common', MAIN_MODULE);
        $uTypes         = is_array($loginControl) ? array_keys($loginControl) : "";
        if (empty($uTypes)) {
            return [false, "系统用户类型不存在！"];
        }

        if (empty($userInfo) || empty($userInfo['openudid']) || ($user_type <= 0) ||
            (!in_array($user_type, $uTypes, true))) {
            return [false, "必要注册信息缺失：" . json_encode($userInfo)];
        }

        //防止多次请求注册
        $unique_key = __FUNCTION__ . "|{$userInfo['openudid']}|1";
        list($anti) = self::ReqAntiConcurrency(0, $unique_key, 3, 'EX');
        if (!$anti) {
            return [false, '重复多次注册请求~：' . json_encode($userInfo)];
        }

        $nick           = empty($userInfo['nick']) ? '' : $this->validateNick($userInfo['nick']);
        $nick           = trim($nick);
        $connection     = by::dbMaster();
        $transaction    = $connection->beginTransaction();
        try {
            $user_id = Uuid::uuid();
            $userDetail['openudid'] = $userInfo['openudid'];
            $userDetail['unionid']  = $userInfo['unionid']??'';
            $userDetail['nick']     = $nick;
            $userDetail['avatar']   = $userInfo['avatar'] ?? '';
            $userDetail['age']      = isset($userInfo['age']) ? CUtil::uint($userInfo['age']) : 22;
            $userDetail['sex']      = isset($userInfo['sex']) ? CUtil::uint($userInfo['sex']) : 0;
            extract($userDetail);
            $reg_time = time();
            $tb_main  = self::userMainTb();

            $query    = " INSERT IGNORE INTO {$tb_main} (`user_id`,`openudid`,`unionid`,`reg_time`, `user_type`)
                          VALUE (:user_id,:openudid, :unionid, :reg_time, :user_type)";
            $command  = $connection->createCommand($query);
            $command->bindParam(":user_id", $user_id);
            $command->bindParam(":openudid", $openudid);
            $command->bindParam(":reg_time", $reg_time);
            $command->bindParam(":user_type",$user_type);
            $command->bindParam(":unionid",$unionid);
            $insert_flag = $command->execute();
            if (empty($insert_flag)) {
                throw new \Exception("注册用户主表失败|insert_flag:{$insert_flag}" . "| SQL:" . $command->getRawSql());
            }
            $userDetail['user_id']          = $user_id;
            $userDetail['isRegisterFlag']   = strlen($userDetail['user_id']) > 0 ? 1 : 0;

            if ($userDetail['user_id']) {
                $tb_detail = self::userTb($userDetail['user_id']);
                $sql       = "INSERT INTO {$tb_detail} (`user_id`,`sex`,`nick`,`age`,`avatar`) 
                            VALUE (:user_id, :sex, :nick,:age,:avatar) ";
                $command = $connection->createCommand($sql);
                $command->bindParam(":user_id", $userDetail['user_id']);
                $command->bindParam(":sex", $userDetail['sex']);
                $command->bindParam(":nick", $userDetail['nick']);
                $command->bindParam(":age", $age);
                $command->bindParam(":avatar", $userDetail['avatar']);
                //@return integer number of rows affected by the execution.
                $insert_flag = $command->execute();
                if (empty($insert_flag)) {
                    throw new \Exception("注册用户详情表失败|insert_flag:{$insert_flag}");
                }
            } else {
                throw new \Exception("注册插入主表失败|user_id:{$userDetail['user_id']}");
            }

            $transaction->commit();

//            self::ReqAntiConcurrency(0, $unique_key, 0, 'DEL');
            return [true, $userDetail];

        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'err.ruser_register');
            $transaction->rollback();//插入失败rollback
            return [false, $e->getMessage()];
        }
    }

    /**
     * @param $user_info
     * @param int $is_need_delete_cache :需要删除缓存
     * @return bool
     * @throws \yii\db\Exception
     * 更新用户信息或缓存
     */
    public function updateLoginInfo(&$user_info, int $is_need_delete_cache = 0): bool
    {

        if (empty($user_info)||empty(CUtil::checkUuid($user_info['user_id'] ?? 0))) {
            return false;
        }

        $now = time();
        $user_info['isTodayFirst'] = 0;

        if (empty($user_info['isRegister'])){
            $todayServen = strtotime("today");
            $active_time = strtotime(date("Ymd", $user_info['active_time'] ?? $now));
            $user_info ['isTodayFirst'] = 0;
            if ($active_time < $todayServen) {
                $user_info ['isTodayFirst'] = 1;
                $is_need_delete_cache = 1;
            }
        }else{
            $user_info ['isTodayFirst'] = 1;
            $is_need_delete_cache = 1;
        }

        $need_update['active_time'] = $now;

        if (!empty($need_update)) {
            $user_info['active_time'] = $now;
            $this->updateMembersInfo($user_info['user_id'], $need_update,self::SCENARIOS['LOGIN']);
        }

        if ($is_need_delete_cache) {
            $this->deleteRedisCache($user_info['user_id']);
        }

        return true;
    }

    /**
     *
     * @param $user_id
     * @param $need_update
     * @param mixed $allowed
     * @return bool
     * @throws \yii\db\Exception
     * 更新用户表字段
     */
    public function updateMembersInfo($user_id, $need_update,$allowed=self::SCENARIOS['MODIFY']): bool
    {
        if ((!is_array($need_update)) || empty(CUtil::checkUuid($user_id))) {
            return false;
        }

        if (isset($need_update['avatar'])) {
            $avatar = urldecode($need_update['avatar']);
            $need_update['avatar'] = $avatar;
        }

        if (isset($need_update['nick'])) {
            $nick = urldecode($need_update['nick']);
            $nick = $this->validateNick($nick, 50);
            $nick = strip_tags($nick);
            $need_update['nick'] = $nick;
        }

        if (isset($need_update['real_name'])) {
            $real_name = urldecode($need_update['real_name']);
            $real_name = $this->validateNick($real_name, 50);
            $real_name = strip_tags($real_name);
            if (empty($real_name)) {
                unset($need_update['real_name']);
            } else {
                $real_name = str_replace("\n",'',$real_name);
                $need_update['real_name'] = $real_name;
            }
        }

        if(!empty($need_update['area']) || !empty($need_update['province']) || !empty($need_update['city'])){
            $data                = [
                'area'     => $need_update['area'] ?? '',
                'province' => $need_update['province'] ?? '',
                'city'     => $need_update['city'] ?? '',
            ];
            $need_update['area'] = json_encode($data);
        }

        if (isset($need_update['birthday'])) {
            $need_update['birthday'] = intval($need_update['birthday']);
        }

        if (isset($need_update['sex'])) {
            $need_update['sex'] = CUtil::uint($need_update['sex']);
        }

        $connection = by::dbMaster();
        $tb_detail = $this->userTb($user_id);

        $sql = " UPDATE {$tb_detail} SET ";

        $bindParam = [];
        foreach ($need_update as $field => $val) {
            if (in_array($field, $allowed)) {
                $escape = ":{$field}";
                $update_sql[] = "`{$field}`={$escape}";
                $bindParam[$escape] = strip_tags($val);
            }
        }

        if (empty($update_sql) || empty($bindParam)) {
            return false; //没有可修改的字段
        }

        $sql .= implode(' , ', $update_sql);
        $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

        $command = $connection->createCommand($sql);

        //bindParam的第二个参数要求是引用变量！
        foreach ($bindParam as $special_param => &$special_bind) {
            $command->bindParam($special_param, $special_bind);
        }

        $command->bindParam(":user_id", $user_id);

        $command->execute();

        //清空缓存
        $this->deleteRedisCache($user_id);

        return true;

    }
    /**
     *
     * @param $user_id
     * @param $need_update
     * @param mixed $allowed
     * @return bool
     * @throws \yii\db\Exception
     * 更新用户表字段
     */
    public function updateMembersInfoNew($user_id, $need_update,$allowed=self::SCENARIOS['MODIFY']): bool
    {
        if ((!is_array($need_update)) || empty(CUtil::checkUuid($user_id))) {
            return false;
        }

        if (isset($need_update['avatar'])) {
            $avatar = urldecode($need_update['avatar']);
            $need_update['avatar'] = $avatar;
        }

        if (isset($need_update['nick'])) {
            $nick = urldecode($need_update['nick']);
            $nick = $this->validateNick($nick, 50);
            $nick = strip_tags($nick);
            $need_update['nick'] = $nick;
        }

        if (isset($need_update['real_name'])) {
            $real_name = urldecode($need_update['real_name']);
            $real_name = $this->validateNick($real_name, 50);
            $real_name = strip_tags($real_name);
            if (empty($real_name)) {
                unset($need_update['real_name']);
            } else {
                $real_name = str_replace("\n",'',$real_name);
                $need_update['real_name'] = $real_name;
            }
        }

        if(!empty($need_update['area']) || !empty($need_update['province']) || !empty($need_update['city'])){
            $data                = [
                'area'     => $need_update['area'] ?? '',
                'province' => $need_update['province'] ?? '',
                'city'     => $need_update['city'] ?? '',
            ];
            $need_update['area'] = json_encode($data);
        }

        if (isset($need_update['birthday'])) {
            $need_update['birthday'] = intval($need_update['birthday']);
        }

        if (isset($need_update['sex'])) {
            $need_update['sex'] = CUtil::uint($need_update['sex']);
        }

        $connection = by::dbMaster();
        $tb_detail = $this->userTb($user_id);

        $sql = " UPDATE {$tb_detail} SET ";

        $bindParam = [];
        foreach ($need_update as $field => $val) {
            if (in_array($field, $allowed)) {
                $escape = ":{$field}";
                $update_sql[] = "`{$field}`={$escape}";
                $bindParam[$escape] = strip_tags($val);
            }
        }

        if (empty($update_sql) || empty($bindParam)) {
            return false; //没有可修改的字段
        }

        $sql .= implode(' , ', $update_sql);
        $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

        $command = $connection->createCommand($sql);

        //bindParam的第二个参数要求是引用变量！
        foreach ($bindParam as $special_param => &$special_bind) {
            $command->bindParam($special_param, $special_bind);
        }

        $command->bindParam(":user_id", $user_id);

        $command->execute();

        //清空缓存
        $this->deleteRedisCache($user_id);

        return true;

    }

    /**
     * @param $user_id
     * @return array
     * @throws \yii\db\Exception
     * 根据用户id获取用户主表信息
     */
    public function getUserMainInfo($user_id): array
    {
        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::userMainInfo($user_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb    = self::userMainTb();
            $sql   = "SELECT `openudid`,`unionid`,`reg_time`,`user_type` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id])->queryOne();
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @return array|bool
     * @throws \yii\db\Exception
     * 废弃非正式服的指定账号
     */
    public function Deprecated($user_id)
    {
//        if(YII_ENV_PROD) {
//            return [false,"非法操作"];
//        }

        $user_id = CUtil::checkUuid($user_id, 0);

        $user_info = $this->getUserMainInfo($user_id);
        if (empty($user_info)) {
            return [false,"游客用户不存在(2)"];
        }

        //防止重复提交
        $openid = $user_info['openudid'] ?? "";
        $arr    = explode("|",$openid);
        if(count($arr) > 1) {
            $openid = $arr[0];
        }

        //将账号废弃
        $row = by::dbMaster()->createCommand()->update(self::userMainTb(), [
            'openudid' => "{$openid}|".START_TIME
        ], ['user_id' => $user_id])->execute();

        if($row <= 0) {
            return [false,"游客用户不存在(3)"];
        }

        $this->delUserMainCache($user_id);
        $this->delUserMainCacheByOpenudid($user_info['openudid'], $user_info['user_type']);
        $this->deleteRedisCache($user_id,true);

        return [true,"OK"];
    }

    /**
     * @param $openudid
     * @param $user_type
     * @return int
     * 清空缓存
     */
    public function delUserMainCacheByOpenudid($openudid, $user_type): int
    {
        $loginType   = 1; //游客
        $session_key = AppCRedisKeys::getUserIdByOpenUdId($openudid, $user_type, $loginType);
        return by::redis('core')->del($session_key);
    }


    /**
     * @param $user_id
     * @return int
     * 删除用户主表缓存数据
     */
    public function delUserMainCache($user_id): int
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }

        $redis_key = AppCRedisKeys::userMainInfo($user_id);
        return by::redis('core')->del($redis_key);
    }

    /**
     * @param $user_id
     * @return int
     * 删除用户单表缓存
     */
    public function delUserCache($user_id): int
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }

        $redis_key = AppCRedisKeys::getOneByUid($user_id);
        return by::redis('core')->del($redis_key);
    }

    /**
     * 删除用户主数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::userMainTb();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }

    /**
     * 删除用户副数据
     * @throws \yii\db\Exception
     */
    public function delDataDetailById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::userTb($user_id);
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }


    public function getUserIdByUnionId(string $unionid = '', string $user_type = '')
    {
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::getRUserIdByUnionId($unionid, $user_type);
        $user_id     = $redis->get($session_key);
        if ($user_id === false) {
            $tb      = self::userMainTb();
            $sql     = "SELECT `user_id` From {$tb} WHERE `unionid`=:unionid AND locate('|',`openudid`)=0 ORDER BY `user_id` DESC LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':unionid' => $unionid])->queryOne();
            $user_id = $aData['user_id'] ?? 0;
        }

        return $user_id;
    }
}
