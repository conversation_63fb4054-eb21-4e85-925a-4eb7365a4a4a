<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2022/2/14
 * Time: 17:00
 */
namespace app\modules\main\controllers;

use app\components\AdvAscribe;
use app\components\WXOA;
use app\models\byNew;
use app\models\CUtil;
use Yii;

class HomeController extends CommController {
    /**
     * @OA\Post(
     *     path="/main/home/<USER>",
     *     summary="banner列表",
     *     description="banner列表",
     *     tags={"banner"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"drop_position", "banner_version"},
     *                         @OA\Property(property="drop_position", type="string", default="1",description="投放位置（小程序、app、h5 ： 1、商城首页 2、探觅），（pc ： 1、头部 2、中部）"),
     *                         @OA\Property(property="banner_version", type="string",default="1", description="banner版本（1、默认旧版本 2、3.0版本"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *         )
     *     )
     * )
     */
    public function actionBanner()
    {
        $post          = Yii::$app->request->post();
        $dropPosition  = CUtil::uint($post['drop_position'] ?? 1);
        $bannerVersion = CUtil::uint($post['banner_version'] ?? 1);
        $result        = byNew::BannerModel()->getBannerList($this->platformId, $dropPosition, $bannerVersion);

        $time = time();

        $list = [];

        foreach ($result as $key => $value) {
            $vtime_start = CUtil::uint($value['vtime_start'] ?? 0);
            $vtime_end   = CUtil::uint($value['vtime_end'] ?? 0);
            if ($vtime_start && $vtime_end && !($vtime_start < $time && $vtime_end > $time)) {
                continue;
            }
            $list[] = [
                'id'        => $value['id'] ?? 0,
                'title'     => $value['title'] ?? "",
                'image'     => $value['image'] ?? "",
                'new_image' => $value['new_image'] ?? "",
                'video'     => $value['video'] ?? "",
                'jump_type' => $value['jump_type'] ?? "",
                'jump_url'  => $value['jump_url'] ?? "",
                'appid'     => $value['appid'] ?? "",
            ];
        }

        //推送腾讯
        if ($this->union && stristr($this->union, 'tencent')) {
            AdvAscribe::factory()->push($this->user_id, 'tencent', ['user_id' => $this->user_id, 'url' => $this->referer, 'event' => 'VIEW_CONTENT', 'click_id' => $this->clickId]);
        }

        CUtil::json_response(1, "OK", $list);

    }

    /**
     * 公众号跳转文章
     */
    public function actionOa(){
        CUtil::json_response(1,"OK",['art'=>WXOA::ART]);
    }
}
