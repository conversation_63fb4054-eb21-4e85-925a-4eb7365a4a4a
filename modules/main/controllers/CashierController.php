<?php

namespace app\modules\main\controllers;

use app\models\CUtil;
use app\modules\main\services\CashierService;

class CashierController extends CommController
{


/**
 * @OA\Post(
 *     path="/main/cashier/list",
 *     summary="收银台支付列表",
 *     description="获取收银台支付列表信息",
 *     tags={"支付活动"},
 *     @OA\RequestBody(
 *         @OA\MediaType(
 *             mediaType="application/x-www-form-urlencoded",
 *             @OA\JsonContent(
 *                 allOf={
 *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
 *                     @OA\Schema(
 *                         required={"order_no"},
 *                         @OA\Property(property="order_no", type="string", description="订单号"),
 *                         @OA\Property(property="api", type="string", description="API 标识"),
 *                         @OA\Property(property="sign_time", type="string", description="签名时间"),
 *                         @OA\Property(property="provider", type="string", description="提供者"),
 *                         @OA\Property(property="sessid", type="string", description="会话ID，用于身份验证"),
 *                         @OA\Property(property="user_id", type="string", description="用户ID")
 *                     )
 *                 }
 *             )
 *         )
 *     ),
 *     @OA\Response(
 *         response=200,
 *         description="成功",
 *         @OA\JsonContent(
 *             @OA\Property(property="iRet", type="integer", description="状态码"),
 *             @OA\Property(property="sMsg", type="string", description="提示消息"),
 *             @OA\Property(
 *                 property="data",
 *                 type="object",
 *                 description="数据",
 *                 @OA\Property(
 *                     property="pay_list",
 *                     type="array",
 *                     description="支付列表",
 *                     @OA\Items(
 *                         @OA\Property(property="pay_type", type="integer", description="支付类型"),
 *                         @OA\Property(property="name", type="string", description="支付名称"),
 *                         @OA\Property(
 *                             property="interests",
 *                             type="array",
 *                             description="优惠信息",
 *                             @OA\Items(
 *                                 @OA\Property(property="key", type="string", description="优惠键"),
 *                                 @OA\Property(property="name", type="string", description="优惠名称"),
 *                                 @OA\Property(property="service_fee", type="integer", description="服务费")
 *                             )
 *                         )
 *                     )
 *                 ),
 *                 @OA\Property(property="price", type="string", description="总价格"),
 *                 @OA\Property(property="remain_time", type="integer", description="剩余时间")
 *             )
 *         )
 *     )
 * )
 */

    public function actionList()
    {
        $orderNo = $this->request->post('order_no');
        $result = CashierService::getInstance()->getPayment($this->user_id,$orderNo);
        CUtil::Ret($result);
    }

    // 只返回PC端支付列表，移动端请使用list，pc专用
    public function actionListjd()
    {
        $orderNo = $this->request->post('order_no');
        $result = CashierService::getInstance()->getPaymentPC($this->user_id,$orderNo);
        CUtil::Ret($result);
    }
}