<?php

namespace app\modules\main\controllers;

use app\models\CUtil;
use app\modules\main\forms\employee\OrderRecordListForm;
use app\modules\main\forms\employee\RegisterRecordListForm;
use app\modules\main\forms\employee\ScoreRecordListForm;
use app\modules\main\services\BoundUserOrderService;
use app\modules\main\services\UserBindService;
use app\modules\main\services\UserEmployeeService;

class UserEmployeeController extends CommController
{
    /**
     * 获取员工详情
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionInfo()
    {
        // 用户ID
        $user_id = $this->user_id;

        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->getDetail($user_id);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        if ($res['type'] == 1){
            $res['level'] = 1;
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }
    /**
     * 申请成为追觅大使
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionApply(){
        // 用户ID
        $user_id = $this->user_id;
        // 调用服务
        list($status, $res, $data) = UserEmployeeService::getInstance()->apply($user_id);
        if (!$status) {
            CUtil::json_response(-1, $res, $data);
        }
        CUtil::json_response(1, 'ok', $res);
    }


    /**
     * 微笑分列表
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionScoreList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new ScoreRecordListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->getScoreList($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 注册记录列表
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionRegisterList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new RegisterRecordListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = UserBindService::getInstance()->getRegisterList($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 订单记录列表
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionOrderList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new OrderRecordListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = BoundUserOrderService::getInstance()->getOrderList($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 任务列表
     * @return void
     * @throws \RedisException
     */
    public function actionTaskList()
    {
        $userId = $this->user_id ?? 0;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = UserEmployeeService::getInstance()->getTaskList($userId, $this->platformSource,$this->version);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * 已收到提示
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionReceivedTips()
    {
        $userId = $this->user_id ?? 0;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        UserEmployeeService::getInstance()->closeTips($userId);
        CUtil::json_response(1, 'ok');
    }
}

