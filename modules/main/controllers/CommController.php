<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/3/26
 * Time: 11:57
 */
namespace  app\modules\main\controllers;

use app\components\AppCRedisKeys;
use app\components\JwtTools;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use app\modules\main\models\PlatformModel;
use app\modules\main\models\UserExtendModel;
use app\modules\main\models\UserModel;
use app\modules\main\v1\models\RuserExtendModel;
use app\modules\main\v1\models\RuserModel;
use RedisException;
use Yii;
use yii\db\Exception;
use yii\web\Controller;

/**
 * Class CommController
 * @package app\modules\main\controllers
 * 基础权限父类
 */
class CommController extends Controller {

    /**
     * @OA\SecurityScheme(
     *     type="apiKey",
     *     in="header",
     *     name="userid",
     *     @OA\Schema(type="string"),
     *     securityScheme="userid"
     * )
     */

    /**
     * @OA\SecurityScheme(
     *     type="apiKey",
     *     in="header",
     *     name="sessid",
     *     @OA\Schema(type="string"),
     *     securityScheme="sessid"
     * )
     */


    public $start_time;                                          //便于日志记录
    public $api;                                                 //渠道包
    public $user_id;                                             //用户ID
    public $version;                                             //版本号


    //默认授权全局变量
    public $userAuth = 1;                                        //判断有没有手机授权 默认授权 1：授权 0：未授权
    public $authOpenudid = 0;                                    //已校验openudid
    public $authUnionid = 0;                                     //已校验unionid
    public $authUserinfo = [];                                   //已校验用户信息
    public $authRet = [];                                        //从微信获取的数据

    //微信版本控制
    public $wxVersion;
    public $wxSyncLock = 1;
    public $wxSyncCenterLock = 1;

    //采用主附表确认
    /** @var UserModel|RuserModel */
    public $userModel;                                           //用户主表MODEL
    public $userGuideModel;                                      //用户绑定导购表MODEL
    /** @var UserExtendModel|RuserExtendModel */
    public $userExtendModel;                                     //用户扩展表MODEL
    public $guideModel;                                          //导购表MODEL
    public $userRecommendModel;                                  //用户绑定推荐人表MODEL


    //定义价格来源
    public $getChannel = 0;
    public $spriceType = 0;
    public $isInternalPurchase = 0;


    //平台确认
    public $platformIds = [1,99];
    // 单一平台（默认微信小程序）
    public $platformId = PlatformModel::PLATFORM['WX'];
    //平台来源
    public $platformSource = 0;

    //用户登录类型
    public $loginType;

    public $loginTypeEnum = [
            'TOURIST' => 1, // 无会员信息游客 没授权过
            'MEMBER'  => 2, // 有会员信息  授权过
    ];

    //用户来源判断
    public $union = '';
    public $euid = '';
    public $referer = '';
    public $clickId = '';
    public $liveMark = '';

    //用完后立刻回收
    protected $_sessid           = null;                         //session
    protected $jwtToken          = null;                         //JWT Token
    protected $_sign             = null;                         //签名
    protected $_sign_time        = null;                         //签名时间
    protected $_security_key     = null;                         //签名私钥
    protected $_expire           = YII_ENV_PROD ? 300 : 86400;   //签名有效期 单位：120秒
    protected $_aPost            = [];                           //临时接收参数

    protected $_user_login       = null;                         //用户登录态


    /**
     * 初始化基础参数
     */
    protected function _initBase() {
        header('Access-Control-Allow-Origin:*');
        header('Access-Control-Allow-Headers:*');
        header('Access-Control-REQUEST-METHOD:GET,POST');

        //拒绝一切非post 请求
        if(!Yii::$app->request->isPost) {
            CUtil::json_response(-1,'请求方式错误');
        }

        $this->start_time   = microtime(true);
        $this->api          = CUtil::getRequestParam("cookie",'api');
        $this->api          = empty($this->api) ? CUtil::getRequestParam("post",'api') : $this->api;
        list($status,$this->_security_key) = CommModel::getApiKey($this->api);
        if($status == -1) {
            CUtil::json_response(-1,'无效的Api信息'.$this->api);
        }

        $this->version      = CUtil::getRequestParam("cookie",'version');
        $this->version      = empty($this->version) ? CUtil::getRequestParam("post",'version') : $this->version;

        $this->_aPost       = Yii::$app->request->post();
        $this->_sign        = empty($this->_aPost['sign']) ? "" : trim($this->_aPost['sign']);

        // 获取token 从header中获取 如果没有的话从 body中取 sessid（值为token）
        $this->jwtToken = CUtil::getRequestParam("headers", 'Access-Token', '');
        $this->jwtToken = empty($this->jwtToken) ? CUtil::getRequestParam("post", 'sessid', '') : $this->jwtToken;


        $this->_sign_time   = $this->_aPost['sign_time'] ?? 0;
        $this->_sign_time   = CUtil::uint($this->_sign_time);



        //签名加密使用
//        $this->_sessid      && $this->_aPost['sessid']  = $this->_sessid;
        $this->version      && $this->_aPost['version'] = $this->version;
        $this->api          && $this->_aPost['api']     = $this->api;
    }

    /**
     * 签名合法性校验
     */
    protected function _isValidSign()
    {
        unset($this->_aPost['sign']);

        //开发服测试服调试
        if(!YII_ENV_PROD && isset($this->_aPost['teaccaaa'])) {
            unset($this->_aPost['teaccaaa']);
            die(CommModel::getSign($this->_aPost,$this->_security_key));
        }

        //开发测试服调试不需要签名验证
        if(!YII_ENV_PROD && isset($this->_aPost['provider'])){
            return true;
        }

        //有效期检验
        if($this->_sign_time < intval(START_TIME) - $this->_expire) {
            CUtil::json_response(-1,'签名已失效');
        }

        //签名校验
        $verify = CommModel::getSign($this->_aPost,$this->_security_key);
        if($this->_sign !== $verify) {
            CUtil::json_response(-1,'无效的签名信息');
        }
    }

    /**
     * 会话已过期
     */
    protected function _expired() {
        $code = CUtil::getConfig('code','http_code',MAIN_MODULE);
        CUtil::json_response($code['session_expired'],"会话已过期，请重新登录",null);
    }

    /**
     * 垃圾回收
     */
    protected function _gc() {

        unset(
            $this->_sessid,     $this->_sign,
            $this->_sign_time,  $this->_security_key,
            $this->_expire,     $this->_aPost
        );
    }


    /**
     * 微信版本控制主副库和用户同步APP
     * @return void
     */
    protected function _versionControl()
    {
        $config = CUtil::getConfig('wx_version_ctrl','common',MAIN_MODULE);
        $this->wxVersion = CUtil::getRequestParam('headers','wx-version');
        $this->wxSyncLock = $config['wx_sync_lock']??1;
        $this->wxSyncCenterLock = $config['wx_sync_center_lock']??1;
    }



    /**
     * 判断用户的合法性，如果在副表的用户数据已经迁移到主表，则禁止uuid登陆
     * @return void
     * @throws Exception
     */
    protected function _isValidUser()
    {
        $userId = $this->user_id;
        if ($userId && is_string($userId) && mb_strlen($userId) > 10) {
            //查找附表是否已经同步主表，已经同步加锁，禁止登陆
            $userData = by::RuserExtend()->getUserExtendInfo($userId, false);
            if ($userData && isset($userData['main_user_id']) && $userData['main_user_id']) {
                CUtil::json_response(-100, "用户不符合规定", ['is_lock' => 1]);
            }
        }
    }

    protected function _judgePlatform()
    {
        $AppApis = ['a_1664246268','i_1666147923'];
        if(in_array($this->api,$AppApis)){
            $this->platformIds = [2,99];//app 平台
            $this->platformId = PlatformModel::PLATFORM['APP'];//app 平台
        }

        $pcApis = ['p_1712037068', 'p_1744612441'];
        if (in_array($this->api, $pcApis)) {
            $this->platformIds = [3];//pc 不在全平台范围内
            $this->platformId = PlatformModel::PLATFORM['PC'];//pc 不在全平台范围内
        }

        //平台来源
        $platformSourceConfig = CUtil::getConfig('platformSource', 'common', MAIN_MODULE) ?? [];
        if ($platformSourceConfig) {
            $this->platformSource = $platformSourceConfig[$this->api] ?? 0;
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    protected function _judgeSource(){
        $unionArr =  CUtil::getConfig('unionFields', 'spread', MAIN_MODULE) ?? [];
        $euidArr = CUtil::getConfig('euidFields', 'spread', MAIN_MODULE) ?? [];
        $cps     = CUtil::removeXss($this->_aPost['cps'] ?? '');

        $this->referer = CUtil::removeXss($this->_aPost['referer'] ?? '');
        $this->referer = html_entity_decode($this->referer);//移除 amp;

        $this->liveMark = CUtil::removeXss($this->_aPost['live_mark'] ?? '');
        if($cps){
            //获取链接
            list($s,$data) = by::spread()->getSpreadCode($cps);
            $path  = $data['path'] ?? '';
            $query = $data['query'] ?? '';
            $scene = $data['scene'] ?? '';

            if($s && $path){
                $this->referer = $path . '/?' . $query . (empty($query) ? $scene : ('&' . $scene));
            }
            //根据cps 获取对应的参数
            if($scene){
                $sceneArr = CUtil::decodeUrlQuery($scene);
                if($sceneArr && is_array($sceneArr)){
                    foreach ($sceneArr as $key=>$item){
                        $item && $this->_aPost[$key] = $item;
                    }
                }
            }
        }

        foreach ($unionArr as $union){
            $this->union = CUtil::removeXss($this->_aPost[$union] ?? '');
            if(!empty($this->union)) {
                break;
            }
        }

        foreach ($euidArr as $euid){
            $this->euid = CUtil::removeXss($this->_aPost[$euid] ?? '');
            if(!empty($this->euid)) {
                break;
            }
        }

        if ($this->referer) {
            if (empty($this->union) || empty($this->euid)) {
                list($url, $referArr) = CUtil::getUrlParam($this->referer);
                $this->union = $referArr['union'] ?? '';
                $this->euid  = $referArr['euid'] ?? '';
            }

            // 解析腾讯平台参数
            $referParam    = by::advAscribeModel()->GetRefererParam($this->referer);
            $this->union   = empty($referParam['union']) ? $this->union : $referParam['union'];
            $this->clickId = empty($referParam['click_id']) ? $this->clickId : $referParam['click_id'];
        }


        $data = [
                'user_id'   => $this->user_id,
                'union'     => $this->union,
                'euid'      => $this->euid,
                'live_mark' => $this->liveMark,
                'referer'   => $this->referer,
        ];

        by::userAdv()->saveLog($data);
    }

    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 判断微信登录状态
     */
    protected function _judgeWxLoginStatus()
    {
        // 获取并验证用户类型和登录类型
        $userType      = CUtil::uint($this->_aPost['user_type'] ?? 0);        // 用户登录平台类型 1:微信 20:支付宝 30:PC
        $userLoginType = CUtil::uint($this->_aPost['user_login_type'] ?? 0);  // 用户登录类型 传2：会员从游客登录、3 游客登录

        // 如果是微信登录
        if ($userType == 1) {
            // 校验微信用户信息
            list($status, $wxAuthData) = WeiXin::factory()->getWxAuthByOpenid($this->_aPost);
            if (!$status) {
                CUtil::json_response(-1, $wxAuthData); // 校验失败，返回错误信息
            }

            $this->authRet      = $wxAuthData;
            $openudid           = $wxAuthData['openudid'] ?? '';
            $unionid            = $wxAuthData['unionid'] ?? '';
            $this->authOpenudid = $openudid;
            $this->authUnionid  = $unionid;

            // 根据登录类型获取用户信息+是否授权给过
            $this->getLoginTypeOpenudid($openudid);

            // 如果前端有传入user_login_type 那代表真正授权 要返回会员信息
            if ($userLoginType == 2 || $userLoginType == 3) {
                $this->_user_login = 1;
            } else {
                // 区分当前用户登录状态 1会员登录 2会员退出后游客登录 3游客登陆
                if ($this->loginType == $this->loginTypeEnum['MEMBER']) {
                    // 获取用户退出状态
                    $mainUserInfo = by::users()->getOneByOpenUdId($openudid, 1);
                    // 这个user_id为主用户user_id
                    $logoutStatus = by::redis()->get(AppCRedisKeys::userLogoutStatus($mainUserInfo['user_id'] ?? ''));
                    // 当前为会员登录状态
                    $this->_user_login = $logoutStatus ? 2 : 1;
                    // 2：会员退出后游客登录；1：会员登录
                } elseif ($this->loginType == $this->loginTypeEnum['TOURIST']) {
                    // 当前为游客登录状态
                    $this->_user_login = 3;
                }
            }

            // 设置用户权限  1:登录状态  0：游客状态
            $this->userAuth = ($this->_user_login == 1) ? 1 : 0;

            // 根据用户权限获取用户信息
            $user_info = ($this->userAuth === 1)
                    ? by::users()->getOneByOpenUdId($openudid, 1)
                    : by::Rusers()->getOneByOpenUdId($openudid, 1);



        }
        // 设置用户信息和模型
        $this->authUserinfo = $user_info ?? [];
    }


    /**
     * 判断主副表
     * @return void
     * @throws Exception
     */
    protected function _judgeDataBases()
    {
        // 获取并验证用户类型（1: 微信, 20: 支付宝, 30: PC）
        $userType = CUtil::uint($this->_aPost['user_type'] ?? 0);

        // 默认用户权限（0: 游客，1: 登录状态）
        if ($userType == 1) {
            // 微信登录，根据 _user_login 确定用户权限
            $this->userAuth = ($this->_user_login == 1) ? 1 : 0;
        } elseif (CUtil::checkUuid($this->user_id)) {
            // 如果是uuid代表是游客
            $this->userAuth = 0;
        }else{
            $this->userAuth = 1;
        }

        $this->setUserModels($this->userAuth);
    }


    /**
     * @param $openudid
     * @return void
     * @throws Exception
     * 通过openudid获取用户是否授权过
     */
    public function getLoginTypeOpenudid($openudid)
    {
        // 获取用户登录类型 如果用户授权给过
        if (by::users()->getOneByOpenUdId($openudid, 1)){
            $this->loginType = $this->loginTypeEnum['MEMBER'];
        }else{
            $this->loginType = $this->loginTypeEnum['TOURIST'];
        }
    }

    /**
     * 设置用户相关模型
     *
     * @param int $userAuth 用户权限 1：会员 0：游客
     */
    protected function setUserModels(int $userAuth)
    {
        $isTourist                = empty($userAuth); // true：游客 false：会员
        $this->userModel          = $isTourist ? by::Rusers() : by::users();
        $this->userGuideModel     = $isTourist ? by::RuserGuide() : by::userGuide();
        $this->userExtendModel    = $isTourist ? by::RuserExtend() : by::userExtend();
        $this->guideModel         = $isTourist ? by::Rguide() : by::guide();
        $this->userRecommendModel = $isTourist ? by::RuserRecommend() : by::userRecommend();
    }


    /**
     * @return void
     * request 处理对进入的参数进行自定义化处理
     */
    protected function _definePostFunction()
    {
        //判断优惠券来源类型
        $this->isInternalPurchase = intval($this->_aPost['is_internal_purchase'] ?? -1);

//        $this->getChannel = ($this->isInternalPurchase == 1) ? (by::userCard()::GET_CHANNEL['internal_purchase']) : 0; //内购查看优惠券放开

        //采用价格种类
        $this->spriceType = ($this->isInternalPurchase == 1) ? (by::Gmain()::SPRICE_TYPE['INTERNAL']) : 0;

    }

    /**
     * @return void
     * 解析token中数据
     * @throws RedisException
     */
    protected function _setUserData()
    {
        // 如果 Token 为空，则直接返回
        if (empty($this->jwtToken)) {
            $this->user_id = CUtil::checkUuid($this->user_id);
            return;
        }

        // 解析 JWT Token 并验证有效性
        list($status, $jwtDecodeSub) = JwtTools::factory()::decodeJsonWebToken($this->jwtToken);
        if ($status && JwtTools::factory()::isTokenInvalid($jwtDecodeSub['user_id'] ?? 0, $this->jwtToken)) {
            $this->_expired();
            return;
        }

        // 从 Token 中获取 user_id
        $this->user_id = $jwtDecodeSub['user_id'] ?? 0;
    }


    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool
    {
        try{

            $this->_initBase();

            //设置用户数据
            $this->_setUserData();

            // 往header里面添加request_id
            $request_id = CUtil::getRequestParam('headers','request_id','');
            if(empty($request_id)){
                $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
                Yii::$app->request->headers->add('request_id', $request_id);
            }

            //版本控制
            $this->_versionControl();

            //签名合法性校验
            $this->_isValidSign();

            //微信登录状态判断
            $this->_judgeWxLoginStatus();

            //判断主附表
            $this->_judgeDataBases();

            //判断平台来源
            $this->_judgePlatform();

            //判断用户来源和订单来源
            $this->_judgeSource();

            //判断价格改动
            $this->_definePostFunction();

            //登录态白名单
            $path_info = Yii::$app->request->getPathInfo();
            $no_check  = CUtil::getConfig("noCheckUri",'common',MAIN_MODULE);
            if(in_array($path_info,$no_check,true)) {
                return true;
            }

            //用户合法性校验
            if(!$this->user_id) {
                $this->_expired();
            }

            //登陆态游客登录校验校验
            $touristAllow = CUtil::getConfig('touristAllow','common',MAIN_MODULE);
            if ((empty($this->user_id) || strlen($this->user_id) > 11) && !in_array($path_info, $touristAllow, true)) {
                CUtil::json_response(1, 'ok！', []);
            }

            //临时垃圾回收
            $this->_gc();

            return true;
        } catch (\Exception $e){
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
            return false;
        }
    }
}
