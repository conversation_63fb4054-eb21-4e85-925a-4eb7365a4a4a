<?php
namespace app\modules\main\controllers;

use app\constants\RespStatusCodeConst;

use app\models\CUtil;

use app\modules\main\services\ProductMatrixService;


class ProductMatrixController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/product-matrix/category-list",
     *     summary="产品站商品品类列表",
     *     description="产品站商品品类列表",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixCategoryListResponse")
     *         )
     *     )
     * )
     */
    public function actionCategoryList()
    {
        $result = ProductMatrixService::getInstance()->categoryList();
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $result);
    }

    /**
     * @OA\Post(
     *     path="/main/product-matrix/detail",
     *     summary="产品站信息详情",
     *     description="产品站信息详情",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"category_id"},
     *                         @OA\Property(property="id", type="integer", description="介绍页信息ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixIntroDetailResponse")
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        // 获取请求参数
        $categoryId = \Yii::$app->request->post('category_id', 0);

        // 检查 category_id 是否有效
        if (!$categoryId) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }

        try {
            // 调用服务层获取产品站信息详情
            $productMatrixDetail = ProductMatrixService::getInstance()->getDetail($categoryId);
            // 返回成功响应，携带产品站信息详情
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $productMatrixDetail);
        } catch (\Exception $e) {
            // 捕获异常并记录日志
            CUtil::setLogMsg(
                    "err.detail_product_matrix",
                    $categoryId,
                    [],
                    [],
                    \Yii::$app->controller->route,
                    '获取产品站详情失败: ' . $e->getMessage()
            );
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }



    /**
     * @OA\Post(
     *     path="/main/product-matrix/intro-detail",
     *     summary="前台--官方介绍详情",
     *     description="前台--官方介绍详情",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="integer", description="ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixIntroDetailResponse")
     *         )
     *     )
     * )
     */
    public function actionIntroDetail()
    {
        $id = \Yii::$app->request->post('id', 0);
        if (empty($id) || !is_numeric($id) || $id <= 0) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }
        $result = ProductMatrixService::getInstance()->introDetail($id);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $result);
    }
}