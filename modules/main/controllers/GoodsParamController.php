<?php

namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\main\services\CateService;
use app\modules\main\services\GmainService;
use app\modules\main\services\GoodsPlatformService;
use app\modules\main\services\GparamGoodsService;
use Yii;

/**
 * 商品参数控制器
 */
class GoodsParamController extends CommController
{
    /**
     * 获取单个商品的参数详情
     */
    public function actionIndex()
    {
        // 请求的参数
        $params = Yii::$app->request->post();

        // 获取参数 sku
        $sku = $params['sku'] ?? '';

        // 验证参数
        if (empty($sku)) {
            CUtil::json_response(-1, "参数错误", []);
        }

        // 获取商品参数
        $res = GparamGoodsService::getInstance()->GetParamsInfoBySku($sku);
        CUtil::json_response(1, "ok", $res);
    }

    /**
     * 商品参数对比（对比页面-详情）
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function actionCompareDetail()
    {
        // 请求的参数
        $params = Yii::$app->request->post();

        // 获取参数 skus
        $skus = $params['skus'] ?? '';
        $skus = (array)json_decode($skus, true);
        // 验证参数
        if (count($skus) < 2) { // 商品小于2
            CUtil::json_response(-1, "对比的商品小于2");
        }

        // 获取商品参数
        list($status, $res) = GparamGoodsService::getInstance()->GetParamsListBySkus($skus, true, $this->platformId);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, "ok", $res);
    }

    /**
     * 商品参数对比（商品详情页）
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function actionCompare()
    {
        // 请求的参数
        $params = Yii::$app->request->post();

        // 获取参数 sku
        $sku = $params['sku'] ?? '';
        $goods = by::Gmain()->GetOneBySku($sku);

        if (empty($goods) || $goods['status'] == by::Gmain()::STATUS['OFF_SALE']) {
            CUtil::json_response(1, "ok", []);
        }

        // 验证是否存在对比的商品，is_compare 为 0 则不对比，返回空。
        if (empty($goods['compare_sku']) || empty($goods['is_compare'])) {
            CUtil::json_response(1, "ok", []);
        }

        // 校验平台
        $compare_goods = by::Gmain()->GetOneBySku($goods['compare_sku']);
        $gids = [
            $goods['id'],
            $compare_goods['id'] ?? 0
        ];
        $status = GoodsPlatformService::getInstance()->checkPlatform($gids, $this->platformId);
        if (!$status) {
            CUtil::json_response(1, "ok", []);
        }

        // 获取商品参数
        list($status, $res) = GparamGoodsService::getInstance()->GetParamsListBySkus([$sku, $goods['compare_sku']], false, $this->platformId);
        if (!$status) {
            CUtil::json_response(1, "ok", []);
        }

        // 处理数据
        $num = $params['num'] ?? 5; // 默认展示五条数据
        $res = $this->__formatCompareData($res, $num);
        CUtil::json_response(1, "ok", $res);
    }

    /**
     * 商品列表
     */
    public function actionGoodsList()
    {
        // 请求的参数
        $params = Yii::$app->request->post();
        $cateId = $params['c_id'] ?? '';

        // 验证类目
        if (empty($cateId)) {
            CUtil::json_response(-1, "商品分类不存在");
        }

        // 验证是否为二级分类
        $cateData = by::cateModel()->getOneInfoById($cateId);
        if (empty($cateData)) {
            CUtil::json_response(-1, "商品分类不存在");
        }
        if ($cateData['level'] != 2) {
            CUtil::json_response(-1, "商品分类有错误");
        }

        // 获取商品列表
        $items = GmainService::getInstance()->GetGoodsListByCateId($cateId, $this->platformId);

        // 获取分类名
        $name = CateService::getInstance()->GetCateNameByCateId($cateId);
        if ($name) {
            $title = sprintf('%s对比', $name);
        } else {
            $title = '';
        }

        // 处理返回数据
        $data = ['title' => $title, 'items' => $items];
        CUtil::json_response(1, "ok", $data);
    }

    /**
     * 分类列表
     */
    public function actionCateList()
    {
        // 获取主机的 tag_name，获取主机ID（hardcode）
        $tagName = 'main';
        $cate = by::cateModel()->getCateByTagName($tagName);
        $pid = $cate['id'] ?? 0;
        if (empty($pid)) {
            CUtil::json_response(-1, "商品分类不存在");
        }

        // 获取有效分类【分类下有效产品数量大于等于2（有效：产品上线且产品有参数值），才会显示对比】
        $res = GparamGoodsService::getInstance()->GetCateListByPids([$pid], $this->platformId);
        CUtil::json_response(1, "ok", $res);
    }


    /**
     * 格式话对比的返回结果
     * @param $items
     * @param $limit
     * @return int[]
     */
    private function __formatCompareData($items, $limit): array
    {
        $data = [
            'has_more' => 0
        ];
        $count = count($items[0]['params'] ?? []);
        if ($count > $limit) {
            $data['has_more'] = 1;
        }

        foreach ($items as $item) {
            $item['params'] = array_slice($item['params'], 0, $limit);
            $data['items'][] = $item;
        }
        return $data;
    }
}