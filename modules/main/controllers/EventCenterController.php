<?php

namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use Yii;

class EventCenterController extends CommController
{//事件中心

    /**
     * 浏览商品15s
     * @return void
     */
    public function actionViewGoods()
    {
        //商品ID
        $gid = Yii::$app->request->post('gid', 0);
        //用户ID
        $user_id = $this->user_id ?? '';
        //浏览商品类型
        $type = Yii::$app->request->post('type', 'check');

        list($s,$data) = by::model("EventCenterModel", MAIN_MODULE)->viewGoods($user_id,$gid,$type);

        CUtil::json_response(1,"OK",$data);
    }

    /**
     * @return void
     * 观看直播60s
     */
    public function actionWatchLive()
    {
        //用户ID
        $user_id = $this->user_id ?? '';

        list($s,$data) = by::model("EventCenterModel", MAIN_MODULE)->watchLive($user_id);

        CUtil::json_response(1,"OK",$data);
    }

}
