<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2022/1/25
 * Time: 17:54
 */
namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class RetailersController extends CommController{


    /**
     * @OA\Post(
     *     path="/main/retailers/near-list",
     *     summary="附近门店",
     *     description="附近门店",
     *     tags={"附近门店"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/NearRetailersRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/NearRetailersListResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionNearList()
    {
        $post = \Yii::$app->request->post();

        // 获取并确保参数类型正确
        $keyword          = trim($post['keyword'] ?? '');
        $pos_l            = floatval($post['pos_l'] ?? 0);
        $pos_b            = floatval($post['pos_b'] ?? 0);
        $is_open_activity = intval($post['is_open_activity'] ?? 0);
        $province_id      = intval($post['province_id'] ?? 0);
        $city_id          = intval($post['city_id'] ?? 0);
        $district_id      = intval($post['district_id'] ?? 0);

        // 通过坐标获取门店 ID 及其距离信息
        $pos_arr = by::retailers()->getListByPos($pos_l, $pos_b);
        if (empty($pos_arr)) {
            CUtil::json_response(1, 'ok', []);
        }

        // 提取 ID 列表
        $id_arr = array_keys($pos_arr);

        // 获取门店详情
        $data = by::retailers()->getListByIds($id_arr, $is_open_activity,$keyword,$province_id,$city_id,$district_id);

        // 1. 构建 ID => 数据 映射表，确保查找高效
        $data_map = [];
        foreach ($data as $item) {
            $data_map[$item['id']] = $item;
        }

        $result = [];

        // 2. 按照 $pos_arr 的顺序匹配数据
        foreach ($pos_arr as $id => $pos_data) {
            if (!isset($data_map[$id])) {
                continue; // 跳过没有匹配的数据
            }

            // 组合数据，确保顺序与 $pos_arr 一致
            $item             = $data_map[$id];
            $item['distance'] = $pos_data['pos'] ?? 0;

            unset($item['id']); // 可选，去除冗余字段
            $result[] = $item;
        }

        // 3. 返回 JSON 数据，保持 $pos_arr 的顺序
        CUtil::json_response(1, 'ok', $result);
    }

}