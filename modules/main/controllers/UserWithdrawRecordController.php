<?php

namespace app\modules\main\controllers;

use app\models\CUtil;
use app\models\by;
use app\models\byNew;
use app\components\WeiXin;
use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\jobs\EmployeeInviteJob;
use app\jobs\EmployeeOptScoreJob;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\UserSmileJob;
use app\modules\main\models\UserBindModel;
use app\modules\main\services\UserBindService;
use app\modules\main\services\UserWithdrawRecordService;

class UserWithdrawRecordController extends CommController
{
    /**
     * 绑定列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $user_id = $this->user_id;
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        list($status,$res) = UserWithdrawRecordService::getInstance()->getList($user_id,-1,$page,$pageSize);
        if (!$status){
            CUtil::json_response(-1, $res,[]);
        }else{
            CUtil::json_response(1, 'ok', $res);
        }
    }
    
    public function actionApply(){
        $post = \Yii::$app->request->post();
        $user_id = $this->user_id;
        $amount = $post['amount'] ?? 0;
        list($status,$res) = UserWithdrawRecordService::getInstance()->apply($user_id, $amount);
        if (!$status){
            CUtil::json_response(-1, $res,[]);
        }else{
            CUtil::json_response(1, 'ok', []);
        }
    }
}