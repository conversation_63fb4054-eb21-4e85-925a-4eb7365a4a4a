<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/4/30
 * Time: 18:29
 */
namespace app\modules\main\controllers;

use app\components\AdvAscribe;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\ActivityAtmosphereService;
use app\modules\back\services\DreameStoreGoodsService;
use app\modules\common\ControllerTrait;
use app\modules\goods\models\ActivityAtmosphere\ActivityAtmosphereModel;
use app\modules\goods\models\DreameStoreModel;
use app\modules\goods\models\StoreGoodsRelationModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\formatters\Format;
use app\modules\main\formatters\goods\InfoFormatter;
use app\modules\main\formatters\goods\ListFormatter;
use app\modules\main\formatters\goods\PartFormatter;
use app\modules\main\models\SalesCommissionModel;
use app\modules\main\services\GoodsRecommendService;
use app\modules\main\services\GoodsService;
use app\modules\main\services\GoodsStoreService;
use app\modules\wares\models\GoodsStockModel;
use app\modules\wares\services\goods\GoodsMainService;
use Yii;
use yii\db\Exception;


class GoodsStoreController extends CommController {

    use ControllerTrait;


    /** @var GoodsStoreService */
    private $service;
    public function __construct($id, $module, $config = [])
    {
        $this->service = GoodsStoreService::getInstance();
        parent::__construct($id, $module, $config);
    }

    protected $page = 1;
    protected $pageSize = 10;

    protected      $format;
    private static $initializedFormatters = null;

    protected static function getFormatters()
    {
        if (self::$initializedFormatters === null) {
            self::$initializedFormatters = [
                'info' => new InfoFormatter(),
                'store-goods' => new ListFormatter(),
                'goods-list' => new ListFormatter(),
                'part' => new PartFormatter(),
            ];
        }
        return self::$initializedFormatters;
    }

    public function beforeAction($action): bool
    {
        $name = $action->id;

        $formatters = self::getFormatters();
        if (array_key_exists($name, $formatters)) {
            $this->format = new Format($formatters[$name]);
        }

        return parent::beforeAction($action);
    }

    /**
     * 追觅小店可选商品列表
     *   - 展示信息为：品类、商品图片、商品名称、商品原价、商品到手价
     * @throws \yii\db\Exception
     */
    public function actionGoodsList() {
        $post       = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = intval($post['tid']  ?? -1);
        $page_size  = $post['page_size'] ?? 10;
        $name       = $post['name'] ?? '';
        $return     = [];
        $platformIds = $this->platformId;

        // 排序
        $gids = $this->service->GetStoreList($page, $page_size, $this->version,$name, $tid, $platformIds);

        // // 获取不显示的商品ID
        // $hiddenGids = ActivityConfigEnum::getHiddenGoods();
        // // 过滤掉不显示的商品
        // $gids = array_filter($gids, function ($gid) use ($hiddenGids) {
        //     return !in_array($gid, $hiddenGids);
        // });

        // 特定商品增加标签显示
        $typeTagNameMap = [
            0 => '品牌',
            1 => '生态',
            2 => '严选',
        ];

        foreach ($gids as $gid) {
            $aGoodsMain = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);
            //t_gmain中sku
            $sku    = $aGoodsMain['sku'] ?? '';
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoodsMain, $post['sprice_type'] ?? 0);
            list($stock, $sales) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);

            // 获取比例
            $rateInfo = byNew::StoreGoodsModel()->getInfo(['goods_id'=>$gid]);

            $gidSid = "{$gid}_0";
            $aGoods = [
                'gid'                   => $aGoods['gid'],
                'sku'                   => $sku,
                'name'                  => $aGoods['name'],
                'cover_image'           => $aGoods['cover_image'],
                'market_image'          => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'pc_cover_image'        => $aGoods['pc_cover_image'],
                'pc_images'             => $aGoods['pc_images'],
                'mprice'                => $aGoodsMain['mprice'],
                'price'                 => $aGoodsMain['price'], //区分多规格商品 统一取优惠价格
                'is_presale'            => $aGoods['is_presale'] ?? 0,
                'presale_time'          => $aGoods['presale_time'] ?? 0,
                'is_internal'           => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase'  => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'                => $aGoods['is_ini'] ?? 0,
                'gini_id'               => $aGoods['gini_id'] ?? 0,
                'is_trade_in'           => $aGoods['is_trade_in'] ?? 0,
                'tids'                  => $aGoods['tids'] ?? [],
                'custom_tag'            => $aGoods['custom_tag'] ?? '',
                'deposit'               => $aGoods['deposit'] ?? '',
                'expand_price'          => $aGoods['expand_price'] ?? '',
                'atmosphere_img'        => ActivityAtmosphereService::getInstance()->getUrlByGid($aGoods['gid'], ActivityAtmosphereModel::POS['LIST']),
                'coupon_price'          => $aGoodsMain['price'],                                                                                                                                               // 券后价 不计算 给个默认值
                'subsidy_price'         => in_array($this->api, ['a_1664246268', 'i_1666147923']) ? byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount($aGoods['gid'], $aGoodsMain['price']) : "0.00",  // 国补价格
                'goods_recommend_times' => $recommendTimes[$gidSid] ?? 0,                                                                                                                                      // 商品推荐次数
                'stock'                 => $stock,                                                                                                                                                              // 库存
                'sales'                 => $sales,
                'goods_tag_name' => $typeTagNameMap[$aGoods['type']] ?? '', // 商品标签名称
                'type' => $aGoods['type'] ?? 0,
                'introduce'             => $aGoodsMain['introduce'] ?? '',
                'commission_rate'       => $rateInfo['rate'] ?? 0.1,
            ];
            $return['list'][] = $aGoods;
        }

        $count                 = $this->service->GetStoreListCount($this->version,$name, $tid, $platformIds);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);
        $return['total']       = $count;

        // 格式化返回数据，此方法不查库，只做数据格式化
        // $return = $this->format->format($return, $this->platformId);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 追觅小店可选商品列表
     *   - 展示信息为：品类、商品图片、商品名称、商品原价、商品到手价
     * @throws \yii\db\Exception
     */
    public function actionGoodsListOld() {
        $post       = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = intval($post['tid']  ?? -1);
        $page_size  = $post['page_size'] ?? 10;
        $status     = 0;
        $return     = [];
        $type       = $post['type'] ?? 999;
        $detailData = [
            'platformIds'=>[$this->platformId], // 平台
            'is_trade_in'=>$post['is_trade_in'] ?? 0, // 以旧换新
            'is_internal_purchase'=>$post['is_internal_purchase'] ?? 0, // 内购商城
        ];

        // 排序
        $sort = ['sort_field' => $post['sort_field'] ?? 'sort', 'sort_type' => $post['sort_type'] ?? 'DESC'];
        $gids = $this->service->GetStoreList($page, $page_size, $this->version, $type, $status, '', '', $tid, $detailData, $sort);

        // 获取不显示的商品ID
        $hiddenGids = ActivityConfigEnum::getHiddenGoods();
        // 过滤掉不显示的商品
        $gids = array_filter($gids, function ($gid) use ($hiddenGids) {
            return !in_array($gid, $hiddenGids);
        });

        // 获取当前商品心愿单状态
        $wishRet = GoodsService::getInstance()->getGoodsIsWish($this->user_id,$gids);


        // 收集所有gid_sid用于批量获取推荐次数
        $gidSids = [];
        foreach ($gids as $gid) {
            $gidSids[] = "{$gid}_0"; // 默认sid为0
        }

        // 批量获取推荐次数
        $recommendTimes = [];
        try {
            $goodsRecommendService = GoodsRecommendService::getInstance();
            $recommendTimes = $goodsRecommendService->getGoodsRecommend($gidSids);
        } catch (\Exception $e) {
            // 获取失败时，推荐次数默认为0
        }

        // 特定商品增加标签显示
        $typeTagNameMap = [
            0 => '品牌',
            1 => '生态',
            2 => '严选',
        ];

        // 获取当前用户积分+等级
        $userId = $this->user_id && strlen($this->user_id) < 11 ? intval($this->user_id) : 0;
        $totalCoin = by::point()->get($userId); // 用户总积分
        // 用户等级
        $pointRate = by::memberCenterModel()->getPointCrash($userId);
        // 商城后台配置抵扣系数
        $deductionRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;

        foreach ($gids as $gid) {
            $aGoodsMain = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);
            //t_gmain中sku
            $sku    = $aGoodsMain['sku'] ?? '';
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoodsMain, $post['sprice_type'] ?? 0);

            // 计算 当前商品可以抵用 积分
            // 根据当前商品金额+等级 计算最多可用多少积分、 在比较当前用户剩余积分 谁少取谁
            $canUseCoin = by::Ouser()->__canCoinByBenefit($totalCoin, $aGoods['price'], $pointRate, $userId);
            list($stock, $sales) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);

            $gidSid = "{$gid}_0";
            $aGoods = [
                'gid'                   => $aGoods['gid'],
                'sku'                   => $sku,
                'name'                  => $aGoods['name'],
                'cover_image'           => $aGoods['cover_image'],
                'market_image'          => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'pc_cover_image'        => $aGoods['pc_cover_image'],
                'pc_images'             => $aGoods['pc_images'],
                'mprice'                => $aGoodsMain['mprice'],
                'price'                 => $aGoodsMain['price'], //区分多规格商品 统一取优惠价格
                'is_presale'            => $aGoods['is_presale'] ?? 0,
                'presale_time'          => $aGoods['presale_time'] ?? 0,
                'is_internal'           => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase'  => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'                => $aGoods['is_ini'] ?? 0,
                'gini_id'               => $aGoods['gini_id'] ?? 0,
                'is_trade_in'           => $aGoods['is_trade_in'] ?? 0,
                'tids'                  => $aGoods['tids'] ?? [],
                'custom_tag'            => $aGoods['custom_tag'] ?? '',
                'deposit'               => $aGoods['deposit'] ?? '',
                'expand_price'          => $aGoods['expand_price'] ?? '',
                'atmosphere_img'        => ActivityAtmosphereService::getInstance()->getUrlByGid($aGoods['gid'], ActivityAtmosphereModel::POS['LIST']),
                'coupon_price'          => $aGoodsMain['price'],                                                                                                                                               // 券后价 不计算 给个默认值
                'subsidy_price'         => in_array($this->api, ['a_1664246268', 'i_1666147923']) ? byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount($aGoods['gid'], $aGoodsMain['price']) : "0.00",  // 国补价格
                'goods_recommend_times' => $recommendTimes[$gidSid] ?? 0,                                                                                                                                      // 商品推荐次数
                'is_wish'               => $wishRet[$aGoods['gid']],                                                                                                                                           // 是否在心愿单
                'can_use_coin'          => $canUseCoin,                                                                                                                                                        // 当前商品可以抵用积分
                'deduction_rate'        => $deductionRate,
                'stock'                 => $stock,                                                                                                                                                              // 库存
                'sales'                 => $sales,
                'goods_tag_name' => $typeTagNameMap[$aGoods['type']] ?? '', // 商品标签名称
                'type' => $aGoods['type'] ?? 0,
                'introduce'             => $aGoodsMain['introduce'] ?? '',
            ];
            $return['list'][] = $aGoods;
        }

        $count                 = by::Gmain()->GetListCount($this->version, $type, $status,'','',$tid,$detailData);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);
        $return['total']       = $count;

        //推送腾讯
        if($this->union && stristr($this->union,'tencent')){
            AdvAscribe::factory()->push($this->user_id,'tencent',['user_id'=>$this->user_id,'url'=>$this->referer,'event'=>'VIEW_CONTENT','click_id'=>$this->clickId,'extra'=>['object'=>'PRODUCT']]);
        }

        // 格式化返回数据，此方法不查库，只做数据格式化
        $return = $this->format->format($return, $this->platformId);

        CUtil::json_response(1, 'ok', $return);
    }
    public function actionStoreGoods(){
        $userId = $this->user_id;
        $post       = Yii::$app->request->post();
        $store_id = $post['store_id'] ?? 0;
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = intval($post['tid']  ?? -1);
        $page_size  = $post['page_size'] ?? 10;
        $name       = $post['name'] ?? '';
        $return     = [];
        $platformId = $this->platformId;
        $status = $post['status'] ?? -1;
        $orderType = $post['order_type'] ?? 1;
        //别人查看店铺 不显示 已下架商品
        if ($userId != $store_id) {
            if ($status == 1) {
                $return = ['pages' => 1, 'list' => [], 'total' => 0];
                CUtil::json_response(1, 'ok', $return);
            }
            if ($status == -1) {$status = 0;}
        }
        $gids = $this->service->GetStoreGoods($store_id,$page, $page_size, $this->version,$name, $tid, $platformId,$status,$orderType);
        
        // 特定商品增加标签显示
        $typeTagNameMap = [
            0 => '品牌',
            1 => '生态',
            2 => '严选',
        ];
        foreach ($gids as $gid) {
            $aGoodsMain = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);
            //t_gmain中sku
            $sku    = $aGoodsMain['sku'] ?? '';
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoodsMain, $post['sprice_type'] ?? 0);
            list($stock, $sales) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);
            // 获取比例
            $rateInfo = byNew::StoreGoodsModel()->getInfo(['goods_id'=>$gid]);

            $gidSid = "{$gid}_0";
            $aGoods = [
                'gid'                   => $aGoods['gid'],
                'sku'                   => $sku,
                'name'                  => $aGoods['name'],
                'cover_image'           => $aGoods['cover_image'],
                'market_image'          => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'pc_cover_image'        => $aGoods['pc_cover_image'],
                'pc_images'             => $aGoods['pc_images'],
                'mprice'                => $aGoodsMain['mprice'],
                'price'                 => $aGoodsMain['price'], //区分多规格商品 统一取优惠价格
                'is_presale'            => $aGoods['is_presale'] ?? 0,
                'presale_time'          => $aGoods['presale_time'] ?? 0,
                'is_internal'           => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase'  => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'                => $aGoods['is_ini'] ?? 0,
                'gini_id'               => $aGoods['gini_id'] ?? 0,
                'is_trade_in'           => $aGoods['is_trade_in'] ?? 0,
                'tids'                  => $aGoods['tids'] ?? [],
                'custom_tag'            => $aGoods['custom_tag'] ?? '',
                'deposit'               => $aGoods['deposit'] ?? '',
                'expand_price'          => $aGoods['expand_price'] ?? '',
                'atmosphere_img'        => ActivityAtmosphereService::getInstance()->getUrlByGid($aGoods['gid'], ActivityAtmosphereModel::POS['LIST']),
                'coupon_price'          => $aGoodsMain['price'],                                                                                                                                               // 券后价 不计算 给个默认值
                'subsidy_price'         => in_array($this->api, ['a_1664246268', 'i_1666147923']) ? byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount($aGoods['gid'], $aGoodsMain['price']) : "0.00",  // 国补价格
                'goods_recommend_times' => $recommendTimes[$gidSid] ?? 0,                                                                                                                                      // 商品推荐次数
                'stock'                 => $stock,                                                                                                                                                              // 库存
                'sales'                 => $sales,
                'goods_tag_name' => $typeTagNameMap[$aGoods['type']] ?? '', // 商品标签名称
                'type' => $aGoods['type'] ?? 0,
                'introduce'             => $aGoodsMain['introduce'] ?? '',
                'commission_rate'       => $rateInfo['rate'] ?? 0.1,
            ];
            $return['list'][] = $aGoods;
        }
        $count                 = $this->service->GetStoreGoodsCount($store_id, $this->version,$name, $tid, $platformId,$status);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);
        $return['total']       = $count;

        // 格式化返回数据，此方法不查库，只做数据格式化
        // $return = $this->format->format($return, $this->platformId);

        CUtil::json_response(1, 'ok', $return);

    }

    /**
     * 追觅小店已选商品列表
     * @return void
     * @throws Exception
     * @throws \Throwable
     */
    public function actionStoreGoodsOld() {
        $userId = $this->user_id;
        $post       = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $page       = $post['page'] ?? 1;
        $tid        = intval($post['tid']  ?? -1);
        $page_size  = $post['page_size'] ?? 10;
        $status     = 0;
        $return     = [];
        $type       = $post['type'] ?? 999;
        $detailData = [
            'platformIds'=>[$this->platformId], // 平台
        ];

        // 排序
        $sort = ['sort_field' => $post['sort_field'] ?? 'sort', 'sort_type' => $post['sort_type'] ?? 'DESC'];
        $gids = $this->service->GetStoreGoods($userId,$page, $page_size, $this->version, $type, $status, '', '', $tid, $detailData, $sort);

        // 获取不显示的商品ID
        $hiddenGids = ActivityConfigEnum::getHiddenGoods();
        // 过滤掉不显示的商品
        $gids = array_filter($gids, function ($gid) use ($hiddenGids) {
            return !in_array($gid, $hiddenGids);
        });

        // 获取当前商品心愿单状态
        $wishRet = GoodsService::getInstance()->getGoodsIsWish($this->user_id,$gids);


        // 收集所有gid_sid用于批量获取推荐次数
        $gidSids = [];
        foreach ($gids as $gid) {
            $gidSids[] = "{$gid}_0"; // 默认sid为0
        }

        // 批量获取推荐次数
        $recommendTimes = [];
        try {
            $goodsRecommendService = GoodsRecommendService::getInstance();
            $recommendTimes = $goodsRecommendService->getGoodsRecommend($gidSids);
        } catch (\Exception $e) {
            // 获取失败时，推荐次数默认为0
        }

        // 特定商品增加标签显示
        $typeTagNameMap = [
            0 => '品牌',
            1 => '生态',
            2 => '严选',
        ];

        // 获取当前用户积分+等级
        $userId = $this->user_id && strlen($this->user_id) < 11 ? intval($this->user_id) : 0;
        $totalCoin = by::point()->get($userId); // 用户总积分
        // 用户等级
        $pointRate = by::memberCenterModel()->getPointCrash($userId);
        // 商城后台配置抵扣系数
        $deductionRate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;

        foreach ($gids as $gid) {
            $aGoodsMain = by::Gmain()->GetAllOneByGid($gid, true, true, $post['sprice_type'] ?? 0, false);
            //t_gmain中sku
            $sku    = $aGoodsMain['sku'] ?? '';
            $aGoods = by::Gmain()->GetSpecsPriceInfo($aGoodsMain, $post['sprice_type'] ?? 0);

            // 计算 当前商品可以抵用 积分
            // 根据当前商品金额+等级 计算最多可用多少积分、 在比较当前用户剩余积分 谁少取谁
            $canUseCoin = by::Ouser()->__canCoinByBenefit($totalCoin, $aGoods['price'], $pointRate, $userId);
            list($stock, $sales) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);

            $gidSid = "{$gid}_0";
            $aGoods = [
                'gid'                   => $aGoods['gid'],
                'sku'                   => $sku,
                'name'                  => $aGoods['name'],
                'cover_image'           => $aGoods['cover_image'],
                'market_image'          => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                'pc_cover_image'        => $aGoods['pc_cover_image'],
                'pc_images'             => $aGoods['pc_images'],
                'mprice'                => $aGoodsMain['mprice'],
                'price'                 => $aGoodsMain['price'], //区分多规格商品 统一取优惠价格
                'is_presale'            => $aGoods['is_presale'] ?? 0,
                'presale_time'          => $aGoods['presale_time'] ?? 0,
                'is_internal'           => $aGoods['is_internal'] ?? 0,
                'is_internal_purchase'  => $aGoods['is_internal_purchase'] ?? 0,
                'is_ini'                => $aGoods['is_ini'] ?? 0,
                'gini_id'               => $aGoods['gini_id'] ?? 0,
                'is_trade_in'           => $aGoods['is_trade_in'] ?? 0,
                'tids'                  => $aGoods['tids'] ?? [],
                'custom_tag'            => $aGoods['custom_tag'] ?? '',
                'deposit'               => $aGoods['deposit'] ?? '',
                'expand_price'          => $aGoods['expand_price'] ?? '',
                'atmosphere_img'        => ActivityAtmosphereService::getInstance()->getUrlByGid($aGoods['gid'], ActivityAtmosphereModel::POS['LIST']),
                'coupon_price'          => $aGoodsMain['price'],                                                                                                                                               // 券后价 不计算 给个默认值
                'subsidy_price'         => in_array($this->api, ['a_1664246268', 'i_1666147923']) ? byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount($aGoods['gid'], $aGoodsMain['price']) : "0.00",  // 国补价格
                'goods_recommend_times' => $recommendTimes[$gidSid] ?? 0,                                                                                                                                      // 商品推荐次数
                'is_wish'               => $wishRet[$aGoods['gid']],                                                                                                                                           // 是否在心愿单
                'can_use_coin'          => $canUseCoin,                                                                                                                                                        // 当前商品可以抵用积分
                'deduction_rate'        => $deductionRate,
                'stock'                 => $stock,                                                                                                                                                              // 库存
                'sales'                 => $sales,
                'goods_tag_name' => $typeTagNameMap[$aGoods['type']] ?? '', // 商品标签名称
                'type' => $aGoods['type'] ?? 0,
                'introduce'             => $aGoodsMain['introduce'] ?? '',
            ];
            $return['list'][] = $aGoods;
        }



        $count                 = by::Gmain()->GetListCount($this->version, $type, $status,'','',$tid,$detailData);
        $return['pages']       = CUtil::getPaginationPages($count, $page_size);
        $return['total']       = $count;



        // 格式化返回数据，此方法不查库，只做数据格式化
        $return = $this->format->format($return, $this->platformId);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 批量添加商品到追觅小店
     */
    public function actionStoreCreate()
    {
        $request = Yii::$app->request;
        $user_id = $this->user_id;
        $goods_ids = $request->post('goods_ids', '');
        $goods_ids = explode(',', $goods_ids);

        // 验证商品ID数组
        if (!is_array($goods_ids) || empty($goods_ids)) {
            CUtil::json_response(-1,'商品ID参数格式不正确或为空');
        }

        // 去重处理
        $goods_ids = array_unique($goods_ids);

        // 获取已存在的商品，避免重复添加
        $model = new StoreGoodsRelationModel();
        $existGoodList = $model->find()->where(['user_id' => $user_id, 'goods_id' => $goods_ids])->asArray()->all();
        $existingGoods = [];
        $downGoodsIds = [];
        if ($existGoodList) {
            $existingGoods = array_column($existGoodList, 'goods_id');
            $downGoodsIds = array_reduce($existGoodList, function ($result, $item) {
                if($item['status'] == 1) $result[] = $item['goods_id'];
                return $result;
            }, []);
        }
        //$existingGoods = $model->getExistingGoods($user_id);

        // 取并集
        //$union = array_unique(array_merge($goods_ids, $existingGoods));

        // 判断是否超出店铺最大商品数量限制
//        $store = byNew::DreameStoreModel()->getInfo(['user_id' => $user_id]);
//        $max = $store['max'] ?? 10;
//
//        if (count($goods_ids) > $max) {
//            CUtil::json_response(-1,'当前店铺最大商品数量' . $max);
//        }

        list($addIds,$deleteIds) = $this->arrayDiff($goods_ids,$existingGoods);


        
        // 准备批量插入数据
        $rows = [];
        $now = time();
        foreach ($addIds as $goods_id) {
            $rows[] = [
                'user_id' => $user_id,
                'goods_id' => $goods_id,
                'ctime' => $now,
                'utime' => $now,
            ];
        }
        // 直接调用模型方法处理业务逻辑
        if (count($rows)){
            $status = $model->batchAdd($rows);
        }

        //已经存在店铺商品列表，但是店铺商品下架状态的店铺商品,设置为上架
        if ($downGoodsIds) {
            $model->updateInfo(['user_id' => $user_id,'goods_id'=>$downGoodsIds],['status' => 0, 'ctime' => $now]);
        }

//        if (count($deleteIds) > 0){
//            $status1 = $model->deleteData($deleteIds,$user_id);
//        }
        CUtil::json_response(1,'添加成功');
    }

    public function actionDeleteGoods()
    {
        $request = Yii::$app->request;
        $goods_id = $request->post('goods_id', 0);
        $user_id = $this->user_id;
        if (empty($goods_id) || empty($user_id)) {
            CUtil::json_response(-1,'参数缺失');
        }
        $model = new StoreGoodsRelationModel();
        $status = $model->deleteData([$goods_id],$user_id);
        if ($status) {
            CUtil::json_response(1,'删除成功');
        } else {
            CUtil::json_response(-1,'删除失败');
        }
    }

    /**
     * 开店首次选品时 显示推广信息
     */
    public function actionConfig()
    {
        $start = strtotime('first day of this month midnight');
        $activity_id = DreameStoreModel::instance()->getActivityId(); //追觅小店活动ID
        $ret = [
            'store_num' => DreameStoreModel::find()->where(['>=','ctime',$start])->count(),   //本月商家量
            //本月商家的订单量
            'order_num' => SalesCommissionModel::find()->where([
                'activity_id' => $activity_id,
            ])->innerJoin(DreameStoreModel::tbName() . 'as s',
                's.user_id=referrer and s.ctime>:ctime', [':ctime' => $start])->count(),
        ];
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * 店铺商品可筛选标签列表
     */
    public function actionTagList()
    {
        $post = Yii::$app->request->post();
        $store_id = $post['store_id'] ?? 0;
        $platformId = $this->platformId;

        // 1=普通商品标签 ，2=优选商品标签，3=严选商品标签
        $type = 1;
        $list = by::Gtag()->getTagList($type);
        $list = array_filter($list, function ($li)  {
            return $li['tid'] < 3 ? true : false;
        });

        foreach ($list as $key => $li) {
            if ($li['tid'] < 0) {
                continue;
            }
            if ($li['tid']) {
                // 不显示没有商品的标签
                $count  = $this->service->GetStoreGoodsCount($store_id, $this->version,'', $li['tid'], $platformId,-1);
                if (empty($count)) {
                    unset($list[$key]);
                }
            }
        }

        $tagImageMap = by::Gtag()->GetTagImageMap();
        foreach ($list as $key => $item) {
            $list[$key]['tag_img'] = $tagImageMap[$item['tid']] ??'';
        }
        $list = array_values($list);
        CUtil::json_response(1, 'ok', $list);
    }

        /**
     * 用户店铺商品上下架
     */
    public function actionSetGoodsStatus()
    {
        $request = Yii::$app->request;
        $goods_ids = $request->post('goods_ids', '');
        $user_id = $this->user_id;
        if (empty($goods_ids) || empty($user_id)) {
            CUtil::json_response(-1,'参数缺失');
        }
        $status = $request->post('status', '');
        if (!is_numeric($status) || !in_array($status, [0,1])) {
            CUtil::json_response(-1,'状态参数错误');
        }
        $goods_ids = explode(',', $goods_ids);
        $model = new StoreGoodsRelationModel();
        $up = ['status' => $status];
        //上架时更新上架时间
        if (0 == $status) {
            $up['ctime'] = time();
        }
        $res = $model->updateInfo(['user_id' => $user_id, 'goods_id' => $goods_ids], $up);
        if ($res) {
            CUtil::json_response(1,'操作成功');
        } else {
            CUtil::json_response(-1,'操作失败');
        }
    }

    public function arrayDiff(array $array1, array $array2): array {
        // 在 $array1 但不在 $array2 的元素
        $onlyInArray1 = array_diff($array1, $array2);
        // 在 $array2 但不在 $array1 的元素
        $onlyInArray2 = array_diff($array2, $array1);
        
        return [
            array_values($onlyInArray1), // 重新索引
            array_values($onlyInArray2)  // 重新索引
        ];
    }
}
