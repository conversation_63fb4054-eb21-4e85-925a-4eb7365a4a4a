<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/14
 * Time: 11:27
 */

namespace app\modules\main\controllers;

use app\components\Partner;
use app\models\by;
use app\models\CUtil;
use Yii;

class PartnerController extends CommController
{


    /**
     * @OA\Post(
     *     path="/main/partner/user",
     *     summary="合作商鉴权",
     *     description="获取合作商鉴权参数",
     *     tags={"合作商"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="client_id", type="string", required={"client_id"},description="平台标识ID，熊洞：10001"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionUser()
    {
        $client_id = Yii::$app->request->post('client_id', '');
        if (empty($client_id)) {
            CUtil::json_response(-1, '平台标识ID为空');
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__, $client_id);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候~');
        }
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', []);
        }

        list($s, $data) = Partner::factory()->run('user', ['user_id' => $this->user_id, 'client_id' => $client_id]);
        if (!$s) {
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, 'OK', $data);
    }
}
