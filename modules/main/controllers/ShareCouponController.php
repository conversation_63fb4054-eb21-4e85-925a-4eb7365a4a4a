<?php

namespace app\modules\main\controllers;


use app\models\CUtil;
use app\modules\main\enums\shareCoupon\ShareCouponEnum;
use app\modules\main\services\ShareCouponService;


class ShareCouponController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/share-coupon/share-list",
     *     summary="分享券分享优惠券列表",
     *     description="分享券分享优惠券列表",
     *     tags={"分享劵"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                        required = {"type"},
     *                        @OA\Property(property="type", type="string", default="master", description="master系列机型")
     *                    )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionShareList()
    {
        $post = \Yii::$app->request->post();

        $type = $post['type'] ?? '';
        if (empty($type)) CUtil::json_response(-1, "活动授权机型不能为空！");

        $config = ShareCouponEnum::SHARE_COUPON_CONFIG[$type] ?? '';

        if (empty($config)) CUtil::json_response(-1, "活动配置属性不存在！");

        list($s, $list) = ShareCouponService::getInstance()->ShareList($this->user_id, $config);
        if (!$s) CUtil::json_response(-1, $list);
        $rule_note = $list['rule_note'] ?? '';
        $list['ac_rules'] = empty($rule_note) ? file_get_contents(__DIR__ . '/../enums/shareCoupon/share20231206.txt') : $rule_note;
        $list['use_rules'] = file_get_contents(__DIR__ . '/../enums/shareCoupon/coupon20231206.txt');

        CUtil::json_response(1, 'OK', $list);
    }

    /**
     * @OA\Post(
     *     path="/main/share-coupon/share-coupon-code",
     *     summary="分享券分享优惠券列表",
     *     description="分享券分享优惠券列表",
     *     tags={"分享劵"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                        required = {"type"},
     *                        @OA\Property(property="type", type="string", default="master", description="master系列机型"),
     *                        @OA\Property(property="remark", type="string", default="master", description="备注，用户分享券的备注")
     *                    )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionShareCouponCode()
    {
        $post = \Yii::$app->request->post();

        $type = $post['type'] ?? '';
        if (empty($type)) CUtil::json_response(-1, "活动授权机型不能为空！");

        $remark = $post['remark']?? '';
        if(strlen($remark) > 500) CUtil::json_response(-1, "备注不能超过500个字符！");

        $config = ShareCouponEnum::SHARE_COUPON_CONFIG[$type] ?? '';

        if (empty($config)) CUtil::json_response(-1, "活动配置属性不存在！");

        $remark = $post['remark']?? '';

        list($s, $list) = ShareCouponService::getInstance()->GetShareCouponCode($this->user_id, $config, $remark);
        if (!$s) CUtil::json_response(-1, $list);

        CUtil::json_response(1, 'OK', $list);
    }



    /**
     * @OA\Post(
     *     path="/main/share-coupon/draw-list",
     *     summary="分享券领取优惠券列表",
     *     description="分享券领取优惠券列表",
     *     tags={"分享劵"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                        required = {"share_coupon_code"},
     *                        @OA\Property(property="share_coupon_code", type="string", default="", description="share_coupon_code 分享券CODE ")
     *                    )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionDrawList()
    {
        $post = \Yii::$app->request->post();

        $share_code = $post['share_coupon_code'] ?? '';
        if (empty($share_code)) CUtil::json_response(-1, "分享券CODE不能为空！");

        list($s, $list) = ShareCouponService::getInstance()->DrawList($this->user_id, $share_code);
        if (!$s) CUtil::json_response(-1, $list);
        $list['ac_rules'] = file_get_contents(__DIR__ . '/../enums/shareCoupon/share20231206.txt');
        $list['use_rules'] = file_get_contents(__DIR__ . '/../enums/shareCoupon/coupon20231206.txt');

        CUtil::json_response(1, 'OK', $list);
    }

    /**
     * @OA\Post(
     *     path="/main/share-coupon/draw",
     *     summary="分享券领取",
     *     description="分享券领取",
     *     tags={"分享劵"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                        required = {"share_coupon_code"},
     *                        @OA\Property(property="share_coupon_code", type="string", default="", description="share_coupon_code 分享券CODE ")
     *                    )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionDraw()
    {
        $post = \Yii::$app->request->post();

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }

        $share_code = $post['share_coupon_code'] ?? '';
        if (empty($share_code)) CUtil::json_response(-1, "分享券CODE不能为空！");


        list($s, $list) = ShareCouponService::getInstance()->Draw($this->user_id, $share_code);
        if (!$s) CUtil::json_response(-1, $list);

        CUtil::json_response(1, 'OK');
    }
}
