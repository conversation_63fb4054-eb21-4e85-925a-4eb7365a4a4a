<?php

namespace app\modules\main\controllers;


use app\constants\RespStatusCodeConst;
use app\models\CUtil;
use app\modules\main\services\GroupPurchaseService;
use app\modules\main\services\PopupService;
use Yii;

/**
 * 商城弹窗
 */
class PopupController extends CommController
{


    /**
     * @OA\Post(
     *     path="/main/popup/get-popup",
     *     summary="获取弹窗",
     *     description="获取弹窗",
     *     tags={"弹窗"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(ref="#/components/schemas/BaseRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据（value：1展示发起拼团 value：0我要当团长、begin_time：活动开始时间、end_time：活动结束时间）"
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */
    public function actionGetPopup()
    {
        $userId = $this->user_id;



        // 提前返回，避免嵌套，且验证 userId 长度
        if (!$userId || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', []);
        }

        // 获取弹窗数据
        $popupData = PopupService::getInstance()->getPopupData($userId);



        // 返回结果
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', $popupData);
    }
}