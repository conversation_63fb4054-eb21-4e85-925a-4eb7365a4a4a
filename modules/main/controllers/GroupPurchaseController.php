<?php

namespace app\modules\main\controllers;

use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\forms\groupPurchase\GroupPurchaseListForm;
use app\modules\main\models\UserPopupModel;
use app\modules\main\services\GroupPurchaseService;

class GroupPurchaseController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/group-purchase/verify-qualification",
     *     summary="拼团活动-校验资格",
     *     description="拼团活动-校验资格",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(ref="#/components/schemas/BaseRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="恭喜您成为团长！", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据"
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */

    public function actionVerifyQualification()
    {
        $userId = $this->user_id;

        // 提前返回，避免嵌套
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请您先授权登录！');
        }

        // 业务逻辑
        $groupPurchaseService = GroupPurchaseService::getInstance();
        list($status, $data) = $groupPurchaseService->verifyQualification($userId);

        // 根据验证结果返回响应
        $responseCode = $status ? RespStatusCodeConst::SUCCESS_CODE : RespStatusCodeConst::AUTH_ERROR_CODE;
        CUtil::json_response($responseCode, $data);
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/get-popup",
     *     summary="拼团活动-获取按钮状态",
     *     description="拼团活动-获取按钮状态",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(ref="#/components/schemas/BaseRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据（value：1展示发起拼团 value：0我要当团长、begin_time：活动开始时间、end_time：活动结束时间）"
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */

    public function actionGetPopup()
    {
        $userId = $this->user_id;

        // 提前定义时间，避免重复代码
        $beginTime = YII_ENV_PROD ? 1729785600 : 1729440000;
        $endTime   = 1731340800;

        // 提前返回，避免嵌套，且验证 userId 长度
        if (!$userId || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', [
                    'value'      => 0,
                    'begin_time' => $beginTime,
                    'end_time'   => $endTime,
            ]);
        }

        // 获取弹窗数据
        $popupData = GroupPurchaseService::getInstance()->getPopupData($userId);

        // 添加弹窗时间数据
        $popupData['begin_time'] = $beginTime;
        $popupData['end_time']   = $endTime;

        // 返回结果
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', $popupData);
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/popup",
     *     summary="拼团活动-弹窗记录",
     *     description="/main/group-purchase/verify-qualification调用成功后，调用此接口保存弹窗记录",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(ref="#/components/schemas/BaseRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据"
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */

    public function actionPopup()
    {
        $userId = $this->user_id;

        // 提前返回，避免嵌套，且验证 userId 长度
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请您先授权登录！');
        }

        // 调用服务层更新弹窗数据
        $groupPurchaseService = GroupPurchaseService::getInstance();
        $groupPurchaseService->savePopup($userId, UserPopupModel::POPUP_TYPE['GROUP_PURCHASES']);

        // 返回结果
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/initiate-group-purchase",
     *     summary="发起拼团",
     *     description="通过活动ID和商品ID发起拼团，获取拼团商品的详细信息。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"activity_id","gid"},
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *              oneOf={
     *                  @OA\Schema(
     *                      type="object",
     *                      required={"iRet", "sMsg", "data"},
     *                      @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                      @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *                      @OA\Property(
     *                          property="data",
     *                          type="object",
     *                          description="成功时返回的数据"
     *                      )
     *                  ),
     *                  @OA\Schema(
     *                      type="object",
     *                      required={"iRet", "sMsg", "data"},
     *                      @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                      @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                      @OA\Property(
     *                          property="data",
     *                          type="object",
     *                          description="失败时返回的数据"
     *                      )
     *                  )
     *              })
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="cookie",
     *         required=false,
     *         description="用户ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionInitiateGroupPurchase()
    {
        // 提取 userId 和 POST 参数
        $userId = $this->user_id;
        $params = \Yii::$app->request->post();

        // 1. 并发限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($userId, $unique_key, 2, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 2. 提前返回，避免嵌套，且验证 userId 长度
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请您先授权登录！');
        }

        // 3. 发起拼团逻辑
        $groupPurchaseService = GroupPurchaseService::getInstance();
        list($success, $message) = $groupPurchaseService->initiateGroupPurchase($params, $userId);

        // 4. 返回处理结果
        if ($success) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '拼团发起成功！', ['id' => $message['id'] ?? 0]);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $message['message'] ?? '拼团发起失败！', ['code' => $message['code'] ?? 120007]);
        }
    }


        /**
         * @OA\Post(
         *     path="/main/group-purchase/goods-list",
         *     summary="获取团购商品列表",
         *     description="根据活动ID获取当前团购活动中的商品列表信息，支持按商品名称筛选。如果不传activity_id，会自动获取当前进行中的活动",
         *     tags={"拼团活动"},
         *     @OA\RequestBody(
         *         required=true,
         *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
         *             @OA\Schema(
         *                 @OA\Property(property="activity_id", type="string", description="活动ID，可选参数，不传时自动获取当前活动"),
         *                 @OA\Property(property="goods_name", type="string", description="商品名称，可选参数，用于筛选商品"),
         *                 @OA\Property(property="tag_id", type="integer", description="标签ID，可选参数，0表示全部，默认为0")
         *             )
         *         )
         *     ),
         *     @OA\Response(
         *         response=200,
         *         description="成功",
         *         @OA\JsonContent(
         *             @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
         *             @OA\Property(property="sMsg", type="string", example="success", description="提示消息"),
         *             @OA\Property(
         *                 property="data",
         *                 type="object",
         *                 description="返回数据",
         *                 @OA\Property(property="title", type="string", description="活动名称"),
         *                 @OA\Property(
         *                     property="goods",
         *                     type="array",
         *                     description="商品列表数据",
         *                     @OA\Items(
         *                         type="object",
         *                         @OA\Property(property="id", type="integer", description="商品关联ID"),
         *                         @OA\Property(property="gid", type="integer", description="商品ID"),
         *                         @OA\Property(property="activity_id", type="integer", description="活动ID"),
         *                         @OA\Property(property="name", type="string", description="商品名称"),
         *                         @OA\Property(property="price", type="number", format="float", description="商品价格，保留2位小数"),
         *                         @OA\Property(property="mprice", type="number", format="float", description="商品市场价，保留2位小数"),
         *                         @OA\Property(property="status", type="integer", description="商品状态 0:正常 1:下架"),
         *                         @OA\Property(property="cover_image", type="string", description="商品封面图片URL"),
         *                         @OA\Property(property="atype", type="integer", description="商品属性（0：统一规格；1：多规格；2：自定义）"),
         *                         @OA\Property(property="button_status", type="integer", description="按钮状态 0：抢 1：已抢光 2：邀请好友 3：已下架 4：团员已满 5：拼团已结束")
         *                     )
         *                 )
         *             )
         *         )
         *     ),
         *     @OA\Response(
         *         response="default",
         *         description="失败",
         *         @OA\JsonContent(
         *             @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
         *             @OA\Property(property="sMsg", type="string", example="活动不存在", description="错误提示消息")
         *         )
         *     ),
         *     @OA\Parameter(
         *         name="sessid",
         *         in="cookie",
         *         required=false,
         *         description="会话ID，用于身份验证",
         *         @OA\Schema(type="string")
         *     )
    * )
    */
    public function actionGoodsList()
    {
        try {
            // 1. 参数验证
            $form = CUtil::VdForm(new GroupPurchaseListForm());
            if (empty($form->activity_id)) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '活动ID不能为空');
            }

            // 2. 获取团购商品列表
            $service = GroupPurchaseService::getInstance();
            $result  = $service->getGroupPurchaseList(
                    $this->user_id,
                    $form->activity_id,
                $form->goods_name,
                $form->tag_id
            );

            // 3. 数据为空处理
            if (empty($result) || empty($result['goods'])) {
                 CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '暂无团购商品', ['goods' => [], 'total' => 0]);
            }

            $goodsList       = $this->groupByKey($result['goods'], 'group_key');
            $result['goods'] = array_values($goodsList);

            // 4. 返回成功结果
             CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $result);

        } catch (\Exception $e) {
            CUtil::debug( '获取团购商品列表失败: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'group_purchase_goods_list.error');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '获取商品列表失败');
        }
    }

    public function groupByKey($array, $key): array
    {
        $result = [];
        foreach ($array as $item) {
            if (!is_array($item) || !isset($item[$key])) {
                continue;
            }
            $result[$item[$key]][] = $item;
        }
        return $result;
    }
    /**
     * @OA\Post(
     *     path="/main/group-purchase/goods-detail",
     *     summary="团购商品详情",
     *     description="验证团购商品是否可参团，返回团购商品ID。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"group_purchase_id"},
     *                         @OA\Property(property="group_purchase_id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1表示可参团，-1表示错误"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(property="gid", type="integer", description="团购商品ID")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="cookie",
     *         required=false,
     *         description="用户ID，用于识别用户",
     *         @OA\Schema(type="string")
     *     )
     * )
     */


    public function actionGoodsDetail()
    {
        // 1. 参数验证
        $form = CUtil::VdForm(new GroupPurchaseListForm(), 'grouped');
        // 2. 获取团购商品详情
        $result = GroupPurchaseService::getInstance()->checkGoodsDetail($form->group_purchase_id, $this->user_id);
        // 3. 返回处理结果
        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/group-members",
     *     summary="获取拼团成员详情",
     *     description="通过活动ID获取拼团成员的详细信息，包括团长、成员头像、拼团累积金额等。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"group_purchase_id"},
     *                         @OA\Property(property="group_purchase_id", type="string", description="活动ID"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功获取拼团成员详情",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回的数据",
     *                 @OA\Property(
     *                     property="members",
     *                     type="array",
     *                     description="成员列表",
     *                     @OA\Items(
     *                         @OA\Property(property="is_leader", type="integer", description="是否团长 1:是 0:否"),
     *                         @OA\Property(property="avatar", type="string", description="成员头像")
     *                     )
     *                 ),
     *                 @OA\Property(property="total_price", type="string", description="拼团累积金额"),
     *                 @OA\Property(property="end_time", type="integer", description="活动结束时间"),
     *                 @OA\Property(property="max_members", type="integer", description="最大团员数"),
     *                 @OA\Property(property="need_items_group_full", type="integer", description="再购买多少件商品拼团成功"),
     *                 @OA\Property(property="is_leader_can_buy", type="integer", description="团长是否可购买 1:可购买 0:不可购买"),
     *                 @OA\Property(property="gid", type="integer", description="商品ID")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="cookie",
     *         required=false,
     *         description="用户ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionGroupMembers()
    {
        // 1. 参数验证
        $form = CUtil::VdForm(new GroupPurchaseListForm(), 'grouped');
        // 2. 获取团购商品详情
        $result = GroupPurchaseService::getInstance()->getGroupMembers($form->group_purchase_id, $this->user_id);
        // 3. 返回处理结果
        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/share-invitation-link",
     *     summary="拼团活动-分享链接",
     *     description="根据拼团ID生成并返回团购的分享邀请链接",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"group_purchase_id"},
     *                         @OA\Property(property="group_purchase_id", type="integer", description="拼团ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="分享链接生成成功！", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据",
     *                         @OA\Property(property="url_link", type="string", example="https://test2-wxmall.dreame.tech/front/ulink/?p=bGiwdoaM", description="生成的团购分享链接")
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="您的等级不符合要求", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */
    public function actionShareInvitationLink()
    {
        // 提取 userId 和 POST 参数
        $userId = $this->user_id;
        $params = \Yii::$app->request->post();

        // 1. 并发限制 - 使用唯一键限制用户的并发请求
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($success) = by::model("CommModel", MAIN_MODULE)->AccFrequency($userId, $unique_key, 1, "EX", 1);
        if (!$success) {
            // 频繁请求的响应
            CUtil::json_response(-1, '请求过于频繁，请稍候再试！');
        }

        // 检查 userId 是否为空或长度是否超过11位
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请先登录或授权！');
        }

        // 提取并验证 group_purchase_id 参数
        $groupPurchaseId = $params['group_purchase_id'] ?? null;
        if (empty($groupPurchaseId)) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '缺少必要参数：group_purchase_id');
        }

        // 调用业务逻辑，生成分享链接
        list($shareSuccess, $linkResult) = GroupPurchaseService::getInstance()->shareInvitationLink($groupPurchaseId);

        // 根据结果返回不同的响应
        if (!$shareSuccess) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '生成分享链接失败：' . $linkResult);
        }

        // 成功生成分享链接，返回成功结果
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '分享链接生成成功！', $linkResult);
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/check-group-leader",
     *     summary="拼团活动-检查是否为团长",
     *     description="检查当前用户是否为拼团团长",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="group_purchase_id", type="integer", description="拼团ID（二选一）"),
     *                         @OA\Property(property="order_no", type="string", description="订单号（二选一）"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据",
     *                         @OA\Property(property="is_leader", type="integer", example=1, description="是否为团长，1是团长，0不是团长"),
     *                         @OA\Property(property="group_purchase_id", type="string", example="12", description="拼团ID")
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="参数错误！", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据"
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */

    public function actionCheckGroupLeader()
    {
        // 获取POST请求参数
        $params = \Yii::$app->request->post();

        // 调用服务检查用户是否为团长
        list($status, $message) = GroupPurchaseService::getInstance()->checkLeader($this->user_id, $params);

        // 根据返回的状态决定响应内容
        if ($status) {
            // 成功时返回状态码、提示消息和数据
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', $message);
        } else {
            // 失败时返回状态码和错误消息
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $message);
        }
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/activity-detail",
     *     summary="团购活动描述",
     *     description="获取指定团购活动的详细信息",
     *     tags={"拼团活动"},
     *         @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                  @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                  @OA\Property(property="group_purchase_id", type="string", description="团ID")
     *                 )
     *             }
     *          )
     *        )
     *    ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="数据",
     *                 @OA\Property(property="details", type="string", description="活动详细描述")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="cookie",
     *         required=false,
     *         description="用户ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionActivityDetail()
    {
        // 1. 参数验证
        $params = \Yii::$app->request->post();
        if (empty($params['activity_id']) && empty($params['group_purchase_id'])) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '缺少必要参数：activity_id,group_purchase_id');
        }
        // 2. 获取拼团活动详情
        $result = GroupPurchaseService::getInstance()->getGroupPurchaseDetail($params['activity_id'] ?? '', $params['group_purchase_id'] ?? '');
        // 3. 返回处理结果
        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/get-id",
     *     summary="获取拼团活动最大ID",
     *     description="获取当前拼团活动表中的最大ID值",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(ref="#/components/schemas/BaseRequest")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(property="max_id", type="integer", example=100, description="拼团活动表中的最大ID值")
     *             )
     *         )
     *     )
     * )
     */
    public function actionGetId()
    {

        $activity = byNew::GroupPurchaseActivityModel()->getCurrentActivity();
        $maxId    = $activity['id'] ?? 0;
        if (empty($maxId)) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '没有进行中的拼团活动');
        }
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', ['max_id' => $maxId]);
    }

    public function actionCheckOrder()
    {
        // 1. 参数验证
        $params   = \Yii::$app->request->post();
        $order_no = $params['order_no'] ?? '';
        if (empty($order_no)) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '缺少必要参数：order_no');
        }

        $result = GroupPurchaseService::getInstance()->checkOrder($order_no);
        // 3. 返回处理结果

        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], ['status' => $result['status']]);
    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/in-progress-list",
     *     summary="获取拼团进行中列表",
     *     description="获取所有进行中的拼团列表，支持正常列表和随机模式。返回包含商品信息、拼团状态、成员信息等详细数据。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="type", type="integer", description="列表类型 1:正常列表 2:随机模式", default=1),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="gid", type="integer", description="商品ID，可选参数，用于过滤特定商品的拼团"),
     *                         required={"activity_id"}
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *             @OA\Property(property="sMsg", type="string", example="获取成功", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="拼团列表数据",
     *                 @OA\Property(
     *                     property="title",
     *                     type="string",
     *                     description="活动标题"
     *                 ),
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="拼团列表",
     *                     @OA\Items(
     *                         type="object",
     *                         @OA\Property(property="gid", type="integer", description="商品ID"),
     *                         @OA\Property(property="name", type="string", description="商品名称"),
     *                         @OA\Property(property="cover_image", type="string", description="商品图片URL"),
     *                         @OA\Property(property="price", type="number", format="float", description="商品拼团价格"),
     *                         @OA\Property(property="mprice", type="number", format="float", description="商品市场原价"),
     *                         @OA\Property(property="status", type="integer", description="商品状态 0:正常 1:下架"),
     *                         @OA\Property(property="button_status", type="integer", description="按钮状态 0:抢 1:已抢光 2:邀请好友 3:已下架 4:团员已满 5:拼团已结束"),
     *                         @OA\Property(property="remaining_members", type="integer", description="还差多少人成团"),
     *                         @OA\Property(property="group_purchase_id", type="integer", description="拼团ID"),
     *                         @OA\Property(property="end_time", type="integer", description="活动结束时间戳"),
     *                         @OA\Property(
     *                             property="member_avatars",
     *                             type="array",
     *                             description="成员头像列表",
     *                             @OA\Items(type="string", format="uri")
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(
     *                     property="page",
     *                     type="integer",
     *                     description="当前页码"
     *                 ),
     *                 @OA\Property(
     *                     property="page_size",
     *                     type="integer",
     *                     description="每页数量"
     *                 ),
     *                 @OA\Property(
     *                     property="total",
     *                     type="integer",
     *                     description="总记录数"
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="default",
     *         description="失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *             @OA\Property(property="sMsg", type="string", example="活动不存在", description="错误提示消息")
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Parameter(
     *         name="user_id",
     *         in="cookie",
     *         required=false,
     *         description="用户ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionInProgressList()
    {
        // 获取请求参数
        $form       = CUtil::VdForm(new GroupPurchaseListForm());
        $type       = !empty($form->type)?$form->type:1;
        $activityId = $form->activity_id??0;
        $userId     = $this->user_id??0;
        $goods_name = $form->goods_name ?? '';
        $gid        = $form->gid ?? null;

        // 调用服务层获取拼团列表
        $result = GroupPurchaseService::getInstance()->getInProgressGroupList($userId, $activityId, $type, $goods_name, $gid);

        // 返回结果
        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/check",
     *     summary="拼团活动-团检查",
     *     description="检查拼团活动的状态和资格，包括活动有效性、团长有效性和待支付订单检查",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"activity_id", "group_purchase_id"},
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="group_purchase_id", type="string", description="拼团ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="检测通过", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="成功时返回的数据"
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="当前存在一笔待支付订单，需先完成支付或取消订单后才可进行下一步", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="失败时返回的数据",
     *                         @OA\Property(
     *                             property="un_pay_orders",
     *                             type="string",
     *                             description="待支付订单号"
     *                         )
     *                     )
     *                 )
     *             }
     *         )
     *     )
     * )
     */
    public function actionCheck()
    {
        // 获取请求参数
        $form       = CUtil::VdForm(new GroupPurchaseListForm(), 'check');
        $userId     = $this->user_id;
        $activityId = $form->activity_id;
        $gid        = $form->gid;

        // 调用服务层获取拼团列表
        $ret = GroupPurchaseService::getInstance()->checkGroupPurchase($userId, $activityId, $gid);

        // 返回结果
        if ($ret['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $ret['message'], $ret['data'] ?? "");
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $ret['message'], $ret['data'] ?? "");
        }

    }

    /**
     * @OA\Post(
     *     path="/main/group-purchase/my",
     *     summary="获取我的拼团列表",
     *     description="获取当前用户参与的或发起的拼团列表，支持按商品名称搜索",
     *     tags={"拼团活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="type", type="integer", description="1:我参与的团购 2:我发起的团购 3:我的正在拼的团", example=1),
     *                         @OA\Property(property="goods_name", type="string", description="商品名称，用于搜索", example="商品名称")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             oneOf={
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg", "data"},
     *                     @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *                     @OA\Property(property="sMsg", type="string", example="获取成功", description="提示消息"),
     *                     @OA\Property(
     *                         property="data",
     *                         type="object",
     *                         description="拼团列表数据",
     *                         @OA\Property(
     *                             property="list",
     *                             type="array",
     *                             description="拼团列表",
     *                             @OA\Items(
     *                                 type="object",
     *                                 @OA\Property(property="id", type="integer", description="商品ID"),
     *                                 @OA\Property(property="gid", type="integer", description="商品关联ID"),
     *                                 @OA\Property(property="name", type="string", description="商品名称"),
     *                                 @OA\Property(property="price", type="number", format="float", description="商品价格"),
     *                                 @OA\Property(property="mprice", type="number", format="float", description="商品市场价"),
     *                                 @OA\Property(property="status", type="integer", description="商品状态 0:正常 1:下架"),
     *                                 @OA\Property(property="cover_image", type="string", description="商品封面图片URL"),
     *                                 @OA\Property(property="atype", type="integer", description="商品属性（0：统一规格；1：多规格；2：自定义）"),
     *                                 @OA\Property(property="button_status", type="integer", description="按钮状态 0：抢 1：已抢光 2：邀请好友 3：已下架 4：团员已满 5：拼团已结束"),
     *                                 @OA\Property(property="number", type="string", description="参团数量")
     *                             )
     *                         ),
     *                         @OA\Property(
     *                             property="page",
     *                             type="integer",
     *                             description="当前页码"
     *                         ),
     *                         @OA\Property(
     *                             property="page_size",
     *                             type="integer",
     *                             description="每页数量"
     *                         ),
     *                         @OA\Property(
     *                             property="total",
     *                             type="integer",
     *                             description="总记录数"
     *                         )
     *                     )
     *                 ),
     *                 @OA\Schema(
     *                     type="object",
     *                     required={"iRet", "sMsg"},
     *                     @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                     @OA\Property(property="sMsg", type="string", example="获取失败", description="错误提示消息")
     *                 )
     *             }
     *         )
     *     )
     * )
     */
    public function actionMy()
    {
        $form = CUtil::VdForm(new GroupPurchaseListForm(), 'my');
        // 获取请求参数
        $userId     = $this->user_id;
        $goods_name = $form->goods_name;
        $type       = !empty($form->type)?$form->type:1;

        $result = GroupPurchaseService::getInstance()->getMyGroupPurchaseList($userId, $type, $goods_name);
        // 返回结果
        if ($result['status']) {
            CUtil::Ret($result);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }

    /**
     * @OA\Get(
     *     path="/main/group-purchase/check-un-pay-order",
     *     summary="检查是否有团购的待支付订单",
     *     tags={"团购"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Schema(
     *                 type="object",
     *                 required={"iRet", "sMsg", "data"},
     *                 @OA\Property(property="iRet", type="integer", example=0, description="状态码，0表示成功"),
     *                 @OA\Property(property="sMsg", type="string", example="有待支付订单", description="提示消息"),
     *                 @OA\Property(
     *                     property="data",
     *                     type="object",
     *                     @OA\Property(property="un_pay_orders", type="array", description="待支付订单列表",
     *                         @OA\Items(
     *                             type="object",
     *                             @OA\Property(property="group_purchase_id", type="integer", description="拼团ID"),
     *                             @OA\Property(property="order_no", type="string", description="订单号")
     *                         )
     *                     )
     *                 )
     *             ),
     *             @OA\Schema(
     *                 type="object",
     *                 required={"iRet", "sMsg"},
     *                 @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *                 @OA\Property(property="sMsg", type="string", example="没有待支付订单", description="错误提示消息")
     *             )
     *         )
     *     )
     * )
     */
    public function actionCheckUnPayOrder()
    {
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请您先授权登录！');
        }
        $group_purchase_id= \Yii::$app->request->post('group_purchase_id', 0);
        if (empty($group_purchase_id)) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '参数错误');
        }
        $ret = GroupPurchaseService::getInstance()->checkUnPayOrder($userId,$group_purchase_id);
        if ($ret['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $ret['message'], $ret['data'] ?? "");
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $ret['message'], $ret['data'] ?? "");
        }
    }


    /**
     * @OA\Post(
     *     path="/main/group-purchase/real-time-leaderboard",
     *     summary="团购实时榜单",
     *     description="获取团购实时榜单，返回拼团最多、销量最多、降价最大的商品，三个商品不重复，如果没有符合的商品则返回空数组",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="activity_id", type="string", description="活动ID，可选参数，不传时自动获取当前活动")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码，1表示成功"),
     *             @OA\Property(property="sMsg", type="string", example="获取成功", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="榜单列表，第一位为拼团最多的商品，第二位为销量最多的商品，第三位为降价最大的商品",
     *                 @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="cover_image", type="string", description="商品封面图片URL"),
     *                     @OA\Property(property="price", type="number", format="float", description="商品拼团价格"),
     *                     @OA\Property(property="mprice", type="number", format="float", description="商品市场原价"),
     *                     @OA\Property(property="type", type="string", description="榜单类型", enum={"group_count", "sales_count", "discount_amount"}),
     *                     @OA\Property(property="value", type="number", description="对应类型的数值（拼团数量、销量、降价金额）"),
     *                     @OA\Property(property="status", type="integer", description="商品状态 0:正常 1:下架")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="default",
     *         description="失败",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=-1, description="状态码，-1表示失败"),
     *             @OA\Property(property="sMsg", type="string", example="获取失败", description="错误提示消息")
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionRealTimeLeaderboard()
    {
        // 获取请求参数
        $params = \Yii::$app->request->post();
        $activityId = $params['activity_id'] ?? '';
        
        // 如果没有传入activity_id，则获取当前进行中的活动ID
        if (empty($activityId)) {

            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '活动ID不能为空');
            return;
        }
        
        // 调用服务层获取榜单数据
        $result = GroupPurchaseService::getInstance()->getRealTimeLeaderboard($activityId);
        
        // 返回处理结果
        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }


    public function actionTags()
    {
        $data = CUtil::dictValue('group_purchase_tags');
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
    }
}
