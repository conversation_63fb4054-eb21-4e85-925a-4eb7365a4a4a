<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CUtil;

class LiveController extends CommController
{


    /**
     * @return void
     * 获取直播列表
     */
    public function actionGetLiveList()
    {
        $userId = $this->user_id ?? null;
        list($status, $ret) = by::model("LiveModel", MAIN_MODULE)->getLiveList($userId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

}
