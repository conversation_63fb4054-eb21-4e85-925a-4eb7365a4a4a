<?php

namespace app\modules\main\controllers;

use app\models\Response;
use app\modules\main\services\AliPayService;
use app\modules\main\services\PayService;
use Yii;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class DepositController extends CommController
{
    /**
     * @return void
     * @throws Exception
     * @throws \RedisException
     * 定金订单详情页
     */
    public function actionDepositBuyInfo(){
        $post                = Yii::$app->request->post();

        list($status, $ret) = by::Odeposit()->depositBuyInfo($post);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        $list = $ret['list'] ?? [];
        if($list){
            $ret['list'] = $this->_responseList($list);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @param $info
     * @return mixed
     * 订单前端输出适配
     */
    private function _responseList($goods){

        //输出封装
        if($goods){
            foreach ($goods as &$good){
                $presale_info = $good['presale_info'] ?? [];
                if($presale_info){
                    $good['presale_info'] = Response::responseList($presale_info,[
                        'is_presale'        => 'int',
                        'presale_time'   => 'time|ms',
                        'start_payment'  => 'time|ms',
                        'end_payment'    => 'time|ms',
                        'surplus_time'   => 'is_int',
                        'scheduled_number' => 'int',
                    ]);
                }
            }
        }

        return $goods;
    }

    /**
     * @return void
     * @throws Exception
     * 创建定金订单
     */
    public function actionDepositBuy()
    {
        $post     = Yii::$app->request->post();
        $post['platform_source'] = $this->platformSource ?? 0;
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $post['union'] = substr($this->union,0,50);
        $post['euid'] = substr($this->euid,0,50);
        $post['referer'] = $this->referer ?? '';
        list($status,$ret) = by::Odeposit()->addDepositRecord($this->user_id,$post);
        if(!$status){
            CUtil::json_response(-1,$ret);
        }
        CUtil::json_response(1,'ok', $ret);
    }

    /**
     * @throws Exception
     * @throws \RedisException
     * 定金订单详情
     */
    public function actionDepositInfo()
    {
        $order_no   = Yii::$app->request->post('order_no', 0);

        $info     = by::Odeposit()->CommDepositPackageInfo($this->user_id,$order_no,false,true,true,true,true,false);

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @return void
     * @throws Exception
     * @throws \RedisException
     * 定金订单支付
     */
    public function actionDepositPay()
    {
        // 参数
        $order_no           = Yii::$app->request->post('order_no', '');
        $order_type         = Yii::$app->request->post('order_type', by::Odeposit()::TYPE['DEPOSIT']);
        $pay_type           = Yii::$app->request->post('pay_type', by::Odeposit()::PAY_BY_WX);
        $pay_type           = CUtil::uint($pay_type);

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency($this->user_id, $unique_key, 3);
        if (!$anti) {
            CUtil::json_response(-1, '请勿频繁操作');
        }

        //定金订单支付校验
        $depositOrderInfo = by::Odeposit()->CommDepositPackageInfo($this->user_id,$order_no);
        if(empty($depositOrderInfo)){
            CUtil::json_response(-1, '定金订单不存在！');
        }
        $presale_time = $depositOrderInfo['goods'][0]['presale_info']['presale_time'] ?? 0;
        if($presale_time && CUtil::uint($presale_time) < intval(START_TIME)){
            CUtil::json_response(-1, '已过定金截止支付时间，支付失败！');
        }

        $gid = $depositOrderInfo['goods'][0]['gid'] ?? 0;
        if($gid){
            //判断产品是否下架
            $aMain       = by::Gmain()->GetOneByGid($gid);
            if(empty($aMain) || $aMain['status'] != 0){
                CUtil::json_response(-1, '该商品已经下架，请稍后再试！');
            }
        }

        // 附加信息
        $attach =['source'=> by::WxPay()::SOURCE['DEPOSIT']];

        if ($pay_type == by::Odeposit()::PAY_BY_WX || $pay_type == by::Odeposit()::PAY_BY_WX_APP) {
            list($status, $ret) = by::WxPay()->AgainPay($this->user_id, $order_no, $this->api, $attach, $order_type, $pay_type);
        } elseif ($pay_type == by::Odeposit()::PAY_BY_WX_H5) {
            list($status, $ret) = by::WxH5Pay()->AgainPay($this->user_id, $order_no, $this->api, $attach, $order_type);
        } elseif ($pay_type == by::Odeposit()::PAY_BY_ALIPAY || $pay_type == by::Odeposit()::PAY_BY_MP_WX || $pay_type == by::Odeposit()::PAY_BY_MP_WEB_WX_H5|| by::Odeposit()::PAY_BY_MP_WEB_ALIPAY_H5) { // 支付宝、中台支付（微信/支付宝）
            list($status, $ret) = PayService::getInstance()->againPay($this->user_id, $order_no, $order_type, $pay_type);
        } else {
            $status = false;
            $ret = '未指定对应支付平台！';
        }

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 补丁：替换返回参数
        if ($pay_type == by::Odeposit()::PAY_BY_WX_APP) {
            $ret['prepayId'] = $ret['prepay_id'];
            $ret['sign'] = $ret['paySign'];
            unset($ret['prepay_id'], $ret['paySign']);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @throws Exception
     * @throws \RedisException
     * 取消定金订单
     */
    public function actionDepositCancel()
    {
        $order_no           = Yii::$app->request->post('deposit_order_no', '');
        $r_type             = Yii::$app->request->post('r_type', 0);

        if (!in_array($r_type, array_keys(by::Odeposit()::R_TYPE))) {
            CUtil::json_response(-1, "请选择正确的取消原因");
        }

        list($status, $ret) = by::Odeposit()->depositRecycle($this->user_id, $order_no, $r_type);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * @return void
     * @throws Exception
     * 根据定金订单号获取尾款订单
     */
    public function actionTailOrderDetail()
    {
        $order_no           = Yii::$app->request->post('order_no', '');//定金订单号
        if(empty($order_no)){
            CUtil::json_response(-1, '订单号不能为空');
        }

        //根据定金订单号获取尾款订单号
        $oInfo        = by::Ouser()->GetInfoByDepositNo($this->user_id,$order_no);
        CUtil::json_response(1, 'ok', $oInfo);
    }
}
