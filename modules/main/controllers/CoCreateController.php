<?php

namespace app\modules\main\controllers;


use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\services\cocreate\CoCreateMaterialService;
use app\modules\wares\services\cocreate\CoCreateSortService;
use app\modules\wares\services\cocreate\CoCreateStatisticService;
use app\modules\wares\services\cocreate\CoCreateUserService;
use Yii;

class CoCreateController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/co-create/user-status",
     *     summary="共创空间是否显示/是否用过",
     *     description="共创空间是否显示/是否用过",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionUserStatus()
    {
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        if (in_array($this->api,['a_1643028263','i_1643028386'])){
            $info = true;
        }else{
            $info = CoCreateUserService::getInstance()->GetInfoByUserId($this->user_id);
        }
        $use = CoCreateStatisticService::getInstance()->GetIfUseByUserId($this->user_id);
        $arr = [
            'is_view' => !empty($info),
            'is_use' => $use,
        ];
        CUtil::json_response(1, 'ok', $arr);
    }


    /**
     * @OA\Post(
     *     path="/main/co-create/material-list",
     *     summary="共创空间素材列表",
     *     description="共创空间素材列表",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page", type="integer", default="1", description="当前页"),
     *                         @OA\Property(property="page_size", type="integer", default="20", description="每次取多少条"),
     *                         @OA\Property(property="topic", type="string", default="car", description="列表主题")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionMaterialList()
    {
        $page_size = Yii::$app->request->post('page_size', 20);
        $page      = Yii::$app->request->post('page', 1);
        $topic      = Yii::$app->request->post('topic', 'car');
        $topic = CUtil::removeXss($topic);
        $user_id   = $this->user_id;
        strlen($user_id) > 12 && $user_id = 0;
        if (empty($user_id)) CUtil::json_response(1, 'ok', []);
//        if (!in_array($this->api,['a_1643028263','i_1643028386'])){
//            $info = CoCreateUserService::getInstance()->GetInfoByUserId($user_id);
//            empty($info) && CUtil::json_response(-1, '该用户未授权!');
//        }
//        $result = CoCreateMaterialService::getInstance()->GetRandListByUserId($user_id, $page, $page_size);
        $result = CoCreateMaterialService::getInstance()->GetNowListByUserId($user_id, $topic, $page, $page_size);
        CUtil::json_response(1, 'ok', $result);
    }


    /**
     * @OA\Post(
     *     path="/main/co-create/material-topic-list",
     *     summary="共创空间主题列表",
     *     description="共创空间主题列表",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionMaterialTopicList()
    {
        CUtil::json_response(1, 'ok', byNew::CoCreateMaterialModel()::TOPIC_LIST);
    }

    /**
     * @OA\Post(
     *     path="/main/co-create/enjoy",
     *     summary="共创空间素材喜好判断",
     *     description="共创空间素材喜好判断",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/CoCreateEnjoyRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionEnjoy()
    {
          $post = Yii::$app->request->post();
          list($s,$msg) = CoCreateMaterialService::getInstance()->Enjoy($this->user_id,$post);
          if(!$s){
              CUtil::json_response(-1, $msg);
          }
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/main/co-create/material-user-list",
     *     summary="共创空间用户喜好列表",
     *     description="共创空间用户喜好列表",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/CoCreateMaterialUserListRequest"),
     *                     @OA\Schema(ref="#/components/schemas/PageRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionMaterialUserList()
    {
        $page_size = Yii::$app->request->post('page_size', 20);
        $page      = Yii::$app->request->post('page', 1);
        $enjoy     = intval(Yii::$app->request->post('enjoy', 1));
        $topic     = trim(Yii::$app->request->post('topic', ''));
        $user_id   = $this->user_id;
        strlen($user_id) > 12 && $user_id = 0;
        if (empty($user_id)) CUtil::json_response(1, 'ok', []);
        $input  = ['enjoy' => $enjoy,'topic'=>$topic];
        $result = CoCreateStatisticService::getInstance()->GetUserStatisticList($user_id, $input, $page, $page_size);
        CUtil::json_response(1, 'ok', $result);
    }

    /**
     * @OA\Post(
     *     path="/main/co-create/material-sort-list",
     *     summary="共创空间素材排序列表",
     *     description="共创空间素材分类列表",
     *     tags={"共创空间"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="topic", type="string", default="car", description="主题")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="boolean", description="数据")
     *         )
     *     )
     * )
     */
    public function actionMaterialSortList()
    {
        $topic     = trim(Yii::$app->request->post('topic', ''));
        $result = CoCreateSortService::getInstance()->GetSortByTopic($topic);
        CUtil::json_response(1, 'ok', $result);
    }
}
