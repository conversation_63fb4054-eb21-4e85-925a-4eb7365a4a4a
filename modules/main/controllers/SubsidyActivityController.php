<?php
namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\back\services\SubsidyActivityService;
use Yii;

class SubsidyActivityController extends CommController {

    /**
     * 获取当前生效的国补活动
     * @OA\Post(
     *     path="/main/subsidy-activity/current",
     *     summary="获取当前生效的国补活动",
     *     description="获取当前时间段内生效的国补活动信息，包括关联商品",
     *     tags={"国补活动管理"},
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema()
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="ok"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", description="活动ID", example=1),
     *                 @OA\Property(property="name", type="string", description="活动名称", example="双11国补活动"),
     *                 @OA\Property(property="start_time", type="integer", description="开始时间戳", example=1672531200),
     *                 @OA\Property(property="end_time", type="integer", description="结束时间戳", example=1672617600),
     *                 @OA\Property(property="desc", type="string", description="活动描述", example="双11国补活动描述"),
     *                 @OA\Property(property="status", type="integer", description="活动状态", example=2),
     *                 @OA\Property(property="create_time", type="integer", description="创建时间戳", example=1672531200),
     *                 @OA\Property(property="time_status", type="string", description="时间状态文本", example="进行中"),
     *                 @OA\Property(property="is_active", type="boolean", description="是否正在进行", example=true),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     description="关联商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="gid", type="integer", description="商品ID", example=123),
     *                         @OA\Property(property="sku", type="string", description="商品SKU", example="DM001"),
     *                         @OA\Property(property="name", type="string", description="商品名称", example="追觅扫地机器人"),
     *                         @OA\Property(property="cover_image", type="string", description="商品封面图", example="https://example.com/image.jpg"),
     *                         @OA\Property(property="price", type="number", description="商品价格", example=1999.00),
     *                         @OA\Property(property="subsidy_ratio", type="number", description="国补比例", example=15.50)
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     * @throws \Throwable
     */
    public function actionCurrent()
    {
        try {
            $service = SubsidyActivityService::getInstance();
            $result  = $service->getCurrentActivity();

            if (empty($result)) {
                CUtil::json_response(1, '当前无生效的国补活动');
            } else {
                CUtil::json_response(1, 'ok', $result);
            }
        } catch (\Exception $e) {
            CUtil::json_response(-1, '获取当前活动失败：' . $e->getMessage());
        }
    }

    // 活动列表查询
    public function actionList()
    {
        try {
            $tid = Yii::$app->request->post('tid', 0);
            $activity_id = Yii::$app->request->post('activity_id', 0);
            if (empty($tid) || empty($activity_id)) {
                CUtil::json_response(-1, '参数有误');
            }

            $service = SubsidyActivityService::getInstance();
            $result  = $service->getActivityGoods( $activity_id,$tid);

            CUtil::json_response(1, 'ok', $result);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '获取列表失败');
        }
    }

}
