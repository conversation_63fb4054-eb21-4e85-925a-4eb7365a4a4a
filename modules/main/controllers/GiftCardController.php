<?php

namespace app\modules\main\controllers;

use app\exceptions\GiftCardGoodsException;
use app\models\by;
use app\models\CUtil;
use app\modules\main\services\GiftCardService;

class GiftCardController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/gift-card/activate-card",
     *     summary="激活卡片",
     *     description="激活卡片",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"card_password"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="card_password", type="string", default="", description="卡密")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionActivateCard()
    {
        $post = \Yii::$app->request->post();
        // 授权
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先用户授权，再绑定卡片');
        }
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__, $userId);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
        if (!$anti) {
            CUtil::json_response(-1, "绑定操作频繁，请稍后再试");
        }
        // 卡密
        $cardPassword = $post['card_password'] ?? '';
        if (empty($cardPassword)) {
            CUtil::json_response(-1, '卡密不能为空');
        }
        // 激活
        try {
            GiftCardService::getInstance()->activateCard($userId, $cardPassword);
            CUtil::json_response(1, 'ok');
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            // 记录日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug('ERROR|' . $error, 'err.giftcard.msg');
            CUtil::json_response(-1, '绑定失败');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/gift-card/validate-card",
     *     summary="校验卡片",
     *     description="校验卡片",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"card_password"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="card_password", type="string", default="", description="卡密")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", ref="#/components/schemas/ValidateCard", description="数据")
     *         )
     *     )
     * )
     */
    public function actionValidateCard()
    {
        $post = \Yii::$app->request->post();
        // 授权
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先用户授权');
        }
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__, $userId);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
        if (!$anti) {
            CUtil::json_response(-1, "操作频繁，请稍后再试");
        }
        // 卡密
        $cardPassword = $post['card_password'] ?? '';
        if (empty($cardPassword)) {
            CUtil::json_response(1, 'ok', [
                'is_valid' => false,
                'msg'      => '卡密不能为空',
            ]);
        }
        // 激活
        try {
            GiftCardService::getInstance()->validateCard($cardPassword);
            CUtil::json_response(1, 'ok', [
                'is_valid' => true,
                'msg'      => '',
            ]);
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(1, 'ok', [
                'is_valid' => false,
                'msg'      => $e->getMessage(),
            ]);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/gift-card/share-card-list",
     *     summary="分享卡片列表",
     *     description="分享卡片列表",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent()
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/SharedGiftCard"), description="数据")
     *         )
     *     )
     * )
     */
    public function actionShareCardList()
    {
        // 授权
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先用户授权，再查看分享记录');
        }

        $res = GiftCardService::getInstance()->getShareCardList($userId);
        CUtil::json_response(1, 'ok', $res);
    }


    /**
     * @OA\Post(
     *     path="/main/gift-card/user-card-list",
     *     summary="我的礼品卡列表",
     *     description="我的礼品卡列表",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="status", type="string", default="1", description="状态 1：可用 2：不可用")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),

     *         )
     *     )
     * )
     */
    public function actionUserCardList()
    {
        $post = \Yii::$app->request->post();

        //状态 1：可用 2：不可用
        $status = $post['status'] ?? 1;

        //适用商品ID
        $goodsId = $post['goods_id'] ?? '';

        //获取卡类型
        $type = intval($post['type'] ?? 0);

        // 用户ID
        $userId = $this->user_id;

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }


        list($s, $ret) = GiftCardService::getInstance()->getUserCardList($userId, $status, $goodsId, $type);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/gift-card/user-card-info",
     *     summary="我的礼品卡详情",
     *     description="我的礼品卡详情",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="id", type="string", default="", description="卡ID 主键")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *            @OA\Property(property="data",type="object",ref="#/components/schemas/UserCardInfo",description="数据"),
     *         )
     *     )
     * )
     */
    public function actionUserCardInfo()
    {
        $id = \Yii::$app->request->post('id', 0);

        list($s, $ret) = GiftCardService::getInstance()->getUserCardInfo($id);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/gift-card/check-content",
     *     summary="敏感词校验",
     *     description="敏感词校验",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="content", type="string", default="", description="祝福语")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */
    public function actionCheckContent()
    {
        $post = \Yii::$app->request->post();

        //用户ID
        $user_id = $this->user_id;

        //祝福语
        $content = $post['content'] ?? '';

        list($s, $ret) = GiftCardService::getInstance()->checkContent($user_id,$content);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/gift-card/share-card",
     *     summary="礼品卡分享",
     *     description="礼品卡分享",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="id", type="string", default="", description="卡ID 主键"),
     *                         @OA\Property(property="content", type="string", default="", description="祝福语")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="string", description="加密参数")
     *         )
     *     )
     * )
     */
    public function actionShareCard()
    {
        $post = \Yii::$app->request->post();

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }

        //用户ID
        $user_id = $this->user_id;

        //祝福语
        $content = $post['content'] ?? '';

        //卡ID
        $id = $post['id'] ?? '';

        list($s, $ret) = GiftCardService::getInstance()->ShareGiftCard($user_id,$id,$content);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/gift-card/share-card-info",
     *     summary="礼品卡分享详情",
     *     description="礼品卡分享详情",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="encode", type="string", default="", description="加密字符串"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="string", description="加密参数")
     *         )
     *     )
     * )
     */
    public function actionShareCardInfo()
    {
        $post = \Yii::$app->request->post();

        //加密字符串（分享人ID，卡ID，时间戳）
        $encode = $post['encode'] ?? '';

        //用户ID
        $user_id = $this->user_id;

        list($s, $ret) = GiftCardService::getInstance()->getShareGiftCardInfo($encode,$user_id);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/gift-card/receive-card",
     *     summary="领取卡片",
     *     description="领取卡片",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"encode"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="encode", type="string", default="", description="签名code")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionReceiveCard()
    {
        $post = \Yii::$app->request->post();

        // 加密字符串（分享人ID，卡ID，时间戳）
        $encode = $post['encode'] ?? '';
        if (!$encode) {
            CUtil::json_response(-1, '参数错误');
        }

        // 用户ID
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先用户授权，再领取');
        }

        // 领取卡片
        try {
            GiftCardService::getInstance()->receiveGiftCard($userId, $encode);
            CUtil::json_response(1, 'ok');
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::json_response(-1, '领取失败');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/gift-card/expend-record",
     *     summary="礼品卡消费记录",
     *     description="礼品卡消费记录",
     *     tags={"礼品卡"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 required={"user_card_id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="user_card_id", type="integer", default="", description="用户的卡ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", ref="#/components/schemas/GiftCardExpendRecord", description="数据")
     *         )
     *     )
     * )
     */
    public function actionExpendRecord()
    {
        $userId = $this->user_id;
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        $userCardId = \Yii::$app->request->post('user_card_id');
        if (empty($userCardId)) {
            CUtil::json_response(-1, '参数错误');
        }
        // 消费记录
        try {
            $res = GiftCardService::getInstance()->getExpendRecord($userId, $userCardId);
            CUtil::json_response(1, 'ok', $res);
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            // 记录日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug('ERROR|' . $error, 'err.giftcard.msg');
            CUtil::json_response(-1, '消费记录查询失败');
        }
    }
}