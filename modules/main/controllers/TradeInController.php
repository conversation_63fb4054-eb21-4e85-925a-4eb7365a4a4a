<?php
namespace app\modules\main\controllers;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\TradeInService;

class TradeInController extends CommController
{

    /**
     * 活动
     * @return void
     */
    public function actionActivity()
    {
        try {
            $res = TradeInService::getInstance()->getActivity();
            CUtil::json_response(1, 'ok', $res);
        } catch (\Exception $e) {
            CUtil::json_response(-1, $e->getMessage());
        }
    }

    /**
     * 获取商品列表
     * @return void
     */
    public function actionProductList()
    {
        try {
            $res = TradeInService::getInstance()->getProductList();
            CUtil::json_response(1, 'ok', $res);
        } catch (\Exception $e) {
            CUtil::json_response(-1, $e->getMessage());
        }
    }

    /**
     * 查询回收类目
     * @return void
     */
    public function actionCateList()
    {
        // 查询回收类目
        list($status, $res) = TradeInService::getInstance()->getCateList();
        if (!$status) {
            CUtil::json_response(-1, $res);
        }

        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 查询型号模型
     * @return void
     */
    public function actionModelList()
    {
        // 请求参数
        $post = \Yii::$app->request->post();
        $cate_id = $post['cate_id'] ?? 0;
        if (!$cate_id) {
            CUtil::json_response(-1, '参数错误');
        }

        // 查询型号模型
        list($status, $res) = TradeInService::getInstance()->getModelList($cate_id);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 查询模型报价
     * @return void
     */
    public function actionInquiry()
    {
        // 频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 请求参数
        $post = \Yii::$app->request->post();
        $cate_id = $post['cate_id'] ?? 0;
        $config = $post['config'] ?? '';
        if (!$cate_id || !$config) {
            CUtil::json_response(-1, '参数错误');
        }

        // json转为数组
        $config = json_decode($config, true);

        // 查询模型报价
        list($status, $res) = TradeInService::getInstance()->getModelPrice($cate_id, $config);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 订单详情
     * @return void
     */
    public function actionOrderInfo()
    {
        // 请求参数
        $post = \Yii::$app->request->post();

        // 查询订单详情
        list($status, $res) = TradeInService::getInstance()->orderInfo($post['order_no'] ?? '');
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 修改发货时间
     */
    public function actionUpdateDeliveryTime(){
        // 请求参数
        $post = \Yii::$app->request->post();
        $order_no = $post['code'] ?? '';
        $delivery_time = $post['in_express_time'] ?? '';
        if (!$order_no || !$delivery_time) {
            CUtil::json_response(-1, '参数错误');
        }

        // 修改发货时间
        list($status, $res) = TradeInService::getInstance()->updateReseTime($order_no, $delivery_time);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 订单确认
     * @return void
     */
    public function actionOrderConfirm()
    {
        // 请求参数
        $post = \Yii::$app->request->post();

        $order_code = $post['order_code'] ?? '';
        $is_confirm = $post['is_confirm'] ?? 0;
        if (!$order_code || !$is_confirm) {
            CUtil::json_response(-1, '参数错误');
        }

        // 查询订单详情
        list($status, $res) = TradeInService::getInstance()->orderConfirm($order_code, $is_confirm);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 取消订单
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionCancelOrder()
    {
        // 请求参数
        $post = \Yii::$app->request->post();

        // 取消订单
        list($status, $res) = TradeInService::getInstance()->cancelOrder($post['order_no'] ?? '');
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 发放优惠券
     * @return void
     */
    public function actionDistributeCoupon()
    {
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再发放优惠券！');
        }

        // 业务逻辑
        try {
            $data = TradeInService::getInstance()->distributeCoupon($userId);
            CUtil::json_response(1, 'ok', $data);
        } catch (\Exception $e) {
            CUtil::json_response(-1, $e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/main/trade-in/tab-list",
     *     summary="获取分类列表",
     *     description="返回商店中可用的商品分类列表",
     *     tags={"以旧换新"},
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="分类列表",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="string", description="分类ID"),
     *                     @OA\Property(property="name", type="string", description="分类名称")
     *                 )
     *             )
     *         )
     *     )
     * )
     */

    public function actionTabList(){
        $data = TradeInService::getInstance()->getTabList();
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/main/trade-in/list",
     *     summary="获取商品列表",
     *     description="获取指定类别下的商品列表，包括类别信息和商品的详细规格。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"category_id"},
     *                         @OA\Property(property="category_id", type="string", description="商品类别ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示信息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回的数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="sids", type="string", description="商品规格ID集合"),
     *                         @OA\Property(property="category_id", type="string", description="商品类型ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(
     *                             property="category",
     *                             type="object",
     *                             description="商品类型信息",
     *                             @OA\Property(property="id", type="string", description="类型ID"),
     *                             @OA\Property(property="name", type="string", description="类型名称")
     *                         ),
     *                         @OA\Property(
     *                             property="goods",
     *                             type="object",
     *                             description="商品的基本信息",
     *                             @OA\Property(property="id", type="string", description="商品ID"),
     *                             @OA\Property(property="name", type="string", description="商品名称"),
     *                             @OA\Property(property="status", type="string", description="商品状态"),
     *                             @OA\Property(
     *                                 property="type0",
     *                                 type="object",
     *                                 description="商品指定规格的基本信息",
     *                                 @OA\Property(property="price", type="string", description="商品价格"),
     *                                 @OA\Property(property="image", type="string", description="商品图片URL"),
     *                                 @OA\Property(property="gid", type="string", description="商品规格ID"),
     *                                 @OA\Property(property="atype", type="string", description="商品类型标识")
     *                             )
     *                         ),
     *                         @OA\Property(
     *                             property="spec",
     *                             type="array",
     *                             description="商品规格信息列表",
     *                             @OA\Items(
     *                                 @OA\Property(property="id", type="string", description="规格ID"),
     *                                 @OA\Property(property="gid", type="string", description="商品ID"),
     *                                 @OA\Property(property="sku", type="string", description="商品SKU"),
     *                                 @OA\Property(property="price", type="string", description="规格价格"),
     *                                 @OA\Property(property="av_ids", type="string", description="属性值ID集合"),
     *                                 @OA\Property(property="image", type="string", description="规格图片"),
     *                                 @OA\Property(property="original_price", type="string", description="规格原价"),
     *                                 @OA\Property(
     *                                     property="pcombines",
     *                                     type="array",
     *                                     description="规格组合信息",
     *                                     @OA\Items(
     *                                         @OA\Property(property="id", type="string", description="组合ID"),
     *                                         @OA\Property(property="sku", type="string", description="组合SKU"),
     *                                         @OA\Property(property="sprice", type="string", description="组合价格"),
     *                                         @OA\Property(property="sprice_type", type="string", description="价格类型"),
     *                                         @OA\Property(property="ctime", type="string", description="创建时间"),
     *                                         @OA\Property(property="utime", type="string", description="更新时间")
     *                                     )
     *                                 ),
     *                                 @OA\Property(property="is_ini", type="integer", description="是否初始化"),
     *                                 @OA\Property(property="gini_info", type="array", @OA\Items(type="string"), description="初始化信息"),
     *                                 @OA\Property(property="gini_id", type="integer", description="初始化ID"),
     *                                 @OA\Property(property="gini_tag", type="string", description="初始化标签"),
     *                                 @OA\Property(property="gini_etime", type="integer", description="初始化结束时间"),
     *                                 @OA\Property(property="is_internal", type="integer", description="是否为内部规格"),
     *                                 @OA\Property(
     *                                     property="attr_cnf",
     *                                     type="array",
     *                                     description="规格属性配置",
     *                                     @OA\Items(
     *                                         @OA\Property(property="at_name", type="string", description="属性名称"),
     *                                         @OA\Property(property="at_val", type="string", description="属性值")
     *                                     )
     *                                 ),
     *                                 @OA\Property(property="sid", type="string", description="规格ID"),
     *                                 @OA\Property(property="underline_price", type="string", description="下划线价格")
     *                             )
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $categoryId = \Yii::$app->request->post('category_id', 0);
          if (!$categoryId) {
          CUtil::json_response(-1, '参数错误');
        }
        $data = TradeInService::getInstance()->getList($categoryId);
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * 订单状态检查
     */
    public function actionOrderStatusCheck()
    {
        $orderNo = \Yii::$app->request->post('order_no', '');
        if (!$orderNo) {
            CUtil::json_response(-1, '参数错误');
        }
        $trade_in_order_no=byNew::TradeInOrderModel()::find()->select('trade_in_order_no')->where(['order_no' => $orderNo])->scalar();
        $trade_in_order_no = $trade_in_order_no ?1:0;
        CUtil::json_response(1, 'ok', ['status'=>$trade_in_order_no]);
    }
}