<?php

namespace app\modules\main\controllers;

use app\components\ExpressTen;
use app\models\CUtil;
use app\modules\main\services\ExpressService;

class ExpressController extends CommController
{
    /**
     * 查询物流信息
     * @return void
     */
    public function actionFind()
    {
        $post = \Yii::$app->request->post();
        $number = $post['number'] ?? '';
        $company = $post['company'] ?? '';

        if (!$number || !$company) {
            CUtil::json_response(-1, '参数错误');
        }

        list($status, $res) = ExpressService::getInstance()->find($number, $company);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }
}