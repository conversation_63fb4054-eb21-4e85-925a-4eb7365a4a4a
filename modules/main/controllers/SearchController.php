<?php

namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\services\SearchService;
use Yii;
use yii\db\Exception;

class SearchController extends CommController
{

    protected $keyword;
    protected $sort_type;
    protected $search_type;
    protected $limit_search_nums = 50;
    protected $ip;
    protected $page = 1;
    protected $pageSize = 10;

    /**
     * @param int $search_type
     * @return string
     * 获得搜索方法
     */
    protected function _getSearchMethod(int $search_type = 1)
    {
        $loginControl   = CUtil::getConfig('searchControl', 'common', MAIN_MODULE);
        $method         = $loginControl[$search_type] ?? "";
        $method         = "_search_{$method}";
        if (!method_exists($this, $method)) {
            return false;
        }

        return $method;
    }



    /**
     * @OA\Post(
     *     path="/main/search/index",
     *     summary="搜索商品",
     *     description="搜索商品-公共方法",
     *     tags={"搜索"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/SearchRequest"),
     *          @OA\Schema(ref="#/components/schemas/PageRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionIndex()
    {
        $post              = Yii::$app->request->post();
        $this->ip          = Yii::$app->request->getUserIP();
        $this->search_type = $post['search_type'] ?? 1;//默认搜索商品
        $this->page        = $post['page'] ?? 1;       //第几页
        $this->pageSize    = $post['page_size'] ?? 10; //每页几条
        $isInternalPurchase= $post['is_internal_purchase'] ?? 0; //是否内购


        //1.参数处理
        $this->keyword   = (string)CUtil::removeXss($post['keyword'] ?? '-1');
        $this->sort_type = (int)($post['sort_type'] ?? 0);
        if (empty($this->keyword) && $this->keyword !== '0') {
            CUtil::json_response(-1, "缺少查询参数！");
        }
        if (mb_strlen($this->keyword) > 50) {
            CUtil::json_response(-1, "查询参数长度不得超过50个字！");
        }

        //3.查询数据
        $search_action = $this->_getSearchMethod($this->search_type);

        if (!$search_action) {
            CUtil::json_response(-1, "不支持的查询方式");
        }
        list($status, $searchData) = $this->$search_action($this->keyword, $this->user_id, $this->page, $this->pageSize,$this->sort_type, $isInternalPurchase);
        if (!$status) {
            CUtil::json_response(-1, $searchData);
        }


        CUtil::json_response(1, "OK", $searchData);
    }


    /**
     * @throws Exception
     */
    protected function _search_goods($keyword, $user_id, $page, $pageSize, $sortType, $isInternalPurchase = 0): array
    {
        list($status, $data) = by::search()->getGoodsByName($keyword, $this->limit_search_nums, $page, $pageSize, $this->platformId, $sortType, $isInternalPurchase,$this->api);
        if (!$status) {
            return [false, $data];
        }
        // 过滤掉活动商品数据
        $hiddenGids = ActivityConfigEnum::getHiddenGoods();

        $data['list'] = array_values(array_filter($data['list'], function ($item) use ($hiddenGids) {
            return !in_array($item['gid'], $hiddenGids);
        }));

        $data['count'] = count($data['list']);
        $data['pages'] = CUtil::getPaginationPages($data['count'], $pageSize);
        return [$status, $data];
    }


    /**
     * @throws Exception
     * @throws \RedisException
     */
    protected function _search_wares($keyword,$user_id, $page, $pageSize,$sortType, $isInternalPurchase = 0): array
    {
        list($status,$data) = SearchService::getInstance()->getWaresByName($keyword,$this->limit_search_nums,$page,$pageSize,by::GoodsMainModel()::SOURCE['POINTS'],$this->platformIds);
        if(!$status){
            return  [false,$data];
        }
        $data['pages'] = CUtil::getPaginationPages($data['count'], $pageSize);
        return [$status,$data];
    }
}
