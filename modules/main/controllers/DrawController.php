<?php


namespace app\modules\main\controllers;


use app\components\AppNRedisKeys;
use app\components\RateLimiter;
use app\constants\RespStatusCodeConst;
use app\exceptions\DrawActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\asset\models\PointConfigModel;
use app\modules\back\services\MemberActivityService;
use app\modules\goods\services\DrawActivityService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\DrawCustomFormRecordModel;
use OpenApi\Annotations as OA;

class DrawController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/draw/activity-list",
     *     summary="抽奖活动-活动详情",
     *     description="抽奖活动-活动详情",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionActivityList()
    {

        $post = \Yii::$app->request->post();

        $acId = intval($post['ac_id'] ?? 0);

        list($s, $ret) = DrawActivityService::getInstance()->getActivityDetail($acId);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/draw-times",
     *     summary="抽奖活动-获取抽奖次数",
     *     description="抽奖活动-获取抽奖次数",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionDrawTimes()
    {
        $post   = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);//活动ID

        $userId = $this->user_id;

        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            // 获取活动信息
            $activityDetail = byNew::DrawActivity()->getActivityDetail($acId);
            $dailyFreeTimes = intval($activityDetail['daily_free_times'] ?? 1);
            CUtil::json_response(1, 'ok',$dailyFreeTimes );
        }



        list($s, $ret) = DrawActivityService::getInstance()->getUserDrawTimes($userId, $acId);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/do-activity-task",
     *     summary="抽奖活动-每日赠送抽奖次数",
     *     description="抽奖活动-每日赠送抽奖次数",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="task_code", type="integer", default="", description="任务CODE")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionDoActivityTask()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再完成任务！');
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__);

        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        $post = \Yii::$app->request->post();

        list($s, $ret) = DrawActivityService::getInstance()->doActivityTask($user_id, $post);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/draw/do-draw-activity",
     *     summary="抽奖活动-抽奖",
     *     description="抽奖活动-抽奖",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDoDrawActivity()
    {
        // 1、用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        //校验活动时间
        // if(!ActivityConfigEnum::judgeActivityTime($user_id,'DRAW')){
        //     CUtil::json_response(-1, '活动已结束！');
        // }

        // 2、频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 3、活动ID
        $post = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);

        // 4、抽奖
        try {
            $ret = DrawActivityService::getInstance()->doDrawActivity($acId, $user_id);
            CUtil::json_response(1, 'ok', $ret);
        } catch (DrawActivityException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::json_response(-1, '抽奖失败');
            // 记录日志
            CUtil::debug("抽奖失败异常信息：file:{$e->getFile()}, err line:{$e->getLine()}, err message:{$e->getMessage()}, err code:{$e->getCode()}", "err.draw.activity");
        }
    }

    /**
     * @OA\Post(
     *     path="/main/draw/draw-record",
     *     summary="抽奖活动-中奖记录",
     *     description="抽奖活动-中奖记录",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
     *                         @OA\Property(property="page", type="integer", default="1", description="第几页"),
     *                         @OA\Property(property="page_size", type="integer", default="20", description="每一页数据量")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/DrawRecordDetail"), description="中奖记录")
     *         )
     *     )
     * )
     */
    public function actionDrawRecord()
    {
        // 1. 公共校验：用户ID
        $userId = $this->user_id;
        // 校验规则：长度不超过11位 + 是纯数字（uint）
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再查看中奖记录！');
        }
        // 2. 解析：请求参数（活动ID、分页、类型）
        $post = \Yii::$app->request->post();
        // 活动ID：必传，为空返回空列表
        $acId = intval($post['ac_id'] ?? 0);
        if (empty($acId)) {
            CUtil::json_response(1, 'ok', []);
        }
        // 分页参数：默认1页20条
        $page     = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);
        // 类型参数：默认空
        $type = $post['type'] ?? '';
        // 3. 业务查询：抽奖记录
        $ret = DrawActivityService::getInstance()->getDrawRecord($acId, $userId, $page, $pageSize, $type);
        // 4. 公共处理：列表数据二次加工
        $processedList = DrawActivityService::getInstance()->processDrawRecordList($ret['list']);
        // 5. 响应：返回纯列表（原接口逻辑）
        CUtil::json_response(1, 'ok', $processedList);
    }

    /**
     * 中奖记录接口V2（原actionDrawRecordV2） 处理分页
     */
    public function actionDrawRecordV2()
    {
        // 1. 公共校验：用户ID
        $userId = $this->user_id;
        // 校验规则：长度不超过11位 + 是纯数字（uint）
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再查看中奖记录！');
        }
        // 2. 解析：请求参数（活动ID、分页、类型）
        $post = \Yii::$app->request->post();
        // 活动ID：必传，为空返回空列表
        $acId = intval($post['ac_id'] ?? 0);
        if (empty($acId)) {
            CUtil::json_response(1, 'ok', []);
        }
        // 分页参数：默认1页20条
        $page     = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);
        // 类型参数：默认空
        $type = $post['type'] ?? '';
        // 3. 业务查询：抽奖记录
        $ret = DrawActivityService::getInstance()->getDrawRecord($acId, $userId, $page, $pageSize, $type);
        // 4. 复用公共方法：列表数据二次加工
        $ret['list'] = DrawActivityService::getInstance()->processDrawRecordList($ret['list']);
        // 5. 响应：返回含分页等信息的完整结构（V2接口逻辑）
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/draw/task-list",
     *     summary="抽奖活动-任务列表",
     *     description="抽奖活动-任务列表",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="任务CODE")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionTaskList()
    {
        $post = \Yii::$app->request->post();

        $acId = intval($post['ac_id'] ?? 0);

        if (empty($acId)) {
            CUtil::json_response(-1, '活动ID有误');
        }

        $userId = $this->user_id;

        list($s, $ret) = DrawActivityService::getInstance()->getTaskList($userId, $acId);

        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', $ret);
    }


    public function actionDoNewDrawActivity()
    {
        // 1、用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        // 2、频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(1, '频繁请求，请稍候！', [
                    'code'    => 1010,
                    'message' => '频繁请求，请稍候！',
                    'result'  => [],
            ]);
        }

        // 3、活动ID
        $post = \Yii::$app->request->post();
        $acRelationId = intval($post['ac_id'] ?? 0);

        // 4、抽奖
        $ret = DrawActivityService::getInstance()->doNewDrawActivity($acRelationId, intval($user_id));
        if ($ret['status'] != 1) {
            CUtil::json_response(-1, $ret['message']);
        }
        CUtil::json_response(1, $ret['message'], [
                'code'    => $ret['code'],
                'message' => $ret['message'],
                'result'  => $ret['data'],
        ]);
    }


    public function actionStock()
    {
        $acRelationId = \Yii::$app->request->post('ac_id',0);
        if (empty($acRelationId)){
            CUtil::json_response(-1,"活动有误，请检查~");
        }

        $key = AppNRedisKeys::envelopeActivity($acRelationId);

        $redis = by::redis();  // 修正组件调用方式

        // 事务开始：确保活动校验和奖品抽取的原子性

        // 检查活动库存
        $listLength = $redis->lLen($key);

        // 校验活动库存
        if ($listLength <= 0) {
            CUtil::json_response(1,"奖品已抽完",[
                    "code"=>2,//没库存
            ]);

        }
        CUtil::json_response(1,"ok",[
                "code"=>1,//有库存
        ]);
    }

    public function actionNewDrawRecord()
    {
        // 用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再查看中奖记录！');
        }

        // 活动ID
        $post         = \Yii::$app->request->post();
        $acRelationId = intval($post['ac_id'] ?? 0);
        if (empty($acRelationId)) {
            CUtil::json_response(1, 'ok', []);
        }

        // 分页
        $page     = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);

        // 增加缓存
        $redis    = by::redis();
        $redisKey = AppNRedisKeys::newDrawActivityRecord($acRelationId);
        $subKey   = CUtil::getNewAllParams("users", $user_id);

        // 如果缓存中有数据，直接返回
        $jsonResponse = $redis->hget($redisKey, $subKey);

        if (!empty($jsonResponse)) {
            $data = json_decode($jsonResponse, true);
            CUtil::json_response(1, 'ok', $data);
        }

        // 查询结果
        $ret = DrawActivityService::getInstance()->getEnvelopeRecord($acRelationId, $user_id, $page, $pageSize);
        // 避免频繁查询数据库
        $redis->hset($redisKey, $subKey, json_encode($ret, JSON_UNESCAPED_UNICODE)); // 缓存1天
        CUtil::json_response(1, 'ok', $ret);
    }


    public function actionCheckNewUser()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再查看是否新用户！');
        }

        // 检查用户是否新用户
        list($status, $message, $data) = DrawActivityService::getInstance()->isNewUser($user_id);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $message, $data);
    }
    
    /**
     * @OA\Post(
     *     path="/main/draw/gold-config",
     *     summary="抽奖活动-获取金币配置",
     *     description="抽奖活动-获取金币配置",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionGoldConfig()
    {
        $post = \Yii::$app->request->post();
        // $acId = intval($post['ac_id'] ?? 0);
        // if (empty($acId)) {
        //     CUtil::json_response(-1, '缺少活动ID');
        // }

        try {
            $config1 = DrawActivityService::getInstance()->getDrawConfig();
            $config2 = DrawActivityService::getInstance()->getDrawUnlockConfig();
            
            // $memberActivity = MemberActivityService::getInstance()->getDetail($acId, true);
            // $memberActivityRelation = $memberActivity['modules'] ?? [];
            // if (empty($memberActivityRelation)) {
            //     CUtil::json_response(-1, '活动模块未配置');
            // }
            // $activityRelation = $memberActivityRelation[0] ?? [];
            // $drawActivityTaskList = $activityRelation['module_data']['draw_task'][0] ?? [];
            // $drawActivityPrizeList = $activityRelation['module_data']['draw_prize'] ?? [];
            // $drawActivityId = $drawActivityTaskList['draw_activity_id'] ?? 0;
            // if (empty($drawActivityId)) {
            //     CUtil::json_response(-1, '抽奖活动模块未配置');
            // }
            //
            // $everytime_consume_gold = empty($drawActivityTaskList) ? 0 : $drawActivityTaskList['consume_type'] == 2 ? ($drawActivityTaskList['consume_gold'] ?? 0) : 0;
            
            $data = [];
            $data['gold_conversion'] = (new PointConfigModel())->getGoldConversion();
            $data['exchange_gold_rate'] = (new PointConfigModel())->getExchangeGoldRate();
            // $data['everytime_consume_gold'] = $everytime_consume_gold;
            foreach ($config1 as $k => $v) {
                if ($k == 'exchange_gold_list') {
                    $data[$k] = explode(',', (string) $v['value']);
                } else {
                    $data[$k] = (int) ($v['value'] ?? 0);
                }
            }
            
            // foreach ($config2 as $v) {
            //     $data['unlock_path'][] = $v;
            // }
            
            // $data['draw_activity_id'] = $drawActivityId;
            // $data['draw_prize_list'] = array_map(function ($v) {
            //     return [
            //         'prize_id' => $v['prize_id'],
            //         'prize_name' => $v['prize_name'],
            //         'prize_image' => $v['prize_image'],
            //     ];
            // }, $drawActivityPrizeList);
            
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
        } catch (\Throwable $e) {
            CUtil::debug("获取金币配置异常：{$e->getMessage()}", "err.gold.draw.config");
            CUtil::json_response(-1, '获取金币配置异常');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/draw/do-gold-draw",
     *     summary="抽奖活动-消费金抽奖",
     *     description="抽奖活动-消费金抽奖",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDoGoldDraw()
    {
        // 1、用户ID
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        // 2、频率限制：3秒钟访问一次
        $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id);
        $rateLimiter = new RateLimiter();
        $isAllowed   = $rateLimiter->checkRateLimit($unique_key, $user_id, 1, 1);

        if (! $isAllowed) {
            CUtil::json_response(-1, '你点太快啦！请稍后再试~');
        }

        // 3、活动ID
        $post = \Yii::$app->request->post();
        $acId = intval($post['ac_id'] ?? 0);
        
        // 4、抽奖
        try {
            $ret = DrawActivityService::getInstance()->doGoldDraw($acId, $user_id);
            CUtil::json_response(1, 'ok', $ret);
        } catch (DrawActivityException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Throwable $e) {
            // 记录日志
            CUtil::debug("金币抽奖异常：{$e->getMessage()}", "err.gold.draw.activity");
            CUtil::json_response(-1, '抽奖失败');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/draw/gold-exchange-point",
     *     summary="抽奖活动-金币兑换积分",
     *     description="抽奖活动-金币兑换积分",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="gold", type="integer", default="", description="金币数值"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionGoldExchangePoint()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再抽奖！');
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id);
        $rateLimiter = new RateLimiter();
        $isAllowed   = $rateLimiter->checkRateLimit($unique_key, $user_id, 1, 1);
        
        if (! $isAllowed) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        $post = \Yii::$app->request->post();
        $use_gold = intval($post['use_gold'] ?? 0);

        try {
            DrawActivityService::getInstance()->goldExchangePoint($user_id, $use_gold);
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '兑换成功');
        } catch (DrawActivityException $e) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $e->getMessage());
        } catch (\Throwable $e) {
            CUtil::debug('用户金币兑换失败: ' . $e->getMessage(), 'err.goldExchangePoint.goldExchangePoint');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '兑换失败，接口异常');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/draw/submit-gold-draw",
     *     summary="抽奖活动-提交中奖信息",
     *     description="抽奖活动-提交中奖信息",
     *     tags={"抽奖活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="gold", type="integer", default="", description="金币数值"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/DoDrawActivity",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSubmitGoldDraw()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再提交！');
        }
        
        $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id);
        $rateLimiter = new RateLimiter();
        $isAllowed   = $rateLimiter->checkRateLimit($unique_key, $user_id, 1, 3);
        
        if (! $isAllowed) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        $post = \Yii::$app->request->post();
        $post['user_id'] = $user_id;

        try {
            list($status, $msg) = DrawActivityService::getInstance()->submitGoldDraw($post);

            if (! $status) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $msg);
            }

            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '提交成功');
        } catch (\Throwable $e) {
            CUtil::debug('提交中奖信息失败: ' . $e->getMessage(), 'err.SubmitGoldDraw');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '提交失败');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/draw/draw-record-roll",
     *     summary="获取中奖飘屏",
     *     description="获取中奖飘屏",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *         )
     *     )
     * )
     */
    public function actionDrawRecordRoll()
    {
        try {
            $post = \Yii::$app->request->post();

            $model = byNew::DrawActivityPrizeRecord();
            $res = $model->getDrawRecordRoll(['draw_activity_id' => (int) ($post['draw_activity_id'] ?? 0), 'prize_type' => [2,3,4,5,6,8]]);
            $data = [];
            foreach ($res as $k => $v) {
                $user_info = by::users()->getOneByUid($v['user_id']);
                $data[$k]['nick'] = CUtil::truncateString($user_info['nick'] ?? '', 2, '**');
                $data[$k]['avatar'] = CUtil::avatar($user_info);
                $data[$k]['prize_name'] = $v['prize_name'] ?? '';
            }

            CUtil::json_response(1, 'ok', $data);
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取中奖飘屏失败：%s', $e->getMessage()), 'err.DrawRecordRoll');
            CUtil::json_response(-1, '获取中奖飘屏失败');
        }
    }
}
