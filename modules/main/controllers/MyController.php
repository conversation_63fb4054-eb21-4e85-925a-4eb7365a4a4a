<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2022/2/14
 * Time: 17:01
 */
namespace app\modules\main\controllers;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\EventMsg;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\GuideService;
use app\modules\main\services\TryOrderService;
use app\modules\main\services\UserBindService;
use app\modules\main\services\WaresActivityService;
use app\modules\main\services\UserEmployeeService;
use RedisException;
use Yii;
use yii\db\Exception;

class MyController extends CommController {


    /**
     * @throws Exception
     * 增删改收货地址
     */
    public function actionOptAddress()
    {
        $post = Yii::$app->request->post();
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        list($status, $msg) = by::Address()->SaveLog($this->user_id, $post);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok', $msg);
    }

    /**
     * @throws Exception
     * 删除收货地址
     */
    public function actionDelAddress()
    {
        $post   = Yii::$app->request->post();
        $id     = $post['id'] ?? 0;

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        list($status, $msg) = by::Address()->Del($this->user_id, $id);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');

    }

    /**
     * @throws Exception
     * 删除收货地址
     */
    public function actionDelAddressV2()
    {
        $post   = Yii::$app->request->post();
        $id     = $post['id'] ?? 0;

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        list($status, $msg) = by::Address()->DelAddress($this->user_id, $id);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');

    }

    /**
     * @throws Exception
     * 收获地址列表
     */
    public function actionAddress()
    {
        $post      = Yii::$app->request->post();
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        $opt       = $post['opt'] ?? 0; //0:全部 1:单个
        $id        = $post['id'] ?? 0;
        $page      = $post['page'] ?? 1;
        $page_size = 50;
        $is_del    = by::Address()::IS_DEL['no']; //未删除的地址
        $return    = ['list' => [], 'pages' => 0];
        if ($opt) {
            if ($id) { // 获取指定 id 的地址
                $return['list'] = by::Address()->GetOneAddress($this->user_id, $id);
            } else {   // 获取默认
                $return['list'] = by::Address()->GetDefaultAddress($this->user_id, $is_del);
            }
        } else {
            $rets = by::Address()->GetList($this->user_id, $page, $page_size, $is_del);
            foreach ($rets as $ret) {
                $return['list'][] = by::Address()->GetOneAddress($this->user_id, $ret['id']);
            }
            $count           = by::Address()->GetListCount($this->user_id);
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }
        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 积分记录
     */
    public function actionPointLog(){
        $post            = Yii::$app->request->post();
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        $type            = $post['type']         ?? 1;
        $page            = $post['page']         ?? 1;
        $page_size       = 20;
        $count           = by::pointLog()->getCount($this->user_id,$type);
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $list            = by::pointLog()->getList($this->user_id,$type,$page,$page_size);
        $return['point'] = (string)by::point()->get($this->user_id);
        $return['money'] = by::point()->convert($return['point']);

        foreach ($list as $v){
            $return['list'][] = by::pointLog()->getInfo($v['user_id'],$v['id']);
        }

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 个人中心信息页
     * @throws Exception
     */
    public function actionInfo()
    {
        if (empty($this->user_id) || strlen($this->user_id) > 11 || $this->user_id=="undefined") {
            CUtil::json_response(1, 'OK', []);
        }

        $user_info       = $this->userModel->getOneByUid($this->user_id);
        $data['user_id'] = $this->user_id;
        $data['nick']    = $user_info['nick']   ?? ''; //用户昵称
        $data['nick_name']    = $user_info['nick']   ?? ''; //用户昵称
        $userInfo = by::Phone()->getDataByUserId($this->user_id);
        $data['uid'] = $userInfo['uid'] ?? '';
        $data['phone'] =  by::Phone()->GetPhoneByUid($this->user_id); //用户手机

        // 用户头像
        if (empty($user_info['avatar'])) {
            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);
            $avatar = $config['cdnAddr'] . '/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png';
        } else {
            $avatar = $user_info['avatar'];
        }
        $data['avatar'] = $avatar; //用户头像

        //获取积分数量
        $data['point']  = empty($this->userAuth)?0:by::point()->get($this->user_id);

        //获取优惠劵
        $data['coupon'] = empty($this->userAuth) ? 0 : by::userCard()->GetEffectCouponsCount($this->user_id);

        $order_status   = by::Omain()::ORDER_STATUS;
        $mOuser         = by::Ouser();

        // 订单状态
        $data['wait_pay']     = empty($this->userAuth) ? 0 : $mOuser->GetListCount($this->user_id, $order_status['WAIT_PAY'], '', $this->platformSource);            //待付款
        $data['wait_send']    = empty($this->userAuth) ? 0 : $mOuser->GetListCount($this->user_id, $order_status['WAIT_SEND'], '', $this->platformSource);           //待发货
        $data['wait_receive'] = empty($this->userAuth) ? 0 : $mOuser->GetListCount($this->user_id, $order_status['WAIT_RECEIVE'], '', $this->platformSource);        //待收货
        $data['finished']     = empty($this->userAuth) ? 0 : $mOuser->GetListCount($this->user_id, $order_status['FINISHED'], '', $this->platformSource);            //已完成
        $data['refunding']    = empty($this->userAuth) ? 0 : by::Orefund()->GetListCount($this->user_id, by::Orefund()::STATUS['audit'], $this->platformSource);             //申请退款

        //获取弹窗次数相关数据
        $popup_data                 = $this->userRecommendModel->getPopupCount($this->user_id);
        $data['reg_popup_count']    = $popup_data['reg_popup_count']    ?? 0; //注册奖励需弹窗次数
        $data['coupon_popup_count'] = $popup_data['coupon_popup_count'] ?? 0; //下单优惠劵奖励需弹窗次数
        $data['point_popup_count']  = $popup_data['point_popup_count']  ?? 0; //下单积分奖励需弹窗次数

        //奖励积分
        $data['reg_reward_point'] = $data['order_reward_point'] = 0;
        $ac_model                 = by::ActivityConfigModel();
        $ac_info                  = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift']);
        if (!empty($ac_info)) {
            $type_model = by::Actype2();
            // 获取邀请人奖励配置
            list($status, $rewardData) = MemberCenter::factory()->ruleFineDetail('mall', 'dreame', 'invite_reg');
            if (! $status) {
                CUtil::debug('获取邀请人奖励失败：user_id = '.$this->user_id.' errmsg='.$rewardData,'warn.reward_user');
                CUtil::json_response(-1, '获取邀请人奖励失败');
            }
            
            $rewardPoint = $rewardData['gold'] ?? 10000;
            $data['reg_reward_point'] = $rewardPoint;     //注册奖励积分
            $data['order_reward_point'] = $type_model::ACTION['order']['reward_point']; //下单奖励积分
        }

        //是否首次登录弹窗
        $isNewGift = empty($user_info['is_new_gift'] ?? 0) ? 1 : 0;

        $loginModel = by::login();
        $r_key = $loginModel->__getH5RegisterTag($this->user_id);
        $data['isAppRegister'] = $loginModel->ifAppRegisterUser($isNewGift,$this->user_id,$r_key);

        // 获取追觅大使信息
        list($status, $res) = UserEmployeeService::getInstance()->getDetail($this->user_id);
        $data['is_employee'] = 0;
        $data['employee_type'] = 0;
        $data['employee_level'] = 0;

        if ($status){
            if ($res && $res['is_zmds'] == 1){
                $data['is_employee'] = 1;
                $data['employee_type'] = $res['type']??0;
                $data['employee_level'] = $res['level']??1;
            }
        }
        // 判断微笑大使，2025-07-31
        $data['is_wxds'] = 0;
        $wxds = byNew::UserSmileModel()->getInfo($this->user_id,'user_id');
        if ($wxds){
            $data['is_wxds'] = 1;
        }

        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @return void
     * 锁住APP用户弹窗
     */
    public function actionLockAppRegister()
    {
        $loginModel = by::login();
        $r_key = $loginModel->__getH5RegisterTag($this->user_id);
        $loginModel->setAppRegisterLock($r_key);
        CUtil::json_response(1, 'ok');
    }

    /**
     * 获取用户手机号码
     * @throws Exception
     */
    public function actionPhone(){
        $phone  = by::Phone()->GetPhoneByUid($this->user_id);
        CUtil::json_response(1, 'ok', ['phone'=>$phone]);
    }


    /**
     *
     * @OA\Post(
     *     path="/main/my/card",
     *     summary="我的-用户优惠券列表",
     *     description="我的-用户优惠券列表",
     *     tags={"我的"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"tab_type"},
     *          @OA\Property(property="tab_type", type="integer", default="1", description="1:待使用;2:已使用;3:已过期"),
     *          @OA\Property(property="is_internal_purchase", type="string", default="", description="是否是内购")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/CardList",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCard()
    {
        $post            = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $data = [];
        if($this->userAuth){
            list($status,$data) = by::userCard()->getList($this->user_id,by::userCard()::SOURCE['INDEX'],$post);
            if (!$status){
                CUtil::json_response(0, '获取失败');
            }
        }
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     *
     * @OA\Post(
     *     path="/main/my/sinfo",
     *     summary="我的-导购个人信息页",
     *     description="我的-导购个人信息页",
     *     tags={"我的"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *               @OA\Property(property="avatar", type="string", default="", description="用户头像")
     *          )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSinfo()
    {
        $avatar = Yii::$app->request->post('avatar', '');

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }
        $s_info             = by::guide()->getGuideInfo($this->user_id);
        if (empty($s_info)) {
            CUtil::json_response(-1, '您还不是导购！');
        }

        by::guide()->upWorkData($this->user_id, $avatar);

        $user_info          = by::users()->getOneByUid($this->user_id);
        $data['nick']       = $user_info['nick'] ?? '';        //用户昵称
        $data['avatar']     = $user_info['avatar'] ?? '';      //用户头像
        $data['store']      = $s_info['store'];                //店铺
        $data['job_no']     = $s_info['job_no'] ?? '';                //员工工号

        $mOsource           = by::Osource();
        $mOrefund           = by::Orefund();

        //今日订单金额
        $d_price            = $mOsource->GetListSum($this->user_id, $mOsource::T_TYPE['DAY']);
        $data['d_price']    = by::Gtype0()->totalFee($d_price, 1);

        //今日订单数量
        $data['d_count']    = $mOsource->GetListCount($this->user_id, $mOsource::T_TYPE['DAY'], -1, '', false);

        //今日退款金额
        $data['d_r_price'] = $mOrefund->getSrList($this->user_id, $mOsource::T_TYPE['DAY'], by::Orefund()::SOURCE['CENTER'])['total']['price'];

        //本月订单金额
        $m_price            = $mOsource->GetListSum($this->user_id, $mOsource::T_TYPE['MONTH']);
        $data['m_price']    = by::Gtype0()->totalFee($m_price, 1);

        //本月订单数量
        $data['m_count']    = $mOsource->GetListCount($this->user_id, $mOsource::T_TYPE['MONTH'], -1, '', false);

        //本月退款金额
        $data['m_r_price'] = $mOrefund->getSrList($this->user_id, $mOsource::T_TYPE['MONTH'], by::Orefund()::SOURCE['CENTER'])['total']['price'];

        CUtil::json_response(1, 'ok', $data);
    }


    /**
     *
     * @OA\Post(
     *     path="/main/my/sjob",
     *     summary="我的-导购员工号设置",
     *     description="我的-设置导购员工号",
     *     tags={"我的"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"job_no"},
     *          @OA\Property(property="job_no", type="integer", default="", description="员工工号"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSjob()
    {
        $post   = Yii::$app->request->post();
        $job_no = $post['job_no'] ?? '';
        if (empty($job_no) || !preg_match("/^[a-zA-Z0-9]{1,9}$/", $job_no)) {
            CUtil::json_response(-1, '工号不符合规则！');
        }
        $userId = $this->user_id;
        if (empty(intval($userId)) || strlen($userId) > 11) {
            CUtil::json_response(-1, '用户不符合规则！');
        }

        list($s,$data) = GuideService::getInstance()->SaveGuideJobNo($userId,$job_no);
        if(!$s){
            CUtil::json_response(-1, '工号保存失败！');
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * 切换设置默认收货地址
     */
    public function actionSetDefaultAddress()
    {
        $add_id = Yii::$app->request->post('add_id', 0);
        if(empty($add_id)){
            CUtil::json_response(-1, '请选择收货地址');
        }
        // 验证数据
        $add_data = by::Address()->GetOneAddress($this->user_id, $add_id);
        if(empty($add_data)){
            CUtil::json_response(-1010, '地址数据错误');
        }
        list($status, $msg) = by::Address()->setDefaultAddress($this->user_id, $add_id);

        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    public function actionCrmPointLog(){

        $post      = Yii::$app->request->post();
        $page      = CUtil::uint($post['page'] ?? 1);
        $page_size = 20;
        $type      = CUtil::uint($post['type'] ?? 0);
        list($count, $return['list']) = by::pointLog()->crmLog($this->user_id, $page, $page_size, $type);

        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $return['point'] = (string)by::point()->get($this->user_id);
        $return['money'] = by::point()->convert($return['point']);


        CUtil::json_response(1,'ok', $return);
    }

    /**
     * @throws Exception
     * 推荐有礼页
     */
    public function actionRecommendGift()
    {
        $user_info      = by::users()->getOneByUid($this->user_id);
        $data['nick']   = $user_info['nick']   ?? ''; //用户昵称
        $data['avatar'] = $user_info['avatar'] ?? ''; //用户头像

        //注册奖励积分
        $data['reg_reward_point'] = $user_info['reg_reward_point'] ?? '';

        //到账优惠劵数量
        $reward_type = by::activityConfigModel()::REWARD_TYPE['coupon'];
        $data['account'] = by::OsourceR()->getRewardNum($this->user_id, $reward_type, by::OsourceR()::REWARD_STATUS['RECEIVED']);

        //预计到账优惠劵数量
        $data['predict_account'] = by::OsourceR()->getRewardNum($this->user_id, $reward_type, by::OsourceR()::REWARD_STATUS['PREDICT']);

        //规则说明
        $ac_model             = by::activityConfigModel();
        $data['rule_note']    = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift'])['rule_note']    ?? '';

        //页面海报
        $data['poster_image'] = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift'])['poster_image'] ?? '';

        //分享海报
        $data['share_image']  = $ac_model->getInfoByType($ac_model::GRANT_TYPE['recommend_gift'])['share_image'] ?? '';

        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @throws Exception
     * 累计邀请注册页
     */
    public function actionInviteReg()
    {
        $list = by::UserExtend()->getInviteRegList($this->user_id);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 累计邀请购买页
     */
    public function actionInviteBuy()
    {
        $year = Yii::$app->request->post('year', '');

        $list = by::OsourceR()->getList($this->user_id, $year, 1, -1, 0, 100);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 分享海报二维码
     */
    public function actionShareCode()
    {
        $r_code = Yii::$app->request->post('r_code', '');
        $gid    = Yii::$app->request->post('gid', '');
        $type   = Yii::$app->request->post('type', 'goods');

        list($s, $ret) = by::OsourceR()->shareRcode($r_code, $gid, $type);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        //获取商品详情信息
        $aGoods = by::Gmain()->GetAllOneByGid($gid);
        if (!empty($aGoods)) {
            // $lockCrmSend = CUtil::getConfig('lockCrmSend', 'member', MAIN_MODULE) ?? false;
            // $lockCrmSend && list($s, $data) = by::model("EventCenterModel", MAIN_MODULE)->shareGoods($this->user_id);
            list($s, $data) = by::model("EventCenterModel", MAIN_MODULE)->shareGoods($this->user_id);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/my/try-list",
     *     summary="我的试用列表",
     *     description="我的试用列表",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"try_status"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="try_status", type="integer", default="", description="试用状态 1:已下单 2:体验中 3:体验完成 4:申请失效"),
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionTryList()
    {
        $post      = Yii::$app->request->post();
        $userId    = $this->user_id;
        $tryStatus = $post['try_status'] ?? -1;
        $page      = $post['page'] ?? 1;
        $pageSize  = $post['page_size'] ?? 20;
        list($s, $ret) = TryOrderService::getInstance()->GetTryOrderList($userId, $tryStatus);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/my/try-info",
     *     summary="我的试用详情",
     *     description="我的试用详情",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"try_status", "ac_id", "order_no"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="try_status", type="integer", default="", description="试用状态 1:已下单 2:体验中 3:体验完成 4:申请失效"),
     *                          @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
     *                          @OA\Property(property="order_no", type="integer", default="", description="订单号"),
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionTryInfo()
    {
        $post      = Yii::$app->request->post();
        $acId      = $post['ac_id'] ?? 0;
        $orderNo   = $post['order_no'] ?? '';
        $tryStatus = $post['try_status'] ?? -1;
        $userId    = $this->user_id;

        // 获取尝试信息
        list($status, $data) = TryOrderService::getInstance()->GetTryInfo($acId, $userId);

        // 如果获取尝试信息失败，则返回错误响应
        if (!$status) {
            CUtil::json_response(-1, $data);
        }

        // 设置额外的数据信息
        $data['try_status'] = $tryStatus;
        $data['order_no']   = $orderNo;

        // 返回成功响应
        CUtil::json_response(1, 'ok', $data);
    }

    public function actionShopMoneyLog(){
        $post = Yii::$app->request->post();
        $type = $post['type'] ?? 1;
        $money_type = $post['money_type'] ?? 1;
        $list = byNew::UserShopMoneyRecordModel()->getListByUserId($this->user_id,$type,$money_type, $post['page']??1, $post['page_size']??10);
        CUtil::json_response(1, 'ok', $list);
    }

    public function actionShopMoneyBalance(){
        $post = Yii::$app->request->post();
        $money_type = $post['money_type'] ?? 1;
        $list = byNew::UserShopMoneyModel()->getInfoByUserId($this->user_id,$money_type,2);
        CUtil::json_response(1, 'ok', $list);
    }

    // 浏览获取积分
    public function actionBrowseAddScore(){
        $userId = $this->user_id;
        // 增加一下用户验证
        $uid = by::Phone()->getUidByUserId($this->user_id);
        if (!$uid) {
            CUtil::json_response(-1, '用户不存在', []);
        }
        $redisKey = "user_browse_add_score|".$userId;
        // 获取缓存中的数据
        $redis = by::redis('core');
        $num = $redis->get($redisKey);
        // 过期时间,零点过期
        $end_time = strtotime(date('Y-m-d 23:59:59'));
        $expire = $end_time - time();

        // 如果num存在，怎校验是否大于10
        if ($num){
            if ($num >= 10){
                // 大于10则不增加积分
                CUtil::json_response(1, 'ok', 0);
            }else{
                $num = $num + 1;
                EventMsg::factory()->run('browseAddScore', ['user_id' => $userId]);
                $redis->set($redisKey, $num, ['EX' => $expire]);
            }

        }else{
            // 不存在则记录num值为1
            $num = 1;
            EventMsg::factory()->run('browseAddScore', ['user_id' => $userId]);
            $redis->set($redisKey, $num, ['EX' => $expire]);
        }
        CUtil::json_response(1, 'ok', 1);
    }

    public function actionActivityEvent(){
        $post = Yii::$app->request->post();
        $inviter_id = $post['inviter_id'] ?? 0;
        $type = $post['type'] ?? '';
        $activity_id = $post['activity_id'] ?? 0;
        if (empty($inviter_id) || empty($type)){
            CUtil::json_response(-1, '参数错误', []);
        }
        $userId = $this->user_id;
        switch ($type) {
            case 'partner':
                $isNew = $post['is_new'] ?? 0;
                $res = UserBindService::getInstance()->newBind($this->user_id,$inviter_id, $isNew);
                break;
            default:
                CUtil::json_response(-1, '不存在的类型', []);
                break;
        }
        CUtil::json_response(1, 'ok', []);

    }

}
