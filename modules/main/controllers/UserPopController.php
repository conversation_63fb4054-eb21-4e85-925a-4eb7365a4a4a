<?php

namespace app\modules\main\controllers;

use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\CUtil;
use app\models\byNew;
use app\modules\main\forms\popup\UserPopupGetForm;
use app\modules\main\forms\popup\UserPopupMarkForm;
use app\modules\main\models\UserPopupModel;
use Yii;

/**
 * 用户弹窗控制器
 */
class UserPopController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/user-pop/get-unshown",
     *     summary="根据类型获取未弹的弹窗",
     *     description="根据弹窗类型获取用户未展示的弹窗，支持可选的自动标记为已读功能。当popup_type不传或传0时，返回全部消息类型",
     *     tags={"用户弹窗"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="popup_type", type="integer", description="弹窗类型（可选：0或不传=全部类型，1=拼团成为团长，2=会员商城邀请好友,3=邀请用户购买反积分）", example=1),
     *                 @OA\Property(property="auto_mark_read", type="integer", description="是否自动标记为已读（0=否，1=是），默认0", example=0)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="count", type="integer", description="未展示弹窗数量"),
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="弹窗记录ID"),
     *                         @OA\Property(property="user_id", type="integer", description="用户ID"),
     *                         @OA\Property(property="popup_type", type="integer", description="弹窗类型"),
     *                         @OA\Property(property="is_displayed", type="integer", description="是否已展示（0=否，1=是）"),
     *                         @OA\Property(property="remark", type="string", description="备注信息"),
     *                         @OA\Property(property="ctime", type="integer", description="创建时间"),
     *                         @OA\Property(property="utime", type="integer", description="更新时间")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionGetUnshown()
    {
        try {
            // 用户权限检查
            if (strlen($this->user_id) > 11 || empty($this->user_id)) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请先授权登录！');
            }

            // 参数验证
            $form = new UserPopupGetForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('获取弹窗参数验证失败: ' . json_encode($errors), 'user_popup_get_error');
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, reset($errors));
            }

            // 防重复请求
            $unique_key = CUtil::getAllParams(__FUNCTION__, $this->user_id, $form->popup_type);
            list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
            if (!$anti) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, "请求频繁，请稍后再试");
            }

            // 获取弹窗数据
            $userPopupModel = byNew::UserPopupModel();
            $popupType = $form->getSafePopupType();
            $autoMarkRead = $form->getSafeAutoMarkRead();

            $popupList = $userPopupModel->getUnshownPopupsByType(
                (int)$this->user_id,
                $popupType,
                $autoMarkRead
            );

            // 格式化返回数据
            $formattedList = [];
            foreach ($popupList as $popup) {
                $formattedList[] = [
                    'id' => (int)$popup['id'],
                    'user_id' => (int)$popup['user_id'],
                    'popup_type' => (int)$popup['popup_type'],
                    'is_displayed' => (int)$popup['is_displayed'],
                    'remark' => $popup['remark'] ?: '',
                    'ctime' => (int)$popup['ctime'],
                    'utime' => (int)$popup['utime']
                ];
            }

            $data = [
                'count' => count($formattedList),
                'list' => $formattedList
            ];

            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', $data);

        } catch (\Exception $e) {
            CUtil::debug('获取弹窗异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'user_popup_get_error');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '系统异常，请稍后重试');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/user-pop/mark-displayed",
     *     summary="手动标记弹窗为已展示",
     *     description="根据弹窗类型手动标记用户的弹窗为已展示状态",
     *     tags={"用户弹窗"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"popup_type"},
     *                 @OA\Property(property="popup_type", type="integer", description="弹窗类型（1=拼团成为团长，2=会员商城邀请好友,3=邀请用户购买反积分）", example=1),
     *                 @OA\Property(property="remark", type="string", description="备注信息（可选）", example="用户主动关闭")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="success", type="boolean", description="操作是否成功"),
     *                 @OA\Property(property="affected_rows", type="integer", description="影响的行数")
     *             )
     *         )
     *     )
     * )
     */
    public function actionMarkDisplayed()
    {
        try {
            // 用户权限检查
            if (strlen($this->user_id) > 11 || empty($this->user_id)) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '请先授权登录！');
            }

            // 参数验证
            $form = new UserPopupMarkForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('标记弹窗参数验证失败: ' . json_encode($errors), 'user_popup_mark_error');
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, reset($errors));
            }

            // 防重复请求
            $unique_key = CUtil::getAllParams(__FUNCTION__, $this->user_id, $form->popup_type);
            list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
            if (!$anti) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, "请求频繁，请稍后再试");
            }

            // 标记弹窗为已展示
            $userPopupModel = byNew::UserPopupModel();
            $popupType = $form->getSafePopupType();
            $remark = $form->getSafeRemark();

            $result = $userPopupModel->markPopupAsDisplayed(
                (int)$this->user_id,
                $popupType,
                $remark
            );

            $data = [
                'success' => $result,
                'affected_rows' => $result ? 1 : 0
            ];

            if ($result) {
                CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '标记成功', $data);
            } else {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '标记失败，可能没有未展示的弹窗', $data);
            }

        } catch (\Exception $e) {
            CUtil::debug('标记弹窗异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'user_popup_mark_error');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '系统异常，请稍后重试');
        }
    }

    /**
     * @OA\Get(
     *     path="/main/user-pop/types",
     *     summary="获取支持的弹窗类型列表",
     *     description="获取系统支持的所有弹窗类型",
     *     tags={"用户弹窗"},
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="types",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="类型ID"),
     *                         @OA\Property(property="name", type="string", description="类型名称"),
     *                         @OA\Property(property="desc", type="string", description="类型描述")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionTypes()
    {
        $typeMap = [
            UserPopupModel::POPUP_TYPE['GROUP_PURCHASES'] => [
                'id' => UserPopupModel::POPUP_TYPE['GROUP_PURCHASES'],
                'name' => 'GROUP_PURCHASES',
                'desc' => '拼团-成为团长'
            ],
            UserPopupModel::POPUP_TYPE['MEMBER_MALL_INVITE'] => [
                'id' => UserPopupModel::POPUP_TYPE['MEMBER_MALL_INVITE'],
                'name' => 'MEMBER_MALL_INVITE',
                'desc' => '会员商城-邀请好友'
            ]
        ];

        $data = [
            'types' => array_values($typeMap)
        ];

        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'ok', $data);
    }
} 