<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/4/30
 * Time: 18:29
 */
namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use Yii;

class SpreadController extends CommController {

    public function actionGetSpreadData()
    {
        $post     = Yii::$app->request->post();
        $md5Scene = CUtil::removeXss($post['cps'] ?? '');
        if(empty($md5Scene)){
            CUtil::json_response(-1, 'cps 参数未传！');
        }

        list($s,$data) = by::spread()->getSpreadCode($md5Scene);
        if(!$s){
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, 'ok',$data);
    }
}
