<?php

namespace app\modules\main\controllers;

use app\components\AliYunCaptcha;
use app\models\by;
use app\models\CUtil;
use app\modules\main\forms\plumbing\ServiceOrderSaveForm;
use app\modules\main\services\PlumbingService;
use Yii;


/**
 * Class PlumbingController
 * @package app\modules\main\controllers
 * 上下水服务控制器
 */
class PlumbingActivityController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/plumbing-activity/service-order-save",
     *     summary="上门服务(不使用)",
     *     description="保存上门服务订单",
     *     tags={"活动上下水"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="jwtToken", type="string",description="身份标识"),
     *              @OA\Property(property="name",type="string",description="姓名"),
     *              @OA\Property(property="phone",type="intger",description="手机号"),
     *              @OA\Property(property="verify_code",type="intger",description="验证码"),
     *              @OA\Property(property="pid",type="intger",description="省份ID"),
     *              @OA\Property(property="cid",type="intger",description="城市ID"),
     *              @OA\Property(property="aid",type="intger",description="区/县ID"),
     *              @OA\Property(property="detail",type="string",description="地址详情"),
     *              @OA\Property(property="expect_time",type="string",description="期望上门时间"),
     *              @OA\Property(property="context_type",type="integer",description="家庭环境"),
     *              @OA\Property(property="sn_config",type="string",description="产品信息"),
     *              @OA\Property(property="product_type",type="integer",description="产品类型：1:扫地机 2:洗地机"),
     *              @OA\Property(property="sn",type="string",description="SN编码"),
     *              @OA\Property(property="type",type="integer",description="服务类型：3:上门服务"),
     *              @OA\Property(property="tenant",type="string",description="租户；默认：00000；mova：000002"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionServiceOrderSave()
    {
        // ***(不使用)***
        $ip = CUtil::get_client_ip();
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($ip, $unique_key, 1, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候~');
        }

        // 验证参数
        $form = new ServiceOrderSaveForm();
        $form->load(Yii::$app->request->post(), '');

        // 验证失败
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        // 提交订单
        list($status, $msg) = PlumbingService::getInstance()->saveServiceOrder($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok', $msg);
    }

    /**
     * @OA\Post(
     *     path="/main/plumbing-activity/send-code",
     *     summary="发送短信（有滑块验证）",
     *     description="发送短信（有滑块验证）",
     *     tags={"活动上下水"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="jwtToken", type="string",description="身份标识"),
     *              @OA\Property(property="phone",type="string",description="手机号"),
     *              @OA\Property(property="nc_token",type="string",description="nc_token 滑块必须数据"),
     *              @OA\Property(property="csessionid",type="string",description="csessionid 滑块必须数据"),
     *              @OA\Property(property="sig",type="string",description="sig 滑块必须数据"),
     *              @OA\Property(property="scene",type="string",description="scene 滑块必须数据"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionSendCode()
    {
        $post  = Yii::$app->request->post();
        $phone = $post['phone'] ?? '';
        if (empty($phone)) {
            CUtil::json_response(-1, "手机号必传！");
        }

        list($s, $m) = AliYunCaptcha::CaptchaVerify($post, 'mova_plumbing');
        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        list($status, $msg) = by::model("SmsModel", MAIN_MODULE)->smsSend($phone, "CODE");
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok');
    }


}
