<?php

namespace app\modules\main\controllers;


use app\components\Device;
use app\models\by;
use app\models\CUtil;


class DeviceController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/device/index",
     *     summary="设备控制器",
     *     description="设备控制器",
     *     tags={"设备调用"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/DeviceRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionIndex()
    {
        $post  = \Yii::$app->request->post();

        if (strlen($this->user_id) > 11) {
            CUtil::json_response(-1, "您尚未授权，请授权！");
        }

        $scene = $post['scene'] ?? '';
        if (empty($scene)) {
            CUtil::json_response(-1, "缺少参数");
        }

        $device_action = $this->_getDeviceMethod($scene);

        if (!$device_action) {
            CUtil::json_response(-1, "不支持的操作方式");
        }
        //调用相对应方法
        $this->$device_action($post);
    }

    private function _getDeviceMethod($scene)
    {
        $deviceControl = CUtil::getConfig('command', 'device', MAIN_MODULE);
        $method        = $deviceControl[$scene] ?? "";
        $method        = "_device_" . "{$method}";

        if (!method_exists($this, $method)) {
            return false;
        }
        return $method;
    }





    public function _device_list($post)
    {
        $offset       = $post['offset'] ?? '';
        $limit        = $post['limit'] ?? '';
        $sharedStatus = $post['sharedStatus'] ?? 1;
        $lang         = $post['lang'] ?? 'zh';
        $user_id      = $this->user_id;
        list($status, $ret) = Device::factory()->run('list_v2', ['user_id' => $user_id, 'sharedStatus' => $sharedStatus, 'lang' => $lang, 'offset' => $offset, 'limit' => $limit]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "操作成功", $ret);
    }


    public function _device_get_info($post)
    {
        $user_id = $this->user_id;
        $did     = $post['did'] ?? '';
        $offset  = $post['offset'] ?? '';
        $limit   = $post['limit'] ?? '';
        list($status, $ret) = Device::factory()->run('get_device_messages', ['user_id' => $user_id, 'did' => $did, 'offset' => $offset, 'limit' => $limit]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "操作成功", $ret);
    }


    public function _device_delete($post)
    {
        $user_id = $this->user_id;
        $msgIds  = $post['msgIds'] ?? '';
        list($status, $ret) = Device::factory()->run('delete_device_messages', ['user_id' => $user_id, 'msgIds' => $msgIds]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "操作成功", $ret);
    }

    public function _device_send_command($post)
    {
        $user_id = $this->user_id;
        $id      = $post['id'] ?? '';
        $did     = $post['did'] ?? '';
        $data    = json_decode($post['data'] ?? '', true);
        $bindId  = $post['bind_id'] ?? '';

        list($status, $ret) = Device::factory()->run('send_command', ['user_id' => $user_id, 'id' => $id, 'did' => $did, 'data' => $data, 'bindId' => $bindId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        if ($status === 80001) {
            CUtil::json_response($status, "设备可能不在线，指令发送超时。", $ret);
        }

        CUtil::json_response(1, "操作成功", $ret);
    }

    public function _device_mark_read($post)
    {

        $user_id  = $this->user_id;
        $deviceId = $post['deviceId'] ?? '';

        list($status, $ret) = Device::factory()->run('mark_read_by_deviceid', ['user_id' => $user_id, 'deviceId' => $deviceId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "操作成功", $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/device/get-file",
     *     summary="通过url获取文件",
     *     description="通过url获取文件",
     *     tags={"设备调用"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="url", type="string", default="", description="url")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionGetFile()
    {
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 2);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候~');
        }

        $url = \Yii::$app->request->post('url', '');

        if (empty($url)) {
            CUtil::json_response(-1, "参数错误");
        }

        $jsonData = file_get_contents($url);

        if ($jsonData === false) {
            CUtil::json_response(-1, "获取失败");
        }

        $decodedData = json_decode($jsonData, true);

        if ($decodedData === null) {
            CUtil::json_response(-1, "JSON 解析失败");
        }

        CUtil::json_response(1, "OK", $decodedData);
    }


}
