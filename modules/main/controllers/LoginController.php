<?php
/**
 * Created by Php<PERSON>torm.
 * User: Kevin
 * Date: 2018/5/9
 * Time: 17:28
 */
namespace app\modules\main\controllers;

use app\components\AdvAscribe;
use app\components\AppCRedisKeys;
use app\components\JwtTools;
use app\components\MemberCenter;
use app\components\WeWorkApp;
use app\constants\RespStatusCodeConst;
use app\jobs\UserSourceJob;
use app\models\by;
use app\models\byNew;
use app\models\CodeModel;
use app\models\CUtil;
use app\models\Response;
use app\modules\main\enums\user\UserTypeEnum;
use app\modules\main\services\DataService;
use app\modules\main\services\LoginService;
use app\modules\main\services\UserBindService;
use Yii;
use yii\db\Exception;

/**
 * Class LoginController
 * @package app\modules\step\controllers
 * 登陆系统
 */
class LoginController extends CommController
{

    public $userTmpInfo = [];
    public $user_type;
    public $openudid;
    public $code;
    public $jwtToken;
    public $authCode;

    CONST WEWORK_ERR  = -10;  //企业微信注册失败
    /**
     * @param int $user_type
     * @return string
     * 获得登陆方式
     */
    protected function _getLoginMethod(int $user_type = 0)
    {
        $loginControl   = CUtil::getConfig('loginControl', 'common', MAIN_MODULE);
        $method         = $loginControl[$user_type] ?? "";
        $method         = "_login_by_{$method}";
        if (!method_exists($this, $method)) {
            return false;
        }

        return $method;
    }

  /**
     * @OA\Post(
     *     path="/main/login/index",
     *     summary="统一登录",
     *     description="统一登录",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API，具体按情况提供"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="code", type="string",description="导购CODE"),
     *              @OA\Property(property="user_type", type="integer",description="1 微信小程序 99 游客 20 支付宝小程序 10 企业微信登录"),
     *              @OA\Property(property="openudid", type="string",description="openudid 微信小程序必填"),
     *              @OA\Property(property="source", type="integer",description="来源"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionIndex()
    {
        $post            = Yii::$app->request->post();
        $this->code      = $post['code']        ?? '';
        $this->user_type = $post['user_type']   ?? '';
        $this->openudid  = $post['openudid']    ?? '';
        $source          = $post['source']      ??  0;
        $employee_uid    = $post['employee_uid']?? '';

        if ($this->user_type == 20) {
            // 调用下面的controller
            $this->actionAlipayLogin();
            return;
        }

        //PC扫码登陆
        if ($this->user_type == 30) {
            // 调用下面的controller
            $this->actionAppLogin();
            return;
        }

        if ($this->code && empty($this->openudid)) {
            CUtil::json_response(-1, "缺少登陆参数");
        }
        $login_action = $this->_getLoginMethod($this->user_type);
        if (!$login_action) {
            CUtil::json_response(-1, "不支持的登陆方式");
        }

        // 防止多次请求
        $unique_key = __FUNCTION__ . "|{$this->openudid}|1";
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
        if (!$anti) {
            CUtil::json_response(-1, "登录操作频繁~");
        }

        $user_info = $this->$login_action($this->openudid, $this->user_type);
        //账号不存在则主动注册 并且是游客登录
        !$user_info && $this->loginType == $this->loginTypeEnum['TOURIST'] && $user_info = $this->_register();


        $this->_after_login($user_info, 1, $employee_uid);

        // 用户登录类型 1：会员 2：会员退出登录 3：游客
        $user_info['user_login_type'] = $this->_user_login;

        if ($user_info['user_id'] && strlen($user_info['user_id']) < 11) {
            // 登陆成功清除 退出标记
            by::redis()->del(AppCRedisKeys::userLogoutStatus($user_info['user_id']));
        }

        CUtil::json_response(1, "OK", $user_info);
    }

    /**
     * @param $js_code
     * @param $user_type
     * @return array
     * 微信登陆 走 UnionID 机制
     *https://open.weixin.qq.com/cgi-bin/showdocument?action=dir_list&t=resource/res_list&verify=1&id=open1419317853&token=03db5ec2e5d52b9a5f3d82390e836d5731ecb4ab&lang=zh_CN
     * @throws \yii\db\Exception
     */
    protected function _login_by_weixin($js_code, $user_type)
    {
        if (empty($js_code)) {
            CUtil::json_response(-1, 'openudid参数缺失！');
        }
        $openudid  = $this->authOpenudid;
        $unionid   = $this->authUnionid;
        $user_info = $this->authUserinfo;

        $this->userTmpInfo = [
                'session_key' => $this->authRet['session_key'] ?? "",
        ];
        if (empty($user_info)) {
            $this->userTmpInfo += [
                    'openudid'  => $openudid,
                    'unionid'   => $unionid,
                    'user_type' => $user_type,
            ];
        }

        if ($user_info && !empty($this->authOpenudid)) {
            $user_info['opid'] = substr(md5($this->authOpenudid . 'dreame@2023'), 8, 16);
        }

        return $user_info;
    }

    /**
     * @throws Exception
     * 自动登陆
     * 客户端存在user_id 和 sessid 时会走自动登陆
     */
    public function actionAuto()
    {
        $reset_sess   = Yii::$app->request->post('reset_sess', 0);    //是否刷新用户信息 1：刷新 2：不刷新（包括token）
        $employee_uid = Yii::$app->request->post('employee_uid', ""); //默认不刷新session

        $user_info = $this->userModel->getOneByUid($this->user_id, false);

        !YII_ENV_PROD && CUtil::debug($this->user_id . '|' . json_encode($user_info) . '|' . json_encode($this->userModel), 'auto_login');
        if (empty($user_info)) {
            CUtil::json_response(-1, "无效的自动登陆操作");
        }

        $this->user_type = $user_info['user_type'] ?? 0;

        $this->_after_login($user_info, $reset_sess, $employee_uid);

        CUtil::json_response(1, "OK", $user_info);
    }


    /**
     * 登陆后续操作
     * @param $user_info
     * @param int $reset_sess
     * @throws Exception
     * @throws \yii\base\Exception
     */
    protected function _after_login(&$user_info, int $reset_sess = 1,$employee_uid = '')
    {

        $reset_sess = intval(!!$reset_sess);

        if ($reset_sess) {
            $user_info['sessid'] = JwtTools::factory()->encodeJsonWebToken($user_info);
        }

        //更新登陆信息
        $this->userModel->updateLoginInfo($user_info);

        $user_info['phone'] = strlen($user_info['user_id']) > 12 ? "" : by::Phone()->GetPhoneByUid($user_info['user_id']) ?? '';

        //-----------------------------当天首次登陆相关--------------------------------------------------//
        if (!empty($user_info['isTodayFirst'])) {
            //by::model('DCoinsModel','step')->recordActUser($user_info['user_id'],$this->api);
        }

	    //-----------------------------新用户注册相关--------------------------------------------------//
        $isNewUser = 0;
        if (!empty($user_info['isRegister'])) {
            $isNewUser = 1;
	        if ($user_info['user_type'] != 10){
                $extendData['ctime'] = time();
                //创建来源
                $source                        = Yii::$app->request->post('source', 0);
                $sceneSource                   = $user_info['sceneSource'] ?? 0;
                $extendData['source']          = empty($source) ? $sceneSource : $source;
                $extendData['platform_source'] = $this->platformSource ?? 0;
                $extendData['union']           = $this->union ?? '';
                $extendData['euid']            = $this->euid ?? '';
                $extendData['referer']         = $this->referer ?? '';
                // 二次登陆不修改信息
                $user_extend = by::userExtend()->getUserExtendInfo($user_info['user_id']);
                if (empty($user_extend['shop_code'])) {
                    $extendData['shop_code'] = $user_info['shop_code'] ?? '';//门店信息
                }
                if (empty($user_extend['store'])) {
                    $oaInfo              = by::WeFocus()->getOaInfoByUnionId($this->userTmpInfo['unionid'] ?? '');
                    $extendData['store'] = $oaInfo['store'] ?? '';//门店信息
                }

                //绑定导购
                $code      = Yii::$app->request->post('code', '');
                if (!empty($code)){
                    list($guide_id)=$this->userGuideModel->decrypt($code);
                    $this->userGuideModel->bound($user_info['user_id'],$guide_id);
                    $extendData['guide_id'] = $guide_id;
                }

                //公众号标签
                list($ts,$tag) = $this->userExtendModel->getTag($user_info['user_id']);
                if ($ts){
                    $extendData['tag'] = $tag;
                }

                //写入扩展表（包含来源，导购邀请，标签）
                $this->userExtendModel->saveUserExtend($user_info['user_id'], $extendData);

                //用户注册推送
                if($this->union && stristr($this->union,'tencent')){
                    AdvAscribe::factory()->push($user_info['user_id'],'tencent',['user_id'=>$user_info['user_id'],'url'=>$this->referer,'event'=>'REGISTER','click_id'=>$this->clickId]);
                }

            }else{
                //修改导购表
                $this->guideModel->saveGuide($user_info['user_id'], []);
            }
	    }
        // if($employee_uid && strlen($user_info['user_id']) < 11){
        //     $error = '';
        //     $bound_uid = by::Phone()->getUidByUserId($user_info['user_id']);
        //     $res = UserBindService::getInstance()->bindEmployee($bound_uid, $employee_uid, $isNewUser, $error);
        //     if(!$res){
        //         // 绑定失败，记录日志
        //         CUtil::debug('用户绑定员工失败:'.$user_info['user_id'].'-'.$employee_uid.'-'.$error,'user_bind.info');
        //     }
        // }

        //-----------------------------已注册绑定推荐人相关--------------------------------------------------//
        //重新绑定推荐人
        $r_code     = Yii::$app->request->post('r_code', '');
        list($r_id) = $this->userGuideModel->decrypt($r_code);
        $r_id       = isset($r_id) ? CUtil::uint($r_id) : 0;
        if (!empty($r_id) && !empty($user_info['phone'])) {
            $this->userRecommendModel->bind($user_info['user_id'], $r_id, $user_info['phone']);
        }

        //获取弹窗次数相关数据
        $popup_data = $this->userRecommendModel->getPopupCount($user_info['user_id'], $r_id);
        $user_info['bind_popup_count']  = $popup_data['bind_popup_count'] ?? 0; //绑定奖励需弹窗次数
        $user_info['other_bind_popup']  = $popup_data['other_bind_popup'] ?? 0; //其他人邀请绑定是否弹窗

        //奖励积分和金额
        $user_info['bind_reward_point'] = $user_info['bind_reward_price'] = 0;
        if (!empty($user_info['bind_popup_count']) || !empty($user_info['other_bind_popup'])) {
            
            // 获取被邀请人奖励配置
            list($status, $rewardData) = MemberCenter::factory()->ruleFineDetail('mall', 'dreame', 'be_invited_reg');
            if (! $status) {
                CUtil::debug('被邀请人获得奖励失败：user_id = '.$user_info['user_id'],'warn.reward_user');
                return [false, '被邀请人获得奖励失败'];
            }
            
            $rewardPoint = $rewardData['gold'] ?? 50000;
            
            $user_info['bind_reward_point'] = $rewardPoint; //奖励积分

            if (!empty($user_info['bind_reward_point'])) { //奖励积分转金额
                $rate = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
                $user_info['bind_reward_price'] = floor(sprintf("%.2f", $user_info['bind_reward_point'] / $rate) * 100) / 100;
            }
        }

        // 当前用户来源 插入队列
        if ($this->user_id && strlen($this->user_id) < 11 && !empty($this->referer)) {
            \Yii::$app->queue->push(new UserSourceJob([
                'referer' => $this->referer,
                'userId'  => $this->user_id,
            ]));
        }


        //-------------------------------------------统计相关--------------------------------------------------//
        //记录用户pvuv数据
        by::statistics()->userStat($user_info['user_id'], by::statistics()::STAT_TYPE['both']);

        if (isset($user_info['avatar']) && empty($user_info['avatar'])) {
            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);
            $user_info['avatar'] = $config['cdnAddr'] . '/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png';
        }

        if (isset($user_info['nick']) && empty($user_info['nick'])) {
            $user_info['nick'] = '微信用户';
        }
        unset($user_info['id']);
    }

    /**
     * @return mixed
     * @throws Exception
     * 注册
     */
    protected function _register()
    {
        list($status, $user_info) = $this->userModel->register($this->userTmpInfo);
        if (!$status || empty($user_info)) {
            CUtil::debug($user_info, 'warn.register');
            CUtil::json_response(-1, $user_info);
        }

        $isRegister                = $user_info['isRegisterFlag'];
        $user_info                 = $this->userModel->getOneByUid($user_info['user_id']);//重新获取一下用户信息
        $user_info['isRegister']   = $isRegister; //标示用户是否刚刚注册
        $user_info['isTodayFirst'] = 1;//新注册自然是当天第一次登陆
        $user_info['user_type']    = $this->userTmpInfo['user_type'];

        if ($user_info && !empty($this->userTmpInfo['openudid'])) {
            $user_info['opid'] = substr(md5($this->authOpenudid . 'dreame@2023'), 8, 16);
        }
        return $user_info;
    }


    /**
     * @param string $openudid
     * @param $user_type
     * @return array
     * @throws \Exception
     * 游客登陆
     */
    protected function _login_by_visitor(string $openudid, $user_type): array
    {
        $user_info = by::users()->getOneByOpenUdId($openudid, $user_type);
        if(empty($user_info)) {
            $sex         = Yii::$app->request->post('sex','2');
            $defaultIcon = by::model("LoginModel","main")->getDefaultImgBySex($sex);
            $this->userTmpInfo = [
                'openudid'  => $openudid,
                'unionid'   => $openudid,
                'user_type' => $user_type,
                'avatar'    => $defaultIcon,
            ];
        }

        return $user_info;
    }

    /**
     * @param $js_code
     * @param $user_type
     * https://developer.work.weixin.qq.com/document/path/92426
     * 企业微信登录模式
     * 企业微信的jscode2session请求url与微信的不同
     * 企业微信的jscode2session返回的是userid，而微信返回的是openid
     */
    protected function _login_by_wework($js_code, $user_type) {
        if (empty($js_code)) {
            CUtil::json_response(-1, 'openudid参数缺失！');
        }

        $openudid = $unionid = "";
//        $openudid = $unionid = $js_code;//用户直接登录


        //空走js_code远程转换
        if (empty($openudid)) {
            list($status, $ret) = WeWorkApp::factory()->code2session($js_code);
            if (empty($ret['session_key']) || empty($ret['userid']) || !$status) {
                CUtil::json_response(CodeModel::STATUS['WEWORK_ERR'], '企业微信授权认证失败！');
            }

           /* $ocorpid    = $ret['corpid']    ?? "";//用户当前所属企业

            $config     = CUtil::getConfig('wework','common',MAIN_MODULE);
            $corpId     = $config['corpId'] ?? "";
            if($corpId !== $ocorpid) {
                CUtil::json_response(CodeModel::STATUS['WEWORK_ERR'], '功能仅对该公司人员开放！');
            }*/

            $openudid   = $ret['userid'];
            $unionid    = $ret['unionid'] ?? '';
        }

        $user_info = by::users()->getOneByOpenUdId($openudid, $user_type);
        $this->userTmpInfo = [
            'session_key' => $ret['session_key'] ?? "",
        ];

        if (empty($user_info)) {
            $this->userTmpInfo += [
                'openudid'      => $openudid,
                'unionid'       => $unionid,
                'user_type'     => $user_type,
            ];
        }

        $this->userAuth = 1;
        $this->userModel = empty($this->userAuth) ? (by::Rusers()) : (by::users());
        $this->userGuideModel = empty($this->userAuth) ? (by::RuserGuide()) : (by::userGuide());
        $this->userExtendModel = empty($this->userAuth) ? (by::RuserExtend()) : (by::userExtend());
        $this->guideModel = empty($this->userAuth) ? (by::Rguide()) : (by::guide());
        $this->userRecommendModel = empty($this->userAuth) ? (by::RuserRecommend()) : (by::userRecommend());

        return $user_info;
    }


    /**
     *
     * @OA\Post(
     *     path="/main/login/logout",
     *     summary="用户登出",
     *     description="APP用户登出",
     *     tags={"用户"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api", type="string", default="a_1643178000",description="api平台 必传；安卓 'a_1664246268' => 'b_m3I6PiPgYX#' ,//app登录  b_m3I6PiPgYX# 是密钥secretKey，算法要用到。IOS: 'i_1666147923' => 'b_m3h^jWfA9jp' "),
     *              @OA\Property(property="sign", type="string", default="test-environment",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="test-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test",description="测试环境-是否核验签名"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionLogout()
    {
        // 检查用户是否已登录
        if (empty($this->user_id) || strlen($this->user_id) > 11 || empty($this->jwtToken)) {
            CUtil::json_response(-1, "当前账户未登录或会话已过期，请重新登录后重试！");
        }

        // 执行登出操作
        $status = by::login()->LogOut($this->user_id, $this->jwtToken,true);
        if (!$status) {
            CUtil::json_response(-1, "退出登录失败，请稍后重试！");
        }

        // 返回成功响应
        CUtil::json_response(1, "退出登录");
    }


    /**
     * @OA\Post(
     *     path="/main/login/app-login",
     *     summary="APP登录",
     *     description="APP用户登录",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="jwtToken", type="string",description="身份标识"),
     *              @OA\Property(property="shop_code", type="string",description="门店加密字符串str"),
     *              @OA\Property(property="scan_code", type="string",description="扫码登录code"),
     *              @OA\Property(property="state", type="string",description="扫码登录state"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionAppLogin()
    {
        $post           = Yii::$app->request->post();
        $this->jwtToken = $post['jwtToken'] ?? '';
        $post['api']    = $this->api;
        // 校验 JWT Token
        if (empty($this->jwtToken)) {
            CUtil::json_app_response(-1, "缺少认证参数");
        }

        // 尝试通过 JWT Token 登录
        list($status, $data) = LoginService::getInstance()->loginByJwtToken($this->jwtToken);
        if (!$status) {
            CUtil::json_app_response(-1, $data);
        }

        // 处理场景来源并获取可能的用户信息
        list($status, $sceneSource, $userMainInfo) = LoginService::getInstance()->determineSceneSource($post, $data);
        if (!$status) {
            CUtil::json_app_response(-1, $sceneSource);  // sceneSource 会包含错误信息
        }

        $isRegister          = $data['isRegisterFlag'] ?? 0;
        $data['isRegister']  = $isRegister; //标示用户是否刚刚注册
        $data['sceneSource'] = $sceneSource;

        // 处理登录后的逻辑
        $this->_after_login($data, 1);

        // 绑定用户信息 (扫码登录) 一定在_after_login之后 才能拿到 user_id
        if ($userMainInfo) {
            DataService::getInstance()->bindExistingUser($data['user_id'], $userMainInfo, UserTypeEnum::USER_TYPE['WECHAT_SCAN']);
        }

        // 记录日志
        CUtil::debug('app登录|' . json_encode($data), 'app.login');

        // 返回成功响应
        CUtil::json_app_response(0, "OK", $data);
    }


    /**
     * @OA\Post(
     *     path="/main/login/alipay-login",
     *     summary="alipay登录",
     *     description="alipay用户登录",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1712037350,IOS:i_1712037455"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="authCode", type="string",description="支付宝小程序authCode"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionAlipayLogin()
    {
        $post   = Yii::$app->request->post();
        $this->authCode = $post['openudid']??'';
        if(empty($this->authCode)){
            CUtil::json_response(-1, "缺少认证参数");
        }
        // 防止多次请求
        $unique_key = __FUNCTION__ . "|{$this->authCode}|1";
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 3, "EX");
        if (!$anti) {
            CUtil::json_response(-1, "登录操作频繁~");
        }
        list($status,$data) = by::login()->loginByAlipay($this->authCode,$this->user_type);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        $data['sceneSource'] = by::userExtend()::SOURCE_FROM_ALIPAY;
        $user_id = $data['user_id'] ?? 0;
        if($user_id && strlen($user_id) < 15){
            $this->userAuth = 1;
        }else{
            $this->userAuth = 0;
        }
        $this->_after_login($data,1);

        CUtil::debug('支付宝小程序登录|'.json_encode($data),'alipay.login');

        CUtil::json_response(1, "OK", $data);
    }


    /**
     * @OA\Post(
     *     path="/main/login/code",
     *     summary="PC扫码登录获取二维码信息",
     *     description="PC扫码登录获取二维码信息",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *         @OA\Schema(
     *               @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1712037350,IOS:i_1712037455"),
     *               @OA\Property(property="sign", type="string", default="",description="签名"),
     *               @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *               @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *               @OA\Property(property="id", type="string", default="login", description="登录：login、绑定微信：bind"),
     *           )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionCode()
    {
        $id = Yii::$app->request->post('id', 'login');
        list($status, $data) = LoginService::getInstance()->getPcUrl($id);
        if (!$status) {
            CUtil::json_response(-1, "获取失败");
        }
        // 输出微信扫码登录的 URL参数
        CUtil::json_response(1, "OK", $data);
    }



    /**
     * @OA\Post(
     *     path="/main/login/retrieve-user-info",
     *     summary="PC扫码登录校验是否绑定手机号",
     *     description="PC扫码登录校验是否绑定手机号",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *         @OA\Schema(
     *               @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1712037350,IOS:i_1712037455"),
     *               @OA\Property(property="sign", type="string", default="",description="签名"),
     *               @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *               @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *               @OA\Property(property="scan_code", type="string",description="扫码登录code"),
     *               @OA\Property(property="state", type="string",description="扫码登录state"),
     *           )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */

    public function actionRetrieveUserInfo()
    {
        // 获取请求参数
        $post     = Yii::$app->request->post();
        $code     = $post['scan_code'] ?? '';
        $state    = $post['state'] ?? '';
        $userType = UserTypeEnum::USER_TYPE['WECHAT_SCAN'];

        // 校验参数
        if (empty($code) || empty($state)) {
            CUtil::json_response(-1, "缺少认证参数");
        }

        // 获取用户信息 unionid+openid
        list($status, $result) = LoginService::getInstance()->getUserWxInfo($code, $state, $userType);

        // 检查用户信息获取状态
        if (!$status) {
            CUtil::json_response(-1, $result);
        }

        // 初始化返回数据
        $data = [
            'user_id'  => $result['user_id'] ?? '',
            'exists'   => $result['exists'] ?? 0,
            'nickname' => $result['nickname'] ?? '',
            'avatar'   => $result['avatar'] ?? '',
            'uid'      => $result['uid'] ?? '',
            'phone'    => $result['phone'] ?? '',
        ];

        // 1.查询 t_platform 表
        $platformData = LoginService::getInstance()->CheckPlatUserByUnionID($result['unionid'], $userType);

        // 2. 如果未在 t_platform 表中找到数据，查询 t_users_main 表
        if (empty($platformData)) {
            // 通过 unionid 查询 t_users_main 表
            $userId = LoginService::getInstance()->CheckMainUserByUnionID($result['unionid'], UserTypeEnum::USER_TYPE['WX_MINI']);

            // 2.1 如果 t_users_main 表中存在用户数据
            if ($userId) {
                // 将用户绑定到 t_platform 表中
                list($mainUserStatus, $mainUserInfo) = DataService::getInstance()->bindExistingUser($userId, $result, $userType);
                if (!$mainUserStatus) {
                    CUtil::json_app_response(-100, "登录失败");
                }
                // 更新昵称头像
                by::users()->updateMembersInfo($userId, ['nick' => $result['nickname'], 'avatar' => $result['avatar']]);
                // 更新返回数据
                $data = array_merge($data, [
                    'user_id' => $userId,
                    'uid'     => $mainUserInfo['uid'],
                    'phone'   => $mainUserInfo['phone'],
                    'exists'  => 1,
                ]);
            }
        } else {
            // 3. t_platform 表查到了数据

            // 判断 openudid 是否正常以及 status 是否为 BIND
            $statusNormal  = $platformData['status'] == by::UsersPlatformModeModel()::STATUS['BIND'];
            $openUdidValid = !strpos($platformData['openudid'], '|');

            // 3.1 如果 openudid 正常，且 status 为 BIND，表示已绑定
            if ($openUdidValid && $statusNormal) {
                $data['exists'] = 1;
            } // 3.2 如果 openudid 正常，但 status 为 UNBIND，表示未绑定，需要提示绑定手机号
            elseif ($openUdidValid && !$statusNormal) {
                $data['exists'] = 0;
            } // 3.3 如果 openudid 异常，进一步查询 t_users_main 表
            elseif (!$openUdidValid) {
                $userId = LoginService::getInstance()->CheckMainUserByUnionID($result['unionid'], UserTypeEnum::USER_TYPE['WX_MINI']);
                if ($userId) {
                    // 将用户绑定到 t_platform 表中
                    list($mainUserStatus, $mainUserInfo) = DataService::getInstance()->bindExistingUser($userId, $result, $userType);
                    if (!$mainUserStatus) {
                        CUtil::json_app_response(-100, "登录失败");
                    }
                    // 更新昵称头像
                    // by::users()->updateMembersInfo($userId, ['nick' => $result['nickname'], 'avatar' => $result['avatar']]);
                    // 更新返回数据
                    $data = array_merge($data, [
                        'user_id' => $userId,
                        'uid'     => $mainUserInfo['uid'],
                        'phone'   => $mainUserInfo['phone'],
                        'exists'  => 1,
                    ]);
                }
            }
        }

        // 初始化用户登录，确保返回 sessid
        if (!empty($data['user_id']) && $data['exists'] && empty($data['sessid'])) {
            $data['sessid'] = JwtTools::factory()->encodeJsonWebToken($data);
        }

        // 返回成功响应
        CUtil::json_response(1, "OK", $data);
    }






    /**
     * @OA\Post(
     *     path="/main/login/unbind",
     *     summary="PC商城解绑微信",
     *     description="PC商城解绑微信",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     * *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionUnbind()
    {
        // 获取当前用户ID
        $userId = $this->user_id;

        // 校验用户ID是否有效
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再进行解绑！');
        }

        // 调用解绑服务
        list($status, $msg) = LoginService::getInstance()->unbindUser($userId);

        // 根据解绑结果返回相应的JSON响应
        if (!$status) {
            CUtil::json_response(-1, $msg);  // 使用服务返回的消息
        }

        CUtil::json_response(1, $msg);  // 使用服务返回的消息
    }


    /**
     * @OA\Post(
     *     path="/main/login/bind",
     *     summary="PC商城绑定微信",
     *     description="PC商城绑定微信",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="code", type="string", default="", description="code"),
     *                          @OA\Property(property="state", type="string", default="", description="state"),
     *                     )
     *                 }
     *             )
     *         )
     * *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionBind()
    {
        // 绑定微信
        $userId = $this->user_id;
        $post   = Yii::$app->request->post();

        // 校验用户ID是否有效
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再进行绑定！');
        }

        // 调用绑定服务
        list($status, $msg) = LoginService::getInstance()->bindUser($userId, $post);

        // 根据绑定结果返回相应的JSON响应
        if (!$status) {
            CUtil::json_response(-1, $msg);  // 使用服务返回的消息
        }

        CUtil::json_response(1, "绑定成功");  // 使用服务返回的消息
    }


    /**
     * @OA\Post(
     *     path="/main/login/binds",
     *     summary="PC商城获取绑定信息",
     *     description="PC商城获取绑定信息",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     * *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionBinds()
    {
        // 获取用户ID
        $userId = $this->user_id;

        // 校验用户ID是否有效
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再获取绑定信息！');
        }

        // 调用服务进行绑定操作
        list($status, $data) = LoginService::getInstance()->binds($userId);

        // 检查绑定操作是否成功
        if (!$status) {
            CUtil::json_response(-1, $data); // 使用服务返回的错误消息
        }

        // 处理返回数据，将 'phone' 键值映射为 'tm'
        $data = Response::responseList($data, ['bindPhone' => 'tm']);

        // 返回成功响应
        CUtil::json_response(1, 'OK', $data);
    }


    /**
     * @OA\Post(
     *     path="/main/login/check-bind",
     *     summary="PC商城校验绑定微信",
     *     description="PC商城校验绑定微信",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="code", type="string", default="", description="code"),
     *                          @OA\Property(property="state", type="string", default="", description="state"),
     *                     )
     *                 }
     *             )
     *         )
     * *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionCheckBind()
    {
        // 绑定微信
        $userId = $this->user_id;
        $post   = Yii::$app->request->post();

        // 校验用户ID是否有效
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先登录！');
        }

        // 调用绑定服务
        list($status, $msg) = LoginService::getInstance()->CheckBind($post);

        // 检查绑定操作是否成功
        if (!$status) {
            CUtil::json_response(-1, $msg); // 使用服务返回的错误消息
        }

        // 返回成功响应
        CUtil::json_response(1, 'OK');
    }


    /**
     * @OA\Post(
     *     path="/main/login/get-login-type",
     *     summary="微信小程序获取用户登录状态",
     *     description="微信小程序获取用户登录状态",
     *     tags={"用户"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     * *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionGetLoginType()
    {
        $userId = $this->user_id;

        try {
            $userLoginType = LoginService::getInstance()->getUserLoginType($userId);
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'OK', ['user_login_type' => $userLoginType]);
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'error.get-login-type');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '系统异常，请稍后重试');
        }
    }

}
