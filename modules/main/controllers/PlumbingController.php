<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 11:47
 */

namespace app\modules\main\controllers;

use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\CUtil;
use app\modules\main\forms\plumbing\ServiceOrderSaveForm;
use app\modules\main\services\PlumbingService;
use Yii;
use yii\db\Exception;
use app\modules\main\controllers\CommController;

/**
 * Class PlumbingController
 * @package app\modules\main\controllers
 * 上下水服务控制器
 */
class PlumbingController extends CommController
{
    /**
     * @throws Exception
     * 工单列表
     */
    public function actionOrderList()
    {
        $source      = by::plumbingOrder()::SOURCE['INDEX'];
        $type        = Yii::$app->request->post("type", 0); //类型（1：勘测工单 2：安装工单 3：上门服务工单）
        $source_page = Yii::$app->request->post("source_page", 1); //来源（ 1：全部订单 2：上下水页面）
        $status      = Yii::$app->request->post("status", -1);
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', []);
        }

        if (empty($type)) {
            $list = by::plumbingOrder()->getListBack($source, 0, 0, '', $this->user_id, 1);
        } else {
            $list = by::plumbingOrder()->getList($source, $type, $source_page, 0, 0, '', $this->user_id, $status);
        }

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 订单唯一数据
     */
    public function actionOrderInfo()
    {
        $order_no = Yii::$app->request->post("order_no", 0);
        $id       = Yii::$app->request->post("id", 0);
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', []);
        }

        if (!empty($id)) {
            $info = by::plumbingOrder()->getInfoById($id);
        } else {
            $info = by::plumbingOrder()->getInfoByOrderNo($order_no);
        }

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 保存订单
     */
    public function actionOrderSave()
    {
        $data = Yii::$app->request->post();

        $type = $data['type']   ?? '';
        if ($type == by::plumbingOrder()::TYPE['EXPLORE'] && !isset($data['is_buy'])) {
            list($status, $msg) = by::plumbingOrder()->plumbingSaveBack($this->user_id, $data);
        } else {
            list($status, $msg) = by::plumbingOrder()->plumbingSave($this->user_id, $this->api, $data);
        }

        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok', $msg);
    }

    /**
     * @OA\Post(
     *     path="/main/plumbing/service-order-save",
     *     summary="上门服务",
     *     description="保存上门服务订单",
     *     tags={"上下水"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="jwtToken", type="string",description="身份标识"),
     *              @OA\Property(property="name",type="string",description="姓名"),
     *              @OA\Property(property="phone",type="intger",description="手机号"),
     *              @OA\Property(property="verify_code",type="intger",description="验证码"),
     *              @OA\Property(property="pid",type="string",description="省份ID"),
     *              @OA\Property(property="cid",type="string",description="城市ID"),
     *              @OA\Property(property="aid",type="string",description="区/县ID"),
     *              @OA\Property(property="detail",type="string",description="地址详情"),
     *              @OA\Property(property="expect_time",type="string",description="期望上门时间"),
     *              @OA\Property(property="context_type",type="integer",description="家庭环境"),
     *              @OA\Property(property="sn_config",type="string",description="产品信息"),
     *              @OA\Property(property="product_type",type="integer",description="产品类型：1:扫地机 2:洗地机"),
     *              @OA\Property(property="sn",type="string",description="SN编码"),
     *              @OA\Property(property="type",type="integer",description="服务类型：3:上门服务"),
     *              @OA\Property(property="province",type="string",description="省名称"),
     *              @OA\Property(property="city",type="string",description="市名称"),
     *              @OA\Property(property="county",type="string",description="区名称")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionServiceOrderSave()
    {
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候~');
        }

        // 验证参数
        $form = new ServiceOrderSaveForm();
        $form->load(Yii::$app->request->post(), '');

        // 验证失败
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        // 提交订单
        list($status, $msg) = PlumbingService::getInstance()->saveServiceOrder($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok', $msg);
    }


    public function actionCommonOrderSave()
    {
        $data = Yii::$app->request->post();
        $data['pay_type'] = by::Omain()::PAY_BY_NO_SET;

        $type = $data['type']   ?? '';
        if ($type == by::plumbingOrder()::TYPE['EXPLORE'] && !isset($data['is_buy'])) {
            list($status, $msg) = by::plumbingOrder()->plumbingSaveBack($this->user_id, $data);
        } else {
            list($status, $msg) = by::plumbingOrder()->plumbingSave($this->user_id, $this->api, $data);
        }

        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok', $msg);
    }

    /**
     * @throws Exception
     * 退款订单列表
     */
    public function actionRefundList()
    {
        $source = by::plumbingOrder()::SOURCE['INDEX'];

        $list = by::plumbingRefund()->getList($source, 0, 0, '', $this->user_id);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 退款订单唯一数据
     */
    public function actionRefundInfo()
    {
        $refund_no = Yii::$app->request->post("refund_no", '');

        $info      = by::plumbingRefund()->getInfoByRefundNo($refund_no);

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 申请退款
     */
    public function actionApplyRefund()
    {
        $data = Yii::$app->request->post();

        list($status, $refund_no) = by::plumbingRefund()->applyRefund($this->user_id, $data);
        if (!$status) {
            CUtil::json_response(-1, $refund_no);
        }

        CUtil::json_response(1, 'ok', ['refund_no' => $refund_no]);
    }

    /**
     * @throws Exception
     * 产品信息配置
     */
    public function actionSnConfig()
    {
        $type = Yii::$app->request->post('type', 0);

        // 产品类型
        $product_type = Yii::$app->request->post('product_type', 0);

        $list = by::plumbingSn()->getConfig($type, '', $product_type);

        // 临时补丁，去除上门服务中的模组
        $tmp = [];
        if (YII_ENV_PROD && ($type == by::plumbingOrder()::TYPE['SERVICE'])) {
            foreach ($list as $item) {
                if (in_array($item['id'], [41, 42, 43])) {
                    continue;
                }
                $tmp[] = $item;
            }
            $list = $tmp;
        }

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 价格配置
     */
    public function actionPriceConfig()
    {
        $info = by::plumbingPrice()->getPriceConfig();

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * 发送验证码
     */
    public function actionSendCode()
    {
//        CUtil::json_response(-1, "功能维护中，暂请联系在线客服登记处理~");
        $post = Yii::$app->request->post();
        $phone = $post['phone'] ?? '';
        if(empty($phone)){
            CUtil::json_response(-1, "手机号必传！");
        }

        list($status, $msg) = by::model("SmsModel",MAIN_MODULE)->smsSend($phone, "CODE");
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * 评价记录
     */
    public function actionCommentSave()
    {
        $data = Yii::$app->request->post();

        list($status, $msg) = by::plumbingComment()->commentSave($this->user_id, $data);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * 评价列表
     */
    public function actionCommentList()
    {
        $list = by::plumbingComment()->getList(0, 0, '', $this->user_id);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 评价唯一数据
     */
    public function actionCommentInfo()
    {
        $order_no = Yii::$app->request->post("order_no", '');

        $info     = by::plumbingComment()->getInfoByOrderNo($order_no);

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 标签配置列表
     */
    public function actionTagConfig()
    {
        $list = by::CommentTag()->getConfig();

        CUtil::json_response(1,"OK", $list);
    }

    /**
     * @throws Exception
     * 保存查询记录
     */
    public function actionSearchSave()
    {
        $data = Yii::$app->request->post();

        list($status, $msg) = by::exploreSearch()->searchSave($data);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok', $msg);
    }

    /**
     * 上门服务的产品类型，如扫地机、洗地机
     */
    public function actionProductType()
    {
        $model = by::plumbingOrder();
        // 产品类型（上门服务的产品类型）
        $data = [];
        $types = $model::PRODUCT_TYPE;
        foreach ($types as $type) {
            $data[] = [
                'id'   => $type,
                'name' => $model::PRODUCT_TYPE_NAME[$type],
            ];
        }
        CUtil::json_response(1, 'ok', $data);
    }


    /**
     * @OA\Post(
     *     path="/main/plumbing/service-address",
     *     summary="上下水地址",
     *     description="根据事件类型（省、市、区）获取对应的地址列表",
     *     tags={"上下水"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"event"},
     *                 @OA\Property(property="api", type="string", default="a_1664246268", description="平台API（安卓:a_1664246268，IOS:i_1666147923）"),
     *                 @OA\Property(property="sign", type="string", default="", description="签名"),
     *                 @OA\Property(property="sign_time", type="string", default="", description="签名时间"),
     *                 @OA\Property(property="provider", type="string", default="test", description="测试环境填写 test，将跳过签名校验"),
     *                 @OA\Property(property="jwtToken", type="string", description="身份标识"),
     *                 @OA\Property(property="event", type="string", description="事件类型：PROVINCE 获取省，CITY 获取市，COUNTY 获取区"),
     *                 @OA\Property(property="province_id", type="string", description="省份ID，获取市时必填"),
     *                 @OA\Property(property="city_id", type="string", description="市级ID，获取区时必填")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="返回地址列表数据",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=200, description="状态码"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ServiceAddressListResponse")
     *         )
     *     )
     * )
     */

    public function actionServiceAddress()
    {
        $params = Yii::$app->request->post();

        list($status, $data) = PlumbingService::getInstance()->serviceAddress($params);
        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE,$data);
        }
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $data);
    }
}
