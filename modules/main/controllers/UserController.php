<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2022/1/25
 * Time: 17:54
 */
namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class UserController extends CommController{



	public function actionModify(){
		//$this->user_id = 548213;//todo test

		$post          = \Yii::$app->request->post();
		$user_name     = $post['user_name'] ?? 0;         //用户名称
		$user_province = $post['user_province'] ?? '';    //用户地址
		$user_city     = $post['user_city'] ?? '';        //用户地址
		$user_area     = $post['user_area'] ?? '';        //用户地址
		$user_birthday = $post['user_birthday'] ?? '';    //用户生日
	    list($status,$msg)	= by::userProfile()->saveUserExtend($this->user_id,$user_name,$user_province,$user_city,$user_area,$user_birthday);
		if(!$status){
			CUtil::json_response(-1, $msg);
		}
		CUtil::json_response(1, 'ok',$msg);
	}


	/**
	 * 用户详情信息
	 * @throws Exception
	 */
	public function actionUserDetail(){

		//$this->user_id = 548213;
		$user_info      = by::users()->getOneByUid($this->user_id);
		$user_profile   = by::userProfile()->getUserExtend($this->user_id);
		$data['nick']   = $user_info['nick'] ?? '';
		$data['avatar'] = $user_info['avatar'] ?? '';
		$data['sex']    = $user_info['sex'] ?? '';
		$data           = array_merge($data, $user_profile);
		CUtil::json_response(1, 'ok', $data);
	}

	/**
	 * 个人中心信息页
	 * @throws Exception
	 */
	public function actionInfo(){
		$user_info = by::users()->getOneByUid($this->user_id);
		$data['nick']   = $user_info['nick'] ?? '';        //用户昵称
		$data['avatar'] = $user_info['avatar'] ?? '';      //用户头像

		//获取优惠劵数量
		$integral = by::integral()->get($this->user_id);
		$data['integral'] = $integral;                      //积分数
        CUtil::json_response(1, 'ok', $data);
	}
}