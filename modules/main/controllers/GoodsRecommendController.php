<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\main\forms\goods\GoodsRecommendForm;
use app\modules\main\forms\goods\GoodsRecommendListForm;
use app\modules\main\forms\goods\GoodsFriendRecommendListForm;
use app\modules\main\models\PlatformModel;
use app\modules\main\services\GoodsRecommendService;
use app\modules\main\services\GrecommendService;
use app\modules\main\services\TurboModeService;
use Yii;
use yii\db\Exception;


class GoodsRecommendController extends CommController
{
    // 频率限制配置
    const RATE_LIMIT = [
            'user-recommend' => [
                    'limit'  => 100,
                    'window' => 3600
            ],
            // 每小时最多100次推荐操作
            'user-list'      => [
                    'limit'  => 200,
                    'window' => 3600
            ],
            // 每小时最多200次列表查询
            'friend-list'    => [
                    'limit'  => 200,
                    'window' => 3600
            ],
            // 每小时最多200次好友列表查询
    ];

    // 分页参数限制
    const MAX_PAGE_SIZE     = 50;
    const DEFAULT_PAGE_SIZE = 10;

    /**
     * 频率限制检查
     * @param string $action
     * @param int $userId
     * @return void
     */
    private function checkRateLimit(string $action, int $userId = 0)
    {
        if (!isset(self::RATE_LIMIT[$action])) {
            return;
        }

        $config = self::RATE_LIMIT[$action];
        $key    = "rate_limit:goods_recommend:{$action}:{$userId}:" . floor(time() / $config['window']);

        $redis   = by::redis('core');
        $current = $redis->incr($key);

        if ($current === 1) {
            $redis->expire($key, $config['window']);
        }

        if ($current > $config['limit']) {
            CUtil::json_response(-1, '您的推荐过于频繁，请稍后再试');
        }
    }

    /**
     * 验证和清理分页参数
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    private function validatePagination(int $page, int $pageSize): array
    {
        $page     = max(1, $page);
        $pageSize = min(max(1, $pageSize), self::MAX_PAGE_SIZE);

        return [
                $page,
                $pageSize
        ];
    }

    /**
     * 安全的错误响应
     * @param string $message
     * @param bool $isUserError
     * @return void
     */
    private function safeErrorResponse(string $message, bool $isUserError = true)
    {
        if (!$isUserError) {
            // 记录系统错误但不暴露给用户
            CUtil::debug($message, 'err.goods-recommend');
            $message = '系统繁忙，请稍后再试';
        }
        CUtil::json_response(-1, $message);
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/list",
     *     summary="商品-推荐商品",
     *     description="推荐商品",
     *     tags={"商品"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/GoodsRecommendResources",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post                = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType ?? 0;
        $post['platform_id'] = $this->platformId ?? 0;

        $Turbo    = TurboModeService::getInstance();
        $cacheKey = $Turbo->getCacheKey('goods-recommend/list:' . $post['platform_id'] . '_' . $post['sprice_type']);

        list($status, $ret) = $Turbo->executeWithCache($cacheKey, function ($post) {
            return GrecommendService::getInstance()->getRecommendList($post);
        }, [$post]);

//      list($status,$ret) = GrecommendService::getInstance()->getRecommendList($post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/goods-tag",
     *     summary="精彩直达",
     *     description="精彩直达",
     *     tags={"商品"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */
    public function actionGoodsTag()
    {
        // 定义平台对应的文件路径
        $platformFiles = [
                PlatformModel::PLATFORM['WX']  => '/../enums/GoodsRecommend/AppGoodsTag.json',
                PlatformModel::PLATFORM['APP'] => '/../enums/GoodsRecommend/AppGoodsTag.json',
                PlatformModel::PLATFORM['PC']  => '/../enums/GoodsRecommend/PcGoodsTag.json',
        ];

        // 检查平台ID并获取对应的文件路径
        $filePath = $platformFiles[$this->platformId] ?? null;
        if (!$filePath) {
            CUtil::json_response(-1, '平台ID错误');
        }

        // 读取并解析JSON文件
        $goodsTag = json_decode(file_get_contents(__DIR__ . $filePath), true);
        if (!$goodsTag) {
            CUtil::json_response(-1, 'JSON文件读取或解析错误');
        }

        $Turbo    = TurboModeService::getInstance();
        $cacheKey = $Turbo->getCacheKey('goods-recommend/goods-tag:' . $this->platformId);
        // 过滤无效的商品标签
        $goodsTag = $Turbo->executeWithCache($cacheKey, function ($goodsTag) {
            return array_values(array_filter($goodsTag, function ($li) {
                if (empty($li['tid'])) {
                    return false;
                }
                $count = by::Gmain()->GetListCount($this->version, 0, 0, '', '', $li['tid'], [
                        'platformIds'          => $this->platformIds,
                        'is_internal_purchase' => 0
                ]);
                return !empty($count);
            }));
        }, [$goodsTag]);

        // 过滤无效的商品标签
//        $goodsTag = array_values(array_filter($goodsTag, function ($li) {
//            if (empty($li['tid'])) {
//                return false;
//            }
//            $count = by::Gmain()->GetListCount($this->version, 0, 0, '', '', $li['tid'], ['platformIds' => $this->platformIds, 'is_internal_purchase' => 0]);
//            return !empty($count);
//        }));

        // 以 JSON 格式返回过滤后的商品标签列表
        CUtil::json_response(1, 'ok', $goodsTag);
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/default-goods",
     *     summary="推荐商品/搜索默认（PC商城）",
     *     description="PC商城加入购物车推荐+搜索默认推荐",
     *     tags={"PC商城"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"limit"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="limit", type="integer", default="", description="获取商品数量")
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionDefaultGoods()
    {
        $post                = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $post['version']     = $this->version;
        $post['platform_id'] = $this->platformId;
        $page                = 1;
        $pageSize            = $post['limit'] ?? 3;

        $data = GrecommendService::getInstance()->getDefaultGoodsList($post, $page, $pageSize);
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/user-recommend",
     *     summary="用户推荐商品",
     *     description="用户推荐或取消推荐商品",
     *     tags={"商品推荐"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gid", "type"},
     *                         @OA\Property(property="gid", type="integer", description="商品ID", example=123),
     *                         @OA\Property(property="type", type="integer", description="推荐类型：1推荐，0取消推荐", enum={0, 1}, example=1)
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码：1成功，-1失败"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     * 用户推荐商品接口
     * @return void
     * @throws Exception
     */
    public function actionUserRecommend()
    {
        $post = Yii::$app->request->post();

        // 表单验证
        $form = new GoodsRecommendForm();
        if (!$form->load($post, '') || !$form->validate()) {
            $errors = array_values($form->getFirstErrors())[0] ?? '参数错误';
            CUtil::json_response(-1, $errors);
        }

        $gid    = intval($form->gid);
        $type   = intval($form->type);
        $userId = intval($this->user_id ?? 0);

        // 检查用户登录状态
        if (empty($userId)) {
            CUtil::json_response(-1, '请先登录');
        }

        // 频率限制检查
        $this->checkRateLimit('user-recommend', $userId);

        // 参数安全性检查
        if ($gid <= 0) {
            CUtil::json_response(-1, '商品ID无效');
        }

        if (!in_array($type, [
                0,
                1
        ], true)) {
            CUtil::json_response(-1, '操作类型无效');
        }

        // 获取用户唯一标识
        $uid = by::Phone()->getUidByUserId($userId) ?? '';

        try {
            $service = GoodsRecommendService::getInstance();

            list($status, $message) = $service->userRecommendGoods($userId, $gid, $type, $uid);

            if ($status) {
                CUtil::json_response(1, $message);
            } else {
                CUtil::json_response(-1, $message);
            }
        } catch (\Exception $e) {
            $this->safeErrorResponse("推荐操作失败: " . $e->getMessage(), false);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/user-list",
     *     summary="获取用户推荐商品列表",
     *     description="获取当前用户已推荐的商品列表",
     *     tags={"商品推荐"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page", type="integer", description="页码", example=1, default=1),
     *                         @OA\Property(property="page_size", type="integer", description="每页数量", example=10, default=10, maximum=50)
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码：1成功，-1失败"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="数据",
     *                 @OA\Property(property="list", type="array", description="商品列表", @OA\Items(type="object")),
     *                 @OA\Property(property="total", type="integer", description="总数"),
     *                 @OA\Property(property="page", type="integer", description="当前页码"),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量"),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     )
     * )
     * 获取用户推荐商品列表
     * @return void
     */
    public function actionUserList()
    {
        $post = Yii::$app->request->post();

        // 表单验证
        $form = new GoodsRecommendListForm();
        if (!$form->load($post, '') || !$form->validate()) {
            $errors = array_values($form->getFirstErrors())[0] ?? '参数错误';
            CUtil::json_response(-1, $errors);
        }

        $userId = intval($this->user_id ?? 0);

        // 检查用户登录状态
        if (empty($userId)) {
            CUtil::json_response(-1, '请先登录');
        }

        // 频率限制检查
//        $this->checkRateLimit('user-list', $userId);

        // 验证和清理分页参数
        list($page, $pageSize) = $this->validatePagination($form->page, $form->page_size);

        try {
            $service = GoodsRecommendService::getInstance();
            list($status, $result) = $service->getUserRecommendList(
                    $userId,
                    $page,
                    $pageSize,
                    $this->platformIds,
                    $this->spriceType
            );

            if ($status) {
                CUtil::json_response(1, 'OK', $result);
            } else {
                CUtil::json_response(-1, $result);
            }
        } catch (\Exception $e) {
            $this->safeErrorResponse("获取列表失败: " . $e->getMessage(), false);
        }
    }

    /**
     * @OA\Post(
     *     path="/main/goods-recommend/friend-list",
     *     summary="获取好友推荐商品列表",
     *     description="获取所有推荐商品按推荐次数从大到小排序",
     *     tags={"商品推荐"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page", type="integer", description="页码", example=1, default=1),
     *                         @OA\Property(property="page_size", type="integer", description="每页数量", example=10, default=10, maximum=50)
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码：1成功，-1失败"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="数据",
     *                 @OA\Property(property="list", type="array", description="商品列表，按推荐次数从大到小排序", @OA\Items(
     *                     type="object",
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="sku", type="string", description="商品SKU"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="cover_image", type="string", description="封面图片"),
     *                     @OA\Property(property="market_image", type="string", description="市场图片"),
     *                     @OA\Property(property="mprice", type="number", description="市场价"),
     *                     @OA\Property(property="price", type="number", description="售价"),
     *                     @OA\Property(property="goods_recommend_times", type="integer", description="推荐次数")
     *                 )),
     *                 @OA\Property(property="total", type="integer", description="总数"),
     *                 @OA\Property(property="page", type="integer", description="当前页码"),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量"),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     )
     * )
     * 获取好友推荐商品列表
     * @return void
     */
    public function actionFriendList()
    {
        $post = Yii::$app->request->post();

        // 表单验证
        $form = new GoodsFriendRecommendListForm();
        if (!$form->load($post, '') || !$form->validate()) {
            $errors = array_values($form->getFirstErrors())[0] ?? '参数错误';
            CUtil::json_response(-1, $errors);
        }

        $userId = intval($this->user_id ?? 0);
//      $this->checkRateLimit('friend-list', $userId);

        // 验证和清理分页参数
        list($page, $pageSize) = $this->validatePagination($form->page, $form->page_size);

        try {
            $service = GoodsRecommendService::getInstance();
            list($status, $result) = $service->getFriendRecommendList(
                    $userId,
                    $page,
                    $pageSize,
                    $this->platformIds,
                    $this->spriceType
            );

            if (!$status) {
                CUtil::json_response(-1, $result);
            }

            CUtil::json_response(1, 'OK', $result);
        } catch (\Exception $e) {
            $this->safeErrorResponse("获取列表失败: " . $e->getMessage(), false);
        }
    }

}
