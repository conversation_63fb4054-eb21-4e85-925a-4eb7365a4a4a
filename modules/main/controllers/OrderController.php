<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/14
 * Time: 11:27
 */
namespace app\modules\main\controllers;

use app\components\RateLimiter;
use app\constants\RespStatusCodeConst;
use app\jobs\CreateTradeInOrderJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\CodeModel;
use app\models\Response;
use app\modules\goods\services\OuserService;
use app\modules\main\formatters\Format;
use app\modules\main\formatters\order\BuyInfoFormatter;
use app\modules\main\models\UserCardModel;
use app\modules\main\services\GoodsReviewService;
use app\modules\main\services\AliPayService;
use app\modules\main\services\OrderService;
use app\modules\main\services\PayService;
use app\modules\main\services\TradeInService;
use RedisException;
use Yii;
use yii\db\Exception;

class OrderController extends CommController {

    private static $initializedFormatters = null;

    protected $format;

    protected static function getFormatters(): ?array
    {
        if (self::$initializedFormatters === null) {
            self::$initializedFormatters = [
                'buy-info' => new BuyInfoFormatter(),
            ];
        }
        return self::$initializedFormatters;
    }

    public function beforeAction($action): bool
    {
        $name = $action->id;

        $formatters = self::getFormatters();
        if (array_key_exists($name, $formatters)) {
            $this->format = new Format($formatters[$name]);
        }

        return parent::beforeAction($action);
    }

    /**
     * @OA\Post(
     *     path="/main/order/buy-info",
     *     summary="订单结算",
     *     description="通过传递参数进行金额预算",
     *     tags={"订单"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gcombines"},
     *                         @OA\Property(property="gcombines", type="string", description="商品列表"),
     *                         @OA\Property(property="aid", type="integer", description="地址Id"),
     *                         @OA\Property(property="gift_card_ids", type="string", description="礼品卡Id"),
     *                         @OA\Property(property="coin", type="integer", description="积分"),
     *                         @OA\Property(property="coin_type", type="integer", description="是否使用积分"),
     *                         @OA\Property(property="coupon_id", type="integer", description="优惠券Id")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(property="code", type="string", description="编码"),
     *                 @OA\Property(property="list", type="string", description="商品列表"),
     *                 @OA\Property(property="total", type="string", description="金额")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionBuyInfo()
    {

        $post                = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        Yii::$app->params['is_trade_in']= $post['is_trade_in'] ?? 0;

        if(strlen($this->user_id)>11){
            CUtil::json_response(-1, '请先授权哟！');
        }

        list($status, $ret) = by::Ouser()->BuyInfo($this->user_id, $post);
        $list = $ret['list'] ?? [];
        if($list){
            // 以旧换新价
            $list = Response::responseList($list,[
                'is_presale'        => 'int',
                'presale_time'   => 'time|ms',
                'start_payment'  => 'time|ms',
                'end_payment'    => 'time|ms',
                'surplus_time'   => 'is_int',
                'scheduled_number' => 'int',
            ]);
            $ret['list'] = $list;
        }

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 格式化返回数据，此方法不查库，只做数据格式化
        $ret = $this->format->format($ret, $this->platformId);

        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @throws Exception
     * 下单
     */
    public function actionBuy() {
        list($lock,$msg) = CUtil::payLock($this->user_id);
        if($lock){
            CUtil::json_response(-1,$msg);
        }

        $post     = Yii::$app->request->post();
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $post['union'] = substr($this->union,0,50);
        $post['euid'] = substr($this->euid,0,50);
        $post['referer'] = $this->referer ?? '';
        $post['platform_source'] = $this->platformSource ?? 0;

        // 以旧换新订单（验参数）
        $is_trade_in = $post['is_trade_in'] ?? 0;
        if ($is_trade_in && (empty($post['cate_code']) ||
                empty($post['item_brand']) ||
                empty($post['item_cates']) ||
                empty($post['item_model']) ||
                empty($post['in_express_time']) ||
                empty($post['trade_in_aid']))) {
            CUtil::json_response(-1, '以旧换新订单参数错误1');
        }
        Yii::$app->params['is_trade_in'] = $is_trade_in;

        list($status,$ret) = by::Omain()->createOrder($this->user_id,$this->api,$post);
        if(!$status){
            CUtil::json_response(-1,$ret);
        }elseif ($status === -1) {
            CUtil::json_response(CodeModel::STATUS['NOT_DELIVERY'],$ret);
        }elseif ($status === -12){
            CUtil::json_response(1,'ok',['has_new'=>1,'msg'=>'商品信息发生变动，将会刷新页面']);
        }

        // 以旧换新订单（异步创建）
        if ($is_trade_in) {
            $params = [
                'order_no'        => $ret['order_no'],
                'cate_code'       => $post['cate_code'],
                'item_brand'      => $post['item_brand'],
                'item_cates'      => $post['item_cates'],
                'item_model'      => $post['item_model'],
                'in_express_time' => $post['in_express_time'],
                'aid'             => $post['trade_in_aid'],
                'trade_in_remark' => $post['trade_in_remark'] ?? '',
            ];
            \Yii::$app->queue->push(new CreateTradeInOrderJob([
                'user_id' => $this->user_id,
                'params'  => $params
            ]));
        }

        CUtil::json_response(1,'ok', $ret);
    }

    /**
     * 常规下单动作，只产生订单
     * @return void
     * @throws Exception
     *
     */
    public function actionCommonBuy() {

        list($lock,$msg) = CUtil::payLock($this->user_id);
        if($lock){
            CUtil::json_response(-1,$msg);
        }

        $post     = Yii::$app->request->post();
        $post['pay_type'] = by::Omain()::PAY_BY_NO_SET;//没有设置支付平台
        $post['get_channel'] = $this->getChannel;
        $post['sprice_type'] = $this->spriceType;
        $post['union'] = substr($this->union,0,50);
        $post['euid'] = substr($this->euid,0,50);
        $post['referer'] = $this->referer ?? '';
        $post['platform_source'] = $this->platformSource ?? 0;

        // 以旧换新订单（验参数）
        $is_trade_in = $post['is_trade_in'] ?? 0;
        if ($is_trade_in && (empty($post['cate_code']) ||
                empty($post['item_brand']) ||
                empty($post['item_cates']) ||
                empty($post['item_model']) ||
                empty($post['in_express_time']) ||
                empty($post['trade_in_aid']))) {
            CUtil::json_response(-1, '以旧换新订单参数错误2');
        }
        // 以旧换新价格参数配置
        Yii::$app->params['is_trade_in'] = $is_trade_in;

        list($status,$ret) = by::Omain()->createOrder($this->user_id,$this->api,$post);
        if(!$status){
            CUtil::json_response(-1,$ret);
        }elseif ($status === -1) {
            CUtil::json_response(CodeModel::STATUS['NOT_DELIVERY'],$ret);
        }elseif ($status === -12){
            CUtil::json_response(1,'ok',['has_new'=>1,'msg'=>'商品信息发生变动，将会刷新页面']);
        }

        // 以旧换新订单（异步创建）
        if ($is_trade_in) {
            $params = [
                'order_no'        => $ret['order_no'],
                'cate_code'       => $post['cate_code'],
                'item_brand'      => $post['item_brand'],
                'item_cates'      => $post['item_cates'],
                'item_model'      => $post['item_model'],
                'in_express_time' => $post['in_express_time'],
                'aid'             => $post['trade_in_aid'],
                'trade_in_remark' => $post['trade_in_remark'] ?? '',
            ];
            \Yii::$app->queue->push(new CreateTradeInOrderJob([
                'user_id' => $this->user_id,
                'params'  => $params
            ]));
        }

        CUtil::json_response(1,'ok', $ret);
    }

    /**
     * @throws Exception
     * 订单列表
     */
    public function actionList() {

        $post      = Yii::$app->request->post();
        $page      = $post['page']         ?? 1;
        $status    = $post['status']       ?? -1;
        $order_no  = $post['order_no']     ?? "";
        $tab       = $post['tab']          ?? 0; //来源 0：我的订单；1：我的页面
        $page_size = $post['page_size']    ??10;
        $return    = [];

        $mOmain    = by::Omain();
        $statua    = explode(',', $status);
        if ( array_diff($statua, $mOmain::ORDER_STATUS) ) {
            CUtil::json_response(1,"OK",[]);
        }

        $rets       = empty($this->userAuth) ? [] : by::Ouser()->GetList($this->user_id, $status, $order_no, $this->platformSource, $page, $page_size);
        $can_remain = $tab == 1 ? true : false;

        // 评价的订单商品
        $reviewOrderGoods = [];

        foreach ($rets as $ret) {
            $info  = by::Ouser()->CommPackageInfo($this->user_id,$ret['order_no'],false,true,false,$can_remain,true,false,true,true);

            $rinfo = [
                    'order_no'         => $info['order_no'],
                    'deposit_order_no' => $info['deposit_order_no'] ?? '',
                    'status'           => $info['status'],
                    'oprice'           => $info['oprice'],
                    'exprice'          => $info['exprice'] ?? 0,
                    'deposit_price'    => $info['deposit_price'] ?? 0,
                    'price'            => $info['price'],
                    'dprice'           => $info['dprice'],
                    'real_price'       => $info['real_price'],
                    'subsidy_price'    => $info['subsidy_price'],
                    'coin'             => $info['coin'] ?? 0,
                    'coin_price'       => $info['coin_price'] ?? 0,
                    'cprice'           => $info['cprice'] ?? 0,
                    'goods'            => $info['goods'],
                    'is_presale_order' => $info['is_presale_order'] ?? 0, //是否参与过预售
                    'user_order_type'  => $info['user_order_type'] ?? 0,  //订单类型
                    'ctime'            => $info['ctime'] ?? 0,            //订单类型
                    'try_info'         => $info['try_info'] ?? [],        //先试后买信息
                    'ac_info'          => $info['ac_info'] ?? [],         //活动信息
            ];

            $reviewOrderGoods[] = [
                'order_no'    => $info['order_no'],
                'skus'       => array_column($info['goods'], 'sku'),
                'status'      => $info['status'],
                'finish_time' => $info['finish_time'],
            ];

            if ($tab == 1) {
                switch ($info['status']) {
                    case $mOmain::ORDER_STATUS['WAIT_PAY']:
                        $rinfo['remain_time']   = $info['remain_time'] ?? 0;
                        break;
                    case $mOmain::ORDER_STATUS['WAIT_SEND']:
                    case $mOmain::ORDER_STATUS['WAIT_RECEIVE']:
                        $rinfo['last_time']   = $info['pay_time'] ?? 0;
                        break;
                    default :
                        $rinfo['last_time']     = $info['ctime'] ?? 0;
                        break;
                }
            }
            $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
            $rinfo['will_coin'] = bcmul(bcadd($info['price'], $info['deposit_price'] ?? 0), $rate, 2);

            //绑定积分
            $rinfo['will_coin'] = by::memberCenterModel()->GetCoinByOrderCoin($this->user_id, $rinfo['will_coin'], $info['ctime'] ?? 0);

            $rinfo['deposit_order_info'] = $info['deposit_order_info'] ?? [];


            //判断流水
            $rinfo['has_opay'] = 0;
            if($rinfo['deposit_order_no']){
                $aOpay      = by::model('OPayModel','goods')->GetOneInfo($rinfo['order_no']);
                if($aOpay && (CUtil::uint($aOpay['pay_type']) !== by::Omain()::PAY_BY_NO_SET && bcsub(time(), $aOpay['ptime']) < 7100)){
                    $rinfo['has_opay'] = 1;//已经产生流水
                }
            }

            $rinfo = $this->_responseList($rinfo);

            $return['list'][] = $rinfo;

        }

        // 批量设置评价状态
        if ($reviewOrderGoods) {
            $reviewStatus = GoodsReviewService::getInstance()->batchGetReviewStatus($reviewOrderGoods);
            $return['list'] =  GoodsReviewService::getInstance()->batchSetReviewStatus($return['list'], $reviewStatus);
        }

        if ($status <= 0 && !in_array($this->platformSource,[9,10])){
            $depositInfos = empty($this->userAuth)?[]:by::Odeposit()->getDepositInfos($this->user_id,$status,'',$page, $page_size);
            if($depositInfos){
                foreach ($depositInfos as $depositInfo){
                    if(isset($depositInfo['status']) && $depositInfo['status'] <300){
                        $depositOrder['order_no']         = $depositInfo['order_no'];
                        $depositOrder['goods']            = $depositInfo['goods'];
                        $depositOrder['is_presale_order'] = by::Gmain()::PTYPE['PRESALE'];
                        $depositOrder['status']           = $depositInfo['status'];
                        $depositOrder['price']            = $depositInfo['price'];
                        $depositOrder['oprice']           = $depositInfo['goods'][0]['oprice'] ?? 0;
                        $depositOrder['real_price']       = $depositInfo['real_price'] ?? 0;
                        $depositOrder['user_order_type']  = by::Omain()::USER_ORDER_TYPE['DEPOSIT'];
                        $depositOrder['ctime']            = $depositInfo['ctime'] ?? 0;
                        $depositOrder = $this->_responseList($depositOrder);
                        $return['list'][]                   = $depositOrder;
                    }
                }
            }
            //排序
            if(isset($return['list']) && $return['list']){
                  $sort = array_column($return['list'],'ctime');
                  array_multisort($sort,SORT_DESC,$return['list']);
            }
        }

        $count           = by::Ouser()->GetListCount($this->user_id, $status, $order_no,$this->platformSource);
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $return['total'] = $count;
        // 订单的以旧换新状态
        $return['list'] = TradeInService::getInstance()->setTradeInTag($return['list'] ?? []);
        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @param $info
     * @return mixed
     * 订单前端输出适配
     */
    private function _responseList($info){

        //输出封装
        $goods = $info['goods'];
        if($goods){
            foreach ($goods as &$good){
                $presale_info = $good['presale_info'] ?? [];
                if($presale_info){
                    $good['presale_info'] = Response::responseList($presale_info,[
                        'is_presale'        => 'int',
                        'presale_time'   => 'time|ms',
                        'start_payment'  => 'time|ms',
                        'end_payment'    => 'time|ms',
                        'surplus_time'   => 'is_int',
                        'scheduled_number' => 'int',
                    ]);
                }
            }
            $info['goods'] = $goods;
        }

        return $info;
    }


    /**
     * @throws Exception
     * 订单详情
     */
    public function actionInfo()
    {
        $post = Yii::$app->request->post();
        $order_no = $post['order_no'] ?? 0;

        $info     = by::Ouser()->CommPackageInfo($this->user_id,$order_no,false,true,true,true,true,false,true,true);

        if ($info) {
            //是否可退款
            $can_status = by::Omain()->GetCanActStatus();
            $can_refund = 0;

            if ( in_array($info['status'], $can_status)) {
                $can_refund = 1;
            }

            // 虚拟商品订单不能退款
            if (count($info['goods']) == 1 && $info['goods'][0]['type'] == 2 && $info['goods'][0]['goods_type'] == 2) {
                $can_refund = 0;
            }

            //添加可修改数据
            if(isset($info['deposit_order_no']) && $info['deposit_order_no']){
                $ouserService = new OuserService();
                //根据订单自身属性判断
                $coin = CUtil::uint($post['coin'] ?? 0);
                if(empty($coin)){
                    $coin = $info['coin'];
                }
                $coin_type = intval($post['coin_type'] ?? 0);
                if(intval($info['coin']??0) > 0 && $coin_type !== 2){
                    $coin_type = 1;
                }

                $post['coin']          = $coin;
                $post['coin_type']     = $coin_type;


                //礼品卡校验
                $giftCardIdsStr = $post['gift_card_ids'] ?? '';
                $isUseGiftCard  = 1;//1使用 0不使用 不使用查详情 使用修改详情
                if (empty($giftCardIdsStr)) {
                    $giftCardIdsStr = $info['gift_card_ids'] ?? '';
                    $isUseGiftCard  = 0;
                }
                $post['is_use_gift_card'] = $isUseGiftCard;
                $post['gift_card_ids']    = $giftCardIdsStr;

                list($s,$info) = $ouserService->getUserCanUse($this->user_id,$info,$post);
                if(!$s){
                    CUtil::json_response(-1, $info);
                }
            }


            $info['can_refund'] = $can_refund;
            $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
            $info['will_coin']  = bcmul(bcadd($info['price'], $info['deposit_price'], 2), $rate, 2);

            !YII_ENV_PROD && CUtil::debug($info['price'].'|'.$info['will_coin'],'coin_type');
            $info['will_coin'] = by::memberCenterModel()->GetCoinByOrderCoin($this->user_id,$info['will_coin'],$info['ctime']??0);
            if (isset($info['goods'][0]['is_employee']) && $info['goods'][0]['is_employee'] == 1) {
                $rate               = byNew::PointConfigModel()::getConfig()['employee_reward_rate'] ?? 2;
                $info['will_coin']  = round(bcmul(bcadd($info['price'], $info['deposit_price'], 2), $rate, 2));
            }


            unset($info['coupon_id'],$info['mail'],$info['consume_id']);

            //订单是否产生过流水
            $info['has_opay'] = 0;
            $aOpay      = by::model('OPayModel','goods')->GetOneInfo($order_no);
            $depositOrderNo = $info['deposit_order_no'] ?? '';
            if ($aOpay && $depositOrderNo && ((CUtil::uint($aOpay['pay_type']) !== by::Omain()::PAY_BY_NO_SET && bcsub(time(), $aOpay['ptime']) < 7100) || $aOpay['price'] == 0)) {
                $info['has_opay'] = 1;//已经产生流水
            }

            // 设置评价状态
            $reviewStatus = GoodsReviewService::getInstance()->getReviewStatus($info['order_no'], array_column($info['goods'], 'sku'), $info['status'], $info['finish_time']);
            foreach ($info['goods'] as &$good) {
                $good['review_status'] = $reviewStatus[$good['sku']] ?? GoodsReviewService::REVIEW_STATUS['CAN_REVIEW'];
            }

            $info['pay_type'] = $aOpay['pay_type'] ?? 0;//支付类型
            $info['payment_plan'] = by::oPay()->getPayPlan($aOpay['payment_plan'] ?? 'NO_INST');//分期支付计划


            $info = $this->_responseList($info);

            // 以旧换新订单
            $info['tradeInOrder'] = TradeInService::getInstance()->getOrderInfo($order_no);
            $info['is_trade_in']  = $info['tradeInOrder'] ? 1 : 0;
            // 一元抢购订单 提供前端做判断不显示积分、优惠券等优惠
            $info['is_one_yuan_order'] = !empty(by::OneYuanSeckillModel()->getOneYuanSeckillInfo($order_no));
        }

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @throws Exception
     * 更新订单详情
     */
    public function actionUpdateInfo()
    {
        $post   = Yii::$app->request->post();
        $ouserService = new OuserService();
        //不能使用优惠券
        unset($post['coupon_id'],$post['consume_id']);
        list($s,$data) = $ouserService->updateOrderRecord($this->user_id,$post);
        if(!$s){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @throws Exception
     * 取消订单
     */
    public function actionCancel()
    {
        $order_no           = Yii::$app->request->post('order_no', '');
        $r_type             = Yii::$app->request->post('r_type', 0);

        if (!in_array($r_type, array_keys(by::Omain()::R_TYPE))) {
            CUtil::json_response(-1, "请选择正确的取消原因");
        }

        list($status, $ret) = by::Omain()->Recycle($this->user_id, $order_no, $r_type);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * 实时查询订单状态
     * @return void
     */
    public function actionQueryPayStatus()
    {
        $order_no           = Yii::$app->request->post('order_no', '');
        if(empty($order_no)){
            CUtil::json_response(-1, "订单号不存在！");
        }
        list($status, $ret) = by::Omain()->GueryOrderPayInfo($order_no,$this->user_id);
        if(!$status){
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @throws Exception
     * 确认收货
     */
    public function actionFinish()
    {
        $order_no       = Yii::$app->request->post('order_no', '');
        list($s, $m)    = by::Omain()->Finish($this->user_id, $order_no);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * 未付款订单拉起支付
     */
    public function actionPay()
    {
        $order_no = Yii::$app->request->post('order_no', '');
        $pay_type = Yii::$app->request->post('pay_type', by::Omain()::PAY_BY_WX);
        $payment_plan = Yii::$app->request->post('payment_plan', 'NO_INST');
        $pay_type = CUtil::uint($pay_type);

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency($this->user_id, $unique_key, 1);
        if (!$anti) {
            CUtil::json_response(-1, '请勿频繁操作');
        }

        list($lock,$msg) = CUtil::payLock($this->user_id);
        if($lock){
            CUtil::json_response(-1,$msg);
        }

        $attach =['source'=> by::WxPay()::SOURCE['MALL']];

        if($pay_type == by::Omain()::PAY_BY_WX || $pay_type == by::Omain()::PAY_BY_WX_APP){
            list($status, $ret) = by::WxPay()->AgainPay($this->user_id, $order_no, $this->api, $attach, '', $pay_type);
        }elseif($pay_type == by::Omain()::PAY_BY_WX_H5){
            list($status, $ret) = by::WxH5Pay()->AgainPay($this->user_id, $order_no, $this->api, $attach);
        }elseif ($pay_type == by::Omain()::PAY_BY_ALIPAY || $pay_type == by::Omain()::PAY_BY_MP_WX || $pay_type == by::Omain()::PAY_BY_MP_ALIPAY|| $pay_type == by::Omain()::PAY_JD_BAITIAO||$pay_type == by::Omain()::PAY_JD_BAITIAO_APP||$pay_type == by::Omain()::PAY_JD_BAITIAO_PC||$pay_type == by::Omain()::PAY_JD_BAITIAO_H5||$pay_type == by::Omain()::PAY_BY_MP_WEB_WX_H5|| by::Omain()::PAY_BY_MP_WEB_ALIPAY_H5) { // 支付宝、中台支付（微信/支付宝）
            list($status, $ret) = PayService::getInstance()->againPay($this->user_id, $order_no, 1, $pay_type,['payment_plan'=>$payment_plan]);
        }else{
            $status = false;
            $ret = '未指定对应支付平台！';
        }

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 补丁：替换返回参数
        if ($pay_type == by::Omain()::PAY_BY_WX_APP) {
            $ret['prepayId'] = $ret['prepay_id'];
            $ret['sign'] = $ret['paySign'];
            unset($ret['prepay_id'], $ret['paySign']);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * 物流详情
     */
    public function actionExpress()
    {
        $order_no           = Yii::$app->request->post('order_no', 0);
        list($status, $ret,$data) = by::Oad()->GetExpressByOrderNo($this->user_id, $order_no);

        if (!$status) {
            CUtil::json_response(-1, $ret,$data);
        }

        CUtil::json_response(1, 'ok', $data);
    }


    /**
     * 取消、退款原因
     */
    public function actionRtList()
    {
        $type   = Yii::$app->request->post('type', 1); // 1:取消 2:退款
        $list   = by::Omain()->GetRtypeList($type);

        CUtil::json_response(1,"OK",$list);
    }

    /**
     * @throws Exception
     * 退款说明上传图片
     */
    public function actionRupload()
    {
        $post        = Yii::$app->request->post();
        $file        = $_FILES['file'] ??  "";
        if(empty($this->user_id)){
            $this->user_id = CUtil::get_client_ip();
        }

        list($status,$ret) = by::Orefund()->FileSave($this->user_id,$file,$post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,"OK",$ret);

    }

    /**
     * @throws Exception
     * 申请退款
     */
    public function actionRefund()
    {
        $post       = Yii::$app->request->post();

        list($status, $refund_no) = by::OrefundMain()->ApplyRefund($this->user_id, $post);

        if (!$status) {
            CUtil::json_response(-1, $refund_no);
        }

        CUtil::json_response(1, 'ok',['refund_no' => $refund_no]);
    }


    /**
     *
     * @OA\Post(
     *     path="/main/order/usable-coupons",
     *     summary="订单-用户可使用优惠券",
     *     description="订单-用户可使用优惠券",
     *     tags={"订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/BaseOrderRequest"),
     *          @OA\Schema(
     *          required = {"gcombines"},
     *          @OA\Property(property="coupon_id", type="integer", default="", description="用户优惠券ID"),
     *          @OA\Property(property="gcombines", type="array",@OA\Items(ref="#/components/schemas/Gcombines"),description="优惠券详细信息"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/OrderCardList",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
	public function actionUsableCoupons()
	{
		//[{"gid":1,"sid":0,"num":1,"is_box":0}]
		$gcombines = Yii::$app->request->post('gcombines', ''); //查询的json数据
		$coupon_id = Yii::$app->request->post('coupon_id', 0); //使用的优惠券
        $consume_id = Yii::$app->request->post('consume_id', 0); //使用的消费券
        $is_trade_in = Yii::$app->request->post('is_trade_in', 0); //以旧换新订单
        Yii::$app->params['is_trade_in'] = $is_trade_in;
        $gcombines = json_decode($gcombines, true);

		if (empty($gcombines)) {
			CUtil::json_response(-1, '商品不存在');
		}
        // 设置返回数据
        $result = ['total' => 0, 'list' => [], 'consume' => [], 'select' => [],'all_price'=>0];
        $userCard = by::userCard();

        // 获取可用的优惠券及其列表
        list($s1, $res1) = $userCard->canUseCard($this->user_id, $userCard::TYPE['all'], $gcombines, 0, $this->getChannel, $this->spriceType);
        list($s2, $res2) = $userCard->canUseCard($this->user_id, $userCard::TYPE['consume'], $gcombines, 0, $this->getChannel, $this->spriceType);
        $list1  = $res1['list'] ?? [];
        $total1 = $res1['total'] ?? 0;
        $ids1   = $res1['select']['ids'] ?? [];
        $list2  = $res2['list'] ?? [];
        $total2 = $res2['total'] ?? 0;
        $ids2   = $res2['consume']['ids'] ?? [];

        // 选择使用的优惠券
        if($coupon_id >= 0 ){
            list($s3, $res3) = $userCard->canUseCard($this->user_id, $userCard::TYPE['all'], $gcombines, $coupon_id, $this->getChannel, $this->spriceType);
        }else{
            list($s3, $res3) = [true,['select'=>['id'=>-1]]];
        }
        $cardType = $res3['select']['type'] ?? -1;

        // 选择使用的消费券
        if($consume_id >= 0 && $cardType != by::userCard()::TYPE['voucher']){
            list($s4, $res4) = $userCard->canUseCard($this->user_id, $userCard::TYPE['consume'], $gcombines, $consume_id, $this->getChannel, $this->spriceType, false,$res3['select']['cprice'] ?? 0);
        }else{
            list($s4, $res4) = [true,['consume'=>['id'=>-1]]];
        }

        // 超限判断
        if ($s4 && $consume_id > 0 && $coupon_id > 0) {
            $new_consume_id = $res4['consume']['id'] ?? 0;
            if($consume_id != $new_consume_id || empty($new_consume_id)){
                //无法使用，该优惠金额已超出剩余金额~
                CUtil::json_response(1, '', ['consume_code' => 100]);
            }
        }

        // 组合数据
        $result['total'] = $total1 + $total2;
        $result['list'] = array_merge($list1, $list2);
        $result['select'] = $res3['select'] ?? [];
        $result['select']['ids'] = $ids1;
        $result['consume'] = $res4['consume'] ?? [];
        $result['consume']['ids'] = $ids2;
        $result['all_price'] = $res1['all_price'] ?? 0;


        //排序
        if(isset($result['list']) && $result['list']){
            $result['list'] = $userCard->sortCardList($result['list'],$result['consume'],$result['select']);
        }

        CUtil::json_response(1, 'ok', $result);
	}

    /**
     * @throws Exception
     * 退货物流单号
     */
    public function actionRexpress()
    {
        $post       = Yii::$app->request->post();

        list($s, $m) = by::Orefund()->Rexpress($this->user_id, $post);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');

    }

    /**
     * @throws Exception
     * 退款列表
     */
    public function actionRlist()
    {
        $post      = Yii::$app->request->post();
        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 50;
        $return    = [];

        $rets = by::Orefund()->GetList($this->user_id, -1, $this->platformSource, $page, $page_size);

        foreach ($rets as $ret) {
            $info  = by::Orefund()->CommPackageInfo($this->user_id,$ret['refund_no'],false,true);

            $rinfo = [
                'refund_no'       => $info['refund_no'],
                'status'          => $info['status'],
                'm_type'          => $info['m_type'],
                'price'           => $info['real_price'],
                'real_price'      => $info['real_price'],
                'goods'           => $info['goods'],
                'try_info'        => $info['try_info'] ?? [],
                'user_order_type' => $info['user_order_type'] ?? 1,
                'coin'            => $info['coin'] ?? 0,
            ];

            $return['list'][] = $rinfo;
        }

        $count                     = by::Orefund()->GetListCount($this->user_id);
        $return['pages']           = CUtil::getPaginationPages($count, $page_size);
        $return['total']           = $count;

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws Exception
     * 退款详情
     */
    public function actionRinfo()
    {
        $refund_no = Yii::$app->request->post('refund_no', 0);

        $info      = by::Orefund()->CommPackageInfo($this->user_id,$refund_no,false,true);

        if (!empty($info)) {

            if ($info['m_type'] == 2) {
                $remain_time    = $info['ctime'] + by::Orefund()::EXPRESS_EXPIRE - intval(START_TIME);
                $remain_time    = $remain_time < 0 ? 0 : $remain_time;
            }

            $info['remain_time']        = $remain_time ?? 0;
            list($_, $info['ostatus'])  = by::Omain()->SplitOrderStatus($info['ostatus']);
        }

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 导购订单列表
     */
    public function actionSlist()
    {
        $post      = Yii::$app->request->post();
        $page      = $post['page']   ?? 1;
        $t_type    = $post['t_type'] ?? 1;
        $status    = $post['status'] ?? -1;
        $page_size = 500;
        $return    = [];

        $mOsource  = by::Osource();
        $mOuser    = by::Ouser();

        if ( !in_array($t_type, $mOsource::T_TYPE) ) {
            $return['pages'] = 1;
            CUtil::json_response(1, "OK", $return);
        }

        if ( !in_array($status, by::Omain()::ORDER_STATUS) ) {
            $return['pages'] = 1;
            CUtil::json_response(1, "OK", []);
        }

        $rets = $mOsource->GetList($this->user_id, by::Osource()::GET_TYPE['PAGE'], $t_type, $status, $page, $page_size);

        foreach ($rets as $ret) {
            $info  = $mOuser->CommPackageInfo($ret['user_id'],$ret['order_no'],true,true);

            $rinfo = [
                'user_id'       => $info['user_id'],
                'order_no'      => $info['order_no'],
                'status'        => $info['status'],
                'oprice'        => $info['oprice'],
                'price'         => $info['price'],
                'real_price'    => $info['real_price'],
                'goods'         => $info['goods'],
                'user'          => $info['user'],
                'ctime'         => $info['ctime'],
            ];

            $return['list'][] = $rinfo;
        }

        if ($page == 1) {
            $count           = $mOsource->GetListCount($this->user_id, $t_type, $status);
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
            $price           = $mOsource->GetListSum($this->user_id, $t_type, $status);

            $return['total'] = [
                'count' => $mOsource->GetListCount($this->user_id, $t_type, $status, '', false),
                'price' => by::Gtype0()->totalFee($price, 1)
            ];
        }

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws Exception
     * 导购订单详情
     */
    public function actionSinfo()
    {
        $order_no = Yii::$app->request->post('order_no', 0);
        $uid      = Yii::$app->request->post('uid', 0);

        $info = by::Osource()->GetOrderInfo($this->user_id,$uid, $order_no);

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 导购退款列表
     */
    public function actionSrList()
    {
        $t_type = Yii::$app->request->post('t_type', 1);

        $return = by::Orefund()->getSrList($this->user_id, $t_type, by::Orefund()::SOURCE['SALE']);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws Exception
     * 导购退款详情
     */
    public function actionSrInfo()
    {
        $refund_no = Yii::$app->request->post('refund_no', 0);
        $uid       = Yii::$app->request->post('uid', 0);

        $r_info = by::Orefund()->CommPackageInfo($uid, $refund_no, false, true);

        if (!empty($r_info)) {
            if ($r_info['m_type'] == 2) {
                $remain_time = $r_info['ctime'] + by::Orefund()::EXPRESS_EXPIRE - intval(START_TIME);
                $remain_time = $remain_time < 0 ? 0 : $remain_time;
            }

            $r_info['remain_time']       = $remain_time ?? 0;
            list($_, $r_info['ostatus']) = by::Omain()->SplitOrderStatus($r_info['ostatus']);

            unset($r_info['images']);
        }

        CUtil::json_response(1, 'ok', $r_info);
    }

    /**
     * 免邮价格
     */
    public function actionFreight()
    {
        $mOfreight  = by::model('OfreightModel', 'goods');
        $type       = $mOfreight::TYPE['WITH_FREE'];

        $data       = $mOfreight->GetOneByType($type);

        $cnf        = $data['cnf'] ?? 0;
        $cnf        = by::Gtype0()->totalFee($cnf, 1);

        //根据个人权益定义满运费
        $shippingPrice = by::memberCenterModel()->getUserRightList($this->user_id,'free_shipping');
        $shippingPrice && $cnf = min($cnf,$shippingPrice);

        CUtil::json_response(1, 'ok', ['val' => $cnf]);
    }


    /**
     * @OA\Post(
     *     path="/main/order/order-status",
     *     summary="订单-查询订单支付状态",
     *     description="订单-查询订单支付状态",
     *     tags={"订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"order_no"},
     *          @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *          @OA\Property(property="order_type", type="int", default="", description="1-（普通订单、尾款订单），2-（定金订单）"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/OrderStatusResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionOrderStatus()
    {
        // 确保用户ID有效
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再查询订单状态！');
        }

        // 从请求中获取订单号并确保为字符串类型
        $orderNo = Yii::$app->request->post('order_no', '');
        // 订单类型：1-（普通订单、尾款订单），2-（定金订单）
        $orderType = Yii::$app->request->post('order_type', 1);
        $orderNo   = trim((string) $orderNo);
        $orderType = intval($orderType);

        // 校验订单号是否为空
        if (empty($orderNo)) {
            CUtil::json_response(-1, '订单号不能为空');
        }

        // 生成唯一的请求标识并检查请求频率
        $unique_key  = CUtil::getAllParams(__FUNCTION__, $orderNo);
        $rateLimiter = new RateLimiter();
        $isAllowed   = $rateLimiter->checkRateLimit($unique_key, $userId, 10, 5);

        if (!$isAllowed) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 调用服务层方法获取订单状态
        list($status, $data) = OrderService::getInstance()->getOrderStatus($userId, $orderNo, $orderType);

        // 返回 JSON 响应
        if (!$status) {
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, 'ok', $data);
    }


}
