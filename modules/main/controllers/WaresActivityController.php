<?php


namespace app\modules\main\controllers;


use app\models\CUtil;
use app\modules\main\services\WaresActivityService;
use Yii;
use yii\db\Exception;

class WaresActivityController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/wares-activity/activity-list",
     *     summary="获取活动列表",
     *     description="获取活动列表",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityList()
    {
        $page      = Yii::$app->request->post('page', 1);
        $page_size = Yii::$app->request->post('page_size', 20);
        list($status, $data) = WaresActivityService::getInstance()->GetActivityList(['status' => 0], $page, $page_size);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     * @OA\Post(
     *     path="/main/wares-activity/activity-info",
     *     summary="获取活动详情",
     *     description="获取活动详情",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"ac_id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="ac_id", type="string", default="", description="活动ID")
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityInfo()
    {
        $acId   = Yii::$app->request->post('ac_id', 0);
        $userId = $this->user_id;
        list($status, $data) = WaresActivityService::getInstance()->GetActivityInfo($acId, $userId);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/main/wares-activity/activity-apply",
     *     summary="申请活动",
     *     description="申请活动",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"ac_id"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="ac_id", type="string", default="", description="活动ID")
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityApply()
    {
        $acId   = Yii::$app->request->post('ac_id', 0);
        $userId = $this->user_id;

        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再申请！');
        }

        list($status, $data) = WaresActivityService::getInstance()->ActivityApply($acId, $userId);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, $data);
    }

    /**
     * @OA\Post(
     *     path="/main/wares-activity/query-survey-result",
     *     summary="查询问卷分数",
     *     description="查询问卷分数",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                  required={"ac_id","formKey"},
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                          @OA\Property(property="ac_id", type="string", default="", description="活动ID"),
     *                          @OA\Property(property="formKey", type="string", default="", description="问卷Key"),
     *                      )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionQuerySurveyResult()
    {
        $userId     = $this->user_id;
        $activityId = Yii::$app->request->post('ac_id', 0);
        $formKey    = Yii::$app->request->post('formKey', '');
        list($status, $ret) = WaresActivityService::getInstance()->QuerySurveyResult($userId, $formKey, $activityId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }
}
