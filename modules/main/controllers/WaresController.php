<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\wares\services\goods\GoodsSpecsService;
use app\modules\wares\services\goods\GoodsStockService;
use app\modules\wares\services\goods\IndexGoodsMainService;
use yii\db\Exception;

class WaresController extends CommController {

    /**
     * @OA\Post(
     *     path="/main/wares/points-enum",
     *     summary="获取积分商品枚举内容",
     *     description="获取积分商品枚举内容",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsEnumRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionPointsEnum()
    {
        $post = \Yii::$app->request->post();
        $post['user_id'] = $this->user_id;
        $post['platformIds'] = $this->platformIds;
        list($status,$ret) = IndexGoodsMainService::getInstance()->getPointsEnum($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,'OK', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/wares/list",
     *     summary="商品列表",
     *     description="商品列表",
     *     tags={"新商品模块"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresListRequest"),
     *          @OA\Schema(ref="#/components/schemas/PageRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $page       = CUtil::uint($post['page'] ?? 1);
        $page_size  = CUtil::uint($post['page_size'] ?? 25);
        $input = [
            'user_id'     => $this->user_id,
            'source'      => CUtil::uint($post['source'] ?? 1),
            'tab'         => intval($post['tab'] ?? 0),
            'position'    => intval($post['position'] ?? 0),
            'sort_rule'   => trim($post['sort_rule'] ?? ''),
            'is_point'    => CUtil::uint($post['is_point'] ?? 0),
            'is_my'       => CUtil::uint($post['is_my'] ?? 0),
            'platformIds'    => $this->platformIds
        ];

        $indexGoodsMainService = new IndexGoodsMainService();
        list($status,$ret) = $indexGoodsMainService->GoodsList($input,$page,$page_size);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }
        CUtil::json_response(1,'OK', $ret);

    }

    /**
     * @OA\Post(
     *     path="/main/wares/info",
     *     summary="商品详情",
     *     description="商品详情",
     *     tags={"新商品模块"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresAttrInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionInfo()
    {
        $post = \Yii::$app->request->post();
        $gid  = CUtil::uint($post['gid'] ?? 0);

        $indexGoodsMainService = new IndexGoodsMainService();
        $aData = $indexGoodsMainService->IndexMainInfoByGid($gid,$this->platformIds,true,true,true, $this->user_id);
        $aData['goods_limit_num'] = $aData['limit_num'] ?? 0;
        if (empty($aData)) {
            CUtil::json_response(-1, '商品不存在', ['good_redirect' => 1]);
        }
        $aData['alert_info'] = $indexGoodsMainService->AlertGoodsInfo($this->user_id,$aData);

        //判断限购数
        if (strlen($this->user_id) < 12 && !empty($this->user_id)) {
            $aData['limit_num'] = by::Ogoods()->GetLimitBuyGid($this->user_id, $gid, $aData['limit_num'] ?? 0, 2);
        }

        CUtil::json_response(1, "OK", [
            'goods' => $aData,
        ]);

    }



    /**
     * @OA\Post(
     *     path="/main/wares/attr-stock",
     *     summary="商品属性库存",
     *     description="商品属性库存",
     *     tags={"新商品模块"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresAttrStockRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WaresAttrStockResponse",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionAttrStock() {
        $post   = \Yii::$app->request->post();
        $gid    = $post['gid'] ?? 0;   // 7
        $av_ids = $post['av_ids'] ?? "";  //"[18]"
        $return = GoodsStockService::getInstance()->InStock($gid, $av_ids);
        CUtil::json_response(1, 'ok', $return);
    }



    /**
     * @OA\Post(
     *     path="/main/wares/specs-info",
     *     summary="商品规格查询（普通/多规格）",
     *     description="商品规格查询（普通/多规格）",
     *     tags={"新商品模块"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresSpecsInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WaresSpecsInfoResponse",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSpecsInfo() {

        $post   = \Yii::$app->request->post();
        $gid        = $post['gid']      ?? 0;
        $av_ids     = $post['av_ids']   ?? "";
        $num        = $post['num']      ?? 1;
        $platformIds = $this->platformIds;

        list($s, $aData) = GoodsSpecsService::getInstance()->GetOneByAvIds($gid,$av_ids,$num,$platformIds);
        if(!$s){
            CUtil::json_response(-1, $aData);
        }

        CUtil::json_response(1, 'ok', $aData);
    }




    /**
     * @OA\Post(
     *     path="/main/wares/attr-info",
     *     summary="商品规程查属性",
     *     description="商品规程查属性",
     *     tags={"新商品模块"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(
     *       mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresAttrInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WaresAttrStockResponse",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionAttrInfo() {
        $post       = \Yii::$app->request->post();
        $gid        = $post['gid']      ?? 0;

        list($s, $aData) = GoodsSpecsService::getInstance()->GetAttrByGid($gid);
        if(!$s){
            CUtil::json_response(-1, $aData);
        }

        CUtil::json_response(1, 'ok', $aData);
    }
}
