<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2022/1/25
 * Time: 17:54
 */
namespace app\modules\main\controllers;

use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use app\modules\main\services\WikiService;
use RedisException;
use yii\db\Exception;

class WikiController extends CommController
{

    /**
     * 产品百科
     */
    public function actionProductWiki(){
        $list = by::Sconfig()->getWiki();
        CUtil::json_response(1, 'ok',$list);
    }

    /**
     * @throws Exception
     * 用户调研
     */
    public function actionSurvey(){
        $data = by::Sconfig()->getConfig();
        CUtil::json_response(1, 'ok',$data);
    }

    /**
     * 话题列表
     */
    public function actionTlist() {
        $post       = \Yii::$app->request->post();
        $page       = $post['page']     ?? 1;
        $pid        = $post['pid']      ?? 0;
        $level      = $post['level']     ??-1;
        $page_size  = 50;

        $mWtopic    = by::Wtopic();
        $list       = $mWtopic->GetList($level, $pid, $page, $page_size);
        foreach($list as $val) {
             $info = $mWtopic->GetOneById($val['id'] ?? 0);
             $infoLevel= intval($info['level'] ?? 0);
             if($infoLevel == 1){
                 list($s, $aLists, $pages) = by::WdynamicMain()->GetDynamicsList($page, $info['id']??0 ,0);
                 !$s && $info = [];
             }
             $info && $data[] =$info;
        }

        $return['list']     = $data ?? [];

        $count              = by::Wtopic()->GetListCount($level, $pid);
        $return['pages']    = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/main/wiki/wlist",
     *     summary="内容列表",
     *     description="内容列表",
     *     tags={"内容管理"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="t2", type="string",description="话题id"),
     *              @OA\Property(property="page",type="string",description="页数"),
     *              @OA\Property(property="sort",type="intger",description="排序 0 时间排序 1 热度排序"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionWlist()
    {
        $post       = \Yii::$app->request->post();
        $page       = CUtil::uint($post['page']     ?? 1);
        $t2         = CUtil::uint($post['t2']       ?? 0);
        $sort       = CUtil::uint($post['sort']     ?? 0); //0 时间排序 1 热度排序

        list($s, $aLists, $pages) = by::WdynamicMain()->GetDynamicsList($page, $t2, $sort);

        if(!$s) {
            CUtil::json_response(1,"已加载完所有数据",[]);
        }

        $ret    = [];
        //拼接点赞数和所需字段
        foreach ($aLists as $aList) {

            $info     = by::Wdynamic()->GetInfoByDid($aList['id'], true);
            $roleInfo = by::WikiRole()->GetOneInfoById($info['role_id'] ?? 0);

            if (empty($info)) {
                continue;
            }

            $ninfo = [
                'did'         => $info['did'],
                'cover_image' => $info['cover_image'],
                'author'      => $info['author'] ?? '',
                'icon'        => $roleInfo['icon'] ?? '',
                'title'       => $info['title'],
                'clicks'      => $aList['clicks'] ?? 0,
            ];

//            list($s, $m, $praise) = by::Wpraise()->GetPraiseCount($aList['id']);
//
//            $ninfo['praise']    = $praise;

            $ret[]              = $ninfo;
        }

        CUtil::json_response(1,"OK",[
            'list'  => $ret,
            'pages' => $pages,
        ]);

    }


    /**
     * @OA\Post(
     *     path="/main/wiki/winfo",
     *     summary="内容详情",
     *     description="内容详情",
     *     tags={"内容管理"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="api", type="string", default="a_1664246268", description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *                 @OA\Property(property="sign", type="string", default="", description="签名"),
     *                 @OA\Property(property="sign_time", type="string", default="", description="签名时间"),
     *                 @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *                 @OA\Property(property="did", type="string", description="内容id"),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionWinfo()
    {
        $post       = \Yii::$app->request->post();
        $did        = $post['did']     ?? 0;

        $info     = by::Wdynamic()->GetInfoByDid($did, true);

        if (empty($info)) {
            CUtil::json_response(-1, '当前数据已下架');
        }

        //角色标签
        $roleInfo = by::WikiRole()->GetOneInfoById($info['role_id'] ?? 0);
        $info['icon']   = $roleInfo['icon'] ?? '';
        //浏览数
        $mainInfo = by::WdynamicMain()->GetInfoByDid($info['did'] ?? '');
        $info['clicks'] = $mainInfo['clicks'] ?? 0;
        //点赞数
        list($s, $m, $praise)   = by::Wpraise()->GetPraiseCount($did);
        $info['praise']         = $praise;

        //当前用户是否已点赞
        list($s, $m, $has_praise)= by::Wpraise()->HasPraised($this->user_id, $did);
        $info['has_praise']      = $has_praise;

        //点击数上报
        $mWdynamicMain  = by::WdynamicMain();
        $mWdynamicMain->UpdateHeat($mWdynamicMain::ACT_TYPE['CLICK'], ['id' => $did]);

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * 点赞、取消点赞
     */
    public function actionPraise()
    {
        $post       = \Yii::$app->request->post();
        $did        = $post['did']     ?? 0;
        $is_del     = $post['is_del']  ?? 0;

        list($s, $m)= by::Wpraise()->SetPraise($this->user_id, $did, $is_del);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
 * 获取文档详情
 */
    public function actionDocInfo()
    {
        $post   = \Yii::$app->request->post();
        $code   = $post['doc_code']     ?? 0;
        if(CUtil::uint($code)){
            CUtil::json_response(-1, '文档标识不能为空');
        }

        $info = by::Doc()->getInfo('', $code);
        CUtil::json_response(1, 'ok',$info);
    }

    /**
     * 获取文档列表
     */
    public function actionDocList()
    {
        $post   = \Yii::$app->request->post();
        $code   = $post['doc_code']     ?? 0;

        $info = by::Doc()->GetListByMain('',$code);
        CUtil::json_response(1, 'ok',$info);
    }


    /**
     * @OA\Post(
     *     path="/main/wiki/wnew-list",
     *     summary="探觅内容前五条",
     *     description="探觅内容前五条",
     *     tags={"内容管理"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="api", type="string", default="a_1664246268", description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *                 @OA\Property(property="sign", type="string", default="", description="签名"),
     *                 @OA\Property(property="sign_time", type="string", default="", description="签名时间"),
     *                 @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *                 @OA\Property(property="pid", type="string", description="一级话题ID"),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionWnewList()
    {
        $post = \Yii::$app->request->post();
        $pid = $post['pid'] ?? 1;

        list($status,$ret) = WikiService::getInstance()->getRandomData($pid);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok',$ret);
    }

    public function actionAppShare(){
        $params = \Yii::$app->request->post();
        $user_info = by::users()->getOneByUid($this->user_id);
        $page = $params['page'] ?? 'pages/index/index';
        $contentId = $params['content_id'] ?? 0;
        $share = $params['share'] ?? 1;

        $res = [
            'uid'=>$user_info['user_id'],
            'username'=>$user_info['nick'],
            'avatar'=>$user_info['avatar'],
            'content_id'=>$contentId
        ];
        
        $query = ['content_id'=>$contentId,'share'=>$share];
        $queryStr = http_build_query($query, '', '&', PHP_QUERY_RFC3986);
        //生产分享链接
        list($s, $url) = WeiXin::factory()->UrlLink($page, $queryStr);
        if (!$s) {
            CUtil::json_response(-1,$url);
        }
        $res['url'] = $url;

        CUtil::json_response(1, 'ok', $res);
    }



}
