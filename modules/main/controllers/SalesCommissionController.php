<?php

namespace app\modules\main\controllers;

use app\models\byNew;
use app\models\CUtil;
use app\modules\main\forms\employee\RegisterRecordListForm;
use app\modules\main\services\SalesCommissionService;
use app\modules\main\services\UserBindService;
use app\modules\main\services\UserSmileService;

/**
 * 佣金信息-控制器
 */
class SalesCommissionController extends CommController
{
    /**
     * 当前用户的佣金列表
     * @OA\Post(
     *     path="/main/sales-commission/list",
     *     summary="佣金列表",
     *     description="当前用户的佣金列表",
     *     tags={"佣金列表"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/PageRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page_size", type="integer", default="10",description="页数，默认10"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $params['activity_id'] = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
        if (empty($params['activity_id'])) {
            CUtil::json_response(1, 'ok', ['list' => [], 'total' => '0']);
        }
        $data = SalesCommissionService::getInstance()->pageList($this->user_id, $params['page'], $params['page_size'], $params['activity_id']);

        CUtil::json_response(1, 'ok', $data);
    }
    /**
     * 当前用户的佣金列表
     * @OA\Post(
     *     path="/main/sales-commission/partner-list",
     *     summary="佣金列表",
     *     description="当前用户的佣金列表",
     *     tags={"佣金列表"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/PageRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page_size", type="integer", default="10",description="页数，默认10"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionPartnerList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $params['activity_id'] = 1;
        if (empty($params['activity_id'])) {
            CUtil::json_response(1, 'ok', ['list' => [], 'total' => '0']);
        }
        $data = SalesCommissionService::getInstance()->pageList($this->user_id, $params['page'], $params['page_size'], $params['activity_id']);

        CUtil::json_response(1, 'ok', $data);
    }

    public function actionStatistics(){
        $activity_id = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
        $data = SalesCommissionService::getInstance()->statistics($this->user_id,$activity_id);

        $return = [
            'estimate_commission' => 0,    //预计还可到账金额
            'actual_commission' => 0,      //已到账金额 结算佣金
            'actual_money' => 0,           //已到账的订单金额数
            'estimate_num' => 0,           //预计还可到账的订单数
            'actual_num' => 0,             //已到账的订单数

        ];
        foreach ($data as $key => $val) {
            if ($val['status'] == 300 || $val['status'] == 400){
                $return['estimate_commission'] += $val['commission'];
                $return['estimate_num']++;
            }else if ($val['status'] == 500){
                // 判断是否超过15天
                if ($val['finish_time'] < (time()-15*86400)){
                    $return['actual_commission'] += $val['commission'];
                    $return['actual_money'] += $val['price'];
                    $return['actual_num']++;
                }else{
                    $return['estimate_commission'] += $val['commission'];
                    $return['estimate_num']++;
                }
            }
        }
        // 转成元
        $return['estimate_commission'] = CUtil::totalFee($return['estimate_commission'], 1);
        $return['actual_commission'] = CUtil::totalFee($return['actual_commission'], 1);
        $return['actual_money'] = CUtil::totalFee($return['actual_money'], 1);

        CUtil::json_response(1, 'ok', $return);
    }
}