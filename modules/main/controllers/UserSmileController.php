<?php

namespace app\modules\main\controllers;

use app\models\byNew;
use app\models\CUtil;
use app\modules\main\forms\employee\RegisterRecordListForm;
use app\modules\main\services\UserBindService;
use app\modules\main\services\UserSmileService;

/**
 * 微笑大使-控制器
 */
class UserSmileController extends CommController
{

    public function actionInfo()
    {
        // 用户ID
        $user_id = $this->user_id;

        // 调用服务
        list($status, $res) = UserSmileService::getInstance()->getDetail($user_id);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }
    /**
     * 微笑大使绑定的用户列表
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionBindUserList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new RegisterRecordListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = UserBindService::getInstance()->getSmileList($this->user_id, $form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 申请成为微笑大使
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionApply(){
        // 用户ID
        $user_id = $this->user_id;
        // 调用服务
        list($status, $res, $data) = UserSmileService::getInstance()->apply($user_id);
        if (!$status) {
            CUtil::json_response(-1, $res, $data);
        }
        
        CUtil::json_response(1, 'ok', $res);
        
    }

    /**
     * 已收到提示
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionReceivedTips()
    {
        $userId = $this->user_id ?? 0;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        UserSmileService::getInstance()->closeTips($userId);
        CUtil::json_response(1, 'ok');
    }

    /**
     * 排行榜
     */
    public function actionRankList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $data = byNew::EmployeeStatisticsModel()->rankList($params);
        CUtil::json_response(1, 'ok', $data);
    }
}