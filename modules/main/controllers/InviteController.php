<?php

namespace app\modules\main\controllers;

use app\components\RateLimiter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\forms\invite\InviteJoinForm;
use app\modules\main\forms\invite\InviteCheckForm;
use app\modules\main\forms\invite\InviteInfoForm;
use app\modules\main\forms\invite\InviteLikeForm;
use app\modules\main\forms\invite\InviteListForm;
use app\modules\main\services\invite\InviteService;
use Yii;
use yii\db\Exception;

/**
 * 通用邀请控制器
 */
class InviteController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/invite/join",
     *     summary="通用加入邀请接口",
     *     description="通用邀请接口，支持不同类型的邀请业务。invite_type=1表示一元秒杀助力邀请。",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"inviter_id", "invite_type", "relate_id"},
     *                 @OA\Property(property="inviter_id", type="integer", description="邀请人ID", example=123456),
     *                 @OA\Property(property="invite_type", type="integer", description="邀请类型（1=一元秒杀）", example=1),
     *                 @OA\Property(property="relate_id", type="integer", description="关联ID（如秒杀ID）", example=1),
     *                 @OA\Property(property="device_id", type="string", description="设备ID（可选，用于防重复）", example="device_abc123")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/OneYuanSeckillInviteModule")
     *                 }
     *             )
     *         )
     *     )
     * )
     */
    public function actionJoin()
    {
        try {
            // 参数验证
            $form = new InviteJoinForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('邀请参数验证失败: ' . json_encode($errors), 'invite_join_error');
                CUtil::json_response(-1, reset($errors));
            }

            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::debug('邀请用户权限检查失败: user_id=' . $this->user_id, 'invite_join_error');
                CUtil::json_response(-1, '请先授权哟！');
            }

            // 获取设备ID（可选参数）
            $deviceId = Yii::$app->request->post('device_id');
            // 生成唯一的请求标识并检查请求频率
            $unique_key = CUtil::getAllParams('invite_join', $this->user_id);
            list($isAllowed, $message, $code) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
            if (!$isAllowed) {
                CUtil::json_response(-1, '频繁请求，请稍候！');
            }

            // 执行邀请
            list($status, $result) = InviteService::getInstance()->invite(
                    (int) $form->inviter_id,
                    (int) $form->invite_type,
                    (int) $form->relate_id,
                    (int) $this->user_id,
                    $deviceId
            );

            if (!$status) {
                CUtil::json_response(-1, $result);
            }

            CUtil::json_response(1, 'ok', $result);

        } catch (\Exception $e) {
            CUtil::debug('邀请加入异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_join_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/invite/check",
     *     summary="检查用户是否可以被邀请",
     *     description="检查用户是否符合邀请条件，可用于前端判断是否显示邀请按钮。",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"inviter_id", "invite_type", "relate_id"},
     *                 @OA\Property(property="inviter_id", type="integer", description="邀请人ID", example=123456),
     *                 @OA\Property(property="invite_type", type="integer", description="邀请类型（1=一元秒杀）", example=1),
     *                 @OA\Property(property="relate_id", type="integer", description="关联ID（如秒杀ID）", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 可参与, -1: 不可参与）"),
     *             @OA\Property(property="sMsg", type="string", example="可以参与邀请", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 ref="#/components/schemas/InviteCheckResponse"
     *             )
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionCheck()
    {

        try {
            // 参数验证
            $form = new InviteCheckForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('邀请检查参数验证失败: ' . json_encode($errors), 'invite_check_error');
                CUtil::json_response(-1, reset($errors));
            }

            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::debug('邀请检查用户权限检查失败: user_id=' . $this->user_id, 'invite_check_error');
                CUtil::json_response(-1, '请先授权哟！');
            }

            // 检查邀请资格
            list($status, $message, $inviter_info) = InviteService::getInstance()->checkInviteEligibility(
                    (int) $form->inviter_id,
                    (int) $form->invite_type,
                    (int) $form->relate_id,
                    (int) $this->user_id
            );

            $data = [
                    'can_invite'   => $status,
                    'reason'       => $status ? '' : $message,
                    'inviter_info' => $inviter_info
            ];

            if ($status) {
                CUtil::json_response(1, $message, $data);
            } else {
                CUtil::json_response(-1, $message, $data);
            }

        } catch (\Exception $e) {
            CUtil::debug('邀请检查异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_check_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/invite/list",
     *     summary="获取邀请记录列表",
     *     description="获取当前用户的邀请记录列表，包含被邀请人的昵称、头像、加入时间和备注信息，支持按邀请类型和关联ID筛选",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *                 @OA\Property(property="invite_type", type="integer", description="邀请类型（可选筛选条件）", example=1),
     *                 @OA\Property(property="relate_id", type="integer", description="关联ID（可选筛选条件）", example=123)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *             @OA\Property(
     *                 property="data",
     *                 @OA\Property(property="total", type="integer", description="总记录数"),
     *                 @OA\Property(property="page", type="integer", description="当前页码"),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量"),
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="邀请记录ID"),
     *                         @OA\Property(property="invitee_id", type="integer", description="被邀请人ID"),
     *                         @OA\Property(property="nick", type="string", description="被邀请人昵称"),
     *                         @OA\Property(property="avatar", type="string", description="被邀请人头像"),
     *                         @OA\Property(property="invited_at", type="integer", description="加入时间（时间戳）"),
     *                         @OA\Property(property="remark", type="string", description="备注信息"),
     *                         @OA\Property(property="invite_date", type="string", description="邀请时间（格式化）"),
     *                         @OA\Property(property="invite_type", type="integer", description="邀请类型"),
     *                         @OA\Property(property="relate_id", type="integer", description="关联ID")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        try {
            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::debug('邀请列表用户权限检查失败: user_id=' . $this->user_id, 'invite_list_error');
                CUtil::json_response(-1, '请先授权哟！');
            }

            // 参数验证
            $form = new InviteListForm();
            $form->load(Yii::$app->request->post(), '');
            if (!$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('邀请列表参数验证失败: ' . json_encode($errors), 'invite_list_error');
                CUtil::json_response(-1, reset($errors));
            }

            // 获取安全的分页参数
            $pageParams = $form->getSafePageParams();
            $page = $pageParams['page'];
            $pageSize = $pageParams['page_size'];

            // 获取安全的筛选参数
            $filterParams = $form->getSafeFilterParams();
            $inviteType = $filterParams['invite_type'];
            $relateId = $filterParams['relate_id'];

            $userInviteModel = by::UserInviteModel();

            // 获取邀请记录列表
            $inviteList = $userInviteModel->getInviteListByInviter($this->user_id, $page, $pageSize, $inviteType, $relateId);
            
            // 获取总记录数
            $total = $userInviteModel->getInviteCountByInviter($this->user_id, $inviteType, $relateId);

            // 格式化返回数据
            $formattedList = [];
            foreach ($inviteList as $invite) {
                $formattedList[] = [
                    'id' => (int)$invite['id'],
                    'invitee_id' => (int)$invite['invitee_id'],
                    'nick' => $invite['nick'] ?: '用户' . $invite['invitee_id'],
                    'avatar' => CUtil::avatar(['avatar' => $invite['avatar']]),
                    'invited_at' => (int)$invite['invited_at'],
                    'remark' => $invite['remark'] ?: '',
                    'invite_date' => date('Y-m-d H:i:s', $invite['invited_at']),
                    'invite_type' => (int)$invite['invite_type'],
                    'relate_id' => (int)$invite['relate_id']
                ];
            }

            $data = [
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
                'list' => $formattedList
            ];

            CUtil::json_response(1, 'ok', $data);

        } catch (\Exception $e) {
            CUtil::debug('邀请列表异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_list_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }

    /**
     * @OA\Post(
     *     path="/main/invite/info",
     *     summary="获取邀请规则信息",
     *     description="获取邀请规则详细信息，包括规则详情、已邀请人数、发放奖品、待消费金额等。当传入relate_id时，返回指定规则的信息（invite_rules为单个对象）；否则返回所有规则的信息（invite_rules为数组）。",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"invite_type"},
     *                 @OA\Property(property="invite_type", type="integer", description="邀请类型", example=2),
     *                 @OA\Property(property="relate_id", type="integer", description="关联规则ID（可选），关联user_invite_gift_rules.id", example=123)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="invite_count", type="integer", description="已邀请人数"),
     *                 @OA\Property(property="pending_amount", type="string", description="待消费金额"),
     *                 @OA\Property(
     *                     property="invite_rules",
     *                     oneOf={
     *                         @OA\Schema(
     *                             type="object",
     *                             description="当传入relate_id时返回单个规则对象",
     *                             @OA\Property(property="rule_id", type="integer", description="规则ID"),
     *                             @OA\Property(property="rule_name", type="string", description="规则名称"),
     *                             @OA\Property(property="rule_details", type="string", description="规则详情"),
     *                             @OA\Property(property="required_count", type="integer", description="所需邀请数"),
     *                             @OA\Property(property="grant_mode", type="integer", description="发放方式"),
     *                             @OA\Property(property="has_received", type="boolean", description="是否已领取"),
     *                             @OA\Property(
     *                                 property="gift_items",
     *                                 type="array",
     *                                 @OA\Items(
     *                                     type="object",
     *                                     @OA\Property(property="gift_id", type="string", description="奖品ID"),
     *                                     @OA\Property(property="gift_type", type="integer", description="奖品类型"),
     *                                     @OA\Property(property="gift_type_name", type="string", description="奖品类型名称"),
     *                                     @OA\Property(property="quantity", type="integer", description="奖品数量")
     *                                 )
     *                             )
     *                         ),
     *                         @OA\Schema(
     *                             type="array",
     *                             description="当不传入relate_id时返回规则数组",
     *                             @OA\Items(
     *                                 type="object",
     *                                 @OA\Property(property="rule_id", type="integer", description="规则ID"),
     *                                 @OA\Property(property="rule_name", type="string", description="规则名称"),
     *                                 @OA\Property(property="rule_details", type="string", description="规则详情"),
     *                                 @OA\Property(property="required_count", type="integer", description="所需邀请数"),
     *                                 @OA\Property(property="grant_mode", type="integer", description="发放方式"),
     *                                 @OA\Property(property="has_received", type="boolean", description="是否已领取"),
     *                                 @OA\Property(
     *                                     property="gift_items",
     *                                     type="array",
     *                                     @OA\Items(
     *                                         type="object",
     *                                         @OA\Property(property="gift_id", type="string", description="奖品ID"),
     *                                         @OA\Property(property="gift_type", type="integer", description="奖品类型"),
     *                                         @OA\Property(property="gift_type_name", type="string", description="奖品类型名称"),
     *                                         @OA\Property(property="quantity", type="integer", description="奖品数量")
     *                                     )
     *                                 )
     *                             )
     *                         )
     *                     }
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        try {
            // 参数验证
            $form = new InviteInfoForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::json_response(-1, reset($errors));
            }

            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::json_response(-1, '请先授权哟！');
            }

            // 获取邀请规则信息
            list($status, $result) = InviteService::getInstance()->getInviteInfo((int)$this->user_id, $form->invite_type, $form->relate_id);

            if (!$status) {
                CUtil::json_response(-1, $result);
            }

            $result['total_consume_money'] = byNew::UserShopMoneyRecordModel()->getTotalConsumeMoneyByUserId($this->user_id);
            CUtil::json_response(1, 'ok', $result);

        } catch (\Exception $e) {
            CUtil::debug('邀请信息查询异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_info_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/invite/like",
     *     summary="赚钱花邀请点赞接口",
     *     description="赚钱花邀请点赞接口",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"inviter_id", "invite_type", "relate_id"},
     *                 @OA\Property(property="inviter_id", type="integer", description="邀请人ID", example=123456),
     *                 @OA\Property(property="invite_type", type="integer", description="邀请类型（1=一元秒杀）", example=1),
     *                 @OA\Property(property="relate_id", type="integer", description="关联ID（如秒杀ID）", example=1),
     *                 @OA\Property(property="device_id", type="string", description="设备ID（可选，用于防重复）", example="device_abc123")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 oneOf={
     *                     @OA\Schema(ref="#/components/schemas/OneYuanSeckillInviteModule")
     *                 }
     *             )
     *         )
     *     )
     * )
     */
    public function actionLike()
    {
        try {
            // 参数验证
            $form = new InviteLikeForm();
            if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
                $errors = $form->getFirstErrors();
                CUtil::debug('邀请点赞参数验证失败: ' . json_encode($errors), 'invite_like_error');
                CUtil::json_response(-1, reset($errors));
            }

            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::debug('邀请用户权限检查失败: user_id=' . $this->user_id, 'invite_like_error');
                CUtil::json_response(-1, '请先授权哟！');
            }

            // 获取设备ID（可选参数）
            $deviceId = Yii::$app->request->post('device_id');

            // 执行邀请
            list($status, $result, $consumeMoneyLikeReturn) = InviteService::getInstance()->inviteLike(
                (int) $form->inviter_id,
                (int) $form->invite_type,
                (int) $form->relate_id,
                (int) $this->user_id,
                $deviceId,
                '赚钱花邀请点赞'
            );
            
            if (!$status) {
                CUtil::json_response(-1, $result);
            }
            
            CUtil::json_response(1, $result, $consumeMoneyLikeReturn);
            
        } catch (\Exception $e) {
            CUtil::debug('赚钱花邀请点赞异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_like_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/invite/consume-money-record",
     *     summary="获取赚钱记录",
     *     description="获取赚钱记录",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *             @OA\Property(
     *                 property="data",
     *                 @OA\Property(property="total", type="integer", description="总记录数"),
     *                 @OA\Property(property="page", type="integer", description="当前页码"),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量"),
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="邀请记录ID"),
     *                         @OA\Property(property="invitee_id", type="integer", description="被邀请人ID"),
     *                         @OA\Property(property="nick", type="string", description="被邀请人昵称"),
     *                         @OA\Property(property="avatar", type="string", description="被邀请人头像"),
     *                         @OA\Property(property="invited_at", type="integer", description="加入时间（时间戳）"),
     *                         @OA\Property(property="remark", type="string", description="备注信息"),
     *                         @OA\Property(property="invite_date", type="string", description="邀请时间（格式化）"),
     *                         @OA\Property(property="invite_type", type="integer", description="邀请类型"),
     *                         @OA\Property(property="relate_id", type="integer", description="关联ID")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionConsumeMoneyRecord()
    {
        try {
            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::debug('邀请列表用户权限检查失败: user_id=' . $this->user_id, 'invite_list_error');
                CUtil::json_response(-1, '请先授权哟！');
            }
            
            $post = Yii::$app->request->post();

            $page = (int) ($post['page'] ?? 1);
            $pageSize = (int) ($post['page_size'] ?? 99);

            $model = byNew::UserShopMoneyRecordModel();
            $inviteList = $model->getConsumeMoneyListByUserId($this->user_id, $page, $pageSize);

            // 获取总记录数
            $total = (int) $model->getConsumeMoneyCountByUserId($this->user_id);
            
            // 格式化返回数据
            $formattedList = [];
            $dictData = CUtil::dictData('zqh_task_short_name');
            $dictDataMap = array_column($dictData, 'value', 'label');
            foreach ($inviteList as $invite) {
                $inviteeId = (int) ($invite['extend'] ?? 0);
                $user = by::users()->getOneByUid($inviteeId);
                $nick = $user['nick'] ?? '';
                $len = mb_strlen($nick);
                // 昵称只显示首位
                if ($len > 1) {
                    $nick = mb_substr($nick, 0, 1) . str_repeat('*', $len - 1);
                }
                
                $event_code = '';
                $event_name = '';
                $short_event_name = '';
                $tag_type = 0;
                if ($invite['remark'] === '邀请好友注册奖励') {
                    $tag_type = 1; // 注册奖励
                    $event_name = '邀请好友注册奖励';
                    $short_event_name = '邀请新人';
                } elseif ($invite['remark'] === '赚钱花邀请点赞') {
                    $tag_type = 2; // 点赞
                    $event_name = '赚钱花邀请点赞';
                    $short_event_name = '好友点赞';
                } elseif ($invite['remark'] === '追觅小店分享佣金') {
                    $tag_type = 3; // 佣金
                    $event_name = '追觅小店分享佣金';
                    $short_event_name = '佣金返现';
                }
                $money = $invite['money'] ?? 0;
                $money = bcdiv($money, 100, 2);
                
                // 任务的取字典配置的短名
                if ($tag_type === 0) {
                    $eventCodeNameArr = explode(':', (string)$invite['remark']);
                    if (count($eventCodeNameArr) > 1) {
                        $event_code = $eventCodeNameArr[0] ?? '';
                        $event_name = $eventCodeNameArr[1] ?? '';
                        $short_event_name = $dictDataMap[$event_code] ?? '';
                    } else {
                        $event_name = $invite['remark'];
                        $short_event_name = $invite['remark'];
                    }
                }

                $formattedList[] = [
                    'id' => (int) $invite['id'],
                    'user_id' => (int) $invite['user_id'],
                    'nick' => $nick,
                    'avatar' => CUtil::avatar($user),
                    'tag_type' => $tag_type,
                    'money' => $money,
                    'event_code' => $event_code,
                    'event_name' => $event_name,
                    'short_event_name' => $short_event_name,
                    'remark' => sprintf('消费金：+%s元', $money),
                    'create_time' => date('Y-m-d H:i:s', $invite['ctime']),
                ];
            }

            $data = [
                'total' => $total,
                'page' => $page,
                'page_size' => $pageSize,
                'list' => $formattedList
            ];

            CUtil::json_response(1, 'ok', $data);
            
        } catch (\Exception $e) {
            CUtil::debug('获取赚钱记录异常: ' . $e->getMessage() . ' | File: ' . $e->getFile() . ' | Line: ' . $e->getLine(), 'invite_list_error');
            CUtil::json_response(-1, '系统异常，请稍后重试');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/invite/consume-money-roll",
     *     summary="获取赚钱滚动飘屏",
     *     description="获取赚钱滚动飘屏",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *         )
     *     )
     * )
     */
    public function actionConsumeMoneyRoll()
    {
        try {
            $post = \Yii::$app->request->post();
            $type = (int) ($post['type'] ?? 1);
            
            $money_type = 1;
            if ($type == 5) {
                $money_type = 2;
            }
            
            $model = byNew::UserShopMoneyRecordModel();
            $res = $model->getTotalConsumeMoney();
            $data = [];
            foreach ($res as $k => $v) {
                $user_info = by::users()->getOneByUid($v['user_id']);
                $data[$k]['nick'] = CUtil::truncateString($user_info['nick'] ?? '', 2, '**');
                $data[$k]['avatar'] = CUtil::avatar($user_info);
                $data[$k]['remark'] = '已赚取￥'. bcdiv($v['money'], 100, 2) . '元！';
            }
            
            CUtil::json_response(1, 'ok', $data);
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取赚钱滚动飘屏失败：%s', $e->getMessage()), 'err.ConsumeMoneyRoll');
            CUtil::json_response(-1, '获取赚钱滚动飘屏失败');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/invite/consume-money-detail-roll",
     *     summary="获取赚钱详情滚动飘屏",
     *     description="获取赚钱详情滚动飘屏",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *         )
     *     )
     * )
     */
    public function actionConsumeMoneyDetailRoll()
    {
        try {
            $post = \Yii::$app->request->post();
            $type = (int) ($post['type'] ?? 1);

            $model = byNew::UserShopMoneyRecordModel();
            $res = $model->getConsumeMoneyDetailRoll();
            $count = byNew::UserShopMoneyModel()->getConsumeMoneyUserCount();
            $list = [];
            foreach ($res as $k => $v) {
                $remark = $v['remark'] ?? '';
                $remarkArr = explode(':', $remark);
                if (count($remarkArr) > 1) {
                    $remark = $remarkArr[1] ?? '';
                } else {
                    $remark = $remarkArr[0] ?? '';
                }
                $user_info = by::users()->getOneByUid($v['user_id']);
                $nick = $user_info['nick'] ?? '';
                $list[$k]['nick'] = CUtil::truncateStringV2($nick);
                $list[$k]['avatar'] = CUtil::avatar($user_info);
                $list[$k]['money'] = bcdiv($v['money'], 100, 2);
                $list[$k]['ctime'] = $v['ctime'] ?? 0;
                $list[$k]['remark'] = $remark;
            }
            
            $data = [
                'count' => $count,
                'list' => $list,
            ];
            
            CUtil::json_response(1, 'ok', $data);
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取赚钱详情滚动飘屏失败：%s', $e->getMessage()), 'err.ConsumeMoneyDetailRoll');
            CUtil::json_response(-1, '获取赚钱详情滚动飘屏屏失败');
        }
    }
    
    /**
     * @OA\Post(
     *     path="/main/invite/consume-money-rank",
     *     summary="获取赚钱排行榜",
     *     description="获取赚钱滚动排行榜",
     *     tags={"通用邀请"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=20),
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1),
     *             @OA\Property(property="sMsg", type="string", example="ok"),
     *         )
     *     )
     * )
     */
    public function actionConsumeMoneyRank()
    {
        try {
            $post = \Yii::$app->request->post();
            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::json_response(-1, '请先授权哟！');
            }

            $model = byNew::UserShopMoneyRecordModel();
            $data = $model->getConsumeMoneyRankData($this->user_id);
            
            CUtil::json_response(1, 'ok', $data);
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取赚钱排行榜失败：%s', $e->getMessage()), 'err.ConsumeMoneyRoll');
            CUtil::json_response(-1, '获取赚钱排行榜失败');
        }
    }

    public function actionConsumeMoneyTip()
    {
        try {
            $post = \Yii::$app->request->post();
            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::json_response(-1, '请先授权哟！');
            }

            $model = byNew::UserShopMoneyRecordModel();
            $data = $model->getConsumeMoneyTip($this->user_id);
            $remark = $data['remark'] ?? '';
            $remarkArr = explode(':', $remark);
            if (count($remarkArr) > 1) {
                $remark = $remarkArr[1] ?? '';
            } else {
                $remark = $remarkArr[0] ?? '';
            }
            $data['remark'] = $remark;
            
            CUtil::json_response(1, 'ok', $data);
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取未读提示失败：%s', $e->getMessage()), 'err.ConsumeMoneyTip');
            CUtil::json_response(-1, '获取未读提示失败');
        }
    }
    public function actionConsumeMoneyCloseTip()
    {
        try {
            $post = \Yii::$app->request->post();
            // 用户权限检查
            if (strlen($this->user_id) > 11) {
                CUtil::json_response(-1, '请先授权哟！');
            }

            $model = byNew::UserShopMoneyRecordModel();
            $data = $model->getConsumeMoneyCloseTip($this->user_id);
            if($data){
                CUtil::json_response(1, 'ok', []);
            }else{
                CUtil::json_response(-1, '设置已读失败');
            }
            
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('设置已读失败：%s', $e->getMessage()), 'err.ConsumeMoneyRoll');
            CUtil::json_response(-1, '设置已读失败');
        }
    }
}