<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CUtil;

class MarketController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/market/receive-market",
     *     summary="领取优惠券",
     *     description="领取优惠券",
     *     tags={"优惠券"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="market_ids", type="array", description="数据", @OA\Items())
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionReceiveMarket()
    {
        $user_id = $this->user_id;
        if (strlen($user_id) > 11 || empty(CUtil::uint($user_id))) {
            CUtil::json_response(-1, '请先授权，再领取优惠券！');
        }

        // 优惠券
        $market_ids = \Yii::$app->request->post('market_ids', '[]');
        // TODO: 优惠券ID，以旧换新的临时补丁，下一个版本删除
        $market_ids = [549];

        $flag = false;
        foreach ($market_ids as $market_id) {
            // 是否领取过
            $is_receive = by::userCard()->isReceive($user_id, $market_id);
            if ($is_receive) {
                $flag = true;
                continue;
            }
            // 发放优惠券
            list($status, $ret) = by::userCard()->backSend(
                $user_id,
                $market_id,
                1);
            if (!$status) {
                CUtil::json_response(-1, $ret);
            }
        }
        CUtil::json_response(1, 'ok', [
            'status' => $flag ? 2 : 1 // 1领取成功、2已领取过
        ]);
    }
}