<?php

namespace app\modules\main\controllers;


use app\models\by;
use app\models\CodeModel;
use app\models\CUtil;
use app\modules\main\services\AliPayService;
use app\modules\main\services\PayService;
use app\modules\main\services\WaresOrderService;
use app\modules\main\services\ZhimaService;
use yii\db\Exception;


class WaresOrderController extends CommController
{


    /**
     * @OA\Post(
     *     path="/main/wares-order/buy-info",
     *     summary="订单结算页",
     *     description="订单结算页",
     *     tags={"新商品订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresOrderBuyInfoRequest"),
     *          @OA\Schema(ref="#/components/schemas/PageRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionBuyInfo()
    {
        $post = \Yii::$app->request->post();
        $post['platformIds'] = $this->platformIds;

        list($status, $ret) = WaresOrderService::getInstance()->BuyInfo($this->user_id, $post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/wares-order/buy",
     *     summary="下单",
     *     description="下单",
     *     tags={"新商品订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresOrderBuyRequest"),
     *          @OA\Schema(ref="#/components/schemas/PageRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     * @throws \RedisException
     */
    public function actionBuy()
    {
        list($lock, $msg) = CUtil::payLock($this->user_id);
        if ($lock) {
            CUtil::json_response(-1, $msg);
        }
        $post = \Yii::$app->request->post();
        $post['pay_source'] = CUtil::uint($post['pay_source'] ?? 0);
        $post['union'] = substr($this->union, 0, 50);
        $post['euid'] = substr($this->euid, 0, 50);
        $post['referer'] = $this->referer ?? '';
        $post['platform_source'] = $this->platformSource ?? 0;
        $post['platformIds'] = $this->platformIds;

        list($status, $ret) = WaresOrderService::getInstance()->AddRecord($this->user_id, $this->api, $post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        } elseif ($status === -1) {
            CUtil::json_response(CodeModel::STATUS['NOT_DELIVERY'], $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/wares-order/pay",
     *     summary="拉起/再次拉起支付",
     *     description="拉起/再次拉起支付",
     *     tags={"新商品订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresOrderPayRequest"),
     *          @OA\Schema(ref="#/components/schemas/PageRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @return array|void
     * @throws Exception
     * @throws \RedisException
     */
    public function actionPay()
    {
        list($lock, $msg) = CUtil::payLock($this->user_id);
        if ($lock) {
            CUtil::json_response(-1, $msg);
        }

        $post = \Yii::$app->request->post();
        $order_no = $post['order_no'] ?? '';
        $pay_type = CUtil::uint($post['pay_type'] ?? 0);
        $pay_source = CUtil::uint($post['pay_source'] ?? 0);
        if (!in_array($pay_source, by::WxPay()::SOURCE)) {
            CUtil::json_response(-1, '订单类型不正确！');
        }

        //防止并发
        $unique_key = CUtil::getAllParams(__FUNCTION__, $order_no);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency($this->user_id, $unique_key, 3);
        if (!$anti) {
            CUtil::json_response(-1, '请勿频繁操作');
        }


        $attach = ['source' => $pay_source, 'ctime' => intval(START_TIME)];

        if ($pay_type == by::Omain()::PAY_BY_WX || $pay_type == by::Omain()::PAY_BY_WX_APP) {
            list($status, $ret) = by::WxPay()->AgainPay($this->user_id, $order_no, $this->api, $attach, '', $pay_type);
        } elseif ($pay_type == by::Omain()::PAY_BY_WX_H5) {
            list($status, $ret) = by::WxH5Pay()->AgainPay($this->user_id, $order_no, $this->api, $attach);
        }elseif ($pay_type == by::Omain()::PAY_BY_ALIPAY || $pay_type == by::Omain()::PAY_BY_MP_WX || $pay_type == by::Omain()::PAY_BY_MP_ALIPAY) { // 支付宝、中台支付（微信/支付宝）
            list($status, $ret) = PayService::getInstance()->againPay($this->user_id, $order_no, 1, $pay_type);
        } elseif ($pay_type == by::Omain()::PAY_BY_ALI_ZHIMA) {
            list($status, $ret) = ZhimaService::getInstance()->AgainPay($this->user_id, $post);
        } else {
            $status = false;
            $ret    = '未指定对应支付平台！';
        }

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 补丁：替换返回参数
        if ($pay_type == by::Omain()::PAY_BY_WX_APP) {
            $ret['prepayId'] = $ret['prepay_id'];
            $ret['sign'] = $ret['paySign'];
            unset($ret['prepay_id'], $ret['paySign']);
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    //卡券发放检查
    public function actionCheckCoupon()
    {
        $post = \Yii::$app->request->post();
        $sub_type= CUtil::uint($post['sub_type'] ?? 0);
        $order_no = $post['order_no'] ?? '';

        list($status, $ret) = WaresOrderService::getInstance()->checkCoupon($this->user_id, $sub_type, $order_no);
        if (!$status) {
            CUtil::json_response(1,'ok', ['status'=>0]);
        }
        CUtil::json_response(1, 'ok', ['status'=>1]);
    }
}
