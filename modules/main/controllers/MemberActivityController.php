<?php
/**
 * <AUTHOR>
 * @date 16/4/2025 上午 11:06
 * 会员活动Controller
 */

namespace app\modules\main\controllers;

use app\constants\RespStatusCodeConst;
use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\common\ControllerTrait;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\services\MemberActivityService;


class MemberActivityController extends CommController
{
    use ControllerTrait;


    private $service;

    public function __construct($id, $module, $config = [])
    {
        $this->service = MemberActivityService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * @OA\Post(
     *     path="/main/member-activity/info",
     *     summary="获取自定义配置活动详情",
     *     description="获取包含多个模块的自定义活动详情信息",
     *     tags={"自定义配置活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 required={"id"},
     *                 @OA\Property(property="id", type="integer", example=142, description="活动ID")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 操作成功, -10000: 系统异常, 56010001: 参数错误）"),
     *             @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="base_info",
     *                     ref="#/components/schemas/ActivityBaseInfo"
     *                 ),
     *                 @OA\Property(
     *                     property="modules",
     *                     type="array",
     *                     description="活动模块列表",
     *                     @OA\Items(
     *                         oneOf={
     *                             @OA\Schema(ref="#/components/schemas/BigTitleModule"),
     *                             @OA\Schema(ref="#/components/schemas/CheckinModule"),
     *                             @OA\Schema(ref="#/components/schemas/ReturnPointsModule"),
     *                             @OA\Schema(ref="#/components/schemas/AdPositionModule"),
     *                             @OA\Schema(ref="#/components/schemas/DrawModule"),
     *                             @OA\Schema(ref="#/components/schemas/NewPersonModule"),
     *                             @OA\Schema(ref="#/components/schemas/RecommendGoodsModule"),
     *                             @OA\Schema(ref="#/components/schemas/CouponModule"),
     *                             @OA\Schema(ref="#/components/schemas/PartGoodsModule"),
     *                             @OA\Schema(ref="#/components/schemas/PointsExchangeModule")
     *                         }
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        try {
            // 获取 POST 请求数据
            $post   = \Yii::$app->request->post();
            $id     = intval($post['id'] ?? 0);
            $userId = empty($this->user_id) || strlen($this->user_id) > 11 || $this->user_id == 'undefined' ? 0 : intval($this->user_id);


            if ($id <= 0) {
                CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, 'Invalid ID');
            }

            // 调用服务获取信息，并捕获异常
            list($status, $data) = $this->service->getInfo($id, $userId);

            if (!$status) {
                CUtil::json_response(RespStatusCodeConst::SYS_ERROR_CODE, $data);
            }

            // 返回成功响应
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $data);

        } catch (\Exception $e) {
            // 捕获异常并返回系统错误
            CUtil::json_response(RespStatusCodeConst::SYS_ERROR_CODE, 'System Error: ' . $e->getMessage());
        }
    }
    public function actionIsStart()
    {
        try {
            $redis = by::redis();  // 修正组件调用方式
            $redisKey = 'member_activity_is_start';

            // 尝试从缓存获取
            $cacheData = $redis->get($redisKey);
            if ($cacheData !== false) {
                $data = json_decode($cacheData, true);
                 $this->successResponse($data['is_start']);
            }

            // 获取活动配置
            $config = CUtil::getConfig('member_activity', 'config', \Yii::$app->id) ?: [];

            // 校验配置有效性
            if (!$this->isValidConfig($config)) {
                 $this->successResponse(0);
            }

            // 检查活动状态
            $isStart = MemberActivityModel::getInstance()->isStart($config['id']);

            // 设置缓存（使用配置中的缓存时间或默认值）
            $cacheTime = $config['cache_time'] ?? 7200;
            $redis->set($redisKey, json_encode(['is_start' => $isStart], JSON_PRETTY_PRINT), ['EX' => $cacheTime]);

             $this->successResponse($isStart);
        } catch (\Exception $e) {
            // 失败时默认返回未开始状态（保证服务可用性）
             $this->successResponse(0);
        }
    }
    /**
     * @OA\Post(
     *     path="/main/member-activity/invite-record",
     *     summary="获取邀请记录",
     *     description="获取用户的邀请记录",
     *     tags={"自定义配置活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/json",
     *             @OA\Schema(
     *                 @OA\Property(property="type", type="integer", example=0, description="邀请类型 1=一元秒杀 2=半价购")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 操作成功, -10000: 系统异常）"),
     *             @OA\Property(property="sMsg", type="string", example="操作成功", description="提示消息"),
     *             @OA\Property(property="data", type="array", @OA\Items(type="object"))
     *         )
     *     )
     * )
     */
    public function actionInviteRecord()
    {
        try {
            $post = \Yii::$app->request->post();
            $type = $post['type'] ?? 0;
            
            $data = $this->service->getUserInviteRecord($type);
            
            $this->success($data);
        } catch (BusinessException $e) {
            $this->error($e->getMessage());
        } catch (\Throwable $e) {
            CUtil::debug(sprintf('获取邀请记录失败：%s', $e->getMessage()), 'err.user-order-try.job');
            $this->error('获取记录失败');
        }
    }

    /**
     * 校验配置有效性
     * @param array $config 活动配置
     * @return bool 配置是否有效
     */
    private function isValidConfig(array $config)
    {
        return !empty($config['show']) && !empty($config['id']);
    }

    /**
     * 统一成功响应格式
     * @param int $isStart 活动是否开始
     * @return void
     */
    private function successResponse(int $isStart)
    {
        CUtil::json_response(1, 'success', ['is_start' => $isStart]);
    }


}