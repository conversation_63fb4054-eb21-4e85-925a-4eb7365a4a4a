<?php

namespace app\modules\main\controllers;

use app\components\Review;
use app\models\by;
use app\models\CUtil;
use app\modules\main\forms\review\CreateGoodsReviewForm;
use app\modules\main\forms\review\CreateReviewReportForm;
use app\modules\main\forms\review\GetMyReviewListForm;
use app\modules\main\forms\review\GetReviewListForm;
use app\modules\main\services\GoodsCommentService;
use app\modules\main\services\GoodsReviewService;

/**
 * 商品评价
 */
class GoodsReviewController extends CommController
{
    // 评价
    public function actionReview()
    {
        // 解析参数
        $post = \Yii::$app->request->post();

        // 参数校验
        $form = new CreateGoodsReviewForm();
        if (!$form->load($post, '') || !$form->validate()) {
            CUtil::json_response(-1, implode('', $form->firstErrors));
        }

        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '操作频繁，请勿重复提交');
        }

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->createReview($this->user_id, $form->toArray());
            // 请求成功
            $status && CUtil::json_response(1, 'ok');
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '创建评价失败');
        }
    }

    // 删除评价
    public function actionDelete()
    {
        // 解析参数
        $post = \Yii::$app->request->post();

        $sku = $post['sku'] ?? '';
        $order_no = $post['order_no'] ?? '';

        if (empty($sku) || empty($order_no)) {
            CUtil::json_response(-1, '参数错误');
        }

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->deleteReview($this->user_id, $order_no, $sku);
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '删除失败');
        }
    }

    // 评价列表
    public function actionList()
    {
        // 解析参数
        $post = \Yii::$app->request->post();

        // 参数校验
        $form = new GetReviewListForm();
        if (!$form->load($post, '') || !$form->validate()) {
            CUtil::json_response(-1, implode('', $form->firstErrors));
        }

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->getReviewList($form->toArray());
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '获取评价列表失败');
        }
    }

    // 评价详情
    public function actionDetail()
    {
        // 解析参数
        $post = \Yii::$app->request->post();
        $review_id = $post['review_id'] ?? 0;

        if (empty($review_id)) {
            CUtil::json_response(-1, '请选择要查看的评价');
        }

        // 自定义逻辑：如果是我的评价，则直接返回
        $reviewIds = GoodsCommentService::getInstance()->getMyReviewIds();
        if (in_array($review_id, $reviewIds)) {
            $reviewResult = GoodsCommentService::getInstance()->getMyReviewInfo($review_id);
            CUtil::json_response(1, 'ok', $reviewResult);
        }

        try {
            list($status, $res) = GoodsReviewService::getInstance()->getReviewDetail($review_id, GoodsReviewService::DETAIL_REVIEW_STATUS['AUDIT_PASS']); // 审核通过
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '查看详情失败');
        }
    }

    /**
     * 我的评价列表
     * @return void
     */
    public function actionMyList()
    {
        // 解析参数
        $post = \Yii::$app->request->post();

        // 参数校验
        $form = new GetMyReviewListForm();
        if (!$form->load($post, '') || !$form->validate()) {
            CUtil::json_response(-1, implode('', $form->firstErrors));
        }

        $params = $form->toArray();

        $status = false;
        $res = '';
        try {
            switch ($params['type']) {
                case GoodsReviewService::MY_REVIEW_TYPE['WAIT_REVIEW']: // 待评价
                    list($status, $res) = GoodsReviewService::getInstance()->getCanReviewOrderList($this->user_id);
                    break;

                case GoodsReviewService::MY_REVIEW_TYPE['CAN_APPEND']: // 可追评
                    list($status, $res) = GoodsReviewService::getInstance()->getCanAppendOrderList($this->user_id);
                    break;

                case GoodsReviewService::MY_REVIEW_TYPE['REVIEW_LIST']: // 已评价（评价列表）
                    list($status, $res) = GoodsReviewService::getInstance()->getMyReviewList($this->user_id, $params['page'], $params['page_size']);
                    break;

                default:
                    CUtil::json_response(-1, '参数错误');
                    break;
            }
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价（我的评价列表）异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '请求数据失败');
        }
    }

    // 上传图片
    public function actionUpload()
    {
        $file = $_FILES['file'] ?? null;
        if (!$file || $file['error'] !== UPLOAD_ERR_OK) {
            CUtil::json_response(-1, 'false', "无效文件");
            return;
        }

        list($status, $res) = Review::factory()->upload($file);
        if (!$status) {
            CUtil::json_response(-1, 'false', "上传失败");
            return;
        }
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 举报
     * @return void
     */
    public function actionReport()
    {
        // 解析参数
        $post = \Yii::$app->request->post();

        // 参数校验
        $form = new CreateReviewReportForm();
        if (!$form->load($post, '') || !$form->validate()) {
            CUtil::json_response(-1, implode('', $form->firstErrors));
        }

        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '操作频繁，请勿重复提交');
        }

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->createReport($this->user_id, $form->toArray());
            // 请求成功
            $status && CUtil::json_response(1, 'ok');
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价（举报）异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '举报失败');
        }
    }

    /**
     * 举报原因列表
     * @return void
     */
    public function actionReasonList()
    {
        // 逻辑处理
        try {
            $res = GoodsReviewService::getInstance()->getReasonList();
            CUtil::json_response(1, 'ok', $res);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '获取举报原因列表失败');
        }
    }

}