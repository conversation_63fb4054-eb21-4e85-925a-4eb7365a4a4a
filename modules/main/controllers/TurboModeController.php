<?php

namespace app\modules\main\controllers;

use app\models\CUtil;
use app\models\by;
use app\models\byNew;
use app\modules\main\services\TurboModeService;
use yii\web\Controller;


class TurboModeController extends CommController
{


    public function actionCheck()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
       CUtil::json_response(1, 'ok',TurboModeService::getInstance()->check());

    }

    public function actionClearCache()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        TurboModeService::getInstance()->clearAllCache();
        CUtil::json_response(1, 'ok');
    }

}