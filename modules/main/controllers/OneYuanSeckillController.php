<?php

namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\main\forms\oneYuanSeckill\SeckillDetailForm;
use app\modules\main\forms\oneYuanSeckill\StartSeckillForm;
use app\modules\main\forms\oneYuanSeckill\HelpSeckillForm;
use app\modules\main\forms\oneYuanSeckill\GetOrCreateSeckillForm;
use app\modules\main\services\OneYuanSeckillService;
use Yii;

/**
 * 一元秒杀控制器
 */
class OneYuanSeckillController extends CommController
{
    /**
     * @OA\Post(
     *     path="/main/one-yuan-seckill/detail",
     *     summary="获取当前用户助力详情",
     *     description="获取当前用户助力详情，包含助力好友列表、秒杀商品信息、助力状态、订单号等",
     *     tags={"一元秒杀"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"seckill_id"},
     *                 @OA\Property(property="seckill_id", type="integer", description="秒杀ID", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="seckill_id", type="integer", description="秒杀ID"),
     *                 @OA\Property(property="activity_id", type="integer", description="活动ID"),
     *                 @OA\Property(property="gid", type="integer", description="商品ID"),
     *                 @OA\Property(property="status", type="integer", description="助力状态：0-进行中，1-助力成功"),
     *                 @OA\Property(property="status_text", type="string", description="助力状态文本"),
     *                 @OA\Property(property="invite_members", type="integer", description="已邀请人数"),
     *                 @OA\Property(property="required_invite_number", type="integer", description="需要邀请人数"),
     *                 @OA\Property(property="order_no", type="string", description="订单号"),
     *                 @OA\Property(property="created_at", type="integer", description="创建时间"),
     *                 @OA\Property(
     *                     property="goods_info",
     *                     type="object",
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="cover_image", type="string", description="商品封面图"),
     *                     @OA\Property(property="price", type="string", description="商品价格"),
     *                     @OA\Property(property="sku", type="string", description="商品SKU"),
     *                     @OA\Property(property="stock", type="integer", description="商品库存数量")
     *                 ),
     *                 @OA\Property(
     *                     property="help_friends",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="user_id", type="integer", description="用户ID"),
     *                         @OA\Property(property="nick", type="string", description="昵称"),
     *                         @OA\Property(property="avatar", type="string", description="头像"),
     *                         @OA\Property(property="help_time", type="integer", description="助力时间"),
     *                         @OA\Property(property="help_time_text", type="string", description="助力时间文本")
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        // 参数验证
        $form = new SeckillDetailForm();
        if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
            $errors = $form->getFirstErrors();
            CUtil::json_response(-1, reset($errors));
        }

        // 用户权限检查
        if (strlen($this->user_id) > 11) {
            CUtil::json_response(-1, '请先授权哟！');
        }

        // 获取助力详情
        list($status, $result) = OneYuanSeckillService::getInstance()->getSeckillDetail(
            $this->user_id,
            $form->seckill_id
        );

        if (!$status) {
            CUtil::json_response(-1, $result);
        }

        CUtil::json_response(1, 'ok', $result);
    }

    /**
     * @OA\Post(
     *     path="/main/one-yuan-seckill/start",
     *     summary="发起秒杀助力",
     *     description="验证当前活动是否可以发起助力，验证商品库存，创建助力活动记录",
     *     tags={"一元秒杀"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"activity_id", "gid"},
     *                 @OA\Property(property="activity_id", type="integer", description="活动ID", example=77),
     *                 @OA\Property(property="gid", type="integer", description="商品ID", example=18)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="seckill_id", type="integer", description="秒杀ID"),
     *                 @OA\Property(property="message", type="string", description="提示信息"),
     *                 @OA\Property(
     *                     property="goods_info",
     *                     type="object",
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="cover_image", type="string", description="商品封面图"),
     *                     @OA\Property(property="price", type="string", description="商品价格"),
     *                     @OA\Property(property="sku", type="string", description="商品SKU"),
     *                     @OA\Property(property="invite_number", type="integer", description="需要邀请人数"),
     *                     @OA\Property(property="buy_goods_number", type="integer", description="限购数量")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionStart()
    {
        // 参数验证
        $form = new StartSeckillForm();
        if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
            $errors = $form->getFirstErrors();
            CUtil::json_response(-1, reset($errors));
        }

        // 用户权限检查
        if (strlen($this->user_id) > 11) {
            CUtil::json_response(-1, '请先授权哟！');
        }

        // 发起秒杀助力
        list($status, $result) = OneYuanSeckillService::getInstance()->startSeckill(
                (int) $this->user_id,
                (int)$form->activity_id,
                (int) $form->gid
        );

        if (!$status) {
            CUtil::json_response(-1, $result);
        }

        CUtil::json_response(1, 'ok', $result);
    }

    /**
     * @OA\Post(
     *     path="/main/one-yuan-seckill/get-or-create",
     *     summary="获取或创建一元秒杀记录",
     *     description="检查是否存在seckill记录，如果不存在就创建。传入参数activity_id、gid",
     *     tags={"一元秒杀"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"activity_id", "gid"},
     *                 @OA\Property(property="activity_id", type="integer", description="活动ID", example=77),
     *                 @OA\Property(property="gid", type="integer", description="商品ID", example=18)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", example=1, description="状态码（1: 成功, -1: 失败）"),
     *             @OA\Property(property="sMsg", type="string", example="ok", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="seckill_id", type="integer", description="秒杀ID"),
     *                 @OA\Property(property="message", type="string", description="提示信息"),
     *                 @OA\Property(property="is_new", type="boolean", description="是否为新创建的记录"),
     *                 @OA\Property(
     *                     property="seckill_info",
     *                     type="object",
     *                     description="秒杀记录信息",
     *                     @OA\Property(property="id", type="integer", description="秒杀ID"),
     *                     @OA\Property(property="activity_id", type="integer", description="活动ID"),
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="user_id", type="integer", description="用户ID"),
     *                     @OA\Property(property="invite_members", type="integer", description="已邀请人数"),
     *                     @OA\Property(property="status", type="integer", description="助力状态"),
     *                     @OA\Property(property="created_at", type="integer", description="创建时间"),
     *                     @OA\Property(property="updated_at", type="integer", description="更新时间")
     *                 ),
     *                 @OA\Property(
     *                     property="goods_info",
     *                     type="object",
     *                     description="商品信息（仅新创建时返回）",
     *                     @OA\Property(property="gid", type="integer", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="cover_image", type="string", description="商品封面图"),
     *                     @OA\Property(property="price", type="string", description="商品价格"),
     *                     @OA\Property(property="sku", type="string", description="商品SKU"),
     *                     @OA\Property(property="invite_number", type="integer", description="需要邀请人数"),
     *                     @OA\Property(property="buy_goods_number", type="integer", description="限购数量")
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionGetOrCreate()
    {
        // 参数验证
        $form = new GetOrCreateSeckillForm();
        if (!$form->load(Yii::$app->request->post(), '') || !$form->validate()) {
            $errors = $form->getFirstErrors();
            CUtil::json_response(-1, reset($errors));
        }

        // 用户权限检查
        if (strlen($this->user_id) > 11) {
            CUtil::json_response(-1, '请先授权哟！');
        }

        // 频率限制检查 3秒内只允许一次
        $unique_key = CUtil::getAllParams(__FUNCTION__, $this->user_id, $form->activity_id, $form->gid);
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $unique_key, 2, "EX");
        if (!$anti) {
            CUtil::json_response(-1, '操作太频繁，请稍后重试');
        }

        // 获取或创建秒杀记录
        list($status, $result) = OneYuanSeckillService::getInstance()->getOrCreateSeckill(
            (int) $this->user_id,
            (int) $form->activity_id,
            (int) $form->gid
        );

        if (!$status) {
            CUtil::json_response(-1, $result);
        }

        CUtil::json_response(1, 'ok', $result);
    }

    /**
     * @OA\Post(
     *     path="/main/one-yuan-seckill/select-gid",
     *     summary="切换商品绑定",
     *     description="输入 activity_id 和 gid，条件 status=1 且 order_no is not null，绑定最新完成记录的商品ID",
     *     tags={"一元秒杀"},
     *     security={{"userid": {}},{"sessid": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 required={"activity_id","gid"},
     *                 @OA\Property(property="seckill_id", type="integer", description="秒杀ID", example=77),
     *                 @OA\Property(property="gid", type="integer", description="商品ID", example=18)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功响应"
     *     )
     * )
     */
    public function actionSelectGid()
    {
        $params = Yii::$app->request->post();
        $seckill_id = intval($params['seckill_id'] ?? 0);
        $gid = intval($params['gid'] ?? 0);
        if ($seckill_id <= 0 || $gid <= 0) {
            CUtil::json_response(-1, '参数错误');
        }
        if (strlen($this->user_id) > 11) {
            CUtil::json_response(-1, '请先授权哟！');
        }
        list($status, $res) = OneYuanSeckillService::getInstance()->selectGidBind((int)$this->user_id, $seckill_id, $gid);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }


} 