<?php

namespace app\modules\main\controllers;

use app\components\AdvAscribe;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\cart\services\CartService;
use RedisException;
use Yii;
use yii\db\Exception;

class CartController extends CommController
{
    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 购物车列表
     */
    public function actionList()
    {
        // 获取请求参数
        if (empty($this->user_id) || strlen($this->user_id) > 11 || $this->user_id=="undefined") {
            CUtil::json_response(1, 'OK', []);
        }
        // 获取请求参数
        $page      = Yii::$app->request->post('page', 1);
        $page_size = 25;
        $return    = [];

        if ($this->userAuth) {
            // 如果是第一页，获取总数并计算分页页数
            if ($page == 1) {
                $count           = CartService::getInstance()->getCount($this->user_id,$this->platformId);
                $return['pages'] = CUtil::getPaginationPages($count, $page_size);
            }

            // 获取购物车列表和数量信息
            $return['list']  = CartService::getInstance()->getCartList($this->user_id, $page, $page_size, $this->platformId,'', $this->spriceType);
            $return['count'] = CartService::getInstance()->getCount($this->user_id,$this->platformId);
        }

        // 返回JSON响应
        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/main/cart/count",
     *     summary="购物车数量（商品种类）",
     *     description="购物车数量（商品种类）",
     *     tags={"购物车"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionCount()
    {
        $return = [];
        if ($this->userAuth) {
            $return['count'] = CartService::getInstance()->getCount($this->user_id, $this->platformId);
        }
        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @OA\Post(
     *     path="/main/cart/add",
     *     summary="添加购物车",
     *     description="添加购物车",
     *     tags={"购物车"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gid", "sid", "num"},
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="sid", type="string", description="商品规格ID"),
     *                         @OA\Property(property="num", type="string", description="数量")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code", "msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionAdd()
    {
        $gid = Yii::$app->request->post('gid', 0);
        $sid = Yii::$app->request->post('sid', 0);
        $num = Yii::$app->request->post('num', 0);
        if(strlen($this->user_id) > 15){
            CUtil::json_response(-1, '请先授权哟~');
        }
        list($status, $msg) = by::cart()->saveData($this->user_id, $gid, $sid, $num, $this->spriceType);

        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        // $lockCrmSend     = CUtil::getConfig('lockCrmSend','member',MAIN_MODULE) ?? false;
        // $lockCrmSend && list($s,$data) = by::model("EventCenterModel", MAIN_MODULE)->goodsAddCart($this->user_id);
        list($s,$data) = byNew::EventCenterModel()->goodsAddCart($this->user_id);
        //推送腾讯
        if($this->union && stristr($this->union,'tencent')){
            AdvAscribe::factory()->push($this->user_id,'tencent',['user_id'=>$this->user_id,'url'=>$this->referer,'event'=>'ADD_TO_CART','click_id'=>$this->clickId]);
        }
        CUtil::json_response(1, 'ok');
    }


    /**
     * @throws Exception
     * 批量加入购物车
     */
    public function actionBatchAdd()
    {
        $post = Yii::$app->request->post();
        if(strlen($this->user_id) > 15){
            CUtil::json_response(-1, '请先授权哟~');
        }
        list($status, $msg) = CartService::getInstance()->batchAddCart($post, $this->spriceType);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        // $lockCrmSend = CUtil::getConfig('lockCrmSend', 'member', MAIN_MODULE) ?? false;
        // $lockCrmSend && list($s, $data) = by::model("EventCenterModel", MAIN_MODULE)->goodsAddCart($this->user_id);
        list($s, $data) = byNew::EventCenterModel()->goodsAddCart($this->user_id);
        //推送腾讯
        if ($this->union && stristr($this->union, 'tencent')) {
            AdvAscribe::factory()->push($this->user_id, 'tencent', ['user_id' => $this->user_id, 'url' => $this->referer, 'event' => 'ADD_TO_CART', 'click_id' => $this->clickId]);
        }
        CUtil::json_response(1, 'ok');
    }


    /**
     * @throws Exception
     * 修改购物车
     */
    public function actionModify()
    {
        $id = Yii::$app->request->post('id', 0);
        $sid = Yii::$app->request->post('sid', 0);
        $num = Yii::$app->request->post('num', 0);

        list($status, $ret) = by::cart()->modifyNum($this->user_id, $id, $sid, $num, $this->spriceType);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * 删除购物车商品
     */
    public function actionDel()
    {
        $ids = Yii::$app->request->post('ids', '');

        $ida = explode(',', $ids);

        list($status, $ret) = by::cart()->del($this->user_id, $ida);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * 计算最优优惠劵价格
     * @throws Exception
     */
    public function actionUseCard()
    {
        $post = Yii::$app->request->post();
        $coupon_id = $post['coupon_id'] ?? 0;
        $consume_id = $post['consume_id'] ?? 0;
        $gcombines = $post['gcombines'] ?? [];
        if(empty($gcombines)){
            CUtil::json_response(-1, '商品信息不能为空');
        }
        $bestCouple = by::Ouser()->GetBestCouple($this->user_id, $gcombines, $this->getChannel, $this->spriceType, $coupon_id, $consume_id);
        CUtil::json_response(1, 'ok', $bestCouple);
    }

    /**
     * 内购商品不能与普通商品一起结算
     * @return void
     * @throws Exception
     */
    public function actionCheckGoods()
    {
        // 商品
        $gcombines = Yii::$app->request->post('gcombines', '');
        if (empty($gcombines)) {
            CUtil::json_response(-1, '请选择商品');
        }
        list($status, $res) = by::Omain()->checkGoods($this->user_id, json_decode($gcombines, true));
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok');
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 插件耗材批量加入购物车
     */
    public function actionPlugBatchAdd()
    {
        // 获取请求参数
        $postParams = Yii::$app->request->post();

        // 校验用户 ID 是否有效
        if (strlen($this->user_id) > 15) {
             CUtil::json_response(-1, '请先授权哟~');
        }

        // 生成操作唯一标识符
        $operationKey = CUtil::getAllParams(__FUNCTION__, $this->user_id);

        // 校验操作频率，限制 5 秒内只能操作一次
        list($isAllowed) = by::model("CommModel", MAIN_MODULE)->AccFrequency(
                $this->user_id,
                $operationKey,
                1,
                "EX",
                5
        );
        if (!$isAllowed) {
             CUtil::json_response(-1, '请勿频繁操作');
        }

        // 批量添加购物车操作
        list($isSuccess, $message) = CartService::getInstance()->plugBatchAddCart($postParams, $this->spriceType);
        if (!$isSuccess) {
             CUtil::json_response(-1, $message);
        }

        // 判断是否需要锁定 CRM 推送
        $isCrmSendLocked = CUtil::getConfig('lockCrmSend', 'member', MAIN_MODULE) ?? false;
        if ($isCrmSendLocked) {
            by::model("EventCenterModel", MAIN_MODULE)->goodsAddCart($this->user_id);
        }

        // 腾讯广告归因推送
        if (!empty($this->union) && str_contains($this->union, 'tencent')) {
            AdvAscribe::factory()->push(
                    $this->user_id,
                    'tencent',
                    [
                            'user_id'  => $this->user_id,
                            'url'      => $this->referer,
                            'event'    => 'ADD_TO_CART',
                            'click_id' => $this->clickId,
                    ]
            );
        }

        // 返回成功响应
         CUtil::json_response(1, '操作成功');
    }

}
