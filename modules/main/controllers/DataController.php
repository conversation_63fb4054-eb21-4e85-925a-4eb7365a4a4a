<?php
/**
 * Created by IntelliJ IDEA.
 * User: LYT
 * Date: 2020/9/21
 * Time: 15:31
 */

namespace app\modules\main\controllers;

use app\components\Crm;
use app\components\JwtTools;
use app\components\Mall;
use app\components\WeiXin;
use app\components\WxTransfer;
use app\jobs\TryBuyUserPathJob;
use app\jobs\UserSourceJob;
use app\jobs\WeWorkSyncJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\UserModel;
use app\modules\main\services\DataService;
use app\modules\main\services\UserBindService;
use Yii;
use yii\db\Exception;

class DataController extends CommController
{
    /**
     * @throws Exception
     *
     */
    public function actionUinfo()
    {

        $ouids = Yii::$app->request->post('ouids',[]);
        $fields = Yii::$app->request->post('fields',[]);

        if (empty($ouids)) {
            CUtil::json_response(-1, '用户不存在');
        }

        if (!empty($fields)) {
            $fields = explode(",", $fields);
        }

        if (strpos($ouids, ',') === false) {
            $ouids = [CUtil::uint($ouids)];
        } else {
            $ouids = explode(",", $ouids);
            $ouids = array_map(function ($value) {
                return CUtil::uint($value);
            }, $ouids);
        }

        if (count($ouids) > 20) {
            CUtil::json_response(-1, '超过允许长度');
        }
        $ouid_arr = [];
        array_walk($ouids, function ($ouid, $key) use ($fields, &$ouid_arr) {
            list($status, $ret) = by::users()->getIdentities($this->user_id, $ouid, $fields);
            if (!$status) {
                return;
            }

            $ouid_arr[] = $ret;
        });

        CUtil::json_response(1, 'ok', $ouid_arr);

    }

    /**
     * @throws Exception
     * @throws Exception
     * 修改用户信息
     */
    public function actionModify()
    {

        $post = Yii::$app->request->post();
        unset($post['active_time']);
        if (empty($post)) {
            CUtil::json_response(-1, '没有要修改的信息');
        }

        if (isset($post['real_name']) && ctype_space($post['real_name'])) {
            CUtil::json_response(-1, '姓名输入有误');
        }
        $status = $this->userModel->updateMembersInfoNew($this->user_id, $post);
        if (!$status) {
            CUtil::json_response(-1, '保存失败');
        }

        // 修改信息后同步到队列
        if ($this->userAuth) {
            Mall::factory()->push($this->user_id, 'centerUpdate', ['user_id' => $this->user_id]);
            Mall::factory()->push($this->user_id, 'mallUpdate', ['user_id' => $this->user_id,'user_detail'=>$post]);
        }

        CUtil::json_response(1, '保存成功');
    }

  /**
     * @OA\Post(
     *     path="/main/data/phone-auth",
     *     summary="统一手机授权",
     *     description="统一手机授权",
     *     tags={"授权"},
     *     @OA\RequestBody(
     *     @OA\MediaType(  
     *          mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API，具体按情况提供"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="encryptedData", type="string",description="手机号码加密字符串"),
     *              @OA\Property(property="auth_type", type="string",description="wx 小程序授权 alipay 支付宝授权 默认小程序授权"),
     *              @OA\Property(property="iv", type="string",description="加密算法的初始向量"),
     *              @OA\Property(property="openudid", type="string",description="前端授权code"),
     *              @OA\Property(property="r_code", type="integer",description="推荐人code"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionPhoneAuth()
    {
        $post          = Yii::$app->request->post();
        $encryptedData = $post['encryptedData'] ?? "";   //包括敏感数据在内的完整用户信息的加密数据
        $iv            = $post['iv'] ?? "";              //加密算法的初始向量
        $openudid      = $post['openudid'] ?? "";        //前端授权code
        $r_code        = $post['r_code'] ?? '';          //推荐人code
        $shop_code     = $post['shop_code'] ?? '';       //门店来源
        $employee_uid  = $post['employee_uid'] ?? '';    //员工id

        $auth_type = $post['auth_type'] ?? 'wx';
        if($auth_type == 'alipay'||in_array($this->api,['i_1712037455','a_1712037350'])){
            $this->actionAlipayPhoneAuth();
            return;
        }

        //解码手机
        if (!YII_ENV_PROD && isset($post['provider']) && strpos($post['provider'], 'test') !== false) {
            $phone = str_replace('#','',$encryptedData);
            if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
                CUtil::json_response(-1, '非法手机号');
            }
        }else{
            list($status, $phone) = by::Phone()->auth($openudid, $iv, $encryptedData);
            if (!$status) {
                CUtil::json_response(-1, $phone);
            }
        }

        if (empty($this->userAuth)) {
            list($status, $this->user_id) = WxTransfer::factory()->transferRegisterData($this->user_id, $phone);
            if (!$status || empty($this->user_id)) {
                CUtil::json_response(-1, $this->user_id);
            }
        } else {
            //数据库更新
            list($status, $msg) = by::Phone()->SaveRelation($this->user_id, $phone);
            if (!$status) {
                CUtil::json_response(-1, $msg);
            }
        }

        $r_id = '';
        if (!empty($r_code)) {
            list($r_id) = by::userGuide()->decrypt($r_code);
            $r_id = isset($r_id) ? CUtil::uint($r_id) : 0;
        }

        $user_shop_code = '';
        if (!empty($shop_code)) {
            list($user_shop_code) = by::retailers()->decrypt($shop_code);
            $user_shop_code = isset($user_shop_code) ? trim($user_shop_code) : "";
        }

        // 注册会员信息同步IOT、CRM
        !YII_ENV_PROD && CUtil::debug($user_shop_code.'|'.$this->user_id,'shop_code');
        Mall::factory()->push($this->user_id,'centerRegister', ['user_id' => $this->user_id, 'r_id' => $r_id, 'phone' => $phone, 'shop_code' => $user_shop_code,'employee_uid'=>$employee_uid]);

        //todo 注册成功后推送公众号消息推送队列
        $umain = by::users()->getUserMainInfo($this->user_id);
        $user_extend = by::userExtend()->getUserExtendInfo($this->user_id);
        $push_data = [
            'type' => by::WxNotice()::TYPE['REG_USER'],
            'user_id' => $this->user_id,
            'card'    => $user_extend['card'] ?? '',
            'level'   => UserModel::USER_LEVEL['v1'] . '觅享家', // 铜牌觅享家
            'reg_time' => $umain['reg_time']
        ];
        by::WxNotice()->AddPush($push_data);

        $user = by::users()->getOneByUid($this->user_id);
        // 初始化登录session
        $user['sessid']      = JwtTools::factory()->encodeJsonWebToken($user);
        $user['user_id']     = $this->user_id;
        $user['phone']       = $phone;
        $user['active_time'] = time();

        // 当前用户来源 插入队列
        if ($this->user_id && strlen($this->user_id) < 11 && !empty($this->referer)) {
            \Yii::$app->queue->push(new UserSourceJob([
                'referer' => $this->referer,
                'userId'  => $this->user_id,
            ]));
        }


        CUtil::json_response(1, 'OK', $user);
    }



    /**
     * @OA\Post(
     *     path="/main/data/alipay-phone-auth",
     *     summary="支付寶用戶授權",
     *     description="支付寶用戶授權",
     *     tags={"通用"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                    @OA\Schema(ref="#/components/schemas/AlipayPhoneAuth"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionAlipayPhoneAuth()
    {
        $post = Yii::$app->request->post();
        $encryptedData = $post['encryptedData'] ?? ""; //包括敏感数据在内的完整用户信息的加密数据
        $openudid = $post['openudid'] ?? ""; //前端授权code
        list($status, $phone) = by::Phone()->AlipayAuth($encryptedData);
        if ($status === false) {
            CUtil::json_response(-1,$phone);
        }

        list($status, $ret) = DataService::getInstance()->AlipayUserAuth($this->user_id, 20, $phone, $openudid);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $this->user_id = intval($ret['user_id'] ?? 0);
        if(empty($this->user_id)) {
            CUtil::json_response(-1, '用户不存在');
        }

        $this->userAuth = 1;
        //todo 初始化登录session
        $user = by::users()->getOneByUid($this->user_id);
        
        //返回session
        $user['sessid']      = JwtTools::factory()->encodeJsonWebToken($user);
        $user['user_id']     = $this->user_id;
        $user['phone']       = $phone;
        $user['active_time'] = time();
        


        // 当前用户来源 插入队列
        if ($this->user_id && strlen($this->user_id) < 11 && !empty($this->referer)) {
            \Yii::$app->queue->push(new UserSourceJob([
                'referer' => $this->referer,
                'userId'  => $this->user_id,
            ]));
        }

        CUtil::json_response(1, 'OK', $user);
    }

    /**
     * @throws Exception
     * 统一数据上报
     */
    public function actionCallback()
    {
        $post = \Yii::$app->request->post();

        $type = $post['type'] ?? 0;           //必须有字段

        //内容分享上报
        by::WdynamicMain()->UpdateHeat($type, $post);

        CUtil::json_response(1, 'ok');
    }

    /**
     * 统一获取AccessToken
     */
    public function actionGetToken()
    {
        //todo 测试服检测IP地址
        $ips = CUtil::getConfig('ips', 'common', MAIN_MODULE);
        $current_ip = CUtil::get_client_ip();
        if ($current_ip != $ips['test'] ?? "-1") {
            CUtil::json_response(-1, "远程服务器不存在");
        }

        list($status, $ret) = WeiXin::factory()->getUniqueAccessToken();
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', ['token' => $ret]);
    }

    /**
     * 绑定导购关系
     * @throws Exception
     */
    public function actionBoundGuide()
    {
        $code = Yii::$app->request->post('code', '');
        if (empty($this->userAuth)) {
            list($guide_id) = $this->userGuideModel->decrypt($code);
            $this->userGuideModel->bound($this->user_id, $guide_id);
        }else{
            list($guide_id) = by::userGuide()->decrypt($code);
            by::userGuide()->bound($this->user_id, $guide_id);
        }

        CUtil::json_response(1, 'OK');
    }

    /**
     * 快递公司
     */
    public function actionExpCom()
    {
        $com = CUtil::getConfig('com', 'express', MAIN_MODULE);
        CUtil::json_response(1, 'OK', $com);
    }


    /**
     * @OA\Post(
     *     path="/main/data/upload-file",
     *     summary="通用图片上传接口",
     *     description="通用图片上传接口",
     *     tags={"通用"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                    @OA\Schema(ref="#/components/schemas/DataUploadFile"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionUploadFile()
    {
        @ini_set('post_max_size', '20M');
        @ini_set('upload_max_filesize', '20M');
        $post        = Yii::$app->request->post();
        $file        = $_FILES['file'] ??  "";

        list($status,$ret) = by::dataModel()->DataFileSave($this->user_id,$file,$post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,"OK",$ret);
    }

    /**
     * @OA\Post(
     *     path="/main/data/year-report",
     *     summary="年终报告",
     *     description="年终报告",
     *     tags={"通用"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/YearReportDataResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionYearReport()
    {
        $userId = $this->user_id;
        if (strlen($userId) > 11 || empty(CUtil::uint($userId))) {
            CUtil::json_response(-1, '请先授权，再查看年终报告！');
        }

        list($status, $ret) = DataService::getInstance()->getYearReport($userId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "OK", $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/data/zfb-password",
     *     summary="支付宝口令",
     *     description="支付宝口令",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data",type="object",ref="#/components/schemas/YearReportDataResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionZfbPassword()
    {
        $userId = $this->user_id;
        list($status, $ret) = DataService::getInstance()->GetZfbPassword($userId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, "OK", $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/data/user-path",
     *     summary="修改用户链路",
     *     description="修改用户链路",
     *     tags={"先试后买活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="scene", type="string", default="", description="场景值 （sign_agreement签署协议 enter_zfb进入支付宝 enter_survey进入问卷 submit_survey提交问卷）")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionUserPath()
    {
        $userId = $this->user_id;
        $scene  = Yii::$app->request->post('scene', 0);

        // 异步更新用户链路
        if ($userId && strlen($userId) < 11) {
            \Yii::$app->queue->delay(DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $this->user_id,
                'scene'   => $scene
            ]));
        }
        CUtil::json_response(1, 'ok');
    }


    public function actionWxUlink()
    {
        $code = Yii::$app->request->post('code', 0);
        if (empty($code)) {
            CUtil::json_response(-1, '参数错误');
        }

        $code = str_replace(" ","+",trim($code));

        list($status, $ulink) = by::model('WxUlinkModel', 'main')->GetUlinkBycode($code);

        if (!$status) {
            CUtil::json_response(-1, $ulink);
        }

        CUtil::json_response(1, 'ok', ['ulink' => $ulink]);
    }


    /**
     * @OA\Post(
     *     path="/main/data/time",
     *     summary="获取系统当前时间戳",
     *     description="支持返回秒级或毫秒级时间戳，默认返回毫秒级",
     *     tags={"公共接口"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="timestamp_type", type="string", enum={"second", "millisecond"}, default="millisecond", description="时间戳类型：second-秒级，millisecond-毫秒级")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息及时间戳",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="time", type="string", description="时间戳"),
     *                 @OA\Property(property="timestamp_type", type="string", description="时间戳类型")
     *             )
     *         )
     *     )
     * )
     */
    public function actionTime()
    {
        // 获取时间戳类型参数，默认为毫秒级
        $timestampType = Yii::$app->request->post('timestamp_type', 'millisecond');
        // 获取当前时间
        $date = new \DateTime();

        // 根据参数类型返回不同精度的时间戳
        if ($timestampType === 'second') {
            // 秒级时间戳
            $timestamp = $date->getTimestamp();
        } else {
            // 毫秒级时间戳
            $milliseconds = $date->format('v');
            $timestamp    = $date->getTimestamp() . substr($milliseconds, 0, 3);
        }

        // 返回结果
        CUtil::json_response(1, 'ok', [
                'time'           => (string) $timestamp,
                'timestamp_type' => $timestampType
        ]);
    }


    public function actionMemberMallRule()
    {
        CUtil::json_response(1, 'ok', [
                'rule' => 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/prod/images/202507/68823c3b87e835570012884.png',
        ]);
    }


    /**
     * @return void
     * 生成短链接
     */
    public function actionShortLink()
    {
        // 获取POST提交的长链接，默认为空字符串
        $longLink = Yii::$app->request->post('long_link', '');

        // 调用数据服务生成短链接
        list($status, $ret) = DataService::getInstance()->getShortLink($longLink);

        // 根据生成结果返回对应JSON响应
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', ['short_link' => $ret]);
    }
}
