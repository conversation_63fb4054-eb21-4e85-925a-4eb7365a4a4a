<?php

namespace app\modules\main\controllers;


use app\components\AppCRedisKeys;
use app\components\MemberCenter;
use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\byNew;
use app\models\CodeModel;
use app\models\CUtil;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\services\MemberCenterService;
use Yii;

class MemberCenterController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/member-center/level-benefit",
     *     summary="获取所有等级权益数据",
     *     description="获取所有等级权益数据",
     *     tags={"会员中心"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                         @OA\Schema(
     *                         @OA\Property(property="single", type="integer", default="1", description="区分单个调用本地数据（这里不是V2版本区分）"),
     *                         @OA\Property(property="single_v", type="integer", default="1", description="新旧版本区分（旧版本不传）V2版本区分"),
     *                         @OA\Property(property="typeCode", type="string", default="", description="例：(new_user_gift、point_crash)")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionLevelBenefit()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $level    = Yii::$app->request->post('level', '');
        $typeCode = Yii::$app->request->post('typeCode', '');
        $single   = Yii::$app->request->post('single', '');
        $singleV   = Yii::$app->request->post('single_v', '');
        //判断app还是小程序
        list($status, $ret) = MemberCenterService::getInstance()->userBenefit(['user_id' => $userId, 'level' => $level, 'typeCode' => $typeCode, 'platformSource' => $this->platformSource, 'single' => $single,'single_v'=>$singleV]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @return void
     * 获取运营说明配置
     */
    public function actionOperateConfig()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $lang     = Yii::$app->request->post('lang', 'zh');
        $category = Yii::$app->request->post('category', '');
        if (empty($category)) {
            CUtil::json_response(-1, '说明类型不能为空');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('operateConfig', ['user_id' => $userId, 'lang' => $lang, 'category' => $category, 'platformSource' => $this->platformSource]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/member-center/growth-center",
     *     summary="会员中心接口",
     *     description="会员中心接口",
     *     tags={"会员中心"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                         @OA\Schema(
     *                         @OA\Property(property="single", type="integer", default="1", description="新旧版本区分（旧版本不传）"),
     *                         @OA\Property(property="level", type="integer", default="", description="当前等级（v1、v2、v3、v4、v5）")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionGrowthCenter()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $level    = Yii::$app->request->post('level', '') ?? null;
        $typeCode = Yii::$app->request->post('type_code', '') ?? null;
        $single = Yii::$app->request->post('single', '') ?? null;
        $singleV = Yii::$app->request->post('single_v', '') ?? null;
        list($status, $ret) = by::memberCenterModel()->CenterMessage('growthCenter', ['user_id' => $userId, 'level' => $level, 'typeCode' => $typeCode, 'platformSource' => $this->platformSource,'single'=>$single,'single_v'=>$singleV]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 会员基本信息接口
     */
    public function actionBasicInfo()
    {
        $userId = $this->user_id ?? null;
        $single = Yii::$app->request->post('single') ?? 0;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('basicInfo', ['user_id' => $userId, 'single' => $single]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        // 获取积分扣除比例
        $deductionRate         = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
        $ret['deduction_rate'] = $deductionRate;
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * 更新弹框状态
     * @return void
     */
    public function actionHasShowBirthCard()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $r_key = AppCRedisKeys::hasShowBirthCard($userId);
        $redis = by::redis();
        $redis->setex($r_key, 86400, true);
        CUtil::json_response(1, 'ok');
    }

    /**
     * 服务权益说明（如果用户user_id=0，则给默认的会员权益信息）
     * @return void
     */
    public function actionBenefitInfo()
    {
        // 用户ID
        $userId = $this->user_id ?? 0;
        if (strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }

        // null 转为 0
        $userId = $userId ?: 0;
        // 获取服务权益
        $ret = MemberCenterService::getInstance()->getBenefitInfo($userId);
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 会员任务中心
     */
    public function actionTaskList()
    {
        $userId = $this->user_id ?? 0;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $type = Yii::$app->request->post('type', '');
        list($status, $ret) = MemberCenterService::getInstance()->getTaskList($userId, $this->platformSource,$type,$this->version);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        foreach ($ret as $key => $item) {
            $ret[$key]['money'] = empty($item['money']) ? '0' : bcdiv($item['money'], 100, 2);
            // // 终身性任务已完成的，不存在待领取的金币，任务不显示（积分那边已经完成，金币任务不显示）
            // if ($item['timePeriodUnit'] == 'all' && $item['completed'] == true && $item['haveUnCollect'] == false) {
            //     unset($ret[$key]);
            // }
        }
        CUtil::json_response(1, 'ok', array_values($ret));
    }
    
    /**
     * @return void
     * 会员任务中心
     */
    public function actionNoAuthTaskList()
    {
        $type = Yii::$app->request->post('type', '');
        $limit = Yii::$app->request->post('limit', 3);
        list($status, $ret) = MemberCenterService::getInstance()->getNoAuthTaskList($this->platformSource, $type, $limit,$this->version);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        foreach ($ret as $key => $item) {
            $ret[$key]['money'] = empty($item['money']) ? '0' : bcdiv($item['money'], 100, 2);
        }
        CUtil::json_response(1, 'ok', array_values($ret));
    }

    /**
     * @return void
     * 导入说明
     */
    public function actionImportConfig()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $file = $_FILES['file'] ?? '';
        list($status, $ret) = by::memberCenterModel()->CenterMessage('importConfig', ['user_id' => $userId, 'file' => $file]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @return void
     * 签到
     */
    public function actionSignIn()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('signIn', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 获取签到状态
     */
    public function actionSignInStatus()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('signInStatus', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 签到首页数据
     */
    public function actionSignInHome()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('signInHome', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 获取月签到明细
     */
    public function actionSignInMonthDetail()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $monthStartTime = Yii::$app->request->post('start_time', intval(START_TIME));
        $monthEndTime   = Yii::$app->request->post('end_time', 0);
        $monthStartTime = CUtil::uint($monthStartTime);
        $monthEndTime   = CUtil::uint($monthEndTime);
        $period_time    = array_unique(array_filter([$monthStartTime, $monthEndTime]));
        $type           = Yii::$app->request->post('type', 1); // 1:积分 3:金币

        list($status, $ret) = by::memberCenterModel()->CenterMessage('signInMonthDetail', ['user_id' => $userId, 'period_time' => $period_time, 'type' => $type]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 消费金分转元
        if ($type == 4) {
            foreach ($ret as $key => $item) {
                foreach ($item as $key2 => $item2) {
                    $item[$key2]['numValue'] = empty($item2['numValue']) ? '0' : bcdiv($item2['numValue'], 100, 2);
                }
                $ret[$key] = $item;
            }
        }

        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     *
     * @return void
     * 获取会员积分和觅享分
     */
    public function actionScoreGet()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreGet', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 领取全部积分
     */
    public function actionScoreCollectAll()
    {
        // 查询类型 1 积分，3 金币；不必须
        $userId = $this->user_id ?? null;
        $type = Yii::$app->request->post('type', 1);
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreCollectAll', ['user_id' => $userId,'type'=>$type]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @return void
     * 领取积分
     */
    public function actionScoreCollect()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $id = Yii::$app->request->post('id', '');
        if (empty($id)) {
            CUtil::json_response(-1, '事件ID不能为空！');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreCollect', ['user_id' => $userId, 'id' => $id]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @return void
     * 觅享分明细
     */
    public function actionScoreGetGrows()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $type = Yii::$app->request->post('type', 0);
        $page = Yii::$app->request->post('page', 1);
        $size = Yii::$app->request->post('size', 20);
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreGetGrows', ['user_id' => $userId, 'type' => $type, 'page' => $page, 'size' => $size]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 积分明细
     */
    public function actionScoreGetPoints()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $type = Yii::$app->request->post('type', 0);
        $assetType = Yii::$app->request->post('asset_type', 1); // 1 积分  2 觅享分 3 金币
        $page = Yii::$app->request->post('page', 1);
        $size = Yii::$app->request->post('size', 20);
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreGetPoints', ['user_id' => $userId, 'type' => $type, 'asset_type' => $assetType, 'page' => $page, 'size' => $size]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @return void
     * 获取未领取积分明细
     */
    public function actionScoreUncollect()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $type = Yii::$app->request->post('type', 1);
        list($status, $ret) = by::memberCenterModel()->CenterMessage('scoreUncollect', ['user_id' => $userId,'type'=>$type]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 获取连续签到信息
     */
    public function actionContinueSignInfo()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('continueSignInfo', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 重置等级变更提示
     */
    public function actionResetLevelChangePrompt()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('resetLevelChangePrompt', ['user_id' => $userId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 获取任务信息
     */
    public function actionTaskInfo()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $taskCode = Yii::$app->request->post('taskCode', 0);
        list($status, $ret) = by::memberCenterModel()->CenterMessage('taskInfo', ['user_id' => $userId, 'taskCode' => $taskCode]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }




    /**
     * @OA\Post(
     *     path="/main/member-center/check-in-in",
     *     summary="打卡",
     *     description="打卡",
     *     tags={"会员中心"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         content={
     *             @OA\MediaType(
     *                 mediaType="application/x-www-form-urlencoded",
     *                 @OA\Schema(
     *                     required={"member_center_save_id"},
     *                     @OA\Property(property="member_center_save_id", type="integer", description="活动ID")
     *                 )
     *             )
     *         }
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionCheckInIn()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $memberCenterSaveId = Yii::$app->request->post('member_center_save_id', 0) ?? 0;
        if (empty($memberCenterSaveId)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }
        list($status, $ret) = by::memberCenterModel()->CenterMessage('checkInIn', ['user_id' => $userId, 'member_center_save_id' => $memberCenterSaveId]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @return void
     * 获取打卡明细
     */
    public function actionCheckInInDetail()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(1, 'OK',['code'=>CodeModel::STATUS['NOT_MEMBER'],'list'=>[]]);
        }
        $startTime   = strtotime(ActivityConfigEnum::YJHX_START_TIME);
        $endTime     = strtotime(ActivityConfigEnum::YJHX_END_TIME);
        $period_time = array_filter([$startTime, $endTime]);
        list($status, $ret) = by::memberCenterModel()->CenterMessage('checkInInDetail', ['user_id' => $userId, 'period_time' => $period_time]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/main/member-center/app-growth-center",
     *     summary="app会员中心接口",
     *     description="app会员中心接口",
     *     tags={"会员中心"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                         @OA\Schema(
     *                         @OA\Property(property="level", type="integer", default="", description="当前等级（v1、v2、v3、v4、v5）")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionAppGrowthCenter()
    {
        // 获取用户ID并检查是否为空或无效
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }

        // 获取请求参数，设置默认值为空字符串
        $level  = Yii::$app->request->post('level', '');
        $single = 1; //默认新版本

        // 调用业务逻辑层，获取成长中心数据
        list($status, $ret) = MemberCenterService::getInstance()->appGrowthCenter($userId, $level, $this->platformSource, $single);

        // 判断返回状态，失败时统一返回错误信息
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        // 成功时返回数据
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/member-center/collect-task-gold",
     *     summary="领取单个任务金币",
     *     description="领取单个任务金币",
     *     tags={"金币"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                         @OA\Schema(
     *                         @OA\Property(property="group_code", type="string", default="", description="group_code 任务列表中字段"),
     *                         @OA\Property(property="type_code", type="string", default="", description="type_code 任务列表中字段"),
     *                         @OA\Property(property="code", type="string", default="", description="code 任务列表中字段")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionCollectTaskGold()
    {
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }
        $groupCode = Yii::$app->request->post('group_code'); // 任务code
        $typeCode  = Yii::$app->request->post('type_code');  // 任务code
        $code      = Yii::$app->request->post('code');       // 任务code

        if (empty($groupCode) || empty($typeCode) || empty($code)) {
            CUtil::json_response(-1, '事件code不能为空！');
        }

        list($status, $ret) = by::memberCenterModel()->CenterMessage('collectGoldByEventCode', ['user_id' => $userId, 'group_code' => $groupCode, 'type_code' => $typeCode, 'code' => $code]);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/main/member-center/browse",
     *     summary="会员中心-任务提交",
     *     description="会员中心-任务提交",
     *     tags={"会员中心"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                         @OA\Schema(
     *                         @OA\Property(property="type", type="string", default="", description="一元购:oneYuanPurchase ,暴富计划:richPlan, 五折购:fiveDiscountPurchase, 半价购:halfPriceBuy,积分购物:pointsShopping,app分享商品:appShareGoods, app邀请注册:appInviteRegGold")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionBrowse()
    {
        // 获取用户ID并检查是否为空或无效
        $userId = $this->user_id ?? null;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(-1, '请先授权手机号');
        }

        $userId = intval($userId);
        $params = Yii::$app->request->post();
        $type = Yii::$app->request->post('type', ''); // 浏览类型，默认为商品
        list($status, $ret) = MemberCenterService::getInstance()->handleBrowseTask($userId, $type, $params);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, '操作成功');
    }


}
