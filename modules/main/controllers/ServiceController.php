<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/14
 * Time: 11:27
 */
namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class ServiceController extends CommController {

    /**
     * 检查校验sn编码
     */
    public function actionProductMatch(){
        $sn             = \Yii::$app->request->post('sn','');
        list($status,$ret,$code) = by::product()->match($sn,$this->user_id);
        if (!$status) {
            CUtil::json_response($code,$ret);
        }
        CUtil::json_response($code,'ok', $ret);
    }

    /**
     * 产品注册
     */
    public function actionProductReg(){
        $post       = \Yii::$app->request->post();
        $sn         = $post['sn'] ?? '';
        $product_id = $post['product_id'] ?? 0;
        $buy_time   = $post['buy_time'] ?? 0;
        $single     = intval($post['single'] ?? 0);//兼容历史版本

        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', null);
        }

        list($status,$ret,$code) = by::productReg()->reg($this->user_id,$product_id,$sn,$buy_time,$single);
        if (!$status) {
            CUtil::json_response($code,$ret);
        }

        CUtil::json_response($code,'ok',$ret);
    }



    /**
     * @OA\Post(
     *     path="/main/service/product-list",
     *     summary="产品注册记录",
     *     description="产品注册记录",
     *     tags={"产品注册"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          @OA\Property(property="is_act", type="integer", default="0",description="是否参与活动 0不关联 1关联"),
     *          )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ProductListResponse",description="数据")
     *         )
     *     )
     * )
     *
     */
    public function actionProductList(){
        $post   = \Yii::$app->request->post();
        $is_act = CUtil::uint($post['is_act'] ?? '');
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            CUtil::json_response(1, 'OK', []);
        }
        $list   = by::productReg()->userPList($this->user_id,$is_act);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * 获取上次注册的手机号
     */
    public function actionLastPhone(){
        $ids    = by::productReg()->getRegList($this->user_id);

        $id     = current($ids);
        $info   = by::productReg()->getOneById($this->user_id,$id);
        CUtil::json_response(1,'ok',['phone'=>$info['phone']??'']);
    }

    /**
     * 获取微信手机号码
     */
    public function actionAuthPhone() {
        $post                   = \Yii::$app->request->post();
        $encryptedData          = $post['encryptedData'] ?? "";//包括敏感数据在内的完整用户信息的加密数据
        $iv                     = $post['iv'] ?? "";// 加密算法的初始向量
        $openudid               = $post['openudid'] ?? ""; // 前端授权code

        //解码手机
        list($status,$phone)    = by::Phone()->auth($openudid,$iv,$encryptedData);
        if (!$status) {
            CUtil::json_response(-1, $phone);
        }

        CUtil::json_response(1, 'OK',['phone'=>$phone]);
    }
}
