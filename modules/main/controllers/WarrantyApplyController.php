<?php

namespace app\modules\main\controllers;

use app\models\by;
use app\models\CUtil;

use app\modules\log\services\warranty\WarrantyApplyDetailService;
use app\modules\log\services\warranty\WarrantyApplyService;
use app\modules\log\services\warranty\WarrantyCardService;
use app\modules\main\enums\warranty\ApplyRulesEnum;
use Yii;
use yii\db\Exception;

class WarrantyApplyController extends CommController
{

    /**
     * @OA\Post(
     *     path="/main/warranty-apply/add",
     *     summary="申请电子保修卡",
     *     description="申请电子保修卡",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyAdd"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionAdd()
    {
        //用户确认
        $user_id = $this->user_id;
        if(strlen($user_id)>11 || empty(CUtil::uint($user_id)) ){
            CUtil::json_response(-1, '游客请先授权~');
        }

        //参数初步整合
        $aData =  Yii::$app->request->post();
        $aData['user_id'] = $user_id;

        //创建保修卡申请
        list($s, $m, $code) = WarrantyApplyService::getInstance()->SaveWarrantyApply($aData);
        if (!$s) {
            CUtil::json_response(-1, $m, ['code' => $code]);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/main/warranty-apply/platform-list",
     *     summary="电子保修卡-平台列表",
     *     description="电子保修卡-平台列表",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest")
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *               @OA\Property(property="data",type="object",ref="#/components/schemas/WarrantyApplyPlatformListResponse",description="数据")
     *         )
     *     )
     * )
     *
     */
    public function actionPlatformList()
    {
        $data = WarrantyApplyDetailService::getInstance()->purchasePlatform;
        unset($data['ALL']);
        $result = array_map(function ($item) {
            return array_change_key_case($item, CASE_LOWER);
        }, array_values($data));
        CUtil::json_response(1, 'ok', $result);
    }


    /**
     * @OA\Post(
     *     path="/main/warranty-apply/active",
     *     summary="激活电子保修卡",
     *     description="激活电子保修卡",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyActive"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     *
     */
    public function actionActive()
    {
        $post            = \Yii::$app->request->post();
        $reg_id = CUtil::uint($post['reg_id'] ??  0);//注册保修卡
        list($s,$m) = WarrantyCardService::getInstance()->productRegCard($this->user_id,$reg_id);
        if (!$s) {
            CUtil::json_response(-1,$m);
        }
        CUtil::json_response(1,'ok');
    }


    /**
     * @OA\Post(
     *     path="/main/warranty-apply/upload",
     *     summary="申请电子保修卡-上传购买凭证",
     *     description="申请电子保修卡-上传购买凭证",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyUpload"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     *
     * @throws Exception
     */
    public function actionUpload()
    {
        @ini_set('post_max_size', '20M');
        @ini_set('upload_max_filesize', '20M');
        $post        = Yii::$app->request->post();
        $file        = $_FILES['file'] ??  "";

        list($status,$ret) = WarrantyApplyService::getInstance()->FileSave($this->user_id,$file,$post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,"OK",$ret);
    }


    /**
     * @OA\Post(
     *     path="/main/warranty-apply/rules",
     *     summary="申请电子保修卡-相关说明",
     *     description="申请电子保修卡-相关说明",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WarrantyApplyRulesResponse",description="数据")
     *         )
     *     )
     * )
     *
     */
    public function actionRules()
    {
        $images = ApplyRulesEnum::RULE_IMAGES;
        CUtil::json_response(1, "OK", ['images' => $images]);
    }


    /**
     * @OA\Post(
     *     path="/main/warranty-apply/hide",
     *     summary="电子保修卡-隐藏申请信息",
     *     description="电子保修卡-隐藏申请信息",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyHideRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     *
     * @throws Exception
     */
    public function actionHide()
    {
        $apply_no = Yii::$app->request->post('apply_no','');

        list($status,$ret) = WarrantyApplyService::getInstance()->HideApplyInfo($this->user_id,$apply_no);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,"OK",$ret);
    }

}
