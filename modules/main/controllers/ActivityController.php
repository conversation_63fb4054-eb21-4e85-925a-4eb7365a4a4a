<?php

namespace app\modules\main\controllers;


use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\CodeModel;
use app\models\CUtil;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\ActivityConfigModel;
use app\modules\main\services\ActivityConfigService;
use RedisException;
use Yii;
use yii\db\Exception;

class ActivityController extends CommController
{

	/**
	 * 礼包列表
	 * @throws Exception
	 */
    public function actionList()
    {
        //新人福利弹窗数据--上锁
        $lock = CUtil::getConfig('lockCoupon', 'member', MAIN_MODULE) ?? false;
        if ($lock) {
            CUtil::json_response(1, '获取成功', []);
        }
        if (empty($this->user_id) || strlen($this->user_id) > 11) {
            $this->user_id = 0;
        }
        $grant_type = Yii::$app->request->post('grant_type', 1); // 1 新人礼包 2 推荐有礼
        $list       = ActivityConfigService::getInstance()->activityList($grant_type, $this->user_id);
        CUtil::json_response(1, '获取成功', $list);
    }

    /**
     * 领取活动礼包
     * @throws Exception
     * @throws RedisException
     */

    public function actionReceive()
    {
        //优惠卷领取上锁
        $lock      = CUtil::getConfig('lockCoupon', 'member', MAIN_MODULE) ?? false;
        $testUsers = CUtil::getConfig('testCouponUser', 'member', MAIN_MODULE) ?? [];
        $user_id   = $this->user_id;
        if(strlen($user_id)>11 || empty(CUtil::uint($user_id))){
            CUtil::json_response(-1, '请先授权，再来领取优惠券！');
        }
        $user_id = CUtil::uint($user_id);
        $phone     = by::Phone()->GetPhoneByUid($user_id);
        if ($lock && !in_array($phone, $testUsers)) {
            CUtil::json_response(1, '领取成功');
        }
        $ids        = Yii::$app->request->post('ids', '');
        $grant_type = Yii::$app->request->post('grant_type', 1); // 1 新人礼包 2 推荐有礼 3通用礼包
        $market_ids = Yii::$app->request->post('market_ids', '');
        $r_id       = Yii::$app->request->post('r_id', 0);
        //实例化service
        $activityConfigService = ActivityConfigService::getInstance();
        switch ($grant_type) {
            case ActivityConfigModel::GRANT_TYPE['newUser']:
            case ActivityConfigModel::GRANT_TYPE['monthly']:
            case ActivityConfigModel::GRANT_TYPE['birthday']:
            case ActivityConfigModel::GRANT_TYPE['goods_detail']: // 直接领取
                if (empty($ids)) {
                    $ids = by::activityConfigModel()->getActivityIdsByType($grant_type);
                    if (empty($ids)) {
                        CUtil::json_response(1, '活动礼包不存在');
                    }
                }
//                list($status, $msg) = by::activityConfigModel()->userDraw($this->user_id, $ids);
                list($status, $msg) = $activityConfigService->UserDrawNew($user_id, $ids, $market_ids, $r_id,$grant_type);
                break;
            case ActivityConfigModel::GRANT_TYPE['common_activity']:
                //判断是否满足活动条件
                $restrictionResult = by::activityConfigModel()->activityRestriction($ids, $user_id);
                if (!$restrictionResult) {
                    CUtil::json_response(-1, '您不满足活动条件');
                }
                if (empty($market_ids)) {
                    CUtil::json_response(-1, '请选择优惠卷');
                }
                list($status, $msg) = by::activityConfigModel()->userCommonDraw($user_id, $ids, $market_ids, $r_id);
                break;
            default:
                list($status, $msg) = [false, '没有该活动，领取失败'];
        }
        if ($status) {
            CUtil::json_response(1, $msg);
        }

        CUtil::json_response(-1, $msg);
    }

    /**
     * @throws Exception
     * 更新推荐有礼各类型弹窗次数
     */
   public function actionUpdatePopupCount()
   {
       $action = Yii::$app->request->post('action', 0);
       if (strlen($this->user_id) > 11 || empty(CUtil::uint($this->user_id))) {
           CUtil::json_response(1, 'OK');
       }

       by::userRecommend()->updatePopupCount($this->user_id, $action);

       CUtil::json_response(1,'OK');
   }


    /**
     * @OA\Post(
     *     path="/main/activity/old-to-new",
     *     summary="活动-以旧换新-商品列表",
     *     description="活动-商品列表",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionOldToNew()
    {
//        $list = by::activityConfigModel()->getOldToNew(); 2022/11/11 失效了
//        $main_json = file_get_contents(__DIR__ . '/../enums/oldtonew/main_machine.json');
//        $part_json = file_get_contents(__DIR__ . '/../enums/oldtonew/part_machine.json');
//        $rules     = file_get_contents(__DIR__ . '/../enums/oldtonew/oldToNewRules.txt');
//        $data      = [
//            'list'  => json_decode($part_json, true),
//            'list1' => json_decode($main_json, true),
//            'rules' => $rules,
//        ];
//        CUtil::json_response(1, 'OK', $data);

        YII_ENV_PROD && CUtil::json_response(-1, '非常抱歉，活动暂时暂停，给您带来不便，请您谅解！');
        $data = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../enums/oldToNewV2/yjhx20231018.json') :
            file_get_contents(__DIR__ . '/../enums/oldToNewV2/yjhx20231018Test.json');
        $rules = file_get_contents(__DIR__ . '/../enums/oldToNewV2/rules20231018.txt');
        CUtil::json_response(1, 'OK', [
            'data'  => json_decode($data, true) ?? null,
            'rules' => $rules,
        ]);
   }


    /**
     * @return void
     * @throws Exception
     * 获取内购/其他优惠券列表
     */
    public function actionMarketDefineList()
    {
        $post = Yii::$app->request->post();
        $userId = $this->user_id;
        list($status,$list) = by::activityConfigModel()->getMarketDefineList($post,$userId);
        if(!$status){
            CUtil::json_response(-1, $list);
        }
        CUtil::json_response(1, 'ok',$list);
   }

    /**
     * @return void
     * 领取优惠券
     *
     */

    public function actionMarketDefineDraw()
    {
        $post = Yii::$app->request->post();
        $userId = $this->user_id;
        list($status,$msg) = by::activityConfigModel()->marketDefineDraw($post,$userId);
        if(!$status){
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/main/activity/activity-detail",
     *     summary="活动-活动详情",
     *     description="活动-活动详情",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="id", type="integer", default="", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityDetail()
    {
        $acId = Yii::$app->request->post('id','');
        $userId = $this->user_id;
        if (empty($acId)){
            CUtil::json_response(-1, '活动id为空');
        }
        list($status,$aData) = by::activityConfigModel()->getActivityDetail($acId,$userId);
        if ($status){
            CUtil::json_response(1, 'ok',$aData);
        }else{
            CUtil::json_response(-1, $aData);
        }

    }


    /**
     * @OA\Post(
     *     path="/main/activity/activity-goods",
     *     summary="活动-根据SN获取活动商品",
     *     description="活动-根据SN获取活动商品",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="sn", type="string", default="", description="SN号")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityGoodsList",description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityGoods()
    {
        $post         = \Yii::$app->request->post();
        $sn           = CUtil::removeXss(trim($post['sn'] ?? ''));
        $remove_chars = array('\\', ';',',');
        $sn           = str_replace($remove_chars, '', $sn);
        $list = ActivityConfigService::getInstance()->activityGoodsList($sn, $this->user_id);
        CUtil::json_response(1, '获取成功', $list);
    }


    /**
     * @OA\Post(
     *     path="/main/activity/activity-check-detail",
     *     summary="活动-获取活动/用户打卡数据",
     *     description="活动-获取活动/用户打卡数据",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityCheckDetail()
    {
        $user_id = $this->user_id ?? null;
        $startTime   = strtotime(ActivityConfigEnum::CHECKIN_START_TIME);
        $endTime     = strtotime(ActivityConfigEnum::CHECKIN_END_TIME);
        //判断是否为白名单
        $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?? [];
        if($testUsers && in_array($user_id,$testUsers)){
            $startTime = strtotime('2024-10-21 00:00:00');
        }
        $period_time = array_filter([$startTime, $endTime]);
        $list = ActivityConfigService::getInstance()->activityCheckDetail($user_id, $period_time);
        CUtil::json_response(1, 'ok', $list);
    }


    /**
     * @OA\Post(
     *     path="/main/activity/recommend-goods",
     *     summary="活动-双11等-推荐商品",
     *     description="活动-双11等-推荐商品",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionRecommendGoods()
    {
        // 用户ID
        $userId = $this->user_id ?? 0;
        // todo 获取用户当前可用积分  判断 积分商品区间值

        // 1.主机
//        $mains = ActivityConfigEnum::getMainGoods();
//        $mains = ActivityConfigService::getInstance()->assembleMainData($userId, $mains);
//        $mains = ActivityConfigService::getInstance()->queryGoodsInfo($mains);

//        // 2.配件
//        $parts = ActivityConfigEnum::getPartGoods();
//
//        // 3.积分商品
        $points = ActivityConfigEnum::getCheckinRewardGoodsData();
//        $points = ActivityConfigService::getInstance()->assemblePointData($userId, $points);
        // 设置积分兑换状态
        $points = ActivityConfigService::getInstance()->setPointGoodsStatus($userId, $points);

        // 4.积分秒杀商品
//        $secKillPoints = ActivityConfigEnum::getSeckillPointGoods();

        // 5.配件秒杀
        $secKills = ActivityConfigEnum::getSeckillGoods();
        $secKills = ActivityConfigService::getInstance()->setSeckillGoodsStatus($secKills);


        // 设置秒杀商品状态
//        $secKills = ActivityConfigService::getInstance()->setSecKillGoodsStatus($user_id, $secKills);

        // 线下门店商品
        $storeGoods = ActivityConfigEnum::getStoreGoods();
        CUtil::json_response(1, 'OK', [
//                'mains'    => $mains ?? null,
//                'parts'    => $parts ?? null,
                'points'      => $points ?? null,
                'seckills'    => $secKills ?? null,
//                'seckill_points' => $secKillPoints ?? null,
                'store_goods' => $storeGoods
        ]);
    }



    /**
     * @OA\Post(
     *     path="/main/activity/rules",
     *     summary="活动-双11等-活动规则",
     *     description="活动-双11等-活动规则",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", description="数据", @OA\Items())
     *         )
     *     )
     * )
     */
    public function actionRules()
    {
        $rules = file_get_contents(__DIR__ . '/../enums/activityRules/rules20250329.txt');
        CUtil::json_response(1, 'OK', [
            'rules'  => $rules,
        ]);
    }


    /**
     * @OA\Post(
     *     path="/main/activity/check-my-machine",
     *     summary="活动-检查我的机器",
     *     description="活动-检查我的机器",
     *     tags={"活动"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *                  @OA\JsonContent(
     *                  allOf={
     *                      @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                  }
     *              )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", ref="#/components/schemas/DeviceData", description="设备数据")
     *         )
     *     )
     * )
     */
    public function actionCheckMyMachine()
    {
        $userId = $this->user_id;
        if (empty($userId) || strlen($userId) > 11) {
            CUtil::json_response(RespStatusCodeConst::LOGIN_ERROR_CODE);
        }

        $category = ActivityConfigEnum::getGoodsCategory();

        list($status, $data) = ActivityConfigService::getInstance()
                ->checkMyMachine($userId, $category);

        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $data);
        }
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'OK', $data);
    }

}
