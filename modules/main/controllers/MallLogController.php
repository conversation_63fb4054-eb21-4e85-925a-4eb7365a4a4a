<?php

namespace app\modules\main\controllers;

use app\components\MallLog;
use app\models\by;
use app\models\CUtil;
use app\modules\main\services\MallLogService;

class MallLogController extends CommController
{
    /**
     *
     * @OA\Post(
     *     path="/main/mall-log/trace",
     *     summary="商城埋点",
     *     description="商城埋点",
     *     tags={"商城埋点"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api", type="string", default="a_1643178000",description="api平台 必传；安卓 'a_1664246268'"),
     *              @OA\Property(property="sign", type="string", default="test-environment",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="test-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test",description="测试环境-是否核验签名"),
     *              @OA\Property(property="log", type="string", default="",description="传递参数"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionTrace()
    {
        // 参数
        $params = \Yii::$app->request->post();

        //频率限制
//        $unique_key = CUtil::getAllParams(__FUNCTION__);
//        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 5);
//        if (!$s) {
//            CUtil::json_response(-1, '频繁请求，请稍候~');
//        }

        // 判空
        if (empty($log = $params['log'])) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // json数据判断
        if (!CUtil::isJson($log)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 替换 uid
        $log = MallLogService::getInstance()->exchangeUid($log);

        list($s, $data) = MallLog::factory()->run('trace', $log);
        if (!$s) {
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, 'OK', $data);
    }
}
