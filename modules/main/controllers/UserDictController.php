<?php

namespace app\modules\main\controllers;

use app\exceptions\CommonException;
use app\modules\common\ControllerTrait;
use app\modules\main\services\UserDictService;

class UserDictController extends CommController
{

    use ControllerTrait;

    /**
     * @OA\Post(
     *     path="/main/user-dict/save",
     *     summary="用户字典-保存",
     *     description="保存用户自定义字典数据",
     *     tags={"用户字典"},
     *     security={
     *         {"user_id": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"dict_key"},
     *                         @OA\Property(property="dict_key", type="string", default="", description="字典标识"),
     *                         @OA\Property(property="content", type="string", default="", description="内容（json字符串）"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        try {
            $dict_key = $this->request->post("dict_key");
            $content = $this->request->post("content");

            if (empty($dict_key)) {
                throw new CommonException("参数错误");
            }

            $dictService = UserDictService::getInstance();
            $dictService->saveDictData($this->user_id, $dict_key, $content);

            $this->success();
        } catch (CommonException $e) {
            $this->error($e->getMessage());
        }

    }

    /**
     * @OA\Post(
     *     path="/main/user-dict/get",
     *     summary="用户字典-读取",
     *     description="读取用户自定义字典数据",
     *     tags={"用户字典"},
     *     security={
     *         {"user_id": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"dict_key"},
     *                         @OA\Property(property="dict_key", type="string", default="", description="字典标识"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据", @OA\Items(
     *                 @OA\Property(property="content", type="string", description="内容（json字符串）"),
     *                 @OA\Property(property="data", type="object", description="内容解析数据", @OA\Items()),
     *             )),
     *         )
     *     )
     * )
     */
    public function actionGet()
    {
        try {
            $dict_key = $this->request->post("dict_key");
            if (empty($dict_key)) {
                throw new CommonException("参数错误");
            }

            $dictService = UserDictService::getInstance();
            $content = $dictService->getDictContent($this->user_id, $dict_key);

            $this->success([
                "content" => $content,
                "data"    => json_decode($content, true) ?? null
            ]);
        } catch (CommonException $e) {
            $this->error($e->getMessage());
        }
    }
}