<?php


namespace app\modules\main\controllers;



use app\models\CUtil;
use app\modules\back\forms\CouponForm;
use app\modules\back\services\CouponService;

class CouponController extends CommController
{
    public function actionInfo()
    {
        $post = CUtil::VdForm(new CouponForm(), 'info');
        $order_no = $post->order_no;
        $result = CouponService::getInstance()->info($order_no, $this->user_id);
        CUtil::Ret($result);

    }

}