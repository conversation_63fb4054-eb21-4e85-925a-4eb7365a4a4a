<?php

namespace app\modules\main\controllers;


use app\modules\main\services\DreameStoreService;
use app\modules\common\ControllerTrait;

/**
 * 追觅小店 - 控制器 - APP端
 */
class DreameStoreController extends CommController
{
    use ControllerTrait;


    /** @var DreameStoreService */
    private $service;
    public function __construct($id, $module, $config = [])
    {
        $this->service = DreameStoreService::getInstance();
        parent::__construct($id, $module, $config);
    }

    public function actionInfo()
    {
        try {
            // 当前用户ID
            $user_id = $this->user_id;
            $store_user_id = \Yii::$app->request->post('store_id');
            if (! $store_user_id) {
                $store_user_id = $user_id;
            }
            // 调用服务
            $res = $this->service->info($user_id, $store_user_id);
//            if ($res) {
//                $this->error('信息不存在');
//            }
            $this->success($res);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 申请
     */
    public function actionApply()
    {
        try {
            // 用户ID
            $user_id = $this->user_id;
            $params = \Yii::$app->request->post();
            // 调用服务
            $res = $this->service->apply($user_id, $params);
            if (!$res) {
                $this->error('申请失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 修改
     */
    public function actionUpdate()
    {
        try {
            // 用户ID
            $user_id = $this->user_id;
            $params = \Yii::$app->request->post();
            // 调用服务
            $res = $this->service->update($user_id, $params);
            if (!$res) {
                $this->error('修改失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}