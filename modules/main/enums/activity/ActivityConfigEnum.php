<?php

namespace app\modules\main\enums\activity;

use app\models\by;
use app\models\CUtil;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\MemberActivityModuleGoodsModel;
use app\modules\main\models\MemberActivityModuleRelationModel;
use app\modules\main\services\ActivityConfigService;

class ActivityConfigEnum
{
    const MONTHLY_CARD_STATUS = [
        'no_activity' => 0,//没有活动
        'has_draw'    => 1,//已经领取
        'can_draw'    => 2,//未领取，可以领取
        'no_stock'    => 3,//领光了
        'no_draw'     => 4,//不可领取
    ];

    const POINTS_ACTIVITY_RESULT = [
            0 => '符合活动规则',
            1 => '参数错误',
            2 => '商品限购1件',
            3 => '您不符合参与条件',
            4 => '您有订单未确认收货',
            5 => '活动商品不符合参与条件',
            6 => '您已经购买过该商品',
            7 => '商品已被兑换光了',
    ];
    //以旧换新开始时间
    const YJHX_START_TIME = YII_ENV_PROD ? '2023-10-18 00:00:00':'2023-10-11 00:00:00';
    //以旧换新结束时间
    const YJHX_END_TIME = '2023-10-31 23:59:59';

    //打卡开始时间
    const CHECKIN_START_TIME = YII_ENV_PROD ? '2025-01-10 10:00:00' : '2024-12-18 00:00:00';
    //打卡结束时间
    const CHECKIN_END_TIME = '2025-01-23 23:59:59';
    //打卡连续天数
    const CHECK_EXTRA_REWARD_DAYS = YII_ENV_PROD ? 7 : 3;
    //连续打卡额外奖励分数
    const CHECK_EXTRA_REWARD_POINTS = 2025;
    //每天打开奖励分数
    const COMMON_REWORD_POINTS = 100;
    // 打卡指定某天奖励积分
    const CHECKIN_REWARD_POINTS      = [
            '2024-04-28'=>100,
    ];

    //抽奖开始时间
    const DRAW_PRIZE_START_TIME =   YII_ENV_PROD ? '2025-01-10 10:00:00' : '2024-12-18 00:00:00';
    //抽奖结束时间
    const DRAW_PRIZE_END_TIME =  '2025-01-23 23:59:59';

    const PURCHASE_COMMON_GOODS_START_TIME = YII_ENV_PROD ? '2024-05-23 00:00:00' : '2024-04-28 00:00:00';

    const PURCHASE_COMMON_GOODS_END_TIME = '2024-06-20 23:59:59';

    const FREE_SALE_POINTS_GOODS_START_TIME = YII_ENV_PROD ? '2024-10-21 00:00:00' : '2024-09-25 00:00:00';

    const FREE_SALE_POINTS_GOODS_END_TIME = '2024-11-20 23:59:59';

    const PURCHASE_COMMON_GOODS_PRICE = YII_ENV_PROD ? 100000 : 100;

    const POINT_DEDUCTION_START_TIME = YII_ENV_PROD ? '2024-09-18 00:00:00' : '2024-08-14 00:00:00'; // 积分抵扣活动开始时间
    const POINT_DEDUCTION_END_TIME   = '2024-10-07 23:59:59'; // 积分抵扣活动结束时间
    const POINT_DEDUCTION_MULTIPLE   = 10;  // 积分抵扣倍数

    // ** 是否限制积分商品售卖 活动期间必须修改此参数 **
    const IS_LIMIT_POINT_CONDITION = false;

    //积分翻倍倍数
    const POINT_DOUBLING_MULTIPLE = 2;
    //积分翻倍开始时间
    const POINT_DOUBLING_START_TIME = YII_ENV_PROD ? '2025-01-01 00:00:00' : '2024-12-28 00:00:00';
    // 积分翻倍结束时间
    const POINT_DOUBLING_END_TIME = '2025-01-31 23:59:59';

    public static function judgeActivityTime($user_id, $type = 'YJHX'): bool
    {
        $time = time();
        switch ($type) {
            case 'YJHX': //以旧换新
                $startTime = self::YJHX_START_TIME;
                $endTime   = self::YJHX_END_TIME;
                break;
            case 'DRAW': //抽奖
                $startTime = self::DRAW_PRIZE_START_TIME;
                $endTime   = self::DRAW_PRIZE_END_TIME;
                break;
            case 'FREE_SALE':
                $startTime = self::FREE_SALE_POINTS_GOODS_START_TIME;
                $endTime   = self::FREE_SALE_POINTS_GOODS_END_TIME;
                break;
            case 'PURCHASE_COMMON_GOODS':
                $startTime = self::PURCHASE_COMMON_GOODS_START_TIME;
                $endTime   = self::PURCHASE_COMMON_GOODS_END_TIME;
                break;
            case 'POINT_DEDUCTION':
                $startTime = self::POINT_DEDUCTION_START_TIME;
                $endTime   = self::POINT_DEDUCTION_END_TIME;
                break;
            case 'POINT_DOUBLING': //积分翻倍
                $startTime = self::POINT_DOUBLING_START_TIME;
                $endTime   = self::POINT_DOUBLING_END_TIME;
                break;
            default:
                return false;
        }

        $testUsers = CUtil::getConfig('testUsers', 'member', MAIN_MODULE) ?? [];
        if ($testUsers && in_array($user_id, $testUsers) && $time <= strtotime($endTime)) {
            return true;
        }


        if (strtotime($startTime) < $time && strtotime($endTime) > $time) {
            return true;
        }

        return false;
    }

    // 判断积分商品是否可售
    public static function judgeActivityFreeSaleTime($gid): bool
    {
        // 获取渠道商品
        if (!self::judgeActivityFreeSaleGoods($gid)) {
            return true;
        }

        // 获取活动时间
        $time = time();
        $startTime = self::FREE_SALE_POINTS_GOODS_START_TIME;
        $endTime   = self::FREE_SALE_POINTS_GOODS_END_TIME;
        if (strtotime($startTime) < $time && strtotime($endTime) > $time) {
            return true;
        }
        return false;
    }

    // 判断积分商品是否可售
    public static function judgeActivityFreeSaleGoods($gid): bool {
        $gids = self::getCheckinRewardGoods();
        // 判断是否限制积分商品售卖
        if ($gid && in_array($gid, $gids) && self::IS_LIMIT_POINT_CONDITION) {
            return true;
        }
        return false;
    }

    //获取活动商品集合 https://dreametech.feishu.cn/docx/EsAIdZi0AoBNHAxXXaxciAKNnv8
    public static function getCheckinRewardGoods(): array
    {
        $goods = self::getCheckinRewardGoodsData();
        return array_column($goods,'gid') ?? [];
    }

    public static function  getCheckinRewardGoodsData(): array
    {
        $points =  YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/pointGoodsProd20250329.json')
            : file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/pointGoodsTest20250329.json');
        $goods = json_decode($points, true) ?? null;
        return $goods ?? [];
    }

    // 获取活动积分数据
    public static function getCheckinRewardPointByDay($day): int
    {
        $dayPoints = self::CHECKIN_REWARD_POINTS;
        if ($day < '2024-05-23' || $day > '2024-06-20'){
            return self::COMMON_REWORD_POINTS;
        }else{
            return $dayPoints[$day] ?? self::COMMON_REWORD_POINTS;
        }
    }

    /**
     * @return mixed
     * 获取配件秒杀商品信息
     */
    public static function getSeckillGoods()
    {
        $secKills      = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/seckillGoodsProd20250329.json') : file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/seckillGoodsTest20250329.json');
        return json_decode($secKills, true);
    }

    /**
     * @return array
     * @throws \Exception
     * 获取隐藏商品ID集合
     */
    public static function getHiddenGoods(): array
    {
        if (date('Y-m-d H:i:s') >= '2025-05-21 00:00:00') {
            $gids1 = self::getHiddenGoods1();
            $gids2 = [];
        } else {
            $gids1 = self::getHiddenGoods1();
            $gids2 = self::getHiddenGoods2();
        }

        return array_unique(array_merge($gids1, $gids2));
    }

    /**
     * @return array
     * @throws \Exception
     * 获取隐藏商品ID集合
     */
    public static function getHiddenGoods1(): array
    {
        // 数据结构包含二维数组时
        $goodsIds = [];
        
        // 获取活动有效期内IDs
        // $activityIds = MemberActivityModel::getInstance()->getAvailableIds();
        // 获取活动关联商品模块
        $moduleRelationModel = MemberActivityModuleRelationModel::getInstance();
        // 获取机器推荐和配件专区活动模块数据、一元抢购
        $relationIds = $moduleRelationModel->getIdsByModuleResId([7, 9, 12]);
        // 获取机器推荐和配件专区的已隐藏活动商品
        $memberActivityModuleGoodsModel = MemberActivityModuleGoodsModel::getInstance();
        $res = $memberActivityModuleGoodsModel->getHiddenByRelationIds($relationIds);

        // 活动有效期内，不展示隐藏的商品
        foreach ($res as $value) {
            // 活动期间内，隐藏的商品添加到数组
            if (empty($value['status'])) {
                $goodsIds[] = $value['goods_id'] ?? 0;
            }
        }
        return array_unique($goodsIds);
    }

    /**
     * @return array
     * @throws \Exception
     * 获取隐藏商品ID集合
     */
    public static function getHiddenGoods2(): array
    {
        // 数据结构包含二维数组时
        $goodsIds = [];
        foreach (ActivityConfigEnum::getSeckillGoods() as $seckillGood) {
            foreach ($seckillGood['arr'] as $value) {
                $beginTime = $value['begin_time'] ?? 0;
                $endTime   = $value['end_time'] ?? 0;
                $now       = time();

                // 如果不在活动期间内，添加到数组
                if ($now < $beginTime || $now > $endTime) {
                    continue;
                }

                // 活动期间内，根据isShow决定是否添加
                if (!($value['isShow'] ?? false)) {
                    $goodsIds[] = $value['gid'] ?? 0; // 使用gid作为商品ID
                }
            }
        }
        return  $goodsIds;
    }

    /**
     * @return mixed
     * 获取活动主机商品信息
     */
    public static function getMainGoods()
    {
        $mains = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/2025NewYear/mainGoodsProd20250110.json') : file_get_contents(__DIR__ . '/../activityRecommendGoods/2025NewYear/mainGoodsTest20250110.json');
        return json_decode($mains, true);
    }

    /**
     * @return mixed
     * 获取活动配件商品信息
     */
    public static function getPartGoods()
    {
        // 确定 JSON 文件路径
        $filePath = __DIR__ . '/../activityRecommendGoods/2025NewYear/' .
            (YII_ENV_PROD ? 'partGoodsProd20250110.json' : 'partGoodsTest20250110.json');

        // 读取并解析 JSON 文件
        return json_decode(file_get_contents($filePath), true);
    }


    /**
     * @return mixed
     * 获取活动积分秒杀商品信息
     */
    public static function getSeckillPointGoods()
    {
        $secKillPoints = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/20240618/seckillPointGoodsProd20240428.json') : file_get_contents(__DIR__ . '/../activityRecommendGoods/20240618/seckillPointGoodsTest20240428.json');
        return json_decode($secKillPoints, true);
    }

    /**
     * @return mixed
     * 获取活动各品类（扫地机、洗地机...）配件信息商品信息
     */
    public static function getGoodsCategory()
    {
        $goodsCategory = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/2025NewYear/partCateProd20250110.json') : file_get_contents(__DIR__ . '/../activityRecommendGoods/2025NewYear/partCateTest20250110.json');
        return json_decode($goodsCategory, true);
    }

    public static function getStoreGoods()
    {
        $storeGood = YII_ENV_PROD ? file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/storeGoodsProd20250329.json') : file_get_contents(__DIR__ . '/../activityRecommendGoods/20250329MemberActivity/storeGoodsTest20250329.json');
        return json_decode($storeGood, true);
    }
}




