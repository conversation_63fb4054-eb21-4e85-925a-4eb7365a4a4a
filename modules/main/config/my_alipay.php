<?php
//支付宝小程序配置
use app\models\CUtil;

$config["alipay_applet"] = YII_ENV_PROD ? [
    "privateKey"           => "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCGrblCh886pPhCeovmB2QBOVosaDoKn8ZuZMyoXrBL85gB7vcjZLwm4PDdZUy4ExQ9K+anV4uBe1/oBTc1DHqkhNfSelxQNYEoFfhUGNS1oj0ArLPD/iVDTElGqcWiiGhBdA6eKXKgcSf4FPpYzDQrAM+SQ6J3k5VC8yoEM782KVSRq0jgSUQa8Ogsw2RYkIwmnUjD+euyaQnGpy5BIiHdnewce4e1mLwqObvaOx0lmoGvLQOzXv9jqDgdWPvQzrL0zM7HMyu/TyC4Q0uQuehKLR3uKq2XZaul+dAfhfjhZKOmfFJ46v9CL3wIrR8ohxnkdorl/iEQeRt8pvTVleFbAgMBAAECggEAQLSwoI1dfACMomv8UP1WB9yFPXaqBDa1gI5/YZl0vFkBvFQSevfICinir4we6wWHoMg53kaKaSBceL4gwyUGu4pzotVVCthaAGdl0wnHEKktA+j1yYGOZ6H8Rq4EnU1KQcA/0a96OimDmYaCrS0XF14hXFWQdkD64pqvois2gA9yoADF8jklCB8y6Kt/IM3AX6+NJwZghvvDpr6RL6GL9gQxc5blz8LIeUDEM9BxdY6KcAQKmaYEu4Bj6zJKVMgBADv8eieTuyOZmQhIaE4eeLXTrirl9QqWfqWIihGoSooYN+btIXKHfJKb5PhHYKfDvV+sW/vhsUplB72YwxzyoQKBgQDF6SvznqS1FSIq8qw/Zaxq+Cq1IpFh9+EXt4svaOmyqVWotttuveN3cQ2Hbmn+RUSHXZLUv9yO0WKh2Hwyhikb0CQd5FrCr9YkGZpUU1lYYVBgwHKPHN0NFj+tj4WbeYZxJK7h6xkFsT5jKOW2RwFwMSePrcAwGVU8aCXuLh3S8QKBgQCuNVgaxEvvl7qh8UvSi5zGvGl66u9ReuJmBqsU9zUUcuSMig35zooR7sxmuhtXGxHFvlNOZMz//pVNMmWRHECdBP3LJ3LfG239E/tWu+Od7X5/PeCsE77YKbk/TnYOhglY00qt3KFfSsrdUI/FXTUzq9EuyPwD1aJge0IeEmjhCwKBgAqHuIXy1/KmmDXOyalp4H2kjKksmK2swbZAVtCD9YNDDUK3z/8ZbWPqiPy+vDcCOn41VV2qg7K5o1TXLuyFROSs827/unCHaI5tKk/4S1Wvf9ZWBwJkV8Kdf2/dpg86rROSDxgIKUGrR5WBFI/ZQVNV2jQD26jzgByBshr1KzMRAoGBAI4PfChfuNjJ72zN+5nXNeKulImi+cOFJQg0GeE/8i4ZvxyIG/uBHQCgrlh7HX572ZNasE52TuHW9mN8XGzoEBy7jEbVhYxArwG5ALXrBCOuHwCeMg/BDljyNVlVn1oanZasvvJmv8WYlTPC1WsJpi/bxfqT9UcfOInbGcCyHIqfAoGANScWfYwqXM2EQU6zADZsMWiolUVH8K2gaE39GomnRj9F4w4TkfdxmWscqSjyFhb5eCeCgisUR68D/w7fDccH//nLd4e0PbvDUDb7mGlbEX4LKMTUiYVZWY7N4Q+68cwD1JdFoHPjh1SPuSVh48Wljy1dQ7lVL+Cj3bFKRroXbGs=",
    "alipayPublicKey"      => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB",
    "serverUrl"            => "https://openapi.alipay.com/gateway.do",
    "appId"                => "2021004140611078",
    "rsaPrivateKey"        => "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCGrblCh886pPhCeovmB2QBOVosaDoKn8ZuZMyoXrBL85gB7vcjZLwm4PDdZUy4ExQ9K+anV4uBe1/oBTc1DHqkhNfSelxQNYEoFfhUGNS1oj0ArLPD/iVDTElGqcWiiGhBdA6eKXKgcSf4FPpYzDQrAM+SQ6J3k5VC8yoEM782KVSRq0jgSUQa8Ogsw2RYkIwmnUjD+euyaQnGpy5BIiHdnewce4e1mLwqObvaOx0lmoGvLQOzXv9jqDgdWPvQzrL0zM7HMyu/TyC4Q0uQuehKLR3uKq2XZaul+dAfhfjhZKOmfFJ46v9CL3wIrR8ohxnkdorl/iEQeRt8pvTVleFbAgMBAAECggEAQLSwoI1dfACMomv8UP1WB9yFPXaqBDa1gI5/YZl0vFkBvFQSevfICinir4we6wWHoMg53kaKaSBceL4gwyUGu4pzotVVCthaAGdl0wnHEKktA+j1yYGOZ6H8Rq4EnU1KQcA/0a96OimDmYaCrS0XF14hXFWQdkD64pqvois2gA9yoADF8jklCB8y6Kt/IM3AX6+NJwZghvvDpr6RL6GL9gQxc5blz8LIeUDEM9BxdY6KcAQKmaYEu4Bj6zJKVMgBADv8eieTuyOZmQhIaE4eeLXTrirl9QqWfqWIihGoSooYN+btIXKHfJKb5PhHYKfDvV+sW/vhsUplB72YwxzyoQKBgQDF6SvznqS1FSIq8qw/Zaxq+Cq1IpFh9+EXt4svaOmyqVWotttuveN3cQ2Hbmn+RUSHXZLUv9yO0WKh2Hwyhikb0CQd5FrCr9YkGZpUU1lYYVBgwHKPHN0NFj+tj4WbeYZxJK7h6xkFsT5jKOW2RwFwMSePrcAwGVU8aCXuLh3S8QKBgQCuNVgaxEvvl7qh8UvSi5zGvGl66u9ReuJmBqsU9zUUcuSMig35zooR7sxmuhtXGxHFvlNOZMz//pVNMmWRHECdBP3LJ3LfG239E/tWu+Od7X5/PeCsE77YKbk/TnYOhglY00qt3KFfSsrdUI/FXTUzq9EuyPwD1aJge0IeEmjhCwKBgAqHuIXy1/KmmDXOyalp4H2kjKksmK2swbZAVtCD9YNDDUK3z/8ZbWPqiPy+vDcCOn41VV2qg7K5o1TXLuyFROSs827/unCHaI5tKk/4S1Wvf9ZWBwJkV8Kdf2/dpg86rROSDxgIKUGrR5WBFI/ZQVNV2jQD26jzgByBshr1KzMRAoGBAI4PfChfuNjJ72zN+5nXNeKulImi+cOFJQg0GeE/8i4ZvxyIG/uBHQCgrlh7HX572ZNasE52TuHW9mN8XGzoEBy7jEbVhYxArwG5ALXrBCOuHwCeMg/BDljyNVlVn1oanZasvvJmv8WYlTPC1WsJpi/bxfqT9UcfOInbGcCyHIqfAoGANScWfYwqXM2EQU6zADZsMWiolUVH8K2gaE39GomnRj9F4w4TkfdxmWscqSjyFhb5eCeCgisUR68D/w7fDccH//nLd4e0PbvDUDb7mGlbEX4LKMTUiYVZWY7N4Q+68cwD1JdFoHPjh1SPuSVh48Wljy1dQ7lVL+Cj3bFKRroXbGs=",
    "alipayrsaPublicKey"   => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB",
    "alipayPublicCertPath" => "",
    "notifyUrl"            => (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/zhima/notify',
    "apiSign" => "dreame@2024",
    "apiAesKey" => "fSYVDI6xnEONuRtq26QB0w==",
] : [
    "privateKey"           => "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCuHiN3uKycOjlhOPpIPGSrJC74U3HENTpuvpNrHcvcoZe2OuHBGRESppNkAGamVxI6rQfn8JS33iNuLM5GH+xqL1lsm+RDkRA/RqSP87LyzTX3zAML9bjk8BcrXgf33WrBo6IRD8e7qlPHtQiZgLJxWQhp1yobg5oSgS7dlkz0Svk3bsvNkBvKbcbBWiWc2Y7XAUE7+/FiXbZgD0mgqmQ+vziRTZu8dGsbPPdjUURU4lGvsjNyREH1N2bRVPOoCIXk/G89V+KNgHJwbrehJqaCsXxhYHEQQexraDZp/BHMFZeGxWzN9dP+jE74l8feUWbBCm06iHw8SkeioPwG6nOBAgMBAAECggEAMlytTZew9Y8xwZlFa1dslsPkghdAtMvglt+wrC72JF7pDuCBI+UoNwjPYQFsr3hH4Yji3IeNvJEYFmoHT+kz7JK9ftiocZKn7GV2e9y1Sd1pWr87sl3kmPOJABY2n87/bfryiZTS5MkjaoRGdH/xHS/vaw29NDAghHGxKI6su4vp3kRRaDcqY8UjD8lsuTM792U/+H813j0mFs5o78cEb4e9ap6B6S2ueJFMF3C65IXj+wAHCGH+WGE15PWIoEvLKx4qliooNRH9iruisFn0JGie6Nq2ymL1/VRZkv3NPDT/QY8k5kOWeikuL1+mdJtsNb1ZAyCZcED09uH6zruXMQKBgQDU58qr9wVnFB2dUYIr8laQ5p/2RnjZ35DnpPDrijEZtVE15jKLLD5NClbIVIrjyIHDsXll/a+ljs4US0pCQEfwcDzGoQX5DD/1UITYCZtRCdSi1kLETI3J1YIyFB1Ryq6IfnIxIHAOYi1ev17zrHSzHehGgSfdjlXa03uP+4QA9wKBgQDRXHfi81Of3/gEa7tIqWC6RUhl8gMIa3oLcyhPhuPC6aL9uIDwXyq5V7joAvzMMWWuA8krjWpwSa3J7qHD2viOO2pkbuEWMJIT1Io9NWGti+vgYk8bmSqYc/FEkuvEB6ODoHdyfSsQBstn0OKI1Win9Ai5iysvw+iUB5x1fQaJRwKBgDWrrQrGvcTrbD/BUWhUsf/bM4342/RIiJXsnZMzBgeuDqfgvPMalyYCqjfcIBsl65hyf+D3H7VDl63EARdMbxYspRWJeCD9No5X2vU/DrlSGhlmm/QyPDgaK7UGHxIEXxUnkw/RVo45r6hH497XpZ+VtX04h90xSXFdiLOWOwOnAoGAINsfIWnIbOWsOZ1ileOm/3SSCRWy7iZXz+YxpFFp+xPJdYH4EkENJmsi5J5bZ/w0lduYPZKC6r5VXMRrdJ4vtbqTVtGj12Sl+CC51SNEggTcbqle+tjNu4EukvFvuKLuQWD0fe4wzOIUF+XLN2gudXUxzVSO4X8gR+Lt5e5KEU8CgYEAhFgIPdVKQcXvXYOq8Z1r6Dbczj+KTu2HcE0ZKAoCA1IgFqLNDg3aZWKpJXxpXpMomVBlZnJ/oRmKBgA4d36rWGrH4yC4UFY9io2ioJKKDwSLSIVDuGlggIevhIMm0ZYpPxTtMOFKZ960YyW98QcjPP+6FWlZe6GCEDM3mIU349g=",
    "alipayPublicKey"      => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB",
    "serverUrl"            => "https://openapi.alipay.com/gateway.do",
    "appId"                => "2021004142620001",
    "rsaPrivateKey"        => "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCuHiN3uKycOjlhOPpIPGSrJC74U3HENTpuvpNrHcvcoZe2OuHBGRESppNkAGamVxI6rQfn8JS33iNuLM5GH+xqL1lsm+RDkRA/RqSP87LyzTX3zAML9bjk8BcrXgf33WrBo6IRD8e7qlPHtQiZgLJxWQhp1yobg5oSgS7dlkz0Svk3bsvNkBvKbcbBWiWc2Y7XAUE7+/FiXbZgD0mgqmQ+vziRTZu8dGsbPPdjUURU4lGvsjNyREH1N2bRVPOoCIXk/G89V+KNgHJwbrehJqaCsXxhYHEQQexraDZp/BHMFZeGxWzN9dP+jE74l8feUWbBCm06iHw8SkeioPwG6nOBAgMBAAECggEAMlytTZew9Y8xwZlFa1dslsPkghdAtMvglt+wrC72JF7pDuCBI+UoNwjPYQFsr3hH4Yji3IeNvJEYFmoHT+kz7JK9ftiocZKn7GV2e9y1Sd1pWr87sl3kmPOJABY2n87/bfryiZTS5MkjaoRGdH/xHS/vaw29NDAghHGxKI6su4vp3kRRaDcqY8UjD8lsuTM792U/+H813j0mFs5o78cEb4e9ap6B6S2ueJFMF3C65IXj+wAHCGH+WGE15PWIoEvLKx4qliooNRH9iruisFn0JGie6Nq2ymL1/VRZkv3NPDT/QY8k5kOWeikuL1+mdJtsNb1ZAyCZcED09uH6zruXMQKBgQDU58qr9wVnFB2dUYIr8laQ5p/2RnjZ35DnpPDrijEZtVE15jKLLD5NClbIVIrjyIHDsXll/a+ljs4US0pCQEfwcDzGoQX5DD/1UITYCZtRCdSi1kLETI3J1YIyFB1Ryq6IfnIxIHAOYi1ev17zrHSzHehGgSfdjlXa03uP+4QA9wKBgQDRXHfi81Of3/gEa7tIqWC6RUhl8gMIa3oLcyhPhuPC6aL9uIDwXyq5V7joAvzMMWWuA8krjWpwSa3J7qHD2viOO2pkbuEWMJIT1Io9NWGti+vgYk8bmSqYc/FEkuvEB6ODoHdyfSsQBstn0OKI1Win9Ai5iysvw+iUB5x1fQaJRwKBgDWrrQrGvcTrbD/BUWhUsf/bM4342/RIiJXsnZMzBgeuDqfgvPMalyYCqjfcIBsl65hyf+D3H7VDl63EARdMbxYspRWJeCD9No5X2vU/DrlSGhlmm/QyPDgaK7UGHxIEXxUnkw/RVo45r6hH497XpZ+VtX04h90xSXFdiLOWOwOnAoGAINsfIWnIbOWsOZ1ileOm/3SSCRWy7iZXz+YxpFFp+xPJdYH4EkENJmsi5J5bZ/w0lduYPZKC6r5VXMRrdJ4vtbqTVtGj12Sl+CC51SNEggTcbqle+tjNu4EukvFvuKLuQWD0fe4wzOIUF+XLN2gudXUxzVSO4X8gR+Lt5e5KEU8CgYEAhFgIPdVKQcXvXYOq8Z1r6Dbczj+KTu2HcE0ZKAoCA1IgFqLNDg3aZWKpJXxpXpMomVBlZnJ/oRmKBgA4d36rWGrH4yC4UFY9io2ioJKKDwSLSIVDuGlggIevhIMm0ZYpPxTtMOFKZ960YyW98QcjPP+6FWlZe6GCEDM3mIU349g=",
    "alipayrsaPublicKey"   => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB",
    "alipayPublicCertPath" => "",
    "notifyUrl"            => (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/zhima/notify',
    "apiSign" => "dreame@2024",
    "apiAesKey" => "8yLibPWs1rIE5pizwV5xkg==",
];
