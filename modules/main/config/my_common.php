<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/15
 * Time: 10:12
 */

use app\models\CUtil;

//app签名 公钥私钥 所有渠道统一放置此处
$config['apiKeys'] = [
        'a_1525857942' => '*&^%$&#@$%#%',   //全平臺
        'p_1643028263' => '*&^%$!@!!&!*',   //后台apikey
        'a_1643178000' => '*&^@!!#$&#@%',   //小程序安卓机
        'i_1643178026' => '*&^$%#%!#$&@',   //小程序IOS
        'p_1643028439' => '*&^$dem%$&#@',   //正式服udp
        'p_1643028386' => '*&^21%#22#@%',   //开发服udp
        'p_1619681152' => '*&^%$!@#^sa&!*', //IMudp 验证码
        'm_1622804151' => '!&A&^&!!!@@',    //消息服务
        'p_1644549157' => '6$#!5d42ess19',  //消息服务
        'a_1664246268' => 'b_m3I6PiPgYX#',  //app安卓机
        'i_1666147923' => 'b_m3h^jWfA9jp',  //app IOS
        'a_1643028263' => 'd_mX1JReO0sv24', //design 安卓机
        'i_1643028386' => 'd_YBeroJFtHFFg', //design IOS
        'p_1712037068' => 'wtAsLRVnE^#*',   //pc 端
        'a_1712037350' => 'a_D4j!3*hgu#',   //支付宝小程序 安卓机
        'i_1712037455' => 'i_67c^$dEWpx',   //支付宝小程序 IOS
        'x_1698595200' => 'x_n5h&jwfB9jp',  //xxl-job
        'p_1744612441' => 'paU5sP4x!n_0',   //移动端H5
];

$config['platformSource'] = [
        'a_1643178000' => 2, //小程序安卓机
        'i_1643178026' => 3, //小程序IOS
        'a_1664246268' => 5, //app安卓机
        'i_1666147923' => 6, //app IOS
        'a_1525857942' => 7, //全平臺
        'p_1712037068' => 8, //pc 端
        'a_1712037350' => 9, //支付宝小程序 安卓机
        'i_1712037455' => 10,//支付宝小程序 IOS
        'p_1744612441' => 11,//移动端H5
];

//登陆方式 key为唯一的user_type
$config['loginControl'] = [
        1  => 'weixin',      // 微信
        10 => 'wework',      // 企业微信登录
        99 => 'visitor',     // 游客-测试用
        20 => 'alipay',      // 支付宝
        30 => 'weixin_scan', // 微信扫码登录
];

//搜索类型 key为唯一的search_type
$config['searchControl'] = [
        1 => 'goods',  // 商品
        2 => 'wares',  // 积分商品
];

$config['sort'] = [
        'default'    => 0, //默认排序
        'price_asc'  => 1, //价格升序
        'price_desc' => 2, //价格降序
];

//无需登陆验证uri
$config['noCheckUri'] = [
        'main/login/index',
        'main/login/app-login',
        'main/goods/list',
        'main/goods/internal-list',
        'main/home/<USER>',
        'main/goods/tag-list',
        'main/goods/tags',
        'main/goods/internal-tag-list',
        'main/search/index',
        'main/goods/recommend',
        'main/goods/tied-sale',                     //搭售购买
        'main/goods/specs-list',                    //商品多规格信息
        'main/activity/old-to-new',
        'main/activity/recommend-goods',            //活动推荐商品
        'main/activity/rules',                      //活动规则
        'main/activity/market-define-list',         //内购优惠券中心
        'main/spread/get-spread-data',              //获取推广参数
        'main/live/get-live-list',                  //获取推广参数
        'main/activity/activity-detail',            //推广优惠券活动
        'main/activity/activity-check-detail',      //打卡活动
        'main/warranty/rules',                      //推广优惠券活动
        'main/wares/points-enum',                   //积分商品枚举内容
        'main/goods/part-cate',                     //配件商品
        'main/draw/activity-list',                  //抽奖活动-活动详情
        'main/goods/info',
        'main/plumbing-activity/service-order-save',//活动上下水 mova 用等
        'main/plumbing-activity/send-code',         //默认发送短信
        'main/plumbing/sn-config',                  //SN配置校验
        'main/plumbing/search-save',                //查询SN数据
        'main/goods-recommend/default-goods',       //默认推荐商品
        'main/goods-recommend/goods-tag',           //商品标签
        'main/goods-recommend/list',                //类目商品推荐
        'main/member-center/benefit-info',          //默认展示铜牌信息
        'main/login/code',                          //获取扫码登录参数
        'main/login/retrieve-user-info',            //码登陆获取用户详情
        'main/goods-param/index',                   //商品参数
        'main/goods-param/compare',                 //商品参数对比（商详）详情
        'main/goods-param/compare-detail',          //商品参数对比（对比页）详情
        'main/goods-param/cate-list',               //商品参数对比-分类列表
        'main/goods-param/goods-list',              //商品参数对比-商品列表
        'main/goods/part',                          //商品类目-列表
        'main/goods/specs-info',                    //商品信息-规格查询
        'main/goods/choose',                        //商品类目-筛选数据
        'main/wares-activity/activity-list',        //活动列表
        'main/goods/sale-status',
        'main/goods-review/list',                       //商品评价列表
        'main/goods-review/detail',                     //商品评价详情
        'main/wiki/wlist',                              //探秘列表
        'main/wiki/winfo',                              //探秘详情
        'main/group-purchase/get-popup',                //获取按钮状态
        'main/group-purchase/activity-detail',          //获取拼团活动详情
    'main/group-purchase/tags',                    // 获取拼团商品标签
        'main/product-matrix/category-list',            //产品站商品品类列表
        'main/product-matrix/detail',                   //产品站信息详情
        'main/data/wx-ulink',                           //获取微信短链
        'main/product-matrix/intro-detail',             //产品站介绍页信息详情
        'main/live/get-live-list',                      //获取直播列表
        'main/retailers/near-list',                     //附近门店列表
        'main/member-activity/info',                    //获取活动详情
        'main/group-purchase/goods-list',               //获取拼团商品列表
        'main/group-purchase/in-progress-list',         //获取进行中的拼团列表
        'main/group-purchase/real-time-leaderboard',    // 获取实时榜单
        'main/member-center/no-auth-task-list',         //获取任务列表
        'main/dreame-store/info',                       //获取追觅小店店铺详情
        'main/goods-store/store-goods',                 //获取店铺商品列表
        'main/goods-store/config',                      //追觅小店开店选品显示的推广信息
        'main/goods-store/tag-list',                    //获取店铺商品的标签
        'main/invite/consume-money-roll',               //获取赚钱花飘屏滚动
        'main/wares/list',                              //积分商品列表
        'main/invite/consume-money-detail-roll',        //获取赚钱详情滚动飘屏
        'main/subsidy-activity/current',                //获取当前生效的国补活动
        'main/draw/draw-record-roll',                //获取盲盒中奖飘屏滚动

];


$config['touristAllow'] = [
        'main/goods-recommend/list',
        'main/goods-recommend/goods-tag',
        'main/activity/list',
        'main/wiki/tlist',
        'main/goods-param/cate-list',
        'main/my/info',
        'main/retailers/near-list',
        'main/goods-param/goods-list',
        'main/goods-param/compare-detail',
        'main/data/phone-auth',
        'main/goods/calculate-price',
        'main/login/get-login-type',
        'main/login/get-login-type',
        'main/trade-in/activity',
        'main/group-purchase/get-id',
        'main/group-purchase/goods-list',
        'main/group-purchase/in-progress-list'
];


//服务器端口
$config['ports'] = [
        'test' => '80',
        'prod' => '9710',
];

//腾讯云连接配置
$config['TxYunOss'] = [
        'AccessKeyId'     => '',             //密钥id
        'AccessKeySecret' => '',             //密钥
        'bucket'          => '',             //桶名称
        'cdnAddr'         => '',             //cdn加速域名
        'imgBucket'       => 'images',       //图片Bucket子目录
        'fileBucket'      => 'files',        //文件Bucket子目录
        'region'          => 'ap-guangzhou', //设置一个默认的存储桶地域
        'schema'          => 'https',        //协议头部 http https
];

//主机名=> 内网IP
$config['hostname'] = YII_ENV_PROD ? [
        'wmpap01' => '**************',  //正式1
] : [
        'iZbp1ireocaix2b7p5tu54Z' => '************',    //test
];

//主机编号
$config['hostid'] = YII_ENV_PROD ? [
        'wmpap01' => 1, //正式1
] : [
        'iZbp1ireocaix2b7p5tu54Z' => 1, //test
];

//微信公众号
$config['wxoa'] = [
        'appId'     => 'wx75158ebbc86abc96',
        'appSecret' => 'b46a3bc13c91cbe45ac8b9dc37e08550',
];

$config['wxoanew'] = [
        'appId'     => 'wx21a67766933cf792',
        'appSecret' => 'd9222caa416bfcf4a18c35ccf3326c35',
];


//微信SDK
$config['weixin'] = YII_ENV_PROD ? [
        'appId'     => 'wx7042d29dafd01227',
        'appSecret' => '863b8b8a97608ba545a79f0a083d4627',

] : [
        'appId'     => 'wx8760891577e5b1ab',
        'appSecret' => 'e42d0dc3c01940dfabb5163124c50f01',

];


// 旧的 （IT bucket）
//$config['AliYunOss'] = [
//        'AccessKeyId'     => 'LTAI5tAPdjiBKkEputFy8M8e',
//        'AccessKeySecret' => '******************************',
//        'endpoint'        => 'http://oss-cn-beijing.aliyuncs.com',
//        'bucket'          => 'wmp-dreame',
//        'cdnAddr'         => 'https://wpm-cdn.dreame.tech', //cdn加速域名
//        'imgBucket'       => 'images',         //图片Bucket子目录
//        'fileBucket'      => 'files',          //文件Bucket子目录
//];


//阿里云OSS
$config['AliYunOss'] = YII_ENV_PROD ?
        [
                'AccessKeyId'     => 'LTAI5tK6JAoYGWC5Tezk16TK',
                'AccessKeySecret' => '******************************',
                'endpoint'        => 'https://oss-cn-shanghai.aliyuncs.com',
                'bucket'          => 'dreame-store-cn',
                'cdnAddr'         => 'https://cdn-cn-oss-dreame-store.dreame.tech',          //cdn加速域名
                'imgBucket'       => PRO_MALL_NAME . '/' . YII_ENV . '/' . 'images',         //图片Bucket子目录
                'fileBucket'      => PRO_MALL_NAME . '/' . YII_ENV . '/' . 'files',          //文件Bucket子目录
        ]
        :
        [
                'AccessKeyId'     => 'LTAI5tK6JAoYGWC5Tezk16TK',
                'AccessKeySecret' => '******************************',
                'endpoint'        => 'http://oss-cn-shanghai.aliyuncs.com',
                'bucket'          => 'dreame-store-cn',
                'cdnAddr'         => 'https://cdn-cn-oss-dreame-store.dreame.tech',          //cdn加速域名
                'imgBucket'       => PRO_MALL_NAME . '/' . 'prod' . '/' . 'images',         //图片Bucket子目录
                'fileBucket'      => PRO_MALL_NAME . '/' . 'prod' . '/' . 'files',          //文件Bucket子目录
        ];



//阿里云短信
$config['aliyunsms'] = [
    'SignName'        => '追觅科技',
    'Product'         => 'Dysmsapi',
    'Domain'          => 'dysmsapi.aliyuncs.com',
    'AccessKeyId'     => 'LTAI5t9fehLSU5kkFvCZzEzn',
    'AccessKeySecret' => '******************************',
    'Region'          => 'cn-hangzhou',
    'EndPointName'    => 'cn-hangzhou',
];
//废弃2
//$config['aliyunsms'] = [
//    'SignName'        => 'Dreamehome',
//    'Product'         => 'Dysmsapi',
//    'Domain'          => 'dysmsapi.aliyuncs.com',
//    'AccessKeyId'     => 'LTAI5tKZceoRY6FSBwohFGSc',
//    'AccessKeySecret' => '******************************',
//    'Region'          => 'cn-hangzhou',
//    'EndPointName'    => 'cn-hangzhou',
//];

// 废弃
//$config['aliyunsms'] = [
//    'SignName'        => '追觅科技',//签名名称 https://dysms.console.aliyun.com/dysms.htm#/develop/sign
//    'Product'         => 'Dysmsapi',//产品名称:云通信短信服务API产品,开发者无需替换
//    'Domain'          => 'dysmsapi.aliyuncs.com',//产品域名,开发者无需替换
//    'AccessKeyId'     => 'LTAI5tAPdjiBKkEputFy8M8e',
//    'AccessKeySecret' => '******************************',
//    'Region'          => 'cn-hangzhou',
//    'EndPointName'    => 'cn-hangzhou',
//];

//阿里云OSS
$config['AliYunSls'] =
        YII_ENV_PROD ? [
                'AccessKeyId'     => 'LTAI5tJ96ATTzA72Tyjt1D4G',
                'AccessKeySecret' => '******************************',
                'endpoint'        => 'mall-test.cn-shanghai.log.aliyuncs.com',
                'project'         => 'dreame-mall',
                'logstore'        => 'mall-api-prod',
        ] :
                (
                YII_ENV_UAT ?
                        [
                                'AccessKeyId'     => 'LTAI5tJ96ATTzA72Tyjt1D4G',
                                'AccessKeySecret' => '******************************',
                                'endpoint'        => 'mall-test.cn-shanghai.log.aliyuncs.com',
                                'project'         => 'dreame-mall',
                                'logstore'        => 'mall-api-uat',
                        ] :
                        [
                                'AccessKeyId'     => 'LTAI5tJ96ATTzA72Tyjt1D4G',
                                'AccessKeySecret' => '******************************',
                                'endpoint'        => 'mall-test.cn-shanghai.log.aliyuncs.com',
                                'project'         => 'dreame-mall',
                                'logstore'        => 'mall-test',
                        ]);


//企业微信小程序
$config['wework_app'] = YII_ENV_PROD ? [
        'AgentId' => '1000015',
        'secret'  => 'HqMCTQF0Dfj2Am_e5tDHHYSoXMk0lWP8spmAAVUzB-c',
] :
        [
                'AgentId' => '1000013',
                'secret'  => 'NdDz49DPAeM_WYgiF2JWBEoVHJH1jY6Nt3XLp37lr5E',
        ];

//企业微信开发
$config['wework'] = YII_ENV_PROD ? [
        'appId'          => 'wx092e921667714819',
        'appSecret'      => '7e2f06f0b452f6c57e5bd0cbb223f429',
        'corpId'         => 'wwa21f0e4d0ae42f0d',
        'secret'         => 'ti3FnQsVZQj_FlXMYao6aARXH9tDbA4MrjnG7A2vm6w',
        'token'          => 'dacjRboiuzP23',
        'encodingAESKey' => 'uPCuYIvsneUe95DixMNQLtuCiJo1OXzJMAlI3kqlduz',
] : [
        'appId'          => 'wxec95af20d6b732e1',
        'appSecret'      => '76f2249ad0087255351e3fa4b5449a61',
//    'corpId'         => 'ww47612045d11332aa',
//    'secret'         => 'tL28hMTW3-VG7hTIr7azD1X4J7ap5ZScbEbtDMg1Uzg',
        'corpId'         => 'wwa21f0e4d0ae42f0d',
        'secret'         => 'ti3FnQsVZQj_FlXMYao6aARXH9tDbA4MrjnG7A2vm6w',
        'token'          => 'dacjRboiuzP23',
        'encodingAESKey' => 'uPCuYIvsneUe95DixMNQLtuCiJo1OXzJMAlI3kqlduz',
];

//企业微信开发
$config['new_wework'] = YII_ENV_PROD ? [
        'corpId'         => 'ww47612045d11332aa',
        'agentId'        => '10004',
    // 'secret'         => 'tL28hMTW3-VG7hTIr7azD1X4J7ap5ZScbEbtDMg1Uzg',//外部联系人
        'secret'         => 'iSYxB2TF1W1m7_r4EgMLvtZ4zH2U2iGoUhBQUsQF8do',//自建应用
        'token'          => 'dacjRboiuzP23',
        'encodingAESKey' => 'uPCuYIvsneUe95DixMNQLtuCiJo1OXzJMAlI3kqlduz',
] : [
        'corpId'         => 'ww47612045d11332aa111',
        'agentId'        => '10004111',
    // 'secret'         => 'tL28hMTW3-VG7hTIr7azD1X4J7ap5ZScbEbtDMg1Uzg',//外部联系人
        'secret'         => 'iSYxB2TF1W1m7_r4EgMLvtZ4zH2U2iGoUhBQUsQF8do111',//自建应用
        'token'          => 'dacjRboiuzP23111',
        'encodingAESKey' => 'uPCuYIvsneUe95DixMNQLtuCiJo1OXzJMAlI3kqlduz111',
];


//日志控制
$config['sendLog'] = [
        'isLock' => 1
];

//前端版本与主副库控制
$config['wx_version_ctrl'] = YII_ENV_PROD ? [
        'wx_version'          => 'v1.0.0',
        'wx_sync_lock'        => 1,
        'wx_sync_center_lock' => 0
] : [
        'wx_version'          => 'v1.0.0',
        'wx_sync_lock'        => 1,
        'wx_sync_center_lock' => 0
];


//环境设置
$config['wxPlatformHost'] = CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host'];


// ALIPAY 支付宝账号信息
$config['ALIPAY'] = YII_ENV_PROD ? [ // 追觅APP收款
        'protocol'           => 'https',
        'gatewayHost'        => 'openapi.alipay.com',
        'signType'           => 'RSA2',
        'appId'              => '2021004144629414',
        'merchantPrivateKey' => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCQBGG6gvpvZVlgH5bBMETrqU6JurCO3K0AESh7HEnllHV3/TS82n+jDT4sqJH6i4iWkeOfQr/ENL5uoOu7eTmNuRcHitN3uWZUqnJE2Kbj7WCFWu2srubCubMBX49RwE5DN+0DIokHndfifxUKX7RQtnNCo+Z3CwX4GbsYlnzoIYhPtmrhUbjMOLqiqVUVj1lA8Zx2+3tiNEPIqp+ibVxfasldCktC5Jmzoqssx73NtxKqfU+VkX3zVpcUytklvczw3L1kAhznj8PbhAvg9CkQV7LyRqXkLxmpGNwTAM79cFFNYcXzGOiIYzqASaZky4kbLaWLb4+XBY3zjWguyRgPAgMBAAECggEAP/pSWAL7siT58WPXGveQhohuJW1UJgMOr6rvqtFEWsL69FT96q++Z/boGfrA7uaxxQIGdqe2fTlZJ9hBIMWXtcf13RnlG5i3RwF5RfBhI3w+lr9wOnhwdyz7wq5rAsxFeEMN/YiIC2QplasS88rUVc7XoVEiftvcyPzVSvT/hDdE4eVcp0mBg8i9g3h21GVsHca1497VjHe8ELoJrXElneeTEaEDIc/hjtkxTNQuTXfbfPe2ZAix2zYtFLORIFmBXwofu6Z8VOAD++5P0SDAObXZvCATlC/1tGnQGyM0Zs+FG3n+FavchhZdwlqT57/Aiatd2zkIVSMW5Go6V4APQQKBgQDeTfdhqS6bdiXBoq/bNj6HcLXbrGikBOsIvRTXLRn9EQOOEQkyefcwtZsVPzn+yNMNAl00lQWlQZpdQj+Q1zgZp7szfR3EsHJqhmU/W9EFqmWzTK98uY7H9+kugsanusASTzgsYhvome1Anf5FdmIozusP+zKi8NTTPHz6HaMB8QKBgQCl2KVAAv2htm0AdFvwCv9JQ801zcAT4lSsqiPSaqTJTkCIpbXAY+L8tWkHUpKOBwjowXBSMHntYFRFr2/Hhl6269LsGrdTEXW6lXKLvO7KUiSLuEk9Wlvbi7tygEhd73RUxe4lM9Xi/D2PunEHL9WvojQz+SqEB0C2/cAOGjO5/wKBgQCkCTX8u2En1+N+YbSP9hPnzxRyV2MTfImN9i/1o1Mk+LP91bZKp9Lt/2PDJtd9mOSbGd562WfEDIeCP0s2boj37VvmX3Fi3KxgzHDTcsXm/1hyGKj5SNcB2X4YEunRhK0MFaamoXJkjYiTAMJKsVM3N5AeR8aSk1EH2f+Cc/H/kQKBgHjcORE9HLkMyMLcCWQVn5U05AehuajD+BEcrJ5bOVu6uyzaFmnuYSzox3Geq071sXht//zldvWWjdN61msBcWS81ylZDuBqiPFAmAld1md7FUb9NeKQRwDoA+Kduh86sfxVggd6jxSr3fEwXc5u/brQ6lo16tXssyyoH54nWirhAoGAK+GBGPh9vvO7j+DjFOVJ2B8shJIpV2Cb2+5lxynxDZbs4ooqi9G0nCUPYCEFxU7k3JALPk2KykO0NogCo6S98NZVdukstwDLPZYOq1iEmH7hA+A49CgwAGbFYQ0vSl8T+CBqFe1gB/zxvqhapByMnovOnRr/rdBC7V174NwW1pA=',
        'alipayPublicKey'    => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB',
        'notifyUrl'          => (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/alipay-notify',
] : [ // 追觅APP收款测试用
        'protocol'           => 'https',
        'gatewayHost'        => 'openapi.alipay.com',
        'signType'           => 'RSA2',
        'appId'              => '2021004144618364',
        'merchantPrivateKey' => 'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCT6waTl/lzROBtG19HA4P3rOQye/7P06n1k7IhI9ZRSPd2AyO1nPJ6CYhzjbd/aNuGHOo/5JUSWApKqg0XeklfSrZPW39nPplrBg6/ZGToyXNL/XfQt7tEim6v5YHZhXPocWIv6PmKxwrR5rUbxCqLwmB69S//elqCeounhFP1gP6guctVaCWnqF/MkPuySaPMoxRqImTi6jdap3EUKjgLR0vYbCBVvjagYvYRzXB6gt7DTSeAL6d6p6MSK44z8ZEwrCooxkoPovOqFGrDA/UadbxOWsFlKI3ViE7+9Qi1DymuqNSncPIhnV9nXw6Ropjx3X5JtrFv8jee4TPjzGtzAgMBAAECggEBAIjetq0nuPjCSPBsnZL2W8x4PIfCWJwx/VhxnCgyIwQAAos+iHJqkav+2jYiciMBAfBtHQyWFKJuuMcx5v9Mdkwos91mitMcB5YSet76pK0KixdLqOyGtk/L0HQOaELAy+nP0M0IRK/3Ubm8QuU4dS3xZJkh+5How4tsnYEaXx4RIDsrfDAHEe+1ZrMbAh4pH9Ij975wop8EQzqCs3lVh89Lk//XLnRsjY2rLW/C465pauYUh9CmIuJ3pAy2dOiGRh73tCJIk3OYdcxc6OGDe1CxKSvPJnLzw0N79bHfZ9SHtoyd/KdF4z47X4Aro65F8RSitbjDLu3E+KOSEVRaLNECgYEAyQH4TlKALWs95pnAPCK2tg7ILfygK80bReg0g89qjfqDNU64gzGPjIElSgxSzm8Kv8rtd5NWSi9oRqdzMc98KsPf9cBZYyxbnohSw0OO8dXW3KADjYTLLVEB0o1UazKElE0w2QQeMp3l/CDxKO7ju1S3VTXOqC1ASJ+IRk3JXP8CgYEAvGLNpiF7OVypmZeVcq602krE/91im6IeZi03eKmHSR6bS2KSYYbrONVAepNE8Bps8gNtlbaRvG1SOpnn6deZYaQZmv2Gkdlovni7sly6wb/riWXhqqkUQoGKMAbrksvJRtE58EBizfOXhEFgJN2cmSoBIMN90xpnz5IB8/eUzY0CgYAMoiIQEiFaOW5HiwxI+0mOgblVIpaSii6XozOBwUyKJd+PPyZboIbMJuXIFKQzs0jyqvdn14vJ19bu8eFMlSTRr0PpNzCnCgesHBdPjHR0gKMjdK8ket8mNvwtVpKiticdBwj16stXFRN6WDuv/eu0P2vSYDJf4ZAzwEsZWBQ60wKBgC3I9aJC0OgIFVA2/yN4QowrT+W/sI+Sv0CmDK1OxoqG3cdclacOW2zcp2cjE8YnJgNLwf54+MKK70k+zpaOx8BrWa3JoEMiS8q/HR8kWl9sfZpPIKGRplLjmOpDRE3RKYIEp8g0Xc8gPNrv6KYNFlpDUzLbUGFWMZTJL6BTs96hAoGAcH9dnQrrf40uDaqwM4SmK7wvNNd0FibytefJBiCcL1b7QTLBNsaZcJZLHfqwe1iR79hMbzc62m022U0XhLEYNSk5DV3FU+Rp/4PJyOJV4M4XE7RPZp4832ZmhJY1OEIpkY3YkHD7DgWhYvjgEDccizY9qviNYKbIOulvG6hWKt4=',
        'alipayPublicKey'    => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAstbD6WxIlUUbVkIx6Z8/XaNf9c//sVXgQ/8VF/nAPUJtqB95Q+2/MEqkvB1i8RQmpfvdfD8MEu3HCRcRptwuTjgd0Xb0nJL3KkUH/FKLbYnQVe8+z92EN/2eCEtG0gAiXa1Gb3romKZL+CzGt/zTq0D/fusZ/obqbsUj0kLMtUNO9ZbgeA96j78Yo8n9mRB8a0fPdo7/XYZAIMiveSYEwo9GqlFty7nyiS6B/7QkG/tP1Qm3rFw8DqHYXmXgs51pS48Ly7hCnAh7YvdIMEWBZYX9ajXa/TEMwbKUynWZ+T+e92Noa9dUi/zQ7IBTlYAAhnhqBsJNE1S1hlFQuylUsQIDAQAB',
        'notifyUrl'          => (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/alipay-notify',
];
//小智（熊洞）回收
$config['xz_trade_in'] = YII_ENV_PROD ? [
        'url'    => 'https://papi.bearhome.cn/hs/open/',
        'appkey' => '202401160001',
        'secret' => 'cRcNZzM0vNRVBrX2m0Jka7EAWWvBZBU4'
] : [
        'url'    => 'https://uat.juranguanjia.com/papi/hs/open/',
        'appkey' => '202401160001',
        'secret' => 'cRcNZzM0vNRVBrX2m0Jka7EAWWvBZBU4'
];


// 以旧换新
$config['trade_in'] = [
        'host_image' => 'https://wpm-cdn.dreame.tech/images/202403/65f7a881aa46b6973199936.png' // 主机线稿图
];

// mova 上下水短信验证码滑块配置
$config['mova_plumbing_message'] = [
        'accesskey'    => 'LTAI5t96WkBXXNzQrX4HtQti',
        'accessSecret' => '******************************',
        'appkey'       => 'FFFF0N0N00000000AFEA',
        'scene'        => 'nc_message_h5'
];

// 内容中台
// 评价
$config['dreame-cms'] = [
        'sign_key' => 'EfCj*YDcb5anqOs'
];

// 用户中台
$config['mp-user-center'] = [
        'sign_key' => 'EfCj*YDcb5anqOs'
];

// 商城go重构项目
$config['mp-mall'] = [
    'sign_key' => 'EfCj*YDcb5anqOs'
];

// 支付中台
$config['mp-pay'] = [
        'sign_key' => 'EfCj*YDcb5anqOs'
];

// PC端登录
$config['pc-weixin'] = YII_ENV_PROD ?
        [
                'login' => [
                        'appId'         => 'wx7f0a0237972bb4d1',
                        'appSecret'     => '861ca939831517b250625085b2ff5862',
                        'scope'         => 'snsapi_login',
                        'redirect_uri'  => 'https://mall.dreame.tech/login',
                        'id'            => 'wechat_redirect',
                        'response_type' => 'code',
                        'web_uri'       => 'https://mall.dreame.tech/'
                ],
                'bind'  => [
                        'appId'         => 'wx7f0a0237972bb4d1',
                        'appSecret'     => '861ca939831517b250625085b2ff5862',
                        'scope'         => 'snsapi_login',
                        'redirect_uri'  => 'https://mall.dreame.tech/mine/accountSecurity?dialog=1',
                        'id'            => 'wechat_redirect',
                        'response_type' => 'code',
                        'web_uri'       => 'https://mall.dreame.tech/'
                ]
        ]
        :
        [
                'login' => [
                        'appId'         => 'wx06aaacdd128121e8',
                        'appSecret'     => '284af7a647cf047677a3d7844214abcf',
                        'scope'         => 'snsapi_login',
                        'redirect_uri'  => 'https://uat-mall.dreame.tech/login',
                        'id'            => 'wechat_redirect',
                        'response_type' => 'code',
                        'web_uri'       => 'https://uat-mall.dreame.tech/'
                ],
                'bind'  => [
                        'appId'         => 'wx06aaacdd128121e8',
                        'appSecret'     => '284af7a647cf047677a3d7844214abcf',
                        'scope'         => 'snsapi_login',
                        'redirect_uri'  => 'https://uat-mall.dreame.tech/mine/accountSecurity?dialog=1',
                        'id'            => 'wechat_redirect',
                        'response_type' => 'code',
                        'web_uri'       => 'https://uat-mall.dreame.tech/'
                ]
        ];

// 用户中台
$config['mp-user-center'] = [
    'sign_key' => 'EfCj*YDcb5anqOs'
];