<?php

$config['com'] = [
        [
                "code" => "sf",
                "name" => "顺丰速运-贸易"
        ],
        [
                "code" => "ems",
                "name" => "EMS"
        ],
        [
                "code" => "sto",
                "name" => "申通E物流"
        ],
        [
                "code" => "yto",
                "name" => "圆通速递"
        ],
        [
                "code" => "zto",
                "name" => "中通速递"
        ],
        [
                "code" => "zjs",
                "name" => "宅急送"
        ],
        [
                "code" => "yunda",
                "name" => "韵达快运"
        ],
        [
                "code" => "pick",
                "name" => "上门提货"
        ],
        [
                "code" => "eyb",
                "name" => "EMS经济快递"
        ],
        [
                "code" => "postb",
                "name" => "邮政国内小包"
        ],
        [
                "code" => "jdkd",
                "name" => "京东快递"
        ],
        [
                "code" => "mtqcs",
                "name" => "美团全城送"
        ],
        [
                "code" => "mtzb",
                "name" => "美团众包"
        ],
        [
                "code" => "mtps",
                "name" => "美团配送(专/快/混)"
        ],
        [
                "code" => "zto-01",
                "name" => "中通-贸易"
        ],
        [
                "code" => "zto-02",
                "name" => "中通-追创"
        ],
        [
                "code" => "zto-03",
                "name" => "中通-信息"
        ],
        [
                "code" => "jdkd-01",
                "name" => "京东快递-贸易"
        ],
        [
                "code" => "jdx",
                "name" => "京东线下"
        ],
        [
                "code" => "sf-sh",
                "name" => "顺丰-贸易-售后"
        ],
        [
                "code" => "4px",
                "name" => "4PX"
        ],
        [
                "code" => "yw56",
                "name" => "燕文物流"
        ],
        [
                "code" => "sfff",
                "name" => "顺丰寄付"
        ],
        [
                "code" => "sf01",
                "name" => "顺丰-贸易-苏州"
        ],
        [
                "code" => "sf02",
                "name" => "顺丰-贸易-翔成"
        ],
        [
                "code" => "yto2",
                "name" => "圆通(无界面）"
        ],
        [
                "code" => "clyunda",
                "name" => "韵达菜鸟"
        ],
        [
                "code" => "shunfeng",
                "name" => "顺丰-贸易-苏州-抖音"
        ],
        [
                "code" => "yto-dy",
                "name" => "抖音圆通"
        ],
        [
                "code" => "sf999",
                "name" => "顺丰速运(常用)"
        ],
        [
                "code" => "jhwl",
                "name" => "锦海物流"
        ],
        [
                "code" => "sfgj",
                "name" => "顺丰国际"
        ],
        [
                "code" => "jd999",
                "name" => "京东物流(常用)"
        ],
        [
                "code" => "yhwl",
                "name" => "越海物流"
        ],
        [
                "code" => "yt999",
                "name" => "圆通快递(常用)"
        ],
        [
                "code" => "fengwang",
                "name" => "丰网速运"
        ],
        [
                "code" => "db999",
                "name" => "德邦快递(常用)"
        ],
        [
                "code" => "sfhk",
                "name" => "顺丰航空"
        ],
        [
                "code" => "kye",
                "name" => "跨越速运"
        ],
        [
                "code" => "kl",
                "name" => "嘉里大通"
        ],
        [
                "code" => "virtual",
                "name" => "电子票券消费"
        ],
        [
                "code" => "bestqjt",
                "name" => "百世快运"
        ],
        [
                "code" => "fnps",
                "name" => "蜂鸟配送"
        ],
        [
                "code" => "cnps",
                "name" => "菜鸟配送"
        ],
        [
                "code" => "ncwl",
                "name" => "鸟潮物流"
        ],
        [
                "code" => "JT",
                "name" => "极兔速递"
        ]
];


$config['kd100_to_oms'] = [
        'yuantong'       => ['yto', 'yto2', 'yto-dy', 'yt999'],
        'yunda'          => ['yunda', 'clyunda'],
        'shentong'       => ['sto'],
        'zhongtong'      => ['zto', 'zto-01', 'zto-02', 'zto-03', 'distributor_526075'],
        'youzhengguonei' => ['postb'],
        'shunfeng'       => ['sf', 'sf-sh', 'sfff', 'sf01', 'sf02', 'sf999', 'sfhk', 'shunfeng', 'sfgj', 'sf03', 'sf04', 'sf05'],
        'ems'            => ['ems', 'eyb'],
        'jd'             => ['jdkd', 'jdkd-01', 'jdx', 'jd999', 'jd'],
        'fengwang'       => ['le09252050'],
        'debangkuaidi'   => ['db999'],
        'huitongkuaidi'  => ['bestqjt', 'htky'],
        'danniao'        => ['zmkm'],
        'zhaijisong'     => ['zjs'],
        'kuayue'         => ['kye'],
        'jialidatong'    => ['kl'],
        'beebird'        => ['fnps'],
        'yw56'           => ['yw56'],
        'ane66'          => ['anps'],
        'tiantian'       => ['ttkdex'],
        'baishiwuliu'    => ['best'],
        'debangwuliu'    => ['dbl'],
        'pjbest'         => ['pj'],
        'huiqiangkuaidi' => ['zhqkd'],
        'jtexpress'      => ['jt']
];

$config['need_query_order_state'] = [
        0, 1, 5, 2, 8
];
