<?php

namespace app\modules\main\forms\goods;

use app\modules\main\forms\BaseModel;

/**
 * 商品推荐表单验证
 */
class GoodsRecommendForm extends BaseModel
{
    // 商品ID
    public $gid;
    // 推荐类型：1推荐，0取消推荐
    public $type;

    public function rules(): array
    {
        return [
            [['gid', 'type'], 'required'],
            [['gid'], 'integer', 'min' => 1],
            ['type', 'in', 'range' => [0, 1]],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'gid' => '商品ID',
            'type' => '推荐类型',
        ];
    }
} 