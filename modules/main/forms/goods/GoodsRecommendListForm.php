<?php

namespace app\modules\main\forms\goods;

use app\modules\main\forms\BaseModel;

/**
 * 获取推荐列表表单验证
 */
class GoodsRecommendListForm extends BaseModel
{
    public $page;
    public $page_size;

    public function rules(): array
    {
        return [
            [['page', 'page_size'], 'integer', 'min' => 1],
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'page' => '页码',
            'page_size' => '每页数量',
        ];
    }
} 