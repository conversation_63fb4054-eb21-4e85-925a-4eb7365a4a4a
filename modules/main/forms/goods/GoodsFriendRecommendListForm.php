<?php

namespace app\modules\main\forms\goods;

use app\modules\main\forms\BaseModel;

/**
 * 获取好友推荐列表表单验证
 */
class GoodsFriendRecommendListForm extends BaseModel
{
    public $page;
    public $page_size;

    // 最大分页限制
    const MAX_PAGE_SIZE = 50;

    public function rules(): array
    {
        return [
            [['page', 'page_size'], 'integer', 'min' => 1],
            ['page_size', 'integer', 'max' => self::MAX_PAGE_SIZE, 'tooBig' => '每页数量不能超过' . self::MAX_PAGE_SIZE],
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
            // 添加安全过滤
            [['page', 'page_size'], 'filter', 'filter' => 'intval'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'page' => '页码',
            'page_size' => '每页数量',
        ];
    }
} 