<?php

namespace app\modules\main\forms\popup;

use app\modules\main\forms\BaseModel;
use app\modules\main\models\UserPopupModel;

/**
 * 获取弹窗表单验证
 */
class UserPopupGetForm extends BaseModel
{
    public $popup_type;     // 弹窗类型
    public $auto_mark_read; // 是否自动标记为已读，0或1

    public function rules(): array
    {
        return [
            // 移除 popup_type 的 required 验证，让其可选
            [['popup_type'], 'integer', 'min' => 0, 'message' => '{attribute}必须为非负整数'],
            [['popup_type'], 'in', 'range' => array_merge([0], array_values(UserPopupModel::POPUP_TYPE)), 'message' => '不支持的弹窗类型'],
            [['auto_mark_read'], 'integer', 'message' => '{attribute}必须为整数'],
            [['auto_mark_read'], 'in', 'range' => [0, 1], 'message' => '{attribute}只能为0或1'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'popup_type' => '弹窗类型',
            'auto_mark_read' => '是否自动标记为已读',
        ];
    }

    /**
     * 获取安全的弹窗类型
     * @return int
     */
    public function getSafePopupType(): int
    {
        // 如果未传或为0，返回0表示获取全部类型
        return (int)($this->popup_type ?? 0);
    }

    /**
     * 获取安全的自动标记参数
     * @return bool
     */
    public function getSafeAutoMarkRead(): bool
    {
        return (bool)$this->auto_mark_read;
    }
} 