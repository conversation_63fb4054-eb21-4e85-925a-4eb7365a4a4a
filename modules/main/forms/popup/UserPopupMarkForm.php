<?php

namespace app\modules\main\forms\popup;

use app\modules\main\forms\BaseModel;
use app\modules\main\models\UserPopupModel;

/**
 * 标记弹窗为已展示表单验证
 */
class UserPopupMarkForm extends BaseModel
{
    public $popup_type; // 弹窗类型
    public $remark;     // 备注信息

    public function rules(): array
    {
        return [
            [['popup_type'], 'required', 'message' => '{attribute}不能为空'],
            [['popup_type'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['popup_type'], 'in', 'range' => array_values(UserPopupModel::POPUP_TYPE), 'message' => '不支持的弹窗类型'],
            [['remark'], 'string', 'max' => 255, 'message' => '{attribute}长度不能超过255个字符'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'popup_type' => '弹窗类型',
            'remark' => '备注信息',
        ];
    }

    /**
     * 获取安全的弹窗类型
     * @return int
     */
    public function getSafePopupType(): int
    {
        return (int)$this->popup_type;
    }

    /**
     * 获取安全的备注信息
     * @return string
     */
    public function getSafeRemark(): string
    {
        return trim((string)$this->remark);
    }
} 