<?php
namespace app\modules\main\forms\groupPurchase;

use app\modules\main\forms\BaseModel;

class GroupPurchaseListForm extends BaseModel
{

    public $activity_id;
    public $group_purchase_id;
    public $goods_name;
    public $type; // 1:我参与的团购 2:我发起的团购 3:我的正在拼的团
    public $gid;
    public $tag_id; // 标签ID，0为全部

    public function rules(): array
    {
        return [
            // 移除activity_id的required验证，改为integer和safe验证
            ['activity_id', 'integer'],
            ['group_purchase_id', 'required', 'message' => '请填写拼团ID'],
            [['activity_id', 'group_purchase_id', 'gid', 'tag_id'], 'integer'],
            [['type'], 'in', 'range' => [1, 2, 3], 'message' => '类型错误'],
            [['activity_id', 'group_purchase_id', 'goods_name', 'type', 'gid', 'tag_id'], 'safe'],
            ['tag_id', 'default', 'value' => 0],
        ];
    }


    public function attributeLabels(): array
    {
        return [
                'activity_id'       => '活动ID',
                'group_purchase_id' => '拼团ID',
                'goods_name'        => '商品名称',
                'type'              => '类型',
                'tag_id' => '标签ID',
        ];
    }

    public function scenarios()
    {
        return [
            'default' => ['activity_id', 'goods_name', 'type', 'gid', 'tag_id'],
                'my'      => ['goods_name', 'type'],
                'grouped' => ['group_purchase_id'],
                'check'   => ['activity_id', 'gid'],
        ];

    }


}