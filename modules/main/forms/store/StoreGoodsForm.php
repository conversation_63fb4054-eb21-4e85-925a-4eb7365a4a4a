<?php

namespace app\modules\main\forms\store;

use app\modules\main\forms\BaseModel;

/**
 * 追觅小店选品表单验证
 */
class StoreGoodsForm extends BaseModel
{
    public $goods_id;


    public function rules(): array
    {
        return [
            ['goods_id', 'required'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'page' => '页码',
            'page_size' => '每页数量',
        ];
    }
} 