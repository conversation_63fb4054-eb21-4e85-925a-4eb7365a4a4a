<?php

namespace app\modules\main\forms\employee;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\UserEmployeeService;

class RegisterRecordListForm extends BaseModel
{
    // 属性
    public $register_time;       // 注册时间
    public $bind_start_time;     // 搜索绑定开始时间
    public $bind_end_time;       // 搜索绑定结束时间
    public $page;
    public $page_size;


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            // 安全赋值
            [['register_time', 'bind_start_time', 'bind_end_time', 'page', 'page_size'], 'safe'],
            [['page', 'page_size'], 'integer'], // 整型
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
            [['register_time'], 'date', 'format' => 'php:Y-m'], // 日期格式校验

        ];
    }


    /**
     * 重新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'register_time'   => '注册时间',
            'bind_start_time' => '搜索注册开始时间',
            'bind_end_time'   => '搜索注册结束时间',
        ];
    }

    public function load($data, $formName = null): bool
    {
        if (parent::load($data, $formName)) {
            if (!empty($data['register_time'])) {
                // 解析年份和月份
                list($year, $month) = explode('-', $data['register_time']);

                // 获取该月的第一天和最后一天
                $firstDayOfMonth = "$year-$month-01";
                $lastDayOfMonth = date('Y-m-t', strtotime($firstDayOfMonth)); // 使用 'Y-m-t' 获取该月最后一天

                // 设置时间戳
                $this->bind_start_time = strtotime("$firstDayOfMonth 00:00:00");
                $this->bind_end_time = strtotime("$lastDayOfMonth 23:59:59");
            }
            return true;
        }
        return false;
    }

    /**
     * 定义要输出的字段，排除register_time
     * @return array
     */
    public function fields(): array
    {
        $fields = parent::fields();

        // 排除register_time字段
        unset($fields['register_time']);

        return $fields;
    }
}