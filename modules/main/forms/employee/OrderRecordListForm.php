<?php

namespace app\modules\main\forms\employee;

use app\modules\goods\models\BoundUserOrderModel;
use app\modules\main\forms\BaseModel;
use app\modules\main\services\UserEmployeeService;

/**
 * 下单记录列表
 */
class OrderRecordListForm extends BaseModel
{
    // 属性
    public $score_status;                    // 微笑分状态
    public $order_create_time;               // 下单时间
    public $order_start_create_time;         // 搜索下单开始时间
    public $order_end_create_time;           // 搜索下单结束时间
    public $page;
    public $page_size;


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            // 安全赋值
            [['score_status', 'order_create_time', 'order_start_create_time', 'order_end_create_time', 'page', 'page_size'], 'safe'],
            [['page', 'page_size'], 'integer'], // 整型
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
            [['order_create_time'], 'date', 'format' => 'php:Y-m'], // 日期格式校验

        ];
    }


    /**
     * 重新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'order_create_time'       => '订单创建时间',
            'order_start_create_time' => '搜索订单创建开始时间',
            'order_end_create_time'   => '搜索订单创建结束时间',
        ];
    }

    public function load($data, $formName = null): bool
    {
        if (parent::load($data, $formName)) {
            if (!empty($data['order_create_time'])) {
                // 解析年份和月份
                list($year, $month) = explode('-', $data['order_create_time']);

                // 获取该月的第一天和最后一天
                $firstDayOfMonth = "$year-$month-01";
                $lastDayOfMonth = date('Y-m-t', strtotime($firstDayOfMonth)); // 使用 'Y-m-t' 获取该月最后一天

                // 设置时间戳
                $this->order_start_create_time = strtotime("$firstDayOfMonth 00:00:00");
                $this->order_end_create_time = strtotime("$lastDayOfMonth 23:59:59");
            }
            return true;
        }
        return false;
    }

    /**
     * 定义要输出的字段，排除order_create_time
     * @return array
     */
    public function fields(): array
    {
        $fields = parent::fields();

        // 排除order_create_time字段
        unset($fields['order_create_time']);

        return $fields;
    }
}