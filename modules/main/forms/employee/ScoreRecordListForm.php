<?php

namespace app\modules\main\forms\employee;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\UserEmployeeService;

class ScoreRecordListForm extends BaseModel
{
    // 属性
    public $score_type; // 积分类型：1、积分，2、觅享分，3、微笑分
    public $page;
    public $page_size;


    /**
     * 验证规则
     * @return array
     */
    public function rules(): array
    {
        return [
            // 安全赋值
            [['score_type', 'page', 'page_size'], 'safe'],
            [['page', 'page_size'], 'integer'], // 整型
            ['score_type', 'in', 'range' => UserEmployeeService::SCORE_TYPE],
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
        ];
    }


    /**
     * 重新定义属性
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'score_type' => '积分类型',
        ];
    }
}