<?php

namespace app\modules\main\forms\plumbing;

use app\modules\main\forms\BaseModel;
use app\modules\plumbing\models\PlumbingOrderModel;

class ServiceOrderSaveForm extends BaseModel
{
    public $type;
    public $name;
    public $phone;
    public $verify_code;
    public $pid;
    public $cid;
    public $aid;
    public $detail;
    public $expect_time;
    public $context_type;
    public $product_type;
    public $sn_config;
    public $sn;
    public $tenant;
    public $province;
    public $city;
    public $county;

    public function rules(): array
    {
        return [
            // 服务类型、姓名、手机号、验证码、省、市、区、详细地址、期望上门时间、家庭环境、产品类型、产品信息、SN编码
                [['type', 'name', 'phone', 'verify_code', 'pid', 'cid', 'aid', 'detail', 'expect_time', 'context_type', 'product_type', 'sn_config', 'sn'], 'required'],
                ['name', 'string', 'length' => [1, 50]],
                ['phone', 'string', 'length' => [11, 11]],
                ['detail', 'string', 'length' => [1, 100]],
                [['pid', 'cid', 'aid'], 'string'],
                [['province', 'city', 'county'], 'string'],
                ['context_type', 'in', 'range' => PlumbingOrderModel::CONTEXT_TYPE],    // 家庭环境
                ['product_type', 'in', 'range' => PlumbingOrderModel::PRODUCT_TYPE],    // 产品类型
                ['tenant', 'in', 'range' => PlumbingOrderModel::TENANT],                // 租户
                ['tenant', 'default', 'value' => PlumbingOrderModel::TENANT['NORMAL']], // 租户
                ['sn', 'match', 'pattern' => '/^[a-zA-Z0-9-\/]+$/u']
        ];
    }

    /**
     * 从新定义属性
     * @return string[]
     */
    public function attributeLabels()
    {
        return [
                'type'         => '服务类型',
                'name'         => '姓名',
                'phone'        => '手机号',
                'verify_code'  => '验证码',
                'pid'          => '省',
                'province'     => '省',
                'cid'          => '市',
                'city'         => '市',
                'aid'          => '区',
                'county'       => '区',
                'detail'       => '详细地址',
                'expect_time'  => '期望上门时间',
                'context_type' => '家庭环境',
                'product_type' => '产品类型',
                'sn_config'    => '产品信息',
                'sn'           => 'SN编码',
                'tenant'       => '租户'
        ];
    }
}