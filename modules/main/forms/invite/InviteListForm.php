<?php

namespace app\modules\main\forms\invite;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\invite\InviteService;

/**
 * 邀请列表查询表单验证
 */
class InviteListForm extends BaseModel
{
    public $page = 1;       // 页码
    public $page_size = 20; // 每页数量
    public $invite_type;    // 邀请类型
    public $relate_id;      // 关联ID

    public function rules(): array
    {
        return [
            [['page', 'page_size'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['page_size'], 'integer', 'max' => 100, 'message' => '每页数量不能超过100'],
            [['invite_type', 'relate_id'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['invite_type'], 'in', 'range' => array_values(InviteService::INVITE_TYPE), 'message' => '不支持的邀请类型'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'page' => '页码',
            'page_size' => '每页数量',
            'invite_type' => '邀请类型',
            'relate_id' => '关联ID',
        ];
    }

    /**
     * 获取安全的分页参数
     * @return array
     */
    public function getSafePageParams(): array
    {
        return [
            'page' => max(1, (int)$this->page),
            'page_size' => min(100, max(1, (int)$this->page_size)),
        ];
    }

    /**
     * 获取安全的筛选参数
     * @return array
     */
    public function getSafeFilterParams(): array
    {
        return [
            'invite_type' => $this->invite_type ? (int)$this->invite_type : null,
            'relate_id' => $this->relate_id ? (int)$this->relate_id : null,
        ];
    }
} 