<?php

namespace app\modules\main\forms\invite;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\invite\InviteService;

/**
 * 新增邀请表单验证
 */
class InviteJoinForm extends BaseModel
{
    public $inviter_id;  // 邀请人ID
    public $invite_type; // 邀请类型
    public $relate_id;   // 关联ID
    public $device_id;   // 设备ID（可选）

    public function rules(): array
    {
        return [
            [['inviter_id', 'invite_type', 'relate_id'], 'required', 'message' => '{attribute}不能为空'],
            [['inviter_id', 'invite_type', 'relate_id'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['invite_type'], 'in', 'range' => array_values(InviteService::INVITE_TYPE), 'message' => '不支持的邀请类型'],
            [['device_id'], 'string', 'max' => 50, 'message' => '设备ID长度不能超过50个字符'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'inviter_id' => '邀请人ID',
            'invite_type' => '邀请类型',
            'relate_id' => '关联ID',
            'device_id' => '设备ID',
        ];
    }
} 