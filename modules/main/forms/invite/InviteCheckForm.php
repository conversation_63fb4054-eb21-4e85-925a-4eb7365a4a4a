<?php

namespace app\modules\main\forms\invite;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\invite\InviteService;

/**
 * 检查邀请资格表单验证
 */
class InviteCheckForm extends BaseModel
{
    public $inviter_id;  // 邀请人ID
    public $invite_type; // 邀请类型
    public $relate_id;   // 关联ID

    public function rules(): array
    {
        return [
            [['inviter_id', 'invite_type', 'relate_id'], 'required', 'message' => '{attribute}不能为空'],
            [['inviter_id', 'invite_type', 'relate_id'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['invite_type'], 'in', 'range' => array_values(InviteService::INVITE_TYPE), 'message' => '不支持的邀请类型'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'inviter_id' => '邀请人ID',
            'invite_type' => '邀请类型',
            'relate_id' => '关联ID',
        ];
    }
} 