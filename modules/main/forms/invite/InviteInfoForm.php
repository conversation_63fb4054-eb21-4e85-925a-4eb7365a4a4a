<?php

namespace app\modules\main\forms\invite;

use app\modules\main\forms\BaseModel;
use app\modules\main\services\invite\InviteService;

/**
 * 邀请信息查询表单验证
 */
class InviteInfoForm extends BaseModel
{
    public $invite_type; // 邀请类型
    public $relate_id; // 关联ID，关联user_invite_gift_rules.id

    public function rules(): array
    {
        return [
            [['invite_type'], 'required', 'message' => '{attribute}不能为空'],
            [['invite_type'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
            [['invite_type'], 'in', 'range' => array_values(InviteService::INVITE_TYPE), 'message' => '不支持的邀请类型'],
            [['relate_id'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'invite_type' => '邀请类型',
            'relate_id' => '关联规则ID',
        ];
    }
} 