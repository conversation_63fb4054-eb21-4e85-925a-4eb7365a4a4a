<?php

namespace app\modules\main\forms\review;

use app\modules\main\forms\BaseModel;

/**
 * 获取我的评价列表
 */
class GetMyReviewListForm extends BaseModel
{
    public $type; // 评价类型：1待评价、2评价列表、3可追评
    public $page;
    public $page_size;

    public function rules(): array
    {
        return [
            [['type'], 'required'], // 必填
            [['type', 'page', 'page_size'], 'integer'], // 整型
            ['type', 'in', 'range' => [1, 2, 3]],
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'type'      => '评价类型',
            'page'      => '页码',
            'page_size' => '每页数量',
        ];
    }
}
