<?php

namespace app\modules\main\forms\review;

use app\modules\main\forms\BaseModel;

/**
 * 获取评价列表
 */
class GetReviewListForm extends BaseModel
{
    public $gid; // 商品ID
    public $has_media; // 是否有媒体文件（0不限、1有图片或视频）
    public $has_append; // 是否有追评（0不限、2有追评）
    public $goods_type; // 商品类型：1普通商品、2积分商品
    public $page;
    public $page_size;

    const GOODS_TYPE = [
        'NORMAL' => 1, // 普通商品
        'POINTS' => 2, // 积分商品
    ];

    public function rules(): array
    {
        return [
            [['gid'], 'required'], // 必填
            [['gid', 'page', 'page_size'], 'integer'], // 整型
            ['has_media', 'default', 'value' => 0],
            ['has_append', 'default', 'value' => 0],
            ['goods_type', 'default', 'value' => self::GOODS_TYPE['NORMAL']],
            ['page', 'default', 'value' => self::PAGE],
            ['page_size', 'default', 'value' => self::PAGE_SIZE],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'gid'        => '商品',
            'has_media'  => '有图片/视频',
            'has_append' => '有追评',
            'page'       => '页码',
            'page_size'  => '每页数量',
        ];
    }

    // 处理数据
    public function load($data, $formName = null): bool
    {
        if (parent::load($data, $formName)) {
            if (!empty($data['has_append'])) {
                $this->has_append = 2; // 有追评
            }
            return true;
        }
        return false;
    }
}
