<?php

namespace app\modules\main\forms\review;

use app\components\Review;
use app\modules\main\forms\BaseModel;

/**
 * 创建商品评价
 */
class CreateGoodsReviewForm extends BaseModel
{
    // 评价ID
    public $review_id;
    // 订单号
    public $order_no;
    // sku
    public $sku;
    // 标签
    public $label;
    // 内容
    public $content;
    // 图片
    public $image_url;
    // 视频
    public $video_url;
    // 打分
    public $rating;
    // 匿名
    public $is_anonymous;
    // 评价类型：1首评、2追评
    public $type;

    public function rules()
    {
        return [
            [['order_no', 'sku', 'is_anonymous', 'type'], 'required'],
            ['content', 'string', 'max' => 1000, 'tooLong' => '您输入的字符已超过最大限制'], // 评价内容1000字符
            ['is_anonymous', 'in', 'range' => [0, 1]],  // 匿名 0:否 1:是
            ['type', 'in', 'range' => [1, 2]],  // 评价类型 1:首评 2:追评
            ['review_id', 'default', 'value' => 0],
            [['label', 'image_url', 'video_url', 'rating'], 'default', 'value' => ''], // 默认为空字符串
            ['content', 'validateContent'], // 验证评价内容
            ['image_url', 'validateImageURLCount', 'skipOnEmpty' => true], // 验证图片数量
            ['video_url', 'validateVideoURLCount', 'skipOnEmpty' => true], // 验证视频数量
            ['rating', 'validateRatingStructure', 'skipOnEmpty' => true], // 验证 rating 的结构
        ];
    }

    public function attributeLabels()
    {
        return [
            'order_no'     => '订单号',
            'sku'          => 'sku',
            'label'        => '描述标签',
            'content'      => '内容',
            'image_url'    => '图片',
            'video_url'    => '视频',
            'rating'       => '评分',
            'is_anonymous' => '匿名',
            'type'         => '评价类型',
        ];
    }

    // 处理数据
    public function load($data, $formName = null): bool
    {
        if (parent::load($data, $formName)) {
            if (!empty($data['label'])) {
                $this->label = json_decode($data['label'], true);
            }
            if (!empty($data['rating'])) {
                $this->rating = json_decode($data['rating'], true);
            }
            if (!empty($data['image_url'])) {
                $this->image_url = json_decode($data['image_url'], true);
            }
            if (!empty($data['video_url'])) {
                $this->video_url = json_decode($data['video_url'], true);
            }
            return true;
        }
        return false;
    }

    /**
     * 校验评价内容
     */
    public function validateContent($attribute, $params)
    {
        $content = $this->$attribute;
        // 进行内容审核的逻辑，这里只是一个示例
        list($status, $res) = Review::factory()->checkContent($content);
        if (isset($res['status']) && $res['status'] != 1) {
            $reason = json_decode($res['reason'], true);
            $words = $this->findCommonSubstrings($content, $reason['riskWords']);
            $this->addError($attribute, '您输入的内容含敏感词，' . implode('、', $words));
            return;
        }
    }

    /**
     * 验证图片数量
     * @param $attribute
     * @param $params
     */
    public function validateImageURLCount($attribute, $params)
    {
        $data = $this->$attribute;
        if (count($data) > 5) {
            $this->addError($attribute, '上限5张图片');
            return;
        }
    }

    /**
     * 验证视频数量
     * @param $attribute
     * @param $params
     */
    public function validateVideoURLCount($attribute, $params)
    {
        $data = $this->$attribute;
        if (count($data) > 1) {
            $this->addError($attribute, '上限1条视频');
            return;
        }
    }

    /**
     * 验证评分数据结构
     * @param $attribute
     * @param $params
     */
    public function validateRatingStructure($attribute, $params)
    {
        $data = $this->$attribute;
        if (!is_array($data) || count($data) !== 4) {
            $this->addError($attribute, '评分格式错误');
            return;
        }

        foreach ($data as $key => $value) {
            if (!in_array($key, ['1', '2', '3', '4']) || !is_int($value) || $value < 1 || $value > 5) {
                $this->addError($attribute, '评分值为1-5的整数');
                return;
            }
        }
    }

    // 获取公共子串
    private function findCommonSubstrings($text1, $text2)
    {
        // 将text2按逗号分割成数组
        $text2Arr = explode(',', $text2);

        // 去除数组中的空白字符
        $text2Arr = array_map('trim', $text2Arr);

        $data = [];

        // 遍历text2Array中的每个子串，检查它是否在text1中出现
        foreach ($text2Arr as $substring) {
            if (mb_strpos($text1, $substring) !== false) {
                $data[] = $substring;
            }
        }

        return $data;
    }

}
