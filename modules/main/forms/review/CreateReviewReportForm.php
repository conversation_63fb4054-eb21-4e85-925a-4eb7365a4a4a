<?php

namespace app\modules\main\forms\review;

use app\modules\main\forms\BaseModel;

/**
 * 创建评价举报
 */
class CreateReviewReportForm extends BaseModel
{
    // 被举报的评价
    public $review_id;
    // 举报理由
    public $reason_id;
    // 举报描述
    public $content;

    public function rules()
    {
        return [
            [['review_id', 'reason_id', 'content'], 'required'],
            ['content', 'string', 'max' => 500, 'tooLong' => '您输入的字符已超过最大限制'], // 500字符
        ];
    }

    public function attributeLabels()
    {
        return [
            'review_id' => '被举报的评价',
            'reason_id' => '举报理由',
            'content'   => '举报描述',
        ];
    }
}