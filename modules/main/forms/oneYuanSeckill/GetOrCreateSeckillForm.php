<?php

namespace app\modules\main\forms\oneYuanSeckill;

use app\modules\main\forms\BaseModel;

/**
 * 获取或创建一元秒杀助力表单验证
 */
class GetOrCreateSeckillForm extends BaseModel
{
    public $activity_id; // 活动ID
    public $gid;         // 商品ID

    public function rules(): array
    {
        return [
            [['activity_id', 'gid'], 'required', 'message' => '{attribute}不能为空'],
            [['activity_id', 'gid'], 'integer', 'message' => '{attribute}必须为整数'],
            [['activity_id'], 'compare', 'compareValue' => 1, 'operator' => '>=', 'message' => '活动ID必须为正整数'],
            // 允许 gid=0 的场景
            [['gid'], 'compare', 'compareValue' => 0, 'operator' => '>=', 'message' => '商品ID必须为大于等于0的整数'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'activity_id' => '活动ID',
            'gid' => '商品ID',
        ];
    }
}