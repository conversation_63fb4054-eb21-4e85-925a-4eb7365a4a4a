<?php

namespace app\modules\main\forms\oneYuanSeckill;

use app\modules\main\forms\BaseModel;

/**
 * 发起一元秒杀助力表单验证
 */
class StartSeckillForm extends BaseModel
{
    public $activity_id; // 活动ID
    public $gid;         // 商品ID

    public function rules(): array
    {
        return [
            [['activity_id', 'gid'], 'required', 'message' => '{attribute}不能为空'],
            [['activity_id', 'gid'], 'integer', 'min' => 1, 'message' => '{attribute}必须为正整数'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'activity_id' => '活动ID',
            'gid' => '商品ID',
        ];
    }
} 