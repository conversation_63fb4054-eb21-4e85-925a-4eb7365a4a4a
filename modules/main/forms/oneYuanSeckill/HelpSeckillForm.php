<?php

namespace app\modules\main\forms\oneYuanSeckill;

use app\modules\main\forms\BaseModel;

/**
 * 为他助力表单验证
 */
class HelpSeckillForm extends BaseModel
{
    public $seckill_id; // 秒杀ID

    public function rules(): array
    {
        return [
            [['seckill_id'], 'required', 'message' => '秒杀ID不能为空'],
            [['seckill_id'], 'integer', 'min' => 1, 'message' => '秒杀ID必须为正整数'],
        ];
    }

    /**
     * 重新定义属性标签
     * @return string[]
     */
    public function attributeLabels(): array
    {
        return [
            'seckill_id' => '秒杀ID',
        ];
    }
} 