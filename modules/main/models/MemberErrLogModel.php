<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\PointCenter;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;
use yii\db\Expression;

class MemberErrLogModel extends CommModel
{
    const STATUS = [
        'success' => 2,
        'fail' => 1
    ];
    
    /** @var int 最大重试次数 */
    const MAX_RETRY = 10;
    
    public static function tableName(): string
    {
        return "`db_dreame_log`.`t_member_err_log`";
    }
    
    /**
     * 保存日志
     * @param string $function
     * @param array $params
     * @param string $msg
     * @return array
     */
    public function saveLog(string $function, array $params, string $msg): array
    {
        $table = $this->tableName();
        $time = time();
        $data = [
            'function' => $function,
            'params' => empty($params) ? '' : json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
            'resp_msg' => $msg,
            'ctime' => $time,
            'utime' => $time,
        ];
        
        try {
            by::dbMaster()->createCommand()->insert($table, $data)->execute();
        } catch (\Throwable $e) {
            return [false, $e->getMessage()];
        }
        
        return [true, 'ok'];
    }
    
    
    /**
     * 获取列表
     */
    public function getList($page, $page_size)
    {
        $tb = $this->tableName();
        $sql = "select count(*) from {$tb} where `status` in (0, 1)";
        $total = by::dbMaster()->createCommand($sql)->queryScalar();
        list($offset) = CUtil::pagination($page, $page_size);
        
        $pages = CUtil::getPaginationPages($total, $page_size);
        $sql = "select * from {$tb} where `status` in (0, 1) order by `id` desc limit {$offset},{$page_size}";
        $list = by::dbMaster()->createCommand($sql)->queryAll();
        
        return [$pages, $list];
    }
    
    public function finalRetry(): array
    {
        $id = 0;
        $maxRetry = self::MAX_RETRY;
        $where = " `status` in (0, 1) AND `err_count` < {$maxRetry}";
        $db = by::dbMaster();
        $tb = $this->tableName();
        
        while (true) {
            $sql = "SELECT * FROM {$tb} WHERE {$where} AND `id` > :id ORDER BY `id` ASC LIMIT 100";
            
            // SELECT * FROM `db_dreame_log`.`t_member_err_log` WHERE `status` in (0, 1) AND `err_count` < 10 AND `id` > 0 ORDER BY `id` ASC LIMIT 100
            $list = $db->createCommand($sql, [':id' => $id])->queryAll();

            if (empty($list)) {
                break;
            }
            
            $end = end($list);
            $id = $end['id'];
            
            foreach ($list as $val) {
                $this->retry($val);
            }
        }
        return [true, "OK"];
    }

    public function retry(array $data): array
    {
        $id = $data['id'] ?? 0;
        $tb = $this->tableName();

        list($status, $msg) = PointCenter::factory()->run($data['function'], json_decode($data['params'], true), $data['ctime']);

        $updateStatus = self::STATUS['fail'];
        if ($status) {
            $updateStatus = self::STATUS['success'];
        }
        
        // UPDATE `db_dreame_log`.`t_member_err_log` SET `err_count`=err_count + 1, `status`=2, `resp_msg`='积分记录成功', `utime`=1742970007 WHERE `id`='1'"
        by::dbMaster()->createCommand()->update($tb, ['err_count' => new Expression('err_count + 1'), 'status' => $updateStatus, 'resp_msg' => $msg, 'utime' => time()], ['id' => $id])->execute();
        
        return [$status, $msg];
    }
    
    
}
