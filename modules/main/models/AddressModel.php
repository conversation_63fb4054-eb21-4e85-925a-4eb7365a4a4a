<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/14
 * Time: 15:02
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
/**
 * 收货地址相关模型
 * @auth hwh
 * @date 2021-7-12
 */
class AddressModel extends CommModel {

    CONST STATUS = [ //是否是默认收货地址
        'NO' => 0, //否
        'YES'=> 1, //是
        'DEL'=> 2, //删除
    ];

    // 是否删除
    const IS_DEL = [
        'no'  => 0,
        'yes' => 1
    ];

    CONST ListExpire  = 600;//列表缓存

    CONST LIMIT_PID   = 650000; //可选pid范围

    public $tb_fields = [
        'id','user_id','nick','phone','pid','cid','aid','detail','status','crm_code','is_del','ctime','dtime'
    ];

    public static function tbName($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return  "`db_dreame`.`t_address_{$mod}`";
    }

    /**
     * @param $user_id
     * @param $aid
     * @return string
     * 查询指定收货信息
     */
    private function __getOneAddress($user_id,$aid): string
    {
        return AppCRedisKeys::getOneAddress($user_id,$aid);
    }

    /**
     * @param $user_id
     * @param $is_del
     * @return string
     * 查询默认地址
     */
    private function __getDefaultAddress($user_id, $is_del): string
    {
        return AppCRedisKeys::getDefaultAddress($user_id, $is_del);
    }

    /**
     * @param $user_id
     * @return string
     * 签码哈希列表
     */
    private function __getAddressListKey($user_id): string
    {
        return AppCRedisKeys::getAddressList($user_id);
    }

    /**
     * @param $user_id
     * @return int
     * 缓存清理
     */
    public function __delListCache($user_id): int
    {
        $r_key1 = $this->__getAddressListKey($user_id);
        $r_key2 = $this->__getDefaultAddress($user_id, null);
        $r_key3 = $this->__getDefaultAddress($user_id, self::IS_DEL['no']);
        $r_key4 = $this->__getDefaultAddress($user_id, self::IS_DEL['yes']);
        return by::redis('core')->del($r_key1,$r_key2,$r_key3,$r_key4);
    }

    /**
     * @param $user_id
     * @param $aid
     * @return int
     * 删除单个记录缓存
     */
    public function __delCache($user_id,$aid): int
    {
        $r_key = $this->__getOneAddress($user_id,$aid);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $user_id
     * @return int
     * 删除默认地址缓存
     */
    private function __delDefaultCache($user_id)
    {
        // 三种情况
        $r_key1 = $this->__getDefaultAddress($user_id, null);
        $r_key2 = $this->__getDefaultAddress($user_id, self::IS_DEL['no']);
        $r_key3 = $this->__getDefaultAddress($user_id, self::IS_DEL['yes']);
        return by::redis('core')->del($r_key1, $r_key2, $r_key3);
    }

    /**
     * @param $user_id
     * @param $arr
     * @return array
     * @throws \yii\db\Exception
     * 收货地址增改
     */
    public function SaveLog($user_id,$arr): array
    {
        $user_id = CUtil::uint($user_id);
        if(empty($user_id)) {
            return [false,"非法操作"];
        }

        $unique_key = CUtil::getAllParams('address',__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,3,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $id      = $arr['id']      ?? 0;
        $status  = $arr['status']  ?? 0;    //是否设置为默认收获地址 0否1是
        $pid     = $arr['pid']     ?? 0;
        $cid     = $arr['cid']     ?? 0;
        $aid     = $arr['aid']     ?? 0;
        $nick    = $arr['nick']    ?? '';
        $detail  = $arr['detail']  ?? '';

        $id      = CUtil::uint($id);
        $pid     = CUtil::uint($pid);
        $cid     = CUtil::uint($cid);
        $aid     = CUtil::uint($aid);
        $status  = intval(!!$status);

        if (empty($pid) || empty($cid) || empty($aid)) {
            return [false, '请选择完整的地区'];
        }

        if (empty($nick)) {
            return [false,"收货人不能为空"];
        }
        if (empty($detail)) {
            return [false,"详细地址不能为空"];
        }
        if (strlen($detail)>499){
            return [false,"详细地址不能超过500字"];
        }
        if(empty($arr['phone']) || !CUtil::reg_valid($arr['phone'],CUtil::REG_PHONE)) {
            return [false,"手机号格式不正确"];
        }

        $pattern = '/^[\w\x{4e00}-\x{9fa5}]+$/u';
        $flag = preg_match($pattern, $nick);
        if (!$flag) {
            return [false, '请输入正确的姓名'];
        }

        //校验pid、cid、aid正确性
        if ($pid > self::LIMIT_PID) {
            return [false, '请选择正确的地址(0)'];
        }

        $list2 = by::model('AreaModel',MAIN_MODULE)->GetList($pid);
        $cids  = array_column($list2, 'id');
        if ( !in_array($cid, $cids) ) {
            return [false, '请选择正确的地址(1)'];
        }

        $list3 = by::model('AreaModel',MAIN_MODULE)->GetList($cid);
        $aids  = array_column($list3, 'id');
        if ( !in_array($aid, $aids) ) {
            return [false, '请选择正确的地址(2)'];
        }


        $detail  = str_replace(PHP_EOL, ' ', $detail);

        $save    = [
            'pid'       => $pid,
            'cid'       => $cid,
            'aid'       => $aid,
            'detail'    => $detail,
            'nick'      => $nick,
            'phone'     => $arr['phone']    ?? '',
            'status'    => $status,
            'is_del'    => self::IS_DEL['no'], // 删除状态，默认为否
        ];

        $tb      = self::tbName($user_id);
        $command = by::dbMaster()->createCommand();

        //本次提交了默认地址 要把旧默认地址置为非默认
        if($status == self::STATUS['YES']) {

            $default          = $this->GetDefaultAddress($user_id);
            //todo 提交了新的默认地址
            if (!empty($default) && $default['id'] != $id) {
                $command->update($tb,['status'=>0],
                    ['id'=>$default['id'], 'user_id'=>$user_id]
                )->execute();

                //清理旧默认地址缓存
                $this->__delCache($user_id,$default['id']);

            }
        }

        //新增
        if(empty($id)) {
            $save['user_id']    = $user_id;
            $save['ctime']      = intval(START_TIME);
            $command->insert($tb,$save)->execute();
            $new_id = by::dbMaster()->getLastInsertID();

        } else {

            $info  = $this->GetOneAddress($user_id, $id);
            if (!$info) {
                return [false,"收货地址不存在或已被删除"];
            }

            //更新
            $command->update($tb,$save,['id'=>$id,'user_id'=>$user_id])->execute();
        }

        //删除列表缓存
        $this->__delListCache($user_id);

        //删除单个记录缓存
        $id && $this->__delCache($user_id,$id);

        return [true, ['id' => $new_id ?? $id]]; // 返回ID
    }

    /**
     * @param $user_id
     * @param $id
     * @return array
     * @throws \yii\db\Exception
     * 删除地址
     */
    public function Del($user_id, $id)
    {
        $user_id    = CUtil::uint($user_id);
        $id         = CUtil::uint($id);

        if (empty($id)) {
            return [false, '参数错误'];
        }

        $unique_key = CUtil::getAllParams('address',__FUNCTION__,$id);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,2,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $info       = $this->GetOneAddress($user_id, $id, false);
        if (empty($info)) {
            return [false,"收货地址不存在或已被删除"];
        }

        //只有一条收货地址时，不准删除
        $count  = $this->GetListCount($user_id);
        if ($count <= 1) {
            return [false, "请至少保留一条收货地址"];
        }

        $db     = by::dbMaster();
        $tb     = self::tbName($user_id);

        $trans  = $db->beginTransaction();

        try {
            $db->createCommand()->delete($tb,['id'=>$id, 'user_id'=>$user_id])->execute();

            //删除默认地址后，第一条地址设为默认地址
            if ($info['status'] == self::STATUS['YES']) {

                $sql    = "SELECT `id` FROM {$tb} WHERE `user_id`=:user_id AND `status`=:status ORDER BY `id` LIMIT 1";
                $aLog   = $db->createCommand($sql, [":user_id"=>$user_id,":status"=>self::STATUS['NO']])->queryOne();

                $db->createCommand()->update($tb,
                    ['status'=>self::STATUS['YES']],
                    ['id'=>$aLog['id'], 'user_id' => $user_id]
                )->execute();

                $this->__delCache($user_id,$aLog['id']);
            }

            //删除列表缓存
            $this->__delListCache($user_id);
            $this->__delCache($user_id,$id);

            $trans->commit();

            //将删除的地址默认改为否

            return [true, 'ok'];
        } catch (\Exception $e) {

            $trans->rollBack();

            return [false, '操作失败'];
        }
    }

    /**
     * 删除地址：默认地址也可以删除，删除后不展示默认标签，直至重新设置默认地址。
     *（新逻辑）
     * @param $user_id
     * @param $id
     * @return array
     * @throws \yii\db\Exception
     */
    public function DelAddress($user_id, $id)
    {
        $user_id = CUtil::uint($user_id);
        $id = CUtil::uint($id);

        if (empty($id)) {
            return [false, '参数错误'];
        }

        $unique_key = CUtil::getAllParams('address', __FUNCTION__, $id);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 2, 'EX');
        if (!$anti) {
            return [false, "请勿频繁操作"];
        }

        $info = $this->GetOneAddress($user_id, $id, false);
        if (empty($info)) {
            return [false, "收货地址不存在或已被删除"];
        }

        $db = by::dbMaster();
        $tb = self::tbName($user_id);

        $trans = $db->beginTransaction();

        try {
            // 软删除，更新is_del状态
            $db->createCommand()->update(
                $tb,
                ['is_del' => self::IS_DEL['yes'], 'dtime' => time()],
                ['id' => $id, 'user_id' => $user_id]
            )->execute();

            //删除列表缓存
            $this->__delListCache($user_id);
            $this->__delCache($user_id, $id);

            $trans->commit();

            return [true, 'ok'];
        } catch (\Exception $e) {

            $trans->rollBack();

            return [false, '操作失败'];
        }
    }


    /**
     * @param $user_id
     * @param $aid
     * @param bool $can_name
     * @return array|false
     * @throws \yii\db\Exception
     * 获取地址详情
     */
    public function GetOneAddress($user_id, $aid, bool $can_name = true)
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getOneAddress($user_id,$aid);
        $aJson       = $redis->get($r_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = self::tbName($user_id);
            $fields              = implode("`,`",$this->tb_fields);
            $sql                 = "SELECT `{$fields}` FROM {$tb} WHERE `id`=:id AND `user_id`=:user_id LIMIT 1";
            $command             = by::dbMaster()->createCommand($sql,[':id'=>$aid, ':user_id'=>$user_id]);
            $aData               = $command->queryOne();
            $aData               = empty($aData) ? [] : $aData;

            by::redis('core')->set($r_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);

        }

        if (empty($aData)) {
            return [];
        }

        if ($can_name) {
            $list1 = by::model('AreaModel',MAIN_MODULE)->GetList();
            $list2 = by::model('AreaModel',MAIN_MODULE)->GetList($aData['pid']);
            $list3 = by::model('AreaModel',MAIN_MODULE)->GetList($aData['cid']);

            $list1 = array_column($list1, 'name', 'id');
            $list2 = array_column($list2, 'name', 'id');
            $list3 = array_column($list3, 'name', 'id');

            $aData['province']  = $list1[$aData['pid']]     ?? '';
            $aData['city']      = $list2[$aData['cid']]     ?? '';
            $aData['area']      = $list3[$aData['aid']]     ?? '';
        }

        return $aData;
    }

    /**
     * 收获地址列表
     * @param int $user_id
     * @param int $page
     * @param int $page_size
     * @param $is_del
     * @return array
     * @throws \yii\db\Exception
     */
    public function GetList($user_id = 0, $page = 1, $page_size = 50, $is_del = null){
        $redis       = by::redis('core');
        $r_key       = $this->__getAddressListKey($user_id);
        // 增加删除状态
        if (is_null($is_del)) {
            $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size);
        } else {
            $sub_key = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$is_del);
        }
        $aJson       = $redis->hGet($r_key,$sub_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = self::tbName($user_id);
            list($offset)        = CUtil::pagination($page,$page_size);
            // 增加删除状态
            if (is_null($is_del)) {
                $sql             = "SELECT `id` FROM  {$tb} WHERE `user_id`=:user_id 
                                    ORDER BY `status` DESC, `id` DESC LIMIT {$offset},{$page_size}";
                $command         = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id]);
            } else {
                $sql             = "SELECT `id` FROM  {$tb} WHERE `user_id`=:user_id AND `is_del`=:is_del
                                    ORDER BY `status` DESC, `id` DESC LIMIT {$offset},{$page_size}";
                $command         = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id, ':is_del'=>$is_del]);
            }
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return $aData;
    }

    /**
     * @param int $user_id
     * @return int
     * @throws \yii\db\Exception
     * 获取收获地址总数
     */
    public function GetListCount($user_id=0): int
    {
        $redis       = by::redis('core');
        $r_key       = $this->__getAddressListKey($user_id);
        $sub_key     = CUtil::getAllParams(__FUNCTION__);
        $count       = $redis->hGet($r_key,$sub_key);

        if($count === false) {
            $tb                  = $this->tbName($user_id);
            $sql                 = "SELECT COUNT(*) FROM  {$tb} WHERE `user_id`=:user_id AND `is_del`=:is_del";
            $command             = by::dbMaster()->createCommand($sql, [':user_id' => $user_id, ':is_del' => self::IS_DEL['no']]);
            $count               = $command->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return intval($count);
    }

    /**
     * @param $user_id
     * @param $is_del
     * @param bool $can_name
     * @return array|false
     * @throws \yii\db\Exception
     */
    public function GetDefaultAddress($user_id, $is_del = null, bool $can_name = true){
        $user_id    = CUtil::uint($user_id);
        $redis      = by::redis('core');
        $r_key      = $this->__getDefaultAddress($user_id, $is_del);
        $id         = $redis->get($r_key);

        if($id === false) {
            $tb                  = self::tbName($user_id);
            if (is_null($is_del)) { // 不处理
                $sql             = "SELECT `id` FROM {$tb} WHERE `user_id`=:user_id ORDER BY status DESC, ctime DESC LIMIT 1";
                $command         = by::dbMaster()->createCommand($sql,[ ':user_id'=>$user_id]);
            } else { // 查询删除状态
                $sql             = "SELECT `id` FROM {$tb} WHERE `user_id`=:user_id AND `is_del`=:is_del ORDER BY status DESC, ctime DESC LIMIT 1";
                $command         = by::dbMaster()->createCommand($sql,[ ':user_id'=>$user_id, ':is_del'=>$is_del]);
            }
            $aData               = $command->queryOne();
            $id                  = empty($aData['id']) ? 0 : $aData['id'];
            by::redis('core')->set($r_key,$id,['EX'=>empty($id) ? 10 : 3600]);
        }

        if (empty($id)) {
            return [];
        }
        return $this->GetOneAddress($user_id, $id, $can_name);
    }

    /**
     * @param $data
     * @return array
     * 统一下发地址
     */
    public function SafeInfo($data)
    {
        if (empty($data)) {
            return [];
        }

        return [
            'province'  => $data['province'],
            'city'      => $data['city'],
            'area'      => $data['area'],
        ];
    }

    /**
     * @param $user_id
     * @param $aid
     * @return array
     * @throws \yii\db\Exception
     * 设置切换用户的默认收货地址
     */
    public function setDefaultAddress($user_id, $aid)
    {
        // 查询出用户当前默认的收货地址id
        $old_default    = $this->GetDefaultAddress($user_id);
        $transaction    = by::dbMaster()->beginTransaction();
        $tb             = self::tbName($user_id);
        $command        = by::dbMaster()->createCommand();
        try {
            // 先全部置为0
            $command->update(
                $tb,
                ['status' => 0],
                "`user_id`=:user_id",
                [':user_id' => $user_id]
            )->execute();
            // 设置默认地址
            $command->update(
                $tb,
                ['status' => 1],
                "`id`=:aid AND `user_id`=:user_id",
                [':user_id' => $user_id, ':aid' => $aid]
            )->execute();

            //删除缓存
            $this->__delListCache($user_id);
            $this->__delCache($user_id, $aid);
            $old_default && $this->__delCache($user_id, $old_default['id']);
            $transaction->commit();

            return [true, 'ok'];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [false, '操作失败'];
        }
    }

    /**
     * @param $user_id
     * @param $id
     * @param $code
     * @return array
     * @throws \yii\db\Exception
     * 绑定crm——code
     */
    public function upCode($user_id,$id,$code){
        $user_id = CUtil::uint($user_id);
        $id = CUtil::uint($id);
        $tb             = self::tbName($user_id);
        by::dbMaster()->createCommand()->update($tb,
            ['crm_code'=>$code],
            ['id'=>$id,'user_id'=>$user_id]
        )->execute();


        $this->__delCache($user_id,$id);
        return [true, 'ok'];
    }
}
