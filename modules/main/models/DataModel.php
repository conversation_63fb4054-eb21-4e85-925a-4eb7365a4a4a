<?php

namespace app\modules\main\models;

use app\components\AliYunOss;

use app\models\CUtil;


class DataModel extends CommModel
{

    CONST UPLOAD_NUM        = 15;
    const UPLOAD_NUM_BY_TYPE = [
        'COMM'       => 15,
        'OLD_TO_NEW' => YII_ENV_PROD ? 100 : 1000,
    ];

    /**
     * 登录用户，通用图片上传接口
     * @param $user_id
     * @param $file
     * @param $arr
     * @return array
     */
    public function DataFileSave($user_id,$file,$arr): array
    {
        if(empty($file) || empty($user_id)) {
            return [false,"参数错误~"];
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__,$user_id,json_encode($arr));

        $type = strtoupper($arr['type'] ?? 'COMM');

        $limitNums = self::UPLOAD_NUM_BY_TYPE[$type] ?? self::UPLOAD_NUM;

        list($s)    = self::AccFrequency($user_id,$unique_key,43200,"EX",$limitNums);
        if (!$s) {
            CUtil::json_response(-1,'图片上传达到限制，明天再来吧！');
        }

        //图片压缩没有文件名处理
        $fileType = $file['type'] ?? '';
        $fileType = strtolower(substr($fileType,strrpos($fileType,'/')+1)); //得到文件类型，并且都转化成小写
        if($fileType && in_array($fileType,AliYunOss::getAllowType())){
            $file['name'] = $user_id . intval(START_TIME) . rand(0, 1000) . '.' . $fileType;
        }

        //文件上传
        list($status,$ret) = AliYunOss::factory()->uploadFileToOss($file, AliYunOss::IMG_TYPE, true, 2);
        if(!$status) {
            return [false,$ret];
        }

        return [true, $ret];
    }
}
