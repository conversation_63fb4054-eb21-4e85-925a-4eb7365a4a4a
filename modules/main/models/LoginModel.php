<?php
/**
 * Created by Ph<PERSON><PERSON>torm.
 * User: Kevin
 * Date: 2018/5/9
 * Time: 19:02
 */

namespace app\modules\main\models;

use app\components\AliApplet;
use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\JwtTools;
use app\components\Mall;
use app\models\by;
use app\models\CUtil;

class LoginModel extends CommModel
{

    public static $expire = YII_ENV_PROD ? 86400 : 86400;

    /**
     * @param int $user_id
     * @return string
     * session key 标准
     */
    public function sessionKey($user_id=0): string
    {
        return AppCRedisKeys::sessionKey($user_id);
    }

    public function appLoginKey($jwtToken): string
    {
        return AppCRedisKeys::AppLoginKey($jwtToken);
    }

    public function delAppLoginKey($jwtToken)
    {
        $r_key = $this->appLoginKey($jwtToken);
        by::redis('core')->del($r_key);
    }


    private function __delCache($user_id){
        $r_key = $this->sessionKey($user_id);
        by::redis('core')->del($r_key);
    }

    public function deleteRedisCache($user_id)
    {
        $this->__delCache($user_id);
    }


    /**
     * @param $user_id
     * @return string
     * 弹窗key
     */
    private function __getAppRegisterTag($user_id): string
    {
        return AppCRedisKeys::getAppRegisterTag($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 弹窗key
     */
    public function __getH5RegisterTag($user_id): string
    {
        return AppCRedisKeys::getH5RegisterTag($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 获取用户退出态
     */
    public function __getUserLogoutStatus($user_id): string
    {
        return AppCRedisKeys::userLogoutStatus($user_id);
    }

    public function __delUserLogoutStatus($user_id)
    {
        $redisKey = AppCRedisKeys::userLogoutStatus($user_id);
        by::redis()->del($redisKey);
    }
    /**
     * @param $user_id
     * @return string
     * 获取用户session
     */
    public function GetSessId($user_id): string
    {
        $redis       = by::redis('core');
        $session_key = $this->sessionKey($user_id);
        return $redis->hget($session_key, 'sessid');
    }

    /**
     * @param int $user_id
     * @param string $session_id
     * @return array
     * 验证用户是否登陆 并对已登陆的用户自动续期
     */
    public function checkLogin($user_id = 0, $session_id = ''): array
    {
        $redis        = by::redis('core');
        $session_key  = $this->sessionKey($user_id);
        $osession_id  = $this->GetSessId($user_id);

        if ($session_id === $osession_id) {
            $ttl = $redis->ttl($session_key);
            if ($ttl < 0) {
                return [false, 0];
            }

            $this->redisKeyRenewal($session_key, self::$expire * 7);
            return [true, $ttl];
        }

        return [false, 0];
    }

    /**
     * @param string $type
     * @param string $redis_key
     * @param int $expire
     * @return mixed
     * redis key 续期
     */
    public function redisKeyRenewal($redis_key = "", $expire = 0, $type = 'expire')
    {
        $redis  = by::redis('core');
        $expire = $expire ? intval($expire) : by::users()::$expire;
        if ($type == 'expire') {
            $redis->expire($redis_key, $expire);
        } else {
            $redis->EXPIREAT($redis_key, $expire);
        }

        return $redis->ttl($redis_key);
    }

    /**
     * @param string $user_id
     * @param array $aData
     * @param bool $reset
     * @return array
     * session 初始化
     */
    public function initUserLoginTarget(string $user_id = '', array $aData = [], bool $reset = true): array
    {
        $redis       = by::redis('core');
        $session_key = $this->sessionKey($user_id);
        $expiredTime = self::$expire;
        $hMSet       = [];
        if ($reset) {
            $redis->del($session_key);
            $session_id  = uniqid(substr($user_id, -4)) . mt_rand(10000, 99999);
            $expiredTime = $aData['expireTime'] ?? self::$expire;
            $expiredTime = $expiredTime >= self::$expire ? self::$expire : $expiredTime;
            $session_id  = md5($session_id);
            $hMSet['sessid'] = $session_id;
        } else {
            $session_id = $redis->hGet($session_key, 'sessid');
        }

        if (!empty($aData)) {
            unset($aData['expireTime']);
            $hMSet = array_merge($hMSet, $aData);
        }

        if (empty($hMSet)) {
            return [false, 'session初始化失败'];
        }

        $redis->hMSet($session_key, $hMSet);
        $this->redisKeyRenewal($session_key, $expiredTime, 'expire');

        return [true, $session_id];
    }

    /**
     * @param int $userId
     * @param string $token
     * @param bool $isRecord
     * @return bool
     * 注销登陆
     * @throws \RedisException
     */
    public function LogOut(int $userId = 0, string $token = '', bool $isRecord = false): bool
    {
        // 1. 将 Token 加入黑名单
        JwtTools::factory()::addTokenToBlacklist($userId, $token);

        if ($isRecord) {
            // 2. 标记用户为退出状态
            $redisKey  = $this->__getUserLogoutStatus($userId);
            $redis     = by::redis();
            $setStatus = $redis->set($redisKey, 1);

            if ($setStatus === false) {
                CUtil::debug("Redis 设置退出状态失败：redisKey={$redisKey}", 'err.LogOut');
                return false;
            }
        }


        return true;
//        $session_key = $this->sessionKey($user_id);
//        return by::redis('core')->del($session_key);
    }

    /**
     * @param int $sex
     * @return mixed
     * 按性别获取头像
     */
    public function getDefaultImgBySex($sex = 1)
    {
        $sex    = $sex == by::users()::USER_SEX['W'] ? $sex : by::users()::USER_SEX['M'];
        $config = CUtil::getConfig('default', 'common', MAIN_MODULE);
        return 0;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function loginByApp($jwtToken): array
    {

        $redis      = by::redis('core');
        $appLoginKey = $this->appLoginKey($jwtToken);
        $aJson       = $redis->get($appLoginKey);
        $aData       = (array)json_decode($aJson,true);

        if(empty($aData)) {
            //高并发限制
            $unique_key = __FUNCTION__ . "|{$jwtToken}";
            list($anti) = self::ReqAntiConcurrency(0, $unique_key, 3, 'EX');
            if (!$anti) {
                return [false, '重复多次登录请求！'];
            }

            //1.解析jwtToken
            list($status,$registerData) = Mall::factory()->centerParseToken($jwtToken);
            if(!$status||!is_array($registerData)){
                return [false,$registerData];
            }
            //2.解码数据
            list($status,$registerData) = Mall::factory()->_decryptData($registerData,1,[],0,1);
            if(!$status){
                return [false,$registerData];
            }
            //3.注册用户
            list($status,$userInfo) = by::usersMall()->saveMallInfo($registerData,1);
            if(!$status){
                return [false,$userInfo];
            }
            //4.同步crm
            if(isset($userInfo['userId'])){
                // Crm::factory()->push($userInfo['userId'], 'user', ['user_id' => $userInfo['userId']]);
                // 注册会员信息同步IOT、CRM
                Mall::factory()->push($userInfo['userId'],'centerRegister', ['user_id' => $userInfo['userId']]);
            }
            //9.用户登录
            $realUserInfo = $userInfo['userInfo'] ?? '';
            $aData   = empty($realUserInfo) ? [] : $realUserInfo;

            if($aData){
                $redis->set($appLoginKey, json_encode($aData));
                CUtil::ResetExpire($appLoginKey);
                //清除并发限制
                self::ReqAntiConcurrency(0, $unique_key, 0, 'DEL');
            }else{
                return [false,'用户登录失败！'];
            }
        }
        //todo 登录先不给弹窗状态 my/info 里面先做
        $aData['isAppRegister'] = 0;
        return [true,$aData];


//        //6.判断有无新人礼
//        $userDetail     = by::users()->getOneByUid($userInfo['userId']);
//        $isNewGift = empty($userDetail['is_new_gift']??0)?1:0;


//        $r_key = $this->__getAppRegisterTag($userInfo['userId']);
//        $realUserInfo['isAppRegister'] = $this->__ifAppRegisterUser($isNewGift,$userInfo['userId'],$r_key);

    }


    /**
     * @param $isNewGift
     * @param $userId
     * @param $r_key
     * @return int
     * 获取APP是否优惠券弹窗
     */
    public function ifAppRegisterUser($isNewGift,$userId,$r_key): int
    {
       $isAppRegister = 0;
       if($isNewGift && $userId){
           $redis = by::redis('core');
           $aJson = $redis->get($r_key);
           $data  = (array)json_decode($aJson, true);
           $currentTime = time();

           if ($aJson === false) {
              //没有缓存创建缓存
//               $data = ['ctime'=>$currentTime];
               $isAppRegister = 1;
           }else{
              //有缓存进行判断
               $ctime = $data['ctime']??'';
               if(empty($ctime)||$ctime>$currentTime){
//                   $data = ['ctime'=>$currentTime];
                   $isAppRegister = 1;
               }else{
                   //获取ctime当天24点的时间戳
                   $ctimeLast = strtotime(date('Y-m-d', strtotime(' +1 day')));
                   if($currentTime >= $ctimeLast){
//                       $data = ['ctime'=>$currentTime];
                       $isAppRegister = 1;
                   }
               }
           }
//           by::redis('core')->set($r_key, json_encode($data), empty($data) ? 10 : 3600*24);
       }
       return $isAppRegister;
    }

    /**
     * @param $r_key
     * @return void
     * 锁住弹窗直到第二天
     */
    public function setAppRegisterLock($r_key)
    {
        $currentTime = time();
        $data = ['ctime'=>$currentTime];
        by::redis('core')->set($r_key, json_encode($data), empty($data) ? 10 : 3600*24);
    }


    public function loginByAlipay($authCode,$userType)
    {
        if(empty($authCode) || empty($userType) || $userType != 20){
            return [false,'参数错误！'];
        }
        try {
            list($status, $alipayUser) = AliApplet::GetUserAuthToken($authCode);
            if (!$status) {
                return [false, $alipayUser];
            }
            $aliUserId = $alipayUser['user_id'] ?? '';
            $openId = $alipayUser['open_id'] ?? '';
            $accessToken = $alipayUser['access_token'] ??'';
            if(empty($openId) || empty($accessToken)){
                return [false, '支付宝授权失败！'];
            }
            // 1.判断用户是否已经是会员
            $userId = by::UsersPlatformModeModel()->GetUserIdByOpenUdId($openId, $userType);
            $user_info = null;
            if (!empty($userId) && strlen($userId) < 15) {
                $user_info = by::users()->getOneByUid($userId);
            }

            if (empty($user_info)) {
                //2.从游客表里面获取用户信息
                $user_info = by::Rusers()->getOneByOpenUdId($openId, $userType);
            }    

            if (empty($user_info)) {
               //a.注册游客（获取游客数据）
            //    list($status, $alipayUserInfo) = AliApplet::GetUserInfoByAccessToken($accessToken);
               $alipayUserInfo = [];
               $gender = $alipayUserInfo['gender'] ??'';
               $userInfo = [
                 'user_type'=> $userType,
                 'openudid'=> $openId,
                 'unionid'=> $aliUserId,
                 'nick'=> $alipayUserInfo['nick_name'] ?? '',
                 'avatar'=> $alipayUserInfo['avatar'] ?? '',
                 'sex'=> empty($gender) ? 0 : ($gender == 'm' ? 1 : 2),
                ];
               //b.注册用户
               list($status, $user_info) = by::Rusers()->register($userInfo);
               $isRegister                = $user_info['isRegisterFlag'];
               $user_info                 = by::Rusers()->getOneByUid($user_info['user_id']);//重新获取一下用户信息
               $user_info['isRegister']   = $isRegister; //标示用户是否刚刚注册
               $user_info['isTodayFirst'] = 1;//新注册自然是当天第一次登陆
               $user_info['user_type']    = $userType;
            }           
            return [true,$user_info];
        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }

}
