<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/3/29
 * Time: 17:06
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Prometheus;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\ActiveRecord;
use yii\db\Connection;

class CommModel extends ActiveRecord {

    // 删除状态
    const IS_DEL = ['no' => 0, 'yes' => 1];

    public static function getDb()
    {
        return by::dbMaster();
    }

    /**
     * @param array $arr
     * @param string $security_key
     * @param string $salt
     * @param string $ext
     * @return string
     * 返回MD5签名
     */
    public static function getSign($arr=[],$security_key='',$salt='',$ext='&'): string
    {
        $target_str = self::kV2String($arr,$security_key,$salt,$ext);
        !YII_ENV_PROD && CUtil::debug($target_str,__FUNCTION__);

        return md5($target_str);
    }

    /**
     * @param $arr
     * @param $security_key
     * @param string $salt
     * @param string $ext
     * @return string
     * 返回一维数组 key-value 待签名字符串
     */
    public static function kV2String($arr=[],$security_key='',$salt='',$ext='&'): string
    {
        $arr['security_key'] = empty($salt) ? $security_key : "{$salt}|{$security_key}";

        //对关联数组按照键名进行升序排序：
        ksort($arr,SORT_STRING); //SORT_STRING - 把每一项作为字符串来处理。
        $target_Arr = [];
        foreach ($arr as $key => $a) {
            if(!is_array($a)) {
                $target_Arr[] = "{$key}={$a}";
            }
        }

        return implode($ext,$target_Arr);
    }

    /**
     * @param $api
     * @return array
     * 获取api 密钥
     */
    public static function getApiKey($api): array
    {

        $apiKeys = CUtil::getConfig('apiKeys','common',MAIN_MODULE);
        if(!isset($apiKeys[$api])) {
            return [-1,'无效的Api信息'];
        }

        //获取对应密钥
        $security_key = $apiKeys[$api];
        return [1,$security_key];
    }

    /**
     * @param int $user_id : 指定用户
     * @param string $unique_key : key唯一标志
     * @param int $time_out : 时长
     * @param string $opt : EX/PX （秒或者毫秒）
     * @param int $limit : 允许次数上限
     * @return array
     * Redis 限速器
     * 设置【指定时间段内】的访问频率限制 ;比如限制用户10086一分钟访问10次：
     * by::model("COmmModel",MAIN_MODULE)->AccFrequency(10086,"aRedisKey",60,"EX",10);
     * 注意：Redis非数据库容量少且有限，请勿将逻辑存储较长时间或者永久存储！！！
     */
    public function AccFrequency($user_id=0, $unique_key='', $time_out=1, $opt='EX', $limit=1): array
    {
        $user_id    = CUtil::uint($user_id);
        $unique_key = strval($unique_key);
        $time_out   = CUtil::uint($time_out);
        $limit      = CUtil::uint($limit);
        $redis_key  = AppCRedisKeys::AccFrequency($unique_key,$user_id);
        $redis      = by::redis('core');
        $get        = $redis->get($redis_key);
        $opt        = $opt == "EX" ? "EX" : "PX";

        //操作码
        $code       = [
            'OK'        =>  0,   //OK
            'LIMITED'   =>  -10, //已达频率限制
            'INIT_ERR'  =>  -20  //初始化KEY失败
        ];

        //已达频率限制（注意并发情况无法拦截）
        if(intval($get) >= $limit) {
            return [false,"Limited",$code['LIMITED']];
        }

        //key不存在时，设置初始值和有效期
        if($get === false) {
            $redis->multi()->set($redis_key,0,['NX',$opt=>$time_out]);
            $redis->exec();
        }

        //二次验证key是否存在
        $exists = $redis->exists($redis_key);
        if(!$exists) {
            //极端情况所有 执行初始化操作全失败
            return [false,"Init Err",$code['INIT_ERR']];
        }

        $incr = $redis->incr($redis_key);//计数开始

        //已达频率限制（注意可以完全拦截）
        if($incr > $limit) {
            return [false,"Limited",$code['LIMITED']];
        }

        //未达频率限制
        return [true,$incr,$code['OK']];
    }


    /**
     * @param int $user_id : 指定用户
     * @param string $unique_key : key唯一标志
     * @param int $time_out : 时长
     * @param int $limit : 允许次数上限
     * @return array
     * @throws RedisException
     */
    public function frequencyLimitation(int $user_id = 0, string $unique_key = '', int $time_out = 1, int $limit = 1): array
    {
        $user_id    = CUtil::uint($user_id);
        $unique_key = strval($unique_key);
        $time_out   = CUtil::uint($time_out);
        $limit      = CUtil::uint($limit);
        $redis_key  = AppCRedisKeys::AccFrequency($unique_key, $user_id);
        $redis      = by::redis('core');
        $get        = $redis->get($redis_key);

        // 操作码
        $code = [
                'OK'       => 0,   // OK
                'LIMITED'  => -10, // 已达频率限制
                'INIT_ERR' => -20  // 初始化 KEY 失败
        ];

        // 已达频率限制
        if (intval($get) >= $limit) {
            return [false, "Limited", $code['LIMITED']];
        }

        // Key 不存在时，设置初始值和有效期
        if ($get === false) {
            $script = "
            if redis.call('EXISTS', KEYS[1]) == 0 then
                redis.call('SET', KEYS[1], 0, 'EX', ARGV[1])
                return 1
            else
                return 0
            end
        ";
            $result = $redis->eval($script, [$redis_key, $time_out], 1);
            if ($result == 0) {
                return [false, "Init Err", $code['INIT_ERR']];
            }
        }

        // 二次验证 Key 是否存在
        $exists = $redis->exists($redis_key);
        if (!$exists) {
            return [false, "Init Err", $code['INIT_ERR']];
        }

        // 计数开始
        $incr = $redis->incr($redis_key);

        // 已达频率限制
        if ($incr > $limit) {
            return [false, "Limited", $code['LIMITED']];
        }

        // 未达频率限制
        return [true, $incr, $code['OK']];
    }


    /**
     * @param int $user_id
     * @param string $unique_key
     * @param int $time_out
     * @param string $opt
     * @return array
     * 并发请求检测
     */
    public function ReqAntiConcurrency($user_id=0, $unique_key='', $time_out=1, $opt='EX' ): array
    {
        try {
            $unique_key = strval($unique_key);

            $redis_key  = AppCRedisKeys::ReqAntiConcurrency($unique_key,$user_id);
            $return     = [TRUE,'ok'];
            $opt        = strtoupper(trim($opt));
            switch ($opt) {
                case 'DEL' :
                    by::redis('core')->$opt($redis_key);
                    break;
                case 'EX'  :
                case 'PX' :
                    $ret = by::redis('core')->set($redis_key, 1, ['NX', $opt => intval($time_out)]);
                    if(!$ret) {
                        $return =  [FALSE, '并发操作'];
                        break;
                    }

                    break;
                case 'TTL' :
                    $ttl    = by::redis('core')->$opt($redis_key);
                    $return = [TRUE,$ttl];
                    break;
                DEFAULT :
                    $return = [FALSE,"未能理解的操作:{$opt}"];
                    break;
            }

            return $return;

        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
            return [FALSE,$e->getMessage()];
        }

    }


    /**
     * 查找或者保存redis数据
     * @param string $unique_key
     * @param int $time_out
     * @param array $data
     * @return array
     */
    public function saveOrGetRedisData(string $unique_key='', int $time_out=1, array $data=[]): array
    {
        if(empty($data)){
            $aJson = by::redis('core')->get($unique_key);
            $data   = (array)json_decode($aJson,true);
        }else{
            //有数据存储数据
            by::redis('core')->set($unique_key,json_encode($data));
            CUtil::ResetExpire($unique_key,$time_out);
        }
        return [true,$data];
    }

    /**
     * @param string $app ：队列名称
     * @param int $lRange ：队列长度
     * @param array $data ：填入队列的值
     * @return array
     * 记录APP发起的Post请求
     */
    public function AppPostLog($app='step',$lRange=300,$data=[]) {
        $post_log_key = AppCRedisKeys::postLogKey($app);
        if(empty($data)) {
            $data = by::redis('core')->LRANGE($post_log_key,0,$lRange);
        } else {
            $data = by::redis('core')->LPUSH($post_log_key,json_encode($data));
            by::redis('core')->LTRIM($post_log_key,0,$lRange);
        }

        return $data;
    }

    /**
     * @return string
     * @throws \yii\base\InvalidConfigException
     * 获取接口路径
     */
    public static function getProxyPath(): string
    {
        //测试服服走了反向代理
        if(YII_ENV_TEST) {
            $host     = \Yii::$app->request->getHostInfo();
            $url      = \Yii::$app->request->getUrl();
            $proxy    = "RD-".strtolower(PRO_NAME);
            $api_path = "{$host}/{$proxy}{$url}";
        }else {
            $api_path = \Yii::$app->request->getAbsoluteUrl();
        }

        return $api_path;
    }

    /**
     * @param array $ext
     * 请求参数解析
     */
    public function recordPostLog($ext=[]) {

        $model = by::model('MonitorModel',MAIN_MODULE);

        $model->apiQps();//QPS统计

        $data['ext'] = $ext;
        try{
            $data['UserIP']      = CUtil::get_client_ip();
            if (IS_CLI) {
                $data['url']     = "<font color='info'>HOST : CLI命令行模式</font>\n";
                $path_info       = empty($_SERVER["argv"]) ? "" : implode("/",$_SERVER["argv"]);
            } else {
                $data['url']     = self::getProxyPath();
                $data['head']    = \Yii::$app->request->getHeaders();
                $data['body']    = \Yii::$app->request->getBodyParams();
                $data['cookies'] = \Yii::$app->request->cookies->toArray();
                $path_info       = \Yii::$app->request->getPathInfo();
            }

            //防止恶意url
            $data['url']         = mb_substr($data['url'],0,150);

            //接口并发统计
            $model->apiRequest(0,$path_info);

            //总耗时毫秒数统计
            $mstTimeUsed  = CUtil::uint(($ext['end_time']*1000 - $ext['start_time']*1000));
            $model->apiRequestTimeUsed(0,$path_info,$mstTimeUsed);

            //异步prometheus
//            !IS_CLI && !YII_ENV_PROD && CUtil::debug(json_encode(['url'=>$data['url'],'timeUsed'=>$mstTimeUsed],320),'ooolokk');
//            !IS_CLI && Prometheus::factory()->syncPrometheus('interfaceUv',['url'=>$data['url'],'timeUsed'=>$mstTimeUsed]);
//            !IS_CLI && (isset($data['iRet']) && $data['iRet']<=-1) && Prometheus::factory()->syncPrometheus('interfaceUv',['url'=>$data['url'].'/ERR','timeUsed'=>$mstTimeUsed]);
        } catch (\Exception $e){
            $error = "参数解析异常：".$e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error."|".json_encode($data),__FUNCTION__);
        }

        $this->AppPostLog(MAIN_MODULE,300,$data);
    }


	/**
	 *
	 * 获得锁,如果锁被占用,阻塞,直到获得锁或者超时。
	 *-- 1、如果 $timeout 参数为 0,则立即返回锁。
	 *-- 2、建议 timeout 设置为 0,避免 redis 因为阻塞导致性能下降。请根据实际需求进行设置。
	 * @param $key
	 * @param int $lockSecond  锁定时长（秒单位）
	 * @param int $retryNum    重试次数
	 * @param int $sleep       休眠时间 （微秒单位）
	 */
	public static function lock($key,$lockSecond=5,$retryNum=3,$sleep=200000){
		$retryNum = CUtil::uint($retryNum);
		if(strlen($key)==0){
			return false;
		}
		$num = 0;
		do{
			$flag = by::redis('core')->set($key,1,['nx','ex'=>$lockSecond]);
			if($flag){
				break;
			}
			if($retryNum==0){
				break;
			}
			$num++;
			usleep($sleep);
		}while($retryNum>$num);
		return $flag ? true : false;
	}

	/**
	 *释放锁
	 * @param $key
	 * @return bool|int
	 */
	public static function replease($key){
		if(strlen($key)==0){
			return false;
		}
		return by::redis('core')->del($key);
	}



    /**
     * 构建 SQL WHERE 子句
     * @param array $conditions 查询条件数组
     * @param array $params 引用传递的参数数组，用于绑定参数
     * @return string 返回构建的 WHERE 子句字符串
     */
    protected function buildWhereClause(array $conditions, array &$params): string
    {
        $where      = [];  // 初始化一个空数组来存储每个条件生成的 SQL 片段
        $usedParams = [];  // 记录已经使用过的参数名

        // 遍历每个条件
        foreach ($conditions as $condition) {
            // 检查每个条件是否完整，即是否包含字段名、条件和值
            if (!isset($condition['field'], $condition['condition'], $condition['value'])) {
                continue;  // 如果条件不完整，则跳过当前循环
            }

            $field = $condition['field'];  // 获取字段名
            $param = ":{$field}";          // 为字段值创建一个参数占位符

            // 处理IN条件和数组值
            // 处理IN条件和数组值
            if (is_array($condition['value']) && strtoupper(trim($condition['condition'])) == 'IN') {
                $placeholders = [];
                foreach ($condition['value'] as $i => $value) {
                    $paramName = ":{$field}_in_{$i}";  // 创建唯一参数名
                    $placeholders[] = $paramName;
                    $params[$paramName] = $value;  // 添加参数值
                }
                $paramStr = implode(',', $placeholders);  // 不需要引号包裹，因为它们是占位符
                $where[] = "`{$field}` IN ({$paramStr})";  // 构建IN条件
            } elseif (is_array($condition['value']) && strtoupper(trim($condition['condition'])) == 'NOT_IN') {
                $placeholders = [];
                foreach ($condition['value'] as $i => $value) {
                    $paramName = ":{$field}_not_in_{$i}";  // 创建唯一参数名
                    $placeholders[] = $paramName;
                    $params[$paramName] = $value;  // 添加参数值
                }
                $paramStr = implode(',', $placeholders);  // 不需要引号包裹，因为它们是占位符
                $where[] = "`{$field}` NOT IN ({$paramStr})";  // 构建IN条件
            } else {
                if (isset($usedParams[$param])) {
                    continue;  // 如果非IN条件参数已使用，则跳过
                }
                $usedParams[$param] = true;
                $where[] = "`{$field}` {$condition['condition']} $param";
                $params[$param] = $condition['value'];  // 将参数值存储到 params 数组中
            }
        }

        // 将所有条件使用 "AND" 连接起来，形成完整的 WHERE 子句
        return $where ? ' WHERE ' . implode(' AND ', $where) : '';
    }



}
