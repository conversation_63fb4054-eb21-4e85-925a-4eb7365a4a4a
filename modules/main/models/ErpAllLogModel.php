<?php


namespace app\modules\main\models;

use app\components\ErpNew;
use app\models\by;
use app\models\CUtil;
use yii\db\Expression;


class ErpAllLogModel extends CommModel
{
    public static function tableName()
    {
        return "`db_dreame_log`.`t_erp_all_log`";
    }


    public function saveLog($function, $rags, $msg, $time = null, $utime = null): array
    {
        $table = $this->tableName();
        $data  = [
            'function' => $function,
            'rags'     => json_encode($rags,320),
            'msg'      => json_encode($msg,320),
            'ctime'    => $time ?: time(),
            'utime'    => $utime ?: time(),
        ];

        by::dbMaster()->createCommand()->insert($table, $data)->execute();
        return [true, 'ok'];
    }

}
