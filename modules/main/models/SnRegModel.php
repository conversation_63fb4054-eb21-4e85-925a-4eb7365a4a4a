<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class SnRegModel extends CommModel
{

    public static function getTable(): string
    {
        return "`db_dreame_log`.`t_sn_reg`";
    }


    /**
     * @param $user_id
     * @param $sn
     * 新增一条sn记录
     */
    public function add($user_id, $sn)
    {
        $user_id = CUtil::uint($user_id);
        $sn = trim(strval($sn));
        if (empty($user_id) || empty($sn)) {
            return [false, '缺少必要参数'];
        }

        $data = [
            'user_id' => $user_id,
            'sn' => $sn,
            'ctime' => Intval(START_TIME),
        ];

        $tb = self::getTable();

        $row = by::dbMaster()->createCommand()->insert($tb, $data)->execute();

        if (!$row) {
            return [false, 'sn_reg新增失败'];
        }

        return [true, 'ok'];
    }

    /**
     * @param $sn
     * @return array
     * @throws Exception
     * 验证唯一性
     */
    public function uniqueCheck($sn): array
    {
        $sn = trim(strval($sn));

        $tb = self::getTable();
        $sql = "SELECT `id` FROM {$tb} WHERE `sn` = :sn LIMIT 1";
        $info = by::dbMaster()->createCommand($sql, ['sn' => $sn])->queryOne();

        if ($info) {
            return [false, 'sn编码已存在'];
        }

        return [true, 'ok'];
    }


    /**
     * @param $sn
     * @return array
     * @throws Exception
     * 获取sn信息
     */
    public function getInfoBySn($sn)
    {
        $sn = trim(strval($sn));

        $tb   = self::getTable();
        $sql  = "SELECT * FROM {$tb} WHERE `sn` = :sn LIMIT 1";
        $info = by::dbMaster()->createCommand($sql, ['sn' => $sn])->queryOne();
        return empty($info) ? [] : $info;
    }

    /**
     * @param $before_sn
     * @param $sn
     * @throws Exception
     * 后台手动改sn码
     */
    public function updateSn($before_sn, $sn)
    {
        $tb = self::getTable();
        by::dbMaster()->createCommand()->update($tb, ['sn' => $sn], ['sn' => $before_sn])->execute();
    }
}
