<?php


namespace app\modules\main\models;

use app\components\AdvAscribe;
use app\models\by;
use app\models\CUtil;
use yii\db\Expression;


class AdvAscribeModel extends CommModel
{
    public function GetRefererParam($referer='')
    {
        $data = [
            'union'    => '',
            'click_id' => ''
        ];
        if($referer){
            $refer_url = parse_url($referer);
            $params = $refer_url['query'] ?? '';
            $arr = [];
            if (!empty($params)) {
                $paramsArr = explode('&', $params);
                foreach ($paramsArr as $k => $v) {
                    $a          = explode('=', $v);
                    $arr[$a[0]] = $a[1];
                }
            }
            foreach (['qz_gdt','gdt_vid'] as $item){
                $value = $arr[$item] ?? '';
                if($value){
                    $data['union'] = 'tencent_'.$item;
                    $data['click_id'] = $value;
                    break;
                }
            }
        }
        return $data;
    }

}
