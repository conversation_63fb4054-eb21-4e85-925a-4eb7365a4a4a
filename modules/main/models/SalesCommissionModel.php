<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\CUtil;
use yii\db\ActiveQuery;

/**
 * 销售分佣表
 */
class SalesCommissionModel extends CommModel
{

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame`.`sales_commission`";
    }

    public function saveData(array $data)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $db->createCommand()->insert($tb,$data)->execute();
    }

    public function updateStatus($id,$params = []){
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        return $db->createCommand()->update($tb,$params,['id' => $id])->execute();
    }
    public function patch($where,$params = []){
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        return $db->createCommand()->update($tb,$params,$where)->execute();
    }

    public function getInfo($order_no){
        $item = self::find()
            ->where(['order_no' => $order_no])
            ->one();

        return $item ? $item->toArray() : [];
    }

    public function getList($user_id,$page,$pageSize,$activity_id){
        $order = $params['order'] ?? 'ctime';
        $query = self::find();
        $query->where(['referrer' => $user_id,'activity_id'=>$activity_id]);
        $query->andWhere(['status'=>[300,400,500,10000000,20000000]]);
        $list = $query->orderBy([$order => SORT_DESC])
            ->offset(($page - 1) * $pageSize)->limit($pageSize)
            ->asArray()->all();
        $total = $query->count();
        return ['list' => $list, 'total' => $total];
    }

    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        $attrs = $this->getSearchFields();
        $map = [];
        $params = array_filter($params, function ($v) {
            return (!empty($v) || $v === '0' || $v === 0 );
        });
        foreach ($attrs as  $field) {
            if (isset($params[$field])) {
                $map[$field] = $params[$field];
            }
        }
        $query->andWhere($map);
        return $query;
    }

    public function getSearchFields(): array
    {
        return ['id', 'referrer', 'activity_id','user_id','order_no','status', 'commission', 'rate', 'extend', 'ctime', 'utime'];
    }

    public function statistics($user_id,$activity_id){
        $list = self::find()->select('*')
        ->where(['referrer' => $user_id,'activity_id'=>$activity_id])
        ->asArray()->all();
        return $list;
    }

    /**
     * 活动周期内小店是否有下单
     * 按 referrer=用户ID 且 order_no 不为空，ctime 落在 [startTime, endTime]
     */
    public function getOrderInfoInPeriod(int $referrer, int $startTime, int $endTime)
    {
        if ($startTime <= 0 || $endTime <= 0 || $endTime <= $startTime) {
            return 0;
        }

        $consume_money_id = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
        $query = self::find()
            ->where(['referrer' => $referrer])
            ->andWhere(['activity_id' => $consume_money_id]) // 确保状态符合要求
            ->andWhere('order_no is not null')
            ->andWhere('status >= 300') // 确保状态符合要求
            ->andWhere(['between', 'ctime', $startTime, $endTime]);

        return $query->asArray()->one();
    }
}