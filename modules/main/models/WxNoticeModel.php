<?php

/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/7/19
 * Time: 14:23
 * 微信公众号推送
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\WXOA;
use app\models\by;
use app\models\CUtil;
use app\jobs\WxNoticeJob;

class WxNoticeModel extends CommModel
{
    const TYPE  = [
        'REG_USER'            => 1, //用户注册通知
        'REG_PRODUCT'         => 2, //产品注册通知
        'POINT_ADD'           => 3, //积分变更提醒-增加
        'POINT_USE'           => 4, //积分变更提醒-使用
        'POINT_EXP'           => 5, //积分即将过期提醒
        'PLUMBING_COMMENT'    => 6, //上下水工单已完成-评价消息通知
        'DEPOSIT_ORDER_START' => 7, //预售尾款开始支付
        'DEPOSIT_ORDER_END'   => 8, //预售尾款结束支付
        'BIRTHDAY_REMINDER'   => 9, //生日提醒
    ];

    const NOTICE_TPL  = [
        'REG_USER'         => 'hW94kODfigSGeySSDOmrXBCFwap7tWQlrYX37sPy_rU', //用户注册通知
        'REG_PRODUCT'      => '78RttLlf8wiz42GgHV4HMAMyu-7iQpn7K0ivEKXYanY', //产品注册通知-未用
        'POINT_ADD'        => '2VVUDNnWSXXGYhm9gH9YsjSv8VKo121icf0UvL7qVLg', //积分增加
        'POINT_USE'        => '2VVUDNnWSXXGYhm9gH9YsjSv8VKo121icf0UvL7qVLg', //积分使用
        'POINT_EXP'        => 'jUW-xN7m88_udcwQe2x4VaBWsEAcRHnPBUwmptb1Dec', //积分过期
        'PLUMBING_COMMENT' => 'YlbI87xHk3xVl9XgzkqjehiQGq09lg3v7VhLWhC0ezw', //评价消息通知
        'DEPOSIT_ORDER_START' => '', //预售尾款开始支付 通知
        'DEPOSIT_ORDER_END' => '', //预售尾款结束支付 通知
        'BIRTHDAY_REMINDER' => 'AjJmV4BnR988DqL2nANTl-5JEKZ7cVk6DcUPuASE__A', //生日提醒
    ];

    const NOTICE_NEW_TPL  = [
        'REG_USER'         => '48-VT6UfOT8Zd_6SShnArdcHPT8VCKqTxI-0xMe1TN0', //用户注册通知
        'REG_PRODUCT'      => '78RttLlf8wiz42GgHV4HMAMyu-7iQpn7K0ivEKXYanY', //产品注册通知-未用
        'POINT_ADD'        => '2VVUDNnWSXXGYhm9gH9YsjSv8VKo121icf0UvL7qVLg', //积分增加
        'POINT_USE'        => '2VVUDNnWSXXGYhm9gH9YsjSv8VKo121icf0UvL7qVLg', //积分使用
        'POINT_EXP'        => 'jUW-xN7m88_udcwQe2x4VaBWsEAcRHnPBUwmptb1Dec', //积分过期
        'PLUMBING_COMMENT' => 'O22z69oajikUVYc9mz9J8_-AqmFkrmTG6XqUZnCiTg8', //评价消息通知
        'DEPOSIT_ORDER_START' => '', //预售尾款开始支付 通知
        'DEPOSIT_ORDER_END' => '', //预售尾款结束支付 通知
        'BIRTHDAY_REMINDER' => 'AjJmV4BnR988DqL2nANTl-5JEKZ7cVk6DcUPuASE__A', //生日提醒
    ];

    const USER_MAX_LIMIT    = 10; //单用户每天最多推送10条（暂定）

    const STOP_USER_TPL = [
        3,4,5
    ];

    /**
     * @return string
     * 消息队列key
     */
    private function __oaPushListKey(): string
    {
        return AppCRedisKeys::OaPushList();
    }

    /**
     * @param $date
     * @return string
     * 用户当天推送次数key
     */
    private function __oaPushLogKey(): string
    {
        $date   = date('Ymd', intval(START_TIME));
        return AppCRedisKeys::OaPushLog($date);
    }

    /**
     * @param $user_id
     * @param $type
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     * 公众号推送消息
     */
    public function wxOaNoticePush($user_id, $type, $params = [])
    {
        if(!CUtil::wxOaLock($user_id)){
            return [true, 'ok'];//关闭消息推送
        }

        if (!YII_ENV_PROD){
            return [true, 'ok'];//测试服关闭推送
        }

        if (empty($user_id)) {
            return [false, '参数错误'];
        }

        if (!in_array($type, self::TYPE)) {
            return [false, 'type错误'];
        }

        if (in_array($type, self::STOP_USER_TPL)) {
            return [true, 'ok']; //被封禁的公众号停止
        }

        $wxOA       = WXOA::factory();

        $main       = by::users()->getUserMainInfo($user_id);

        if (empty($main['unionid'])) {
            return [false, "{$user_id}无unionid"];
        }

        $focus  = by::OaFocus()->GetALogByUnionid($main['unionid']);
        if (empty($focus['oa_openid']) || $focus['status'] == 0) {
            CUtil::debug("{$user_id}没有关注公众号","wxOaPush");
            return [true, "{$user_id}没有关注公众号"];
        }

        $redis  = by::redis();
        $r_key  = $this->__oaPushLogKey();
        $num    = $redis->hIncrBy($r_key, $user_id, 1);
        $redis->expireAt($r_key, strtotime("today"));

        if ($num > self::USER_MAX_LIMIT) {
            return [false, '已超过最大推送次数'];
        }

        //模板切换
        $noticeTpl = self::NOTICE_TPL;
        if(CUtil::wxOaLock()){
            $noticeTpl = self::NOTICE_NEW_TPL;
        }

        switch ($type) {
            case self::TYPE['REG_USER']:
                $reg_time = date("Y年m月d日 H:i:s", $params['reg_time'] ?? time());
                $data = [
                    'first'     => ['value' => '了解您的会员福利'],
                    'keyword1'  => ['value' => $params['card'] ?? ''],  // 会员卡号
                    'keyword2'  => ['value' => $params['level'] ?? ''], // 会员等级
                    'keyword3'  => '追觅会员官方商城',  // 入会门店
                    'keyword4'  => ['value' => $reg_time],  // 注册时间
                    'remark'    => ['value' => '您的优惠券已到账，点击查看详情。'],
                ];

                $tpl_id     = $noticeTpl['REG_USER'];
                $jump_page  = "/pagesA/coupon/coupon";
                break;

            case self::TYPE['REG_PRODUCT']:
                $data = [
                    'first'     => ['value' => '了解你的追觅神器'],
                    'keyword1'  => ['value' => $params['real_name'] ?? ''],
                    'keyword2'  => ['value' => $params['phone'] ?? ''],
                    'remark'    => ['value' => "欢迎使用{$params['p_name']}，您已获得一年延保服务。点击了解更多 产品资讯，尽享追觅客科技带来的生活无限可能。"],
                ];

                $tpl_id     = $noticeTpl['REG_PRODUCT'];
                $jump_page  = "/pages/index/index";
                break;

            case self::TYPE['POINT_ADD']:
                $data = [
                    'first'     => ['value' => '亲爱的觅友，您的积分发生变更'],
                    'keyword1'  => ['value' => $params['card'] ?? ''],
                    'keyword2'  => ['value' => "增加{$params['point']}积分"],
                    'keyword3'  => ['value' => "积分余额{$params['all_point']}"],
                    'remark'    => ['value' => "点击下方链接，查看积分余额。链接：跳转小程序“我的”界面"],
                ];

                $tpl_id     = $noticeTpl['POINT_ADD'];
                $jump_page  = "/pages/mine/mine";
                break;

            case self::TYPE['POINT_USE']:
                $data = [
                    'first'     => ['value' => '亲爱的觅友，您的积分发生变更'],
                    'keyword1'  => ['value' => $params['card'] ?? ''],
                    'keyword2'  => ['value' => "扣减{$params['point']}积分"],
                    'keyword3'  => ['value' => "积分余额{$params['all_point']}"],
                    'remark'    => ['value' => "点击下方链接，查看积分余额。链接：跳转小程序“我的”界面"],
                ];

                $tpl_id     = $noticeTpl['POINT_USE'];
                $jump_page  = "/pages/mine/mine";
                break;

            case self::TYPE['POINT_EXP']:
                $data = [
                    'first'     => ['value' => '亲爱的觅友，您的积分年底就要到期啦！请尽快使用！'],
                    'keyword1'  => ['value' => $params['all_point'] ?? ''],
                    'keyword2'  => ['value' => $params['point']],
                    'keyword3'  => ['value' => $params['date']],
                    'remark'    => ['value' => "您的积分即将失效，速戳会员官方商城购买产品，可使用积分抵现，更多好礼等着你哦。链接：跳转到小程序“首页”。"],
                ];

                $tpl_id     = $noticeTpl['POINT_EXP'];
                $jump_page  = "/pages/index/index";

                break;
            case self::TYPE['PLUMBING_COMMENT']:
                $data = [
                    'first'     => ['value' => '您好，您的上下水服务项目已完成，请对本次服务做出评价'],
                    'keyword1'  => ['value' => $params['order_no'] ?? ''],
                    'keyword2'  => ['value' => date("Y-m-d H:i", $params['finish_time'] ?? time())],
                    'remark'    => ['value' => "感谢您的关注与支持"],
                ];

                $tpl_id     = $noticeTpl['PLUMBING_COMMENT'];
                $jump_page  = "/pagesA/evaluation/evaluation?type={$params['order_type']}&order_no={$params['order_no']}";

                break;

            case self::TYPE['DEPOSIT_ORDER_START']:
                $data = [
                    'first'     => ['value' => '尾款支付提醒'],
                    'keyword1'  => ['value' => $params['order_no'] ?? ''],
                    'remark'    => ['value' => "你所预定{$params['good_name']}已经开启尾款支付时间，快去结清尾款吧！"],
                ];

                $tpl_id     = $noticeTpl['DEPOSIT_ORDER_START'];
                $jump_page  = "pages/orderDetail/orderDetail?order_no={$params['order_no']}";

                break;
            case self::TYPE['DEPOSIT_ORDER_END']:
                $data = [
                    'first'     => ['value' => '尾款支付提醒'],
                    'keyword1'  => ['value' => $params['order_no'] ?? ''],
                    'remark'    => ['value' => "距离你所预定{$params['good_name']}尾款结束支付仅剩{$params['pay_remain_time']}小时，快去结清尾款吧！"],
                ];

                $tpl_id     = $noticeTpl['DEPOSIT_ORDER_START_END'];
                $jump_page  = "pages/orderDetail/orderDetail?order_no={$params['order_no']}";

                break;
            case self::TYPE['BIRTHDAY_REMINDER']:
                $data = [
                    'thing1'  => ['value' => '追觅科技祝您生日快乐！'],
                    'phrase7' => ['value' => '惊喜派送中'],
                    'thing10' => ['value' => '追觅心享官'],
                ];

                $tpl_id     = $noticeTpl['BIRTHDAY_REMINDER'];
                $level      = $params['level'] ?? '';
                $jump_page  = "/pagesA/birthday/birthday?level={$level}";

                break;
            default:
                return [false, '通知类型错误'];
        }

        list($status, $ret) = $wxOA->templatePush($focus['oa_openid'], $tpl_id, $data, $jump_page);

        if (!$status) {
            return [false, $ret];
        }
        return [true, 'ok'];
    }

    /**
     * @param $data
     * @return bool
     * 加入推送队列
     */
    public function AddPush($data)
    {
        if (empty($data)) {
            return false;
        }
        //注释下面redis，改用supervisors维护进程
        \Yii::$app->queue->push(new WxNoticeJob(['user_id' => $data['user_id'], 'type' => $data['type'], 'data' => $data]));

        // $r_key = $this->__oaPushListKey();
        // $redis = by::redis();

        // $redis->rPush($r_key, json_encode($data));

        // $redis->expire($r_key, 86400);

        return true;
    }

    /**
     * 废弃
     * @throws \yii\db\Exception
     * 队列推送
     */
    public function OaPush()
    {
        try {
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $redis_key  = $this->__oaPushListKey();
            $exist_key  = AppCRedisKeys::ProcessExistKey("wxOaPush");
            $start_time = START_TIME;

            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);
            while (true) {
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'wxOaPush');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";
//                    $command = " ps aux | grep dreame | grep 'yii' | grep '/service/data/wxoa-push' | grep -v grep | awk '{print $2}' | xargs kill -15";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'wxOaPush');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);
                CUtil::debug(json_encode($ret),'link-crm-notice');
                $message = $ret[1] ?? "";
                if(!is_string($message) || empty($message)) {
                    CUtil::debug('continue:'. var_export($message, true), "err.wxOaPush");
                    continue;
                }

                $aData = json_decode($message,true);
                if(empty($aData)) {
                    CUtil::debug('continue:'. var_export($message, true), "err.wxOaPush");
                    continue;
                }

                list($status, $msg) = $this->wxOaNoticePush($aData['user_id'], $aData['type'], $aData);
                if (!$status) {
                    CUtil::debug("{$msg}|{$message}","err.wxOaPush");
                }
            }

        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
            CUtil::debug($error,'err.wxOaPush');
            exit($error);
        }
    }
}
