<?php

namespace app\modules\main\models;

use app\models\BusinessException;
use app\models\by;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use Throwable;

final class MemberActivityModuleGoodsModel extends CommModel
{
    use Singleton, ModelTrait;

    private $fillable = ['id', 'module_relation_id', 'module_type', 'category_id', 'category_name', 'goods_id', 'goods_name', 'show_name', 'goods_sku', 'goods_image', 'sale_price', 'free_periods', 'trade_subsidy', 'sale_points', 'sort_order', 'status', 'extra', 'create_time', 'update_time', 'delete_time'];

    public static function tableName(): string
    {
        return "`db_dreame_goods`.`member_activity_module_goods`";
    }
    
    /**
     * 新增数据
     * @param array $data
     * @return array
     */
    public function doCreate(array $data): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $resp = $db->createCommand()->batchInsert($tb, ['module_relation_id', 'module_type', 'category_id', 'category_name', 'goods_id', 'goods_name', 'show_name', 'goods_sku', 'goods_image', 'sale_price', 'free_periods', 'trade_subsidy', 'sale_points', 'sort_order', 'status', 'extra', 'create_time', 'update_time'], $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('活动商品创建失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 更新数据
     * @param int $id 模块活动商品ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $data['update_time'] = time();
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();

            if (empty($resp)) {
                throw new BusinessException('模块活动商品保存失败');
            }

            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 删除数据
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['delete_time' => time()], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }

            if (empty($resp)) {
                throw new BusinessException('删除活动商品失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 根据活动模块ID获取活动商品列表
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function getListByRelationId(int $moduleRelationId): array
    {
        return self::find()->where(['module_relation_id' => $moduleRelationId, 'delete_time' => 0])->orderBy('sort_order ASC, id ASC')->asArray()->all();
    }
    
    /**
     * 根据活动模块ID获取活动商品IDs
     * @param int $moduleRelationId
     * @return array
     */
    public function getIdsByRelationId(int $moduleRelationId): array
    {
        $res = $this->getListByRelationId($moduleRelationId);
 
        return array_column($res, 'id');
    }

    public function getHiddenByRelationIds(array $moduleRelationIds): array
    {
        $query = self::find();
        
        $query->where(['module_relation_id' => $moduleRelationIds, 'delete_time' => 0, 'status' => 0]);

        return $query->asArray()->all();
    }
}