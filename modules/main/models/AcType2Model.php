<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 17:35
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use yii\db\DataReader;
use yii\db\Exception;

class AcType2Model extends CommModel
{
    CONST EXP = 3600; //缓存时间

    //行为
    const ACTION  = [
        'reg'  => [
            'is_reward'    => 1,
            'reward_point' => YII_ENV_PROD ? 100 : 100
        ],
        'bind' => [
            'is_reward'    => 1,
            'reward_point' => YII_ENV_PROD ? 500 : 20
        ],
        'order' => [
            'reward_type'  => 1,
            'reward_point' => YII_ENV_PROD ? 0 : 100
        ]
    ];

    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame`.`t_ac_type2`";
    }

    public $tb_fields = [
        'id', 'ac_id', 'poster_image', 'share_image', 'is_reg_reward', 'inviter_reg_limit', 'order_min_money', 'is_bind_reward',
        'invitee_bind_limit', 'reward_type', 'rule_note', 'update_time'
    ];

    /**
     * @param $ac_id
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getInfoAcTypeKey($ac_id): string
    {
        return AppCRedisKeys::getInfoAcType2ByAcId($ac_id);
    }

    /**
     * @param $ac_id
     * 缓存清理
     */
    public function delCache($ac_id)
    {
        $r_key1 = $this->__getInfoAcTypeKey($ac_id);

        by::redis('core')->del($r_key1);
    }


    /**
     * @param $ac_id
     * @return array|DataReader
     * @throws Exception
     * 获取指定详情信息
     */
    public function getInfoByAcId($ac_id)
    {
        $ac_id = CUtil::uint($ac_id);
        if ($ac_id == 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getInfoAcTypeKey($ac_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`", $this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `ac_id`=:ac_id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':ac_id' => $ac_id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }

    /**
     * 更新或插入数据
     * @param $ac_id
     * @param $data
     * @throws Exception
     * @throws \RedisException
     */
    public function saveLog($ac_id, $data)
    {
        $tb = self::tbName();
        if ($ac_id) { // 更新
            by::dbMaster()->createCommand()->update($tb, $data, ['ac_id' => $ac_id])->execute();
        } else {      // 添加
            by::dbMaster()->createCommand()->insert($tb, $data)->execute();
        }
        // 删除缓存
        $this->delCache($ac_id);
    }
}
