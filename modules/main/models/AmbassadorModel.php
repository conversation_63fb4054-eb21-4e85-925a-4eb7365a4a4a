<?php

namespace app\modules\main\models;

use yii\db\ActiveQuery;

class AmbassadorModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame`.`t_user_employee`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function getInfoById($id)
    {
        return self::find()
            ->where(["id" => $id])
            ->andWhere(["is_delete" => 0])
            ->one();
    }

    public function getInfoByUid($uid, $filter_id = 0)
    {
        return self::find()
            ->where(["uid" => $uid])
            ->andWhere(["<>", "id", $filter_id])
            ->andWhere(["is_delete" => 0])
            ->one();
    }

    public function getAmbassadorQuery($params): ActiveQuery
    {
        $query = self::find()->alias("a")
            ->innerJoin("t_phone p", "a.user_id = p.user_id")
            ->where(["a.is_delete" => 0]);

        // 姓名查询
        if (!empty($params["name"])) {
            $query->andWhere(["a.name" => $params["name"]]);
        }

        // UID查询
        if (!empty($params["uid"])) {
            $query->andWhere(["a.uid" => $params["uid"]]);
        }

        // 等级查询
        if (!empty($params["level"])) {
            $query->andWhere(["a.level" => $params["level"]]);
        }

        // 手机号码查询
        if (!empty($params["phone"])) {
            $query->andWhere(["p.phone" => $params["phone"]]);
        }

        return $query;
    }

    public function getAmbassadorCount($params)
    {
        $query = $this->getAmbassadorQuery($params);
        return $query->count();
    }

    public function getAmbassadorList($params, $offset = 0, $limit = 0): array
    {
        $query = $this->getAmbassadorQuery($params);
        return $query->offset($offset)->limit($limit)
            ->select([
                "a.id", "a.uid", "a.user_id", "p.phone", "a.level",
                "a.name", "a.id_card", "a.bank_name", "a.bank_card", "a.contact_name",
                "a.update_level_month"
            ])
            ->orderBy("a.id DESC")
            ->asArray()
            ->all();
    }
}