<?php

namespace app\modules\main\models;

use app\models\by;

/**
 * 微笑大使
 */
class UserSmileModel extends CommModel
{

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame`.`t_user_smile`";
    }

    public function saveData(array $data): bool
    {
        // 获取用户记录
        $user = self::findOne(['uid' => $data['uid']]);

        if ($user === null) {
            // 检查必需字段是否存在
            if (empty($data['uid'])) {
                return false;
            }

            // 如果用户不存在，创建新的记录
            $user              = new self();
            $user->uid         = $data['uid'];
            $user->ctime       = time(); // 设置创建时间
        }

        // 更新或设置员工信息字段
        if (isset($data['level'])) {
            $user->level = $data['level'];
        }
        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        }
        if (isset($data['is_tip'])) {
            $user->is_tip = $data['is_tip'];
        }

        $user->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$user->save(false)) {
            return false;
        }

        return true;
    }
    // 获取员工信息
    public function getInfo(string $uid,string $field = 'uid'): array
    {
        $item = self::find()
            ->where([$field => $uid])
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->one();

        return $item ? $item->toArray() : [];
    }

    public function updateInfo($value,string $field,array $params){
        $db = by::dbMaster();
        $tb = self::tbName();
        return $db->createCommand()->update($tb, $params, "`$field`=:field", [":field" => $value])->execute();
    }

}