<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 17:35
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use yii\db\DataReader;
use yii\db\Exception;

class AcType3Model extends CommModel
{
    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame`.`t_ac_type3`";
    }

    public $tb_fields = [
        'id','ac_id','ac_image','middleware_image','back_image','rule_note','remark','url','url_type','reward_type','create_time','update_time'
    ];

    /**
     * @param $ac_id
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getInfoAcTypeKey($ac_id): string
    {
        return AppCRedisKeys::getInfoAcType3ByAcId($ac_id);
    }


    /**
     * @param $ac_id
     * @return array|false|DataReader
     * @throws Exception
     * 获取指定详情信息
     */
    public function getInfoByAcId($ac_id)
    {

        $ac_id = CUtil::uint($ac_id);
        if ($ac_id == 0) {
            return [];
        }
        $redis     = by::redis('core');
        $redis_key = $this->__getInfoAcTypeKey($ac_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`", $this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `ac_id`=:ac_id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':ac_id' => $ac_id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    public function updateUrlByAcId($ac_id,$update)
    {
        $ac_id = CUtil::uint($ac_id);
        if ($ac_id == 0) {
            return false;
        }
        $tb      = $this->tbName();
        $result = by::dbMaster()->createCommand()->update($tb,
            $update,
            ['ac_id' => $ac_id]
        )->execute();
        if ($result){
            return true;
        }else{
            return false;
        }
    }

    /**
     * 更新或插入数据
     * @param $ac_id
     * @param $data
     * @throws Exception
     * @throws \RedisException
     */
    public function saveLog($ac_id, $data)
    {
        $tb = self::tbName();
        if ($ac_id) { // 更新
            by::dbMaster()->createCommand()->update($tb, $data, ['ac_id' => $ac_id])->execute();
        } else {      // 添加
            by::dbMaster()->createCommand()->insert($tb, $data)->execute();
        }
        // 删除缓存
        $this->delAcTypeCache($ac_id);
    }

    /**
     * @param $ac_id
     * @return void
     * @throws \RedisException
     * 清除缓存
     */
    public function delAcTypeCache($ac_id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getInfoAcTypeKey($ac_id);
        $redis->del($redis_key);
    }
}
