<?php
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\models\Response;
use yii\db\Exception;

class ProductDetailModel extends CommModel
{

    public static function getTable($time=null): string
    {
        $time = $time ?: START_TIME;
        $year = strlen($time) == 4 ? $time : date("Y",intval($time));
        return "`db_dreame_log`.`t_p_reg_detail_{$year}`";
    }

    private function __getProductDetailList() {
        return AppCRedisKeys::getProductDetailList();
    }

    private function __delCache(){
        $r_key = $this->__getProductDetailList();
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $str
     * 导出csv 字符串处理
     */
    private function __exportStr($str){
        //去除，和空格
        $str = str_replace(',','*',$str);
        $str = str_replace(' ','',$str);
        return trim($str)."\t";
    }

    CONST YEAR = 2022;

    /**
     * @param $create_time
     * @param $id
     * @return array|false
     * @throws Exception
     * 根据id查询数据
     */
    private function __getOneById($create_time, $id)
    {
        $tb     = self::getTable($create_time);
        $sql    = "SELECT `user_id`,`reg_id`,`sn`,`before_sn`,`crm_sync` FROM {$tb} WHERE `id` = :id LIMIT 1";
        $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();

        return $aData;
    }

    public function addRegDetail($user_id,$phone,$reg_id,$product_id,$sn){
        $user_id    = CUtil::uint($user_id);
        $reg_id    = CUtil::uint($reg_id);
        $product_id = CUtil::uint($product_id);
        $phone      = trim(strval($phone));
        $sn         = trim(strval($sn));
        $time       = intval(START_TIME);

        if(empty($user_id) || empty($reg_id) || empty($product_id) || empty($phone) || empty($sn)){
            return [false,'缺少参数'];
        }

        $tb     = $this->getTable($time);

        $data  = [
            'reg_id'        => $reg_id,
            'user_id'       => $user_id,
            'phone'         => $phone,
            'sn'            => $sn,
            'product_id'    => $product_id,
            'create_time'   => $time
        ];

        $row    = by::dbMaster()->createCommand()->insert($tb, $data)->execute();

        if (!$row){
            return [false,'插入detail表失败'];
        }

        $this->__delCache();
        return [true,'OK'];
    }

    public function getRegDetailByRegId($create_time,$reg_id,$sn)
    {
        $tb     = self::getTable($create_time);
        $sql    = "SELECT * FROM {$tb} WHERE `reg_id` = :reg_id and `sn` = :sn LIMIT 1";
        return by::dbMaster()->createCommand($sql, [':reg_id' => $reg_id, ':sn'=>$sn])->queryOne();

    }


    /**
     * @param $create_time
     * @param $id
     * @param $data
     * @return array
     * @throws Exception
     * 编辑数据
     */
    public function updateRegDetail($create_time, $id, $data)
    {
        $create_time    = CUtil::uint($create_time);
        $id             = CUtil::uint($id);

        $need_fields = ['sn', 'crm_sync'];
        foreach($data as $field => $val) {
            if (!in_array($field, $need_fields)) {
                unset($data[$field]);
            }
        }

        if (empty($data)) {
            return [false, '无数据修改'];
        }

        if (empty($create_time) || empty($id)) {
            return [false, '参数错误'];
        }

        $info   = $this->__getOneById($create_time, $id);
        if (empty($info)) {
            return [false, '数据不存在'];
        }

        if (isset($data['sn'])) {
            if (empty($data['sn'])) {
                return [false, 'sn码不能为空'];
            }

            if ($data['sn'] == $info['sn']) {
                unset($data['sn']);
            } else {
                if ($info['crm_sync'] == 1) {
                    $data['before_sn']  = $info['sn'];
                    $data['crm_sync']   = 0;
                }

                //判断此sn码是否占用
                list($status)    = by::model('SnRegModel', 'main')->uniqueCheck($data['sn']);
                if (!$status){
                    return [false,"{$data['sn']}编码已被绑定"];
                }
            }

        }

        if (empty($data)) {
            return [true, 'ok'];
        }

        $db     = by::dbMaster();
        $tb     = self::getTable($create_time);

        $trans  = $db->beginTransaction();

        try {
            by::dbMaster()->createCommand()->update($tb, $data, ['id' => $id])->execute();

            //判断是否改了sn，需要同步到用户产品注册表
            if (isset($data['sn'])) {
                list($s, $m) = by::productReg()->updateSn($info['user_id'], $info['reg_id'], $data['sn'], $info['sn']);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
            }


            $trans->commit();

            $this->__delCache();

            return [true, $info['sn']];
        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getFile().'|'.$e->getMessage(), 'err.reg.edit');

            return [false, '修改失败'];
        }
    }

    /**
     * @param $create_time
     * @param $user_id
     * @param $reg_id
     * @param $crm_sync
     * @return array
     * @throws Exception
     * 修改同步状态
     */
    public function updateByRegId($create_time, $user_id, $reg_id, $crm_sync)
    {
        $create_time       = CUtil::uint($create_time);
        $reg_id     = CUtil::uint($reg_id);
        $user_id    = CUtil::uint($user_id);

        if (empty($create_time) || empty($reg_id) || empty($user_id)) {
            return [false, '参数错误'];
        }

        $tb     = self::getTable($create_time);

        by::dbMaster()->createCommand()->update($tb,
            ['crm_sync' => $crm_sync],
            ['user_id' => $user_id, 'reg_id' => $reg_id]
        )->execute();

        return [true, 'ok'];

    }


    /**
     * @param int $year
     * @param int $user_id
     * @param string $phone
     * @param string $sn
     * @param int $ctime_start
     * @param int $ctime_end
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 列表
     */
    public function getList(int $year,int $user_id,string $phone, string $sn,int $ctime_start=0, int $ctime_end=0,int $page=1, int $page_size=10): array
    {
        if ($year<self::YEAR){
            return [];
        }

        $r_key   = $this->__getProductDetailList();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$year,$user_id,$phone,$sn,$ctime_start,$ctime_end,$page,$page_size);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = $this->getTable($year);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($user_id,$phone,$sn,$ctime_start,$ctime_end);
            $sql                 = "SELECT `id`,`reg_id`,`user_id`,`phone`,`product_id`,`before_sn`,`crm_sync` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $command             = by::dbMaster()->createCommand($sql,$params);
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;
            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key);
        }

        return empty($aData) ? [] : $aData;
    }

    public function getCount(int $year,int $user_id,string $phone, string $sn,int $ctime_start=0, int $ctime_end=0) {
        if ($year<self::YEAR){
            return 0;
        }

        $r_key   = $this->__getProductDetailList();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$year,$user_id,$phone,$sn,$ctime_start,$ctime_end);
        $count   = by::redis('core')->hGet($r_key,$sub_key);

        if($count === false) {
            $tb                  = $this->getTable($year);
            list($where,$params) = $this->__getCondition($user_id,$phone,$sn,$ctime_start,$ctime_end);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key);
        }

        return intval($count);
    }

    /**
     * @param $list
     * @throws \yii\db\Exception
     */
    public function getBackData($list){
        if (empty($list)){
            return [];
        }

        $muserCard = by::userCard();
        $mproduct = by::product();
        $mproductReg = by::productReg();
        $muser = by::users();
        $mmarketConfig = by::marketConfig();
        $return = [];
        foreach ($list as $value){
            $m_name = [];
            $user = $muser->getOneByUid($value['user_id']);
            $product = $mproduct->getOneById($value['product_id']);
            $reg = $mproductReg->getOneById($value['user_id'],$value['reg_id']);
            $market_ids = $muserCard->getListByGetRelation($value['user_id'],UserCardModel::GET_CHANNEL['product'],$value['reg_id']);
            foreach ($market_ids as $m_id){
                $market = $mmarketConfig->getOneById($m_id);
                $m_name[] = $market['name'] ?? '';
            }
            $return[] = [
                'id'            => $value['id'],
                'user_id'       => $value['user_id'] ?? 0,
                'phone'         => $value['phone'] ?? '',
                'nick'          => $user['nick'] ?? '',
                'name'          => $product['name'] ?? '',
                'image'         => $product['image'] ?? '',
                'sn'            => $reg['sn'] ?? '',
                'm_name'        => $product['m_name'] ?? '',
                'create_time'   => $reg['create_time'] ?? '',
                'buy_time'      => $reg['buy_time'] ?? '',
                'is_support_care'  => $reg['is_support_care'] ?? '',
                'care_extend_month'=> $reg['care_extend_month'] ?? '',
                'is_automatic'  => $reg['is_automatic'] ?? '',
                'market'        => $m_name,
                'before_sn'     => $value['before_sn'] ?? '',
                'crm_sync'      => $value['crm_sync']  ?? 0,
            ];
        }

        return $return;
    }

    /**
     * @deprecated gezhiqiang-export-VER2177-3874 版本上线后，将废弃。2023-07-18
     * @param $year
     * @param $user_id
     * @param $phone
     * @param $sn
     * @param $s_time
     * @param $e_time
     * @throws Exception
     * 注册记录导出
     */
    public function export($year=0, $user_id=0, $phone=0, $sn='', $s_time=0, $e_time=0, $viewSensitive=false)
    {
        $headList = [
            '用户ID',
            '用户名称',
            '用户手机号',
            '产品名称',
            '产品SN编码',
            'sku编码',
            '资源配置',
            '购买时间',
            '注册时间',
        ];
        $fileName   = '产品注册记录' . date('Ymd') . mt_rand(1000, 9999);
        $year       = $year ?: date('Y');
        $tb         = self::getTable($year);
        list($where,$params)    = $this->__getCondition($user_id, $phone, $sn, $s_time, $e_time);

        //导出
        CUtil::export_csv_new($headList, function () use($tb, $where, $params, $viewSensitive) {

            $db                 = by::dbMaster();
            $userModel          = by::users();
            $product            = by::product();
            $productReg         = by::productReg();
            $muserCard          = by::userCard();
            $mmarketConfig      = by::marketConfig();
            $id = 0;

            while (true) {

                $sql    = "SELECT `id`,`reg_id`,`user_id`,`sn`,`phone`,`product_id` FROM {$tb} 
                           WHERE `id` > :id AND {$where} ORDER BY `id` ASC LIMIT 100";

                $params[':id'] = $id;
                $list   = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $dataList   = [];
                foreach ($list as $value) {
                    $user           = $userModel->getOneByUid($value['user_id']);
                    $nick           = preg_replace_callback(
                        '/./u',
                        function (array $match) {
                            return strlen($match[0]) >= 4 ? '' : $match[0];
                        },
                        $user['nick']??'');
                    $pro            = $product->getOneById($value['product_id']);
                    $reg            = $productReg->getOneById($value['user_id'],$value['reg_id']);
                    $market_ids = $muserCard->getListByGetRelation($value['user_id'],UserCardModel::GET_CHANNEL['product'],$value['reg_id']);
                    $m_name = [];
                    foreach ($market_ids as $m_id){
                        $market = $mmarketConfig->getOneById($m_id);
                        $m_name[] = $market['name'] ?? '';
                    }

                    $dataList[]         = [
                        'user_id'       => $value['user_id'],
                        'uname'         => $nick,
                        'phone'         => $value['phone']."\t" ?? '',
                        'name'          => $pro['name']."\t",
                        'sn'            => $reg['sn']."\t",
                        'm_name'        => $pro['m_name']."\t",
                        'market'        => !empty($m_name) ? implode(',',$m_name) : '',
                        'buy_time'      => $reg['buy_time'] ? date('Y-m-d',$reg['buy_time'])."\t" : '',
                        'ctime'         => $reg['create_time'] ? date('Y-m-d',$reg['create_time'])."\t" : '',
                    ];
                }

                !$viewSensitive && $dataList = Response::responseList($dataList,['phone'=>'tm']);

                yield $dataList;

            }

        }, $fileName);
    }

    /**
     * @deprecated
     * @param $year
     * @param $user_id
     * @param $phone
     * @param $sn
     * @param $s_time
     * @param $e_time
     * @param $viewSensitive
     * @return array|mixed
     * @throws Exception
     */
    public function exportDataBak($year=0, $user_id=0, $phone=0, $sn='', $s_time=0, $e_time=0, $viewSensitive=false)
    {
        $headList = [
            '用户ID',
            '用户名称',
            '用户手机号',
            '产品名称',
            '产品SN编码',
            'sku编码',
            '资源配置',
            '购买时间',
            '注册时间',
        ];
        $year       = $year ?: date('Y');
        $tb         = self::getTable($year);
        list($where,$params)    = $this->__getCondition($user_id, $phone, $sn, $s_time, $e_time);

        //导出
        $db                 = by::dbMaster();
        $userModel          = by::users();
        $product            = by::product();
        $productReg         = by::productReg();
        $muserCard          = by::userCard();
        $mmarketConfig      = by::marketConfig();
        $id = 0;

        $dataList[]   = $headList;
        while (true) {

            $sql    = "SELECT `id`,`reg_id`,`user_id`,`sn`,`phone`,`product_id` FROM {$tb} 
                       WHERE `id` > :id AND {$where} ORDER BY `id` ASC LIMIT 100";

            $params[':id'] = $id;
            $list   = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $value) {
                $user           = $userModel->getOneByUid($value['user_id']);
                $nick           = preg_replace_callback(
                    '/./u',
                    function (array $match) {
                        return strlen($match[0]) >= 4 ? '' : $match[0];
                    },
                    $user['nick']??'');
                $pro            = $product->getOneById($value['product_id']);
                $reg            = $productReg->getOneById($value['user_id'],$value['reg_id']);
                $market_ids = $muserCard->getListByGetRelation($value['user_id'],UserCardModel::GET_CHANNEL['product'],$value['reg_id']);
                $m_name = [];
                foreach ($market_ids as $m_id){
                    $market = $mmarketConfig->getOneById($m_id);
                    $m_name[] = $market['name'] ?? '';
                }

                $dataList[]         = [
                    'user_id'       => $value['user_id'],
                    'uname'         => $nick,
                    'phone'         => $value['phone']."\t" ?? '',
                    'name'          => $pro['name']."\t",
                    'sn'            => $reg['sn']."\t",
                    'm_name'        => $pro['m_name']."\t",
                    'market'        => !empty($m_name) ? implode(',',$m_name) : '',
                    'buy_time'      => $reg['buy_time'] ? date('Y-m-d',$reg['buy_time'])."\t" : '',
                    'ctime'         => $reg['create_time'] ? date('Y-m-d',$reg['create_time'])."\t" : '',
                ];
            }
        }
        !$viewSensitive && $dataList = Response::responseList($dataList,['phone'=>'tm']);
        return $dataList;
    }

    public function exportData($year = 0, $user_id = 0, $phone = 0, $sn = '', $s_time = 0, $e_time = 0, $viewSensitive = false)
    {
        // 表头
        $heads = [
            '用户ID',
            '用户名称',
            '用户手机号',
            '产品名称',
            '产品SN编码',
            'sku编码',
            '资源配置',
            '购买时间',
            '注册时间',
            '是否支持Care+',
            '是否自动激活Care+',
            'Care+时长'
        ];
        $dataList[] = $heads;

        // 查询条件
        $where = '1=1';
        if (!empty($user_id)) {
            $where .= " AND `user_id` = {$user_id}";
        }
        if (!empty($phone)) {
            $where .= " AND `phone` = '{$phone}'";
        }
        if (!empty($sn)) {
            $where .= " AND `sn` LIKE '%{$sn}%'";
        }
        if ($s_time && $e_time) {
            $where .= " AND (`create_time` BETWEEN {$s_time} AND {$e_time})";
        }

        // 表名
        $table = self::getTable($year ?: date('Y'));

        // 循环查询
        $id = 0;
        while (true) {
            $sql = "SELECT `id`,`reg_id`,`user_id`,`sn`,`phone`,`product_id` FROM {$table} 
                       WHERE `id` > :id AND {$where} ORDER BY `id` ASC LIMIT 1000";
            $params[':id'] = $id;
            $list = by::dbMaster()->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            // 获取数组中最后一个元素
            $id = end($list)['id'];

            // 批量获取用户
            $users = by::users()->getListByUserIds(array_unique(array_column($list, 'user_id')), ['user_id', 'nick']);
            $users = array_column($users, 'nick', 'user_id');

            // 获取产品
            $products = by::product()->getListByIds(array_unique(array_column($list, 'product_id')), ['id', 'name', 'm_name']);
            $products = array_column($products, null, 'id');

            // 获取注册信息
            $groupIds = $this->groupRegIds($list, 10);
            $group = [];
            foreach ($groupIds as $index => $groupId) {
                $group[$index] = $groupId['reg_id'];
            }
            $regs = by::productReg()->getListByGroupIds($group);

            // 获取营销资源卡
            $groupIds = $this->groupRegIds($list, 100);
            $group = [];
            foreach ($groupIds as $index => $groupId) {
                $group[$index] = [
                    'user_id'     => $groupId['user_id'],
                    'relation_id' => $groupId['reg_id'],
                ];
            }
            $userCards = by::userCard()->getListByGroupUserIdsAndRelationIds($group, UserCardModel::GET_CHANNEL['product'], ['user_id', 'market_id', 'get_relation']);
            $marketIds = [];
            foreach ($userCards as $userCard) {
                $items = $userCard[UserCardModel::GET_CHANNEL['product']];
                $items = array_values($items);
                foreach ($items as $item) {
                    $marketIds = array_merge($marketIds, array_column($item, 'market_id'));
                }
            }
            $marketIds = array_unique($marketIds);
            // 查询营销配置信息
            $markets = by::marketConfig()->getListByIds($marketIds, ['id', 'name']);
            $markets = array_column($markets, 'name', 'id');

            // 循环处理数据
            foreach ($list as $item) {
                $nick = preg_replace_callback(
                    '/./u',
                    function (array $match) {
                        return strlen($match[0]) >= 4 ? '' : $match[0];
                    },
                    $users[$item['user_id']] ?? '');

                $market = [];
                $market_ids = array_column($userCards[$item['user_id']][UserCardModel::GET_CHANNEL['product']][$item['reg_id']] ?? [], 'market_id');
                foreach ($market_ids as $market_id) {
                    $market[] = $markets[$market_id] ?? '';
                }

                $dataList[] = [
                    'user_id'  => $item['user_id'],
                    'uname'    => '\''.$nick,
                    'phone'    => $item['phone'],
                    'name'     => '\''.($products[$item['product_id']]['name'] ?? ''),
                    'sn'       => '\''.($regs[$item['user_id']][$item['reg_id']]['sn'] ?? ''),
                    'm_name'   => '\''.($products[$item['product_id']]['m_name'] ?? ''),
                    'market'   => '\''.(!empty($market) ? implode(',', $market) : ''),
                    'buy_time' => $regs[$item['user_id']][$item['reg_id']]['buy_time'] ? date('Y-m-d', $regs[$item['user_id']][$item['reg_id']]['buy_time']) : '',
                    'ctime'    => $regs[$item['user_id']][$item['reg_id']]['create_time'] ? date('Y-m-d', $regs[$item['user_id']][$item['reg_id']]['create_time']) : '',
                    'is_support_care'    => $regs[$item['user_id']][$item['reg_id']]['is_support_care'] == 1 ? '是' : '否',
                    'is_automatic'    => $regs[$item['user_id']][$item['reg_id']]['is_automatic'] == 1 ? '是' : '否',
                    'care_extend_month'    => $regs[$item['user_id']][$item['reg_id']]['care_extend_month'] ?  : '',
                ];
            }
        }
        !$viewSensitive && $dataList = Response::responseList($dataList, ['phone' => 'tm']);
        return $dataList;
    }


    /**
     * @param  $user_id
     * @param  $phone
     * @param  $sn
     * @param $ctime_start
     * @param $ctime_end
     * @return array
     * 规范查询
     */
    private function __getCondition( $user_id, $phone, $sn,$ctime_start, $ctime_end): array
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if(!empty($user_id)) {
            $where              .= " AND `user_id` LIKE :user_id";
            $params[":user_id"]  = "%{$user_id}%";
        }

        if(!empty($phone)) {
            $where              .= " AND `phone` LIKE :phone";
            $params[":phone"]   = "%{$phone}%";
        }

        if(!empty($sn)) {
            $where              .= " AND `sn` LIKE :sn";
            $params[":sn"]      = "%{$sn}%";
        }

        if($ctime_start && $ctime_end) {
            $where                 .= " AND (`create_time` BETWEEN :ctime_start AND :ctime_end)";
            $params[":ctime_start"] = CUtil::uint($ctime_start);
            $params[":ctime_end"]   = CUtil::uint($ctime_end);
        }

        return [$where, $params];
    }

    /**
     * 注册ID分组
     * @param array $items
     * @param int $mod
     * @return array
     */
    private function groupRegIds(array $items, int $mod = 10): array
    {
        $data = [];
        foreach ($items as $item) {
            // 取模
            $index = intval($item['user_id']) % $mod;
            $data[$index]['reg_id'][] = $item['reg_id'];
            $data[$index]['user_id'][] = $item['user_id'];
        }
        return $data;
    }

}
