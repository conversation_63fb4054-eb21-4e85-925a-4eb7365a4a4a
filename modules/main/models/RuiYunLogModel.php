<?php


namespace app\modules\main\models;

use app\components\RuiYun;
use app\models\by;
use app\models\CUtil;
use yii\db\Expression;


class RuiYunLogModel extends CommModel
{
    public static function tableName()
    {
        return "`db_dreame_log`.`t_ruiyun_err_log`";
    }

    CONST STATUS = [
        'success'=>2,
        'fail'=>1
    ];
    CONST LIMIT_RETRY_NUMS = 10;

    public function saveLog($function,$rags,$msg,$time=null,$utime=null): array
    {
        $table = $this->tableName();
        $data  = [
            'function'      => $function,
            'rags'          => json_encode($rags),
            'msg'           => json_encode($msg),
            'ctime'         => $time?:time(),
            'utime'         => $utime?:time(),
        ];

        by::dbMaster()->createCommand()->insert($table, $data)->execute();
        return [true,'ok'];
    }


    /**
     * 获取列表
     */
    public function getList($page,$page_size)
    {
        $tb     = $this->tableName();
        $sql    = "select count(*) from {$tb} where `status`=1";
        $total  = by::dbMaster()->createCommand($sql)->queryScalar();
        list($offset) = CUtil::pagination($page,$page_size);

        $pages = CUtil::getPaginationPages($total,$page_size);
        $sql  = "select * from {$tb} where `status`=1 order by `id` desc limit {$offset},{$page_size}";
        $list = by::dbMaster()->createCommand($sql)->queryAll();

        return [$pages,$list];
    }


    public function finalRetry(){
        $status        = self::STATUS['fail'];
        $retryNums = self::LIMIT_RETRY_NUMS;
        $id            = 0;

        $where         = " `status`={$status} AND `nums` <= {$retryNums}";

        $db            = by::dbMaster();

        $tb     = $this->tableName();

        while (true) {
            $sql    = "SELECT `id` FROM {$tb} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

            $list   = $db->createCommand($sql, [':id'=>$id])->queryAll();

            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $id     = $end['id'];

            foreach($list as $val) {
                $this->retry($val['id']);
            }
        }
        return [true,"OK"];
    }

    public function retry($id){
        $id     = CUtil::uint($id);
        $tb     = $this->tableName();
        $where  = " `id`={$id}";
        $sql    = "SELECT * FROM {$tb} WHERE {$where}";

        $info   = by::dbMaster()->createCommand($sql)->queryOne();

        list($status,$ret) = RuiYun::factory()->run($info['function'],json_decode($info['rags'],true),$info['ctime']);

        $updateData = [
            'utime'=>time(),
            'nums' => new Expression("nums+1"),
            'msg' => json_encode($ret,320)
        ];

        if ($status){
            $updateData['status'] = self::STATUS['success'];
        }else{
            // 飞书告警
            $sendData = [
                'title' => YII_ENV_PROD ? '上下水订单推送失败' : '测试-上下水订单推送失败',
                'rags'  => $info['rags'],
                'msg'   => json_encode($ret,320),
            ];    
            CUtil::sendMsgToFs($sendData, 'waterOrderPush');
        }
        by::dbMaster()->createCommand()->update($tb,$updateData, ['id'=>$id])->execute();

        return [$status,$ret];
    }


}
