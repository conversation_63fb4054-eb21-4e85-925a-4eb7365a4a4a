<?php
namespace app\modules\main\models;


use app\components\Erp;
use app\models\by;
use app\models\CUtil;

class CommPayModel
{
    const ERP_LIMIT_STOCK   = 100; //库存数小于多少查erp

    /**
     * @param $arr
     * @param $limit
     * @return array
     * 获取支付过期时间
     */
    public function GetPayExpireTime($arr,$limit=7100)
    {
        !YII_ENV_PROD && CUtil::debug(json_encode($arr),'expire.time');
        $endPayment = CUtil::uint($arr['endPayment'] ?? 0);
        $needReunified = CUtil::uint($arr['needReunified'] ?? 1);
        $now_time       = time();
        //订单过期时间约束
        $time_expire    = 30;

        if($needReunified == 1 && $endPayment){
            $ctime          = $arr['ctime'] ?? 0;
            if ($ctime > 0) {
                $pay_expire     = by::Omain()::PAY_EXPIRE;
                $time_expire    = $ctime + $pay_expire - $now_time - 10;
            }

            $time_expire_new = min($time_expire,$endPayment-$now_time-10);
            if($time_expire_new == $time_expire && $time_expire_new < 20){
                return [false,'定金订单快失效，请重新下单'];
            }
            if($time_expire_new == $endPayment-$now_time-10 && $time_expire_new < 20){
                return [false,'定金订单支付已截止，请等待下次机会！'];
            }
            $time_expire = $time_expire_new - 10;
            CUtil::debug($time_expire.'|'.$time_expire_new.'|'.$now_time.'|'.$endPayment,'pay.expire.time');
        }elseif ($needReunified == 2 && $endPayment){

            $time_expire = min($limit,$endPayment-$now_time-10);
            if($time_expire < 20){
                return [false,'尾款订单支付已截止，请等待下次机会！'];
            }
            $time_expire = $time_expire - 10;
        }else{
            $ctime          = $arr['ctime'] ?? 0;
            if ($ctime > 0) {
                $pay_expire     = by::Omain()::PAY_EXPIRE;
                $time_expire    = $ctime + $pay_expire - $now_time - 10;
            }
            if ($time_expire < 20) {
                return [false,'订单快失效，请重新下单'];
            }
        }

        return [true,$now_time+$time_expire];

    }


    /**
     * @param $user_id
     * @param $deposit_order_no
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     * 校验尾款订单库存
     */
    public function CheckTailOrderStock($user_id,$deposit_order_no)
    {
        //获取尾款信息
        $dInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id,$deposit_order_no);
        if(empty($dInfo)){
            return [false,'非尾款订单！'];
        }
        $gid = $dInfo['gid'] ?? 0;
        $sid = $dInfo['sid'] ?? 0;
        $num = intval($dInfo['num'] ?? 0);
        if(empty($gid)||empty($num)){
            return [false,'尾款信息失误！'];
        }

        $g_info = by::Gmain()->GetOneByGidSid($gid, $sid, false);
        if(empty($g_info)){
            return [false, '商城系统商品不存在，下单失败~'];
        }
        //TODO 尾款不限制库存

//        $lockKey = CUtil::getAllParams('TAIL_ORDER', __FUNCTION__, $gid, $sid);
//        $redis = by::redis('core');
//
//        //todo erp校验库存
//        $mGmain         = by::Gmain();
//        if ($g_info['type'] != $mGmain::TYPE['Y_LINK']) {
//            //获取库存
//            $stock1  = by::Gstock()->OptStock($gid, $sid);
//
//            if($num < $stock1){
//                //解锁
//                $redis->del($lockKey);
//            }
//
//            //判断锁
//            $aData = $redis->get($lockKey);
//            if($aData){
//                return [true,'OK'];
////                return [false, '商品库存不够，下单失败~'];
//            }
//
//            if($num >= $stock1){
//                //加锁
//                $redis->set($lockKey,1,3600*24*30);
//            }
//
//            if ($num > $stock1) {
////                return [false, 'ERP 商品库存不够，下单失败~~'];
//                return [true,'OK'];
//            }
//        }

        return [true,'OK'];
    }
}
