<?php

namespace app\modules\main\models;

use Alipay\EasySDK\Kernel\Config;
use Alipay\EasySDK\Kernel\Factory;
use Alipay\EasySDK\Kernel\Util\ResponseChecker;
use app\models\CUtil;

/**
 * @deprecated
 * 支付宝支付
 */
class AliPayModel
{
    private static $instance = null;

    private function __construct()
    {
        Factory::setOptions(self::getOptions());
    }

    public static function getInstance(): AliPayModel
    {
        if (self::$instance === null) {
            self::$instance = new AliPayModel();
        }
        return self::$instance;
    }

    // 配置信息
    private static function getOptions(): Config
    {
        // 沙箱环境
        if (true) {
            $options = new Config();
            $options->protocol = 'https';
            $options->gatewayHost = 'openapi-sandbox.dl.alipaydev.com'; // 'openapi.alipay.com';
            $options->signType = 'RSA2';
            $options->appId = '9021000132696508';
            // 应用私钥
            $options->merchantPrivateKey = 'MIIEowIBAAKCAQEAkiMn3ufaCUVReHMyvZiifDHXw7SAufHtlMH/oyCjbfbebMj1KCmRbZWvrz71vgiVxM941nkobV+dq8oriN5QijbGdZJFWyuLlQbxnGihN3UX+87FCzXp2lL5Z34fpGzXXUHs1lWKaoh8L6Pi9jLXE4QtZVF+GEM7Wieds50a1xF8YqKnwVz57MG/3a3qgcF1dBFniBZFhOukAJjoOHwZXIsKgaPJ1Y8jmJa2I8lzOWTRSlszXz6WH6N56T9lgVYekmYc7QgWYG8bwHqfuekcFq9LFkfU3xWkWljk4Muexp3qACUiT3fCPjzf4e/h06XxEMuIa3/F8DJ1KwOfYrE8JwIDAQABAoIBAGgz7zsvobZYmjZEFltBGiNquwqOi/eK/bjD+E/OdxpzHLj/Nmhz84HraUGRqFLf9kNHGUf9Tolm05vqZc6Lj2xukGbHbPMMgtQ6BlBb/xD3SqnAD5rlz6Fg5vwX3+IzxgF2f7V5e2Bpf/vMZYreR6Rf7KrBSCNu1UR82SN5E+lkNsTnsdV1JQudVP0cb0dWj5mmg57Rjrle9gJQbKH3RDO70JzScRPsUJAtZ144IIjtqJQQo/P7Ng7RWaatAY69fuVBrNt4ktNHJLsIMxiLCfV4760KvySBLqFU0L6RmAfeg3ngVPhpwec7bp0bFFZ67l+sX3PUEjd2CfkaD04JykkCgYEA0euAkV1aLb7M0N8SBv0Cj30xgrSFgY+nnvDbmSMA+JWgr24mc2KG5TuYkCYiYvjHEpKjhnSHzVx7+5AsqS1jiPCQMLIa9mVTF6Y4Rf6S5cFuyJh+gT8xHevmodGlybvCvUdZjOCiqfLI42KVT0x+n/q0WecuziQEgiTt8qA1Ny0CgYEAsjdgMd+voBez0RvX9GR8VV9hgA3GhQqxSP07XJoUl6Rlv92HhxJOwBgqUkGmeooWlylOVI5g8PMwpoTn5Sh2LtMp0fj46Yz6BLyxSyZ7wFdwN94xvUqeKbG81fQ4g9IVPcdaDhzeoAstC4Kaoa7V/oWHWhP+oYTiohawVQP0FSMCgYBaW9bhu58/paugFQU7gNkDTs5rBq2ZEW8DmYXNKyYV2sJqAS4H6NdMARUwp1mR5Kg8NGazT6HNIgw0QuEWiXUY0wQeEexNN1xHB8OdTXGJ+HNpYUrC86J+0LZb3VRRu8KdmwLPAzrBkMIsLpk6oyC2WEIdmMeq8BIIu0C5eYCumQKBgFHzxweIPmHXJHhfXnJjFw3HO7XAH+8kGJxg3sBZlmiZT7ToMR962r3ZHvUOfXjSMUs30m5tvsRugIvCiETH03j637tPy17S1lIBV2Iggz3St6bpRWQvgQXZVuXoVWjA3cW3+4CL5BAis4ccevEozf0p+DEDCs/JpNI2rsWZeM+TAoGBALXEHRZm5NHCVyYw0lDAqi6INg62UU5+W/lRcqcnHVfGMwSaMKGwRJIF6QUg8urUYbDEOyvBhbNG7annTYUEjKBxp3savky9u6+liPwnPZivv4rDjt0hOoOq7/xcIG6f+5e2TEXReA4QlY91ovgWyTvBHbdh0FkvPOoB7l71Ks2s';
            // 支付宝公钥
            $options->alipayPublicKey = 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtFvDkbIB/s6o0IlUD9mN87I0UYQBWC1yq8EC8Hc6/A6dUL/XIdXLW6GQn/MgFuTNziW9SZq969KbjK+Gyr12sSBYgA3M7qqgS7h62S5EfzHPfZGWzcuRBYIsaPSOGdBCpgu24pOr5+AZzs7NYqn4x9+MFOpJBZsbmeYc8DoQR3wmWsSs+xmTQo7XKux0FAqHtjpTkRlW6bdSK/H839HOtfUP4oVo3zRcCw45Arx6tFUe1aGbBSaDLcrv45w23k5hocEYTNhQUkNLt++iJjP5Y7yOAhavMWObJ1P3JjVDm+FIr4Vrc2/t4DL+0dfu43RNvVEWn/5e4DddEmrP/1BsjQIDAQAB';
            // 支付宝公钥证书文件路径
            // 异步通知接收服务地址
            $options->notifyUrl = 'https://test2-wxmall.dreame.tech/comm/alipay-notify';
            return $options;
        }
        // 参数配置
        $alipayConfig = CUtil::getConfig('ALIPAY', 'common', MAIN_MODULE);
        $options = new Config();
        $options->protocol = $alipayConfig['protocol'];
        $options->gatewayHost = $alipayConfig['gatewayHost'];
        $options->signType = $alipayConfig['signType'];
        $options->appId = $alipayConfig['appId'];
        // 应用私钥
        $options->merchantPrivateKey = $alipayConfig['merchantPrivateKey'];
        // 支付宝公钥
        $options->alipayPublicKey = $alipayConfig['alipayPublicKey'];
        // 异步通知接收服务地址
        $options->notifyUrl = $alipayConfig['notifyUrl'];
        return $options;

    }

    /**
     * 测试支付
     * @param $order_no
     * @param $amount
     * @return array
     */
    public function test($order_no, $amount, $source = 1): array
    {
        $user_id = 1234;
        try {
            $result = Factory::payment()->wap()
                ->batchOptional($this->getOptionals("user_id={$user_id}&order_no={$order_no}&order_type={$source}", date('Y-m-d H:i:s', time() + 600)))
                ->pay('追觅商城测试', $order_no, $amount, '', '');
            $responseChecker = new ResponseChecker();
            if ($responseChecker->success($result)) {
                // 请求成功
                $res = ['data' => $result->body];
                return [true, $res];
            } else {
                // 请求失败
                CUtil::debug(serialize($result->body), 'err.alipay_wap_pay');
                return [false, '支付请求失败'];
            }
        } catch (\Exception $e) {
            // 请求失败
            CUtil::debug($e->getMessage(), 'err.alipay_wap_pay');
            return [false, '支付请求异常'];
        }
    }


    /**
     * 支付
     * @param $order_no
     * @param $pay_amount
     * @param array $params
     * @return array
     */
    public function pay($order_no, $pay_amount, array $params): array
    {
        // 参数
        $param = "user_id={$params['user_id']}&order_no={$order_no}&order_type={$params['order_type']}";
        $bizParams = $this->getOptionals($param, $params['time_expire']);

        try {
            // 支付请求
            $result = Factory::payment()->wap()
                ->batchOptional($bizParams)
                ->pay('追觅官方商城', $order_no, $pay_amount, '', '');

//            var_dump($result);

            // 结果校验
            $responseChecker = new ResponseChecker();
            if ($responseChecker->success($result)) {
                // 请求成功 返回form字符串
                $data = ['body' => $result->body, 'order_no' => $order_no];
                return [true, $data];
            } else {
                // 请求失败
                CUtil::debug(serialize($result->body), 'err.alipay_wap_pay');
                return [false, '支付请求失败，请稍后重试'];
            }
        } catch (\Exception $e) {
            // 请求失败
            $errorMsg = "调用失败，异常信息：" . $e->getMessage();
            // 记录日志
            CUtil::debug($errorMsg, 'err.alipay_wap_pay');
            return [false, '支付请求异常，请稍后重试'];
        }
    }

    /**
     * 退款
     * @param $order_no
     * @param $refund_no
     * @param $refund_amount
     * @return array
     */
    public function refund($order_no, $refund_no, $refund_amount): array
    {
        try {
            // 退款请求
            $result = Factory::payment()
                ->common()
                ->batchOptional(['out_request_no' => $refund_no]) // 退款单号
                ->refund($order_no, $refund_amount);
            // 结果校验
            $responseChecker = new ResponseChecker();
            if ($responseChecker->success($result)) {
                // 请求成功
                $httpBody = json_decode($result->httpBody, true);
                return [true, $httpBody];
            } else {
                // 请求失败
                $requestParams = [
                    'order_no'      => $order_no,
                    'refund_no'     => $refund_no,
                    'refund_amount' => $refund_amount
                ];
                CUtil::debug('请求参数：' . json_encode($requestParams) . ' 响应结果：' . serialize($result->httpBody), 'err.alipay_wap_refund');
                return [false, '退款请求失败，请稍后重试'];
            }
        } catch (\Exception $e) {
            // 请求失败
            // 记录日志
            $requestParams = [
                'order_no'      => $order_no,
                'refund_no'     => $refund_no,
                'refund_amount' => $refund_amount
            ];
            $errorMsg = '请求参数：' . json_encode($requestParams) . ' 调用失败，异常信息：' . $e->getMessage();
            CUtil::debug($errorMsg, 'err.alipay_wap_refund');
            return [false, '退款请求异常，请稍后重试'];
        }
    }

    /**
     * 查询
     * @param $order_no
     * @return array
     */
    public function query($order_no): array
    {
        try {
            // 查询请求
            $result = Factory::payment()->common()->query($order_no);
            // 结果校验
            $responseChecker = new ResponseChecker();
            if ($responseChecker->success($result)) {
                // 请求成功
                $httpBody = json_decode($result->httpBody, true);
                return [true, $httpBody];
            } else {
                // 请求失败
                CUtil::debug(serialize($result->httpBody), 'err.alipay_wap_query');
                return [false, '查询请求失败，请稍后重试'];
            }
        } catch (\Exception $e) {
            // 请求失败
            $errorMsg = "调用失败，异常信息：" . $e->getMessage();
            // 记录日志
            CUtil::debug($errorMsg, 'err.alipay_wap_query');
            return [false, '查询请求异常，请稍后重试'];
        }
    }

    /**
     * 校验异步通知
     * @param $parameters
     * @return bool
     */
    public function verifyNotify($parameters): bool
    {
        return Factory::payment()->common()->verifyNotify($parameters);
    }

    /**
     * 请求参数：biz_content
     * @param string $params
     * @param $time_expire
     * @return array
     */
    private function getOptionals(string $params, $time_expire = null): array
    {
        return [
            'passback_params' => urlencode($params), // 公用回传参数
            'time_expire'     => $time_expire, // 过期时间，用户多长时间不支付订单关闭
        ];
    }
}