<?php

namespace app\modules\main\models;

use app\modules\common\ModelTrait;
use app\modules\common\Singleton;

final class MemberActivityModuleResourceModel extends CommModel
{
    use Singleton, ModelTrait;
    
    const DRAW = 5; //抽奖活动
    
    private $fillable = ['id', 'module_name', 'module_code', 'create_time'];
    
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`member_activity_module_resource`";
    }
    
    public function getList(array $params = []): array
    {
        $query = self::find();
        return $query->asArray()->select($this->fillable)->all();
    }

    /**
     * 批量获取模块信息
     *
     * @param array $moduleIds 模块ID数组
     * @param array $fields 查询字段
     * @return array 以module_id为key的模块信息数组
     */
    public function getBatchInfoByModuleIds(array $moduleIds, array $fields = []): array
    {
        if (empty($moduleIds)) {
            return [];
        }

        $query = self::find()
                ->where(['id' => $moduleIds]);

        if (!empty($fields)) {
            $query->select($fields);
        }

        $results = $query->asArray()->all();

        return array_column($results, null, 'id');
    }
    
    /**
     * 获取模块ID映射
     * @return array
     */
    public function getAllModuleResIdMap(): array
    {
        $res = $this->getList();
        return array_column($res, null, 'id');
    }

    /**
     * 获取模块code映射
     * @return array
     */
    public function getAllModuleResCodeMap(): array
    {
        $res = $this->getList();
        return array_column($res, null, 'module_code');
    }
}