<?php

namespace app\modules\main\models;


use app\components\AliZhima;
use app\components\Crm;
use app\components\ErpNew;
use app\components\EventMsg;
use app\components\PointCenter;
use app\jobs\AutoCouponActivityPushJob;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\FinishTaskJob;
use app\jobs\GroupPurchaseJob;
use app\jobs\SalesCommissionJob;
use app\jobs\VirtualGoodsJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\activities\AutoCouponActivity;
use yii\base\Event;

/**
 * 支付公共处理类（小程序支付/微信H5支付）
 */
class BasePayModel extends CommModel
{

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function afterPay($user_id, $order_no, $notify_data, $order, $source, $payType): array
    {
        //基础数据处理
        list($s, $noData) = $this->_anaNotifyData($notify_data, $payType); // todo
        if (!$s) {
            return [false, $noData];
        }

        //todo 先更新订单支付状态
        $pay_time = $noData['pay_time'];
        $tid = $noData['tid'] ?? '';
        $update_data = $noData['update_data'];

        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            //todo 同步支付流水
            by::model('OPayModel', 'goods')->SaveLog(
                $order_no,
                ['pay_time' => $pay_time, 'tid' => $tid, 'pay_type' => $payType]
            );

            switch ($source) {
                case by::wxPay()::SOURCE['MALL']:
                case by::wxPay()::SOURCE['POINTS']:
                case by::wxPay()::SOURCE['BUY_AFTER_PAY']:
                    $mOuser = by::Ouser();
                    $mOmain = by::Omain();

                    //todo 订单状态设置为待发货
                    $next_st = $mOmain::ORDER_STATUS['WAIT_SEND'];

                    //同步支付结果,调整订单状态
                    list($status, $ret) = by::Omain()->SyncInfo($user_id, $order_no, $next_st, $update_data);
                    if (!$status) {
                        CUtil::debug("订单状态修改失败:{$ret}|" . json_encode($notify_data), 'err.n.pay');
                        throw new \Exception('订单状态修改失败');
                    }

                    //todo 未付款数修改
                    $skus=[];
                    $oGlist = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
                    foreach ($oGlist as $v) {
                        //判断商品类型
                        $goodsType = $v['goods_type'] ?? '';
                        $goodsSource = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                            ? by::GoodsStockModel()::SOURCE['WARES']
                            : by::GoodsStockModel()::SOURCE['MAIN'];

                        list($stockStatus,$sku) = by::GoodsStockModel()->UpdateStock($v['gid'], $v['sid'], $v['num'], 'WAIT', true, $goodsSource);
                        if ($stockStatus){
                            $skus[] = $sku;
                        }

                        $giniId = $v['gini_id'] ?? 0;
                        if ($giniId) {
                            by::Gini()->UpdateStock($giniId, $v['num'], 'WAIT');
                        }
                    }

                    // 如果是先试后买订单，需要保存auth_no
                    if ($source == by::wxPay()::SOURCE['BUY_AFTER_PAY']) {
                        $orderTry = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no)]);
                        byNew::ActivityTypeModel()->saveLog($orderTry['ac_id'], ['apply_number' => new \yii\db\Expression('apply_number + 1')]);
                        if (!isset($notify_data['fund_amount']) && !isset($notify_data['credit_amount'])) {
                            // 用户支付押金  如果有没有 credit_amount和fund_amount 这个2个参数，那么就代表用户使用的是纯资金冻结的
                            $fundAmount   = $notify_data['amount'] ?? 0;
                            $creditAmount = 0;
                        } else {
                            // 用户免押
                            $fundAmount   = $notify_data['fund_amount'] ?? 0;
                            $creditAmount = $notify_data['credit_amount'] ?? 0;
                        }
                        $tryStatus = $orderTry['try_status'] ?? 0;
                        if($tryStatus >= byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY']){
                            break;
                        }else{
                            byNew::UserOrderTry()->SaveLog([
                                'user_id'       => $user_id,
                                'order_no'      => $order_no,
                                'auth_no'       => $notify_data['auth_no'] ?? '',
                                'pay_time'      => time(),
                                'try_status'    => byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY'],
                                'fund_amount'   => CUtil::totalFee($fundAmount),
                                'credit_amount' => CUtil::totalFee($creditAmount)
                            ]);
                        }
                    }

                    // TODO 拼团订单保存成员信息 修改团数据
                    $orderInfo  = $mOuser->GetInfoByOrderId($user_id, $order_no);
                    // 如果是拼团订单 异步 修改团数据 + 插入团员信息
                    if (!empty($orderInfo['group_purchase_id'] ?? 0)){
                        \Yii::$app->queue->push(new GroupPurchaseJob(['user_id' => $user_id, 'order_no' => $order_no, 'group_purchase_id' => $orderInfo['group_purchase_id']]));
                    }

                    // 自动发放优惠券
                    \Yii::$app->queue->push(new AutoCouponActivityPushJob(['user_id' => $user_id, 'order_no' => $order_no, 'skus' => $skus]));

                    \Yii::$app->queue->push(new EmployeeStatisticsJob(['user_id'=>$user_id,'order_no' => $order_no,'type'=>2,'field'=>'','number'=>0,'addOrsubtract'=>'+']));

                    // 修改分佣表
                    $commissionInfo = byNew::SalesCommissionModel()->getInfo($order_no);
                    if ($commissionInfo){
                        byNew::SalesCommissionModel()->patch(['order_no'=>$order_no],['status'=>300,'utime'=>time()]);
                        // 如果是小店的订单，则给奖励
                        $avt = YII_ENV_PROD ? 32 : 170;
                        if ($commissionInfo['activity_id'] == $avt){
                            EventMsg::factory()->run('friendBuyShopGoods', ['user_id' => $commissionInfo['referrer']]);
                        }
                    }

                    // 虚拟商品发放
                    if ($source==by::wxPay()::SOURCE['POINTS']) {
                        \Yii::$app->queue->push(new VirtualGoodsJob(['user_id' => $user_id, 'order_no' => $order_no, 'gid' => $oGlist[0]['gid']??0, 'sid' => $oGlist[0]['sid']??0,'num' => $oGlist[0]['num']??0]));
                    }

                    break;
                case by::wxPay()::SOURCE['PLUMBING']:
                    $plumbing_order = by::plumbingOrder();

                    //todo 工单状态设置为待服务
                    $next_status = $plumbing_order::STATUS['WAIT_SERVICE'];

                    //同步支付结果,调整工单状态
                    list($status, $ret) = by::plumbingOrder()->SyncInfo($user_id, $order_no, $next_status, $update_data);
                    if (!$status) {
                        CUtil::debug("工单状态修改失败:{$ret}|" . json_encode($notify_data), 'plumbing-pay.err');
                        throw new \Exception('工单状态修改失败');
                    }

                    break;
                case by::wxPay()::SOURCE['DEPOSIT']:
                    //todo 订单状态设置为待发货
                    $next_st = by::Odeposit()::STATUS['WAIT_SEND'];

                    //同步支付结果,调整订单状态
                    list($status, $ret) = by::Odeposit()->SyncInfo($user_id, $order_no, $next_st, $update_data);
                    if (!$status) {
                        CUtil::debug("定金订单状态修改失败:{$ret}|" . json_encode($notify_data), 'deposit-pay.err');
                        throw new \Exception('定金订单状态修改失败');
                    }

                    //获取定金订单详情
                    $dInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
                    if (empty($dInfo)) {
                        throw new \Exception('定金订单数据不存在');
                    }

                    //todo 未付款数修改
                    by::Gprestock()->UpdateStock($dInfo['gid'], $dInfo['sid'], $dInfo['num'], 'WAIT');

                    //创建尾款订单
                    list($status, $tail_order) = by::Omain()->addDepositTailRecord($user_id, $order_no);
                    if (!$status) {
                        CUtil::debug("尾款订单创建失败:{$tail_order}|" . json_encode($notify_data), 'deposit-pay.err');
                        throw new \Exception('尾款订单创建失败');
                    }

                    break;
                default:
                    throw new \Exception('订单来源不合法（1）');
            }

            $trans->commit();

            !YII_ENV_PROD && CUtil::debug($user_id . '=====' . $source . '========' . $order_no, 'base-pay');

            switch ($source) {
                case by::wxPay()::SOURCE['MALL']:
                case by::wxPay()::SOURCE['POINTS']:
                case by::wxPay()::SOURCE['BUY_AFTER_PAY']:
                    //todo 同步erp
                    $mOuser = by::Ouser();
                    //新旧erp锁
                    // $lockOldErp = CUtil::omsLock($user_id, $order_no);
                    // !$lockOldErp && $mOuser->ErpAddOrder($user_id, $order_no, 0, $order, false);
                    // $lockOldErp && ErpNew::factory()->synErp('addOrder', ['user_id' => $user_id, 'order_no' => $order_no]);

                    // 团购订单不推送E3+
                    if (empty($orderInfo['group_purchase_id'] ?? 0)) {
                        ErpNew::factory()->synErp('addOrder', ['user_id' => $user_id, 'order_no' => $order_no]);
                    }
                    // //todo 订单同步crm
                    // Crm::factory()->push($user_id, 'order', ['user_id' => $user_id, 'order_no' => $order_no]);
                    // Crm::factory()->push($user_id, 'orderLine', ['user_id' => $user_id, 'order_no' => $order_no]);
                    // 订单同步IOT
                    PointCenter::factory()->orderPush($user_id, $order_no);

                    //todo 消息通知处理
                    by::UserMsg()->changeTailOrderMsgStatus($user_id, $order_no);
                    //todo 同步腾讯信息
                    by::userAdv()->pushAdv($user_id, $order_no, 'PURCHASE');
                    //todo 插入抽奖购买任务队列
                    \Yii::$app->queue->push(new FinishTaskJob(['user_id' => $user_id, 'order_no' => $order_no]));

                    break;
                case by::wxPay()::SOURCE['PLUMBING']:

                    break;
                case by::wxPay()::SOURCE['DEPOSIT']:
                    //todo 同步腾讯信息
                    by::userAdv()->pushAdv($user_id, $order_no, 'PURCHASE');
                    //todo 插入抽奖购买任务队列
                    \Yii::$app->queue->push(new FinishTaskJob(['user_id' => $user_id, 'order_no' => $order_no]));
                    break;
                default:
                    throw new \Exception('订单来源不合法（2）');
            }

            return [true, 'ok'];
        } catch (\Exception $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];
        }
    }


    /**
     * @param $notify_data
     * @param $payType
     * @return array
     * @throws \Exception
     * 处理通知数据
     */
    public function _anaNotifyData($notify_data, $payType): array
    {
        switch ($payType) {
            case by::Omain()::PAY_BY_WX:
            case by::Omain()::PAY_BY_WX_APP:
                //todo 先更新订单支付状态
                $time_end = $notify_data['time_end'] ?? 0;
                $pay_time = $time_end > 0 ? strtotime($notify_data['time_end']) : time();
                $tid = $notify_data['transaction_id'] ?? '';
                $update_data = ['pay_time' => $pay_time];
                break;
            case by::Omain()::PAY_BY_WX_H5:
                //todo 先更新订单支付状态 支付时间处理
                $time_end = $notify_data['success_time'] ?? 0;
                //获取时间戳
                if ($time_end) {
                    //设置时区
                    $timezones = new \DateTimeZone('Asia/Shanghai');
                    //获取当前时间
                    $timeObj = new \DateTime($time_end, $timezones);
                    $time_end = $timeObj->getTimestamp() ?? 0;
                }
                $pay_time = $time_end > 0 ? $time_end : time();
                $tid = $notify_data['transaction_id'] ?? '';
                $update_data = ['pay_time' => $pay_time];
                break;
            case by::Omain()::PAY_BY_ALI_ZHIMA:
                //todo 先更新订单支付状态
                $time_end = $notify_data['gmt_trans'] ?? 0;
                $pay_time = $time_end > 0 ? strtotime($notify_data['gmt_trans']) : time();
                $tid = $notify_data['out_request_no'] ?? '';
                $update_data = ['pay_time' => $pay_time];
                break;
            default:
                return [false, '支付类型不存在！'];
        }

        return [
            true,
            [
                'time_end' => $time_end,
                'pay_time' => $pay_time,
                'tid' => $tid,
                'update_data' => $update_data,
            ]
        ];
    }

    public function CancelNoPayOrder($payType, $opayInfo)
    {
        $oldPayType = $opayInfo['pay_type'] ?? 0;
        if ($payType == $oldPayType || !empty($opayInfo['pay_time'])) {
            return true;
        }
        switch ($oldPayType) {
            case by::Omain()::PAY_BY_WX:
                //todo 取消微信订单
                break;
            case by::Omain()::PAY_BY_WX_H5:
                //todo 取消微信H5订单
                break;
            case by::Omain()::PAY_BY_ALI_ZHIMA:
                list($sta, $data) = AliZhima::factory()->CancelOrder($opayInfo['order_no'] ?? '', $opayInfo['tid'] ?? '');
                if (!$sta) {
                    return false;
                }
                //todo 取消芝麻订单
                break;
            case by::Omain()::PAY_BY_NO_SET:
                return true;
            default:
                return false;
        }
        return true;
    }

}
