<?php

namespace app\modules\main\models;



class CommentAuditModel extends CommModel
{
    /**
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame_log`.`t_comment_audit`";
    }

    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_log`.`t_comment_audit`";
    }

    public $tb_fields = [
            'id',  'comment_task_id', 'user_id', 'nick_name', 'phone', 'comment_content', 'status', 'auto_audit_opinion', 'audit_opinion', 'audit_user_id', 'audit_time', 'is_reward_sent', 'reward_remark', 'reward_time', 'redbook_id', 'redbook_url', 'sn_code', 'purchase_channel', 'created_at', 'updated_at'
    ];

}
