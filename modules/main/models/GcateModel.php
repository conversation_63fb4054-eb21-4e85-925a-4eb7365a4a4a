<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 17:35
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

class GcateModel extends CommModel
{

    /**
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_gcate`";
    }

    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_gcate`";
    }

    public $tb_fields = [
        'id', 'sku', 'main_sku', 'c_id', 'status', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    private function __getCateInfoBySku($sku): string
    {
        return AppCRedisKeys::getCateInfoBySku($sku);
    }

    /**
     * @throws RedisException
     */
    public function __delCateInfoBySkuCache($skus)
    {
        $redis     = by::redis('core');
        $redis_key = [];
        foreach ($skus as $sku) {
            $redis_key[] = $this->__getCateInfoBySku($sku);
        }
        $redis->del($redis_key);
    }

    private function __getCateInfoByCid($c_id): string
    {
        return AppCRedisKeys::getCateInfoByCid($c_id);
    }


    /**
     * @throws RedisException
     */
    public function __delCateInfoByCidCache($c_id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getCateInfoByCid($c_id);
        $redis->del($redis_key);
    }

    private function __getCidsBySkus(): string
    {
        return AppCRedisKeys::getCidsBySkus();
    }

    /**
     * @throws RedisException
     */
    public function __delCidsBySkuCache()
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getCidsBySkus();
        $redis->del($redis_key);
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getCateInfoBySku($sku)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getCateInfoBySku($sku);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `sku`=:sku LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [":sku" => $sku])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getCateInfoByCid($c_id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getCateInfoByCid($c_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `c_id`=:c_id";
            $aData  = by::dbMaster()->createCommand($sql, [":c_id" => $c_id])->queryAll();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 增加 修改 分类
     * 配件绑定主机sku
     */
    public function saveLog($sku, $c_id, $status): array
    {
        $main_skus = by::mainPartModel()->getMainByPartSku($sku);
        $main_skus = array_column($main_skus, 'main_sku');
        $main_skus = implode(',', $main_skus);

        $cateInfo = $this->getCateInfoBySku($sku);
        $tb       = self::tbName();
        $db       = by::dbMaster();
        $data     = [
            'sku'      => $sku,
            'main_sku' => $main_skus,
            'c_id'     => $c_id,
            'status'   => $status,
            'utime'    => intval(START_TIME),
        ];

        if (!$cateInfo) {
            $data['ctime'] = intval(START_TIME);
            $result        = $db->createCommand()->insert($tb, $data)->execute();
        } else {
            $result = $db->createCommand()->update($tb, $data, ['sku' => $sku])->execute();
        }

        if ($result) {
            $this->__delCidsBySkuCache();
            $this->__delCateInfoBySkuCache(explode(',', $sku));
            return [true, '保存成功'];
        } else {
            return [false, '保存失败'];
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 编辑商品类目
     */
    public function updateByCid($c_id, $u_id): array
    {
        if (empty($c_id) || empty($u_id)) {
            return [false, '参数有误'];
        }
        $tb   = self::tbName();
        $db   = by::dbMaster();
        $info = $this->getCateInfoByCid($c_id);
        $sku  = array_column($info, 'sku');

        $result = $db->createCommand()->update($tb, ["c_id" => $u_id, "utime" => intval(START_TIME)], ['c_id' => $c_id])->execute();


        $this->__delCateInfoByCidCache($c_id);
        $this->__delCateInfoBySkuCache($sku);
        $this->__delCidsBySkuCache();
        $this->__delCateInfoByCidCache($u_id);
        by::cateModel()->__delGetListCache();
        if ($result) {
            return [true, '修改成功'];
        } else {
            return [false, '保存失败'];
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 获取筛选机型
     */
    public function choose($c_id, $platformId): array
    {
        if (!is_numeric($c_id)) {
            return [false, '参数有误'];
        }

        $part_info = $this->getCateInfoByCid($c_id);
        if (!$part_info) {
            return [false, []];
        }

        $choose = [];

        foreach ($part_info as $value) {
            // 跳过下架或已删除的配件
            if ($value['status'] == 1 || $value['is_del'] == 1) {
                continue;
            }

            // 获取配件信息
            $part = by::Gmain()->GetOneBySku($value['sku']);
            if (empty($part)) {
                continue;
            }

            // 获取完整的主信息
            $aMain = by::Gmain()->GetAllOneByGid($part['id'] ?? '', false);
            // 如果当前平台下此类目没有绑定上架的商品，跳过
            if (!$aMain || !in_array($platformId, $aMain['platform_ids']) || $aMain['status'] == 1) {
                continue;
            }

            // 获取主SKU信息
            $main_skus    = by::mainPartModel()->getMainByPartSku($value['sku']);
            $main_sku_str = implode(',', array_column($main_skus, 'main_sku'));

            // 获取CID信息并筛选机型
            $cidInfos = $this->getCidInfosBySkus($main_sku_str);
            $choose   = array_merge($choose, $cidInfos);
        }

        //处理筛选数据
        $choose = array_unique(array_map('json_encode', $choose));
        $choose = array_map(function ($item) {
            return json_decode($item, true);
        }, $choose);

        return [true, array_values($choose) ?? []];
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function filterPart($main_sku)
    {
        //判断是否是配件
        foreach ($main_sku as $key => $item) {
            $main_info = $this->getCateInfoBySku($item['main_sku']);
            $parent    = by::cateModel()->findParent($main_info['c_id'] ?? '') ?? [];
            $tag_name  = $parent[0]['tag_name'] ?? '';
            if ($tag_name != 'main') {
                unset($main_sku[$key]);
            }
        }
        return array_values($main_sku);
    }

    /**
     * @param $skus
     * @return array
     * @throws Exception
     * @throws RedisException
     * 通过sku获取机型id和名称
     */
    public function getCidInfosBySkus($skus): array
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getCidsBySkus();
        $sub_key   = $skus;
        $aJson     = $redis->hget($redis_key, $sub_key);
        $c_ids     = (array)json_decode($aJson, true);

        if (!$c_ids) {
            if (!is_array($skus)) {
                $skus = explode(',', $skus);
            }
            $skus = array_filter(array_unique($skus));

            //判断机器是否删除
            foreach ($skus as $k => $sku) {
                $machine = by::Gmain()->GetOneBySku($sku);
                if (empty($machine)) unset($skus[$k]);
            }

            $where = implode("','", $skus);
            $tb    = self::tbName();
            $sql   = "SELECT `c_id` FROM {$tb} WHERE `sku` IN ('{$where}')";
            $c_ids = by::dbMaster()->createCommand($sql)->queryAll() ?? [];

            $redis->hset($redis_key, $sub_key, json_encode($c_ids));
            CUtil::ResetExpire($redis_key, rand(600, 900));
        }

        $aData = [];
        foreach ($c_ids as $k => $item) {
            $info             = by::cateModel()->getOneInfoById($item['c_id'] ?? '');
            $data['id']       = $info['id'];
            $data['name']     = $info['name'];
            $data['isActive'] = false;
            $aData[]          = $data;
        }
        return $aData;
    }

    public function getDataBySkus(array $skus, array $columns = ['*']): array
    {
        // 判空
        if (empty($skus)) {
            return [];
        }

        $tb = self::tbName();
        $skus = implode('","', $skus);
        $columns = implode(',', $columns);

        try {
            $sql = "SELECT {$columns}
            FROM {$tb}
            WHERE
                sku IN (\"{$skus}\")
            ORDER BY id DESC";

            $res = by::dbMaster()->createCommand($sql)->queryAll();
            return $res;
        } catch (\Exception $e) {
            return [];
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 获取配件详情+适配机型
     */
    public function getMachineTypeByPartSku($sku): array
    {
        // 如果SKU为空，返回空数组
        if (!$sku) {
            return [];
        }

        // 获取主要部件信息
        $mainParts = by::mainPartModel()->getMainByPartSku($sku);
        // 提取主SKU并拼接成字符串
        $mainSkus = implode(',', array_column($mainParts, 'main_sku'));

        // 获取主配件信息
        $mainInfo = by::Gmain()->GetOneBySku($sku);
        $aMain    = by::Gmain()->GetAllOneByGid($mainInfo['id'] ?? '', false);

        // 如果主配件信息为空，返回空数组
        if (empty($aMain)) {
            return [];
        }

        // 校验自定义商品类型
        $aType = $aMain['atype'] ?? -1;
        if ($aType == by::Gtype0()::ATYPE['SPECS']) {
            // 获取规格列表
            $specs = by::Gspecs()->GetListByGid($aMain['id']);
            // 获取规格名称
            $aMain['specs'] = by::Gspecs()->AttrToName($aMain['id'], $specs);
        }

        // 计算库存
        $stock = 0;
        switch ($aMain['atype']) {
            case by::Gtype0()::ATYPE['SPEC']:
                $stock = $aMain['stock'];
                break;
            case by::Gtype0()::ATYPE['SPECS']:
                foreach ($aMain['specs'] as $item) {
                    $stock += $item['stock'];
                }
                break;
        }

        // 校验是否有库存
        $aMain['is_stock'] = $stock > 0 ? 1 : 0;

        // 获取机型信息
        $cidInfos = $this->getCidInfosBySkus($mainSkus);
        // 提取机型名称并去重、过滤空值
        $mainCidNames          = implode('、', array_unique(array_filter(array_column($cidInfos, 'name'))));
        $aMain['machine_type'] = $mainCidNames;

        return $aMain ?? [];
    }


}
