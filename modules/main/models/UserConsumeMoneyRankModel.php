<?php

namespace app\modules\main\models;

use app\models\by;
use yii\db\Exception;

class UserConsumeMoneyRankModel extends CommModel
{
    public $tb_fields = [
        'id', 'user_id', 'money', 'money_count'
    ];

    public static function tableName(): string
    {
        return self::tbName();
    }

    public static function tbName(): string
    {
        return "`db_dreame`.`user_consume_money_rank`";
    }
    
    /**
     * 更新用户排名
     * @param int $user_id
     * @return bool
     */
    public function updateUserRank(int $user_id = 0): bool
    {
        $sql = "INSERT INTO " . self::tbName() . " (user_id, money, money_count)

                SELECT `user_id`, sum(money) AS `money`, count(*) AS `money_count`
                FROM `db_dreame`.`user_shop_money_record`
                WHERE `user_id` = :user_id
                    AND `money_type` = 2
                    AND `type` = 1
                    AND LENGTH(extend) < 10
                GROUP BY `user_id`
                HAVING sum(money) > 0

                ON DUPLICATE KEY UPDATE 
                money = VALUES(money),
                money_count = VALUES(money_count)";
        
        $params = [
            ':user_id' => $user_id,
        ];

        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 更新总排行
     * @return bool
     */
    public function updateRank(): bool
    {
        $sql = "INSERT INTO " . self::tbName() . " (user_id, money, money_count)

                SELECT `user_id`, sum(money) AS `money`, count(*) AS `money_count`
                FROM `db_dreame`.`user_shop_money_record`
                WHERE `money_type` = 2
                    AND `type` = 1
                    AND LENGTH(extend) < 10
                GROUP BY `user_id`
                HAVING sum(money) > 0

                ON DUPLICATE KEY UPDATE
                money = VALUES(money),
                money_count = VALUES(money_count)";
        
        $params = [];
        
        try {
            $command = by::dbMaster()->createCommand($sql, $params);
            return $command->execute() > 0;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 赚钱花排行榜
     * @param int $user_id 用户ID
     * @param int $top 显示数量
     * @return array
     */
    public function getConsumeMoneyRank(int $user_id = 0, int $top = 50): array
    {
        $param = [];
        if (! empty($user_id)) {
            $param['user_id'] = $user_id;
        }

        $query = self::find()->where($param)->select(['user_id', 'money_count', 'money'])->orderBy('money DESC');
        
        if (! empty($top)) {
            $query->limit($top);
        }

        return $query->asArray()->all();
    }
} 