<?php


namespace app\modules\main\models;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\GtagModel;
use yii\db\Exception;
use function GuzzleHttp\Psr7\str;

class MarketConfigModel extends CommModel
{

    protected $expire_time = 1800;

    CONST ListExpire    = 600;  //列表缓存

    CONST IdsListExpire    = 600;  //营销资源ID列表缓存

    const BIG_STOCK     = -99; //无限库存

    //资源类型
    const TYPE  = [
        'coupon'  => 1, //优惠券
        'voucher' => 6, //兑换券
        'consume' => 10, //消费券
    ];

    //资源类型
    const TYPE_NAME  = [
        1 => '优惠券',
        6 => '兑换券',
        10 => '消费券'
    ];

    // 资源内容类型
    const RESOURCE_TYPE = [
        'discount'  => 1,
        'amount'    => 2
    ];

    //使用金额（1：全部金额；2：最低订单金额）
    const C_AMOUNT_TYPE = [
        'all'       => 1,
        'lowest'    => 2
    ];

    //使用条件（1：全部标签；2：适用标签；3：排除标签）
    const C_TAG_TYPE = [
        'all'       => 1,
        'apply'     => 2,
        'out'       => 3
    ];

    //商品条件（1：适用商品；2：排除商品）
    const C_GOODS_TYPE = [
        'apply'     => 1,
        'out'       => 2
    ];

    const STATUS = [
        'not_check'     => 0,
        'check_success' => 1,
        'check'         => 2,
        'check_failed'  => 3
    ];

    const STATUS_NAME = [
        0 => '未审核',
        1 => '审核通过',
        2 => '审核中',
        3 => '审核不通过'
    ];

    const IS_DEL = [
        'no'        => 0,
        'yes'       => 1
    ];

    const VALID_TYPE = [
        'fixed'     => 1,       //固定时间
        'get_after' => 2        //领取后多少天
    ];

    const ACT = [
        'pass'      => 1001, //通过
        'apply'     => 1002, //申请审核
        'fail'      => 1003, //拒绝
        'cancel'    => 1004, //取消审核
    ];

    CONST MSG_TYPE = [
        'manage'      => 1, //管理端
        'web'         => 2  //小程序
    ];

    const PRODUCT_IS_STAT = [
        'NO'  => 0,
        'YES' => 1
    ];

    const IS_STAT_NAME = [
        0 => '否',
        1 => '是',
    ];

    // 满足的条件类型
    const C_MEET_TYPE = [
        'min' => 1, // 最少
        'max' => 2  // 最多
    ];

    public static function getTable()
    {
        return "`db_dreame_goods`.`t_market_config`";
    }


    public static function getDb()
    {
        return by::dbMaster();
    }

    public static function tableName()
    {
        return self::getTable();
    }

    /**
     * @return int
     * 清理缓存列表
     */
    private function __delListCache(): int
    {
        $r_key = AppCRedisKeys::marketConfigList();
        return by::redis()->del($r_key);
    }

    private function __delIdsByTabCache()
    {
        $r_key = AppCRedisKeys::marketConfigIdsByTab();
        return by::redis()->del($r_key);
    }


    /**
     * @param int|array $ids
     * 删除缓存
     * */
    public function _delCache($ids)
    {
        if (is_array($ids)) {
            foreach($ids as $id) {
                $r_key  = AppCRedisKeys::marketConfigINfo($id);
                $redis  = by::redis();
                $redis->del($r_key);
            }
        } else {
            $r_key  = AppCRedisKeys::marketConfigINfo($ids);
            $redis  = by::redis();
            $redis->del($r_key);
        }

    }

    /**
     * @param int $status
     * @param string $name
     * @param int $type
     * @param int $is_delete
     * @return int
     * @throws Exception
     * @throws \RedisException
     * 获取数量
     */
    public function getCount(int $status = -1, string $name = '', int $type = -1, int $is_delete = self::IS_DEL['no'], int $tab =0): int
    {
        $r_key      = AppCRedisKeys::marketConfigList();
        $h_key      = CUtil::getAllParams($status, $name, $is_delete, $tab);
        $count      = by::redis()->hGet($r_key, $h_key);

        if ($count === false) {
            $tb = self::getTable();

            $where                      = "is_delete=:is_delete";
            $params[':is_delete']       = $is_delete;

            if ($status > -1) {
                $where                 .= " AND status = :status";
                $params[':status']      = strip_tags($status);
            }

            if (!empty($name)) {
                $where                 .= " AND `name` like :name ";
                $params[':name']        = "%{$name}%";
            }

            if ($type > -1) {
                $where                 .= " AND `type` like :type ";
                $params[':type']        = "%{$type}%";
            }

            if($tab == 1){
                $ids = $this->__getEffectiveIdsByTab($tab);
                $idArr = implode("','",$ids);
                $idArr && $where .= " AND `id` in ('{$idArr}')";
            }elseif($tab == 2){
                $ids = $this->__getEffectiveIdsByTab($tab);
                $idArr = implode("','",$ids);
                $idArr && $where .= " AND `id` in ('{$idArr}')";
            }


            $sql    = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $count  = by::dbMaster()->createCommand($sql, $params)->queryScalar();

            by::redis('core')->hSet($r_key,$h_key,$count);
            CUtil::ResetExpire($r_key, self::ListExpire);
        }

        return intval($count);
    }

    /**
     * @throws Exception
     * @throws \RedisException
     */
    private function __getEffectiveIdsByTab($tab): array
    {
        $page_size  = 50;
        $count      = by::marketConfig()->getCount();
        $pages      = CUtil::getPaginationPages($count, $page_size);
        $num        = $pages;
        $r_key      = AppCRedisKeys::marketConfigIdsByTab();
        $h_key      = CUtil::getAllParams(__FUNCTION__,$tab);
        $aJson      = by::redis()->hGet($r_key, $h_key);
        $aData      = json_decode($aJson, true);

        if ($aJson === false) {
            $aData = [];
            for ($i = 1; $i <= $num; $i++) {
                $temp = by::marketConfig()->getList($i, 25);
                if ($tab == 1) {
                    $temp = array_map(function ($v) {
                        if ($this->checkOverdue($v)) {
                            return $v['id'] ?? 0;
                        } else {
                            return 0;
                        }
                    }, $temp);
                } else {
                    $temp = array_map(function ($v) {
                        if ($this->checkExpired($v)) {
                            return $v['id'] ?? 0;
                        } else {
                            return 0;
                        }
                    }, $temp);
                }
                $temp = array_values(array_filter($temp));
                $aData = array_merge($aData, $temp);
            }
            by::redis('core')->hSet($r_key, $h_key, json_encode($aData, 320));
            CUtil::ResetExpire($r_key, self::IdsListExpire);
        }
        return $aData;
    }

    /**
     * @param $value
     * @return bool
     * 未过期
     */
    public function checkOverdue($value): bool
    {
        $status = true;
        $valid_val = $value['valid_val'] ?? '';
        $valid_type = $value['valid_type'] ?? '';
        $valid_val_arr = explode('~', $valid_val);
        $lastTime = $valid_val_arr[1] ?? 0;
        if ($valid_type == 1 && $lastTime < date('Y-m-d H:i:s')) {
            $status = false;
        }
        return $status;
    }

    /**
     * @param $value
     * @return bool
     * 已过期
     */
    public function checkExpired($value): bool
    {
        $status = false;
        $valid_val = $value['valid_val'] ?? '';
        $valid_type = $value['valid_type'] ?? '';
        $valid_val_arr = explode('~', $valid_val);
        $lastTime = $valid_val_arr[1] ?? 0;
        if ($valid_type == 1 && $lastTime < date('Y-m-d')) {
            $status = true;
        }
        return $status;
    }


    /**
     * @param $page
     * @param $page_size
     * @param int $status
     * @param string $name
     * @param int $type
     * @param int $is_delete
     * @throws Exception
     * @throws \RedisException
     * 营销配置列表
     */
    public function getList($page, $page_size, int $status = -1, string $name = '', int $type = -1, int $is_delete = self::IS_DEL['no'], int $tab = 0)
    {
        $r_key      = AppCRedisKeys::marketConfigList();
        $h_key      = CUtil::getAllParams($page, $page_size, $status, $name, $is_delete,$tab);

        $aJson      = by::redis()->hGet($r_key, $h_key);
        $aData      = json_decode($aJson, true);
        if ($aJson === false) {
            $tb                         = self::getTable();

            $where                      = "is_delete = :is_delete";
            $params[':is_delete']       = $is_delete;

            if ($status > -1) {
                $where                 .= " AND status = :status";
                $params[':status']      = strip_tags($status);
            }

            if (!empty($name)) {
                $where                 .= " AND `name` like :name ";
                $params[':name']        = "%{$name}%";
            }

            if($type > -1) {
                $where                 .= " AND `type` = :type";
                $params[":type"]        = $type;
            }

            if($tab == 1){
                $ids = $this->__getEffectiveIdsByTab($tab);
                $idArr = implode("','",$ids);
                $where .= " AND `id` in ('{$idArr}')";
            }elseif($tab == 2){
                $ids = $this->__getEffectiveIdsByTab($tab);
                $idArr = implode("','",$ids);
                $where .= " AND `id` in ('{$idArr}')";
            }
            list($offset)               = CUtil::pagination($page, $page_size);

            $sql        = "SELECT `code`,`id`,`name`,`images`,`type`,`resource`,`stock_num`,`send_num`,`use_num`,`set_num`,`jump_goods_id`,`use_rule_note`,`status`,`product_is_stat` FROM {$tb} 
                            WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size} ";
            $aData      = by::dbMaster()->createCommand($sql, $params)->queryAll();

            $aData = array_map(function ($v) {
                $resource = json_decode($v['resource'], true);
                if ( isset($resource['discount']) && $resource['resource_type'] == self::RESOURCE_TYPE['discount'] ) {
                    $resource['discount'] = bcmul($resource['discount'], 100, 2);
                }
                unset($v['resource']);
                return array_merge($v, $resource);
            }, $aData);

            by::redis()->hSet($r_key, $h_key, json_encode($aData));
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return $aData;
    }

    /**
     * @param $name
     * @param $type
     * @param $status
     * @param $resource
     * @param $stock_num
     * @param $id
     * @return array
     * 添加编辑表单验证
     */
    protected function _modifyCheck($name, $type, $status, $resource, $stock_num, $id = 0,$web_name = ''): array
    {
        if ($stock_num != self::BIG_STOCK && ($stock_num < 0 || $stock_num > 9999999)) {
            return [false, '库存范围限制0-9999999'];
        }

        if ( !in_array($status, [self::STATUS['not_check'], self::STATUS['check']]) ) {
            return [false, '申请状态错误'];
        }

        $r_data = json_decode($resource, true);
        //优惠券或兑换券参数校验
        if ($type == self::TYPE['coupon'] || $type == self::TYPE['voucher'] || $type == self::TYPE['consume']) {
            $r_data = json_decode($resource, true);

            // 初始化数据
            if (!isset($r_data['c_meet_type'])) {
                $r_data['c_meet_type'] = self::C_MEET_TYPE['min'];
            }
            if (!isset($r_data['c_meet_num'])) {
                $r_data['c_meet_num'] = 1;
            }

            if ( !in_array($r_data['c_tag_type'], self::C_TAG_TYPE) ) {
                return [false, '限制条件错误'];
            }

            if ( !in_array($r_data['c_goods_type'], self::C_GOODS_TYPE) ) {
                return [false, '商品条件错误'];
            }

            if ( !in_array($r_data['c_meet_type'], self::C_MEET_TYPE) ) {
                return [false, '满足类型错误'];
            }

            //选择优惠券时，折扣优惠限制1-99；适用条件中最低金额限制1-9999
            if ($type == self::TYPE['coupon'] || $type == self::TYPE['consume']) {
                if ( !in_array($r_data['resource_type'], self::RESOURCE_TYPE) ) {
                    return [false, '资源类型错误'];
                }

                // 消费券不能创建折扣券
                if ($type == self::TYPE['consume'] && $r_data['resource_type'] == self::RESOURCE_TYPE['discount']) {
                    return [false, '消费券不能创建折扣券'];
                }

                if ($r_data['resource_type'] != self::RESOURCE_TYPE['discount'] &&
                    ($r_data['c_meet_type'] != self::C_MEET_TYPE['min'] || $r_data['c_meet_num'] != 1)) {
                    return [false, '非折扣券，不可设置限制条件'];
                }

                if ($r_data['resource_type'] == self::RESOURCE_TYPE['discount'] && $r_data['c_meet_num'] == 0) {
                    return [false, '折扣券，限制条件数量不能为0'];
                }

                if ( !in_array($r_data['c_amount_type'], self::C_AMOUNT_TYPE) ) {
                    return [false, '使用金额错误'];
                }

                $r_data['discount'] = sprintf("%.2f", $r_data['discount']);
                if ($r_data['resource_type'] == self::RESOURCE_TYPE['discount']) {
                    $r_data['discount'] = intval($r_data['discount']);
                    if ($r_data['discount'] < 1 || $r_data['discount'] > 99) {
                        return [false, '折扣优惠限制1-99'];
                    }
                    $r_data['discount'] = bcdiv($r_data['discount'], 100, 2);
                }else{
                    if ($r_data['discount'] <= 0){
                        return [false, '优惠券金额至少大于0元'];
                    }
                }

                if ($r_data['c_amount_type'] == self::C_AMOUNT_TYPE['lowest']) {
                    if ($r_data['c_amount_val'] < 1 || $r_data['c_amount_val'] > 9999) {
                        return [false, '最低订单金额限制1-9999'];
                    }
                    $r_data['c_amount_val'] = sprintf("%.2f", $r_data['c_amount_val']);
                }
            } else { // 非优惠券
                if ($r_data['c_meet_type'] != self::C_MEET_TYPE['min'] || $r_data['c_meet_num'] != 1) {
                    return [false, '非折扣券，不可设置限制条件'];
                }
            }

            if ($r_data['c_tag_type'] != self::C_TAG_TYPE['all']) {
                $tagCode = by::Gtag()->GetTagCodeMap();
                // $tags      = GtagModel::TAG;
                $tags      = $tagCode;
                $limit_num = count($tags) - 2;
                $c_tag_val = explode(',', $r_data['c_tag_val']);
                if (count($c_tag_val) > $limit_num) {
                    return [false, "最多添加{$limit_num}个标签"];
                }

                unset($tags['no']);
                if (array_diff($c_tag_val, $tags)) {
                    return [false, '请选择正确的标签'];
                }
            } else {
                $r_data['c_tag_val'] = '';
            }

            $r_data['jump_goods_id'] = CUtil::uint($r_data['jump_goods_id']);

        } else {
            if (empty($r_data['resource_num'])) {
                return [false, '请输入数量'];
            }

        }

        //优惠券、免邮卡、提示泡泡、排除卡、兑换券过期时间判断
        $valid_type = [];
        if ($type == self::TYPE['coupon'] || $type == self::TYPE['voucher'] || $type == self::TYPE['consume']) {
            $valid_type = self::VALID_TYPE;
        }

        if ( !empty($valid_type)) {
            //有效期校验
            if (!isset($r_data['valid_type']) || !in_array($r_data['valid_type'], $valid_type) ) {
                return [false, '有效期类型错误'];
            }
            if (empty($r_data['valid_val'])) {
                return [false, '请输入有效期'];
            }
            if ($r_data['valid_type'] == self::VALID_TYPE['fixed']) {

                list($start, $end) = CUtil::explodeDatePicker($r_data['valid_val']);
                if (empty($start) || empty($end)) {
                    return [false, '请输入有效的开始结束时间'];
                }

                if (empty($id)){
                    $expire_time = $end + 86399;
                    if ($expire_time < $start || $expire_time < START_TIME) {
                        return [false, '请输入正确的开始结束时间'];
                    }
                }

            } else {
                if (!strpos($r_data['valid_val'], '~')) {
                    $r_data['valid_val'] = '0~'.$r_data['valid_val'];
                }

                $time = explode('~', $r_data['valid_val']);

                $end  = isset($time[1]) ? CUtil::uint($time[1]) : 0;
                if ($end == 0) {
                    return [false, '请输入正确有效期多少天'];
                }
            }
        }

        //资源名称限制1-99字符
        if (!preg_match('/^[\x{4e00}-\x{9fa5}×\w]{1,99}$/u', $name)) {
            return [false, '资源名称只能包含中文字符、字母、数字、下划线或×，并且长度在1到99个字符之间'];
        }

        //判断名称是否重复
        $ret_name = self::find()->select('id, name')->where(['name' => $name, 'is_delete' => self::IS_DEL['no']])->one();
        if (!empty($ret_name) && ($ret_name['id'] != $id || $id == '')) {
            return [false, '资源名称已存在'];
        }

        return [true, json_encode($r_data)];

    }

    /**
     * @param $uinfo
     * @param $name
     * @param $type
     * @param $resource
     * @param $id
     * @param $status
     * @param array $data
     * @return array
     * 添加修改营销配置
     */
    public function modify($uinfo, $name, $type, $resource, $id, $status, array $data = [],$web_name = '')
    {
        //校验
        list($s, $ret) = $this->_modifyCheck($name, $type, $status, $resource, $data['stock_num'], $id,$web_name);
        if ( !$s ) {
            return [false, $ret];
        }

        try {
            $tb = self::getTable();

            $data['name']       = $name;
            $data['web_name']   = $web_name;
            $data['type']       = $type;
            $data['admin_id']   = $uinfo['id'] ?? 0;
            $data['resource']   = $ret;
            if ($status == self::STATUS['check']) {
                //判断权限
                $check_url    = 'back/market-config/audit'; //资源审核权限
                if (by::adminUserModel()->checkAuth($uinfo,$check_url)) {
                    $status = self::STATUS['check_success'];
                }
            }
            $data['status']     = $status;
            if ( empty($id) ) {
                // 唯一编码
                $data['code']         = 'YX'.date('YmdHis'). uniqid().rand(1000,9999);
                $data['create_time']   = intval(START_TIME);
                by::dbMaster()->createCommand()->insert($tb, $data)->execute();
                $id = by::dbMaster()->getLastInsertID();

            } else {
                $item = self::findOne(['id' => $id, 'is_delete' => self::IS_DEL['no']]);

                if (empty($item)) {
                    return [false, '数据不存在'];
                }
                if ($item['status'] == self::STATUS['check']) {
                    return [false, '审批中的数据不允许修改'];
                }
                $r_data=json_decode($data['resource'],true) ?? "";
                if ($r_data['valid_type'] == 1){
                    list($start, $end) = CUtil::explodeDatePicker($r_data['valid_val']);
                    $expire_time = $end + 86399;
                    if ($expire_time < $start || $expire_time < START_TIME) {
                        $data['status'] = 0;
                    }
                }
                //审核通过的数据能修改库存和图片及生效时间
                if ($item['status'] == self::STATUS['check_success']) {
                    $data = [
                        'stock_num' => $data['stock_num'] ?? $item['stock_num'],
                        'images'    => $data['images'] ?? $item['images'],
                        'resource'  => $data['resource'] ?? $item['resource'],
                        'status'    => $data['status'] ?? $item['status'],
                        'web_name'  => $data['web_name'] ?? $item['web_name']
                    ];
                }

                $data['update_time']   = intval(START_TIME);
                by::dbMaster()->createCommand()->update($tb, $data, ['id' => $id])->execute();
            }

            //删除缓存
            $this->_delCache($id);
            $this->__delListCache();
            $this->__delIdsByTabCache();

            return [true, json_encode($data,JSON_UNESCAPED_UNICODE)];

        } catch (\Exception $e) {

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();

            CUtil::debug($error, 'clarence-market-config');

            return [false, '操作失败'];
        }

    }

    /**
     * @param $id
     * @param mixed $is_del
     * @return array
     * @throws Exception
     * 详情
     */
    public function getOneById($id, $is_del = self::IS_DEL['no']): array
    {
        $id     = CUtil::uint($id);
        if ($id <= 0) {
            return [];
        }
        $r_key  = AppCRedisKeys::marketConfigINfo($id);
        $redis  = by::redis();
        $aJson  = $redis->get($r_key);
        $aData  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb   = self::getTable();
            $sql  = "SELECT `id`,`images`,`name`,`web_name`,`type`,`resource`,`stock_num`,`send_num`,`jump_goods_id`,
                            `status`,`is_delete`,`freight_free`,`product_is_stat`,`use_rule_note` FROM {$tb}
                     WHERE `id`=:id LIMIT 1";
            $info = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();

            if (!empty($info) ) {
                $resource = json_decode($info['resource'], true);

                if ( isset($resource['discount']) && isset($resource['resource_type']) && $resource['resource_type'] == self::RESOURCE_TYPE['discount'] ) {
                    $resource['discount'] = bcmul($resource['discount'], 100, 2);
                }
                unset($info['resource']);

                $aData              = array_merge($info, $resource);

                $redis->set($r_key, json_encode($aData), ['ex' => $this->expire_time]);
            } else {

                $redis->set($r_key, json_encode([]), ['ex' => 10]);
            }
        }
        if (empty($aData)) {
            return [];
        }
        $aData['freight_free'] = $aData['freight_free'] ?? 0; //hack
        if(!empty($aData) && $aData['valid_type'] == 1)
        {
            list($start,$end)   = explode('~', $aData['valid_val']);
            $aData['v_start']   = (string)strtotime($start);
            $aData['v_end']     = (string)strtotime($end);
        }

        if(!empty($aData) && $aData['valid_type'] == 2)
        {
            if (!strpos($aData['valid_val'], '~')) {
                $aData['valid_val'] = "0~".$aData['valid_val'];
            }

            list($start, $end)  = explode('~', $aData['valid_val']);

            $aData['v_start']   = $start;
            $aData['v_end']     = $end;
        }

        // 初始化c_meet_type、c_meet_num
        if (!isset($aData['c_meet_type'])) {
            $aData['c_meet_type'] = strval(self::C_MEET_TYPE['min']);
        }

        if (!isset($aData['c_meet_num'])) {
            $aData['c_meet_num'] = strval(1);
        }

        //hack
        if ($is_del == self::IS_DEL['no'] && $aData['is_delete'] == self::IS_DEL['yes']) {
            return [];
        }

        return $aData;
    }
    /**
     * @param $code
     * @param mixed $is_del
     * @return array
     * @throws Exception
     * 详情
     */
    public function getOneByCode($code, $is_del = self::IS_DEL['no']): array
    {
        $r_key  = AppCRedisKeys::marketConfigINfo($code);
        $redis  = by::redis();
        $aJson  = $redis->get($r_key);
        $aData  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb   = self::getTable();
            $sql  = "SELECT `id`,`images`,`name`,`web_name`,`type`,`resource`,`stock_num`,`send_num`,`jump_goods_id`,`code`,
                            `status`,`is_delete`,`freight_free`,`product_is_stat`,`use_rule_note` FROM {$tb}
                     WHERE `code`=:code LIMIT 1";
            $info = by::dbMaster()->createCommand($sql, [':code' => $code])->queryOne();

            if (!empty($info) ) {
                $aData = $info;
                $redis->set($r_key, json_encode($info), ['ex' => $this->expire_time]);
            } else {
                $redis->set($r_key, json_encode([]), ['ex' => 10]);
            }
        }
        if (empty($aData)) {
            return [];
        }
        if ($is_del == self::IS_DEL['no'] && $aData['is_delete'] == self::IS_DEL['yes']) {
            return [];
        }
        return $aData;
    }

    /**
     * 根据id批量查询（暂无缓存）
     * @param array $ids
     * @param array|string[] $columns
     * @return array
     * @throws Exception
     */
    public function getListByIds(array $ids, array $columns = ['id']): array
    {
        if (empty($ids)) {
            return [];
        }

        $tb = self::getTable();
        // 字段
        $columns = implode("`,`", $columns);
        // 条件
        $ids = implode(',', $ids);
        // 执行SQL
        $sql = "SELECT `{$columns}` FROM {$tb} WHERE `id` IN ({$ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }

    /**TODO 删除数据
     * @param array $ids
     * @return array
     * @throws Exception
     */
    public function del($ids = []): array
    {
        $tb         = self::getTable();
        $data       = ['is_delete' => self::IS_DEL['yes']];

        $can_status = [self::STATUS['not_check'], self::STATUS['check'], self::STATUS['check_failed']];
        $num = 0;
        try{
            $num = by::dbMaster()->createCommand()
            ->update($tb, $data, ['id' => $ids, 'status' => $can_status])->execute();
            $this->_delCache($ids);
            $this->__delListCache();
            return [true, $num];
        }catch(\Exception $e){
            return [false, $num];
        }
    }

    /**
     * @param int  $id
     * @param int $num 需要修改的数量
     * @return array
     * @throws \yii\db\StaleObjectException
     * TODO 库存数量修改
     */
    public function stockModify($id, $num): array
    {
        $recode = self::findOne($id);

        if (empty($recode)) {
            return [false, '数据不存在'];
        }

        if ($num == 0) {
            return [true, 0];
        }

        if ($recode->stock_num == self::BIG_STOCK) {
            return [true, 0];
        }
        if ($recode->stock_num < $num) {
            return [false, '库存数不足'];
        }
        if ($recode->send_num + $num < 0) {
            return [false, '该资源发放数据有误'];
        }

        try {
            $tb     = self::getTable();

            $sql    = "UPDATE {$tb} SET stock_num = stock_num - (:num), send_num = send_num + (:num_1) 
                        WHERE id = :id AND stock_num - (:num_2) >= 0";
            $params = [':num' => $num, ':id' => $id, ':num_1' =>$num, ':num_2' =>$num];
            $u_num  = by::dbMaster()->createCommand($sql, $params)->execute();

            if ($u_num == 0) {
                throw new \Exception('库存不够!');
            }

            $this->_delCache($id);
            $this->__delListCache();

            return [true, 0];

        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            trigger_error($error);
            return [false, '库存不够!'];
        }
    }

    /**
     * @param $id
     * @param $act
     * @param $uinfo
     * @return array
     * @throws Exception
     * 审核操作
     */
    public function audit($id, $act, $uinfo)
    {
        $info               = $this->getOneById($id);

        if (empty($info)) {
            return [false, '数据不存在'];
        }
        //判断资源是否过期
        if ($info['valid_type'] == 1){
            list($start, $end) = CUtil::explodeDatePicker($info['valid_val']);
            $expire_time = $end + 86399;
            if ($expire_time < $start || $expire_time < START_TIME) {
                return [false, '该资源已过期，审核不通过'];
            }
        }
        $tb                 = self::getTable();
        switch ($act) {
            case self::ACT['pass'] :
                $data   = ['status' => self::STATUS['check_success']];
                break;

            case self::ACT['apply'] :

                if ($info['status'] == self::STATUS['check_success']) {
                    return [false, '数据已审核通过！'];
                }
                //判断权限
                $check_url    = 'back/market-config/audit'; //资源审核权限
                if (by::adminUserModel()->checkAuth($uinfo,$check_url)) {
                    $data   = ['status' => self::STATUS['check_success']];
                }else{
                    $data   = ['status' => self::STATUS['check']];
                }
                break;

            case self::ACT['fail'] :
                $data   = ['status' => self::STATUS['check_failed']];
                break;

            case self::ACT['cancel'] :
                $data   = ['status' => self::STATUS['not_check']];
                if ($info['status'] == self::STATUS['check_success']) {
                    return [false, '数据已审核通过！'];
                }
                break;

            default :
                return [false, '不支持的操作'];
        }

        $num = by::dbMaster()->createCommand()->update($tb, $data, ['id' => $id])->execute();

        $this->_delCache($id);
        $this->__delListCache();
        $this->__delIdsByTabCache();

        return [true, $num];
    }

    /**
     * @param $market_id
     * @return array
     * @throws Exception
     * 优惠券限制条件使用范围转换
     */
    public function couponCondition($market_id)
    {
        $market_id = CUtil::uint($market_id);
        $market_data           = $this->getOneById($market_id);
        if(empty($market_data)) return [];
        $data['web_name']      = $market_data['web_name'] ?? '';
        $data['type']          = $market_data['type'] ?? '';
        $data['market_id']     = $market_id;
        $data['valid_type']    = $market_data['valid_type'] ?? '';
        $data['valid_val']     = $market_data['valid_val'] ?? '';
        $data['resource_type'] = $market_data['resource_type'] ?? 0;
        $data['jump_goods_id'] = $market_data['jump_goods_id'] ?? 0;
        $data['use_rule_note'] = $market_data['use_rule_note'] ?? 0;
        $data['goods_type']    = $market_data['c_goods_type'];
        $data['goods_val']     = $market_data['c_goods_val'];

        //折扣优惠、固定金额配置值，若配置的是折扣优惠券并且discount = 18.66，则为1.866折
        //优惠券名称
        if ($data['type'] == self::TYPE['coupon'] || $data['type'] == self::TYPE['consume']) {
            $type_name = $data['type'] == by::userCard()::TYPE['coupon'] ? '优惠券' : '消费券';
            list($data['discount'],$data['type_name'],$data['resource_name']) = $this->resourceType($data['resource_type'],$market_data['discount'],$type_name);
        }

        //兑换券名称
        if ($data['type'] == self::TYPE['voucher']) {
            $data['type_name'] = '兑换券';
        }

        //限制条件展示
        $data['condition']       = '无门槛';
        $data['condition_scope'] = '全场通用';
        if (isset($market_data['c_amount_type']) && $market_data['c_amount_type'] == self::C_AMOUNT_TYPE['lowest']) {
            //限制条件后配置值，满100可用就是100
            $data['condition'] = '满' . floatval($market_data['c_amount_val']) . '元可用';
        }

        // 折扣券
        if ($market_data['type'] == MarketConfigModel::TYPE['coupon'] && $market_data['resource_type'] == MarketConfigModel::RESOURCE_TYPE['discount']) {
            //限制条件后配置值，满N件可用
            $meet_type = $market_data['c_meet_type'] ?? MarketConfigModel::C_MEET_TYPE['min'];
            $meet_num = $market_data['c_meet_num'] ?? 1;

            if ($meet_type == MarketConfigModel::C_MEET_TYPE['min']) {
                $data['condition'] = '满' . $meet_num . '件可用';
            } else {
                $data['condition'] = $meet_num . '件内可用';
            }
        }

        //有效期
        list($data['start_time'], $data['expire_time']) = $this->expireTime($market_data['valid_type'] ?? '', $market_data['valid_val'] ?? '');

        if (!empty($market_data['c_tag_val']) && $market_data['c_tag_type'] != self::C_TAG_TYPE['all']) {
            $c_tag_val      = explode(',', $market_data['c_tag_val']);
            $c_tag_val      = array_slice($c_tag_val, 0, 2);
            $tagMap = by::Gtag()->GetTagNameMap();
            $tag_name       = $tagMap;
            $c_tag_name     = '';
            foreach ($c_tag_val as $tag) {
                $c_tag_name .= ($tag_name[$tag] ?? $tag). ',';
            }
            $c_tag_name = trim($c_tag_name, ',');

            $tag_count = count($c_tag_val);
            if (($data['type'] == self::TYPE['coupon'] || $data['type'] == self::TYPE['voucher']) && $tag_count >= 2) {
                $c_tag_name = "部分标签";
            }

            $c_tag_type = $market_data['c_tag_type'] == self::C_TAG_TYPE['apply']? '可用' :'不可用';
            $data['condition_scope'] = "仅{$c_tag_name}{$c_tag_type}";
        } else {

            if (!empty($market_data['c_goods_val'])) {
                $c_goods_val    = explode(',', $market_data['c_goods_val']);

                $c_goods_name   = '';
                $is_goods       = 0;
                foreach ($c_goods_val as $gid) {
                    $ginfo = by::Gmain()->GetAllOneByGid($gid);
                    if (empty($ginfo)) {
                        continue;
                    }
                    $is_goods += 1;
                    $is_goods <= 2 && $c_goods_name .= '、'. $ginfo['name'];
                    if ($is_goods >= 3) {
                        $c_goods_name .= '等';
                        break;
                    }
                }

                if (!empty($c_goods_name)) {
                    $c_goods_name = ltrim($c_goods_name, '、');

                    $goods_count = count($c_goods_val);
                    if (($data['type'] == self::TYPE['coupon'] || $data['type'] == self::TYPE['voucher']) && $goods_count >= 2) {
                        $c_goods_name = "部分商品";
                    }

                    $c_goods_type = $market_data['c_goods_type'] == self::C_GOODS_TYPE['apply'] ? '可用' : '不可用';
                    $data['condition_scope'] = "仅{$c_goods_name}{$c_goods_type}";
                }
            }
        }

        return $data;
    }

    /**
     * @param $market_id
     * @return array
     * @throws Exception
     * 判断优惠券 免邮卡是否过期
     */
    public function checkTime($market_id, $type = self::MSG_TYPE['manage']){
        $marketData = self::getOneById($market_id);
        if(!empty($marketData) && $marketData['valid_type'] == 1)
        {
            $expireTime = explode('~', $marketData['valid_val']);
            if(strtotime($expireTime[1]) <= time())
            {
                return [false, $type == self::MSG_TYPE['web'] ? '商品已过期，请重新选择！' : '营销资源已过期，请重新选择！'];
            }
        }
        return [true, 'ok'];
    }

    /**
     * @param $resource_type
     * @param $discount
     * @return array
     * 资源类型
     */
    public function resourceType($resource_type, $discount, $type_name = '优惠券')
    {
        switch ($resource_type) {
            case 1:
                $discount      = $discount / 10;
                $resource_name = $discount . '折' . $type_name;
                break;
            default:
                $discount      = floatval($discount);
                $resource_name = $discount . '元' . $type_name;
                break;
        }
        return [$discount, $type_name, $resource_name];
    }

    /**
     * @param $valid_type
     * @param $valid_val
     * @return array
     * 获取有效期
     */
    public function expireTime($valid_type,$valid_val): array
    {
        $expire_time = $start_time = 0;
        switch ($valid_type) {
            case by::marketConfig()::VALID_TYPE['fixed'] :
                list($start_time,$expire_time) = CUtil::explodeDatePicker($valid_val);
                break;
            case by::marketConfig()::VALID_TYPE['get_after'] :
                if (!strpos($valid_val, '~')) {
                    $valid_val = '0~'.$valid_val;
                }

                $time        = explode('~', $valid_val);
                $start       = isset($time[0]) ? CUtil::uint($time[0]) : 0;
                $end         = isset($time[1]) ? CUtil::uint($time[1]) : 0;
                $d           = date("Y-m-d", time());
                $start_time  = strtotime($d) + $start * 86400;
                $expire_time = $start_time + $end * 86400 - 1;

                break;
        }

        return [$start_time, $expire_time];
    }

    /**
     * @param $id
     * @param $num
     * @return array
     * 修改已使用数量
     */
    public function useNumModify($id,$num=1){

        $recode = self::findOne($id);
        if (empty($recode)) {
            return [false, '数据不存在'];
        }

        try {
            $tb     = self::getTable();

            $sql    = "UPDATE {$tb} SET use_num = use_num + (:num) WHERE id = :id";
            $params = [':num' => $num, ':id' => $id];
            $u_num  = by::dbMaster()->createCommand($sql, $params)->execute();

            if ($u_num == 0) {
                throw new \Exception('修改失败!');
            }

            $this->_delCache($id);
            $this->__delListCache();

            return [true, 'ok'];

        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            trigger_error($error);
            return [false, '修改失败'];
        }
    }

    /**
     * @param $id
     * @param $num
     * @return array
     * 修改已领取数量
     */
    public function setNumModify($id,$num=1){

        $recode = self::findOne($id);
        if (empty($recode)) {
            return [false, '数据不存在'];
        }

        try {
            $tb     = self::getTable();

            $sql    = "UPDATE {$tb} SET set_num = set_num + (:num) WHERE id = :id";
            $params = [':num' => $num, ':id' => $id];
            $u_num  = by::dbMaster()->createCommand($sql, $params)->execute();

            if ($u_num == 0) {
                throw new \Exception('修改失败!');
            }

            $this->_delCache($id);
            $this->__delListCache();

            return [true, 'ok'];

        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            trigger_error($error);
            return [false, '修改失败'];
        }
    }

//    /**
//     * @param $id
//     * @param $num
//     * @return array
//     * 修改废除后的库存数量及领取数量
//     */
//    public function delNumModify($id,$num=1){
//
//        $recode = self::findOne($id);
//        if (empty($recode)) {
//            return [false, '数据不存在'];
//        }
//
//        try {
//            $tb     = self::getTable();
//
//            $sql    = "UPDATE {$tb} SET set_num = set_num - (:num),stock_num=stock_num+1 WHERE id = :id";
//            $params = [':num' => $num, ':id' => $id];
//            $u_num  = by::dbMaster()->createCommand($sql, $params)->execute();
//
//            if ($u_num == 0) {
//                throw new \Exception('修改失败!');
//            }
//
//            $this->_delCache($id);
//            $this->__delListCache();
//
//            return [true, 'ok'];
//
//        } catch (\Exception $_e) {
//            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
//            trigger_error($error);
//            return [false, '修改失败'];
//        }
//    }

    /**
     * @param $id
     * @param $product_is_stat
     * @return array
     * @throws Exception
     */
    public function upIsStat($id, $product_is_stat): array
    {
        $id = CUtil::uint($id);
        if(!isset($product_is_stat) || empty($id)){
            return [false,' 缺少参数'];
        }

        if (!in_array($product_is_stat, self::PRODUCT_IS_STAT)){
            return [false, '是否统计值不合法'];
        }

        $info = $this->getOneById($id);
        if (!$info){
            return [false, '营销资源不存在'];
        }

        by::dbMaster()->createCommand()->update(
            self::getTable(),
            ['product_is_stat' => $product_is_stat, 'update_time' => time()],
            "`id`=:id",
            [':id' => $id]
        )->execute();

        $this->_delCache($id);
        $this->__delListCache();

        return [true, 'ok'];
    }

    /**
     * @param string $name
     * @param int $status
     * @param int $type
     * @param int $is_delete
     * 营销列表导出
     */
    public function export(string $name = '', int $status = -1, int $type = -1, int $is_delete = self::IS_DEL['no'])
    {
        $head   = ['资源名称', '营销资源类型', '总库存', '已发放数', '已领取数', '已使用数', '剩余库存', '审核状态', '是否统计'];
        $f_name = '营销列表' . date('Ymd') . mt_rand(1000, 9999);
        $tb     = self::tableName();

        $where                = "is_delete = :is_delete";
        $params[':is_delete'] = $is_delete;

        if ($status > -1) {
        $where            .= " AND status = :status";
        $params[':status'] = strip_tags($status);
        }

        if (!empty($name)) {
        $where          .= " AND `name` like :name ";
        $params[':name'] = "%{$name}%";
        }

        if($type > -1) {
        $where           .= " AND `type` = :type";
        $params[":type"]  = $type;
        }

        //导出
        CUtil::export_csv_new($head, function () use($tb, $where, $params)
        {
            $db  = $this->getDb();
            $id  = 0;
            $sql = "SELECT `id`,`name`,`type`,`stock_num`,`send_num`,`use_num`,`set_num`,`status`,`product_is_stat` FROM {$tb} 
                            WHERE `id` > :id AND {$where} ORDER BY `id` ASC LIMIT 200";

            while (true) {
                $params['id'] = $id;
                $list = $db->createCommand($sql, $params)->queryAll();
                if (empty($list)) {
                    break;
                }

                $end = end($list);
                $id  = $end['id'];
                $data = [];

                foreach ($list as $info) {
                    $data[] = [
                        'name'            => $info['name'],
                        'type'            => self::TYPE_NAME[$info['type']],
                        'total_stock'     => $info['stock_num'] + $info['send_num'],
                        'send_num'        => $info['send_num'],
                        'set_num'         => $info['set_num'],
                        'use_num'         => $info['use_num'],
                        'stock_num'       => $info['stock_num'],
                        'status'          => self::STATUS_NAME[$info['status']],
                        'product_is_stat' => self::IS_STAT_NAME[$info['product_is_stat']]
                    ];
                }

                yield $data;
            }

        }, $f_name);
    }

    public function exportData(string $name = '', int $status = -1, int $type = -1, int $is_delete = self::IS_DEL['no'])
    {
        $head   = ['资源名称', '营销资源类型', '总库存', '已发放数', '已领取数', '已使用数', '剩余库存', '审核状态', '是否统计'];

        $tb     = self::tableName();

        $where                = "is_delete = :is_delete";
        $params[':is_delete'] = $is_delete;

        if ($status > -1) {
            $where            .= " AND status = :status";
            $params[':status'] = strip_tags($status);
        }

        if (!empty($name)) {
            $where          .= " AND `name` like :name ";
            $params[':name'] = "%{$name}%";
        }

        if($type > -1) {
            $where           .= " AND `type` = :type";
            $params[":type"]  = $type;
        }

        //导出
        $db  = $this->getDb();
        $id  = 0;
        $sql = "SELECT `id`,`name`,`type`,`stock_num`,`send_num`,`use_num`,`set_num`,`status`,`product_is_stat` FROM {$tb} 
                        WHERE `id` > :id AND {$where} ORDER BY `id` ASC LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id'] = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];

            foreach ($list as $info) {
                $data[] = [
                    'name'            => $info['name'],
                    'type'            => self::TYPE_NAME[$info['type']],
                    'total_stock'     => $info['stock_num'] + $info['send_num'],
                    'send_num'        => $info['send_num'],
                    'set_num'         => $info['set_num'],
                    'use_num'         => $info['use_num'],
                    'stock_num'       => $info['stock_num'],
                    'status'          => self::STATUS_NAME[$info['status']],
                    'product_is_stat' => self::IS_STAT_NAME[$info['product_is_stat']]
                ];
            }
        }
        return $data;
    }
}
