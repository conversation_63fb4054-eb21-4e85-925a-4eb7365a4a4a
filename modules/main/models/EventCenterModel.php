<?php


namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\models\by;
use app\models\CUtil;
use yii\db\Expression;


class EventCenterModel extends CommModel

{
    CONST VIEW_GOODS_LIMIT = 15;
    CONST VIEW_GOODS_EXPIRE = YII_ENV_PROD ? 24 *3600 : 24*3600;//浏览15s


    CONST WATCH_LIVE_EXPIRE = YII_ENV_PROD ? 7*24*3600 : 24*3600;//进入直播间

    CONST GOODS_ADD_CART_EXPIRE = YII_ENV_PROD ? 7*24*3600 : 24*3600;//商品加入购物车
    CONST SHARE_GOODS_EXPIRE = YII_ENV_PROD ? 7*24*3600 : 24*3600;//分享商品

    private function __viewGoodsKey($user_id,$gid = 0): string
    {
        return AppCRedisKeys::userViewGoods($user_id,$gid);
    }

    private function __goodsAddCart($user_id): string
    {
        return AppCRedisKeys::userAddCart($user_id);
    }

    private function __shareGoods($user_id): string
    {
        return AppCRedisKeys::userShareGoods($user_id);
    }

    private function __watchLive($user_id):string
    {
        return AppCRedisKeys::watchLive($user_id);
    }

    public function viewGoods($user_id, $gid = 0, $type = 'check')
    {
        $redis     = by::redis('core');
        $redis_key = $this->__viewGoodsKey($user_id, $gid);
        $aJson     = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($type == 'check'){
            if($aJson === false){
                //返回构造的倒计时事件
                $aData      = [
                    'event'=> 'count_down',
                    'view_goods_limit'=>self::VIEW_GOODS_LIMIT,
                    'current_time'=> intval(START_TIME),
                    'msg'=>'开始倒计时！'
                ];
                $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::VIEW_GOODS_EXPIRE]);
            }else{
                $event = $aData['event'] ?? '';
                if($event == 'view_push'){
                    //时间校验
                    $currentTime = $aData['current_time'] ?? 0;
                    $nowTime = intval(START_TIME);
                    $c = YII_ENV_PROD ? $this->__getWeekByTime($currentTime,true) : date('Y-m-d',$currentTime);
                    $n = YII_ENV_PROD ? $this->__getWeekByTime($nowTime,true) : date('Y-m-d',$nowTime);
                    if ($n !== $c) {
                        $aData      = [
                            'event'=> 'count_down',
                            'view_goods_limit'=>self::VIEW_GOODS_LIMIT,
                            'current_time'=> intval(START_TIME),
                            'msg'=>'开始倒计时！'
                        ];
                        $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::VIEW_GOODS_EXPIRE]);
                        return [true,$aData];
                    }
                }
            }
            return [true,$aData];
        }


        if($type == 'push'){
            if($aJson === false){
                $aData = [
                    'event'            => '',
                    'view_goods_limit' => 0,
                    'current_time'     => intval(START_TIME),
                    'msg'=> '倒计时不存在，应该先查看~！'
                ];
            }else{
                $event = $aData['event'] ?? '';
                if($event == 'count_down'){
                    //信息推送事件
                    EventMsg::factory()->run( 'viewGoods', ['user_id'=>$user_id]);
                    $aData = [
                        'event'            => 'view_push',
                        'view_goods_limit' => 0,
                        'current_time'     => intval(START_TIME),
                        'msg'=> '推送成功~！'
                    ];
                    $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::VIEW_GOODS_EXPIRE]);
                    return [true,$aData];
                }
            }
            return [true,$aData];
        }

        $aData      = [
            'event'=> '',
            'view_goods_limit'=>0,
            'current_time'=> intval(START_TIME),
            'msg'=>'传递参数有误！'
        ];
        return [true,$aData];
    }


    /**
     * @param $user_id
     * @return array
     * 直播 一周触发一次
     */
    public function watchLive($user_id): array
    {
        $redis     = by::redis('core');
        $redis_key = $this->__watchLive($user_id);
        $aJson     = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if ($aJson === false) {
            //没有在限制时间内可以触发事件
            //信息推送事件
            EventMsg::factory()->run('watchLive', ['user_id' => $user_id]);
            $aData = [
                'event' => 'watchLive',
                'cart_time' => intval(START_TIME),
                'msg' => '推送成功~！'
            ];
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::WATCH_LIVE_EXPIRE]);
        } else {
            $event = $aData['event'] ?? '';
            if ($event == 'watchLive') {
                //时间校验
                $cartTime = $aData['cart_time'] ?? 0;
                $nowTime = intval(START_TIME);
                $c = YII_ENV_PROD ? $this->__getWeekByTime($cartTime,true) : date('Y-m-d', $cartTime);
                $n = YII_ENV_PROD ? $this->__getWeekByTime($nowTime,true) : date('Y-m-d', $nowTime);
                if ($n !== $c) {
                    EventMsg::factory()->run('watchLive', ['user_id' => $user_id]);
                    $aData = [
                        'event' => 'watchLive',
                        'cart_time' => intval(START_TIME),
                        'msg' => '推送成功~！'
                    ];
                    $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::WATCH_LIVE_EXPIRE]);
                    return [true, $aData];
                }
            }
            $aData = [
                'event' => '',
                'cart_time' => intval(START_TIME),
                'msg' => '本周已经推送过！'
            ];
        }
        return [true, $aData];
    }

    /**
     * @param $user_id
     * @return array
     * 商品加入购物车一周触发一次
     */
    public function goodsAddCart($user_id)
    {
        $redis = by::redis('core');
        $redis_key = $this->__goodsAddCart($user_id);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        $end_of_week = strtotime('next Sunday 23:59:59');
        if ($aJson === false) {
            //没有在限制时间内可以触发事件
            //信息推送事件
            EventMsg::factory()->run('addChart', ['user_id' => $user_id]);
            $aData = [
                'event' => 'addChart',
                'cart_time' => intval(START_TIME),
                'msg' => '推送成功~！'
            ];
            $cart_time = $aData['cart_time'];
            $expire_time = $end_of_week - $cart_time;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : $expire_time]);
        }
        return [true, $aData];
    }

    /**
     * @param $user_id
     * @return array
     * 分享商品一周触发一次
     */
    public function shareGoods($user_id)
    {
        $redis = by::redis('core');
        $redis_key = $this->__shareGoods($user_id);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            //没有在限制时间内可以触发事件
            //信息推送事件
            EventMsg::factory()->run('shareGoods', ['user_id' => $user_id]);
            $aData = [
                'event' => 'share_goods',
                'share_time' => intval(START_TIME),
                'msg' => '推送成功~！'
            ];
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::SHARE_GOODS_EXPIRE]);
            return [true, $aData];
        } else {
            $event = $aData['event'] ?? '';
            if ($event == 'share_goods') {
                //时间校验
                $shareTime = $aData['share_time'] ?? 0;
                $nowTime = intval(START_TIME);
                $c = YII_ENV_PROD ? $this->__getWeekByTime($shareTime,true) : date('Y-m-d', $shareTime);
                $n = YII_ENV_PROD ? $this->__getWeekByTime($nowTime,true) : date('Y-m-d', $nowTime);
                if ($n !== $c) {
                    EventMsg::factory()->run('shareGoods', ['user_id' => $user_id]);
                    $aData = [
                        'event' => 'share_goods',
                        'share_time' => intval(START_TIME),
                        'msg' => '推送成功~！'
                    ];
                    $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::SHARE_GOODS_EXPIRE]);
                    return [true, $aData];
                }
            }
            $aData = [
                'event' => '',
                'share_time' => intval(START_TIME),
                'msg' => '本周已经推送过！'
            ];
            return [true, $aData];
        }
    }


    /**
     * @param $time
     * @param bool $year
     * @return false|string
     */
    private function __getWeekByTime($time, bool $year=false){
        if(!is_numeric($time)){
            $time = strtotime($time);
        }

        if($year){
            return date('Y').'-'.date('W', $time);
        }
        return date('W', $time);
    }

}
