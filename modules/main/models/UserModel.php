<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\Employee;
use app\components\GoDreameMall;
use app\components\Mall;
use app\components\Review;
use app\components\WeiXin;
use app\jobs\UpdateCoCreateUserJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\enums\user\UserInfoEnum;
use RedisException;

class UserModel extends CommModel
{

    public static $expire = 1800;

    //0：保密，1：男，2：女  PHP MYSQL 枚举类型问题较多
    const USER_SEX = ['S' => 0, 'M' => 1, 'W' => 2];
    const USER_SEX_TXT = [0 => '保密', 1 => '男', 2 => '女'];


    //1 QQ, 99游客
    const USER_TYPE = [
        'QQ' => 1, 'VISITOR' => 99
    ];

	//针对不同场景 授权可修改字段
	CONST SCENARIOS = [
		'LOGIN'  => [ 'sex', 'nick', 'age', 'avatar', 'active_time'],
		'MODIFY' => [ 'sex', 'nick', 'age', 'avatar','real_name','birthday','area','is_new_gift'],
		'AUTH'   => [ 'oa_openid'],
        'MAIN'   => ['sex', 'nick', 'age', 'avatar','real_name','birthday','area','is_new_gift','openudid','unionid','reg_time','update_time']

	];

	CONST STATUS = [
        0   =>'正常',
        1   =>'冻结',
        2   =>'平台禁用'
    ];

    // 会员等级，与会员中心保持一致，如果会员中心有变动，则需要相应修改。
    CONST USER_LEVEL = [
        'v1'   =>'铜牌',
        'v2'   =>'银牌',
        'v3'   =>'金牌',
        'v4'   =>'钻石',
        'v5'   =>'黑金',
    ];

    public static function userMainTb(): string
    {
        return '`db_dreame`.`t_users_main`';
    }

    public static function tableName()
    {
        return self::userMainTb();
    }

    public $user_tb_fields = [
        'id','user_id','nick','real_name','birthday','status','age','avatar','active_time','sex','area','is_new_gift',
        'reg_reward_point','reg_reward_count','reg_popup_count','coupon_reward_count','coupon_popup_count','point_reward_count','point_popup_count'
    ];


    /**
     * @param $user_id
     * @return string
     * 分表user tb
     */
    public static function userTb($user_id): string
    {
        $user_id = CUtil::uint($user_id);
        $mod     = $user_id % 100;

        return "`db_dreame`.`t_user_{$mod}`";
    }

    /**
     * @param $nick
     * @param int $length
     * @return string
     * 昵称截取
     */
    public function validateNick($nick, $length = 32): string
    {
        return mb_substr(trim($nick), 0, $length, 'utf-8');
    }


    private function __getOneByUid($user_id): string
    {
        return AppCRedisKeys::getOneByUid($user_id);
    }

    private function __userMainInfo($user_id): string
    {
        return AppCRedisKeys::userMainInfo($user_id);
    }

    private function __getWxUserByPhone($phone): string
    {
        return AppCRedisKeys::getWxUserByPhone($phone);
    }

    private function __getRealMainUserByIdKey($user_id)
    {
        return AppCRedisKeys::getRealMainUserByIdKey($user_id);
    }

    private function __delCache($user_id = 0, $phone = 0)
    {
        if($user_id){
            $redis_key   = $this->__getOneByUid($user_id);
            $redis_key_1   = $this->__userMainInfo($user_id);
            $redis_key_2   = $this->__getRealMainUserByIdKey($user_id);

            by::redis()->del($redis_key);
            by::redis()->del($redis_key_1);
            by::redis()->del($redis_key_2);
        }

        if($phone){
            $redis_key   = $this->__getWxUserByPhone($phone);
            by::redis()->del($redis_key);
        }

    }

    public function selfDelCache($user_id = 0, $phone = 0)
    {
        $this -> __delCache($user_id,$phone);
    }

    //合规化参数，方便注册与更新
    private function __doWellUserTbFields($data): array
    {
        $returnData = [];
        $fields = $this->user_tb_fields;
        foreach ($data as $key=>$item){
            if(in_array($key,$fields)){
                $returnData[$key] = $item;
            }
        }
        return $returnData;
    }

    /**
     * @param string $openudid
     * @param string $user_type
     * @return int
     * @throws \yii\db\Exception
     * 根据 $openudid  $user_type 获取 user_id
     */
    public function getUserIdByOpenUdId(string $openudid = '', string $user_type = ''): int
    {
        $loginType   = 2; // 会员
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::getUserIdByOpenUdId($openudid, $user_type, $loginType);
        $user_id     = $redis->get($session_key);
        if ($user_id === false) {
            $tb      = self::userMainTb();
            $sql     = "SELECT `user_id` From {$tb} WHERE `openudid`=:openudid  LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':openudid' => $openudid])->queryOne();
            $user_id = $aData['user_id'] ?? 0;
            $redis->set($session_key, $user_id, ['EX' => $user_id ? self::$expire : 10]);
        }

        return CUtil::uint($user_id);
    }
    /**
     * @param string $user_id
     * @param string $user_type
     * @return int
     * @throws \yii\db\Exception
     * 根据 $user_id  $user_type 获取 openudid
     */
    public function getOpenUdIdByUserId($user_id)
    {
        $tb         = self::userMainTb();
        $sql        = "SELECT `openudid` From {$tb} WHERE `user_id`=:user_id  LIMIT 1";
        $aData      = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id])->queryOne();
        $openudid    = $aData['openudid'] ?? ''; 
        return $openudid;
    }

    /**
     * @param string $unionid
     * @param string $user_type
     * @return mixed
     * @throws RedisException
     * @throws \yii\db\Exception
     * 根据 $unionid  $user_type 获取 user_id
     */
    public function getUserIdByUnionId(string $unionid = '', string $user_type = '')
    {
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::getUserIdByUnionId($unionid, $user_type);
        $user_id     = $redis->get($session_key);
        if ($user_id === false) {
            $tb      = self::userMainTb();
            $sql     = "SELECT `user_id` From {$tb} WHERE `unionid`=:unionid AND locate('|',`openudid`)=0 ORDER BY `user_id` DESC LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':unionid' => $unionid])->queryOne();
            $user_id = $aData['user_id'] ?? 0;
            $redis->set($session_key, $user_id, ['EX' => $user_id ? self::$expire : 10]);
        }

        return CUtil::uint($user_id);
    }
    /**
     * @param string $user_id
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据user_id 获取用户信息
     */
    public function getOneByUid(string $user_id, $cache = true)
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id <= 0) {
            return [];
        }

        $redis      = by::redis('core');
        $redis_key  = AppCRedisKeys::getOneByUid($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info  = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $user_tb = $this->userTb($user_id);
            $sql     = "SELECT * FROM {$user_tb} WHERE `user_id`=:user_id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);

            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            !empty($user_info) && $redis->set($redis_key, json_encode($user_info), ['EX' => self::$expire]);
        }
	    if(!empty($user_info['area'])){
		    $user_info['area'] = json_decode($user_info['area'],true);
	    }
        return $user_info;
    }

    /**
     * 组装用户详情多分表SQL
     * @param array $user_ids
     * @return string
     */
    public function packUserInfoListFromAllTableSql(array $user_ids): string
    {
        $sql = [];
        foreach ($user_ids as $user_id) {
            $userTb = $this->userTb($user_id);
            $sql[] = "SELECT * FROM {$userTb} WHERE `user_id` = {$user_id}";
        }

        if (empty($sql)) {
            return '';
        }

        return implode(' UNION ALL ', (array) $sql);
    }

    /**
     * 获取用户信息列表
     * @param array $user_ids
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public function getUserInfoListFromAllTable(array $user_ids)
    {
        $sql = $this->packUserInfoListFromAllTableSql($user_ids);
        $command = by::dbMaster()->createCommand($sql);
        return $command->queryAll();
    }

    public function delOneCache($user_id)
    {
        $redis_key   = $this->__getOneByUid($user_id);
        by::redis()->del($redis_key);
    }

    /**
     * @param int $user_id
     * @return bool
     * @throws \yii\db\Exception
     * 删除 各类redis缓存
     */
    public function deleteRedisCache($user_id = 0 , $phone = 0): bool
    {
        if($user_id){
            $user       = $this->getUserMainInfo($user_id);
            $openudid   = $user['openudid']  ?? '';
            $user_type  = $user['user_type'] ?? 0;
            $this->delUserMainCacheByOpenudid($openudid, $user_type);

            $this->delUserMainCache($user_id);
            $this->delUserCache($user_id);
            $redis_key   = $this->__getOneByUid($user_id);
            $redis_key_1   = $this->__userMainInfo($user_id);
            $redis_key_2   = $this->__getRealMainUserByIdKey($user_id);
            by::redis()->del($redis_key,$redis_key_1,$redis_key_2);
        }

        if($phone){
            $redis_key   = $this->__getWxUserByPhone($phone);
            by::redis()->del($redis_key);
        }
        return true;
    }

    /**
     * @param $openudid
     * @param $user_type
     * @return array
     * @throws \yii\db\Exception
     * 获取用户信息
     */
    public function getOneByOpenUdId($openudid, $user_type): array
    {
        $user_id = $this->getUserIdByOpenUdId($openudid, $user_type);

        if ($user_id <= 0) {
            return [];
        }
        return by::users()->getOneByUid($user_id);
    }

    /**
     * @throws \yii\db\Exception
     */
    public function getRealMainUserById(int $user_id , int $user_type = 1, $cache = true)
    {
        if (empty(CUtil::uint($user_id))) {
            return [];
        }
        $redis      = by::redis('core');
        $redis_key  = $this->__getRealMainUserByIdKey($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = $this -> userMainTb();
            $sql     = "SELECT * FROM {$tb} WHERE `user_id`=:user_id AND `user_type`= :user_type AND locate('|',`openudid`)=0 LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);
            $command->bindParam(":user_type", $user_type);
            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            !empty($user_info) && $redis->set($redis_key, json_encode($user_info), ['EX' =>  self::$expire]);
        }

        return $user_info;
    }


    /**
     * @param $sex
     * @return bool
     * 检测性别是否合法
     */
    public function isValidSex($sex): bool
    {
        $sex = CUtil::uint($sex);
        return in_array($sex, self::USER_SEX, true);
    }

    /**
     * @param $userInfo
     * @return array
     * @throws \yii\db\Exception
     * 新用户注册
     */
    public function register($userInfo,$antiConcurrency=true): array
    {
        $user_type      = isset($userInfo['user_type']) ? intval($userInfo['user_type']) : 0;
        $loginControl   = CUtil::getConfig('loginControl', 'common', MAIN_MODULE);
        $uTypes         = is_array($loginControl) ? array_keys($loginControl) : "";
        if (empty($uTypes)) {
            return [false, "系统用户类型不存在！"];
        }

        if (empty($userInfo) || empty($userInfo['openudid']) || ($user_type <= 0) ||
            (!in_array($user_type, $uTypes, true))) {
            return [false, "必要注册信息缺失：" . json_encode($userInfo)];
        }

        //防止多次请求注册
        if($antiConcurrency){
            $unique_key = __FUNCTION__ . "|{$userInfo['openudid']}|2";
            list($anti) = self::ReqAntiConcurrency(0, $unique_key, 3, 'EX');
            if (!$anti) {
                return [false, '重复多次注册请求~~：' . json_encode($userInfo)];
            }
        }

        $nick           = empty($userInfo['nick']) ? '' : $this->validateNick($userInfo['nick']);
        $nick           = trim($nick);
        $connection     = by::dbMaster();
        $transaction    = $connection->beginTransaction();
        try {
            $userDetail['openudid'] = $userInfo['openudid'];
            $userDetail['unionid']  = $userInfo['unionid']??'';
            $userDetail['nick']     = $nick;
            $userDetail['avatar']   = $userInfo['avatar'] ?? '';
            $userDetail['age']      = isset($userInfo['age']) ? CUtil::uint($userInfo['age']) : 22;
            $userDetail['sex']      = isset($userInfo['sex']) ? CUtil::uint($userInfo['sex']) : 0;
            $userDetail['birthday'] = isset($userInfo['birthday']) ? CUtil::uint($userInfo['birthday']) : 0;
            extract($userDetail);
            $reg_time = time();
            $update_time = time();
            $tb_main  = self::userMainTb();

            $query    = " INSERT IGNORE INTO {$tb_main} (`openudid`,`unionid`,`reg_time`, `user_type`, `update_time`)
                          VALUE (:openudid, :unionid, :reg_time, :user_type, :update_time) ";
            $command  = $connection->createCommand($query);
            $command->bindParam(":openudid", $openudid);
            $command->bindParam(":reg_time", $reg_time);
            $command->bindParam(":update_time", $update_time);
            $command->bindParam(":user_type",$user_type);
            $command->bindParam(":unionid",$unionid);
            $command->execute();
            $userDetail['user_id']          = $connection->getLastInsertId();
            $userDetail['isRegisterFlag']   = $userDetail['user_id'] > 0 ? 1 : 0;

            if ($userDetail['user_id']) {
                $tb_detail = self::userTb($userDetail['user_id']);
                $sql       = "INSERT INTO {$tb_detail} (`user_id`,`sex`,`nick`,`age`,`avatar`,`birthday`) 
                            VALUE (:user_id, :sex, :nick,:age,:avatar,:birthday) ";
                $command = $connection->createCommand($sql);
                $command->bindParam(":user_id", $userDetail['user_id']);
                $command->bindParam(":sex", $userDetail['sex']);
                $command->bindParam(":nick", $userDetail['nick']);
                $command->bindParam(":age", $age);
                $command->bindParam(":avatar", $userDetail['avatar']);
                $command->bindParam(":birthday", $userDetail['birthday']);
                //@return integer number of rows affected by the execution.
                $insert_flag = $command->execute();
                if (empty($insert_flag)) {
                    throw new \Exception("注册用户详情表失败|insert_flag:{$insert_flag}");
                }
            } else {
                throw new \Exception("注册插入主表失败|user_id:{$userDetail['user_id']}");
            }

            $transaction->commit();

            if($antiConcurrency){
                self::ReqAntiConcurrency(0, $unique_key, 0, 'DEL');
            }
            return [true, $userDetail];

        } catch (\Exception $e) {
            $transaction->rollback();//插入失败rollback
            return [false, $e->getMessage()];
        }
    }

    /**
     * @throws \yii\db\Exception
     */
    public function saveMembersInfo($user_id, $userDetail=[]): array
    {
        if(empty($userDetail)||empty($user_id)){
            return [false,'用户详情信息不正确！'];
        }
        $connection     = by::dbMaster();
        $tb_detail = self::userTb($user_id);
        $sql       = "INSERT INTO {$tb_detail} (`user_id`,`sex`,`nick`,`age`,`avatar`,`birthday`) 
                            VALUE (:user_id, :sex, :nick,:age,:avatar,:birthday) ";
        $command = $connection->createCommand($sql);
        $sex = intval($userDetail['sex']??0);
        $nick = trim($userDetail['nick']??'');
        $age = intval($userDetail['age']??22);
        $avatar = intval($userDetail['avatar']??0);
        $birthday = CUtil::uint($userDetail['birthday']??0);
        $command->bindParam(":user_id", $user_id);
        $command->bindParam(":sex", $sex);
        $command->bindParam(":nick", $nick);
        $command->bindParam(":age", $age);
        $command->bindParam(":avatar", $avatar);
        $command->bindParam(":birthday", $birthday);
        //@return integer number of rows affected by the execution.
        $insert_flag = $command->execute();
        if (empty($insert_flag)) {
            return [false,"注册用户详情表失败|insert_flag:{$insert_flag}"];
        }
        return [true,$insert_flag];
    }
    /**
     * @param $user_info
     * @param int $is_need_delete_cache :需要删除缓存
     * @return bool
     * @throws \yii\db\Exception
     * 更新用户信息或缓存
     */
    public function updateLoginInfo(&$user_info, int $is_need_delete_cache = 0): bool
    {

        if (empty($user_info)) {
            return false;
        }

        $now = time();
        $user_info['isTodayFirst'] = 0;

        if (empty($user_info['isRegister'])){
            $todayServen = strtotime("today");
            $active_time = strtotime(date("Ymd", $user_info['active_time'] ?? $now));
            $user_info ['isTodayFirst'] = 0;
            if ($active_time < $todayServen) {
                $user_info ['isTodayFirst'] = 1;
                $is_need_delete_cache = 1;
            }
        }else{
            $user_info ['isTodayFirst'] = 1;
            $is_need_delete_cache = 1;
        }

        $need_update['active_time'] = $now;

        if (isset($user_info['avatar'])) {
            $need_update['avatar'] = $user_info['avatar'];
        }

        if (isset($user_info['nick_name'])) {
            $need_update['nick'] = $user_info['nick_name'];
        }

        if (!empty($need_update)) {
            $user_info['active_time'] = $now;
            $this->updateMembersInfo($user_info['user_id'], $need_update,UserModel::SCENARIOS['LOGIN']);
        }

        if ($is_need_delete_cache) {
            $this->deleteRedisCache($user_info['user_id']);
        }

        return true;
    }

    /***
     * @param $user_id
     * @param $need_update
     * @param $allowed
     * @return bool
     * @throws \yii\db\Exception
     */
    public function updateMainInfo($user_id, $need_update,$allowed=self::SCENARIOS['MODIFY']): bool
    {
        $user_id = CUtil::uint($user_id);
        if ((!is_array($need_update)) || ($user_id <= 0)) {
            return false;
        }

        $connection = by::dbMaster();
        $tb_main  = self::userMainTb();

        $sql = " UPDATE {$tb_main} SET ";

        $bindParam = [];
        foreach ($need_update as $field => $val) {
            if (in_array($field, $allowed)) {
                $escape = ":{$field}";
                $update_sql[] = "`{$field}`={$escape}";
                $bindParam[$escape] = strip_tags($val);
            }
        }

        if (empty($update_sql) || empty($bindParam)) {
            return false; //没有可修改的字段
        }

        $sql .= implode(' , ', $update_sql);
        $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

        $command = $connection->createCommand($sql);

        //bindParam的第二个参数要求是引用变量！
        foreach ($bindParam as $special_param => &$special_bind) {
            $command->bindParam($special_param, $special_bind);
        }

        $command->bindParam(":user_id", $user_id);

        $command->execute();

        return true;
    }

    /**
	 *
	 * @param $user_id
	 * @param $need_update
	 * @param mixed $allowed
	 * @return bool
	 * @throws \yii\db\Exception
	 * 更新用户表字段
	 */
    public function updateMembersInfo($user_id, $need_update,$allowed=self::SCENARIOS['MODIFY']): bool
    {
        $user_id = CUtil::uint($user_id);
        if ((!is_array($need_update)) || ($user_id <= 0)) {
            return false;
        }
        //合规化参数
        $need_update = $this->__doWellUserTbFields($need_update);

        if (isset($need_update['avatar'])) {
            $avatar = urldecode($need_update['avatar']);
            $need_update['avatar'] = $avatar;
        }

        if (isset($need_update['nick'])) {
            $nick = urldecode($need_update['nick']);
            $nick = $this->validateNick($nick, 50);
            $nick = strip_tags($nick);
            $need_update['nick'] = $nick;
        }

	    if (isset($need_update['real_name'])) {
		    $real_name = urldecode($need_update['real_name']);
		    $real_name = $this->validateNick($real_name, 50);
		    $real_name = strip_tags($real_name);
		    if (empty($real_name)) {
			    unset($need_update['real_name']);
		    } else {
                $real_name = str_replace("\n",'',$real_name);
			    $need_update['real_name'] = $real_name;
		    }
	    }

	    if(!empty($need_update['area']) || !empty($need_update['province']) || !empty($need_update['city'])){
		    $data                = [
			    'area'     => $need_update['area'] ?? '',
			    'province' => $need_update['province'] ?? '',
			    'city'     => $need_update['city'] ?? '',
		    ];
		    $need_update['area'] = json_encode($data);
	    }

	    if (isset($need_update['birthday'])) {
		    $need_update['birthday'] = intval($need_update['birthday']);
	    }

        if (isset($need_update['sex'])) {
            $need_update['sex'] = CUtil::uint($need_update['sex']);
        }

        $connection = by::dbMaster();
        $tb_detail = $this->userTb($user_id);

        $sql = " UPDATE {$tb_detail} SET ";

        $bindParam = [];
        foreach ($need_update as $field => $val) {
            if (in_array($field, $allowed)) {
                $escape = ":{$field}";
                $update_sql[] = "`{$field}`={$escape}";
                $bindParam[$escape] = strip_tags($val);
            }
        }

        if (empty($update_sql) || empty($bindParam)) {
            return false; //没有可修改的字段
        }

        $sql .= implode(' , ', $update_sql);
        $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

        $command = $connection->createCommand($sql);

        //bindParam的第二个参数要求是引用变量！
        foreach ($bindParam as $special_param => &$special_bind) {
            $command->bindParam($special_param, $special_bind);
        }

        $command->bindParam(":user_id", $user_id);

        $command->execute();

        //清空缓存
        $this->deleteRedisCache($user_id);

        return true;

    }


	/**
	 *
	 * @param $user_id
	 * @param $need_update
	 * @param mixed $allowed
	 * @return bool
	 * @throws \yii\db\Exception
	 * 更新用户表字段
	 */
    public function updateMembersInfoNew($user_id, $need_update,$allowed=self::SCENARIOS['MODIFY']): bool
    {
        $user_id = CUtil::uint($user_id);
        if ((!is_array($need_update)) || ($user_id <= 0)) {
            return false;
        }
        // 通过userid查询用户详情表
        $user_tb = $this->userTb($user_id);
        $sql     = "SELECT * FROM {$user_tb} WHERE `user_id`=:user_id LIMIT 1";
        $command = by::dbMaster()->createCommand($sql);
        $command->bindParam(":user_id", $user_id);
        $user_info = $command->queryOne();
        if (empty($user_info)) {
            return false;
        }
        //合规化参数
        $need_update = $this->__doWellUserTbFields($need_update);

        if (isset($need_update['avatar'])) {
            $avatar = urldecode($need_update['avatar']);
            // 判断头像是否改变，改变的话需要验证敏感性
            if ($avatar != $user_info['avatar']) {
                // 图片验证是异步，其他流程正常走
                $this->checkSensitive($avatar, $user_info['user_id'], 2);
            }
            unset($need_update['avatar']);
        }

        if (isset($need_update['nick'])) {
            $nick = urldecode($need_update['nick']);
            $nick = $this->validateNick($nick, 50);
            $nick = strip_tags($nick);
            // 判断昵称是否存在敏感词 todo
            $res = $this->checkSensitive($nick, $user_info['user_id'], 1);
            if (!$res){
                return false;
            }
            $need_update['nick'] = $nick;
        }

	    if (isset($need_update['real_name'])) {
		    $real_name = urldecode($need_update['real_name']);
		    $real_name = $this->validateNick($real_name, 50);
		    $real_name = strip_tags($real_name);
		    if (empty($real_name)) {
			    unset($need_update['real_name']);
		    } else {
                $real_name = str_replace("\n",'',$real_name);
                // 判断真实姓名是否存在敏感词 todo
                $res = $this->checkSensitive($real_name, $user_info['user_id'], 1);
                if (!$res){
                    return false;
                }
			    $need_update['real_name'] = $real_name;
		    }
	    }

	    if(!empty($need_update['area']) || !empty($need_update['province']) || !empty($need_update['city'])){
		    $data                = [
			    'area'     => $need_update['area'] ?? '',
			    'province' => $need_update['province'] ?? '',
			    'city'     => $need_update['city'] ?? '',
		    ];
		    $need_update['area'] = json_encode($data);
	    }

	    if (isset($need_update['birthday'])) {
		    $need_update['birthday'] = intval($need_update['birthday']);
	    }

        if (isset($need_update['sex'])) {
            $need_update['sex'] = CUtil::uint($need_update['sex']);
        }

        $connection = by::dbMaster();
        $tb_detail = $this->userTb($user_id);

        $sql = " UPDATE {$tb_detail} SET ";

        $bindParam = [];
        foreach ($need_update as $field => $val) {
            if (in_array($field, $allowed)) {
                $escape = ":{$field}";
                $update_sql[] = "`{$field}`={$escape}";
                $bindParam[$escape] = strip_tags($val);
            }
        }

        if (empty($update_sql) || empty($bindParam)) {
            return false; //没有可修改的字段
        }

        $sql .= implode(' , ', $update_sql);
        $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

        $command = $connection->createCommand($sql);

        //bindParam的第二个参数要求是引用变量！
        foreach ($bindParam as $special_param => &$special_bind) {
            $command->bindParam($special_param, $special_bind);
        }

        $command->bindParam(":user_id", $user_id);

        $command->execute();

        //清空缓存
        $this->deleteRedisCache($user_id);

        return true;

    }

    /**
     * @param $user_id
     * @return array
     */
    public function getUserMainInfo($user_id): array
    {
        if (CUtil::checkUuid($user_id)) {
            return [];
        }

        $user_id   = CUtil::uint($user_id);
        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::userMainInfo($user_id);


        $aJson = $redis->get($redis_key);
        $aData = (array) json_decode($aJson, true);
        if ($aData) {
            return $aData;
        }

        // 从数据库获取数据
        $aData = self::find()
                ->select(['openudid', 'unionid', 'reg_time', 'user_type'])
                ->where(['user_id' => $user_id])
                ->asArray()
                ->one();

        // 处理 null 值
        $aData = $aData ?: [];

        // 设置缓存，用户存在缓存 10 分钟，用户不存在缓存 10 秒
        $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 600]);

        return $aData;
    }


    /**
     * 获取用户主表信息列表
     * @param array $user_ids
     * @return array
     */
    public function getUserManList(array $user_ids): array
    {
        $query = self::find();
        return $query->select(['user_id', 'openudid', 'unionid', 'reg_time', 'user_type'])->where(['user_id' => $user_ids])->asArray()->all();
    }

    /**
     * @param $user_id
     * @return array|bool
     * @throws \yii\db\Exception
     * 废弃非正式服的指定账号（注销）
     */
    public function Deprecated($user_id,$ifCenter=0,$wxSyncCenterLock=1)
    {
//        if(YII_ENV_PROD) {
//            return [false,"非法操作"];
//        }

        $user_id = CUtil::uint($user_id, 0);
        if ($user_id <= 0) {
            return [false,"用户不存在"];
        }

        $user_info = $this->getUserMainInfo($user_id);

        if (empty($user_info)) {
            return [false,"用户不存在~~"];
        }

        //防止重复提交
        $openid  = $user_info['openudid'] ?? "";
        $unionid = $user_info['unionid'] ?? "";
        $arr    = explode("|",$openid);
        if(count($arr) > 1) {
            $openid = $arr[0];
        }

        $uids = [];

        $transaction = by::dbMaster()->beginTransaction();
        try {
            //将账号废弃
            $row = by::dbMaster()->createCommand()->update(self::userMainTb(), [
                'openudid' => "{$openid}|".START_TIME
            ], ['user_id' => $user_id])->execute();

            if($row <= 0) {
                throw new MyExceptionModel('用户不存在~~~');
            }
            //将同步的用户注销
            $mallsInfo = by::usersMall()->getMallsByUserId($user_id,false);
            if($mallsInfo){
                foreach ($mallsInfo as $mall){
                    list($status,$msg) = by::usersMall()->deprecated($mall['id']);
                    if(!$status){
                        throw new MyExceptionModel($msg);
                    }
                    //捞取所有的UID
                    if($mall['uid']??0) $uids[]= $mall['uid'];
                }
            }
            
            //删除alipay用户
            list($status,$msg) = by::UsersPlatformModeModel()->Deprecated($user_id);
            if(!$status){
                throw new MyExceptionModel($msg);
            }

            //游客删除
            $ruser_id = by::Rusers()->getUserIdByOpenUdId($user_info['openudid'] ?? '', 1);
            if ($ruser_id) {
                by::Rusers()->delUserMainCacheByOpenudid($user_info['openudid'] ?? '',1);
                //删除数据
                $r1 = by::Rguide()->delDataById($ruser_id);
                $r2 = by::RuserExtend()->delDataById($ruser_id);
                $r3 = by::RuserGuide()->delDataById($ruser_id);
                $r4 = by::Rusers()->delDataById($ruser_id);
                $r5 = by::Rusers()->delDataDetailById($ruser_id);
                $r6 = by::RuserRecommend()->delDataById($ruser_id);
                CUtil::debug($r1.$r2.$r3.$r4.$r5.$r6);
            }



            //5.同步注销用户信息
            if($uids && empty($ifCenter) && empty($wxSyncCenterLock)){
                foreach ($uids as $uid){
                    list($status,$msg) = Mall::factory()->centerDiscard($uid);
                    if(!$status){
                        CUtil::debug($uid . '|' . json_encode($msg), 'err.iot_discard_user');
                    }
                }
            }

            $transaction->commit();

            //删除缓存
            by::Rusers()->deleteRedisCache($ruser_id,true);
            by::RuserExtend()->deleteRedisCache($ruser_id);

            //4.清除缓存
            //获取用户电话号码
            $phone = by::Phone()->GetPhoneByUid($user_id);
            $this->deleteRedisCache($user_id,$phone);
            by::userExtend()->deleteRedisCache($user_id);
            by::Phone()->deleteRedisCache($user_id,$phone);
            by::login()->deleteRedisCache($user_id);
            by::login()->__delUserLogoutStatus($user_id);// 删除用户退出态缓存
            by::login()->LogOut($user_id);
            $this->__delUserUnionIDKey($unionid);
            by::WeFocus()->__delOaInfoByUnionId($unionid);
            //mall 注销
            by::usersMall()->deleteRedisCache(0,$user_id,0);

            //注销共创人员
            \Yii::$app->queue->push(new UpdateCoCreateUserJob(['user_id'=>$user_id,'type'=>"change_status"]));

            //如果是员工的话，清空微笑分
            if(count($uids) > 0){
                foreach ($uids as $uv){
                    $eInfo = byNew::UserEmployeeModel()->getEmployeeInfo($uv);
                    if($eInfo){
                        //如果存在员工信息则清空微笑分
                        Employee::factory()->clearScore(['uid'=>$eInfo['employee_id']]);
                        byNew::UserEmployeeModel()->deleteDataById($eInfo['id']);
                        // 去除绑定关系
                        byNew::UserBindModel()->delDataByUid($uv);
                    }

                }
            }
            // 去商城重构项目中删除发布的作品
            GoDreameMall::factory()->cancel($user_id);

            return [true,"OK"];
        } catch (\exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'discard_user');
            $transaction->rollBack();
            return [false,"注销用户失败"];
        }

    }


    public function batchDeprecated($data)
    {
        if(!is_array($data)||empty($data)){
            return [false,["数据不符合规定"]];
        }
        $tb = self::userMainTb();
        $sql = CUtil::batchUpdate($data,'user_id',$tb);
        if($sql && by::dbMaster()->createCommand($sql)->execute()){
            //清除redis缓存
            foreach ($data as $item){
                $user_id = $item['user_id']??0;
                if($user_id){
                    $this->delUserMainCache($user_id);
                    $this->delUserMainCacheByOpenudid($item['openudid'], 1);
                    by::login()->LogOut($user_id);
                }
            }
            return [true,array_column($data,'user_id')];
        }else{
            return [false,array_column($data,'user_id')];
        }
    }


    /**
     * @param $openudid
     * @param $user_type
     * @return int
     * 清空缓存
     */
    public function delUserMainCacheByOpenudid($openudid, $user_type): int
    {
        $loginType = 2;// 会员
        $session_key = AppCRedisKeys::getUserIdByOpenUdId($openudid, $user_type,$loginType);
        return by::redis('core')->del($session_key);
    }


    /**
     * @param $user_id
     * @return int
     * 删除用户主表缓存数据
     */
    public function delUserMainCache($user_id): int
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id <= 0) {
            return 0;
        }

        $redis_key = AppCRedisKeys::userMainInfo($user_id);
        return by::redis('core')->del($redis_key);
    }

    /**
     * @param $user_id
     * @return int
     * 删除用户单表缓存
     */
    public function delUserCache($user_id): int
    {
        $user_id = CUtil::uint($user_id);
        if ($user_id <= 0) {
            return 0;
        }

        $redis_key = AppCRedisKeys::getOneByUid($user_id);
        return by::redis('core')->del($redis_key);
    }

	/**判断用户是否会员
	 * @param $user_id
	 * @throws \yii\db\Exception
	 */
    public function is_vip($user_id){
        if (strlen($user_id) > 11) {
            $user_id = 0;
        }
	    $user_id = CUtil::uint($user_id);
	    return by::Phone()->GetPhoneByUid($user_id); //绑定手机就是会员
    }


    /**
     * 查找最新的用户
     * @param $phone
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public static function getWxUserByPhone($phone,$cache = true)
    {
        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::getWxUserByPhone($phone);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb    = self::userMainTb();
            $tbPhone = PhoneModel::tbName();
            $sqlPhone = "SELECT `user_id`,`phone` FROM {$tbPhone} WHERE `phone`=:phone ORDER BY `id` DESC LIMIT 1";
            $sql = "SELECT a.`user_id`,a.`reg_time`,a.`openudid`,a.`unionid` FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`user_id` = b.`user_id` WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 ORDER BY a.`reg_time` DESC LIMIT 1";
            $aData =by::dbMaster()->createCommand($sql,[':phone'=>$phone,':userType'=>1])->queryOne();
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function getWxUsersByPhones($phones)
    {
        $tb    = self::userMainTb();
        $tbPhone = by::Phone()::tbName();
        $phones = implode("','",$phones);
        $sqlPhone = "SELECT DISTINCT `user_id`,`phone`,`mall_id` FROM {$tbPhone} WHERE `phone` in ('{$phones}') GROUP BY `user_id`,`phone`,`mall_id` ORDER BY `id` DESC";
        $sql = "SELECT a.`user_id`,a.`reg_time`,a.`openudid`,a.`unionid`,b.`phone`,b.`mall_id` FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`user_id`  COLLATE utf8mb4_0900_ai_ci  = b.`user_id` COLLATE utf8mb4_0900_ai_ci WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 ORDER BY a.`reg_time` DESC";
        $aData =by::dbMaster()->createCommand($sql,[':userType'=>1])->queryAll();
        return empty($aData) ? [] : $aData;
    }


    /**
     * @throws \yii\db\Exception
     */
    public function getWxUsersByCards($cards)
    {
        $tb = self::userMainTb();
        $tbExtend = by::userExtend()::tbName();
        $cards = implode("','",$cards);
        $sqlExtend = "SELECT DISTINCT `user_id`,`card` FROM {$tbExtend} WHERE `card` in ('{$cards}') GROUP BY `user_id`,`card` ORDER BY `ctime` DESC";
        $sql = "SELECT a.`user_id`,a.`reg_time`,a.`openudid`,a.`unionid`,b.`card` FROM {$tb} AS a INNER JOIN ({$sqlExtend}) AS b ON a.`user_id` = b.`user_id` WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 ORDER BY a.`reg_time` DESC";
        $aData =by::dbMaster()->createCommand($sql,[':userType'=>1])->queryAll();
        return empty($aData) ? [] : $aData;
    }

    /**
     * 获取所有该mallId的用户
     * @param $mallId
     * @param bool $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public static function getUsersByMallId($mallId, bool $cache = true)
    {
        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::getUsersByMallId($mallId);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData     = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb    = self::userMainTb();
            $tbPhone = PhoneModel::tbName();
            $sqlPhone = "SELECT `user_id`,`phone` FROM {$tbPhone} WHERE `mall_id`=:mallId ORDER BY `ctime` DESC";
            $sql = "SELECT a.`user_id`,a.`reg_time`,a.`openudid`FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`user_id` = b.`user_id` WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 ORDER BY a.`reg_time` DESC";
            $aData =by::dbMaster()->createCommand($sql,[':mallId'=>$mallId,':userType'=>1])->queryAll();
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }
        return $aData;
    }


    /**
     * 查找所有未同步的用户
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     */
    public function getNoSyncUser(){
        $tb = self::userMainTb();
        $tbExtend = by::userExtend()::tbName();
        $tbPhone = by::Phone()::tbName();
        $sqlExtend1 = "SELECT `user_id`,`is_sync_app` FROM {$tbExtend} WHERE `is_sync_app`=:is_sync_app GROUP BY `user_id`";
        $sqlExtend2 = "SELECT `user_id` FROM {$tbExtend} GROUP BY `user_id`";
        $sql = "(SELECT a.`user_id`  as `user_id` FROM {$tb} AS a INNER JOIN {$tbPhone} AS phone on phone.`user_id` = a.`user_id` INNER JOIN ({$sqlExtend1}) AS b ON a.`user_id` = b.`user_id`WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 )";
        $sql .= " UNION ";
        $sql .= "("."SELECT c.`user_id` as `user_id` FROM {$tb} AS c INNER JOIN {$tbPhone} AS phone on phone.`user_id` = c.`user_id` LEFT JOIN ({$sqlExtend2}) AS d ON c.`user_id` = d.`user_id` WHERE c.`user_type`=:userType AND d.`user_id` is null AND locate('|',c.`openudid`)=0".")";
        $aData =by::dbMaster()->createCommand($sql,[':is_sync_app'=>0,':userType'=>1])->queryAll();
        CUtil::debug(by::dbMaster()->createCommand($sql,[':is_sync_app'=>0,':userType'=>1])->getRawSql());
        return $aData?:[];
    }


    /**
     * 查找所有未授权的用户进行注销
     * @throws \yii\db\Exception
     */
    public function getALLNoAuthUsers()
    {
        $tb    = self::userMainTb();
        $tbPhone = PhoneModel::tbName();
        $sqlPhone = "SELECT `user_id`,`phone` FROM {$tbPhone} GROUP BY `user_id`, `phone`";
        $sql = "SELECT a.`user_id`,a.`reg_time`,a.`openudid`FROM {$tb} AS a LEFT JOIN ({$sqlPhone}) AS b ON a.`user_id` = b.`user_id` WHERE a.`user_type`=:userType AND locate('|',a.`openudid`)=0 AND b.`user_id` is null ORDER BY a.`reg_time` DESC";
        $aData = by::dbMaster()->createCommand($sql,[':userType'=>1])->queryAll();
        return empty($aData) ? [] : $aData;
    }

    /**
     * 删除用户主数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::userMainTb();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }

    /**
     * 删除用户副数据
     * @throws \yii\db\Exception
     */
    public function delDataDetailById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::userTb($user_id);
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }



    /**
     * @throws \yii\db\Exception
     * 判断用户是否达到领取时间
     */
    public function UserReachBirthday($user_id): int
    {
        //status (0: 未填写生日 1：生日未到该月 2.生日已到达该月 3.生日已过该月)
        $info     = $this->getOneByUid($user_id);
        $birthday = $info['birthday'] ?? 0;
        if (empty($birthday)) {
            $status = UserInfoEnum::BIRTHDAY_STATUS['NO'];
        } else {
            if (date('m') < date('m', $birthday)) {
                $status = UserInfoEnum::BIRTHDAY_STATUS['NO_REACH'];
            } elseif (date('m') == date('m', $birthday)) {
                $status = UserInfoEnum::BIRTHDAY_STATUS['REACH'];
            } else {
                $status = UserInfoEnum::BIRTHDAY_STATUS['BEYOND'];
            }
        }
        return $status;
    }



    /**
     * 根据用户ID查询用户信息（无缓存，用于导出）
     * @param array $userIds
     * @param array|string[] $columns
     * @return array
     * @throws \yii\db\Exception
     */
    public function getListByUserIds(array $userIds, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $columns);
        // 分组查询
        $groupUserIds = $this->groupUserId($userIds, 100);
        foreach ($groupUserIds as $index => $ids) {
            $tb = self::userTb($index);
            // 查询条件
            $ids = implode(',', $ids);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `user_id` IN ({$ids})";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            $data = array_merge($data, $res);
        }
        return $data;
    }

    /**
     * 用户ID分组
     * @param array $userIds
     * @param int $mod
     * @return array
     */
    private function groupUserId(array $userIds, int $mod): array
    {
        $data = [];
        foreach ($userIds as $userId) {
            // 取模
            $index = intval($userId) % $mod;
            $data[$index][] = $userId;
        }
        return $data;
    }

    /**
     * @param $unionId
     * @return void
     * @throws RedisException
     * 清除unionid获取user_id缓存
     */
    public function __delUserUnionIDKey($unionId)
    {
        $redis       = by::redis('core');
        $key1 = AppCRedisKeys::getRUserIdByUnionId($unionId, 1);
        $key2 = AppCRedisKeys::getUserIdByUnionId($unionId, 1);
        $redis->del($key1,$key2);
    }

    /**
     * 验证图片是否敏感
     * @param $content 验证内容
     * @param $user_id 用户id
     * @param $type 类型 1文字 2图片
     * @return int
    */
    public function checkSensitive($content,$user_id,$type = 1){
        if ($type == 1){
            $source = 2;
        }else{
            $source = 1;
        }
        $extra = [];
        // 获取token
        list($status, $ret) = WeiXin::factory()->getUniqueAccessToken();
        if (!$status) {
            return 0;
        }
        $token = $ret;
        $openid = $this->getOpenUdIdByUserId($user_id);
        $extra['access_token'] = $token;
        $extra['openid'] = $openid;
        // 验证图片时，先记录修改日志
        $unique_id = CUtil::getUniqueID();
        if ($type == 2){
            $data = [
                'user_id' => $user_id,
                'avatar' => $content,
                'status' => 0,
                'unique_id' => $unique_id,
                'create_time' => time(),
                'update_time' => time(),
            ];

            list($res,$msg) = by::userChangeLogModel()->saveLog($data);
        }
        $host = CUtil::getConfig('host', 'config', \Yii::$app->id)['private_host'] ?? '';
        $back_url = $host.'/comm/cms-image-verify-notify';
        list($status, $res) = Review::factory()->newCheckContent($content,$type,$source,$extra,$back_url,$unique_id);
        if (!$status) {
            return 0;
        }
        CUtil::debug('响应结果' . json_encode($res) , 'info.cms.request');
        if ($type == 1 && isset($res['status']) && $res['status'] != 0){
            return 0;
        }
        return 1;
    }
    public function updateMemberAvatarByNotify($unique_id,$status){
        $changeLog = by::userChangeLogModel()->getOneByUniqueId($unique_id);
        if (empty($changeLog)){
            return false;
        }
        if ($changeLog['status'] != 0){
            return true;
        }
        if ($status == 0){
            $avatar = $changeLog['avatar'];
            if (empty($avatar)){
                return false;
            }
            // 审核通过，替换用户主表头像字段
            $connection = by::dbMaster();
            $tb_detail = $this->userTb($changeLog['user_id']);
            $sql = " UPDATE {$tb_detail} SET `avatar`='{$avatar}' ";
            $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";

            $command = $connection->createCommand($sql)->bindParam(":user_id", $changeLog['user_id'])->execute();
            $config = CUtil::getConfig('wx_version_ctrl','common',MAIN_MODULE);
            $wxSyncCenterLock = $config['wx_sync_center_lock']??1;
            // Crm::factory()->push($changeLog['user_id'], 'user', ['user_id' => $changeLog['user_id']]);
            // if(empty($wxSyncCenterLock)){
            //     Mall::factory()->push($changeLog['user_id'], 'centerUpdate', ['user_id' => $changeLog['user_id']]);
            //     Mall::factory()->push($changeLog['user_id'], 'mallUpdate', ['user_id' => $changeLog['user_id'],'user_detail'=>['avatar'=>$avatar]]);
            // }
            Mall::factory()->push($changeLog['user_id'], 'centerUpdate', ['user_id' => $changeLog['user_id']]);
            Mall::factory()->push($changeLog['user_id'], 'mallUpdate', ['user_id' => $changeLog['user_id'],'user_detail'=>['avatar'=>$avatar]]);

            by::userChangeLogModel()->updateData($changeLog['id'],['status' => 1]);
        }else{
            // 审核不通过，删除记录
            by::userChangeLogModel()->updateData($changeLog['id'],['status' => 2]);
        }

        return true;
    }

    /**
     * 获取默认头像
     */
    public function getDefaultAvatar()
    {
        $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);
        $avatar = $config['cdnAddr'] . '/dreame-php-mall/local/images/202501/677e129d4ddd63191554815.png';
        return $avatar;
    }
}
