<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\GoodsPlatformService;
use yii\db\Exception;

/**
 * banner
 * @auth link
 * @date 2022-2-14
 */
class SearchModel extends CommModel {


    /**
     * @throws Exception
     */
    public function getGoodsByName($keyword, $limit, $page = 1, $pageSize = 10, $platformId = 1, $sortType = 0, $isInternalPurchase = 0,$api = ''): array
    {
        $return = [
            'list'  => [],
            'count' => 0
        ];

        // 1. 获取所有的商品
        $allGoods = by::Gmain()->GetAllList(false,$keyword);
        if (!$allGoods) {
            return [true, $return];
        }

        // 2. 取出所有符合条件商品的IDs
        $gids = array_column($allGoods, 'id');

        // 3. 查出商品详情
        $data = [];
        foreach ($gids as $gid) {
            $aGoods = by::Gmain()->GetAllOneByGid($gid);

            // 区分是否内购商品
            if ($aGoods['is_internal_purchase'] != $isInternalPurchase) {
                continue;
            }

            if ($aGoods && in_array($platformId, $aGoods['platform_ids'])) {
                $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platformId, $aGoods);
                $data[] = [
                        'gid'            => $aGoods['gid'],
                        'gini_id'        => $aGoods['gini_id'],
                        'sku'            => $aGoods['sku'],
                        'name'           => $aGoods['name'],
                        'cover_image'    => $aGoods['cover_image'],
                        'market_image'   => empty($aGoods['market_image'] ?? '') ? $aGoods['cover_image'] : $aGoods['market_image'] ?? '',
                        'mprice'         => $aGoods['mprice'],
                        'price'          => $aGoods['price'],
                        'uprice'         => $aGoods['price'],
                        'tids'           => $aGoods['tids'],
                        'is_presale'     => $aGoods['is_presale'],
                        'deposit'        => $aGoods['deposit'],
                        'expand_price'   => $aGoods['expand_price'],
                        'custom_tag'     => $aGoods['custom_tag'],
                        'second_cate_id' => $aGoods['cate'][1] ?? 0,
                        'platform_image' => [
                                'cover_image' => $platformInfo['cover_image'] ?? '',
                                'images'      => $platformInfo['images'] ?? ''
                        ],
                        'subsidy_price'  => in_array($api, ['a_1664246268', 'i_1666147923']) ? byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount($aGoods['gid'], $aGoods['price']) : "0.00", // 国补价格 仅 app有
                ];
            }
        }

        // 4. 根据排序方式排序
        $sortConfig = CUtil::getConfig('sort', 'common', MAIN_MODULE);
        if ($sortType == $sortConfig['price_asc']) {
            array_multisort(array_column($data, 'price'), SORT_ASC, $data);
        } elseif ($sortType == $sortConfig['price_desc']) {
            array_multisort(array_column($data, 'price'), SORT_DESC, $data);
        }

        // 5. 分页处理
        $goodsData       = array_chunk($data, $pageSize);
        $return['count'] = count($data);
        $return['list']  = $goodsData[$page - 1] ?? [];

        return [true, $return];
    }


}
