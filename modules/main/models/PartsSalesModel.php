<?php

namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use Redis;
use RedisException;
use yii\db\Exception;

class PartsSalesModel extends CommModel
{
    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_parts_sales`";
    }

    public $tb_fields = [
        'id', 'main_sku', 'part_sku', 'reminder_copy', 'sort', 'status', 'is_del', 'ctime', 'utime'
    ];

    const HOST_TYPE = 'host';
    const PART_TYPE = 'part';

    const IS_DEL = [
        'no'        => 0,
        'yes'       => 1
    ];

    const TYPE = [
        'add' => 0,
        'update' => 1
    ];
    public function hostInfoCache(): string
    {
        return AppCRedisKeys::getHostInfo();
    }

    public function partInfoCache(): string
    {
        return AppCRedisKeys::getPartInfo();
    }

    public function __getOnePartSalesByMainId($id): string
    {
        return AppCRedisKeys::GetOnePartSalesByMainId($id);
    }

    private function __getPartSalesList(): string
    {
        return AppCRedisKeys::getPartSalesList();
    }

    private function __getPartInfoById($id): string
    {
        return AppCRedisKeys::getPartInfoById($id);
    }

    /**
     * @throws RedisException
     */
    public function __delPartInfoById($id)
    {
        $redis = by::redis('core');
        $redis_key = $this->__getPartInfoById($id);
        $redis->del($redis_key);
    }


    private function __getPartIdByMainSku($sku): string
    {
        return AppCRedisKeys::getPartIdByMainSku($sku);
    }

    /**
     * @throws RedisException
     */
    public function __delPartIdByMainSku($sku)
    {
        $redis = by::redis('core');
        $redis_key = $this->__getPartIdByMainSku($sku);
        $redis->del($redis_key);
    }



    /**
     * @throws Exception
     * @throws RedisException
     * 通过id获取信息
     */
    public function getOneById($id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getPartInfoById($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $fields = implode("`,`", $this->tb_fields);
            $tb     = self::tbName();
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` =:id AND `status`=:status AND `is_del`=:is_del ORDER BY `sort` ASC";
            $info   = by::dbMaster()->createCommand($sql, [':id' => $id, ':status' => 0, ':is_del' => 0])->queryOne();
            $aData  = empty($info) ? [] : $info;
        }
        return $aData;
    }

    /**
     * @param string $where
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取主机or配件信息
     */
    public function getInfo(string $where = 'host'): array
    {
        $redis = by::redis('core');
        switch ($where) {
            case self::HOST_TYPE:
                $redis_key = $this->hostInfoCache();
                break;
            case self::PART_TYPE:
                $redis_key = $this->partInfoCache();
                break;
            default:
                $redis_key = $this->hostInfoCache();
        }

        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        $tag   = by::Gtag()->getGidInfo($where);

        if ($aJson === false) {
            $aData = [];
            foreach ($tag as $key => $value) {
                $info = by::Gmain()->GetOneByGid($value['gid']);
                if (isset($info['is_del']) && $info['is_del'] == 1 || isset($info['status']) && $info['status'] == 1) {
                    continue;
                } else {
                    $info && $info['stock'] = $value['stock'];
                    $info && $info['part_check'] = false;
                    $info && $info['tied_check'] = false;
                    unset($info['type'], $info['status'], $info['is_del'], $info['version'], $info['ck_code']);
                    $info && $aData[] = $info;
                }
            }
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    /**
     * @throws RedisException
     * 上下架商品时删除主机配件信息缓存
     */
    public function __delHostAndPartCache()
    {
        $redis    = by::redis('core');
        $host_key = $this->hostInfoCache();
        $part_key = $this->partInfoCache();
        $redis->del($host_key, $part_key);
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getPartIdByMainSku($sku): array
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getPartIdByMainSku($sku);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb    = self::tbName();
            $db    = by::dbMaster();
            $sql   = "SELECT `id` FROM {$tb} WHERE `main_sku`=:main_sku";
            $ids   = $db->createCommand($sql, [":main_sku" => $sku])->queryAll();
            $aData = array_column($ids, 'id');
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }




    /**
     * @param $id
     * @param $host_sku
     * @param $input
     * @param $type
     * @param $tran
     * @return array
     * 添加主机配件关联信息
     */
    public function modify($id, $host_sku, $input, $type, $tran = null): array
    {
        $tb       = $this->tbName();
        $db       = by::dbMaster();
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;
        $data     = [];
        try {
            if ($type == self::TYPE['update']) {
                //修改后id
                $uIds = array_filter(array_column($input, 'part_id'));
                //原有id
                $ids = $this->getPartIdByMainSku($host_sku);

                $dIds = array_diff($ids, $uIds);

                list($status, $ret) = $this->del($dIds, self::PART_TYPE);
                if (!$status) {
                    throw new MyExceptionModel('删除失败');
                }

                foreach ($input as $value) {
                    if (isset($value['part_id']) && !empty($value['part_id'])) {
                        $uData = [
                            'main_sku'      => $host_sku,
                            'main_id'       => $id,
                            'part_sku'      => $value['part_sku'] ?? '',
                            'reminder_copy' => $value['reminder_copy'] ?? '',
                            'sort'          => $value['sort'] ?? '',
                            'utime'         => intval(START_TIME),
                        ];
                        $urow  = $db->createCommand()->update($tb, $uData, ['id' => $value['part_id']])->execute();
                        if (!$urow) {
                            throw new MyExceptionModel('修改失败');
                        }
                    } else {
                        $cData = [
                            'main_sku'      => $host_sku,
                            'main_id'       => $id,
                            'part_sku'      => $value['part_sku'] ?? '',
                            'reminder_copy' => $value['reminder_copy'] ?? '',
                            'sort'          => $value['sort'] ?? '',
                            'ctime'         => intval(START_TIME),
                            'utime'         => intval(START_TIME)
                        ];
                        $crow  = $db->createCommand()->insert($tb, $cData)->execute();
                        if (!$crow) {
                            throw new MyExceptionModel('修改失败');
                        }
                    }

                }


            } else {
                foreach ($input as $value) {
                    $data = [
                        'main_sku'      => $host_sku,
                        'main_id'       => $id,
                        'part_sku'      => $value['part_sku'] ?? '',
                        'reminder_copy' => $value['reminder_copy'] ?? '',
                        'sort'          => $value['sort'] ?? '',
                        'ctime'         => intval(START_TIME),
                        'utime'         => intval(START_TIME)
                    ];
                    $row  = $db->createCommand()->insert($tb, $data)->execute();
                    if (!$row) {
                        throw new MyExceptionModel('添加失败');
                    }
                }
            }
            is_null($tran) && $new_tran->commit();

            $this->__delListCache();
            $this->__delPartIdByMainSku($host_sku);
            $this->__delPartIdByMainSku($host_sku);
            return [true, $data];
        } catch (MyExceptionModel $_e) {
            is_null($tran) && $new_tran->rollBack();

            return [false, $_e->getMessage()];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false, '添加失败'];
        }
    }


    /**
     * @param $id
     * @return array
     * @throws Exception
     * @throws RedisException 获取配件详情信息,通过main_id
     */
    public function GetOneByMainId($id): array
    {
        if (empty($id)) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOnePartSalesByMainId($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $fields = implode("`,`", $this->tb_fields);
            $tb     = self::tbName();
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `main_id` =:main_id AND `status`=:status AND `is_del`=:is_del ORDER BY `sort` ASC";
            $aData  = by::dbMaster()->createCommand($sql, [':main_id' => $id, ':status' => 0, ':is_del' => 0])->queryAll();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        $result = [];
        if ($aData) {
            foreach ($aData as $value) {
                $part_info                  = by::Gmain()->GetOneBySku($value['part_sku'] ?? '') ?? [];
                $part_info['reminder_copy'] = $value['reminder_copy'];
                $part_info['sort']          = $value['sort'];
                $part_info['part_id']       = $value['id'];
                unset($part_info['type'], $part_info['status'], $part_info['is_del'], $part_info['version'], $part_info['ck_code']);
                $result[] = $part_info;
            }
        }
        return $result ?? [];
    }


    /**
     * @param $ids
     * @return void
     * @throws RedisException
     * 清除详情数据通过id
     */
    public function __delGetOneCache($ids)
    {
        $redis     = by::redis('core');
        $redis_key = [];
        foreach ($ids as $id) {
            $redis_key[] = $this->__getOnePartSalesByMainId($id);
        }
        $redis->del($redis_key);
    }


    /**
     * @param $info
     * @param $type
     * @return array
     * @throws Exception
     * 通过sku获取main_id
     */
    public function getMainIdBySku($info, $type): array
    {
        switch ($type) {
            case self::HOST_TYPE:
                $input = [
                    'main_sku' => $info
                ];
                break;
            case self::PART_TYPE:
                $input = [
                    'part_sku' => $info
                ];
                break;
            default:
                $input = [];
        }

        list($where, $params) = by::partsSalesModel()->__getCondition($input);
        $tb    = self::tbName();
        $sql   = "SELECT `main_id` FROM {$tb} WHERE {$where} ORDER BY `sort` DESC";
        $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $ids   = array_column($aData, 'main_id');
        return $ids ?? [];
    }


    /**
     * @param $input
     * @return array
     * 组装条件
     */
    public function __getCondition($input): array
    {
        //字段解析
        $mainSku = trim($input['main_sku'] ?? '');
        $partSku = trim($input['part_sku'] ?? '');

        //初始化查询
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;
        if (!empty($mainSku)) {
            $where               .= " AND `main_sku` =:main_sku";
            $params[":main_sku"] = $mainSku;
        }

        if (!empty($partSku)) {
            $where               .= " AND `part_sku` =:part_sku";
            $params[":part_sku"] = $partSku;
        }
        return [$where, $params];
    }


    /**
     * @param array $ids
     * @param string $type
     * @return array
     * 批量删除配件数据
     */
    public function del(array $ids = [], string $type = 'host'): array
    {

        if ($type == self::HOST_TYPE) {
            $where = ['main_id' => $ids];
        } else {
            $where = ['id' => array_values($ids)];
        }
        $tb     = self::tbName();
        $data   = ['is_del' => self::IS_DEL['yes']];
        $result = 0;

        try {
            $result = by::dbMaster()->createCommand()->update($tb, $data, $where)->execute();
            $this->__delListCache();
            $this->__delGetOneCache($ids);
            by::mainSalesModel()->__delGetMainInfoByIdCache($ids);
            return [true, $result];
        } catch (\Exception $e) {
            return [false, $result];
        }
    }


    /**
     * @throws RedisException
     * 清除列表
     */
    private function __delListCache()
    {
        $r_key = $this->__getPartSalesList();
        by::redis('core')->del($r_key);
    }

}
