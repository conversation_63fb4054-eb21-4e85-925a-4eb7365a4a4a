<?php

namespace app\modules\main\models;


use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class UserTryModel extends CommModel
{
    public static function tableName(): string
    {
        return '`db_dreame`.`t_user_try`';
    }

    public $fields = [
        'id', 'user_id', 'uid', 'phone', 'status', 'reason', 'ctime', 'utime'
    ];

    const STATUS = [
        '0' => 'NORMAL',
        '1' => 'SHIELD'
    ];

    public function SaveUser($data): array
    {
        $now  = time();
        $save = [
            'user_id' => $data['user_id'],
            'uid'     => $data['uid'],
            'phone'   => $data['phone'],
            'ctime'   => $now,
            'utime'   => $now
        ];

        // 检查用户是否已存在
        $exists = self::find()->where(['user_id' => $data['user_id']])->exists();
        if ($exists) {
            return [true, 'ok'];
        }
        $result = by::dbMaster()->createCommand()->insert(self::tableName(), $save)->execute();
        if (!$result) {
            return [false, '保存先试后买用户失败'];
        }
        return [true, 'ok'];
    }


    public function UpdateUser($userId, array $update): array
    {
        //允许修改的字段
        $allowed = [
            'status', 'reason', 'utime'
        ];

        foreach ($update as $field => $val) {
            if (!in_array($field, $allowed)) {
                unset($update[$field]);
            }
        }

        if (empty($update)) {
            return [false, '无字段更改'];
        }

        $userId = CUtil::uint($userId);
        $aLog   = self::find()->where(['user_id' => $userId])->one();

        if (empty($aLog)) {
            return [false, '无字段更改(1)'];
        }

        $tb = self::tableName();

        by::dbMaster()->createCommand()->update($tb, $update, ['user_id' => $userId])->execute();

        return [true, "成功"];
    }


    public function getList(array $input, $page, $pageSize, bool $isCache = false): array
    {
        $limit = CUtil::pagination($page, $pageSize);
        $query = $this->__getCondition($input);
        $list = $query->offset($limit[0])
            ->limit($limit[1])
            ->asArray();
        $list = $list->all();
        //是否需要数量
        $need_count = $input['need_count'] ?? true;
        if ($need_count) {
            $total = (clone $query)->count();
            $pages = CUtil::getPaginationPages($total, $pageSize);
        }

        return [intval($total ?? 0), $pages ?? 1, array_filter($list)];
    }

    private function __getCondition(array $input): \yii\db\ActiveQuery
    {
        $query = self::find();

        if (!empty($input['uid'])) {
            $query->andWhere(['uid' => $input['uid']]);
        }
        if (!empty($input['phone'])) {
            $query->andWhere(['phone' => $input['phone']]);
        }
        if (!empty($input['user_ids'])) {
            $query->andWhere(['IN', 'user_id', $input['user_ids']]);
        }

        if (!empty($input['goods_name'])) {
            $uoTryTb = byNew::UserOrderTry()::tbName();
            $sql     = "SELECT `user_id` FROM {$uoTryTb} WHERE `goods_name` LIKE :goods_name AND `try_status` > 0";
            //去重
            $userIds       = by::dbMaster()->createCommand($sql, [':goods_name' => "%{$input['goods_name']}%"])->queryColumn();
            $uniqueUserIds = array_unique($userIds);

            $query->andWhere(['IN', 'user_id', $uniqueUserIds]);
        }

        if (isset($input['status']) && $input['status'] > -1) {
            $query->andWhere(['status' => $input['status']]);
            $input['status'] == 0 ? $query->orderBy('id DESC') : $query->orderBy('utime DESC');
        }

        if (isset($input['label']) && $input['label'] > -1) {
            $uoTryTb = byNew::UserOrderTry()::tbName();
            $sql     = "SELECT `user_id` FROM {$uoTryTb} WHERE `label` =:label AND `try_status` > 0";
            //去重
            $userIds = by::dbMaster()->createCommand($sql, [':label' => $input['label']])->queryColumn();

            $uniqueUserIds = array_unique($userIds);
            $query->andWhere(['IN', 'user_id', $uniqueUserIds]);
        }

        if (!empty($input['try_status']) && $input['try_status'] > -1) {
            $uoTryTb = byNew::UserOrderTry()::tbName();
            $sql     = <<<SQL
                SELECT `t`.`user_id`   FROM `db_dreame_goods`.`t_uo_try` t  
                         INNER JOIN ( 
                          SELECT `user_id`, MAX(`ctime`) as `latest_ctime`
                          FROM {$uoTryTb}    
                          WHERE `try_status` =:try_status
                          GROUP BY `user_id`  
                          ) as `latest_orders` ON `t`.`user_id` = `latest_orders`.`user_id` AND `t`.`ctime` = `latest_orders`.`latest_ctime` 
             WHERE `t`.`try_status` =:try_status;
SQL;

            //去重
            $userIds = by::dbMaster()->createCommand($sql, [':try_status' => $input['try_status']])->queryColumn();

            $uniqueUserIds = array_unique($userIds);
            $query->andWhere(['IN', 'user_id', $uniqueUserIds]);
        }

        return $query;
    }

    public function getUserDetail($id)
    {
        return self::find()->where(['id' => $id])->asArray(true)->one();
    }


    public function getInfoByUserId($userId)
    {
        $tb    = self::tableName();
        $sql   = "SELECT * FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";
        $aData = by::dbMaster()->createCommand($sql, [':user_id' => $userId])->queryOne();
        return $aData ?? [];
    }


    public function exportData($uid = '', $phone = '', $goods_name = '', $label = -1, $status = -1, $viewSensitive = false): array
    {
        // 初始化输入参数
        $input = [
            'uid'        => $uid,
            'phone'      => $phone,
            'goods_name' => $goods_name,
            'label'      => $label,
            'status'     => $status,
        ];

        // 获取查询条件
        $query = $this->__getCondition($input);
        // 获取结果集
        $list = $query->orderBy('utime DESC')->asArray()->all();

        // 初始化数据数组
        $data = [];

        // 定义表头
        if ($status == 1) {
            $head = ['用户UID', '手机号', '拉入黑名单时间', '拉入黑名单原因'];
        } else {
            $head = ['用户UID', '手机号', '试用商品类型', '试用商品名称', '最近申请试用时间', '当前用户状态', '历史参与试用次数', '是否购买新机'];
        }
        $data[] = $head;

        // 如果列表不为空，为每个用户获取其所有订单
        if (!empty($list)) {
            $tagMap = by::Gtag()->GetTagNameMap();
            foreach ($list as $value) {
                if ($status == 1) {
                    $data[] = [
                        'uid'    => $value['uid'],
                        'phone'  => $value['phone'],
                        'ctime'  => date('Y-m-d H:i:s', $value['utime']),
                        'reason' => $value['reason'],
                    ];
                } else {
                    // 获取特定用户的所有订单
                    $orderList = byNew::UserOrderTry()->GetUserAllOrder($value['user_id']);
                    $order     = $orderList[0] ?? [];
                    $data[]    = [
                        'uid'        => $value['uid'],
                        'phone'      => $value['phone'],
                        'label'      => $tagMap[$order['label']] ?? '',
                        'goods_name' => $order['goods_name'] ?? '',
                        'ctime'      => date('Y-m-d H:i:s', $order['ctime']),
                        'status'     => byNew::UserOrderTry()::TRY_STATUS_NAME[$order['try_status']] ?? '',
                        'try_number' => count($orderList),
                        'is_buy'     => 0 == 1 ? '已购买' : '未购买', // 修正为根据用户数据判断是否购买
                    ];
                }
            }
        }

        return $data;
    }

}
