<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use yii\db\Expression;

/**
 * 用户购物金记录表
 */
class UserShopMoneyRecordModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame`.`user_shop_money_record`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'UserShopMoneyRecord.info');
            return [false, '保存操作失败'];
        }
    }

    public function getListByUserId($userId, $type, $money_type, $page = 1, $pageSize = 10)
    {
        $param = ['user_id' => $userId, 'money_type' => $money_type];
        if ($type != 0) {
            $param['type'] = $type;
        }
        return self::find()->where($param)->andWhere(['or', 'LENGTH(extend) > 20', ['and', ['remark' => '盲盒抽奖', 'type' => 2]]])->offset(($page - 1) * $pageSize)->limit($pageSize)->orderBy('ctime desc')->asArray()->all();
    }
    
    public function getConsumeMoneyListByUserId(int $user_id, int $page = 1, int $pageSize = 10): array
    {
        $param = ['user_id' => $user_id, 'money_type' => 2, 'type' => 1];

        return self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->offset(($page - 1) * $pageSize)->limit($pageSize)->orderBy('ctime desc')->asArray()->all();
    }
    
    public function getConsumeMoneyCountByUserId(int $user_id)
    {
        $param = ['user_id' => $user_id, 'money_type' => 2, 'type' => 1];
        
        return self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->count();
    }
    
    /**
     * 获取前20个用户各自的总消费金
     * @param int $user_id
     * @return array
     */
    public function getTotalConsumeMoney(int $user_id = 0): array
    {
        $param = ['money_type' => 2, 'type' => 1];

        if (! empty($user_id)) {
            $param['user_id'] = $user_id;
        }

        return self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->select(['user_id', 'sum(money) as money'])->groupBy('user_id')->orderBy('id DESC')->limit(20)->asArray()->all();
    }
    
    /**
     * 获取用户总消费金
     * @param int $user_id
     * @return string
     */
    public function getTotalConsumeMoneyByUserId(int $user_id = 0): string
    {
        if (empty($user_id)) {
            return '0';
        }
        
        $param = ['money_type' => 2, 'type' => 1, 'user_id' => $user_id];
        
        $total = self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->sum('money');
        return empty($total) ? '0' : bcdiv($total, '100', 2);
    }
    
    /**
     * 获取前20个用户获取消费金明细
     * @param int $nums 数量
     * @return array
     */
    public function getConsumeMoneyDetailRoll(int $nums = 20): array
    {
        $param = ['money_type' => 2, 'type' => 1];

        return self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->andWhere(['not in', 'remark', ['盲盒抽奖']])->select(['user_id', 'money', 'ctime', 'remark'])->orderBy('id DESC')->limit(100)->asArray()->all();
    }
    
    /**
     * 赚钱花排行榜
     * @param int $user_id 用户ID
     * @param int $top 显示数量
     * @return array
     */
    public function getConsumeMoneyRank(int $user_id = 0, int $top = 50): array
    {
        $param = ['money_type' => 2, 'type' => 1];
        if (! empty($user_id)) {
            $param['user_id'] = $user_id;
        }

        $query = self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->select(['user_id', 'count(*) as money_count', 'sum(money) as money'])->groupBy('user_id')->having('sum(money) > 0')->orderBy('money DESC');

        if (! empty($top)) {
            $query->limit($top);
        }

        return $query->asArray()->all();
    }

    public function getConsumeMoneyTip(int $user_id = 0)
    {
        $param['user_id'] = $user_id;
        $param['is_read'] = 0;
        $param['money_type'] = 2;
        $param['type'] = 1;

        $data = self::find()->where($param)->andWhere(['<', 'LENGTH(extend)', 10])->select(['*'])->orderBy('ctime desc')->asArray()->one();
        return $data;
    }

    public function getConsumeMoneyCloseTip(int $user_id = 0){
        $tb = $this->tbName();
        $updateData['is_read'] = 1;

        $status = by::dbMaster()->createCommand()->update($tb,$updateData, ['user_id'=>$user_id,'is_read'=>0,'money_type'=>2,'type'=>1])->execute();
        if ($status !== false){
            return true;
        }else{
            return false;
        }
    }
    
    /**
     * 获取赚钱花排行榜
     * @param int $user_id
     * @return array
     * @throws \yii\db\Exception
     */
    public function getConsumeMoneyRankData(int $user_id = 0): array
    {
        $allRank = byNew::UserConsumeMoneyRankModel()->getConsumeMoneyRank(0, 0);
        $rank50 = byNew::UserConsumeMoneyRankModel()->getConsumeMoneyRank();

        $my_rank = [];
        $all_rank = [];
        foreach ($rank50 as $k => $v) {
            $user_info = by::users()->getOneByUid($v['user_id']);
            $nick = $user_info['nick'] ?? '';
            $all_rank[$k]['rank'] = (int) ($k + 1);
            $all_rank[$k]['nick'] = CUtil::truncateStringV2($nick);
            $all_rank[$k]['avatar'] = CUtil::avatar($user_info);
            $all_rank[$k]['money'] = bcdiv($v['money'], 100, 2);
            $all_rank[$k]['money_count'] = (int) ($v['money_count'] ?? 0);
        }
        
        foreach ($allRank as $k => $v) {
            if ($v['user_id'] == $user_id) {
                $user_info = by::users()->getOneByUid($v['user_id']);
                $nick = $user_info['nick'] ?? '';
                $my_rank[$k]['rank'] = (int) ($k + 1);
                $my_rank[$k]['nick'] = CUtil::truncateStringV2($nick);
                $my_rank[$k]['avatar'] = CUtil::avatar($user_info);
                $my_rank[$k]['money'] = bcdiv($v['money'], 100, 2);
                $my_rank[$k]['money_count'] = (int) ($v['money_count'] ?? 0);
                break;
            }
        }
        
        if (empty($my_rank)) {
            $user_info = by::users()->getOneByUid($user_id);
            $nick = $user_info['nick'] ?? '';
            $my_rank[] = [
                'rank' => '-',
                'nick' => CUtil::truncateStringV2($nick),
                'avatar' => CUtil::avatar($user_info),
                'money' => '0',
                'money_count' => 0,
            ];
        }
        
        return [
            'my_rank' => array_values($my_rank),
            'all_rank' => $all_rank,
        ];
    }

}