<?php

namespace app\modules\main\models;

use app\components\AliYunOss;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;


class SourceExtendModel extends CommModel
{

    public $tb_fields = [
        'id', 'user_id', 'source_union', 'source_euid', 'source_referer', 'ctime', 'utime'
    ];


    public static function tableName(): string
    {
        return "`db_dreame`.`t_source_extend`";
    }

    private function getLatestSourceListKey(): string
    {
        return AppCRedisKeys::getLatestSourceListKey();
    }

    public function __delLatestSourceListKey()
    {
        $redis    = by::redis();
        $redisKey = $this->getLatestSourceListKey();
        $redis->del($redisKey);
    }

    /**
     * @throws Exception
     */
    public function saveLog($input): bool
    {
        $user_id = $input['user_id'] ?? '';
        $referer = $input['referer'] ?? '';
        $union   = $input['union'] ?? '';
        $euid    = $input['euid'] ?? '';

        $db = by::dbMaster();
        $tb = self::tableName();

        $save = [
            'user_id'        => $user_id,
            'source_union'   => $union,
            'source_euid'    => $euid,
            'source_referer' => $referer,
            'ctime'          => intval(START_TIME),
            'utime'          => intval(START_TIME)
        ];

        $sql = "SELECT * FROM `db_dreame`.`t_source_extend` WHERE `user_id`=:user_id";
        $record = $db->createCommand($sql, [':user_id' => $user_id])->queryOne();

        if ($record) {
            // 如果记录存在，更新数据
            $db->createCommand()->update($tb, ['utime' => intval(START_TIME), 'source_euid' => $euid, 'source_union' => $union, 'source_referer' => $referer], ['user_id' => $user_id])->execute();
        } else {
            // 如果记录不存在，插入数据
            $db->createCommand()->insert($tb, $save)->execute();
        }

        $this->__delLatestSourceListKey();
        return true;
    }



    /**
     * @param $input
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     */
    public function getLatestSourceList($input)
    {
        $redis    = by::redis();
        $redisKey = $this->getLatestSourceListKey();
        $subKey   = CUtil::getAllParams(__FUNCTION__, json_encode($input));
        $aJson    = $redis->hGet($redisKey, $subKey);
        $aData    = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb = self::tableName();
            $db = by::dbMaster();
            list($where, $params) = $this->_condition($input);
            $sql   = "SELECT `user_id`,`source_euid` FROM {$tb} WHERE {$where} ORDER BY `ctime`";
            $aData = $db->createCommand($sql, $params)->queryAll();
            $aData = empty($aData) ? [] : $aData;
            $redis->hSet($redisKey, $subKey, json_encode($aData));
            CUtil::ResetExpire($redisKey, rand(600, 900));
        }

        return $aData;
    }

    /**
     * 获取用户来源扩展列表
     * @param array $user_ids
     * @return array
     */
    public function getUserSourceExtendList(array $user_ids): array
    {
        $query = self::find()->from(self::tableName());
        return $query->where(['user_id' => $user_ids])->asArray()->all();
    }

    private function _condition($input): array
    {
        $source_code = $input['source_code'] ?? '';
        $user_id     = $input['user_id'] ?? '';

        //SQL初始化条件
        $where           = "1=:init";
        $params[':init'] = 1;

        if (!empty($source_code)) {
            $where                  .= " AND `source_euid`=:source_code";
            $params[":source_code"] = $source_code;
        }

        if (!empty($user_id)) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"] = $user_id;
        }

        return [$where, $params];
    }


}
