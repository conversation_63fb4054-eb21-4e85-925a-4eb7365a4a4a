<?php

namespace app\modules\main\models;

use app\components\AppNRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\modules\back\services\MemberActivityService;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use RedisException;
use Throwable;

final class MemberActivityModuleRelationModel extends CommModel
{
    use Singleton, ModelTrait;

    private $fillable = ['id', 'activity_id', 'module_res_id', 'title', 'summary', 'start_time', 'end_time', 'extra', 'sort', 'create_time', 'update_time', 'delete_time'];

    public static function tableName(): string
    {
        return "`db_dreame_goods`.`member_activity_module_relation`";
    }

    /**
     * @param $acId
     * @return void
     * @throws RedisException
     * 删除redis key
     */
    public function __delRedisKey($acId)
    {
        if (empty($acId)) {
            return;
        }
        // 根据活动ID 获取模块下module_relation_id
        $acRelationId = self::find()->where(['activity_id' => $acId, 'module_res_id' => MemberActivityService::MODULE_RED_ID['RED_ENVELOPE']['id'], 'delete_time' => 0])->select(['id'])->asArray()->scalar();
        if (empty($acRelationId)) {
            return;
        }
        $redis    = by::redis();
        $redisKey = AppNRedisKeys::memberActivityModuleRelationList($acRelationId);
        $redis->del($redisKey);
    }

    /**
     * 新增
     * @param array $data 模块数据
     * @return array
     */
    public function doCreate(array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $data['create_time'] = time();
            $data['update_time'] = time();
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException(sprintf('%s模块创建失败', $data['title'] ?? ''));
            }
            
            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 更新
     * @param int $id 模块ID
     * @param array $data 模块数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $activity_id = $data['activity_id'] ?? 0;
            unset($data['activity_id']);
            unset($data['module_res_id']);
            if (! isset($data['update_time'])) {
                $data['update_time'] = time();
            }
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id, 'activity_id' => $activity_id])->execute();
            if (empty($resp)) {
                throw new BusinessException(sprintf('%s模块保存失败', $data['title'] ?? ''));
            }

            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

    /**
     * 删除数据
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['delete_time' => time()], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }
            
            if (empty($resp)) {
                throw new BusinessException('删除活动商品失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 根据活动ID获取模块列表
     *
     * @param int $activityId 活动ID
     * @param array $fields 指定要查询的字段，默认查询所有字段
     * @return array 返回模块列表数组
     */
    public function getModuleListByActivityId(int $activityId, array $fields = []): array
    {
        $query = self::find()->where(['activity_id' => $activityId,'delete_time'=>0]);

        // 如果指定了字段则只查询指定字段
        if (!empty($fields)) {
            $query->select($fields);
        }

        return $query->orderBy('sort ASC, id ASC')->asArray()->all();
    }
    
    /**
     * 根据ID获取一条数据
     * @param int $id
     * @return array|null
     */
    public function getOneById(int $id)
    {
        return self::find()->where(['id' => $id, 'delete_time' => 0])->asArray()->one();
    }
    
    /**
     * 是否存在模块资源ID
     * @param int $activityId 活动ID
     * @param int $moduleResId 模块资源ID
     * @param int $moduleRelationId 活动模块ID
     * @return bool
     */
    public function isExistsModuleResId(int $activityId, int $moduleResId, int $moduleRelationId): bool
    {
        $res = self::find()->where(['activity_id' => $activityId, 'module_res_id' => $moduleResId, 'delete_time' => 0])->asArray()->one();

        if (empty($res) || $res['id'] == $moduleRelationId) {
            return false;
        }

        return true;
    }
    
    /**
     * 根据模块资源ID获取活动模块IDs
     * @param array $moduleResIds
     * @param array $activityIds
     * @return array
     */
    public function getIdsByModuleResId(array $moduleResIds, array $activityIds = []): array
    {
        $query = self::find();
        $query->where(['module_res_id' => $moduleResIds]);
        if (! empty($activityIds)) {
            $query->andWhere(['activity_id' => $activityIds]);
        }

        $res = $query->select(['id'])->asArray()->all();

        return empty($res) ? [] : array_column($res, 'id');
    }


    /**
     * 根据活动 ID 和模块资源 ID 获取单条记录
     *
     * @param int $acRelationId 活动模块关联 ID
     * @param int $modelResId 模块资源 ID
     * @throws RedisException
     */
    public static function getOneByCondition(int $acRelationId, int $modelResId): array
    {
        // 如果参数无效，直接返回 null
        if ($acRelationId <= 0 || $modelResId <= 0) {
            return [];
        }

        //  在修改/新增模块时需要删除缓存
        $redis    = by::redis();
        $redisKey = AppNRedisKeys::memberActivityModuleRelationList($acRelationId);
        $subKey   = "module_res_id:{$modelResId}";

        // 从缓存中取
        $cachedData = $redis->hGet($redisKey, $subKey);
        if ($cachedData !== false) {
            return json_decode($cachedData, true);
        }

        // 没缓存，从数据库查询
        $query = self::find();
        if (!empty($fields)) {
            $query->select($fields);
        }

        // 直接设置查询条件
        $query->where([
                'id'            => $acRelationId,
                'module_res_id' => $modelResId,
        ]);

        $result = $query->asArray()->one();

        if ($result) {
            $redis->hSet($redisKey, $subKey, json_encode($result));
            $redis->expire($redisKey, 7200);
        }

        return $result ?: [];
    }

}