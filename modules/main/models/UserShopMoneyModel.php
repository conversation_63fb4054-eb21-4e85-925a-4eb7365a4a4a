<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use yii\db\Expression;

/**
 * 用户购物金
 */
class UserShopMoneyModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame`.`user_shop_money`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }
    
    const TYPE_CONSUME_MONEY = 2; // 消费金

    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        try {
            // 保存主表数据
            $db->createCommand()->insert($tb,$aData)->execute();
            return [true, '保存操作成功'];
        } catch (\Exception $e) {
            CUtil::debug('保存操作失败:'.$e->getMessage() ,'UserShopMoney.info');
            return [false, '保存操作失败'];
        }
    }

    public function getInfoByUserId($user_id,$type = 1,$FenOrYuan = 1){
        $userShopMoney = self::find()->where(['user_id' => $user_id,'money_type'=>$type])->asArray()->one();
        if (empty($userShopMoney)){
            return 0;
        }else{
            if ($FenOrYuan == 1){
                return $userShopMoney['money'];
            }else{
                $price = bcdiv($userShopMoney['money'], 100, 2);
                return sprintf("%.2f", $price);
            } 
        }
    }

    public function AddOrSubtract($user_id,$calculate_type,$money_type,$money,$extend='',$remark = ''){
        // 先通过用户id获取是否有数据
        $userShopMoney = self::find()->where(['user_id' => $user_id,'money_type'=>$money_type])->asArray()->one();
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        $trans  = $db->beginTransaction();
        if (empty($userShopMoney)){
            // 没有数据，新增一条记录
            // 新增数据只处理增加
            if ($calculate_type == 'add'){
                $aData = [
                    'user_id' => $user_id,
                    'money' => $money,
                    'money_type'=> $money_type,
                    'ctime' => time(),
                    'utime' => time(),
                ];
                list($status,$res) = self::SaveLog($aData);
                if (!$status) {
                    $trans->rollBack();
                    return false;
                }
                $recordData = [
                    'user_id' => $user_id,
                    'money' => $money,
                    'extend' => $extend,
                    'remark' => $remark,
                    'money_type' => $money_type,
                    'type' => 1,
                    'ctime' => time(),
                    'utime' => time(),
                ];
                list($status,$res) = byNew::UserShopMoneyRecordModel()->SaveLog($recordData);
                if (!$status) {
                    $trans->rollBack();
                    return false;
                }
            }
            
        }else{
            if ($calculate_type == 'add'){
                $updateData = [
                    'money' => new Expression('money + ' .$money),
                ];
                by::dbMaster()->createCommand()->update($tb,$updateData, ['user_id'=>$user_id,'money_type'=>$money_type])->execute();
                $recordData = [
                    'user_id' => $user_id,
                    'money' => $money,
                    'extend' => $extend,
                    'remark' => $remark,
                    'money_type' => $money_type,
                    'type' => 1,
                    'ctime' => time(),
                    'utime' => time(),
                ];
                list($status,$res) = byNew::UserShopMoneyRecordModel()->SaveLog($recordData);
                if (!$status) {
                    $trans->rollBack();
                    return false;
                }

            }else if ($calculate_type == 'subtract'){
                $updateData = [
                    'money' => new Expression('money - ' .$money),
                ];
                by::dbMaster()->createCommand()->update($tb,$updateData, ['user_id'=>$user_id,'money_type'=>$money_type])->execute();
                $recordData = [
                    'user_id' => $user_id,
                    'money' => ($money * -1),
                    'extend' => $extend,
                    'remark' => $remark,
                    'money_type' => $money_type,
                    'type' => 2,
                    'ctime' => time(),
                    'utime' => time(),
                ];
                list($status,$res) = byNew::UserShopMoneyRecordModel()->SaveLog($recordData);
                if (!$status) {
                    $trans->rollBack();
                    return false;
                }
            }
        }
        $trans->commit();
        return true;
    }
    
    public function getUserShopMoney(int $user_id = 0, int $money_type = 1)
    {
        $where = ['money_type' => $money_type];
        
        if (! empty($user_id)) {
            $where['user_id'] = $user_id;
        }
        
        return self::find()->where($where)->orderBy('id DESC')->limit(20)->asArray()->all();
    }
    
    /**
     * 获取赚钱花参与人数
     * @return int
     */
    public function getConsumeMoneyUserCount(): int
    {
        $param = ['money_type' => 2];
        
        return (int) self::find()->where($param)->select(['user_id'])->distinct()->count();
    }

}