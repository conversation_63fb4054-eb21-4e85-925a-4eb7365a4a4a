<?php

namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\components\Mall;
use app\models\by;
use app\models\CUtil;
use app\modules\main\services\CateService;
use RedisException;
use yii\db\ActiveQuery;
use yii\db\DataReader;
use yii\db\Exception;

class CateModel extends CommModel
{
    /**
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_cate`";
    }

    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_cate`";
    }

    public $tb_fields = [
        'id', 'name', 'cate_alias', 'tag_name', 'icon', 'pid', 'level', 'sort', 'view', 'is_del', 'ctime', 'utime', 'dtime'
    ];


    const MACHINE_TYPE = [
        0 => '',
        1 => 'main',
        2 => 'part'
    ];


    // 类目类型
    const CATE_TYPE = [
        'all'  => 0,
        'main' => 1,
        'part' => 2
    ];

    const IS_DEL = [
        'no'  => 0,
        'yes' => 1
    ];

    const LEVEL = [
        'level_one'   => 1,
        'level_two'   => 2,
        'level_three' => 3
    ];

    const TAG_NAME = [
        'main' => 'main',
        'part' => 'part',
    ];

    private function __getList(): string
    {
        return AppCRedisKeys::getCateList();
    }


    /**
     * @throws RedisException
     */
    public function __delGetListCache()
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getList();
        $redis->del($redis_key);
    }

    private function __getOneInfoById($id): string
    {
        return AppCRedisKeys::getOneInfoById($id);
    }


    /**
     * @throws RedisException
     */
    public function __delGetOneInfoById($id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoById($id);
        $redis->del($redis_key);
    }


    private function __getOneInfoByPid($pid): string
    {
        return AppCRedisKeys::getOneInfoByPid($pid);
    }


    /**
     * @throws RedisException
     */
    public function __delGetOneInfoByPid($pid)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoByPid($pid);
        $redis->del($redis_key);
    }


    private function __getSameData($level, $pid): string
    {
        return AppCRedisKeys::getSameData($level, $pid);
    }

    /**
     * @throws RedisException
     */
    public function __delSameDataCache($level, $pid)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getSameData($level, $pid);
        $redis->del($redis_key);
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getList($type = 0): array
    {
        if (!is_numeric($type)) {
            return [false, '参数错误'];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getList();
        $sub_key   = CUtil::getAllParams(__FUNCTION__, $type);
        $aJson     = $redis->hGet($redis_key, $sub_key);
        $aData     = (array)json_decode($aJson, true);

        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `is_del` = 0 ORDER BY `sort`,`utime` DESC,`ctime`";
            $aData  = by::dbMaster()->createCommand($sql)->queryAll() ?? [];

            $redis->hset($redis_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }
        return $aData;
    }



    /**
     * @param $data
     * @param int $type
     * @return mixed
     * 取指定数据,如果没有就取所有
     */
    public function __getCondition($data, int $type = 0)
    {
        foreach ($data as $value) {
            if ($value['tag_name'] == (self::MACHINE_TYPE[$type] ?? '')) {
                return $value;
            }
        }
        return $data;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 通过id获取详情数据
     */
    public function getOneInfoById($id)
    {
        if (!$id) {
            return [];
        }
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoById($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` =:id AND `is_del` =:is_del LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id, ':is_del' => 0])->queryOne();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 通过pid获取数据
     */
    public function getOneInfoByPid($pid)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoByPid($pid);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `pid` =:pid AND `is_del` =:is_del ORDER BY `sort`";
            $aData  = by::dbMaster()->createCommand($sql, [':pid' => $pid, ':is_del' => 0])->queryAll();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 删除类目
     */
    public function del($id): array
    {
        if (!$id) {
            return [false, '请选择数据', []];
        }

        $selfInfo = $this->getOneInfoById($id);
        $p_key    = $selfInfo['pid'] ?? '';

        $childrenData = $this->getOneInfoByPid($id) ?? [];

        if ($childrenData) {
            return [true, '请先删除子类目', ['code' => -1, 'data' => []]];
        }

        $cate_info = $this->getOneInfoById($id);
        $level     = $cate_info['level'] ?? 0;
        $pid       = $cate_info['pid'] ?? 0;

        //判断是否有绑定商品,有的话返回同级别类目
        $gcate_info = by::gCateModel()->getCateInfoByCid($id);

        if ($gcate_info) {
            $sameData = $this->getSameData($level, $pid, $id);
            return [true, '已有商品绑定在此类目下,请先进行转移', ['code' => -1, 'data' => $sameData]];
        }
        $tb = self::tbName();

        $ret = by::dbMaster()->createCommand()->update($tb, ['is_del' => self::IS_DEL['yes'], 'utime' => intval(START_TIME), 'dtime' => intval(START_TIME)], ['id' => $id])->execute();
        if ($ret) {
            //删除类目同步IOT  无需校验值是什么  iot不存在不会报错
            Mall::factory()->deletePartByCateId($id);
            $this->__delGetListCache();
            $this->__delGetOneInfoByPid($id);
            $this->__delGetOneInfoById($id);
            $this->__delSameDataCache($level, $pid);
            $this->__delGetOneInfoByPid($p_key);
            return [true, '删除成功', []];
        }
        return [false, '删除失败', []];
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 增改类目
     */
    public function modify($id, $post, $user_id): array
    {
        $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 1, 'EX');
        if (!$anti) {
            return [false, '1s内请勿重复点击！'];
        }

        list($status, $ret) = $this->__checkModify($id, $post);

        if (!$status) {
            return [false, $ret];
        }

        $tb    = self::tbName();
        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            if (!$id) {
                $result = $db->createCommand()->insert($tb, $ret)->execute();
                $id     = $db->getLastInsertID();
            } else {
                $result = $db->createCommand()->update($tb, $ret, ["id" => $id])->execute();
            }
            $trans->commit();
            if ($result) {
                if ($id) {
                    Mall::factory()->deletePartByCateId($id, $ret['name'], false);//修改类目名称同步IOT
                }
                $this->__delGetListCache();
                $this->__delGetOneInfoByPid($ret['pid'] ?? '');
                $this->__delGetOneInfoById($id);
                $this->__delSameDataCache($ret['level'] ?? '', $ret['pid'] ?? '');
                return [true, $id];
            } else {
                return [false, '保存失败'];
            }
        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.cate');

            return [false, '操作失败'];
        }


    }


    /**
     * @throws Exception
     * @throws RedisException
     * 校验数据
     */
    private function __checkModify($id, $post): array
    {
        $name     = $post['name'] ?? '';
        $level    = $post['level'] ?? 1;
        $tag_name = $post['tag_name'] ?? '';
        $icon     = $post['icon'] ?? '';
        $pid      = $post['pid'] ?? 0;
        $sort     = $post['sort'] ?? '';

        $data = [];

        if (empty($name)) {
            return [false, '名称不能为空'];
        }


        $ret_name = self::checkNameExist($name, $level, $pid, $id);
        if ($ret_name) {
            return [false, '类目名称已存在'];
        }

        $data['name']  = $name;
        $data['pid']   = $pid;
        $data['level'] = $level;
        $data['icon']  = $icon;
        $data['utime'] = intval(START_TIME);
        //新增
        if (empty($id)) {
            $data['tag_name'] = $tag_name;
            if (!is_numeric($level) || $level > self::LEVEL['level_three'] || $level < self::LEVEL['level_one']) {
                return [false, '类目级别有误'];
            }

            if (!is_numeric($pid)) {
                return [false, '请选择正确的上级'];
            }

            switch ($level) {
                case self::LEVEL['level_one']:
                    if (empty($tag_name)) return [false, '顶级标签不能为空'];
                    if (!in_array($tag_name, array_values(self::TAG_NAME))) return [false, '标签有误,请联系管理员'];
                    $tb     = self::tbName();
                    $fields = implode("`,`", $this->tb_fields);
                    $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `tag_name` =:tag_name AND `is_del` =:is_del LIMIT 1 ";
                    $tag    = by::dbMaster()->createCommand($sql, [':tag_name' => $tag_name, ':is_del' => self::IS_DEL['no']])->queryOne();
                    if ($tag) return [false, '标签已存在,请联系管理员'];
                    break;
                case self::LEVEL['level_two']:
                    if (empty($pid)) {
                        return [false, '请选择正确的上级'];
                    }
                    break;
                case self::LEVEL['level_three']:
                    if (empty($pid)) {
                        return [false, '请选择正确的上级'];
                    }
                    if ($tag_name == self::TAG_NAME['part']) {
                        if (empty($icon)) {
                            return [false, '请上传图片'];
                        }
                    }
                    $data['tag_name'] = '';
                    break;
                default:
                    break;
            }
            $data['icon']  = $icon;
            $data['ctime'] = intval(START_TIME);
            $data['level'] = $level;
            $data['sort']  = $this->getSortByPid($pid);
            $data['pid']   = $pid;
        }
        return [true, $data];
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取排序
     */
    public function getSortByPid($pid): int
    {
        $data = $this->getOneInfoByPid($pid);
        if (!$data) {
            return 1;
        } else {
            $sort = array_column($data, 'sort');
            return (max($sort) + 1);
        }
    }


    /**
     * @throws Exception
     * 验证名称是否存在
     */
    public static function checkNameExist($name, $level, $pid, $id = 0)
    {
        $tb       = self::tbName();
        $sql      = "SELECT `name` FROM {$tb} WHERE `name`=:name AND `level`=:level AND `pid`=:pid AND `is_del`=:is_del AND `id` !=:id LIMIT 1";
        $ret_name = by::dbMaster()->createCommand($sql, [":name" => $name, ":level" => $level, ":pid" => $pid, ":is_del" => self::IS_DEL['no'], ":id" => $id])->queryOne();
        return $ret_name['name'] ?? '';
    }

    /**
     * @param $level
     * @param $pid
     * @param int $id
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 获取相同级别的类目
     */
    public function getSameData($level, $pid, int $id = 0)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getSameData($level, $pid);
        $sub_key   = CUtil::getAllParams(__FUNCTION__, $id);
        $aJson     = $redis->hget($redis_key, $sub_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE  `level`=:level AND `pid`=:pid AND `is_del`=:is_del AND `id` !=:id";
            $aData  = by::dbMaster()->createCommand($sql, [":level" => $level, ":pid" => $pid, ":is_del" => self::IS_DEL['no'], ":id" => $id])->queryAll();
            $redis->hset($redis_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 拖拽排序
     */
    public function drag($post): array
    {
        $id   = CUtil::uint($post['id'] ?? 0);
        $pid  = CUtil::uint($post['pid'] ?? 0);
        $sort = CUtil::uint($post['sort'] ?? '');

        if (empty($id) || empty($sort)) {
            return [false, '参数错误'];
        }

        $db = by::dbMaster();
        $tb = self::tbName();

        //没拖拽前数据
        $i_info = $this->getOneInfoByid($id);
        if (empty($i_info)) {
            return [false, '数据不存在！'];
        }
        //拖拽前level
        $i_level = $i_info['level'] ?? '';
        //拖拽后的父级数据
        $p_info = $this->getOneInfoByid($pid);

        $level = $p_info['level'] ?? 0;
        //拖拽后的level
        $drag_level = $level + 1;

        //判断是否跨级别
        if ($drag_level != $i_level) {
            //判断此类目下是否有子类目
            $same_info = $this->getOneInfoByPid($id);
            if ($same_info) {
                return [false, '此类目下有子类目'];
            }
            $g_info = by::gCateModel()->getCateInfoByCid($id);
            if ($g_info) {
                return [false, '此类目下有商品'];
            }

        }
        $sameData     = $this->getSameData($i_level, $pid, $id) ?? [];
        $idSorts      = array_column($sameData, 'sort', 'id');
        $batchArray[] = [
            'id'   => $id,
            'sort' => $sort
        ];
        foreach ($idSorts as $k => $v) {
            $batch['id'] = $k;
            if ($v >= $sort) {
                $batch['sort'] = $v + 1;
            } else {
                $batch['sort'] = $v;
            }
            $batchArray[] = $batch;
        }
        $uSql   = CUtil::batchUpdate($batchArray, 'id', $tb);
        $result = $db->createCommand($uSql)->execute();
        if ($result) {
            $this->__delSameDataCache($i_level, $pid);
            $this->__delGetListCache();
            $this->__delGetOneInfoByPid($pid);
            $this->__delGetOneInfoById($id);
            $this->__delGetOneInfoByPid($id);
            by::gCateModel()->__delCateInfoByCidCache($id);
        } else {
            return [false, '保存失败'];
        }
        return [true, '保存成功'];
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 找到父级形成树状
     */
    public function findParent($cid, $includeSelf = true): array
    {
        $list = by::cateModel()->getList(0);
        $aData = [];
        if ($list) {
            $selfArr  = array_column($list, null, 'id');
            $selfItem = $selfArr[$cid] ?? [];
            if ($includeSelf) {
                $selfItem && $aData[] = $selfItem;
            }
            $selfItem && $aData = $this->_getSelfParent($list, $selfItem, $aData);
        }
        return $aData;
    }

    /**
     * @param $list
     * @param $selfItem
     * @param $aData
     * @return mixed
     * 查询自己的父级
     */
    protected function _getSelfParent($list, $selfItem, $aData)
    {
        if(empty($selfItem)) return [];
        $data = $this->_getParentItem($list, $selfItem);
        $data && array_unshift($aData,$data);
        if ($data && $data['pid'] != 0) {
            $tree = $this->_getParentItem($list,$data);
            $tree && array_unshift($aData,$tree);
            if($tree && $tree['pid'] != 0){
                $tree1 = $this->_getParentItem($list,$tree);
                $tree1 && array_unshift($aData,$tree1);
            }
        }
        return $aData;
    }

    /**
     * @param $list
     * @param $selfItem
     * @return array
     * 获取父级数据
     */
    public function _getParentItem($list,$selfItem): array
    {
        $data = [];
        foreach ($list as $li){
            if($selfItem['pid'] != 0 && $selfItem['pid'] == $li['id'] && $selfItem['id'] != $selfItem['pid']){
                $data = $li;
                break;
            }
        }
        return $data;
    }

    /**
     * 根据 tag_name 获取分类列表
     * @param string $tagName
     * @param [] $columns
     * @return array
     */
    public function getCateByTagName(string $tagName): array
    {
        $items = $this->getOneInfoByPid(0);
        $items = array_column($items, null, 'tag_name');
        $data = $items[$tagName] ?? [];
        return $data;
    }

    /**
     * 根据 pids 获取分类列表
     * @param array $pids
     * @param [] $columns
     * @return array
     */
    public function getCateByPids(array $pids): array
    {
        $data = [];
        foreach ($pids as $pid) {
            $items = $this->getOneInfoByPid($pid);
            foreach ($items as $item) {
                $data[] = [
                    'id'   => $item['id'],
                    'name' => $item['name'],
                    'pid'  => $item['pid'],
                    'icon' => $item['icon'],
                ];
            }
        }
        return $data;
    }

    /**
     * 递归查询数据及其父级数据，直到 level=1 为止
     * @param array $ids
     * @param array $columns
     * @return array
     */
    public function getCateByIds(array $ids, array $columns = ['*'])
    {
        $result = [];

        // 查询数据
        $data = $this->getDataByIds($ids, $columns);
        $result = array_merge($result, $data);

        // 获取所有父级id
        $parentIds = array_column($data, 'pid');

        // 递归查询父级数据，直到 level=1
        if (!empty($parentIds)) {
            $parentData = $this->getCateByIds($parentIds, $columns);
            $result = array_merge($result, $parentData);
        }

        return $result;
    }

    /**
     * 根据ID数组查询数据
     * @param array $ids
     * @param array $columns
     * @return array
     */
    public function getDataByIds(array $ids, array $columns = ['*']): array
    {
        return self::find()
            ->select($columns)
            ->where(['id' => $ids])
            ->asArray(true)
            ->all();
    }

    /**
     * 获取分类
     * @param int $id
     * @param [] $columns
     * @param [] $with
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getCateById(int $id, $columns = ['*'], $with = [])
    {
        return self::find()
            ->with($with)
            ->select($columns)
            ->where(['id' => $id])
            ->andWhere(['is_del' => self::IS_DEL['no']])
            ->asArray(true)
            ->one();
    }

    /**
     * 获取父节点
     * @return ActiveQuery
     */
    public function getParent(): ActiveQuery
    {
        return $this->hasOne(self::class, ['id' => 'pid'])
            ->select(['id', 'name', 'tag_name', 'level', 'is_del'])
            ->where(['is_del' => self::IS_DEL['no']]);
    }

    public function getChildren(): ActiveQuery
    {
        return $this->hasMany(self::class, ['pid' => 'id'])
            //->select(['id', 'name', 'tag_name', 'level', 'is_del'])
            ->where(['is_del' => self::IS_DEL['no']]);
    }
}
