<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;

class PmConfigModel extends CommModel
{
    CONST MAX_REG = 5;//绑定超过五个产品不发优惠券
    public static function tbName(): string
    {
        return "`db_dreame`.`t_pm_config`";
    }

    private function __getPMConfig(): string
    {
        return AppCRedisKeys::getPMConfig();
    }

    private function __delCache()
    {
        $redis_key   = $this->__getPMConfig();
        by::redis()->del($redis_key);
    }

    public function getConfig(){
        $redis       = by::redis('core');
        $redis_key   = $this->__getPMConfig();
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);
        if ($aJson === false) {
            $tb      = $this->tbName();
            $sql     = "SELECT `max_count` FROM  {$tb} WHERE `id`=1 LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $aData     = $command->queryOne();

            $redis->set($redis_key, json_encode($aData),86400);
        }

        return $aData;
    }

    public function edit($max_count){
        $max_count = CUtil::uint($max_count);
        $tb        = $this->tbName();

        $data = [
            'max_count' => $max_count
        ];
        by::dbMaster()->createCommand()->update($tb,$data,"`id`=1")->execute();
        $this->__delCache();
        return [true,'ok'];
    }

}