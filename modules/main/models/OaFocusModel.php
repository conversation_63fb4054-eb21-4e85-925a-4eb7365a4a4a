<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/25
 * Time: 17:14
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\WXOA;
use app\models\by;
use app\models\CUtil;
use spec\Prophecy\Exception\Doubler\InterfaceNotFoundExceptionSpec;

class OaFocusModel extends CommModel {

    CONST SCAN_TYPE = [
        'channel_code' => 1001,  //渠道活码加密串类型
        'ar_code'      => 1002   //ar展示
    ];

    public $tb_fields = [
        'id','oa_openid','unionid','status','ctime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame`. `t_oa_focus`";
    }

    /**
     * @param $id
     * @return string
     * 根据id获取关注记录
     */
    private function __getOaFocusByIdKey($id) {
        return AppCRedisKeys::OaFocusByIdKey($id);
    }

    /**
     * @param $oa_openid
     * @return string
     * 根据oa_openid获取关注记录
     */
    private function __getOaFocusByOpenIdKey($oa_openid): string
    {
        return AppCRedisKeys::OaFocusByOpenIdKey($oa_openid);
    }

    /**
     * @param $Unionid
     * @return string
     * 根据Unionid获取关注记录
     */
    private function __getOaFocusByUnionidKey($Unionid): string
    {
        return AppCRedisKeys::OaFocusByUnionidKey($Unionid);
    }


    /**
     * @param $id
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据ID查找公众号记录
     */
    public function __getOneLog($id) {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOaFocusByIdKey($id);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = self::tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":id", $id);
            $aData   = $command->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * @param $oa_openid
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取关注记录；
     */
    public function GetALogByOpenId($oa_openid) {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOaFocusByOpenIdKey($oa_openid);
        $id          = $redis->get($redis_key);
        if($id === false) {
            $tb      = self::tbName();
            $sql     = "SELECT `id` FROM  {$tb} WHERE `oa_openid`=:oa_openid LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":oa_openid", $oa_openid);
            $aData   = $command->queryOne();
            $id      = $aData['id'] ?? 0;
            $redis->set($redis_key,$id,['EX'=>$id ? 600 : 10]);
        }

        if(empty($id)) {
            return [];
        }

        $aData = $this->__getOneLog($id);

        return $aData;
    }

    /**
     * @param $unionid
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 获取关注记录；
     */
    public function GetALogByUnionid($unionid) {
        $redis       = by::redis('core');
        $redis_key   = $this->__getOaFocusByUnionidKey($unionid);
        $id          = $redis->get($redis_key);
        if($id === false) {
            $tb      = self::tbName();
            $sql     = "SELECT `id` FROM  {$tb} WHERE `unionid`=:unionid LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":unionid", $unionid);
            $aData   = $command->queryOne();
            $id      = $aData['id'] ?? 0;
            $redis->set($redis_key,$id,['EX'=>$id ? 600 : 10]);
        }

        if(empty($id)) {
            return [];
        }

        $aData = $this->__getOneLog($id);

        return $aData;
    }

    /**
     * @param $oa_openid
     * @param $unionid : 取消关注时该值为空
     * @param $status
     * @param $eventKey
     * @param $info  //公众号用户信息
     * @return array
     * @throws \yii\db\Exception
     * 保存 关注/取消关注公众号状态
     */
    public function SaveLog($oa_openid,$unionid,$status,$eventKey = '',$info=[]) {

        if(empty($oa_openid)) {
            return [false,"无效信息"];
        }

        $tb      = self::tbName();
        $save    = [
            'ctime'    => intval(START_TIME),
            'status'   => intval(!!$status),
        ];

        if(!empty($unionid))
        {
            $save['unionid'] = $unionid;
        }

        $command = by::dbMaster()->createCommand();

        $aLog    = $this->GetALogByOpenId($oa_openid);

        if(empty($aLog['id'])) {
            $save['oa_openid'] = $oa_openid;
            $command->insert($tb,$save)->execute();
            $r_key = $this->__getOaFocusByOpenIdKey($oa_openid);

        } else {
            $command->update($tb,$save,"oa_openid=:oaid",[':oaid'=>$oa_openid])->execute();
            $r_key = $this->__getOaFocusByIdKey($aLog['id']);
        }

        by::redis('core')->del($r_key);

        return [true,"OK"];
    }


}
