<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\Device;
use app\components\EventMsg;
use app\components\IotDs;
use app\jobs\ProductRegisterExceptionJob;
use app\jobs\WarrantyCardJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\log\services\warranty\WarrantyCardService;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class ProductRegModel extends CommModel
{
    public $tb_fields = [
        'id', 'user_id', 'product_id', 'phone', 'sn', 'create_time', 'buy_time', 'card_no','is_support_care','care_extend_month','is_automatic'
    ];

    public static function getTable($user_id): string
    {
        $mod = $user_id % 10;
        return "`db_dreame_log`.`t_p_reg_{$mod}`";
    }

    public static function getTableMod($mod): string
    {
        return "`db_dreame_log`.`t_p_reg_{$mod}`";
    }

    const ERR_CODE = [
        'ERR'             => -1,      //其他错误'
        'OK'              => 1,       //成功
        'REPEAT'          => 100,     //已被绑定
        'NO_DATA'         => 200,     //用户未激活设备
        'REGISTER_FAIL'   => 300,     //注册失败 sn码不匹配
        'RESTART_DEVICE'  => 400,     //请重启机器后再进行产品注册
        'CHANGE_SN_ERROR' => 500,     //sn码修改失败
        'SELF_REPEAT'     => 600,     //自己相同手机号重复注册
    ];


    /**
     * @param $user_id
     * @return string
     * 注册产品列表
     */
    private function __getProductRegList($user_id): string
    {
        return AppCRedisKeys::getProductRegList($user_id);
    }

    /**
     * @param $user_id
     * @param $id
     * @return string
     * 注册产品
     */
    private function __getProductReg($user_id, $id): string
    {
        return AppCRedisKeys::getProductReg($user_id, $id);
    }

    private function __getPRegBySn($user_id, $sn): string
    {
        return AppCRedisKeys::getPRegBySn($user_id, $sn);
    }

    private function __delCacheOne($user_id, $id, $sn ='')
    {
        $r_key = $this->__getProductReg($user_id, $id);
        $r_key1 = $this->__getPRegBySn($user_id, $sn);
        return by::redis('core')->del($r_key,$r_key1);
    }

    private function __delCacheList($user_id)
    {
        $r_key = $this->__getProductRegList($user_id);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $user_id
     * @param $product_id
     * @param $sn
     * @param $buy_time
     * @return array
     * @throws Exception
     * 产品注册
     */
    public function reg($user_id, $product_id, $sn, $buy_time,$single =0): array
    {
        $user_id    = CUtil::uint($user_id);
        $product_id = CUtil::uint($product_id);
        $buy_time   = CUtil::uint($buy_time);
        $sn         = trim(CUtil::removeXss($sn));
        $time       = intval(START_TIME);

        //相同sn编码10分钟内只能提交一次
        $unique_key = CUtil::getAllParams(__FUNCTION__, $sn);
        list($anti) = self::ReqAntiConcurrency(0, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '请勿重复提交', self::ERR_CODE['ERR']];
        }

        $phone = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            return [false, '用户未绑定手机号', self::ERR_CODE['ERR']];
        }

        if (empty($user_id) || empty($product_id) || empty($buy_time) || empty($sn)) {
            return [false, '缺少参数', self::ERR_CODE['ERR']];
        }

        //购买时间校验
        if ($buy_time > intval(START_TIME)) {
            return [false, '购买时间不能大于当前时间~', self::ERR_CODE['ERR']];
        }

        $sn_time = CUtil::getTimeBySn($sn, $buy_time);
        if ($sn_time === false) {
            return [false, '购买时间有误，请确认~', self::ERR_CODE['ERR']];
        }

        $user = by::users()->getOneByUid($user_id);
        if (empty($user)) {
            return [false, '用户不存在', self::ERR_CODE['ERR']];
        }

        $product  = by::product()->getOneById($product_id); // 获取产品信息
        // 兼容历史版本

//        if (!empty($single)) {
            //  判断sn是扫地机
            $tagCode = by::Gtag()->GetTagCodeMap();
            // if (!empty($product['tid']) && $product['tid'] == by::Gtag()::TAG['SWEEP']) {
            if (!empty($product['tid']) && $product['tid'] == $tagCode['SWEEP']) {
                list($s, $msg, $err) = $this->checkRegPermission($user_id, $sn);
                if(!$s){
                    self::pushExceptionJob($sn, $product_id, $user, $phone, $buy_time, $err);
                    return [false, $msg, $err];
                }
            }else{ // 非扫地机
                $regSnInfo = by::model('SnRegModel', 'main')->getInfoBySn($sn);
                if($regSnInfo){
                    self::pushExceptionJob($sn, $product_id, $user, $phone, $buy_time, self::ERR_CODE['REPEAT']);
                    return [false, 'SN码已被占用~', self::ERR_CODE['REPEAT']];
                }
            }
//        }else{ // 兼容历史版本
//            $regSnInfo = by::model('SnRegModel', 'main')->getInfoBySn($sn);
//            if($regSnInfo){
//                self::pushExceptionJob($sn, $product_id, $user, $phone, $buy_time, self::ERR_CODE['REPEAT']);
//                return [false, 'SN码已被占用~', self::ERR_CODE['REPEAT']];
//            }
//        }

        $tran = by::dbMaster()->beginTransaction();

        try {
            $data = [
                    'user_id'           => $user_id,
                    'product_id'        => $product_id,
                    'buy_time'          => $buy_time,
                    'phone'             => $phone,
                    'sn'                => $sn,
                    'create_time'       => $time,
                    'is_support_care'   => $product['is_support_care'],
                    'is_automatic'      => $product['is_automatic'] ?? 0,
                    'care_extend_month' => $product['care_extend_month'] ?? 0
            ];

            $tb = self::getTable($user_id);
            $db = by::dbMaster();

            $ret = by::dbMaster()->createCommand()->insert($tb, $data)->execute();
            if (!$ret) {
                throw new MyExceptionModel('产品注册失败', self::ERR_CODE['ERR']);
            }

            $reg_id = $db->getLastInsertID();
            list($status, $msg) = by::model('ProductDetailModel', 'main')->addRegDetail($user_id, $phone, $reg_id, $product_id, $sn);
            if (!$status) {
                throw new MyExceptionModel($msg, self::ERR_CODE['ERR']);
            }

            list($status, $msg) = by::model('SnRegModel', 'main')->add($user_id, $sn);
            if (!$status) {
                throw new MyExceptionModel($msg, self::ERR_CODE['REPEAT']);
            }
            $is_care = 0;
            if ($product['is_support_care'] == 1 && $product['is_automatic'] == 1) {
                $is_care = 1;
            }

            $is_card = 0;
            //领取优惠券 (为了不影响产品绑定的主要逻辑，这里不要抛异常)
            list(, $pm_ids) = by::pMarket()->getIdsByPid($product_id);//查询绑定优惠券
            $get_channel = UserCardModel::GET_CHANNEL['product'];
            $card_list   = [];
            if (!empty($pm_ids)) {
                $config    = by::PmConfigModel()->getConfig();
                $max_count = $config['max_count'] ?? 0;
                $count     = by::userCard()->getCountByGetChannel($user_id, $get_channel);

                foreach ($pm_ids as $pm_id) {
                    //领取最大次数验证
                    if ($count >= $max_count) {
                        break;
                    }

                    $is_card = 1;
                    //配置资源信息验证
                    $pMarket = by::pMarket()->getOneById($pm_id);
                    if (!$pMarket) {
                        continue;
                    }

                    //扣除库存
                    list($status) = by::pMarket()->stockModify($product_id, $pm_id, 1);
                    if (!$status) {
                        continue;
                    }

                    list($status, $msg) = by::userCard()->setCard($user_id, $pMarket['mc_id'], $get_channel, $reg_id);
                    if (!$status) {
                        by::pMarket()->stockModify($product_id, $pm_id, -1);//回滚库存
                        CUtil::debug("绑定产品发放优惠券异常信息:{$msg},user_id={$user_id},资源信息：" . json_encode($pMarket), 'link-product_reg_card');
                        continue;
                    }

                    $market = by::marketConfig()->couponCondition($pMarket['mc_id']);
                    if (!$market) {
                        continue;
                    }
                    $card_list[] = $market;
                }
            }

            $this->__delCacheList($user_id);

            $tran->commit();

            EventMsg::factory()->run('productReg', ['user_id' => $user_id, 'sn' => $sn]);

            //异步产生保修卡
            \Yii::$app->queue->push(new WarrantyCardJob(['is_apply'=>false,'apply_no'=>'','user_id'=>$user_id,'sn'=>$sn,'buy_time'=>$buy_time]));
            // WarrantyCardService::getInstance()->CreateWarrantyCardSync(false,'',$user_id,$sn,$buy_time);

            //todo 注册成功后查询是否在白名单中，如果在白名单中则加入标识
            $regWhiteModel = byNew::RegWhiteListModel();
            if($sn && $regWhiteModel->GetOneInfo(['sn'=>$sn])){
                $regWhiteModel->SaveLog(['sn'=>$sn,'is_reg'=>1,'reg_time'=>time(),'reg_user_id'=>$user_id]);
            }
            $regInfo = $this->getOneById($user_id,$reg_id);
            $regInfo['product'] = $product;

            // 这里修改的原因是如果优惠券库存为0，is_card还是为1，前端会有弹框，所以需要同时判断优惠券列表是否为空
            return [true, ['is_card' => ($is_card && count($card_list) > 0) ? 1 : 0, 'list' => $card_list,'is_care'=>$is_care,'care_info'=>$regInfo], self::ERR_CODE['OK']];
        } catch (MyExceptionModel $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'link-product_reg');

            $tran->rollBack();

            self::ReqAntiConcurrency(0, $unique_key, 1, 'DEL');

            return [false, $e->getMessage(), $e->getCode()];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'link-product_reg');

            $tran->rollBack();

            self::ReqAntiConcurrency(0, $unique_key, 1, 'DEL');

            return [false, '操作失败', self::ERR_CODE['ERR']];
        }
    }

    /**
     * @throws Exception
     */
    public function checkRegPermission($userId, $sn)
    {
        // 查询用户手机号是否已绑定，检查SN码唯一性，获取用户注册记录数量
        list($phone, $regInfo, $count) = $this->getInitialData($userId, $sn);

        if (empty($phone)) {
            return [false, '用户未绑定手机号', self::ERR_CODE['ERR']];
        }


        // 同一用户已经注册过该SN码
        if ($regInfo && $regInfo['user_id'] == $userId) {
            return [false, '您已绑定该SN~', self::ERR_CODE['SELF_REPEAT']];
        }

        // SN已被占用（产品反馈这种情况不防范，同一用户可以反复注销注册）
//        $phoneReg = $regInfo['phone'] ?? '';
//        if($phoneReg && $phoneReg == $phone){ //相同手机号
//            return [false, 'SN码已被占用~', self::ERR_CODE['SELF_REPEAT']];
//        }

        //检查是否在白名单中，在白名单中的不做限制
        if(empty($regInfo) && $sn && byNew::RegWhiteListModel()->GetOneInfo(['sn'=>$sn])){
            return [true, 'ok', self::ERR_CODE['OK']];
        }

        // 用户有注册记录
        if ($count > 0) {
            return $this->handleUserRecords($userId, $sn, $regInfo, 1);
        }

        // 用户无注册记录
        return $this->handleUserRecords($userId, $sn, $regInfo, 0);
    }

    /**
     * @throws Exception
     */
    private function getInitialData($userId, $sn)
    {
        $phone = by::Phone()->getPhoneByUid($userId);
        $regSnInfo = by::model('SnRegModel', 'main')->getInfoBySn($sn);
        $regInfo = $this->getPRegInfo($regSnInfo['user_id'] ?? 0, $sn);

        $count = $this->getRegDataCountByPhone($phone);

        return [$phone, $regInfo, $count];
    }

    /**
     * @throws Exception
     */
    private function handleUserRecords($userId, $sn, $regInfo, $isWithRecords)
    {
        // SN未被占用
        if (empty($regInfo)) {
            if ($isWithRecords) {
                return $this->checkIfSweepRegister($userId, $sn);
            } else {
                return [true, 'ok', self::ERR_CODE['OK']];
            }
        }


        // 查找占用方是否已经有清扫记录
//        list($sta, $data) = $this->checkIfSweepRegister($regInfo['user_id'], $sn, false);
        $mallInfo = by::usersMall()->getInfoByUserId($regInfo['user_id']);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) {
            return [false, '用户UID不存在',self::ERR_CODE['NO_DATA']];
        }

        list($sta, $data) = IotDs::factory()->run('judgecleanrecord', ['uid' => $uid, 'sn' => $sn]);
        if ($data) { // 实际已经被占用
            return [false, 'SN码已被占用', self::ERR_CODE['REPEAT']];
        }

        // 对方未注册，判断用户是否配网
        list($status, $message) = $this->checkIfSweepRegister($userId, $sn);
        if (!$status) {
            return [false, $message, self::ERR_CODE['NO_DATA']];
        }
        // 修改错误注册数据
        list($status, $message, $errorCode) = $this->changeWrongRegData($regInfo['create_time'], $regInfo['id'], $sn);
        if (!$status) {
            return [false, $message, self::ERR_CODE['CHANGE_SN_ERROR']];
        }

        return [true, 'ok', self::ERR_CODE['OK']];
    }


    private function changeWrongRegData($create_time, $reg_id, $sn)
    {
        if (empty($create_time) || empty($reg_id)) return [false, '未找到产品注册记录~', self::ERR_CODE['CHANGE_SN_ERROR']];
        //1.删除对方注册记录
        $info     = by::model('ProductDetailModel', 'main')->getRegDetailByRegId($create_time, $reg_id, $sn);
        $detailId = $info['id'] ?? '';
        if ($detailId) {
            $data['sn'] = $sn . "|" . microtime(true);
            list($s, $msg) = by::model('ProductDetailModel', 'main')->updateRegDetail($create_time, $detailId, $data);
            if (!$s) {
                return [false, $msg, self::ERR_CODE['CHANGE_SN_ERROR']];
            }
        } else {
            return [false, '未找到产品注册记录~', self::ERR_CODE['CHANGE_SN_ERROR']];
        }
        return [true, 'ok', self::ERR_CODE['OK']];
    }

    public function CheckIfSweepRegister($user_id, $sn, $isCheckSweep = true)
    {
        // 获取设备列表
        list($status, $ret) = Device::factory()->run('list_v2', [
            'user_id'      => $user_id,
            'sharedStatus' => 1,
            'lang'         => 'zh',
            'offset'       => 1,
            'limit'        => 20
        ]);

        $records = $ret['page']['records'] ?? [];

        if($records){
            if ($isCheckSweep) {
                // 二维数组 字段中model不含有vacuum 说明不是扫地机 需要过滤
                $records = array_filter($records, function ($item) {
                    return strpos($item['model'], 'vacuum') !== false;
                });


                // 过滤掉 共享设备： master=>false  的记录
                $records = array_filter($records, function ($item) {
                    return $item['master'] !== false;
                });
            }

            // 过滤掉 共享设备： master=>false  的记录，并获取 did 列值
            $dids = array_column($records, 'did');

            // 账号下无设备，引导配网
            if(empty($dids)){
                return [false, '用户未激活设备', self::ERR_CODE['NO_DATA']];
            }

            $snData = [];
            // 根据did获取sn码
            foreach ($dids as $did) {
                $deviceProps = Device::factory()->run('props', ['user_id' => $user_id, 'did' => $did]);
                $deviceSn = $deviceProps[1][0]['value'] ?? '';
                if ($deviceSn == $sn) {
                    return [true, 'ok', self::ERR_CODE['OK']];
                }
                if ($deviceProps[0] && $deviceSn) {
                    $snData[] = $deviceSn; // 确保每次添加的都是有效值
                }
            }

            // 该sn不在设备列表中,查找翻新记录
            if(!in_array($sn, $snData) && !empty($snData)){
                $oldSn = byNew::SnRenovateModel()->GetOldSnByNewSn($sn);
                !empty($oldSn) && $sn = $oldSn;
            }

            // 设备列表设备数量 与 sn码数量不一致 => 已配网，但是未机器上报sn码
            if (!in_array($sn, $snData) && count($dids) != count($snData)) {
                return [false, '请重启机器后再进行产品注册', self::ERR_CODE['RESTART_DEVICE']];
            }

            // 账号下有设备，但未匹配上sn码 这种情况是设备列表中没有该sn码 按要求修改为去配网
            if (!in_array($sn, $snData)) {
                return [false, '用户未激活设备', self::ERR_CODE['NO_DATA']];
            }

            return [true, 'ok', self::ERR_CODE['OK']];

        }else{
            return [false, '用户未激活设备', self::ERR_CODE['NO_DATA']];
        }
    }

    /**
     * @param $sn
     * @param $product_id
     * @param $user
     * @param $mallInfo
     * @param $buy_time
     * @param $errCode
     * @return void
     * 记录异常申请
     */
    private static function pushExceptionJob($sn, $product_id, $user, $phone, $buy_time, $errCode)
    {
        \Yii::$app->queue->push(new ProductRegisterExceptionJob([
            'sn'        => $sn,
            'productId' => $product_id,
            'user'      => $user,
            'phone'     => $phone,
            'buyTime'   => $buy_time,
            'errCode'   => $errCode,
        ]));
    }

    /**
     * @param $user_id
     * @param $id
     * @param $sn
     * @param $before_sn
     * @return array
     * @throws Exception
     * 后台手动修改sn码
     */
    public function updateSn($user_id, $id, $sn, $before_sn)
    {
        if (empty($user_id) || empty($id) || empty($sn)) {
            return [false, '参数错误(1)'];
        }

        $db = by::dbMaster();
        $tb = self::getTable($user_id);

        $trans = $db->beginTransaction();

        try {
            by::model('SnRegModel', 'main')->updateSn($before_sn, $sn);

            $db->createCommand()->update($tb, ['sn' => $sn], ['id' => $id])->execute();

            $trans->commit();

            $this->__delCacheOne($user_id, $id, $before_sn);

            //todo 如果原SN不等于现SN将保修卡sn号变更
            $regInfo = $this->getOneById($user_id,$id);
            if($regInfo && $regInfo['card_no'] && $before_sn != $sn){
                by::WarrantyCard()->UpdateLog($regInfo['card_no'],['sn'=>$sn,'utime'=>time()]);
            }

            return [true, 'ok'];
        } catch (\Exception $e) {
            $trans->rollBack();
            return [false, '用户sn码修改失败'];
        }
    }

    public function updateCardNo($user_id, $id, $card_no): array
    {
        if (empty($user_id)|| empty($id)  || empty($card_no) ) {
            return [false, '参数错误(2)'];
        }

        $info = $this->getOneById($user_id,$id);
        if(empty($info)){
            return [false, '数据不存在'];
        }

        $db = by::dbMaster();
        $tb = self::getTable($user_id);

        $trans = $db->beginTransaction();

        try {
            $db->createCommand()->update($tb, ['card_no' => $card_no], ['id' => $id])->execute();

            $trans->commit();

            $this->__delCacheOne($user_id, $id, $info['sn']??'');

            return [true, 'ok'];
        } catch (\Exception $e) {
            $trans->rollBack();
            return [false, '用户保修卡号修改失败'];
        }
    }

    /**
     * @param $user_id
     * @param $id
     * @return array|false
     * @throws Exception
     * 获取指定记录
     */
    public function getOneById($user_id, $id)
    {
        $user_id = CUtil::uint($user_id);
        $id = CUtil::uint($id);
        if (empty($id) || empty($user_id)) {
            return [];
        }

        $redis = by::redis('core');
        $redis_key = $this->__getProductReg($user_id, $id);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);

        if (empty($aData)) {
            $tb = $this->getTable($user_id);
            $fields = implode("`,`", $this->tb_fields);

            $sql = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":id", $id);
            $aData = $command->queryOne();
            $aData = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }

        return $aData;
    }

    public function getRegList($user_id)
    {
        $user_id = CUtil::uint($user_id);

        $r_key = $this->__getProductRegList($user_id);
        $aJson = by::redis('core')->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = self::getTable($user_id);

            $sql = "SELECT `id` FROM  {$tb} WHERE `user_id`=:user_id AND `sn` NOT LIKE '%|%' ORDER BY `id` DESC";
            $aData = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryAll();

            $aData = empty($aData) ? [] : $aData;
            $aData = array_column($aData, 'id');
            by::redis('core')->set($r_key, json_encode($aData));
            CUtil::ResetExpire($r_key, 81600);
        }

        return $aData;
    }


    public function GetRegCountByProductId($user_id,$product_ids)
    {
        if(empty($user_id)||empty($product_ids) || !is_array($product_ids)) return 0;
        $product_ids = implode("','",$product_ids);
        $tb = self::getTable($user_id);
        $sql = "SELECT  COUNT(*) FROM  {$tb} WHERE `user_id`=:user_id AND `product_id` IN ('{$product_ids}')";
        $count =  by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryScalar();
        $count = empty($count) ? 0 : $count;
        return intval($count);
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * @throws RedisException
     * 用户产品列表
     */
    public function userPList($user_id,$is_act=0)
    {
        $ids = by::productReg()->getRegList($user_id);

        $list = [];
        if ($ids) {
            foreach ($ids as $id) {
                $r_info    = by::productReg()->getOneById($user_id, $id);
                $p_info    = by::product()->getOneById($r_info['product_id'] ?? '');
                $pre_sn    = $p_info['sn'] ?? '';
                $p_ids     = by::mainSalesModel()->getIdBySku($p_info['m_name'] ?? '', by::partsSalesModel()::HOST_TYPE);
                $part_info = [];
                //todo 优化
                foreach ($p_ids as $p_id) {
                    $part_info = by::partsSalesModel()->GetOneByMainId($p_id);
                }
                $list[] = [
                    'reg_id'      => $r_info['id'] ?? '',
                    'product_id'  => $p_info['id'] ?? '',
                    'image'       => $p_info['image'] ?? '',
                    'name'        => $p_info['name'] ?? '',
                    'sn'          => $r_info['sn'] ?? '',
                    'create_time' => $r_info['create_time'] ?? '',
                    'buy_time'    => $r_info['buy_time'] ?? '',
                    'sn_time'     => 0,
                    'word'        => '免费延长保修期一年',
                    'card_no'     => $r_info['card_no'] ?? '',
                    'part_info'   => array_values($part_info),
                    'pre_sn'      => $pre_sn,
                    'product'     => $p_info
                ];
            }

            if (empty($is_act)) {
                $list = by::WarrantyCard()->bindBlackList($list);//黑名单信息
                $list = by::WarrantyCard()->bindWarrantyCard($user_id, $list);//保修卡信息
                $list = by::WarrantyApply()->bindWarrantyApply($user_id, $list);//保修卡申请信息
            } else {
                $list = by::GmainAcDepriceModel()->bindAcDeprice($list,'pre_sn');
            }
        }



        return $list;
    }





    /**
     * @param $user_id
     * @param $sn
     * @return array|DataReader
     * @throws Exception
     * 获取注册产品信息
     */
    public function getPRegInfo($user_id, $sn)
    {
        $user_id = CUtil::uint($user_id);
        $sn = CUtil::removeXss($sn);
        if (empty($sn) || empty($user_id)) {
            return [];
        }

        $redis = by::redis('core');
        $redis_key = $this->__getPRegBySn($user_id, $sn);
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);

        if (empty($aData)) {
            $tb = $this->getTable($user_id);
            $fields = implode("`,`", $this->tb_fields);

            $sql = "SELECT `{$fields}` FROM  {$tb} WHERE `sn`=:sn LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":sn", $sn);
            $aData = $command->queryOne();
            $aData = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }

        return $aData;
    }


    /**
     * 注册列表
     * @param array $groupIds
     * @return array
     * @throws Exception
     */
    public function getListByGroupIds(array $groupIds): array
    {
        $data = [];
        // 查询字段
        $columns = implode("`,`", $this->tb_fields);
        // 分组查询
        foreach ($groupIds as $index => $ids) {
            $tb = self::getTable($index);
            // 查询条件
            $ids = implode(',', $ids);
            // 执行SQL
            $sql = "SELECT `{$columns}` FROM {$tb} WHERE `id` IN ({$ids})";
            $items = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            foreach ($items as $item) {
                $data[$item['user_id']][$item['id']] = [
                    'sn'          => $item['sn'],
                    'buy_time'    => $item['buy_time'],
                    'create_time' => $item['create_time'],
                    'is_support_care' =>$item['is_support_care'],
                    'care_extend_month' => $item['care_extend_month'],
                    'is_automatic' =>$item['is_automatic']
                ];
            }
        }
        return $data;
    }


    /**
     * @param $phone
     * @return array|DataReader
     * @throws Exception
     * 根据手机号查询注册信息
     */
    public function GetRegDataByPhone($phone)
    {
        $sql = "";
        for ($i = 0; $i <= 9; $i++) {
            $tableName = self::getTableMod($i);
            $sql .= ($i > 0 ? "UNION ALL " : "") . "SELECT * FROM {$tableName} WHERE phone = :phone ";
        }

        $command = by::dbMaster()->createCommand($sql);
        $command->bindParam(":phone", $phone);
        $data = $command->queryAll();
        return $data;
    }

    /**
     * @param $phone
     * @return int|mixed
     * @throws Exception
     * 查询手机号为phone的记录数量
     */
    public function GetRegDataCountByPhone($phone)
    {
        // 查询手机号为phone的记录数量
        $sql = "";
        for ($i = 0; $i <= 9; $i++) {
            $tableName = self::getTableMod($i);
            $sql .= ($i > 0 ? "UNION ALL " : "") . "SELECT COUNT(*) as count FROM {$tableName} WHERE phone = :phone ";
        }

        $command = by::dbMaster()->createCommand($sql);
        $command->bindParam(":phone", $phone);
        $data = $command->queryAll();

        $totalCount = 0;
        foreach ($data as $row) {
            $totalCount += $row['count'];
        }

        return $totalCount;
    }

}
