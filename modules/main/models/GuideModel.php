<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\WeWork;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\DataReader;
use yii\db\Exception;

class GuideModel extends CommModel
{
    public static $expire = 3600;

    const TIME = !YII_ENV_PROD ? 43200 : 60*86400;


    public static function tbName(): string
    {
        return '`db_dreame`.`t_guide`';
    }


    public $tb_fields = [
        'id', 'name', 'user_id', 'store', 'status', 'job_no'
    ];

    /**
     * @param $user_id
     * 根据id获取信息
     */
    private function __getGuideByUidKey($user_id){
        return AppCRedisKeys::getGuideByUidKey($user_id);
    }

    /**
     * @param $job_no
     * @return string
     * 根据工号获取信息
     */
    private function __getGuideByJobNoKey($job_no){
        return AppCRedisKeys::getGuideByJobNoKey($job_no);
    }

    /**
     * 列表缓存
     */
    private function __adminGuideKey(){
        return AppCRedisKeys::adminGuideKey();
    }

    /**
     * @param $user_id
     * 缓存清理
     */
    private function __delCache($user_id,$job_no='')
    {
        $r_key1 = $this->__getGuideByUidKey($user_id);
        $r_key2 = $this->__adminGuideKey();
        $r_key3 = $this->__getGuideByJobNoKey($job_no);
        by::redis('core')->del($r_key1,$r_key2,$r_key3);
    }



    /**
     * @param $user_id
     * @return array|DataReader|false
     * @throws Exception
     * 根据用户ID获取扩展字段
     */
    public function getGuideInfo($user_id)
    {
        $user_id     = CUtil::uint($user_id);
        if($user_id <=0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getGuideByUidKey($user_id);
        $aJson        = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = self::tbName();
            $fields  = implode("`,`", $this->tb_fields);
            $sql        = "SELECT `{$fields}` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $aData;
    }


    /**
     * @param string $job_no
     * @return array|DataReader|false
     * @throws Exception
     * 根据工号获取扩展字段
     */
    public function getGuideInfoByJobNo(string $job_no='')
    {
        if(empty($job_no)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getGuideByJobNoKey($job_no);
        $aJson        = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = self::tbName();
            $fields  = implode("`,`", $this->tb_fields);
            $sql        = "SELECT `{$fields}` FROM {$tb} WHERE `job_no`=:job_no LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':job_no' => $job_no])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param $data
     * @throws Exception
     */
    public function saveGuide($user_id, $data)
    {
        $data['user_id'] = CUtil::uint($user_id);

        $fields = array_keys($data);
        $fields = implode("`,`",$fields);

        $rows   = implode("','",$data);

        $dup    = [];
        foreach ($data as $key => $value){
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup    = implode(' , ',$dup);
        $tb     = self::tbName();
        $sql    = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";

        by::dbMaster()->createCommand($sql)->execute();

        //删除列表缓存
        $jobNo = $data['job_no'] ?? '';
        $this->__delCache($user_id,$jobNo);

        return [true,'ok'];
    }


    public function getWxUserData($w_uid){
        list($status,$user) =  WeWork::factory()->getUser($w_uid);
        if(!$status){
            return [false,$user.'(1)'];
        }

        $department_id = end($user['department']);
        list($status,$department) =  WeWork::factory()->getDepartment($department_id);
        if(!$status){
            return [false,$department.'(2)'];
        }

        $data=[
            'phone' => $user['mobile'] ??'',
            'avatar' => $user['avatar'] ??'',
            'name'   => $user['name'] ?? '',
            'store'  => $department['name'] ?? '',
        ];

        return [true,$data];
    }

    /**
     * @param $user_id
     * @param $avatar
     * @return array
     * @throws Exception
     * 更新企业微信信息（1小时最多更新一次）
     */
    public function upWorkData($user_id, $avatar): array
    {
        $user_id = CUtil::uint($user_id);

        $user_avatar = by::users()->getOneByUid($user_id)['avatar'] ?? '';
        if (!empty($user_avatar)) {
            $unique_key = CUtil::getAllParams(__FUNCTION__, $user_id);
            list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,3600,'EX');
            if(!$anti) {
                return [false, "1小时只能更新1次"];
            }
        }

        $main = by::users()->getUserMainInfo($user_id);

        if (empty($main)){
            return [false,'用户不存在'];
        }
        if (!in_array($main['user_type'],[10,11])){
            return [false,'不属于导购员'];
        }

        list($status,$workData) = $this->getWxUserData($main['openudid']);
        if (!$status){
            return [false,$status];
        }

        $trans  = by::dbMaster()->beginTransaction();

        try {
            $user_update = [
                'nick'      => $workData['name'],
                'real_name' => $workData['name']
            ];

            if (!empty($avatar)) {
                $user_update['avatar'] = $avatar;
            }

            //修改用户表，姓名 头像
            $s = by::users()->updateMembersInfo($user_id, $user_update);
            if (!$s) {
                throw new MyExceptionModel('修改users失败');
            }

            //修改导购表,姓名，门店
            list($s, $m) = $this->saveGuide($user_id, [
                'name'  => $workData['name'],
                'store' => $workData['store'],
            ]);

            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //修改手机号
            /*list($s, $m) =by::Phone()->SaveRelation($user_id, $phone);
            if (!$s) {
                throw new MyExceptionModel($m);
            }*/

            $trans->commit();
            return [true,'ok'];
        } catch (MyExceptionModel $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            $trans->rollBack();

            CUtil::debug($error, 'link-guide-save');
            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            $trans->rollBack();

            CUtil::debug($error, 'link-guide-save');
            return [false,$_e->getMessage()];
        }

    }

    /**
     * @param $page
     * @param $page_size
     * @param int $user_id
     * @param string $name
     * @param int $status
     * @return array|DataReader
     * @throws Exception
     */
    public function getList($page,$page_size,$user_id=0,$name='',$status=0)
    {
        $redis_key  = $this->__adminGuideKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__,$page,$page_size,$user_id,$name,$status);
        $redis      = by::redis();
        $aJson      = $redis->hGet($redis_key, $h_key);
        $list       = (array)json_decode($aJson,true);
        if($aJson === false){
            $tb = self::tbName();
            list($where,$params) = $this->_condition($user_id,$name,$status);
            list($offset) = CUtil::pagination($page,$page_size);
            $sql  = "select `user_id` from {$tb} where {$where} order by `user_id` desc limit {$offset},{$page_size}";
            $list = by::dbMaster()->createCommand($sql,$params)->queryAll();
            $redis->hSet($redis_key,$h_key,json_encode($list));
            CUtil::ResetExpire($redis_key,self::$expire);
        }

        return $list;
    }

    public function getGuideData($list){
        $return = [];
        foreach ($list as $value) {
            $user   = by::users()->getOneByUid($value['user_id']);
            $main   = by::users()->getUserMainInfo($value['user_id']);
            $phone  = by::Phone()->GetPhoneByUid($value['user_id']);
            $guide  = $this->getGuideInfo($value['user_id']);

            if (empty($user)){
                $status = '注销';
            }else{
                $status = UserModel::STATUS[$user['status']];
            }
            $return[] = [
                'user_id'  => $value['user_id'],
                'name'     => $guide['name'] ?? '',
                'phone'    => $phone ?? '',
                'reg_time' => $main['reg_time'] ?? 0,
                'store'    => $guide['store'] ?? '',
                'job_no'   => $guide['job_no'] ?? '',
                'status'   => $status,
            ];
        }

        return $return;
    }

    /**
     * @param int $user_id
     * @param string $name
     * @param int $status
     * @return false|string|DataReader|null
     * @throws Exception
     */
    public function getTotal($user_id=0,$name='',$status=0)
    {
        $redis_key  = $this->__adminGuideKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__,$user_id,$name,$status);
        $redis      = by::redis();
        $total      = $redis->hGet($redis_key, $h_key);
        if($total === false){
            $tb     = self::tbName();
            list($where,$params) = $this->_condition($user_id,$name,$status);
            $sql    = "select count(*) from {$tb} where {$where}";
            $total  = by::dbMaster()->createCommand($sql,$params)->queryScalar();

            $redis->hSet($redis_key,$h_key,$total);
            CUtil::ResetExpire($redis_key,self::$expire);
        }
        return $total;
    }

    /**
     * @param $user_id
     * @param $name
     * @param $status
     * @return array
     * @throws Exception
     */
    protected function _condition($user_id,$name,$status)
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if(!empty($user_id)) {
            if(strpos($user_id, '|')!==false){
                $uids    = explode('|',$user_id);
                $uids    = implode(',',$uids);
                $where  .= " AND `user_id` IN ({$uids})";
            }else{
                if (strlen($user_id) == 11) {
                    $uids = by::Phone()->GetUidsByPhone($user_id);
                    if(!empty($uids)) {
                        $uids    = implode(',',$uids);
                        $where  .= " AND `user_id` IN ({$uids})";
                    }else{
                        //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                        $where  .= " AND `user_id` = -1";
                    }
                } else {
                    $where  .= " AND `user_id` = {$user_id}";
                }
            }
        }

        if(!empty($name)) {
            $where              .= " AND (`name` LIKE '%{$name}%' or `job_no`  LIKE '%{$name}%' )";
        }


        if($status>-1) {
            $where              .= " AND `status` = :status";
            $params[":status"]  = $status;
        }

        return [$where, $params];
    }

    /**
     * @param $user_id
     * @param $name
     * @param $status
     * @param $job_no
     * @return array
     * @throws Exception
     */
    public function exportData($user_id = 0, $name = '', $status = 0,$job_no='')
    {
        $head   = [
            '用户ID', '导购员昵称', '工号', '注册时间', '所属门店', '状态'
        ];

        $tb = self::tbName();
        if($job_no && empty($name)) $name = $job_no;

        list($where, $params) = $this->_condition($user_id, $name, $status);

        //导出
        $db      = by::dbMaster();
        $muser   = by::users();

        $id     = 0;
        $sql  = "select `id`,`user_id` from {$tb} where `id` > :id AND {$where} order by `id` limit 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $value) {
                $user   = $muser->getOneByUid($value['user_id']);
                $main   = $muser->getUserMainInfo($value['user_id']);
                $guide  = $this->getGuideInfo($value['user_id']);

                if (empty($user)) {
                    $status = '注销';
                } else {
                    $status = UserModel::STATUS[$user['status']];
                }
                $data[] = [
                    'user_id'   => $value['user_id'],
                    'name'      => '\''.($guide['name'] ?? ''),
                    'job_no'    => $guide['job_no'] ?? '',
                    'reg_time'  => isset($main['reg_time']) ? date("Y-m-d H:i:s", $main['reg_time']) : 0,
                    'store'     => $guide['store'] ?? '',
                    'status'    => $status,
                ];
            }

        }
        return $data;
    }


    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }
}
