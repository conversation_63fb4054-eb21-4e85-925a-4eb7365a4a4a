<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 * 地址库
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use yii\db\Exception;
use app\modules\main\models\CommModel;

class AreaModel extends CommModel
{

    const EXPIRE_TIME         = 1800;

    const DOMESTIC            = 710000;   //erp地址库中国（不包含港澳台）分界点

    public $tb_fields = [
        'id','pid','name'
    ];

    public static function tbName()
    {
        return "`db_dreame`.`t_area_2`";
    }


    /**
     * @param $pid
     * @return string
     * 通过id获取数据
     */
    private function __getAreaListKey($pid): string
    {
        return AppCRedisKeys::getAreaByPid($pid);
    }

    /**
     * @param int $pid
     * @return array
     * @throws Exception
     * 根据pid获取数据
     */
    public function GetList($pid = 1)
    {
        $r_key = $this->__getAreaListKey($pid);
        $redis = by::redis();
        $aJson = $redis->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb             = self::tbName();

            $where          = " `pid` = :pid ";
            $param[':pid']  = $pid;

            if ($pid  == 0) {
                $where         .= " AND `id` < :id ";
                $param[':id']   = self::DOMESTIC;
            }

            $fields         = implode("`,`",$this->tb_fields);
            $sql            = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";

            $aData          = by::dbMaster()->createCommand($sql, $param)->queryAll();

            $redis->set($r_key, json_encode($aData), ['ex'=>self::EXPIRE_TIME]);
        }

        return $aData;
    }


    /**
     * @param int $pid
     * @return array
     * @throws Exception
     * 根据pid获取数据
     */
    public function GetRealList($pid = 1)
    {

        $tb = self::tbName();

        $where         = " `pid` = :pid ";

        $param[':pid'] = $pid;

        $where .= " AND `is_show` = 1";

        if ($pid  == 0) {
            $where         .= " AND `id` < :id ";
            $param[':id']   = self::DOMESTIC;
        }

        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";

        $aData = by::dbMaster()->createCommand($sql, $param)->queryAll();

        return $aData;
    }

}
