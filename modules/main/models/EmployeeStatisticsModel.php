<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;
use yii\db\Expression;

/**
 * 用户统计
 */
class EmployeeStatisticsModel extends CommModel
{
    public static function tbName(): string
    {
        return "`db_dreame`.`employee_statistics`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public function patch($user_id,$field,$number,$addOrsubtract,$activity_id = 1){
        $tb = $this->tableName();

        //判断一下是否存在
        $info = self::find()->where(['user_id'=>$user_id,'activity_id'=>$activity_id])->one();
        if ($info){
            $info = $info->toArray();
            $updateData = [
                $field => new Expression($field ." ". $addOrsubtract ." ". $number),
            ];
            $status = by::dbMaster()->createCommand()->update($tb,$updateData, ['id'=>$info['id']])->execute();
            if ($status === false){
                return [false,'更新失败'];
            }
        }else{
            if ($addOrsubtract == '-'){
                $number = 0;
            }
            $user_data = by::Phone()->getDataByUserId($user_id);
            $insertData = [
                'user_id'=>$user_id,
                'uid' => $user_data['uid'] ?? '',
                $field=>$number,
                'activity_id'=>$activity_id,
                'ctime' => time(),
                'utime' => time(),
            ];
            $status = by::dbMaster()->createCommand()->insert($tb,$insertData)->execute();
            if ($status === false){
                return [false,'更新失败'];
            }
        }
        return [true,'更新成功'];
    }

    public function updateData($user_id,$data){
        $tb = $this->tableName();
        by::dbMaster()->createCommand()->update($tb,$data, ['user_id'=>$user_id])->execute();
        return [true,'更新成功'];
    }

    public function getInfo($user_id,$activity_id = 1){
        $tb = $this->tableName();
        return self::find()->where(['user_id'=>$user_id,'activity_id'=>$activity_id])->asArray()->one();
    }

    /**
     * 排行榜列表
     */
    public function rankList($params)
    {
        $order = $params['order'] ?? 'recommend_num';
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        $items = self::find()->orderBy([$order => SORT_DESC])->offset(($page - 1) * $pageSize)
            ->limit($pageSize)->asArray()->all();
        // 查询用户信息
        $userIds  = array_column($items, 'user_id');
        $users = by::users()->getListByUserIds($userIds, ['user_id', 'nick', 'avatar']);
        $users = array_column($users, null, 'user_id');
        // 整合数据
        $list = [];
        foreach ($items as $item) {
            $list[] = [
                'nickname'    => $users[$item['user_id']]['nick'] ?? '',
                'value'    => $item[$order],
            ];
        }
        return ['list' => $list];
    }
}