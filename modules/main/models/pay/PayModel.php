<?php

namespace app\modules\main\models\pay;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\modules\goods\models\OmainModel;

class PayModel
{
    // 交易单缓存有效期2小时
    const TRADE_ORDER_EXPIRE = 7200;

    // 支付类型
    const PAY_TYPE = [
        'PAY_BY_WX'        => 1, // 微信JSAPI
        'PAY_BY_WX_H5'     => 2, // 微信H5
        'PAY_BY_WX_APP'    => 3, // 微信APP
        'PAY_BY_ALIPAY'    => 4, // 支付宝APP
        'PAY_BY_ALI_ZHIMA' => 6, // 芝麻
        'PAY_BY_MP_WX'     => 7, // 中台微信Native
        'PAY_BY_MP_ALIPAY' => 8, // 中台支付宝Native
    ];

    // 支付有效期：7200（2小时）、300（5分钟）
    const PAY_EXPIRE = [
        1 => 7200, // 微信JSAPI
        2 => 300,  // 微信H5
        3 => 7200, // 微信APP
        4 => 7200, // 支付宝APP
        6 => 7200, // 芝麻
        7 => 7200, // 中台微信Native
        8 => 7200, // 中台支付宝Native
        9 => 7200, // 中台京东白条Native
        10 => 7200, // 中台微信H5
        11 => 7200, // 中台支付宝H5
        12 => 7200, // 中台京东APP
        13 => 7200, // 中台京东H5
        14 => 7200, // 中台京东PC
    ];

    private static $instance = null;

    private function __construct()
    {

    }

    public static function getInstance(): PayModel
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // 关闭交易
    public function close($orderNo, $payType): array
    {
        $result = [false, '不支持关闭交易'];
        switch ($payType) {
            case OmainModel::PAY_BY_WX:     // 1 微信JSAPI
            case OmainModel::PAY_BY_WX_APP: // 3 微信APP
                $result = by::WxPay()->close($orderNo, $payType);
                break;
            case OmainModel::PAY_BY_ALIPAY:  // 4 支付宝APP
                $result = AliPayModel::getInstance()->close($orderNo);
                break;
            case OmainModel::PAY_BY_MP_WX:  // 7、8、9 中台（微信、支付宝支付、京东白条）
            case OmainModel::PAY_BY_MP_ALIPAY:
            case OmainModel::PAY_JD_BAITIAO:
            case OmainModel::PAY_JD_BAITIAO_APP:
            case OmainModel::PAY_JD_BAITIAO_H5:
            case OmainModel::PAY_JD_BAITIAO_PC:
            case OmainModel::PAY_BY_MP_WEB_WX_H5:
            case OmainModel::PAY_BY_MP_WEB_ALIPAY_H5:
                $result = MpPayModel::getInstance()->close($orderNo);
                break;
            default:
                break;
        }
        return $result;
    }

    // 获取交易单（从缓存获取）

    public function getTradeOrder($orderNo, $payType,$paymentPlan='NO_INST'): array
    {
        $key = $this->__getTradeOrderKey($orderNo, $payType . $paymentPlan);
        $res = by::redis()->get($key);
        if ($res === false) {
            // 从数据库中获取交易单数据，可能多个，取第一个
            $items = $this->getEffectiveOrderPayData($orderNo, $payType,$paymentPlan);
            $data  = $items[0] ?? [];
            if ($data) { // TODO 有数据（兼容之前只存缓存的逻辑，之后不存缓存直接从数据库中查询），返回
                return $data;
            }
            return [];
        }

        return json_decode($res, true);
    }


    public function saveTradeOrder($orderNo, $payType, $prepayId, $h5Url, $ptime,$paymentPlan='NO_INST'): bool
    {
        $key = $this->__getTradeOrderKey($orderNo, $payType);

        // 存在不创建
        $exists = by::redis()->exists($key);
        if ($exists) {
            return true;
        }

        // TODO 查询数据库（不再查询缓存）
        $items = $this->getEffectiveOrderPayData($orderNo, $payType,$paymentPlan);
        $data  = $items[0] ?? [];
        if ($data) {
            return true;
        }

        // 存储数据
        $params = [
            'order_no'    => $orderNo,
            'pay_type'    => $payType,
            'prepay_id'   => ($payType == self::PAY_TYPE['PAY_BY_WX_H5']) ? $h5Url : $prepayId,
            'expire_time' => $this->getExpireTime($payType, $ptime),
            'payment_plan' => $paymentPlan,
        ];
        byNew::orderPayHistoryModel()->saveOrderPayData($orderNo, $params);

        return true;
    }

    /**
     * 获取有效的订单支付流水
     * @param $orderNo
     * @param $payType
     * @return array
     */
    private function getEffectiveOrderPayData($orderNo, $payType,$paymentPlan): array
    {
        $data = [];
        // 当前时间戳
        $currentTimestamp = time();
        $items            = byNew::orderPayHistoryModel()->getOrderPayData($orderNo, $payType,$paymentPlan);
        foreach ($items as $item) {
            if ($item['expire_time'] <= $currentTimestamp) {
                continue;
            }
            $data[] = [
                'order_no'  => $item['order_no'],
                'pay_type'  => $item['pay_type'],
                'prepay_id' => $item['prepay_id'],
                'h5_url'    => ($item['pay_type'] == self::PAY_TYPE['PAY_BY_WX_H5']) ? $item['prepay_id'] : '',
                'ptime'     => $this->getPTime($item['pay_type'], $item['expire_time']),
            ];
        }

        return $data;
    }

    // 校验单号的缓存key
    private function __getTradeOrderKey($orderNo, $payType): string
    {
        return AppCRedisKeys::getTradeOrder($orderNo, $payType);
    }

    /**
     * 根据过期时间，获取ptime
     * @param $pay_type
     * @param $expire_time
     * @return int
     */
    private function getPTime($pay_type, $expire_time): int
    {
        return $expire_time - self::PAY_EXPIRE[$pay_type];
    }

    /**
     * 根据ptime，获取过期时间
     * @param $pay_type
     * @param $ptime
     * @return int
     */
    private function getExpireTime($pay_type, $ptime): int
    {
        return $ptime + self::PAY_EXPIRE[$pay_type];
    }
}