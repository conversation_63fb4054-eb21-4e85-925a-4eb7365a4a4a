<?php

namespace app\modules\main\models\pay;

use app\components\MpSign;
use app\models\CUtil;
use app\modules\main\models\CommModel;

/**
 * 中台（Middle Platform）支付
 */
class MpPayModel
{
    private static $instance = null;

    // 项目code码
    CONST ProjectCode='DREAME_MALL';
    // 加密盐
    CONST ENCODE_SALT = 'a@#*!*&^$@#$!!@!!KSJ@@';

    // 支付平台
    const PAY_PLATFORM = 'DREAME_MALL_PC';

    // 支付标题
    const PAY_SUBJECT = '追觅会员官方商城';

    // 支付渠道
    const PAY_CHANNEL = [
            'WX'     => 1,
            'ALIPAY' => 2,
            'JD_BAITIAO'=> 3,
    ];

    // 支付方式
    const PAY_METHOD = [
            'NATIVE' => 1,
            'APP'    => 2,
            'FACE'   => 3,
            'PC'     => 4,
            'H5'     => 5,
    ];

    // host
    private $host;

    // 支付回调URL
    private $pay_callback_url;

    // 退款回调URL
    private $refund_callback_url;

    // 签名
    private $sign_key;

    private function __construct()
    {
        // 获取host
        $this->host = CUtil::getConfig('host', 'config', \Yii::$app->id)['mp_pay_host'] ?? '';

        $this->pay_callback_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['private_host']) . '/comm/mp-pay-notify';
        $this->refund_callback_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['private_host']) . '/comm/mp-refund-notify';

        $config = CUtil::getConfig('mp-pay', 'common', MAIN_MODULE);
        $this->sign_key = $config['sign_key'];
    }

    public static function getInstance(): MpPayModel
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    // 创建支付订单
    public function pay($order_no, $pay_amount, array $order_params,$ret_all=false): array
    {

        $order_params['order_no'] = $order_no;
        $lock_plan                = $order_params['lock_plan'] ?? '';        // 锁定的分期计划
        $mini_program_redirect    = $order_params['mini_program_redirect'] ?? 0;// 小程序支付
        $goods_detail             = self::jsonParams($order_params['goods_detail'] ?? []); // 白条免息分期SKU
        $receiver_info            = self::jsonParams($order_params['receiver_info'] ?? []); // 收货信息
        $risk_info                = self::jsonParams($order_params['risk_info'] ?? []); // 风控信息
        $device_type              = $order_params['device_type'] ?? ''; // 设备类型:ios,Android, Wap

        $params = [
                "amount"                => floatval($pay_amount),        // 金额
                "order_id"              => $order_no,                    // 订单号
                "pay_method"            => $order_params['pay_method'],  // 支付方式
                "pay_channel"           => $order_params['pay_channel'], // 支付渠道
                "uid"                   => $order_params['uid'],         // 用户ID
                'lock_plan'             => (string) $lock_plan, // 锁定的分期计划
                "description"           => self::PAY_SUBJECT,
                "goods_detail"          => $goods_detail,
                "receiver_info"         => $receiver_info,
                "risk_info"             => $risk_info,
                "passback"              => $this->getPassback($order_params),
                "mini_program_redirect" => $mini_program_redirect,
                "order_async_url"       => $this->pay_callback_url,
                "platform_no"           => self::PAY_PLATFORM,
                "device_type "          => $device_type,
        ];

        list($status, $res) = $this->request('/internal-api/v1/pay/create', $params);

        if (!$status) {
            return [$status, $res];
        }

        if ($ret_all){
            $response=json_encode($res,true);
        }else{
            $response= $res['response'] ?? '';
        }

        return [true, [
                'pay_url'     => $response,
                'ptime'      => $res['expire_time'] + 8 * 3600 - 2 * 3600, // 提前2小时，有效期2小时
        ]];
    }

    private function jsonParams($params)
    {
        $json=json_encode($params, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        if (json_last_error() == JSON_ERROR_NONE) {
            return $json;
        }else{
            return '';
        }
    }

    // 退款
    public function refund($trade_no, $refund_no, $refund_amount, array $order_params): array
    {
        $params = [
                "amount"               => floatval($refund_amount),  // 金额
                "bill_id"              => $trade_no,                 // 账单号
                "order_async_url"      => $this->refund_callback_url,// 退款回调地址
                "reason"               => '追觅商城退款',
                "refund_goods_details" => json_encode([], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES),
                "passback"             => $this->getPassback($order_params),
                "refund_number"        => $refund_no,
                "uid"                  => $order_params['uid'],
        ];

        list($status, $res) = $this->request('/internal-api/v1/refund/create', $params);

        if (!$status) {
            return [$status, $res];
        }

        // 中台的退款状态：0：退款处理中 1：退款成功 2：退款失败 3：退款关闭
        if (($res['status'] ?? 0) == 0 || ($res['status'] ?? 0) == 1) { // 退款成功
            return [true, '退款成功'];
        }

        return [false, $res['err']];
    }

    // 查询
    public function query($order_id)
    {
        $params = [
                "order_id" => $order_id
        ];

        list($status, $res) = $this->request('/internal-api/v1/query/orderId', $params);

        if (!$status) {
            return [$status, $res];
        }

        return [true, ['status' => $res['pay'] ?? []]];
    }

    // 关闭支付订单
    public function close($order_id)
    {
        $params = [
                "order_id" => $order_id
        ];

        list($status, $res) = $this->request('/internal-api/v1/close/create', $params);

        if (!$status) {
            return [$status, $res];
        }

        return [true, ['status' => $res['status'] ?? '']]; // 交易状态 0: 关闭成功 1: 关闭失败
    }

    // 回调验证
    public function verifyNotify(array $params): bool
    {
        if (empty($params['passback'])) {
            return false;
        }
        // 解下回调参数
        parse_str(urldecode($params['passback']), $order_params);
        if (!isset($order_params['user_id'], $order_params['order_no'], $order_params['order_type'], $order_params['pay_type'])) {
            return false;
        }

        if (isset($params['refund_amount']) && $params['refund_amount'] == 0) {
            return false;
        }

        return true;
    }

    // 请求支付平台
    public function request(string $path, array $params, $is_sign = true): array
    {
        try {
            // 请求地址
            $url = $this->host . '/dreame-pay' . $path;

            // 请求头，加上时间戳和签名
            if ($is_sign) {
                $timestamp = intval(microtime(true) * 1000);
                $sign      = MpSign::sign($params, $timestamp, $this->sign_key);
                $header    = [
                        "Content-Type: application/json",
                        "Tenant-Id: 000000",
                        "dreame-api-timestamp: {$timestamp}",
                        "dreame-api-sign: {$sign}",
                        "Project:".$this::ProjectCode,
                ];
            } else {
                $header = [
                        "Content-Type: application/json",
                        "Tenant-Id: 000000",
                ];
            }

            // 发送请求
            $request=json_encode($params, 320);
            $response = CUtil::curl_post($url, $request, $header, 10, true);

            // 请求结果
            $result = json_decode($response, true);
            if (!YII_ENV_PROD){
                // 记录请求日志
                CUtil::debug('，请求地址：' . $url . '，请求参数：' . json_encode($params, 320).'返回参数：' . $response , 'pay_middle_platform.request');
            }

            if (!isset($result['code']) || $result['code'] != 0) {
                // 请求支付中台异常
                CUtil::debug('请求支付中台异常：' . $response . '，请求地址：' . $url . '，请求参数：' . json_encode($params, 320), 'err.pay_middle_platform.request（1）');
                return [false, '请求支付中台异常'];
            }

            return [true, $result['data']];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                    'message' => $e->getMessage(),
                    'file'    => $e->getFile(),
                    'line'    => $e->getLine(),
            ];
            CUtil::debug('请求支付中台异常：' . json_encode($msg, 320) . '，请求地址：' . $url . '，请求参数：' . json_encode($params, 320), 'err.pay_middle_platform.request（2）');
            return [false, '请求支付中台异常'];
        }
    }

    /**
     * 校验回调参数
     * @param array $passback
     * @param string $api
     * @return bool
     */
    public function checkPassback(array $passback, string $api = 'p_1712037068'): bool
    {
        // 参数校验
        if (!isset($passback['user_id'], $passback['pay_type'], $passback['order_no'], $passback['order_type'], $passback['sign'])) {
            return false;
        }

        // 参数
        $params = [
                'user_id'    => $passback['user_id'],
                'pay_type'   => $passback['pay_type'],
                'order_no'   => $passback['order_no'],
                'order_type' => $passback['order_type'],
        ];

        // 获取签名
        $api_keys = CUtil::getConfig('apiKeys', 'common', MAIN_MODULE);
        $sign     = CommModel::getSign($params, $api_keys[$api], self::ENCODE_SALT);

        return $sign == $passback['sign'];
    }

    /**
     * 获取回调参数
     * @param array $order_params
     * @param string $api
     * @return string
     */
    public function getPassback(array $order_params, string $api = 'p_1712037068'): string
    {
        // 参数
        $params = [
                'user_id'    => $order_params['user_id'],
                'order_no'   => $order_params['order_no'],
                'order_type' => $order_params['order_type'],
                'pay_type'   => $order_params['pay_type'],
        ];

        // 获取签名
        $api_keys       = CUtil::getConfig('apiKeys', 'common', MAIN_MODULE);
        $params['sign'] = CommModel::getSign($params, $api_keys[$api], self::ENCODE_SALT);

        return urlencode("user_id={$params['user_id']}&order_no={$params['order_no']}&order_type={$params['order_type']}&pay_type={$params['pay_type']}&sign={$params['sign']}");
    }

    /**
     * 分期手续费
     */
    public function getInstFee($pay_channel, $amount) : array
    {
        list($status, $res) = $this->request('/internal-api/v1/installment/calculateInterest', [
                'pay_channel' => $pay_channel, //支付渠道 1：微信，2：支付宝 3：京东
                'principal'   => floatval($amount),  //本金
        ]);

        if (!$status) {
            return [];
        }

        return $res['interest_periods'] ?? [];

    }
}