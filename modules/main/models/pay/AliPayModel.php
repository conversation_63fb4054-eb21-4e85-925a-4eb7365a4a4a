<?php

namespace app\modules\main\models\pay;

use Alipay\EasySDK\Kernel\Config;
use Alipay\EasySDK\Kernel\Factory;
use Alipay\EasySDK\Kernel\Util\ResponseChecker;
use app\models\CUtil;

/**
 * 支付宝支付
 */
class AliPayModel
{
    private static $instance = null;

    // 支付宝支付标题
    const PAY_SUBJECT = '追觅官方商城';

    private function __construct()
    {
        Factory::setOptions($this->getOptions());
    }

    public static function getInstance(): AliPayModel
    {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    private function getOptions(): Config
    {
        $options = new Config();

        // 环境配置
        $envConfig = $this->isSandbox() ? $this->getSandboxConfig() : CUtil::getConfig('ALIPAY', 'common', MAIN_MODULE);

        foreach ($envConfig as $key => $value) {
            $options->$key = $value;
        }

        return $options;
    }

    private function isSandbox(): bool
    {
        return false; // TODO 测试开发时用
    }

    private function getSandboxConfig(): array
    {
        return [
            'protocol'           => 'https',
            'gatewayHost'        => 'openapi-sandbox.dl.alipaydev.com',
            'signType'           => 'RSA2',
            'appId'              => '9021000132696508',
            // 应用私钥
            'merchantPrivateKey' => 'MIIEpAIBAAKCAQEA0b530uyWA8LsNGv6yyuIzXckOfTz1BC9YN1X0SCVl0B6KT8mi040r1vMpxo+inxFaz/a4I0+X1xObrigSqBjmuCVi+BMNf+aWMMbc74RqdtWQXbdbOQvuUIYlqVs2gSmJCS7La67HvD1mtSS+1Ha7hp44iRkPQyz3rJ+F/ByNVba8EfWr7plJmyG5L+vEpYr0NZ/8csrYQdAI7d0ZfSZJ8KKGWig3GvmbFRyjSmxxtRFn1VTRytjWwioY7gA0OabyrvnR3OvqM+bzvbNo4lHTTbzZ2p2Zb5yAwMYLuz/+BQo89hGPTDRMEuDRvbSmhYPnpOV8vqeqBwBg3Kaasv6WQIDAQABAoIBAQDNBtHcphYSgTUiHTdf6SNmLYOE//RlJSPqDxQrwh1YZ9fCgA+udvk7PZI0+ouOFeJJ73aGsKr6zqGAzAssR9J17/lfbRcyZbQ79iTcpDxh/J05ivbx3bDk+D5O7FAWYhsCE8HA6x/RJBwBiLBQ6XgH7mqiLI6rusIM/BAUWJ9bkM+UobtB164GlKgzyoj2aS8lLRlxrwI5Qysfm4UvGMWzQlceGwkA/10/5wqGXZbUNmP6SrhzWNMRSUmVgtDM6d5tDVmCTFBGmv5rq0RHgTLrAWh74WjTFT+8UTwMXL7XDdMlnoAuto8/cIxALdEYF5pQuKC/pMl1NFcQtRoyMgJNAoGBAP6auuldlK6vwML0RWCiYVoI10HMHh9F+c7wb/ex4kkCiCumgkHoR7DQx3ZVc6g/eff70AnQdn9hY2U5+3uOEVSesZhZC0cwh7l8w2tBpeLtoa9jRuytqJgVD0L1zIxiLshB1NeV0yEXu/NTWIELtZSNf+AP5wiw/Fgs2zdROPZnAoGBANLkycqkBudSii/zhRKiJgkpSc2J9Jfd9IBObAUeYARzqaJHv++x7+hsKw9WUQYc5Yuijw3sCsCZ/4/eSwbrJAj+WaLQagEUd0oPcZVSNrlv02YIlTcaOXYBbvRJQ4IYlrTTp1kBmBxLHq29SaMIRitDk1S7ywRPCbbI6gWrsZE/AoGAGpeUoB0rE1e9/V7ABEjRfDLiMLgeCUwovEl2mFtW9CM0j8KTZkjZfAlCXASu0MrYdWMypmPEjKdiV0dIEe6Tts/gYrYiLGxQMLpsOr6fkeUZ9xSaasv0iPXiIy9DDwKOOlJN5bp7U8CD2/U/kDLl4z8oNQHUMp8xrA/7UXrFRU8CgYEAwgqYUMcTQwwC8ZPSwQ5IiO1gZqmkT4d9TADcCumj1vTFA/eYdmb2IphEFlyJOGKmIhB0J48jZYtMRYd26Oq5wTymJNQ2dL/r8ky7aXS3vW9SxvLmuEh8zkMLX+F5J96P69lChPaeionjtGARvNwEFBKdIL2ZUmGWwoOiL42GV+kCgYAbIADHizn01boufukIe+Ajt+y1nJ3Rcd+235vmlLdH23uKgucQhF8LTMyd1nPmiD8URC5a+EJF1aJ6aqmpRw0VJcmbp8TvPYegX1cc1pMxYEye+Lo6wpffW8aB6bCXyQQA0mPY+CYalaOUp2adWDXC4IT9vTTuyrMEkKs9G3gZKg==',
            // 支付宝公钥
            'alipayPublicKey'    => 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtFvDkbIB/s6o0IlUD9mN87I0UYQBWC1yq8EC8Hc6/A6dUL/XIdXLW6GQn/MgFuTNziW9SZq969KbjK+Gyr12sSBYgA3M7qqgS7h62S5EfzHPfZGWzcuRBYIsaPSOGdBCpgu24pOr5+AZzs7NYqn4x9+MFOpJBZsbmeYc8DoQR3wmWsSs+xmTQo7XKux0FAqHtjpTkRlW6bdSK/H839HOtfUP4oVo3zRcCw45Arx6tFUe1aGbBSaDLcrv45w23k5hocEYTNhQUkNLt++iJjP5Y7yOAhavMWObJ1P3JjVDm+FIr4Vrc2/t4DL+0dfu43RNvVEWn/5e4DddEmrP/1BsjQIDAQAB',
            // 回调地址
            'notifyUrl'          => 'https://test2-wxmall.dreame.tech/comm/alipay-notify',
        ];
    }

    // 测试
    public function test($user_id, $order_no, $amount, $source = 1): array
    {
        return $this->pay($order_no, $amount, [
            'user_id'     => $user_id,
            'order_type'  => $source,
            'time_expire' => date('Y-m-d H:i:s', time() + 3600),
        ]);
    }

    // 创建支付订单
    public function pay($order_no, $pay_amount, array $params): array
    {
        $bizParams = $this->getOptionals($params['user_id'], $order_no, $params['order_type'], $params['time_expire']);

        try {
            $result = Factory::payment()
                ->app()
                ->batchOptional($bizParams)
                ->pay(self::PAY_SUBJECT, $order_no, $pay_amount);

            return $this->handleResponse($result, 'pay', $order_no, '', $pay_amount);
        } catch (\Exception $e) {
            return $this->handleException($e, 'pay', $order_no, '', $pay_amount);
        }
    }

    // 退款
    public function refund($order_no, $refund_no, $refund_amount): array
    {
        try {
            $result = Factory::payment()
                ->common()
                ->batchOptional(['out_request_no' => $refund_no]) // 退款单号
                ->refund($order_no, $refund_amount);
            return $this->handleResponse($result, 'refund', $order_no, $refund_no, $refund_amount);
        } catch (\Exception $e) {
            return $this->handleException($e, 'refund', $order_no, $refund_no, $refund_amount);
        }
    }

    // 查询
    public function query($order_no, $is_log = true): array
    {
        try {
            $result = Factory::payment()
                ->common()
                ->query($order_no);
            return $this->handleResponse($result, 'query', $order_no, '', 0, $is_log);
        } catch (\Exception $e) {
            return $this->handleException($e, 'query', $order_no, '', 0);
        }
    }

    // 关闭支付订单
    public function close($order_no): array
    {
        try {
            $result = Factory::payment()
                ->common()
                ->close($order_no);
            return $this->handleResponse($result, 'close', $order_no, '', 0, false);
        } catch (\Exception $e) {
            return $this->handleException($e, 'close', $order_no, '', 0);
        }
    }

    // 回调验证
    public function verifyNotify($parameters): bool
    {
        return Factory::payment()->common()->verifyNotify($parameters);
    }

    private function getOptionals($user_id, $order_no, $order_type, $time_expire = null): array
    {
        return [
            'passback_params' => urlencode("user_id={$user_id}&order_no={$order_no}&order_type={$order_type}"),
            'time_expire'     => $time_expire,
        ];
    }

    // 返回结果处理
    private function handleResponse($result, $action, $order_no, $refund_no = '', $amount = 0, $is_log = true): array
    {
        if ((new ResponseChecker())->success($result)) {
            $body = ($action == 'pay') ? $result->body : $result->httpBody;
            return [true, $body];
        }

        // 记录错误日志
        $params = [
            'order_no'  => $order_no,
            'refund_no' => $refund_no,
            'amount'    => $amount,
        ];

        $body = ($action == 'pay') ? $result->body : $result->httpBody;
        // $is_log && CUtil::debug('Request Parameters: ' . json_encode($params) . ' Response: ' . $body, "err.alipay_{$action}_fail");
        $is_log && CUtil::setLogMsg(
            "err.alipay_{$action}_fail",
            $params,
            $body,
            [],
            '',
            ''
        );

        return [false, "{$action} request failed. please try again later."];
    }

    // 异常结果处理
    private function handleException(\Exception $e, $action, $order_no, $refund_no = '', $amount = 0): array
    {
        // 记录错误日志
        $params = [
            'order_no'  => $order_no,
            'refund_no' => $refund_no,
            'amount'    => $amount,
        ];

        CUtil::debug('Request Parameters: ' . json_encode($params) . ' Exception Message: ' . $e->getMessage(), "err.alipay_{$action}_exception");
        return [false, "{$action} request exception. please try again later."];
    }

}