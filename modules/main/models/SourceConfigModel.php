<?php


namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

class SourceConfigModel extends CommModel
{

    public static function tableName(): string
    {
        return "`db_dreame`.`t_source_config`";
    }

    public $tb_fields = [
        'id', 'source_code', 'param', 'remark', 'is_del', 'ctime', 'utime', 'dtime'
    ];

    const IS_DEL = [
        'no'  => 0, //未删除
        'yes' => 1, //已删除
    ];

    private function getSourceListKey(): string
    {
        return AppCRedisKeys::getSourceListKey();
    }

    public function __delSourceListKey()
    {
        $redis    = by::redis();
        $redisKey = $this->getSourceListKey();
        $redis->del($redisKey);
    }

    private function getParamListKey(): string
    {
        return AppCRedisKeys::getParamListKey();
    }


    public function __delParamListKey()
    {
        $redis    = by::redis();
        $redisKey = $this->getParamListKey();
        $redis->del($redisKey);
    }

    public function saveLog($id, $input)
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        if (!$id) {
            $db->createCommand()->insert($tb, $input)->execute();
            $id = $db->getLastInsertID();
        } else {
            $db->createCommand()->update($tb, $input, ['id' => $id])->execute();
        }
        $this->__delSourceListKey();
        $this->__delParamListKey();
        return $id;
    }


    public function deleteSource($id): bool
    {
        $db     = by::dbMaster();
        $tb     = self::tableName();
        $result = $db->createCommand()->update($tb, ['is_del' => 1, 'dtime' => intval(START_TIME)], ['id' => $id])->execute();
        if (!$result) {
            return false;
        }
        $this->__delSourceListKey();
        $this->__delParamListKey();
        return true;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 来源列表
     */
    public function getList(): array
    {
        $redis    = by::redis();
        $redisKey = $this->getSourceListKey();
        $aJson    = $redis->get($redisKey);
        $aData    = (array)json_decode($aJson, true);
        if (!$aData) {
            $db     = by::dbMaster();
            $tb     = self::tableName();
            $fields = implode("`,`", $this->tb_fields);
            $where  = " `is_del` = " . self::IS_DEL['no'];
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";
            $aData  = $db->createCommand($sql)->queryAll();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redisKey, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }
        return $aData;
    }

    /**
     * @throws RedisException
     * 通过source_code获取param
     */
    public function getParamByCode($sourceCode): array
    {
        $redis    = by::redis();
        $redisKey = $this->getParamListKey();
        $sub_key  = CUtil::getAllParams(__FUNCTION__, $sourceCode);
        $aJson    = $redis->hGet($redisKey, $sub_key);
        $aData    = (array)json_decode($aJson, true);

        if (!$aData) {
            $aData = self::findOne(['source_code' => $sourceCode]);
            if ($aData !== null) {
                $aData = $aData->toArray();
            } else {
                $aData = [];
            }
            $redis->hSet($redisKey, $sub_key, json_encode($aData));
            CUtil::ResetExpire($redisKey, 600);
        }
        return $aData;
    }

    /**
     * 获取来源列表
     * @return array
     */
    public function getSourceConfigMap(): array
    {
        $query = self::find();
        $res = $query->asArray()->all();
        if (empty($res)) {
            return [];
        }

        return array_column($res, null, 'source_code');
    }


}
