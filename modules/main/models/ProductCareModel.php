<?php

namespace app\modules\main\models;

use app\models\by;
/**
 * 用户绑定关系表
 */
class ProductCareModel extends CommModel
{
    const BIND_STATUS = [
        'BIND'   => 1, // 绑定
        'UNBIND' => 2  // 解绑
    ];

    // 是否新用户
    const IS_NEW_USER = [
        'NO'  => 0,
        'YES' => 1
    ];

    public static function tbName(): string
    {
        return "`db_dreame`.`t_product_care`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 获取绑定列表
    public function getBindList(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->select(['*'])
            ->where($condition)
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
    }

    // 获取数量
    public function getBindCount(array $params = []): int
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->where($condition)
            ->count();
    }

    // 获取搜索条件
    private function getSearchCondition(array $params): array
    {
        $conditions = ['and'];

        if (!empty($params['uid'])) {
            $conditions[] = ['uid' => $params['uid']];
        }

        if (!empty($params['bound_uid'])) {
            $conditions[] = ['bound_uid' => $params['bound_uid']];
        }

        if (!empty($params['bind_status'])) {
            $conditions[] = ['bind_status' => $params['bind_status']];
        }

        if (isset($params['is_new_user']) && $params['is_new_user'] !== '') {
            $conditions[] = ['is_new_user' => $params['is_new_user']];
        }

        // 查询订单创建时间
        if (!empty($params['bind_start_time']) && !empty($params['bind_end_time'])) {
            $conditions[] = ['between', 'bind_time', $params['bind_start_time'], $params['bind_end_time']];
        }

        // 移除数组中只有 'and' 的情况，即没有其他条件时
        if (count($conditions) === 1) {
            return [];
        }

        return $conditions;
    }

    public function getOneByProductId($productId){
        return self::find()->where(['product_id'=>$productId])->asArray()->one();
    }

    public function saveData($pid,array $data){
        if(empty($pid)){
            return false;
        }
        $db   = by::dbMaster();
        $tb   = self::tbName();
        // 看看是否有数据
        $res = self::find()->where(['product_id'=>$pid])->asArray()->one();
        if(!empty($res)){
            $row = $db->createCommand()->update($tb, $data, "`id`=:id", [":id" => $res['id']])->execute();
            if (empty($row)) {
                return false;
            }
        }else{
            $data['product_id'] = $pid; // 添加产品ID product_id
            $data['ctime']      = time(); // 添加创建时间 ctime
            $row = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($row)) {
                return false;
            }
        }
        return true;
    }

}