<?php


namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\models\by;
use yii\db\Exception;

/**
 * 新人大礼包
 */
class AcType1Model extends CommModel
{

    const HAS_POINT_OR_COUPON = [
        'NO'  => 0,
        'YES' => 1
    ];

    /**
     * @return string
     */
    public static function tableName(): string
    {
        return "`db_dreame`.`t_ac_type1`";
    }

    public $tb_fields = [
        'id', 'ac_id', 'has_point', 'point_value', 'point_multiplier', 'multiplier_start_time', 'multiplier_end_time', 'has_coupon', 'ctime', 'utime'
    ];


    private function __getInfoAcTypeKey($ac_id): string
    {
        return AppCRedisKeys::getInfoAcType1ByAcId($ac_id);
    }

    public function delCache($ac_id)
    {
        $r_key1 = $this->__getInfoAcTypeKey($ac_id);
        by::redis('core')->del($r_key1);
    }


    // 获取信息
    public function getInfoByAcId($ac_id)
    {
        $ac_id = (int)$ac_id;
        if ($ac_id == 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getInfoAcTypeKey($ac_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            // 使用 Active Record 查询数据
            $aData = self::find()
                ->select($this->tb_fields)
                ->where(['ac_id' => $ac_id])
                ->asArray()
                ->one();

            $aData = empty($aData) ? [] : $aData;

            // 缓存数据到 Redis
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }

    /**
     * 更新或插入数据
     * @param $ac_id
     * @param $data
     * @throws Exception
     */
    public function saveLog($ac_id, $data)
    {
        $tb = self::tableName();
        // 防止新增数据ac_id有值 但当前表中没有数据 不能做编辑操作 只能做新增操作
        $record = self::find()->where(['ac_id' => $ac_id])->one();
        if ($ac_id && $record) { // 更新
            by::dbMaster()->createCommand()->update($tb, $data, ['ac_id' => $ac_id])->execute();
        } else {      // 添加
            $data['ctime'] = intval(START_TIME);
            by::dbMaster()->createCommand()->insert($tb, $data)->execute();
        }
        // 删除缓存
        $this->delCache($ac_id);
    }


}
