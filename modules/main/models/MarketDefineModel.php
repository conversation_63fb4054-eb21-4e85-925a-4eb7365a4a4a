<?php


namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\Exception;

class MarketDefineModel extends CommModel
{
    public $tb_fields = [
        'id', 'name','use_rule_note','market_id', 'define_type', 'sort', 'utime', 'ctime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_market_define`";
    }


    /**
     *
     * @return string
     * 获取自定义营销资源列表
     */
    private function __getMarketDefineList(): string
    {
        return AppCRedisKeys::getMarketDefineList();
    }


    /**
     * @return string
     * 获取自定义营销资源详情
     */
    private function __getMarketDefineById($id): string
    {
        return AppCRedisKeys::getMarketDefineById($id);
    }


    /**
     * @return string
     * 根据营销资源ID和自定义类型获取营销资源
     */
    private function __getMarketDefineByMarketId($marketId,$defineType): string
    {
        return AppCRedisKeys::getMarketDefineByMarketId($marketId,$defineType);
    }

    /**
     * @param $id
     * @return int|\Redis
     */
    private function __delIdCache($id)
    {
        $r_key = $this->__getmarketDefineById($id);
        return by::redis('core')->del($r_key);
    }


    /**
     * @return int|\Redis
     */
    private function __delListCache()
    {
        $r_key = $this->__getMarketDefineList();
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $marketId
     * @param $defineType
     * @return int|\Redis
     */
    private function __delMarketDefineByMarketIdCache($marketId,$defineType)
    {
        $r_key = $this->__getMarketDefineByMarketId($marketId,$defineType);
        return by::redis('core')->del($r_key);
    }



    /**
     * @param $id
     * @param $cache
     * @return array|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据id获取数据
     */
    public function GetOneById($id, $cache = true)
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis = by::redis('core');
        $r_key = $this->__getmarketDefineById($id);

        $aJson = $cache ? $redis->get($r_key) : false;
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = $this::tbName();
            $fields = implode(",", $this->tb_fields);
            $sql    = "SELECT {$fields} FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }


        //获取优惠券详情
        $marketInfo = [];
        if (isset($aData['market_id']) && $aData['market_id']) {
            $marketInfo = by::marketConfig()->getOneById($aData['market_id']);
        }
        $aData['market_info'] = $marketInfo;

        return $aData;
    }


    /**
     * @param $marketId
     * @param $defineType
     * @param $cache
     * @return array
     * @throws Exception
     * 根据MarketId查询
     */
    public function GetOneByMarketId($marketId,$defineType,$cache = true): array
    {
        if (empty($marketId)||empty($defineType)) {
            return [];
        }

        $redis      = by::redis('core');
        $r_key      = $this->__getMarketDefineByMarketId($marketId,$defineType);

        $aJson      = $cache ? $redis->get($r_key) : false;
        $aData      = (array)json_decode($aJson, true);

        if ($aJson  === false) {
            $tb      = $this::tbName();
            $sql     = "SELECT `id`,`market_id`,`define_type` FROM  {$tb} WHERE `market_id`=:market_id AND `define_type`=:define_type AND `is_del`=0 LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':market_id' => $marketId,':define_type'=>$defineType])->queryOne();
            $aData   = $aData ?: [];
            $cache && $redis->set($r_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 1800]);
        }

        if (empty($aData)) {
            return [];
        }

        return $this->GetOneById($aData['id'], false, $cache);
    }


    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 优惠券修改
     */
    public function SaveDefine(array $aData): array
    {
        //校验主表参数
        $id          = CUtil::uint($aData['id'] ?? 0);
        $marketId    = $aData['market_id'] ?? 0;
        $sort        = $aData['sort'] ?? 0;
        $defineType  = $aData['define_type'] ?? 0;
        $useRuleNote = $aData['use_rule_note'] ?? '';
        $name        = $aData['name'] ?? "";

        $marketId    = CUtil::uint($marketId);
        $sort        = CUtil::uint($sort);
        $defineType  = CUtil::uint($defineType);
        $name        = trim($name);
        $useRuleNote = trim($useRuleNote);

        if (empty($marketId) || empty($defineType)) {
            return [false, "创建自定义优惠券-缺少对应的参数"];
        }

        if (empty($useRuleNote)){
            return [false, "优惠券规则必填"];
        }

        if (strlen($useRuleNote)>1500){
            return [false, "优惠券规则必须小于1500字"];
        }

        $aLog = $this->GetOneByMarketId($marketId, $defineType);
        if(($id && intval($aLog['id'])!==$id)||(empty($id) && $aLog)){
            return [false, "自定义优惠券{$marketId}-{$defineType}已经存在"];
        }

        //优惠券校验
        $marketInfo = by::marketConfig()->getOneById($marketId);
        if(empty($marketInfo)){
            return [false, "优惠券不存在，选择不正确"];
        }
        $stockNum = $marketInfo['stock_num'] ?? 0;
        $status = $marketInfo['status'] ?? 0;
        if ($stockNum <= 0) {
            return [false, "优惠券库存不足，选择不正确"];
        }
        if ($status != 1) {
            return [false, "优惠券审批不通过，选择不正确"];
        }

        $save = [
            'name'          => $name,
            'market_id'     => $marketId,
            'define_type'   => $defineType,
            'use_rule_note' => $useRuleNote,
            'sort'          => $sort,
            'utime'         => intval(START_TIME),
        ];

        $db    = by::dbMaster();
        $tb    = $this->tbName();
        $trans = $db->beginTransaction();

        try {
            if ($id) {
                $db->createCommand()->update($tb, $save, "`id`=:id", [":id" => $id])->execute();
                $this->__delIdCache($id); //todo 清空详情缓存
            } else {
                $save['ctime'] = intval(START_TIME);
                $db->createCommand()->insert($tb, $save)->execute();
                $id = $db->getLastInsertID();
            }

            $trans->commit();
            $this->__delListCache();
            $this->__delIdCache($id);
            $this->__delMarketDefineByMarketIdCache($marketId,$defineType);


        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.market.define');
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.market.define');
            return [false, '操作失败'];
        }

        return [true, $id];
    }


    /**
     * @throws Exception
     */
    public function GetList($input, $page = 1, $page_size = 50): array
    {
        $r_key = $this->__getMarketDefineList();

        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, json_encode($input));

        $aJson = by::redis('core')->hGet($r_key, $sub_key);
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `sort` DESC,`utime` DESC,`id` DESC ";

            if ($page_size) {
                $sql .= " LIMIT {$offset},{$page_size}";
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }


    public function GetListCount($input)
    {
        $r_key   = $this->__getMarketDefineList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, json_encode($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return intval($count);
    }

    private function __getCondition($input): array
    {
        //字段解析
        $name       = trim($input['name'] ?? '');
        $marketId   = intval($input['market_id'] ?? 0);
        $defineType = intval($input['define_type'] ?? 0);

        //初始化查询
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;
        if (!empty($name)) {
            $where          .= " AND `name` LIKE :name";
            $params[":name"] = "%{$name}%";
        }


        if (!empty($marketId)) {
            $where          .= " AND `market_id` = :market_id";
            $params[":market_id"] = $marketId;
        }


        if (!empty($defineType)) {
            $where          .= " AND `define_type` = :define_type";
            $params[":define_type"] = $defineType;
        }

        return [$where, $params];
    }


    public function Del($id)
    {
        //查看该ID是否存在
        $defineInfo = $this->GetOneById($id);
        if (empty($defineInfo)) {
            return [false, "设置自定义优惠券不存在，参数不合法！"];
        }

        $db = by::dbMaster();
        $tb = self::tbName();
        $trans = $db->beginTransaction();

        try {
            $db->createCommand()->update($tb,
                ['is_del' => 1, 'dtime' => time()],
                ['id' => $id]
            )->execute();

            $trans->commit();

            $this->__delIdCache($id);
            $this->__delListCache();
            $this->__delMarketDefineByMarketIdCache($defineInfo['market_id'],$defineInfo['define_type']);

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.market.define.del');
            return [false, '操作失败'];
        }

    }

}
