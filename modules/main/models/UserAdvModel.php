<?php

namespace app\modules\main\models;

use app\components\AdvAscribe;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class UserAdvModel extends CommModel
{

    const CONTINUED = YII_ENV_PROD ? 30 : 1; //绑定关系持续时间 (单位天)

    const ADV_LIMIT_UPDATE_EXPIRE = YII_ENV_PROD ? 7200 : 3600; //限制广告绑定用户更新时长 (单位秒)

    public static function tbName(): string
    {
        return "`db_dreame`.`t_user_adv`";
    }

    public $tb_fields = [
        'id', 'user_id', 'union', 'euid', 'live_mark', 'referer', 'ctime', 'utime'
    ];

    private function _getInfoKey($user_id, $union, $limit): string
    {
        return AppCRedisKeys::getUserAdvInfoKey($user_id, $union, $limit);
    }

    private function _getLastInfo($user_id): string
    {
        return AppCRedisKeys::getLastAdvInfoKey($user_id);
    }

    /**
     * @param $user_id
     * @param $union
     * @return void
     * @throws RedisException
     */
    private function _delInfoKey($user_id, $union)
    {
        $redis     = by::redis();
        $redis_key = $this->_getInfoKey($user_id, $union, true);
        $redis_key0 = $this->_getInfoKey($user_id, $union, false);
        $redis_key1 = $this->_getLastInfo($user_id);
        $redis->del($redis_key,$redis_key0,$redis_key1);

    }


    /**
     * @param $data
     * @return array
     *
     */
    public function saveLog($data): array
    {
        if (empty($data['user_id']) || empty($data['union'])) {
            return [true, '数据有误！'];
        }
        if (strlen($data['user_id']) > 11) {
            return [true, '游客不记录'];
        }

        $data['utime'] = intval(START_TIME);
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $tb = self::tbName();
            $aData = $this->getInfo($data['user_id'], $data['union']);
            $ctime = $aData['ctime'] ?? 0;
            //数据没过期且在有效期
            if ($aData && intval(bcadd($ctime, self::CONTINUED * 86400)) < time()) {
                //超时更新数据
                $id = $aData['id'] ?? '';
                $s = by::dbMaster()->createCommand()->update($tb, $data, ['id' => $id])->execute();
                if ($s === false) {
                    return [true, '更新失败！'];
                }
                $this->_delInfoKey($data['user_id'], $data['union']);
            } elseif(empty($aData)) {
                $data['ctime'] = intval(START_TIME);
                //不存在就添加数据
                by::dbMaster()->createCommand()->insert($tb, $data)->execute();
            }
            $trans->commit();

        } catch (\Exception $exception) {
            $error = $exception->getMessage() . "|" . $exception->getFile() . ":" . $exception->getLine();
            CUtil::debug($error, 'err.adv');
            $trans->rollBack();
        }
        return [true, 'OK'];
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getInfo($user_id, $union, $limit = false)
    {
        $redis     = by::redis();
        $redis_key = $this->_getInfoKey($user_id, $union, $limit);
        $aJson     = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `user_id`=:user_id AND `union`=:union ORDER BY `ctime` DESC LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [":user_id" => $user_id, ":union" => $union])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::ADV_LIMIT_UPDATE_EXPIRE]);
        }
        if(empty($aData)){
            return [];
        }

        $ctime = $aData['ctime'] ?? 0;
        if ($limit && intval(bcadd($ctime, self::CONTINUED * 86400)) < time()) {
            return [];
        }

        return $aData;
    }


    /**
     * @throws Exception|RedisException
     */
    public function clearExpireData(): array
    {
        $expireTime = intval(bcsub(time(), self::CONTINUED * 86400));

        $id = 0;

        $where = " `ctime`<={$expireTime} ";

        $db = by::dbMaster();

        $tb = self::tbName();

        while (true) {
            $sql = "SELECT `id`,`union`,`user_id` FROM {$tb} WHERE {$where} AND `id`>:id  
                           ORDER BY `id` ASC LIMIT 100";

            $list = $db->createCommand($sql, [':id' => $id])->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id  = $end['id'];

            foreach ($list as $val) {
                by::dbMaster()->createCommand()
                    ->delete($tb, "id = '{$val['id']}'")
                    ->execute();
                $this->_delInfoKey($val['user_id']??0,$val['union'] ?? '');
            }
        }
        return [true, "OK"];
    }


    /**
     * @param $user_id
     * @return array|DataReader
     * @throws Exception|RedisException
     * 获取最新的用户推广记录
     */
    public function getLastUnionInfo($user_id)
    {
        $redis     = by::redis();
        $redis_key = $this->_getLastInfo($user_id);
        $aJson     = $redis->get($redis_key);
        $aData = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `user_id`=:user_id ORDER BY `utime` DESC LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [":user_id" => $user_id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : self::ADV_LIMIT_UPDATE_EXPIRE]);
        }
        if(empty($aData)){
            return [];
        }
        $ctime = $aData['ctime'] ?? 0;
        //过期
        if(intval(bcadd($ctime,self::CONTINUED * 86400))<time()){
            return [];
        }
        return $aData;
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $event
     * @return array
     * @throws Exception
     * @throws RedisException 推送广告
     */
    public function pushAdv($user_id,$order_no,$event): array
    {
        $unions = array_keys(by::Omain()::UNION_SOURCE);
        if(strlen($user_id)<11){
            foreach ($unions as $union){
                $unionInfo = $this->getInfo($user_id,$union,true);
                if($unionInfo){
                    switch ($union){
                        case 'tencent_gdt_vid':
                        case 'tencent_qz_gdt': $this->tencentPush($user_id,$order_no,$event,$unionInfo);break;
                        default:break;
                    }
                }
                continue;
            }
        }
        return [true,'OK'];
    }


    /**
     * @param $user_id
     * @param string $order_no
     * @param string $event
     * @param array $unionInfo
     * @return void
     * 推送腾讯订单广告
     */
    protected function tencentPush($user_id, string $order_no = '', string $event='',array $unionInfo = [])
    {
        //腾讯推送
        $referer = $unionInfo['referer'] ?? '';
        $order_no && $referer && AdvAscribe::factory()->push($user_id, 'tencent', ['user_id' => $user_id, 'url' => $referer, 'event' => $event, 'click_id' => '', 'extra' => ['order_no' => $order_no]]);
    }

}
