<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\MemberCenter;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\services\UserBindService;
use app\modules\back\services\UserService;
use Redis;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class UserExtendModel extends CommModel
{
    public static $expire = 3600;

    const TIME = !YII_ENV_PROD ? 43200 : 60*86400;

    //可修改字段
    CONST MODIFY =  ['source', 'ctime','tag','guide_id'];
    CONST SOURCE_FROM_APP = 14;
    CONST SOURCE_FROM_ALIPAY = 20;

    CONST SOURCE_FROM_COMMON_PC = 25;

    CONST SOURCE_FROM_SHOP_CODE_PC = 26;

    public static function tbName(): string
    {
        return '`db_dreame`.`t_user_extend`';
    }

    /**
     * @param $user_id
     * 根据id获取信息
     */
    private function __getExtendByUidKey($user_id): string
    {
        return AppCRedisKeys::getExtendByUseridKey($user_id);
    }


    /**
     * @param $card
     * 根据crm卡号获取用户id
     */
    private function __getUidByCard($card){
        return AppCRedisKeys::getUidByCard($card);
    }


    /**
     * 列表缓存
     */
    private function __adminUserKey(){
        return AppCRedisKeys::adminUserKey();
    }

    /**
     * @param $r_id
     * @return string
     * 邀请注册列表
     */
    private function __getInviteRegList($r_id): string
    {
        return AppCRedisKeys::getInviteRegList($r_id);
    }

    /**
     * @param $user_id
     * @param string $card
     * @param int $r_id
     * 缓存清理
     */
    private function __delCache($user_id, string $card='', int $r_id=0)
    {
        $r_key1 = $this->__getExtendByUidKey($user_id);
        $r_key2 = $this->__adminUserKey();
        by::redis('core')->del($r_key1,$r_key2);

        if ($card){
            $r_key3 = $this->__getUidByCard($card);
            by::redis('core')->del($r_key3);
        }

        if (!empty($r_id)){
            $r_key4 = $this->__getInviteRegList($r_id);
            by::redis('core')->del($r_key4);
        }
    }

    public function deleteRedisCache($user_id, string $card='', int $r_id=0)
    {
        $this->__delCache($user_id, $card, $r_id);
    }

    /**
     * @param $user_id
     * @param bool $cache
     * @return array|false
     * @throws Exception
     * 根据用户ID获取扩展字段
     */
    public function getUserExtendInfo($user_id, $cache=true)
    {
        $user_id = CUtil::uint($user_id);
        if($user_id <=0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getExtendByUidKey($user_id);
        $aJson        = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false || !$cache) {
            $tb         = self::tbName();
            $sql        = "SELECT `source`,`ctime`,`guide_id`,`r_id`,`tag`,`card`,`union`,`euid`,`referer`,`platform_source`,`store`,`shop_code` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $aData;
    }

    /**
     * 获取用户扩展信息列表
     * @param array $user_ids
     * @return array
     */
    public function getUserExtendList(array $user_ids): array
    {
        $query = self::find()->from(self::tbName());
        return $query->select(['user_id', 'source', 'ctime', 'guide_id', 'r_id', 'tag', 'card', 'union', 'euid', 'referer', 'platform_source', 'store', 'shop_code'])->where(['user_id' => $user_ids])->asArray()->all();
    }

    /**
     * @param $card
     * @return false|int|mixed|string
     * @throws Exception
     * 根据crm card 获取 用户id
     */
    public function getIDbyCard($card){
        $redis        = by::redis('core');
        $redis_key    = $this->__getUidByCard($card);
        $user_id      = $redis->get($redis_key);

        if($user_id === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `user_id` FROM {$tb} WHERE `card`=:card ORDER BY `id` DESC LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql, [':card' => $card])->queryOne();
            $user_id    = $aData['user_id'] ?: 0;

            $redis->set($redis_key,$user_id,['EX'=> empty($aData) ? 10 : self::$expire]);
        }

        return $user_id;
    }

    /**
     * @param $user_id
     * @param $data
     * @return bool
     * @throws Exception
     * 保存数据
     */
    public function saveUserExtend($user_id, $data): bool
    {
        $data['user_id'] = CUtil::uint($user_id);
        $fields = array_keys($data);
        $fields = implode("`,`",$fields);

        $rows   = implode("','",$data);

        $dup    = [];
        foreach ($data as $key => $value){
            $dup[] = "`{$key}` = '{$value}'";
        }
        $dup    = implode(' , ',$dup);
        $tb     = self::tbName();
        $sql    = "INSERT INTO {$tb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";

        $res    = by::dbMaster()->createCommand($sql)->execute();

        !YII_ENV_PROD && CUtil::debug(json_encode($user_id).'|'.json_encode($data).'|'.by::dbMaster()->createCommand($sql)->getRawSql(),'extend_update');

        //删除列表缓存
        $this->__delCache($user_id,$data['card']??'', $data['r_id'] ?? 0);

        return $res;
    }

    public function platformSourceConfig($platformSource)
    {
        switch ($platformSource) {
            case 1 :
                $str = '小程序全平台（存量）';
                break;
            case 2 :
                $str = '小程序安卓机';
                break;
            case 3 :
                $str = '小程序IOS';
                break;
            case 4 :
                $str = 'app全平台（存量）';
                break;
            case 5 :
                $str = 'app安卓机';
                break;
            case 6 :
                $str = 'appIOS';
                break;
            case 7 :
                $str = '全平台';
                break;
            case 8 :
                $str = 'PC 端';
                break;
            case 9 :
                $str = '支付宝小程序 安卓机';
                break;
            case 10 :
                $str = '支付宝小程序 IOS';
                break;
            case 11 :
                $str = '移动端H5';
                break;
            default  :
                $str = '未知';
                break;
        }
        return $str;
    }


    /**
     * @param $source
     * @return array
     * 来源code说明
     */
    public function sourceConfig($source)
    {
        switch ($source){
            case 1001:case 1005:case 1006:case 1010:case 1024:case 1026:case 1027:case 1089:case 1106:
            $str = '微信首页'; $pv = 'source_1'; break;
            case 1007:case 1008:
            $str = '好友分享'; $pv = 'source_2'; break;
            case 1045:case 1046:case 1084:
            $str = '朋友圈广告'; $pv = 'source_3'; break;
            case 1067:
                $str = '公众号文章广告'; $pv = 'source_4'; break;
            case 1095:
                $str = '小程序广告'; $pv = 'source_5'; break;
            case 1035:case 1102:
            $str = '公众号菜单栏'; $pv = 'source_6'; break;
            case 1058:case 1144:
            $str = '公众号文章'; $pv = 'source_7'; break;
            case 1074:case 1081:case 1082:
            $str = '公众号消息'; $pv = 'source_8'; break;
            case 1175:case 1176:case 1177:case 1191:case 1195:
            $str = '视频号'; $pv = 'source_9'; break;
            case 1011:case 1012:case 1013:
            $str = '扫描二维码'; $pv = 'source_10'; break;
            case 1065:
                $str = '微信外部打开'; $pv = 'source_11'; break;
            case self::SOURCE_FROM_APP:
                $str = 'DREAME_HOME来源';$pv = 'source_14';break;
            case self::SOURCE_FROM_ALIPAY:
                $str = '支付宝小程序';$pv = 'source_15';break;
            case self::SOURCE_FROM_COMMON_PC:
                $str = 'PC端';$pv = 'source_25';break;
            case self::SOURCE_FROM_SHOP_CODE_PC:
                $str = 'PC端-门店来源';$pv = 'source_26';break;
            default  :
                $str = '其他'; $pv = 'source_12'; break;
        }
        return [$str,$pv];
    }

    const SOURCE_TXT = [
        'source_1' => [1001,1005,1006,1010,1024,1026,1027,1089,1106],
        'source_2' => [1007,1008],
        'source_3' => [1045,1046,1084],
        'source_4' => [1067],
        'source_5' => [1095],
        'source_6' => [1035,1102],
        'source_7' => [1058,1144],
        'source_8' => [1074,1081,1082],
        'source_9' => [1175,1176,1177,1191,1195],
        'source_10' => [1011,1012,1013],
        'source_11' => [1065],
        'source_12' => [0],
        'source_14' => [self::SOURCE_FROM_APP],
        'source_15' => [self::SOURCE_FROM_ALIPAY],
        'source_25' => [self::SOURCE_FROM_COMMON_PC],
        'source_26' => [self::SOURCE_FROM_SHOP_CODE_PC],
    ];

    /**
     * @param $page
     * @param $page_size
     * @param $user_id
     * @param $phone
     * @param $source
     * @param int $s_time
     * @param int $e_time
     * @param int $vip_s_time
     * @param int $vip_e_time
     * @param string $union
     * @param string $p_sources
     * @param int $r_id
     * @param int $guide_id
     * @param $store
     * @return array|DataReader
     * @throws Exception
     * @throws RedisException
     * 获取用户列表
     */
    public function getUserList($page, $page_size, $user_id, $phone, $source, int $s_time = 0, int $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0, string $union = '', string $p_sources = '-1', int $r_id = 0, int $guide_id=0 ,$store='',$source_code='')
    {
        $redis_key  = $this->__adminUserKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources, $r_id, $guide_id,$store,$source_code);
        $redis      = by::redis();
        $aJson      = $redis->hGet($redis_key, $h_key);
        $list       = (array)json_decode($aJson,true);

        if($aJson === false){
            $tb           = self::tbName();
            $tb_phone     = by::Phone()::tbName();
            $where        = $this->_condition($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources, $r_id, $guide_id,$store,$source_code);

            list($offset) = CUtil::pagination($page,$page_size);
            $sql          = "select e.user_id,e.shop_code from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id 
                                where {$where} order by p.ctime desc limit {$offset},{$page_size}";
            $list         = by::dbMaster()->createCommand($sql)->queryAll();

            $redis->hSet($redis_key,$h_key,json_encode($list));
            CUtil::ResetExpire($redis_key,self::$expire);
        }

       return $list;
    }

    public function getUserData($list): array
    {
        if (empty($list)) {
            return [];
        }

        $return = [];
        $shop_codes = array_unique(array_filter(array_column($list, 'shop_code')));
        $shopInfos = by::retailers()->getList(1,100,'',$shop_codes);
        $shopNames = array_column($shopInfos,'shop_name','shop_code');

        // 优化项：
        // 1. 用户列表中循环查询操作改为内存查询操作，主要就是先提取出列表中的user_ids，然后把需要的用户信息提前查询出来，然后根据user_id进行hash map查询，无mysql、redis的io查询
        // 2. 用户列表中调用会员中心接口去除，因为页面上并没有使用这些数据
        $userIds = array_unique(array_column($list,'user_id'));
        $userList = by::users()->getUserInfoListFromAllTable($userIds);
        $userListMap = array_column($userList, null, 'user_id');
        $phoneList = by::Phone()->getPhoneList($userIds);
        $phoneListMap = array_column($phoneList, null, 'user_id');
        $userMainList = by::users()->getUserManList($userIds);
        $userMainListMap = array_column($userMainList, null, 'user_id');
        $userExtandList = $this->getUserExtendList($userIds);
        $userExtandListMap = array_column($userExtandList, null, 'user_id');
        $userGuideList = by::userGuide()->getUserGuideList($userIds);
        $userGuideListMap = array_column($userGuideList, null, 'user_id');
        $userRecommendList = by::userRecommend()->getUserRecommendList($userIds);
        $userRecommendListMap = array_column($userRecommendList, null, 'user_id');
        $userSourceExtendList = by::SourceExtend()->getUserSourceExtendList($userIds);
        $userSourceExtendListMap = array_column($userSourceExtendList, null, 'user_id');
        $sourceConfigMap = by::SourceConfig()->getSourceConfigMap();
        foreach ($list as $value) {
            $user     = $userListMap[$value['user_id']] ?? [];
            $phone    = $phoneListMap[$value['user_id']]['phone'] ?? '';
            $main     = $userMainListMap[$value['user_id']] ?? [];
            $extend   = $userExtandListMap[$value['user_id']] ?? [];
            $vip_time = $phoneListMap[$value['user_id']]['ctime'] ?? '';
            $guide    = $userGuideListMap[$value['user_id']] ?? [];
            $recommend = $userRecommendListMap[$value['user_id']] ?? [];
            $userSource = $userSourceExtendListMap[$value['user_id']] ?? [];
            $sourceCode = $userSource['source_euid'] ?? '';
            $sourceInfo = $sourceConfigMap[$sourceCode] ?? [];

            // list($s, $basicInfo) = by::model("MemberCenterModel", MAIN_MODULE)->CenterMessage('basicInfo',['user_id'=>$user['user_id']]);;
            //
            // $dataInfo = $basicInfo['currentLevelInfo'] ?? '';
            // $levelInfo     = $dataInfo['level'] ?? '';

            if (!empty($extend['source'])){
                list($source) = $this->sourceConfig($extend['source']);

            }
            $shop_code = $extend['shop_code'] ?? '';
            $return[] = [
                'user_id'              => $value['user_id'],
                'card'                 => $extend['card'] ?? '',
                'store'                => $extend['store'] ?? '',
                'nick'                 => $user['nick'] ?? '',
                'phone'                => $phone,
                'source'               => $source ?? '其他',
                'is_vip'               => $phone ? 1 : 0,
                'is_r_invite'          => $extend['r_id'] ?? '',
                'is_invite'            => $extend['guide_id'] ?? '',
                'now_guide_id'         => $guide['guide_id'] ?? '',
                'now_r_id'             => $recommend['r_id'] ?? '',
                'tag'                  => $extend['tag'] ?? '',
                'union'                => $extend['union'] ?? '',
                'euid'                 => $extend['euid'] ?? '',
                'platform_source'      => $extend['platform_source'] ?? 0,
                'platform_source_name' => $this->platformSourceConfig($extend['platform_source'] ?? 0) ?? '未知',
                'vip_time'             => $vip_time,
                'status'               => strpos($main['openudid'] ?? '', '|') ? 1 : 0,
                // 'level'                => $levelInfo['level'] ?? '',
                // 'level_name'           => $levelInfo['name'] ?? '',
                // 'level_order'          => $levelInfo['order'] ?? '',
                // 'level_min_grow'       => $levelInfo['minGrow'] ?? '',
                // 'level_max_grow'       => $levelInfo['maxGrow'] ?? '',
                // 'level_picture'        => $levelInfo['picture'] ?? '',
                // 'level_uid'            => $basicInfo['uid'] ?? '',
                'param'                => $sourceInfo['param'] ?? '',
                'channel'              => $sourceInfo['source_code'] ?? '',//当前渠道来源
                'shop_code'            => $shop_code,
                'shop_name' => $shopNames[$shop_code] ?? '' // 门店名称
            ];
        }

        return $return;
    }


    /**
     * @deprecated gezhiqiang-export-VER2177-3874 版本上线后，将废弃。2023-07-18
     * @param $s_user_id
     * @param $s_phone
     * @param $s_source
     * @param $s_time
     * @param $e_time
     * @param int $vip_s_time
     * @param int $vip_e_time
     * @param int $is_export_point
     * @return array
     * 用户列表导出
     */
    public function export($s_user_id = 0, $s_phone = 0, $s_source = 0, $s_time = 0, $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0, int $is_export_point = 0, string $union= '', string $p_sources = '-1',$viewSensitive=false): array
    {
//        $total      = $this->getUserTotal($s_user_id,$s_phone,$s_source,$s_time,$e_time);
//        $page_size  = 100;
//        $pages      = CUtil::getPaginationPages($total,$page_size);
        $headList = [
            '用户id',
            '会员卡号',
            '姓名',
            '手机号',
            '生日',
            '积分',
            '注册时间',
            '活跃时间',
            '注册会员时间',
            '来源',
            '推广渠道',
            '平台来源',
            '标签',
            '性别',
            '地区',
            '默认收货地址',
            '是否注销',
            '是否邀请人邀请注册',
            '是否导购邀请注册',
            '是否绑定导购员',
            '绑定人ID',
            '是否领取新人礼包',
//            '累计支付金额',
//            '累计支付订单数',
//            '客单价',
//            '累计退款金额',
//            '累计退款订单数',
//            '注册30天支付金额',
//            '今年支付金额',
        ];
        $fileName   = '用户列表' . date('Ymd') . mt_rand(1000, 9999);
        //导出
        CUtil::export_csv_new ($headList, function () use($s_user_id, $s_phone, $s_source, $s_time, $e_time, $vip_s_time, $vip_e_time, $is_export_point, $union, $p_sources,$viewSensitive)
        {
            $db       = by::dbMaster();
            $tb       = self::tbName();
            $tb_phone = by::Phone()::tbName();
            $last_uid = 0;
            $where    = $this->_condition($s_user_id, $s_phone, $s_source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources);

            while (true) {
                $sql  = "select e.user_id from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id 
                            where e.user_id > :user_id AND {$where} order by e.user_id ASC limit 100";

                $list = $db->createCommand($sql, [':user_id'=>$last_uid])->queryAll();


                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $last_uid   = $end['user_id'];
                $dataList   = [];

                foreach ($list as $v) {
                    $user               = by::users()->getOneByUid($v['user_id']);
                    $main               = by::users()->getUserMainInfo($v['user_id']);
                    $address            = by::Address()->GetDefaultAddress($v['user_id']);
                    $phone              = by::Phone()->GetPhoneByUid($v['user_id']);
                    $extend             = $this->getUserExtendInfo($v['user_id']);
                    $p_time             = by::Phone()->GetCtimeByUid($v['user_id']);
                    if (!empty($extend['source'])){
                        list($source) = $this->sourceConfig($extend['source']);
                    }
                    $platformSourceName = $this->platformSourceConfig($extend['platform_source']??0);

                    $u_guide    = by::userGuide()->getGuideByUid($v['user_id']);

                    $r_info = by::userRecommend()->getInfoByUid($v['user_id']);
                    $r_id   = $r_info['r_id'] ?? 0;
                    if (!empty($r_info['expire_time']) && START_TIME > $r_info['expire_time']) {
                        $r_id = 0;
                    }

                    //获取积分
                    if (!empty($is_export_point)) {
                        try {
                            $score = by::point()->get($v['user_id']);
                        } catch (\Exception $e) {
                            $score = '';
                        }
                    }

                    $dataList[] = [
                        'user_id'              => $v['user_id'],
                        'card'                 => $extend['card'] ?? '',
                        'real_name'            => $user['real_name'] ?? '',
                        'phone'                => $phone,
                        'birthday'             => !empty($user['birthday']) ? date('Y-m-d', $user['birthday']) : '',
                        'score'                => $score ?? '',
                        'reg_time'             => !empty($main['reg_time']) ? date('Y-m-d H:i:s', $main['reg_time']) : '',
                        'active_time'          => !empty($user['active_time']) ? date('Y-m-d H:i:s', $user['active_time']) : '',
                        'vip_time'             => !empty($p_time) ? date('Y-m-d H:i:s', $p_time) : '',
                        'source'               => $source ?? '',
                        'union'                => $extend['union'] ?? '',
                        'platform_source_name' => $platformSourceName ?? '',
                        'tag'                  => $extend['tag'] ?? '',
                        'sex'                  => by::users()::USER_SEX_TXT[$user['sex'] ?? 0],
                        'area'                 => empty($user['area']) ? '' : sprintf('%s，%s，%s', $user['area']['province'] ?? '', $user['area']['city'] ?? '', $user['area']['area'] ?? ''),
                        'address'              => empty($address) ? '' : sprintf('%s，%s，%s，%s', $address['province'] ?? '', $address['city'] ?? '', $address['area'] ?? '', $address['detail'] ?? ''),
                        'status'               => strpos($main['openudid'], '|') ? '注销' : '正常',
                        'is_r_invite'          => !empty($extend['r_id']) ? $extend['r_id'] : '',
                        'is_invite'            => !empty($extend['guide_id']) ? $extend['guide_id'] : '',
                        'guide'                => $u_guide ? '是' : '否',
                        'r_id'                 => !empty($r_id) ? $r_id : '',
                        'is_new_gift'          => ($user['is_new_gift'] ?? 0) ? '是' : '否',
                    ];
                }
                !$viewSensitive && $dataList = Response::responseList($dataList,['phone'=>'tm','address'=>'tm','area'=>'tm']);

                yield $dataList;

            }
        },$fileName);

        return [true,'成功'];
    }

    public function exportData($s_user_id = 0, $s_phone = 0, $s_source = 0, $s_time = 0, $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0, int $is_export_point = 0, string $union= '', string $p_sources = '-1',$viewSensitive=false): array
    {
        $headList = [
            '用户id',
            '会员卡号',
            '姓名',
            '手机号',
            '生日',
            '积分',
            '注册时间',
            '活跃时间',
            '注册会员时间',
            '来源',
            '推广渠道',
            '平台来源',
            '标签',
            '性别',
            '地区',
            '默认收货地址',
            '是否注销',
            '是否邀请人邀请注册',
            '是否导购邀请注册',
            '是否绑定导购员',
            '绑定人ID',
            '是否领取新人礼包',
            '门店',
            '是否绑定员工',
        ];
        //导出
        $db       = by::dbMaster();
        $tb       = self::tbName();
        $tb_phone = by::Phone()::tbName();
        $last_uid = 0;
        $where    = $this->_condition($s_user_id, $s_phone, $s_source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources);

        $dataList = [];
        while (true) {
            $sql  = "select e.user_id from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id 
                        where e.user_id > :user_id AND {$where} order by e.user_id ASC limit 100";

            $list = $db->createCommand($sql, [':user_id'=>$last_uid])->queryAll();


            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $last_uid   = $end['user_id'];

            foreach ($list as $v) {
                $user               = by::users()->getOneByUid($v['user_id']);
                $main               = by::users()->getUserMainInfo($v['user_id']);
                $address            = by::Address()->GetDefaultAddress($v['user_id']);
                $phone              = by::Phone()->GetPhoneByUid($v['user_id']);
                $extend             = $this->getUserExtendInfo($v['user_id']);
                $p_time             = by::Phone()->GetCtimeByUid($v['user_id']);
                if (!empty($extend['source'])){
                    list($source) = $this->sourceConfig($extend['source']);
                }
                $platformSourceName = $this->platformSourceConfig($extend['platform_source']??0);

                $u_guide    = by::userGuide()->getGuideByUid($v['user_id']);

                $r_info = by::userRecommend()->getInfoByUid($v['user_id']);
                $r_id   = $r_info['r_id'] ?? 0;
                if (!empty($r_info['expire_time']) && START_TIME > $r_info['expire_time']) {
                    $r_id = 0;
                }

                //获取积分
                if (!empty($is_export_point)) {
                    try {
                        $score = by::point()->get($v['user_id']);
                    } catch (\Exception $e) {
                        $score = '';
                    }
                }

                $dataList[] = [
                    'user_id'              => $v['user_id'],
                    'card'                 => $extend['card'] ?? '',
                    'real_name'            => '\''.($user['real_name'] ?? ''),
                    'phone'                => $phone,
                    'birthday'             => !empty($user['birthday']) ? date('Y-m-d', $user['birthday']) : '',
                    'score'                => $score ?? '',
                    'reg_time'             => !empty($main['reg_time']) ? date('Y-m-d H:i:s', $main['reg_time']) : '',
                    'active_time'          => !empty($user['active_time']) ? date('Y-m-d H:i:s', $user['active_time']) : '',
                    'vip_time'             => !empty($p_time) ? date('Y-m-d H:i:s', $p_time) : '',
                    'source'               => $source ?? '',
                    'union'                => $extend['union'] ?? '',
                    'platform_source_name' => $platformSourceName ?? '',
                    'tag'                  => $extend['tag'] ?? '',
                    'sex'                  => by::users()::USER_SEX_TXT[$user['sex'] ?? 0],
                    'area'                 => empty($user['area']) ? '' : sprintf('%s，%s，%s', $user['area']['province'] ?? '', $user['area']['city'] ?? '', $user['area']['area'] ?? ''),
                    'address'              => empty($address) ? '' : sprintf('%s，%s，%s，%s', $address['province'] ?? '', $address['city'] ?? '', $address['area'] ?? '', $address['detail'] ?? ''),
                    'status'               => strpos($main['openudid'], '|') ? '注销' : '正常',
                    'is_r_invite'          => !empty($extend['r_id']) ? $extend['r_id'] : '',
                    'is_invite'            => !empty($extend['guide_id']) ? $extend['guide_id'] : '',
                    'guide'                => $u_guide ? '是' : '否',
                    'r_id'                 => !empty($r_id) ? $r_id : '',
                    'is_new_gift'          => ($user['is_new_gift'] ?? 0) ? '是' : '否',
                    'store'                => $extend['store'] ?? '',
                    'bind_employee_status' => $extend['store'] ?? ''
                ];
            }
        }

        // 绑定员工状态
        $dataList = $this->setBindEmployeeStatus($dataList);

        !$viewSensitive && $dataList = Response::responseList($dataList,['phone'=>'tm','address'=>'tm','area'=>'tm']);
        array_unshift($dataList, $headList);
        return $dataList;
    }

    /**
     * @param $user_id
     * @throws \yii\db\Exception
     * @return array
     * 获取用户公众号标签
     */
    public function getTag($user_id)
    {
        if (!YII_ENV_PROD){
            return [false,'无标签'];
        }
        $uMain   = by::users()->getUserMainInfo($user_id);
        if (empty($uMain['unionid'])){
            return [false,'用户信息不存在'];
        }
        $aLog    = by::OaFocus()->GetALogByUnionid($uMain['unionid']);
        if(!empty($aLog['oa_openid'])) {
            list($state, $tagIds) = WeiXin::factory()->getTagsByUid($aLog['oa_openid'],empty(CUtil::wxOaLock())?WeiXin::UQ_TOKEN['OA']:WeiXin::UQ_TOKEN['OANEW']);
            if($state && !empty($tagIds['tagid_list'])){
                list($state, $tagList) = WeiXin::factory()->getTags(empty(CUtil::wxOaLock())?WeiXin::UQ_TOKEN['OA']:WeiXin::UQ_TOKEN['OANEW']);

                if (empty($tagList['tags'])) {
                    return [true, ''];
                }

                $tags = $arr = [];
                foreach ($tagList['tags'] as $v) {
                    $arr[$v['id']] = $v['name'];
                }
                foreach ($tagIds['tagid_list'] as $v) {
                    $tags[] = $arr[$v];
                }
                $tag = implode('，', $tags);
                return [true,$tag];
            }
        }
        return [false,'无标签'];
    }

    /**
     * @param $user_id
     * @param $phone
     * @param $source
     * @param $s_time
     * @param $e_time
     * @param $vip_s_time
     * @param $vip_e_time
     * @param $union
     * @param $p_sources
     * @param $r_id
     * @param $guide_id
     * @param $store
     * @return string
     * @throws Exception
     */
    protected function _condition($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union = '', $p_sources = '-1', $r_id = 0, $guide_id = 0,$store='',$source_code=''): string
    {
        $where = "1=1";

        //vip注册时间
        if($vip_s_time && $vip_e_time) {
            $vip_s_time   = CUtil::uint($vip_s_time);
            $vip_e_time   = CUtil::uint($vip_e_time) + 86399;
            $vip_end_time = strtotime("+3 month", $vip_s_time) + 86399;
            if ($vip_e_time > $vip_end_time) $vip_e_time = $vip_end_time;
            $where       .= " AND (p.ctime BETWEEN {$vip_s_time} AND {$vip_e_time})";
        }

        if (!empty($source_code)) {
            $sourceInfo = by::SourceExtend()->getLatestSourceList(['source_code' => $source_code]);
            $userIds = array_filter(array_unique(array_column($sourceInfo, 'user_id')));
            $userIds = implode("','", $userIds);
            $where   .= " AND e.`user_id` in ('{$userIds}')";
        }


        if(!empty($user_id)){
            $where .= " and e.user_id = {$user_id}";
        }

        if(!empty($phone)) {
            $uids = by::Phone()->GetUidsByPhone($phone);
            if(!empty($uids)) {
                $uids    = implode(',',$uids);
                $where  .= " AND e.user_id IN ({$uids})";
            }else{
                //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                $where  .= " AND e.user_id = -1";
            }
        }

        if(!empty($source)){
            $arr = self::SOURCE_TXT[$source] ?? [];
            if(!empty($arr)){
                $sources = implode(',',$arr);
                $where  .= " and e.source in ({$sources})";
            }
        }

        if(empty($phone) && empty($user_id)){
            if($s_time && $e_time) {
                $s_time = CUtil::uint($s_time);
                $e_time = CUtil::uint($e_time) + 86399;
                $where .= " AND (e.ctime BETWEEN {$s_time} AND {$e_time})";
            }

            // //vip注册时间
            // if($vip_s_time && $vip_e_time) {
            //     $vip_s_time   = CUtil::uint($vip_s_time);
            //     $vip_e_time   = CUtil::uint($vip_e_time) + 86399;
            //     $vip_end_time = strtotime("+3 month", $vip_s_time) + 86399;
            //     if ($vip_e_time > $vip_end_time) $vip_e_time = $vip_end_time;
            //     $where       .= " AND (p.ctime BETWEEN {$vip_s_time} AND {$vip_e_time})";
            // }
        }


        if($union){
            $where .= " and e.union like '%$union%'";
        }

        if(intval($p_sources) != -1){
            $p_sources = trim($p_sources);
            $p_sources = implode("','",explode(',',$p_sources));
            $where .= " and e.platform_source  in ('{$p_sources}')";
        }

        if($r_id){
            $where .= " and e.r_id = {$r_id}";
        }

        if($guide_id){
            $where .= " and e.guide_id = {$guide_id}";
        }

        if ($store){
            $where .= " and e.store = '{$store}'";
        }
        return $where;
    }


    /**
     * @param $user_id
     * @param $phone
     * @param $source
     * @param int $s_time
     * @param int $e_time
     * @param int $vip_s_time
     * @param int $vip_e_time
     * @param string $union
     * @param string $p_sources
     * @param int $r_id
     * @param int $guide_id
     * @param string $store
     * @return false|int|Redis|string|DataReader|null
     * @throws Exception
     * @throws RedisException
     * 用户总数
     */
    public function getUserTotal($user_id, $phone, $source, int $s_time = 0, int $e_time = 0, int $vip_s_time = 0, int $vip_e_time = 0, string $union = '', string $p_sources = '-1', int $r_id=0, int $guide_id=0, string $store='', $source_code='')
    {
        $redis_key  = $this->__adminUserKey();
        $h_key      = CUtil::getAllParams(__FUNCTION__, $user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time,$union,$p_sources,$r_id,$guide_id,$store,$source_code);
        $redis      = by::redis();
        $total      = $redis->hGet($redis_key, $h_key);

        if ($total === false) {
            $tb       = self::tbName();
            $tb_phone = by::Phone()::tbName();
            $where    = $this->_condition($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources, $r_id,$guide_id,$store,$source_code);
            $sql      = "select count(*) from {$tb} as e LEFT JOIN {$tb_phone} as p ON e.user_id = p.user_id where {$where}";
            $total    = by::dbMaster()->createCommand($sql)->queryScalar();

            $redis->hSet($redis_key,$h_key,$total);
            CUtil::ResetExpire($redis_key,self::$expire);
        }

        return $total;
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 用户详情（note：限后台使用）
     */
    public function getUserInfo($user_id)
    {
        $main       = by::users()->getUserMainInfo($user_id);
        if (!$main){
            return [false,'用户不存在'];
        }

        $user    = by::users()->getOneByUid($user_id);
        $extend  = $this->getUserExtendInfo($user_id);
        $u_guide = by::userGuide()->getGuideByUid($user_id);
        $phone   = by::Phone()->GetPhoneByUid($user_id);
        $p_time  = by::Phone()->GetCtimeByUid($user_id);
        if (!empty($extend['source'])){
            list($source) = $this->sourceConfig($extend['source']);
        }

        $shop_code = $extend['shop_code'] ?? '';
        if($shop_code){
            $shopInfos = by::retailers()->getList(1,100,'',[$shop_code]);
            $shop_name = $shopInfos[0]['shop_name'] ?? '';
        }


        $point      = by::point()->get($user_id);

        $addressArr = by::Address()->GetDefaultAddress($user_id);
        if (!empty($addressArr['id'])){
            $address = by::Address()->GetOneAddress($user_id,$addressArr['id']);
        }

        $r_info = by::userRecommend()->getInfoByUid($user_id);
        $r_id   = $r_info['r_id'] ?? 0;
        if (!empty($r_info['expire_time']) && START_TIME > $r_info['expire_time']) {
            $r_id = 0;
        }

        $data = [
            'user_id'     => $user_id,
            'avatar'      => $user['avatar'] ?? '',
            'nick'        => $user['nick'] ?? '',
            'sex'         => $user['sex'] ?? '',
            'phone'       => $phone,
            'active_time' => $user['active_time'] ?? '',
            'reg_time'    => $main['reg_time'],
            'vip_time'    => $p_time ?? '',
            'source'      => $source ?? '其他',
            'status'      => strpos($main['openudid'], '|') ? '注销' : '正常',
            'point'       => $point ?? 0,
            'address'     => $address ?? [],
            'birthday'    => $user['birthday'] ?? 0,
            'area'        => $user['area'] ?? '',
            'real_name'   => $user['real_name'] ?? '',
            'is_invite'   => !empty($extend['guide_id']) ? 1 : 0,
            'guide'       => $u_guide,
            'r_id'        => $r_id,
            'tag'         => $extend['tag'] ?? '',
            'is_new_gift' => $user['is_new_gift'] ?? '0',
            'is_vip'      => $phone ? 1 : 0,
            'card'        => $extend['card'] ?? '',
            'store'       => $extend['store'] ?? '',
            'shop_code'   => $extend['shop_code'] ?? '',
            'shop_name'   => $shop_name ?? '',
        ];

        return [true,$data];
    }


    public function getCrmSource($source=0){
        $source = CUtil::uint($source);

        $crmSource = '01';
        foreach (UserExtendModel::SOURCE_TXT as $k => $v){
            if (in_array($source,$v)){
                 list(,$crmSource) = explode('_',$k);
            }
        }

        return str_pad($crmSource,2,'0',STR_PAD_LEFT);
    }


    /**
     * @param $r_id
     * @return array
     * @throws Exception
     * 邀请注册列表
     */
    public function getInviteRegList($r_id): array
    {
        $r_id = CUtil::uint($r_id);
        if ($r_id == 0) {
            return [];
        }

        $redis   = by::redis('core');
        $r_key   = $this->__getInviteRegList($r_id);
        $aJson   = $redis->get($r_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb    = self::tbName();
            $sql   = "SELECT `user_id` FROM {$tb} WHERE `r_id`=:r_id ORDER BY `ctime` DESC";
            $aData = by::dbMaster()->createCommand($sql, [':r_id' => $r_id])->queryAll();
            $aData = !empty($aData) ? $aData : [];

            by::redis('core')->set($r_key, json_encode($aData), empty($aData) ? 10 : self::$expire);
        }

        $list = [];
        foreach ($aData as $v) {
            $extend_info = $this->getUserExtendInfo($v['user_id']);
            if (empty($extend_info)) {
                continue;
            }

            //获取用户信息
            $u_info = by::users()->getOneByUid($v['user_id']);
            $extend_info['user'] = [
                'avatar' => $u_info['avatar'] ?? "",
                'nick'   => $u_info['nick']   ?? ""
            ];

            $list[] = $extend_info;
        }

        return $list;
    }


    /**
     * @return false|string|DataReader|null
     * @throws Exception
     * 新用户注册数
     */
    public function getInviteRegTotal()
    {
        $r_key = $this->__adminUserKey();
        $h_key = CUtil::getAllParams(__FUNCTION__);
        $redis = by::redis();
        $total = $redis->hGet($r_key, $h_key);

        if ($total === false) {
            $tb    = self::tbName();
            $sql   = "select count(`user_id`) from {$tb} where `r_id` > 0";
            $total = by::dbMaster()->createCommand($sql)->queryScalar();
            $total = !empty($total) ? $total : 0;

            $redis->hSet($r_key, $h_key, $total);
            CUtil::ResetExpire($r_key,empty($num) ? 10 : self::$expire);
        }

        return $total;
    }

    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }

    /**
     * @throws Exception
     */
    public function getCardNumsByUsers($users)
    {
        $tb      = self::tbName();
        $users = implode("','",$users);
        $sql = "SELECT COUNT( DISTINCT `card`) as nums FROM {$tb} WHERE `card` is not null AND `user_id` in ('{$users}') LIMIT 1 ";
        $res = by::dbMaster()->createCommand($sql)->queryOne();
        if(!$res){
            return [false,'卡号查询错误！'];
        }
        return [true,$res['nums']??-1] ;
    }

    /**
     * 根据用户ID获取数据（无缓存）
     * @param array $user_ids
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getListByUserIds(array $user_ids, array $columns = ['id']): array
    {
        $tb = self::tbName();
        $user_ids = implode(',', $user_ids);
        // 查询字段
        $columns = implode("`,`", $columns);
        $sql = "SELECT `{$columns}` FROM {$tb} WHERE `user_id` in ({$user_ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 更新门店信息
     */
    public function updateStoreData($unionId, $store): bool
    {
        $user_id = by::users()->getUserIdByUnionId($unionId, 1) ?? '';

        // 检查用户是否存在并且 store 字段为空
        if (!empty($user_id)) {
            $extend      = by::userExtend()->getUserExtendInfo($user_id, false);
            $currentTime = intval(START_TIME);
            //判断十分钟内 是会员 且没门店信息
            if (!empty($extend) && empty($extend['store']) && abs($currentTime - $extend['ctime']) < by::WeFocus()::UPDATE_STORE_EXPIRE) {
                // 更新主表 store 字段
                $affectedRows = by::dbMaster()->createCommand()
                    ->update(self::tbName(), ['store' => $store], ['user_id' => $user_id])
                    ->execute();

                // 返回更新是否成功
                if ($affectedRows > 0) {
                    // 删除缓存
                    by::userExtend()->deleteRedisCache($user_id);
                    return true;
                }
            }
        }
        // 如果没有执行更新，返回 false
        return false;
    }

    /**
     * 设置绑定员工状态
     * @param array $dataList
     * @return array
     */
    private function setBindEmployeeStatus(array $dataList): array
    {
        $user_ids = array_column($dataList, 'user_id');

        // 获取uid
        $uids = by::Phone()->getUidByUserIds($user_ids);
        // 绑定状态
        $bind_status = UserBindService::getInstance()->getCurrentBindStatus($uids);

        foreach ($dataList as $key => $value) {
            $dataList[$key]['bind_employee_status'] = $bind_status[$uids[$value['user_id']]] ? '是' : '否';
        }
        return $dataList;
    }

}
