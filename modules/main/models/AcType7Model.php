<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 17:35
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\ActiveRecord;
use yii\db\DataReader;
use yii\db\Exception;

class AcType7Model extends CommModel
{
    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame`.`t_ac_type7`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public $tb_fields = [
            'id', 'ac_id', 'sku', 'start_time', 'end_time', 'create_time', 'update_time'
    ];

    /**
     * @param $ac_id
     * @return string
     * 商品唯一数据缓存KEY
     */
    private function __getInfoAcTypeKey($ac_id): string
    {
        return AppCRedisKeys::getInfoAcType3ByAcId($ac_id);
    }


    /**
     * SKU对应可自动发放的卡券
     * @param $sku
     * @return string
     */
    private function __getAutoCouponSkuKey($sku): string
    {
        return AppCRedisKeys::getAutoCouponSkuKey($sku);
    }


    /**
     * @param $ac_id
     * @return array|DataReader
     * @throws Exception
     * 获取指定详情信息
     */
    public function getInfoByAcId($ac_id)
    {
        $ac_id = CUtil::uint($ac_id);
        if ($ac_id == 0) {
            return [];
        }
        $redis_key = $this->__getInfoAcTypeKey($ac_id);

        $redis = by::redis('core');
        $aJson = $redis->get($redis_key);
        $aData = (array) json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `ac_id`=:ac_id";
            $aData  = by::dbMaster()->createCommand($sql, [':ac_id' => $ac_id])->queryAll();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData;
    }

    /**
     * 删除记录
     * @param int $acId The ID of the record to delete.
     * @return bool Returns `true`
     */
    public function del(int $acId, $gant_type): bool
    {
        if ($gant_type == ActivityConfigModel::GRANT_TYPE['auto_coupon']) {
            $skus = self::find()->where(['ac_id' => $acId])->select(['sku'])->column();
            self::updateAll(['is_delete' => 1], ['ac_id' => $acId]);
            by::acType7()->delAcTypeCache($acId, $skus);
            return true;
        }
        return false;
    }

    /**
     * 更新或插入数据
     * @param $acId
     * @param $skus
     * @param $start_time
     * @param $end_time
     */
    public function saveLog($acId, $skusInfo, $start_time, $end_time)
    {
        $item2 = [];
        foreach ($skusInfo as $skuInfo) {
            $id   = $skuInfo['id'] ?? 0;
            $save = [
                    'sku'         => $skuInfo['sku'],
                    'id'          => $id,
                    'start_time'  => $start_time,
                    'end_time'    => $end_time,
                    'update_time' => time(),
            ];
            if ($id == 0) {
                $save['create_time'] = time();
            }
            $item2[] = $save;
        }
        CUtil::mSaveMultiple(by::acType7(), $item2, ['ac_id' => $acId]);
        $skus = array_column($skusInfo, 'sku');
        by::acType7()->delAcTypeCache($acId, $skus);
    }

    /**
     * @param $ac_id
     * @param $skus
     * @return void
     */
    public function delAcTypeCache($ac_id, $skus)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getInfoAcTypeKey($ac_id);
        // 批量删除$data中的所有sku对应的缓存
        if (!empty($skus)) {
            foreach ($skus as $sku) {
                $redis->del($this->__getAutoCouponSkuKey($sku));
            }
        }
        $redis->del($redis_key);
    }


    /**
     *
     * 获取SKU对应的自动发放卡券
     * @param array $skus SKU列表
     * @return array 可用的优惠券列表,格式: [ac_id => ['grant_time' => 100, 'coupons' => [...]]]
     * @throws Exception
     */
    public function getAvailableCoupons(array $skus): array
    {
        if (empty($skus)) {
            throw new Exception('getAvailableCoupons SKU list is empty');
        }

        // 1. 获取所有SKU关联的活动数据
        $actData = $this->getActivityDataFromSkus($skus);
        if (empty($actData)) {
            return [];
        }

        // 2. 获取活动配置属性
        $activityAttributes = $this->getActivityAttributes($actData);

        // 3. 整合优惠券数据
        $result = [];
        foreach ($actData as $act) {
            $acId = $act['ac_id'];
            $couponList = by::aM()->getCouponListByAcId($acId);

            $result[$acId] = [
                    'grant_time' => $activityAttributes[$acId]['grant_time'] ?? 0,
                    'coupons' => $this->filterAvailableCoupons($couponList, $acId)
            ];
        }

        return $result;
    }

    /**
     * 从SKU列表获取活动数据
     */
    private function getActivityDataFromSkus(array $skus): array
    {
        $actData = [];
        foreach ($skus as $sku) {
            $act = $this->getActIdBySku($sku);
            if (!empty($act['data'])) {
                $actData = array_merge($actData, $act['data']);
            }
        }
        return $actData;
    }

    /**
     * 获取活动配置属性
     * @throws Exception
     */
    private function getActivityAttributes(array $actData): array
    {
        $attributes = [];
        foreach ($actData as $item) {
            $acId = $item['ac_id'];
            $config = by::activityConfigModel()->getActivityOne($acId);
            $attributes[$acId] = $config['attributes'] ?? '';
        }
        return $attributes;
    }

    /**
     * 过滤有效的优惠券
     */
    private function filterAvailableCoupons(array $couponList, int $acId): array
    {
        $availableCoupons = [];
        foreach ($couponList as $coupon) {
            if (intval($coupon['stock']) > 0) {
                $availableCoupons[] = [
                        'market_id' => (int)$coupon['market_id'],
                        'ac_id' => $acId
                ];
            }
        }
        return $availableCoupons;
    }


    /**
     * 获取SKU对应的活动ID
     * @param $sku
     * @return array
     */
    public function getActIdBySku($sku): array
    {
        return CUtil::rememberCache($this->__getAutoCouponSkuKey($sku), function () use ($sku) {
            $activities     = $this->getActiveRecords($sku);
            $expire_seconds = CUtil::calculateCacheTtl($activities);
            return ['set_expire' => $expire_seconds, 'expire_time' => time() + $expire_seconds, 'data' => $activities];
        });
    }



    /**
     * 获取SKU对应的活动ID
     * @param $sku
     * @return array|ActiveRecord[]
     */
    function getActiveRecords($sku): array
    {
        return self::find()->where(['sku' => $sku])->andWhere(['is_delete'=>0])->andWhere(['is_delete' => 0])->andWhere(['>', 'end_time', time()])->andWhere(['<', 'start_time', time()])->select(['ac_id', 'start_time', 'end_time'])->asArray()->all();
    }


}
