<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use phpDocumentor\Reflection\Types\This;
use yii\db\Exception;


class PhoneModel extends CommModel {
    public $tb_fields = [
        'id','user_id','phone','ctime'
    ];

    public static function tbName(): string
    {
        return "`db_dreame`.`t_phone`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @param $user_id
     * @return string
     * 根据用户ID获取手机号
     */
    private function __getPhoneByUidKey($user_id): string
    {
        return AppCRedisKeys::GetPhoneByUid($user_id);
    }

    /**
     * @param $user_id
     * @return string
     * 根据用户ID获取绑定时间
     */
    private function __getCtimeByUidKey($user_id): string
    {
        return AppCRedisKeys::getCtimeByUidKey($user_id);
    }

    /**
     * @param $phone
     * @return string
     * 根据手机号获取用户ID
     */
    private function __getUidByPhone($phone): string
    {
        return AppCRedisKeys::GetUidByPhone($phone);
    }

    /**
     * @param $user_id
     * @param $phone
     * 缓存清理
     */
    private function __delCache($user_id,$phone) {

        $redis = by::redis('core');

        if (!empty($user_id)) {
            $r_key = $this->__getPhoneByUidKey($user_id);
            $r_key_2 = $this->__getCtimeByUidKey($user_id);
            $redis->del($r_key);
            $redis->del($r_key_2);
        }
        if (!empty($phone)) {
            $r_key = $this->__getUidByPhone($phone);
            $redis->del($r_key);
        }
    }

    public function deleteRedisCache($user_id, $phone)
    {
        $this->__delCache($user_id, $phone);
    }

    /**
     * @param $user_id
     * @return false|mixed|string
     * @throws Exception
     * 根据用户ID获取手机号
     */
    public function GetPhoneByUid($user_id, $cache = true) {
        if (strlen($user_id) > 13) {
            return "";
        }
        $user_id = CUtil::uint($user_id);
        if ($user_id <= 0) {
            return "";
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getPhoneByUidKey($user_id);
        $phone       = $cache ? $redis->get($redis_key) : false;

        if($phone === false || empty($phone)) {

            $tb         = self::tbName();
            $sql        = "SELECT `phone` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $command    = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);

            $info       = $command->queryOne();
            !YII_ENV_PROD && CUtil::debug($command->getRawSql().'|'.json_encode($info), 'auto_login');

            $phone      = $info['phone'] ?? "";

            !empty($phone) && $redis->set($redis_key,$phone,['EX'=>  3600]);
        }

        return $phone;
    }

    /**
     * 获取用户手机号列表
     * @param array $user_ids
     * @return array
     */
    public function getPhoneList(array $user_ids): array
    {
        $query = self::find()->from(self::tbName());
        return $query->select(['user_id', 'mall_id', 'phone', 'ctime'])->where(['user_id' => $user_ids])->asArray()->all();
    }

    /**
     * @param $user_id
     * @return false|mixed|string
     * @throws Exception
     * 根据用户ID获取绑定时间
     */
    public function GetCtimeByUid($user_id) {
        $user_id     = CUtil::uint($user_id);
        if($user_id <=0) {
            return "";
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getCtimeByUidKey($user_id);
        $ctime       = $redis->get($redis_key);
        if($ctime === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `ctime` FROM {$tb} WHERE `user_id`=:user_id LIMIT 1";

            $command    = by::dbMaster()->createCommand($sql);
            $command->bindParam(":user_id", $user_id);

            $info       = $command->queryOne();
            $ctime      = $info['ctime'] ?? "";

            $redis->set($redis_key,$ctime,['EX'=> empty($ctime) ? 10 : 3600]);
        }

        return $ctime;
    }

    /**
     * @param $phone
     * @param int $page_size : 默认获取100个，一个手机号被100人在微信绑定过
     * @return array
     * @throws Exception
     * 根据手机号获取 所有用户ID
     */
    public function GetUidsByPhone($phone,$page_size=100,$cache=true): array
    {
        if(empty($phone)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getUidByPhone($phone);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData       = (array)json_decode($aJson,true);
        if($aJson === false) {
            $tb         = self::tbName();
            $sql        = "SELECT `user_id` FROM {$tb} WHERE `phone`=:phone ORDER BY `user_id` DESC LIMIT {$page_size}";
            $command    = by::dbMaster()->createCommand($sql);
            $command->bindParam(":phone", $phone);

            $info       = $command->queryAll();
            $aData      = empty($info) ? [] : array_column($info,'user_id');

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }
    /**
     * @param $phone
     * @return array
     * @throws Exception
     * 根据手机号获取 用户详情信息
     */
    public function GetInfoByPhone($phone)
    {
        if(empty($phone)) {
            return [];
        }

        $tb         = self::tbName();
        $sql        = "SELECT * FROM {$tb} WHERE `phone`=:phone ORDER BY `user_id` DESC LIMIT 1";
        $command    = by::dbMaster()->createCommand($sql);
        $command->bindParam(":phone", $phone);

        $info       = $command->queryOne();
       
        return $info;
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 判断手机号是否是自己独占
     */
    public function OnlyMeUse($user_id): array
    {
        $phone = $this->GetPhoneByUid($user_id);
        if(empty($phone)) {
            return [false,$phone];
        }

        $uids  = $this->GetUidsByPhone($phone);

        if(count($uids) == 1 && (current($uids)==$user_id)) {
            return [true,$phone];
        }

        return [false,$phone];
    }

    /**
     * @param $user_id
     * @param $phone
     * @return array
     * @throws Exception
     * 用户关联手机号
     * 手机号和账号ID有一对多关系
     */
    public function SaveRelation($user_id,$phone) {

        if (empty($phone)) {
            return [false,"手机号不能为空"];
        }

        if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
            return [false,"手机号格式不正确"];
        }

        $now     = intval(START_TIME);
        $oPhone = $this->GetPhoneByUid($user_id, false);
        $tb      = self::tbName();
        $command = by::dbMaster()->createCommand();
        if(!empty($oPhone)) {
            CUtil::debug('手机号已注册'.'|'.$user_id.'|'.$phone,'same.phone');
            return [false,"手机号已注册"];
        }

        //已经存在的手机号不允许注册（非注销）
        $userData = by::users()->getWxUserByPhone($phone,false);
        if($userData){
            CUtil::debug('当前手机号已有绑定的微信账号'.'|'.$user_id.'|'.$phone,'same.phone');
            return [false,"当前手机号已有绑定的微信账号"];
        }

        $ret = $command->insert($tb,['user_id'=>$user_id,'phone'=>$phone,'ctime'=>$now])->execute();

        //缓存清理
        $this->__delCache($user_id,$oPhone);
        $this->__delCache(0,$phone);

        return [true,$ret];
    }


    /**
     * 更新手机关联
     * @param $userId
     * @param $data
     * @return array
     * @throws Exception
     */
    public function updateRelation($userId,$data): array
    {
        $tb = self::tbName();
        if (empty($userId)) {
            return [false,"用户ID不能为空"];
        }
        $ret = by::dbMaster()->createCommand()->update($tb, $data, ['user_id' => $userId])->execute();
        return [true,$ret];
    }

    /**
     * 根据商城ID更新手机关联
     * @param $mallId
     * @param $data
     * @return array
     * @throws Exception
     */
    public function updateRelationByMallId($mallId,$data,$userIds=[]): array
    {
        $tb = self::tbName();
        if (empty($mallId)) {
            return [false,"商城ID不能为空"];
        }
        //清除原用户手机缓存
        if($userIds){
            foreach ($userIds as $user_id){
                $this->__delCache($user_id,0);
            }
        }
        //更新数据
        $ret = by::dbMaster()->createCommand()->update($tb, $data, ['mall_id' => $mallId])->execute();
        return [true,$ret];
    }


    /**
     * @param string $openudid
     * @param string $iv
     * @param string $encryptedData
     * @return array
     * 解码手机号
     */
    public function auth(string $openudid='', string $iv='', string $encryptedData=''): array
    {
        $unique_key   = CUtil::getAllParams(__FUNCTION__,$openudid);
        list($anti)   = self::ReqAntiConcurrency(0, $unique_key, 1, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }

        $encryptedData          = urldecode($encryptedData);
        $iv                     = urldecode($iv);

        if(empty($openudid) || empty($iv) || empty($encryptedData)) {
            CUtil::json_response(-1,"无效参数");
        }

        //拿到会话密钥
        list($status, $ret)     = WeiXin::factory()->code2accessToken($openudid);
        $session_key            = $status ? $ret['session_key'] ?? "" : "";

        //解码手机号
        list($status, $phone)   = WeiXin::factory()->GetPhone($encryptedData, $iv, $session_key);
        !YII_ENV_PROD && CUtil::debug("{$status}|{$encryptedData}|{$iv}|{$openudid}|{$session_key}|{$phone}","kevin.phone.auth");
        if(!$status){
            CUtil::debug("{$status}|{$encryptedData}|{$iv}|{$openudid}|{$session_key}|{$phone}","kevin.phone.auth");
            return [false,'解码手机号失败'];
        }
        return [true,$phone];
    }


    public function AlipayAuth(string $encryptedData=''): array
    {
        $unique_key   = CUtil::getAllParams(__FUNCTION__,$encryptedData);
        list($anti)   = self::ReqAntiConcurrency(0, $unique_key, 1, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }
        $encryptedData = urldecode($encryptedData);
        $encrypetdArr = json_decode($encryptedData,true);
        if(empty($encrypetdArr)){
            CUtil::debug("{$encryptedData}","err.phone.alipay");
            return [false,"解码手机号失败"];
        }
        $encrypedRes = $encrypetdArr['response'] ?? '';
        if(empty($encrypedRes)){
            CUtil::debug("{$encryptedData}|{$encrypedRes}","err.phone.alipay");
            return [false,"解码手机号失败"];
        }
        $config = CUtil::getConfig('alipay_applet', 'alipay', MAIN_MODULE);
        $aesKey = $config['apiAesKey'] ??'';
        $result = openssl_decrypt(base64_decode($encrypedRes), 'AES-128-CBC', base64_decode($aesKey),OPENSSL_RAW_DATA);
        $v = json_decode($result, true);
        $phone = $v['mobile'] ?? '';
        if(empty($phone)){
            CUtil::debug("{$encrypedRes}|{$result}|{$phone}","err.phone.alipay");
            return [false,"解码手机号失败"];
        }
        return [true,$phone];
    }

    /**
     * 删除用户数据
     * @throws \yii\db\Exception
     */
    public function delDataById($user_id)
    {
        if(empty(CUtil::checkUuid($user_id))){
            return 0;
        }
        $tb         = self::tbName();
        return by::dbMaster() ->createCommand()
            ->delete($tb, "user_id = '{$user_id}'")
            ->execute();
    }


    /**
     * @throws Exception
     */
    public function getPhoneNumsByUsers($users)
    {
        $tb      = self::tbName();
        $users = implode("','",$users);
        $sql = "SELECT COUNT( DISTINCT `phone`) as nums FROM {$tb} WHERE `user_id` in ('{$users}') LIMIT 1";
        CUtil::debug(by::dbMaster()->createCommand($sql)->getRawSql());
        $res = by::dbMaster()->createCommand($sql)->queryOne();
        if(!$res){
            return [false,'手机号码查询错误！'];
        }
        return [true,$res['nums']??-1] ;
    }

    /**
     * 通过user_id获取用户信息
     * @param $user_id
     * @return mixed|string
     * @throws Exception
     */
    public function getDataByUserId($user_id)
    {
        $tb_phone    = self::tbName();
        $t_user_mall = by::usersMall()::tbName();
        $sql         = "SELECT `mall`.`uid`, `mall`.`phone` FROM {$t_user_mall} as `mall` INNER JOIN {$tb_phone} as `phone` ON `mall`.`id` = `phone`.`mall_id` WHERE `phone`.`user_id` =:user_id and `mall`.`is_deleted` = 0";
        $command     = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
        $data        = [
            'uid'   => $command['uid'] ?? '',
            'phone' => $command['phone'] ?? ''

        ];
        return $data;
    }

    /**
     * @param $user_id
     * @return mixed|string
     * @throws Exception
     * 通过user_id获取uid
     */
    public function getUidByUserId($user_id){
        $tb_phone = self::tbName();
        $t_user_mall = by::usersMall()::tbName();
        $sql = "SELECT `mall`.`uid` FROM {$t_user_mall} as `mall` INNER JOIN {$tb_phone} as `phone` ON `mall`.`id` = `phone`.`mall_id` WHERE `phone`.`user_id` =:user_id";
        $command = by::dbMaster()->createCommand($sql,[':user_id'=>$user_id])->queryOne();
        $uid = $command['uid'] ?? '';
        return $uid;
    }

    /**
     * 批量通过user_id获取uid
     * @param array $user_ids
     * @return array
     */
    public function getUidByUserIds(array $user_ids): array
    {
        // 查询字段
        $columns = ['user_id', 'uid'];
        $t_user_mall = by::usersMall()::tbName();
        $data = self::find()
            ->select($columns)
            ->innerJoin($t_user_mall, 't_users_mall.id = t_phone.mall_id')
            ->where(['t_phone.user_id' => $user_ids])
            ->asArray(true)
            ->all();
        return array_column($data, 'uid', 'user_id');
    }


    /**
     * 批量通过uid获取user_id
     * @param array $uids
     * @return array
     */
    public function getUserIdsByUids(array $uids): array
    {
        // 查询字段
        $columns     = ['user_id', 'uid'];
        $t_user_mall = by::usersMall()::tbName();
        $data        = self::find()
            ->select($columns)
            ->innerJoin($t_user_mall, 't_users_mall.id = t_phone.mall_id')
            ->where(['t_users_mall.uid' => $uids])
            ->asArray(true)
            ->all();
        return array_column($data, 'user_id', 'uid');
    }


    /**
     * 根据用户ID获取数据（无缓存）
     * @param array $user_ids
     * @return array
     * @throws Exception
     */
    public function getListByUserIds(array $user_ids): array
    {
        $tb = self::tbName();
        $user_ids = implode(',', $user_ids);
        $sql = "SELECT * FROM {$tb} WHERE `user_id` in ({$user_ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }


    /**
     * @param $mall_id
     * @return array|false|\yii\db\DataReader
     * @throws Exception
     * 通过mall_id获取数据
     */
    public function getInfoByMallId($mall_id)
    {
        if(empty($mall_id)) return [];
        $tb = self::tbName();
        $sql = "SELECT * FROM {$tb} WHERE `mall_id` = :mall_id ORDER BY `id` DESC LIMIT 1";
        return by::dbMaster()->createCommand($sql, [':mall_id' => $mall_id])->queryOne();
    }
    /**
     * 根据Mall ID 获取数据
     * @param int $mall_id
     * @return array
     * @throws Exception
     */
    public function getDataByMallId(int $mall_id): array
    {
        $tb = self::tbName();
        $sql = "SELECT * FROM {$tb} WHERE `mall_id` = :mall_id";
        $item = by::dbMaster()->createCommand($sql)
            ->bindParam(':mall_id', $mall_id)
            ->queryOne();
        if ($item) {
            return $item;
        }
        return [];
    }


    /**
     * @param $mallIds
     * @return array
     * @throws Exception
     * 通过mall_id获取数据
     */
    public function GetInfosByMallIds($mallIds)
    {
        if(empty($mallIds)) return [];
        $tb = self::tbName();
        $mallIdIn = implode(',', array_map(function ($item) {
            return "'$item'";
        }, $mallIds));

        // 首先，找到每个mall_id的最大id
        $sql = "SELECT MAX(`id`) as `max_id` FROM {$tb} WHERE `mall_id` IN ({$mallIdIn}) GROUP BY `mall_id`";
        $maxIds = by::dbMaster()->createCommand($sql)->queryAll();

        if(empty($maxIds)) return [];

        // 然后，根据这些id来获取完整的记录
        $maxIdIn = implode(',', array_map(function ($item) {
            return "'{$item['max_id']}'";
        }, $maxIds));
        $sql = "SELECT * FROM {$tb} WHERE `id` IN ({$maxIdIn})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }
}
