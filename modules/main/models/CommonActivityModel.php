<?php
/**
 * <AUTHOR>
 * @date 31/3/2025 下午 5:57
 */

namespace app\modules\main\models;

use app\models\CUtil;
use Yii;
use yii\db\ActiveRecord;

class CommonActivityModel extends CommModel
{
    public $tb_fields = [
            'id', 'type', 'start_time', 'end_time', 'ctime', 'utime'
    ];

    public static function tableName(): string
    {
        return '`db_dreame`.`t_common_activity`';
    }

    /**
     * 新增活动
     * @param array $data
     * @return CommonActivityModel|null
     */
    public static function createActivity(array $data): ?self
    {
        $activity = new self();
        $activity->setAttributes($data);
        $activity->ctime = time();
        $activity->utime = time();
        return $activity->save() ? $activity : null;
    }

    /**
     * 根据ID查询活动
     * @param int $id
     * @return CommonActivityModel|null
     */
    public static function getActivityById(int $id): ?self
    {
        return self::findOne($id);
    }

    /**
     * 查询符合条件的活动
     * @param array $conditions
     * @param string $orderBy
     * @return CommonActivityModel[]
     */
    public static function getActivities(array $conditions = [], string $orderBy = 'start_time ASC'): array
    {
        return self::find()
                ->where($conditions)
                ->orderBy([$orderBy => SORT_ASC])
                ->all();
    }

    /**
     * 更新活动信息
     * @param int $id
     * @param array $save
     * @return bool
     */
    public static function updateActivity(int $id, array $save): bool
    {
        $activity = static::findOne($id);
        if (!$activity) {
            return false;
        }

        // 额外字段手动赋值（触发修改器）
        foreach ($save as $attribute => $value) {
            $activity->$attribute = $value;
        }

        // 确保更新时间被更新
        $activity->utime = time();

        // 保存数据，并记录错误日志（如果失败）
        if (!$activity->save(false)) {
            CUtil::debug("更新活动失败：" . json_encode($activity->errors), 'err.update_common_activity');
            return false;
        }

        return true;
    }


    /**
     * 删除活动
     * @param int $id
     * @return bool
     */
    public static function deleteActivity(int $id): bool
    {
        $activity = self::findOne($id);
        return $activity ? (bool) $activity->delete() : false;
    }

    /**
     * 批量更新活动
     * @param array $conditions
     * @param array $data
     * @return int 影响行数
     */
    public static function batchUpdate(array $conditions, array $data): int
    {
        $data['utime'] = time(); // 更新时间
        return self::updateAll($data, $conditions);
    }

    /**
     * 批量删除活动
     * @param array $conditions
     * @return int 影响行数
     */
    public static function batchDelete(array $conditions): int
    {
        return self::deleteAll($conditions);
    }
}
