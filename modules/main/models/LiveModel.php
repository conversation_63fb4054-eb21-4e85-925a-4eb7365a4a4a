<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\CommCurl;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;


class LiveModel extends CommModel
{

    const LIMIT = 100;
    const START = 0;
    const EXP = 120;
    const STOP_CACHE = 7200;

    const JAVA_LIVE_DOMAIN = YII_ENV_PROD?'http://127.0.0.1:8181':'http://127.0.0.1:8181';

    const URL = [
      'live_list'=>  '/wx/liveStreamRoom',
    ];

    private function __getListKey(): string
    {
        return AppCRedisKeys::getTxLiveList();
    }

    private function __stopCacheKey():string
    {
        return AppCRedisKeys::stopCache();
    }

    private function __delStopCache(){
        $r_key = $this->__stopCacheKey();
        by::redis('core')->del($r_key);
    }

    private function __delCache()
    {
        $r_key1 = $this->__getListKey();
        by::redis('core')->del($r_key1);
    }

    /**
     * @param $user_id
     * @return array
     * 调用java接口
     */
    public function getLiveListJava($user_id){
        list($status,$data) = $this->__request('live_list',[],'GET');
        if(!$status){
            return [false,$data];
        }
        return [true, $data['room_info'] ?? []];
    }


    public function getLiveList($user_id)
    {
        $redis = by::redis('core');
        $unique_key = __FUNCTION__;
        $user_id = empty($user_id) ? 0 : $user_id;

//        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($user_id, $unique_key, 1, "EX", self::LIMIT);
//        if (!$s) {
//            return [false, "频繁请求，请稍候~"];
//        }
        $s_key = $this->__stopCacheKey();

        $r_key = $this->__getListKey();
        $json = $redis->get($r_key);
        $info = (array)json_decode($json, true);
        $limitList = true;
        if ($json === false) {
//            list($status, $access_token) = WeiXin::factory()->getUniqueAccessToken();
//            if (!$status) {
//                CUtil::debug('获取腾讯直播access_token失败|' . $user_id . '|' . $access_token, 'err.gettxlive');
//                return [false, "频繁请求，请稍候~"];
//            }
//            $url = "https://api.weixin.qq.com/wxa/business/getliveinfo?access_token=" . $access_token;
//            $file_data = json_encode(array('start' => self::START, 'limit' => self::LIMIT), JSON_UNESCAPED_UNICODE);
//            $output = CUtil::curl_post($url, $file_data, null, 20, YII_ENV_DEV ? true : false);
//            $output = (array)json_decode($output, true);
//            $errcode = $output['errcode'] ?? -1;
//            if ($errcode == 0) {
//                $info = $output['room_info'] ?? [];
//                $redis->set($r_key, json_encode($info, JSON_UNESCAPED_UNICODE), ['EX' => empty($info) ? 1 : self::EXP]);
//                $redis->setex($s_key,self::STOP_CACHE,intval(START_TIME));
//            } else {
//                if($errcode == 9410000) return [true,[]];
//                CUtil::debug('获取直播列表失败|' . $user_id . '|' . $access_token . '|' . json_encode($output).'|'.$errcode, 'err.gettxlive');
//                return [false, "频繁请求，请稍候~~"];
//            }

            return [true,[]];
        }

        if ($info && !$limitList) {
            $this->__delCache();
        }

        return [true, $info];
    }


    public function repeatLiveList()
    {
        $redis = by::redis('core');
        $s_key = $this->__stopCacheKey();
        $r_key = $this->__getListKey();
        $json = $redis->get($s_key);
//        $info = [];
//        if($json && (intval(START_TIME)-intval($json)) > intval(self::EXP/2)){
            list($status, $access_token) = WeiXin::factory()->getUniqueAccessToken();
            if (!$status) {
                (YII_ENV_PROD || YII_ENV_UAT) && CUtil::debug('获取腾讯直播access_token失败|' . $access_token, 'err.gettxlive');
                return [false, "频繁请求，请稍候~"];
            }
            $url = "https://api.weixin.qq.com/wxa/business/getliveinfo?access_token=" . $access_token;
            $file_data = json_encode(array('start' => self::START, 'limit' => self::LIMIT), JSON_UNESCAPED_UNICODE);
            $output = CUtil::curl_post($url, $file_data, null, 20, YII_ENV_DEV ? true : false);
            $output = (array)json_decode($output, true);
            $errcode = $output['errcode'] ?? -1;
            if ($errcode == 0) {
                $info = $output['room_info'] ?? [];
                $redis->set($r_key, json_encode($info, JSON_UNESCAPED_UNICODE), ['EX' => empty($info) ? 1 : self::EXP]);
                $redis->setex($s_key,self::STOP_CACHE,intval(START_TIME));
            } else {
                if($errcode == 9410000) return [true,[]];
                CUtil::debug('获取直播列表失败|' . $access_token . '|' . json_encode($output).'|'.$errcode, 'err.gettxlive');
                return [false, "频繁请求，请稍候~~"];
            }
//        }
        return [true, $info];
    }


    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param array $header
     * @param array $arr
     * @param string $type
     * @return array
     * 根据URL转接数据
     */
    private function __request($event, array $body = [], string $method = 'POST', array $header = [], array $arr =[], string $type = 'json'): array
    {
        $url = self::URL[$event] ?? '';
        if(empty($url)){
            return [false, 'event不存在'];
        }
        $url = self::JAVA_LIVE_DOMAIN.$url;

        $header = empty($header) ? [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Expect: "
        ] : $header;

        $type == 'json' && $body =  json_encode($body, 320);
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method,20);

        if (!$status) {
            // CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|data：" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "live_model.{$event}");
            CUtil::setLogMsg(
                "live_model.{$event}",
                $body,
                $ret,
                $header,
                $url,
                $err,
                [],
                $httpCode
            );
            return [false, $err];
        }
        return [$ret['errcode'] === 0, $ret];
    }

}
