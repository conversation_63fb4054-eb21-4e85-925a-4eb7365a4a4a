<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\AppWRedisKeys;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;

class MemberCenterModel extends CommModel
{

    CONST LIMIT = 100;
    CONST APP_FILTER_TYPE_CODE = [
      'invite_buy','invite_reg','share_goods','follow_official_account','watch_live','raffle','checkin','add_wechat'
    ];
    CONST WX_FILTER_TYPE_CODE = [
        'f_pair','start_clean','clean_area','f_set_quick_cmd','f_create_map','share','f_voice_task','accessory_usage','f_clean_log','f_video','f_scheduled','f_map_edit','raffle','checkin' // 扫地机任务
    ];
    CONST ACTIVITY_POINT_REWARD_PERIOD = [
      1.5,2,2,2
    ];
    const ACTIVITY_POINT_REWARD_TIME = YII_ENV_PROD ? [
        [**********, **********],
        [**********, **********],//双11
        [**********, **********],//双12
        [**********, **********],//618活动
    ] : [
        [**********, **********],
        [**********, **********],//双11
        [**********, **********],//双12
        [**********, **********],//618活动
    ];

    public function GetTodayCheckInInKey($user_id,$timeT): string
    {
        return AppWRedisKeys::todayCheckInIn($user_id,$timeT);
    }

    public $expire_time =  YII_ENV_PROD ? 3600 : 60;



    /**
     * @param $function
     * @param $arr
     * @return string
     * 根据方法和查询参数获取对应的会员数据key
     */
    public function __getMemberCenterInfoKey($function,$arr): string
    {
        return AppCRedisKeys::GetMemberCenterInfo($function,$arr);
    }

    /**
     * @param $function
     * @param $arr
     * @return string
     * 删除会员权益缓存
     */
    private function __delMemberCenterInfoKey($function, $arr): string
    {
        $r_key = $this->__getMemberCenterInfoKey($function,$arr);
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $function
     * @param $arr
     * @return array
     * 获取会员权益信息
     */
    public function CenterMessage($function,$arr)
    {

        list($s, $data) = MemberCenter::factory()->run($function, $arr);

        if(!$s) return [$s,$data];//如果查询错误，直接返回空

        $data = $this->__doWellData($function, $arr, $data);

        return [$s, $data];
    }


    /**
     * @param $benefitRuleList
     * @param $arr
     * @return mixed
     * 按照平台过滤benefit
     */
    public function __filterBenefit($benefitRuleList,$arr){
        $platformSource = $arr['platformSource'] ?? 0;
        if(in_array($platformSource,[5,6])){//app平台过滤相关功能
            foreach ($benefitRuleList as $key=>$item){
                $typeCodeP = $item['typeCode'] ?? '';
                if(in_array($typeCodeP,self::APP_FILTER_TYPE_CODE)){
                    unset($benefitRuleList[$key]);
                }
            }
        }

        if(in_array($platformSource,[2,3])){//小程序平台过滤相关功能
            foreach ($benefitRuleList as $key=>$item){
                $typeCodeP = $item['typeCode'] ?? '';
                if(in_array($typeCodeP,self::WX_FILTER_TYPE_CODE)){
                    unset($benefitRuleList[$key]);
                }
            }
        }

        return array_values($benefitRuleList);
    }


    /**
     * @param $userId
     * @param $typeCode
     * @return mixed
     */
    public function getUserRightList($userId,$typeCode)
    {
        // $lockCrmSend     = CUtil::getConfig('lockCrmSend','member',MAIN_MODULE) ?? false;
        // if(!$lockCrmSend){
        //     return 0;
        // }
        $list = $this->__getRightListByData([],'',['user_id'=>$userId],$typeCode);
        $listValue = array_column($list,'value','isMark');
        !YII_ENV_PROD && CUtil::debug(json_encode($list),'coin_type');
        return floatval($listValue[1] ?? 0);
    }


    /**
     * @param array $centerData
     * @param string $level
     * @param array $arr
     * @param string $typeCode
     * @return array
     * 获取会员的权益
     */
    public function __getRightListByData(array $centerData=[], string $level = '', array $arr=[], string $typeCode=''): array
    {
        if (empty($centerData)){
            list($s, $centerData) = MemberCenter::factory()->run('levelBenefit', $arr);
            if (!$s) {
                return [];
            }
        }
        if (empty($level)) {
            //没有level时查询获取对应用户的level
            list($s, $basicInfo) = MemberCenter::factory()->run('basicInfo', $arr);
            $levelInfo = $basicInfo['currentLevelInfo'] ?? [];
            $level     = $levelInfo['level']['level'] ?? 'v1';
        }

        $lastLevel = end($centerData);
        $rightList = [];
        $lastArr   = array_column($lastLevel['benefitRuleList'] ?? [], 'typeCode');
        if ($lastLevel && $lastArr) {
            foreach ($lastArr as $value) {
                $arra = [];
                foreach ($centerData as $center) {
                    $realRightData = $this->__realRightData($value, $center, $level);
                    $realRightData && $arra[] = $realRightData;
                }
                !empty($arra) && $rightList[$value] = $arra;
            }
        }
        if($typeCode){
            return $rightList[$typeCode] ?? [];
        }
        return $rightList;
    }


    /**
     * @param $function
     * @param $arr
     * @param $data
     * @return mixed
     * 处理会员权益信息
     */
    private function __doWellData($function, $arr, $data)
    {
        switch ($function) {
            case 'basicInfo':
            case 'taskList':
            case 'growthCenter':
            case 'checkInIn':
                return $this->$function($arr, $data);
            default:
               return $data;
        }
    }


    public function checkInIn($arr, $data)
    {
        //打卡成功设置用户缓存
        $user_id = $arr['user_id'] ?? 0;
        $timeT = date('Y-m-d');
        if(strlen($user_id)<11 && !empty(intval($user_id))){
            $rkey = $this->GetTodayCheckInInKey($user_id,$timeT);
            by::redis()->set($rkey,1,3600);
        }
        return $data;
    }

    public function growthCenter($arr, $data)
    {
        $allLevelInfo = $data['allLevelInfo'] ?? [];
        if ($arr['single'] ?? ''){
            $data['allBenefitList'] = $this->getUserAllBenefit($allLevelInfo,$arr);
        }
        if ($allLevelInfo) {
            foreach ($allLevelInfo as &$levelData) {
                if (isset($levelData['benefitRuleList'])) {
                    $levelData['benefitRuleList'] = $this->__filterBenefit($levelData['benefitRuleList'], $arr);
                }
            }
            unset($levelData); // 释放最后一个引用
        }
        $data['allLevelInfo'] = $allLevelInfo;
        return $data;
    }


    private function getUserAllBenefit($allLevelInfo,$arr){
        $userLevel = $arr['level'] ?? '';
        if (empty($userLevel)) {
            $userLevel = by::memberCenterModel()->GetUserLevel($arr['user_id'] ?? 0);
        }

        $levelMapping = ['v1', 'v2', 'v3', 'v4', 'v5'];
        $levelIndex = array_search($userLevel, $levelMapping);
        $currentLevelInfo = $allLevelInfo[$levelIndex] ?? [];

        //当前用户拥有的权益
        $currentLevelBenefit = $currentLevelInfo['benefitRuleList'] ?? [];

        //当前等级没有的权益及图标
        $currentNoBenefitRuleList = $currentLevelInfo['noBenefitRuleList'] ?? [];

        //获取最高等级权益数据
        $maxLevelInfo = end($allLevelInfo)['benefitRuleList'] ?? [];

        // 如果当前有 没有的权益
        if ($currentNoBenefitRuleList) {
            $noBenefitRulesMap = array();
            foreach ($currentNoBenefitRuleList as $noBenefit) {
                $noBenefitRulesMap[$noBenefit['code']] = $noBenefit;
            }

            foreach ($maxLevelInfo as &$value) {
                if (isset($noBenefitRulesMap[$value['typeCode']])) {
                    $value['lockedIcon']   = $noBenefitRulesMap[$value['typeCode']]['lockedIcon'] ?? '';
                    $value['isHave']       = false;
                    $currentLevelBenefit[] = $value;
                }
            }
        }
        // 将'isHave'键设置为true以确保所有元素都有'isHave'键
        foreach ($currentLevelBenefit as &$item) {
            if (!isset($item['isHave'])) {
                $item['isHave'] = true;
            }
        }
        return $currentLevelBenefit ?? [];
    }



    public function basicInfo($arr, $data)
    {
        $userId = $arr['user_id'] ?? '';
        if ($userId && $data && is_array($data)) {
            $mallInfo    = by::usersMall()->getMallInfoByUserId($userId);
            $data['uid'] = $mallInfo['uid'] ?? '';
            // 获取用户生日
            $userInfo = by::users()->getOneByUid($userId);
            // 没生日，不弹框
            if ($userInfo && !$userInfo['birthday']) {
                $data['is_show_birth_card'] = false;
                return $data;
            }
            // 有生日，判断是否弹框
            // 1、是否生日当天
            $day = date('md');
            $birthDay = date('md', $userInfo['birthday']);
            $isBirthDay = ($day == $birthDay);
            // 2、24小时内是否弹过
            $r_key = AppCRedisKeys::hasShowBirthCard($userId);
            $redis = by::redis();
            $hasShow = $redis->get($r_key);
            // 3、生日当天，且没有弹过
            $isShowBirthCard = ($isBirthDay && !$hasShow);
            // 弹出状态
            $data['is_show_birth_card'] = $isShowBirthCard;
        }
        return $data;
    }



    /**
     * @param $arr
     * @param $data
     * @return array|mixed
     * 任务列表过滤特定的数据
     */
    public function taskList($arr, $data)
    {
        $platformSource = $arr['platformSource'] ?? 0;

        if(in_array($platformSource,[5,6])){//app平台过滤相关功能
            foreach ($data as $key=>$item){
                $typeCodeP = $item['code'] ?? '';
                if(in_array($typeCodeP,self::APP_FILTER_TYPE_CODE)){
                    unset($data[$key]);
                }
            }
        }

        if(in_array($platformSource,[2,3])){//小程序平台过滤相关功能
            foreach ($data as $key=>$item){
                $typeCodeP = $item['code'] ?? '';
                if(in_array($typeCodeP,self::WX_FILTER_TYPE_CODE)){
                    unset($data[$key]);
                }
            }
        }

        $data = array_values($data);
        return $data;
    }

    /**
     * @param $typeCode
     * @param $right
     * @param $level
     * @return array
     * 获取权益列表
     */
    private function  __realRightData($typeCode,$right,$level=''): array
    {
        $data = [
            'code' => '',
            'name' => '',
            'level' => '',
            'levelBenefitDescri' => '',
            'isMark' => 0,
        ];
        $benefitRuleList = $right['benefitRuleList'] ?? [];
        $levelData = $right['level'] ?? [];
        if (empty($benefitRuleList) || empty($levelData)) return $data;
        $formatData = [];
        if (empty($typeCode)) {
            $formatData = $benefitRuleList[0];
        } else {
            foreach ($benefitRuleList as $key => $value) {
                if ($value['typeCode'] == $typeCode) {
                    $formatData = $value;
                }
            }
        }
        if(empty($formatData['levelBenefitDescri']??'')){
            return [];
        }

        $data['code'] = $formatData['code'] ?? '';
        $data['levelBenefitDescri'] = $formatData['levelBenefitDescri'] ?? '';
        $data['name'] = $levelData['name'] ?? '';
        $data['level'] = $levelData['level'] ?? '';
        $data['isMark'] = ($data['level'] == $level) ? 1 : 0;
        $data['value'] = $this->__getValueByTypeCode($typeCode,$formatData);

        return $data;
    }

    /**
     * @param $typeCode
     * @param $data
     * @return int
     * 获取不同类型的值
     */
    private function __getValueByTypeCode($typeCode,$data)
    {
        switch ($typeCode){
            case 'free_shipping'://包邮特权
                return $data['data']['minNum'] ?? 0;
            case 'expensive_pay'://贵必赔
                return $data['data']['maxNum'] ?? 0;
            case 'point_crash'://积分抵现
                return $data['data']['maxNum'] ?? 0;
            case 'birth_benefits'://积分抵现
                return $data['data']['discount'] ?? 0;
            case 'return_exchange'://退换无忧
                return $data['data']['maxNum'] ?? 0;
            case 'points_double'://积分翻倍
                return $data['data']['pointDoubleFactor'] ?? 0;
            default:
                return 0;
        }
    }


    /**
     * @param $user_id
     * @param $coin
     * @param int $ctime
     * @return int|mixed
     * 获取用户订单获取积分
     */
    public function GetCoinByOrderCoin($user_id, $coin, int $ctime=0)
    {
        if(empty($user_id)||empty($coin)) return $coin;
        $rate = $this->getUserRightList($user_id,'points_double');
        $rate = empty($rate) ? 1: $rate;
        //时间校验
        $activityTime = self::ACTIVITY_POINT_REWARD_TIME;
        if($activityTime && $ctime){
            foreach ($activityTime as $key=>$item){
                if(count($item)==2 && $item[0]<=$ctime && $item[1]>=$ctime){
                    $rate = bcmul($rate,(self::ACTIVITY_POINT_REWARD_PERIOD[$key] ?? 1),4);
                    break;
                }
            }
        }
        !YII_ENV_PROD && CUtil::debug($coin.'|'.$rate.'|'.$user_id,'coin_type');
        return CUtil::uint(round($coin*$rate));
    }


    /**
     * @param $user_id
     * @return mixed|string
     * 获取用户等级
     */
    public function GetUserLevel($user_id)
    {
        if(empty($user_id)) return 'v1';
        list($s, $data) = MemberCenter::factory()->run('basicInfo', ['user_id'=>$user_id]);
        return $data['currentLevelInfo']['level']['level'] ?? 'v1';
    }

    /**
     * 获取积分使用的倍率
     * @param int $user_id
     * @return float
     * @throws \yii\db\Exception
     */
    public function getPointCrash(int $user_id): float
    {
        // 获取积分使用倍率
        return by::memberCenterModel()->getUserRightList($user_id, 'point_crash');

        // 获取员工信息
        $uid               = by::Phone()->getUidByUserId($user_id);
        $userEmployeeModel = byNew::UserEmployeeModel();
        $employee          = $userEmployeeModel->getEmployeeInfo($uid);

        // 校验是否为微笑大使，微笑大使积分最低倍率为 30
        $minPointRate = 30;
        if ($employee && $employee['employee_status'] == $userEmployeeModel::EMPLOYEE_STATUS['NORMAL'] && $pointRate < $minPointRate) {
            return $minPointRate;
        }

        return $pointRate;
    }
}
