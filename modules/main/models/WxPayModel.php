<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/6/11
 * Time: 16:41
 * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=7_4&index=3 //小程序支付
 */
namespace app\modules\main\models;

use app\components\AdvAscribe;
use app\components\Crm;
use app\components\ErpNew;
use app\components\AppCRedisKeys;
use app\components\PointCenter;
use app\components\EventMsg;
use app\jobs\SyncPointGrowJob;
use app\jobs\UserShopMoneyJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\GroupPurchaseService;
use app\modules\goods\models\OmainModel;
use app\modules\main\models\pay\PayModel;
use yii\db\Exception;

class WxPayModel extends CommModel
{


//新的商户号
//微信支付：
//服务商商户的APPID                                         wx7042d29dafd01227
//服务商商户的商户号                                         1636516125
//微信商户平台设置的秘钥KEY                            srC3WVMIerkulvNUCed5dsnEH4F9Fe7U

    CONST ENCODE_SALT = 'a@#*!*&^$@#$!!@!!KSJ!!';

    CONST APP_ID = YII_ENV_PROD ? 'wx7042d29dafd01227' : 'wx7042d29dafd01227';//服务商商户的APPID

    CONST APP_PAY_APP_ID = YII_ENV_PROD ? 'wx702f42edc7a11f89' : "wx702f42edc7a11f89";//服务商商户的APPID（微信APP支付）

    CONST MCH_ID = YII_ENV_PROD ? '1636516125' : '1636516125';//服务商商户的商户号

    //微信商户平台设置的密钥key
    CONST WX_MCH_ID_KEY = YII_ENV_PROD ? 'srC3WVMIerkulvNUCed5dsnEH4F9Fe7U' : "srC3WVMIerkulvNUCed5dsnEH4F9Fe7U";

    //统一下单地址
    CONST UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";

    //查询订单地址
    CONST WX_ORDER_QUERY = 'https://api.mch.weixin.qq.com/pay/orderquery';

    //关闭订单
    CONST WX_CLOSE_ORDER_URL = 'https://api.mch.weixin.qq.com/pay/closeorder';

    //退款
    CONST WX_REFUND_ORDER_URL = 'https://api.mch.weixin.qq.com/secapi/pay/refund';

    //证书路径
    CONST PEM_PATH          = YII_ENV_PROD ? "prod" : "test";
    CONST SSL_CERT_PATH     = WEB_PATH. '/../modules/back/config/'.self::PEM_PATH.'/wx_dreame_information_tech_cert.pem';
    CONST SSL_KEY_PATH      = WEB_PATH. '/../modules/back/config/'.self::PEM_PATH.'/wx_dreame_information_tech_key.pem';

    //下单来源
    const SOURCE = [
        'MALL'          => 1,
        'PLUMBING'      => 2,
        'DEPOSIT'       => 3,
        'POINTS'        => 4,//积分商城
        'BUY_AFTER_PAY' => 5,//先试后买商品
    ];

    // 支付回调
    private $notify_url;

    // 退款回调
    private $refund_notify_url;

    public function __construct($config = [])
    {
        parent::__construct($config);

        // 回调地址
        $this->notify_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/notify';
        $this->refund_notify_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/refund-notify';
    }

    /**
     * @param array $aData
     * @return string
     * 统一签名
     */
    protected function _getSign($aData = [])
    {
        ksort($aData);
        $tmp = [];
        foreach ($aData as $key => $value) {
            !empty($value) && $tmp[] = "{$key}={$value}";
        }

        $key = $this::WX_MCH_ID_KEY;
        $stringA = implode("&", $tmp);
        $stringSignTemp = "{$stringA}&key={$key}";

        $sign = strtoupper(md5($stringSignTemp));

        return $sign;
    }

    /**
     * @param $price
     * @return int
     * 货币单位转换
     * 配置文件以元为单位 微信支付以分为单位
     */
    protected function _totalFee($price)
    {
        $price = bcmul($price,100,2);
        return CUtil::uint($price);
    }

    public function GetWxPayKey($order_no): string
    {
        return AppCRedisKeys::getWxPayStatus($order_no);
    }

    /**
     * @param $order_no
     * @param int $type
     * @param int $pay_type 支付类型 1微信JSAPI、3微信APP
     * @return array
     * 查询微信订单
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_2
     */
    public function wxOrderQuery($order_no, int $type = 0, int $pay_type = 1)
    {
//        $orderKey = $this->GetWxPayKey($order_no);
//        $redis = by::redis();
//        list($s) = by::redis()->get($orderKey);
//        if($s){
//            return [true, 'ok'];
//        }

        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $query['appid'] = self::APP_PAY_APP_ID;
        } else {
            $config = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
            $query['appid'] = $config['appId'];
        }
        $query['mch_id'] = self::MCH_ID;

        if ($type == 0) {
            $query['transaction_id']    = $order_no;
        } else {
            $query['out_trade_no']      = $this->tagOrderNo($order_no, $pay_type);
        }

        $query['nonce_str'] = CUtil::createVerifyCode(10, 1);
        $query['sign'] = $this->_getSign($query);
        $xml = CUtil::arrayToXml($query);
        $response = CUtil::curl_post($this::WX_ORDER_QUERY, $xml, null, 10, true, 'IPV4');

        // !YII_ENV_PROD && CUtil::debug(json_encode($query) . "|" . $response, 'wxp_wx_query');
        !YII_ENV_PROD && CUtil::setLogMsg(
            "wxp_wx_query",
            $query,
            $response,
            [],
            $this::WX_ORDER_QUERY,
            '',
            200
        );

        $response = CUtil::xmlToArray($response);

        if ($response['return_code'] != "SUCCESS" || $response['result_code'] != 'SUCCESS' || $response['trade_state'] != 'SUCCESS') {
            return [false, $response['return_msg'] ?? '支付失败'];
        }

        return [true, $response];
    }

    /**
     * @param $sslCertPath
     * @param $sslKeyPath
     * 设置证书路径
     */
    public function GetSSLCertPath(&$sslCertPath, &$sslKeyPath)
    {
        $sslCertPath = self::SSL_CERT_PATH;
        $sslKeyPath = self::SSL_KEY_PATH;
    }

    /**
     * 统一下单
     * @param $user_id
     * @param $api
     * @param $order_no
     * @param $real_price
     * @param array $arr
     * @param array $attach
     * @param int $pay_type 支付类型：1微信JSAPI、3微信APP
     * @return array
     * @throws Exception 统一下单
     * https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_1
     */
    public function unifiedOrder($user_id, $api, $order_no, $real_price, array $arr, array $attach = [], int $pay_type = 1): array
    {
        list($status,$security_key) = $this::getApiKey($api);
        if($status == -1) {
            return [false,'无效的Api信息'];
        }

        $commPay = new CommPayModel();
        list($s,$time_expire) = $commPay->GetPayExpireTime($arr,7100);
        if(!$s){
            return [$s,$time_expire];
        }

        $time_expire    = date("YmdHis", $time_expire);

        //获取用户openid
        $uinfo = by::users()->getUserMainInfo($user_id);
        if (empty($uinfo)) {
            return [false,'用户信息错误'];
        }
        $openid = $uinfo['openudid'];

        $config                    = CUtil::getConfig('weixin','common',MAIN_MODULE);
        $appid                     = $config['appId'] ?? "";
        $trade_type                = 'JSAPI';
        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $appid = self::APP_PAY_APP_ID;
            $trade_type = 'APP';
            $openid = '';
        }

        $attach['api']             = $api;
        $attach['user_id']         = $user_id;
        $attach['sign']            = $this::getSign($attach, $security_key, $this::ENCODE_SALT);//自身签名

        $aData['appid']            = $appid;
        $aData['mch_id']           = $this::MCH_ID;
        $aData['device_info']      = 'WEB';
        $aData['nonce_str']        = CUtil::createVerifyCode(10,1);
        $aData['body']             = $arr['body'] ?? '';
        $aData['out_trade_no']     = $this->tagOrderNo($order_no, $pay_type); //商户订单
        $aData['fee_type']         = 'CNY'; //
        $aData['total_fee']        = CUtil::uint($real_price);
        $aData['spbill_create_ip'] = CUtil::get_client_ip();
        $aData['time_expire']      = $time_expire;
        $aData['notify_url']       = $this->notify_url;
        $aData['trade_type']       = $trade_type;
        $aData['openid']           = $openid;
        $aData['attach']           = json_encode($attach);//用于微信透传给服务器 	String(127)

        $aData['sign'] = $this->_getSign($aData);

        $xml           = CUtil::arrayToXml($aData);

        $response      = CUtil::curl_post($this::UNIFIED_ORDER_URL,$xml,null,10,true, 'IPV4');
        $response      = CUtil::xmlToArray($response);

        // !YII_ENV_PROD && CUtil::debug(json_encode($aData)."|".json_encode($response),'unifiedorder');
        !YII_ENV_PROD && CUtil::setLogMsg(
            "unifiedorder",
            $aData,
            $response,
            [],
            $this::UNIFIED_ORDER_URL,
            ''
        );

        if ($response['return_code'] != 'SUCCESS') {
            // CUtil::debug('下单失败'.json_encode($aData)."|".json_encode($response),'err.unifiedorder');
            CUtil::setLogMsg(
                "err.unifiedorder",
                $aData,
                $response,
                [],
                $this::UNIFIED_ORDER_URL,
                '下单失败'
            );
            return [false,"下单失败"];
        }

        if ($response['result_code'] != 'SUCCESS') {
            // CUtil::debug('下单失败~'.json_encode($aData)."|".json_encode($response),'err.unifiedorder');
            CUtil::setLogMsg(
                "err.unifiedorder",
                $aData,
                $response,
                [],
                $this::UNIFIED_ORDER_URL,
                '下单失败'
            );
            return [false,"下单失败~"];
        }

        //组装数据供客户端拉起微信支付
        //https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=7_7&index=3
        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $signParams = [
                'appid'     => $response['appid'],
                'partnerid' => $response['mch_id'],
                'prepayid'  => $response['prepay_id'],
                'package'   => "Sign=WXPay",
                'noncestr'  => CUtil::createVerifyCode(10, 1),
                'timestamp' => time()
            ];
            $sign = $this->_getSign($signParams);

            // 返回信息
            $PayReq = [
                'timeStamp' => $signParams['timestamp'],
                'nonceStr'  => $signParams['noncestr'],
                'package'   => 'Sign=WXPay',
                'signType'  => "",
                'paySign'   => $sign,
            ];
        } else {
            $signParams = [
                'appId'     => $response['appid'],
                'timeStamp' => (string)START_TIME,
                'nonceStr'  => CUtil::createVerifyCode(10, 1),
                'package'   => "prepay_id={$response['prepay_id']}",
                'signType'  => "MD5",
            ];
            $sign = $this->_getSign($signParams);

            // 返回信息
            $PayReq = [
                'timeStamp' => $signParams['timeStamp'],
                'nonceStr'  => $signParams['nonceStr'],
                'package'   => "prepay_id={$response['prepay_id']}",
                'signType'  => "MD5",
                'paySign'   => $sign,
            ];
        }

        // 公共返回值
        $PayReq['prepay_id'] = $response['prepay_id'];
        $PayReq['order_no'] = $order_no;
        $PayReq['pay_type'] = $pay_type;

        return [true, $PayReq];

    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param bool $r_freight
     * @param int $source
     * @param int $pay_type
     * @return array
     * @throws Exception
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_4
     * 退款
     */
    public function  refund($user_id, $order_no, $refund_no, bool $r_freight = false, int $source = self::SOURCE['MALL'], int $pay_type = 1): array
    {
        // 订单创建时间，为了避免订本期开发之前创建的订单，在本期开发之后退款，导致支付订单号不一致
        $order_create_time = 0;
        switch ($source) {
            case self::SOURCE['MALL'] :
                $mOgoods    = by::Ogoods();
                $o_info     = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
                $r_goods    = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
                $o_goods    = $mOgoods->GetListByOrderNo($user_id, $order_no);
                $og_ids     = array_column($r_goods, 'og_id');
                $refund_fee = 0;

                foreach($o_goods as $val) {
                    if (in_array($val['id'], $og_ids)) {
                        $refund_fee = bcadd($refund_fee, $val['price']);
                    }
                }

                if ($r_freight) {
                    $refund_fee = bcadd($refund_fee, $o_info['fprice']);
                }

                $total_fee = bcadd($o_info['price'], $o_info['fprice']);

                $order_create_time = $o_info['ctime'];

                break;
            case self::SOURCE['PLUMBING'] :
                $order_info = by::plumbingOrder()->getInfoByOrderNo($order_no);
                $total_fee  = $refund_fee = by::Gtype0()->totalFee($order_info['price'] ?? 0);

                $order_create_time = $order_info['ctime'];
                break;
            case self::SOURCE['DEPOSIT'] :
                $order_info = by::Odeposit()->getInfoByDepositOrderNo($user_id,$order_no);
                $total_fee  = $refund_fee = by::Gtype0()->totalFee($order_info['price'] ?? 0);

                $order_create_time = $order_info['ctime'];
                break;

            default :

                break;
        }


        $config                 = CUtil::getConfig('weixin','common',MAIN_MODULE);
        $appid = $config['appId'];
        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $appid = self::APP_PAY_APP_ID;
        }

        $aData['appid']         = $appid;
        $aData['mch_id']        = $this::MCH_ID;
        $aData['nonce_str']     = CUtil::createVerifyCode(10,1);
        $aData['out_trade_no']  = $this->tagOrderNo($order_no, $pay_type, $order_create_time);
        $aData['out_refund_no'] = $refund_no; //商户系统内部的退款单号
        $aData['total_fee']     = $total_fee ?? 0;
        $aData['refund_fee']    = $refund_fee ?? 0;
        $aData['notify_url']    = $this->refund_notify_url;
        $aData['sign']          = $this->_getSign($aData);

        $xml           = CUtil::arrayToXml($aData);

        $response      = CUtil::curl_post($this::WX_REFUND_ORDER_URL,$xml,null,10,true,'IPV4','wx');

        $response      = CUtil::xmlToArray($response);

        if($response['return_code'] != 'SUCCESS') {
            // CUtil::debug("{$user_id}|".var_export($response, true).var_export($aData,true),'err.refund');
            CUtil::setLogMsg(
                "err.refund",
                $aData,
                $response,
                [],
                $this::WX_REFUND_ORDER_URL,
                '退款失败'
            );
            return [false, $response['return_msg'] ?? '退款失败(1)'];
        }

        if($response['result_code'] != 'SUCCESS') {
            // CUtil::debug("{$user_id}|".var_export($response, true).var_export($aData,true),'err.refund');
            CUtil::setLogMsg(
                "err.refund",
                $aData,
                $response,
                [],
                $this::WX_REFUND_ORDER_URL,
                '退款失败'
            );
            return [false, $response['err_code_des'] ?? '退款失败(2).'];
        }

        return [true,'ok'];
    }

    /**
     * 关闭交易
     * @param $order_no
     * @param int $pay_type
     * @return array
     */
    public function  close($order_no, int $pay_type = 1): array
    {

        $config = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
        $appid = $config['appId'];
        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $appid = self::APP_PAY_APP_ID;
        }

        $aData['appid']         = $appid;
        $aData['mch_id']        = $this::MCH_ID;
        $aData['nonce_str']     = CUtil::createVerifyCode(10,1);
        $aData['out_trade_no']  = $this->tagOrderNo($order_no, $pay_type);
        $aData['sign']          = $this->_getSign($aData);

        $xml           = CUtil::arrayToXml($aData);

        $response      = CUtil::curl_post($this::WX_CLOSE_ORDER_URL,$xml,null,10,true,'IPV4','wx');

        $response      = CUtil::xmlToArray($response);

        if($response['return_code'] != 'SUCCESS') {
            // CUtil::debug(var_export($response, true).var_export($aData,true),'err.close');
            CUtil::setLogMsg(
                "err.close",
                $aData,
                $response,
                [],
                $this::WX_CLOSE_ORDER_URL,
                '关闭失败'
            );
            return [false, $response['return_msg'] ?? '关闭失败(1)'];
        }

        if($response['result_code'] != 'SUCCESS' && $response['err_code'] != 'ORDERPAID') {
            // CUtil::debug(var_export($response, true).var_export($aData,true),'err.close');
            CUtil::setLogMsg(
                "err.close",
                $aData,
                $response,
                [],
                $this::WX_CLOSE_ORDER_URL,
                '关闭失败'
            );
            return [false, $response['err_code_des'] ?? '关闭失败(2).'];
        }

        return [true,'ok'];
    }

    /**
     * @param array $notify_data
     * @return array
     * @throws Exception
     * 小程序微信支付结果通用通知
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_7&index=8
     */
    public function notify($notify_data = [])
    {
        if ($notify_data['return_code'] != 'SUCCESS' || $notify_data['result_code'] != 'SUCCESS') {
            CUtil::debug("未付款:" . json_encode($notify_data), 'err.n.pay');
            return [false, '未付款'];
        }

        $attachJson     = $notify_data['attach'] ?? "";
        $attach         = json_decode($attachJson, true);
        $api            = $attach['api'] ?? "";
        $user_id        = $attach['user_id'] ?? 0;
        $source         = $attach['source']  ?? self::SOURCE['MALL'];
        $sign           = $attach['sign']    ?? "";
        unset($attach['sign']);

        $order_no       = $this->unTagOrderNo($notify_data['out_trade_no'] ?? "");

        list($status, $security_key) = $this::getApiKey($api);
        if ($status == -1) {
            return [false, '无效的订单Api信息'];
        }

        if (empty($user_id) || empty($order_no) || empty($sign)) {
            CUtil::debug("参数缺失:" . json_encode($notify_data), 'err.n.pay');
            return [false, '参数缺失'];
        }

        $verify = $this::getSign($attach, $security_key, $this::ENCODE_SALT);
        if ($verify !== $sign) {
            CUtil::debug("签名检测错误:" . json_encode($notify_data), 'err.n.pay');
            return [false, '无效的支付订单'];
        }

         //####################支付成功订单通知，设置redis KEY
         $redis = by::redis();
         $orderKey = $this->GetWxPayKey($order_no);
         $redis->set($orderKey,$order_no,1800);

        //####################进行回调逻辑判断
        list($status, $msg) = $this->__orderNotify($user_id, $order_no, $notify_data, $source);
        if (!$status) {
            return [false, $msg];
        }


        return [true, 'ok'];
    }

    /**
     * @param array $notify_data
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 小程序微信退款结果通知
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_16&index=10
     */
    public function refundNotify($notify_data = [])
    {
        $return_code = $notify_data['return_code'] ?? '';
        if ($return_code != 'SUCCESS') {
            CUtil::debug("退款失败:" . json_encode($notify_data), 'err.nr.pay');
            return [false, $notify_data['return_msg'] ?? '退款失败'];
        }

        $req_info = $notify_data['req_info'] ?? '';
        if (empty($req_info)) {
            CUtil::debug("校验失败:" . json_encode($notify_data), 'err.nr.pay');
            return [false, '校验失败'];
        }

        $decrypt = base64_decode($req_info, true);
        $data    = openssl_decrypt($decrypt , 'aes-256-ecb', md5(self::WX_MCH_ID_KEY), OPENSSL_RAW_DATA);
        $data    = CUtil::xmlToArray($data);
        if (empty($data)) {
            CUtil::debug('解码失败:'.json_encode($notify_data), 'err.nr.pay');
            return [false, '解码失败'];
        }

        $order_no  = $this->unTagOrderNo($data['out_trade_no'] ?? "");
        $refund_no = $data['out_refund_no'] ?? "";

        //todo 订单状态检测
        $mOrefundMain    = by::OrefundMain();
        $plumbing_refund = by::plumbingRefund();
        $d_refund = by::OrefundDepositMain();
        $rm_info         = $mOrefundMain->GetInfoByRefundNo($refund_no);
        $pr_info         = $plumbing_refund->getInfoByRefundNo($refund_no);
        $d_info = $d_refund->GetInfoByRefundNo($refund_no);

        !YII_ENV_PROD && CUtil::debug(json_encode($rm_info).json_encode($pr_info).json_encode($d_info), 'info.nr.pay');
        if (empty($rm_info) && empty($pr_info) && empty($d_info)) {
            CUtil::debug("退款订单不存在:{$refund_no}", 'err.nr.pay');
            return [false, '退款订单不存在'];
        }

        if (!empty($rm_info)) {
            $r_info = by::Orefund()->GetInfoByRefundNo($rm_info['user_id'], $refund_no);

            if(intval($r_info['status']) == $mOrefundMain::STATUS['SUCCESS']){
                CUtil::debug("退款已完成:" . json_encode($r_info), 'warn.nr.pay');
                return [true, '退款已完成'];
            }
            if ($r_info['status'] != $mOrefundMain::STATUS['P_PASS']) {
                CUtil::debug("无效订单:" . json_encode($r_info), 'warn.nr.pay');
                return [false, '无效订单'];
            }

            //订单表数据
            $o_info = by::Ouser()->GetInfoByOrderId($r_info['user_id'], $order_no);

            //订单商品表数据
            $oGoods = by::Ogoods()->GetListByOrderNo($r_info['user_id'], $order_no);

            //退款商品表数据
            $rGoods = by::Orgoods()->GetListByRefundNo($r_info['user_id'], $refund_no, $order_no);
            $ids    = array_column($rGoods, 'og_id');

            //todo 退款提醒
            EventMsg::factory()->run('orderMsgSend', ['event' => 'refund', 'order_no' => $order_no, 'refund_no'=>$refund_no]);

        } elseif(!empty($d_info)) {
            $dr_info = by::OrefundDeposit()->GetInfoByRefundNo($d_info['user_id'], $refund_no);
            !YII_ENV_PROD && CUtil::debug(json_encode($dr_info), 'info.nr.pay');
            if($dr_info['status'] == $d_refund::STATUS['SUCCESS']){
                return [true, 'OK'];
            }
            if($dr_info['status'] != $d_refund::STATUS['P_PASS']){
                CUtil::debug("无效订单:" . json_encode($dr_info), 'warn.deposit.pay');
                return [false, '无效订单'];
            }
            //订单表数据
            $od_info = by::Odeposit()->getInfoByDepositOrderNo($dr_info['user_id'], $order_no);

        } else{
            !YII_ENV_PROD && CUtil::debug(json_encode($pr_info), 'info.nr.pay');
            if ($pr_info['status'] != by::plumbingOrder()::REFUND_STATUS['AUDIT_PASS']) {
                CUtil::debug("无效订单:" . json_encode($pr_info), 'plumbing-pay-refund.warn');

                return [false, '无效订单'];
            }
        }


        $trans = by::dbMaster()->beginTransaction();
        if (!empty($rm_info)) {
            try {
                //回退销量
                foreach($oGoods as $val) {
                    if (in_array($val['id'], $ids)) {
                        //判断商品类型
                        $goodsType = $val['goods_type'] ?? '';
                        $goodsSource    = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                            ? by::GoodsStockModel()::SOURCE['WARES']
                            : by::GoodsStockModel()::SOURCE['MAIN'];
                        by::GoodsStockModel()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'SALE', false, $goodsSource);
                    }

                        $giniId = $val['gini_id'] ?? 0;
                        if ($giniId) {
                            list($sg, $mg) = by::Gini()->UpdateStock($giniId, $val['num'], 'SALE', false);
                            if (!$sg) {
                                throw new \Exception($mg);
                            }
                        }

                }

                //退优惠券
                $r_count = $mOrefundMain->getCountByOrderNo($r_info['user_id'], $order_no);
                if(count($oGoods) == (count($rGoods)+$r_count) && !empty($o_info['coupon_id'])){
                    list($s,$m) = by::userCard()->UnLockCard($r_info['user_id'],$o_info['coupon_id'],$trans);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                }

                //退消费券
                if(count($oGoods) == (count($rGoods)+$r_count) && !empty($o_info['consume_id'])){
                    list($s,$m) = by::userCard()->UnLockCard($r_info['user_id'],$o_info['consume_id'],$trans);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                }

                //更改退款申请表为退款成功
                $next_st     = $mOrefundMain::STATUS['SUCCESS'];
                $save        = [
                    'rtime' => strtotime($data['success_time']),
                    'price' => $data['refund_fee'],
                ];
                list($s, $m) = $mOrefundMain->SyncInfo($r_info['user_id'],$refund_no, $next_st, $save);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                // //todo 订单同步crm
                // Crm::factory()->push($r_info['user_id'],'order',['user_id'=>$r_info['user_id'],'order_no'=>$order_no]);
                // Crm::factory()->push($r_info['user_id'],'orderLine',['user_id'=>$r_info['user_id'],'order_no'=>$order_no]);
                //
                // //todo 退款crm
                // Crm::factory()->push($r_info['user_id'],'refund',['user_id'=>$r_info['user_id'],'refund_no'=>$refund_no]);
                // Crm::factory()->push($r_info['user_id'],'refundLine',['user_id'=>$r_info['user_id'],'refund_no'=>$refund_no]);
                PointCenter::factory()->refundPush($r_info['user_id'], $refund_no);

                //todo oms退款推送
                by::OrefundMain()->refundPushOms($r_info['m_type']??0,$r_info['user_id'],$refund_no,$order_no);

                // 团购订单退款消息推送
                GroupPurchaseService::getInstance()->sendRefundMessage($order_no);

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'err.nr.pay');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        }elseif(!empty($d_info)){
            try {
                //回退销量
                if(isset($od_info) && isset($od_info['gid']) && $od_info['gid']){
                    by::Gprestock()->UpdateStock($od_info['gid'], $od_info['sid']??0, $od_info['num']??1, 'SALE',false);
                }

                //更改退款申请表为退款成功
                $next_st     = $d_refund::STATUS['SUCCESS'];
                $save        = [
                    'rtime' => strtotime($data['success_time']),
                    'price' => $data['refund_fee'],
                ];
                list($s, $m) = $d_refund->SyncInfo($d_info['user_id'],$refund_no, $next_st, $save);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'err.deposit.pay');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        } else {
            try {
                //更改退款申请表为退款成功
                $next_st     = by::plumbingOrder()::REFUND_STATUS['REFUND_SUCCESS'];
                $update_data = [
                    'utime' => time(),
                    'rtime' => strtotime($data['success_time']),
                    'price' => $data['refund_fee']
                ];

                list($s, $m) = $plumbing_refund->SyncInfo($pr_info['user_id'], $refund_no, $order_no, $next_st, $update_data);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'plumbing-pay-refund.err');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        }
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $notify_data
     * @param $source
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 订单回调
     */
    private function __orderNotify($user_id, $order_no, $notify_data, $source): array
    {
        switch ($source) {
            case self::SOURCE['MALL']:
            case self::SOURCE['POINTS']:
                $mOuser = by::Ouser();
                $mOmain = by::Omain();
                //todo 订单状态检测
                $order  = $mOuser->CommPackageInfo($user_id, $order_no, false, false, false, false,true,false,false);
                if (empty($order)) {
                    CUtil::debug("订单不存在|{$user_id}|{$order_no}", 'err.n.pay');
                    return [false, '订单不存在'];
                }

                if ($order['status'] == $mOmain::ORDER_STATUS['WAIT_SEND']) {
                    CUtil::debug("订单状态已更新:" . json_encode($order), 'err.n.pay');
                    return [true, 'ok'];
                }

                if ($order['status'] != $mOmain::ORDER_STATUS['WAIT_PAY']) {
                    CUtil::debug("无效订单:" . json_encode($order), 'warn.n.pay');
                    return [false, '无效订单'];
                }

                $pay_price  = bcadd($order['price'], $order['fprice'], 2);
                $pay_price = $this->_totalFee($pay_price);
                if (bccomp($pay_price, $notify_data['total_fee'], 2) != 0) {
                    CUtil::debug("支付金额与商品实际金额不一致:" . json_encode($notify_data), 'err.n.pay');
                    return [false, '支付金额与商品实际金额不一致'];
                }

                break;
            case self::SOURCE['PLUMBING']:
                $order = by::plumbingOrder()->getInfoByOrderNo($order_no, false, true);
                if (empty($order)) {
                    CUtil::debug("工单不存在|{$user_id}|{$order_no}", 'plumbing-pay.err');
                    return [false, '工单不存在'];
                }

                if ($order['status'] == by::plumbingOrder()::STATUS['WAIT_SERVICE']) {
                    CUtil::debug("工单状态已更新:" . json_encode($order), 'plumbing-pay.err');
                    return [true, 'ok'];
                }

                if ($order['status'] != by::plumbingOrder()::STATUS['WAIT_PAY']) {
                    CUtil::debug("无效工单:" . json_encode($order), 'plumbing-pay.err');
                    return [false, '无效工单'];
                }

                $pay_price = $this->_totalFee($order['price'] ?? 0);
                if (bccomp($pay_price, $notify_data['total_fee'], 2) != 0) {
                    CUtil::debug("支付金额与实际金额不一致:" . json_encode($notify_data), 'plumbing-pay.err');
                    return [false, '支付金额与实际金额不一致'];
                }

                break;

            case self::SOURCE['DEPOSIT']:
                $order = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
                if (empty($order)) {
                    CUtil::debug("定金订单不存在|{$user_id}|{$order_no}", 'deposit-pay.err');
                    return [false, '定金订单不存在'];
                }

                if ($order['status'] == by::Odeposit()::STATUS['WAIT_SEND']) {
                    CUtil::debug("定金订单状态已更新:" . json_encode($order), 'deposit-pay.err');
                    return [true, 'ok'];
                }

                if ($order['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                    CUtil::debug("无效订单:" . json_encode($order), 'deposit-pay.warn');
                    return [false, '无效订单'];
                }

                $pay_price = $this->_totalFee($order['price'] ?? 0);
                if (bccomp($pay_price, $notify_data['total_fee'], 2) != 0) {
                    CUtil::debug("支付金额与实际金额不一致:" . json_encode($notify_data), 'deposit-pay.err');
                    return [false, '支付金额与实际金额不一致'];
                }

                break;
            default :
                return [false, '订单来源不合法'];
        }

        return $this->afterPay($user_id, $order_no, $notify_data, $order, $source);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $notify_data
     * @param $order
     * @param $source
     * @return array
     * @throws Exception
     * @throws \Exception
     * 付款后操作
     */
    public function afterPay($user_id, $order_no, $notify_data, $order, $source): array
    {
        // 此处是微信支付后的回调，如果交易流水存的是支付宝的，则支付类型改为微信APP支付。
        $opayModel = by::model('OPayModel', 'goods');
        $pay = $opayModel->GetOneInfo($order_no);
        $pay_type = $pay['pay_type'] ?? OmainModel::PAY_BY_WX;

        if ($pay_type == OmainModel::PAY_BY_ALIPAY) {
            // 支付类型
            $pay_type = (($notify_data['trade_type'] ?? "") == 'APP') ? OmainModel::PAY_BY_WX_APP : OmainModel::PAY_BY_WX;
            $opayModel->SaveLog($order_no, [
                'pay_type'  => $pay_type,
                'prepay_id' => 'fake-' . ($notify_data['transaction_id'] ?? '')
            ]);
            CUtil::debug("回调与支付方式不一致:" . json_encode($notify_data), 'wx-pay');
        }

        return by::BasePayModel()->afterPay($user_id, $order_no, $notify_data, $order, $source, $pay_type);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $api
     * @return array
     * @throws Exception
     * @throws \RedisException
     * https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_12&index=2
     * 未付款订单付款
     */
    public function AgainPay($user_id, $order_no, $api, $attach = ['source' => self::SOURCE['MALL']], $order_type = '', $pay_type = 1)
    {
        if (empty($user_id) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //是否需要重新创建订单
        $needReunified = 0;
        $endPayment = 0;


        if ($order_type == by::Odeposit()::TYPE['DEPOSIT']){
            $oInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Odeposit()::STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }

            $oeInfo = by::OdepositE()->GetInfoByOrderId($user_id,$order_no);
            $cfg = $oeInfo['cfg'] ?? [];


            //定金订单
            $endPayment = $cfg['presale_time'] ?? 0;
            $needReunified = 1;

        }else{
            $oInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
            if(isset($oInfo['deposit_order_no']) && $oInfo['deposit_order_no']){
                $now = intval(START_TIME);
                $oInfo['ctime'] = $now;
                $oMainInfo = by::Omain()->getInfoByOrderNo($user_id,$order_no);
                //尾款订单校验库存
                $commPay = new CommPayModel();
                list($s,$msg) = $commPay->CheckTailOrderStock($user_id,$oInfo['deposit_order_no']);
                if(!$s){
                    return [$s,$msg];
                }

                //尾款订单
                $endPayment = $oMainInfo['end_payment'] ?? 0;
                $needReunified = 2;
            }

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Omain()::ORDER_STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Omain()::ORDER_STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }
        }

        //查微信订单是否已付款
        list($s) = $this->wxOrderQuery($order_no, 1, $pay_type);
        if ($s) {
            return [false, '该订单已支付'];
        }

        //付款流水
        $aOpay      = by::model('OPayModel','goods')->GetOneInfo($order_no);
        if (empty($aOpay)) {
            return [false, '订单不存在(2)'];
        }

        // 重新创建
        $rebuild = false;
        // 切换支付方式
        if ($aOpay['pay_type'] != $pay_type) {
            // 保存支付信息
            if ($aOpay['pay_type'] != OmainModel::PAY_BY_NO_SET) {
                PayModel::getInstance()->saveTradeOrder($order_no, $aOpay['pay_type'], $aOpay['prepay_id'], $aOpay['h5_url'], $aOpay['ptime']);
            }

            // 新支付方式的支付信息
            $currentPayData = PayModel::getInstance()->getTradeOrder($order_no, $pay_type);

            if ($currentPayData && ($aOpay['pay_type'] != OmainModel::PAY_BY_NO_SET)) {
                // 更新支付流水表
                $payData = [
                    'prepay_id' => $currentPayData['prepay_id'], 'ptime' => $currentPayData['ptime'], 'h5_url' => $currentPayData['h5_url'], 'pay_type' => $pay_type,
                ];
                by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);
                // 更新数据
                $aOpay['prepay_id'] = $currentPayData['prepay_id'];
                $aOpay['ptime'] = $currentPayData['ptime'];
            } else {
                // 新支付方式的支付信息不存在，重新生成
                $rebuild = true;
            }
        }

        // todo 统一下单生成prepay_id只有两小时有效期
        $now        = time();
        if ($rebuild || (empty($aOpay['prepay_id']) && $aOpay['price']) || bcsub($now, $aOpay['ptime']) > 7100) {

            $other              = [
                'body'          => '订单支付',
                'ctime'         => $oInfo['ctime'],
                'needReunified' => $needReunified,
                'endPayment'    => $endPayment,
            ];

            list($status, $PayReq) = $this->unifiedOrder($user_id, $api, $order_no, $aOpay['price'], $other, $attach, $pay_type);
            if (!$status) {
                return [false, $PayReq];
            }

            //更新支付流水表
            $payData = [
                'prepay_id' => $PayReq['prepay_id'], 'ptime' => $now, 'h5_url' => $PayReq['h5_url'] ?? '', 'pay_type' => $pay_type,
            ];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

        } else {
            if ($pay_type == OmainModel::PAY_BY_WX_APP) {
                $signParams = [
                    'appid'     => self::APP_PAY_APP_ID,
                    'partnerid' => self::MCH_ID,
                    'prepayid'  => $aOpay['prepay_id'],
                    'package'   => "Sign=WXPay",
                    'noncestr'  => CUtil::createVerifyCode(10, 1),
                    'timestamp' => time()
                ];
                $sign = $this->_getSign($signParams);

                // 返回信息
                $PayReq = [
                    'timeStamp' => $signParams['timestamp'],
                    'nonceStr'  => $signParams['noncestr'],
                    'package'   => 'Sign=WXPay',
                    'signType'  => "",
                    'paySign'   => $sign,
                    'prepay_id' => $aOpay['prepay_id'],
                    'order_no'  => $order_no
                ];
            } else {
                $config = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
                $PayReq = [
                    'appId'     => $config['appId'],
                    'timeStamp' => (string)$now,
                    'nonceStr'  => CUtil::createVerifyCode(10, 1),
                    'package'   => "prepay_id={$aOpay['prepay_id']}",
                    'signType'  => "MD5",
                ];

                $PayReq['paySign'] = $this->_getSign($PayReq);
                $PayReq['order_no'] = $order_no;
            }
        }

        $PayReq['pay_type'] = $pay_type;
        $r_price            = bcsub($aOpay['price'], $oInfo['fprice'] ?? 0);
        $r_price            = by::Gtype0()->totalFee($r_price, 1);
        $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
        $PayReq['coin']     = bcmul($r_price, $rate, 2);


        //TODO 绑定用户等级
        $PayReq['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id,$PayReq['coin'],time());


        if (isset($PayReq['appId'])) {
            unset($PayReq['appId']);
        }

        return [true, $PayReq];
    }

    /**
     * 订单号打标签
     * @param $order_no
     * @param $pay_type
     * @return string
     */
    private function tagOrderNo($order_no, $pay_type, $order_create_time = null): string
    {
        // 判空
        if (empty($order_no)) {
            return '';
        }

        // 上线时的时间戳，千万记得修改！！！
        // 如果订单创建时间小于 2024-05-20 23:40:00，直接返回订单号
        if ($order_create_time && $order_create_time < 1716219600) {
            return $order_no;
        }

        // 判断支付类型
        if ($pay_type == OmainModel::PAY_BY_WX) {
            $prefix = 'J-'; // JSAPI
        } elseif ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $prefix = 'A-'; // APP
        } else {
            return $order_no;
        }
        // 替换字符串的前两位
        return $prefix . substr($order_no, 2);
    }

    /**
     * 订单号去标签
     * @param $order_no
     * @return string
     */
    private function unTagOrderNo($order_no): string
    {
        // 检查订单号是否以 'J-' 或 'A-' 开头
        if (strpos($order_no, 'J-') === 0 || strpos($order_no, 'A-') === 0) {
            // 如果是，将前缀替换为 '20' 并返回新的订单号
            return '20' . substr($order_no, 2);
        }
        // 如果不是以 'J-' 或 'A-' 开头，直接返回原订单号
        return $order_no;
    }

}
