<?php
/**
 * Created by PhpStorm.
 * User: CP
 * Date: 20240408
 * Time: 16:18
 */

namespace app\modules\main\models;


use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class UsersPlatformModel extends CommModel
{
    public static function tableName()
    {
        return "`db_dreame`.`t_users_platform`";
    }


    const STATUS = [
        'UNBIND' => 0, // 未绑定
        'BIND'   => 1, // 绑定
    ];

    public function SaveLog($data)
    {
        $table = $this->tableName();
        $data['reg_time'] = time();
        try {
            by::dbMaster()->createCommand()->insert($table, $data)->execute();
            return [true, 'ok'];
        } catch (Exception $e) {
            return [false, $e->getMessage()];
        }
    }

    public function GetOneByUserId($user_id)
    {
        $user_id = intval($user_id);
        $table = $this->tableName();
        $sql = "select * from {$table} where `user_id`=:user_id";
        $data = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryAll();
        return $data;
    }

    public function GetUserIdByOpenUdId($openUdId,$userType)
    {
        $table = $this->tableName();
        $sql = "select `user_id` from {$table} where `openudid`=:openudid and `user_type`=:user_type";
        $data = by::dbMaster()->createCommand($sql, [':openudid' => $openUdId,':user_type'=>$userType])->queryOne();
        return $data['user_id'] ?? 0;
    }


    // 根据unionid + user_type 查询用户
    public function GetOneByUnionId($unionId,$userType)
    {
        $table = $this->tableName();
        $sql = "select * from {$table} where `unionid`=:unionid and `user_type`=:user_type  ORDER BY `id` DESC LIMIT 1";
        $data = by::dbMaster()->createCommand($sql, [':unionid' => $unionId,':user_type'=>$userType])->queryOne();
        return $data ?: [];
    }


    public function GetOneByPhone($phone, $userType)
    {
        $phone = intval($phone);
        $userType = intval($userType);
        $phoneTable = by::Phone()::tbName(); 
        $table = $this->tableName();
        $sql = "select * from {$table} inner join {$phoneTable} on {$table}.user_id = {$phoneTable}.user_id where {$phoneTable}.phone = :phone and {$table}.user_type = :user_type AND locate('|',{$table}.openudid)=0 ORDER BY {$table}.id DESC LIMIT 1";
        $data = by::dbMaster()->createCommand($sql, [':phone' => $phone, ':user_type' => $userType])->queryOne();
        return $data;
    }

    public function Deprecated($user_id)
    {
        $user_id = CUtil::uint($user_id, 0);
        if ($user_id <= 0) {
            return [false, "用户不存在"];
        }

        // 获取用户信息 （支付宝、PC商城用户）
        $user_info = $this->GetOneByUserId($user_id);
        if (empty($user_info)) {
            return [true, "OK"];
        }

        // 启动事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 批量更新用户 openudid
            $updates      = [];
            $openUdidList = []; // 存储所有 openudid 用于后续操作
            foreach ($user_info as $user) {
                $openId         = $user['openudid'];
                $userId         = $user['user_id'];
                $updates[]      = [
                        'openudid'        => $openId,
                        'openudid_update' => "{$openId}|" . START_TIME,
                        'user_id'         => $userId
                ];
                $openUdidList[] = $openId;  // 添加到 openudid 列表
            }

            if (!empty($updates)) {
                $db  = by::dbMaster();
                $sql = "UPDATE {$this->tableName()} SET openudid = CASE ";

                $userIds = [];
                foreach ($updates as $update) {
                    // 使用引号将 openudid 包装起来
                    $sql       .= "WHEN openudid = '{$update['openudid']}' THEN '{$update['openudid_update']}' ";
                    $userIds[] = $update['user_id'];
                }

                // 结束 CASE 语句并为 status 设置 UNBIND 状态
                $sql .= "ELSE openudid END, status = " . self::STATUS['UNBIND'] . " WHERE user_id IN (" . implode(',', $userIds) . ")";

                // 执行 SQL 更新
                $row = $db->createCommand($sql)->execute();
                if ($row <= 0) {
                    return [false, "注销会员失败~"];
                }
            }

            // 游客删除操作
            foreach ($openUdidList as $openUdid) {
                $ruser_id = by::Rusers()->getUserIdByOpenUdId($openUdid, 20);
                if ($ruser_id) {
                    by::Rusers()->delUserMainCacheByOpenudid($openUdid, 20);

                    // 执行删除数据
                    $r1 = by::Rguide()->delDataById($ruser_id);
                    $r2 = by::RuserExtend()->delDataById($ruser_id);
                    $r3 = by::RuserGuide()->delDataById($ruser_id);
                    $r4 = by::Rusers()->delDataById($ruser_id);
                    $r5 = by::Rusers()->delDataDetailById($ruser_id);
                    $r6 = by::RuserRecommend()->delDataById($ruser_id);

                    CUtil::debug($r1 . $r2 . $r3 . $r4 . $r5 . $r6, 'discard_alipay_user');
                }
            }

            // 提交事务
            $transaction->commit();

            // 删除缓存
            foreach ($openUdidList as $openUdid) {
                $ruser_id = by::Rusers()->getUserIdByOpenUdId($openUdid, 20);
                if ($ruser_id) {
                    by::Rusers()->deleteRedisCache($ruser_id, true);
                    by::RuserExtend()->deleteRedisCache($ruser_id);
                }
            }

            return [true, "OK"];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'discard_alipay_user');
            $transaction->rollBack();
            return [false, "注销会员失败~~"];
        }
    }

    public function updateLog($userId, array $data): int
    {
        // 执行更新操作
        return by::dbMaster()
            ->createCommand()
            ->update($this->tableName(), $data, ['user_id' => $userId])
            ->execute();
    }



}
