<?php

namespace app\modules\main\models;

use app\models\BusinessException;
use app\models\by;
use app\modules\common\models\BaseModel;
use app\modules\common\Singleton;
use Throwable;
use yii\db\ActiveQuery;

final class DrawCustomFormRecordModel extends BaseModel
{
    use Singleton;

    protected $fillable = ['id', 'dpr_id', 'realname', 'phone', 'address', 'status', 'created_at', 'updated_at'];

    public static function tableName(): string
    {
        return "`db_dreame_goods`.`t_draw_custom_form_record`";
    }

    /**
     * 搜索处理器
     * @param ActiveQuery $query
     * @param array $params
     * @return ActiveQuery
     */
    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        $where = [];
        if (isset($params['realname']) && $params['realname'] != '') {
            $where = array_merge($where, ['realname' => $params['realname']]);
        }
        
        if (isset($params['phone']) && $params['phone'] != '') {
            $where = array_merge($where, ['phone' => $params['phone']]);
        }
        
        if (isset($params['status']) && $params['status'] != '') {
            $where = array_merge($where, ['status' => $params['status']]);
        }

        $query->where($where);

        return $query;
    }

    /**
     * 创建
     * @param array $data 创建数据
     * @return array
     */
    public function doCreate(array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $data['created_at'] = time();
            $data['updated_at'] = time();
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('新增失败');
            }

            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 更新
     * @param int $id 数据ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            unset($data['id']);
            $data['updated_at'] = time();
            $one = self::find()->where(['id' => $id])->limit(1)->asArray()->one();
            if (empty($one)) {
                throw new BusinessException(sprintf('数据[%s]不存在', $id));
            }

            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException('更新失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 删除数据
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['delete_time' => time()], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }

            if (empty($resp)) {
                throw new BusinessException('删除失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 根据中奖记录ID获取提交记录
     * @param int $dpr_id
     * @return array
     */
    public function getSubmitRecordByDprId(int $dpr_id): array
    {
        $one = self::find()
            ->where(['dpr_id' => $dpr_id])
            ->asArray()
            ->one();
        
        if (empty($one)) {
            return [];
        }
        
        return $one;
    }
    
    /**
     * 根据记录ID获取提交信息
     * @param array $dpr_ids 记录IDs
     * @return array
     */
    public function getListByDprIds(array $dpr_ids): array
    {
        $data = self::find()
            ->where(['dpr_id' => $dpr_ids])
            ->asArray()
            ->all();
        
        if (empty($data)) {
            return [];
        }
        
        return $data;
    }
}