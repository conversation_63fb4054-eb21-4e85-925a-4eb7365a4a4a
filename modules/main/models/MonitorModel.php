<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/5/20
 * Time: 10:39
 * 监控相关
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

class MonitorModel extends CommModel {

    public static function tbName(): string
    {
        return "`db_dreame_log`.`t_qps`";
    }

    public function getApiRequestKey(): string
    {
        return AppCRedisKeys::apiRequest();
    }

    public function getApiTimeUsedKey(): string
    {
        return AppCRedisKeys::apiTimeUsed();
    }

    //QPS KEY
    private function __getApiQpsKey($date): string
    {
        return AppCRedisKeys::getApiQpsKey($date);
    }

    /**
     * @param bool $all
     * @return array
     * $all == true 生成 00:00 - 23:59 区间map
     * $all == false 生成 00:00 - 当前时间 区间map
     */
    private function __initTodayTime($all=true): array
    {
        $base  = [];
        $now_h = intval(date("H"));
        $now_i = intval(date("i"));

        for ($i=0;$i<=23;$i++) {

            if(!$all && ($i > $now_h) ) {
                break;
            }

            $hour = str_pad($i,2,'0',STR_PAD_LEFT);
            for ($j=0;$j<=59;$j++) {
                $min = str_pad($j,2,'0',STR_PAD_LEFT);

                if(!$all && ($i == $now_h && $j > $now_i) ) {
                    break;
                }

                $base["{$hour}:{$min}"] = "0";
            }
        }

        return $base;
    }

    /**
     * @param string $pathInfo
     * @param int $opt : 0统计请求数量，1返回指定接口请求数量，2获取所有接口请求数量，
     * 3：删除key,4删除所有，5过期时间
     * @return array
     * 接口并发统计相关
     */
    public function apiRequest($opt=0,$pathInfo=''): array
    {
        $redis_key = $this->getApiRequestKey();
        switch ($opt) {
            case 0 :
                $ret = by::redis('core')->ZINCRBY($redis_key,1,$pathInfo);
                by::redis('core')->EXPIREAT($redis_key,strtotime('tomorrow')-1);
                break;
            case 1 :
                $ret = by::redis('core')->ZSCORE($redis_key,$pathInfo);
                break;
            case 2 :
                $ret = by::redis('core')->ZREVRANGE($redis_key,0,-1,true);
                break;
            case 3 :
                $ret = by::redis('core')->ZREM($redis_key,$pathInfo);
                break;
            case 4 :
                $ret = by::redis('core')->DEL($redis_key);
                break;
            case 5 :
            default:
                $ret = by::redis('core')->TTL($redis_key);
                break;
        }

        return [true,$ret];
    }

    /**
     * @param $opt
     * @param string $pathInfo
     * @param int $ms 毫秒数
     * @return array
     * 接口总耗时统计
     */
    public function apiRequestTimeUsed($opt=0,$pathInfo='',$ms=0): array
    {
        $redis_key = $this->getApiTimeUsedKey();
        switch ($opt) {
            case 0 :
                $ret = by::redis('core')->HINCRBY($redis_key,$pathInfo,$ms);
                by::redis('core')->EXPIREAT($redis_key,strtotime('tomorrow')-1);
                break;
            case 1 :
                $ret = by::redis('core')->HGET($redis_key,$pathInfo);
                break;
            case 2 :
                $ret = by::redis('core')->HGETALL($redis_key);
                break;
            case 3 :
                $ret = by::redis('core')->HDEL($redis_key,$pathInfo);
                break;
            case 4 :
                $ret = by::redis('core')->DEL($redis_key);
                break;
            case 5 :
            default:
                $ret = by::redis('core')->TTL($redis_key);
                break;
        }

        return [true,$ret];
    }


    /**
     * QPS统计
     */
    public function apiQps() {
        $date    = date("Ymd");
        $r_key   = $this->__getApiQpsKey($date);
        $sub_key = date("H:i");

        $redis   = by::redis('core');
        $redis->hIncrBy($r_key,$sub_key,1);

        //提供跨周末，数据落地异常修复时间
        $redis->expireAt($r_key,strtotime("+3 days",strtotime("tomorrow")));
    }

    /**
     * @param $date
     * @return array
     * @throws \yii\db\Exception
     * 按日期获取QPS
     */
    public function getQpsByDate($date=null): array
    {

        $today = date("Ymd");
        $date  = empty($date) ? $today : $date;
        $date  = CUtil::uint($date);
        $redis = by::redis('core');

        if($date > $today || $date < 20210728) {
            return $this->__initTodayTime();
        }

        $r_key = $this->__getApiQpsKey($date);
        $aData = $redis->hGetAll($r_key);

        //当天数据只从缓存拿
        if($date === intval($today)) {
            $base  = $this->__initTodayTime(false);
            return array_merge($base,$aData);
        }

        //其他日志缓存不存在走DB
        if(empty($aData)) {
            $tb    = self::tbName();
            $sql   = "SELECT `data` FROM {$tb} WHERE `date`={$date} LIMIT 1";
            $aJson = by::dbMaster()->createCommand($sql)->queryOne();
            $aData = empty($aJson) ? [] : (array)json_decode($aJson['data'],true);
            $redis->hMSet($r_key,$aData);
            $redis->expire($r_key,3600);
        }

        ksort($aData);

        return $aData;
    }

    /**
     * @param null $date
     * @return int
     * @throws \yii\db\Exception
     * QPS数据落地
     */
    public function QpsDataToDB($date=null) {

        $date  = empty($date) ? date("Ymd") : $date;
        $r_key = $this->__getApiQpsKey($date);

        $tb    = self::tbName();
        $aData = $this->getQpsByDate($date);

        $base  = $this->__initTodayTime();
        $aData = array_merge($base,$aData);
        $arr   = [
            ':aDate' => $date,
            ':aData' => json_encode($aData),
        ];

        $sql   = "INSERT IGNORE INTO {$tb} (`date`,`data`) VALUE (:aDate,:aData) ON DUPLICATE KEY UPDATE `data`=:aData";

        by::dbMaster()->createCommand($sql,$arr)->execute();

        by::redis('core')->del($r_key);//缓存清理

        return true;
    }
}