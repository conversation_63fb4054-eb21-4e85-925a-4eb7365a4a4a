<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;

/**
 * 用户绑定关系表
 */
class UserBindModel extends CommModel
{
    const BIND_STATUS = [
        'BIND'   => 1, // 绑定
        'UNBIND' => 2  // 解绑
    ];

    // 是否新用户
    const IS_NEW_USER = [
        'NO'  => 0,
        'YES' => 1
    ];

    const ACTIVITY_ID = [
        'smile' => 2  //微笑大使活动
    ];

    public static function tbName(): string
    {
        return "`db_dreame`.`t_user_bind`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 获取绑定列表
    public function getBindList(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->select(['*'])
            ->where($condition)
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
    }

    // 获取数量
    public function getBindCount(array $params = []): int
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->where($condition)
            ->count();
    }

    // 获取数量
    public function getInviteCount($user_id)
    {
        $info = self::find()->select(['count(DISTINCT bound_phone) as count'])->where(['user_id'=>$user_id,'is_new_user'=>1])->asArray()->one();
        return $info['count'] ?? 0;
    }
    public function getInfoByUserId($user_id){
        $info = self::find()->where(['bound_user_id'=>$user_id,'bind_status'=>1])->asArray()->one();
        return $info;
    }

    // 获取搜索条件
    private function getSearchCondition(array $params): array
    {
        $conditions = ['and'];

        if (!empty($params['uid'])) {
            $conditions[] = ['uid' => $params['uid']];
        }
        if (!empty($params['bound_phone'])) {
            $conditions[] = ['bound_phone' => $params['bound_phone']];
        }

        if (!empty($params['bound_uid'])) {
            $conditions[] = ['bound_uid' => $params['bound_uid']];
        }

        if (!empty($params['bind_status'])) {
            $conditions[] = ['bind_status' => $params['bind_status']];
        }

        if (isset($params['is_new_user']) && $params['is_new_user'] !== '') {
            $conditions[] = ['is_new_user' => $params['is_new_user']];
        }

        if (isset($params['activity_id'])) {
            $conditions[] = ['activity_id' => $params['activity_id']];
        }
        if (isset($params['is_new_user'])) {
            $conditions[] = ['is_new_user' => $params['is_new_user']];
        }
        if (isset($params['user_id'])) {
            $conditions[] = ['user_id' => $params['user_id']];
        }

        // 查询订单创建时间
        if (!empty($params['bind_start_time']) && !empty($params['bind_end_time'])) {
            $conditions[] = ['between', 'bind_time', $params['bind_start_time'], $params['bind_end_time']];
        }

        // 移除数组中只有 'and' 的情况，即没有其他条件时
        if (count($conditions) === 1) {
            return [];
        }

        return $conditions;
    }

    public function isOldUser($uid){
        // 通过uid获取手机号
        $status = false;
        
        $db = by::dbMaster();
        $sqlMain = "SELECT * FROM `db_dreame`.`t_users_mall` WHERE `uid` = :user_id LIMIT 1 ";
        $aData = $db->createCommand($sqlMain, [':user_id' => $uid])->queryOne();
        if($aData){
            $phone = $aData['phone'];
            // 通过手机号找到所有的注销用户
            $sql = "SELECT * FROM `db_dreame`.`t_users_mall` WHERE `phone` = :phone AND `is_deleted` = 1 order by id desc";
            $list = $db->createCommand($sql, [':phone' => $phone])->queryAll();
            if($list){
                foreach($list as $v){
                    // 通过uid获取绑定状态
                    $bindStatus = self::find()->where(['bound_uid'=>$v['uid']])->asArray()->one();
                    if($bindStatus){
                        $status = true;
                        break;
                    }
                }
            }
        }

        return $status;
    }

    public function bindEmployee($userId,$employeeUserId,$boundUid, $employeeUid,$phone,$isNew,&$error,$isForceUpdate = 0,$activity_id = 1){
        if(empty($employeeUid) || empty($boundUid)) {
            $error = '参数错误';
            return false;
        }
        if($employeeUid == $boundUid){
            $error = '不能绑定自己';
            return false;
        }
        // 不能互相绑定
        $oldInfo = self::find()->where(['uid'=>$boundUid,'bound_uid'=>$employeeUid])->asArray()->one();
        if($oldInfo){
            $error = '不能互相绑定';
            return false;
        }
        // 如果手机号存在，则认定不是新用户
        $oldInfo1 = self::find()->where(['bound_phone'=>$phone,'activity_id'=>$activity_id])->asArray()->one();
        if($oldInfo1 && $activity_id == 1){
            $isNew = 0;
        }

        // 默认同一时间一个用户只有一个处于绑定状态的数据
        $info = self::find()->where(['bound_uid'=>$boundUid,'bind_status'=>1])->asArray()->one();
        
        $db = by::dbMaster();
        $tb = self::tbName();

        $nowTime = time();
        $thirtyDays = 90 * 24 * 60 * 60;
        // 如果没有数据，证明是第一次绑定，直接创建数据
        if(empty($info)){
            $data = [
                'user_id'=>$employeeUserId,
                'uid'=>$employeeUid,
                'bound_user_id'=>$userId,
                'bound_uid'=>$boundUid,
                'bound_phone'=>$phone,
                'bind_status'=>1,
                'is_new_user'=>$isNew,
                'activity_id'=>$activity_id,
                'bind_time'=>$nowTime,
                'ctime'=>$nowTime,
                'utime'=>$nowTime,
            ];
            return $db->createCommand()->insert($tb, $data)->execute();
        }else{
            // 如果有数据，判断是不是绑定的当前员工
            if($info['uid'] == $employeeUid){
                if($isForceUpdate == 1) {
                    // 如果强制更新，则更新is_new_user数据
                    $db->createCommand()->update($tb, ['is_new_user'=>$isNew,'utime'=>$nowTime], "`id`=:id", [":id" => $info['id']])->execute();
                    return true;
                }
                $error = '已经绑定过该员工';
                return false;
            }else{
                // 不是当前员工，则判断是否在保护期
                if(($nowTime - $info['bind_time']) > $thirtyDays){
                    // 如果不在保护期则换绑,不验证是否之前绑定过，直接写入新数据
                    $data = [
                        'user_id'=>$employeeUserId,
                        'uid'=>$employeeUid,
                        'bound_user_id'=>$userId,
                        'bound_uid'=>$boundUid,
                        'bound_phone'=>$phone,
                        'bind_status'=>1,
                        'is_new_user'=>$isNew,
                        'activity_id'=>$activity_id,
                        'bind_time'=>$nowTime,
                        'ctime'=>$nowTime,
                        'utime'=>$nowTime,
                    ];
                    $db->createCommand()->insert($tb, $data)->execute();
                    // 把当前员工解绑
                    
                    $db->createCommand()->update($tb, ['bind_status'=>2,'unbind_time'=>$nowTime,'utime'=>$nowTime], "`id`=:id", [":id" => $info['id']])->execute();
                    return true;
                }else{
                    // 在保护期内，不能换绑
                    $error = '会员在保护期内不能换绑';
                    return false;
                }
            }
        }
        $error = '绑定失败，请联系管理员';
        return false;
    }

    public function delDataByUid($uid){
        $db = by::dbMaster();
        return $db->createCommand()->delete(self::tbName(), ['uid'=>$uid])->execute();
    }


    //获取推荐注册数量
    public function getRecommendRegisterCount(){
        $list = self::find()
            ->select(['count(id) as count','uid'])
            ->where(['is_new_user' => 1])
            ->groupBy('uid')
            ->asArray()
            ->all();
        $res = [];
        foreach($list as $k=>$v){
            $res[$v['uid']] = $v['count'];
        }
        return $res;
    }

    // 处理uid数据
    public function handleUidData(){
        $list = self::find()->asArray()->all();
        $uids = array_merge(array_column($list,'uid'),array_column($list,'bound_uid'));
        // $uids = array_unique($uids);
        $userIds = by::Phone()->getUserIdsByUids($uids);
        foreach($list as $k=>$v){
            $save = [
                'user_id' => 0,
                'bound_user_id' => 0,
            ];
            if (isset($userIds[$v['uid']])) {
                $save['user_id'] = $userIds[$v['uid']];
            }
            if (isset($userIds[$v['bound_uid']])) {
                $save['bound_user_id'] = $userIds[$v['bound_uid']];
            }
            $db = by::dbMaster();
            $tb = self::tbName();
            $id = $v['id'];
            $resp = $db->createCommand()->update($tb, $save, ['id' => $id])->execute();
        }
    }

    // 处理历史推荐数据
    public function handleHistoryData($uid,$user_id){
        //查询历史的数据
        $list = by::userRecommend()->getUserListByRid($user_id);
        $nowTime = time();
        foreach($list as $k=>$v){
            //获取被推荐人uid
            $boundUid = by::Phone()->getUidByUserId($v['user_id']);
            // 默认同一时间一个用户只有一个处于绑定状态的数据
            $info = self::find()->where(['bound_uid'=>$boundUid,'bind_status'=>1])->asArray()->one();
            
            $db = by::dbMaster();
            $tb = self::tbName();
            if(empty($info)){
            $data = [
                'user_id'=>$user_id,
                'uid'=>$uid,
                'bound_user_id'=>$v['user_id'],
                'bound_uid'=>$boundUid,
                'bind_status'=>1,
                'is_new_user'=>1,
                'activity_id' => 47,
                'bind_time'=>$nowTime,
                'ctime'=>$nowTime,
                'utime'=>$nowTime,
            ];
           $db->createCommand()->insert($tb, $data)->execute();
        }
        }
        return [true,'success'];
    }
}