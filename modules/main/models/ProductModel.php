<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\jobs\ProductSaveJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\DataReader;
use yii\db\Exception;

class ProductModel extends CommModel
{
    public $tb_fields = [
        'id', 'name', 'image', 'm_name', 'period', 'sn', 'ctime', 'is_del', 'tid','is_support_care'
    ];

    public static function tbName(): string
    {
        return "`db_dreame`.`t_products`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    const DELETE = [ //删除
        'YES' => ['CODE' => 1, 'NAME' => '已删除'],
        'NO'  => ['CODE' => 0, 'NAME' => '未删除'],
    ];

    const SN_FIX_DIGITS = ['MIN' => 3, 'MAX' => 12];//sn编码前缀位数

    //sn编码匹配状态
    const MATCH_CODE = [
        'ONE' => 1,     //匹配到一个
        'MUL' => 100,   //匹配到多个
        'ERR' => -1,    //未匹配到
    ];

    const PERIOD_MONTH     = 24;
    const PERIOD_MIN_MONTH = 3;

    /**
     * @param $id
     * @return string
     * 产品唯一数据缓存KEY
     */
    private function __getOneProduct($id): string
    {
        return AppCRedisKeys::getOneProduct($id);
    }

    /**
     * @return string
     * 产品列表KEY
     */
    private function __getProductList(): string
    {
        return AppCRedisKeys::getProductList();
    }

    /**
     * @return string
     * 产品集合
     */
    private function __getProductSnSet(): string
    {
        return AppCRedisKeys::getProductSnSet();
    }

    private function __delCache($id = 0): string
    {
        $key1 = $this->__getOneProduct($id);
        $key2 = $this->__getProductList();
        $key3 = $this->__getProductSnSet();
        return by::redis('core')->del($key1, $key2, $key3);
    }

    /**
     * @return string
     * 获取集合key(确保集合中有数据)
     */
    private function __getProductSet()
    {
        $page_size = 50;
        $redis     = by::redis('core');
        $redis_key = $this->__getProductSnSet();
        $scard     = by::redis('core')->SCARD($redis_key);

        if ($scard <= 0) {
            //保证只有一次db查询
            $unique_key = CUtil::getAllParams(__FUNCTION__);
            list($free) = self::ReqAntiConcurrency(0, $unique_key, 5, 'EX');
            if ($free) {

                $tb = self::tbName();
                list($where, $params) = $this->__getCondition();
                //拿到产品数量
                $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
                $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
                //分页刷入缓存
                $pages    = CUtil::getPaginationPages($count, $page_size);
                $last_id  = 0;
                $sql_temp = "SELECT `sn`,`id` FROM {$tb} WHERE {$where}";
                for ($page = 1; $page <= $pages; $page++) {
                    $sql = sprintf($sql_temp, $last_id);

                    $rows = by::dbMaster()->createCommand($sql, $params)->queryAll();

                    if (empty($rows)) {
                        break;
                    }
                    $end     = end($rows);
                    $last_id = $end['id'];

                    $sn = array_column($rows, "sn");
                    $redis->sAdd($redis_key, ...$sn);
                }

                $redis->expire($redis_key, 86400 * 7);
            } else {
                usleep(200000);
            }
        }

        return $redis_key;
    }

    /**
     * @param string $name
     * @param string $image
     * @param string $m_name
     * @param string $sn
     * @param int $id
     * @param array $ext
     * @return array
     * @throws Exception
     */
    public function saveProduct(string $name = '', string $image = '', string $m_name = '', int $period = 0, string $sn = '', int $id = 0, array $ext = [], $tid = '',int $is_support_care = 2,array $care_info = []): array
    {

        $id = CUtil::uint($id);
        $sn = trim(strval($sn));
        if (empty($name) || empty($image) || empty($sn) || empty($tid)) {
            return [false, '缺少必要参数'];
        }

        $mConfig = [];
        if ($ext['is_market'] == 1) {
            if (!is_array($ext['mConfig'])) {
                return [false, '缺少资源配置'];
            }

            // if (count($ext['mConfig']) > PmarketModel::MAX_COUNT) {
            //     return [false, sprintf('最多配置 %s 张优惠券', PmarketModel::MAX_COUNT)];
            // }
            $mConfig = $ext['mConfig'];
        }

        $save = [
            'name'   => $name,
            'image'  => $image,
            'm_name' => $m_name,
            'sn'     => $sn,
            'period' => $period,
            'tid'    => $tid,
            'is_support_care' => $is_support_care,
            'ctime'  => Intval(START_TIME),
        ];

        $db   = by::dbMaster();
        $tb   = self::tbName();
        $tran = $db->beginTransaction();

        try {
            if (CUtil::checkMultiRepeat($mConfig, 'mc_id')) {
                throw new MyExceptionModel('存在重复选择的优惠券');
            }
            
            if ($id) {
                //编辑
                $info = $this->getOneById($id);
                if (empty($info)) {
                    throw new MyExceptionModel('产品不存在');
                }

                $row = $db->createCommand()->update($tb, $save, "`id`=:id", [":id" => $id])->execute();
                if (empty($row)) {
                    throw new MyExceptionModel('编辑产品失败');
                }
                // 保存Care+配置
                byNew::ProductCareModel()->saveData($id, $care_info);
                

                if ($ext['is_market'] == 0) {
                    list($status, $pm_ids) = by::pMarket()->getIdsByPid($id);
                    if (!$status) {
                        return [false, '产品不存在'];
                    }
                    foreach ($pm_ids as $pm_id) {
                        list($status, $msg) = by::pMarket()->del($pm_id);
                        if (!$status) {
                            return [false, $msg];
                        }
                    }
                } else {
                    // 以下为新增代码 优化原本更新逻辑，由单独操作优惠券接口变更为产品注册接口统一操作
                    list($status, $pm_ids) = by::pMarket()->getIdsByPid($id);

                    // 添加或编辑的优惠券ID
                    $editIds = array_filter(array_column($mConfig, 'id'), function ($item) {
                        return ! empty($item);
                    });

                    // 待删除优惠券ID
                    $delIds = array_diff($pm_ids, $editIds);
                    // 删除优惠券
                    foreach ($delIds as $delId) {
                        list($status, $msg) = by::pMarket()->del($delId);
                        if (! $status) {
                            throw new MyExceptionModel($msg);
                        }
                    }

                    foreach ($mConfig as $m) {
                        // 添加优惠券
                        if (empty($m['id'])) {
                            list($status, $msg) = by::pMarket()->add($id, $m['mc_id'], $m['stock']);
                            if (! $status) {
                                throw new MyExceptionModel($msg);
                            }
                            continue;
                        }

                        // 更新优惠券
                        if (in_array($m['id'], $pm_ids)) {
                            list($status, $msg) = by::pMarket()->edit($m['id'], $id, $m['mc_id'], $m['stock']);
                            if (! $status) {
                                throw new MyExceptionModel($msg);
                            }
                        }
                    }
                }
            } else {
                //新增产品
                $row = $db->createCommand()->insert($tb, $save)->execute();
                if (empty($row)) {
                    throw new MyExceptionModel('新增产品失败');
                }

                $pid = $db->getLastInsertID();
                // 保存Care+配置
                byNew::ProductCareModel()->saveData($pid, $care_info);
                
                //新增产品绑定的优惠券
                foreach ($mConfig as $m) {
                    list($status, $msg) = by::pMarket()->add($pid, $m['mc_id'], $m['stock']);
                    if (!$status) {
                        return [false, $msg];
                    }
                }
            }
            $tran->commit();
            //异步触发修改产品tid
            \Yii::$app->queue->push(new ProductSaveJob());
            $this->__delCache($id);
            return [true, $save];
        } catch (MyExceptionModel $e) {
            $tran->rollBack();

            return [false, $e->getMessage()];

        } catch (\Exception $e) {

            $tran->rollBack();

            CUtil::debug($e->getMessage(), 'err.gtype');

            return [false, '操作失败'];
        }

    }

    /**
     * 管理后台 - 产品列表
     * @param int $page
     * @param int $page_size
     * @param string $name :  名称
     * @param string $sn :  sn编码
     * @return array
     * @throws Exception
     */
    public function getList(string $sn = '', string $name = '', $tid = 0, int $page = 1, int $page_size = 30): array
    {
        $r_key   = $this->__getProductList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $name, $sn, $tid);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($name, $sn, $tid);
            $sql     = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $command = by::dbMaster()->createCommand($sql, $params);
            $aData   = $command->queryAll();
            $aData   = empty($aData) ? [] : $aData;
            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key);
        }

        return empty($aData) ? [] : $aData;
    }

    /**
     * @return array
     * @throws Exception
     * 预约管理-产品列表
     */
    public function getProductList(): array
    {
        $tb = $this->tbName();
        list($where, $params) = $this->__getCondition();
        $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC";
        $ids = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $ids = empty($ids) ? [] : $ids;

        $list = [];
        foreach ($ids as $id) {
            $info = by::product()->getOneById($id['id']);
            if (empty($info)) {
                continue;
            }

            $list[] = $info;
        }

        return $list;
    }

    /**
     * @param string $name :  名称
     * @param string $sn :  sn编码
     * @return int
     * @throws Exception
     * 管理后台查询产品列表总数
     */
    public function getCount(string $sn = '', string $name = '', $tid = '')
    {

        $r_key   = $this->__getProductList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $name, $sn, $tid);
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($name, $sn, $tid);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key);
        }

        return intval($count);
    }

    /**
     * @param $id
     * @return array|false
     * @throws Exception
     * 获取指定产品根据id
     */
    public function getOneById($id)
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOneProduct($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if (empty($aData)) {
            $tb     = $this->tbName();
            $fields = implode("`,`", $this->tb_fields);

            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":id", $id);
            $aData = $command->queryOne();
            $aData = empty($aData) ? [] : $aData;

            //补充Care+信息
            $care_info = byNew::ProductCareModel()->getOneByProductId($id);
            if(!empty($care_info)){
                unset($care_info['id']);
                unset($care_info['product_id']);
                foreach($care_info as $k=>$v){
                    $aData[$k] = $v;
                }
            }

            $redis->set($redis_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }

        if ($aData) {
            $aData['period'] = empty($aData['period'] ?? 0) ? self::PERIOD_MONTH : $aData['period'];
        }

        return $aData;
    }

    /**
     * @param $sn
     * @return array
     * @throws Exception
     * 获取指定产品根据sn编码
     */
    public function getListBySn($sn)
    {
        $sn = trim(strval($sn));
        if (empty($sn)) {
            return [];
        }

        $redis   = by::redis('core');
        $r_key   = $this->__getProductList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $sn);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if (empty($aData)) {
            $tb     = $this->tbName();
            $is_del = self::DELETE['NO']['CODE'];

            $sql     = "SELECT `id` FROM  {$tb} WHERE `sn`=:sn AND `is_del`=:is_del";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":sn", $sn);
            $command->bindParam(":is_del", $is_del);
            $aData = $command->queryAll();
            $aData = empty($aData) ? [] : $aData;

            $redis->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key);
        }

        return $aData;
    }

    /**
     * @param $id
     * @return array
     * 删除产品
     */
    public function del($id)
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }

        $info = $this->getOneById($id);
        if (empty($info)) {
            return [false, '产品不存在'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        try {

            $tb  = self::tbName();
            $row = by::dbMaster()->createCommand()->update($tb, ['is_del' => self::DELETE['YES']['CODE']], "`id`=:id", [':id' => $id])->execute();
            if (!$row) {
                throw new \Exception('删除产品失败');
            }

            list($status, $pm_ids) = by::pMarket()->getIdsByPid($id);
            if ($status) {
                foreach ($pm_ids as $pm_id) {
                    list($status, $msg) = by::pMarket()->del($pm_id);
                    if (!$status) {
                        throw new \Exception($msg);
                    }
                }
            }
            $transaction->commit();
            $this->__delCache($id);
            return [true, '操作成功'];

        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);
            return [false, $e->getMessage()];
        }
    }

    /**
     * @param $sn
     * sn编码匹配产品
     * @throws Exception
     */
    public function match($sn,$userId = 0)
    {
        $sn = trim(strval($sn));
        if (empty($sn)) {
            return [false, '请填写sn编码', self::MATCH_CODE['ERR']];
        }

//        if(!preg_match("/^[a-zA-Z0-9-\/]+$/u",$sn)){
//            return [false,'sn编码不符合规则',self::MATCH_CODE['ERR']];
//        }
        if (!CUtil::reg_valid($sn, CUtil::REG_SN)) {
            return [false, 'sn编码不符合规则', self::MATCH_CODE['ERR']];
        }

        $redis  = by::redis('core');
        $digits = self::SN_FIX_DIGITS;
        //拿缓存key之时 确保缓存中有数据
        $r_key = $this->__getProductSet();

        $ids = [];

        //截取不同的位数 循环在缓存中查询
        for ($i = $digits['MIN']; $i <= $digits['MAX']; $i++) {
            $sn_fix = substr($sn, 0, $i);
            $is_m   = $redis->sIsMember($r_key, $sn_fix);

            if ($is_m) {
                $id_list = $this->getListBySn($sn_fix);
                $ids     = array_merge($ids, $id_list);
            }
        }

        $ids = array_column($ids, 'id', 'id');

        //根据查询结果 返回 未查询到，查询到一个，查询到多个，三种状态
        if (empty($ids)) {
            return $this->__notFound($sn,$userId);
        } elseif (count($ids) > 1) {
            $list = [];

            foreach ($ids as $id) {
                $info = $this->getOneById($id);
                !empty($info) && $list[] = $info;
            }

            if (empty($list)) {
                return $this->__notFound($sn,$userId);
            }

            //判断是否存在最长的SN编码
            $sns = array_column($list, 'sn', 'id');
            // 第一次遍历，找出最长元素的长度
            $max_length = max(array_map('strlen', $sns));
            // 第二次遍历，找出所有长度等于最长长度的元素
            $longest_elements = array_filter($sns, function ($item) use ($max_length) {
                return strlen($item) == $max_length;
            });
            // 检查最长元素是否唯一
            if (count($longest_elements) > 1) {
                $snsAll  = array_column($list, null, 'id');
                $listNew = [];
                foreach ($longest_elements as $key => $value) {
                    if (isset($snsAll[$key])) {
                        $listNew[] = $snsAll[$key];
                    }
                }
                return [true, $listNew, self::MATCH_CODE['MUL']];
            } else {
                $key = array_keys($longest_elements)[0];
                return [true, ['product_id' => $key], self::MATCH_CODE['ONE']];
            }

        } else {
            $product_id = current($ids);

            if (empty($product_id)) {
                return $this->__notFound($sn,$userId);
            }

            return [true, ['product_id' => $product_id], self::MATCH_CODE['ONE']];
        }
    }


    /**
     * 统一返回产品SN不存在的格式
     */
    private function __notFound($sn, $userId): array
    {
        // 判断 SN 首位是否是字母
        $firstChar = substr($sn, 0, 1);
        $isAlpha   = ctype_alpha($firstChar);

        // 只有首位是字母时才告警
        if ($isAlpha && YII_ENV_PROD) {
            $data['title']      = sprintf('%s 产品注册异常告警', date('Y-m-d H:i:s'));
            $data['contents'][] = sprintf('**%s** 环境，异常SN为：%s，用户ID为：%s，请及时关注！', YII_ENV, $sn, $userId);
            CUtil::sendMsgToFs($data, 'productSnNotice', 'interactive');
        }

        return [false, '产品SN编码不存在', self::MATCH_CODE['ERR']];
    }


    /**
     * @param $file : 文件
     * @return array
     * 产品导入
     */
    public function entrance($file): array
    {
        $filename = $file['tmp_name'] ?? '';
        if (!$filename) {
            return [false, '请选择文件'];
        }

        $name = explode('.', $file['name']);
        if (array_pop($name) != 'csv') {
            return [false, '请上传csv类型文件'];
        }

        $handle = fopen($filename, 'r');
        list($s, $data) = $this->__anCsv($handle);
        if (!$s) {
            return [false, $data];
        }

        if (!count($data)) {
            return [false, '没有任何数据'];
        }

        $columns = array_keys(reset($data));

        $tb = self::tbName();

        $row = by::dbMaster()->createCommand()->batchInsert($tb, $columns, $data)->execute();
        if (!$row) {
            return [false, '修改失败'];
        }

        $this->__delCache();

        return [true, '修改成功'];
    }

    /**
     * @return array
     * 解析 csv 文件
     */
    private function __anCsv($handle)
    {
        $out = [];
        $n   = 1;

        while ($data = fgetcsv($handle, 10000)) {

            if ($n > 2 && !empty($data[0])) {
                if (!isset($data[0]) || !isset($data[1]) || !isset($data[2])) {
                    return [false, '请检查文件格式'];
                }
                $out[] = [
                    'sn'     => trim($data[0]),
                    'name'   => mb_convert_encoding($data[1], "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                    'm_name' => mb_convert_encoding($data[2], "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                ];
            }

            $n++;
        }
        return [true, array_values($out)];
    }

    /**
     * @param string $name :  姓名
     * @param string $sn : sn编码
     * @param int $tid : 产品标签
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(string $name = '', string $sn = '', int $tid = 0): array
    {

        $where             = "is_del=:is_del";
        $params[':is_del'] = self::DELETE['NO']['CODE'];

        if (!empty($name)) {
            $where           .= " AND (`name` LIKE :name OR `m_name` LIKE :name)";
            $params[":name"] = "%{$name}%";
        }

        if (!empty($sn)) {
            $where         .= " AND `sn` LIKE :sn";
            $params[":sn"] = $sn;
        }

        if (!empty($tid)) {
            $where         .= " AND `tid` =:tid";
            $params[":tid"] = $tid;
        }

        return [$where, $params];
    }


    /**
     * @return void
     * 更新产品tid信息（只保留主机tid不取配件）
     */
    public function updateProductTid()
    {
        try {
            $tb = self::tbName();
            $sql = "SELECT `id`, `m_name` FROM {$tb} WHERE `tid` = '' AND `is_del` = 0";
            $productData = by::dbMaster()->createCommand($sql)->queryAll();

            if (empty($productData)) {
                return;
            }

            // $notMainTid = by::Gtag()::NOT_MAIN_TAG; // 需要过滤的 tid
            $notMainTid = by::Gtag()->GetNotMainTag();; // 需要过滤的 tid
            $batchArray = [];

            foreach ($productData as $productDatum) {
                $goods = by::Gmain()->GetOneBySku($productDatum['m_name'], 1);
                $gid = $goods['id'] ?? 0;

                // 获取商品的主机标签 tid
                $goodsTidList = array_column(by::Gtag()->GetListByGid($gid), 'tid');
                $filteredTidList = array_diff($goodsTidList, $notMainTid);

                // 检查是否有多个主机标签
                if (count($filteredTidList) > 1) {
                    CUtil::debug(
                            sprintf("产品ID：%s ，有多个主机标签为：%s", $productDatum['id'], implode(',', $filteredTidList)),
                            'err.update_product_tid'
                    );
                    continue;
                }

                $tid = implode(',', $filteredTidList);
                $batchArray[] = [
                        'id' => $productDatum['id'],
                        'tid' => $tid,
                ];

                // 清理缓存
                $this->__delCache($productDatum['id']);
            }

            // 批量更新产品 tid
            if (!empty($batchArray)) {
                $sql = CUtil::batchUpdate($batchArray, 'id', $tb);
                by::dbMaster()->createCommand($sql)->execute();
            }
        } catch (\Exception $e) {
            CUtil::debug(
                    sprintf("Error: %s | File: %s:%d", $e->getMessage(), $e->getFile(), $e->getLine()),
                    'err.update_product_tid'
            );
        }
    }


    /**
     * 查询列表
     * @param array $ids
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getListByIds(array $ids, array $columns = ['id']): array
    {
        // 查询字段
        $columns = implode("`,`", $columns);
        // 查询
        $tb  = self::tbName();
        $ids = implode(',', $ids);
        // 执行SQL
        $sql = "SELECT `{$columns}` FROM {$tb} WHERE `id` IN ({$ids})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }

}
