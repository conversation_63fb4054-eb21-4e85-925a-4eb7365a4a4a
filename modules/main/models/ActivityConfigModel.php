<?php


namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\components\EventMsg;
use app\jobs\MemberRegJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\wares\services\goods\GoodsMainService;
use app\modules\wares\services\goods\IndexGoodsMainService;
use RedisException;
use yii\db\Exception;

/**
 * Class ActivityConfigModel
 * @package app\modules\main\models
 * TOTO 获取配置
 */
class ActivityConfigModel extends CommModel
{

    protected $expire_time = 1800;

    const GRANT_TYPE = [
            'newUser'         => 1,  //新人礼包
            'recommend_gift'  => 2,  //推荐有礼
            'common_activity' => 3,  //通用活动
            'birthday'        => 4,  //生日活动
            'monthly'         => 5,  //每月活动
            'goods_detail'    => 6,  //商品详情
            'auto_coupon'     => 7,  //自动发券
    ];

    const GRANT_TYPE_NAMES = [
            1 => '新人会员礼包',
            2 => '推荐有礼',
            3 => '优惠券领取活动',
            4 => '每月领券',
            5 => '生日福利',
            6 => '商品详情页领券',
            7 => '自动发券',
    ];

    const NO_ARR_MONTHLY = 'noArriveMonthly';

    const REWARD_TYPE = [
            'coupon' => 1,  //优惠券
            'point'  => 2,  //积分
    ];

    const COUPON_STATUS = [
            'WAIT_GET'    => 0,//待领取
            'HAS_GET'     => 1,//已领取
            'HAS_EXPIRE'  => 2,//已过期
            'HAS_USE'     => 3,//已使用
            'HAS_GET_ALL' => 4,//已经领光
    ];

    const COUPON_BTN_STATUS = [
            'NO_AUTH'    => -1,//无权限领取
            'SAME_USER'  => -2,//不能领取自己
            'OVER_TIMES' => -3,//领取超次数
            'ONCE_DRAW'  => 0,//一键领取
            'TO_USE'     => 1,//分享 去使用
            'HAS_END'    => 2,//已结束
    ];


    const PAGE_IMAGE_LIMIT = 1; //页面海报数量上限

    const SHARE_IMAGE_LIMIT = 3; //分享海报数量上限

    //活动限制
    const ACTIVITY_RESTRICTION = [
            'have_consumed'     => 1,//消费过
            'not_have_consumed' => 2//没消费过
    ];

    const DELAY_TIME = 5; //延迟时间

    public static function getDb()
    {
        return by::dbMaster();
    }

    public static function tableName()
    {
        return "`db_dreame`.`t_activity_config`";
    }

    private $_filed = "`id`,`market_id`,`name`,`sales_num`,`surplus_num`,`grant_type`,`user_type`,`platform`,`start_time`,`end_time`,`img`,`a_type`,`btn_img`,`weight`,`resource`,`help_order`,`attributes`";


    /**
     * @deprecated 2023-07-06 废弃
     * @param $id
     * @param $data
     * @return array
     * @throws Exception
     * 添加编辑表单验证
     */
    protected function _modifyCheck($id, $data): array
    {
        if (empty($data)) {
            return [false, '数据不存在'];
        }

        if (empty($data['name'])) {
            return [false, '名称不能为空'];
        }
        $grant_type = isset($data['grant_type']) ? CUtil::uint($data['grant_type']) : 0;
        $resource   = '';
        switch ($grant_type) {
            case self::GRANT_TYPE['newUser'] :
                $re_id = self::find()->where(['name' => $data['name'], 'is_delete' => 0])->scalar();
                if (!empty($re_id) && (empty($data['id']) || $re_id != $data['id'])) {
                    return [false, '名称已存在'];
                }

                if (empty($data['market_id'])) {
                    return [false, '营销ID不能为空'];
                }

                if ((!is_numeric($data['surplus_num']) || $data['surplus_num'] <= 0)) {
                    return [false, '库存配置不正确'];
                }

                if (empty($data['start_time'])) {
                    return [false, '开始时间不能为空'];
                }

                if (empty($data['end_time'])) {
                    return [false, '结束时间不能为空'];
                }

                if ($data['start_time'] > $data['end_time']) {
                    return [false, '开始时间不能大于结束时间'];
                }

                $type_data = [
                        'market_id'   => $data['market_id'],
                        'surplus_num' => $data['surplus_num'],
                        'start_time'  => $data['start_time'],
                        'end_time'    => $data['end_time'],
                        'a_type'      => $data['a_type'] ?? 0,
                        'img'         => $data['a_type'] ?? '',
                        'btn_img'     => $data['btn_img'] ?? '',
                        'weight'      => $data['weight'] ?? 0
                ];
                $resource  = $data['resource'] ?? '';
                break;
            case self::GRANT_TYPE['recommend_gift'] :
                $re_id = self::find()->where(['name' => $data['name'], 'is_delete' => 0])->scalar();
                if (!empty($re_id) && (empty($data['id']) || $re_id != $data['id'])) {
                    return [false, '名称已存在'];
                }
                $ac_id   = $this->getActivityIdsByType($data['grant_type']);
                $ac_info = $this->getActivityOne($ac_id);

                if (!empty($ac_info) && empty($id)) {
                    return [false, '推荐有礼活动已存在'];
                }

                if (empty($data['poster_image'])) {
                    return [false, '页面海报不能为空'];
                }

                if (substr_count($data['poster_image'], "|") > self::PAGE_IMAGE_LIMIT) {
                    return [false, "页面海报数量最多" . self::PAGE_IMAGE_LIMIT . "张"];
                }

                if (empty($data['share_image'])) {
                    return [false, '分享海报不能为空'];
                }

                if (substr_count($data['share_image'], "|") > self::SHARE_IMAGE_LIMIT) {
                    return [false, "分享海报数量最多" . self::SHARE_IMAGE_LIMIT . "张"];
                }

                $is_reg_reward = isset($data['is_reg_reward']) ? CUtil::uint($data['is_reg_reward']) : 0;
                if ($is_reg_reward == 0) {
                    return [false, '邀请人注册是否奖励积分配置不正确'];
                }

                $inviter_reg_limit = isset($data['inviter_reg_limit']) ? CUtil::uint($data['inviter_reg_limit']) : 0;
                if ($inviter_reg_limit == 0) {
                    return [false, '邀请人注册奖励积分次数配置不正确'];
                }

                $order_min_money = by::Gtype0()->totalFee($data['order_min_money'] ?? 0); //分
                if ($order_min_money == 0) {
                    return [false, '下单金额配置不正确'];
                }

                $is_bind_reward = isset($data['is_bind_reward']) ? CUtil::uint($data['is_bind_reward']) : 0;
                if ($is_bind_reward == 0) {
                    return [false, '被邀请人绑定是否奖励积分配置不正确'];
                }

                $invitee_bind_limit = isset($data['invitee_bind_limit']) ? CUtil::uint($data['invitee_bind_limit']) : 0;
                if ($invitee_bind_limit == 0) {
                    return [false, '被邀请人绑定奖励次数配置不正确'];
                }

                $reward_type = isset($data['reward_type']) ? CUtil::uint($data['reward_type']) : 0;
                if (!in_array($reward_type, self::REWARD_TYPE)) {
                    return [false, '下单奖励类型不合法'];
                }

                if (empty($data['rule_note'])) {
                    return [false, '使用规则不能为空'];
                }

                $type_data = [
                        'poster_image'       => $data['poster_image'],
                        'share_image'        => $data['share_image'],
                        'is_reg_reward'      => $is_reg_reward,
                        'inviter_reg_limit'  => $inviter_reg_limit,
                        'order_min_money'    => $order_min_money,
                        'is_bind_reward'     => $is_bind_reward,
                        'invitee_bind_limit' => $invitee_bind_limit,
                        'reward_type'        => $reward_type,
                        'rule_note'          => $data['rule_note']
                ];

                if ($reward_type == self::REWARD_TYPE['coupon']) {
                    if (empty($data['market_config'])) {
                        return [false, '优惠券配置不正确'];
                    }

                    $market_config = (array) json_decode($data['market_config'], true);
                    if (count($market_config) > by::Am()::MAX_COUNT) {
                        return [false, '最多配置三张优惠券'];
                    }

                    $type_data['market_config'] = $market_config;
                }
                $resource = $data['resource'] ?? '';
                break;
            case self::GRANT_TYPE['common_activity']:
                $ac_id = $this->getActivityIdsByType($data['grant_type'], $data['id'] ?? 0);

                if (empty($data['rule_note'])) {
                    return [false, '活动规则说明不能为空'];
                }
                if (empty($data['share_title'])) {
                    return [false, '分享标题为空'];
                }
                if (empty($data['share_image'])) {
                    return [false, '分享图片为空'];
                }
                if (empty($data['start_time'])) {
                    return [false, '开始时间不能为空'];
                }

                if (empty($data['end_time'])) {
                    return [false, '结束时间不能为空'];
                }

                if ($data['start_time'] > $data['end_time']) {
                    return [false, '开始时间不能大于结束时间'];
                }

                $reward_type = isset($data['reward_type']) ? CUtil::uint($data['reward_type']) : 0;
                if (!in_array($reward_type, self::REWARD_TYPE)) {
                    return [false, '下单奖励类型不合法'];
                }
                $resource = [
                        'share_title' => $data['share_title'],
                        'share_image' => $data['share_image']
                ];

                $type_data = [
                        'ac_id'            => $ac_id,
                        'ac_image'         => $data['ac_image'] ?? '',
                        'back_image'       => $data['back_image'] ?? '',
                        'middleware_image' => $data['middleware_image'] ?? '',
                        'rule_note'        => $data['rule_note'] ?? '',
                        'remark'           => $data['remark'] ?? '',
                        'url'              => $data['url'] ?? '',
                        'user_type'        => $data['user_type'] ?? 0,
                        'platform'         => $data['platform'] ?? 99,
                        'reward_type'      => $reward_type ?? '',
                        'start_time'       => $data['start_time'] ?? '',
                        'end_time'         => $data['end_time'] ?? '',
                ];
                if ($reward_type == self::REWARD_TYPE['coupon']) {
                    if (empty($data['market_config'])) {
                        return [false, '优惠券配置不正确'];
                    }
                    $market_config = (array) json_decode($data['market_config'], true);
                    foreach ($market_config as $value) {
                        $marketConfigInfo = by::marketConfig()->getOneById($value['mc_id']);
                        if ($marketConfigInfo['stock_num'] <= 0) {
                            return [false, '库存不足,请检查'];
                        }
                        if ($value['stock'] <= 0 || $value['stock'] > $marketConfigInfo['stock_num']) {
                            return [false, '库存不合理,请检查'];
                        }
                    }
                    $stock                      = array_column($market_config, 'stock');
                    $type_data['surplus_num']   = array_sum(array_column($market_config, 'stock'));
                    $type_data['market_config'] = $market_config;
                }
                break;
            default :
                return [false, '活动类型不合法'];
        }

        $type_data['name']        = $data['name'];
        $type_data['grant_type']  = $data['grant_type'];
        $type_data['resource']    = json_encode($resource, 256);
        $type_data['help_order']  = $data['help_order'] ?? '';
        $type_data['update_time'] = time();
        return [true, $type_data];
    }


    /**
     * @throws Exception
     */
    public function getList($input, $page, $pageSize): array
    {
        $limit = CUtil::pagination($page, $pageSize);
        $query = $this->__getCondition($input);

        $list = $query->orderBy('id DESC')
                ->offset($limit[0])
                ->limit($limit[1])
                ->asArray()
                ->all();

        //是否需要数量
        $need_count = $input['need_count'] ?? true;
        if ($need_count) {
            $total = $query->count();
            $pages = CUtil::getPaginationPages($total, $pageSize);
        }


        //是否需要详情
        $need_detail = $input['need_detail'] ?? true;
        if ($need_detail) {
            foreach ($list as &$val) {
                $val = $this->__formatSingleData($val);
            }
        }


        return ['total' => intval($total ?? 0), 'pages' => $pages ?? 1, 'list' => $list];
    }

    /**
     * @throws Exception
     * 查询单条数据
     */
    public function getDetail($input)
    {
        $query = $this->__getCondition($input);
        $query = $query->orderBy('id DESC')->one();
        return $query ? $query->toArray() : [];
    }

    /**
     * 处理列表单条数据
     * @throws Exception
     */
    private function __formatSingleData($val)
    {
        $market_data = by::marketConfig()->getOneById($val['market_id']);
        switch ($val['grant_type']) {
            case self::GRANT_TYPE['newUser']:
                $m_name        = '';
                $acType1Data   = byNew::AcType1Model()->getInfoByAcId($val['id']);
                $val['coupon'] = by::aM()->getCouponStockByAcId($val['id']);

                if ($acType1Data) {
                    // 判断是否有优惠券
                    if ($acType1Data['has_coupon'] == AcType1Model::HAS_POINT_OR_COUPON['YES']) {
                        foreach ($val['coupon'] as $coupon) {
                            $m_name .= $coupon['name'] . ',库存:' . $coupon['stock'] . ',销量:' . $coupon['sales'] . ',总库存:' . $coupon['all_stock'] . ";" . "<br>";
                        }
                    }
                    // 判断是否有积分
                    if ($acType1Data['has_point'] == AcType1Model::HAS_POINT_OR_COUPON['YES']) {
                        $m_name .= '积分:' . $acType1Data['point_value'] . ',积分倍数:' . $acType1Data['point_multiplier'] . ',积分生效时间:' . date('Y-m-d H:i:s', $acType1Data['multiplier_start_time']) . '-' . date('Y-m-d H:i:s', $acType1Data['multiplier_end_time']);
                    }
                } else {
                    foreach ($val['coupon'] as $coupon) {
                        $m_name .= $coupon['name'] . ',库存:' . $coupon['stock'] . ',销量:' . $coupon['sales'] . ',总库存:' . $coupon['all_stock'] . ";" . "<br>";
                    }
                }

                $val['m_name'] = $m_name;

                break;
            case self::GRANT_TYPE['common_activity']:
            case self::GRANT_TYPE['monthly']:
            case self::GRANT_TYPE['birthday']:
            case self::GRANT_TYPE['goods_detail']:
            case self::GRANT_TYPE['auto_coupon']:
                $val['coupon'] = by::aM()->getCouponStockByAcId($val['id']);
                $m_name        = '';
                foreach ($val['coupon'] as $coupon) {
                    $m_name .= $coupon['name'] . ',库存:' . $coupon['stock'] . ',销量:' . $coupon['sales'] . ',总库存:' . $coupon['all_stock'] . ";" . "<br>";
                }
                $val['m_name'] = $m_name;
                break;
            default:
                $val['m_name'] = $market_data['name'] ?? '';
                $val['coupon'] = [];
        }

        return $val;
    }

    /**
     * @param $input
     * @return \yii\db\ActiveQuery
     * 查询条件配置
     */
    private function __getCondition($input): \yii\db\ActiveQuery
    {
        $query       = self::find()->andWhere(['is_delete' => 0]);
        $status      = CUtil::uint($input['status'] ?? 0);     //优惠券生效状态
        $name        = trim($input['name'] ?? '');             //活动名称
        $grant_type  = intval($input['grant_type'] ?? -1);     //活动类型
        $start_time  = CUtil::uint($input['start_time'] ?? 0); //活动开始时间
        $end_time    = CUtil::uint($input['end_time'] ?? 0);   //活动结束时间
        $nid         = CUtil::uint($input['nid'] ?? 0);        //不等于该活动ID
        $middle_time = CUtil::uint($input['middle_time'] ?? 0);//活动时间范围内的时间
        $isDel       = CUtil::uint($input['is_del'] ?? 0);
        if ($isDel) {
            $query = self::find()->andWhere(['is_delete' => 1]);
        }

        if ($status) {
            $time = time();
            switch ($status) {
                case 1://生效中
                    $query->andWhere(['>=', 'end_time', $time]);
                    $query->andWhere(['<=', 'start_time', $time]);
                    break;
                case 2://未生效
                    $query->andWhere(['>=', 'start_time', $time]);
                    break;
                case 3://已结束
                    $query->andWhere(['<=', 'end_time', $time]);
                    break;
                default://全部
                    break;
            }
        }

        if ($name) {
            $query->andWhere(['like', 'name', $name]);
        }
        if ($grant_type > 0) {
            $query->andWhere(['=', 'grant_type', $grant_type]);
        }
        if (!empty($start_time) && !empty($end_time)) {
            $query->andWhere(['>=', 'start_time', $start_time]);
            $query->andWhere(['<=', 'end_time', $end_time]);
        } else if (!empty($start_time) && empty($end_time)) {
            $query->andWhere(['>=', 'end_time', $start_time]);
        } else if (empty($start_time) && !empty($end_time)) {
            $query->andWhere(['<=', 'end_time', $end_time]);
        }
        if ($nid) {
            $query->andWhere(['<>', 'id', $nid]);
        }
        if ($middle_time) {
            $query->andWhere(['<=', 'start_time', $middle_time]);
            $query->andWhere(['>=', 'end_time', $middle_time]);
        }

        return $query;
    }


    /**
     * @deprecated 2023-07-06 废弃
     * @param $id
     * @param $data
     * @return array
     * @throws Exception
     * TOTO 后台管理修改添加 活动配置
     */
    public function modify($id, $data): array
    {
        //校验
        list($status, $type_data) = $this->_modifyCheck($id, $data);
        if (!$status) {
            return [$status, $type_data];
        }
        $ac_tb       = $this->tableName();
        $ac_type2_tb = by::acType2()->tbName();
        $ac_type3_tb = by::acType3()->tbName();
        $db          = $this->getDb();
        $transaction = $db->beginTransaction();
        try {
            $market_config = $type_data['market_config'] ?? [];
            $grant_type    = $type_data['grant_type'];
            if ($grant_type == self::GRANT_TYPE['recommend_gift']) {
                $ac_data = [
                        'name'        => $type_data['name'],
                        'grant_type'  => $type_data['grant_type'],
                        'resource'    => $type_data['resource'],
                        'help_order'  => $type_data['help_order'],
                        'create_time' => time(),
                        'update_time' => $type_data['update_time']
                ];

                unset(
                        $type_data['name'], $type_data['grant_type'], $type_data['market_config'], $type_data['resource'],
                        $type_data['help_order']
                );

            }

            if ($grant_type == self::GRANT_TYPE['common_activity']) {
                $ac_data = [
                        'name'        => $type_data['name'],
                        'grant_type'  => $type_data['grant_type'],
                        'resource'    => $type_data['resource'],
                        'help_order'  => $type_data['help_order'],
                        'create_time' => time(),
                        'update_time' => $type_data['update_time'],
                        'user_type'   => $type_data['user_type'],
                        'platform'    => $type_data['platform'],
                        'surplus_num' => $type_data['surplus_num'],
                        'start_time'  => $type_data['start_time'],
                        'end_time'    => $type_data['end_time']
                ];
                unset(
                        $type_data['name'], $type_data['grant_type'], $type_data['resource'], $type_data['help_order'], $type_data['user_type'],
                        $type_data['platform'], $type_data['market_config'], $type_data['surplus_num'], $type_data['start_time'], $type_data['end_time']
                );
            }

            //修改
            if (!empty($id)) {

                $record = self::findOne($id);
                if (empty($record)) {
                    throw new \Exception('记录不存在，无法修改', 1001);
                }

                if ($grant_type == $record['grant_type']) {
                    $delCache = true;
                } else {
                    $delCache = false;
                }

                switch ($grant_type) {
                    case self::GRANT_TYPE['newUser'] :

                        $type_data['update_time'] = time();//更新时间
                        //修改前回退旧的资源库存
                        if ($record['surplus_num'] > 0) {
                            list($status, $msg) = by::marketConfig()->stockModify($record['market_id'], -($record['surplus_num']));
                            if (!$status) {
                                throw new \Exception($msg, 1001);
                            }
                        }

                        //修改营销配置的库存
                        $type_data['last_data'] = json_encode(['surplus_num' => $type_data['surplus_num']]);

                        //销量
                        if ($type_data['market_id'] != $record['market_id']) {
                            $type_data['sales_num'] = 0;

                        }
                        //todo 更新活动配置表
                        $res = $db->createCommand()->update($ac_tb, $type_data, "`id`=:id", [":id" => $id])->execute();

                        if (!$res) {
                            throw new MyExceptionModel('更新活动配置表失败');
                        }

                        //活动配置增加，营销资源库存那边就减少
                        if ($type_data['surplus_num'] != 0) {
                            list($status, $msg) = by::marketConfig()->stockModify($type_data['market_id'], $type_data['surplus_num']);
                            if (!$status) {
                                throw new \Exception($msg, 1001);
                            }
                        }

                        break;

                    case self::GRANT_TYPE['recommend_gift'] :
                        //todo 更新活动配置表
                        $res = $db->createCommand()->update($ac_tb, $ac_data, "`id`=:id", [":id" => $id])->execute();
                        if (!$res) {
                            throw new MyExceptionModel('更新活动配置表失败（2）');
                        }

                        //todo 更新推荐有礼活动配置表
                        $res = $db->createCommand()->update($ac_type2_tb, $type_data, "`ac_id`=:ac_id", [":ac_id" => $id])->execute();
                        if (!$res) {
                            throw new MyExceptionModel('更新推荐有礼配置表失败');
                        }

                        //todo 更新活动绑定的优惠券
                        if ($type_data['reward_type'] == self::REWARD_TYPE['point']) {
                            list($status, $ac_m_ids) = by::Am()->getIdsByAid($id);
                            if (!$status) {
                                return [false, '活动优惠券不存在'];
                            }

                            foreach ($ac_m_ids as $ac_m_id) {
                                list($status, $msg) = by::Am()->del($ac_m_id);
                                if (!$status) {
                                    return [false, $msg];
                                }
                            }
                        }
                        break;
                    case self::GRANT_TYPE['common_activity']:

                        //todo 更新活动配置表
                        $res = $db->createCommand()->update($ac_tb, $ac_data, "`id`=:id", [":id" => $id])->execute();
                        if (!$res) {
                            throw new MyExceptionModel('更新活动配置表失败（3）');
                        }
                        //todo 更新通用礼包表


//                        //生成短链
//                        $urlData = ['path' => "pagesA/couponReceive/couponReceive", 'query' => "id={$id}", 'type' => 1];
//                        list($s, $res) = by::model('WxUlinkModel', 'main')->CreateLink($urlData);
//                        !YII_ENV_PROD && CUtil::debug(json_encode($res,320),'create.link');
//                        $type_data['url'] = $res['url_link'] ?? '';

                        $result = $db->createCommand()->update($ac_type3_tb, $type_data, "`ac_id`=:ac_id", [":ac_id" => $id])->execute();
                        if (!$result) {
                            throw new MyExceptionModel('更新推荐有礼配置表失败');
                        }


                        //todo 更新活动绑定的优惠券
                        if ($type_data['reward_type'] == self::REWARD_TYPE['point']) {
                            list($status, $ac_m_ids) = by::Am()->getIdsByAid($id);
                            if (!$status) {
                                return [false, '活动优惠券不存在'];
                            }

                            foreach ($ac_m_ids as $ac_m_id) {
                                list($status, $msg) = by::Am()->del($ac_m_id);
                                if (!$status) {
                                    return [false, $msg];
                                }
                            }
                        }
                        //修改成功删除缓存
                        $this->delActivityCache($id);

                        break;
                    default :
                        throw new MyExceptionModel('活动类型不合法（2）');
                }
            } else {

                switch ($grant_type) {
                    case self::GRANT_TYPE['newUser'] :
                        $type_data['create_time'] = time();//添加时间

                        //todo 新增活动配置表
                        $res = $db->createCommand()->insert($ac_tb, $type_data)->execute();
                        if (!$res) {
                            throw new MyExceptionModel('新增活动配置失败');
                        }

                        //活动配置增加，营销资源库存那边就减少
                        if ($type_data['surplus_num'] != 0) {
                            list($status, $msg) = by::marketConfig()->stockModify($data['market_id'], $type_data['surplus_num']);
                            if (!$status) {
                                throw new \Exception($msg, 1001);
                            }
                        }

                        break;
                    case self::GRANT_TYPE['recommend_gift'] :
                        //todo 新增活动配置表
                        $res = $db->createCommand()->insert($ac_tb, $ac_data)->execute();
                        if (!$res) {
                            throw new MyExceptionModel('新增活动配置失败（2）');
                        }

                        $ac_id              = $db->getLastInsertID();
                        $type_data['ac_id'] = $ac_id; //活动id

                        //todo 新增推荐有礼活动配置表
                        $res = $db->createCommand()->insert($ac_type2_tb, $type_data)->execute();
                        if (!$res) {
                            throw new MyExceptionModel('新增推荐有礼配置失败');
                        }

                        //todo 新增活动绑定的优惠券
                        if ($type_data['reward_type'] == self::REWARD_TYPE['coupon']) {
                            foreach ($market_config as $mc) {
                                list($status, $msg) = by::Am()->add($ac_id, $mc['mc_id'], $mc['stock']);
                                if (!$status) {
                                    return [false, $msg];
                                }
                            }
                        }

                        break;
                    case self::GRANT_TYPE['common_activity']:
                        $res = $db->createCommand()->insert($ac_tb, $ac_data)->execute();

                        if (!$res) {
                            throw new MyExceptionModel('新增活动配置失败（4）');
                        }
                        $ac_id = $db->getLastInsertID();

                        $type_data['ac_id'] = $ac_id; //活动id
                        //todo 通用礼包活动配置表
                        $type_data['create_time'] = time();

                        //生成短链
                        $urlData = ['path' => "pagesA/couponReceive/couponReceive", 'query' => "id={$ac_id}", 'type' => 1];
                        list($s, $res) = by::model('WxUlinkModel', 'main')->CreateLink($urlData);
                        $type_data['url'] = $res['url_link'] ?? '';

                        $res = $db->createCommand()->insert($ac_type3_tb, $type_data)->execute();
                        if (!$res) {
                            throw new MyExceptionModel('通用礼包配置失败');
                        }
                        //todo 新增活动绑定的优惠券
                        if ($type_data['reward_type'] == self::REWARD_TYPE['coupon']) {
                            foreach ($market_config as $mc) {
                                list($status, $msg) = by::Am()->add($ac_id, $mc['mc_id'], $mc['stock']);
                                if (!$status) {
                                    return [false, $msg];
                                }
                            }
                        }
                        break;
                    default :
                        throw new MyExceptionModel('活动类型不合法（3）');
                }

                $delCache = true;
            }

            $transaction->commit();

            //删除缓存
            if ($delCache) {
                $this->delCache($id, $grant_type);
            } else {
                $this->delCache($id);
            }
            by::Actype3()->delAcTypeCache($id);
            by::Actype2()->delCache($id);
        } catch (MyExceptionModel $e) {
            $transaction->rollBack();

            if ($e->getCode() == 1001) {
                return [false, $e->getMessage()];
            }

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);
        } catch (\Exception $e) {
            $transaction->rollBack();
            CUtil::debug($e->getMessage(), 'coupon.error');
            return [false, '操作失败'];
        }

        return [true, '操作成功'];
    }


    /**
     * @param $grant_type
     * @param string $user_id
     * @param int $limit
     * @param bool $checkStock
     * @return array
     * @throws Exception
     * 获取当前活动列表
     */
    public function getActivity($grant_type, string $user_id = '', int $limit = 20, bool $checkStock = true): array
    {
        $limit      = CUtil::uint($limit);
        $redis      = by::redis('core');
        $redis_key  = AppCRedisKeys::activityConfig($grant_type);
        $cache_data = (array) json_decode($redis->get($redis_key), true);

        if ($cache_data == false) {
            $table = self::tableName();
            $time  = time();

            switch ($grant_type) {
                case self::GRANT_TYPE['newUser']:
                case self::GRANT_TYPE['monthly']:
                    $where = "";
                    // if($checkStock){
                    //     $where = " AND `surplus_num` > 0 ";
                    // }

                    $sql = "SELECT `id`,`end_time` FROM {$table} WHERE `grant_type` = {$grant_type} AND `start_time` < {$time} 
                                    AND `end_time` > {$time} {$where} AND `is_delete` = 0  ORDER BY `id` DESC limit {$limit}";

                    break;
                case self::GRANT_TYPE['recommend_gift']:
                    $sql = "SELECT `id` FROM {$table} WHERE `grant_type` = {$grant_type} AND `is_delete` = 0 limit 1";
                    break;
                case self::NO_ARR_MONTHLY:
                    $grant_type = self::GRANT_TYPE['monthly'];
                    $first      = date('Y-m-01', $time);
                    $etime      = strtotime("$first +1 month -1 day") + 24 * 3600;

                    $sql = "SELECT `id`,`end_time` FROM {$table} WHERE `grant_type` = {$grant_type} AND `start_time` > {$time} 
                                    AND `end_time` < {$etime}  AND `is_delete` = 0  ORDER BY `id` DESC limit {$limit}";
                    break;
                default :
                    return [];
            }

            $cache_data = by::dbMaster()->createCommand($sql)->queryAll() ?? [];

            $expire_time = $this->expire_time;
            foreach ($cache_data as &$v) {
                if (isset($v['end_time']) && $v['end_time'] - $time < $expire_time) {
                    $expire_time = $v['end_time'] - $time;
                }
            }

            $redis->set($redis_key, json_encode($cache_data), ['ex' => $expire_time]);
        }

        unset($v);
        //组装资源数据
        $list = [];
        foreach ($cache_data as $v) {
            $info = $this->getActivityOne($v['id']);
            if (empty($info)) {
                continue;
            }
            switch ($info['grant_type']) {
                case self::GRANT_TYPE['newUser'] :
                    // 初始化优惠券和积分列表
                    $list['coupon'] = [];
                    $list['point']  = [];

                    // 获取活动类型1的相关信息
                    $acType1Info = byNew::AcType1Model()->getInfoByAcId($v['id']);

                    if (!empty($acType1Info)) {
                        $list['has_coupon'] = $acType1Info['has_coupon'] ?? 0;
                        $list['has_point']  = $acType1Info['has_point'] ?? 0;

                        // 处理优惠券逻辑
                        if ($list['has_coupon'] == byNew::AcType1Model()::HAS_POINT_OR_COUPON['YES']) {
                            $acmList    = by::aM()->getListByAid($info['id'] ?? 0);
                            $couponList = [];

                            foreach ($acmList as $item) {
                                $couponInfo = by::marketConfig()->couponCondition($item['mc_id'] ?? 0);

                                if (!empty($couponInfo)) {
                                    // 更新库存信息
                                    $couponInfo['surplus_num'] = $item['stock'] ?? 0;
                                    $couponList[]              = $couponInfo;
                                }
                            }

                            $list['coupon'] = $couponList;
                        }

                        // 处理积分逻辑
                        if ($list['has_point'] == byNew::AcType1Model()::HAS_POINT_OR_COUPON['YES']) {
                            if ($acType1Info['multiplier_start_time'] <= time() && time() < $acType1Info['multiplier_end_time']) {
                                $point = bcmul($acType1Info['point_value'] ?? 0, $acType1Info['point_multiplier'] ?? 0);
                            } else {
                                $point = $acType1Info['point_value'] ?? 0;
                            }

                            $rate            = byNew::PointConfigModel()::getConfig()['deduction_rate'] ?? 100;
                            $deductiblePrice = bcdiv($point, $rate, 2);
                            $list['point'] = [
                                    'point_multiplier' => $acType1Info['point_multiplier'],
                                    'point_value'      => $point,
                                    'deductible_price' => $deductiblePrice, //可抵扣金额
                            ];
                        }
                    }

                    // $couponInfo = by::marketConfig()->couponCondition($info['market_id']);
                    // $couponInfo['surplus_num'] = $info['surplus_num'] ?? 0;
                    // $list[] = $couponInfo;
                    break;
                case self::GRANT_TYPE['monthly']:
                    //多张优惠卷处理
                    $acmList = by::aM()->getListByAid($info['id'] ?? 0);
                    //获取用户level
                    $level = strtolower(by::memberCenterModel()->GetUserLevel($user_id));
                    foreach ($acmList as $item) {
                        if ($this->isIncludeLevel($item['level'], $level)) {
                            $couponInfo = by::marketConfig()->couponCondition($item['mc_id'] ?? 0);
                            if (empty($couponInfo)) continue;
                            //判断优惠券有没有过期
                            if (CUtil::uint($couponInfo['expire_time']) > intval(START_TIME)) {
                                $couponInfo['surplus_num'] = $item['stock'];
                                $couponInfo['ac_s_time']   = $info['start_time'] ?? 0;
                                $couponInfo['ac_e_time']   = $info['end_time'] ?? 0;
                                $list[]                    = $couponInfo;
                            }
                        }
                    }
                    break;
                case self::GRANT_TYPE['recommend_gift'] :
                    if (isset($info['reward_type']) && $info['reward_type'] == self::REWARD_TYPE['coupon']) {
                        $am_list = by::aM()->getListByAid($info['id']);
                        foreach ($am_list as $am_info) {
                            if (empty($am_info)) {
                                continue;
                            }

                            if (empty($am_info['stock']) && isset($am_info['last_user'])) {
                                if ($user_id != $am_info['last_user']) {
                                    continue;
                                } else {
                                    $tb     = by::aM()::tbName();
                                    $params = [':last_user' => 0, ':id' => $am_info['id']];
                                    $sql    = "UPDATE {$tb} SET last_user = :last_user WHERE id = :id";
                                    by::dbMaster()->createCommand($sql, $params)->execute();

                                    by::aM()->__delCache($info['id'], $am_info['id']);
                                }
                            }
                            $couponInfo = by::marketConfig()->couponCondition($am_info['mc_id']);
                            if (empty($couponInfo)) continue;
                            $couponInfo['surplus_num'] = $info['surplus_num'] ?? 0;
                            $list[]                    = $couponInfo;
                        }
                    }

                    break;
                default :
                    break;
            }


        }

        return $list;
    }

    /**
     * @param int $id
     * @param int $grant_type
     * @return bool
     * TODO 删除缓存
     * @throws
     */
    public function delCache(int $id = 0, int $grant_type = 0): bool
    {
        $one_key = AppCRedisKeys::getActivityOne($id);
        by::redis()->del($one_key);

        if ($grant_type) {
            $redis_key  = AppCRedisKeys::activityConfig($grant_type);
            $redis_key1 = AppCRedisKeys::activityConfig($grant_type, $id);
            by::redis()->del($redis_key, $redis_key1);
        }
        return true;
    }

    /**
     * @param $ac_id
     * @return void
     * @throws RedisException
     */
    public function delActivityCache($ac_id)
    {
        $redis      = by::redis('core');
        $redis_key1 = AppCRedisKeys::getActivityOne($ac_id);
        $redis_key2 = by::aM()->__getCouponListByAcId($ac_id);
        $redis->del($redis_key1, $redis_key2);
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除活动配置
     */
    public function deleteData($id): array
    {
        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $data = self::find()->where(['id' => $id, 'is_delete' => 0])->one();
        if (empty($data)) {
            return [false, '活动不存在'];
        }
        $transaction = by::dbMaster()->beginTransaction();
        try {
            $data->update_time = time();
            $data->is_delete   = 1;
            $data->update();
            //修改后回退旧的资源库存，退还库存
            $amList = by::aM()->getListByAid($data->id);
            foreach ($amList as $am) {
                list($status, $msg) = by::marketConfig()->stockModify($am['mc_id'], -($am['stock']));
                if (!$status) {
                    throw new \Exception($msg);
                }
            }

            //删除活动配置表
           (new AcType7Model())->del($id,$data->grant_type);

            $transaction->commit();
            //删除缓存
            $this->delCache($id, $data->grant_type);
            return [true, '操作成功'];
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);
            return [false, $e->getMessage()];
        }
    }

    /**
     * @param $user_id //用户id
     * @param $ids //活动礼包id集 (1,2,3)
     * @return array
     * @throws Exception
     * 领取新人礼
     */
    public function userDraw($user_id, $ids): array
    {

        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__, $ids);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }

        $ids = explode(',', CUtil::trimTags($ids));
        $ids = array_unique($ids);
        if (empty($ids)) {
            return [false, '参数错误'];
        }
        //优惠卷ID
        $marketConfig = by::aM()->getListByAid($ids[0]);
        $marketIds    = [];
        foreach ($marketConfig as $item) {
            if ($item['stock'] > 0) {
                $marketIds[] = $item['mc_id'];
            }
        }

        $userInfo = by::users()->getOneByUid($user_id, false);
        if ($userInfo['is_new_gift'] != 0) {
            return [false, '新人礼包只能领取一次哟!'];
        }
        $transaction = by::dbMaster()->beginTransaction();
        try {
            //发放奖励
            // list($status, $msg) = $this->activityDraw($user_id, self::GRANT_TYPE['newUser'], $ids);
            list($status, $msg) = $this->activityDraw($user_id, self::GRANT_TYPE['newUser'], $ids, $marketIds);
            if (!$status) {
                throw new Exception($msg);
            }

            //###################################更新用户新人礼包领取状态
            $bool = by::users()->updateMembersInfo($user_id, ['is_new_gift' => 1]);
            if (!$bool) {
                throw new Exception('新人礼包状态更新失败!');
            }

            $transaction->commit();

            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');

            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();

            //删除并发标识
            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);

            return [false, '领取失败'];
        }

    }


    /**
     * @param $user_id
     * @param $ids
     * @param $market_ids
     * @param $r_id
     * @return array
     * @throws Exception
     */
    public function userCommonDraw($user_id, $ids, $market_ids, $r_id)
    {
        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__, $ids);
        list($anti) = self::ReqAntiConcurrency($user_id, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }
        $redis = by::redis('core');
        //获取活动详情
        $aData = $this->getActivityOne($ids);
        if (intval(START_TIME) < $aData['start_time'] ?? 0) {
            return [false, '当前活动还未开始'];
        }
        if (isset($aData['end_time']) && $aData['end_time'] < intval(START_TIME)) {
            return [false, '活动已结束'];
        }
        $ids = explode(',', CUtil::trimTags($ids));
        $ids = array_filter(array_unique($ids));
        if (empty($ids)) {
            return [false, '参数错误'];
        }
        $market_ids     = explode(',', CUtil::trimTags($market_ids));
        $market_ids     = array_unique($market_ids);
        $stockMarketIds = $market_ids;
        if (empty($market_ids)) {
            return [false, '优惠券不存在'];
        }


        //验证是否领取过
        list($status, $data) = by::userCard()->getList($user_id, by::userCard()::SOURCE['INDEX']);
        $cardList          = $data['list'] ?? [];
        $myRecevieActivity = array_unique(array_column($cardList, 'get_relation'));
        $relationArr       = [];
        if ($cardList) {
            foreach ($cardList as $card) {
                if ($card['get_relation'] == $ids[0]) {
                    $relationArr[] = $card;
                }
            }
        }
        $haveMarketIds = array_column($relationArr, 'market_id') ?? [];

        $newMarketIds = [];
        $noStockNum   = 0;
        foreach ($market_ids as $k => $market_id) {
            $m_key       = CUtil::getAllParams(__FUNCTION__, $ids[0] ?? 0, $market_id);
            $stockResult = $redis->get($m_key);
            if ($stockResult) {
                continue;
            }

            //验证是否没有库存
            $couponNum = by::aM()->getCouponNumByAidAndMid($ids[0], $market_id);
            if ($couponNum <= 0) {
                $noStockNum += 1;
                by::aM()->__delCouponList($ids[0]);
                by::marketConfig()->_delCache($market_id);
                by::aM()->__delAmList($ids[0]);
                $this->delActivityCache($ids[0]);
                continue;
            } elseif ($couponNum == 1) {
                $redis->set($m_key, 1, 5);
            }
            //如果领取过跳过领取此条优惠卷
            if (in_array($market_id, $haveMarketIds) && in_array($ids[0], $myRecevieActivity)) {
                continue;
            }
            $newMarketIds[] = $market_id;
        }
        //如果库存都没有了，活动直接结束
        if (count($stockMarketIds) == $noStockNum) {
            return [false, '优惠券已领光（1）'];
        }
        if (empty($newMarketIds)) {
            return [false, '优惠券已领光'];
        }
        $transaction = by::dbMaster()->beginTransaction();

        try {
            //发放奖励
            list($status, $msg) = $this->activityDraw($user_id, self::GRANT_TYPE['common_activity'], $ids, $newMarketIds, $r_id);
            if (!$status) {
                throw new Exception($msg);
            }

            $transaction->commit();

            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');

            by::userCard()->__delCache($user_id);

            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();

            //删除并发标识
            self::ReqAntiConcurrency($user_id, $unique_key, 0, 'DEL');

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);

            return [false, '领取失败'];
        }

    }


    /**
     * @param $user_id
     * @param $grant_type
     * @param $ids
     * @param array $market_ids
     * @param  $r_id
     * @return array
     * 活动优惠券领取
     */
    public function activityDraw($user_id, $grant_type, $ids, $market_ids = [], $r_id = 0): array
    {
        if ((empty($ids) || empty($market_ids)) && $grant_type != self::GRANT_TYPE['newUser']) return [false, '优惠券不存在~'];
        $acDrawLogModel = by::AcDrawLogModel();

        // 获取手机号
        $phone = by::Phone()->GetPhoneByUid($user_id);
        if (empty($phone)) {
            return [false, '用户未填写手机号，无法领取！'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        try {
            foreach ($ids as $id) {
                $info = $this->getActivityOne($id);
                if (empty($info) || $info['grant_type'] != $grant_type) {
                    continue;
                }
                switch ($grant_type) {
                    case ActivityConfigModel::GRANT_TYPE['newUser']:
                    case ActivityConfigModel::GRANT_TYPE['common_activity']:
                    case ActivityConfigModel::GRANT_TYPE['birthday']:
                    case ActivityConfigModel::GRANT_TYPE['monthly']:
                    case ActivityConfigModel::GRANT_TYPE['goods_detail']:
                        $drawMid = [];
                        foreach ($market_ids as $market_id) {
                            list($status, $data) = by::userCard()->setCard($user_id, $market_id, by::userCard()::GET_CHANNEL['activity'], $id, 1, $r_id);
                            if ($status) {
                                $acmid = by::aM()->getIdByAidAndMid($id, $market_id);
                                //###################################更新礼包库存，当礼包库存为零时过滤
                                list($s, $msg) = by::aM()->stockModify($id, $acmid, 1);
                                if ($s) $drawMid[] = $market_id;
                            }
                        }
                        if (empty($drawMid) && $grant_type != self::GRANT_TYPE['newUser']) throw new Exception("优惠券已经领光了！");
                        break;
                    default:
                        break;
                }

                //保存 领取日志
                $logData = [
                        'user_id'    => $user_id,
                        'phone'      => $phone,
                        'grant_type' => $grant_type,
                        'ac_id'      => $id,
                        'market_ids' => implode(',', $market_ids),
                ];
                // 更新用户新人礼包领取状态 + 如果配置积分推送IOT增加积分
                if ($grant_type == ActivityConfigModel::GRANT_TYPE['newUser']) {
                    $acTypeInfo = byNew::AcType1Model()->getInfoByAcId($id);
                    if (isset($acTypeInfo['has_point']) && $acTypeInfo['has_point'] == AcType1Model::HAS_POINT_OR_COUPON['YES']) {
                        $point = $acTypeInfo['point_value'] ?? 0;

                        // 使用时间比较函数
                        $currentTime = time();
                        if ($acTypeInfo['multiplier_start_time'] <= $currentTime && $currentTime <= $acTypeInfo['multiplier_end_time']) {
                            $point = bcmul($point, $acTypeInfo['point_multiplier']);
                        }
                        \Yii::$app->queue->delay(self::DELAY_TIME)->push(new MemberRegJob([
                                'user_id' => $user_id,
                                'point'   => $point
                        ]));
                        $logData['point'] = $point;
                    }

                    $updateResult = by::users()->updateMembersInfo($user_id, ['is_new_gift' => 1]);
                    if (!$updateResult) {
                        throw new Exception('新人礼包状态更新失败!');
                    }
                }

                list($s, $msg) = $acDrawLogModel->SaveLog($logData);
                if (!$s) {
                    throw new Exception("用户领取记录失败！");
                }


            }
            $transaction->commit();

            //清除缓存
            by::AcDrawLogModel()->__delCacheOne($phone);

            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();
            return [false, $e->getMessage()];
        }

    }

    /**
     * 领取活动礼包
     * @param $user_id //用户id
     * @param $grant_type //活动类型
     * @param $ids //活动礼包id集 [1,2,3]
     * @return array
     * @throws Exception
     */
    public function activityDrawOld($user_id, $grant_type, $ids, $market_ids = [], $r_id = 0): array
    {
        $transaction = by::dbMaster()->beginTransaction();
        $market_ids  = (array) $market_ids;
        try {
            $joinId = '';

            foreach ($ids as $id) {
                $info = $this->getActivityOne($id);

                $market_id = $info['market_id'] ?? '';
                if (empty($info) || $info['grant_type'] != $grant_type) {
                    continue;
                }

                switch ($grant_type) {
                    case ActivityConfigModel::GRANT_TYPE['newUser']:
                        $num = count($market_ids);

                        foreach ($market_ids as $market_id) {

                            $marketConfig = by::marketConfig()->getOneById($market_id);

                            $markerType = MarketConfigModel::TYPE;

                            if (empty($marketConfig['type']) || !in_array($marketConfig['type'], $markerType)) {
                                throw new Exception("资源id：{$market_id}数据有误");
                            }
                            $joinId = by::aM()->getIdByAidAndMid($id, $market_id);
                            //获取资源信息
                            switch ($marketConfig['type']) {
                                case $markerType['voucher']:
                                case $markerType['coupon'] :
                                    //写入券类
                                    list($status) = by::userCard()->setCard($user_id, $market_id, by::userCard()::GET_CHANNEL['activity'], $id, 1, $r_id);
                                    if (!$status) {
                                        throw new Exception("用户获取券类id：{$market_id}失败");
                                    }
                                    break;
                            }

                            //###################################更新礼包库存，当礼包库存为零时过滤

                            // $res = $this->stockModify($id, $info['surplus_num'], $info['grant_type'],$num);
                            $amRes = by::aM()->stockModify($id, $joinId, 1);
                            // if (!$res || !$amRes) {
                            //     continue;
                            // }
                        }


                        // $marketConfig = by::marketConfig()->getOneById($market_id);
                        // $markerType = MarketConfigModel::TYPE;
                        // if (empty($marketConfig['type']) || !in_array($marketConfig['type'], $markerType)) {
                        //     throw new Exception("资源id：{$market_id}数据有误");
                        // }
                        // //获取资源信息
                        // switch ($marketConfig['type']) {
                        //     case $markerType['voucher']:
                        //     case $markerType['coupon'] :
                        //         //写入券类
                        //         list($status) = by::userCard()->setCard($user_id, $market_id, by::userCard()::GET_CHANNEL['activity'], $id);
                        //         if (!$status) {
                        //             throw new Exception("用户获取券类id：{$market_id}失败");
                        //         }
                        //         break;
                        // }
                        // //###################################更新礼包库存，当礼包库存为零时过滤
                        // $res = $this->stockModify($id, $info['surplus_num'], $info['grant_type']);
                        // $amRes = by::aM()->stockModify($id, $joinId, 1);
                        // if (!$res || !$amRes) {
                        //     continue;
                        // }
                        break;
                    case ActivityConfigModel::GRANT_TYPE['common_activity']:
                        $num = count($market_ids);
                        foreach ($market_ids as $market_id) {
                            $marketConfig = by::marketConfig()->getOneById($market_id);
                            $markerType   = MarketConfigModel::TYPE;
                            if (empty($marketConfig['type']) || !in_array($marketConfig['type'], $markerType)) {
                                throw new Exception("资源id：{$market_id}数据有误");
                            }
                            $joinId = by::aM()->getIdByAidAndMid($id, $market_id);
                            //获取资源信息
                            switch ($marketConfig['type']) {
                                case $markerType['voucher']:
                                case $markerType['coupon'] :
                                    //写入券类
                                    list($status) = by::userCard()->setCard($user_id, $market_id, by::userCard()::GET_CHANNEL['activity'], $id, 1, $r_id);
                                    if (!$status) {
                                        throw new Exception("用户获取券类id：{$market_id}失败");
                                    }
                                    break;
                            }
                            //###################################更新礼包库存，当礼包库存为零时过滤
                            $res   = $this->stockModify($id, $info['surplus_num'], $info['grant_type'], $num);
                            $amRes = by::aM()->stockModify($id, $joinId, 1);
                            if (!$res || !$amRes) {
                                continue;
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
            $transaction->commit();

            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();
            return [false, $e->getMessage()];
        }
    }


    /**
     * @param $id
     * @param $surplus_num
     * @param int $grant_type
     * @param int $num
     * @return bool
     * @throws Exception 更新礼包库存
     */
    public function stockModify($id, $surplus_num, $grant_type = 0, $num = 1)
    {
        switch ($grant_type) {
            case ActivityConfigModel::GRANT_TYPE['newUser']:
                $surplus_num = $surplus_num - 1;
                break;
            case ActivityConfigModel::GRANT_TYPE['common_activity']:
                $surplus_num = $surplus_num - $num;
                break;
        }
        if ($surplus_num < 0) {
            return false;
        }
        $tb  = self::tableName();
        $sql = "UPDATE {$tb} SET surplus_num = :num, sales_num = sales_num + 1 WHERE id = :id AND surplus_num - :num_1 >= 0 LIMIT 1";
        $res = by::dbMaster()->createCommand($sql)
                ->bindParam(':num', $surplus_num)
                ->bindParam(':num_1', $surplus_num)
                ->bindParam(':id', $id)
                ->execute();
        if (!$res) {
            return false;
        }

        //删除缓存
        $this->delCache($id, $grant_type);
        return true;
    }

    /**
     * 获取单个活动配置详情
     * @param $id
     * @return array|false
     * @throws Exception
     */
    public function getActivityOne($id)
    {
        $id = CUtil::uint($id);
        if ($id == 0) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::getActivityOne($id);
        $data      = $redis->get($redis_key);
        $aData     = (array) json_decode($data, true);

        if ($aData == false) {
            $tb    = self::tableName();
            $sql   = "SELECT {$this->_filed} FROM {$tb} WHERE `id`=:id AND `is_delete`= 0 LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, ['id' => $id])->queryOne();
            $aData = $aData ?: [];

            $redis->set($redis_key, json_encode($aData), empty($aData) ? 10 : $this->expire_time);
        }

        if (!empty($aData['resource'])) {
            $aData['resource'] = json_decode($aData['resource'], true);
        }

        if (!empty($aData['help_order'])) {
            $aData['help_order'] = json_decode($aData['help_order'], true);
        }

        $grant_type = $aData['grant_type'] ?? 0;

        switch ($grant_type) {
            case self::GRANT_TYPE['newUser']:
                $ac_type_info = byNew::AcType1Model()->getInfoByAcId($id);

                $aData['has_point']             = $ac_type_info['has_point'] ?? '';
                $aData['point_value']           = $ac_type_info['point_value'] ?? '';
                $aData['point_multiplier']      = $ac_type_info['point_multiplier'] ?? '';
                $aData['multiplier_start_time'] = $ac_type_info['multiplier_start_time'] ?? '';
                $aData['multiplier_end_time']   = $ac_type_info['multiplier_end_time'] ?? '';
                $aData['has_coupon']            = $ac_type_info['has_coupon'] ?? '';
                break;
            case self::GRANT_TYPE['recommend_gift'] :
                $ac_type_info                = by::acType2()->getInfoByAcId($id);
                $aData['poster_image']       = $ac_type_info['poster_image'] ?? '';
                $aData['share_image']        = $ac_type_info['share_image'] ?? '';
                $aData['is_reg_reward']      = $ac_type_info['is_reg_reward'] ?? 0;
                $aData['inviter_reg_limit']  = $ac_type_info['inviter_reg_limit'] ?? 0;
                $aData['order_min_money']    = by::Gtype0()->totalFee($ac_type_info['order_min_money'] ?? 0, 1); //元
                $aData['is_bind_reward']     = $ac_type_info['is_bind_reward'] ?? 0;
                $aData['invitee_bind_limit'] = $ac_type_info['invitee_bind_limit'] ?? 0;
                $aData['reward_type']        = $ac_type_info['reward_type'] ?? 0;
                $aData['rule_note']          = $ac_type_info['rule_note'] ?? '';
                break;
            case self::GRANT_TYPE['common_activity'] :
                $ac_type_info              = by::acType3()->getInfoByAcId($id);
                $aData['ac_image']         = $ac_type_info['ac_image'] ?? '';
                $aData['back_image']       = $ac_type_info['back_image'] ?? '';
                $aData['middleware_image'] = $ac_type_info['middleware_image'] ?? '';
                $aData['rule_note']        = $ac_type_info['rule_note'] ?? '';
                $aData['remark']           = $ac_type_info['remark'] ?? '';
                $aData['url']              = $ac_type_info['url'] ?? '';
                $aData['url_type']         = $ac_type_info['url_type'] ?? '';
                $aData['reward_type']      = $ac_type_info['reward_type'] ?? '';
                $aData['share_title']      = $aData['resource']['share_title'] ?? '';
                $aData['share_image']      = $aData['resource']['share_image'] ?? '';
                break;
            case self::GRANT_TYPE['auto_coupon'] :
                $ac_type_info        = by::acType7()->getInfoByAcId($id);
                $aData['skus']       = Collection::make($ac_type_info)->map(function ($item) {
                    return [
                            'sku' => $item['sku'],
                            'id'    => $item['id'],
                    ];
                })->sortBy('id')->values()->toArray();

                $attributes=$aData['attributes'] ?? '';
                $aData['attributes'] = json_decode($attributes, true);
                break;
            default :
                break;
        }

        return $aData;
    }

    /**
     * @param $market_id
     * @return array
     * @throws Exception
     * 组装资源数据
     */
    private function _market($market_id)
    {
        $market_data = by::marketConfig()->getOneById($market_id);
        $data        = [];
        if (!empty($market_data)) {
            $data['images']        = $market_data['images'];
            $data['type']          = $market_data['type'];
            $data['discount']      = $market_data['discount'] ?? 0;
            $data['resource_type'] = $market_data['resource_type'] ?? 0;
            if ($market_data['type'] != by::marketConfig()::TYPE['coupon']) {
                $data['m_num'] = $market_data['resource_num'];
            } else {
                $data['m_num'] = 1;
            }
            $data['name'] = $type_name = $data['type'] == by::userCard()::TYPE['coupon'] ? '优惠券' : '消费券';

            list($data['discount'], $data['type_name']) = by::marketConfig()->resourceType($data['resource_type'], $market_data['discount'], $type_name);

        }
        return $data;
    }


    /**
     * 获取单个活动配置详情
     * @param $grant_type
     * @param  $ac_id
     * @return array
     * @throws Exception
     */
    public function getActivityByType($grant_type, $ac_id = 0): array
    {
        $redis     = by::redis('core');
        $redis_key = AppCRedisKeys::activityConfig($grant_type, $ac_id);
        $aData     = (array) json_decode($redis->get($redis_key), true);

        if ($aData == false) {
            $table = self::tableName();
            $time  = time();

            switch ($grant_type) {
                case self::GRANT_TYPE['newUser']:
                case self::GRANT_TYPE['monthly']://获取当月领劵的活动
                case self::GRANT_TYPE['goods_detail']:
                case self::GRANT_TYPE['auto_coupon']:
                    $sql = "SELECT `id`,`end_time`,`start_time` FROM {$table} WHERE `grant_type` = {$grant_type} AND `start_time` < {$time} 
                                    AND `end_time` > {$time} AND `is_delete` = 0  ORDER BY `id` DESC";
                    break;

                case self::GRANT_TYPE['recommend_gift']:
                    $sql = "SELECT `id` FROM {$table} WHERE `grant_type` = {$grant_type} AND `is_delete` = 0 limit 1";
                    break;
                case self::GRANT_TYPE['common_activity']:
                case self::GRANT_TYPE['birthday']: // 生日活动
                    $ac_id
                            ? $sql = "SELECT `id` FROM {$table} WHERE `grant_type` = {$grant_type} AND `is_delete` = 0 AND `id` = {$ac_id} limit 1"
                            : $sql = "SELECT `id` FROM {$table} WHERE `grant_type` = {$grant_type} AND `is_delete` = 0 limit 1";
                    break;
                default :
                    return [];
            }

            $aData = by::dbMaster()->createCommand($sql)->queryAll() ?? [];

            $expire_time = $this->expire_time;
            foreach ($aData as &$v) {
                if (isset($v['end_time']) && $v['end_time'] - $time < $expire_time) {
                    $expire_time = $v['end_time'] - $time;
                }
            }

            $redis->set($redis_key, json_encode($aData), ['ex' => $expire_time]);
        }
        return $aData;
    }


    /**
     * @param $grant_type
     * @param int $ac_id
     * @return string
     * @throws Exception 获取活动ids
     */
    public function getActivityIdsByType($grant_type, $ac_id = 0): string
    {
        $cfg = by::activityConfigModel()->getActivityByType($grant_type, $ac_id);

        if (empty($cfg)) {
            return '';
        }

        switch ($grant_type) {
            case self::GRANT_TYPE['newUser']:
            case self::GRANT_TYPE['monthly']:
            case self::GRANT_TYPE['goods_detail']:
                $id_arr = [];
                foreach ($cfg as $item) {
                    array_push($id_arr, $item['id']);
                }
                $ids = implode(",", $id_arr);
                break;
            case self::GRANT_TYPE['recommend_gift']:
            case self::GRANT_TYPE['common_activity']:
            case self::GRANT_TYPE['birthday']:
                $ids = $cfg[0]['id'];
                break;
            default :
                $ids = '';
                break;
        }

        return $ids;
    }

    /**
     * @param $grant_type
     * @return array|false
     * @throws Exception
     * 通过类型获取单个活动详情
     */
    public function getInfoByType($grant_type)
    {
        $grant_type = CUtil::uint($grant_type);
        if ($grant_type == 0) {
            return [];
        }

        $ac_model = by::activityConfigModel();
        $ac_id    = $ac_model->getActivityIdsByType($grant_type);
        $ac_info  = $ac_model->getActivityOne($ac_id);

        return $ac_info ?? [];
    }

    /**
     * @param $post
     * @param $userId
     * @return array|void
     * 获取自定义优惠券列表
     * @throws Exception
     */
    public function getMarketDefineList($post, $userId)
    {
        //参数校验
        $defineType = CUtil::uint($post['define_type'] ?? 0);
        if (empty($defineType)) {
            return [false, '优惠券类型必填！'];
        }

        //获取所有的优惠券列表
        $ids = by::marketDefine()->GetList(['define_type' => $defineType], 1, 0);

        //用户已有的过滤
        $inUseCards = [];
        if (strlen($userId) < 10 && !empty(CUtil::uint($userId))) {//存在用户,获取用户所有未使用的优惠券,不区分获得方式
            list($status, $data) = by::userCard()->getList($userId, by::userCard()::SOURCE['INDEX'], ['get_channel' => by::userCard()::GET_CHANNEL['internal_purchase']]);
            $cardList = $data['list'] ?? [];
            if ($cardList) {
                foreach ($cardList as $li) {
                    if ($li['status'] == 0) {
                        $inUseCards[] = $li['market_id'];
                    }
                }
            }
        }
        $list = [];
        if ($ids) {
            foreach ($ids as $id) {
                $aMarketDefine = by::marketDefine()->GetOneById($id);
                //校验库存，没有库存直接过滤
                $marketInfo = $aMarketDefine['market_info'] ?? [];
                if ($marketInfo) {
                    //库存校验
                    $stockNum = $marketInfo['stock_num'] ?? 0;
                    if ($stockNum <= 0) {
                        continue;
                    }
                    //已领取校验
                    $marketId = $marketInfo['id'] ?? 0;
                    if ($inUseCards && in_array($marketId, $inUseCards)) {
                        continue;
                    }
                    //审核校验
                    $status = $marketInfo['status'] ?? 0;
                    if (CUtil::uint($status) !== 1) {
                        continue;
                    }

                    //输出封装
                    $newMarketInfo = by::marketConfig()->couponCondition($marketId);
                    if (empty($newMarketInfo)) continue;

                    $expireTime = $newMarketInfo['expire_time'] ?? 0;
                    $startTime  = $newMarketInfo['start_time'] ?? 0;

                    //过期校验
                    if ($startTime > time()) {
                        continue;
                    }

                    if ($expireTime == 0 || $expireTime <= time()) {
                        continue;
                    }

                    //输出优惠券内容
                    $newMarketInfo['name']          = empty($aMarketDefine['name'] ?? '') ? $newMarketInfo['name'] : $aMarketDefine['name'];
                    $newMarketInfo['use_rule_note'] = empty($aMarketDefine['use_rule_note'] ?? '') ? $newMarketInfo['use_rule_note'] : $aMarketDefine['use_rule_note'];
                    $list[]                         = $newMarketInfo;
                }
            }
        }
        return [true, $list];
    }


    /**
     * @param $post
     * @param $userId
     * @return array
     * 领取优惠券
     */
    public function marketDefineDraw($post, $userId): array
    {
        //参数校验
        $defineType = CUtil::uint($post['define_type'] ?? 0);
        if (empty($defineType)) {
            return [false, '优惠券类型必填！'];
        }

        //优惠券列表（数组）
        $marketIds = CUtil::trimTags($post['market_ids'] ?? '');
        if (empty($marketIds)) {
            return [false, '优惠券必选！'];
        }
        $marketIds = array_unique(explode(',', $marketIds));

        //已领取优惠券不允许多次领取
        $inUseCards = [];
        list($status, $data) = by::userCard()->getList($userId, by::userCard()::SOURCE['INDEX'], ['get_channel' => by::userCard()::GET_CHANNEL['internal_purchase']]);
        $cardList = $data['list'] ?? [];
        if ($cardList) {
            foreach ($cardList as $li) {
                if ($li['status'] == 0) {
                    $inUseCards[] = $li['market_id'];
                }
            }
        }
        $intersect = array_intersect($marketIds, $inUseCards);
        if ($intersect) {
            return [false, '该用户已领取相关优惠券！'];
        }

        //防止多次请求
        $unique_key = CUtil::getAllParams(__FUNCTION__, $defineType, json_encode($marketIds));
        list($anti) = self::ReqAntiConcurrency($userId, $unique_key, 5, 'EX');
        if (!$anti) {
            return [false, '操作太频繁，请稍后重试'];
        }


        $transaction = by::dbMaster()->beginTransaction();
        try {
            foreach ($marketIds as $market_id) {
                //获取资源信息
                $marketConfig = by::marketConfig()->getOneById($market_id);
                $markerType   = MarketConfigModel::TYPE;
                if (empty($marketConfig['type']) || !in_array($marketConfig['type'], $markerType)) {
                    throw new Exception("资源id：{$market_id}数据有误");
                }
                switch ($marketConfig['type']) {
                    case $markerType['voucher']:
                    case $markerType['coupon'] :
                        //写入券类
                        list($status) = by::userCard()->setCard($userId, $market_id, by::userCard()::GET_CHANNEL['internal_purchase']);
                        if (!$status) {
                            throw new Exception("用户获取券类id：{$market_id}失败");
                        }
                        break;
                }
                //减少库存
                list($status, $ret) = by::marketConfig()->stockModify($market_id, 1);
                if (!$status) {
                    throw new Exception($ret);
                }
            }

            $transaction->commit();
            //清除用户优惠券列表缓存
            by::userCard()->__delCache($userId);

            self::ReqAntiConcurrency($userId, $unique_key, 0, 'DEL');

            return [true, '领取成功'];
        } catch (\Exception $e) {
            $transaction->rollback();
            //删除并发标识
            self::ReqAntiConcurrency($userId, $unique_key, 0, 'DEL');
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.activity.draw');
            return [false, $e->getMessage()];
        }

    }

    /**
     * @param $acId
     * @param string $user_id
     * @return array
     * @throws Exception
     */
    public function getActivityDetail($acId, string $user_id = ''): array
    {
        //获取活动详情
        $aData = $this->getActivityOne($acId);
        if (!$aData) {
            return [false, '活动id有误'];
        }
        //优惠券列表
        $coupon          = by::aM()->getCouponListByAcId($acId);
        $aData['coupon'] = $coupon;

        $aData = $this->_couponListStatus($aData);


        //判断是否授权
        if ($user_id && strlen($user_id) < 11) {
            $user_id = CUtil::uint($user_id);
            $aData   = $this->_couponWithUser($aData, $user_id);
        }
        return [true, $aData];

    }


    /**
     * @param $aData
     * @param $userId
     * @return mixed
     * @throws Exception
     * 优惠券绑定用户状态
     */
    private function _couponWithUser($aData, $userId)
    {
        if (empty($aData)) return $aData;

        $acId = $aData['id'] ?? 0;
        //获取用户已领取优惠卷
        list($status, $data) = by::userCard()->getList($userId, by::userCard()::SOURCE['INDEX']);
        $cardList    = $data['list'] ?? [];
        $relationArr = [];
        if ($cardList) {
            foreach ($cardList as $card) {
                if ($card['get_relation'] == $acId) {
                    $relationArr[] = $card;
                }
            }
        }
        $market_ids = array_column($relationArr, 'market_id') ?? [];

        //领取状态判断
        $coupon = $aData['coupon'] ?? [];
        if ($coupon && $acId) {
            $getNum    = 0;
            $activeNum = 0;
            foreach ($coupon as $key => &$value) {
                if (in_array($value['market_id'], $market_ids)) {
                    //领取过
                    $value['coupon_status'] = self::COUPON_STATUS['HAS_GET'];
                    $getNum                 += 1;
                }

                if ($value['coupon_status'] == self::COUPON_STATUS['WAIT_GET']) {
                    $activeNum += 1;
                }
            }

            if ($activeNum) {
                $aData['btn_status'] = self::COUPON_BTN_STATUS['ONCE_DRAW'];
            } elseif ($getNum > 0) {
                $aData['btn_status'] = self::COUPON_BTN_STATUS['TO_USE'];
            }
        }


        $aData['coupon'] = $coupon;

        return $aData;
    }


    /**
     * @param $aData
     * @return mixed
     * 处理活动状态
     */
    private function _couponListStatus($aData)
    {
        if (empty($aData)) return $aData;
        $coupon = $aData['coupon'] ?? [];
        //活动状态处理
        $activityStatus = self::COUPON_STATUS['WAIT_GET'];
        if ($aData['end_time'] < intval(START_TIME)) {
            $activityStatus = self::COUPON_STATUS['HAS_EXPIRE'];//已过期
        } else {
            if (intval($aData['surplus_num']) <= 0) {
                $activityStatus = self::COUPON_STATUS['HAS_GET_ALL'];//已经领光
            }
        }

        //优惠券自身状态判断
        //找出过期的优惠券
        $expireNum  = 0;
        $noStockNum = 0;
        $btnStatus  = self::COUPON_BTN_STATUS['ONCE_DRAW'];

        if (!empty($coupon)) {
            //优惠券自身状态判断
            foreach ($coupon as $key => $item) {
                $coupon[$key]['coupon_status'] = self::COUPON_STATUS['WAIT_GET'];

                if ($item['expire_time'] < intval(START_TIME)) {
                    $expireNum                     += 1;
                    $coupon[$key]['coupon_status'] = self::COUPON_STATUS['HAS_EXPIRE'];
                }
                if (CUtil::uint($item['stock']) <= 0) {
                    $noStockNum                    += 1;
                    $coupon[$key]['coupon_status'] = self::COUPON_STATUS['HAS_GET_ALL'];
                }
            }


            //按钮状态 过期或者库存全部为空 活动直接结束
            $couponNum = count($coupon);

            if (($expireNum + $noStockNum) == $couponNum || $expireNum == $couponNum || $noStockNum == $couponNum || in_array($activityStatus, [self::COUPON_STATUS['HAS_EXPIRE'], self::COUPON_STATUS['HAS_GET_ALL']])) {
                $btnStatus = self::COUPON_BTN_STATUS['HAS_END'];
            }

            //活动有效期 过滤过期的优惠券
            if ($expireNum !== $couponNum) {
                foreach ($coupon as $key2 => $value2) {
                    if ($value2['coupon_status'] == self::COUPON_STATUS['HAS_EXPIRE']) {
                        unset($coupon[$key2]);
                    }
                }
            }

            //活动状态干预
            if (in_array($activityStatus, [self::COUPON_STATUS['HAS_EXPIRE'], self::COUPON_STATUS['HAS_GET_ALL']])) {
                foreach ($coupon as $key1 => $value1) {
                    $coupon[$key1]['coupon_status'] = $activityStatus;
                }
            }

            $coupon = array_values($coupon);
        }


        $aData['coupon']       = $coupon;
        $aData['btn_status']   = $btnStatus;
        $aData['expire_num']   = $expireNum;
        $aData['no_stock_num'] = $noStockNum;

        return $aData;

    }


    /**
     * @param $ac_id
     * @param $user_id
     * @return bool
     * @throws Exception
     * @throws RedisException
     * 判断是否满足条件
     */
    public function activityRestriction($ac_id, $user_id)
    {
        $activity = by::activityConfigModel()->getActivityOne($ac_id);
        //活动限制类型
        $activity_user_type = $activity['user_type'] ?? 0;
        if ($activity_user_type == 3) {
            return true;
        }
        $tagList            = by::Gtag()->GetGidAndTag() ?? [];
        $gid                = array_column($tagList, 'gid');
        $orderInfo          = by::Ouser()->getOrderListByUid($user_id) ?? [];
        $myOrderNumberArray = array_column($orderInfo, 'order_no') ?? [];
        $myOrderNumber      = implode("','", $myOrderNumberArray);
        $t_uo_info          = by::Ogoods()->getOrderListByUid($user_id, $myOrderNumber);
        $myGids             = array_column($t_uo_info, 'gid');
        //判断我购买的商品中是否有主机
        $buyResult = array_intersect($gid, $myGids);
        switch ($activity_user_type) {
            //购买过
            case self::ACTIVITY_RESTRICTION['have_consumed']:
                if ($buyResult) {
                    return true;
                } else {
                    return false;
                }
            //没购买过
            case self::ACTIVITY_RESTRICTION['not_have_consumed']:
                if ($buyResult) {
                    return false;
                } else {
                    return true;
                }
            default:
                return true;
        }
    }


    public function saveLog($id, $data)
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        if ($id) {
            //update
            $result = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
        } else {
            //insert
            $result = $db->createCommand()->insert($tb, $data)->execute();
            $id     = $db->getLastInsertID();
        }

        return $id;
    }

    /**
     * 是否包含此等级
     * @param $levels
     * @param $level
     * @return bool
     */
    private function isIncludeLevel($levels, $level): bool
    {
        $levels = explode(',', $levels);
        return in_array($level, $levels);
    }


    //活动积分商品规则
    public function activityFreePointsRule($user_id, $gid, $num = 1): array
    {
        // 0.非积分商品直接return true
        if (!ActivityConfigEnum::judgeActivityFreeSaleGoods($gid)) {
            return [true, 0];
        }
        // 1.判断数据是否正确
        if (empty($user_id) || empty($gid)) {
            return [false, 1];
        }
        // 2.判断数量是否超限
        if ($num > 1) {
            return [false, 2];
        }
        //2.判断是否在活动期间
        if (!ActivityConfigEnum::judgeActivityFreeSaleTime($gid)) {
            return [false, 5];
        }
        //3.判断用户是否有资格购买
        $startTime = strtotime(ActivityConfigEnum::PURCHASE_COMMON_GOODS_START_TIME);
        $endTime   = strtotime(ActivityConfigEnum::PURCHASE_COMMON_GOODS_END_TIME);
        $mod       = intval($user_id) % 10;
        $tb        = "`db_dreame_goods`.`t_uo_{$mod}`";
        $sql       = "SELECT `id`,`status` FROM {$tb} WHERE `price`>= :price AND `user_id`=:user_id  AND `pay_time` BETWEEN :startTime AND :endTime";
        $command   = by::dbMaster()->createCommand($sql, [':price' => ActivityConfigEnum::PURCHASE_COMMON_GOODS_PRICE, ':user_id' => $user_id, ':startTime' => $startTime, ':endTime' => $endTime]);
        !YII_ENV_PROD && CUtil::debug($command->getRawSql(), 'activity-sql');
        $orderData = $command->queryAll();
        if (empty($orderData)) {
            return [false, 3];
        }
        $statusEs = array_column((array) $orderData, 'status');
        if (empty($statusEs)) {
            return [false, 3];
        }
        // 判断是否存在进行中的订单
        $filteredArray = array_filter($statusEs, function ($value) {
            return $value > 100 && $value < 500;
        });
        if (!in_array(500, $statusEs) && !empty($filteredArray)) {
            return [false, 4];
        }
        if (!in_array(500, $statusEs)) {
            return [false, 3];
        }
        // 4.判断用户是否已经购买过该商品
        $tb1     = "`db_dreame_goods`.`t_uo_g_{$mod}`";
        $sql1    = "SELECT `b`.`order_no`, `b`.`status` FROM {$tb1} AS `a` INNER JOIN {$tb} AS `b` ON `a`.`order_no` = `b`.`order_no` WHERE `a`.`gid`= :gid AND `a`.`user_id`=:user_id AND `a`.`goods_type` = :type ORDER BY `a`.`id` DESC";
        $command = by::dbMaster()->createCommand($sql1, [':gid' => $gid, ':user_id' => $user_id, ':type' => 2]);
        !YII_ENV_PROD && CUtil::debug($command->getRawSql() . "-abg", 'activity-sql');
        $info        = $command->queryOne();
        $orderStatus = $info['status'] ?? -1;
        if (!in_array($orderStatus, [-1, 100, 20000000])) {
            return [false, 6];
        }
        return [true, 0];
    }


    //判断是否有活动订单未退款，如果有不允许退款
    public function activityRefundRule($user_id, $order_no): bool
    {
        $activityGids = ActivityConfigEnum::getCheckinRewardGoods();
        if (empty($activityGids)) {
            return true;
        }
        $startTime = strtotime(ActivityConfigEnum::PURCHASE_COMMON_GOODS_START_TIME);
        $endTime   = strtotime(ActivityConfigEnum::PURCHASE_COMMON_GOODS_END_TIME);

        //当前订单实付金额小于设定值，直接返回true
        $mod     = intval($user_id) % 10;
        $tb      = "`db_dreame_goods`.`t_uo_{$mod}`";
        $sql     = "SELECT `price`,`status`,`pay_time` FROM {$tb} WHERE `order_no`=:order_no AND `user_id`=:user_id";
        $command = by::dbMaster()->createCommand($sql, [':order_no' => $order_no, ':user_id' => $user_id]);
        !YII_ENV_PROD && CUtil::debug($command->getRawSql(), 'activity-sql-rule');
        $s       = $command->queryOne();
        $price   = $s['price'] ?? 0;
        $status  = $s['status'] ?? 0;
        $payTime = $s['pay_time'] ?? 0;
        //如果不在活动期间，直接返回true
        if ($startTime > $payTime || $endTime < $payTime) {
            return true;
        }
        if ($price < ActivityConfigEnum::PURCHASE_COMMON_GOODS_PRICE || $status != 500) {
            return true;
        }
        //存在满足大于1个条件的订单，返回true
        $sql     = "SELECT COUNT(*) FROM {$tb} WHERE `price`>= :price AND `user_id`=:user_id  AND `status` = 500 AND `pay_time` BETWEEN :startTime AND :endTime";
        $command = by::dbMaster()->createCommand($sql, [':price' => ActivityConfigEnum::PURCHASE_COMMON_GOODS_PRICE, ':user_id' => $user_id, ':startTime' => $startTime, ':endTime' => $endTime]);
        !YII_ENV_PROD && CUtil::debug($command->getRawSql(), 'activity-sql-rule');
        $count = $command->queryScalar();
        if ($count > 1) {
            return true;
        }

        //查询用户订单信息
        $tb1     = "`db_dreame_goods`.`t_uo_g_{$mod}`";
        $gidsStr = "'" . implode("','", $activityGids) . "'";
        $sql1    = "SELECT `b`.`status` FROM {$tb1} AS `a` INNER JOIN {$tb} AS `b` ON `a`.`order_no` = `b`.`order_no` WHERE `a`.`gid` IN ($gidsStr) AND `a`.`user_id`=:user_id AND `a`.`goods_type` = :type AND `b`.`status` NOT IN (100,20000000) ORDER BY `a`.`id` DESC";
        $command = by::dbMaster()->createCommand($sql1, [':user_id' => $user_id, ':type' => 2]);
        !YII_ENV_PROD && CUtil::debug($command->getRawSql(), 'activity-sql-rule');
        $info = $command->queryOne();
        if (!empty($info)) {
            return false;
        }
        return true;
    }


    /**
     * 检查积分商品兑换规则
     *
     * @param int|string $user_id 用户ID
     * @param int|string $gid 商品ID
     * @param int $num 兑换数量，默认为1
     * @return array [是否可兑换, 状态码]
     *         状态码说明：
     *         0: 可兑换
     *         1: 用户ID或商品ID无效
     *         2: 兑换数量超限
     *         5: 商品未到上线时间
     *         7: 库存不足(但返回true，特殊业务逻辑)
     */
    /**
     * 检查积分商品兑换规则
     *
     * @param int|string $user_id 用户ID
     * @param int|string $gid 商品ID
     * @param int $num 兑换数量，默认为1
     * @return array [是否可兑换, 状态码]
     *         状态码说明：
     *         0: 可兑换
     *         1: 用户ID或商品ID无效
     *         2: 兑换数量超限
     *         5: 商品未到上线时间
     *         7: 库存不足(但返回true，特殊业务逻辑)
     */
    public function checkPointsGoodsRule($user_id, $gid, int $num = 1): array
    {
        try {
            // 1. 参数校验
            if (empty($user_id) || empty($gid)) {
                return [false, 1];
            }

            // 2. 数量校验
            if ($num > 1) {
                return [false, 2];
            }

            // 3. 获取商品信息
            $goodsInfo = (new IndexGoodsMainService())->GetAllOneByGid($gid);
            if (empty($goodsInfo)) {
                return [false, 1]; // 商品不存在视为无效ID
            }

            // 4. 上线时间校验
            $onlineTime = $goodsInfo['online_time'] ?? 0;
            if (CUtil::uint($onlineTime) > time()) {
                return [false, 5];
            }

            // 5. 库存检查
            $source = by::GoodsStockModel()::SOURCE['WARES'];
            list($stock, $sales) = GoodsMainService::getInstance()->GetSumByGid($gid, $source);

            // 特殊业务逻辑：库存不足时返回true但状态码7
            return $stock > 0 ? [true, 0] : [true, 7];

        } catch (\Exception $e) {
            // 记录异常日志（包含关键参数）
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug(sprintf(
                    "积分商品规则检查异常: user_id=%s, gid=%s, error=%s",
                    $user_id,
                    $gid,
                    $error
            ), 'error.checkPointsGoodsRule');

            // 返回系统异常状态码
            return [false, 1];
        }
    }

}
