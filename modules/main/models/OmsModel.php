<?php

namespace app\modules\main\models;

use app\components\CommCurl;
use app\models\CUtil;
use app\modules\goods\services\ErpService;
use yii\db\Exception;

class OmsModel
{
    //签名过滤字段
    const OMS_SIGN_FILTER_FIELDS = ['tenant_code'];
    const OMS_VERSION            = '2.0.0';

    //回写参数校验
    const OMS_REWRITE_KEYS = ['app_id', 'charset', 'sign_type', 'format', 'timestamp', 'version', 'biz_content', 'method', 'sign'];

    /**
     * @var mixed|string
     */
    private $platform = 'TP';
    /**
     * @var mixed|string
     */
    private $oms_url;
    /**
     * @var mixed|string
     */
    private $app_id;
    /**
     * @var mixed|string
     */
    private $tenant_code;
    /**
     * @var string
     */
    private static $oms_public_key;
    /**
     * @var string
     */
    private static $oms_private_key;
    /**
     * @var mixed|string
     */
    private $shop_code;
    /**
     * @var mixed|string
     */
    private $wx_url;


    public function __construct()
    {
        $config_key = 'oms';
        $oms_config = CUtil::getConfig($config_key, 'oms', \Yii::$app->id);
        if (empty($oms_config)) {
            return [false, "{$config_key}配置不存在"];
        }
        $this->app_id          = $oms_config['APP_ID'] ?? '';
        $this->platform        = $oms_config['PLATFORM'] ?? '';
        $this->oms_url         = $oms_config['OMS_URL'] ?? '';
        $this->tenant_code     = $oms_config['TENANT_CODE'] ?? '';
        $this->shop_code       = $oms_config['SHOP_CODE'] ?? '';
        $this->wx_url          = $oms_config['WX_URL'] ?? '';
        self::$oms_public_key  = WEB_PATH . '/../modules/main/config/' . YII_ENV . '/oms_public_key.pem';
        self::$oms_private_key = WEB_PATH . '/../modules/main/config/' . YII_ENV . '/oms_private_key.pem';
    }


    /**
     * @param $post
     * @return array
     * 推送数据到OMS
     */
    public function pushWxData($post): array
    {
        //1.post 参数校验
        $method = $post['method'] ?? '';
        $data   = $post['data'] ?? '';
        if (empty($method) || empty($data)) {
            return [false, 'oms推送方法或参数必传！'];
        }
        if (is_null(json_decode($data))) {
            return [false, 'oms推送参数不是json类型！'];
        }


        //2.封装参数
        $arr = [
            'app_id'      => $this->app_id,
            'charset'     => 'utf-8',
            'sign_type'   => 'RSA2',
            'format'      => 'JSON',
            'timestamp'   => date('Y-m-d H:i:s'),
            'version'     => self::OMS_VERSION,
            'biz_content' => $data,
            'method'      => $method,
            'is_sync'     => 1,
            'tenant_code' => $this->tenant_code,
        ];
        //4.请求数据
        list($s, $m) = $this->_request($method, $arr);
        if (!$s) {
            return [false, '请求OMS失败！'];
        }
        return [true, $m];
    }

    /**
     * @param $method
     * @param $arr
     * @return array
     * 统一请求
     */
    protected function _request($method, $arr): array
    {
        $signArr      = $arr;
        $filterFields = self::OMS_SIGN_FILTER_FIELDS;
        foreach ($filterFields as $field) {
            unset($signArr[$field]);
        }

        //增加签名
        $arr       = array_merge(self::addOmsSign($signArr), $arr);
        $body      = http_build_query($arr);
        $url       = $this->oms_url;
        $headers   = [];
        $headers[] = 'Content-Type：application/x-www-form-urlencoded';
        $headers[] = 'Expect: ';


        try {
            // 发起请求
            list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $headers, 'POST', 10);

            $isSuccess = CUtil::uint($ret['code'] ?? 0) === 100000;

            // 默认事件类型
            $event = $isSuccess ? "oms.{$method}" : "error.oms.{$method}";

            CUtil::setLogMsg($event, $arr, $ret, $headers, $url, $err, "推送E3+的数据信息", $httpCode);

            return [$isSuccess, $ret];

        } catch (\Exception $e) {
            // 捕获异常并记录错误信息
            $errorMsg = "异常信息：{$e->getMessage()} | 文件：{$e->getFile()} | 行号：{$e->getLine()}";

            CUtil::setLogMsg("error.oms.{$method}", $arr, [], $headers, $url, "【请求E3+异常】".$e->getMessage(), $errorMsg);
            return [false, ['error' => '系统异常，请稍后重试']];
        }
    }


    /**
     * @param $data
     * @return mixed
     * 增加数据签名(私钥加密)
     */
    public static function addOmsSign($data)
    {
        ksort($data, SORT_STRING);
        openssl_sign(urldecode(http_build_query($data)), $signature, self::getPrivateKey(self::$oms_private_key), defined('OPENSSL_ALGO_SHA256') ? OPENSSL_ALGO_SHA256 : 'sha256WithRSAEncryption');
        $data['sign'] = base64_encode($signature);
        return $data;
    }


    /**
     * @param $filepath
     * @return bool
     * 获取私钥
     */
    public static function getPrivateKey($filepath)
    {
        return openssl_pkey_get_private(file_get_contents($filepath));
    }


    /**
     * @param $arr
     * @return array
     * @throws Exception
     * 回写数据到小程序
     */
    public function rewriteOmsData($arr): array
    {
        $app_id      = $arr['app_id'] ?? '';
        $method      = $arr['method'] ?? '';
        $biz_content = json_decode($arr['biz_content'] ?? '', true);
        $shop_code   = $biz_content['shop_code'] ?? '';

        if ($app_id != $this->app_id) {
            return [false, 'APP_ID:' . $app_id . '不正确！'];
        }

        if (empty($method)) {
            return [false, 'METHOD:' . $method . '不正确！'];
        }

        if (empty($biz_content)) {
            return [false, 'BIZ_CONTENT 不能为空！'];
        }

        if (!in_array($shop_code, $this->shop_code)) {
            return [false, 'SHOP_CODE:' . $shop_code . '不正确！'];
        }

        // 直接调取处理ERP的方法
        $erpService = new ErpService();
        list($s, $data) = $erpService->rewrite($method, $biz_content);
        return [$s, $data];
    }


    /**
     * @param $method
     * @param $arr
     * @return array
     * 统一请求
     */
    protected function _wxRequest($method, $arr): array
    {
        //增加签名
        $body      = [
            'method' => $method,
            'data'   => $arr,
        ];
        $url       = $this->wx_url;
        $headers   = [];
        $headers[] = 'Content-Type：application/json';
        $headers[] = 'Expect: ';
        $res       = CUtil::curl_post($url, json_encode($body, 320), $headers, 10, true, '', '', true);
        CUtil::debug("url:{$url}|body:" . json_encode($body, 320) . " |res:{$res}", "oms_rewrite.{$method}");
        $data  = (array)json_decode($res, true);
        $iRet  = $data['iRet'] ?? -1;
        $sMsg  = $data['sMsg'] ?? '';
        $rdata = $data['data'] ?? [];
        if (intval($iRet) !== 1) {
            return [false, $sMsg];
        }

        return [true, $rdata];
    }


    /**
     * @param array $data
     * @param string $sign
     * @return bool
     * 公钥验签
     */
    public static function verifyOmsSign(array $data = [], string $sign = ''): bool
    {
        if (empty($data) || empty($sign)) {
            return false;
        }

        ksort($data, SORT_STRING);
        return (bool)openssl_verify(urldecode(http_build_query($data)), base64_decode($sign),
            self::getPublicKey(self::$oms_public_key),
            defined('OPENSSL_ALGO_SHA256') ? OPENSSL_ALGO_SHA256 : 'sha256WithRSAEncryption');
    }


    /**
     * @param $filepath
     * @return false|resource
     * 获取公钥
     */
    public static function getPublicKey($filepath)
    {
        return openssl_pkey_get_public(file_get_contents($filepath));
    }

}