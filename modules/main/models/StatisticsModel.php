<?php
/*
 * @Author: linze
 * @Date: 2022-03-03 14:10:32
 * @LastEditors: linze
 * @LastEditTime: 2022-03-03 15:34:42
 * @Description: file content
 * @FilePath: \dreame\modules\main\models\StatisticsModel.php
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

/**
 * 统计报表相关模型
 */
class StatisticsModel extends CommModel
{

    const STAT_TYPE = [
        'uv'                => 1,
        'pv'                => 2,
        'both'              => 3,
    ];

    //用户类型统计表字段名称
    public $userFieldsName = [
        'day'       => '日期',
        'pv'        => '访问次数',
        'uv'        => '访问人数',
    ];

    public static function userTbName()
    {
        return "`db_dreame_log`.`t_user_record`";
    }

    /**
     * @param $date
     * @return string
     * 当日统计相关redis key
     */
    public function getUserStatRedisKey($date)
    {
        return AppCRedisKeys::userStat($date);
    }

    /**
     * @param $date
     * @return string
     * 当日统计uv redis key
     */
    public function getUserUVRedisKey($date)
    {
        return AppCRedisKeys::userUVStat($date);
    }

    /**
     * @param $user_id
     * @param mixed $type
     * @return int
     * @throws \yii\db\Exception
     * 统计用户uv pv以及其他数据
     */
    public function userStat($user_id, $type = self::STAT_TYPE['both'])
    {
        $date  = date('Ymd');
        $r_key = $this->getUserStatRedisKey($date);
        $redis = by::redis();
        switch ($type) {
            case self::STAT_TYPE['pv']:   //登录的时候统计一次
                $redis->hIncrBy($r_key, 'PV', 1);
                by::redis()->expireAt($r_key, strtotime('+3 day'));
                break;
            case self::STAT_TYPE['uv']:
                $key  = $this->getUserUVRedisKey($date);
                $this->__CommOpt($user_id, 1, $key);
                break;
            case self::STAT_TYPE['both']:
                self::userStat($user_id, self::STAT_TYPE['pv']);
                self::userStat($user_id, self::STAT_TYPE['uv']);
                //$this->recordSource($user_id);
                break;
            default:
                break;
        }

        return true;
    }

    /**
     * @param null $time
     * @return array
     * 每日统计数据写入数据库
     */
    public function dataToTb($time = null)
    {
        $current   = $time ? $time : strtotime("-1 day");//20190103 : 00:00:00【前一天零点】
        $yesterday = date('Ymd', $current);
        try {
            //uv pv 签到等用户类数据入库
            $tb                  = self::userTbName();
            list($status, $data) = $this->getUserStatData($yesterday);
            $this->__statToDb($tb, $data);

            return [true, 'ok'];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'fhh_statistics');
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
        }
    }

    private function __statToDb($tb, $data)
    {
        $field  = "";
        $values = "";
        $dup    = "";
        foreach ($data as $key => $value) {
            $value = trim($value);
            if ($value === '') {
                continue;
            }
            $field  .= "`$key`,";
            $values .= "'$value',";
            $dup    .= "`$key`='$value',";
        }
        $field  = trim($field, ',');
        $values = trim($values, ',');
        $dup    = trim($dup, ',');
        $sql    = "INSERT INTO {$tb} ({$field}) VALUES ({$values}) ON DUPLICATE KEY UPDATE {$dup}";
        by::dbMaster()->createCommand($sql)->execute();
        $id     = by::dbMaster()->getLastInsertId();
        return $id;
    }

    /**
     * @param int $user_id
     * @param int $opt
     * @param string $r_key
     * @param int $expire
     * @return int
     * 相同行为操作
     */
    private function __CommOpt($user_id = 0, $opt = 0, $r_key = '', $expire = 0): int
    {

        if (empty($r_key)) {
            return 0;
        }

        switch ($opt) {
            case 1:
                $ret = by::redis('core')->sAdd($r_key, $user_id);
                if ($expire <= time()) {
                    $expire = strtotime('+3 days');
                }
                by::redis('core')->expireAt($r_key, $expire);
                break;
            case 2:
                $ret = by::redis('core')->sIsmember($r_key, $user_id);
                break;
            default:
                $ret = by::redis('core')->sCard($r_key);
                break;
        }

        return $ret;
    }

    /**
     * @param $date
     * @return array
     * @throws \yii\db\Exception
     * 用户类缓存数据进行处理
     */
    public function getUserStatData($date)
    {
        $redis     = by::redis();
        $key       = $this->getUserStatRedisKey($date);
        $pv        = $redis->hGet($key, 'PV');

        $key       = $this->getUserUVRedisKey($date);
        $uv        = $redis->sCard($key);
        $data = [
            'pv'        => $pv ?: 0,
            'uv'        => $uv ?: 0,
            'day'       => $date
        ];

        return [true, $data];
    }

    /**
     * 获取用户pvuv统计类数据
     */
    public function getUserList($sDay, $eDay, $name)
    {
        //默认只展示最近七天的数据
        $limit = ' LIMIT 7';
        $where = '';
        if ($sDay > $eDay) {
            return [false, '起始日期不能比结束日期大'];
        }
        if (!empty($sDay) && !empty($eDay)) {
            $limit = ' LIMIT 30';
            $where = " WHERE `day` BETWEEN {$sDay} AND {$eDay}";
        }

        $userFieldsName = $this->userFieldsName;
        if (!empty($name)) {
            if (!isset($userFieldsName[$name])) {
                return [true, []];
            }
            $fields = "day`,`{$name}";
        } else {
            $keys   = array_keys($userFieldsName);
            $fields = implode('`,`', $keys);
        }

        $tb       = self::userTbName();
        $sql      = "SELECT `{$fields}` FROM {$tb}{$where} ORDER BY `day` DESC{$limit}";
        $data     = by::dbMaster()->createCommand($sql)->queryAll();
        $dataList = [];
        $dayList  = [];

        if ($data) {
            $arr  = explode('`,`', $fields);
            foreach ($arr as $v) {

                if ($v == 'day') {
                    $dayList = array_column($data, $v);
                    continue;
                }
                $list     = array_column($data, $v);
                $new_list = [];
                foreach ($list as $ks => $vs) {
                    $new_list[$dayList[$ks]] = $vs;
                }
                $new_list['name']  = $this->userFieldsName[$v];
                $new_list['field'] = $v;
                $dataList[]        = $new_list;
            }
        }

        return [true, ['dataList' => $dataList, 'dayList' => $dayList]];
    }
}
