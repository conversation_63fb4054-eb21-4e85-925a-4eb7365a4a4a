<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\common\ModelTrait;
use app\modules\common\Singleton;
use Throwable;

final class MemberActivityModel extends CommModel
{
    use Singleton, ModelTrait;
    
    // 状态
    const STATUS = [0 => '未开始', 1 => '进行中', 2 => '已结束'];
    
    // 活动周期类型-单次
    const CYCLE_TYPE_ONCE = 'once';
    
    private $fillable = ['id', 'title', 'description', 'rule', 'cycle_type', 'start_time', 'end_time', 'create_time', 'update_time', 'delete_time'];
    
    public function __delDetailCache($id): bool
    {
        $redis = by::redis();
        $cacheKey = AppCRedisKeys::getMemberActivityDetail($id);
        $redis->del($cacheKey);
        return true;
    }

    public static function getStatusText(int $status): string
    {
        return self::STATUS[$status] ?? 'unknown';
    }
    
    public static function tableName(): string
    {
        return "`db_dreame_goods`.`member_activity`";
    }
    
    public function getList(array $params = []): array
    {
        $fields = $params['__select_fields__'] ?? $this->getFillable();

        $fields = $this->filterQueryAttributes($fields);

        $query = self::find();

        $query->where(['delete_time' => 0]);

        $res = $query->select($fields)->asArray()->all();
        
        foreach ($res as $key => $val) {
            if (time() < $val['start_time']) {
                $status = 0;
                $statusText = '未开始';
            } elseif (time() > $val['end_time']) {
                $status = 2;
                $statusText = '已结束';
            } else {
                $status = 1;
                $statusText = '进行中';
            }

            $res[$key]['status'] = $status;
            $res[$key]['status_text'] = $statusText;
        }

        return $res;
    }
    
    /**
     * 创建活动
     * @param array $data 创建数据
     * @return array
     */
    public function doCreate(array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            $data['create_time'] = time();
            $data['update_time'] = time();
            $resp = $db->createCommand()->insert($tb, $data)->execute();
            if (empty($resp)) {
                throw new BusinessException('创建活动失败');
            }

            $id = $db->getLastInsertID();
            return [true, $id, 'success'];
        } catch (Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }
    
    /**
     * 更新活动
     * @param int $id 活动ID
     * @param array $data 更新数据
     * @return array
     */
    public function doUpdate(int $id, array $data): array
    {
        $this->filterExecuteAttributes($data);
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            unset($data['id']);
            $data['update_time'] = time();
            $one                 = self::find()->where(['id' => $id, 'delete_time' => 0])->limit(1)->asArray()->one();
            if (empty($one)) {
                throw new BusinessException(sprintf('活动数据[%s]不存在', $id));
            }
            // 判断活动是否结束 结束不允许修改
            if (time() > $one['end_time']) {
                throw new BusinessException('活动已结束，无法修改');
            }
            $resp = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
            if (empty($resp)) {
                throw new BusinessException('更新活动失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 删除数据
     * @param array $ids 主键IDs
     * @param bool $softDelete 是否软删除 true=是，false=否
     * @return array
     */
    public function doDelete(array $ids, bool $softDelete = true): array
    {
        $db = by::dbMaster();
        $tb = self::tableName();
        try {
            if ($softDelete) {
                $resp = $db->createCommand()->update($tb, ['delete_time' => time()], ['id' => $ids])->execute();
            } else {
                $resp = $db->createCommand()->delete($tb, ['id' => $ids])->execute();
            }

            if (empty($resp)) {
                throw new BusinessException('删除活动失败');
            }

            return [true, 'success'];
        } catch (Throwable $e) {
            return [false, $e->getMessage()];
        }
    }

    /**
     * 获取活动信息
     *
     * @param int $id 活动ID
     * @param array $fields 指定要查询的字段，默认使用模型fillable字段
     * @return array|null 返回活动信息数组，找不到返回null
     */
    public function getInfo(int $id, array $fields = [])
    {
        $query = self::find()->where(['id' => $id]);

        // 如果指定了字段则使用指定字段，否则使用模型fillable字段
        $selectFields = !empty($fields) ? $fields : $this->fillable;

        return $query->asArray()
                ->select($selectFields)
                ->one();
    }
    
    /**
     * 获取活动有效期内IDs
     * @return array
     */
    public function getAvailableIds(): array
    {
        $query = self::find();
        $res = $query->where(':time between start_time and end_time', [':time' => time()])->andWhere(['delete_time' => 0])->asArray()->all();
        return empty($res) ? [] : array_column($res, 'id');
    }

    public function isStart($id): int
    {
        $time = time();
        $res = self::find()->where(['id' => $id])->andWhere(['delete_time' => 0])->andWhere(['<=', 'start_time', $time])->andWhere(['>=', new \yii\db\Expression('end_time + 86400 * 7'), $time])->asArray()->one();
        return empty($res) ? 0 : 1;

    }
}