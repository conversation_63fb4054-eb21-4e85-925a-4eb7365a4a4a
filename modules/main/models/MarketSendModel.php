<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/11/15
 * Time: 16:23
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use yii\db\Exception;

class MarketSendModel extends CommModel {
    CONST ListExpire  = 600;    //缓存时间
    //是否删除
    const IS_DEL = [
        'no'  => 0,
        'yes' => 1
    ];

    //配置活动类型
    CONST TYPE = [
        'coupon'          => 1,     //商品优惠券
    ];

    //卡包状态   0 未领取  1 已领取  2已领完 3已结束
    CONST STATUS = [
        'no_obtain' => 0,
        'obtain'    => 1,
        'receive'   => 2,
        'over'      => 3
    ];

    public $tb_fields = [
        'id','market_id','gid','name','stock_num','send_num','start_time','end_time','update_time','create_time','is_del'
    ];


    public static function tableName() {
        return "`db_dreame_goods`.`t_market_send`";
    }

    /**
     * @param $id
     * 删除列表缓存
     */
    private function __delListCache($id) {
        $r_key  = AppCRedisKeys::adminMarketSendKey();
        $r_key1 = AppCRedisKeys::adminMarketSendInfo($id);
        by::redis()->del($r_key, $r_key1);
    }

    /**
     * @param $name
     * @param $s_time
     * @param $e_time
     * @return array
     * 资源发放列表条件
     */
    private function __condition($name, $s_time, $e_time) {
        $where   = "1 = 1";
        $where  .= " and `is_del` = 0";
        $params = [];
        if (!empty($name)) {
            $where           .= " and `name`=:name";
            $params[':name']  = $name;
        }
        if (!empty($s_time)) {
            $where             .= " and `start_time`>=:s_time";
            $params[':s_time']  = $s_time;
        }
        if (!empty($e_time)) {
            $where             .= " and `end_time`<=:e_time";
            $params[':e_time']  = $e_time;
        }
        return [$where, $params];
    }

    /**
     * @param $name
     * @return array|false
     * @throws Exception
     * 根据name查找信息（仅限后台校验用）
     */
    private function __getOneByName($name)
    {
        $tb  = self::tableName();
        $sql = "SELECT `id` FROM {$tb} WHERE `name` = :name AND `is_del` = :is_del LIMIT 1";
        $aData = by::dbMaster()->createCommand($sql, [':name' => $name, ':is_del' => 0])->queryOne();

        return $aData;
    }

    /**
     * @param $id
     * @param $data
     * @return array
     * @throws Exception
     * 校验表单数据
     */
    private function __modifyForm($id, $data) {
        if (empty($data['name'])) {
            return [false, '活动名称不能为空'];
        }

        $aData = $this->__getOneByName($data['name']);
        if (!empty($aData) && (empty($id) || $aData['id'] != $id)) {
            return [false, '名称已存在'];
        }
        if (empty($data['market_id'])) {
            return [false, '营销资源不能为空'];
        }
        if (empty($data['start_time'])) {
            return [false, '开始时间不能为空'];
        }
        if (empty($data['end_time'])) {
            return [false, '结束时间不能为空'];
        }
        if ($data['start_time'] > $data['end_time']) {
            return [false, '开始时间不能大于结束时间'];
        }
        if ((!is_numeric($data['stock_num']) || $data['stock_num'] <= 0)) {
            return [false, '库存配置不正确'];
        }
        if ($data['market_id'] <= 0) {
            return [false, '资源配置错误'];
        }
        return [true, 'ok'];
    }

    /**
     * @param $name
     * @param $s_time
     * @param $e_time
     * @return false|string|null
     * @throws Exception
     * 营销资源发放总数量
     */
    public function getMarketSendTotal($name, $s_time, $e_time) {
        $redis_key = AppCRedisKeys::adminMarketSendKey();
        $h_key     = CUtil::getAllParams(__FUNCTION__, $name, $s_time, $e_time);
        $redis     = by::redis();
        $total     = $redis->hGet($redis_key, $h_key);

        if ($total === false) {
            $tb                   = self::tableName();
            list($where, $params) = $this->__condition($name, $s_time, $e_time); //条件
            $sql                  = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $total                = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $redis->hSet($redis_key, $h_key, $total);
            CUtil::ResetExpire($redis_key, self::ListExpire);
        }
        return intval($total);
    }

    /**
     * @param $page
     * @param $page_size
     * @param $name
     * @param $s_time
     * @param $e_time
     * @return array
     * @throws Exception
     * 营销资源发放列表
     */
    public function getMarketSendList($page, $page_size, $name, $s_time, $e_time) {
        $redis_key = AppCRedisKeys::adminMarketSendKey();
        $h_key     = CUtil::getAllParams(__FUNCTION__, $page, $page_size, $name, $s_time, $e_time);
        $redis     = by::redis();
        $aJson     = $redis->hGet($redis_key, $h_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb                   = self::tableName();
            list($where, $params) = $this->__condition($name, $s_time, $e_time); //条件
            list($offset)         = CUtil::pagination($page, $page_size);
            $sql                  = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $aData                = by::dbMaster()->createCommand($sql, $params)->queryAll();

            $redis->hSet($redis_key, $h_key, json_encode($aData));
            CUtil::ResetExpire($redis_key, self::ListExpire);
        }

        return empty($aData) ? [] : array_column($aData,"id");
    }

    /**
     * @param $id
     * @param $data
     * @return array
     * @throws Exception
     * 添加编辑  id存在编辑  不存在添加
     */
    public function modify($id, $data) {
        list($status, $msg) = $this->__modifyForm($id, $data);  //校验输入信息
        if (!$status) {
            return [false, $msg];
        }

        list($s, $m) = $this->__SaveData($id, $data);

        if (!$s) {
            return [false, $m];
        }

        $msg  = json_encode($data, JSON_UNESCAPED_UNICODE);
        $msg1 = $id ? "修改了营销资源发放ID: {$id} " : "新增了营销资源发放: {$data['name']}, 内容是: {$msg}";
        (new SystemLogsModel())->record($msg1, RbacInfoModel::MARKET_SEND);

        return [true, 'ok'];
    }

    private function __SaveData($id, $data)
    {
        $tb     = self::tableName();
        $id     = CUtil::uint($id);

        $tran   = by::dbMaster()->beginTransaction();
        try {
            if (empty($id)) {
                //营销资源下发增加，营销资源库存那边就减少
                list($status, $msg) = by::marketConfig()->stockModify($data['market_id'], $data['stock_num']);
                if (!$status) {
                    return [false, $msg];
                }
                $data['create_time'] = intval(START_TIME);
                by::dbMaster()->createCommand()->insert($tb, $data)->execute();

            } else {
                $aData = $this->getOneById($id);
                if (empty($aData)) {
                    return [false, '资源不存在'];
                }

                //如果改变营销资源id，并且剩余库存还有剩，那就先把库存返回去
                if ($aData['market_id'] > 0 && $aData['stock_num'] > 0) {
                    list($status, $msg) = by::marketConfig()->stockModify($aData['market_id'], -($aData['stock_num']));
                    if (!$status) {
                        return [false, $msg];
                    }
                }

                if (isset($data['is_del']) && $data['is_del'] == self::IS_DEL['yes']) {
                    $save['is_del'] = $data['is_del'];

                } else {
                    //修改营销下放资源 库存
                    //营销资源下发增加，营销资源库存那边就减少
                    list($status, $msg) = by::marketConfig()->stockModify($data['market_id'], $data['stock_num']);
                    if (!$status) {
                        return [false, $msg];
                    }

                    $save = [
                        'stock_num'     => $data['stock_num'],
                        'name'          => $data['name'],
                        'market_id'     => $data['market_id'],
                        'start_time'    => $data['start_time'],
                        'end_time'      => $data['end_time'],
                        'gid'           => $data['gid'],
                    ];

                    //切换为另外的营销资源 发放数量0
                    if ($data['market_id'] != $aData['market_id']) {
                        $save['send_num'] = 0;
                    }
                }

                $save['update_time'] = intval(START_TIME);

                by::dbMaster()->createCommand()->update($tb, $save, ['id' => $id])->execute();
            }

            $tran->commit();
            //删除缓存
            $this->__delListCache($id);
            $this->optStockNum($id, 'DEL');

        } catch (\Exception $e) {
            $tran->rollBack();
            return [false, '操作失败'];
        }

        return [true, 'ok'];
    }

    /**
     * @param $id
     * @return array|false
     * @throws Exception
     * 获取单条数据详情
     */
    public function getOneById($id) {
        $id        = CUtil::uint($id);
        $redis_key = AppCRedisKeys::adminMarketSendInfo($id);
        $redis     = by::redis();
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = self::tableName();
            $fields = implode("`,`",$this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` = :id AND `is_del` = :is_del LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':id' => $id, ':is_del' => 0])->queryOne();
            $aData  = $aData ?: [];

            $redis->set($redis_key, json_encode($aData), ['NX','EX' => empty($aData) ? 10 : self::ListExpire]);
        }

        return $aData;
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除
     */
    public function del($id)
    {
        if (empty($id)) {
            return [false, 'ID不能为空'];
        }

        $data = [
            'is_del' => self::IS_DEL['yes']
        ];
        list($s, $m) = $this->__SaveData($id, $data);

        if (!$s) {
            return [false, $m];
        }

        (new SystemLogsModel())->record("营销资源发放ID:{$id}", RbacInfoModel::MARKET_SEND);

        return [true, 'ok'];
    }

    /**
     * @param $id
     * @param string $opt
     * @return array|int
     * @throws Exception
     * 操作库存
     */
    public function optStockNum($id, $opt='GET')
    {
        $r_key      = AppCRedisKeys::marketSendStockNum($id);
        $redis      = by::redis();

        switch ($opt) {
            case 'GET' :
                $stock_num  = $redis->get($r_key);

                if ($stock_num === false) {
                    $tb         = self::tableName();
                    $sql        = "SELECT `stock_num` FROM {$tb} WHERE `id` = :id AND `is_del` = :is_del LIMIT 1";
                    $stock_num  = by::dbMaster()->createCommand($sql, [
                        ':id' => $id,
                        ':is_del' => self::IS_DEL['no']]
                    )->queryScalar();

                    $stock_num  = intval($stock_num);
                    $redis->set($r_key, $stock_num, ['NX','ex'=>600]);
                }
                return intval($stock_num);

            case 'INCR' :
                if ($redis->exists($r_key) == false) {
                    $this->optStockNum($id);
                }
                $s_num  = $redis->incrBy($r_key, -1);
                if ($s_num < 0) {
                    return [false, '来晚了，已被领取完了~'];
                }

                $tb     = self::tableName();
                $sql    = "UPDATE {$tb} SET stock_num = stock_num - 1, send_num = send_num + 1 
                            WHERE id = :id AND stock_num - 1 >= 0";
                $c_num  = by::dbMaster()->createCommand($sql, [':id' => $id])->execute();
                if ($c_num == 0) {
                    return [false, '来晚了，已被领取完了'];
                }

                return [true, 'ok'];

            case 'DEL' :
                $redis->del($r_key);
        }

        return [true, 'ok'];
    }
}
