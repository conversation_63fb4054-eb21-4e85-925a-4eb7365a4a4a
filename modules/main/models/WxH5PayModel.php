<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/6/11
 * Time: 16:41
 * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=7_4&index=3 //小程序支付
 */
namespace app\modules\main\models;

use app\components\AdvAscribe;
use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\Erp;
use app\components\ErpNew;
use app\components\PointCenter;
use app\components\EventMsg;
use app\jobs\SyncCrmJob;
use app\jobs\SyncPointGrowJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\GroupPurchaseService;
use app\modules\main\models\pay\PayModel;
use yii\db\Exception;

class WxH5PayModel extends CommModel
{

    CONST ENCODE_SALT = 'a@#*!*&^$@#$!!@!!WH5!!';


    CONST APP_ID = YII_ENV_PROD ? 'wx7042d29dafd01227' : 'wx7042d29dafd01227';//服务商商户的APPID

    CONST MCH_ID = YII_ENV_PROD ? '1636516125' : '1636516125';//服务商商户的商户号

    //微信商户平台设置的密钥key
    CONST WX_MCH_ID_KEY = YII_ENV_PROD ? 'PSgrF8NBWuHuqByvkgmrT6v09Pt2zfCw' : 'PSgrF8NBWuHuqByvkgmrT6v09Pt2zfCw';

    //支付序列号
    CONST WX_CERT_SERIAL_NO = YII_ENV_PROD ? '49511647B4447FCBF2E6135563DBA070AA95BCFE' : '49511647B4447FCBF2E6135563DBA070AA95BCFE';

    //微信签名URL
//    CONST WX_SIGN_URL = 'https://api.mch.weixin.qq.com/v3/certificates';

    //统一下单地址
    CONST UNIFIED_ORDER_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/h5";

    //查询订单地址
    CONST WX_ORDER_QUERY_BY_ORDER_NO = 'https://api.mch.weixin.qq.com/v3/pay/transactions/out-trade-no';
    CONST WX_ORDER_QUERY_BY_TRANS_ID = 'https://api.mch.weixin.qq.com/v3/pay/transactions/id';

    //关闭订单
    CONST WX_CLOSE_ORDER_URL = 'https://api.mch.weixin.qq.com/pay/closeorder';

    //退款
    CONST WX_REFUND_ORDER_URL = 'https://api.mch.weixin.qq.com/v3/refund/domestic/refunds';

    //证书路径
    CONST PEM_PATH          = YII_ENV_PROD ? "prod" : "prod";
// H5支付没有用（先放在这里）
    CONST SSL_CERT_PATH     = WEB_PATH. '/../modules/back/config/'.self::PEM_PATH.'/wx_dreame_information_tech_cert.pem';
    CONST SSL_KEY_PATH      = WEB_PATH. '/../modules/back/config/'.self::PEM_PATH.'/wx_dreame_information_tech_key.pem';

    //下单来源
    CONST SOURCE = [
        'MALL'     => 1,
        'PLUMBING' => 2,
        'DEPOSIT'  => 3,
        'POINTS'  => 4,//积分商城
    ];

    //H5场景信息
    CONST H5_INFO = [
      'a_1664246268'=>'Android',
      'i_1666147923'=>'iOS',
    ];

    const KEY_LENGTH_BYTE = 32;
    const AUTH_TAG_LENGTH_BYTE = 16;

    //通知地址
    private $notify_url;

    //退款回调
    private $refund_notify_url;

    public function __construct($config = [])
    {
        parent::__construct($config);

        $this->notify_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/h5-notify';
        $this->refund_notify_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/refund-h5-notify';
    }

    /**
     * @param $price
     * @return int
     * 货币单位转换
     * 配置文件以元为单位 微信支付以分为单位
     */
    protected function _totalFee($price)
    {
        $price = bcmul($price,100,2);
        return CUtil::uint($price);
    }

    public function GetH5PayKey($order_no): string
    {
        return AppCRedisKeys::getH5PayStatus($order_no);
    }

    private function _getH5SignHeaders($http_method,$url,$data)
    {
        $timestamp = time();
        $nonce = CUtil::createVerifyCode(10, 1);

        $url_parts = parse_url($url);
        $canonical_url = ($url_parts['path'] . (!empty($url_parts['query']) ? "?${url_parts['query']}" : ""));
        $message = $http_method."\n".
            $canonical_url."\n".
            $timestamp."\n".
            $nonce."\n".
            $data."\n";

        openssl_sign($message, $raw_sign, self::getPrivateKey(self::SSL_KEY_PATH), 'sha256WithRSAEncryption');
        $sign = base64_encode($raw_sign);

        $schema = 'WECHATPAY2-SHA256-RSA2048';
        $token = sprintf('mchid="%s",nonce_str="%s",signature="%s",timestamp="%d",serial_no="%s"',
            self::MCH_ID, $nonce, $sign, $timestamp, self::WX_CERT_SERIAL_NO);

        return [
            'Authorization: '.$schema. ' ' .$token,
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'User-Agent:*/*',
        ];
    }

    /**
     * @param $sslCertPath
     * @param $sslKeyPath
     * 设置证书路径
     */
    public function GetSSLCertPath(&$sslCertPath, &$sslKeyPath)
    {
        $sslCertPath = self::SSL_CERT_PATH;
        $sslKeyPath = self::SSL_KEY_PATH;
    }


    public static function getPrivateKey($filepath)
    {
        return openssl_get_privatekey(file_get_contents($filepath));
    }

    /**
     * @param $order_no
     * @param int $type
     * @return array
     * 查询微信订单
     * https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_3_2.shtml
     */
    public function wxH5OrderQuery($order_no, int $type = 0)
    {
        $orderKey = $this->GetH5PayKey($order_no);
        $redis = by::redis();
        list($s) = by::redis()->get($orderKey);
        if($s){
            return [true, 'ok'];
        }

        if($type == 0 ){
            $url = $this::WX_ORDER_QUERY_BY_TRANS_ID;
        }else{
            $url = $this::WX_ORDER_QUERY_BY_ORDER_NO;
        }
        $url .= '/'.$order_no.'?mchid='.self::MCH_ID;
        $headers = $this->_getH5SignHeaders('GET',$url,'');
        $response = CUtil::curl_get($url,10,$headers,'','',true);
        // !YII_ENV_PROD && CUtil::debug($url."|" . $response, 'wxp_wx_h5_query');
        CUtil::setLogMsg(
            "wxp_wx_h5_query",
            [],
            $response,
            $headers,
            $url,
            ''
        );
        if(empty($response)){
            return [false, '没有支付信息！'];
        }
        $response = json_decode($response,true);

        if(!isset($response['trade_state'])||$response['trade_state'] !== 'SUCCESS'){
            return [false, $response['trade_state'] ?? '支付失败'];
        }

        return [true, $response];
    }


    /**
     * @param $user_id
     * @param $api
     * @param $order_no
     * @param $real_price
     * @param array $arr
     * @param array $attach
     * @return array
     * @throws Exception 统一下单
     * https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_3_1.shtml
     */
    public function unifiedOrder($user_id, $api, $order_no, $real_price, array $arr, array $attach = []): array
    {
        list($status,$security_key) = $this::getApiKey($api);
        if($status == -1) {
            return [false,'无效的Api信息'];
        }


        $commPay = new CommPayModel();
        list($s,$time_expire) = $commPay->GetPayExpireTime($arr,240);
        if(!$s){
            return [$s,$time_expire];
        }
        $time_expire    = date("Y-m-d\\TH:i:s\\+08:00", $time_expire);


        //attach 不能超过128 字符，这边要注意
        $attach['api']      = $api;
        $attach['user_id']  = $user_id;
        $attach['sign']     = $this::getSign($attach, $security_key, $this::ENCODE_SALT);//自身签名

        $aData['appid']          = $this::APP_ID;
        $aData['mchid']          = $this::MCH_ID;
        $aData['description']    = 'Dreame';
        $aData['out_trade_no']   = $order_no; //商户订单
        $aData['time_expire']    = $time_expire;
        $aData['attach']         = json_encode($attach);//用于微信透传给服务器 	String(127)
        $aData['notify_url']     = $this->notify_url;
        $aData['support_fapiao'] = true;
        //订单金额
        $aData['amount']['total']    = CUtil::uint($real_price);
        $aData['amount']['currency'] = 'CNY'; //CNY：人民币，境内商户号仅支持人民币。
        //场景信息
        $aData['scene_info']['payer_client_ip'] = CUtil::get_client_ip();
        //场景信息-H5场景信息
        $aData['scene_info']['h5_info']['type'] = self::H5_INFO[$api] ?? 'Wap';

        $jsonData = json_encode($aData);

        //生成签名
        $headers = $this->_getH5SignHeaders('POST',self::UNIFIED_ORDER_URL,$jsonData);
        $response      = CUtil::curl_post(self::UNIFIED_ORDER_URL,$jsonData,$headers,10,true, 'IPV4','',true);
        $response  = json_decode($response,true);
        // CUtil::debug(json_encode($aData)."|".json_encode($response),'unifiedorder.h5');
        CUtil::setLogMsg(
            "unifiedorder.h5",
            $aData,
            $response,
            $headers,
            self::UNIFIED_ORDER_URL,
            ''
        );

        if(isset($response['code'])){
            // CUtil::debug('下单失败'.json_encode($aData)."|".json_encode($response),'err.unifiedorder.h5');
            CUtil::setLogMsg(
                "unifiedorder.h5",
                $aData,
                $response,
                $headers,
                self::UNIFIED_ORDER_URL,
                '下单失败'
            );
            return [false,"下单失败"];
        }
        if(!isset($response['h5_url'])){
            // CUtil::debug('下单失败~'.json_encode($aData)."|".json_encode($response),'err.unifiedorder.h5');
            CUtil::setLogMsg(
                "unifiedorder.h5",
                $aData,
                $response,
                $headers,
                self::UNIFIED_ORDER_URL,
                '下单失败'
            );
            return [false,"下单失败"];
        }

        //h5_url 解码
        $h5_url_parts = parse_url($response['h5_url']);
        $h5_url_parts_query = CUtil::decodeUrlQuery($h5_url_parts['query']??'');
        $response = array_merge($response,$h5_url_parts_query);

        //组装发起H5支付
        $PayReq = [
            'h5_url'         => $response['h5_url']??'',
            'timeStamp'     => (string)START_TIME,
            'nonceStr'      => CUtil::createVerifyCode(10,1)
        ];

        $PayReq['prepay_id']    = $response['prepay_id']??'';
        $PayReq['order_no']     = $order_no;

        return [true, $PayReq];
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $refund_no
     * @param bool $r_freight
     * @param int $source
     * @return array
     * @throws Exception
     * @throws \RedisException
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_4
     * 退款
     */
    public function  refund($user_id, $order_no, $refund_no, bool $r_freight = false, int $source = self::SOURCE['MALL']): array
    {

        switch ($source) {
            case self::SOURCE['MALL'] :
                $mOgoods    = by::Ogoods();
                $o_info     = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
                $r_goods    = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
                $o_goods    = $mOgoods->GetListByOrderNo($user_id, $order_no);
                $og_ids     = array_column($r_goods, 'og_id');
                $refund_fee = 0;

                foreach($o_goods as $val) {
                    if (in_array($val['id'], $og_ids)) {
                        $refund_fee = bcadd($refund_fee, $val['price']);
                    }
                }

                if ($r_freight) {
                    $refund_fee = bcadd($refund_fee, $o_info['fprice']);
                }

                $total_fee = bcadd($o_info['price'], $o_info['fprice']);

                break;
            case self::SOURCE['PLUMBING'] :
                $order_info = by::plumbingOrder()->getInfoByOrderNo($order_no);
                $total_fee  = $refund_fee = by::Gtype0()->totalFee($order_info['price'] ?? 0);

                break;
            case self::SOURCE['DEPOSIT'] :
                $order_info = by::Odeposit()->getInfoByDepositOrderNo($user_id,$order_no);
                $total_fee  = $refund_fee = by::Gtype0()->totalFee($order_info['price'] ?? 0);
                break;

            default :

                break;
        }


        $aData['out_trade_no']  = $order_no;
        $aData['out_refund_no']  = $refund_no;//小程序商户系统内部的退款单号
        $aData['reason'] = '申请退款！';
        $aData['notify_url']    = $this->refund_notify_url;
        $aData['amount']['refund'] = CUtil::uint($refund_fee ?? 0);
        $aData['amount']['total'] = CUtil::uint($total_fee ?? 0);
        $aData['amount']['currency'] = 'CNY';
        $jsonData = json_encode($aData);

        //生成签名
        $headers  = $this->_getH5SignHeaders('POST', self::WX_REFUND_ORDER_URL, $jsonData);
        $response = CUtil::curl_post(self::WX_REFUND_ORDER_URL, $jsonData, $headers, 10, true, 'IPV4', '', true);
        $response = json_decode($response, true);
        // !YII_ENV_PROD && CUtil::debug(json_encode($aData)."|".json_encode($response),'refund.h5');
        !YII_ENV_PROD && CUtil::setLogMsg(
            "refund.h5",
            $aData,
            $response,
            $headers,
            self::WX_REFUND_ORDER_URL,
            ''
        );

        if(isset($response['code'])){
            // CUtil::debug('退款失败'.json_encode($aData)."|".json_encode($response),'err.refund.h5');
            CUtil::setLogMsg(
                "err.refund.h5",
                $aData,
                $response,
                $headers,
                self::WX_REFUND_ORDER_URL,
                '退款失败'
            );
            return [false,"退款失败"];
        }
        if(!isset($response['out_trade_no'])){
            // CUtil::debug('退款失败~'.json_encode($aData)."|".json_encode($response),'err.refund.h5');
            CUtil::setLogMsg(
                "err.refund.h5",
                $aData,
                $response,
                $headers,
                self::WX_REFUND_ORDER_URL,
                '退款失败'
            );
            return [false,"退款失败"];
        }

        return [true,'ok'];
    }


    public function decryptNotifyData($associatedData, $nonceStr, $ciphertext)
    {
        $aesKey = self::WX_MCH_ID_KEY;
        if (strlen($aesKey) != self::KEY_LENGTH_BYTE) {
            return [false, '无效的ApiV3Key，长度应为32个字节'];
        }
        $ciphertext = \base64_decode($ciphertext);
        if (strlen($ciphertext) <= self::AUTH_TAG_LENGTH_BYTE) {
            return [false, '回调参数不正确！'];
        }
        // openssl (PHP >= 7.1 support AEAD)
        if (PHP_VERSION_ID >= 70100 && in_array('aes-256-gcm', \openssl_get_cipher_methods())) {
            $ctext   = substr($ciphertext, 0, -self::AUTH_TAG_LENGTH_BYTE);
            $authTag = substr($ciphertext, -self::AUTH_TAG_LENGTH_BYTE);

            $data = \openssl_decrypt($ctext, 'aes-256-gcm', $aesKey, \OPENSSL_RAW_DATA, $nonceStr,
                $authTag, $associatedData);

            if(empty($data)||is_null(json_decode($data))){
                return [false,'解密失败!(1)'];
            }

            return [true, json_decode($data,true)];
        }

        return [false, '解密失败!(2)'];
    }



    /**
     * @param array $notify_data
     * @return array
     * @throws Exception
     * 小程序微信H5支付结果通用通知
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_7&index=8
     */
    public function notify($notify_data = [])
    {
        if (!isset($notify_data['trade_state']) || $notify_data['trade_state'] != 'SUCCESS' || !isset($notify_data['out_trade_no'])) {
            CUtil::debug("未付款:" . json_encode($notify_data), 'err.n.pay');
            return [false, '未付款'];
        }

        $attachJson     = $notify_data['attach'] ?? "";
        $attach         = json_decode($attachJson, true);
        $api            = $attach['api'] ?? "";
        $user_id        = $attach['user_id'] ?? 0;
        $source         = $attach['source']  ?? self::SOURCE['MALL'];
        $sign           = $attach['sign']    ?? "";
        unset($attach['sign']);

        $order_no       = $notify_data['out_trade_no'] ?? "";

        list($status, $security_key) = $this::getApiKey($api);
        if ($status == -1) {
            return [false, '无效的订单Api信息'];
        }

        if (empty($user_id) || empty($order_no) || empty($sign)) {
            CUtil::debug("参数缺失:" . json_encode($notify_data), 'err.n.pay');
            return [false, '参数缺失'];
        }

        $verify = $this::getSign($attach, $security_key, $this::ENCODE_SALT);
        if ($verify !== $sign) {
            CUtil::debug("签名检测错误:" . json_encode($notify_data), 'err.n.pay');
            return [false, '无效的支付订单'];
        }
        //####################支付成功订单通知，设置redis KEY
        $redis = by::redis();
        $orderKey = $this->GetH5PayKey($order_no);
        $redis->set($orderKey,$order_no,1800);

        //####################进行回调逻辑判断
        list($status, $msg) = $this->__orderNotify($user_id, $order_no, $notify_data, $source);
        if (!$status) {
            return [false, $msg];
        }

        return [true, 'ok'];
    }

    /**
     * @param array $notify_data
     * @return array
     * @throws Exception
     * 小程序微信退款结果通知
     * https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_16&index=10
     */
    public function refundNotify($notify_data = [])
    {
        if ($notify_data['refund_status'] != 'SUCCESS') {
            CUtil::debug("退款失败:" . json_encode($notify_data), 'err.nr.pay');
            return [false, $notify_data['return_msg'] ?? '退款失败'];
        }

        $order_no  = $notify_data['out_trade_no'] ?? "";
        $refund_no = $notify_data['out_refund_no'] ?? "";


        //todo 处理退款成功时间
        $success_time    = $notify_data['success_time'] ?? 0;
        //获取时间戳
        if($success_time){
            //设置时区
            $timezones = new \DateTimeZone('Asia/Shanghai');
            //获取当前时间
            $timeObj  = new \DateTime($success_time, $timezones);
            $success_time = $timeObj->getTimestamp() ?? 0;
        }

        $amount = $notify_data['amount']??[];
        $total_fee = CUtil::uint($amount['total'] ?? 0);//订单金额
        $refund_fee = CUtil::uint($amount['refund'] ?? 0);//退款金额
        $payer_total_fee = CUtil::uint($amount['payer_total'] ?? 0);//用户支付金额
        $payer_refund_fee = CUtil::uint($amount['payer_refund'] ?? 0);//用户退款金额

        //todo 订单状态检测
        $mOrefundMain    = by::OrefundMain();
        $plumbing_refund = by::plumbingRefund();
        $rm_info         = $mOrefundMain->GetInfoByRefundNo($refund_no);
        $pr_info         = $plumbing_refund->getInfoByRefundNo($refund_no);
        $d_refund = by::OrefundDepositMain();
        $d_info = $d_refund->GetInfoByRefundNo($refund_no);

        if (empty($rm_info) && empty($pr_info) && empty($d_info)) {
            CUtil::debug("退款订单不存在:{$refund_no}", 'err.nr.pay');
            return [false, '退款订单不存在'];
        }

        if (!empty($rm_info)) {
            $r_info = by::Orefund()->GetInfoByRefundNo($rm_info['user_id'], $refund_no);
            if(intval($r_info['status']) == $mOrefundMain::STATUS['SUCCESS']){
                CUtil::debug("退款已完成:" . json_encode($r_info), 'warn.nr.pay');
                return [true, '退款已完成'];
            }
            if ($r_info['status'] != $mOrefundMain::STATUS['P_PASS']) {
                CUtil::debug("无效订单:" . json_encode($r_info), 'warn.nr.pay');
                return [false, '无效订单'];
            }

            //订单表数据
            $o_info = by::Ouser()->GetInfoByOrderId($r_info['user_id'], $order_no);

            //订单商品表数据
            $oGoods = by::Ogoods()->GetListByOrderNo($r_info['user_id'], $order_no);

            //退款商品表数据
            $rGoods = by::Orgoods()->GetListByRefundNo($r_info['user_id'], $refund_no, $order_no);
            $ids    = array_column($rGoods, 'og_id');

            //todo 退款提醒
            EventMsg::factory()->run('orderMsgSend', ['event' => 'refund', 'order_no' => $order_no,'refund_no'=>$refund_no]);
        } elseif(!empty($d_info)) {
            $dr_info = by::OrefundDeposit()->GetInfoByRefundNo($d_info['user_id'], $refund_no);
            if($dr_info['status'] != $d_refund::STATUS['P_PASS']){
                CUtil::debug("无效订单:" . json_encode($dr_info), 'warn.deposit.pay');
                return [false, '无效订单'];
            }
            //订单表数据
            $od_info = by::Odeposit()->getInfoByDepositOrderNo($dr_info['user_id'], $order_no);

        } else {
            if ($pr_info['status'] != by::plumbingOrder()::REFUND_STATUS['AUDIT_PASS']) {
                CUtil::debug("无效订单:" . json_encode($pr_info), 'plumbing-pay-refund.warn');

                return [false, '无效订单'];
            }
        }



        $trans = by::dbMaster()->beginTransaction();
        if (!empty($rm_info)) {
            try {
                //回退销量
                foreach($oGoods as $val) {
                    if (in_array($val['id'], $ids)) {
                        //判断商品类型
                        $goodsType = $val['goods_type'] ?? '';
                        $goodsSource    = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                            ? by::GoodsStockModel()::SOURCE['WARES']
                            : by::GoodsStockModel()::SOURCE['MAIN'];
                        by::GoodsStockModel()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'SALE', false, $goodsSource);

                        $giniId = $val['gini_id'] ?? 0;
                        if($giniId){
                            by::Gini()->UpdateStock($giniId,$val['num'],'SALE',false);
                        }
                    }
                }

                //退优惠券
                $r_count = $mOrefundMain->getCountByOrderNo($r_info['user_id'], $order_no);
                if(count($oGoods) == (count($rGoods)+$r_count) && !empty($o_info['coupon_id'])){
                    list($s,$m) = by::userCard()->UnLockCard($r_info['user_id'],$o_info['coupon_id'],$trans);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                }

                //退消费券
                if(count($oGoods) == (count($rGoods)+$r_count) && !empty($o_info['consume_id'])){
                    list($s,$m) = by::userCard()->UnLockCard($r_info['user_id'],$o_info['consume_id'],$trans);
                    if (!$s) {
                        throw new \Exception($m);
                    }
                }

                //更改退款申请表为退款成功
                $next_st     = $mOrefundMain::STATUS['SUCCESS'];

                $save        = [
                    'rtime' => $success_time>0 ? $success_time:time(),
                    'price' => $refund_fee,
                ];
                list($s, $m) = $mOrefundMain->SyncInfo($r_info['user_id'],$refund_no, $next_st, $save);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                // //todo 订单同步crm
                // Crm::factory()->push($r_info['user_id'],'order',['user_id'=>$r_info['user_id'],'order_no'=>$order_no]);
                // Crm::factory()->push($r_info['user_id'],'orderLine',['user_id'=>$r_info['user_id'],'order_no'=>$order_no]);
                //
                // //todo 退款crm
                // Crm::factory()->push($r_info['user_id'],'refund',['user_id'=>$r_info['user_id'],'refund_no'=>$refund_no]);
                // Crm::factory()->push($r_info['user_id'],'refundLine',['user_id'=>$r_info['user_id'],'refund_no'=>$refund_no]);
                PointCenter::factory()->refundPush($r_info['user_id'], $refund_no);

                //todo oms退款推送
                by::OrefundMain()->refundPushOms($r_info['m_type']??0,$r_info['user_id'],$refund_no,$order_no);

                // 团购订单退款消息推送
                GroupPurchaseService::getInstance()->sendRefundMessage($order_no);

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'err.nr.pay');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        } elseif(!empty($d_info)){
            try {
                //回退销量
                if(isset($od_info) && isset($od_info['gid']) && $od_info['gid']){
                    by::Gprestock()->UpdateStock($od_info['gid'], $od_info['sid']??0, $od_info['num']??1, 'SALE',false);
                }

                //更改退款申请表为退款成功
                $next_st     = $d_refund::STATUS['SUCCESS'];
                $save        = [
                    'rtime' => $success_time>0 ? $success_time:time(),
                    'price' => $refund_fee,
                ];
                list($s, $m) = $d_refund->SyncInfo($d_info['user_id'],$refund_no, $next_st, $save);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'err.deposit.pay');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        } else {
            try {
                //更改退款申请表为退款成功
                $next_st     = by::plumbingOrder()::REFUND_STATUS['REFUND_SUCCESS'];
                $update_data = [
                    'utime' => time(),
                    'rtime' => $success_time,
                    'price' => $refund_fee
                ];

                list($s, $m) = $plumbing_refund->SyncInfo($pr_info['user_id'], $refund_no, $order_no, $next_st, $update_data);
                if (!$s) {
                    throw new \Exception($m);
                }

                $trans->commit();

                return [true, 'ok'];
            } catch (\Exception $e) {
                CUtil::debug($e->getMessage(), 'plumbing-pay-refund.err');

                $trans->rollBack();

                return [false, '退款失败'];
            }
        }
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $notify_data
     * @param $source
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 订单回调
     */
    private function __orderNotify($user_id, $order_no, $notify_data, $source): array
    {
        //金额处理
        $total_fee       = CUtil::uint($notify_data['amount']['total'] ?? 0);
        $currency        = $notify_data['amount']['currency'] ?? 'CNY';
        $payer_total_fee = CUtil::uint($notify_data['amount']['payer_total'] ?? 0);
        $payer_currency  = $notify_data['amount']['payer_currency'] ?? 'CNY';


        switch ($source) {
            case self::SOURCE['MALL']:
            case self::SOURCE['POINTS']:
                $mOuser = by::Ouser();
                $mOmain = by::Omain();
                //todo 订单状态检测
                $order  = $mOuser->CommPackageInfo($user_id, $order_no, false, false, false, false,true,false,false);
                if (empty($order)) {
                    CUtil::debug("订单不存在|{$user_id}|{$order_no}", 'err.n.pay');
                    return [false, '订单不存在'];
                }

                if ($order['status'] == $mOmain::ORDER_STATUS['WAIT_SEND']) {
                    CUtil::debug("订单状态已更新:" . json_encode($order), 'err.n.pay');
                    return [true, 'ok'];
                }

                if ($order['status'] != $mOmain::ORDER_STATUS['WAIT_PAY']) {
                    CUtil::debug("无效订单:" . json_encode($order), 'warn.n.pay');
                    return [false, '无效订单'];
                }

                $pay_price  = bcadd($order['price'], $order['fprice'], 2);
                $pay_price = $this->_totalFee($pay_price);
                if (bccomp($pay_price, $total_fee, 2) != 0) {
                    CUtil::debug("支付金额与商品实际金额不一致:" . $pay_price . '|' . $total_fee . '|' . json_encode($notify_data), 'err.n.pay');
                    return [false, '支付金额与商品实际金额不一致'];
                }

                break;
            case self::SOURCE['PLUMBING']:
                $order = by::plumbingOrder()->getInfoByOrderNo($order_no, false, true);
                if (empty($order)) {
                    CUtil::debug("工单不存在|{$user_id}|{$order_no}", 'plumbing-pay.err');
                    return [false, '工单不存在'];
                }

                if ($order['status'] == by::plumbingOrder()::STATUS['WAIT_SERVICE']) {
                    CUtil::debug("工单状态已更新:" . json_encode($order), 'plumbing-pay.err');
                    return [true, 'ok'];
                }

                if ($order['status'] != by::plumbingOrder()::STATUS['WAIT_PAY']) {
                    CUtil::debug("无效工单:" . json_encode($order), 'plumbing-pay.err');
                    return [false, '无效工单'];
                }

                $pay_price = $this->_totalFee($order['price'] ?? 0);
                if (bccomp($pay_price, $total_fee, 2) != 0) {
                    CUtil::debug("支付金额与实际金额不一致:" . json_encode($notify_data).'|'.$pay_price.'|'.$total_fee, 'plumbing-pay.err');
                    return [false, '支付金额与实际金额不一致'];
                }

                break;
            case self::SOURCE['DEPOSIT']:

                $order = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);
                if (empty($order)) {
                    CUtil::debug("定金订单不存在|{$user_id}|{$order_no}", 'deposit-pay.err');
                    return [false, '定金订单不存在'];
                }


                if ($order['status'] == by::Odeposit()::STATUS['WAIT_SEND']) {
                    CUtil::debug("定金订单状态已更新:" . json_encode($order), 'deposit-pay.err');
                    return [true, 'ok'];
                }


                if ($order['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                    CUtil::debug("无效订单:" . json_encode($order), 'deposit-pay.warn');
                    return [false, '无效订单'];
                }


                $pay_price = $this->_totalFee($order['price'] ?? 0);
                if (bccomp($pay_price, $total_fee, 2) != 0) {
                    CUtil::debug("支付金额与实际金额不一致:" . json_encode($notify_data).'|'.$pay_price.'|'.$total_fee, 'deposit-pay.err');
                    return [false, '支付金额与实际金额不一致'];
                }

                break;
            default :
                return [false, '订单来源不合法'];
        }

        return $this->afterPay($user_id, $order_no, $notify_data, $order, $source);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $notify_data
     * @param $order
     * @param $source
     * @return array
     * @throws Exception
     * @throws \Exception
     * 付款后操作
     */
    public function afterPay($user_id, $order_no, $notify_data, $order, $source): array
    {
        return by::BasePayModel()->afterPay($user_id, $order_no, $notify_data, $order, $source, by::Omain()::PAY_BY_WX_H5);
    }

    /**
     * @param $user_id
     * @param $order_no
     * @param $api
     * @return array
     * @throws Exception
     * https://pay.weixin.qq.com/wiki/doc/api/app/app.php?chapter=9_12&index=2
     * 未付款订单付款
     */
    public function AgainPay($user_id, $order_no, $api, $attach =['source'=> self::SOURCE['MALL']],$order_type = '')
    {
        if (empty($user_id) || empty($order_no)) {
            return [false, '参数错误'];
        }

        //是否需要重新创建订单
        $needReunified = 0;
        $endPayment = 0;


        if ($order_type == by::Odeposit()::TYPE['DEPOSIT']){
            $oInfo = by::Odeposit()->getInfoByDepositOrderNo($user_id, $order_no);

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Odeposit()::STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Odeposit()::STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }

            $oeInfo = by::OdepositE()->GetInfoByOrderId($user_id,$order_no);
            $cfg = $oeInfo['cfg'] ?? [];

            //定金订单
            $endPayment = $cfg['presale_time'] ?? 0;
            $needReunified = 1;

        }else{
            $oInfo = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
            if(isset($oInfo['deposit_order_no']) && $oInfo['deposit_order_no']){
                $now = intval(START_TIME);
                $oInfo['ctime'] = $now;
                $oMainInfo = by::Omain()->getInfoByOrderNo($user_id,$order_no);

                //尾款订单校验库存
                $commPay = new CommPayModel();
                list($s,$msg) = $commPay->CheckTailOrderStock($user_id,$oInfo['deposit_order_no']);
                if(!$s){
                    return [$s,$msg];
                }

                //定金订单
                $endPayment = $oMainInfo['end_payment'] ?? 0;
                $needReunified = 2;
            }

            if (empty($oInfo)) {
                return [false, '订单不存在'];
            }
            if ($oInfo['status'] == by::Omain()::ORDER_STATUS['WAIT_SEND']) {
                return [false, '请刷新页面，检查订单支付状态'];
            } elseif ($oInfo['status'] != by::Omain()::ORDER_STATUS['WAIT_PAY']) {
                return [false, '该订单不可操作'];
            }
        }

        //查微信订单是否已付款
        list($s) = $this->wxH5OrderQuery($order_no, 1);
        if ($s) {
            return [false, '该订单已支付'];
        }

        //付款流水
        $aOpay      = by::model('OPayModel','goods')->GetOneInfo($order_no,false);
        if (empty($aOpay)) {
            return [false, '订单不存在(2)'];
        }

        // 重新创建
        $rebuild = false;
        // 切换支付方式
        if ($aOpay['pay_type'] != by::Omain()::PAY_BY_WX_H5) {
            // 保存支付信息
            if ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET) {
                PayModel::getInstance()->saveTradeOrder($order_no, $aOpay['pay_type'], $aOpay['prepay_id'], $aOpay['h5_url'], $aOpay['ptime']);
            }

            // 新支付方式的支付信息
            $currentPayData = PayModel::getInstance()->getTradeOrder($order_no, by::Omain()::PAY_BY_WX_H5);

            if ($currentPayData && ($aOpay['pay_type'] != by::Omain()::PAY_BY_NO_SET)) {
                // 更新支付流水表
                $payData = [
                    'prepay_id' => $currentPayData['prepay_id'], 'ptime' => $currentPayData['ptime'], 'h5_url' => $currentPayData['h5_url'], 'pay_type' => by::Omain()::PAY_BY_WX_H5,
                ];
                by::model('OPayModel', 'goods')->SaveLog($order_no, $payData);
                // 更新数据
                $aOpay['prepay_id'] = $currentPayData['prepay_id'];
                $aOpay['ptime'] = $currentPayData['ptime'];
                $aOpay['h5_url'] = $currentPayData['h5_url'];
            } else {
                // 新支付方式的支付信息不存在，重新生成
                $rebuild = true;
            }
        }

        // todo 统一下单生成h5_url只有5分钟时间
        $now        = time();
        if ($rebuild || (empty($aOpay['prepay_id']) && $aOpay['price']) || bcsub($now, $aOpay['ptime']) > 240 ) {
            $other              = [
                'body'          => '订单支付',
                'ctime'         => $oInfo['ctime'],
                'needReunified' => $needReunified,
                'endPayment'    => $endPayment,
            ];

            list($status, $PayReq) = $this->unifiedOrder($user_id, $api, $order_no, $aOpay['price'], $other, $attach);
            if (!$status) {
                return [false, $PayReq];
            }

            //更新支付流水表
            $payData = [
                'prepay_id' => $PayReq['prepay_id'], 'ptime' => $now, 'h5_url' => $PayReq['h5_url'] ?? '', 'pay_type'=>by::Omain()::PAY_BY_WX_H5,
            ];
            by::model('OPayModel','goods')->SaveLog($order_no,$payData);

        } else {
            $prepay_id      = $aOpay['prepay_id'];
            $PayReq = [
                'appId'         => self::APP_ID,
                'timeStamp'     => (string)$now,
                'nonceStr'      => CUtil::createVerifyCode(10,1),
                'prepay_id'     => $prepay_id,
                'h5_url'        => $aOpay['h5_url']??''
            ];
            $PayReq['order_no']     = $order_no;
        }

        $PayReq['pay_type'] = by::Omain()::PAY_BY_WX_H5;
        $r_price            = bcsub($aOpay['price'], $oInfo['fprice'] ?? 0);
        $r_price            = by::Gtype0()->totalFee($r_price, 1);
        $rate               = byNew::PointConfigModel()::getConfig()['reward_rate'] ?? 1;
        $PayReq['coin']     = bcmul($r_price, $rate, 2);

        //TODO 绑定用户等级
        $PayReq['coin'] = by::memberCenterModel()->GetCoinByOrderCoin($user_id,$PayReq['coin'],time());

        if (isset($PayReq['appId'])) {
            unset($PayReq['appId']);
        }

        return [true, $PayReq];
    }

}
