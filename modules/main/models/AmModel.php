<?php
/**
 * Created by PhpStor<PERSON>.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 16:18
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class AmModel extends CommModel
{
    CONST EXP       = 3600; //缓存时间
    CONST MAX_COUNT = 20;    //最大优惠券数量

    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame`.`t_ac_m`";
    }

    public $tb_fields = [
        'id', 'ac_id', 'mc_id', 'stock', 'sales','level', 'last_user', 'ctime'
    ];

    /**
     * @param $ac_id
     * @return string
     */
    private function __getAmIdsByAid($ac_id): string
    {
        return AppCRedisKeys::getAmIdsByAid($ac_id);
    }

    /**
     * @param $id
     * @return string
     */
    private function __getAmById($id): string
    {
        return AppCRedisKeys::getAmById($id);
    }

    /**
     * @param $ac_id
     * @return string
     */
    private function __getListCount($ac_id): string
    {
        return AppCRedisKeys::getAmListCount($ac_id);
    }

    private function __getIdByAidAndMid($ac_id,$market_id):string
    {
        return AppCRedisKeys::getIdByAidAndMid($ac_id,$market_id);
    }

    public function __delIdByAidAndMid($ac_id,$market_id)
    {
        $redis = by::redis();
        $redis_key = $this->__getIdByAidAndMid($ac_id,$market_id);
        $redis->del($redis_key);
    }

    public function __getCouponNumByAidAndMid($ac_id,$market_id):string
    {
        return AppCRedisKeys::getCouponNumByAidAndMid($ac_id,$market_id);
    }

    public function __getCouponListByAcId($ac_id):string
    {
        return AppCRedisKeys::getCouponListByAcId($ac_id);
    }
    /**
     * @param int $ac_id
     * @param int $id
     */
    public function __delCache(int $ac_id = 0, int $id = 0)
    {
        $r_key1 = $this->__getAmById($id);
        $r_key2 = $this->__getAmIdsByAid($ac_id);
        $r_key3 = $this->__getListCount($ac_id);
        $r_key4 = $this->__getCouponListByAcId($ac_id);

        by::redis('core')->del($r_key1, $r_key2, $r_key3, $r_key4);
    }

    /**
     * @param int $ac_id
     * @param int $user_id
     * 清除优惠卷列表  ---针对个人
     */
    public function __delCouponCache(int $ac_id = 0 ,$user_id=0)
    {
        $r_key1 = $this->__getCouponListByAcId($ac_id);
        by::redis('core')->hdel($r_key1,$user_id);
    }

    /**
     * @param int $ac_id
     * 清除优惠卷列表  ---针对所有
     */
    public function __delCouponAllCache(int $ac_id = 0)
    {
        $r_key1 = $this->__getCouponListByAcId($ac_id);
        by::redis('core')->del($r_key1);
    }
    /**
     * @param $ac_id
     * @param $mc_id
     * @param $stock
     * @param $tran
     * @return array
     * @throws Exception
     * 添加优惠券
     */
    public function add($ac_id, $mc_id, $stock, $level='', $tran = null): array
    {
        $ac_id = CUtil::uint($ac_id);
        $mc_id = CUtil::uint($mc_id);
        $stock = CUtil::uint($stock);
        $ctime = intval(START_TIME);

        if(empty($ac_id) || empty($mc_id)){
            return [false,'缺少参数'];
        }

        $market = by::marketConfig()->getOneById($mc_id);
        if (empty($market)){
            return [false,'优惠券不存在'];
        }

        $tb = $this->tbName();
        $db = by::dbMaster();
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            $data = [
                'ac_id'      => $ac_id,
                'mc_id'      => $mc_id,
                'stock'      => $stock,
                'ctime'      => $ctime,
                'level'      => $level,
            ];

            $row = $db->createCommand()->insert($tb, $data)->execute();
            $id = $db->getLastInsertID();
            if (!$row){
                throw new MyExceptionModel('添加优惠券失败');
            }

            //扣除资源库存
            list($status, $msg) = by::marketConfig()->stockModify($mc_id, $stock);
            if (!$status) {
                throw new MyExceptionModel($msg);
            }

            is_null($tran) && $new_tran->commit();

            $this->__delCache($ac_id);

            return [true,$this->getOneById($id)];
        }catch (MyExceptionModel $_e) {
            is_null($tran) && $new_tran->rollBack();

            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();

            return [false,'添加优惠券失败，请检查库存是否充足'];
        }
    }

    /**
     * @param $id
     * @param $ac_id
     * @param $mc_id
     * @param $stock
     * @param string $level
     * @param $tran
     * @return array
     * @throws Exception
     * 编辑优惠券
     */
    public function edit($id, $ac_id, $mc_id, $stock, string $level = '', $tran = null): array
    {
        $id    = CUtil::uint($id);
        $ac_id = CUtil::uint($ac_id);
        $mc_id = CUtil::uint($mc_id);
        $stock = CUtil::uint($stock);

        if (empty($id) || empty($ac_id) ||empty($mc_id)){
            return [false,'缺少参数'];
        }

        $info = $this->getOneById($id);
        if (empty($info)){
            return [false,'数据不存在'];
        }

        $market = by::marketConfig()->getOneById($mc_id);
        if (empty($market)){
            return [false,'优惠券不存在'];
        }

        if ($info['mc_id'] == $mc_id && $stock == $info['stock'] && $level == $info['level']){
            return [true, '修改成功'];
        }

        $tb       = $this->tbName();
        $db       = by::dbMaster();
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            $data = [
                'mc_id'     => $mc_id,
                'stock'     => $stock,
                'last_user' => 0,
                'level'     => $level,
            ];

            if ($info['mc_id'] != $mc_id){
                $data['sales'] = 0;//更改优惠券 发放数量归0
            }

            //修改前回退旧的资源库存
            if($info['stock'] > 0) {
                list($status, $msg) = by::marketConfig()->stockModify($info['mc_id'], -($info['stock']));
                if (!$status) {
                    throw new MyExceptionModel($msg);
                }
            }

            $row = $db->createCommand()->update($tb, $data, "`id`=:id",[":id"=>$id])->execute();
            if (!$row){
                throw new MyExceptionModel('修改优惠券失败');
            }

            //扣除资源库存
            list($status, $msg) = by::marketConfig()->stockModify($mc_id, $stock);
            if (!$status) {
                throw new MyExceptionModel($msg);
            }

            is_null($tran) && $new_tran->commit();

            $this->__delCache($ac_id, $id);

            return [true, $this->getOneById($id)];
        }catch (MyExceptionModel $_e) {
            is_null($tran) && $new_tran->rollBack();

            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();

            return [false, '修改优惠券失败，请检查同一活动下优惠券是否重复，或库存是否充足'];
        }
    }

    /**
     * @param $id
     * @return array|false|DataReader
     * @throws Exception
     * 通过ID获取唯一数据
     */
    public function getOneById($id)
    {
        $id = CUtil::uint($id);
        if(empty($id)) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getAmById($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`", $this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":id", $id);
            $aData   = $command->queryOne();
            $redis->set($redis_key, json_encode($aData),empty($aData) ? 10 : self::EXP);
        }

        return $aData;
    }

    /**
     * @param $ac_id
     * @return array
     * @throws Exception
     */
    public function getListByAid($ac_id): array
    {
        $ac_id = CUtil::uint($ac_id);
        if(empty($ac_id)) {
            return [];
        }

        list(,$ids) = $this->getIdsByAid($ac_id);
        if (empty($ids)){
            return [];
        }

        $list = [];
        foreach ($ids as $id) {
            $info  = $this->getOneById($id);
            if (!empty($info)) {
                $market       = by::marketConfig()->getOneById($info['mc_id']);
                $info['name'] = $market['name']??'';
                $list[]       = $info;
            }
        }

        return $list;
    }

    public function __delAmList($ac_id)
    {
        $ac_id = CUtil::uint($ac_id);
        if(empty($ac_id)) {
            return [];
        }
        list(,$ids) = $this->getIdsByAid($ac_id);
        if (empty($ids)){
            return [];
        }

        $redis       = by::redis('core');

        foreach ($ids as $id){
            $redis_key   = $this->__getAmById($id);
            $redis->del($redis_key);
        }
    }



    /**
     * @param $ac_id
     * @return array
     * @throws Exception
     * 通过活动ID获取优惠券
     */
    public function getIdsByAid($ac_id): array
    {
        $ac_id = CUtil::uint($ac_id);
        if(empty($ac_id)) {
            return [false,'aid不能为空'];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getAmIdsByAid($ac_id);
        $aJson       = $redis->get($redis_key);
        $ids        = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            $count   = self::MAX_COUNT;
            $sql     = "SELECT `id` FROM  {$tb} WHERE `ac_id`=:ac_id ORDER BY `id` asc LIMIT {$count}";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":ac_id", $ac_id);
            $ids     = $command->queryAll();
            $ids     = empty($ids) ? [] : array_column($ids,'id');

            $redis->set($redis_key, json_encode($ids),empty($aData) ? 10 : self::EXP);
        }

        return [true,$ids];
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除优惠券
     */
    public function del($id): array
    {
        $id  = CUtil::uint($id);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }

        $info = $this->getOneById($id);
        if(empty($info)){
            return [false, '优惠券不存在'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        try {
            $tb  = self::tbName();
            $sql = "DELETE FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $row = by::dbMaster()->createCommand($sql,[':id'=>$id])->execute();
            if (!$row) {
                throw new \Exception('删除am失败');
            }

            //修改后回退旧的资源库存
            if ($info['stock'] > 0) {
                list($status, $msg) = by::marketConfig()->stockModify($info['mc_id'], -$info['stock']);
                if (!$status) {
                    throw new \Exception($msg);
                }
            }

            $transaction->commit();

            $this->__delCache($info['ac_id'],$id);
            $this->__delIdByAidAndMid($info['ac_id'], $info['mc_id']);

            return [true, $info];
        } catch (\Exception $e) {
            $transaction->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);

            return [false, $e->getMessage()];
        }
    }

    /**
     * @param $ac_id
     * @return int
     * @throws Exception
     * 通过活动ID获取优惠券数量
     */
    public function getListCount($ac_id): int
    {
        $ac_id = CUtil::uint($ac_id);
        if(empty($ac_id)) {
            return 0;
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getListCount($ac_id);
        $count       = $redis->get($redis_key);

        if ($count === false) {
            $tb      = $this->tbName();
            $sql     = "SELECT COUNT(*) FROM  {$tb} WHERE `ac_id`=:ac_id";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":ac_id", $ac_id);
            $count   = $command->queryScalar();
            $count     = !empty($count) ? $count : 0;

            $redis->set($redis_key, $count,empty($count) ? 10 : self::EXP);
        }

        return CUtil::uint($count);
    }

    /**
     * @param $ac_id
     * @param $id
     * @param $num
     * @param int $stock
     * @param int $last_user
     * @return array
     * @throws Exception
     * 库存修改
     */
    public function stockModify($ac_id, $id, $num, int $stock = 0, int $last_user = 0): array
    {

        $set    = " stock = stock - (:num), sales = sales + (:num_1)";
        $params = [':num' => $num, ':id' => $id, ':num_1' => $num, ':num_2' => $num];

        switch (true) {
            case $stock == 1 && $num == 1 :
            case $stock == 0 && $num == -1 :
                $set .= ", last_user = (:last_user)";
                $params[':last_user'] = $last_user;

                break;
            default :
                break;
        }

        $tb  = self::tbName();
        $sql = "UPDATE {$tb} SET{$set} WHERE id = :id AND stock - (:num_2) >= 0";
        $res = by::dbMaster()->createCommand($sql, $params)->execute();

        if ($res == 0) {
            return [false, '库存不够!'];
        }

        $this->__delCache($ac_id, $id);

        return [true, 'ok'];

    }

    /**
     * @param $ac_id
     * @param $market_id
     * @return int|mixed|string
     * @throws Exception
     * 通过ac_id和mc_id获取关联表id
     */
    public function getIdByAidAndMid($ac_id, $market_id)
    {
        $ac_id     = CUtil::uint($ac_id);
        $market_id = CUtil::uint($market_id);
        if (empty($ac_id) || empty($market_id)) {
            return 0;
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getIdByAidAndMid($ac_id, $market_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb    = $this->tbName();
            $sql   = "SELECT `id` FROM {$tb} WHERE `ac_id` =:ac_id AND mc_id=:mc_id LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [":ac_id" => $ac_id, ":mc_id" => $market_id])->queryOne();
            $redis->set($redis_key, json_encode($aData), empty($aData) ? 10 : self::EXP);
        }

        return $aData['id'] ?? 0;
    }

    /**
     * @param $ac_id
     * @param $market_id
     * @return int|mixed|string
     * @throws Exception
     * 通过ac_id和mc_id获取优惠卷库存
     */
    public function getCouponNumByAidAndMid($ac_id, $market_id)
    {

        $ac_id = CUtil::uint($ac_id);
        if (empty($ac_id)) {
            return 0;
        }

        $market_id = CUtil::uint($market_id);
        if (empty($market_id)) {
            return 0;
        }
        $tb = $this->tbName();
        $sql = "SELECT `stock` FROM {$tb} WHERE `ac_id` =:ac_id AND mc_id=:mc_id LIMIT 1";
        $aData = by::dbMaster()->createCommand($sql, [":ac_id" => $ac_id, ":mc_id" => $market_id])->queryOne();
        return $aData['stock'] ?? '';
    }

    /**
     * @param $ac_id
     * @param $user_id
     * @return array|int
     * @throws Exception
     * 通过活动id获取优惠卷
     */
    public function getCouponListByAcId($ac_id)
    {
        $ac_id = CUtil::uint($ac_id);
        if (empty($ac_id)) {
            return [];
        }
        $redis     = by::redis('core');
        $redis_key = $this->__getCouponListByAcId($ac_id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $amData = by::aM()->getListByAid($ac_id);
            $aData  = [];
            if ($amData) {
                foreach ($amData as $key => $am) {
                    $coupon          = by::marketConfig()->couponCondition($am['mc_id']);
                    if(empty($coupon)) continue;
                    $coupon['stock'] = $am['stock'] ?? 0;
                    $coupon['ac_id'] = $am['ac_id'] ?? 0;
                    $coupon['sales'] = $am['sales'] ?? 0;
                    $coupon['name']  = $am['name'] ?? '';
                    $coupon['level'] = $am['level'] ?? '';
                    $aData[]         = $coupon;
                }
            }

            $redis->set($redis_key, json_encode($aData), empty($aData) ? 10 : self::EXP);
        }

        return $aData ?? [];
    }

    /**
     * @param $ac_id
     * @return array|int|DataReader
     * @throws Exception
     * 通过活动id获取优惠卷库存列表
     */
    public function getCouponStockByAcId($ac_id)
    {
        $ac_id = CUtil::uint($ac_id);
        if (empty($ac_id)) {
            return 0;
        }
        $tb = self::tbName();
        $fields = implode("`,`", $this->tb_fields);
        $sql = "SELECT `{$fields}` FROM {$tb} WHERE `ac_id`=:ac_id ";
        $couponList = by::dbMaster()->createCommand($sql, [':ac_id' => $ac_id])->queryAll();
        $mc_tb = by::marketConfig()::getTable();
        foreach ($couponList as &$value) {
            $mc_sql = "SELECT `name` FROM {$mc_tb} WHERE `id` =:mc_id LIMIT 1";
            $nameArray = by::dbMaster()->createCommand($mc_sql, [':mc_id' => $value['mc_id']])->queryOne();
            $name = $nameArray['name'];
            unset($value['ac_id'], $value['mc_id'], $value['last_user'], $value['ctime']);
            $value['name'] = $name;
            $value['all_stock'] = $value['stock'] + $value['sales'];
        }
        return $couponList ?? [];
    }


    /**
     * @param $ac_id
     * @return void
     * @throws RedisException
     * 清除优惠卷列表
     */
    public function __delCouponList($ac_id)
    {
        $redis = by::redis('core');
        $redis_key = $this->__getCouponListByAcId($ac_id);
        $redis->del($redis_key);
    }
}
