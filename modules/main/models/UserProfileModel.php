<?php
/**
 * Created by PhpStorm.
 * User: wind
 * Date: 2021/7/16
 * Time: 16:07
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

class UserProfileModel extends CommModel
{

	const  USER_PHONE    = 10;             //手机号码
	const  USER_NAME     = 20;             //用户姓名
	const  USER_PROVINCE = 30;             //用户所在省
	const  USER_CITY     = 40;             //用户所在市
	const  USER_AREA     = 50;             //用户所在区
	const USER_BIRTHDAY  = 60;             //用户生日

	protected $expire_time = 86400;     //缓存时间
	/**
	 * 允许字段列表(最多200个)
	 * @var array
	 */
	const ALLOW_FIELDS = [
		10,
		20,
		30,
		40,
		50,
		60,
	];
	const VOUCHER_SET_EXPIRE = 3;


	/**
	 * @param $user_id
	 * @return string
	 * 分表user tb
	 */
	public static function getTable($user_id): string
	{
		$user_id = CUtil::uint($user_id);
		$mod     = $user_id % 10;
		return "`db_dreame`.`t_userprofile_{$mod}`";
	}



	/**
	 * @param $user_id
	 * @param $k
	 * @return string
	 * @throws \yii\db\Exception
	 */
	public function getValue($user_id, $k)
	{
		$data = $this->getList($user_id, $k);
		return empty($data['v'])? '' :strval($data['v']);
	}



	private function __getExtend($user_id){
		$user_id = CUtil::uint($user_id);
		$key     = AppCRedisKeys::getUserExtendKey($user_id);
		$redis   = by::redis();
		$aJson   = $redis->get($key);
		$aData   = (array)json_decode($aJson, true);
		if ($aJson === false) {
			$db        = by::dbMaster();
			$tb        = self::getTable($user_id);
			$start_num = self::USER_PHONE;
			$end_num   = self::USER_BIRTHDAY;
			$sql       = "SELECT v,k,t FROM {$tb} where `user_id`=:user_id and `k` between {$start_num} and {$end_num}";
			$info      = $db->createCommand($sql, [':user_id' => $user_id])->queryAll();
			$aData     = empty($info) ? [] : $info;
			$redis->set($key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
		}
		if(empty($aData)){
			return $aData;
		}
		$data = [];
		foreach ($aData as $item){
			$k = $item['k']??0;
			$data[$k] = $item;
		}
		return $data;
	}

	/**
	 * 获取用户基础扩展属性
	 * @param $user_id
	 */
	public function getUserExtend($user_id){
		$info = $this->__getExtend($user_id);
		$data['user_phone']    = $info[self::USER_PHONE]['v'] ?? '';
		$data['user_name']     = $info[self::USER_NAME]['v'] ?? '';
		$data['user_province'] = $info[self::USER_PROVINCE]['v'] ?? '';
		$data['user_city']     = $info[self::USER_CITY]['v'] ?? '';
		$data['user_area']     = $info[self::USER_AREA]['v'] ?? '';
		$data['user_birthday'] = $info[self::USER_BIRTHDAY]['v'] ?? '';
		return $data;
	}


	/**
	 * 保存用户扩展属性
	 * @param $user_id
	 * @param string $user_name
	 * @param string $user_province
	 * @param string $user_city
	 * @param string $user_area
	 * @param string $user_birthday
	 * @return array
	 * @throws \yii\db\Exception
	 */
	public function saveUserExtend($user_id,$user_name='',$user_province='',$user_city='',$user_area='',$user_birthday=''){
		$user_id = CUtil::uint($user_id);
		if(empty($user_id)){
			return [false,"用户ID不能为空"];
		}

		if(!empty($user_name)){
			list($status,$msg)= $this->saveVal($user_id,self::USER_NAME,$user_name);
			if(empty($status)){
				return [false,"更新用户名称失败"];
			}
		}

		if(!empty($user_province) && !empty($user_city) && !empty($user_area)){
			list($status, $msg)   = $this->saveVal($user_id, self::USER_PROVINCE,$user_province);
			list($status_1, $msg) = $this->saveVal($user_id,self::USER_CITY, $user_city);
			list($status_2, $msg) = $this->saveVal($user_id, self::USER_AREA,$user_city);
			if(empty($status) || empty($status_1) || empty($status_2)){
				return [false,"更新用户区域失败"];
			}
		}


		if(!empty($user_birthday)){
			$val = $this->getValue($user_id,self::USER_BIRTHDAY);
			if(!empty($val)){
				return [false,'只能修改一次用户生日'];
			}
			list($status,$msg) =  $this->saveVal($user_id,self::USER_BIRTHDAY,$user_birthday);
			if(empty($status)){
				return [false,"更新用户生日失败"];
			}
		}

		$this->delExtendCache($user_id);
		return [true,'更新成功'];
	}


	public function delExtendCache($user_id){
		$key     = AppCRedisKeys::getUserExtendKey($user_id);
		return by::redis()->del($key);
	}


	/**
	 * 获取详情列表
	 * @param $user_id
	 * @param $k
	 * @return array|false
	 * @throws \yii\db\Exception
	 */
	public function getList($user_id, $k){

		$user_id = CUtil::uint($user_id);
		$k       = CUtil::uint($k);
		$key     = AppCRedisKeys::getUserProfileKey($user_id, $k);
		$redis   = by::redis();
		$aJson   = $redis->get($key);
		$aData   = (array)json_decode($aJson, true);
		if ($aJson === false) {
			$db   = by::dbMaster();
			$tb   = self::getTable($user_id);
			$sql  = "SELECT v,t FROM {$tb} where `user_id`=:user_id and `k`=:k";
			$info = $db->createCommand($sql, [':user_id' => $user_id, ':k' => $k])->queryOne();
			$aData  = empty($info) ? [] : $info;
			$redis->set($key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
		}
		return $aData;
	}

	/**
	 *
	 * @param $user_id
	 * @param $k
	 * @param $val
	 * @return array
	 */
	public function saveVal($user_id, $k, $val)
	{
		$val     = addslashes($val);
		$k       = CUtil::uint($k);
		$user_id = CUtil::uint($user_id);
		if (!in_array($k, self::ALLOW_FIELDS)) {
			return [false, '字段不匹配'];
		}
		$db             = by::dbMaster();
		$tb             = self::getTable($user_id);
		$concurrent_Key = AppCRedisKeys::userProfileConcurrentKey($user_id, $k);
		if (!CommModel::lock($concurrent_Key, self::VOUCHER_SET_EXPIRE, 0)) {
			$msg = "updateValue fail|user_id:{$user_id}|k:{$k}|val:{$val}|";
			CUtil::debug($msg,"userprofile_err");
			return [false, '数据冲突'];
		}
		$update = [
			't' => time(),
			'v' => $val,
		];
		try {
			$affected          = $db->createCommand()->update($tb, $update, ['user_id' => $user_id, 'k' => $k])->execute();
			$update['k']       = $k;
			$update['user_id'] = $user_id;
			if (!$affected) {
				$db->createCommand()->insert($tb, $update)->execute();
				unset($update['user_id']);
			}
			$key = AppCRedisKeys::getUserProfileKey($user_id, $k);
			$data =[
				't' => $update['t'],
				'v' => $update['v'],
			];
			by::redis('core')->hMSet($key, $data);
			//设置生存时间
			CUtil::ResetExpire($key, $this->expire_time);
			CommModel::replease($concurrent_Key);//释放key
			return [true, $update];
		} catch (\exception $e) {
			CommModel::replease($concurrent_Key);//释放key
			CUtil::debug($e->getMessage(),"userprofile_err");
			return [false, '更新数据失败'];
		}
	}


}