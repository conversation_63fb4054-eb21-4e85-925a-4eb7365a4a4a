<?php

namespace app\modules\main\models;


use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class UserTryPathModel extends CommModel
{
    public static function tableName(): string
    {
        return '`db_dreame`.`t_users_path`';
    }

    public $fields = [
        'id', 'uid', 'user_id', 'ac_id', 'phone', 'nick_name', 'avatar', 'source', 'is_auth', 'is_sign_agreement', 'is_enter_zfb', 'is_enter_survey'
        , 'is_submit_survey', 'is_pass_survey', 'is_send', 'ctime', 'utime'
    ];

    const SCENE = [
        'auth'           => 'is_auth',
        'sign_agreement' => 'is_sign_agreement',
        'enter_zfb'      => 'is_enter_zfb',
        'enter_survey'   => 'is_enter_survey',
        'submit_survey'  => 'is_submit_survey',
        'pass_survey'    => 'is_pass_survey',
        'send'           => 'is_send',
        'manual_audit'   => 'is_manual_audit'
    ];

    public function saveUserPath($data): array
    {
        // 检查用户是否已存在
        $exists = self::find()->where(['user_id' => $data['user_id'], 'ac_id' => $data['ac_id']])->exists();
        if ($exists) {
            return [true, 'ok'];
        }
        $result = by::dbMaster()->createCommand()->insert(self::tableName(), $data)->execute();
        if (!$result) {
            return [false, '先试后买用户链路保存失败'];
        }
        return [true, 'ok'];
    }

    /**
     * @param $userId
     * @param $acId
     * @param $update
     * @return array
     * @throws Exception
     * 更新用户链路
     */
    public function updateUserPath($userId, $acId, $update): array
    {
        // 参数校验
        if (empty($userId) || empty($acId)) {
            return [false, '参数错误'];
        }

        // 允许更新的字段
        $updateField = ['is_sign_agreement', 'is_enter_zfb', 'is_enter_survey', 'is_submit_survey', 'is_pass_survey', 'is_manual_audit', 'is_send', 'utime'];

        // 过滤非法字段
        $update = array_intersect_key($update, array_flip($updateField));

        // 检查是否有字段需要更新
        if (empty($update)) {
            return [false, '无字段更改，用户ID：'.$userId.'|活动ID：'.$acId];
        }

        // 检查是否存在要更新的记录
        $aLog = self::find()->where(['user_id' => $userId, 'ac_id' => $acId])->one();
        if (empty($aLog)) {
            return [false, '记录不存在，用户ID：'.$userId.'|活动ID：'.$acId];
        }

        // 更新记录
        $tb           = self::tableName();

        $rowsAffected = by::dbMaster()->createCommand()->update($tb, $update, ['user_id' => $userId, 'ac_id' => $acId])->execute();

        // 返回结果
        return [true, "更新成功"];
    }



    /**
     * @param $where
     * @return array
     * 获取链路详情
     */
    public function getUserPathInfo($where): array
    {
        $record = self::find()->where($where)->one();
        return $record ? $record->toArray() : [];
    }


    /**
     * @param array $input
     * @param $page
     * @param $pageSize
     * @return array
     * 用户链路列表
     */
    public function getUserPathList(array $input, $page, $pageSize): array
    {
        $limit = CUtil::pagination($page, $pageSize);
        $query = $this->__getCondition($input);

        $list = $query->orderBy('id DESC')
            ->offset($limit[0])
            ->limit($limit[1])
            ->asArray();
        $list = $list->all();
        //是否需要数量
        $need_count = $input['need_count'] ?? true;
        if ($need_count) {
            $total = (clone $query)->count();
            $pages = CUtil::getPaginationPages($total, $pageSize);
        }

        return [intval($total ?? 0), $pages ?? 1, array_filter($list)];
    }

    private function __getCondition(array $input): \yii\db\ActiveQuery
    {
        $query = self::find();

        if (isset($input['ac_id']) && $input['ac_id']) {
            $query->andWhere(['ac_id' => $input['ac_id']]);
        }

        if (isset($input['uid']) && $input['uid']) {
            $query->andWhere(['uid' => $input['uid']]);
        }

        if (isset($input['phone']) && $input['phone']) {
            $query->andWhere(['phone' => $input['phone']]);
        }

        if (isset($input['nick_name']) && $input['nick_name']) {
            $query->andWhere(['like', 'nick_name', $input['nick_name']]);
        }

        return $query;
    }


    public function exportData($uid = '', $phone = '', $ac_id = '', $nick_name = '', $viewSensitive = false): array
    {
        // 使用 compact 函数初始化输入参数
        $input = compact('uid', 'phone', 'ac_id', 'nick_name');

        // 获取查询条件
        $query = $this->__getCondition($input);

        // 获取结果集并按ID倒序排序
        $list = $query->orderBy('id DESC')->asArray()->all();

        // 表头
        $head = [
            'ID', 'UID', '用户ID', '活动ID', '手机号', '昵称', '头像', '来源',
            '是否微信授权', '是否签署协议', '是否进入支付宝', '是否进入问卷',
            '是否提交问卷', '是否通过问卷','是否通过人工审核', '是否发货', '创建时间', '更新时间'
        ];
        $data = [$head];

        // 辅助函数，用于转换布尔值为'是'或'否'
        $boolToYesNo = function ($value) {
            return $value == 1 ? '是' : '否';
        };

        // 遍历结果集，构建数据行
        foreach ($list as $item) {
            $data[] = [
                $item['id'],
                $item['uid'],
                $item['user_id'],
                $item['ac_id'],
                $item['phone'],
                $item['nick_name'],
                $item['avatar'],
                $item['source'],
                $boolToYesNo($item['is_auth']),
                $boolToYesNo($item['is_sign_agreement']),
                $boolToYesNo($item['is_enter_zfb']),
                $boolToYesNo($item['is_enter_survey']),
                $boolToYesNo($item['is_submit_survey']),
                $boolToYesNo($item['is_pass_survey']),
                $boolToYesNo($item['is_manual_audit']),
                $boolToYesNo($item['is_send']),
                date('Y-m-d H:i:s', $item['ctime']),
                date('Y-m-d H:i:s', $item['utime']),
            ];
        }

        return $data;
    }


}
