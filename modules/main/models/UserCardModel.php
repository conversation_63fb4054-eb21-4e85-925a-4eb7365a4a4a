<?php


namespace app\modules\main\models;


use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use yii\db\DataReader;
use yii\db\Exception;
use yii\helpers\Json;


class UserCardModel extends CommModel
{

    protected $expire_time = 600;

    const STATUS = [
            'get'      => 0,     //未使用
            'use'      => 1,     //锁定或已使用
            'overtime' => 2,     //卡券过期
    ];

    const TYPE = [
            'all'     => -1,   //所有
            'coupon'  => 1,    //商品优惠券
            'voucher' => 6,    //商品兑换券
            'consume' => 10,   //消费券
    ];

    const GET_CHANNEL = [
            'sign'              => 1,   //签到
            'activity'          => 2,   //活动礼包
            'product'           => 3,   //产品绑定
            'manual_setting'    => 4,   //后台手动设置
            'internal_purchase' => 5,   //内购领券
            'draw_activity'     => 6,   //抽奖活动
            'auto_coupon'       => 7,   //自动发放优惠券
    ];

    const USE_CHANNEL = [
            'shopping' => 1,     //购买商品
    ];

    const GET_CARD_CHANNEL = [
            'my_card'      => 1,     //我的优惠劵
            'can_use_card' => 2,     //可使用优惠劵
    ];

    const SOURCE = [
            'INDEX' => 1, //小程序
            'ADMIN' => 2  //后台
    ];

    public $tb_fields = [
            'id',
            'user_id',
            'market_id',
            'type',
            'get_channel',
            'get_relation',
            'use_channel',
            'use_relation',
            'status',
            'create_time',
            'use_time',
            'start_time',
            'expire_time'
    ];

    public static function tbName($user_id)
    {
        $mod = $user_id % 100;
        return "`db_dreame`.`t_user_card_{$mod}`";
    }

    //删除缓存
    public function __delCache($user_id)
    {
        $r_key = [
                AppCRedisKeys::userCardList($user_id),
                AppCRedisKeys::GetPropsCard($user_id),
        ];
        by::redis()->del(...$r_key);
    }

    private function __batchSendCard($back_user_id)
    {
        return AppCRedisKeys::batchSendCard($back_user_id);
    }

    private function __singleSendCard($back_user_id)
    {
        return AppCRedisKeys::singleSendCard($back_user_id);
    }

    private function __batchDelCard($back_user_id)
    {
        return AppCRedisKeys::batchDelCard($back_user_id);
    }

    private function __singleDelCard($back_user_id)
    {
        return AppCRedisKeys::singleDelCard($back_user_id);
    }


    /**
     * @param $user_id
     * @param $market_id
     * @param $get_channel
     * @param $relation_id
     * 删除$relation_id是否已领过
     */
    private function __delCheckCardCache($user_id, $market_id, $get_channel, $relation_id)
    {
        $r_key1 = AppCRedisKeys::checkCard($user_id, $market_id, $get_channel, $relation_id);
        $r_key2 = AppCRedisKeys::checkCard($user_id, 0, $get_channel, $relation_id);
        by::redis()->del($r_key1, $r_key2);
    }

    /**
     * @param $market_data
     * @param $data
     * @return mixed
     * @throws Exception
     * 优惠券使用条件使用范围转换
     */
    public function __conditionToName($market_data, &$data)
    {
        $marketConfigModel = by::marketConfig();

        //使用条件展示
        $data['condition'] = '无门槛';
        $data['web_name']  = $market_data['web_name'] ?? '';
        if (isset($market_data['c_amount_type']) && $market_data['c_amount_type'] == $marketConfigModel::C_AMOUNT_TYPE['lowest']) {
            //使用条件后配置值，满100可用就是100
            $data['condition'] = '满' . floatval($market_data['c_amount_val']) . '元可用';
        }

        // 折扣券
        if (in_array($market_data['type'], [$marketConfigModel::TYPE['coupon'], $marketConfigModel::TYPE['consume']]) && $market_data['resource_type'] == $marketConfigModel::RESOURCE_TYPE['discount']) {
            //使用条件后配置值，满N件可用
            $meet_type = $market_data['c_meet_type'] ?? $marketConfigModel::C_MEET_TYPE['min'];
            $meet_num  = $market_data['c_meet_num'] ?? 1;

            if ($meet_type == $marketConfigModel::C_MEET_TYPE['min']) {
                $data['condition'] = '满' . $meet_num . '件可用';
            } else {
                $data['condition'] = $meet_num . '件内可用';
            }
        }

        $data['condition_scope'] = '全场通用';
        if ($market_data['c_tag_val'] && $market_data['c_tag_type'] != $marketConfigModel::C_TAG_TYPE['all']) {
            $c_tag_val  = explode(',', $market_data['c_tag_val']);
            $c_tag_val  = array_slice($c_tag_val, 0, 2);
            $c_tag_name = '';
            $tagMap = by::Gtag()->GetTagNameMap();
            $tag_name   = $tagMap;

            foreach ($c_tag_val as $tag) {
                $c_tag_name .= ($tag_name[$tag] ?? $tag) . ',';
            }
            $c_tag_name = trim($c_tag_name, ',');

            $tag_count = count($c_tag_val);
            if (($data['type'] == self::TYPE['coupon'] || $data['type'] == self::TYPE['voucher']) && $tag_count >= 2) {
                $c_tag_name = "部分标签";
            }

            $c_tag_type              = $market_data['c_tag_type'] == $marketConfigModel::C_TAG_TYPE['apply'] ? '可用' : '不可用';
            $data['condition_scope'] = "仅{$c_tag_name}{$c_tag_type}";
        } else {
            if ($market_data['c_goods_val']) {
                $c_goods_val  = explode(',', $market_data['c_goods_val']);
                $c_goods_name = '';
                $g_num        = 0;
                $c_g          = [];

                foreach ($c_goods_val as $gid) {
                    $goods_info = by::Gmain()->GetOneByGid($gid);
                    if (empty($goods_info)) {
                        continue;
                    }

                    $g_num += 1;
                    $g_num <= 2 && $c_goods_name .= '、' . $goods_info['name'];

                    $c_g[] = [
                            'gid'    => $gid,
                            'c_type' => $goods_info['type']
                    ];

                    if ($g_num >= 3) {
                        $c_goods_name .= '等';
                        break;
                    }
                }

                if (!empty($c_goods_name)) {
                    $c_goods_name = ltrim($c_goods_name, '、');

                    $goods_count = count($c_goods_val);
                    if (($data['type'] == self::TYPE['coupon'] || $data['type'] == self::TYPE['voucher']) && $goods_count >= 2) {
                        $c_goods_name = "部分商品";
                    }

                    $c_goods_type            = $market_data['c_goods_type'] == $marketConfigModel::C_GOODS_TYPE['apply'] ? '可用' : '不可用';
                    $data['condition_scope'] = "仅{$c_goods_name}{$c_goods_type}";

                    if (count($c_g) == 1 && $market_data['c_goods_type'] == $marketConfigModel::C_GOODS_TYPE['apply']) {
                        $data['jump_conf'] = [
                                'gid'    => $c_g[0]['gid'],
                                'c_type' => $c_g[0]['c_type'],
                        ];
                    }
                }
            }
        }

        return $data;
    }

    /**
     * @param $user_id
     * @param $source
     * @return array
     * @throws Exception
     */
    private function __getlist($user_id, $source, $arr = []): array
    {
        //优惠券展示目前tab类下拥有的优惠券
        $expire_day = $source == by::userCard()::SOURCE['INDEX'] ? 30 : 0;
        $getChannel = $arr['get_channel'] ?? 0;
        $tab_type   = CUtil::uint($arr['tab_type'] ?? 0);//1未使用 2 已使用 3 已过期

        $coupon_list = $this->__getEffList($user_id, $source, -1, 'DESC', $expire_day, self::GET_CARD_CHANNEL['my_card'], $getChannel);
        $data        = $this->getMarketData($coupon_list, $source, $tab_type);
        return [true, $data];
    }

    /**
     * @param $coupon_list
     * @param $source
     * @return array
     * @throws Exception
     */
    public function getMarketData($coupon_list, $source, $tab_type = 0): array
    {
        $data = [];
        $now  = time();
        foreach ($coupon_list as $key => $val) {
            $market_data = by::marketConfig()->getOneById($val['market_id']);
            if (!$market_data) {
                unset($coupon_list[$key]);
                continue;
            }
            $val['images']        = $market_data['images'] ?? '';
            $val['name']          = $market_data['name'] ?? '';
            $val['web_name']      = $market_data['web_name'] ?? '';
            $val['num']           = 1;
            $val['jump_goods_id'] = $market_data['jump_goods_id'] ?? '';
            $val['use_rule_note'] = $market_data['use_rule_note'] ?? '';


            switch ($val['type']) {
                case self::TYPE['coupon']:
                case self::TYPE['consume']:
                    //资源内容类型(1:折扣优惠N%;2:固定金额N元;3:免运费)
                    $val['resource_type'] = $market_data['resource_type'] ?? 0;
                    $val['discount']      = $market_data['discount'] ?? 0;

                    //折扣优惠、固定金额配置值，若配置的是折扣优惠券并且discount = 18.66，则为1.866折
                    $type_name = $val['type'] == self::TYPE['coupon'] ? '优惠券' : '消费券';
                    list($val['discount'], $val['type_name']) = by::marketConfig()->resourceType($val['resource_type'], $val['discount'], $type_name);

                    break;
                case self::TYPE['voucher']:
                    $val['type_name'] = '兑换券';

                    break;
                default:
                    $val['type_name'] = '卡券';

                    break;
            }
            /*if ($val['start_time'] > $now){
                $val['status'] = '3';
            }*/

            if ($val['expire_time'] < $now) {
                switch ($source) {
                    case by::userCard()::SOURCE['INDEX']:
                        $val['status'] = (isset($val['status']) && $val['status'] == 1) ? $val['status'] : 2;
                        break;
                    case by::userCard()::SOURCE['ADMIN']:
                        $val['status'] = (isset($val['status']) && $val['status'] == 1) ? $val['status'] : 2;
                        if ($val['expire_time'] == 0) {
                            $market_config = by::marketConfig();
                            $mc_info       = $market_config->getOneById($val['market_id']);
                            list($start_time, $expire_time) = $market_config->expireTime($mc_info['valid_type'] ?? 0, $mc_info['valid_val'] ?? 0);

                            $val['status']      = 3;
                            $val['expire_time'] = $expire_time ?? 0;
                        }
                        break;
                    default:
                        break;
                }
            }


            //使用条件展示
            $val    = $this->__conditionToName($market_data, $val);
            $data[] = $val;
        }

        if (count($data) >= 1) {
            $data = $this->__getCardDataByTab($data, $tab_type);
        }

        $data = $this->modifyCardInfoByChannel($data);
        return $data;
    }

    private function __getCardDataByTab($data, $tab_type = '')
    {

        switch ($tab_type) {
            case 1:
                $status = 0;
                break;//未使用
            case 2:
                $status = 1;
                break;//已使用
            case 3:
                $status = 2;
                break;//已过期
            default:
                $status = -1;
                break;
        }

        if ($status == 0) {
            $data = $this->_getCardListByTab($data, $status);
            if (count($data) > 1) {
                $end    = array_column($data, 'expire_time');
                $create = array_column($data, 'create_time');
                array_multisort($end, SORT_ASC, $create, SORT_DESC, $data);
            }
        } elseif ($status == 1) {
            $data = $this->_getCardListByTab($data, $status);
            if (count($data) > 1) {
                $use    = array_column($data, 'use_time');
                $end    = array_column($data, 'expire_time');
                $create = array_column($data, 'create_time');
                array_multisort($use, SORT_DESC, $end, SORT_ASC, $create, SORT_DESC, $data);
            }
        } elseif ($status == 2) {
            $data = $this->_getCardListByTab($data, $status);
            if (count($data) > 1) {
                $end    = array_column($data, 'expire_time');
                $create = array_column($data, 'create_time');
                array_multisort($end, SORT_DESC, $create, SORT_DESC, $data);
            }
        } else {
            if (count($data) > 1) {
                $status = array_column($data, 'status');
                array_multisort($status, SORT_ASC, $data);
            }
            return $data;
        }

        return $data;
    }

    /**
     * @param $data
     * @param $status
     * @return array
     * 根据tab过滤优惠券状态
     */
    private function _getCardListByTab($data, $status)
    {
        $realData = [];
        foreach ($data as $value) {
            if (CUtil::uint($value['status']) === $status) {
                $realData[] = $value;
            }
        }
        return array_values($realData);
    }

    /**
     * 优惠券外显操作
     * @param $data
     * @return array
     * @throws Exception
     */
    public function modifyCardInfoByChannel($data)
    {
        foreach ($data as $key => $item) {
            $getChannel = $item['get_channel'] ?? '';
            $marketId   = $item['market_id'] ?? 0;
            if ($getChannel == self::GET_CHANNEL['internal_purchase'] && $marketId) {//导购标签
                $defineInfo = by::marketDefine()->GetOneByMarketId($marketId, 1);
                if ($defineInfo) {
                    $data[$key]['name']          = empty($defineInfo['name'] ?? '') ? $item['name'] : $defineInfo['name'];
                    $data[$key]['use_rule_note'] = empty($defineInfo['use_rule_note'] ?? '') ? $item['use_rule_note'] : $defineInfo['use_rule_note'];
                }
            }
        }

        return $data;
    }


    /**
     * @param $user_id
     * @param $source
     * @return array
     * @throws Exception
     * 获取用户的卡券列表
     */
    public function getList($user_id, $source, $arr = []): array
    {
        list($status, $list) = $this->__getlist($user_id, $source, $arr);

        return [true, ['total' => count($list), 'list' => $list]];
    }


    /**
     * @param $user_id
     * @param $id
     * @return array
     * @throws Exception
     * 根据用户的user_id和card_id获取卡券
     */
    public function getCardById($user_id, $id, $getChannel = 0)
    {
        $redis   = by::redis();
        $r_key   = AppCRedisKeys::userCardList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $id, $getChannel);
        $aJson   = $redis->hGet($r_key, $sub_key);
        $aData   = (array) json_decode($aJson, true);

        if ($aJson === false) {
            $tb     = self::tbName($user_id);
            $fields = implode("`,`", $this->tb_fields);

            $where  = " `id` = :id AND `user_id` = :user_id";
            $params = [
                    ':id'      => $id,
                    ':user_id' => $user_id,
            ];

            if ($getChannel > 0) {
                $where                  .= " AND `get_channel` = :get_channel";
                $params[':get_channel'] = $getChannel;
            }

            $sql = "SELECT `{$fields}` FROM {$tb} WHERE {$where} LIMIT 1";

            $aData = by::dbMaster()->createCommand($sql, $params)->queryOne();

            $aData = $aData ?: [];

            $redis->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, $this->expire_time);
        }

        return $aData;
    }

    /**
     * 根据用户id，获取用户领取的活动优惠卡券集合
     * @param $user_id
     * @return array|mixed|DataReader
     * @throws Exception
     * @throws \RedisException
     */
    public function getCardListByUserId($user_id, $get_channel = 0, $get_relation = 0)
    {
        $redis   = by::redis();
        $r_key   = AppCRedisKeys::userCardList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $user_id, $get_channel, $get_relation);

        $cachedData = $redis->hGet($r_key, $sub_key);
        if ($cachedData !== false) {
            return json_decode($cachedData, true);
        }

        $where  = "`user_id` = :user_id";
        $params = [':user_id' => $user_id];
        if ($get_channel > 0) {
            $where                  .= " AND `get_channel` = :get_channel";
            $params[':get_channel'] = $get_channel;
        }
        if ($get_relation > 0) {
            $where                   .= " AND `get_relation` = :get_relation";
            $params[':get_relation'] = $get_relation;
        }

        $tb     = self::tbName($user_id);
        $fields = implode("`,`", $this->tb_fields);
        $sql    = "SELECT `{$fields}` FROM {$tb} WHERE {$where}";
        $items  = by::dbMaster()->createCommand($sql, $params)->queryAll();

        $data = $items ?: [];

        $redis->hSet($r_key, $sub_key, json_encode($data));
        CUtil::ResetExpire($r_key, $this->expire_time);

        return $data;
    }

    /**
     * @param $user_id :用户ID
     * @param $market_id :资源id
     * @param $get_channel :获得方式 1 签到 2 新人礼包 3 等级礼包 4 后台手动设置 5 内购领券 6 抽奖活动 7 自动发放
     * @param int $relation_id :关联id
     * @param int $num :发放数量，后台用
     * @param int $r_id 邀请人id
     * @param int $ac_id
     * @return array
     * @throws Exception 获得卡券
     */
    public function setCard($user_id, $market_id, $get_channel, $relation_id = 0, $num = 1, $r_id = 0, $ac_id = 0): array
    {
        $user_info = by::users()->getOneByUid($user_id);
        if (!$user_info) {
            return [false, '用户不存在'];
        }

        $market_data = by::marketConfig()->getOneById($market_id);
        if (!$market_data) {
            return [false, '没找到相应资源'];
        }

        $key = array_search($market_data['type'], by::marketConfig()::TYPE);
        if (!isset(self::TYPE[$key])) {
            return [false, '资源有误'];
        }

        //提示卡和排除卡加了过期时间
        $time = time();
        if ($get_channel != self::GET_CHANNEL['manual_setting']) {
            $num = $market_data['resource_num'] ?? 1;
        }

        list($start_time, $expire_time) = by::marketConfig()->expireTime($market_data['valid_type'], $market_data['valid_val']);

        $table = self::tbName($user_id);
        $data  = [
                'user_id'      => $user_id,
                'r_id'         => $r_id,
                'type'         => self::TYPE[$key],
                'market_id'    => $market_id,
                'get_channel'  => $get_channel,
                'get_relation' => $relation_id,
                'create_time'  => $time,
                'start_time'   => $start_time ?? 0,
                'expire_time'  => $expire_time ?? 3999999999,
                'ac_id'        => $ac_id
        ];
        for ($i = 0; $i < $num; $i++) {
            by::dbMaster()->createCommand()->insert($table, $data)->execute();
        }

        list($s) = by::marketConfig()->setNumModify($market_id, $num);
        if (!$s) {
            return [false, '修改领取数量失败'];
        }
        //删除缓存
        $this->__delCache($user_id);
        $this->__delCheckCardCache($user_id, $market_id, $get_channel, $relation_id);

        return [true, '获取卡券成功'];
    }

    /**
     * @param $user_id
     * @param $market_id
     * @param $get_channel
     * @param $relation_id
     * @param $num
     * @return array
     * @throws Exception
     * 废除卡卷
     */
    public function batchDelCard($user_id, $market_id, $get_channel, $relation_id = 0, $num = 1): array
    {
        $user_info = by::users()->getOneByUid($user_id);
        if (!$user_info) {
            return [false, '用户不存在'];
        }

        $market_data = by::marketConfig()->getOneById($market_id);
        if (!$market_data) {
            return [false, '没找到相应资源'];
        }

        $key = array_search($market_data['type'], by::marketConfig()::TYPE);
        if (!isset(self::TYPE[$key])) {
            return [false, '资源有误'];
        }

        //提示卡和排除卡加了过期时间
        if ($get_channel != self::GET_CHANNEL['manual_setting']) {
            $num = $market_data['resource_num'] ?? 1;
        }

        $table = self::tbName($user_id);

        for ($i = 0; $i < $num; $i++) {
            by::dbMaster()->createCommand()->update(
                    $table,
                    ['expire_time' => 0],
                    [
                            'market_id' => $market_id,
                            'user_id'   => $user_id,
                            'status'    => self::STATUS['get']
                    ]
            )->execute();
        }

        //删除缓存
        $this->__delCache($user_id);
        $this->__delCheckCardCache($user_id, $market_id, $get_channel, $relation_id);

        return [true, '废除卡券成功'];
    }

    /**
     * @param $user_id
     * @param $market_id
     * @param $num
     * @return array
     * @throws Exception
     * 后台发放
     */
    public function backSend($user_id, $market_id, $num, $relation_id = 0): array
    {
        $user_id   = CUtil::uint($user_id);
        $market_id = CUtil::uint($market_id);
        $num       = CUtil::uint($num);
        if (empty($user_id) || empty($market_id)) {
            return [false, '缺少必要参数'];
        }

        $market_data = by::marketConfig()->getOneById($market_id);
        if (!$market_data) {
            return [false, '没找到相应资源'];
        }

        if ($market_data['stock_num'] < $num) {
            return [false, '该优惠券库存不足'];
        }
        $tran = by::dbMaster()->beginTransaction();
        try {
            //发放
            list($status, $ret) = by::userCard()->setCard(
                    $user_id,
                    $market_id,
                    UserCardModel::GET_CHANNEL['manual_setting'],
                    $relation_id,
                    $num
            );

            if (!$status) {
                throw new \Exception($ret);
            }

            //减少库存
            list($status, $ret) = by::marketConfig()->stockModify($market_id, $num);
            if (!$status) {
                throw new \Exception($ret);
            }

            $tran->commit();

            return [true, $ret];
        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug($error, 'back_send_error');

            $tran->rollBack();
            return [true, $_e->getMessage()];
        }

    }

    /**
     * @param $user_id
     * @param $market_id
     * @param $num
     * @return array
     * @throws Exception
     */
    public function backDel($user_id, $market_id, $num): array
    {
        $user_id   = CUtil::uint($user_id);
        $market_id = CUtil::uint($market_id);
        $num       = CUtil::uint($num);
        if (empty($user_id) || empty($market_id)) {
            return [false, '缺少必要参数'];
        }

        $market_data = by::marketConfig()->getOneById($market_id);
        if (!$market_data) {
            return [false, '没找到相应资源'];
        }

        $tran = by::dbMaster()->beginTransaction();

        try {
            //废除
            list($status, $ret) = by::userCard()->batchDelCard(
                    $user_id,
                    $market_id,
                    UserCardModel::GET_CHANNEL['manual_setting'],
                    0,
                    $num
            );

            if (!$status) {
                throw new \Exception($ret);
            }

            $tran->commit();

            return [true, $ret];
        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug($error, 'back_del_error');

            $tran->rollBack();
            return [true, $_e->getMessage()];
        }
    }

    /**
     * @param $user_id
     * @param $market_id
     * @return false|int|string|DataReader|null
     * @throws Exception
     */
    public function getUserEffectiveCardNumsByMarketId($user_id, $market_id)
    {
        $user_id   = CUtil::uint($user_id);
        $market_id = CUtil::uint($market_id);

        if (empty($user_id) || empty($market_id)) {
            return 0;
        }

        $tb  = self::tbName($user_id);
        $sql = "SELECT COUNT(*) as `num`,`user_id` FROM  {$tb} WHERE `user_id`=:user_id AND `market_id`=:market_id AND `status` = :status AND `expire_time`>:expire_time";
        return by::dbMaster()->createCommand($sql, [':user_id' => $user_id, ':market_id' => $market_id, ':status' => 0, ':expire_time' => time()])->queryScalar();
    }


    /**
     * @throws Exception
     */
    public function batchSendUserCard($userNo, $file, $market_id, $backUserId = 0): array
    {
        //防止多重点击上传，单人限制3s
        $unique_key = CUtil::getAllParams(__FUNCTION__, $backUserId);
        list($anti) = self::ReqAntiConcurrency($backUserId, $unique_key, 3, 'EX');
        if (!$anti) {
            return [false, '3s内请勿重复点击！'];
        }

        //参数校验
        if (empty($userNo) && empty($file)) {
            return [false, '用户卡号或上传文件至少填写一条！'];
        }
        if (empty($market_id)) {
            return [false, '卡券不能为空！'];
        }

        //优惠券校验
        $market_data = by::marketConfig()->getOneById($market_id);

        if (!$market_data) {
            return [false, '没找到相应资源'];
        }
        $marketName = $market_data['name'] ?? '';

        //获取填入的用户卡号或者电话号码
        $userData = array_unique(array_filter(explode(',', $userNo)));
        if (count($userData) > 20) {
            return [false, '用户数据大于20条建议采用批量上传方式'];
        }


        //用户批量上传数据处理
        $data = [];
        if ($file) {
            $filename = $file['tmp_name'] ?? '';
            if (!$filename) {
                return [false, '请选择文件'];
            }

            $name = explode('.', $file['name']);
            if (array_pop($name) != 'csv') {
                return [false, '请上传csv类型文件'];
            }

            $handle = fopen($filename, 'r');
            list($s, $data) = $this->__anCsv($handle);
            if (!$s) {
                return [false, $data];
            }

            if (!count($data)) {
                return [false, '没有任何数据'];
            }

            //明显重复项校验
            $memberNos     = array_column($data, 'memberNo');
            $realMemberNos = array_filter($memberNos);
            if (count($realMemberNos) !== count(array_unique($realMemberNos))) {
                return [false, '表格存在明显相同数据行，请校验后重新上传！'];
            }
            if (count($realMemberNos) > 2000) {
                return [false, '文件数据超过2000行，为防止操作时间过长，请分批上传！'];
            }
        }
        $data          = empty($data) ? [] : array_column($data, NULL, 'memberNo');
        $excelDataKeys = array_keys($data);

        foreach ($userData as $v) {
            if (isset($data[$v])) {
                $data[$v]['num']      += 1;
                $data[$v]['limitNum'] += 1;
            } else {
                $data[$v] = [
                        'memberNo' => $v,
                        'num'      => 1,
                        'limitNum' => 1,
                ];
            }
        }


        //判断库存够不够
        $nums       = 0;
        $cardUsers  = [];
        $phoneUsers = [];
        $errorUsers = [];

        foreach ($data as $k => $item1) {
            //根据$k 区分手机号码和会员卡号
            if (strlen(CUtil::uint($k)) == 11) {
                $phoneUsers[] = $k;
            } elseif (strpos($k, 'Dreame') !== false) {
                $cardUsers[] = $k;
            } else {
                $errorUsers[] = $k;
            }
            $nums += intval($item1['num'] ?? 0);
        }

        if ($market_data['stock_num'] < $nums) {
            return [false, '该优惠券库存不足'];
        }
        //判断该优惠卷是否已过期
        $valid_val_arr = explode('~', $market_data['valid_val']) ?? '';
        $lastTime      = $valid_val_arr[1] ?? 0;
        if ($market_data['valid_type'] == 1 && $lastTime < date('Y-m-d')) {
            return [false, '该优惠券已过期'];
        }

        //用户重复项过滤
        //1.查找所有电话号码对应的user_id
        $allPhoneUsers = empty($phoneUsers) ? [] : by::users()->getWxUsersByPhones($phoneUsers);
        if ($allPhoneUsers) {
            $allPhoneUsers = array_column($allPhoneUsers, 'user_id', 'phone');
        }

        //2.查找所有会员卡号对应的user_id
        $allCardUsers = empty($cardUsers) ? [] : by::users()->getWxUsersByCards($cardUsers);
        if ($allCardUsers) {
            $allCardUsers = array_column($allCardUsers, 'user_id', 'card');
        }

        //获取两个数组相同的项
        $repeatUsers      = [];
        $revertPhoneUsers = [];
        $revertCardUsers  = [];
        if ($allPhoneUsers && $allCardUsers) {
            $sameUser         = array_intersect($allPhoneUsers, $allCardUsers);
            $revertCardUsers  = array_flip($allCardUsers);
            $revertPhoneUsers = array_flip($phoneUsers);
            foreach ($sameUser as $user) {
                $repeatUsers[] = $revertCardUsers[$user] ?? '';
            }
        }

        //组合现有数据 查出对应的已有优惠券数量
        $noDataUsers = [];
        foreach ($data as $k => $i) {
            $userId = empty($allPhoneUsers[$k] ?? '') ? ($allCardUsers[$k] ?? '') : ($allPhoneUsers[$k] ?? '');
            $count  = $this->getUserEffectiveCardNumsByMarketId($userId, $market_id);
            if (empty($userId))
                $noDataUsers[] = $k;
            $data[$k]['user_id'] = $userId;
            $data[$k]['count']   = $count;
        }

        $outData1 = [];
        $outData2 = [];
        //逐行发放优惠券
        foreach ($data as $key => $item2) {
            if (in_array($key, $errorUsers)) {
                $msg = '参数既不是卡号也不是手机号码，请检查！';
            } elseif (in_array($key, $repeatUsers)) {
                $msg = '用户卡号和电话号码:' . ($revertPhoneUsers[$item2['user_id'] ?? 0] ?? '') . '重复，请检查！';
            } elseif (in_array($key, $noDataUsers)) {
                $msg = '该用户不存在，请检查！';
            } elseif ((intval($item2['num']) + intval($item2['count'])) > intval($item2['limitNum'])) {
                $msg = '用户发券超限，目前已发放' . $item2['count'] . '条，限制' . $item2['limitNum'] . '条';
            } else {
                //发券
                list($status, $msg) = $this->backSend($item2['user_id'] ?? '', $market_id, $item2['num']);
                if ($status) {
                    $msg = '';
                }
            }
            //写入日志
            $user_id = $item2['user_id'] ?? '';
            $num     = $item2['num'] ?? '';
            (new SystemLogsModel())->record("给用户{$user_id}发放了{$num}张优惠券{$market_id}", RbacInfoModel::MARKET_MANAGER, $backUserId);
            $dataItem        = $item2;
            $dataItem['msg'] = $msg;
            if (in_array($key, $userData)) {
                $outData1[$key] = $dataItem;
            } elseif (in_array($key, $excelDataKeys)) {
                $outData2[$key] = $dataItem;
            }
        }
        //生成导出结果
        return $this->__uploadSendCardsInfo($outData1, $outData2, $backUserId, $marketName);

    }


    /**
     * @param $userNo
     * @param $file
     * @param $market_id
     * @param $backUserId
     * @return array
     * @throws Exception
     */
    public function batchDelUserCard($userNo, $file, $market_id, $backUserId = 0): array
    {
        //防止多重点击上传，单人限制3s
        $unique_key = CUtil::getAllParams(__FUNCTION__, $backUserId);
        list($anti) = self::ReqAntiConcurrency($backUserId, $unique_key, 3, 'EX');
        if (!$anti) {
            return [false, '3s内请勿重复点击！'];
        }

        //参数校验
        if (empty($userNo) && empty($file)) {
            return [false, '用户卡号或上传文件至少填写一条！'];
        }
        if (empty($market_id)) {
            return [false, '卡券不能为空！'];
        }

        //优惠券校验
        $market_data = by::marketConfig()->getOneById($market_id);
        if (!$market_data) {
            return [false, '没找到相应资源'];
        }
        $marketName = $market_data['name'] ?? '';

        //获取填入的用户卡号或者电话号码
        $userData = array_unique(array_filter(explode(',', $userNo)));
        if (count($userData) > 20) {
            return [false, '用户数据大于20条建议采用批量上传方式'];
        }


        //用户批量上传数据处理
        $data = [];
        if ($file) {
            $filename = $file['tmp_name'] ?? '';
            if (!$filename) {
                return [false, '请选择文件'];
            }

            $name = explode('.', $file['name']);
            if (array_pop($name) != 'csv') {
                return [false, '请上传csv类型文件'];
            }

            $handle = fopen($filename, 'r');
            list($s, $data) = $this->__anDelCsv($handle);
            if (!$s) {
                return [false, $data];
            }

            if (!count($data)) {
                return [false, '没有任何数据'];
            }

            //明显重复项校验
            /*$memberNos = array_column($data, 'memberNo');
            $realMemberNos = array_filter($memberNos);
            if (count($realMemberNos) !== count(array_unique($realMemberNos))) {
                return [false, '表格存在明显相同数据行，请校验后重新上传！'];
            }*/
        }

        $data      = empty($data) ? [] : array_column($data, NULL, 'memberNo');
        $excelData = array_keys($data);
        foreach ($userData as $v) {
            if (isset($data[$v])) {
                $data[$v]['num'] += 1;
            } else {
                $data[$v] = [
                        'memberNo' => $v,
                        'num'      => 1,
                ];
            }
        }


        //判断库存够不够
        $cardUsers  = [];
        $phoneUsers = [];
        $errorUsers = [];

        foreach ($data as $k => $item1) {
            //根据$k 区分手机号码和会员卡号
            if (strlen(CUtil::uint($k)) == 11) {
                $phoneUsers[] = $k;
            } elseif (strpos($k, 'Dreame') !== false) {
                $cardUsers[] = $k;
            } else {
                $errorUsers[] = $k;
            }
        }

        //用户重复项过滤
        //1.查找所有电话号码对应的user_id
        $allPhoneUsers = empty($phoneUsers) ? [] : by::users()->getWxUsersByPhones($phoneUsers);
        if ($allPhoneUsers) {
            $allPhoneUsers = array_column($allPhoneUsers, 'user_id', 'phone');
        }

        //2.查找所有会员卡号对应的user_id
        $allCardUsers = empty($cardUsers) ? [] : by::users()->getWxUsersByCards($cardUsers);
        if ($allCardUsers) {
            $allCardUsers = array_column($allCardUsers, 'user_id', 'card');
        }

        //获取两个数组相同的项
        $repeatUsers      = [];
        $revertPhoneUsers = [];
        $revertCardUsers  = [];
        if ($allPhoneUsers && $allCardUsers) {
            $sameUser         = array_intersect($allPhoneUsers, $allCardUsers);
            $revertCardUsers  = array_flip($allCardUsers);
            $revertPhoneUsers = array_flip($phoneUsers);
            foreach ($sameUser as $user) {
                $repeatUsers[] = $revertCardUsers[$user] ?? '';
            }
        }

        //组合现有数据 查出对应的已有优惠券数量
        $noDataUsers = [];
        foreach ($data as $k => $i) {
            $userId = empty($allPhoneUsers[$k] ?? '') ? ($allCardUsers[$k] ?? '') : ($allPhoneUsers[$k] ?? '');
            $count  = $this->getUserEffectiveCardNumsByMarketId($userId, $market_id);
            if (empty($userId))
                $noDataUsers[] = $k;
            $data[$k]['user_id'] = $userId;
            $data[$k]['count']   = $count;
        }
        $outData1 = [];
        $outData2 = [];
        //逐行废除优惠券
        foreach ($data as $key => $item2) {
            $item2['market_name'] = $marketName;
            if (in_array($key, $errorUsers)) {
                $msg = '参数既不是卡号也不是手机号码,请检查！';
            } elseif (in_array($key, $repeatUsers)) {
                $msg = '用户卡号和电话号码:' . ($revertPhoneUsers[$item2['user_id'] ?? 0] ?? '') . '重复,请检查！';
            } elseif (in_array($key, $noDataUsers)) {
                $msg = '该用户不存在,请检查！';
            } elseif (empty($item2['count'])) {
                $msg = '该用户未拥有该张未使用的优惠卷！';
            } else {
                //废卷
                list($status, $msg) = $this->backDel($item2['user_id'] ?? '', $market_id, $item2['num']);
                if ($status) {
                    $msg = '';
                }
            }
            //写入日志
            $user_id = $item2['user_id'] ?? '';
            $num     = $item2['num'] ?? '';
            (new SystemLogsModel())->record("给用户{$user_id}废除了{$num}张优惠券{$market_id}", RbacInfoModel::MARKET_MANAGER, $backUserId);
            $dataItem        = $item2;
            $dataItem['msg'] = $msg;

            if (in_array($key, $userData)) {
                $outData1[$key] = $dataItem;
            } elseif (in_array($key, $excelData)) {
                $outData2[$key] = $dataItem;
            }
        }
        //生成导出结果
        return $this->__uploadDelCardsInfo($outData1, $outData2, $backUserId, $marketName);

    }

    private function __uploadDelCardsInfo($outData1, $outData2, $backUserId, $marketName): array
    {
        if ($outData2) {
            $headList   = [
                    "会员卡号/手机号",
                    "用户ID",
                    "优惠卷名称",
                    "错误信息"
            ];
            $data[0][0] = '不要存在空行或重复行（勿删）';
            $outData2   = array_values($outData2);
            foreach ($outData2 as $key => $item3) {
                unset($item3['count'], $item3['num']);
                $data[$key + 1] = $item3;
            }
            $fileName = '废除优惠券-' . $marketName . '-' . $backUserId . '-' . date('Ymd') . mt_rand(1000, 9999);
            //临时存入本地缓存
            $data = CUtil::saveTmpFile($headList, $data, $fileName);
            if (isset($data['filename'])) {
                //读取文件上传阿里云oss
                if (file_exists($data['filename'])) {
                    $file['name']      = $fileName . '.csv';
                    $file['temp_name'] = $data['filename'];
                    list($status, $link) = AliYunOss::factory()->uploadFileDirectToOss($file);
                    //如果正确塞入redis
                    if (!$status) {
                        return [false, '文件上传oss服务器失败！'];
                    }
                    //同步存储url日志作为依据
                    (new SystemLogsModel())->record("给用户批量废除优惠券，结果路径为:{$link['link']}", RbacInfoModel::MARKET_MANAGER, $backUserId);
                    $this->__pushDelCardRedis([], [
                            'url'          => $link['link'] ?? '',
                            'ctime'        => date('Y-m-d H:i:s'),
                            'back_user_id' => $backUserId
                    ], $backUserId);
                }
            }
        }

        if ($outData1) {
            $headList = [
                    "会员卡号/手机号",
                    "用户ID",
                    "优惠卷名称",
                    "错误信息"
            ];
            foreach ($outData1 as $key => $item) {
                unset($item['count']);
                $item = array_values($item);
                $msg  = '';
                foreach ($item as $ik => $iv) {
                    $msg .= ($headList[$ik] ?? '') . ':' . $iv . ';';
                }
                $saveData['ctime']        = date('Y-m-d H:i:s');
                $saveData['msg']          = $msg;
                $saveData['back_user_id'] = $backUserId;
                $this->__pushDelCardRedis($saveData, [], $backUserId);
            }
        }

        return $this->__pushDelCardRedis([], [], $backUserId);

    }


    public function __pushDelCardRedis($data1, $data2, $backUserId): array
    {
        //single数据
        $singleKey  = $this->__singleDelCard($backUserId);
        $singleData = $this->__delCardData($singleKey, 50, $data1);
        if ($singleData && is_array($singleData)) {
            foreach ($singleData as $key => $single) {
                $singleData[$key] = json_decode($single, true);
            }
        }

        //batch数据
        $batchKey  = $this->__batchDelCard($backUserId);
        $batchData = $this->__delCardData($batchKey, 50, $data2);
        if ($batchData && is_array($batchData)) {
            foreach ($batchData as $key => $batch) {
                $batchData[$key] = json_decode($batch, true);
            }
        }

        return [
                true,
                [
                        'single_data_list' => $singleData,
                        'batch_data_list'  => $batchData,
                ]
        ];
    }


    private function __delCardData($post_log_key, $lRange = 30, $data = [])
    {
        if (empty($data)) {
            $data = by::redis('core')->LRANGE($post_log_key, 0, $lRange);
        } else {
            $data = by::redis('core')->LPUSH($post_log_key, json_encode($data));
            by::redis('core')->LTRIM($post_log_key, 0, $lRange);
        }
        return $data;
    }

    private function __uploadSendCardsInfo($outData1, $outData2, $backUserId, $marketName): array
    {
        if ($outData2) {
            $headList   = [
                    "会员卡号/手机号",
                    "优惠券个数",
                    "限制优惠券个数",
                    "用户ID",
                    "错误信息"
            ];
            $data[0][0] = '不要存在空行或重复行（勿删）';
            $outData2   = array_values($outData2);
            foreach ($outData2 as $key => $item3) {
                unset($item3['count']);
                $data[$key + 1] = $item3;
            }
            $fileName = '发送优惠券-' . $marketName . '-' . $backUserId . '-' . date('Ymd') . mt_rand(1000, 9999);
            //临时存入本地缓存
            $data = CUtil::saveTmpFile($headList, $data, $fileName);
            if (isset($data['filename'])) {
                //读取文件上传阿里云oss
                if (file_exists($data['filename'])) {
                    $file['name']      = $fileName . '.csv';
                    $file['temp_name'] = $data['filename'];
                    list($status, $link) = AliYunOss::factory()->uploadFileDirectToOss($file);
                    //如果正确塞入redis
                    if (!$status) {
                        return [false, '文件上传oss服务器失败！'];
                    }
                    //同步存储url日志作为依据
                    (new SystemLogsModel())->record("给用户批量发送优惠券，结果路径为:{$link['link']}", RbacInfoModel::MARKET_MANAGER, $backUserId);
                    $this->__pushCardRedis([], [
                            'url'          => $link['link'] ?? '',
                            'ctime'        => date('Y-m-d H:i:s'),
                            'back_user_id' => $backUserId
                    ], $backUserId);
                }
            }
        }

        if ($outData1) {
            $headList = [
                    "会员卡号/手机号",
                    "优惠券个数",
                    "限制优惠券个数",
                    "用户ID",
                    "错误信息"
            ];
            foreach ($outData1 as $key => $item) {
                unset($item['count']);
                $item = array_values($item);
                $msg  = '';
                foreach ($item as $ik => $iv) {
                    $msg .= ($headList[$ik] ?? '') . ':' . $iv . ';';
                }
                $saveData['ctime']        = date('Y-m-d H:i:s');
                $saveData['msg']          = $msg;
                $saveData['back_user_id'] = $backUserId;
                $this->__pushCardRedis($saveData, [], $backUserId);
            }
        }

        return $this->__pushCardRedis([], [], $backUserId);

    }


    public function __pushCardRedis($data1, $data2, $backUserId): array
    {
        //single数据
        $singleKey  = $this->__singleSendCard($backUserId);
        $singleData = $this->__sendCardData($singleKey, 50, $data1);
        if ($singleData && is_array($singleData)) {
            foreach ($singleData as $key => $single) {
                $singleData[$key] = json_decode($single, true);
            }
        }

        //batch数据
        $batchKey  = $this->__batchSendCard($backUserId);
        $batchData = $this->__sendCardData($batchKey, 50, $data2);
        if ($batchData && is_array($batchData)) {
            foreach ($batchData as $key => $batch) {
                $batchData[$key] = json_decode($batch, true);
            }
        }

        return [
                true,
                [
                        'single_data_list' => $singleData,
                        'batch_data_list'  => $batchData,
                ]
        ];
    }


    private function __sendCardData($post_log_key, $lRange = 30, $data = [])
    {
        if (empty($data)) {
            $data = by::redis('core')->LRANGE($post_log_key, 0, $lRange);
        } else {
            $data = by::redis('core')->LPUSH($post_log_key, json_encode($data));
            by::redis('core')->LTRIM($post_log_key, 0, $lRange);
        }
        return $data;
    }


    /**
     * @return array
     * 解析 csv 文件
     */
    private function __anDelCsv($handle): array
    {
        $out = [];
        $n   = 1;
        while ($data = fgetcsv($handle, 50)) {
            if ($n > 2 && !empty($data[0])) {
                $out[] = [
                        'memberNo' => trim($data[0]),
                        'num'      => intval($data[1] ?? 1),
                ];
            }

            $n++;
        }
        return [true, array_values($out)];
    }


    /**
     * @return array
     * 解析 csv 文件
     */
    private function __anCsv($handle): array
    {
        $out = [];
        $n   = 1;


        while ($data = fgetcsv($handle, 50)) {

            if ($n > 2 && !empty($data[0])) {
                if (isset($data[3]))
                    return [false, '模板不符合要求~！'];

                if (!empty($data[1]) && !is_numeric($data[1])) {
                    return [false, '模板不符合要求~~！'];
                }
                if (!empty($data[2]) && !is_numeric($data[2])) {
                    return [false, '模板不符合要求~~~！'];
                }
                $itemData = [
                        'memberNo' => mb_convert_encoding(trim($data[0]), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                        'num'      => intval($data[1] ?? 1),
                        'limitNum' => empty(intval($data[2] ?? 0)) ? intval($data[1] ?? 1) : intval($data[2] ?? 1),
                ];
                if ($itemData['num'] <= 0 || $itemData['limitNum'] <= 0) {
                    return [false, '数据不符合要求，请确认后重新上传！'];
                }
                $out[] = $itemData;
            }

            $n++;
        }

        return [true, array_values($out)];
    }


    /**
     * @param string $user_id 用户ID
     * @param int $card_type 要使用的卡片 1：商品优惠券 2：泡泡卡 3：排除卡 4：免运费券 6：兑换券 10:消费券
     * @param array $data 二维数组，类似array(array('gid' => 1,'num'=>1,'price' => 10),array('gid' => 1,'num'=>1,'price' => 10))
     * @param int $choose_card 自主选中的卡片id，若无则为0
     * @return array
     * @throws Exception
     * 当前可以使用的卡券（只是查询，不做修改操作）
     */
    public function canUseCard(string $user_id, int $card_type, array $data = [], int $choose_card = 0, int $getChannel = 0, int $spriceType = 0, bool $needSort = false, $sub_price = null): array
    {
        $choose_card = CUtil::uint($choose_card);

        if (empty($data)) {
            return [false, '购买数据不存在'];
        }

        if (!in_array($card_type, self::TYPE)) {
            return [false, '卡券类型不存在'];
        }


        //计算商品总价和规格价格
        $all_price = 0;
        $num_arr   = [];
        foreach ($data as $k => $v) {
            if (empty($v['gid'])) {
                return [false, '商品ID不存在'];
            }

            $num = CUtil::uint($v['num']);
            if ($num == 0) {
                return [false, '购买数量有误'];
            }

            $goods_info = by::Gmain()->GetOneByGidSid($v['gid'], $v['sid'], true, false, $spriceType);
            if (empty($goods_info['is_coupons'])) {
                unset($data[$k]);
                continue;
            }

            $num_arr[]             = $v['num'];
            $v_all_price           = by::cart()->getGoodsAllPrice($v['gid'], $v['sid'], $v['num'], $spriceType);
            $all_price             = bcadd($all_price, $v_all_price, 2);
            $data[$k]['all_price'] = $v_all_price;
        }

        if (!empty($sub_price)) {
            $all_price_sum = bcsub($all_price, $sub_price, 2);
            if ($all_price_sum <= 0) {
                return [false, '商品总价有误'];
            }
        }

        //获取可使用卡券
        $result = $this->__getEffList($user_id, by::userCard()::SOURCE['INDEX'], $card_type, 'ASC', 0, self::GET_CARD_CHANNEL['can_use_card'], $getChannel);
        if (empty($result)) {
            return [false, '没有可使用的卡券'];
        }
        if ($choose_card) { //选中了对应的卡券
            $chooseCards = array_column($result, null, 'id');
            $chooseInfo  = $chooseCards[$choose_card] ?? [];

            if (empty($chooseInfo)) {
                return [false, '选中的卡券错误~'];
            }
            $card_type = $chooseInfo['type'] ?? -1;
        }

        // 初始化选择、消费、总计、列表和最佳数组
        $select   = $consume = ['id' => 0, 'cprice' => 0, 'ids' => [], 'm_id' => 0, 'm_name' => ""];
        $total    = 0;
        $list     = [];
        $best_arr = [];


        // 根据卡券类型进行过滤和处理
        if ($card_type != self::TYPE['consume']) { //必须要有优惠券
            // 过滤出非消费券
            $otherCards = array_filter($result, function ($v) {
                return $v['type'] != self::TYPE['consume'];
            });
            // 如果存在非消费券，则尝试获取最佳卡券
            if (!empty($otherCards)) {
                list($s2, $res2) = $this->GetBestCard($otherCards, $card_type, $all_price, $data, $choose_card, $num_arr, $sub_price);
                if ($s2) {
                    // 更新选择、总计、列表和最佳数组
                    $select   = $res2['select'] ?? $select;
                    $total    += $res2['total'] ?? 0;
                    $list     = array_merge($list, $res2['list'] ?? []);
                    $best_arr = array_merge($best_arr, $res2['best_arr'] ?? []);
                } else {
                    // 如果没有找到最佳卡券，直接返回结果
                    return [$s2, $res2];
                }
            }
        } elseif ($card_type == self::TYPE['consume']) {
            // 处理仅为消费券的情况
            $result = array_filter($result, function ($v) {
                return $v['type'] == self::TYPE['consume'];
            });
            if (!empty($result)) {
                list($s1, $res1) = $this->GetBestCard($result, $card_type, $all_price, $data, $choose_card, $num_arr, $sub_price);
                if ($s1) {
                    // 更新消费、总计、列表和最佳数组
                    $consume  = $res1['select'] ?? $consume;
                    $total    += $res1['total'] ?? 0;
                    $list     = array_merge($list, $res1['list'] ?? []);
                    $best_arr = array_merge($best_arr, $res1['best_arr'] ?? []);
                }
            }
        } else {
            return [false, '没有可使用的卡券'];
        }

        $best_arr = array_column($best_arr, 'discount_price', 'id');
        foreach ($list as $key => $re) {
            $list[$key]['discount_price'] = $best_arr[$re['id']] ?? 0;
        }

        //排序
        if ($needSort) {
            $list = $this->sortCardList($list, $consume, $select);
        }


        $data = [
                'total'     => $total,
                'select'    => $select,
                'consume'   => $consume,
                'list'      => array_values($list),
                'all_price' => $all_price,
        ];

        return [true, $data];
    }


    public function sortCardList($list, $consume, $select)
    {
        $list = array_unique($list, SORT_REGULAR);
        // 将list按照按expire_time排序，越小越靠前
        usort($list, function ($a, $b) use ($consume, $select) {
            // 按expire_time排序，越小越靠前
            if ($a['expire_time'] != $b['expire_time']) {
                return $a['expire_time'] <=> $b['expire_time'];
            }
            // 如果expire_time相同，则按market_id排序，越大越靠前
            return $b['market_id'] <=> $a['market_id'];
        });

        // 将list中discount_price>0的分为一组，discount_price=0的分为另外一组，并将两组合并
        $list1 = $list2 = [];
        foreach ($list as $item) {
            if ($item['discount_price'] > 0) {
                $list1[] = $item;
            } else {
                $list2[] = $item;
            }
        }
        return array_values(array_merge($list1, $list2));
    }


    public function GetBestCard($result, $card_type, $all_price, $data, $choose_card, $num_arr, $sub_price = 0)
    {
        $discount_id        = 0;
        $max_discount_price = 0;
        $discount_type      = 0;
        $ids                = [];
        $m_id               = 0;
        $m_name             = '';
        $marketConfigModel  = by::marketConfig();
        $count              = count($data);
        $best_arr           = [];

        // 如果有sub_price，需要重新计算每个商品的占比
        if (!empty($sub_price)) {
            foreach ($data as $key => $item) {
                $item_sub_price          = bcdiv(bcmul($item['all_price'], $sub_price, 2), $all_price, 2);
                $data[$key]['all_price'] = bcsub($item['all_price'], $item_sub_price, 2);
            }
            //获取需要打折的商品总价
            $all_price = bcsub($all_price, $sub_price, 2);
        }

        foreach ($result as $k => &$v) {
            $market_data = $marketConfigModel->getOneById($v['market_id']);
            if (empty($market_data)) {
                unset($result[$k]);
                continue;
            }

            $discount_price = 0;
            if ($v['type'] == self::TYPE['voucher']) {
                if ($count != 1) {
                    if ($card_type == by::userCard()::TYPE['all']) {
                        $v['type_name'] = $v['resource_name'] = '兑换券';
                        $market_data && $v = $this->__conditionToName($market_data, $v);
                        continue;
                    }

//                    return [false, '兑换券仅可兑换单个商品'];
                } else {
                    if (isset($num_arr[0]) && $num_arr[0] != 1) {
                        if ($card_type == by::userCard()::TYPE['all']) {
                            $v['type_name'] = $v['resource_name'] = '兑换券';
                            $market_data && $v = $this->__conditionToName($market_data, $v);
                            continue;
                        }

//                        return [false, '兑换券仅可兑换单个商品单个数量'];
                    }
                }
            }

            //商品总价
            $goods_price = $all_price;
            //首轮进来没有查询资源详情的就去查一遍
            if (!isset($v['condition'])) {
                if (!$market_data) {
                    unset($result[$k]);
                    continue;
                }
                $v['use_rule_note'] = $market_data['use_rule_note'] ?? '';
                switch ($v['type']) {
                    case self::TYPE['coupon']:
                    case self::TYPE['consume']:
                        //资源内容类型(1:折扣优惠N%;2:固定金额N元;)
                        $v['resource_type'] = $market_data['resource_type'];

                        //优惠券名称-折扣优惠、固定金额配置值，若配置的是折扣优惠券并且discount = 18.66，则为1.866折
                        $type_name = $v['type'] == self::TYPE['coupon'] ? '优惠券' : '消费券';
                        list($v['discount'], $v['type_name'], $v['resource_name']) = by::marketConfig()->resourceType($v['resource_type'], $market_data['discount'], $type_name);

                        break;
                    case self::TYPE['voucher']:
                        $v['type_name'] = $v['resource_name'] = '兑换券';

                        break;
                    default:
                        break;
                }

                //使用条件
                $v = $this->__conditionToName($market_data, $v);
            }


            //获取需要打折的商品总价
            $tag_g_id_arr = [];
            if (!empty($market_data['c_tag_val'])) {
                $c_tag_val = explode(',', $market_data['c_tag_val']);
                foreach ($data as $val) {
                    $goods_info = by::Gmain()->GetAllOneByGid($val['gid']);
                    if (empty($goods_info)) {
                        return [false, '该商品已下架'];
                    }

                    $flag = array_intersect($goods_info['tids'], $c_tag_val);
                    switch ($market_data['c_tag_type']) {
                        case $marketConfigModel::C_TAG_TYPE['apply']:
                            //不在适用标签里就去掉
                            if (empty($flag)) {
                                $goods_price    = bcsub($goods_price, $val['all_price'], 2);
                                $tag_g_id_arr[] = $val['gid'];
                            }

                            break;
                        default:
                            //在排除标签里就去掉
                            if (!empty($flag)) {
                                $goods_price    = bcsub($goods_price, $val['all_price'], 2);
                                $tag_g_id_arr[] = $val['gid'];
                            }

                            break;
                    }
                }
            }

            //获取需要打折的商品总价
            if (!empty($market_data['c_goods_val'])) {
                $c_goods_val = explode(',', $market_data['c_goods_val']);
                foreach ($data as $val) {
                    if (!empty($tag_g_id_arr) && in_array($val['gid'], $tag_g_id_arr)) {
                        continue;
                    }

                    $flag = in_array($val['gid'], $c_goods_val);
                    switch ($market_data['c_goods_type']) {
                        case $marketConfigModel::C_GOODS_TYPE['apply']:
                            //不在适用商品里就去掉
                            if (empty($flag)) {
                                $goods_price = bcsub($goods_price, $val['all_price'], 2);
                            }
                            break;
                        default:
                            //在排除商品里就去掉
                            if (!empty($flag)) {
                                $goods_price = bcsub($goods_price, $val['all_price'], 2);
                            }

                            break;
                    }
                }
            }


            if ($v['type'] == self::TYPE['coupon'] || $v['type'] == self::TYPE['consume']) {
                //最低订单金额判断
                if ($market_data['c_amount_type'] == $marketConfigModel::C_AMOUNT_TYPE['lowest']) {
                    //金额不满足就跳过
                    if ($goods_price < $market_data['c_amount_val']) {
                        continue;
                    }
                }

                //价格要大于优惠券面额
                if ($v['resource_type'] == 2 && bccomp($goods_price, $v['discount'], 2) < 1) {
                    continue;
                }

                //优惠券最优选项判断
                if ($v['resource_type'] == 1) {
                    //折扣之后价格（向下取整保留两位数）
                    $discount_price = floor(sprintf("%.2f", ($goods_price - ($goods_price * $v['discount'] / 10)) * 100)) / 100;
                    //若优惠金额连0.01元都不满足则跳过
                    if ($discount_price == 0) {
                        continue;
                    }

                    //优惠券里的折扣券，校验使用条件
                    $goods_num = 0;
                    foreach ($data as $val) {
                        //不满足的商品
                        if (!empty($tag_g_id_arr) && in_array($val['gid'], $tag_g_id_arr)) {
                            continue;
                        }
                        //商品数量
                        $goods_num += $val['num'];
                    }
                    if (
                            isset($market_data['c_meet_type']) &&
                            (
                                    ($market_data['c_meet_type'] == MarketConfigModel::C_MEET_TYPE['min'] && $market_data['c_meet_num'] > $goods_num) ||
                                    ($market_data['c_meet_type'] == MarketConfigModel::C_MEET_TYPE['max'] && $market_data['c_meet_num'] < $goods_num)
                            )
                    ) {
                        continue;
                    }

                } else {
                    //固定金额优惠券
                    $discount_price = $v['discount'];
                }
            }

            //兑换券折扣价格
            if ($v['type'] == self::TYPE['voucher']) {
                if ($goods_price <= 0) {
                    continue;
                }

                $discount_price = $goods_price;
            }

            // 使用券控制（未到时间显示不可用）
            if ($v['start_time'] > time() || $v['expire_time'] < time()) {
                continue;
            }

            //是否有被选中的卡券
            if ($choose_card > 0) {
                if ($choose_card == $v['id']) {
                    $max_discount_price = $discount_price;
                    $discount_id        = $v['id'];
                    $discount_type      = $v['type'];
                    $m_id               = $v['market_id'];
                    $m_name             = $v['resource_name'];
                }
            } else {
                if ($discount_price > $max_discount_price) {
                    $max_discount_price = $discount_price;
                    $discount_id        = $v['id'];
                    $discount_type      = $v['type'];
                    $m_id               = $v['market_id'];
                    $m_name             = $v['resource_name'];
                }
            }

            $best_arr[] = [
                    'id'             => $v['id'],
                    'discount_price' => $discount_price,
            ];

            $ids[] = $v['id'];
        }

        if ($choose_card > 0 && $discount_id == 0) {
            return [true, [
                    'total'    => count($result),
                    'select'   => [
                            'id'     => 0,
                            'cprice' => 0,
                            'ids'    => [],
                            'm_id'   => 0,
                            'm_name' => "",
                            'type'   => "",
                    ],
                    'list'     => array_values($result),
                    'best_arr' => $best_arr
            ]];
        }

        $max_discount_price = floatval($max_discount_price);
        if ($card_type == self::TYPE['voucher'] && $max_discount_price <= 0) {
            return [false, '没有可用兑换券'];
        }

        $select = [
                'id'     => $discount_id,         //选中的优惠券id
                'cprice' => $max_discount_price,  //优惠金额
                'ids'    => $ids,                 //查出的优惠券列表中满足条件可使用的优惠券id
                'm_id'   => $m_id,                //资源id
                'm_name' => $m_name,              //优惠券名称
                'type'   => $discount_type, //优惠券类型
                //            'real_price'     => bcsub($all_price, $max_discount_price, 2),//优惠后价格
        ];


        $rdata = [
                'total'    => count($result),
                'select'   => $select,
                'list'     => array_values($result),
                'best_arr' => $best_arr
        ];

        return [true, $rdata];
    }

    /**
     * @param $select
     * @param $result
     * @param $best_arr
     * @return array|mixed
     * 优惠券排序
     */
    private function _conditionSort($select, $result, $best_arr)
    {
        //优惠券排序
        $id       = $select['id'] ?? 0;
        $ids      = $select['ids'] ?? [];
        $best_arr = array_column($best_arr, 'discount_price', 'id');

        if ($id && $ids) {
            $result = array_values($result);
            foreach ($result as $key => $re) {
                $result[$key]['discount_price'] = $best_arr[$re['id']] ?? 0;
            }

            //按照过期时间越早的在上面，最后按创建时间越晚的在上
            $expireTimeSort    = array_column($result, 'expire_time');
            $createTimeSort    = array_column($result, 'create_time');
            $discountPriceSort = array_column($result, 'discount_price');
            array_multisort($discountPriceSort, SORT_DESC, $expireTimeSort, SORT_ASC, $createTimeSort, SORT_DESC, $result);
            //排序有用/不可用优惠券
            $preArr  = [];
            $midArr  = [];
            $lastArr = [];
            foreach ($result as $key => $v) {
                if ($v['id'] == $id) {
                    $preArr[] = $v;
                    continue;
                }
                if (in_array($v['id'], $ids)) {
                    $midArr[] = $v;
                } else {
                    $lastArr[] = $v;
                }
            }
            $result = array_merge($preArr, $midArr, $lastArr);
        }

        return $result;
    }

    /**
     * @param int $user_id
     * @param int $card_type
     * @param string $relation_id
     * @param array $data
     * @param int $choose_card
     * @param $tran
     * @return array
     * @throws Exception
     * 使用卡券
     */
    public function useCard(int $user_id, int $card_type, string $relation_id, array $data = [], int $choose_card = 0, $tran = null, int $getChannel = 0, int $spriceType = 0): array
    {
        //查询有无此卡券可以使用
        list($status, $result) = $this->canUseCard($user_id, $card_type, $data, $choose_card, $getChannel, $spriceType);
        if (!$status) {
            return [false, $result];
        }


        $use_channel = self::USE_CHANNEL['shopping'];
        $table       = self::tbName($user_id);

        if ($card_type == self::TYPE['consume']) {
            $cprice    = $result['consume']['cprice'] ?? 0;
            $update_id = $result['consume']['id'] ?? 0;
            $m_id      = $result['consume']['m_id'] ?? 0;
        } else {
            $cprice    = $result['select']['cprice'] ?? 0;
            $update_id = $result['select']['id'] ?? 0;
            $m_id      = $result['select']['m_id'] ?? 0;
        }

        if ($update_id == 0) {
            return [false, '没有可用卡券'];
        }

        $db       = by::dbMaster();
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;

        try {
            $res = $db->createCommand()->update($table, [
                    'status'       => self::STATUS['use'],
                    'use_channel'  => $use_channel,
                    'use_relation' => $relation_id,
                    'use_time'     => time(),
            ], ['id' => $update_id])
                    ->execute();

            if (!$res) {
                throw new MyExceptionModel('使用卡券失败（1）');
            }

            list($s) = by::marketConfig()->useNumModify($m_id, 1);
            if (!$s) {
                throw new MyExceptionModel('使用卡券失败（2）');
            }

            is_null($tran) && $new_tran->commit();

            //删除缓存
            $this->__delCache($user_id);

            return [true, $cprice];
        } catch (MyExceptionModel $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("使用卡券失败|{$error}", 'link-useCard');

            is_null($tran) && $new_tran->rollBack();
            return [false, $_e->getMessage()];

        } catch (\Exception $_e) {

            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("使用卡券失败|{$error}", 'link-useCard');

            is_null($tran) && $new_tran->rollBack();
            return [false, '操作失败'];

        }
    }

    /**
     * @param $user_id
     * @param int $type
     * @param mixed $status
     * @return false|int|string|null
     * @throws Exception
     * 根据条件获取卡券总数
     */
    public function getTotal($user_id, $type = -1, $status = self::STATUS['get'], $getChannel = 0)
    {
        $redis   = by::redis();
        $r_key   = AppCRedisKeys::userCardList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $type, $getChannel);
        $total   = $redis->hGet($r_key, $sub_key);

        if ($total === false) {
            $tb       = self::tbName($user_id);
            $now_time = time();

            $where  = " `user_id` = :user_id AND `expire_time` > :e_time AND `status` = :status";
            $params = [
                    ':user_id' => $user_id,
                    ':e_time'  => $now_time,
                    ':status'  => $status,
            ];

            if ($type > -1) {
                $where           .= " AND `type` = :type";
                $params[':type'] = $type;
            }

            if ($getChannel) {
                $where                  .= " AND `get_channel` = :get_channel";
                $params[':get_channel'] = $getChannel;
            }

            $sql   = "SELECT count(*) FROM {$tb} WHERE {$where}";
            $total = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $redis->hSet($r_key, $sub_key, $total);
            CUtil::ResetExpire($r_key, $this->expire_time);
        }

        return intval($total);
    }


    /**
     * @param $user_id
     * @return int
     * @throws Exception
     * 获取用户所有可用的卡券数量
     */
    public function GetEffectCouponsCount($user_id)
    {
        $list = $this->__getEffList($user_id, by::userCard()::SOURCE['INDEX'], -1, 'ASC', 0, self::GET_CARD_CHANNEL['can_use_card']);
        $data = $this->getMarketData($list, by::userCard()::SOURCE['INDEX'], 1);
        return count($data) ?? 0;
    }


    /**
     * @param $user_id
     * @param $source
     * @param int $type
     * @param string $order
     * @param int $expire_day
     * @param int $channel
     * @param int $getChannel
     * @return array
     * @throws Exception
     * 获取有效期内所有卡券
     */
    private function __getEffList($user_id, $source, int $type = -1, string $order = 'ASC', int $expire_day = 7, int $channel = self::GET_CARD_CHANNEL['my_card'], int $getChannel = 0): array
    {
        $e_time  = strtotime("-{$expire_day} days");
        $redis   = by::redis();
        $r_key   = AppCRedisKeys::userCardList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $source, $type, $expire_day, $channel, $getChannel);

        $aJson = $redis->hGet($r_key, $sub_key);
        $aData = (array) json_decode($aJson, true);
        if ($aJson === false) {
            $tb     = self::tbName($user_id);
            $where  = " `user_id` = :user_id";
            $params = [
                    ':user_id' => $user_id,

            ];

            if ($source == by::userCard()::SOURCE['INDEX']) {
                $where             .= " AND `expire_time` > :e_time";
                $params[':e_time'] = $e_time;
            }

            if ($type > -1) {
                $where           .= " AND `type` = :type";
                $params[':type'] = $type;
            }


            if ($getChannel > 0) {
                $where                  .= " AND `get_channel` = :get_channel";
                $params[':get_channel'] = $getChannel;
            }

            $sql   = "SELECT `id`,`market_id`,`type`,`start_time`,`create_time`,`use_time`,`expire_time`,`status`,`get_channel`,`get_relation` FROM {$tb} 
                            WHERE {$where} ORDER BY `expire_time` {$order},`id` DESC";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $redis->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, $this->expire_time);
        }
        return array_values(array_filter($aData, function ($v) use ($channel, $e_time, $source) {
            if ($channel == self::GET_CARD_CHANNEL['can_use_card']) {
//                if ($v['start_time'] > time()) {  要求能显示未开始的卡券，但是不可使用置灰
//                    return false;
//                }
                if (!empty($v['use_time'])) { //可使用的不包括已使用的
                    return false;
                }
            }

            if ($source == by::userCard()::SOURCE['INDEX']) {
                if ($v['expire_time'] <= $e_time) {
                    return false;
                }

                if (!empty($v['use_time']) && $v['use_time'] <= $e_time) {
                    return false;
                }
            }

            return true;
        }));

    }


    /**
     * @param $user_id
     * @param $card_id
     * @return array
     * @throws \yii\db\Exception
     * 回滚卡券
     */
    public function UnLockCard($user_id, $card_id, $tran = null, $limitVoucher = false)
    {
        //查询此卡券是否被锁定使用和什么时候使用的
        $card_id = CUtil::uint($card_id);
        $aLog    = $this->getCardById($user_id, $card_id);

        if (empty($aLog)) {
            return [false, '没有可返还的卡券'];
        }

        if ($limitVoucher) {
            $marketConfigModel = by::marketConfig();
            $marketData        = $marketConfigModel->getOneById($aLog['market_id']);
            if (empty($marketData)) {
                return [true, '卡券资源不存在'];
            }
            if ($marketData['type'] == self::TYPE['voucher']) {
                return [true, '兑换券不可返还'];
            }
        }


        if ($aLog['status'] != self::STATUS['use']) {
            //            return [false, '没有可返还的卡券~~'];
            return [true, '卡券已经返还！'];
        }

        $m_id = $aLog['market_id'];
        //如果卡券解锁后当前也到了过期时间那就直接改成过期
        if (time() > $aLog['expire_time']) {
            $status = self::STATUS['overtime'];
        } else {
            $status = self::STATUS['get'];
        }

        $db = by::dbMaster();
        $tb = self::tbName($user_id);

        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;

        try {
            $save = [
                    'status'       => $status,
                    'use_channel'  => 0,
                    'use_relation' => 0,
                    'use_time'     => 0,
            ];

            $res = $db->createCommand()->update($tb, $save, ['id' => $card_id])->execute();
            if (!$res) {
                throw new MyExceptionModel('回滚卡券失败（1）');
            }

            list($s,) = by::marketConfig()->useNumModify($m_id, -1);

            if (!$s) {
                throw new MyExceptionModel('回滚卡券失败（2）');
            }
            is_null($tran) && $new_tran->commit();

            //删除缓存
            $this->__delCache($user_id);

            return [true, '卡券返还成功'];
        } catch (MyExceptionModel $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("回滚卡券失败|{$error}", 'link-UnLockCard');

            is_null($tran) && $new_tran->rollBack();
            return [false, $_e->getMessage()];

        } catch (\Exception $_e) {

            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("回滚卡券失败|{$error}", 'link-UnLockCard');

            is_null($tran) && $new_tran->rollBack();
            return [false, '操作失败'];

        }

    }

    /**
     * @param $user_id
     * @param int $id
     * @param int $type
     * @param int $expire_time
     * @return array
     * @throws \yii\db\Exception
     * 后台删除卡券
     */
    public function delCard($user_id, $id = 0, $type = 0, $expire_time = 0)
    {
        if (empty($user_id)) {
            return [false, '请输入用户'];
        }
        $tb = self::tbName($user_id);
        if ($id > 0) {
            $num = by::dbMaster()->createCommand()->update(
                    $tb,
                    ['expire_time' => 0],
                    [
                            'id'      => $id,
                            'user_id' => $user_id,
                            'status'  => self::STATUS['get']
                    ]
            )->execute();

        } else {
            if ($type <= 0 || $expire_time <= 0) {
                return [false, '请选择卡券删除'];
            }
            $num = by::dbMaster()->createCommand()->update(
                    $tb,
                    ['expire_time' => 0],
                    [
                            'user_id'     => $user_id,
                            'type'        => $type,
                            'expire_time' => $expire_time,
                    ]
            )->execute();
        }

        //删除缓存
        $this->__delCache($user_id);

        if ($num > 0) {
            $msg = "删除卡券：uid：{$user_id}|id：{$id}|type：{$type}|expire_time：{$expire_time}；成功条数：{$num}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);
        }

        return [true, $num];
    }

    /**
     * @param $user_id
     * @param $get_channel
     * 查询某个获取方式的优惠券数量（领取优惠券前查询，无需缓存）
     */
    public function getCountByGetChannel($user_id, $get_channel)
    {
        // 使用安全的类型转换函数，确保user_id和get_channel为整数
        $user_id     = CUtil::uint($user_id);
        $get_channel = CUtil::uint($get_channel);

        // 检查user_id和get_channel是否为空或不在有效的渠道列表中
        if (empty($user_id) || empty($get_channel) || !in_array($get_channel, self::GET_CHANNEL)) {
            return 0;
        }

        // 获取用户的电话号码和关联的UID列表
        $phoneModel = by::Phone();
        $phone      = $phoneModel->GetPhoneByUid($user_id);
        $uids       = array_unique(array_filter($phoneModel->GetUidsByPhone($phone)));

        // 初始化计数器数组
        $countArray = [];
        if (!empty($uids)) {
            // 遍历所有的UID，查询每个UID在指定渠道的优惠券数量
            foreach ($uids as $uid) {
                // 获取特定UID的表名
                $tb = self::tbName($uid);

                // 构造SQL查询并执行，获取指定渠道的优惠券领取数量并按get_relation分组  count_number为每次产品注册领取的优惠券数量
                $sql = "SELECT `get_relation`, COUNT(*) AS `count_number`
                    FROM {$tb}
                    WHERE `user_id` = :user_id AND `get_channel` = :get_channel
                    GROUP BY `get_relation`";

                $result = by::dbMaster()->createCommand($sql, [
                        ':user_id'     => $uid,
                        ':get_channel' => $get_channel
                ])->queryAll();

                // 过滤空结果，存储在计数数组中
                if ($result) {
                    $countArray[] = $result;
                }
            }
        }

        // 返回产品注册领取数量
        return array_sum(array_map('count', $countArray));
    }


    /**
     * @param $user_id
     * @param $get_channel
     * @param $get_relation
     * @return array
     * @throws Exception
     * 根据管理业务查询对应优惠券列表
     */
    public function getListByGetRelation($user_id, $get_channel, $get_relation, $is_limit = false): array
    {
        $redis   = by::redis();
        $r_key   = AppCRedisKeys::userCardList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__, $get_channel, $get_relation, $is_limit);
        $aJson   = $redis->hGet($r_key, $sub_key);
        $aData   = (array) json_decode($aJson, true);

        if ($aJson === false) {
            $is_limit ? $max = 100 : $max = PmarketModel::MAX_COUNT;
            $tb  = self::tbName($user_id);
            $sql = "SELECT `market_id` FROM {$tb} 
                WHERE `get_channel` = :get_channel AND `user_id` = :user_id";

            $params = [':get_channel' => $get_channel, ':user_id' => $user_id];

            if ($get_relation) {
                if (is_array($get_relation)) {
                    $get_relation = implode("','", $get_relation);
                    $sql          .= " AND `get_relation` in ('{$get_relation}')";
                } else {
                    $sql                     .= " AND `get_relation` = :get_relation";
                    $params[':get_relation'] = $get_relation;
                }

            }

            $sql   .= " LIMIT {$max}";
            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
            $aData = $aData ? array_column($aData, 'market_id') : [];

            $redis->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, $this->expire_time);
        }

        return $aData;
    }


    public function exportCardTemplate($headList, $data, $fileName)
    {
        $data = CUtil::saveTmpFile($headList, $data, $fileName);
        if (isset($data['filename'])) {
            //读取文件上传阿里云oss
            if (file_exists($data['filename'])) {
                $file['name']      = $fileName . '.csv';
                $file['temp_name'] = $data['filename'];
                list($status, $link) = AliYunOss::factory()->uploadFileDirectToOss($file);
                //如果正确塞入redis
                if (!$status) {
                    return [false, '文件上传oss服务器失败！'];
                }
            }
        } else {
            return [false, '文件暂时存储本地失败！'];
        }
        return [true, ['url' => $link['link'] ?? '']];
    }

    /**
     * 根据id查询信息（无缓存）
     * @param array $groupIds
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getListByIds(array $groupIds, array $columns = ['id']): array
    {
        $data = [];
        // 查询字段
        $columns1 = implode("`,`", $columns);
        // 分组查询
        foreach ($groupIds as $index => $ids) {
            $tb = self::tbName($index);
            // 查询条件
            $ids = implode(',', $ids);
            // 执行SQL
            $sql = "SELECT `{$columns1}` FROM {$tb} WHERE `id` IN ({$ids})";
            $res = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            if (in_array('id', $columns)) {
                $res = array_column($res, null, 'id');
            }
            $data[$index] = $res;
        }
        return $data;
    }

    /**
     * 查询数据
     * @param array $groupIds
     * @param int $channel
     * @param array $columns
     * @return array
     * @throws Exception
     */
    public function getListByGroupUserIdsAndRelationIds(array $groupIds, int $channel, array $columns = ['id'])
    {
        $data = [];
        // 查询字段
        $columns1 = implode("`,`", $columns);
        // 分组查询
        foreach ($groupIds as $index => $ids) {
            $tb          = self::tbName($index);
            $userIds     = implode(',', $ids['user_id']);
            $relationIds = implode(',', $ids['relation_id']);
            // 执行SQL
            $sql   = "SELECT `{$columns1}` FROM {$tb} WHERE `user_id` IN ({$userIds}) AND  `get_relation` IN ({$relationIds}) AND `get_channel` = {$channel}";
            $items = by::dbMaster()->createCommand($sql)->queryAll();
            // 有数据合并
            foreach ($items as $item) {
                $userId                               = $item['user_id'];
                $relation                             = $item['get_relation'];
                $data[$userId][$channel][$relation][] = $item;
            }
        }
        return $data;
    }


    /**
     * 是否领取过
     * @param $user_id
     * @param $market_id
     * @return bool
     * @throws Exception
     */
    public function isReceive($user_id, $market_id): bool
    {
        // 是否领取过
        $tb    = self::tbName($user_id);
        $sql   = "SELECT 1 FROM {$tb} WHERE `user_id` = {$user_id} AND `market_id` = {$market_id}";
        $count = by::dbMaster()->createCommand($sql)->query()->count();
        return $count > 0;
    }


    /**
     * 订单是否领发放卡券
     * @param $user_id
     * @param $order_no
     * @return int
     * @throws Exception
     *
     */
    public function checkCouponSend($user_id, $order_no): int
    {
        $tb = self::tbName($user_id);
        return by::dbMaster()->createCommand("SELECT 1 FROM {$tb} WHERE `user_id` = {$user_id} AND `get_relation` = '{$order_no}'")->query()->count();
    }
}

