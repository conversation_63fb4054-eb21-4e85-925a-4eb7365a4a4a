<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/25
 * Time: 17:14
 */

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\WXOA;
use app\jobs\UpdateUserStoreJob;
use app\models\by;
use app\models\CUtil;
use RedisException;
use spec\Prophecy\Exception\Doubler\InterfaceNotFoundExceptionSpec;
use yii\db\Exception;

class WeFocusModel extends CommModel
{

    //队列延时时间
    const PAY_EXPIRE = YII_ENV_PROD ? 180 : 60;

    //补偿时间范围  *** 一定大于队列延时时间( 补偿时间600 + 企业微信回调队列延时时间180 + 补偿队列延时时间180   by::WeFocus()::PAY_EXPIRE + CommController::PAY_EXPIRE)
    const UPDATE_STORE_EXPIRE = YII_ENV_PROD ? 960 : 300;

    public $tb_fields = [
        'id', 'we_openid', 'unionid', 'store', 'source', 'resources', 'createTime', 'ctime'
    ];

    public static function tableName(): string
    {
        return "`db_dreame`.`t_we_focus`";
    }

    private function getOaInfoByUnionIdKey($unionId): string
    {
        return AppCRedisKeys::getOaInfoByUnionIdKey($unionId);
    }

    public function __delOaInfoByUnionId($unionId)
    {
        $redis    = by::redis();
        $redisKey = $this->getOaInfoByUnionIdKey($unionId);
        $redis->del($redisKey);
    }

    public function saveLog($data)
    {
        $db        = by::dbMaster();
        $tb        = self::tableName();
        $unionid   = $data['unionid'] ?? '';
        $store     = $data['store'] ?? '';
        $source    = $data['source'] ?? '';
        $resources = $data['resources'] ?? '';

        try {
            // 尝试查找记录
            $record = self::findOne(['unionid' => $unionid]);

            if (empty($record)) {
                // 插入企业微信表用户数据
                $db->createCommand()->insert($tb, $data)->execute();
                //防止操作过快3分钟后刷新门店信息
                !empty($store) && \Yii::$app->queue->delay(self::PAY_EXPIRE)->push(new UpdateUserStoreJob([
                    'unionId' => $unionid,
                    'store'   => $store
                ]));
            } else {
                // 更新记录
                $db->createCommand()->update($tb, ['resources' => $resources, 'source' => $source, 'utime' => time()], ['unionid' => $unionid])->execute();
            }
            $this->__delOaInfoByUnionId($unionid);
            return true;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug('企业微信用户添加失败|' . $error, 'error.ADD_OA_USER.info');
            return false;
        }
    }


    /**
     * @throws RedisException
     * 通过unionId获取详情
     */
    public function getOaInfoByUnionId($unionId)
    {
        if (empty($unionId)) {
            return [];
        }
        $redis    = by::redis();
        $redisKey = $this->getOaInfoByUnionIdKey($unionId);
        $aJson    = $redis->get($redisKey);
        $aData    = (array)json_decode($aJson, true);
        if (!$aData) {
            $aData = self::find()
                ->where(['unionid' => $unionId])
                ->asArray()
                ->one() ?? [];
            $redis->set($redisKey, json_encode($aData, 320), ['EX' => empty($aData) ? 10 : 1800]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     * 门店列表
     */
    public function storeList()
    {
        $tb  = self::tableName();
        $sql = "SELECT DISTINCT(`store`) AS `name` FROM {$tb}";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }
}
