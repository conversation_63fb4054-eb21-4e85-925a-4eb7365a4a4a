<?php
/**
 * Created by IntelliJ IDEA.
 * User: fantasy
 * Date: 2022/5/23
 * Time: 15:43
 */
namespace app\modules\main\models;

use app\components\AliYunSms;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;

class SmsModel extends CommModel {

    CONST CODE_LEN           = 4; //验证码长度

    CONST CODE_EXPIRE        = 600; // 每条验证码有效期 10分钟

    CONST SMS_SEND_EXPIRE    = 60;  // 短信每60秒发送一次

    CONST SMS_SEND_FOR_HOURS = 20;   // 每小时最多发20条

    CONST SMS_SEND_FOR_DAY   = 20;  // 每天最多发20条

    //场景短信模板映射
    const SCENES_ID = [
            'CODE'                   => AliYunSms::TEMPLATE_CODE['code'],                   //验证码
            'REG'                    => AliYunSms::TEMPLATE_CODE['reg'],                    //邀请好友
            'BIND'                   => AliYunSms::TEMPLATE_CODE['bind'],                   //绑定成功
            'AGAIN_BIND'             => AliYunSms::TEMPLATE_CODE['again_bind'],             //再次绑定
            'ORDER_PRESALE_START'    => AliYunSms::TEMPLATE_CODE['order_presale_start'],    //预售订单开始
            'ORDER_PRESALE_END'      => AliYunSms::TEMPLATE_CODE['order_presale_end'],      //预售订单结束
            'TRADE_IN_ORDER_CONFIRM' => AliYunSms::TEMPLATE_CODE['trade_in_order_confirm'], //旧机核验结果通知
            'REFUND_SMS'             => AliYunSms::TEMPLATE_CODE['refund_sms'],             //退款短信模版
    ];

    /**
     * @param string $phone
     * @param int $opt
     * @param string $sid
     * @return bool|int
     * 验证码管理
     */
    public function smsCodeManger(string $phone='', string $sid='', int $opt=0) {
        $code_key  = AppCRedisKeys::SmsCode($phone,$sid);
        switch ($opt) {
            case 0 : // 获取验证码信息 不管有无,都会返回 code , time
                $cache       = by::redis('core')->HGETALL($code_key);
                $ret['code'] = $cache['code'] ?? '';
                $ret['time'] = $cache['time'] ?? intval(START_TIME);
                break;
            case 1 : //删除
                $ret = by::redis('core')->DEL($code_key);
                break;
            case 2 : //生成验证码并返回
                $ret['code'] = CUtil::createVerifyCode(self::CODE_LEN);
                $ret['time'] = intval(START_TIME);
                by::redis('core')->HMSET($code_key,$ret);
                by::redis('core')->EXPIRE($code_key,self::CODE_EXPIRE);
                break;
            case 3 :
            default: //过期时间
                $ret = by::redis('core')->TTL($code_key);
                break;
        }

        return $ret;
    }


    /**
     * @param string $phone : 手机号
     * @param string $sid : 场景
     * @param array $ext : 扩展信息
     * @return array : 成功 [true,code] ; 失败 [false,msg]
     * 发送短信验证码
     * 1分钟内短信发送条数不超过： 1
     * 1小时内短信发送条数不超过： 5
     * 1个自然日内短信发送条数不超过：10
     */
    public function smsSend(string $phone='', string $sid='CODE', array $ext=[]): array
    {
        if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
            return [false,"非法手机号:{$phone}"];
        }

        if(!isset(self::SCENES_ID[$sid])) {
            return [false,'模板不合法(不存在或被拉黑:0)'];
        }

        //每天次数验证
        $day_key = AppCRedisKeys::SmsSendByDay($phone,$sid);
        if(by::redis('core')->get($day_key) >= self::SMS_SEND_FOR_DAY) {
            return [false,"验证码请求次数达到上限"];
        }

        //每小时次数验证
        $hours_key = AppCRedisKeys::SmsSendByHours($phone,$sid);
        if(by::redis('core')->get($hours_key) >= self::SMS_SEND_FOR_HOURS) {
            return [false,"验证码请求次数达到上限,请1小时候重试"];
        }

        //每次短信发送频率验证
        $send_key  = AppCRedisKeys::SmsSend($phone, $sid);
        if(!by::redis('core')->SETNX($send_key, 1)) {
            $ttl = by::redis('core')->TTL($send_key);

            return [false,"请求手机验证码太频繁，请于{$ttl}s后请求"];
        }

        //设置有效期
        by::redis('core')->EXPIRE($send_key,self::SMS_SEND_EXPIRE);

        $code_info = "";
        switch ($sid) {
            case 'CODE' :
                $code_info      = $this->smsCodeManger($phone, $sid,2);
                list($ret,$msg) = AliYunSms::sendSms($phone, self::SCENES_ID[$sid], ['code' => $code_info['code']]);

                break;
            case 'REG'  :
            case 'BIND' :
            case 'AGAIN_BIND' :
            case 'ORDER_PRESALE_START' :
            case 'ORDER_PRESALE_END' :
                list($ret, $msg) = AliYunSms::sendSms($phone, self::SCENES_ID[$sid], $ext);

                break;
            default :
                list($ret, $msg) = [false,"非法操作"];

                break;
        }


        if(!$ret) {
            switch (true) {
                case $msg == "触发小时级流控Permits:20" :
                    return [false,"验证码请求次数达到上限,请1小时候重试"];
                case $msg == "触发天级流控Permits:20" :
                    return [false, "验证码请求次数达到上限"];
                default :
                    return [false, $msg];
            }
        }

        //记录每天发送次数
        by::redis('core')->INCR($day_key);
        by::redis('core')->EXPIREAT($day_key,mktime(23, 59, 59));

        //记录每小时发送次数
        by::redis('core')->INCR($hours_key);
        by::redis('core')->EXPIREAT($hours_key,strtotime("+1 hours",strtotime(date("Y/m/d H:00:00"))) - 1);

        return [true,$code_info['code'] ?? ''];
    }

    /**
     * @param $scenes_id
     * @param $phone
     * @return array
     * 针对不同场景下的验证
     */
    public function checkScenes($scenes_id, $phone): array
    {
        if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
            return [false,"非法手机号:{$phone}"];
        }

        $sid = CUtil::uint($scenes_id);
        if(!in_array($sid, self::SCENES_ID)){
            return [false,'场景id不正确'];
        }

        switch ($sid) {
            case 1:   //登录的时候 不检查是否被占用
                return [true, 'ok'];
            default:
                return [true, 'ok'];
        }
    }


    /**
     * @param $phone
     * @param $sid
     * @param $code
     * @return array
     * 短信验证码验证
     */
    public function verifyCode($phone, $sid, $code): array
    {
        if (empty($code)) {
            return [false,"验证码不能为空"];
        }

        $ttl = $this->smsCodeManger($phone,$sid,3);
        if($ttl <=0 ) {
            return [false,"验证码已过期"];
        }

        $code_info = $this->smsCodeManger($phone,$sid);

        if($code_info['code'] === $code) {
            //立即删除验证码
            $this->smsCodeManger($phone,$sid,1);
            return [true,'ok'];
        }

        return [false,"验证码错误"];
    }
}
