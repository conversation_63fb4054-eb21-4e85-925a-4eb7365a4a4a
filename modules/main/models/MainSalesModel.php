<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/11
 * Time: 17:35
 */

namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class MainSalesModel extends CommModel
{
    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_main_sales`";
    }

    public $tb_fields = [
        'id', 'main_sku', 'name', 'status', 'is_del', 'ctime', 'utime'
    ];

    const HOST_TYPE = 'host';

    const PART_TYPE = 'part';

    const IS_DEL = [
        'no' => 0,
        'yes' => 1
    ];


    private function __getMainSalesList(): string
    {
        return AppCRedisKeys::getMainSalesList();
    }

    private function __getMainInfoById($id): string
    {
        return AppCRedisKeys::getMainInfoById($id);
    }

    /**
     * @throws RedisException
     * 删除主机信息缓存
     */
    public function __delGetMainInfoByIdCache($ids)
    {
        $redis = by::redis('core');
        $redis_key = [];
        foreach ($ids as $id){
            $redis_key[] = $this->__getMainInfoById($id);
        }
        $redis->del($redis_key);
    }

    /**
     * @param $main_sku
     * @return string
     * main_parts中获取主机信息
     */
    private function __getOneInfoByMainSku($main_sku): string
    {
        return AppCRedisKeys::getOneInfoByMainSku($main_sku);
    }

    /**
     * @throws RedisException
     */
    public function __delOneInfoByMainSku($main_sku)
    {
        $redis = by::redis('core');
        $redis_key = $this->__getOneInfoByMainSku($main_sku);
        $redis->del($redis_key);
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取详情信息通过id
     */
    public function getMainInfoById($id)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getMainInfoById($id);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` =:id AND `status` =:status LIMIT 1";
            $main   = by::dbMaster()->createCommand($sql, [':id' => $id, ':status' => 0])->queryOne();
            $aData  = empty($main) ? [] : $main;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }


        $id = $aData['id'] ?? '';
        //配件信息
        if ($id) {
            $partInfo           = by::partsSalesModel()->GetOneByMainId($id);
            $aData['part_info'] = empty($partInfo) ? [] : $partInfo;
        }
        return $aData ?? [];
    }


    /**
     * @param $id
     * @param $host_sku
     * @param $partInfo
     * @param $tran
     * @return array
     * @throws Exception
     */
    public function modify($id, $host_sku, $partInfo, $tran = null): array
    {
        list($status, $ret) = $this->_modifyCheck($id, $host_sku, $partInfo);
        if (!$status) {
            return [false, $ret];
        }

        $db       = by::dbMaster();
        $tb       = self::tbName();
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            if ($id) {
                //修改
                $data = [
                    'main_sku' => $ret['host_sku'],
                    'name'     => $ret['host_name'],
                    'utime'    => intval(START_TIME),
                ];
                $row  = $db->createCommand()->update($tb, $data, ['id' => $id])->execute();
                if (!$row) {
                    throw new MyExceptionModel('主机修改失败');
                }
                list($status, $result) = by::partsSalesModel()->modify($id, $host_sku, $ret['part_info'], by::partsSalesModel()::TYPE['update']);
                if (!$status) {
                    throw new MyExceptionModel($result);
                }
            } else {
                $mainInfo = by::mainSalesModel()->getOneInfoByMainSku($ret['host_sku']);
                if ($mainInfo) {
                    return [false, '主机已存在'];
                }
                $data = [
                    'main_sku' => $ret['host_sku'],
                    'name'     => $ret['host_name'],
                    'ctime'    => intval(START_TIME),
                    'utime'    => intval(START_TIME)
                ];
                $row  = $db->createCommand()->insert($tb, $data)->execute();
                $id   = $db->getLastInsertID();
                if (!$row) {
                    throw new MyExceptionModel('添加失败');
                }
                //添加 配件
                list($status, $result) = by::partsSalesModel()->modify($id, $host_sku, $ret['part_info'], by::partsSalesModel()::TYPE['add']);
                if (!$status) {
                    throw new MyExceptionModel('配件添加失败');
                }
            }

            is_null($tran) && $new_tran->commit();

            $this->__delOneInfoByMainSku($ret['host_sku']);
            $this->__delListCache();
            by::mainSalesModel()->__delGetMainInfoByIdCache(explode(',', $id));
            by::partsSalesModel()->__delGetOneCache(explode(',', $id));
            return [true, '保存成功'];
        } catch (MyExceptionModel $_e) {

            is_null($tran) && $new_tran->rollBack();
            return [false, $_e->getMessage()];

        } catch (\Exception $_e) {

            is_null($tran) && $new_tran->rollBack();
            return [false, '保存失败'];

        }
    }

    /**
     * @throws RedisException
     */
    private function __delListCache()
    {
        $r_key = $this->__getMainSalesList();
        by::redis('core')->del($r_key);
    }


    /**
     * @throws Exception
     */
    protected function _modifyCheck($id, $host_sku, $input): array
    {
        $data = [];
        //添加
        if (empty($host_sku)) {
            return [false, '请选择主机'];
        }
        $mainInfo = by::Gmain()->GetOneBySku($host_sku) ?? [];
        if (!$mainInfo) {
            return [false, '主机不存在'];
        }
        if (!is_array($input) || !$input) {
            return [false, '请选择配件'];
        }

        //检验排序
        $sorts        = array_column($input, 'sort');
        $unique_sorts = array_unique($sorts);

        if (count($sorts) != count($unique_sorts)) {
            return [false, '排序有误,请检查'];
        }

        //检验sku
        $part_count       = count($input);
        $part_skus        = array_column($input, 'part_sku');
        $unique_part_skus = array_filter(array_unique($part_skus));

        if (count($part_skus) != count($unique_part_skus)) {
            return [false, '配件参数有误，请检查'];
        }
        if ($part_count != count($unique_part_skus)) {
            return [false, '配件参数有误，请检查'];
        }


        $data['host_sku']  = $host_sku;
        $data['host_name'] = $mainInfo['name'] ?? '';
        $data['part_info'] = $input;
        return [true, $data];
    }


    /**
     * @throws Exception
     * 获取main_id通过主机sku
     */
    public function getIdBySku($info, $type): array
    {
        switch ($type) {
            case self::HOST_TYPE:
                if ($info){
                    $info = by::Gspecs()->GetMainSkuBySpecsSku($info);
                }
                $input = [
                    'main_sku' => $info
                ];
                break;
            case self::PART_TYPE:
                $input = [
                    'part_sku' => $info
                ];
                break;
            default:
                $input = [];
        }

        list($where, $params) = by::partsSalesModel()->__getCondition($input);
        $tb    = self::tbName();
        $sql   = "SELECT `id` FROM {$tb} WHERE {$where}";
        $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();
        $ids   = array_column($aData, 'id');
        return $ids ?? [];
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取主机总条数
     */
    public function GetListCount($input = []): int
    {
        $r_key   = $this->__getMainSalesList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, json_encode($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if (!$count) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, rand(600, 900));
        }
        return intval($count);
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function GetList($input, $page = 1, $page_size = 50): array
    {
        $r_key = $this->__getMainSalesList();

        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, json_encode($input));

        $aJson = by::redis('core')->hGet($r_key, $sub_key);
        $aData = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `ctime` DESC,`id` DESC ";

            if ($page_size) {
                $sql .= " LIMIT {$offset},{$page_size}";
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }


    public function __getCondition($input): array
    {
        //字段解析
        $mainSku = trim($input['main_sku'] ?? '');
        $name    = trim($input['name'] ?? '');
        $tid     = trim($input['tid'] ?? '');

        //初始化查询
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;
        if (!empty($mainSku)) {
            $where               .= " AND `main_sku` =:main_sku";
            $params[":main_sku"] = $mainSku;
        }

        if (!empty($name)) {
            $where                .= " AND `name` LIKE :name";
            $params[":name"]       = "%{$name}%";
        }

        if (!empty($tid)) {
            $gids = by::Gtag()->getGidByTid($tid);

            // 使用 array_map 去除单引号并连接为逗号分隔的字符串
            $mainSku = implode("','", array_map(function ($gid) {
                return str_replace("'", '', by::Gmain()->GetOneByGid($gid)['sku'] ?? '');
            }, $gids));

            if (!empty($mainSku)) {
                $where .= " AND `main_sku` IN ('{$mainSku}')";
            }
        }


        return [$where, $params];
    }


    /**
     * @param array $ids
     * @param $tran
     * @return array
     * 删除
     */
    public function del(array $ids = [], $tran = null): array
    {
        $tb       = self::tbName();
        $data     = ['is_del' => self::IS_DEL['yes']];
        $db       = by::dbMaster();
        $result   = 0;
        $new_tran = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            $result = $db->createCommand()->update($tb, $data, ['id' => $ids])->execute();
            if (!$result) {
                return [false, '主机删除失败'];
            }
            list($status, $ret) = by::partsSalesModel()->del($ids);
            if (!$status) {
                return [false, '配件删除失败'];
            }
            is_null($tran) && $new_tran->commit();
            foreach ($ids as $id) {
                $mainInfo = $this->getMainInfoById($id);
                $main_sku = $mainInfo['main_sku'] ?? '';
                $this->__delOneInfoByMainSku($main_sku);
            }
            $this->__delListCache();
            $this->__delGetMainInfoByIdCache($ids);
            return [true, $result];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false, '删除失败'];
        }
    }


    /**
     * @param $main_sku
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function getOneInfoByMainSku($main_sku): array
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getOneInfoByMainSku($main_sku);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb}  WHERE `main_sku` =:main_sku AND `is_del` =:is_del LIMIT 1";
            $main   = by::dbMaster()->createCommand($sql, [":main_sku" => $main_sku, "is_del" => self::IS_DEL['no']])->queryOne();
            $aData  = empty($main) ? [] : $main;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }


}
