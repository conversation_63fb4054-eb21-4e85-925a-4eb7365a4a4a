<?php

namespace app\modules\main\models;


use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use RedisException;
use yii\db\Exception;

class MainPartModel extends CommModel
{
    /**
     * @return string
     */
    public static function tbName(): string
    {
        return "`db_dreame_goods`.`t_main_part`";
    }

    public $tb_fields = [
        'id', 'main_sku', 'part_sku','tied_sale', 'sort', 'status', 'is_del', 'ctime', 'utime'
    ];


    const IS_DEL = [
        'no'  => 0,
        'yes' => 1
    ];

    private function __getInfoByMainSku($sku): string
    {
        return AppCRedisKeys::getInfoByMainSku($sku);
    }

    /**
     * @throws RedisException
     * 清除详情数据
     */
    public function __delGetInfoByMainSku($skus)
    {
        $redis     = by::redis('core');
        $redis_key = [];
        foreach ($skus as $sku) {
            $redis_key[] = $this->__getInfoByMainSku($sku);
        }
        $redis->del($redis_key);
    }

    private function __getMainList(): string
    {
        return AppCRedisKeys::getMainList();
    }

    /**
     * @throws RedisException
     * 清除列表
     */
    public function __delGetMainList()
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getMainList();
        $redis->del($redis_key);
    }

    private function __getMainByPartSku($sku): string
    {
        return AppCRedisKeys::getMainByPartSku($sku);
    }

    /**
     * @throws RedisException
     */
    public function __delGetMainByPartSku($skus)
    {
        $redis     = by::redis('core');
        $redis_key = [];
        foreach ($skus as $sku) {
            $redis_key[] = $this->__getMainByPartSku($sku);
        }
        $redis->del($redis_key);
    }


    /**
     * @throws RedisException
     * @throws Exception
     * 通过main_sku获取详情
     */
    public function getInfoByMainSku($sku)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getInfoByMainSku($sku);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);

        if (!$aData) {
            $fields = implode("`,`", $this->tb_fields);
            $tb     = self::tbName();
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `main_sku` =:main_sku LIMIT 1";
            $aData  = by::dbMaster()->createCommand($sql, [':main_sku' => $sku])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        $part_sku  = $aData['part_sku'] ?? '';
        $part_skus = explode(',', $part_sku);

        //处理搭售数据
        $tied_sale = json_decode(empty($aData['tied_sale']) ? '' : $aData['tied_sale'], true) ?? [];
        $tied_skus = [];
        foreach ($tied_sale as $tied_sku){
           if ($tied_sku['is_tied'] ?? ''){
               $tied_skus[] = $tied_sku['part_sku'];
           }
        }

        //普通配件
        $partInfo = [];
        foreach ($part_skus as $value) {
            $info = by::Gmain()->GetOneBySku($value);

            if ($info && in_array($value,$tied_skus)){
                $info['tied_check'] = 1;
            }elseif ($info && !in_array($value,$tied_skus)){
                $info['tied_check'] = 0;
            }
            $info && $info['part_check'] = 1;
            if (isset($info['is_del']) && $info['is_del'] == 1) {
                continue;
            } else {
                $partInfo[] = $info;
            }
        }


        if ($aData) {
            $main               = by::Gmain()->GetOneBySku($aData['main_sku'] ?? '') ?? [];
            $aData['main_name'] = $main['name'] ?? '';
            $partInfo ? $aData['info'] = array_filter(array_values($partInfo)) : $aData['info'] = [];
        } else {
            $aData['info'] = [];
        }

        return $aData;
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 保存修改数据
     */
    public function saveLog($sku, $part_sku,$tied_sale): array
    {
        $info = array_filter($this->getInfoByMainSku($sku));
        $db = by::dbMaster();
        $tb = self::tbName();

        $data = [
            'main_sku' => $sku,
            'part_sku' => $part_sku,
            'tied_sale'=>$tied_sale,
            'utime' => intval(START_TIME),

        ];
        if ($info) {
            $result = $db->createCommand()->update($tb, $data, ['main_sku' => $sku])->execute();
        } else {
            $data['ctime'] = intval(START_TIME);
            $result        = $db->createCommand()->insert($tb, $data)->execute();
        }
        if ($result) {
            $this->__delGetMainList();
            $this->__delGetInfoByMainSku(explode(',', (string)$sku));
            $this->__delGetMainByPartSku(explode(',', $part_sku));
            return [true, '保存成功'];
        } else {
            return [false, '保存失败'];
        }
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function GetListCount($input = []): int
    {
        $r_key   = $this->__getMainList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, json_encode($input));
        $count   = by::redis('core')->hGet($r_key, $sub_key);
        if ($count === false) {
            $tb = $this->tbName();
            list($where, $params) = $this->__getCondition($input);
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $params)->queryScalar();
            $count = empty($count) ? 0 : $count;

            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key, rand(600, 900));
        }
        return intval($count);
    }

    private function __getCondition($input): array
    {
        //字段解析
        $mainSku = trim($input['main_sku'] ?? '');

        //初始化查询
        $where             = "is_del=:is_del";
        $params[':is_del'] = 0;
        if (!empty($mainSku)) {
            $where               .= " AND `main_sku` =:main_sku";
            $params[":main_sku"] = $mainSku;
        }

        return [$where, $params];
    }


    /**
     * @throws RedisException
     * @throws Exception
     */
    public function getList($input = [], $page = 1, $page_size = 50): array
    {
        $r_key = $this->__getMainList();

        $sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size, json_encode($input));
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = $this->tbName();
            list($offset) = CUtil::pagination($page, $page_size);
            list($where, $params) = $this->__getCondition($input);

            $sql = "SELECT `main_sku` FROM {$tb} WHERE {$where} ORDER BY `utime` DESC,`id` DESC ";

            if ($page_size) {
                $sql .= " LIMIT {$offset},{$page_size}";
            }

            $aData = by::dbMaster()->createCommand($sql, $params)->queryAll();

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, rand(600, 900));
        }

        return empty($aData) ? [] : array_column($aData, "main_sku");
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 通过part_sku获取主机sku
     */
    public function getMainByPartSku($sku)
    {
        $redis     = by::redis('core');
        $redis_key = $this->__getMainByPartSku($sku);
        $aJson     = $redis->get($redis_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aData) {
            $tb    = $this->tbName();
            $sql   = "SELECT `main_sku` FROM {$tb} WHERE FIND_IN_SET('$sku',`part_sku`) ";
            $aData = by::dbMaster()->createCommand($sql, [":sku" => $sku])->queryAll();
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }
        return $aData ?? [];
    }


}
