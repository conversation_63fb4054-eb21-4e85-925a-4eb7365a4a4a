<?php

namespace app\modules\main\models;


use app\models\by;

class UserPopupModel extends CommModel
{
    public static function tableName(): string
    {
        return '`db_dreame`.`t_user_popup`';
    }

    public $fields = [
            'id', 'user_id', 'popup_type', 'is_displayed', 'ctime', 'utime','remark'
    ];

    const IS_DISPLAYED = [
            'NO'  => '0',
            'YES' => '1'
    ];


    const POPUP_TYPE = [
            'GROUP_PURCHASES'    => 1, // 拼团-成为团长
            'MEMBER_MALL_INVITE' => 2, // 会员商城邀请好友
            'INVITE_FRIEND_BUY'  => 3, // 好友购买积分到账
            'BECOME_AMBASSADOR'  => 4, // 成为追觅大使
    ];

    public function GetPopupInfo($params): array
    {
        // 基本的 SQL 查询语句
        $sql = "SELECT * FROM `t_user_popup` WHERE 1=1";
        $queryParams = [];

        // 检查是否传递了 user_id，并动态构造 SQL 和参数
        if (!empty($params['user_id'])) {
            $sql .= " AND `user_id` = :user_id";
            $queryParams[':user_id'] = $params['user_id'];
        }

        // 检查是否传递了 popup_type，并动态构造 SQL 和参数
        if (!empty($params['popup_type'])) {
            $sql .= " AND `popup_type` = :popup_type";
            $queryParams[':popup_type'] = $params['popup_type'];
        }

        // 执行查询
        $queryData = by::dbMaster()->createCommand($sql, $queryParams)->queryOne();

        // 返回查询结果，如果为空则返回空数组
        return $queryData !== false ? $queryData : [];
    }

    public function savePopup($userId, $popupType,$remark='',$onlyOne=false): bool
    {
        $currentTime = time();

        // 检查是否已有相同记录，防止重复插入
        if ($onlyOne){
            $existingRecord = self::find()
                    ->where(['user_id' => $userId, 'popup_type' => $popupType, 'is_displayed' => self::IS_DISPLAYED['NO']])
                    ->one();

            if ($existingRecord) {
                return true; // 如果记录已存在，直接返回 true
            }
        }

        // 插入新记录
        $result = by::dbMaster()->createCommand()->insert(self::tableName(), [
                'user_id'      => $userId,
                'popup_type'   => $popupType,
                'is_displayed' => self::IS_DISPLAYED['NO'],
                'remark'       => $remark,
                'ctime'        => $currentTime,
                'utime'        => $currentTime
        ])->execute();

        // 根据插入结果返回 true 或 false
        return $result > 0;
    }

    /**
     * 根据类型获取用户未弹出的弹窗
     * @param int $userId 用户ID
     * @param int $popupType 弹窗类型，0表示获取全部类型
     * @param bool $autoMarkAsRead 是否自动标记为已读，默认false
     * @return array 返回未弹出的弹窗列表
     */
    public function getUnshownPopupsByType(int $userId, int $popupType, bool $autoMarkAsRead = false): array
    {
        if (empty($userId)) {
            return [];
        }

        // 构建查询条件
        $whereCondition = ['user_id' => $userId, 'is_displayed' => self::IS_DISPLAYED['NO']];

        // 如果 popupType 不为0，则添加类型过滤条件
        if ($popupType > 0) {
            $whereCondition['popup_type'] = $popupType;
        }

        // 查询未弹出的弹窗
        $query = self::find()
            ->where($whereCondition)
            ->asArray()
            ->all();

        // 如果设置了自动标记为已读，且有未弹出的弹窗
        if ($autoMarkAsRead && !empty($query)) {
            $popupIds = array_column($query, 'id');
            $this->markPopupsAsDisplayed($popupIds);

            // 更新返回数据中的 is_displayed 状态
            foreach ($query as &$popup) {
                $popup['is_displayed'] = self::IS_DISPLAYED['YES'];
                $popup['utime'] = time();
            }
        }

        return $query;
    }

    /**
     * 手动标记弹窗为已展示
     * @param int $userId 用户ID
     * @param int $popupType 弹窗类型
     * @param string $remark 备注信息
     * @return bool 返回操作结果
     */
    public function markPopupAsDisplayed(int $userId, int $popupType, string $remark = ''): bool
    {
        if (empty($userId) || empty($popupType)) {
            return false;
        }

        $currentTime = time();
        $updateData = [
            'is_displayed' => self::IS_DISPLAYED['YES'],
            'utime' => $currentTime
        ];

        // 如果提供了备注信息，则更新备注
        if (!empty($remark)) {
            $updateData['remark'] = $remark;
        }

        // 更新匹配条件的记录
        $result = by::dbMaster()->createCommand()->update(
            self::tableName(),
            $updateData,
            [
                'user_id' => $userId,
                'popup_type' => $popupType,
                'is_displayed' => self::IS_DISPLAYED['NO']
            ]
        )->execute();

        return $result > 0;
    }

    /**
     * 批量标记弹窗为已展示（内部方法）
     * @param array $popupIds 弹窗ID数组
     * @return bool 返回操作结果
     */
    private function markPopupsAsDisplayed(array $popupIds): bool
    {
        if (empty($popupIds)) {
            return false;
        }

        $currentTime = time();
        $result = by::dbMaster()->createCommand()->update(
            self::tableName(),
            [
                'is_displayed' => self::IS_DISPLAYED['YES'],
                'utime' => $currentTime
            ],
            ['in', 'id', $popupIds]
        )->execute();

        return $result > 0;
    }


    public function GetPopupData($params): array
    {
        $tableName = self::tableName();
        // 基本的 SQL 查询语句
        $sql         = "SELECT * FROM {$tableName} WHERE 1=1";
        $queryParams = [];

        // 检查是否传递了 user_id，并动态构造 SQL 和参数
        if (!empty($params['user_id'])) {
            $sql                     .= " AND `user_id` = :user_id";
            $queryParams[':user_id'] = $params['user_id'];
        }

        // 检查是否传递了 popup_type，并动态构造 SQL 和参数
        if (!empty($params['popup_type'])) {
            $sql                        .= " AND `popup_type` = :popup_type";
            $queryParams[':popup_type'] = $params['popup_type'];
        }

        // 执行查询
        $queryData = by::dbMaster()->createCommand($sql, $queryParams)->queryAll();

        // 返回查询结果，如果为空则返回空数组
        return empty($queryData) ? [] : $queryData;
    }


}
