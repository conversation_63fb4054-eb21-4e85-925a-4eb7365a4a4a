<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/10
 * Time: 11:56
 */
namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use yii\db\DataReader;
use yii\db\Exception;

class PmarketModel extends CommModel {

    public static function tbName(): string
    {
        return "`db_dreame`.`t_p_m`";
    }

    /**
     * @param $p_id
     * @return string
     */
    private function __getMIdsByPid($p_id): string
    {
        return AppCRedisKeys::getMIdsByPid($p_id);
    }

    private function __getPMById($id): string
    {
        return AppCRedisKeys::getPMById($id);
    }

    /**
     * @param int $p_id
     * @param int $id
     */
    private function __delCache($p_id=0,$id=0)
    {
        $r_key1 = $this->__getPMById($id);
        $r_key2 = $this->__getMIdsByPid($p_id);
        by::redis('core')->del($r_key1,$r_key2);
    }


    CONST MAX_COUNT = 3;

    /**
     * @param $p_id
     * @param $mc_id
     * @param $stock
     * @param null $tran
     * @return array
     * @throws \yii\db\Exception
     * @throws \yii\db\StaleObjectException
     */
    public function add($p_id,$mc_id,$stock,$tran = null){
        $p_id       = CUtil::uint($p_id);
        $mc_id      = CUtil::uint($mc_id);
        $stock      = CUtil::uint($stock);
        $ctime      = intval(START_TIME);

        if(empty($p_id) || empty($mc_id)){
            return [false,'缺少参数'];
        }

        // $ids = $this->getIdsByPid($p_id);
        // if (count($ids) > self::MAX_COUNT-1){
        //     return [false,'最多绑定'.self::MAX_COUNT.'张优惠券'];
        // }

        $market = by::marketConfig()->getOneById($mc_id);
        if (empty($market)){
            return [false,'优惠券不存在'];
        }

        $tb     = $this->tbName();
        $db     = by::dbMaster();
        $new_tran   = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            $data  = [
                'p_id'        => $p_id,
                'mc_id'       => $mc_id,
                'stock'       => $stock,
                'ctime'       => $ctime,
            ];

            $row    = $db->createCommand()->insert($tb, $data)->execute();

            $id = $db->getLastInsertID();
            if (!$row){
                throw new MyExceptionModel('添加优惠券失败');
            }

            //扣除资源库存
            list($status, $msg) = by::marketConfig()->stockModify($mc_id, $stock);
            if (!$status) {
                throw new MyExceptionModel($msg);
            }
            is_null($tran) && $new_tran->commit();
            $this->__delCache($p_id);
            return [true,$this->getOneById($id)];
        }catch (MyExceptionModel $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false,'添加优惠券失败，请检查库存是否充足'];
        }
    }

    public function edit($id,$p_id,$mc_id,$stock,$tran = null){
        $id         = CUtil::uint($id);
        $p_id       = CUtil::uint($p_id);
        $mc_id      = CUtil::uint($mc_id);
        $stock      = CUtil::uint($stock);

        if (empty($p_id) || empty($id) ||empty($mc_id)){
            return [false,'缺少参数'];
        }

        $info = $this->getOneById($id);
        if (empty($info)){
            return [false,'数据不存在'];
        }

        $market = by::marketConfig()->getOneById($mc_id);
        if (empty($market)){
            return [false,'优惠券不存在'];
        }

        if ($info['mc_id'] == $mc_id && $stock == $info['stock']){
            return [true,'修改成功'];
        }

        $tb     = $this->tbName();
        $db     = by::dbMaster();
        $new_tran   = is_null($tran) ? $db->beginTransaction() : $tran;
        try {
            $data  = [
                'mc_id'       => $mc_id,
                'stock'       => $stock,
            ];

            if ($info['mc_id'] != $mc_id){
                $data['sales'] = 0;//更改优惠券 发放数量归0
            }

            //修改前回退旧的资源库存
            if($info['stock'] > 0) {
                list($status, $msg) = by::marketConfig()->stockModify($info['mc_id'], -($info['stock']));
                if (!$status) {
                    throw new MyExceptionModel($msg);
                }
            }

            $row = $db->createCommand()->update($tb,$data,"`id`=:id",[":id"=>$id])->execute();
            if (!$row){
                throw new MyExceptionModel('修改优惠券失败');
            }

            //扣除资源库存
            list($status, $msg) = by::marketConfig()->stockModify($mc_id, $stock);
            if (!$status) {
                throw new MyExceptionModel($msg);
            }

            is_null($tran) && $new_tran->commit();
            $this->__delCache($p_id,$id);
            return [true,$this->getOneById($id)];
        }catch (MyExceptionModel $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            is_null($tran) && $new_tran->rollBack();
            return [false,'修改优惠券失败，请检查库存是否充足'];
        }
    }


    public function getIdsByPid($p_id){
        $p_id = CUtil::uint($p_id);
        if(empty($p_id)) {
            return [false,'pid不能为空'];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getMIdsByPid($p_id);
        $aJson       = $redis->get($redis_key);
        $ids        = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            // $count   = self::MAX_COUNT;
            // $sql     = "SELECT `id` FROM  {$tb} WHERE `p_id`=:p_id ORDER BY `id` asc LIMIT {$count}";
            $sql     = "SELECT `id` FROM  {$tb} WHERE `p_id`=:p_id ORDER BY `id` asc";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":p_id", $p_id);
            $ids     = $command->queryAll();
            $ids     = empty($ids) ? [] : array_column($ids,'id');

            $redis->set($redis_key, json_encode($ids),3600);
        }

        return [true,$ids];
    }

    public function getOneById($id){
        $id = CUtil::uint($id);
        if(empty($id)) {
            return [];
        }
        $redis       = by::redis('core');
        $redis_key   = $this->__getPMById($id);
        $aJson       = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson,true);
        if ($aJson === false) {
            $tb      = $this->tbName();
            $sql     = "SELECT * FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":id", $id);
            $aData     = $command->queryOne();
            $redis->set($redis_key, json_encode($aData),3600);
        }

        return $aData;
    }

    public function getListByPid($p_id){
        $p_id = CUtil::uint($p_id);
        if(empty($p_id)) {
            return [];
        }

        list(,$ids) = $this->getIdsByPid($p_id);
        if (empty($ids)){
            return [];
        }
        $list = [];
        foreach ($ids as $id){
            $info   = $this->getOneById($id);
            if (!empty($info)){
                $market = by::marketConfig()->getOneById($info['mc_id']);
                $info['name'] = $market['name']??'';
                $list[] = $info;
            }
        }
        return $list;
    }

    public function del($id){
        $id  = CUtil::uint($id);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }

        $info = $this->getOneById($id);
        if(empty($info)){
            return [false, '产品不存在'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        try {

            $tb     = self::tbName();
            $sql    = "DELETE FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $row    = by::dbMaster()->createCommand($sql,[':id'=>$id])->execute();

            if(!$row){
                throw new \Exception('删除pm失败');
            }

            //修改后回退旧的资源库存
            if($info['stock'] > 0) {
                list($status, $msg) = by::marketConfig()->stockModify($info['mc_id'], -$info['stock']);
                if (!$status) {
                    throw new \Exception($msg);
                }
            }

            $transaction->commit();

            $this->__delCache($info['p_id'],$id);
            return [true, $info];

        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);
            return [false, $e->getMessage()];
        }
    }


    /**
     * @param $pid
     * @param $id
     * @param $num
     * @return array
     * 库存修改
     */
    public function stockModify($pid,$id,$num){

        $tb     = self::tbName();

        $sql    = "UPDATE {$tb} SET stock = stock - (:num), sales = sales + (:num_1) 
                        WHERE id = :id AND stock - (:num_2) >= 0";
        $params = [':num' => $num, ':id' => $id, ':num_1' =>$num, ':num_2' =>$num];
        $u_num  = by::dbMaster()->createCommand($sql, $params)->execute();

        if ($u_num == 0) {
            return [false, '库存不够!'];
        }

        $this->__delCache($pid,$id);
        return [true, 'ok'];

    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除活动配置
     */
    public function deleteData($id)
    {
        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $data = self::find()->where(['id' => $id, 'is_delete' => 0])->one();
        if (empty($data)) {
            return [false, '活动不存在'];
        }

        $transaction = by::dbMaster()->beginTransaction();
        try {
            $data->update_time = time();
            $data->is_delete = 1;
            $data->update();

            //修改后回退旧的资源库存
            if($data->surplus_num > 0) {
                list($status, $msg) = by::marketConfig()->stockModify($data->market_id, -($data->surplus_num));
                if (!$status) {
                    throw new \Exception($msg);
                }
            }

            $transaction->commit();
            //删除缓存
            $this->delCache($id, $data->grant_type);
            return [true, '操作成功'];
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            trigger_error($error);
            return [false, $e->getMessage()];
        }
    }
}