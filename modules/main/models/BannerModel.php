<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use RedisException;
use yii\db\ActiveQuery;
use yii\db\Exception;

/**
 * banner
 * @auth link
 * @date 2022-2-14
 */
class BannerModel extends CommModel {

    CONST JUMP_TYPE  = [ //跳转类型
        'URL'       => 1, //跳转链接
        'MOVING'    => 2, //商城动态
        'MINI_URL'  => 3, //小程序链接
        'MINI_APPID'=> 4, //其他小程序链接
    ];

    CONST BANNER_STATUS    = [ //最终状态
        'ALREADY'   => ['CODE'=>1,'NAME'=>'已发布'],
        'WAIT'      => ['CODE'=>0,'NAME'=>'未发布'],
        'ING'       => ['CODE'=>2,'NAME'=>'发布中'],
    ];

    CONST STATUS     = [ //审核状态
        'PASS'      => ['CODE'=>1,'NAME'=>'审核通过'],
        'WAIT'      => ['CODE'=>0,'NAME'=>'未审核'],
        'ING'       => ['CODE'=>2,'NAME'=>'审核中'],
        'NOT'       => ['CODE'=>3,'NAME'=>'审核不通过'],
    ];

    CONST DELETE    = [ //删除
        'YES'   => ['CODE'=>1,'NAME'=>'已删除'],
        'NO'    => ['CODE'=>0,'NAME'=>'未删除'],
    ];

    CONST ALL_PLATFORM_TAG = 3;

    CONST MAX = 100;

    CONST BANNER_EXPIRE_TIME = 81600;

    public $tb_fields = [
        'id', 'admin_id', 'title', 'image', 'new_image', 'jump_type', 'jump_url', 'appid', 'sort', 'vtime_start', 'vtime_end', 'ctime', 'status', 'is_delete', 'video', 'platform', 'drop_position', 'banner_version',
    ];

    public static function tbName(): string
    {
        return  "`db_dreame`.`t_banner`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    /**
     * @param $id
     * @return string
     * banner唯一数据缓存KEY
     */
    private function __getOneBanner($id): string
    {
        return AppCRedisKeys::getBannerById($id);
    }

    /**
     * @return string
     * banner哈希列表
     */
    private function __getBannerList(): string
    {
        return AppCRedisKeys::getBannerList();
    }

    /**
     * @param $id
     * @return int
     * 缓存清理
     */
    public function __delCache($id): int
    {
        $key1 = $this->__getOneBanner($id);
        $key2  = $this->__getBannerList();
        return  by::redis('core')->del($key1,$key2);
    }

    /**
     * 获取banner列表（客户端）
     */
    public function getBannerList($platformId, $dropPosition = 1, $bannerVersion = 1): array
    {
        $now       = intval(START_TIME);
        $nowEnd    = $now + self::BANNER_EXPIRE_TIME + 200;
        $status    = self::STATUS['PASS']['CODE'];
        $is_delete = self::DELETE['NO']['CODE'];

        $r_key   = $this->__getBannerList();
        $sub_key = CUtil::getAllParams(__FUNCTION__, $platformId, $dropPosition, $bannerVersion);
        $aJson   = by::redis('core')->hGet($r_key, $sub_key);
        $aData   = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $query = self::find()
                ->select(['id', 'title', 'image', 'new_image', 'video', 'jump_type', 'jump_url', 'drop_position', 'appid', 'banner_version', 'vtime_start', 'vtime_end'])
                ->where(['status' => $status, 'is_delete' => $is_delete, 'drop_position' => $dropPosition, 'banner_version' => $bannerVersion])
                ->andWhere(['<', 'vtime_start', $nowEnd])
                ->andWhere(['>', 'vtime_end', $now])
                ->orderBy(['sort' => SORT_DESC, 'id' => SORT_DESC]);

            if (!empty($platformId)) {
                $bannerIds = byNew::BannerPlatformModel()::find()
                    ->select('banner_id')
                    ->where(['platform_id' => $platformId])
                    ->column();

                $query->andWhere(['id' => $bannerIds]);
            }

            $aData = $query->asArray()->all();

            $aData = empty($aData) ? [] : $aData;

            by::redis('core')->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key, self::BANNER_EXPIRE_TIME);
        }

        return $aData;
    }

    /**
     * 管理后台 - banner列表
     * @param $post
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws RedisException
     */
    public function getList($post, int $page = 1, int $page_size = 20): array
    {
        $r_key   = $this->__getBannerList();                             // 获取Redis缓存键名
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($post));  // 获取子键名
        $redis   = by::redis('core');                               // 获取Redis实例
        $aJson   = $redis->hGet($r_key, $sub_key);                       // 从Redis中获取JSON数据
        $aData   = json_decode($aJson, true);                  // 解析JSON数据为数组

        if (!$aData) {
            list($offset) = CUtil::pagination($page, $page_size); // 分页偏移量计算
            $query = self::find();                                // 创建查询构建器

            // 应用条件并获取查询构建器
            $query = $this->__getCondition($query, $post);

            // 应用排序、偏移和限制条件
            $query->orderBy(['sort' => SORT_DESC, 'id' => SORT_DESC]) // 排序方式
            ->offset($offset)                                         // 偏移量
            ->limit($page_size);                                      // 查询条数限制

            // 执行查询以获取ID列表
            $idList = $query->select('id')->asArray()->column();
            $aData  = empty($idList) ? [] : $idList;

            // 将查询结果存入Redis缓存
            $redis->hSet($r_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($r_key); // 重置缓存过期时间
        }

        return $aData;
    }


    /**
     * 管理后台查询banner列表总数
     * @param $post
     * @return int
     * @throws RedisException
     */
    public function getCount($post): int
    {
        $r_key   = $this->__getBannerList();                            // 获取Redis缓存键名
        $sub_key = CUtil::getAllParams(__FUNCTION__, serialize($post)); // 获取子键名
        $count   = by::redis('core')->hGet($r_key, $sub_key);           // 从Redis中获取计数

        if ($count === false) {
            $query = self::find();                                        // 创建查询构建器

            // 应用条件并获取查询构建器
            $query = $this->__getCondition($query, $post);

            // 查询符合条件的记录数
            $count = $query->count();

            // 将计数结果存入Redis缓存
            by::redis('core')->hSet($r_key, $sub_key, $count);
            CUtil::ResetExpire($r_key);                                // 重置缓存过期时间
        }

        return intval($count); // 返回整数类型的计数
    }


    /**
     * @param $id
     * @param $save
     * @return array
     */
    public function saveBanner($id, $save): array
    {
        if ($id) {
            // 编辑
            $banner = self::findOne($id);
            if (!$banner) {
                return [false, "banner图不存在"];
            }
        } else {
            // 新增
            $banner = new self();
        }

        // 设置属性并保存  使用修改器
        foreach ($save as $attribute => $value) {
            $banner->$attribute = $value;
        }

        if (!$banner->save()) {
            return [false, '保存失败'];
        }

        // 清除缓存
        $this->__delCache($id);

        return [true, $banner->attributes]; // 返回保存成功和保存后的数据
    }



    /**
     * @param int $id
     * @return array
     * 判断已发布数总数
     */
    public function checkPublishTotal($id = 0)
    {
        $tb     = $this->tbName();
        $time   = time();
        $where  = "`status` = 1";
        $where .= " AND `is_delete` = 0";
        $where .= " AND `vtime_start` < {$time}";
        $where .= " AND `vtime_end` > {$time}";
        if($id){
            $where .= " AND `id` <> {$id}";
        }

        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $count               = by::dbMaster()->createCommand($sql)->queryScalar();

        if($count >= self::MAX){
            $msg  = sprintf('已发布个数最多为%s', self::MAX);
            return [false, $msg];
        }

        return [true, $count];
    }

    /**
     * @param $id
     * @return array
     * @throws RedisException
     * 获取指定banner
     */
    public function GetOneById($id): array
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis     = by::redis('core');
        $redis_key = $this->__getOneBanner($id);
        $aData     = (array)json_decode($redis->get($redis_key), true);

        if (empty($aData)) {
            $fields = $this->tb_fields;
            $delete = self::DELETE['NO']['CODE'];

            $aData = self::find()
                ->select($fields)
                ->where(['id' => $id, 'is_delete' => $delete])
                ->asArray()
                ->one();

            $aData = $aData ?: [];

            // 设置 drop_position 属性，如果它存在则转换为整数，不存在则设置为 0
            $aData['drop_position'] = isset($aData['drop_position']) ? (int)$aData['drop_position'] : 0;


            $redis->set($redis_key, json_encode($aData));
            CUtil::ResetExpire($redis_key);
        }

        //获得最终状态（已发布，未发布，发布中）
        $now     = time();
        $bStatus = self::BANNER_STATUS['WAIT']['CODE'];

        if ($aData['status'] == self::STATUS['PASS']['CODE']) {
            $bStatus = ($now > $aData['vtime_start'] && $now < $aData['vtime_end']) ?
                self::BANNER_STATUS['ING']['CODE'] :
                ($now > $aData['vtime_end'] ? self::BANNER_STATUS['ALREADY']['CODE'] : $bStatus);
        }

        $aData['b_status'] = (string)$bStatus;


        return $aData;
    }

    /**
     * @param $id
     * @param $check 1通过 3未通过
     * @return array
     * 审核通过，审核未通过
     */
    public function audit($id,$check){
        $id     = CUtil::uint($id);
        $check  = CUtil::uint($check);

        if(empty($id)){
            return [false, 'id不能为空'];
        }

        $bannerData = $this->GetOneById($id);
        if(empty($bannerData)){
            return [false, 'banner不存在'];
        }

        if($check == 1){
            list($ret,$msg) = $this->checkPublishTotal($id);
            if(!$ret){
                return [false, $msg];
            }
        }

        if($check != self::STATUS['PASS']['CODE'] && $check != self::STATUS['NOT']['CODE']){
            return [false, '提交的审核状态有误'];
        }
        if($bannerData['status'] != self::STATUS['ING']['CODE']){
            return [false, '不是审核中的banner不能操作'];
        }

        $tb     = self::tbName();
        $row = by::dbMaster()->createCommand()->update($tb,['status'=>$check,'update_time'=>time()],"`id`=:id",[":id"=>$id])->execute();

        if(!$row){
            return [true, '失败'];
        }

        $this->__delCache($id);
        return [true, '操作成功'];
    }

    /**
     * @param $id
     * @return array
     * 删除banner
     */
    public function del($id)
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $bannerData = $this->GetOneById($id);
        if(empty($bannerData)){
            return [false, 'banner不存在'];
        }

        $tb     = self::tbName();
        $row = by::dbMaster()->createCommand()->update($tb,['is_delete'=>self::DELETE['YES']['CODE']],"`id`=:id",[':id'=>$id])->execute();

        if(!$row){
            return [true, '失败'];
        }

        $this->__delCache($id);
        return [true, '操作成功'];
    }

    /**
     * @param $id
     * @param $admin
     * @param $check :0撤销 2提交
     * @return array
     * 提交审核、撤销审核
     */
    public function subAudit($id,$check,$admin)
    {
        $id     = CUtil::uint($id);
        $check  = CUtil::uint($check);

        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $bannerData = $this->GetOneById($id);
        if(empty($bannerData)){
            return [false, 'banner不存在'];
        }

        if ($admin['id'] != $bannerData['admin_id']){
            return [false,'只有创建人有权操作'];
        }

        if ($check == self::STATUS['WAIT']['CODE']){
            if($bannerData['status'] != self::STATUS['ING']['CODE']){
                return [false, '不是审核中的banner不能撤销'];
            }
        }elseif ($check == self::STATUS['ING']['CODE']){
            if($bannerData['status'] != self::STATUS['WAIT']['CODE']){
                return [false, '不是未审核的banner不能提交审核'];
            }
            //判断权限
            $check_url    = 'back/banner/audit'; //资源审核权限
            if (by::adminUserModel()->checkAuth($admin,$check_url)) {
                $check   = self::STATUS['PASS']['CODE'];
            }
        }else{
            return [false, '提交的审核状态有误'];
        }

        $tb     = self::tbName();
        $row = by::dbMaster()->createCommand()->update($tb,['status'=>$check,'update_time'=>time()],"`id`=:id",[':id'=>$id])->execute();

        if(!$row){
            return [true, '失败'];
        }

        $this->__delCache($id);
        return [true, '操作成功'];
    }



    /**
     * @param $id
     * @return array
     * banner排序
     */
    public function sort($id,$sort)
    {
        $id = CUtil::uint($id);
        $sort = CUtil::uint($sort);
        if (empty($id)) {
            return [false, 'id不能为空'];
        }
        $bannerData = $this->GetOneById($id);
        if(empty($bannerData)){
            return [false, 'banner不存在'];
        }

        $tb     = self::tbName();
        $row = by::dbMaster()->createCommand()->update($tb,['sort'=>$sort],"`id`=:id",[":id"=>$id])->execute();

        if(!$row){
            return [true, '失败'];
        }

        $this->__delCache($id);
        return [true, '操作成功'];
    }

    /**
     * @param $query
     * @param $post
     * @return ActiveQuery
     * 规范化查询条件
     */
    private function __getCondition($query, $post): ActiveQuery
    {
        $title        = $post['title'] ?? "";
        $status       = $post['status'] ?? -1;
        $b_status     = $post['b_status'] ?? -1;
        $ctime_start  = $post['vtime_start'] ?? 0;
        $ctime_end    = $post['vtime_end'] ?? 0;
        $platform     = $post['platform'] ?? 0;
        $dropPosition = $post['drop_position'] ?? 0;

        $query->andWhere(['is_delete' => 0]); // 添加基础条件

        if (!empty($title)) {
            $query->andWhere(['like', 'title', $title]); // 标题模糊匹配条件
        }

        if ($status > -1) {
            $query->andWhere(['status' => $status]); // 状态匹配条件
        }

        if ($b_status > -1) {
            $now = time();
            if ($b_status == self::BANNER_STATUS['ALREADY']['CODE']) {
                // 已发布且已过期条件
                $query->andWhere(['status' => self::STATUS['PASS']['CODE']])
                    ->andWhere(['<', 'vtime_end', $now]);
            } elseif ($b_status == self::BANNER_STATUS['ING']['CODE']) {
                // 发布中条件
                $query->andWhere(['status' => self::STATUS['PASS']['CODE']])
                    ->andWhere(['<', 'vtime_start', $now])
                    ->andWhere(['>', 'vtime_end', $now]);
            } elseif ($b_status == self::BANNER_STATUS['WAIT']['CODE']) {
                // 未发布条件
                $query->andWhere(['or',
                    ['<>', 'status', self::STATUS['PASS']['CODE']],
                    ['>', 'vtime_start', $now]
                ]);
            }
        }

        if ($ctime_start && $ctime_end) {
            $query->andWhere(['between', 'ctime', CUtil::uint($ctime_start), CUtil::uint($ctime_end)]); // 创建时间范围条件
        }

        if ($platform) {
            // 如果平台是4（原有平台99），则将其转换为数组[1, 2]
            $platform = $platform == byNew::PlatformModel()::PLATFORM['WX_AND_APP'] ? [byNew::PlatformModel()::PLATFORM['WX'], byNew::PlatformModel()::PLATFORM['APP']] : (array)$platform;

            // 查找与指定平台相关的 banner_id
            $bannerIds = byNew::BannerPlatformModel()::find()
                ->select('banner_id')
                ->where(['platform_id' => $platform])
                ->column();

            // 将找到的 banner_id 应用到查询条件中
            $query->andWhere(['id' => $bannerIds]);
        }

        if ($dropPosition) {
            $query->andWhere(['drop_position' => $dropPosition]); // 投放位置条件
        }

        return $query; // 返回构建好的查询对象
    }


}
