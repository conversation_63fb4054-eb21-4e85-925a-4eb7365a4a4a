<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use yii\db\Expression;

/**
 * 用户提现记录
 */
class UserWithdrawRecordModel extends CommModel
{

    public static function tbName(): string
    {
        return "`db_dreame`.`user_withdraw_record`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }
    
    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 主表增改
     */
    public function SaveLog(array $aData)
    {
        $db     = by::dbMaster();
        $tb     = $this->tbName();
        // 保存主表数据
        return  $db->createCommand()->insert($tb,$aData)->execute();
    }

    public function getInfo($id){
        return self::find()->where(['id' => $id])->asArray()->one();
    }

    public function patch($id,$field,$value){
        $tb = $this->tableName();
        $updateData = [
            $field => $value,
            'utime' => time()
        ];
        $status = by::dbMaster()->createCommand()->update($tb,$updateData, ['id'=>$id])->execute();
        return $status;
    }

    public function getList($user_id,$status,$page,$pageSize=10)
    {
        $params = [];
        if ($status != -1){
            $params['status'] = $status;
        }
        if ($user_id){
            $params['user_id'] = $user_id;
        }
        if (count($params) > 0 ){
            return self::find()->where($params)->offset(($page - 1) * $pageSize)->limit($pageSize)->orderBy('ctime asc')->asArray()->all();
        }else{
            return self::find()->offset(($page - 1) * $pageSize)->limit($pageSize)->orderBy('ctime asc')->asArray()->all();
        }
        
    }

    public function getCount($user_id,$status){
        $params = [];
        if ($status != -1){
            $params['status'] = $status;
        }
        if ($user_id){
            $params['user_id'] = $user_id;
        }
        if (count($params) > 0 ){
            return self::find()->where($params)->count();
        }else{
            return self::find()->count();
        }
        
    }


}