<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\CUtil;
use yii\db\Expression;

/**
 * 用户员工表
 */
class UserEmployeeModel extends CommModel
{
    // 是否黑名单
    const IS_BLACKLIST = [
        'YES' => 1,
        'NO'  => 2
    ];

    // 员工状态
    const EMPLOYEE_STATUS = [
        'NORMAL' => 1, // 正常
        'STOP'   => 2  // 停用
    ];

    // 是否提示
    const IS_TIP = [
        'NO'  => 0,
        'YES' => 1
    ];

    public static function tbName(): string
    {
        return "`db_dreame`.`t_user_employee`";
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    // 获取员工列表
    public function getEmployeeList(array $params = [], int $page = 1, int $pageSize = 10): array
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->select(['*'])
            ->where($condition)
            ->offset(($page - 1) * $pageSize)
            ->limit($pageSize)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
    }
    public function getEmployeeAll(array $params = []): array
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->select(['*'])
            ->where($condition)
            ->orderBy(['id' => SORT_DESC])
            ->asArray()
            ->all();
    }

    // 获取员工数量
    public function getEmployeeCount(array $params = []): int
    {
        // 创建查询构建器
        $condition = $this->getSearchCondition($params);

        // 查询数据
        return self::find()
            ->where($condition)
            ->count();
    }

    // 获取员工信息
    public function getEmployeeInfo(string $uid,string $field = 'uid'): array
    {
        $item = self::find()
            ->where([$field => $uid])
            ->orderBy(['id' => SORT_DESC]) // 根据 id排序，最新的数据在前
            ->one();

        return $item ? $item->toArray() : [];
    }

    // 增加积分
    public function addEmployeePoint($user_id,$integral){
        $tb = $this->tableName();
        $updateData = [
            'score' => new Expression("score+".$integral),
        ];
        by::dbMaster()->createCommand()->update($tb,$updateData, ['user_id'=>$user_id])->execute();
    }

    public function updateEmployeeInfo($value,string $field,array $params){
        $db = by::dbMaster();
        $tb = self::tbName();
        return $db->createCommand()->update($tb, $params, "`$field`=:field", [":field" => $value])->execute();
    }

    // 保存员工信息，先通过 uid 判断用户是否存在，存在则更新，不存在则插入
    public function saveData(array $data): bool
    {
        // 获取用户记录
        $user = self::findOne(['uid' => $data['uid']]);

        if ($user === null) {
            // 检查必需字段是否存在
            if (empty($data['uid'])) {
                return false;
            }

            // 如果用户不存在，创建新的记录
            $user              = new self();
            $user->uid         = $data['uid'];
            $user->employee_id = $data['employee_id'];
            $user->ctime       = time(); // 设置创建时间
        }

        // 更新或设置员工信息字段
        if (isset($data['level'])) {
            $user->level = $data['level'];
        }
        if (isset($data['employee_no'])) {
            $user->employee_no = $data['employee_no'];
        }
        if (isset($data['employee_status'])) {
            $user->employee_status = $data['employee_status'];
        }
        if (isset($data['is_blacklist'])) {
            $user->is_blacklist = $data['is_blacklist'];
        }
        if (isset($data['blacklist_time'])) {
            $user->blacklist_time = $data['blacklist_time'];
        }
        if (isset($data['type'])) {
            $user->type = $data['type'];
        }
        if (isset($data['user_id'])) {
            $user->user_id = $data['user_id'];
        }

        $user->utime = time(); // 更新时间戳

        // 保存记录并返回结果
        if (!$user->save(false)) {
            return false;
        }

        return true;
    }
    public function deleteDataById($id){
        $tb  = self::tbName();
        $sql = "DELETE FROM  {$tb} WHERE `id`=:id LIMIT 1";
        $row = by::dbMaster()->createCommand($sql,[':id'=>$id])->execute();
        CUtil::debug($row.'-'.$id, 'delete_employee');
        return $row;
    }


    // 加入黑名单
    public function addBlacklist(string $uid): bool
    {
        // 获取用户记录
        $user = self::findOne(['uid' => $uid]);

        // 如果用户存在，更新相关字段
        if ($user !== null) {
            $user->is_blacklist   = self::IS_BLACKLIST['YES'];
            $user->blacklist_time = time();
            $user->utime          = time();

            // 保存更新
            return $user->save(false); // `false` 跳过验证
        }

        return false; // 未找到用户时返回 false
    }

    // 移除黑名单
    public function removeBlacklist(string $uid): bool
    {
        // 获取用户记录
        $user = self::findOne(['uid' => $uid]);

        // 如果用户存在，更新相关字段
        if ($user !== null) {
            $user->is_blacklist   = self::IS_BLACKLIST['NO'];
            $user->blacklist_time = 0;
            $user->utime          = time();

            // 保存更新
            return $user->save(false); // `false` 跳过验证
        }

        return false; // 未找到用户时返回 false
    }

    /**
     * 是否为有效的员工（微笑大使）
     * @param $uid
     * @return bool
     */
    public function isEffectiveEmployee($uid): bool
    {
        return self::find()
                ->where([
                        'uid' => $uid
                ])
                ->exists();
    }

    /**
     * 获取员工信息（方法用于脚本，写的比较局限）
     * @param $id
     * @param $limit
     * @return array
     */
    public function getEmployeeListForUpdateLevel($id, $limit): array
    {
        return self::find()
            ->where(['>', 'id', $id])
            ->andWhere(['employee_status' => self::EMPLOYEE_STATUS['NORMAL']])
            ->andWhere(['<>', 'update_level_month', strval(date('Y-m'))])
            ->limit($limit)
            ->asArray()
            ->all();
    }

    /**
     * 批量更新员工等级
     * @param array $ids
     * @param $level
     * @param $is_tip
     * @return bool
     * @throws \yii\db\Exception
     */
    public function updateEmployeeLevel(array $ids, $level, $is_tip): bool
    {
        $tb   = self::tbName();
        $data = [
            'level'              => $level,
            'update_level_month' => strval(date('Y-m')),
            'utime'              => time(),
        ];

        // 如果 $is_tip 存在于允许的值中，添加到更新数据中
        if (in_array($is_tip, self::IS_TIP)) {
            $data['is_tip'] = $is_tip;
        }

        return by::dbMaster()
            ->createCommand()
            ->update($tb, $data, ['id' => $ids])
            ->execute();
    }

    // 获取搜索条件
    private function getSearchCondition(array $params): array
    {
        $conditions = [];

        if (!empty($params['uid'])) {
            $conditions['uid'] = $params['uid'];
        }

        if (!empty($params['is_blacklist'])) {
            $conditions['is_blacklist'] = $params['is_blacklist'];
        }

        if (!empty($params['employee_id'])) {
            $conditions['employee_id'] = $params['employee_id'];
        }

        if (!empty($params['employee_status'])) {
            $conditions['employee_status'] = $params['employee_status'];
        }
        if (!empty($params['employee_no'])) {
            $conditions['employee_no'] = $params['employee_no'];
        }

        return $conditions;
    }
    // 处理uid数据
    public function handleUidData(){
        $list = self::find()->asArray()->all();
        $uids = array_column($list,'uid');
        // $uids = array_unique($uids);
        $userIds = by::Phone()->getUserIdsByUids($uids);
        foreach($list as $k=>$v){
            $save = [
                'user_id' => 0
            ];
            if (isset($userIds[$v['uid']])) {
                $save['user_id'] = $userIds[$v['uid']];
            }
            $db = by::dbMaster();
            $tb = self::tbName();
            $id = $v['id'];
            $resp = $db->createCommand()->update($tb, $save, ['id' => $id])->execute();
        }
    }
}