<?php

namespace app\modules\main\models;

use app\components\AppCRedisKeys;
use app\components\Dwz;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;


class WxUlinkModel extends CommModel {

    CONST EN_KEY    = 'WX_U_LINK';

    public $tb_fields = [
        'id','path','query','md5_val','ctime'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_log`.`t_wx_ulink`";
    }

    /**
     * @param $id
     * @return string
     * banner唯一数据缓存KEY
     */
    private function __getOneByIdkey($id): string
    {
        return AppCRedisKeys::getWxUlinkById($id);
    }

    private function __getOneByMd5key($md5_val): string
    {
        return AppCRedisKeys::getWxUlinkByMd5($md5_val);
    }

    /**
     * @param $id
     * @return int
     * 缓存清理
     */
    private function __delCache($id): int
    {
        $key = $this->__getOneByIdkey($id);
        return  by::redis('core')->del($key);
    }

    /**
     * @param $md5_val
     * @return int
     * 缓存清理
     */
    private function __delMd5Cache($md5_val): int
    {
        $key = $this->__getOneByMd5key($md5_val);
        return  by::redis('core')->del($key);
    }

    /**
     * @param $id
     * @return array|false
     * @throws Exception
     * 详情
     */
    public function GetOneById($id)
    {
        $id          = CUtil::uint($id);
        if (empty($id)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneByIdkey($id);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * @param $md5_val
     * @return int
     * @throws Exception
     * 根据md5_val获取数据
     */
    public function GetOneByMd5($md5_val)
    {
        if (empty($md5_val)) {
            return 0;
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneByMd5key($md5_val);
        $aJson       = $redis->get($redis_key);
        $id          = json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName();
            $sql     = "SELECT `id` FROM  {$tb} WHERE `md5_val`=:md5_val LIMIT 1";
            $id      = by::dbMaster()->createCommand($sql, [':md5_val' => $md5_val])->queryScalar();

            $redis->set($redis_key,$id,['EX'=>empty($id) ? 10 : 600]);
        }

        return (int)$id;
//        return $this->GetOneById($id);
    }

    /**
     * @param $code
     * @return array
     * @throws Exception
     * cp根据code返回url_link
     */
    public function GetUlinkBycode($code)
    {
        if (empty($code)) {
            return [false, '参数错误(1)'];
        }

        $decode     = CUtil::decrypt($code, self::EN_KEY);

        $data       = explode('|', $decode);

        if (count($data) != 2) {
            return [false, '网络错误'];
        }

        list($id, $rand) = $data;

        $aLog   = $this->GetOneById($id);

        list($s, $url_link) = WeiXin::factory()->UrlLink($aLog['path'], $aLog['query']);
        if (!$s) {
            return [false, $url_link];
        }

        return [true, $url_link];


    }

    /**
     * @param $post
     * @return array
     * @throws Exception
     * 生成url-link
     */
    public function CreateLink($post)
    {
        $path   = $post['path']     ?? '';
        $query  = $post['query']    ?? '';
        $type   = $post['type']     ?? '0'; //0直接生成 1短链生成-跳转h5

        if (empty($path)) {
            return [false, '请输入页面路径'];
        }

        if (mb_strlen($query) > 1024) {
            return [false, 'query超过1024字符'];
        }

        switch ($type) {
            case '1' :
                list($s, $code) = $this->__saveLog($path, $query);
                if (!$s) {
                    return [false, $code];
                }

                $url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . (CUtil::getConfig('host', 'config', \Yii::$app->id)['ulink_path']);

                $long_url = $url.'?p='.$code;

                //生成短链
                list($s1, $res) = Dwz::factory()->getDwz($long_url);
                if (!$s1) {
                    return [false, $res];
                }

                if ($res['Code'] != 0) {
                    return [false, $res['Code']];
                }

                $link = $res['ShortUrls'][0]['ShortUrl'] ?? '';

                break;
            case 2:
                list($s, $code) = $this->__saveLog($path, $query);
                if (!$s) {
                    return [false, $code];
                }

                $url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . (CUtil::getConfig('host', 'config', \Yii::$app->id)['ulink_path']);

                $link = $url.'?p='.$code;
                break;
            default :
                list($s, $link) = WeiXin::factory()->UrlLink($path, $query);
                if (!$s) {
                    return [false, $link];
                }
        }

        return [true, ['url_link' => $link]];
    }


    /**
     * @param $path
     * @param $query
     * @return array
     * @throws Exception
     * 数据增改
     */
    private function __saveLog($path, $query)
    {
        $md5_val    = md5($path.$query);
        $id         = $this->GetOneByMd5($md5_val);

        if (empty($id)) {
            $db         = by::dbMaster();
            $tb         = self::tbName();

            $path       = trim($path);
            $query      = trim($query);

            $save = [
                'path'      => $path,
                'query'     => $query,
                'md5_val'   => $md5_val,
                'ctime'     => intval(START_TIME)
            ];

            $db->createCommand()->insert($tb,$save)->execute();

            $id = $db->getLastInsertID();

            $this->__delCache($id); //todo 清空缓存
            $this->__delMd5Cache($md5_val); //todo 清空缓存
        }

        //加密id
        $rand   = CUtil::createVerifyCode(3,1);
        $code   = CUtil::encrypt("{$id}|{$rand}", self::EN_KEY);
        return [true, $code];
    }


    public function activityLink($post): array
    {
        $path   = $post['path']     ?? '';
        $query  = $post['query']    ?? '';
        $type   = $post['type']     ?? '0'; //0直接生成 1短链生成-跳转h5
        $id     = $post['id']       ?? '';
        if (empty($path)) {
            return [false, '请输入页面路径'];
        }

        if (mb_strlen($query) > 1024) {
            return [false, 'query超过1024字符'];
        }

        switch ($type) {
            case '1' :
                list($s, $code) = $this->__saveLog($path, $query);
                if (!$s) {
                    return [false, $code];
                }

                if (!$id){
                    return [false,'活动id为空'];
                }

                $url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . (CUtil::getConfig('host', 'config', \Yii::$app->id)['ulink_path']);

                $long_url = $url.'?p='.$code;

                //生成短链
                list($s1, $res) = Dwz::factory()->getDwz($long_url);
                if (!$s1) {
                    return [false, $res];
                }

                if ($res['Code'] != 0) {
                    return [false, $res['Code']];
                }

                $link = $res['ShortUrls'][0]['ShortUrl'] ?? '';

                break;
            default :
                list($s, $link) = WeiXin::factory()->UrlLink($path, $query);
                if (!$s) {
                    return [false, $link];
                }
        }
        //落库修改url
        $updateResult = by::acType3()->updateUrlByAcId($id,['url'=>$link,'update_time'=>START_TIME]);
        if ($updateResult){
            //删除缓存
            by::acType3()->delAcTypeCache($id);
            return [true, ['url_link' => $link]];
        }else{
            return [false, ['url_link' => '']];
        }
    }


}
