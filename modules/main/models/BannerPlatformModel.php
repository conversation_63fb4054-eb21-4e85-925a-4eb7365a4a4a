<?php

namespace app\modules\main\models;

use app\models\by;
use app\models\byNew;

class BannerPlatformModel extends CommModel
{
    public static function tableName(): string
    {
        return "`db_dreame`.`t_banner_platform`";
    }


    public function getPlatformByBannerId($bannerId): array
    {
        return self::find()->select('platform_id')->where(['banner_id' => $bannerId])->column();
    }

    // 批量新增
    public function saveLogs($bannerId, $platforms)
    {
        $data = [];
        foreach ($platforms as $platform) {
            $data[] = [
                'banner_id'   => $bannerId,
                'platform_id' => $platform,
                'ctime'       => time(),  // 假设有一个创建时间字段
                'utime'       => time()   // 假设有一个更新时间字段
            ];
        }
        // 执行批量插入操作，假设使用 Yii2 的批量插入方法
        by::dbMaster()->createCommand()->batchInsert(self::tableName(), ['banner_id', 'platform_id', 'ctime', 'utime'], $data)->execute();

        byNew::BannerModel()->__delCache($bannerId);
    }

    // 批量删除
    public function deleteLogs($bannerId, $platforms)
    {
        // 执行批量删除操作
        by::dbMaster()->createCommand()
            ->delete(self::tableName(), ['banner_id' => $bannerId, 'platform_id' => $platforms])
            ->execute();

        byNew::BannerModel()->__delCache($bannerId);
    }
}
