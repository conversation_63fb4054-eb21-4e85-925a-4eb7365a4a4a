<?php
/**
 * 附近门店
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2022/2/7
 * Time: 17:55
 */

namespace app\modules\main\models;

use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\components\WeiXin;
use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class RetailersModel extends CommModel
{

	CONST STATUS = [
		'YES' => ['STATUS' => 0, 'NAME' => '生效'],
		'NO'  => ['STATUS' => 1, 'NAME' => '隐藏']
	];

	CONST EXPIRE_TIME = 86400*7;    //redis 过期时间
	const DISTANCE = 100000000000;     //门店搜索范围（单位米）

    const SHOP_SHARE = "SHOP_SHARE";


    public $tb_fields = [
            'id', 'shop_img', 'shop_name', 'shop_phone', 'start_time', 'end_time', 'shop_addr', 'pos_l', 'pos_b', 'status', 'ctime', 'shop_code', 'shop_code_img', 'is_open_activity',
            'province', 'province_id', 'city', 'city_id', 'district', 'district_id'
    ];

	public static function tbName(): string
	{
		return '`db_dreame`.`t_retailers`';
	}

    public static function tableName(): string
    {
        return self::tbName();
    }

    const OPEN_ACTIVITY_STATUS = [
            'OPEN' => ['STATUS' => 1, 'NAME' => '开启'],
            'CLOSE'  => ['STATUS' => 2, 'NAME' => '关闭']
    ];
	/**
	 * @param $shop_img :门店图片
	 * @param $shop_name :门店名称
	 * @param $shop_phone :门店电话
	 * @param $start_time :门店开业时间
	 * @param $end_time : 门店结束时间
	 * @param $shop_addr :门店详细地址
	 * @param $pos_l :门店经度
	 * @param $pos_b :门店维度
	 */
	/**
	 *
	 * @param $aData
	 */
	public function saveAddr($aData)
	{
		//$shop_img,$shop_name,$shop_phone,$start_time,$end_time,$shop_addr,$pos_l,$pos_b,$shop_code
		//商品修改基础校验
        list($status, $msg) = $this->__checkUpdateShop($aData);
        if (empty($status)) {
            return [false, $msg];
        }
        $shop_status = $aData['status'] ?? self::STATUS['YES']['STATUS'];
        $shop_code   = trim($aData['shop_code'] ?? '');
        $shopCodeImg = '';
        if (!empty($shop_code)) {
            list($s, $shop_code_str) = $this->encrypt($shop_code);
            // 生成对应的二维码
            list($s, $sunCode) = WeiXin::factory()->getWxaCodeUnlimit("pages/index/index", 'shop_code=' . $shop_code_str, true);
            if ($s && $sunCode) {
                list($s, $shopCodeImg) = AliYunOss::factory()->uploadBinaryImage($sunCode, "png");
            }
        }
        $id     = $aData['id'] ?? 0;
        $id     = CUtil::uint($id);
        $params = [
                'shop_img'         => $aData['shop_img'] ?? '',
                'shop_name'        => $aData['shop_name'] ?? '',
                'shop_phone'       => $aData['shop_phone'] ?? 0,
                'start_time'       => $aData['start_time'] ?? '',
                'end_time'         => $aData['end_time'] ?? '',
                'status'           => $shop_status,
                'shop_addr'        => $aData['shop_addr'],
                'pos_l'            => $aData['pos_l'] ?? 0,
                'pos_b'            => $aData['pos_b'] ?? 0,
                'shop_code'        => $shop_code,
                'shop_code_img'    => $shopCodeImg,
                'ctime'            => intval(START_TIME),
                'is_open_activity' => $aData['is_open_activity'] ?? 0,
                'province'         => $aData['province'],
                'province_id'      => $aData['province_id'],
                'city'             => $aData['city'],
                'city_id'          => $aData['city_id'],
                'district'         => $aData['district'],
                'district_id'      => $aData['district_id'],
        ];

        $db     = by::dbMaster();
        $trans  = $db->beginTransaction();
		try {
			$command = $db->createCommand();
			$tb      = self::tbName();
			if ($id) {
				unset($params['ctime']); //不修改添加修改
				$command->update($tb, $params, "`id`=:id", [":id" => $id])->execute();
			} else {
				//新增
				$ret = $command->insert($tb, $params)->execute();
				if (!$ret) {
					throw new \Exception('新增门店失败!', 2001);
				}
				$id = $db->getLastInsertID();
			}

            if($shop_status==self::STATUS['YES']['STATUS']){
                $flag   = $this->__addShopPos($id,$params['pos_l'],$params['pos_b']);
                if ($flag === false) {
                    throw new \Exception('请输入正确的经纬度', 2001);
                }
            }else{
                $this->__delShopPos($id);
            }
            $this->__delCache($id);

			$trans->commit();

			return [true, $params];
		} catch (\Exception $e) {
			$trans->rollBack();
			$error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
			CUtil::debug("保存失败{$error}", 'save-shop');
			return $e->getCode() == 2001 ? [false, $e->getMessage()] : [false, '添加失败'];
		}

	}


    /**
     * @param $shop_code
     * @return array
     * 门店编码加密
     */
    public function encrypt($shop_code): array
    {
        $rand = CUtil::createVerifyCode(3, 1);
        $params = CUtil::getAllParams($shop_code, $rand);
        $encode = CUtil::encrypt($params, self::SHOP_SHARE);

        return [true, $encode];

    }

    /**
     * @param $code
     * @return false|string[]
     * 门店编码解密
     */
    public function decrypt($code)
    {
        $decode = CUtil::decrypt($code, self::SHOP_SHARE);
        return explode('|', $decode);
    }

	/**
	 * 添加成员的经纬度信息
	 * @param $id
	 * @param $pos_l
	 * @param $pos_b
	 * @return mixed
	 */
	private function __addShopPos($id,$pos_l,$pos_b){
		return by::redis()->rawCommand('geoadd', AppCRedisKeys::getShopPos(), $pos_l, $pos_b, $id);
	}

	/**
	 * 删除成员经纬度信息
	 * @param $id
	 */
	private function __delShopPos($id){
		by::redis()->zRem(AppCRedisKeys::getShopPos(), $id);
	}


	/**删除缓存
	 * @param $id
	 */
	private function __delCache($id){
		$r_key   = $this->__getShopListKey();
		$r_key2 = $this->__getOneShopKey($id);
		by::redis()->del($r_key,$r_key2);
	}

	/**
	 * 商品哈希列表
	 * @return string
	 */
	private function __getShopListKey(): string
	{
		return AppCRedisKeys::getShopList();
	}

	/**
	 * @param int $gid
	 * @return string
	 * 商品唯一数据缓存KEY
	 */
	private function __getOneShopKey($id): string
	{
		return AppCRedisKeys::getOneShopByGid($id);
	}


    /**
     * 根据条件获取总数
     * @param $shop_name
     * @return int
     * @throws Exception
     */
	public function getCount($shop_name='',$shop_codes=[],$is_open_activity=0)
	{
		$r_key   = $this->__getShopListKey();
		$sub_key = CUtil::getAllParams(__FUNCTION__,$shop_name,json_encode($shop_codes),$is_open_activity);
		$count   = by::redis('core')->hGet($r_key, $sub_key);
		if($count === false) {
			$tb                  = self::tbName();
            list($where,$params) = $this->_condition($shop_name,$shop_codes,$is_open_activity);
			$sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
			$count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
			by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key, self::EXPIRE_TIME + mt_rand(600, 3600));
		}
		return intval($count);
	}


    /**
     * @param int $page
     * @param int $page_size
     * @param string $shop_name
     * @param array $shop_codes
     * @param $is_open_activity
     * @return array
     * @throws Exception 门店列表
     * @throws \RedisException
     */
	public function getList($page = 1, $page_size = 25,$shop_name='',$shop_codes=[],$is_open_activity=0): array
	{
		$r_key   = $this->__getShopListKey();
		$sub_key = CUtil::getAllParams(__FUNCTION__, $page, $page_size,$shop_name,json_encode($shop_codes),$is_open_activity);
		$aJson   = by::redis('core')->hGet($r_key, $sub_key);
		$aData   = json_decode($aJson, true);
		if($aJson === false) {
			$tb                  = self::tbName();
			list($offset)        = CUtil::pagination($page,$page_size);
			$order               = "";
			$order              .= " `id` DESC ";
			$limit               = "";
			if($page_size){
				$limit          .= " LIMIT {$offset},{$page_size}";
			}
            list($where,$params) = $this->_condition($shop_name,$shop_codes,$is_open_activity);
			$sql                 = "SELECT * FROM {$tb} WHERE {$where} ORDER BY {$order} {$limit} ";
			$aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();
			$aData               = empty($aData) ? [] : $aData;
			by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
			CUtil::ResetExpire($r_key,self::EXPIRE_TIME+mt_rand(600,3600));
		}
		return $aData;
	}


	/**
	 * 基础数据校验
	 * @param $aData
	 * @return array
	 */
	private function __checkUpdateShop($aData)
	{
		if (empty($aData['shop_name'])) {
			return [false, "门店名称不能为空"];
		}

		if (empty($aData['shop_img'])) {
			return [false, "门店图片不能为空"];
		}

		if (empty($aData['shop_phone'])) {
			return [false, "门店联系方式不能为空"];
		}

        if (empty($aData['shop_code'])) {
            return [false, "门店编码不能为空"];
        }

		if (empty($aData['start_time']) || empty($aData['end_time'])) {
			return [false, "门店营业时间不能为空"];
		}

        if (empty($aData['province']) || empty($aData['province_id']) ){
            return [false,'省份信息不能为空'];
        }

        if (empty($aData['city']) || empty($aData['city_id']) ){
            return [false,'市级信息不能为空'];
        }

        if (empty($aData['district']) || empty($aData['district_id']) ){
            return [false,'区县信息不能为空'];
        }

		if (empty($aData['shop_addr'])) {
			return [false, "门店详细地址不能为空"];
		}

		if (!isset($aData['pos_l']) || !isset($aData['pos_b'])) {
			return [false, "门店经纬度不能为空"];
		}
		return [true, 'ok'];
	}


	/**根据经纬度获取附近门店列表
	 * @param $pos_l
	 * @param $pos_b
	 * @param int $page_size
	 * @return array
	 */
	public function getListByPos($pos_l,$pos_b,$page_size=5000){
		$data = by::redis()->rawCommand('georadius',AppCRedisKeys::getShopPos(),$pos_l, $pos_b, self::DISTANCE, 'km', 'WITHDIST','count',$page_size);
		if(empty($data)){
			return [];
		}
		$info = [];
		foreach ($data as $item){
			$info[$item[0]]['pos'] = $item[1];  //0:门店id，1:相对位置
		}
		return $info;
	}


    /**
     * 根据门店 ID 数组获取门店列表
     *
     * @param array $id_arr 门店 ID 数组
     * @param int|null $is_open_activity 是否开启活动（1 开启，0 关闭，null 忽略）
     * @return array 返回门店信息列表
     * @throws Exception 查询失败时可能抛出异常
     */
    public function getListByIds(array $id_arr, int $is_open_activity = 0, $keyword = '', $province_id = 0, $city_id = 0, $district_id = 0): array
    {
        if (empty($id_arr)) {
            return []; // 如果 ID 数组为空，直接返回空数组
        }

        // 过滤 ID 数组，确保所有值均为正整数，并去重
        $id_arr = array_unique(array_filter(array_map('intval', $id_arr)));

        if (empty($id_arr)) {
            return []; // 过滤后仍为空，返回空数组
        }

        // 构造查询条件
        $params = [];
        $where  = ["status = 0", "id IN (" . implode(',', $id_arr) . ")"];


        // 获取门店活动时间
        $info        = CommonActivityModel::getActivityById(1);
        $currentTime = time();

        if ($info && $currentTime > $info['start_time'] && $currentTime < $info['end_time']) {
            // 处理 is_open_activity 参数
            if (!empty($is_open_activity)) {
                $where[]                     = "is_open_activity = :is_open_activity";
                $params[':is_open_activity'] = $is_open_activity;
            }
        }

        if (!empty($keyword)){
            $where [] = "shop_name LIKE :keyword";
            $params[':keyword']   = "%{$keyword}%";
        }

        if (!empty($province_id)) {
            $where[]                = "province_id = :province_id";
            $params[':province_id'] = $province_id;
        }

        if (!empty($city_id)) {
            $where[]            = "city_id = :city_id";
            $params[':city_id'] = $city_id;
        }

        if (!empty($district_id)) {
            $where[]                = "district_id = :district_id";
            $params[':district_id'] = $district_id;
        }


        // 生成 SQL 语句
        $sql = "SELECT * FROM " . self::tbName() . " WHERE " . implode(' AND ', $where);

        // 执行查询并返回结果
        return by::dbMaster()
                ->createCommand($sql)
                ->bindValues($params)
                ->queryAll();
    }



    /**
	 * 根据id获取门店详情
	 * @param $id
	 */
	public function backInfo($id){
		$id = CUtil::uint($id);
		if(empty($id)){
			return [];
		}
		$redis       = by::redis('core');
		$redis_key   = $this->__getOneShopKey($id);
		$aJson       = $redis->get($redis_key);
		$aData       = (array)json_decode($aJson,true);
		if($aJson  === false) {
			$tb      = $this->tbName();
			$fields  = implode("`,`",$this->tb_fields);
			$sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
			$command = by::dbMaster()->createCommand($sql);
			$command->bindParam(":id", $id);
			$aData   = $command->queryOne();
			$aData   = empty($aData) ? [] : $aData;
			$redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
		}
		return $aData;
	}

    /**
     * @param $id
     * @param $status
     * @return array
     * @throws Exception
     */
	public function upStatus($id,$status){
        $id     = CUtil::uint($id);
        $status = CUtil::uint($status);
        if(empty($id)){
            return [false,' 缺少参数'];
        }

        $info = $this->backInfo($id);
        if (!$info){
            return [false,'门店不存在'];
        }
        $tb      = $this->tbName();
        by::dbMaster()->createCommand()->update($tb, ['status' => $status], ['id' => $id])->execute();
        $this->__delCache($id);

        if($status==self::STATUS['YES']['STATUS']){
            $flag   = $this->__addShopPos($id,$info['pos_l'],$info['pos_b']);
            if ($flag === false) {
                return [false,'请确认经纬度是否正确'];
            }
        }else{
            $this->__delShopPos($id);
        }

        return [true,'ok'];
    }

    /**
     * @param string $shop_name
     * @param array $shop_codes
     * @param int $is_open_activity
     * @return array
     */
    protected function _condition(string $shop_name = '', array $shop_codes = [], int $is_open_activity=0)
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if (!empty($shop_name)) {
            $where                .= " AND `shop_name` LIKE :shop_name";
            $params[":shop_name"] = "%{$shop_name}%";
        }

        if (!empty($shop_codes)) {
            $shop_codes = implode("','", $shop_codes);
            $where      .= " AND `shop_code` in ('{$shop_codes}')";
        }

        if (!empty($is_open_activity)) {
            $where                       .= " AND `is_open_activity` =:is_open_activity";
            $params[":is_open_activity"] = $is_open_activity;
        }

        return [$where, $params];
    }

    /**
     * @param array $ids
     * @param int $status
     * @return int
     * @throws Exception
     * 批量开启/关闭活动
     */
    public function batchOpenActivity(array $ids, int $status): int
    {
        if (empty($ids)) {
            return 0; // 避免执行无效 SQL
        }

        $tb = self::tbName();

        // 执行批量更新
        $affectedRows = by::dbMaster()->createCommand()
                ->update($tb, ['is_open_activity' => $status], ['id' => $ids])
                ->execute();

        // 清除缓存
        foreach ($ids as $id) {
            $this->__delCache($id);
        }

        return $affectedRows; // 返回受影响行数
    }


    /**
     * 批量修改所有门店的活动状态（开启或关闭）
     *
     * @param int $isOpenActivity 目标状态（1=开启，2=关闭）
     * @return int 成功更新的行数
     * @throws Exception
     */
    public function updateAllStoreActivityStatus(int $isOpenActivity): int
    {
        $tb = self::tbName();

        // 反向状态，用于查找需要变更的门店
        $fromStatus = $isOpenActivity === self::OPEN_ACTIVITY_STATUS['OPEN']['STATUS'] ?
                self::OPEN_ACTIVITY_STATUS['CLOSE']['STATUS'] :
                self::OPEN_ACTIVITY_STATUS['OPEN']['STATUS'];

        // 查询需要变更的门店 ID 列表
        $storeIds = self::find()
                ->select('id')
                ->where(['is_open_activity' => $fromStatus])
                ->column(); // 更高效，直接返回一维 ID 数组

        if (empty($storeIds)) {
            return 0; // 没有可更新门店
        }

        // 清除缓存
        foreach ($storeIds as $id) {
            $this->__delCache($id);
        }

        // 执行批量更新
        return by::dbMaster()
                ->createCommand()
                ->update($tb, ['is_open_activity' => $isOpenActivity], ['id' => $storeIds])
                ->execute();
    }

}
