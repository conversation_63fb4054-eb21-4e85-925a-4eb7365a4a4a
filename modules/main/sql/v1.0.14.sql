
/*
* 上下水服务二期
*/
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` MODIFY `status` tinyint(1) unsigned NOT NULL DEFAULT '4' COMMENT '状态（0：待服务 1：已取消 2:已完成 3：服务中 4：待支付）';
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` MODIFY `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型（1：勘测工单 2：安装工单）';

-- 勘测
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `sn_alias` varchar(50) NOT NULL DEFAULT '' COMMENT '产品别名' AFTER `sn`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实付款（分）' AFTER `address`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `explore_case` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '勘测情况（1：待跟进 2：勘探失败 3：勘探成功 4：未勘探）' AFTER `price`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `refund_status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款状态（1000：待审核 1010：审核通过 1020：审核拒绝 20000000：退款成功）' AFTER `status`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `is_buy` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已购买追觅产品（0：否 1：是）' AFTER `context_type`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `is_export` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已导出（0：否 1：是）' AFTER `is_buy`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间' AFTER `expect_time`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `finish_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间' AFTER `pay_time`;

-- 安装
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `images` varchar(500) NOT NULL DEFAULT '' COMMENT '现场图片' AFTER `price`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `install_case` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '安装情况（1：未安装 2：安装失败 3：已安装）' AFTER `explore_case`;
ALTER TABLE `db_dreame_goods`.`t_plumbing_order` ADD `is_explore` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已上门勘测（0：否 1：是）' AFTER `is_export`;

-- 价格配置
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_plumbing_price` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `oprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原价（分）',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实付款（分）',
    `note` varchar(50) NOT NULL DEFAULT '' COMMENT '提示',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水服务价格配置表';

-- sn配置
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_plumbing_sn` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `p_id` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '产品id',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '注册产品名称',
    `alias` varchar(50) NOT NULL DEFAULT '' COMMENT '产品名称',
    `images` varchar(200) NOT NULL DEFAULT '' COMMENT '产品图片',
    `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型（1:勘测工单 2:安装工单）',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_p_t` (`p_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水服务sn编码配置表';

-- 退款表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_plumbing_refund` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '工单号',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `name` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款金额（分）',
    `describe` varchar(100) NOT NULL DEFAULT '' COMMENT '补充描述',
    `images` varchar(500) NOT NULL DEFAULT '' COMMENT '图片',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码（已购机退款）',
    `buy_order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '购买产品订单号（已购机退款）',
    `channel` varchar(100) NOT NULL DEFAULT '' COMMENT '购买渠道（已购机退款）',
    `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '退款类型（1:我要取消 2:已购机退款 3：勘测失败退款）',
    `order_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型（1：勘探工单 2：安装工单）',
    `ostatus` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款前状态（0：待服务 1：已取消 2:已完成 3：服务中 4：待支付）',
    `status` int(10) unsigned NOT NULL DEFAULT '1000' COMMENT '退款状态（1000：待审核 1010：审核通过 1020：审核拒绝 20000000：退款成功）',
    `refuse_reason` varchar(200) NOT NULL DEFAULT '' COMMENT '拒绝原因',
    `rtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款时间',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_r` (`refund_no`) USING BTREE,
    KEY `i_c` (`ctime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水服务退款表';