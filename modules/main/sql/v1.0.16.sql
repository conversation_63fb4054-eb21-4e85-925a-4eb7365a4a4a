
/*
* 上下水服务二期
*/

-- 评价表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_plumbing_comment` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '评论id',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '工单号',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
    `tags` varchar(50) NOT NULL DEFAULT '' COMMENT '标签',
    `overall_grade` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '总体评价等级（五颗星，默认5颗星）',
    `speed_grade` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '上门速度等级（五颗星，默认5颗星）',
    `manner_grade` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '服务态度等级（五颗星，默认5颗星）',
    `specialty_grade` tinyint(1) unsigned NOT NULL DEFAULT '5' COMMENT '专业能力等级（五颗星，默认5颗星）',
    `brand_grade` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '品牌服务分数（十分）',
    `content` varchar(100) NOT NULL DEFAULT '' COMMENT '评论内容',
    `images` varchar(500) NOT NULL DEFAULT '' COMMENT '图片',
    `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型（1：勘探工单 2：安装工单）',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评价时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_o` (`order_no`) USING BTREE,
    KEY `i_c` (`ctime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水评价表';

ALTER TABLE `db_dreame_goods`.`t_plumbing_comment` MODIFY `tags` varchar(200) NOT NULL DEFAULT '' COMMENT '标签';

-- 标签表
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_comment_tag` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(10) NOT NULL DEFAULT '' COMMENT '标签名称',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_n` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水评价标签配置表';
