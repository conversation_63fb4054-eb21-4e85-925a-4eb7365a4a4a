
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_0` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) NOT NULL COMMENT '用户id',
    `year` char(4) NOT NULL DEFAULT '2022' COMMENT '年',
    `point` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '积分',
    PRIMARY KEY (`id`),
    UNIQUE KEY `i_u_y` (`user_id`,`year`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_1` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_2` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_3` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_4` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_5` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_6` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_7` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_8` LIKE `db_dreame`.`t_point_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_point_9` LIKE `db_dreame`.`t_point_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_detail_2022` (
    `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
    `log_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '日志ID',
    `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `act_jf_ids` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '流水类型',
    `point` int(10) NOT NULL DEFAULT '0' COMMENT '流水金额',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '流水产生时间',
    PRIMARY KEY (`id`),
    KEY `i_a_c_u` (`act_jf_ids`,`ctime`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分流水主表';


CREATE TABLE `db_dreame_log`.`t_point_detail_2023` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2024` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2025` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2026` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2027` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2028` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2029` LIKE `db_dreame_log`.`t_point_detail_2022` ;
CREATE TABLE `db_dreame_log`.`t_point_detail_2030` LIKE `db_dreame_log`.`t_point_detail_2022` ;



CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_0` (
    `id` bigint(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `act_jf_ids` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '流水类型',
    `point` int(10) NOT NULL DEFAULT '0' COMMENT '积分',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '流水产生时间',
    `detail` varchar(200) NOT NULL COMMENT '详细记录流水对应的时间',
    `remark` varchar(100) NOT NULL COMMENT '备注',
    `rpoint` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '已回滚的积分',
    PRIMARY KEY (`id`),
    KEY `i_u_a` (`user_id`,`act_jf_ids`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='积分流水记录';

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_1` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_2` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_3` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_4` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_5` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_6` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_7` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_8` LIKE `db_dreame_log`.`t_point_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_point_log_9` LIKE `db_dreame_log`.`t_point_log_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_sn_reg` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `sn` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'sn编码',
  `ctime` int(11) NOT NULL COMMENT '创建时间',
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `sn` (`sn`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=0 DEFAULT CHARSET=utf8mb4 COMMENT='sn编码注册表';