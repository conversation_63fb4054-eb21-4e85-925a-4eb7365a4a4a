/*-----------------订单自动收货-----------------*/
ALTER TABLE `db_dreame_goods`.`t_ofinish`
CHANGE COLUMN `ctime` `check_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '签收时间' AFTER `order_no`,
COMMENT = '订单签收表（签收7天确认收货）';

DELETE from `db_dreame_goods`.`t_ofinish`;

ALTER TABLE `db_dreame_goods`.`t_uo_0`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_1`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_2`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_3`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_4`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_5`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_6`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_7`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_8`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_uo_9`
ADD COLUMN `stime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `pay_time`;

ALTER TABLE `db_dreame_goods`.`t_om_2022`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2023`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2024`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2025`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2026`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2027`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2028`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2029`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_om_2030`
ADD COLUMN `stime` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '发货时间' AFTER `ctime`;
/*-----------------订单自动收货-----------------*/


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_wx_ulink` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `path` varchar(100) NOT NULL DEFAULT '',
  `query` varchar(1024) NOT NULL DEFAULT '',
  `md5_val` char(32) NOT NULL DEFAULT '',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_m` (`md5_val`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='微信url_link生成关系';
