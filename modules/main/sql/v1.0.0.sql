
CREATE DATABASE IF NOT EXISTS `db_dreame`;
CREATE DATABASE IF NOT EXISTS `db_dreame_log`;
CREATE DATABASE IF NOT EXISTS `db_dreame_admin`;

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_users_main` (
    `user_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `openudid` varchar(50) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识',
    `unionid` varchar(200) NOT NULL DEFAULT '' COMMENT '用户在开放平台的唯一标识符',
    `reg_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
    `user_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户类型',
    PRIMARY KEY (`user_id`) USING BTREE,
    UNIQUE KEY `openudid` (`openudid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=548213 DEFAULT CHARSET=utf8mb4 COMMENT='用户主表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_0` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) NOT NULL COMMENT '用户id',
    `nick` varchar(50) NOT NULL DEFAULT '' COMMENT '昵称',
    `real_name` varchar(50) NOT NULL DEFAULT '' COMMENT '真实姓名',
    `birthday` int(10) NOT NULL DEFAULT 0 COMMENT '生日',
    `area` varchar(200) DEFAULT '' COMMENT '所在区域',
    `status` tinyint(1) DEFAULT '0' COMMENT '状态 0正常 1冻结 2平台禁用',
    `age` tinyint(3) DEFAULT NULL COMMENT '年龄',
    `avatar` varchar(200) DEFAULT '' COMMENT '头像',
    `active_time` int(11) DEFAULT NULL COMMENT '最后登录时间',
    `sex` tinyint(1) DEFAULT '0' COMMENT '性别 0保密 1男 2女',
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户详情表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_1` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_2` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_3` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_4` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_5` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_6` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_7` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_8` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_9` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_10` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_11` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_12` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_13` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_14` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_15` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_16` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_17` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_18` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_19` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_20` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_21` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_22` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_23` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_24` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_25` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_26` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_27` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_28` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_29` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_30` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_31` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_32` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_33` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_34` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_35` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_36` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_37` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_38` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_39` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_40` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_41` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_42` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_43` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_44` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_45` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_46` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_47` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_48` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_49` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_50` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_51` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_52` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_53` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_54` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_55` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_56` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_57` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_58` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_59` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_60` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_61` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_62` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_63` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_64` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_65` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_66` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_67` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_68` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_69` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_70` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_71` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_72` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_73` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_74` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_75` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_76` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_77` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_78` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_79` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_80` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_81` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_82` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_83` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_84` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_85` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_86` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_87` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_88` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_89` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_90` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_91` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_92` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_93` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_94` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_95` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_96` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_97` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_98` LIKE `db_dreame`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_99` LIKE `db_dreame`.`t_user_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_qps` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `date` int(10) NOT NULL DEFAULT 0 COMMENT '日期',
    `data` text NOT NULL COMMENT 'QPS记录',
    PRIMARY KEY (`id`),
    UNIQUE KEY `date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='QPS记录';



    ALTER TABLE `db_dreame`.`t_user_0`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_1`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_2`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_3`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_4`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_5`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_6`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_7`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_8`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_9`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_10`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_11`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_12`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_13`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_14`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_15`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_16`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_17`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_18`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_19`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_20`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_21`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_22`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_23`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_24`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_25`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_26`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_27`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_28`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_29`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_30`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_31`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_32`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_33`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_34`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_35`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_36`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_37`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_38`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_39`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_40`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_41`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_42`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_43`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_44`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_45`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_46`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_47`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_48`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_49`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_50`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_51`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_52`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_53`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_54`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_55`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_56`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_57`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_58`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_59`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_60`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_61`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_62`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_63`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_64`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_65`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_66`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_67`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_68`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_69`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_70`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_71`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_72`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_73`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_74`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_75`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_76`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_77`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_78`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_79`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_80`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_81`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_82`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_83`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_84`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_85`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_86`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_87`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_88`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_89`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_90`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_91`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_92`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_93`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_94`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_95`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_96`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_97`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_98`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';
    ALTER TABLE `db_dreame`.`t_user_99`  add `is_new_gift` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包';