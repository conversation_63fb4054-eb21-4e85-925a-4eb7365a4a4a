
ALTER TABLE `db_dreame_goods`.`t_market_config`
ADD COLUMN `freight_free` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否免邮' AFTER `set_num`;


ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2022`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2023`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2024`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2025`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2026`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2027`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2028`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2029`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

ALTER TABLE `db_dreame_log`.`t_p_reg_detail_2030`
ADD COLUMN `before_sn` varchar(100) NOT NULL DEFAULT '' COMMENT '修改前sn码' AFTER `sn`,
ADD COLUMN `crm_sync` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已同步 0否 1是' AFTER `before_sn`;

update db_dreame_log.t_p_reg_detail_2022 set crm_sync = 1;

############删除redis缓存 del dreame|getProductDetailList
