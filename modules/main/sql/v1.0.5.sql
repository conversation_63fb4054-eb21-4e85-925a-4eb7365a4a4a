ALTER TABLE `db_dreame_goods`.`t_orefund_main`
ADD COLUMN `m_type` tinyint(3) UNSIGNED NOT NULL DEFAULT 0 COMMENT '退款方式 1:仅退款 2:退货退款' AFTER `status`;

ALTER TABLE `db_dreame_goods`.`t_market_config`  add `set_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '领取数量';

ALTER TABLE `db_dreame_goods`.`t_gmain`
ADD COLUMN `ck_code` varchar(20) NOT NULL DEFAULT '' COMMENT '仓库' AFTER `version`;

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (171, 'wxUlink', 170, 'back/data/wx-ulink', '微信生成url-link权限', 1649841758, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (170, 'wxUlink', 5, 'system/wx-ulink', '生成小程序URL', **********, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (169, 'updatePwd', 168, 'rbac/manage/update-pwd', '修改账号密码权限', **********, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (168, 'accountManger', 0, '', '账号管理', **********, 0, 1, 1, 0, '');

ALTER TABLE `db_dreame_goods`.`t_market_config`  add `jump_goods_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '跳转商品ID' AFTER `use_num`;
ALTER TABLE `db_dreame_goods`.`t_market_config`  add `use_rule_note` varchar(1000)  NOT NULL DEFAULT '' COMMENT '使用规则' AFTER `jump_goods_id`;

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (172, 'expCom', 128, 'back/data/exp-com', '获取物流公司列表权限', **********, 0, 1, 0, 0, '');


ALTER TABLE `db_dreame_goods`.`t_orefund_0`
ADD COLUMN `rback` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已退单（0：否 1：是）' AFTER `rtime`;

ALTER TABLE `db_dreame_goods`.`t_orefund_1`
ADD COLUMN `rback` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已退单（0：否 1：是）' AFTER `rtime`;

ALTER TABLE `db_dreame_goods`.`t_orefund_2`
ADD COLUMN `rback` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已退单（0：否 1：是）' AFTER `rtime`;

ALTER TABLE `db_dreame_goods`.`t_orefund_3`
ADD COLUMN `rback` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已退单（0：否 1：是）' AFTER `rtime`;

ALTER TABLE `db_dreame_goods`.`t_orefund_4`
ADD COLUMN `rback` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已退单（0：否 1：是）' AFTER `rtime`;

