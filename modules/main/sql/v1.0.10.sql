
CREATE DATABASE IF NOT EXISTS `db_dreame_wiki`;

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wdtopic` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `did` int(10) unsigned NOT NULL DEFAULT '0',
  `tid1` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '一级话题id',
  `tid2` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '二级话题id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_t_g` (`tid2`,`did`) USING BTREE,
  KEY `i_g` (`did`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品内容话题表';

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wdynamic_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `did` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主表id',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '动态类型（0：文章，1：视频）',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `cover_image` varchar(255) NOT NULL DEFAULT '' COMMENT '封面图片',
  `video` varchar(255) NOT NULL DEFAULT '' COMMENT '视频地址',
  `text` text COMMENT '文章',
  `author` varchar(20) NOT NULL DEFAULT '' COMMENT '作者',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后编辑时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `i_did` (`did`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品内容分表';

create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_1` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_2` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_3` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_4` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_5` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_6` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_7` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_8` LIKE `db_dreame_wiki`.`t_wdynamic_0`;
create table if not EXISTS `db_dreame_wiki`.`t_wdynamic_9` LIKE `db_dreame_wiki`.`t_wdynamic_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wdynamic_main` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '标题',
  `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '动态类型（0：文章，1：视频）',
  `clicks` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点击数',
  `praise` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点赞数',
  `share` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分享数',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '文章状态（0未发布 1已发布 2已删除）',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `rtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发布时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `i_rtime` (`rtime`),
  KEY `i_clicks` (`clicks`),
  KEY `i_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品内容总表';

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wpraise` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `did` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '产品百科表ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点赞时间',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除（0否，1是）',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_uid_did` (`did`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='点赞表';

create table if not EXISTS `db_dreame_wiki`.`t_wpraise_202206` LIKE `db_dreame_wiki`.`t_wpraise`;

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wtag` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `did` int(10) unsigned NOT NULL DEFAULT '0',
  `tid` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '标签',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_t_g` (`tid`,`did`) USING BTREE,
  KEY `i_g` (`did`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品内容标签表';

CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_wtopic` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '话题名',
  `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '等级',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0',
  `is_del` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除0否 1是',
  `dtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='话题表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_s_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `s_url` varchar(255) NOT NULL DEFAULT '' COMMENT '用户调研链接调研',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='内容配置表';
INSERT INTO `db_dreame`.`t_s_config` (`id`) VALUES (1);
