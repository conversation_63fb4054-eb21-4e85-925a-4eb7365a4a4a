ALTER TABLE `db_dreame`.`t_retailers`
    ADD `is_open_activity` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否开启活动 1：开启  2：不开启';


CREATE TABLE IF NOT EXISTS  `db_dreame`.`t_common_activity`
(
    `id`         int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `type`       tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '活动类型（1：线下门店活动）',
    `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
    `end_time`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
    `ctime`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`)
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='通用活动信息表';

-- 手动增加活动sql
INSERT INTO `db_dreame`.`t_common_activity` (`id`, `type`, `start_time`, `end_time`, `ctime`, `utime`) VALUES (1, 1, 1744905600, 1745164800, 1743415416, 0);

-- 后台权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (970, 'RetailersBatchOpenActivity', 53, 'back/retailers/batch-open', '批量开启门店活动', 1744881742, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (971, 'RetailersActivityInfo', 53, 'back/retailers/activity-info', '门店活动详情', 1744881742, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (972, 'RetailersPatchActivity', 53, 'back/retailers/patch-activity', '门店活动详情', 1744881742, 0, 1, 0, 0, '');