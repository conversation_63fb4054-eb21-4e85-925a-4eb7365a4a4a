-- 支持的会员等级
ALTER TABLE `db_dreame`.`t_ac_m` ADD COLUMN `level` VARCHAR (100) NOT NULL DEFAULT '' COMMENT '支持的会员等级' AFTER `sales`;

-- 修改优惠券种类
ALTER TABLE `db_dreame`.`t_activity_config` MODIFY COLUMN `grant_type` TINYINT UNSIGNED NOT NULL DEFAULT '1' COMMENT '发放方式（1：新人会员礼包 2：推荐有礼 3：优惠券领取活动 4：生日福利 5：每月领券）' AFTER `last_data`;

-- 获取
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_0` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '用户id',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
    `ac_id` int(10) NOT NULL DEFAULT '0' COMMENT '活动id',
    `market_ids` varchar(500) NOT NULL DEFAULT '' COMMENT '优惠券IDS，以逗号分割',
    `grant_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '发放方式（1：新人会员礼包 2：推荐有礼 3：优惠券领取活动 4：生日福利 5：每月领券）',
    `draw_time` int unsigned NOT NULL DEFAULT '0' COMMENT '领取时间',
    `year` int(10) NOT NULL DEFAULT 0 COMMENT '领取年份',
    `month` int(10) NOT NULL DEFAULT 0 COMMENT '领取月份',
    `day` int(10) NOT NULL DEFAULT 0 COMMENT '领取日期',
    `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY  `idx_phone_type_time` (`phone`,`grant_type`,`year`,`month`,`day`) USING BTREE
)  DEFAULT CHARSET=utf8mb4 COMMENT='优惠券活动用户领取日志表';

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_1` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_2` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_3` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_4` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_5` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_6` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_7` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_8` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_ac_draw_log_9` LIKE `db_dreame_log`.`t_ac_draw_log_0`;
