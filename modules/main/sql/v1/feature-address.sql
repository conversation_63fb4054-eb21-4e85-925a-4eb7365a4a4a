ALTER TABLE `db_dreame`.`t_address_0` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_0` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_1` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_1` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_2` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_2` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_3` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_3` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_4` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_4` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_5` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_5` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_6` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_6` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_7` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_7` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_8` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_8` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;
ALTER TABLE `db_dreame`.`t_address_9` ADD `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '是否删除：0否、1是' AFTER `status`;
ALTER TABLE `db_dreame`.`t_address_9` ADD `dtime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '删除时间' AFTER `ctime`;

ALTER TABLE `db_dreame_goods`.`t_uo_ad_2022` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2023` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2024` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2025` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2026` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2027` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2028` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2029` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_ad_2030` ADD `address_id` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '收货地址id' AFTER `order_no`;
