-- 创建异步导出任务记录
CREATE TABLE `db_dreame_log`.`t_async_export_log` (
`id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
`name` VARCHAR ( 50 ) NOT NULL DEFAULT '' COMMENT '文件名',
`export_url` VARCHAR ( 100 ) NOT NULL DEFAULT '' COMMENT '导出地址',
`export_time` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '导出时间',
`admin_user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '后台用户id',
`status` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '导出状态：0进行中、1已完成、2导出失败',
`ctime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
`utime` INT UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
PRIMARY KEY ( `id` ),
KEY `idx_name` ( `name` ),
KEY `idx_admin_user_id` ( `admin_user_id` )
) ENGINE = INNODB AUTO_INCREMENT = 1 COMMENT = '异步导出任务记录';

-- 异步导出任务记录权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (340, 'exportManager', 0, '', '导出管理', 1689696000, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (341, 'exportLogList', 340, 'back/async-export/log-list', '导出列表', 1689696000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (342, 'exportOperation', 340, 'back/async-export/export', '导出操作', 1644579242, 0, 1, 0, 0, '');