-- 库：db_dreame_goods
-- 初始化商品表的is_internal_purchase字段
UPDATE t_gtype_0 SET is_internal_purchase = 0 WHERE id >0;
UPDATE t_gtype_99 SET is_internal_purchase = 0 WHERE id >0;

-- 库：db_dreame
-- 用户员工关联表
CREATE TABLE `t_user_employee` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
   `uid` varchar(20) NOT NULL COMMENT '用户id',
   `level` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '等级：1普通微信大使、2超员工',
   `employee_id` varchar(50) NOT NULL DEFAULT '' COMMENT '员工id',
   `employee_no` varchar(50) NOT NULL DEFAULT '' COMMENT '员工号',
   `employee_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '员工状态：1在职 2离职',
   `is_blacklist` tinyint unsigned NOT NULL DEFAULT '2' COMMENT '黑名单状态（1:是 2:否）',
   `blacklist_time` int unsigned NOT NULL DEFAULT '0' COMMENT '加入黑名单时间',
   `update_level_month` varchar(10) NOT NULL DEFAULT '' COMMENT '更新等级的月份：Y-m',
   `is_tip` tinyint unsigned DEFAULT '1' COMMENT '是否提示：0不提示 1提示',
   `last_month_score` int NOT NULL DEFAULT '0' COMMENT '上个月新增分',
   `three_average_score` int DEFAULT '0' COMMENT '最近三个月的平均分，用于保级',
   `four_average_score` int DEFAULT '0' COMMENT '前三个月的平均分，用于定级',
   `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
   `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `idx_uid` (`uid`) USING BTREE,
   KEY `idx_employee_id` (`employee_id`) USING BTREE,
   KEY `idx_employee_no` (`employee_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户员工关联表';

-- 用户绑定关系表
CREATE TABLE `t_user_bind` (
   `id` int unsigned NOT NULL AUTO_INCREMENT,
   `uid` varchar(20) NOT NULL COMMENT '用户id',
   `bound_uid` varchar(20) NOT NULL COMMENT '被绑定的用户id',
   `bind_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '绑定状态（1:有效 2:失效 ）',
   `bind_time` int unsigned NOT NULL DEFAULT '0' COMMENT '绑定时间',
   `unbind_time` int unsigned NOT NULL DEFAULT '0' COMMENT '解绑时间',
   `is_new_user` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否新注册的用户：0否、1是',
   `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
   `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `idx_uid` (`uid`) USING BTREE,
   KEY `idx_bound_uid` (`bound_uid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户绑定关系表';

-- 绑定的用户订单
CREATE TABLE `t_bound_user_order` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `uid` varchar(20) NOT NULL COMMENT '用户uid',
  `bound_uid` varchar(20) NOT NULL COMMENT '被绑定的用户uid',
  `order_no` varchar(50) NOT NULL COMMENT '普通商品订单号',
  `score` int unsigned NOT NULL DEFAULT '0' COMMENT '微笑分',
  `score_status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '微笑分状态（1:待发放 2:已发放 3:已作废）',
  `order_create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '订单创建时间',
  `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`) USING BTREE,
  KEY `idx_bound_uid` (`bound_uid`) USING BTREE,
  KEY `idx_order_no` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='被绑定的用户订单';

-- db_dreame_admin
INSERT INTO
    `t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES
    (711, 'employeeManager', 710, 'user-employee/list', '员工管理', 1644994229, 0, 1, 1, 0, ''),
    (710, 'userEmployeeManager', 0, 'user-employee/manage', '微笑大使管理', 1644994229, 0, 1, 1, 0, '');
