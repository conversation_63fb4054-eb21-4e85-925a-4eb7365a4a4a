-- 抽奖活动
INSERT INTO `db_dreame_goods`.`t_draw_activity` ( `id`, `name`, `start_time`, `end_time`, `daily_free_times`, `status`, `desc`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 8, '年货节活动', 1736474400, 1737647999, 1, 1, '', 0, 1734500565, 1734500565, 0 );


-- 奖品信息
INSERT INTO `db_dreame_goods`.`t_draw_prize`
(`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`)
VALUES
    (35, '配件五折优惠券（一个月有效期）', 3, '746', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png', '', '', '', '优惠券id', 0, 1734500565, 1734500565, 0),
    (36, '扑克牌套装', 3, '747', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ac8a6d25672030049.png', '', '', '', '优惠券id', 0, 1734500565, 1734500565, 0),
    (37, '多巴胺杯-渐变蜜桃', 3, '748', 'https://wpm-cdn.dreame.tech/images/202409/66e4f5132d5221862240865', '', '', '', '优惠券id', 0, 1734500565, 1734500565, 0),
    (38, '露营椅', 3, '749', 'https://wpm-cdn.dreame.tech/images/202409/66e4f5332c1df1812240867.png', '', '', '', '优惠券id', 0, 1734500565, 1734500565, 0),
    (39, 'Pocket吹风机', 3, '750', 'https://wpm-cdn.dreame.tech/images/202409/66e4f4ec3c2562462240887.png', '', '', '', '优惠券id', 0, 1734500565, 1734500565, 0);


-- 活动奖品
INSERT INTO `db_dreame_goods`.`t_draw_activity_prize`
(`activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`,
 `is_del`, `ctime`, `utime`, `dtime`)
VALUES (8, 14, 1000000, 1000000, 0, 0, 5.01, 1, 0, 1727157271, 1727157271, 0),
       (8, 15, 100000, 10000, 0, 0, 5.00, 0, 0, 1727157271, 1727157271, 0),
       (8, 16, 4000, 4000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (8, 17, 4000, 4000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (8, 18, 3000, 3000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (8, 19, 2000, 2000, 0, 0, 9.69, 0, 0, 1727157271, 1727157271, 0),
       (8, 35, 3000, 1, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (8, 36, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (8, 37, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (8, 38, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (8, 39, 1, 1, 0, 0, 0.00, 0, 0, 1727157271, 1727157271, 0);

-- 活动任务
INSERT INTO `db_dreame_goods`.`t_draw_activity_task` ( `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 8, 1, 1, 1, 1, 0, 1727157271 , 1727157271 , 0 ),
    ( 8, 6, 2, 12, 1, 0, 1727157271 , 1727157271 , 0 );

-- 活动气氛
CREATE TABLE `db_dreame_goods`.`activity_atmosphere`
(
    `id`         int NOT NULL AUTO_INCREMENT,
    `name`       varchar(50)  DEFAULT NULL COMMENT '活动名称',
    `start_time` int NOT NULL COMMENT '开始时间',
    `end_time`   int NOT NULL COMMENT '结束时间',
    `img`        varchar(255) DEFAULT '' COMMENT '图片url',
    `pos`        tinyint      DEFAULT '1' COMMENT '位置 1：商城列表 2：详情页',
    `ctime`      int          DEFAULT '0' COMMENT '创建时间',
    `utime`      int          DEFAULT '0' COMMENT '更新时间',
    `dtime`      int          DEFAULT '0' COMMENT '删除时间',
    `is_del`     tinyint      DEFAULT '0' COMMENT '删除标识',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动气氛';

-- 活动气氛商品
CREATE TABLE `db_dreame_goods`.`activity_atmosphere_goods`
(
    `id`          int NOT NULL AUTO_INCREMENT,
    `activity_id` int NOT NULL COMMENT '活动id',
    `gid`         int NOT NULL COMMENT '商品id',
    `dtime`       int          DEFAULT '0' COMMENT '删除时间',
    `is_del`      tinyint      DEFAULT '0' COMMENT '删除标识',
    `ctime`       int DEFAULT '0' COMMENT '创建时间',
    `utime`       int DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX idx_gid (`gid`),
    INDEX idx_activity_id (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动气氛商品';

-- 活动气氛菜单
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (766, 'activityAtmosphere', 89, 'activity/atmosphere', '活动氛围配置', 1734938656, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (780, 'activityAtmosphereList', 766, 'back/activity-atmosphere/list', '列表', 1734938656, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (781, 'activityAtmosphereDetail', 766, 'back/activity-atmosphere/detail', '详情', 1734938656, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (782, 'activityAtmosphereStore', 766, 'back/activity-atmosphere/store', '新增、编辑', 1734938656, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (783, 'activityAtmosphereDel', 766, 'back/activity-atmosphere/del', '删除', 1734938656, 0, 1, 0, 0, '');