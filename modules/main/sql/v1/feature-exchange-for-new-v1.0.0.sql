-- 支持假删除取消名称唯一限制
DROP INDEX uniq_name ON `db_dreame_goods`.t_trade_in_activity;
-- 以旧换新活动表商品是否有效
alter table `db_dreame_goods`.t_trade_in_activity add column has_goods tinyint unsigned default '0' not null comment '商品是否有效 0否 1是';

-- 以旧换新商品表
create table `db_dreame_goods`.t_trade_in_goods
(
    id          int auto_increment primary key,
    category_id int          default 0  not null comment '分类id',
    gid         int          default 0  not null comment '商品id',
    sids        varchar(255) default '' not null comment '规格ids',
    ctime       int          default 0  not null comment '创建时间',
    utime       int          default 0  not null comment '更新时间',
    constraint idx_gid unique (gid),
    index idx_trade_in_goods (category_id, gid, sids)
) comment '添加以旧换新商品表' charset = utf8mb4 charset = utf8mb4;

-- 以旧换新活动关联表
create table `db_dreame_goods`.`t_trade_in_activity_goods`
(
    id              int auto_increment primary key,
    activity_id     int default 0 not null comment '活动id',
    gid             int default 0 not null comment '商品id',
    sid             int default 0 not null comment '规格id',
    price           decimal(10, 2)   default 0.00 not null comment '价格',
    underline_price decimal(10, 2)   default 0.00 not null comment '划线价格',
    is_del          tinyint unsigned default '0' not null comment '是否删除 0否 1是',
    ctime           int default 0 not null comment '创建时间',
    utime           int default 0 not null comment '更新时间',
    dtime           int default 0 not null comment '删除时间',
    index           idx_gid(gid),
    index           idx_activity_gid_sid_isdel(activity_id, gid, sid, is_del)
) comment '添加以旧换活动商品关联表' charset = utf8mb4;

-- 以旧换分类表
create table `db_dreame_goods`.`t_trade_in_category`
(
    id     int auto_increment primary key,
    name   varchar(255) default '' not null comment '分类名称',
    is_del tinyint unsigned default '0' not null comment '是否删除 0否 1是',
    ctime  int          default 0  not null comment '创建时间',
    utime  int          default 0  not null comment '更新时间',
    dtime  int          default 0  not null comment '删除时间'
) comment '添加以旧换分类表' charset = utf8mb4;

INSERT INTO db_dreame_goods.t_trade_in_category (id, name, is_del, ctime, utime, dtime) VALUES (1, '扫地机', 0, 0, 0, 0);
INSERT INTO db_dreame_goods.t_trade_in_category (id, name, is_del, ctime, utime, dtime) VALUES (2, '洗地机', 0, 0, 0, 0);
INSERT INTO db_dreame_goods.t_trade_in_category (id, name, is_del, ctime, utime, dtime) VALUES (3, '吸尘器', 0, 0, 0, 0);
INSERT INTO db_dreame_goods.t_trade_in_category (id, name, is_del, ctime, utime, dtime) VALUES (4, '个护系列', 0, 0, 0, 0);


-- 添加菜单
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (767, 'TradeIn', 0, '', '以旧换新', 1735351302, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (768, 'tradeInIndex', 767, 'tradeIn/index', '活动列表', 1735351212, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (770, 'tradeInList', 768, 'back/trade-in-activity/list', '活动列表', 1735351212, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (771, 'tradeInDetail', 768, 'back/trade-in-activity/detail', '活动详情', 1735351212, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (772, 'tradeInStore', 768, 'back/trade-in-activity/store', '活动新增、编辑', 1735351212, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (773, 'tradeInDel', 768, 'back/trade-in-activity/del', '活动删除', 1735351212, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (774, 'tradeInStatus', 768, 'back/trade-in-activity/status', '活动上下架', 1735351212, 0, 1, 0, 0, '');

INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (769, 'tradeInGood', 767, 'tradeIn/good', '商品管理', 1735351251, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (775, 'tradeInGoodList', 769, 'back/trade-in-goods/list', '商品列表', 1735351251, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (776, 'tradeInGoodDetail', 769, 'back/trade-in-goods/detail', '商品详情', 1735351251, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (777, 'tradeInGoodStore', 769, 'back/trade-in-goods/store', '商品详情', 1735351251, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (778, 'tradeInGoodDel', 769, 'back/trade-in-goods/del', '商品删除', 1735351251, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (779, 'tradeInGoodRelate', 769, 'back/trade-in-goods/relate-activity', '商品关联活动', 1735351251, 0, 1, 0, 0, '');

