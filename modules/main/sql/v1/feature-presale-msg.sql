CREATE DATABASE IF NOT EXISTS `db_dreame_msg`;

CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202301` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int NOT NULL DEFAULT '0' COMMENT '用户id',
    `phone` varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '推送平台（1：短信 2：微信公众号 3：APP）',
    `send_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '推送时间',
    `resource` text NOT NULL COMMENT '推送需要内容 json数据',
    `is_send`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否推送（0：未推送; 1：已推送）',
    `need_send`  tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否要推送',
    `is_del` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除0否 1是',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `i_type` (`type`) USING BTREE,
    KEY `i_send_time` (`send_time`) USING BTREE,
    KEY `i_is_send` (`is_send`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户推送信息';


CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202302` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202303` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202304` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202305` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202306` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202307` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202308` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202309` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202310` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202311` LIKE `db_dreame_msg`.`t_user_msg_202301`;
CREATE TABLE IF NOT EXISTS `db_dreame_msg`.`t_user_msg_202312` LIKE `db_dreame_msg`.`t_user_msg_202301`;


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_usermsg_err_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `function` varchar(50) NOT NULL COMMENT '方法名',
    `rags` text NOT NULL COMMENT '参数集合',
    `nums` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败次数，默认为1',
    `msg` text NOT NULL COMMENT '返回信息',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败=1 处理成功后改成2 默认1',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户消息存库数据';
