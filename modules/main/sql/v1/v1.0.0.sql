
CREATE DATABASE IF NOT EXISTS `db_dreame_no_auth`;

CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_users_main` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户UUID',
    `openudid` varchar(50) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识',
    `unionid` varchar(200) NOT NULL DEFAULT '' COMMENT '用户在开放平台的唯一标识符',
    `reg_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
    `user_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户类型',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `openudid` (`openudid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户主表';

CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_0` (
        `id` int unsigned NOT NULL AUTO_INCREMENT,
        `user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户UUID',
        `nick` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '昵称',
        `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '真实姓名',
        `birthday` int NOT NULL DEFAULT '0' COMMENT '生日',
        `area` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '所在区域',
        `status` tinyint(1) DEFAULT '0' COMMENT '状态 0正常 1冻结 2平台禁用',
        `age` tinyint DEFAULT NULL COMMENT '年龄',
        `avatar` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT '' COMMENT '头像',
        `active_time` int DEFAULT NULL COMMENT '最后登录时间',
        `sex` tinyint(1) DEFAULT '0' COMMENT '性别 0保密 1男 2女',
        `is_new_gift` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '新人礼包',
        `reg_reward_point` int unsigned NOT NULL DEFAULT '0' COMMENT '注册已奖励积分',
        `reg_reward_count` int unsigned NOT NULL DEFAULT '0' COMMENT '注册奖励已发放次数',
        `reg_popup_count` int unsigned NOT NULL DEFAULT '0' COMMENT '注册奖励已弹窗次数',
        `coupon_reward_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下单优惠券奖励已发放次数',
        `coupon_popup_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下单优惠券奖励已弹窗次数',
        `point_reward_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下单积分奖励已发放次数',
        `point_popup_count` int unsigned NOT NULL DEFAULT '0' COMMENT '下单积分奖励已弹窗次数',
        PRIMARY KEY (`id`) USING BTREE,
        UNIQUE KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='用户详情表';



CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_1` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_2` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_3` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_4` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_5` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_6` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_7` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_8` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_9` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_10` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_11` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_12` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_13` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_14` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_15` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_16` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_17` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_18` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_19` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_20` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_21` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_22` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_23` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_24` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_25` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_26` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_27` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_28` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_29` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_30` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_31` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_32` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_33` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_34` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_35` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_36` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_37` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_38` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_39` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_40` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_41` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_42` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_43` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_44` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_45` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_46` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_47` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_48` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_49` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_50` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_51` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_52` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_53` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_54` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_55` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_56` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_57` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_58` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_59` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_60` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_61` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_62` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_63` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_64` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_65` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_66` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_67` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_68` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_69` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_70` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_71` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_72` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_73` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_74` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_75` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_76` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_77` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_78` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_79` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_80` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_81` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_82` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_83` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_84` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_85` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_86` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_87` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_88` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_89` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_90` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_91` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_92` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_93` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_94` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_95` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_96` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_97` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_98` LIKE `db_dreame_no_auth`.`t_user_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_no_auth`.`t_user_99` LIKE `db_dreame_no_auth`.`t_user_0`;

CREATE TABLE `db_dreame_no_auth`.`t_guide`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT,
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户UUID',
    `name`    varchar(50) NOT NULL DEFAULT '' COMMENT '名字',
    `store`   varchar(50) NOT NULL DEFAULT '' COMMENT '门店',
    `status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `index_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='导购表';



CREATE TABLE `db_dreame_no_auth`.`t_user_extend`
(
    `id`       int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` varchar(50) NOT NULL DEFAULT '' COMMENT '用户UUID',
    `source`   int(11) NOT NULL DEFAULT '0' COMMENT '来源',
    `ctime`    int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
    `guide_id` varchar(50) NOT NULL DEFAULT '' COMMENT '导购员id（只是邀请注册，并非实际导购关系）',
    `r_id`     varchar(50) NOT NULL DEFAULT '' COMMENT '推荐人id（只是邀请绑定，并非实际推荐关系）',
    `tag`      varchar(50)  NOT NULL DEFAULT '' COMMENT '标签',
    `main_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '迁移主表返回的用户ID',
    `card`     varchar(100) NOT NULL DEFAULT '' COMMENT 'crm系统下发的card',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `index_uid` (`user_id`) USING BTREE,
    KEY        `card` (`card`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户扩展表';


CREATE TABLE `db_dreame_no_auth`.`t_user_guide`
(
    `id`       int(11) NOT NULL AUTO_INCREMENT,
    `user_id`  varchar(50) NOT NULL DEFAULT '' COMMENT '用户id',
    `guide_id` varchar(50) NOT NULL DEFAULT '' COMMENT '导购id',
    `ctime`    int(11) NOT NULL DEFAULT '0' COMMENT '绑定时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `index_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户绑定导购表';


CREATE TABLE `db_dreame_no_auth`.`t_user_recommend`
(
    `id`                int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id`           varchar(50) NOT NULL DEFAULT '' COMMENT '用户id',
    `r_id`              varchar(50) NOT NULL DEFAULT '' COMMENT '推荐人id',
    `bind_reward_point` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '绑定已奖励积分',
    `reward_count`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '绑定奖励已发放次数',
    `popup_count`       int(10) unsigned NOT NULL DEFAULT '0' COMMENT '绑定奖励已弹窗次数',
    `ctime`             int(10) unsigned NOT NULL DEFAULT '0' COMMENT '绑定时间',
    `expire_time`       int(10) unsigned NOT NULL DEFAULT '0' COMMENT '绑定到期时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_u` (`user_id`) USING BTREE,
    KEY  `i_r` (`r_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户绑定推荐人表';
