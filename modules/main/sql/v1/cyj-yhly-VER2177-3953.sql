CREATE TABLE `db_dreame`.`t_source_config`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `source_code` varchar(30)  NOT NULL DEFAULT '' COMMENT 'code值',
    `param`       varchar(100) NOT NULL DEFAULT '' COMMENT '参数',
    `remark`      varchar(100) NOT NULL DEFAULT '' COMMENT '备注',
    `is_del`      tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime`       int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime`       int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_code` (`source_code`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户来源配置表';



CREATE TABLE `db_dreame`.`t_source_extend`
(
    `id`             int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`        int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `source_union`   varchar(50)  NOT NULL DEFAULT '' COMMENT '来源union',
    `source_euid`    varchar(50)  NOT NULL DEFAULT '' COMMENT '来源euid',
    `source_referer` varchar(500) NOT NULL DEFAULT '' COMMENT '来源referer',
    `ctime`          int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`          int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_user_id` (`user_id`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户来源拓展表';


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (380, 'SourceList', 48, 'user/source-list', '来源列表', 1698115812, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (381, 'UserSourceList', 380, 'back/data/list', '获取来源列表权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (382, 'SourceSave', 380, 'back/data/source-save', '来源增改权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (383, 'SourceDel', 380, '/back/data/source-del', '来源删除权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (384, 'OrderSourceList', 127, 'back/data/source-list', '订单来源权限', 1698115812, 0, 1, 0, 0, '');
