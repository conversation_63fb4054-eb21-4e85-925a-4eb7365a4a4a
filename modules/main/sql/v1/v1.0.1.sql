ALTER TABLE `db_dreame`.`t_user_extend`  add `is_sync_app` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否同步APP（0：未同步，1：已同步）';
ALTER TABLE `db_dreame`.`t_user_extend`  add `sync_app_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '同步APP时间';
ALTER TABLE `db_dreame`.`t_user_extend`  add INDEX idx_is_sync_app(is_sync_app);

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_app_err_log` (
     `id` int(11) NOT NULL AUTO_INCREMENT,
     `function` varchar(50) NOT NULL COMMENT '方法名',
     `rags` text NOT NULL COMMENT '参数集合',
     `nums` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败次数，默认为1',
     `msg` text NOT NULL COMMENT '返回信息',
     `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败=1 处理成功后改成2 默认1',
     `ctime` int(10) NOT NULL COMMENT '加入时间',
     `utime` int(10) NOT NULL COMMENT '更新时间',
     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='app错误日志';
