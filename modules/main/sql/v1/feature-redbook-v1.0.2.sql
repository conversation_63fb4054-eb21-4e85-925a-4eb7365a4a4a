ALTER TABLE `db_dreame_log`.t_comment_task
    ADD COLUMN coupon_id VARCHAR(50) default '' COMMENT '卡券ID';

-- copy1 的数据
INSERT INTO db_dreame_admin.t_uri_copy1 (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (602, 'PostReview', 600, 'postPurchase/postReview', '晒单审核管理', 1747733940, 0, 1, 1, 0, '', 0);
INSERT INTO db_dreame_admin.t_uri_copy1 (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (601, 'PostTask', 600, 'postPurchase/postTask', '晒单任务管理', 1747733949, 0, 1, 1, 0, '', 0);
INSERT INTO db_dreame_admin.t_uri_copy1 (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (600, 'PostPurchase', 6, '', '晒单管理', 1747807029, 0, 1, 1, 0, '', 0);

-- 晒单任务管理
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ('postTaskList', 601, 'admin-api/v1/post/task/list', '晒单任务列表', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ('postTaskCreate', 601, 'admin-api/v1/post/task/create', '新建晒单任务', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ( 'postTaskDelete', 601, 'admin-api/v1/post/task/delete', '删除晒单任务', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ( 'postTaskUpdate', 601, 'admin-api/v1/post/task/update', '晒单任务更新', 1747733949, 0, 1, 0, 0, '');
--  晒单审核更新
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ('postAuditUpdate', 602, 'admin-api/v1/post/audit/update', '晒单审核更新', 1747733949, 0, 1, 0 , 0, '');
INSERT INTO db_dreame_admin.t_uri_copy1 ( name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES ('postAuditSend', 602, 'back/comment-audit/send', '晒单审核手动发放', 1747733949, 0, 1, 0 , 0, '');

-- admin t_uri 的数据
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1000, 'PostPurchase', 0, '', '晒单管理', 1747807029, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1001, 'PostTask', 1000, 'postPurchase/postTask', '晒单任务管理', 1747733949, 0, 1, 1, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1002, 'PostReview', 1000, 'postPurchase/postReview', '晒单审核管理', 1747733940, 0, 1, 1, 0, '');
-- 晒单任务管理
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1003, 'postTaskList', 1001, 'admin-api/v1/post/task/list', '晒单任务列表', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1004, 'postTaskCreate', 1001, 'admin-api/v1/post/task/create', '新建晒单任务', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1005, 'postTaskDelete', 1001, 'admin-api/v1/post/task/delete', '删除晒单任务', 1747733949, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1006, 'postTaskUpdate', 1001, 'admin-api/v1/post/task/update', '晒单任务更新', 1747733949, 0, 1, 0, 0, '');
--  晒单审核更新
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1007, 'postAuditUpdate', 1002, 'admin-api/v1/post/audit/update', '晒单审核更新', 1747733949, 0, 1, 0 , 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (1008, 'postAuditSend', 1002, 'back/comment-audit/send', '晒单审核手动发放', 1747733949, 0, 1, 0 , 0, '');

