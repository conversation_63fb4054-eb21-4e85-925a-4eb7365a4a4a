-- 自定义价格表

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gini` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `sku` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'SKU',
    `stock` int unsigned NOT NULL DEFAULT '0' COMMENT '限量数量',
    `sales` int unsigned NOT NULL DEFAULT '0' COMMENT '销量',
    `wait` int unsigned NOT NULL DEFAULT '0' COMMENT '未付款数量',
    `tag`  varchar(255) NOT NULL DEFAULT '' COMMENT '价格标签',
    `status` tinyint(1) DEFAULT '0' COMMENT '状态（0：未执行;1:执行中;2:封锁中;3：执行完成;99：中断执行）',
    `iniprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格（分）',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1删除0正常',
    `stime` int unsigned NOT NULL DEFAULT '0' COMMENT '生效开始时间',
    `etime` int unsigned NOT NULL DEFAULT '0' COMMENT '生效结束时间',
    `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品自定义价格表';

ALTER TABLE `db_dreame_goods`.`t_gini` ADD `no_limit` tinyint(1) DEFAULT '0' COMMENT '状态（0：限制库存;1:不限制库存）' after `wait`;

--//新建自定义价格字段
ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_10` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_11` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_12` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_13` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_14` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_15` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_16` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_17` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_18` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_19` ADD `gini_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '自定义价格ID' after `num`;


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (258, 'selfPriceManager', 58, 'goods/selfPrice', '自定义价格', 1644579242, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (259, 'selfPriceList', 258, 'back/goods-ini-price/list', '自定义价格列表', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (260, 'selfPriceSave', 258, 'back/goods-ini-price/save', '自定义价格创建/编辑', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (261, 'selfPriceLock', 258, 'back/goods-ini-price/lock', '自定义价格锁定', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (262, 'selfPriceBreak', 258, 'back/goods-ini-price/force-break', '自定义价格中断', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (263, 'selfPriceDel', 258, 'back/goods-ini-price/del', '自定义价格删除', 1644579242, 0, 1, 0, 0, '');

