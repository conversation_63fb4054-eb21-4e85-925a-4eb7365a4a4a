ALTER TABLE `db_dreame`.`t_banner`
    ADD COLUMN `new_image` varchar(255) NOT NULL DEFAULT '' COMMENT '新版本图片地址' after `image`,
ADD COLUMN `drop_position` tinyint(1) NOT NULL DEFAULT '1' COMMENT '投放位置(1、商城首页 2、探觅)' after `platform`;


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`,`rank`, `ext_uri`)
VALUES (310, 'bannerRelease', 66, 'back/banner/release', '获取投放位置权限', 1685433485, 0, 1, 0, 0, '');


CREATE TABLE IF NOT EXISTS `db_dreame_wiki`.`t_role` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID自增',
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '角色名称',
    `icon` varchar(255)  NOT NULL DEFAULT '' COMMENT '角色图标',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`)
    )  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='内容管理角色表';

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`,`rank`, `ext_uri`)
VALUES (314, 'contentRolesManager', 180, '/content/roles', '内容角色管理', 1685497391, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`,`rank`, `ext_uri`)
VALUES (315, 'roleList', 314, 'back/wiki/role-list', '内容角色列表权限', 1685497391, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`,`rank`, `ext_uri`)
VALUES (316, 'roleDel', 314, 'back/wiki/role-del', '内容角色删除权限', 1685497391, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`,`rank`, `ext_uri`)
VALUES (317, 'roleSave', 314, 'back/wiki/role-save', '内容角色添加权限', 1685497391, 0, 1, 0, 0, '');



ALTER TABLE `db_dreame_wiki`.`t_wpraise` MODIFY `user_id` varchar(100)  NOT NULL DEFAULT '0' COMMENT '用户ID';


ALTER TABLE `db_dreame_wiki`.`t_wdynamic_0`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_1`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_2`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_3`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_4`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_5`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_6`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_7`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_8`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

ALTER TABLE `db_dreame_wiki`.`t_wdynamic_9`
    ADD COLUMN `avatar` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '作者头像' AFTER `author`,
ADD COLUMN `images` TEXT NULL COMMENT '图片集合' AFTER `video`,
ADD COLUMN `role_id` INT(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '角色id' AFTER `did`;

