ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `is_recommend` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐(0：否;1：是)' after `atype`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `is_recommend` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐(0：否;1：是)' after `price`;

ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `introduce` varchar(255) NOT NULL DEFAULT '' COMMENT '简介' after `is_recommend`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `introduce` varchar(255) NOT NULL DEFAULT '' COMMENT '简介' after `is_recommend`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `parameters` text CHARACTER SET utf8 NOT NULL  after `introduce`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `parameters` text CHARACTER SET utf8 NOT NULL  after `introduce`;

ALTER TABLE `db_dreame`.`t_banner` ADD `platform` tinyint(1) unsigned NOT NULL DEFAULT '99' COMMENT '平台（1:微信小程序;2:app/H5平台;99:全平台）' AFTER `sort`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `platform` tinyint(1) unsigned NOT NULL DEFAULT '99' COMMENT '平台（1:微信小程序;2:app/H5平台;99:全平台）' AFTER `parameters`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `platform` tinyint(1) unsigned NOT NULL DEFAULT '99' COMMENT '平台（1:微信小程序;2:app/H5平台;99:全平台）' AFTER `parameters`;

ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD INDEX idx_platform(`platform`);
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD INDEX idx_platform(`platform`);


-- //支付模块
ALTER TABLE `db_dreame_goods`.`t_opay_2022` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2023` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2024` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2025` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2026` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2027` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2028` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2029` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;
ALTER TABLE `db_dreame_goods`.`t_opay_2030` ADD `pay_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '支付方式（1:微信小程序;2:微信H5支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2022` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2023` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2024` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2025` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2026` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2027` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2028` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2029` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;
ALTER TABLE `db_dreame_goods`.`t_opay_2030` ADD `h5_url` varchar(255) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径' AFTER `pay_type`;




-- alter table `db_dreame_goods`.`t_om_2022` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2023` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2024` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2025` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2026` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2027` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2028` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2029` drop column `pay_type`;
-- alter table `db_dreame_goods`.`t_om_2030` drop column `pay_type`;


ALTER TABLE `db_dreame_goods`.`t_orefund_0` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';
ALTER TABLE `db_dreame_goods`.`t_orefund_1` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';
ALTER TABLE `db_dreame_goods`.`t_orefund_2` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';
ALTER TABLE `db_dreame_goods`.`t_orefund_3` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';
ALTER TABLE `db_dreame_goods`.`t_orefund_4` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';
ALTER TABLE `db_dreame_goods`.`t_plumbing_refund` modify `describe` varchar(500) NOT NULL DEFAULT '' COMMENT '补充描述';


ALTER TABLE `db_dreame`.`t_users_main` modify `openudid` varchar(100) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识';commit;
ALTER TABLE `db_dreame_no_auth`.`t_users_main` modify `openudid` varchar(100) NOT NULL DEFAULT '' COMMENT '开放平台唯一标识';commit;


-- //新增 11.25 16:43
ALTER TABLE `db_dreame_goods`.`t_opay_2022` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2023` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2024` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2025` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2026` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2027` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2028` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2029` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
ALTER TABLE `db_dreame_goods`.`t_opay_2030` MODIFY `h5_url` varchar(500) NOT NULL DEFAULT '' COMMENT 'H5调起支付页面路径';
