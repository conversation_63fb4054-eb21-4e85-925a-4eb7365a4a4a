ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD  INDEX idx_advance_status_code(`advance_status_code`);


ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD  INDEX idx_advance_status_code(`advance_status_code`);

ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD  INDEX idx_advance_status_code(`advance_status_code`);

ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD  INDEX idx_advance_status_code(`advance_status_code`);

ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD  INDEX idx_advance_status_code(`advance_status_code`);


ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD  INDEX idx_advance_status_code(`advance_status_code`);

ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD  INDEX idx_advance_status_code(`advance_status_code`);

ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD  INDEX idx_advance_status_code(`advance_status_code`);


ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD  `state` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '快递单当前状态' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD  `advance_status_code` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '物流高级状态名称值' after `state`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD  `advance_status` varchar(50) NOT NULL DEFAULT '' COMMENT '物流高级状态名称' after `advance_status_code`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD  INDEX idx_state(`state`);
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD  INDEX idx_advance_status_code(`advance_status_code`);

