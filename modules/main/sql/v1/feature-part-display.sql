CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_cate` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '类目名',
    `tag_name` varchar(50)  DEFAULT '' COMMENT '类目别名 pid为0时不能为空',
    `icon` varchar(255)  NOT NULL DEFAULT '' COMMENT '类目图标',
    `pid`int(11) NOT NULL DEFAULT '0' COMMENT 'pid',
    `level` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '类目等级',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `view` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态 0不显示 1显示',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    `dtime` int(10) NOT NULL  DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品类目表';



CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gcate` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品sku',
    `main_sku` varchar(500) NOT NULL DEFAULT '' COMMENT '主机sku',
    `c_id` int(11) NOT NULL DEFAULT 0 COMMENT '分类id',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    `dtime` int(10) NOT NULL  DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品分类绑定表';






INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (280, 'goodsCate', 0, 'back/cate/list', '商品类目', 1678245053, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (281, 'cateModify', 280, 'back/cate/modify', '商品类目添加编辑', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (282, 'cateDel', 280, 'back/cate/delete', '商品类目删除', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (283, 'catUpdate', 280, 'back/cate/update', '商品类目删除转移类目', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (284, 'cateDrag', 280, 'back/cate/cate-drag', '商品类目拖拽', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (285, 'cateDetail', 280, 'back/cate/detail', '商品类目详情', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (286, 'goodsPartList', 59, 'back/goods/part-list', '商品配件列表', 1680588846, 0, 1, 0, 0, '');


ALTER TABLE `db_dreame_goods`.`t_gcate` ADD `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态 0上架 1下架' after `c_id`;