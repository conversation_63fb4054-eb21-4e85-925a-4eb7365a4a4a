
DROP TABLE IF EXISTS `t_draw_activity`;
CREATE TABLE `t_draw_activity`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`name` varchar(50) NOT NULL DEFAULT '' COMMENT '活动名称',
`start_time` bigint NOT NULL DEFAULT 0 COMMENT '活动开始时间',
`end_time` bigint NOT NULL DEFAULT 0 COMMENT '活动结束时间',
`daily_free_times` tinyint NOT NULL DEFAULT 0 COMMENT '每日免费抽奖次数',
`status` tinyint NOT NULL DEFAULT 1 COMMENT '活动状态 1开启 2停用',
`desc` text NOT NULL COMMENT '活动详情',
`is_del` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
`dtime` int NOT NULL DEFAULT 0 COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `idx_name`(`name` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖活动表';


DROP TABLE IF EXISTS `t_draw_activity_prize`;
CREATE TABLE `t_draw_activity_prize`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`activity_id` int NOT NULL DEFAULT 0 COMMENT '活动id',
`prize_id` int NOT NULL DEFAULT 0 COMMENT '奖品id',
`prize_num` int NOT NULL DEFAULT 0 COMMENT '奖品数量',
`prize_limit` int NOT NULL DEFAULT 0 COMMENT '中奖限制次数（活动期间，每人最多中奖次数）',
`prize_issue_num` int NOT NULL DEFAULT 0 COMMENT '奖品发放数量',
`prize_lock_num` int NOT NULL DEFAULT 0 COMMENT '奖品锁定数量',
`rate` decimal(5, 2) NOT NULL DEFAULT 0 COMMENT '中奖概率',
`is_default` tinyint NOT NULL DEFAULT 0 COMMENT '是否默认奖品 0否 1是',
`is_del` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
`dtime` int NOT NULL DEFAULT 0 COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
INDEX `idx_activity_id`(`activity_id` ASC) USING BTREE,
INDEX `idx_prize_id`(`prize_id` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖活动-奖品（关联）表';


DROP TABLE IF EXISTS `t_draw_activity_prize_record`;
CREATE TABLE `t_draw_activity_prize_record`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`activity_id` int NOT NULL DEFAULT 0 COMMENT '活动id',
`prize_id` int NOT NULL DEFAULT 0 COMMENT '奖品id',
`user_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
`user_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '用户手机号',
`prize_name` varchar(50) NOT NULL DEFAULT '' COMMENT '奖品名称',
`prize_type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '奖品类型',
`prize_value` varchar(50) NOT NULL DEFAULT '' COMMENT '奖品的值',
`prize_num` smallint UNSIGNED NOT NULL DEFAULT 1 COMMENT '奖品数量',
`draw_time` bigint NOT NULL DEFAULT 0 COMMENT '抽奖时间',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
INDEX `idx_activity_id`(`activity_id` ASC) USING BTREE,
INDEX `idx_prize_id`(`prize_id` ASC) USING BTREE,
INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
INDEX `idx_user_phone`(`user_phone` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖活动-抽奖记录表';


DROP TABLE IF EXISTS `t_draw_activity_task`;
CREATE TABLE `t_draw_activity_task`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`activity_id` int NOT NULL DEFAULT 0 COMMENT '活动id',
`task_id` int NOT NULL DEFAULT 0 COMMENT '任务id',
`task_limit_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '任务限制周期类型：1每天 2活动期间',
`task_limit` int NOT NULL DEFAULT 0 COMMENT '任务限制次数',
`draw_times` smallint NOT NULL DEFAULT 0 COMMENT '抽奖次数',
`is_del` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
`dtime` int NOT NULL DEFAULT 0 COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
INDEX `idx_activity_id`(`activity_id` ASC) USING BTREE,
INDEX `idx_task_id`(`task_id` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖活动-任务（关联）表';


DROP TABLE IF EXISTS `t_draw_activity_task_record`;
CREATE TABLE `t_draw_activity_task_record`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`activity_id` int NOT NULL DEFAULT 0 COMMENT '活动id',
`task_id` int NOT NULL DEFAULT 0 COMMENT '任务id',
`user_id` int NOT NULL DEFAULT 0 COMMENT '用户id',
`user_phone` varchar(20) NOT NULL DEFAULT '' COMMENT '用户手机号',
`task_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '任务类型：1默认任务 2常规任务（ 同draw_task表中的任务类型）',
`draw_times` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '抽奖次数',
`used_draw_times` int NOT NULL DEFAULT 0 COMMENT '已使用的抽奖次数',
`complete_time` bigint NOT NULL DEFAULT 0 COMMENT '任务完成时间',
`draw_expire_time` bigint NOT NULL DEFAULT 0 COMMENT '抽奖过期时间',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE,
INDEX `idx_activity_id`(`activity_id` ASC) USING BTREE,
INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
INDEX `idx_user_phone`(`user_phone` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖活动-任务（完成）记录表';


DROP TABLE IF EXISTS `t_draw_external_coupon`;
CREATE TABLE `t_draw_external_coupon`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`name` varchar(50) NOT NULL DEFAULT '' COMMENT '券名称',
`type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '券类型 1轻喜到家',
`code` varchar(50) NOT NULL DEFAULT '' COMMENT '券码',
`status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1未发放 2已发放',
`issue_time` int NOT NULL DEFAULT 0 COMMENT '发放时间',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
PRIMARY KEY (`id`) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖第三方（外部）券码表';


DROP TABLE IF EXISTS `t_draw_prize`;
CREATE TABLE `t_draw_prize`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`name` varchar(50) NOT NULL DEFAULT '' COMMENT '奖品名称',
`type` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '奖品类型 1谢谢参与 2积分 3优惠券 4兑换券（第三方券码）',
`value` varchar(50) NOT NULL DEFAULT '' COMMENT '奖品的值',
`image` varchar(255) NOT NULL DEFAULT '' COMMENT '奖品图标',
`default_image` varchar(255) NOT NULL DEFAULT '' COMMENT '抽奖默认图片',
`active_image` varchar(255) NOT NULL DEFAULT '' COMMENT '抽奖中奖图片',
`desc` varchar(2000) NOT NULL DEFAULT '' COMMENT '奖品描述',
`remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
`is_del` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
`dtime` int NOT NULL DEFAULT 0 COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖奖品表';


DROP TABLE IF EXISTS `t_draw_task`;
CREATE TABLE `t_draw_task`  (
`id` int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
`name` varchar(50) NOT NULL DEFAULT '' COMMENT '任务名称',
`task_code` varchar(50) NOT NULL DEFAULT '' COMMENT '任务CODE',
`type` tinyint NOT NULL DEFAULT 1 COMMENT '任务类型：1默认任务 2常规任务',
`desc` varchar(500) NOT NULL DEFAULT '' COMMENT '任务描述',
`status` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '任务状态 1上架 2下架',
`is_del` tinyint UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
`ctime` int NOT NULL DEFAULT 0 COMMENT '创建时间',
`utime` int NOT NULL DEFAULT 0 COMMENT '更新时间',
`dtime` int NOT NULL DEFAULT 0 COMMENT '删除时间',
PRIMARY KEY (`id`) USING BTREE,
UNIQUE INDEX `idx_name`(`name` ASC) USING BTREE,
UNIQUE INDEX `idx_task_code`(`task_code` ASC) USING BTREE
)AUTO_INCREMENT = 1 COMMENT = '抽奖任务表';


-- 初始化数据
-- 抽奖活动 DONE
INSERT INTO `t_draw_activity` (`id`, `name`, `start_time`, `end_time`, `daily_free_times`, `status`, `desc`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (1, '双十一抽奖活动', 1697990400, 1699718399, 1, 1, '', 0, 1698076800, 1698076800, 0);
-- 奖品 DONE
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (1, '谢谢参与', 1, '', 'https://wpm-cdn.dreame.tech/images/202310/652cf90feed119784014037.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded7d8c8285752255158.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee15193681033122930.png', '', '', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (2, '100积分', 2, '100', 'https://wpm-cdn.dreame.tech/images/202310/652cf8ffeac549614013882.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded806dd3b4492206431.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee09d9aae8912247652.png', '', '积分值', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (3, '500积分', 2, '500', 'https://wpm-cdn.dreame.tech/images/202310/652cf8d8cdcbd8424014144.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded87a420c6722322956.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee18050140203107435.png', '', '积分值', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (4, '1111积分', 2, '1111', 'https://wpm-cdn.dreame.tech/images/202310/652cf9061f19a1272257798.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded76db5e68983122972.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee122156e1363099094.png', '', '积分值', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (5, '韶光pro香槟金吹风机', 3, '406', 'https://wpm-cdn.dreame.tech/images/202310/6532479d100870656863287.png', 'https://wpm-cdn.dreame.tech/images/202310/6532479b06a900277109830.png', 'https://wpm-cdn.dreame.tech/images/202310/653247a2467522886719320.png', '', '优惠券id', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (6, '户外推车', 3, '403', 'https://wpm-cdn.dreame.tech/images/202310/652cf8ecb15d17264013987.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded6ecc4fd8362271528.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee03882885572220139.png', '', '优惠券id', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (7, '洗地机500ml清洁液兑换券', 3, '404', 'https://wpm-cdn.dreame.tech/images/202310/652cf8cd0a85e0434013776.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded7a540b53442238913.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee07110df0692239442.png', '', '优惠券id', 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_prize` (`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (8, '轻喜到家269元清洁兑换券', 4, '', 'https://wpm-cdn.dreame.tech/images/202310/652cf8dd175d40954014220.png', 'https://wpm-cdn.dreame.tech/images/202310/652ded83d5a998752239487.png', 'https://wpm-cdn.dreame.tech/images/202310/652dee0f9e0ce6473120850.png', '轻喜到家4小时上门服务内容详情<br>服务定位：高端家庭保洁全屋10区保洁+5项家务；<br>服务时长：每次4小时<br>服务时间：8:00-12:00或14：00-18：00<br>①服务工具：<br>（1）进口工具组：包含航空级拉杆式工具包、保洁水箱、万能拖、伸缩杆、瓷砖刷、八色保洁布、玻璃刮、小黄刮、双头刮铲等<br>（2）专业免费耗材：洗洁精、苏打粉、去污膏、含氯消毒水、75%酒精、84 消毒泡腾片\r\n服务标准：根据客户个性化需求，在服务中完成日常居家环境里的 10区基础保洁（卧房、厨房、卫生间、客厅、餐厅、书房、儿童房、衣帽间、入户区、阳台）+5项家务（床铺整理、衣物整理（不包含橱柜、衣柜等内部整理）、台面整理、鞋柜整理、厨房收纳整理）<br>服务面积：120平方以内<br>②用户领取兑换码流程：\r\n用户抽中轻喜到家兑换码需于2023年12月31日之前进行兑换，请保管好券码尽快使用，以免过期失效<br>③用户兑换流程：<br>微信公众号关注“轻喜到家”，点击右下角预约服务-个人中心-优惠券-下拉底部兑换-0元支付下单即可，优惠券自兑换后60天内有效<br>④该项服务由“深圳轻喜到家科技有限公司”提供，若产生任何售后纠纷由轻喜到家进行处理', '', 0, 1698076800, 1698076800, 0);
-- 抽奖活动-奖品 DONE
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (1, 1, 1, 1000000, 1000000, 0, 0, 23.88, 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (2, 1, 2, 10000, 10000, 0, 0, 50.00, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (3, 1, 3, 1000, 1000, 0, 0, 20.00, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (4, 1, 4, 200, 1, 0, 0, 5.00, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (5, 1, 5, 1, 1, 0, 0, 0.02, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (6, 1, 6, 5, 1, 0, 0, 0.10, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (7, 1, 7, 20, 1, 0, 0, 0.50, 0, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_prize` (`id`, `activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (8, 1, 8, 10, 1, 0, 0, 0.50, 0, 0, 1698076800, 1698076800, 0);
-- 任务 DONE
INSERT INTO `t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (1, '每天免费抽奖任务', 'DAILY_FREE', 1, '每人每天免费获得1次参与机会，当日不用就清零', 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (2, '购买追觅主机产品', 'BUY_HOST', 2, '下单成功可获得3次抽奖机会', 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (3, '购买追觅配件产品', 'BUY_FITTING', 2, '下单成功可获得1次抽奖机会', 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (4, '邀请有礼', 'INVITE_FRIEND', 2, '每邀请一位好友成功下单，可获得1次抽奖机会', 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (5, '添加追觅心享官', 'ADD_CUSTOMER_SERVICE', 2, '添加成功可获得1次抽奖机会，同时享一对一专属服务', 1, 0, 1698076800, 1698076800, 0);
-- 抽奖活动-任务 DONE
INSERT INTO `t_draw_activity_task` (`id`, `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (1, 1, 1, 1, 1, 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_task` (`id`, `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (2, 1, 2, 2, 1, 3, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_task` (`id`, `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (3, 1, 3, 2, 1, 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_task` (`id`, `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (4, 1, 4, 1, 5, 1, 0, 1698076800, 1698076800, 0);
INSERT INTO `t_draw_activity_task` (`id`, `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime`) VALUES (5, 1, 5, 2, 1, 1, 0, 1698076800, 1698076800, 0);
-- 第三方券码 （保密）DONE
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (1, '轻喜到家', 1, 'bqc9dpqi', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (2, '轻喜到家', 1, '56zz21zb', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (3, '轻喜到家', 1, 'fgjumiid', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (4, '轻喜到家', 1, 'h3gai4b9', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (5, '轻喜到家', 1, 'xwb2xzr0', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (6, '轻喜到家', 1, 'bnqfbnr4', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (7, '轻喜到家', 1, 'rr2021ti', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (8, '轻喜到家', 1, 'voxszmop', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (9, '轻喜到家', 1, 'by33cqdp', 1, 0, 1698076800, 1698076800);
INSERT INTO `t_draw_external_coupon` (`id`, `name`, `type`, `code`, `status`, `issue_time`, `ctime`, `utime`) VALUES (10, '轻喜到家', 1, 's20x400j', 1, 0, 1698076800, 1698076800);


