-- （1/6）卡表
CREATE TABLE `db_dreame_wares`.`t_gift_card` (
   `id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
   `name` VARCHAR (50) NOT NULL DEFAULT '' COMMENT '名称',
   `web_name` VARCHAR (50) NOT NULL DEFAULT '' COMMENT '外显名称',
   `image` VARCHAR (200) NOT NULL DEFAULT '' COMMENT '图片',
   `amount` INT NOT NULL DEFAULT '0' COMMENT '金额（分）',
   `goods_ids` VARCHAR (200) NOT NULL DEFAULT '' COMMENT '商品IDs',
   `expire_days` INT NOT NULL DEFAULT '0' COMMENT '有效期：天',
   `type` TINYINT NOT NULL DEFAULT '1' COMMENT '类型：1 储值现金卡 2 指定兑换卡',
   `status` TINYINT NOT NULL DEFAULT '1' COMMENT '审核状态：1 待审核 2 审核通过 3 审核拒绝',
   `desc` text CHARACTER SET utf8mb4 NOT NULL COMMENT '规则描述',
   `is_del` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
   `ctime` INT NOT NULL DEFAULT '0' COMMENT '加入时间',
   `utime` INT NOT NULL DEFAULT '0' COMMENT '更新时间',
   `dtime` INT NOT NULL DEFAULT '0' COMMENT '删除时间',
   PRIMARY KEY (`id`) USING BTREE,
   KEY `idx_name` (`name`) USING BTREE,
   KEY `idx_web_name` (`web_name`) USING BTREE
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '卡表';

-- （2/6）卡祝福表
CREATE TABLE `db_dreame_wares`.`t_gift_card_bless` (
     `id` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `encode` varchar(255) NOT NULL COMMENT '加密key',
     `bless_content` varchar(255) NOT NULL COMMENT '祝福语',
     `ctime` int NOT NULL COMMENT '创建时间',
     PRIMARY KEY (`id`),
     KEY `idx_encode` (`encode`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='礼品卡祝福语表';

-- （3/6）卡消费记录表
CREATE TABLE `db_dreame_wares`.`t_gift_card_expend_record` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `card_id` int NOT NULL DEFAULT '0' COMMENT '卡ID',
     `user_card_id` int NOT NULL DEFAULT '0' COMMENT '用户卡ID',
     `order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '订单号',
     `expend_amount` int NOT NULL DEFAULT '0' COMMENT '消费金额（分）',
     `current_amount` int NOT NULL DEFAULT '0' COMMENT '当前余额（分）',
     `type` tinyint NOT NULL DEFAULT '1' COMMENT '类型：1使用 2退还',
     `ctime` int NOT NULL DEFAULT '0' COMMENT '加入时间',
     `utime` int NOT NULL DEFAULT '0' COMMENT '更新时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_user_card_id` (`user_card_id`) USING BTREE,
     KEY `idx_order_no` (`order_no`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='卡消费记录表';

-- （4/6）卡商品
CREATE TABLE `db_dreame_wares`.`t_gift_card_goods` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `sku` varchar(50) NOT NULL COMMENT '商品编号',
     `name` varchar(50) NOT NULL DEFAULT '' COMMENT '卡名称',
     `web_name` varchar(50) NOT NULL COMMENT '卡外显名称',
     `cover_image` varchar(200) NOT NULL COMMENT '商品封面',
     `images` varchar(1000) NOT NULL DEFAULT '' COMMENT '商品主图',
     `card_resource` varchar(1000) NOT NULL COMMENT '卡资源',
     `price` int NOT NULL DEFAULT '0' COMMENT '价格（分）',
     `stock` int NOT NULL DEFAULT '0' COMMENT '库存',
     `type` tinyint NOT NULL DEFAULT '1' COMMENT '卡商品类型：1实体卡 2虚拟卡',
     `start_time` int NOT NULL COMMENT '开始时间',
     `end_time` int NOT NULL COMMENT '结束时间',
     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1 上架 2 下架',
     `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
     `ctime` int NOT NULL DEFAULT '0' COMMENT '加入时间',
     `utime` int NOT NULL DEFAULT '0' COMMENT '更新时间',
     `dtime` int NOT NULL DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_name` (`name`) USING BTREE,
     KEY `idx_sku` (`sku`) USING BTREE,
     KEY `idx_web_name` (`web_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='卡商品';

-- （5/6）卡商品资源表
CREATE TABLE `db_dreame_wares`.`t_gift_card_goods_resource` (
      `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
      `card_goods_id` int NOT NULL DEFAULT '0' COMMENT '卡商品ID',
      `card_no` varchar(100) NOT NULL DEFAULT '' COMMENT '卡号',
      `card_password` varchar(100) NOT NULL DEFAULT '' COMMENT '卡密',
      `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1 未激活 2 已激活 3 作废 ',
      `act_user_id` int NOT NULL DEFAULT '0' COMMENT '激活用户ID',
      `act_time` int NOT NULL DEFAULT '0' COMMENT '激活时间',
      `ctime` int NOT NULL DEFAULT '0' COMMENT '加入时间',
      `utime` int NOT NULL DEFAULT '0' COMMENT '更新时间',
      PRIMARY KEY (`id`) USING BTREE,
      UNIQUE KEY `uniq_card_password` (`card_password`) USING BTREE,
      UNIQUE KEY `uniq_card_no` (`card_no`) USING BTREE,
      KEY `idx_card_goods_id` (`card_goods_id`) USING BTREE,
      KEY `idx_act_user_id` (`act_user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='卡商品资源表';

-- （6/6）用户卡表
CREATE TABLE `db_dreame_wares`.`t_gift_user_cards` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `user_id` int NOT NULL DEFAULT '0' COMMENT '用户ID',
     `sharer_id` int NOT NULL DEFAULT '0' COMMENT '分享人ID',
     `card_id` int NOT NULL DEFAULT '0' COMMENT '卡ID',
     `card_no` varchar(100) NOT NULL DEFAULT '' COMMENT '卡号',
     `amount` int NOT NULL DEFAULT '0' COMMENT '金额（分）',
     `type` tinyint NOT NULL DEFAULT '0' COMMENT '类型：1 储值现金卡 2 指定兑换卡',
     `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1 已领取 2 分享冻结 3已分享 4已使用',
     `content` varchar(200) NOT NULL DEFAULT '' COMMENT '祝福语',
     `expire_time` int NOT NULL DEFAULT '0' COMMENT '过期时间',
     `ctime` int NOT NULL DEFAULT '0' COMMENT '加入时间',
     `utime` int NOT NULL DEFAULT '0' COMMENT '更新时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_user_id` (`user_id`) USING BTREE,
     KEY `idx_sharer_id` (`sharer_id`) USING BTREE,
     KEY `idx_card_id` (`card_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='用户-卡表';


--订单表中增加使用礼品卡ID字段
ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `gift_card_ids` varchar(200) NOT NULL DEFAULT '' COMMENT '使用礼品卡IDS';


ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `gift_card_price`  int(10) unsigned NOT NULL DEFAULT '0' COMMENT '礼品卡抵扣金额';


--订单商品表中增加礼品卡抵扣金额字段
ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `gift_card_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '礼品卡抵扣类型：1金额 2兑换券';


ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `gift_card_value` varchar(20) NOT NULL DEFAULT '' COMMENT '礼品卡抵扣的金额（分）、礼品卡ID';

-- 操作权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (500, 'giftCardManager', 84, 'back/gift-card/manager', '礼品卡管理', 1703520000, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (501, 'giftCard', 84, 'back/gift-card/goods', '礼品卡商品', 1703520000, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (502, 'giftCardList', 84, 'back/gift-card/list', '礼品卡列表', 1703520000, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (503, 'giftCardAdd', 84, 'back/gift-card/add', '新建礼品卡', 1703520000, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (504, 'giftCardGoodsAdd', 84, 'back/gift-card-goods/add', '新建礼品卡商品', 1703520000, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (505, 'giftCardListData', 500, 'back/gift-card/card-list', '礼品卡列表', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (506, 'giftCardInfo', 500, 'back/gift-card/card-info', '礼品卡详情', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (507, 'giftCardAudit', 500, 'back/gift-card/card-audit', '礼品卡审核', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (508, 'giftCardSave', 503, 'back/gift-card/card-save', '礼品卡保存', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (509, 'giftCardResourcesList', 502, 'back/gift-card/card-resources-list', '礼品卡资源列表', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (510, 'giftDiscardResource', 502, 'back/gift-card/discard-resource', '礼品卡资源作废', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (511, 'GiftCardGoodsList', 501, 'back/gift-card/card-goods-list', '礼品卡商品列表', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (512, 'GiftCardGoodsDetail', 501, 'back/gift-card/card-goods-detail', '礼品卡商品详情', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (513, 'GiftCardGoodsSave', 501, 'back/gift-card/card-goods-save', '礼品卡商品添加、编辑', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (514, 'GiftCardGoodsResourceUpload', 501, 'back/gift-card/card-goods-resource-upload', '礼品卡商品资源上传', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (515, 'GiftCardGoodsResourceGenerate', 501, 'back/gift-card/card-goods-resource-generate', '礼品卡商品资源生成', 1703520000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (516, 'GiftCardCards', 501, 'back/gift-card/gift-cards', '礼品卡下拉列表', 1703520000, 0, 1, 0, 0, '');



