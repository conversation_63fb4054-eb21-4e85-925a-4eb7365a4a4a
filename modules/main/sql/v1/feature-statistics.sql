CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_marketing_information` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `user_id`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `phone`     varchar(20) NOT NULL DEFAULT '' COMMENT '手机号码',
    `uid`       varchar(18) NOT NULL DEFAULT '' COMMENT '其他平台 UID',
    `level`     varchar(10) NOT NULL DEFAULT '' COMMENT '会员等级',
    `point`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户积分',
    `grow`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户觅享分',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `u_p` (`user_id`,`phone`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='营销信息表';



CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_statistics_register` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `tid` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '标签(10洗地机,11扫地机器人,12吸尘器,13吹风机)',
    `buy_people_number` int(10) NOT NULL DEFAULT '0' COMMENT '购买人数',
    `register_people_number`       int(10) NOT NULL DEFAULT '0' COMMENT '注册人数',
    `buy_number`     int(10) NOT NULL DEFAULT 0 COMMENT '购买台数',
    `register_number`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '注册台数',
    `buy_part_number`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '小程序购买配件人数',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `t` (`tid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='营销注册数据表';




CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_statistics_network` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `uid` varchar(18) NOT NULL DEFAULT '' COMMENT '其他平台 UID',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `u` (`uid`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='配网数据表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_statistics_member` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `level`     varchar(10) NOT NULL DEFAULT '' COMMENT '会员等级',
    `people_number` int(10) NOT NULL DEFAULT '0' COMMENT '人数',
    `point`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '积分数',
    `avg_point`     int(10) unsigned NOT NULL DEFAULT '0' COMMENT '人均积分数',
    `buy_main_number`     int(10) NOT NULL DEFAULT 0 COMMENT '购买主机数量',
    `buy_main_price`     int(10) NOT NULL DEFAULT 0 COMMENT '购买主机金额',
    `main_deduction_price`     int(10) NOT NULL DEFAULT 0 COMMENT '购买主机抵扣金额',
    `buy_part_number`     int(10) NOT NULL DEFAULT 0 COMMENT '购买配件数量',
    `buy_part_price`     int(10) NOT NULL DEFAULT 0 COMMENT '购买配件金额',
    `part_deduction_price`     int(10) NOT NULL DEFAULT 0 COMMENT '购买配件抵扣金额',
    `grow`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '觅享分数量',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `l` (`level`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='会员数据表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_statistics_card` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
    `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `market_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联营销资源id',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未使用 1 锁定或已使用 2 过期',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='优惠卷数据';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_statistics_repeat` (
    `id`        int(11) NOT NULL AUTO_INCREMENT,
    `frequency` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '频次',
    `people` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '人数',
    `price`     int(10) NOT NULL DEFAULT 0 COMMENT '金额',
    `ctime`     int(10) NOT NULL COMMENT '加入时间',
    `utime`     int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='复购数据';



CREATE TABLE `t_statistics_repeat`
(
    `id`                 int(11) NOT NULL AUTO_INCREMENT,
    `level`              varchar(10) NOT NULL DEFAULT '' COMMENT '会员等级',
    `people_number`      int(10) NOT NULL DEFAULT '0' COMMENT '复购人数',
    `repeat_main_number` int(10) NOT NULL DEFAULT '0' COMMENT '复购主机数量',
    `repeat_main_price`  int(10) NOT NULL DEFAULT '0' COMMENT '复购主机金额',
    `repeat_part_number` int(10) NOT NULL DEFAULT '0' COMMENT '复购配件数量',
    `repeat_part_price`  int(10) NOT NULL DEFAULT '0' COMMENT '复购配件金额',
    `ctime`              int(10) NOT NULL COMMENT '加入时间',
    `utime`              int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `l` (`level`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1443 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='会员复购数据表';



ALTER TABLE `db_dreame_log`.`t_statistics_member` ADD `buy_main_people` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买主机人数' AFTER `buy_main_number`;
ALTER TABLE `db_dreame_log`.`t_statistics_member` ADD `buy_part_people` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买配件人数'  AFTER `buy_part_number`;




CREATE TABLE `db_dreame_log`.`t_statistics_recommend`
(
    `id`        int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `is_buy`   tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否购买0没购买1购买过',
    `main_name` varchar(500) NOT NULL DEFAULT '' COMMENT '购买主机名称',
    `buy_price` int(10) NOT NULL DEFAULT '0' COMMENT '购买主机金额',
    `ctime`     int(10) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`     int(10) NOT NULL DEFAULT '0' COMMENT '更新时间',
        PRIMARY KEY (`id`)
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='邀请好友购买数据';

ALTER TABLE `db_dreame_log`.`t_statistics_recommend`
    MODIFY COLUMN `main_name` varchar(1000) NOT NULL DEFAULT '' COMMENT '购买主机名称';



ALTER TABLE `db_dreame_log`.`t_marketing_information` ADD `use_point` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用积分数量';



-- 2320/12/4 增加 用户中心注册时间SQL
ALTER TABLE `db_dreame_log`.`t_marketing_information` ADD `reg_time` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '注册时间' AFTER `grow`;
