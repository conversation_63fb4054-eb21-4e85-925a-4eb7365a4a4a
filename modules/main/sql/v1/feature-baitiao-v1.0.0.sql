CREATE TABLE `db_dreame_goods`.`t_payment_activity`
(
    `id`            int                 NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `activity_name` varchar(55)         NOT NULL COMMENT '活动名称',
    `start_time`    int unsigned        NOT NULL DEFAULT '0' COMMENT '活动开始时间',
    `end_time`      int unsigned        NOT NULL DEFAULT '0' COMMENT '活动结束时间',
    `pay_type`      tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '支付类型，1：京东白条',
    `ctime`         int unsigned        NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`         int unsigned        NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`         int unsigned        NOT NULL DEFAULT '0' COMMENT '删除时间',
    `is_del`        tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '逻辑删除标识，0未删除，1已删除',
    PRIMARY KEY (`id`),
    KEY `idx_isdel_paytype_start_end` (is_del,pay_type,start_time,end_time) COMMENT '按支付复合检索'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付活动配置表';

CREATE TABLE `db_dreame_goods`.`t_payment_activity_goods`
(
    `id`                    int              NOT NULL AUTO_INCREMENT COMMENT '商品配置ID',
    `activity_id`           int unsigned     NOT NULL DEFAULT '0' COMMENT '活动ID，关联payment_activity表',
    `interest_free` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '免息期数 0：不免息, 3：3期免息, 6：6期免息, 12：12期免息, 24：24期免息',
    `gid`                   int unsigned     NOT NULL DEFAULT '0' COMMENT '商品ID',
    `ctime`                 int unsigned     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`                 int unsigned     NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_activity_id_gid` (`activity_id`,`gid`) COMMENT '按活动商品ID进行检索'
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='支付活动商品配置表';

-- 修改商品类型表，添加是否支持京东白条字段
ALTER TABLE `db_dreame_goods`.`t_gtype_0`
    ADD COLUMN `jd_baitiao_support` TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否支持京东白条，0:不支持，1:支持';


-- 修改商品订单表，添加支付方案字段
ALTER TABLE `db_dreame_goods`.`t_opay_2022` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2023` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2024` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2025` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2026` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2027` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2028` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2029` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_opay_2030` ADD COLUMN payment_plan VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';

-- 修改订单支付历史表，添加分期支付方案字段
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2024` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2025` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2026` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2027` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2028` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2029` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';
ALTER TABLE `db_dreame_goods`.`t_order_pay_history_2030` ADD COLUMN `payment_plan` VARCHAR(20) NOT NULL DEFAULT 'NO_INST' COMMENT '支付方案: NO_INST:不分期, INST_3:3期分期, INST_6:6期分期, INST_12:12期分期, FREE_3:3期免息, FREE_6:6期免息, FREE_12:12期免息';

-- 菜单修改
-- 1. 活动管理改为优惠券活动配置
UPDATE db_dreame_admin.t_uri SET name = 'activity', p_id = 89, uri = 'activity/main', uri_name = '优惠券活动配置', time = 1731462616, is_del = 0, is_check = 1, is_menu = 1, `rank` = 0, ext_uri = '' WHERE id = 90;
-- 2. 新增支付活动配置
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (743, 'payActivity', 89, 'activity/pay', '支付活动配置', 1731464302, 0, 1, 1, 1, '');
-- back/payment-activity/list
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (745, 'payActivityList', 743, 'back/payment-activity/list', '支付活动列表', 1731476917, 0, 1, 0, 0, '');
-- back/payment-activity/detail
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (746, 'payActivityDetail', 743, 'back/payment-activity/detail', '支付活动详情', 1731476917, 0, 1, 0, 0, '');
-- back/payment-activity/save
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (747, 'payActivitySave', 743, 'back/payment-activity/save', '保存支付活动', 1731476917, 0, 1, 0, 0, '');
-- back/payment-activity/delete
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (748, 'payActivityDelete', 743, 'back/payment-activity/delete', '删除支付活动', 1731476917, 0, 1, 0, 0, '');
-- back/payment-activity/goods-search
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (749, 'payActivityGoodsSearch', 743, 'back/payment-activity/goods-search', '搜索商品', 1731476917, 0, 1, 0, 0, '');