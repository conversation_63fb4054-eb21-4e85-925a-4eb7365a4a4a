
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gmain_acdeprice` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `sku` varchar(50)  NOT NULL DEFAULT '' COMMENT 'sku',
    `sn` varchar(100)  NOT NULL DEFAULT '' COMMENT 'sn编码',
    `deprice` int unsigned NOT NULL DEFAULT '0' COMMENT '抵扣金额（单位：分）',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime`  int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_sku_sn` (`sku`,`sn`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品以旧换新活动抵扣金额';


INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'P2028', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'P2009', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'P2029', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'P2008', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'P2027', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2104', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2215', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2235', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2332A', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2251', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2246', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2247', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2228S', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2233', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2228', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2317', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2348', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2345', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2352', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2360', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2316', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2334', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2313', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2370', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2398', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2355', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2332', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2263B', 80000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2263', 80000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000224', 'R2253', 80000, 0, 1696573932, 1696573932, 0);


INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'P2028', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'P2009', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'P2029', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'P2008', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'P2027', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2104', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2215', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2235', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2332A', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2251', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2246', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2247', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2228S', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2233', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2228', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2317', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2348', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2345', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2352', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2360', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2316', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2334', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2313', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2370', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2398', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2355', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2332', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2263B', 80000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2263', 80000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010204AA000257', 'R2253', 80000, 0, 1696573932, 1696573932, 0);




INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'P2028', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'P2009', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'P2029', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'P2008', 10000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'P2027', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2104', 20000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2215', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2235', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2332A', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2251', 30000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2246', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2247', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2228S', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2233', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2228', 40000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2317', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2348', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2345', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2352', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2360', 50000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2316', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2334', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2313', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2370', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2398', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2355', 60000, 0, 1696573932, 1696573932, 0);
INSERT INTO `db_dreame_goods`.`t_gmain_acdeprice`(`sku`, `sn`, `deprice`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('010201AA000250', 'R2332', 60000, 0, 1696573932, 1696573932, 0);



--商城以旧换新处理--
ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';


ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_10` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_11` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_12` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_13` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_14` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_15` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_16` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_17` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_18` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_19` ADD `acdeprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '以旧换新活动抵扣金额（分）';


ALTER TABLE `db_dreame_goods`.`t_om_2022` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2023` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2024` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2025` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2026` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2027` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2028` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2029` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2030` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单，5以旧换新订单）';

CREATE TABLE `db_dreame_goods`.`t_otn_2022` (
     `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `user_id` int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
     `order_no`  varchar(50)  NOT NULL DEFAULT '' COMMENT '订单号',
     `image` varchar(2000)   NOT NULL DEFAULT '' COMMENT '以旧换新旧机图片',
     `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
     PRIMARY KEY (`id`) USING BTREE,
     UNIQUE KEY `uniq_o` (`order_no`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品以旧换新订单表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2023` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2024` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2025` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2026` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2027` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2028` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2029` LIKE `db_dreame_goods`.`t_otn_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_otn_2030` LIKE `db_dreame_goods`.`t_otn_2022`;
