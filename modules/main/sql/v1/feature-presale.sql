-- 新增普通商品表预售字段
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `is_presale` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否预售 （0不参与预售，1参与预售）' after `is_recommend`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `presale_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '预售截止时间' after `is_presale`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `deposit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金价格' after `presale_time`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `expand_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格' after `deposit`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `start_payment` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '尾款付款开始时间' after `expand_price`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `end_payment` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '尾款付款结束时间' after `start_payment`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `end_payment`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `scheduled_number` int(10) NOT NULL DEFAULT '0' COMMENT '前端虚拟预购商品数' after `surplus_time` ;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `coefficient` int(10) NOT NULL DEFAULT '0' COMMENT '预定增长系数' after `scheduled_number` ;



-- 新增一元链接商品表预售字段
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `is_presale` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否预售 （0不参与预售，1参与预售）' after `is_recommend`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `presale_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '预售截止时间' after `is_presale`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `deposit` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金价格' after `presale_time`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `expand_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格' after `deposit`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `start_payment` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '尾款付款开始时间' after `expand_price`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `end_payment` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '尾款付款结束时间' after `start_payment`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `end_payment`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `scheduled_number` int(10) NOT NULL DEFAULT '0' COMMENT '前端虚拟预购商品数' after `surplus_time` ;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `coefficient` int(10) NOT NULL DEFAULT '0' COMMENT '预定增长系数' after `scheduled_number` ;

-- 新建定金订单表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2022` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `cart_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购物车id',
    `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
    `num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号',
    `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金订单状态...',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单金额（分）',
    `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间',
    `finish_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `deposit_note` varchar(500) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '备注',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_o` (`order_no`) USING BTREE,
    KEY `i_c` (`ctime`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定金订单表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2023` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2024` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2025` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2026` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2027` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2028` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2029` LIKE `db_dreame_goods`.`t_odeposit_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_2030` LIKE `db_dreame_goods`.`t_odeposit_2022`;


-- 定金订单表新增距尾款时间推送字段
ALTER TABLE `db_dreame_goods`.`t_odeposit_2022` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2023` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2024` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2025` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2026` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2027` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2028` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2029` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2030` ADD `surplus_time` varchar(20) NOT NULL DEFAULT '' COMMENT '距尾款时间推送（第二次推送）' after `pay_time`;



-- 主订单表增加订单类型/定金订单号字段
ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单）' after `status`;


ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD `end_payment` int unsigned NOT NULL DEFAULT '0' COMMENT '取消支付时间' after `type`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_2022` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2023` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2024` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2025` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2026` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2027` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2028` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2029` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_2030` ADD `cr_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '取消原因' after `deposit_note`;



ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号' after `order_no`;

ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;

ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `deposit_price` int unsigned NOT NULL DEFAULT '0' COMMENT '定金价格（分）' after `oprice`;

-- 新建定金订单退款表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_main` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
    `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `status` int(10) unsigned NOT NULL DEFAULT '1000' COMMENT '1000待审核 1010审核通过 1020 审核拒绝 100取消 20000000退款成功',
    `m_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '退款方式 1:仅退款 2:退货退款',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_no` (`refund_no`) USING BTREE,
    KEY `i_s` (`status`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='定金订单退款主表';


ALTER TABLE `db_dreame_goods`.`t_orefund_d_main` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';



CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_0` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
    `deposit_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `r_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '退款原因',
    `m_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '退款方式',
    `status` int(10) unsigned NOT NULL DEFAULT '1000' COMMENT '1000待审核 1010审核通过 1020 审核拒绝 100取消 20000000退款成功',
    `ostatus` int(10) unsigned NOT NULL COMMENT '退款时订单状态',
    `describe` varchar(200) NOT NULL DEFAULT '' COMMENT '补充描述',
    `images` varchar(500) NOT NULL DEFAULT '' COMMENT '图片',
    `a_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '拒绝原因',
    `mail_no` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流单号',
    `express_code` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流公司代码',
    `express_name` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流公司名称',
    `rback` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否退单',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款价格',
    `rtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_no` (`refund_no`) USING BTREE,
    KEY `i_u_g` (`user_id`) USING BTREE,
    KEY `i_d` (`deposit_order_no`) USING BTREE,
    KEY `i_s` (`status`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='定金订单退款表';


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_1` LIKE `db_dreame_goods`.`t_orefund_d_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_2` LIKE `db_dreame_goods`.`t_orefund_d_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_3` LIKE `db_dreame_goods`.`t_orefund_d_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_d_4` LIKE `db_dreame_goods`.`t_orefund_d_0`;




ALTER TABLE `db_dreame_goods`.`t_orefund_d_0` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';
ALTER TABLE `db_dreame_goods`.`t_orefund_d_1` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';
ALTER TABLE `db_dreame_goods`.`t_orefund_d_2` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';
ALTER TABLE `db_dreame_goods`.`t_orefund_d_3` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';
ALTER TABLE `db_dreame_goods`.`t_orefund_d_4` change column `deposit_order_no` `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号';

-- 新增预售商品库存表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gprestock` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `gid` int(10) unsigned NOT NULL DEFAULT '0',
    `sid` int(10) unsigned NOT NULL DEFAULT '0',
    `stock` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '库存',
    `sales` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '销量',
    `wait` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '未付款数量',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_g_s` (`gid`,`sid`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='预售商品库存表';


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (264, 'presaleDetail', 59, 'back/goods/presale', '预售详情信息', 1644579242, 0, 1, 0, 0, '');


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_0` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号',
    `cfg` text  COMMENT '商品关键信息拷贝',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单金额（分）',
    `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金订单状态...',
    `r_id` int(11) NOT NULL DEFAULT '0' COMMENT '邀请人id',
    `guide_id` int(11) NOT NULL DEFAULT '0' COMMENT '导购id',
    `guide_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '导购类型（1：正常导购，2：内购）',
    `union` varchar(50) NOT NULL DEFAULT '' COMMENT '订单来源-推广渠道',
    `euid` varchar(50) NOT NULL DEFAULT '' COMMENT '订单来源-标识参数',
    `live_mark` varchar(50) NOT NULL DEFAULT '' COMMENT '直播标识',
    `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '订单来源-referer',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_o` (`order_no`) USING BTREE,
    KEY `i_c` (`ctime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='定金订单扩展表';


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_1` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_2` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_3` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_4` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_5` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_6` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_7` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_8` LIKE `db_dreame_goods`.`t_odeposit_e_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_odeposit_e_9` LIKE `db_dreame_goods`.`t_odeposit_e_0`;



ALTER TABLE `db_dreame_goods`.`t_odeposit_e_0` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_0` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_1` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_1` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_2` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_2` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_3` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_3` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_4` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_4` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_5` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_5` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_6` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_6` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_7` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_7` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_8` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_8` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;


ALTER TABLE `db_dreame_goods`.`t_odeposit_e_9` ADD `platform_source` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `status`;
ALTER TABLE `db_dreame_goods`.`t_odeposit_e_9` ADD `source` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '来源（0：普通下单 1：导购分销 2：推荐有礼 3：导购、推荐）' after `platform_source`;



ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_10` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_11` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_12` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_13` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_14` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_15` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_16` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_17` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_18` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;
ALTER TABLE `db_dreame_goods`.`t_uo_g_19` ADD `exprice` int unsigned NOT NULL DEFAULT '0' COMMENT '膨胀价格（分）' after `oprice`;

