

ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `consume_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券ID',ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';



ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_10` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_11` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_12` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_13` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_14` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_15` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_16` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_17` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_18` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';
ALTER TABLE `db_dreame_goods`.`t_uo_g_19` ADD `consume_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费券使用金额（分）';