ALTER TABLE `db_dreame_goods`.`t_main_part` ADD `tied_sale` text  DEFAULT NULL COMMENT '是否搭售购买' after `part_sku`;

CREATE TABLE `db_dreame_goods`.`t_gparam` (
                            `id` int NOT NULL AUTO_INCREMENT,
                            `name` varchar(50) NOT NULL DEFAULT '' COMMENT '参数名',
                            `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
                            `ctime` int NOT NULL COMMENT '加入时间',
                            `utime` int NOT NULL COMMENT '更新时间',
                            `dtime` int NOT NULL DEFAULT '0' COMMENT '删除时间',
                            PRIMARY KEY (`id`),
                            UNIQUE KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='商品参数库';


CREATE TABLE `db_dreame_goods`.`t_gparam_categroup` (
                                      `id` int NOT NULL AUTO_INCREMENT,
                                      `pid` int NOT NULL DEFAULT '0' COMMENT '父ID',
                                      `cate_id` int NOT NULL DEFAULT '0' COMMENT '商品类目id',
                                      `name` varchar(255) NOT NULL DEFAULT '' COMMENT '分组名',
                                      `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
                                      `ctime` int NOT NULL COMMENT '加入时间',
                                      `utime` int NOT NULL COMMENT '更新时间',
                                      `dtime` int NOT NULL DEFAULT '0' COMMENT '删除时间',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_pid` (`pid`),
                                      KEY `idx_cate_id` (`cate_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4  COMMENT='商品参数的二级类目分组表';

CREATE TABLE `db_dreame_goods`.`t_gparam_categroup_detail` (
                                             `id` int NOT NULL AUTO_INCREMENT,
                                             `categroup_id` int NOT NULL DEFAULT '0' COMMENT '分组ID',
                                             `gparam_id` int NOT NULL DEFAULT '0' COMMENT '参数ID',
                                             `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
                                             `ctime` int NOT NULL COMMENT '加入时间',
                                             `utime` int NOT NULL COMMENT '更新时间',
                                             `dtime` int NOT NULL DEFAULT '0' COMMENT '删除时间',
                                             PRIMARY KEY (`id`),
                                             KEY `idx_categroup_id` (`categroup_id`),
                                             KEY `idx_gparam_id` (`gparam_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4  COMMENT='商品参数的二级类目分组详情';

CREATE TABLE `db_dreame_goods`.`t_gparam_goods` (
                                  `id` int NOT NULL AUTO_INCREMENT,
                                  `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品sku',
                                  `categroup_detail_id` int NOT NULL DEFAULT '0' COMMENT '分组详情ID',
                                  `param_info` varchar(1000) NOT NULL DEFAULT '' COMMENT '参数信息',
                                  `sort` smallint NOT NULL DEFAULT '0' COMMENT '参数排序',
                                  `is_show` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否展示 0否 1是',
                                  `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
                                  `ctime` int NOT NULL COMMENT '加入时间',
                                  `utime` int NOT NULL COMMENT '更新时间',
                                  `dtime` int NOT NULL DEFAULT '0' COMMENT '删除时间',
                                  PRIMARY KEY (`id`),
                                  KEY `idx_sku` (`sku`),
                                  KEY `idx_categroup_detail_id` (`categroup_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4  COMMENT='商品的商品参数表';

-- 添加对比的sku
ALTER TABLE `db_dreame_goods`.`t_gmain` ADD compare_sku VARCHAR ( 50 ) NOT NULL DEFAULT '' COMMENT '对比的商品sku' AFTER `name`;
ALTER TABLE `db_dreame_goods`.`t_gmain` ADD is_compare TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品是否对比：0否 1是' AFTER `compare_sku`;
ALTER TABLE `db_dreame_goods`.`t_gmain` ADD INDEX idx_compare_sku ( `compare_sku` );

-- 添加3D封面
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `cover_image_3d` VARCHAR ( 255 ) NOT NULL DEFAULT '' COMMENT '3D封面图' AFTER `images`;
ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `model_3d` TEXT NOT NULL COMMENT '3D模型图' AFTER `cover_image_3d`;

-- 增加权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (290, 'goodsParams', 280, 'back/goods-param/add-group', '商品参数-添加参数类型', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (291, 'goodsParams', 280, 'back/goods-param/edit-group', '商品参数-编辑参数类型（修改名字）', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (292, 'goodsParams', 280, 'back/goods-param/group-param-detail', '商品参数-参数类型详情', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (293, 'goodsParams', 280, 'back/goods-param/edit-group-param', '商品参数-编辑参数类型的参数', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (294, 'goodsParams', 280, 'back/goods-param/param-list', '商品参数-参数列表', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (295, 'goodsParams', 59, 'back/goods-param/goods-param-detail', '商品参数-商品参数信息', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (296, 'goodsParams', 59, 'back/goods/compare-list', '商品参数-商品参数列表', 1680588846, 0, 1, 0, 0, '');

-- 初始化参数库
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主机尺寸', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('额定功率', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('基站重量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('基站尺寸', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('功率(清洗)', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('吸力大小', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主机水箱', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主刷抬升', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('拖布拆卸', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('复拖复洗', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('自主学习', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('智能托管', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('脏污检测', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('避障系统', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('视频管家', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('语音通话', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主机语控', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('自动集尘', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('拖布清洗', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('拖布干燥', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('除菌功能', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('自动加注清洁液', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('电池快充', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('上下水装置', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('额定电压(基站)', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('吸力', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('噪音', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('污水箱容量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('洗地续航', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('吸尘续航', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('滚刷配置', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('贴边设计', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('电解水', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('活水清洁系统', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('离心风干', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('正反转自清洁', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('脏污感应', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('洗地模式', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('屏幕显示', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('语音提醒', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('吸尘洗地二合一', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('电压', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('整机功率', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('外形尺寸', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('净重', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('外观工艺', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('风嘴配置', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('风速风压', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('风速档位', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('温度档位', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('风量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('NTC温控', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('精华功能', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('冷热循环', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('一键冷风', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('灯显', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('恒温护发', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('噪音控制', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('马达转速', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('尘杯容量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主机重量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('电池容量', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('充电时长', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('运行时间', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('外观颜色', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('地刷配置', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('吸力模式', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('一键持续清洁', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('电池包拆卸', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('智能屏显', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('绿光显尘', 0, 1684288131, 1684288131, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('配件', 0, 1684288131, 1684288131, 0);

-- //230906 补充

INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('真空度', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('尘杯拆卸', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('主地刷类型', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('除螨刷', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('长扁吸', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('二合一宽嘴刷', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('软毛刷', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('底部转接头', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('供电形式', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('杀菌率', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('除螨率', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('超声波除螨', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('负离子净化', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('香薰', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('热烘', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('UV杀菌', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('双尘杯', 0, 1693987355, 1693987355, 0);


INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('拖布抬升', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('基站自清洁', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('加热方式', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('程序', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('额定电压', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('频率', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('额定总功率', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('而定净水总量', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('原水箱容积', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('纯水箱容积', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('浓水水位容积', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('制热水能力', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('过滤工艺', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('聚丙烯活性炭符合滤芯寿命', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('反渗透膜滤芯寿命', 0, 1693987355, 1693987355, 0);
INSERT INTO `db_dreame_goods`.`t_gparam` (`name`, `is_del`, `ctime`, `utime`, `dtime`) VALUES ('出水水质', 0, 1693987355, 1693987355, 0);

