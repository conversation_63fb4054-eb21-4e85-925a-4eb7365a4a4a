CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_adv` (
    `id` int NOT NULL AUTO_INCREMENT,
    `user_id` int NOT NULL COMMENT '用户id',
    `union` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道来源-推广渠道',
    `euid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道来源-标识参数',
    `live_mark` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '直播标识',
    `referer` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '渠道来源-referer',
    `ctime` int NOT NULL DEFAULT '0' COMMENT '绑定时间',
    `utime` int NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='广告绑定用户';


