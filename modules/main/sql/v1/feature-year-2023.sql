CREATE TABLE `t_sum_2023`
(
    `id`                      int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`                 int(11) NOT NULL DEFAULT '0' COMMENT '用户ID',
    `uid`                     varchar(50)   NOT NULL DEFAULT '' COMMENT '用户UID',
    `register_time`           int(11) NOT NULL DEFAULT '0' COMMENT '用户注册时间',
    `purchase_nums`           int(11) NOT NULL DEFAULT '0' COMMENT '购买次数',
    `purchase_sum_price`      int(11) NOT NULL DEFAULT '0' COMMENT '购买总金额(分)',
    `purchase_coupon_nums`    int(11) NOT NULL DEFAULT '0' COMMENT '优惠券数量',
    `purchase_points`         int(11) NOT NULL DEFAULT '0' COMMENT '积分总数',
    `purchase_discount_price` int(11) NOT NULL DEFAULT '0' COMMENT '优惠总金额(分)',
    `double11_check_nums`     int(11) NOT NULL DEFAULT '0' COMMENT '双11打卡次数',
    `checkin_rate`            decimal(5, 2) NOT NULL DEFAULT '0.00' COMMENT '打卡比率（超越多少人）',
    `double11_draw_nums`      int(11) NOT NULL DEFAULT '0' COMMENT '双11抽奖次数',
    `double11_flash_nums`     int(11) NOT NULL DEFAULT '0' COMMENT '双11秒杀次数',
    `double11_sum_points`     int(11) NOT NULL DEFAULT '0' COMMENT '双11积分总数',
    `double11_flash_goods`    varchar(500)  NOT NULL DEFAULT '' COMMENT '秒杀商品名',
    `max_cart_infos`          varchar(500)  NOT NULL DEFAULT '' COMMENT '加购商品数据',
    `latest_purchase_time`    int(11) NOT NULL DEFAULT '0' COMMENT '最后一次购买时间',
    `product_dry_name`        varchar(50)   NOT NULL DEFAULT '' COMMENT '产品注册吹风机名称',
    `product_cleaner_name`    varchar(50)   NOT NULL DEFAULT '' COMMENT '产品注册吸尘器名称',
    `product_master`          tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否注册主推商品0否1是',
    `product_reg_num`         int(10) NOT NULL DEFAULT '0' COMMENT '产品注册数量（购买件数）',
    `product_reg_name`        varchar(500)  NOT NULL DEFAULT '' COMMENT '产品注册 产品名称',
    `is_del`                  tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime`                   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`                   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime`                   int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_user_uid` (`user_id`,`uid`)
) AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户年终统计表';