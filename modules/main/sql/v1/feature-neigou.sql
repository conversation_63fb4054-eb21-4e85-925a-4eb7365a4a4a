-- 设置价格表

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gsprice` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `sku` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT 'SKU',
    `sprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '设置价格（分）',
    `sprice_type` tinyint(1) DEFAULT '0' COMMENT '状态（0：默认，1：内购价）',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1删除0正常',
    `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `i_s` (`sku`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品设置价格表';

ALTER TABLE `db_dreame_goods`.`t_gtype_0` ADD `is_internal_purchase` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否内购(0：否;1：是)' after `atype`;
ALTER TABLE `db_dreame_goods`.`t_gtype_99` ADD `is_internal_purchase` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否内购(0：否;1：是)' after `price`;



--活动营销资源自定义表
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_market_define` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `name` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    `market_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联营销配置ID',
    `define_type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '自定义类型（1：内购）',
    `sort` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `dtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `market_id` (`market_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动营销资源自定义表';

ALTER TABLE `db_dreame_goods`.`t_market_define` ADD `use_rule_note` text  DEFAULT NULL COMMENT '使用规则' after `name`;



INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (95, 'marketFineList', 89, 'back/market-define/list', '优惠券配置', 1645097627, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (96, 'marketFineSave', 89, 'back/market-define/save', '优惠券配置创建/编辑', 1645097622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (97, 'marketFineDel', 89, 'back/market-define/Del', '优惠券配置删除', 1645097622, 0, 1, 0, 0, '');


-- UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'marketFineList', `p_id` = 89, `uri` = 'back/market-define/list', `uri_name` = '优惠券配置', `time` = 1645097627, `is_del` = 0, `is_check` = 1, `is_menu` = 1, `rank` = 0, `ext_uri` = '' WHERE `id` = 95;
-- UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'marketFineSave', `p_id` = 89, `uri` = 'back/market-define/save', `uri_name` = '优惠券配置创建/编辑', `time` = 1645097622, `is_del` = 0, `is_check` = 1, `is_menu` = 0, `rank` = 0, `ext_uri` = '' WHERE `id` = 96;
-- UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'marketFineDel', `p_id` = 89, `uri` = 'back/market-define/Del', `uri_name` = '优惠券配置删除', `time` = 1645097622, `is_del` = 0, `is_check` = 1, `is_menu` = 0, `rank` = 0, `ext_uri` = '' WHERE `id` = 97;





ALTER TABLE `db_dreame_goods`.`t_osource_2022` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2023` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2024` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2025` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2026` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2027` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2028` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2029` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
ALTER TABLE `db_dreame_goods`.`t_osource_2030` ADD `type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '导购类型（1：正常导购，2：内购）' after `price`;
