ALTER TABLE `db_dreame_goods`.`t_market_config` ADD `web_name` varchar(255) NOT NULL DEFAULT '' COMMENT '外显名称' after `name`;



-- //推送订单完成奖励


ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD `syn_iot_reward` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经发放完成奖励 1是0否 默认0';



UPDATE `db_dreame_goods`.`t_uo_0` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_1` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_2` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_3` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_4` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_5` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_6` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_7` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_8` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;
UPDATE `db_dreame_goods`.`t_uo_9` SET `syn_iot_reward` = 1 where `status` = 500 AND `finish_time` < 1675955400;

