CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_parts_sales` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `main_sku` varchar(50) NOT NULL DEFAULT '' COMMENT '主机sku',
    `main_id` int(10) NOT NULL DEFAULT '0' COMMENT '主表id',
    `part_sku` varchar(50) NOT NULL DEFAULT '' COMMENT '配件sku',
    `reminder_copy` varchar(255) NOT NULL DEFAULT '' COMMENT '提醒文案',
    `sort` int(11) NOT NULL default 0 COMMENT '排序',
    `status` tinyint(1) NOT NULL default 0 COMMENT '是否下架 0否 1是',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `main_sku`(`main_sku`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='智能配件关联表';




CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_main_sales` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `main_sku` varchar(50) NOT NULL DEFAULT '' COMMENT '主机sku',
    `name`  varchar(50) NOT NULL DEFAULT '' COMMENT '主机名称',
    `status` tinyint(1) NOT NULL default 0 COMMENT '是否下架 0否 1是',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `main_sku`(`main_sku`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='智能配件主机表';



CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_main_part` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `main_sku` varchar(100) NOT NULL DEFAULT '' COMMENT '主机sku',
    `part_sku` varchar(500) NOT NULL DEFAULT '' COMMENT '配件sku',
    `sort` int(11) NOT NULL default 0 COMMENT '排序',
    `status` tinyint(1) NOT NULL default 0 COMMENT '是否下架 0否 1是',
    `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除 0否 1是',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `main_sku`(`main_sku`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='主机配件原始关联表';



INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (270, 'registerParts', 73, 'product/parts', '产品注册配件管理', 1678245053, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (271, 'registerPartsList', 270, 'back/parts-sales/list', '产品注册配件管理列表', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (272, 'machineList', 270, 'back/parts-sales/machine', '产品注册主机列表', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (273, 'registerPartsModify', 270, 'back/parts-sales/modify', '产品注册添加编辑', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (274, 'registerPartsDetail', 270, 'back/parts-sales/detail', '产品注册详情', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (275, 'registerPartsDelete', 270, 'back/parts-sales/delete', '产品注册删除', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (276, 'registerPartsList', 270, 'back/parts-sales/parts', '产品注册已选主机的配件', 1678245053, 0, 1, 0, 0, '');