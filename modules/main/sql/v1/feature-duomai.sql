
--用户扩展表 新增
ALTER TABLE `db_dreame`.`t_user_extend` ADD `union` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-推广渠道';
ALTER TABLE `db_dreame`.`t_user_extend` ADD `euid` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-标识参数';
ALTER TABLE `db_dreame`.`t_user_extend` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer';

ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `union` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-推广渠道';
ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `euid` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-标识参数';
ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer';


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2022` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
    `union` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-推广渠道',
    `euid` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道来源-标识参数',
    `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态同订单状态',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单金额（分）',
    `finish_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_o` (`order_no`) USING BTREE,
    KEY `i_union` (`union`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单来源-汇总表(按时间分割)';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2023` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2024` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2025` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2026` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2027` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2028` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2029` LIKE `db_dreame_goods`.`t_osource_m_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_m_2030` LIKE `db_dreame_goods`.`t_osource_m_2022`;

ALTER TABLE `db_dreame_goods`.`t_osource_m_2022` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2023` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2024` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2025` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2026` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2027` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2028` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2029` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;
ALTER TABLE `db_dreame_goods`.`t_osource_m_2030` ADD `referer` varchar(500) NOT NULL DEFAULT '' COMMENT '渠道来源-referer' after `euid`;


ALTER TABLE `db_dreame`.`t_user_extend` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source` ;
ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;


ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD `platform_source` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '平台来源（1:小程序全平台（存量）,2:小程序安卓机,3:小程序IOS,4:app全平台（存量）,5:app安卓机,6:appIOS,7:全平臺）' after `source`;


