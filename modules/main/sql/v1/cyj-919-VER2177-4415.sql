-- 919活动
INSERT INTO `db_dreame_goods`.`t_draw_activity` ( `id`, `name`, `start_time`, `end_time`, `daily_free_times`, `status`, `desc`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 5, '追觅919会员节', 1723996800, 1724032600, 1, 1, '', 0, 1723685193, 1723685193, 0 );


-- 新增任务 邀请新用户
INSERT INTO `db_dreame_goods`.`t_draw_task` (`id`, `name`, `task_code`, `type`, `desc`, `status`, `is_del`, `ctime`,
                                             `utime`, `dtime`)
VALUES (6, '邀请新用户', 'INVITE_NEW_USER', 2, '每邀请1位新用户成功入会，可获取1次抽奖机会', 1, 0,
        1723685193, 1723685193, 0);


-- 活动任务
INSERT INTO `db_dreame_goods`.`t_draw_activity_task` ( `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 5, 1, 1, 1, 1, 0, 1723685193 , 1723685193 , 0 ),
    ( 5, 6, 2, 12, 1, 0, 1723685193 , 1723685193 , 0 );


-- 奖品信息
    INSERT INTO `db_dreame_goods`.`t_draw_prize`
(`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`)
VALUES
    (14, '50积分', 2, '50', 'https://wpm-cdn.dreame.tech/images/202408/66c5a00d44c152822030051.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (15, '100积分', 2, '100', 'https://wpm-cdn.dreame.tech/images/202408/66c5a029cf9a78502030049.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (16, '188积分', 2, '188', 'https://wpm-cdn.dreame.tech/images/202408/66c5a03ec8f158232030067.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (17, '200积分', 2, '200', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0533d9712522030023.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (18, '288积分', 2, '288', 'https://wpm-cdn.dreame.tech/images/202408/66c5a06a681ac4262030039.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (19, '588积分', 2, '588', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0822d25c1852030098.png', '', '', '', '积分值', 0, 1724032600, 1724032600, 0),
    (20, '配件五折优惠券（一个月有效期，不含上下水）', 3, '601', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (21, '扑克牌套装', 3, '596', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ac8a6d25672030049.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (22, '多巴胺杯-渐变蜜桃', 3, '595', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0c6185d71002030031.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (23, '露营椅', 3, '597', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0dc20a5a1342030065.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (24, 'Pocket吹风机', 3, '599', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ef3445c2142030051.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (25, '配件五折优惠券（一个月有效期，不含上下水）', 3, '613', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (26, '扑克牌套装', 3, '614', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ac8a6d25672030049.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (27, '多巴胺杯-渐变蜜桃', 3, '615', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0c6185d71002030031.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (28, '露营椅', 3, '616', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0dc20a5a1342030065.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0),
    (29, 'Pocket吹风机', 3, '617', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ef3445c2142030051.png', '', '', '', '优惠券id', 0, 1724032600, 1724032600, 0);


-- 活动奖品
INSERT INTO `db_dreame_goods`.`t_draw_activity_prize`
(`activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`,
 `is_del`, `ctime`, `utime`, `dtime`)
VALUES (5, 14, 1000000, 1000000, 0, 0, 5.00, 1, 0, 1724032600, 1724032600, 0),
       (5, 15, 10000, 10000, 0, 0, 5.00, 0, 0, 1724032600, 1724032600, 0),
       (5, 16, 4000, 4000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (5, 17, 4000, 4000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (5, 18, 3000, 3000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (5, 19, 2000, 2000, 0, 0, 9.69, 0, 0, 1724032600, 1724032600, 0),
       (5, 20, 3000, 1, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (5, 21, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (5, 22, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (5, 23, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (5, 24, 1, 1, 0, 0, 0.01, 0, 0, 1724032600, 1724032600, 0);


-- 新人活动配置表
CREATE TABLE  if NOT EXISTS `db_dreame`.`t_ac_type1`
(
    `id`                    int NOT NULL AUTO_INCREMENT COMMENT '礼包ID',
    `ac_id`                 int NOT NULL COMMENT '活动ID，关联主表t_activity',
    `has_point`             tinyint(1) DEFAULT '0' COMMENT '是否包含积分 1:是, 0:否',
    `point_value`           int DEFAULT '0' COMMENT '积分数值，只有当has_point为1时有效',
    `point_multiplier`      int DEFAULT '0' COMMENT '积分倍数，只有当has_point为1时有效',
    `multiplier_start_time` int unsigned DEFAULT '0' COMMENT '积分倍数生效开始时间',
    `multiplier_end_time`   int unsigned DEFAULT '0' COMMENT '积分倍数生效结束时间',
    `has_coupon`            tinyint(1) DEFAULT '0' COMMENT '是否包含优惠券 1:是, 0:否',
    `ctime`                 int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`                 int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`),
    KEY `idx_ac_id` (`ac_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='新人活动配置表';


-- 活动用户领取日志表
ALTER TABLE `db_dreame_log`.`t_ac_draw_log_0`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_1`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_2`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_3`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_4`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_5`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_6`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_7`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_8`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;

ALTER TABLE `db_dreame_log`.`t_ac_draw_log_9`
    ADD `point` int unsigned NOT NULL DEFAULT '0' COMMENT '积分数量' AFTER `market_ids`;



-- 大屏幕抽奖
INSERT INTO `db_dreame_goods`.`t_draw_activity` ( `id`, `name`, `start_time`, `end_time`, `daily_free_times`, `status`, `desc`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 6, '追觅919会员节（大屏幕抽奖）', 1723996800, 1735660799, 1, 1, '', 0, 1723685193, 1723685193, 0 );

-- 活动奖品
INSERT INTO `db_dreame_goods`.`t_draw_activity_prize`
(`activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`,
 `is_del`, `ctime`, `utime`, `dtime`)
VALUES (6, 14, 1000000, 1000000, 0, 0, 5.00, 1, 0, 1724032600, 1724032600, 0),
       (6, 15, 10000, 10000, 0, 0, 5.00, 0, 0, 1724032600, 1724032600, 0),
       (6, 16, 4000, 4000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (6, 17, 4000, 4000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (6, 18, 3000, 3000, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (6, 19, 2000, 2000, 0, 0, 9.69, 0, 0, 1724032600, 1724032600, 0),
       (6, 25, 3000, 1, 0, 0, 20.00, 0, 0, 1724032600, 1724032600, 0),
       (6, 26, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (6, 27, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (6, 28, 10, 1, 0, 0, 0.10, 0, 0, 1724032600, 1724032600, 0),
       (6, 29, 1, 1, 0, 0, 0.01, 0, 0, 1724032600, 1724032600, 0);

-- 活动任务
INSERT INTO `db_dreame_goods`.`t_draw_activity_task` ( `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 6, 1, 1, 1, 1, 0, 1723685193 , 1723685193 , 0 ),
    ( 6, 6, 2, 12, 1, 0, 1723685193 , 1723685193 , 0 );





-- 订单快照表增加创建时间字段
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2022` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2023` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2024` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2025` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2026` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2027` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2028` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2029` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2030` ADD `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间';




UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f5132d5221862240865.png'  WHERE `id` = 22;
UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f5332c1df1812240867.png'  WHERE `id` = 23;
UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f4ec3c2562462240887.png'  WHERE `id` = 24;

UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f5132d5221862240865.png'  WHERE `id` = 27;
UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f5332c1df1812240867.png'  WHERE `id` = 28;
UPDATE `db_dreame_goods`.`t_draw_prize` SET `image` = 'https://wpm-cdn.dreame.tech/images/202409/66e4f4ec3c2562462240887.png'  WHERE `id` = 29;



UPDATE `db_dreame_goods`.`t_draw_activity` SET `end_time` = 1728316799  WHERE `id` = 5;
