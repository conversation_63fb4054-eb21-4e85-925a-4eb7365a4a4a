-- 山姆等新增积分和觅享分记录表
CREATE TABLE if NOT EXISTS `db_dreame`.`t_point_push`
(
    `id`            int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id`       int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `phone`         varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `uid`           varchar(50)  NOT NULL DEFAULT '' COMMENT '用户UID',
    `score`         int(11) NOT NULL DEFAULT '0' COMMENT '数值',
    `model`         varchar(50)  NOT NULL DEFAULT '' COMMENT '类型,枚举：积分point，觅享分grow',
    `type`          varchar(50)  NOT NULL DEFAULT 'add' COMMENT '增减类型,枚举：增加add，减少reduce',
    `source`        varchar(100)  NOT NULL DEFAULT '' COMMENT '来源,枚举：山姆订单，线下门店，企微活动，会员活动，其他...',
    `sub_source`    varchar(100)  NOT NULL DEFAULT '' COMMENT '子来源,枚举：具体门店/活动描述等',
    `event`         varchar(50)  NOT NULL DEFAULT '' COMMENT '积分事件,枚举：购物purchase，活动activity，福利benifits，其他...',
    `excel_no`      varchar(50)  NOT NULL DEFAULT '' COMMENT 'excel编号',
    `push_no`       varchar(100)  NOT NULL DEFAULT '' COMMENT '推送编号',
    `remark`        varchar(500)  NOT NULL DEFAULT '' COMMENT '备注',
    `status`        tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态,枚举：1待发放，2已发放，3已取消',
    `release_time`  int(11) NOT NULL DEFAULT '0' COMMENT '发放时间',
    `admin_id`      int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `ctime`         int(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime`         int(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_idx_push_no` (`push_no`) USING BTREE
)DEFAULT CHARSET = utf8mb4 COMMENT = '添加/扣减积分和觅享分';

ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `shop_code` varchar(100) NOT NULL DEFAULT '' COMMENT '门店编码';


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (695, 'pointPush', 0, 'points/push', '积分/觅享分推送', 1645013991, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (696, 'pointPushList', 695, 'back/point-push/list', '积分/觅享分上传记录表', 1645013991, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (697, 'pointPushGivePoints', 695, 'back/point-push/give-points', '积分/觅享分批量上传', 1645013991, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (698, 'pointPushQueryPoints', 695, 'back/point-push/query-upload', '积分/觅享分批量上传查询记录', 1645013991, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (699, 'pointPushPushPoints', 695, 'back/point-push/push-points', '积分/觅享分批量发放', 1645013991, 0, 1, 0, 0, '');