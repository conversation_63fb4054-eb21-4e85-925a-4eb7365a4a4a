CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_white_list` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `phone` varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `is_reg` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否已被注册',
    `reg_user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '注册人',
    `reg_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '注册时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_idx_sn` (`sn`) USING BTREE
    ) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品注册白名单表';

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES
    (690, 'productWhiteList', 73, 'product/whitelist', '产品注册白名单', 1644994229, 0, 1, 1, 0, ''),
    (691, 'whiteList', 690, 'back/product/white-list', '获取产品注册白名单列表', 1645443366, 0, 1, 0, 0, ''),
    (692, 'whiteSave', 690, 'back/product/white-save', '产品注册白名单增改', 1645443366, 0, 1, 0, 0, ''),
    (693, 'whiteDel', 690, 'back/product/white-del', '产品注册白名单删除', 1645443366, 0, 1, 0, 0, '');