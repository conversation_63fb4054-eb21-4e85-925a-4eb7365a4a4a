-- 国补活动主表
CREATE TABLE `db_dreame_goods`.`subsidy_activity`
(
    `id`          BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `name`        VARCHAR(255) NOT NULL COMMENT '活动名称',
    `start_time`  BIGINT       NOT NULL COMMENT '开始时间',
    `end_time`    BIGINT       NOT NULL COMMENT '结束时间',
    `desc`        TEXT COMMENT '活动描述',
    `status`      TINYINT      NOT NULL DEFAULT 0 COMMENT '活动状态：0-启用，1-禁用',
    `create_time` BIGINT       NOT NULL COMMENT '创建时间（时间戳，秒）',
    `update_time` BIGINT       NOT NULL DEFAULT 0 COMMENT '更新时间',
    `delete_time` BIGINT                DEFAULT NULL COMMENT '删除时间',
    `is_deleted`  TINYINT      NOT NULL DEFAULT 0 COMMENT '是否删除：0-未删除，1-已删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动主表';

-- 国补活动商品关联表
CREATE TABLE `db_dreame_goods`.`subsidy_activity_goods`
(
    `id`            BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '关联ID',
    `activity_id`   BIGINT UNSIGNED NOT NULL COMMENT '关联活动ID（关联 subsidy_activity.id）',
    `gid`           int  NOT NULL COMMENT '商品ID',
    `tid`           int  NOT NULL default 0 COMMENT '分类ID',
    `subsidy_ratio` DECIMAL(5, 2) NOT NULL COMMENT '国补比例（如：10.00%）',
    `create_time`   BIGINT        NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY             `idx_activity_id` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='国补活动商品关联表';


-- 营销推广模块下添加国补活动菜单
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (661, 'allowanceManager', 6, 'allowanceManager/index', '国家补贴配置', 1752027096, 0, 1, 1, 0, '', 0);
-- 国补活动权限配置
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (660, 'subsidyActivityDelete', 661, 'back/subsidy-activity/delete', '国补活动删除权限', 1751963311, 0, 1, 0, 0, '', 0);
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (659, 'subsidyActivitySave', 661, 'back/subsidy-activity/save', '国补活动保存权限', 1751963311, 0, 1, 0, 0, '', 0);
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (658, 'subsidyActivityDetail', 661, 'back/subsidy-activity/detail', '国补活动详情权限', 1751963311, 0, 1, 0, 0, '', 0);
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri, is_hidden) VALUES (657, 'subsidyActivityList', 661, 'back/subsidy-activity/list', '国补活动列表权限', 1751963311, 0, 1, 0, 0, '', 0);


ALTER TABLE t_uo_0
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_1
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_2
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_3
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_4
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_5
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_6
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_7
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_8
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_9
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';



ALTER TABLE t_uo_g_0
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_1
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_2
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_3
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_4
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_5
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_6
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_7
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_8
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';
ALTER TABLE t_uo_g_9
    ADD COLUMN subsidy_price int(10) unsigned NOT NULL DEFAULT '0' COMMENT '国补金额（分）';