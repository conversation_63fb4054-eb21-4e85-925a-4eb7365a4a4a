
CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_main` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `sku` VARCHAR ( 50 ) NOT NULL DEFAULT '' COMMENT '商品编码',
    `short_code` VARCHAR ( 100 ) NOT NULL DEFAULT '' COMMENT '商品短码',
    `name` VARCHAR ( 50 ) NOT NULL DEFAULT '' COMMENT '商品名',
    `status` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否下架 0否 1是',
    `sort` INT(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `atype` TINYINT NOT NULL DEFAULT '0' COMMENT '商品属性（0：统一规格；1：多规格；2：自定义）',
    `source` TINYINT NOT NULL DEFAULT '1' COMMENT '商品来源（1：普通商品；2：积分商品）',
    `version` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '可见版本，0都可见',
    `is_del` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY ( `id` ) USING BTREE,
    KEY `idx_sku` ( `sku` ) USING BTREE,
    KEY `idx_name` ( `name` ) USING BTREE
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = COMPACT COMMENT = '商品表';



CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_specs`(
    `id` INT(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid` INT(11) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
    `av_ids` varchar(100) NOT NULL DEFAULT '' COMMENT '规格属性id ,分割',
    `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0'  COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0'  COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY ( `id` ) USING BTREE,
    KEY `idx_gid_sku` (`gid`,`sku`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品规格表';



CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_points` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商品id',
    `name` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '商品名',
    `type` TINYINT UNSIGNED NOT NULL DEFAULT '1' COMMENT '商品类型（1：普通商品 2：虚拟商品）',
    `cover_image` VARCHAR (2000) NOT NULL DEFAULT '' COMMENT '封面图（1：小程序，2：APP）json数据类型',
    `cover_image_3d` VARCHAR (255) NOT NULL DEFAULT '' COMMENT '3D封面图',
    `model_3d` VARCHAR (2000) NOT NULL DEFAULT '' NOT NULL COMMENT '3D模型图',
    `video` VARCHAR (255) NOT NULL DEFAULT '' COMMENT '视频',
    `images` VARCHAR (2000) NOT NULL DEFAULT '' COMMENT '图片集（最多10张）',
    `mprice` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '市场价（原价：分）',
    `introduce` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '简介',
    `label` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '商品标签',
    `position` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '展示位置（多选）',
    `tab` VARCHAR(50) NOT NULL DEFAULT '' COMMENT '商品tab（1：新品）',
    `is_send` TINYINT NOT NULL DEFAULT '1' COMMENT '是否发货（1：发货；2：不发货）',
    `detail` VARCHAR ( 5000 ) NOT NULL DEFAULT '' COMMENT '商品详情',
    `levels` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '用户等级',
    `limit_num` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '限购数',
    `notice` VARCHAR (2000) NOT NULL DEFAULT '' COMMENT '兑换须知（json数据）',
    `platform` TINYINT unsigned NOT NULL DEFAULT '99' COMMENT '平台（1:微信小程序;2:app/H5平台;99:全平台）',
    `online_time` VARCHAR(100) NOT NULL DEFAULT '' COMMENT '上架时间',
    `shipping` TINYINT unsigned NOT NULL DEFAULT '1' COMMENT '是否包邮（1:否;2:是）',
    `is_del` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY ( `id` ) USING BTREE,
    KEY `idx_gid` ( `gid`) USING BTREE,
    KEY `idx_name` ( `name` ) USING BTREE
 ) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = COMPACT COMMENT = '积分商品表';




CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_points_price` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商品id',
    `sid` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '多规格id',
    `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
    `exchange` TINYINT UNSIGNED NOT NULL DEFAULT '1' COMMENT '兑换类型（1：积分加钱组合;2：全积分;3：指定兑换券）',
    `price` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '价格（分）',
    `point` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '积分',
    `coupon_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '兑换券ID',
    `image` VARCHAR (500) NOT NULL DEFAULT '' COMMENT '小图（多规格）',
    `is_del` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY ( `id` ) USING BTREE,
    KEY `idx_gid_sid` ( `gid`, `sid` ) USING BTREE
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = COMPACT COMMENT = '积分商品价格表';



CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_atk` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `gid` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '商品id',
    `at_name` VARCHAR (50) NOT NULL DEFAULT '' COMMENT '属性名',
    `is_del` TINYINT UNSIGNED NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY ( `id` ) USING BTREE,
    KEY `idx_gid` ( `gid` ) USING BTREE
) DEFAULT CHARSET = utf8mb4 ROW_FORMAT = COMPACT COMMENT = '属性名（键）表';


CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_av` (
     `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
     `ak_id` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT 'attr-id',
     `at_val` varchar(50)  NOT NULL DEFAULT '' COMMENT '属性值',
     `image` VARCHAR (500) NOT NULL DEFAULT '' COMMENT '小图（多规格）',
     `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
     PRIMARY KEY (`id`) USING BTREE,
     KEY `idx_aid` (`ak_id`) USING BTREE
)  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='属性值表';


CREATE TABLE IF NOT EXISTS `db_dreame_wares`.`t_goods_stock` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品编码',
    `stock` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '库存',
    `sales` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '销量',
    `wait` INT(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '未付款数量',
    `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime` INT(11) NOT NULL DEFAULT '0' COMMENT '加入时间',
    `utime` INT(11) NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` INT(11) NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_sku` (`sku`) USING BTREE
)  DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='商品库存表';


INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (360, 'WaresManager', 0, '', '积分商品管理', 1644579242, 0, 1, 1, 0, '');


INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (361, 'WaresGoodsListManager', 360, 'wares-goods/list', '商品列表', 1690793536, 0, 1, 1, 0, '');


INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (370, 'WaresGoodsList', 361, 'back/wares-goods/list', '商品列表', 1690793536, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (371, 'WaresGoodsSort', 361, 'back/wares-goods/sort', '商品排序', 1690793536, 0, 1, 0, 0, '');


INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (372, 'WaresGoodsSale', 361, 'back/wares-goods/sale', '商品上下架', 1690793536, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (373, 'WaresGoodsSyncStock', 361, 'back/wares-goods/sync-stock', '商品同步库存', 1690793536, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (374, 'WaresGoodsDel', 361, 'back/wares-goods/del', '删除商品', 1690793536, 0, 1, 0, 0, '');



INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (362, 'WaresGoodsSaveManager', 360, 'wares-goods/save', '添加/修改商品', 1690793536, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (375, 'WaresGoodsSave', 362, 'back/wares-goods/save', '添加/修改商品', 1690793536, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri`(`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (376, 'WaresGoodsInfo', 362, 'back/wares-goods/info', '商品详情', 1690793536, 0, 1, 0, 0, '');


ALTER TABLE `db_dreame_goods`.`t_om_2022` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2023` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2024` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2025` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2026` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2027` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2028` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2029` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';
ALTER TABLE `db_dreame_goods`.`t_om_2030` MODIFY `type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '订单类型 （1普通订单，2预售订单，3定金订单（deposit表），4积分商城订单）';


ALTER TABLE `db_dreame_goods`.`t_uo_g_0` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_10` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_11` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_12` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_13` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_14` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_15` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_16` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_17` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_18` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_g_19` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';



ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2022` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2023` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2024` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2025` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2026` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2027` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2028` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2029` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_uo_cfg_2030` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';


ALTER TABLE `db_dreame_goods`.`t_orefund_g_2022` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2023` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2024` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2025` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2026` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2027` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2028` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2029` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
ALTER TABLE `db_dreame_goods`.`t_orefund_g_2030` ADD `goods_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '1:基础商品模块，2：新商品模块';
