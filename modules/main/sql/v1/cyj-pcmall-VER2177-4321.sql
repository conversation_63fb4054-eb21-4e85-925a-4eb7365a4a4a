-- 数据库：db_dreame_goods
ALTER TABLE `t_grecommend`
    ADD `pc_image` varchar(255) NOT NULL DEFAULT '' COMMENT 'PC主推商品图片';
-- 商品平台
CREATE TABLE `t_goods_platform`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT,
    `gid`         int unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `platform_id` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '平台id',
    `cover_image` varchar(255)  NOT NULL DEFAULT '' COMMENT '封面图',
    `images`      varchar(2000) NOT NULL DEFAULT '' COMMENT '图片',
    `detail`      text          NOT NULL COMMENT '商品详情',
    `goods_type`  tinyint unsigned NOT NULL DEFAULT '1' COMMENT '商品类型：1普通商品、2积分商品、3先试后买',
    `ctime`       int           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       int           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_gid` (`gid`) USING BTREE,
    KEY           `idx_platform_id` (`platform_id`) USING BTREE
)ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '商品-平台关联表';

-- 初始化商品平台数据（普通商品-微信小程序平台），market_image为小程序的图，cover_image为APP/H5的图，market_image可能不存在
INSERT INTO `t_goods_platform`(gid, platform_id, cover_image, images, goods_type, detail, ctime, utime)
SELECT gid,
       1,
       CASE
           WHEN market_image = '' THEN cover_image
           ELSE market_image
           END AS cover_image,
       images,
       1,
       detail,
       UNIX_TIMESTAMP(),
       UNIX_TIMESTAMP()
FROM `t_gtype_0`
WHERE platform IN (1, 99);

-- 初始化商品平台数据（普通商品-APP/H5平台）
INSERT INTO `t_goods_platform`(gid, platform_id, cover_image, images, goods_type, detail, ctime, utime)
SELECT gid,
       2,
       cover_image,
       images,
       1,
       detail,
       UNIX_TIMESTAMP(),
       UNIX_TIMESTAMP()
FROM `t_gtype_0`
WHERE platform IN (2, 99);

-- 初始化商品平台数据（普通商品-微信小程序平台），market_image为小程序的图，cover_image为APP/H5的图，market_image可能不存在
INSERT INTO `t_goods_platform`(gid, platform_id, cover_image, images, goods_type, detail, ctime, utime)
SELECT gid,
       1,
       CASE
           WHEN market_image = '' THEN cover_image
           ELSE market_image
           END AS cover_image,
       images,
       1,
       detail,
       UNIX_TIMESTAMP(),
       UNIX_TIMESTAMP()
FROM `t_gtype_99`
WHERE platform IN (1, 99);

-- 初始化商品平台数据（普通商品-APP/H5平台）
INSERT INTO `t_goods_platform`(gid, platform_id, cover_image, images, goods_type, detail, ctime, utime)
SELECT gid,
       2,
       cover_image,
       images,
       1,
       detail,
       UNIX_TIMESTAMP(),
       UNIX_TIMESTAMP()
FROM `t_gtype_99`
WHERE platform IN (2, 99);

-- 增加标签字段
ALTER TABLE `db_dreame_goods`.`t_gtype_0`
    ADD COLUMN `custom_tag` varchar(50) NOT NULL DEFAULT '' COMMENT '自定义标签' AFTER `platform`;
-- 增加标签字段
ALTER TABLE `db_dreame_goods`.`t_gtype_99`
    ADD COLUMN `custom_tag` varchar(50) NOT NULL DEFAULT '' COMMENT '自定义标签' AFTER `platform`;



CREATE TABLE `t_order_pay_history_2024`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2025`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2026`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2027`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2028`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2029`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';

CREATE TABLE `t_order_pay_history_2030`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT,
    `order_no`    VARCHAR(50)   NOT NULL DEFAULT '' COMMENT '订单号',
    `pay_type`    TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付类型：1微信JSAPI、2微信H5、3微信APP、4支付宝APP、6先试后买、7支付中台（微信Native）、8支付中台（支付宝Native）',
    `prepay_id`   VARCHAR(2000) NOT NULL DEFAULT '' COMMENT '预支付ID',
    `expire_time` INT           NOT NULL DEFAULT '0' COMMENT '过期时间',
    `ctime`       INT           NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       INT           NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_order_no_pay_type` ( `order_no`, `pay_type` ) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '订单支付流水';



-- 数据库 db_dreame
-- 平台表
CREATE TABLE `t_platform`
(
    `id`    INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID（平台ID）',
    `name`  VARCHAR(100) NOT NULL DEFAULT '' COMMENT '平台名称',
    `ctime` INT          NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` INT          NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = INNODB DEFAULT CHARSET = utf8mb4 COMMENT = '平台表';

-- Banner-平台关联表
CREATE TABLE `t_banner_platform`
(
    `id`          int UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `banner_id`   INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品id',
    `platform_id` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '平台id',
    `ctime`       int NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       int NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_banner_id` ( `banner_id` ) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4  COMMENT='Banner-平台关联表';

-- 初始化平台数据
INSERT INTO `db_dreame`.`t_platform` (`id`, `name`, `ctime`, `utime`)
VALUES (1, '微信小程序', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
       (2, 'APP/H5', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
       (3, 'PC', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());


-- 初始化Banner平台数据（微信小程序平台）
INSERT INTO `t_banner_platform`(banner_id, platform_id, ctime, utime)
SELECT id, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
FROM `t_banner`
WHERE platform IN (1, 99);

-- 初始化Banner平台数据（APP/H5）
INSERT INTO `t_banner_platform`(banner_id, platform_id, ctime, utime)
SELECT id, 2, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()
FROM `t_banner`
WHERE platform IN (2, 99);


-- 用户平台表增加解绑状态字段
ALTER TABLE `db_dreame`.`t_users_platform`
    ADD COLUMN `status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '解绑状态（0：正常 1：解绑）' AFTER `user_id`;

-- 清除缓存！！！
getOneGtypeByGid