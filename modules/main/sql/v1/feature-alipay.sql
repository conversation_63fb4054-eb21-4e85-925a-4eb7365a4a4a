-- 修改支付宝支付表字段
ALTER TABLE `db_dreame_goods`.`t_opay_2024`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2025`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2026`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2027`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2028`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2029`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;

ALTER TABLE `db_dreame_goods`.`t_opay_2030`
    MODIFY COLUMN `prepay_id` varchar(2000) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)/支付宝统一下单返回' AFTER `order_no`;


ALTER TABLE `db_dreame_goods`.`t_opay_2024`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2025`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2026`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2027`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2028`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2029`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;

ALTER TABLE `db_dreame_goods`.`t_opay_2030`
    MODIFY COLUMN `pay_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '支付方式（1:微信小程序;2:微信H5支付;3:微信APP支付;4:支付宝APP支付）' AFTER `tid`;