CREATE TABLE `db_dreame_goods`.`t_goods_category`
(
    `id`     INT          NOT NULL AUTO_INCREMENT COMMENT '分类主键ID',
    `name`   VARCHAR(100) NOT NULL COMMENT '分类名称，如“扫地机”',
    `sort`   INT          NOT NULL COMMENT '排序',
    `is_del` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime`  int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`  int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime`  int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='产品站分类表';



CREATE TABLE `db_dreame_goods`.`t_product_matrix`
(
    `category_id`  int NOT NULL COMMENT '分类ID，作为主键，外键关联 categories.id',
    `main_product` text COMMENT '主推商品信息的 JSON 字符串',
    `sub_products` text COMMENT '次推商品信息的 JSON 数组字符串',
    `ctime`        int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`        int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`category_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='商品矩阵表';


CREATE TABLE `db_dreame_goods`.`t_official_intro`
(
    `id`     INT          NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `title`  VARCHAR(100) NOT NULL COMMENT '页面名称',
    `status` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态（0上架 1下架）',
    `intro`  text         NOT NULL COMMENT '简介详情',
    `ctime`  int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`  int unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='官网简介表';


-- 新增字段
ALTER TABLE `db_dreame`.`t_banner` ADD `banner_detail` TEXT NOT NULL COMMENT 'PC商城banner详情信息';

ALTER TABLE `db_dreame`.`t_banner` ADD `delete_time` int unsigned  NOT NULL  COMMENT '删除时间';

ALTER TABLE `db_dreame`.`t_banner` MODIFY COLUMN jump_url VARCHAR(255)  NULL COMMENT '跳转url';

-- 只存在一个平台的
UPDATE `db_dreame`.`t_banner` AS banner
    JOIN (
    SELECT
    banner_id,
    platform_id
    FROM
    t_banner_platform AS platform
    GROUP BY
    banner_id
    HAVING
    COUNT(platform.banner_id) = 1
    ) AS platform_data
ON banner.id = platform_data.banner_id
    SET banner.platform = platform_data.platform_id;

-- 存在多个平台  4代表微信小程序和app
UPDATE `db_dreame`.`t_banner` AS banner
    JOIN (
    SELECT
    banner_id,
    platform_id
    FROM
    t_banner_platform AS platform
    GROUP BY
    banner_id
    HAVING
    COUNT(platform.banner_id) > 1
    ) AS platform_data
ON banner.id = platform_data.banner_id
    SET banner.platform = 4;


-- 后台权限SQL
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (755, 'productMatrixManager', 58, 'goods/productMatrix', 'PC商城商品矩阵', 1732863193, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (756, 'productMatrixModify', 755, 'back/product-matrix/modify', '产品站信息保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (757, 'productMatrixDetail', 755, 'back/product-matrix/detail', '产品站信息详情', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (758, 'productMatrixCategoryModify', 755, 'back/product-matrix/category-modify', '产品站商品品类保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (759, 'productMatrixCategoryList', 755, 'back/product-matrix/category-list', '产品站商品品类列表', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (760, 'productMatrixCategoryDelete', 755, 'back/product-matrix/category-delete', '产品站商品品类删除', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (761, 'productMatrixIntroManager', 0, 'introManager', '介绍页管理', 1732863193, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (762, 'productMatrixIntroModify', 761, 'back/product-matrix/intro-modify', '官方介绍保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (763, 'productMatrixIntroDetail', 761, 'back/product-matrix/intro-detail', '官方介绍详情', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (764, 'productMatrixIntroList', 761, 'back/product-matrix/intro-list', '官方介绍列表', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (765, 'productMatrixIntroStatus', 761, 'back/product-matrix/intro-status', '官方介绍上下架', 1732863193, 0, 1, 0, 0, '');

-- banner相关后台权限
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerList', `p_id` = 66, `uri` = 'admin-api/v1/banner/search', `uri_name` = 'banner列表权限' WHERE `id` = 67;
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerCreate', `p_id` = 66, `uri` = 'admin-api/v1/banner/create', `uri_name` = 'banner新增权限' WHERE `id` = 68;
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerDelete', `p_id` = 66, `uri` = 'admin-api/v1/banner/delete', `uri_name` = 'banner删除权限' WHERE `id` = 69;
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerDetail', `p_id` = 66, `uri` = 'admin-api/v1/banner/detail', `uri_name` = 'banner详情权限' WHERE `id` = 70;
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerUpdate', `p_id` = 66, `uri` = 'admin-api/v1/banner/update', `uri_name` = 'banner修改权限' WHERE `id` = 71;
UPDATE `db_dreame_admin`.`t_uri` SET `name` = 'bannerPatch', `p_id` = 66, `uri` = 'admin-api/v1/banner/patch', `uri_name` = 'banner部分更新权限' WHERE `id` = 72;

