-- 919活动
INSERT INTO `db_dreame_goods`.`t_draw_activity` ( `id`, `name`, `start_time`, `end_time`, `daily_free_times`, `status`, `desc`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 7, '双十一活动', 1729440000, 1732118399, 1, 1, '', 0, 1727157271, 1727157271, 0 );



-- 活动任务
INSERT INTO `db_dreame_goods`.`t_draw_activity_task` ( `activity_id`, `task_id`, `task_limit_type`, `task_limit`, `draw_times`, `is_del`, `ctime`, `utime`, `dtime` )
VALUES
    ( 7, 1, 1, 1, 1, 0, 1727157271 , 1727157271 , 0 ),
    ( 7, 6, 2, 12, 1, 0, 1727157271 , 1727157271 , 0 );


-- 奖品信息
    INSERT INTO `db_dreame_goods`.`t_draw_prize`
(`id`, `name`, `type`, `value`, `image`, `default_image`, `active_image`, `desc`, `remark`, `is_del`, `ctime`, `utime`, `dtime`)
VALUES
    (30, '配件五折优惠券', 3, '652', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png', '', '', '', '优惠券id', 0, 1727157271, 1727157271, 0),
    (31, '扑克牌套装', 3, '625', 'https://wpm-cdn.dreame.tech/images/202408/66c5a0ac8a6d25672030049.png', '', '', '', '优惠券id', 0, 1727157271, 1727157271, 0),
    (32, '多巴胺杯-渐变蜜桃', 3, '626', 'https://wpm-cdn.dreame.tech/images/202409/66e4f5132d5221862240865', '', '', '', '优惠券id', 0, 1727157271, 1727157271, 0),
    (33, '露营椅', 3, '627', 'https://wpm-cdn.dreame.tech/images/202409/66e4f5332c1df1812240867.png', '', '', '', '优惠券id', 0, 1727157271, 1727157271, 0),
    (34, 'Pocket吹风机', 3, '628', 'https://wpm-cdn.dreame.tech/images/202409/66e4f4ec3c2562462240887.png', '', '', '', '优惠券id', 0, 1727157271, 1727157271, 0);


-- 活动奖品
INSERT INTO `db_dreame_goods`.`t_draw_activity_prize`
(`activity_id`, `prize_id`, `prize_num`, `prize_limit`, `prize_issue_num`, `prize_lock_num`, `rate`, `is_default`,
 `is_del`, `ctime`, `utime`, `dtime`)
VALUES (7, 14, 1000000, 1000000, 0, 0, 5.01, 1, 0, 1727157271, 1727157271, 0),
       (7, 15, 10000, 10000, 0, 0, 5.00, 0, 0, 1727157271, 1727157271, 0),
       (7, 16, 4000, 4000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (7, 17, 4000, 4000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (7, 18, 3000, 3000, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (7, 19, 2000, 2000, 0, 0, 9.69, 0, 0, 1727157271, 1727157271, 0),
       (7, 30, 3000, 1, 0, 0, 20.00, 0, 0, 1727157271, 1727157271, 0),
       (7, 31, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (7, 32, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (7, 33, 10, 1, 0, 0, 0.10, 0, 0, 1727157271, 1727157271, 0),
       (7, 34, 1, 1, 0, 0, 0.00, 0, 0, 1727157271, 1727157271, 0);

-- 新增 拼团活动表
CREATE TABLE `db_dreame_goods`.`t_group_purchase_activities`
(
    `id`          int              NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `name`        varchar(255)     NOT NULL COMMENT '活动名称',
    `start_time`  int unsigned     NOT NULL DEFAULT '0' COMMENT '活动开始时间',
    `end_time`    int unsigned     NOT NULL DEFAULT '0' COMMENT '活动结束时间',
    `details`     text COMMENT '活动详情',
    `min_members` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '默认团员最小人数',
    `max_members` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '默认团员最大人数',
    `is_del`      tinyint unsigned NOT NULL DEFAULT '0' COMMENT '删除标志（0: 未删除，1: 已删除）',
    `ctime`       int unsigned     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`       int unsigned     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`       int unsigned              DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='拼团活动表';

CREATE TABLE `db_dreame_goods`.`t_group_purchase_goods`
(
    `id`             int              NOT NULL AUTO_INCREMENT COMMENT '商品关联ID',
    `gid`            int unsigned     NOT NULL COMMENT '商品ID',
    `activity_id`    int unsigned     NOT NULL COMMENT '活动ID',
    `min_members`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '最小团员数',
    `max_members`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '最大团员数',
    `purchase_limit` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '限购数量',
    `min_group_qty`  tinyint unsigned NOT NULL DEFAULT '0' COMMENT '成团最低数量',
    `sort`           smallint unsigned         DEFAULT '0' COMMENT '排序',
    `is_del`         tinyint unsigned NOT NULL DEFAULT '0' COMMENT '删除标志（0: 未删除，1: 已删除）',
    `ctime`          int unsigned     NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`          int unsigned     NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`          int unsigned              DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_activity_id` (`activity_id`),
    KEY `idx_gid` (`gid`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='团购商品表';

CREATE TABLE `db_dreame_goods`.`t_group_purchase_members`
(
    `id`                int         NOT NULL AUTO_INCREMENT,
    `activity_id`       int unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `group_purchase_id` int unsigned DEFAULT '0' COMMENT '参团ID',
    `user_id`           int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    uid                 varchar(18)          default '' not null comment 'uid',
    `order_no`          varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
    `items_qty`         int unsigned NOT NULL DEFAULT '0' COMMENT '商品数量',
    `ctime`             int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`             int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                 `idx_activity_id` (`activity_id`),
    KEY                 `idx_gpurchase_id` (`group_purchase_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='参团记录表';

CREATE TABLE `db_dreame_goods`.`t_group_purchases`
(
    `id`            int                    NOT NULL AUTO_INCREMENT COMMENT '团ID',
    `activity_id`   int unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `gid`           int unsigned NOT NULL COMMENT '商品ID',
    `sid`           int unsigned default '0' not null comment '规格ID',
    `user_id`       int unsigned NOT NULL COMMENT '用户ID',
    `uid`           varchar(18) default '' not null comment 'uid',
    `status`        tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态 （0：拼团中 1：拼团成功  2：团结束已拼满）',
    `total_items`   smallint unsigned DEFAULT '0' COMMENT '总购买件数',
    `total_members` smallint unsigned NOT NULL DEFAULT '0' COMMENT '总参团人数',
    `total_price`   int unsigned default '0' not null comment '总金额',
    `ctime`         int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`         int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY             `idx_activity_id` (`activity_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='发起团购表';


CREATE TABLE `db_dreame`.`t_user_popup`
(
    `id`           int NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id`      int unsigned NOT NULL COMMENT '用户ID',
    `popup_type`   tinyint unsigned DEFAULT '0' COMMENT '弹窗类型（1：成为团长）',
    `is_displayed` ENUM('0', '1') DEFAULT '0' COMMENT '是否展示（0：未展示 1：已展示）',
    `ctime`        int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`        int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY            `idx_user_id_type` (`user_id`,`popup_type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  COMMENT='用户弹窗管理表';

-- 修改 订单表添加拼团ID
alter table `db_dreame_goods`.t_uo_0
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_1
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_2
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_3
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_4
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_5
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_6
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_7
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_8
    add group_purchase_id int unsigned default '0' not null comment '参团ID';
alter table `db_dreame_goods`.t_uo_9
    add group_purchase_id int unsigned default '0' not null comment '参团ID';

-- 后台路由
-- 菜单
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (720, 'groupPurchaseManager', 0, 'group-purchase/manage',   '拼团管理', 1727575925, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (721, 'groupPurchaseActivityManager', 720, 'group-purchase-activity/manage',   '拼团活动', 1727575925, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (722, 'groupPurchaseRecordManager', 720, 'group-purchase-record/manage',   '拼团记录', 1727575925, 0, 1, 1, 0, '');
-- 拼团记录明细
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (733, 'groupPurchaseRecordDetail', 720, 'group-purchase-record/detail',   '拼团记录明细', 1727575925, 0, 1, 1, 0, '');
-- 新增/编辑拼团活动
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (734, 'groupPurchaseActivityEdit', 720, 'group-purchase-activity/edit',   '新增/编辑拼团活动', 1727575925, 0, 1, 1, 0, '');

-- 接口
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (723, 'groupPurchaseActivityList', 721, 'back/group-purchase/activity-list',   '拼团活动列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (724, 'groupPurchaseActivitySave', 721, 'back/group-purchase/activity-save',   '拼团活动新增/修改', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (725, 'groupPurchaseActivityDelete', 721, 'back/group-purchase/activity-delete',   '拼团活动删除', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (726, 'groupPurchaseActivityDetail', 721, 'back/group-purchase/activity-detail',   '拼团活动详情', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (727, 'groupPurchaseGoodSearch', 721, 'back/group-purchase/goods-search',   '拼团活动商品搜索', 1727575925, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (728, 'groupPurchaseList', 722, 'back/group-purchase/list',   '拼团列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (729, 'groupPurchaseDetail', 722, 'back/group-purchase/detail',   '拼团详情', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (730, 'groupPurchaseRecordList', 722, 'back/group-purchase/group-record-list',   '拼团记录列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (731, 'groupPurchaseRecordDetail', 722, 'back/group-purchase/group-record-detail',   '拼团记录详情', 1727575925, 0, 1, 0, 0, '');
