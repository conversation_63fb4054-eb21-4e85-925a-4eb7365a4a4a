
ALTER TABLE `db_dreame_log`.`t_p_reg_0` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_1` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_2` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_3` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_4` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_5` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_6` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_7` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_8` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';
ALTER TABLE `db_dreame_log`.`t_p_reg_9` ADD `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号';

ALTER TABLE `db_dreame`.`t_products` ADD `period` tinyint(1) NOT NULL DEFAULT '0' COMMENT '保修月份（单位：个）';
UPDATE `db_dreame`.`t_products` SET `period` = 24 where `id`>0;

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2023` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `phone` varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品id',
    `card_no` varchar(50) NOT NULL DEFAULT '' COMMENT '卡号',
    `apply_no` varchar(50) NOT NULL DEFAULT '' COMMENT '申请号',
    `sn_data` varchar(500) NOT NULL DEFAULT '' COMMENT '资源内容 json数据',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `period_time` int(11) NOT NULL DEFAULT '0' COMMENT '保修期',
    `buy_time` int(11) NOT NULL  DEFAULT '0' COMMENT '购买时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_idx_card_no` (`card_no`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品电子保修卡详情表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2024` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2025` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2026` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2027` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2028` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2029` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_card_2030` LIKE `db_dreame_log`.`t_p_warranty_card_2023`;


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_0` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `reg_id` int(11) NOT NULL DEFAULT '0' COMMENT '注册id',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品id',
    `apply_no` varchar(50) NOT NULL DEFAULT '' COMMENT '申请号',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `apply_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '审核（1审核通过 0未审核 2审核中 3审核不通过）',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_idx_apply_no` (`apply_no`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品电子保修卡申请表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_1` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_2` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_3` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_4` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_5` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_6` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_7` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_8` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_9` LIKE `db_dreame_log`.`t_p_warranty_apply_0`;


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2023` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `phone` varchar(20)  NOT NULL DEFAULT '' COMMENT '手机号',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品id',
    `apply_no` varchar(50) NOT NULL DEFAULT '' COMMENT '申请号',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `period_time` int(11) NOT NULL DEFAULT '0' COMMENT '保修期',
    `buy_time` int(11) NOT NULL DEFAULT '0' COMMENT '购买时间',
    `purchase_platform` tinyint(1) NOT NULL DEFAULT '0' COMMENT '购买平台',
    `order_no` varchar(100)  NOT NULL DEFAULT '' COMMENT '订单编号',
    `purchase_cert` varchar(1000)  NOT NULL DEFAULT '' COMMENT '购物凭证',
    `apply_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '审核（1审核通过 0未审核 2审核中 3审核不通过）',
    `reason` varchar(1000)  NOT NULL DEFAULT '' COMMENT '审核原因',
    `admin_user_id` int(11) NOT NULL DEFAULT '0' COMMENT '操作人ID',
    `admin_operate_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '操作时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `dtime` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_idx_apply_no` (`apply_no`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='产品电子保修卡申请详情表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2024` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2025` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2026` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2027` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2028` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2029` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_warranty_apply_detail_2030` LIKE `db_dreame_log`.`t_p_warranty_apply_detail_2023`;


INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (350, 'WarrantyManager', 0, '', '保修卡管理', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (351, 'WarrantyCardManager', 350, 'warranty/card/list', '电子保修卡记录', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (352, 'WarrantyCardList', 351, 'back/warranty-card/list', '保修卡列表', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (353, 'WarrantyCardDetail', 351, 'back/warranty-card/detail', '保修卡详情', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (357, 'WarrantyCardDetail', 351, 'back/warranty-card/set-period', '设置电子保修卡时间', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (354, 'WarrantyApplyDetailManager', 350, 'warranty/apply-detail/list', '保修卡待审核记录', 1644579242, 0, 1, 1, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (355, 'WarrantyApplyDetailList', 354, 'back/warranty-apply-detail/list', '待审核记录列表', 1644579242, 0, 1, 0, 0, '');

INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`)
VALUES (356, 'WarrantyApplyAudit', 354, 'back/warranty-apply-detail/audit', '保修卡申请审核', 1644579242, 0, 1, 0, 0, '');