CREATE TABLE if NOT EXISTS `db_dreame_log`.`t_order_zwx` (
    `id` int(10)  unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '定金订单号',
    `order_status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定金订单状态...',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建事件',
    `utime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `u_order` (`user_id`,`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='OMS待置无效订单表';