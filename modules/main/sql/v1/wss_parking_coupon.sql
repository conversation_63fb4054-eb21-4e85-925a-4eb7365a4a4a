CREATE TABLE `db_dreame_goods`.`t_coupon`
(
    `id`          int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID主键',
    `name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '卡券名称',
    `image`       varchar(255) NOT NULL DEFAULT '' COMMENT '卡券封面',
    `code`        varchar(50)  NOT NULL DEFAULT '' COMMENT '卡券编码',
    `validity`    int          NOT NULL DEFAULT '0' COMMENT '有效期，时间戳',
    `type`        tinyint      NOT NULL DEFAULT '0' COMMENT '卡券类型（待定）',
    `stock_num`   int unsigned NOT NULL DEFAULT '0' COMMENT '库存数量',
    `appid`       varchar(255) NOT NULL DEFAULT '' COMMENT 'appid',
    `app_path`    varchar(255) NOT NULL DEFAULT '' COMMENT 'app path',
    `originid`    varchar(255) NOT NULL DEFAULT '' COMMENT 'originid',
    `origin_path` varchar(255) NOT NULL DEFAULT '' COMMENT 'origin path',
    `short_chain` varchar(255) NOT NULL DEFAULT '' COMMENT '短链',
    `is_deleted`  tinyint      NOT NULL DEFAULT '0' COMMENT '是否已删除 0 未删除 1已删除',
    `create_at`   bigint                DEFAULT '0' COMMENT '添加时间',
    `update_at`   bigint                DEFAULT '0' COMMENT '更新时间',
    `delete_at`   bigint                DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY           `idx_name` (`name`) USING BTREE,
    KEY           `idx_code` (`code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡券主表';

CREATE TABLE `db_dreame_goods`.`t_coupon_codes`
(
    `id`              int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID主键',
    `coupon_code`     varchar(50)  NOT NULL DEFAULT '' COMMENT '卡券编码',
    `ticket_code`     varchar(50)  NOT NULL DEFAULT '' COMMENT '券码',
    `status`          tinyint      NOT NULL DEFAULT '0' COMMENT '是否已领取',
    `receive_user_id` int          NOT NULL DEFAULT '0' COMMENT '领取用户',
    `receive_at`      bigint                DEFAULT '0' COMMENT '领取时间',
    `order_no`        varchar(255) NOT NULL DEFAULT '' COMMENT '对应订单号',
    `is_deleted`      tinyint      NOT NULL DEFAULT '0' COMMENT '是否已删除 0 未删除 1已删除',
    `create_at`       bigint                DEFAULT '0' COMMENT '添加时间',
    `update_at`       bigint                DEFAULT '0' COMMENT '更新时间',
    `delete_at`       bigint                DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY               `idx_coupon_code` (`coupon_code`) USING BTREE,
    KEY               `idx_order_no` (`order_no`) USING BTREE,
    KEY               `idx_user_id` (`receive_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='卡券券码表';


-- 优惠券增加编号字段
ALTER TABLE `db_dreame_goods`.`t_market_config`
    ADD COLUMN `code` varchar(50) NOT NULL COMMENT '营销券编号' AFTER `id`;

--  积分商品增加子类型
ALTER TABLE `db_dreame_wares`.`t_goods_points`
    ADD COLUMN `subtype` tinyint(3) NOT NULL DEFAULT 0 COMMENT '商品子类型' AFTER `type`;

INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (811, 'WaresGoodsCouponManager', 360, 'wares-goods/coupon', '卡券管理', 1737105502, 0, 1, 1, 0, '');

INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (985, 'CouponList', 811, 'coupon/list', '卡券列表', 1737105502, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (986, 'CouponDel', 811, 'coupon/del', '卡券删除', 1737105502, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (987, 'CouponSave', 811, 'coupon/save', '卡券新增/编辑', 1737105502, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (988, 'CouponCodeList', 811, 'coupon/code-list', '券码列表', 1737105502, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (989, 'CouponSend', 811, 'coupon/send', '券码发放', 1737105502, 0, 1, 0, 0, '');
INSERT INTO db_dreame_admin.t_uri (id, name, p_id, uri, uri_name, time, is_del, is_check, is_menu, `rank`, ext_uri) VALUES (990, 'CouponInvalid', 811, 'coupon/invalid', '券码失效', 1737105502, 0, 1, 0, 0, '');

-- 生成营销券编号
UPDATE `db_dreame_goods`.t_market_config SET code = CONCAT('YX', UPPER(REPLACE(UUID(), '-', ''))) WHERE code IS NULL OR code = '';

-- 包邮字段新增
ALTER TABLE `db_dreame_goods`.t_gtype_0 ADD COLUMN is_free_shipping TINYINT UNSIGNED DEFAULT 0 NOT NULL COMMENT '是否包邮 0否 1是';