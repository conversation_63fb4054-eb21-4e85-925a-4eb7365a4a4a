CREATE TABLE `t_uri_copy1`
(
    `id`        int(10) unsigned NOT NULL AUTO_INCREMENT,
    `name`      varchar(255)          DEFAULT NULL COMMENT '组件名称',
    `p_id`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
    `uri`       varchar(255)          DEFAULT NULL COMMENT '路由地址',
    `uri_name`  varchar(100) NOT NULL DEFAULT '' COMMENT 'uri路由备注',
    `time`      int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
    `is_del`    tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '是否已删除（0:否，1:是）',
    `is_check`  tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否权限验证（0：否，1是）',
    `is_menu`   tinyint(1) DEFAULT '1' COMMENT '是否为菜单 0否 1是',
    `rank`      tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序0-255（值越大越靠前）',
    `ext_uri`   varchar(255) NOT NULL DEFAULT '' COMMENT '扩展uri',
    `is_hidden` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否隐藏 0：不隐藏  1：隐藏',
    PRIMARY KEY (`id`),
    KEY         `index_uri` (`uri`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='路由表';

-- 一级菜单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'mallManager', 0, '', '商城管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'pcWebsiteManager', 0, '', 'PC官网内容管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'orderAndSalesManager', 0, '', '订单/售后管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'usersManager', 0, '', '用户管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'marketResourcesManager', 0, '', '营销资源', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'marketPromotionManager', 0, '', '营销推广', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'marketManager', 0, '', '营销管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'communityManager', 0, '', '社区管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'storeAndSalespersonManager', 0, '', '门店/导购', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'otherManager', 0, '', '其他', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'systemManager', 0, '', '系统', 1745547747, 0, 1, 1, 0, '' );
-- 二级菜单
-- 商城管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'goodsManager', 1, '', '商品管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'goodsCategoryManager', 1, '', '商品类目', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'goodsReviewManager', 1, '', '商品评价', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'pointGoodsManager', 1, '', '积分商品管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'freightManager', 1, '', '配送配置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'wxUlinkManager', 1, '', '生成小程序URL', 1745547747, 0, 1, 1, 0, '' );
-- 	PC官网内容管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'productMatrixIntroManager', 2, '', '介绍页管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'abortUsManager', 2, '', '关于我们', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'latestUpdateManager', 2, '', '最新动态', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'mediaReviewsIndexManager', 2, '', '媒体测评', 1745547747, 0, 1, 1, 0, '' );
-- 	订单/售后管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'orderManager', 3, '', '订单管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'warrantyManager', 3, '', '保修卡管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookManager', 3, '', '预约管理', 1745547747, 0, 1, 1, 0, '' );
-- 用户管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'userCenterManager', 4, '', '用户中心', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'userEmployeeManager', 4, '', '微笑大使管理', 1745547747, 0, 1, 1, 0, '' );
-- 营销资源
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'couponManager', 5, '', '优惠券管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'giftCardManager', 5, '', '礼品卡管理', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productManager', 6, '', '产品注册管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'bannerManger', 6, '', '广告位管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'activityManager', 6, '', '优惠券活动配置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'payActivityManager', 6, '', '支付活动配置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'activityAtmosphereManager', 6, '', '活动氛围配置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'activityTemplateManager', 6, '', '活动模版配置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tryBeforeBuyManager', 6, '', '先用后付', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'TradeInManager', 6, '', '以旧换新', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'groupPurchaseManager', 6, '', '拼团管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'smsManager', 7, '', '短信管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'couponManager', 7, '', '优惠券操作', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'pointsManager', 7, '', '积分/觅享分操作', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'contentManger', 8, '', '内容管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'userReportManager', 8, '', '用户举报管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'storeCenterManager', 9, '', '周边门店', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'guideManager', 9, '', '导购管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'exportLogManager', 10, '', '导出列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'PrivacyManager', 10, '', '隐私管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'systemSettingsManager', 11, '', '系统设置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'controlPanelManager', 11, '', '控制面板', 1745547747, 0, 1, 1, 0, '' );
-- 三级菜单
-- 商品管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'productMatrixManager', 12, '', 'PC商城商品矩阵', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'goodsListManager', 12, '', '商品列表', 1745547747, 0, 1, 1, 0, '' );
-- 商品评价
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'reviewManager', 14, '', '评价管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'reportManager', 14, '', '举报管理', 1745547747, 0, 1, 1, 0, '' );
-- 积分商品管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'pointGoodsListManager', 15, '', '商品列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'waresGoodsCouponManager', 15, '', '卡券管理', 1745547747, 0, 1, 1, 0, '' );
-- 	关于我们
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'companyProfileManager', 19, '', '公司简介', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'founderManager', 19, '', '创始人简介', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'innovativeTechnologyManager', 19, '', '创新科技简介', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'innovativeTechnologyContentManager', 19, '', '创新科技内容', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'marketPerformanceManager', 19, '', '市场表现', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'winAnAwardManager', 19, '', '荣获奖项', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'publicWelfareManager', 19, '', '公益事业简介', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'publicWelfareContentManager', 19, '', '公益事业内容', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'integritySupervisionManager', 19, '', '廉政监督', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'legalAffairsManager', 19, '', '法律事务', 1745547747, 0, 1, 1, 0, '' );
-- 最新动态
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'latestUpdateClassificationManager', 20, '', '分类', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'latestUpdateContentManager', 20, '', '最新动态', 1745547747, 0, 1, 1, 0, '' );
-- 媒体测评
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'mediaReviewsIndexClassificationManager', 21, '', '分类', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'mediaReviewsIndexContentManager', 21, '', '媒体测评', 1745547747, 0, 1, 1, 0, '' );
-- 	订单管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'orderListManager', 22, '', '订单列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'refundListManager', 22, '', '退款列表', 1745547747, 0, 1, 1, 0, '' );

-- 	保修卡管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'warrantyRecordManager', 23, '', '电子保修卡记录', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'warrantyApplyDetailManager', 23, '', '保修卡待审核记录', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'warrantyBlackListManager', 23, '', '电子保修卡黑名单', 1745547747, 0, 1, 1, 0, '' );

-- 预约管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookOrderListManager', 24, '', '预约工单', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookInstallListManager', 24, '', '安装工单', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'serviceOrderListManager', 24, '', '上门服务工单', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookRecordManager', 24, '', '查询记录', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookAddressManager', 24, '', '地址管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookRefundListManager', 24, '', '退款工单', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'bookAssessCenterManager', 24, '', '评价中心', 1745547747, 0, 1, 1, 0, '' );
-- 用户中心
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'userInfoManager', 25, '', '用户查询', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'userListManager', 25, '', '用户列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'sourceListManager', 25, '', '来源列表', 1745547747, 0, 1, 1, 0, '' );
-- 微笑大使管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'employeeManager', 26, '', '员工管理', 1745547747, 0, 1, 1, 0, '' );
-- 礼品卡管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'giftCardManager', 28, '', '礼品卡管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'giftCardGoodsManager', 28, '', '礼品卡商品', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES  ( 'giftCardListManager', 28, '', '礼品卡列表', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'registerListManager', 29, '', '产品注册列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'registerRecordManager', 29, '', '产品注册记录', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'registerPartsManager', 29, '', '产品注册配件管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productExceptionManager', 29, '', '产品注册异常', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productWhiteListManager', 29, '', '产品注册白名单', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productDescManager', 29, '', '产品注册说明', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'marketFineListManager', 31, '', '优惠券配置', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tradeInIndexManager', 35, '', '活动列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tradeInGoodManager', 35, '', '商品管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tryOrderManager', 35, '', '订单管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tryUserManager', 35, '', '用户管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tryAuditManager', 35, '', '人工审核', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'tryUserPathManager', 35, '', '用户链路', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'zfbPasswordManager', 35, '', '支付宝口令维护', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'ActivityWaresManager', 36, '', '活动列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'TryGoodsListManager', 36, '', '商品管理', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'groupPurchaseActivityManager', 37, '', '拼团活动', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'groupPurchaseRecordManager', 37, '', '拼团记录', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'smsSendManager', 38, '', '短信发送', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'smsSendListManager', 38, '', '会员短信查询', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'releaseCouponManager', 39, '', '批量发放优惠券', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'cancelCouponManager', 39, '', '批量废除优惠券', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'pointsRuleManager', 40, '', '积分规则设置', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'pointsRecordManager', 40, '', '积分记录', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'pointsBatchChargeManager', 40, '', '积分/觅享分批量充值', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'pointsBatchChargeRecordManager', 40, '', '积分/觅享分批量充值记录', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'contentListManager', 41, '', '内容列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'contentRolesManager', 41, '', '内容角色管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'contentReportManager', 41, '', '内容举报管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'reviewReportManager', 41, '', '评论举报管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'userContentListManager', 41, '', '用户作品管理', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'storeListManager', 43, '', '门店列表', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'guideListManager', 44, '', '导购列表', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'guideRecordManager', 44, '', '导购记录', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'PrivacyIndexManager', 46, '', '小程序隐私政策', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'PrivacyTypeManager', 46, '', '隐私类型管理', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'userManger', 47, '', '管理员管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'roleManger', 47, '', '角色管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'routerManager', 47, '', '路由管理', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'systemLogManager', 47, '', '系统日志', 1745547747, 0, 1, 1, 0, '' );

INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'apiLogManager', 48, '', '接口日志', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'apiMonitor', 48, '', '接口并发监控', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'codeExecManager', 48, '', '代码执行', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'serverLoadManager', 48, '', '服务器负载', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'qpsMonitorManager', 48, '', 'QPS监控', 1745547747, 0, 1, 1, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'crmLogManager', 48, '', 'CRM错误日志', 1745547747, 0, 1, 1, 0, '' );




-- 接口权限
-- PC商城商品矩阵
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'productMatrixModify', 49, 'back/product-matrix/modify', '产品站信息保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'productMatrixDetail', 49, 'back/product-matrix/detail', '产品站信息详情', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'productMatrixCategoryModify', 49, 'back/product-matrix/category-modify', '产品站商品品类保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'productMatrixCategoryList', 49, 'back/product-matrix/category-list', '产品站商品品类列表', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'productMatrixCategoryDelete', 49, 'back/product-matrix/category-delete', '产品站商品品类删除', 1732863193, 0, 1, 0, 0, '');
-- 商品类目
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsCate', 13, 'back/cate/list', '商品类目列表', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('cateModify', 13, 'back/cate/modify', '商品类目添加编辑', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('cateDel', 13, 'back/cate/delete', '商品类目删除', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('catUpdate', 13, 'back/cate/update', '商品类目删除转移类目', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('cateDrag', 13, 'back/cate/cate-drag', '商品类目拖拽', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('cateDetail', 13, 'back/cate/detail', '商品类目详情', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/add-group', '商品参数-添加参数类型', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/edit-group', '商品参数-编辑参数类型（修改名字）', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/group-param-detail', '商品参数-参数类型详情', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/edit-group-param', '商品参数-编辑参数类型的参数', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/param-list', '商品参数-参数列表', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsParams', 13, 'back/goods-param/del-group', '商品参数-删除参数类型', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsRecommendTag', 13, 'back/goods-recommend/goods-tag', '推荐商品标签', 1686294364, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsRecommendInfo', 13, 'back/goods-recommend/info', '推荐商品详情', 1686294364, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsRecommendSave', 13, 'back/goods-recommend/save', '推荐商品增改', 1686294364, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsRecommendSave', 13, 'back/goods-recommend/goods-list', '推荐商品列表', 1686294364, 0, 1, 0, 0, '');
-- 普通商品列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('saveGoods', 50, 'back/goods/save', '新增/修改商品权限', 1644579805, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsInfo', 50, 'back/goods/info', '获取商品详情权限', 1644579836, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('checkIsSku', 50, 'back/goods/is-sku', '判断sku是否在oms存在权限', 1654850580, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('goodsList', 50, 'back/goods/list', '获取商品列表权限', 1644579529, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'goodsSort', 50, 'back/goods/sort', '修改商品排序权限', 1644579560, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'batchGoods', 50, 'back/goods/batch', '批量商品操作权限', 1646192248, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'delGoods', 50, 'back/goods/del', '删除商品权限', 1647746404, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'syncStock', 50, 'back/goods/sync-stock', '商品同步库存权限', 1648106158, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'presaleDetail', 50, 'back/goods/presale', '预售详情信息', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'goodsPartList', 50, 'back/goods/part-list', '商品配件列表', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'goodsParams', 50, 'back/goods-param/goods-param-detail', '商品参数-商品参数信息', 1680588846, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'goodsParams', 50, 'back/goods/compare-list', '商品参数-商品参数列表', 1680588846, 0, 1, 0, 0, '');
-- 评价管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewList', 51, 'back/goods-review/list', '评价列表', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewDetail', 51, 'back/goods-review/detail', '评价详情', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewAudit', 51, 'back/goods-review/audit', '评价审核', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewRemove', 51, 'back/goods-review/remove', '评价下架', 1720454400, 0, 1, 0, 0, '');
-- 举报管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reportList', 52, 'back/goods-review/report-list', '举报列表', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reportDetail', 52, 'back/goods-review/detail', '举报详情', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reportAudit', 52, 'back/goods-review/audit-report', '举报审核', 1720454400, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reportLabel', 52, 'back/goods-review/labels', '举报标签', 1720454400, 0, 1, 0, 0, '');
-- 积分商品列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('waresGoodsList', 53, 'back/wares-goods/list', '商品列表', 1690793536, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('waresGoodsSort', 53, 'back/wares-goods/sort', '商品排序', 1690793536, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('waresGoodsSale', 53, 'back/wares-goods/sale', '商品上下架', 1690793536, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('waresGoodsSyncStock', 53, 'back/wares-goods/sync-stock', '商品同步库存', 1690793536, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('waresGoodsDel', 53, 'back/wares-goods/del', '删除商品', 1690793536, 0, 1, 0, 0, '');
-- 卡券管理的接口
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponList', 54, 'back/coupon/list', '卡券列表', 1737105502, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponDel', 54, 'back/coupon/del', '卡券删除', 1737105502, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponSave', 54, 'back/coupon/save', '卡券新增/编辑', 1737105502, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponCodeList', 54, 'back/coupon/code-list', '券码列表', 1737105502, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponSend', 54, 'back/coupon/send', '券码发放', 1737105502, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('couponInvalid', 54, 'back/coupon/invalid', '券码失效', 1737105502, 0, 1, 0, 0, '');
-- 配送配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('freightList', 16, 'back/order/freight', '获取运费列表权限', 1645099337, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('saveFreight', 16, 'back/order/freight-save', '运费增改权限', 1645099356, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('delFreight', 16, 'back/order/freight-del', '运费删除权限', 1645099375, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('areaList', 16, 'back/data/area-list', '获取地址库权限', 1645099409, 0, 1, 0, 0, '');
-- 生成小程序URL
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('wxUlink', 17, 'back/data/wx-ulink', '微信生成url-link权限', 1649841758, 0, 1, 0, 0, '');
-- 介绍页管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productMatrixIntroModify', 18, 'back/product-matrix/intro-modify', '官方介绍保存', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productMatrixIntroDetail', 18, 'back/product-matrix/intro-detail', '官方介绍详情', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productMatrixIntroList', 18, 'back/product-matrix/intro-list', '官方介绍列表', 1732863193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productMatrixIntroStatus', 18, 'back/product-matrix/intro-status', '官方介绍上下架', 1732863193, 0, 1, 0, 0, '');
-- 公司简介
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('companyProfileList', 55, 'admin-api/v1/website-info/list', '公司简介列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('companyProfileCreate', 55, 'admin-api/v1/website-info/create', '公司简介创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('companyProfileDetail', 55, 'admin-api/v1/website-info/get', '公司简介详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('companyProfileDelete', 55, 'admin-api/v1/website-info/delete-by-ids', '公司简介删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('companyProfilePatch', 55, 'admin-api/v1/website-info/patch', '公司简介修改', 1743408971, 0, 1, 0, 0, '');
-- 创始人简介
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('founderList', 56, 'admin-api/v1/website-info/list', '创始人简介列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('founderCreate', 56, 'admin-api/v1/website-info/create', '创始人简介创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('founderDetail', 56, 'admin-api/v1/website-info/get', '创始人简介详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('founderDelete', 56, 'admin-api/v1/website-info/delete-by-ids', '创始人简介删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('founderPatch', 56, 'admin-api/v1/website-info/patch', '创始人简介修改', 1743408971, 0, 1, 0, 0, '');
-- 创新科技简介
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyList', 57, 'admin-api/v1/website-info/list', '创新科技简介列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyCreate', 57, 'admin-api/v1/website-info/create', '创新科技简介创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyDetail', 57, 'admin-api/v1/website-info/get', '创新科技简介详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyDelete', 57, 'admin-api/v1/website-info/delete-by-ids', '创新科技简介删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyPatch', 57, 'admin-api/v1/website-info/patch', '创新科技简介修改', 1743408971, 0, 1, 0, 0, '');
-- 创新科技内容
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentList', 58, 'admin-api/v1/website-info/list', '创新科技内容列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentCreate', 58, 'admin-api/v1/website-info/create', '创新科技内容创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentDetail', 58, 'admin-api/v1/website-info/get', '创新科技内容详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentDelete', 58, 'admin-api/v1/website-info/delete-by-ids', '创新科技内容删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentPatch', 58, 'admin-api/v1/website-info/patch', '创新科技内容修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('innovativeTechnologyContentIsPassed', 58, 'admin-api/v1/website-info/activate/is-passed', '创新科技内容上下架', 1743408971, 0, 1, 0, 0, '');
-- 市场表现
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformanceList', 59, 'admin-api/v1/website-info/list', '市场表现列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformanceCreate', 59, 'admin-api/v1/website-info/create', '市场表现创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformanceDetail', 59, 'admin-api/v1/website-info/get', '市场表现详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformanceDelete', 59, 'admin-api/v1/website-info/delete-by-ids', '市场表现删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformancePatch', 59, 'admin-api/v1/website-info/patch', '市场表现修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPerformanceIsPassed', 59, 'admin-api/v1/website-info/activate/is-passed', '市场表现上下架', 1743408971, 0, 1, 0, 0, '');
-- 荣获奖项
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardList', 60, 'admin-api/v1/website-info/list', '荣获奖项列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardCreate', 60, 'admin-api/v1/website-info/create', '荣获奖项创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardDetail', 60, 'admin-api/v1/website-info/get', '荣获奖项详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardDelete', 60, 'admin-api/v1/website-info/delete-by-ids', '荣获奖项删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardPatch', 60, 'admin-api/v1/website-info/patch', '荣获奖项修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('winAnAwardIsPassed', 60, 'admin-api/v1/website-info/activate/is-passed', '荣获奖项上下架', 1743408971, 0, 1, 0, 0, '');
-- 公益事业简介
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareList', 61, 'admin-api/v1/website-info/list', '公益事业简介列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareCreate', 61, 'admin-api/v1/website-info/create', '公益事业简介创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareDetail', 61, 'admin-api/v1/website-info/get', '公益事业简介详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareDelete', 61, 'admin-api/v1/website-info/delete-by-ids', '公益事业简介删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfarePatch', 61, 'admin-api/v1/website-info/patch', '公益事业简介修改', 1743408971, 0, 1, 0, 0, '');
-- 公益事业内容
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentList', 62, 'admin-api/v1/website-info/list', '公益事业内容列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentCreate', 62, 'admin-api/v1/website-info/create', '公益事业内容创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentDetail', 62, 'admin-api/v1/website-info/get', '公益事业内容详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentDelete', 62, 'admin-api/v1/website-info/delete-by-ids', '公益事业内容删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentPatch', 62, 'admin-api/v1/website-info/patch', '公益事业内容修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('publicWelfareContentIsPassed', 62, 'admin-api/v1/website-info/activate/is-passed', '公益事业内容上下架', 1743408971, 0, 1, 0, 0, '');
-- 廉政监督
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionList', 63, 'admin-api/v1/website-info/list', '廉政监督列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionCreate', 63, 'admin-api/v1/website-info/create', '廉政监督创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionDetail', 63, 'admin-api/v1/website-info/get', '廉政监督详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionDelete', 63, 'admin-api/v1/website-info/delete-by-ids', '廉政监督删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionPatch', 63, 'admin-api/v1/website-info/patch', '廉政监督修改', 1743408971, 0, 1, 0, 0, '');
-- 法律事务
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionList', 64, 'admin-api/v1/website-info/list', '廉政监督列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionCreate', 64, 'admin-api/v1/website-info/create', '廉政监督创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionDetail', 64, 'admin-api/v1/website-info/get', '廉政监督详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionDelete', 64, 'admin-api/v1/website-info/delete-by-ids', '廉政监督删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('integritySupervisionPatch', 64, 'admin-api/v1/website-info/patch', '廉政监督修改', 1743408971, 0, 1, 0, 0, '');
-- 最新动态分类
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationList', 65, 'admin-api/v1/website-title-type/list', '最新动态-分类列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationCreate', 65, 'admin-api/v1/website-title-type/create', '最新动态-分类创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationDetail', 65, 'admin-api/v1/website-title-type/get', '最新动态-分类详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationDelete', 65, 'admin-api/v1/website-title-type/delete-by-ids', '最新动态-分类删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationPatch', 65, 'admin-api/v1/website-title-type/patch', '最新动态-分类修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateClassificationIsPassed', 65, 'admin-api/v1/website-title-type/activate/is-passed', '最新动态-分类上下架', 1743408971, 0, 1, 0, 0, '');
-- 最新动态 最新动态
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentList', 66, 'admin-api/v1/website-info/list', '最新动态-最新动态列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentCreate', 66, 'admin-api/v1/website-info/create', '最新动态-最新动态创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentDetail', 66, 'admin-api/v1/website-info/get', '最新动态-最新动态详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentDelete', 66, 'admin-api/v1/website-info/delete-by-ids', '最新动态-最新动态删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentPatch', 66, 'admin-api/v1/website-info/patch', '最新动态-最新动态修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('latestUpdateContentIsPassed', 66, 'admin-api/v1/website-info/activate/is-passed', '最新动态-最新动态上下架', 1743408971, 0, 1, 0, 0, '');
-- 媒体测评分类
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationList', 67, 'admin-api/v1/website-title-type/list', '媒体测评-分类列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationCreate', 67, 'admin-api/v1/website-title-type/create', '媒体测评-分类创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationDetail', 67, 'admin-api/v1/website-title-type/get', '媒体测评-分类详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationDelete', 67, 'admin-api/v1/website-title-type/delete-by-ids', '媒体测评-分类删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationSPatch', 67, 'admin-api/v1/website-title-type/patch', '媒体测评-分类修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexClassificationSIsPassed', 67, 'admin-api/v1/website-title-type/activate/is-passed', '媒体测评-分类上下架', 1743408971, 0, 1, 0, 0, '');
-- 媒体测评 媒体测评
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentList', 68, 'admin-api/v1/website-info/list', '媒体测评-媒体测评列表', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentCreate', 68, 'admin-api/v1/website-info/create', '媒体测评-媒体测评创建', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentDetail', 68, 'admin-api/v1/website-info/get', '媒体测评-媒体测评详情', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentDelete', 68, 'admin-api/v1/website-info/delete-by-ids', '媒体测评-媒体测评删除', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentPatch', 68, 'admin-api/v1/website-info/patch', '媒体测评-媒体测评修改', 1743408971, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('mediaReviewsIndexContentIsPassed', 68, 'admin-api/v1/website-info/activate/is-passed', '媒体测评-媒体测评上下架', 1743408971, 0, 1, 0, 0, '');
-- 订单列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderList', 69, 'back/order/list', '获取订单列表权限', 1645698124, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderDetail', 69, 'back/order/info', '获取订单详情权限', 1645698177, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('export1005', 69, 'admin-api/v1/order/export', '订单导出权限', 1646912353, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('export1006', 69, 'admin-api/v1/order/goods-export', '订单商品导出权限', 1646912346, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('OrderSourceList', 69, 'back/data/source-list', '订单来源权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderSyncE3+', 69, 'back/order/sync-e3', '同步E3+权限', 1645698177, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderApplyRefund', 69, 'back/order/apply-refund', '订单申请退款', 1742263277, 0, 1, 0, 0, '');
-- 退款列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('refundList', 70, 'back/order/refund-list', '获取退款列表权限', 1645698227, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderAudit', 70, 'back/order/raudit', '订单确认退款权限', 1645704644, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('refundInfo', 70, 'back/order/refund-info', '获取退款详情权限', 1645698293, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('refundExpress', 70, 'back/order/refund-express', '修改退款物流权限', 1646993766, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderRback', 70, 'back/order/rback', '退款单退单权限', 1648719483, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('expCom', 70, 'back/data/exp-com', '获取物流公司列表权限', 1650597596, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderAudit', 70, 'back/order/force-raudit', '订单强制退款权限', 1645704644, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderSendMsg', 70, 'back/order/send-msg', '短信提醒', 1742263277, 0, 1, 0, 0, '');
-- 电子保修卡记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyCardList', 71, 'back/warranty-card/list', '保修卡列表', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyCardDetail', 71, 'back/warranty-card/detail', '保修卡详情', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyCardDetail', 71, 'back/warranty-card/set-period', '设置电子保修卡时间', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyCardUpgrade', 71, 'back/warranty-card/upgrade', '保修卡升级care+', 1727575925, 0, 1, 0, 0, '');
-- 保修卡待审核记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyApplyDetailList', 72, 'back/warranty-apply-detail/list', '待审核记录列表', 1644579242, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('warrantyApplyAudit', 72, 'back/warranty-apply-detail/audit', '保修卡申请审核', 1644579242, 0, 1, 0, 0, '');
-- 电子保修卡黑名单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('blackList', 73, 'back/warranty-card/black-list', '获取电子保修卡黑名单列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('blackSave', 73, 'back/warranty-card/black-save', '电子保修卡黑名单增改', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('blackDel', 73, 'back/warranty-card/black-del', '电子保修卡黑名单删除', 1645443366, 0, 1, 0, 0, '');
-- 预约工单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingOrderList', 74, 'back/plumbing/order-list', '获取预约工单列表权限', 1657162337, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingOrderInfo', 74, 'back/plumbing/order-info', '获取预约工单详情权限', 1657162367, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingUpdateFields', 74, 'back/plumbing/update-fields', '修改预约工单状态权限', 1657162401, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('priceConfig', 74, 'back/plumbing/price-config', '获取价格配置列表权限', 1659681399, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('priceSave', 74, 'back/plumbing/price-save', '价格配置权限', 1659681429, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productList', 74, 'back/plumbing/product-list', '获取预约管理需要的产品列表权限', 1659681480, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('snConfig', 74, 'back/plumbing/sn-config', '获取SN配置列表权限', 1659681517, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('snEdit', 74, 'back/plumbing/sn-edit', 'SN配置编辑权限', 1659681547, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('snDel', 74, 'back/plumbing/sn-del', 'SN配置删除权限', 1659681569, 0, 1, 0, 0, '');
-- 安装工单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingInstallOrderList', 75, 'back/plumbing/install-order', '获取安装工单列表权限', 1660792872, 0, 1, 0, 0, '');
-- 上门服务工单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('serviceOrderList', 76, 'back/plumbing/service-order', '上门服务工单列表', 1684837093, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('serviceOrderList', 76, 'back/plumbing/product-type', '上门服务工单产品列表', 1684837093, 0, 1, 0, 0, '');
-- 查询记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bookRecordList', 77, 'back/plumbing/search-list', '查询记录列表', 1684837093, 0, 1, 0, 0, '');
-- 地址管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingAddress', 78, 'back/plumbing/address', '获取地址列表权限', 1657162166, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('saveAddress', 78, 'back/plumbing/address-save', '地址增改权限', 1657162192, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('delAddress', 78, 'back/plumbing/address-del', '地址删除权限', 1657162215, 0, 1, 0, 0, '');
-- 退款工单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingRefundInfo', 79, 'back/plumbing/refund-info', '获取预约管理退款详情权限', 1659681809, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingRefundAudit', 79, 'back/plumbing/refund-audit', '退款审核权限', 1659681849, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingRefundList', 79, 'back/plumbing/refund-list', '获取预约管理退款列表权限', 1659681915, 0, 1, 0, 0, '');
-- 评价中心
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingCommentList', 80, 'back/plumbing/comment-list', '获取评价中心列表权限', 1660793076, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('plumbingCommentInfo', 80, 'back/plumbing/comment-info', '获取评论中心详情权限', 1660793137, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tagConfig', 80, 'back/plumbing/tag-config', '获取标签配置列表权限', 1660793168, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tagEdit', 80, 'back/plumbing/tag-edit', '标签配置编辑权限', 1660793190, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tagDel', 80, 'back/plumbing/tag-del', '标签配置删除权限', 1660793212, 0, 1, 0, 0, '');
-- 用户查询
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataDiscard', 81, 'back/data/discard', '废弃指定用户接口权限', 1627031610, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataUser', 81, 'back/data/user', '查询用户信息接口权限', 1627031590, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('givePoint', 81, 'back/data/give-user-point', '发放积分权限', 1646902127, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('gietUserCoupon', 81, 'back/data/user-card', '查询用户卡券权限', 1646819787, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giveUserCard', 81, 'back/data/give-user-card', '给用户增加卡券权限', 1646824550, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('delCard', 81, 'back/data/del-card', '废弃卡券权限', 1646825187, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('resetGift', 81, 'back/data/reset-new-gift', '修改用户是否已领取新人礼包权限', 1646899252, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('viewSensitive', 81, 'back/data/view_sensitive', '查看用户敏感数据权限', 1646899252, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('UpdateMemberPoint', 81, 'back/data/update-member-point', '手动增加积分', 1698908644, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('UpdateMemberGrow', 81, 'back/data/update-member-grow', '手动增加觅享分', 1698908644, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userBindList', 81, 'back/user-bind/list', '用户绑定员工列表接口权限', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('getCardByCode', 81, 'back/market-config/detail-by-code', '通过卡券编码获取卡券详情', 1646902127, 0, 1, 0, 0, '');
-- 用户列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userList', 82, 'back/data/user-list', '获取用户列表权限', 1646635207, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userDetail', 82, 'back/data/user-info', '获取用户详情权限', 1646635231, 0, 1, 0, 0, '');
-- 来源列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('UserSourceList', 83, 'back/data/list', '获取来源列表权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('SourceSave', 83, 'back/data/source-save', '来源增改权限', 1698115812, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('SourceDel', 83, 'back/data/source-del', '来源删除权限', 1698115812, 0, 1, 0, 0, '');
-- 员工管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('employeeManagerList', 84, 'back/user-employee/list', '员工管理列表', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userEmployeeInfo', 84, 'back/user-employee/info', '员工详情', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userEmployeeAddBlacklist', 84, 'back/user-employee/add-blacklist', '加入黑名单', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userEmployeeRemoveBlacklist', 84, 'back/user-employee/remove-blacklist', '移除黑名单', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userEmployeeScoreRecord', 84, 'back/user-employee/score-record', '积分列表', 1644994229, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userEmployeeOptScore', 84, 'back/user-employee/opt-score', '积分列表', 1644994229, 0, 1, 0, 0, '');
-- 营销资源
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketList', 27, 'back/market-config/list', '获取营销列表权限', 1645415634, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketDel', 27, 'back/market-config/delete', '营销删除权限', 1645415650, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketApplyAudit', 27, 'back/market-config/apply-audit', '营销资源提交审核权限', 1645415701, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('upIsStat', 27, 'back/market-config/up-is-stat', '修改产品是否统计权限', 1662717909, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketPick', 27, 'back/market-config/pick-list', '营销配置下拉列表权限', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketDetail', 27, 'back/market-config/detail', '获取营销资源详情权限', 1645415546, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketModify', 27, 'back/market-config/modify', '营销资源新增修改权限', 1645415511, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketSubmit', 27, 'back/market-config/audit', '营销资源审核权限', 1645415574, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketGetCfg', 27, 'back/market-config/get-cfg', '获取优惠劵资源配置列表权限', 1645415607, 0, 1, 0, 0, '');
-- 礼品卡管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftCardListData', 85, 'back/gift-card/card-list', '礼品卡列表', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftCardInfo', 85, 'back/gift-card/card-info', '礼品卡详情', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftCardAudit', 85, 'back/gift-card/card-audit', '礼品卡审核', 1645080193, 0, 1, 0, 0, '');
-- 礼品卡商品
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftCardGoodsAdd', 86, 'back/gift-card-goods/add', '新建礼品卡商品', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardGoodsList', 86, 'back/gift-card/card-goods-list', '礼品卡商品列表', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardGoodsDetail', 86, 'back/gift-card/card-goods-detail', '礼品卡商品详情', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardGoodsSave', 86, 'back/gift-card/card-goods-save', '礼品卡商品添加、编辑', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardGoodsResourceUpload', 86, 'back/gift-card/card-goods-resource-upload', '礼品卡商品资源上传', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardGoodsResourceGenerate', 86, 'back/gift-card/card-goods-resource-generate', '礼品卡商品资源生成', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('GiftCardCards', 86, 'back/gift-card/gift-cards', '礼品卡下拉列表', 1645080193, 0, 1, 0, 0, '');
-- 礼品卡列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftCardResourcesList', 87, 'back/gift-card/card-resources-list', '礼品卡资源列表', 1645080193, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('giftDiscardResource', 87, 'back/gift-card/discard-resource', '礼品卡资源作废', 1645080193, 0, 1, 0, 0, '');
-- 产品注册列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productList', 88, 'back/product/product-list', '获取产品注册列表', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productSave', 88, 'back/product/product-save', '产品增改', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productDel', 88, 'back/product/product-del', '产品删除', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'uploadExcel', 88, 'back/product/entrance', '批量导入产品', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productMarketList', 88, 'back/product/market-list', '获取产品获取绑定优惠券权限', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'saveProductMarket', 88, 'back/product/market-save', '增改优惠券权限', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'delProductMarket', 88, 'back/product/market-del', '删除优惠券权限', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productConfigUp', 88, 'back/product/config-up', '修改最大领取数权限', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productConfig', 88, 'back/product/config', '获取最大领取数权限', 1745547747, 0, 1, 0, 0, '' );

-- 产品注册记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productRegs', 89, 'back/product/reg-list', '获取产品注册记录', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productRegEdit', 89, 'back/product/reg-edit', '产品注册修改sn码权限', 1745547747, 0, 1, 0, 0, '' );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ) VALUES ( 'productCrmSync', 89, 'back/product/crm-sync', '产品注册修改sn后同步crm权限', 1745547747, 0, 1, 0, 0, '' );

-- 产品注册配件管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('registerPartsList', 90, 'back/parts-sales/list', '产品注册配件管理列表', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('machineList', 90, 'back/parts-sales/machine', '产品注册主机列表', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('registerPartsModify', 90, 'back/parts-sales/modify', '产品注册添加编辑', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('registerPartsDetail', 90, 'back/parts-sales/detail', '产品注册详情', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('registerPartsDelete', 90, 'back/parts-sales/delete', '产品注册删除', 1678245053, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('registerPartsList', 90, 'back/parts-sales/parts', '产品注册已选主机的配件', 1678245053, 0, 1, 0, 0, '');

-- 产品注册异常
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('productExceptionList', 91, 'back/product/exception-list', '产品注册异常列表', 1678245053, 0, 1, 0, 0, '');

-- 产品注册白名单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('whiteList', 92, 'back/product/white-list', '获取产品注册白名单列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('whiteSave', 92, 'back/product/white-save', '产品注册白名单增改', 1645443366, 0, 1, 0, 0, '');

-- 产品注册说明
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('docSave', 93, 'back/wiki/doc-save', '新增/编辑文档权限', 1661511202, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('docList', 93, 'back/wiki/doc-list', '获取文档列表权限', 1661511234, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('docInfo', 93, 'back/wiki/doc-info', '获取文档详情权限', 1661511261, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('docDel', 93, 'back/wiki/doc-del', '文档删除权限', 1661511288, 0, 1, 0, 0, '');


-- 广告位管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerList', 30, 'admin-api/v1/banner/search', '列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerCreate', 30, 'admin-api/v1/banner/create', '新增', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerDelete', 30, 'admin-api/v1/banner/delete', '删除', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerDetail', 30, 'admin-api/v1/banner/detail', '详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerUpdate', 30, 'admin-api/v1/banner/update', '修改', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerPatch', 30, 'admin-api/v1/banner/patch', '部分更新', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerRelease', 30, 'back/banner/release', '获取投放位置', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('bannerVersion', 30, 'back/banner/banner-version', '获取banner版本', 1645443366, 0, 1, 0, 0, '');

-- 优惠券活动配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityConfigList', 31, 'back/activity-config/list', '优惠券活动配置列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityConfigModify', 31, 'back/activity-config/modify', '优惠券活动配置新增更新', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityConfigDetail', 31, 'back/activity-config/detail', '优惠券活动配置详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityConfigDetail', 31, 'back/activity-config/delete', '优惠券活动配置删除', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityConfigCensus', 31, 'back/activity-config/census', '获取推荐有礼统计列表权限', 1656504468, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityModifyTmp', 31, 'back/activity-config/modify-tmp', '活动配置新增/修改权限临时', 1645098495, 0, 1, 0, 0, '');

-- 优惠券配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketDefineList', 94, 'back/market-define/list', '优惠券配置', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketDefineAdd', 94, 'back/market-define/save', '优惠券配置保存', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('marketDefineDel', 94, 'back/market-define/del', '优惠券配置删除', 1645443366, 0, 1, 0, 0, '');

-- 支付活动配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityList', 32, 'back/payment-activity/list', '支付活动列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityDetail', 32, 'back/payment-activity/detail', '支付活动详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivitySave', 32, 'back/payment-activity/save', '保存支付活动', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityDelete', 32, 'back/payment-activity/delete', '删除支付活动', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityGoodsSearch', 32, 'back/payment-activity/goods-search', '搜索商品', 1645443366, 0, 1, 0, 0, '');

-- 支付活动配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityList', 32, 'back/payment-activity/list', '支付活动列表', 1731476917, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityDetail', 32, 'back/payment-activity/detail', '支付活动详情', 1731476917, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivitySave', 32, 'back/payment-activity/save', '保存支付活动', 1731476917, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityDelete', 32, 'back/payment-activity/delete', '删除支付活动', 1731476917, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('payActivityGoodsSearch', 32, 'back/payment-activity/goods-search', '搜索商品', 1731476917, 0, 1, 0, 0, '');

-- 活动氛围配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityAtmosphereList', 33, 'back/activity-atmosphere/list', '列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityAtmosphereDetail', 33, 'back/activity-atmosphere/detail', '详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityAtmosphereStore', 33, 'back/activity-atmosphere/store', '新增、编辑', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('activityAtmosphereDel', 33, 'back/activity-atmosphere/del', '删除', 1645443366, 0, 1, 0, 0, '');

-- 活动模版配置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityModuleResList', 34, 'back/member-activity/module-res-list', '获取模块资源列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityList', 34, 'back/member-activity/list', '获取主题活动列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityDetail', 34, 'back/member-activity/detail', '获取主题活动详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityTaskList', 34, 'back/member-activity/task-list', '获取任务列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityCreate', 34, 'back/member-activity/create', '添加主题活动', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityUpdate', 34, 'back/member-activity/update', '更新主题活动', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityDelete', 34, 'back/member-activity/delete', '删除主题活动', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityCreateModRel', 34, 'back/member-activity/create-module-relation', '新增活动模块', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityUpdateModRel', 34, 'back/member-activity/update-module-relation', '更新活动模块', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityDeleteModRel', 34, 'back/member-activity/delete-module-relation', '删除活动模块', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('MemberActivityDeleteModRel', 34, 'back/member-activity/delete-module-relation', '删除活动模块', 1645443366, 0, 1, 0, 0, '');

-- 活动列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivityWaresList', 95, 'back/activity/list', '活动列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivityWaresSave', 95, 'back/activity/modify', '活动创建/编辑', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivityWaresDetail', 95, 'back/activity/detail', '活动详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivityWaresStatus', 95, 'back/activity/status', '活动上下架', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivityWaresDel', 95, 'back/activity/del', '活动删除', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('ActivitySurveyKeyList', 95, 'back/activity/survey-key', '活动问卷列表', 1711934843, 0, 1, 0, 0, '');

-- 商品管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('TryGoodsList', 96, 'back/try-goods/list', '商品列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('WaresGoodsSale', 96, 'back/try-goods/sale', '商品上下架', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('WaresGoodsInfo', 96, 'back/try-goods/info', '商品详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('WaresGoodsDel', 96, 'back/try-goods/del', '商品删除', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('WaresGoodsSave', 96, 'back/try-goods/save', '商品保存', 1645443366, 0, 1, 0, 0, '');

-- 订单管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderList', 97, 'back/try-orders/list', '订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryRefundList', 97, 'back/try-orders/refund-list', '退款订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryExceptionList', 97, 'back/try-orders/exception-list', '异常订单列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderInfo', 97, 'back/try-orders/info', '订单详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderRefundInfo', 97, 'back/try-orders/refund-info', '退款订单详情', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderBegin', 97, 'back/try-orders/exception-begin', '订单开始试用', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderRaudit', 97, 'back/try-orders/exception-raudit', '订单完结/强制扣款', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryOrderRefund', 97, 'back/try-orders/exception-refund', '客服申请退款', 1711934843, 0, 1, 0, 0, '');

-- 用户管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryUserList', 98, 'back/try-user/list', '用户列表', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryUserInfo', 98, 'back/try-user/info', '用户详情', 1645443366, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryUserShield', 98, 'back/try-user/info', '用户-拉黑/移除黑名单', 1645443366, 0, 1, 0, 0, '');

-- 人工审核
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryAuditList', 99, 'back/data/survey-audit-list', '人工审核列表', 1711934843, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryAudit', 99, 'back/data/survey-audit', '审核', 1711934843, 0, 1, 0, 0, '');

-- 用户链路
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tryUserPathList', 100, 'back/try-user/user-path-list', '用户链路列表', 1711934843, 0, 1, 0, 0, '');
-- 支付宝口令维护
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('zfbPasswordInfo', 101, 'back/data/zfb-password-info', '支付宝口令维护详情', 1716187970, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('zfbPasswordSave', 101, 'back/data/zfb-password', '支付宝口令维护保存', 1716187970, 0, 1, 0, 0, '');

-- 活动列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInList', 102, 'back/trade-in-activity/list', '活动列表', 1735351212, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInDetail', 102, 'back/trade-in-activity/detail', '活动详情', 1735351212, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInStore', 102, 'back/trade-in-activity/store', '活动新增、编辑', 1735351212, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInDel', 102, 'back/trade-in-activity/del', '活动删除', 1735351212, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInStatus', 102, 'back/trade-in-activity/status', '活动上下架', 1735351212, 0, 1, 0, 0, '');

-- 商品管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInGoodList', 103, 'back/trade-in-goods/list', '商品列表', 1735351251, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInGoodDetail', 103, 'back/trade-in-goods/detail', '商品详情', 1735351251, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInGoodStore', 103, 'back/trade-in-goods/store', '商品详情', 1735351251, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInGoodDel', 103, 'back/trade-in-goods/del', '商品删除', 1735351251, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('tradeInGoodRelate', 103, 'back/trade-in-goods/relate-activity', '商品关联活动', 1735351251, 0, 1, 0, 0, '');

-- 拼团活动
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseActivityList', 104, 'back/group-purchase/activity-list', '拼团活动列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseActivitySave', 104, 'back/group-purchase/activity-save', '拼团活动新增/修改', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseActivityDelete', 104, 'back/group-purchase/activity-delete', '拼团活动删除', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseActivityDetail', 104, 'back/group-purchase/activity-detail', '拼团活动详情', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseGoodSearch', 104, 'back/group-purchase/goods-search', '拼团活动商品搜索', 1727575925, 0, 1, 0, 0, '');

-- 拼团记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseList', 105, 'back/group-purchase/list', '拼团列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseDetail', 105, 'back/group-purchase/detail', '拼团详情', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseRecordList', 105, 'back/group-purchase/group-record-list', '拼团记录列表', 1727575925, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('groupPurchaseRecordDetail', 105, 'back/group-purchase/group-record-detail', '拼团记录详情', 1727575925, 0, 1, 0, 0, '');

-- 短信发送
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('smsTmplList', 106, 'back/sms/sms-tmpl-list', '短信模板列表', 1685418142, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('smsTmpl', 106, 'back/sms/sms-tmpl', '短信模板', 1685418142, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('sendSms', 106, 'back/sms/send-sms', '发送短信', 1685418142, 0, 1, 0, 0, '');

-- 会员短信查询
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('smsList', 107, 'back/sms/sms-list', '短信发送列表', 1685418142, 0, 1, 0, 0, '');

-- 批量发放优惠券
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('releaseCoupon', 108, 'back/discount/batch-give-user-card', '批量发放优惠券', 1665653983, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('viewCoupon', 108, 'back/discount/get-user-card-log', '查询个人发放/废除记录', 1665653983, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('downloadCouponTemplate', 108, 'back/discount/user-card-template', '下载导入模板', 1665653983, 0, 1, 0, 0, '');

-- 批量废除优惠券
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('cancelCoupon', 109, 'back/discount/batch-del-user-card', '批量废除优惠券', 1665653983, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('viewDelCoupon', 109, 'back/discount/del-user-card-log', '查询废除记录', 1665653983, 0, 1, 0, 0, '');

-- 积分规则设置
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointsRule', 110, 'back/point/rule', '获取积分规则', 1645511047, 0, 1, 0, 0, '');
-- 积分记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointsList', 111, 'back/point/list', '积分记录列表', 1645013991, 0, 1, 0, 0, '');
-- 积分/觅享分批量充值
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointPushGivePoints', 112, 'back/point-push/give-points', '批量上传', 1722247244, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointPushQueryPoints', 112, 'back/point-push/query-upload', '积分/觅享分批量上传查询记录', 1722242174, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointPushPushPoints', 112, 'back/point-push/push-points', '积分批量发放', 1645013991, 0, 1, 0, 0, '');
-- 积分/觅享分批量充值记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('pointPushList', 113, 'back/point-push/list', '上传记录', 1722247253, 0, 1, 0, 0, '');

-- 内容列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('wikiWlist', 114, 'admin-api/v1/content/search', '获取内容列表权限', 1653621540, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('wikiWdel', 114, 'admin-api/v1/content/delete', '内容删除权限', 1653621568, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('export1012', 114, 'admin-api/v1/content/export', '导出内容列表权限', 1656915649, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('contentStatistics', 114, 'admin-api/v1/content/statistics', '内容统计权限', 1731293566, 0, 1, 0, 0, '');
-- 内容角色管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('roleList', 115, 'admin-api/v1/tag/list', '内容角色列表权限', 1685497391, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('roleDel', 115, 'admin-api/v1/tag/delete', '内容角色删除权限', 1685497391, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('roleSave', 115, 'admin-api/v1/tag/create', '内容角色添加权限', 1685497391, 0, 1, 0, 0, '');
-- 内容举报管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('contentReportList', 116, 'admin-api/v1/report/content-list', '内容举报列表权限', 1731293566, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('contentReportAudit', 116, 'admin-api/v1/report/content-audit', '内容举报审核权限', 1731293566, 0, 1, 0, 0, '');
-- 评论举报管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewReportList', 117, 'admin-api/v1/report/review-list', '评论举报审核权限', 1731293566, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('reviewReportAudit', 117, 'admin-api/v1/report/review-audit', '评论举报审核权限', 1731293566, 0, 1, 0, 0, '');
-- 用户作品管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userContentDetail', 118, 'admin-api/v1/user-content/detail', '用户作品详情', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userContentAuditHistory', 118, 'admin-api/v1/user-content/audit-history', '用户作品审核历史', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userContentAudit', 118, 'admin-api/v1/user-content/audit', '用户作品审核', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('getAuditMode', 118, 'admin-api/v1/content/audit-mode', '获取文章审核模式', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('setAuditMode', 118, 'admin-api/v1/content/set-audit-mode', '设置文章审核模式', 1736250622, 0, 1, 0, 0, '');

-- 用户举报管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userReportDetail', 42, 'admin-api/v1/user-report/detail', '用户举报详情', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userReportHistory', 42, 'admin-api/v1/user-report/history', '用户举报历史', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userReportAudit', 42, 'admin-api/v1/user-report/audit', '用户举报审核', 1736250622, 0, 1, 0, 0, '');

-- 门店列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('shopList', 119, 'back/retailers/list', '获取周边门店列表权限', 1647333446, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'retailersStatus', 119, 'back/retailers/status', '门店上下架权限', 1647333440, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'RetailersBatchOpenActivity', 119, 'back/retailers/batch-open', '批量开启门店活动', 1744881742, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'RetailersActivityInfo', 119, 'back/retailers/activity-info', '门店活动详情', 1744881742, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'RetailersPatchActivity', 119, 'back/retailers/patch-activity', '门店活动详情', 1744881742, 0, 1, 0, 0, '');


-- 导购列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('guideList', 120, 'back/data/guide-list', '获取导购订单列表权限', 1646192399, 0, 1, 0, 0, '');

-- 导购记录
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('orderSlist', 121, 'back/order/slist', '获取导购记录列表权限', 1646192452, 0, 1, 0, 0, '');

-- 导出列表
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('exportLogList', 45, 'back/async-export/log-list', '导出列表', 1689696000, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('exportOperation', 45, 'back/async-export/export', '导出操作', 1644579242, 0, 1, 0, 0, '');

-- 小程序隐私政策
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyList', 122, 'admin-api/v1/privacy/list', '隐私列表', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCreate', 122, 'admin-api/v1/privacy/create', '隐私创建', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCreateWithDetail', 122, 'admin-api/v1/privacy/create-with-detail', '隐私及详情创建', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCreateWithDetail', 122, 'admin-api/v1/privacy/create-with-detail', '隐私及详情创建', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyDetailBatch', 122, 'admin-api/v1/privacy-detail/batch-save', '隐私详情批量保存', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyReview', 122, 'admin-api/v1/privacy/review', '隐私审核', 1736250622, 0, 1, 0, 0, '');

-- 隐私类型管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCategoryCreate', 123, 'admin-api/v1/privacy-category/create', '隐私类别创建', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCategoryDelete', 123, 'admin-api/v1/privacy-category/delete', '隐私类别删除', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCategoryUpdate', 123, 'admin-api/v1/privacy-category/update', '隐私类别修改', 1736250622, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('privacyCategoryList', 123, 'admin-api/v1/privacy-category/list', '隐私类别列表', 1736250622, 0, 1, 0, 0, '');

-- 管理员管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userList', 124, 'rbac/manage/user', '获取管理列表接口', 1623067070, 0, 1, 0, 0, 'manage/lead-list,manage/roles-auth');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userDetail', 124, 'rbac/manage/user-detail', '获取管理员详情接口', 1623067064, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('modifyUser', 124, 'rbac/manage/modify-users', '添加修改管理员接口', 1623067005, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('userDelete', 124, 'rbac/manage/user-delete', '删除管理员接口', 1623067019, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('rolesAuthList', 124, 'rbac/manage/roles-auth', '获取角色权限列表接口', 1623066487, 0, 1, 0, 0, '');


-- 角色管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('rolesList', 125, 'rbac/manage/roles', '获取角色列表接口', 1623124135, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('modifyRoles', 125, 'rbac/manage/modify-roles', '修改添加角色接口', 0, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('rolesDetail', 125, 'rbac/manage/roles-detail', '获取角色详情接口', 1623067152, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('roleDisable', 125, 'rbac/manage/role-disable', '角色禁用启用接口', 1625802411, 0, 1, 0, 0, '');


-- 路由管理
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('uriList', 126, 'rbac/manage/uri-list', '获取路由列表接口', 1623067124, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('modifyUri', 126, 'rbac/manage/modify-uri', '添加修改路由接口', 1623124209, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('uriDetail', 126, 'rbac/manage/uri-detail', '路由详情接口', 0, 0, 1, 0, 0, '');
-- 系统日志
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('modelLogs', 127, 'rbac/manage/logs-module', '日志模块条件接口', 1623122888, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('logsList', 127, 'rbac/manage/logs', '获取日志列表接口', 1623122885, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('loadLogsUrl', 127, 'rbac/manage/logs-export', '导出日志接口', 1623122882, 0, 1, 0, 0, '');
-- 接口日志
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('logsList', 128, 'back/data/post', '获取请求记录列表接口', 1624437329, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('againExec', 128, 'back/data/again-exec', '列表接口再次请求接口', 1624437316, 0, 1, 0, 0, '');
-- 接口并发监控
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('requsetTime', 129, 'back/data/api-request', '接口并发监控接口', 1623123895, 0, 1, 0, 0, '');
-- 代码执行
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('codeExec', 130, 'back/data/code-exec', '代码执行接口', 1623123864, 0, 1, 0, 0, '');
-- 服务器负载
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataServerList', 131, 'back/data/server-list', '获取服务器列表接口', 1624438224, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataLoadAvg', 131, 'back/data/load-avg', '服务器负载接口', 1624438170, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataRedisInfo', 131, 'back/data/redis-info', 'redis信息接口', 1624438203, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataBranch', 131, 'back/data/branch', '查询分支信息接口', 1624438528, 0, 1, 0, 0, '');
-- QPS监控
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('dataQps', 132, 'back/data/qps', 'qps数据接口权限', 1628242380, 0, 1, 0, 0, '');
-- CRM错误日志
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('crmLogsList', 133, 'back/data/crm-log', '获取crm错误记录列表', 1653565050, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` (`name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ('retryCrm', 133, 'back/data/crm-retry', 'crm错误重试权限', 1653618727, 0, 1, 0, 0, '');



-- 额外新增菜单
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ,`is_hidden` ) VALUES  ( 'addGoodsManager', 12, '', '新增/编辑商品', 1745547747, 0, 1, 1, 0, '',1 );
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri` ,`is_hidden`) VALUES  ( 'addPointGoodsManager', 15, '', '新增/编辑积分商品', 1745547747, 0, 1, 1, 0, '' ,1);
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`  ,`is_hidden`) VALUES  ( 'addCouponManager', 5, '', '新增/编辑优惠券', 1745547747, 0, 1, 1, 0, '',1 );

-- 数据字典
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dataDictionary', 11, '', '数据字典', 1745547747, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictTypeList', 560, 'back/dict-type/index', '获取字典列表', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'addDictType', 560, 'back/dict-type/create', '新增字典', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'updateDictType', 560, 'back/dict-type/update', '更新字典', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'deleteDictType', 560, 'back/dict-type/delete', '删除字典', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictTypeChangeStatus', 560, 'back/dict-type/change-status', '更新字典状态', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictDataIndex', 560, 'back/dict-data/index', '获取字典数据列表', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictDataList', 560, 'back/dict-data/list', '获取字典数据列表-无分页', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'addDictData', 560, 'back/dict-data/create', '新增字典数据', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'updateDictData', 560, 'back/dict-data/update', '更新字典数据', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'deleteDictData', 560, 'back/dict-data/delete', '删除字典数据', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictDataChangeStatus', 560, 'back/dict-data/change-status', '更新字典数据状态', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictDataClearCache', 560, 'back/dict-data/clear-cache', '清除字典缓存', 1745547747, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri_copy1` ( `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES ( 'dictDataChangeSort', 560, 'back/dict-data/change-sort', '更新字典数据排序', 1745547747, 0, 1, 0, 0, '');