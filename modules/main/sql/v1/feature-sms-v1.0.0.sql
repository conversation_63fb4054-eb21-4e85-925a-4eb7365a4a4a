-- 短信模板表
CREATE TABLE `db_dreame`.`t_sms_tmpl` (
                                          `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                          `code` VARCHAR ( 100 ) NOT NULL DEFAULT '' COMMENT '模板ID',
                                          `name` VARCHAR ( 100 ) NOT NULL DEFAULT '' COMMENT '模板名称',
                                          `content` VARCHAR ( 1024 ) NOT NULL DEFAULT '' COMMENT '模板内容',
                                          `tmpl_url` VARCHAR ( 255 ) NOT NULL DEFAULT '' COMMENT '导入模板地址',
                                          `is_del` TINYINT NOT NULL DEFAULT '0' COMMENT '删除状态 0未删除 1已删除',
                                          `ctime` INT NOT NULL COMMENT '新增时间',
                                          `utime` INT NOT NULL COMMENT '修改时间',
                                          `dtime` INT NOT NULL DEFAULT '0' COMMENT '删除时间',
                                          PRIMARY KEY ( `id` ) USING BTREE,
                                          KEY `idx_code` ( `code` ),
                                          KEY `idx_name` ( `name` )
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '短信模板表';

-- 短信记录表
CREATE TABLE `db_dreame_log`.`t_sms_send_record` (
                                                     `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
                                                     `sms_code` VARCHAR ( 100 ) NOT NULL DEFAULT '' COMMENT '短信模板code',
                                                     `phone` VARCHAR ( 11 ) NOT NULL DEFAULT '' COMMENT '手机号',
                                                     `content` VARCHAR ( 1024 ) NOT NULL DEFAULT '' COMMENT '短信内容',
                                                     `send_time` INT NOT NULL COMMENT '发送时间',
                                                     `ctime` INT NOT NULL COMMENT '新增时间',
                                                     `utime` INT NOT NULL COMMENT '修改时间',
                                                     PRIMARY KEY ( `id` ) USING BTREE,
                                                     KEY `idx_sms_code` ( `sms_code` ),
                                                     KEY `idx_phone` ( `phone` )
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '短信记录表';

-- 初始化权限
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (303, 'smsManager', 0, '', '短信管理', 1685418142, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (304, 'smsSend', 303, 'sms/sms-send', '短信发送', 1685418142, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (305, 'smsSendList', 303, 'sms/sms-send-list', '会员短信查询', 1685418142, 0, 1, 1, 1, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (306, 'smsTmplList', 304, 'back/sms/sms-tmpl-list', '短信模板列表', 1685418142, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (307, 'smsTmpl', 304, 'back/sms/sms-tmpl', '短信模板', 1685418142, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (308, 'sendSms', 304, 'back/sms/send-sms', '发送短信', 1685418142, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (309, 'smsList', 305, 'back/sms/sms-list', '短信发送列表', 1685418142, 0, 1, 0, 0, '');