CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`member_activity` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '活动ID',
    `title` varchar(200) NOT NULL DEFAULT '' COMMENT '活动标题',
    `description` varchar(200) NOT NULL DEFAULT '' COMMENT '活动副标题',
    `rule` text COMMENT '活动规则',
    `cycle_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'once' COMMENT '活动周期类型 once=单次',
    `start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '活动开始时间',
    `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '活动结束时间',
    `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_starttime_endtime` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='活动表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`member_activity_module_resource` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '模块资源ID',
    `module_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块资源名称',
    `module_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块资源编号',
    `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uni_module_code` (`module_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='模块枚举表';

INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (1, '大标题模块', 'BIG_TITLE');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (2, '积分打卡', 'CHECKIN');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (3, '购物返积分', 'RETURN_POINTS');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (4, '广告位', 'AD_POSITION');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (5, '抽奖活动', 'DRAW');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (6, '新人入会福利', 'NEW_PERSON');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (7, '机器推荐', 'RECOMMEND_GOODS');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (8, '优惠券', 'COUPON');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (9, '配件专区', 'PART_GOODS');
INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`) VALUES (10, '积分兑换', 'POINTS_EXCHANGE');

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`member_activity_module_relation` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `activity_id` int unsigned NOT NULL DEFAULT '0' COMMENT '活动ID',
    `module_res_id` int NOT NULL COMMENT '模块资源ID',
    `title` varchar(200) NOT NULL DEFAULT '' COMMENT '模块名称',
    `summary` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '模块文案',
    `start_time` int unsigned NOT NULL DEFAULT '0' COMMENT '模块显示开始时间',
    `end_time` int unsigned NOT NULL DEFAULT '0' COMMENT '模块显示结束时间',
    `extra` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '模块额外数据',
    `sort` int unsigned NOT NULL DEFAULT '0' COMMENT '排序 数字越大越靠前',
    `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_acid_moduleid` (`activity_id`,`module_res_id`),
    KEY `idx_moduleid` (`module_res_id`),
    KEY `idx_startime_endtime` (`start_time`,`end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='模块活动关联表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`member_activity_module_goods` (
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `module_relation_id` int unsigned NOT NULL DEFAULT '0' COMMENT '活动模块关联ID',
    `module_type` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '模块类型:1-机器推荐 2-配件专区 3-积分兑换',
    `category_id` int unsigned NOT NULL DEFAULT '0' COMMENT '品类ID',
    `category_name` varchar(50) NOT NULL DEFAULT '' COMMENT '品类名称',
    `goods_id` int unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
    `goods_name` varchar(200) NOT NULL DEFAULT '' COMMENT '商品名称',
    `show_name` varchar(200) NOT NULL DEFAULT '' COMMENT '外显名称(主要用于配件专区)',
    `goods_sku` varchar(100) NOT NULL DEFAULT '' COMMENT '商品SKU',
    `goods_image` varchar(1000) NOT NULL DEFAULT '' COMMENT '商品图片',
    `sale_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '销售价格',
    `free_periods` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '免息期数(机器推荐)',
    `trade_subsidy` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '以旧换新补贴金额(机器推荐)',
    `sale_points` int unsigned NOT NULL DEFAULT '0' COMMENT '兑换积分(积分兑换)',
    `sort_order` int unsigned NOT NULL DEFAULT '0' COMMENT '排序权重',
    `status` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '状态:0-禁用 1-启用',
    `extra` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '扩展字段',
    `create_time` int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `update_time` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `delete_time` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY `idx_relation_module` (`module_relation_id`,`module_type`),
    KEY `idx_cate_id` (`category_id`),
    KEY `idx_goods_id` (`goods_id`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='活动商品表(整合机器推荐、配件专区、积分兑换)';

-- 抽奖活动，新增module_relation_id字段
ALTER TABLE `db_dreame_goods`.`t_draw_activity`
    ADD COLUMN `module_relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动模块ID' AFTER `id`;

-- 抽奖奖品，新增activity_id，module_relation_id字段
ALTER TABLE `db_dreame_goods`.`t_draw_prize`
    ADD COLUMN `activity_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '抽奖活动ID' AFTER `id`,
    ADD COLUMN `module_relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID' AFTER `activity_id`;

-- 抽奖活动奖品，新增module_relation_id字段
ALTER TABLE `db_dreame_goods`.`t_draw_activity_prize`
    ADD COLUMN `module_relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID' AFTER `id`;

-- 抽奖活动任务，新增module_relation_id，extra字段
ALTER TABLE `db_dreame_goods`.`t_draw_activity_task`
    ADD COLUMN `module_relation_id` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID' AFTER `id`,
    ADD COLUMN `extra` text NULL COMMENT '任务扩展' AFTER `draw_times`;

-- 删除唯一索引限制
ALTER TABLE `db_dreame_goods`.`t_draw_activity`
    DROP INDEX `idx_name`,
    ADD INDEX `idx_name`(`name` ASC) USING BTREE;

-- 加索引
ALTER TABLE `db_dreame_goods`.`t_draw_activity`
    ADD INDEX `idx_module_relation_id`(`module_relation_id`);
ALTER TABLE `db_dreame_goods`.`t_draw_activity_prize`
    ADD INDEX `idx_module_relation_id`(`module_relation_id`);
ALTER TABLE `db_dreame_goods`.`t_draw_activity_task`
    ADD INDEX `idx_module_relation_id`(`module_relation_id`);
ALTER TABLE `db_dreame_goods`.`t_draw_prize`
    ADD INDEX `idx_activity_id_relation_id`(`activity_id`, `module_relation_id`),
    ADD INDEX `idx_module_relation_id`(`module_relation_id`);

-- 后台路由
-- 菜单
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (962, 'Theme', 0, '', '活动模板管理', 1744787444, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (963, 'ThemeActivity', 962, 'theme-activity/theme', '主题活动管理', 1744784788, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (964, 'ThemeActivityResource', 962, 'theme-activity/resource', '模块资源管理', 1744784779, 0, 1, 1, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (965, 'ThemeActivityEdit', 962, 'theme-activity/theme-edit', '主题活动编辑', 1744786650, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (966, 'ThemeActivityResourceView', 962, 'theme-activity/resource-view', '模块资源查看', 1744786644, 0, 1, 0, 0, '');

-- 接口
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (973, 'AdminMemberActivityList', 963, 'back/member-activity/list', '获取主题活动列表', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (975, 'AdminMemberActivityDetail', 963, 'back/member-activity/detail', '获取主题活动详情', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (976, 'AdminMemberActivityCreate', 963, 'back/member-activity/create', '添加主题活动', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (977, 'AdminMemberActivityUpdate', 963, 'back/member-activity/update', '更新主题活动', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (978, 'AdminMemberActivityDelete', 963, 'back/member-activity/delete', '删除主题活动', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (979, 'AdminMemberActivityResList', 964, 'back/member-activity/module-res-list', '获取模块资源列表', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (980, 'AdminMemberActivityDrawTaskList', 963, 'back/member-activity/task-list', '获取抽奖任务列表', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (981, 'AdminMemberActivityCreateModuleRelation', 963, 'back/member-activity/create-module-relation', '新增活动模块', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (982, 'AdminMemberActivityUpdateModuleRelation', 963, 'back/member-activity/update-module-relation', '更新活动模块', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (983, 'AdminMemberActivityDeleteModuleRelation', 963, 'back/member-activity/delete-module-relation', '删除活动模块', 1745460370, 0, 1, 0, 0, '');
INSERT INTO `db_dreame_admin`.`t_uri` (`id`, `name`, `p_id`, `uri`, `uri_name`, `time`, `is_del`, `is_check`, `is_menu`, `rank`, `ext_uri`) VALUES (984, 'AdminMemberActivityChangeGoodsStatus', 963, 'back/member-activity/change-module-goods-status', '更新活动商品状态', 1745460370, 0, 1, 0, 0, '');
