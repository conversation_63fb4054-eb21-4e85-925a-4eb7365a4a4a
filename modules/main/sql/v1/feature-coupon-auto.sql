-- Description: 优惠券自动发放 grant_type=7优惠券自动发放

-- 创建优惠券自动发放配置表添加附加属性字段
alter table db_dreame.t_activity_config
    add attributes VARCHAR(255) not null default '' COMMENT '附加属性';

-- 创建优惠券自动发放配置表 t_ac_type7
create table db_dreame.t_ac_type7
(
    id          int unsigned auto_increment
        primary key,
    ac_id       int default 0 not null comment '活动ID',
    sku         varchar(50)  default ''  not null comment '商品sku',
    start_time  int unsigned default 0 not null comment '开始时间',
    end_time    int unsigned default 0 not null comment '结束时间',
    is_delete   tinyint unsigned default '0'  not null comment '是否删除 0 未删除 1删除',
    create_time int unsigned default 0 not null comment '创建时间',
    update_time int unsigned default 0 not null comment '更新时间',
    INDEX idx_ac_id (ac_id),
    INDEX idx_sku (sku)
) comment '自动发放配置表7' ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;