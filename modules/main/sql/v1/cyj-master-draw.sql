INSERT INTO `db_dreame_goods`.`member_activity_module_resource` (`id`, `module_name`, `module_code`, `create_time`)
VALUES (11, '现金红包', 'RED_ENVELOPE', 1750665929);



CREATE TABLE `db_dreame_goods`.`member_activity_envelope_record`
(
    `id`                   int          NOT NULL AUTO_INCREMENT,
    `activity_relation_id` int          NOT NULL DEFAULT '0' COMMENT '活动关联id',
    `user_id`              int          NOT NULL DEFAULT '0' COMMENT '用户id',
    `user_phone`           varchar(20)  NOT NULL DEFAULT '' COMMENT '用户手机号',
    `prize_name`           varchar(50)  NOT NULL DEFAULT '' COMMENT '奖品名称',
    `prize_value`          varchar(255) NOT NULL DEFAULT '' COMMENT '奖品的值',
    `prize_num`            int unsigned NOT NULL DEFAULT '1' COMMENT '奖品数量',
    `collect_time`         bigint       NOT NULL DEFAULT '0' COMMENT '领取时间',
    `ctime`                bigint       NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`                bigint       NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY                    `idx_user_id` (`user_id`) USING BTREE,
    KEY                    `idx_activity_relation_id` (`activity_relation_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4  ROW_FORMAT=DYNAMIC COMMENT='红包领取记录';