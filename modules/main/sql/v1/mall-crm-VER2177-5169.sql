CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_member_err_log`
(
    `id`        INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `function`  VARCHAR(50) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '方法名',
    `params`    TEXT CHARACTER SET utf8mb4 NOT NULL COMMENT '调用参数',
    `err_count` TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试失败次数，默认次数0',
    `resp_msg`  TEXT CHARACTER SET utf8mb4 NOT NULL COMMENT '响应信息',
    `status`    TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '重试状态 0=待处理，1=处理失败，2=处理成功，默认0',
    `ctime`     INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime`     INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COMMENT = '会员推送失败日志' ROW_FORMAT = COMPACT;


ALTER TABLE `db_dreame_goods`.`t_uo_0` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_1` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_2` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_3` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_4` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_5` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_6` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_7` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_8` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;
ALTER TABLE `db_dreame_goods`.`t_uo_9` ADD COLUMN `syn_iot` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已经同步到iot 0=否，1=是，默认0' AFTER `syn_crm`;