CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_adv_err_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `function` varchar(50) NOT NULL COMMENT '方法名',
    `rags` text NOT NULL COMMENT '参数集合',
    `nums` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败次数，默认为1',
    `msg` text NOT NULL COMMENT '返回信息',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败=1 处理成功后改成2 默认1',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `utime` int(10) NOT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='广告推送错误日志';




