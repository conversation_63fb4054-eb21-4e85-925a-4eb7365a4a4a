CREATE TABLE if NOT EXISTS `db_dreame_log`.`t_share_coupon_draw_log` (
    `id` int(10)  unsigned NOT NULL AUTO_INCREMENT COMMENT '主键自增',
    `user_id` int(10) NOT NULL DEFAULT '0' COMMENT '领取用户ID',
    `share_id` int(10) NOT NULL DEFAULT '0' COMMENT '分享用户ID',
    `ac_id` int(10) NOT NULL DEFAULT '0' COMMENT '活动ID',
    `md5phone` varchar(50) NOT NULL DEFAULT '' COMMENT '手机号码(md5)',
    `share_time` int unsigned NOT NULL DEFAULT '0' COMMENT '分享时间',
    `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
    `ctime` int unsigned NOT NULL DEFAULT '0' COMMENT '创建事件',
    `utime` int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime` int unsigned NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY  `idx_user_id` (`user_id`) USING BTREE,
    KEY  `idx_share_id` (`share_id`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='分享券领取记录表';


