-- 以旧换新活动表
CREATE TABLE `t_trade_in_activity`
(
    `id`         int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name`       varchar(50)  NOT NULL DEFAULT '' COMMENT '活动名称',
    `picture`    varchar(255) NOT NULL DEFAULT '' COMMENT '活动图片',
    `products`   text         NOT NULL COMMENT '活动产品',
    `start_time` bigint       NOT NULL COMMENT '活动开始时间',
    `end_time`   bigint       NOT NULL COMMENT '活动结束时间',
    `extras`     text         NOT NULL COMMENT '活动信息',
    `status`     tinyint      NOT NULL DEFAULT '1' COMMENT '活动状态 1开启 2停用',
    `is_del`     tinyint unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
    `ctime`      int          NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`      int          NOT NULL DEFAULT '0' COMMENT '更新时间',
    `dtime`      int          NOT NULL DEFAULT '0' COMMENT '删除时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_name` (`name`) USING BTREE
)AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新活动表';

-- 以旧换新订单表
CREATE TABLE `t_trade_in_order`
(
    `id`                int unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `order_no`          varchar(50) NOT NULL DEFAULT '' COMMENT '普通订单号',
    `trade_in_order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '以旧换新订单号',
    `product`           varchar(1000) NOT NULL DEFAULT '' COMMENT '商品',
    `status`            tinyint unsigned NOT NULL DEFAULT '0' COMMENT '状态：0等待确认 5等待寄回 10等待签收 20待验机 30待确认 50等待付款 60等待退回 70订单关闭 80订单取消 90订单异常 100订单完成',
    `notify`            tinyint unsigned NOT NULL DEFAULT '0' COMMENT '通知次数',
    `remark`            varchar(2000) NOT NULL DEFAULT '' COMMENT '备注',
    `ctime`             int unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `utime`             int unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY                 `idx_order_no` (`order_no`) USING BTREE,
    KEY                 `idx_trade_in_order_no` (`trade_in_order_no`) USING BTREE
)AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='以旧换新订单表';