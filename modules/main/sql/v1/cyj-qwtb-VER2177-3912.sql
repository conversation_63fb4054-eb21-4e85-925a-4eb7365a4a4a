CREATE TABLE `t_we_focus`
(
    `id` int unsigned NOT NULL AUTO_INCREMENT COMMENT '自增ID',
    `we_openid`  varchar(100)  NOT NULL DEFAULT '' COMMENT 'openid',
    `unionid`    varchar(100) NOT NULL DEFAULT '' COMMENT '用户在开放平台的唯一标识符',
    `store`      varchar(50) NOT NULL DEFAULT '' COMMENT '门店名称',
    `resources`  varchar(2000) NOT NULL DEFAULT '' COMMENT '资源内容 json数据',
    `ctime`      int unsigned NOT NULL DEFAULT '0' COMMENT '同步时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `uniq_unionid` (`unionid`) USING BTREE
) DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='企业微信用户列表';



ALTER TABLE `db_dreame`.`t_user_extend` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';



ALTER TABLE `db_dreame_goods`.`t_om_2022` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2023` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2024` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2025` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2026` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2027` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2028` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2029` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
ALTER TABLE `db_dreame_goods`.`t_om_2030` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';

ALTER TABLE `db_dreame_no_auth`.`t_user_extend` ADD `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店信息';
