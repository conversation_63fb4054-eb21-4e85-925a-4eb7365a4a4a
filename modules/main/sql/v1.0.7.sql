
ALTER TABLE `db_dreame`.`t_user_extend` add  `card` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm系统下发的card';

ALTER TABLE `db_dreame`.`t_user_extend` ADD INDEX `card`(`card`) USING BTREE;

ALTER TABLE `db_dreame`.`t_address_0` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_1` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_2` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_3` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_4` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_5` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_6` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_7` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_8` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';
ALTER TABLE `db_dreame`.`t_address_9` add  `crm_code` varchar(100) NOT NULL DEFAULT '' COMMENT 'crm返回code';


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gtype_99` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
    `cover_image` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '封面图',
    `images` text CHARACTER SET utf8 NOT NULL COMMENT '图片',
    `mprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '市场价（分）',
    `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '价格（分）',
    `is_coupons` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用优惠券(0：否 1：是)',
    `limit_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '限购数',
    `t_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '定时状态（0：否 1：启用）',
    `t_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定时时间',
    `detail` text NOT NULL COMMENT '商品详情',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `u_g` (`gid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='一元链接商品表';

ALTER TABLE `db_dreame_goods`.`t_gtype_0` add `video` varchar(255) NOT NULL DEFAULT '' COMMENT '视频';
ALTER TABLE `db_dreame_goods`.`t_gtype_99` add `video` varchar(255) NOT NULL DEFAULT '' COMMENT '视频';

ALTER TABLE `db_dreame_goods`.`t_uo_0` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_1` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_2` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_3` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_4` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_5` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_6` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_7` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_8` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';
ALTER TABLE `db_dreame_goods`.`t_uo_9` add `syn_crm` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已经同步到crm 1是0否 默认0';

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_crm_err_log` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `function` varchar(50) NOT NULL COMMENT '方法名',
    `rags` text NOT NULL COMMENT '参数集合',
    `ctime` int(10) NOT NULL COMMENT '加入时间',
    `msg` text NOT NULL COMMENT '返回信息',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '失败=1 处理成功后改成2 默认1',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='crm错误日志';

ALTER TABLE `db_dreame`.`t_banner` add `video` varchar(255) NOT NULL DEFAULT '' COMMENT '视频';