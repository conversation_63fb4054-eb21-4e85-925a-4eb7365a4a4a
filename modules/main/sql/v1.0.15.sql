CREATE TABLE `db_dreame_wiki`.`t_doc` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `doc_name` varchar(255) NOT NULL DEFAULT '' COMMENT '文档名称',
  `doc_code` varchar(50) NOT NULL DEFAULT '' COMMENT '唯一标识',
  `update_time` int(10) NOT NULL DEFAULT '0' COMMENT '时间更新',
  `img_list` text NOT NULL COMMENT '图片列表',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除 0否1是',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_unique` (`doc_code`) USING BTREE COMMENT '唯一标识'
) ENGINE=MyISAM DEFAULT CHARSET=utf8mb4 COMMENT='文档管理';
