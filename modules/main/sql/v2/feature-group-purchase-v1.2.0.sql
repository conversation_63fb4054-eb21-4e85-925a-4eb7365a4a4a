-- 免拼卡邀请配置
INSERT
`user_invite_gift_rules` (`id`,
                              `invite_type`,
                              `required_count`,
                              `rule_name`,
                              `rule_details`,
                              `is_active`,
                              `grant_mode`,
                              `start_time`,
                              `end_time`,
                              `created_at`,
                              `updated_at`,
                              `max_claims`
)
VALUES (6,
        6,
        10,
        '免拼卡邀请规则',
        '邀请10个好友获得1张免拼卡',
        1,  -- is_active: 1 = 激活
        1, -- grant_mode: 1 = 自动发放
        0, -- start_time: 0 = 不限制
        0, -- end_time: 0 = 不限制
        UNIX_TIMESTAMP(),
        UNIX_TIMESTAMP(),
        0 -- max_claims: 0 = 不限制
       );

INSERT INTO `user_invite_rule_gift_items` (`rule_id`,
                                           `gift_id`,
                                           `gift_type`,
                                           `quantity`,
                                           `created_at`,
                                           `updated_at`)
VALUES (6,
        '0',
        7, -- gift_type: 7 = 免拼卡
        1, -- quantity: 1 = 1张免拼卡
        UNIX_TIMESTAMP(),
        UNIX_TIMESTAMP());



ALTER TABLE `db_dreame_goods`.`t_group_purchase_goods`
    ADD COLUMN `rate` float(2, 2) NOT NULL DEFAULT 0 COMMENT '折扣率' AFTER `member_gift_card_id`,
    ADD COLUMN `group_key` varchar(10) NULL COMMENT '分组key' AFTER `rate`;