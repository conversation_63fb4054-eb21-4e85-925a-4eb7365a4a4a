CREATE TABLE `t_user_dict` (
   `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
   `dict_key` varchar(50) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '字典标识',
   `content` text COLLATE utf8mb4_general_ci NOT NULL COMMENT '字典内容（json格式）',
   `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态（1：启用；2：禁用）',
   `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
   `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
   PRIMARY KEY (`id`),
   UNIQUE KEY `uniq_user_key` (`user_id`,`dict_key`) USING BTREE COMMENT '用户字典唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户字典表';

