-- 商品推荐总表
CREATE TABLE db_dreame_goods.goods_recommend
(
    id          INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    gid         INT UNSIGNED NOT NULL COMMENT '商品全局唯一标识',
    sid         INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品规格全局唯一标识',
    total_times INT UNSIGNED DEFAULT 0 COMMENT '总推荐次数',
    UNIQUE KEY unique_gid_sid (gid, sid) COMMENT 'gid和sid的唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品推荐总表';

-- 用户商品推荐明细表
CREATE TABLE db_dreame_goods.goods_recommend_users
(
    id          INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '自增主键',
    user_id     INT UNSIGNED NOT NULL COMMENT '推荐用户ID',
    gid         INT UNSIGNED NOT NULL COMMENT '推荐商品ID',
    sid         INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品规格全局唯一标识',
    uid         VARCHAR(64) DEFAULT '' COMMENT '用户唯一标识',
    create_time BIGINT      DEFAULT 0 COMMENT '推荐创建时间(时间戳)',
    delete_time BIGINT DEFAULT 0 COMMENT '推荐删除时间(时间戳)',
    is_delete   TINYINT(1) UNSIGNED DEFAULT 0 COMMENT '删除状态(0-未删除,1-已删除)',
    UNIQUE KEY uniq_user_gid_sid (user_id, gid, sid, is_delete) COMMENT '用户对同一商品只能有一条有效推荐'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户商品推荐明细表';