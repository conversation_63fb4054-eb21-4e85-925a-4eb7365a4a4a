-- 新增 exchange_gold_rate 字段 int（10） 备注 金币兑换积分比例
ALTER TABLE `db_dreame_goods`.`point_config`
    ADD COLUMN `exchange_gold_rate` int(10) NOT NULL DEFAULT 0 COMMENT '金币兑换积分比例' ;

-- 新增金币兑换现金比例
ALTER TABLE `db_dreame_goods`.`point_config`
    ADD COLUMN `gold_conversion` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '金币兑换现金比例' AFTER `exchange_gold_rate`;

-- 修改中奖概率小数点由2位改为4位（百分比位数），转换成浮点数是6位
ALTER TABLE db_dreame_goods.t_draw_activity_prize MODIFY COLUMN rate decimal(10, 4) NOT NULL COMMENT '中奖概率' AFTER prize_lock_num;

-- 新增任务消耗类型
ALTER TABLE `db_dreame_goods`.`t_draw_activity_task`
    ADD COLUMN `consume_type` tinyint UNSIGNED NOT NULL DEFAULT 1 COMMENT '任务消耗类型 1=按抽奖次数，2=按消耗金币' AFTER `task_limit`;

-- 新增消耗金币字段
ALTER TABLE `db_dreame_goods`.`t_draw_activity_task`
    ADD COLUMN `consume_gold` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗金币' AFTER `draw_times`;

-- 中奖记录新增消耗金币字段
ALTER TABLE `db_dreame_goods`.`t_draw_activity_prize_record`
    ADD COLUMN `consume_gold` int UNSIGNED NOT NULL DEFAULT 0 COMMENT '消耗金币(金币抽奖)' AFTER `draw_time`;

-- 新增字典
INSERT INTO `db_dreame_admin`.`t_sys_dict_type` (`id`, `name`, `code`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (9, '金币抽奖配置', 'draw_gold_config', 1, '', 1752204018, 1752229056, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_type` (`id`, `name`, `code`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (10, '金币抽奖解锁配置', 'draw_gold_unlock_config', 1, '', 1752229042, 1752229042, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_type` (`id`, `name`, `code`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, '抽奖奖品类型', 'draw_prize_type', 1, '', 1752309895, 1752309895, 0);

-- 新增字典项
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (9, 'draw_gold_config', 'exchange_gold_list', '100000,200000,500000', 0, 1, '可兑换金币项，英文逗号分隔', 1752286976, 1752286976, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (10, 'draw_gold_unlock_config', 'min_exchange_gold:兑换', '10000000', 0, 1, '解锁兑换所需总金币', 1752229425, 1752284684, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (10, 'draw_gold_unlock_config', 'min_draw_gold:抽奖', '50000', 0, 1, '解锁抽奖所需总金币', 1752229322, 1752229322, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '谢谢参与', '1', 0, 1, '', 1752309937, 1752309937, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '积分', '2', 0, 1, '', 1752309962, 1752309962, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '优惠券', '3', 0, 1, '', 1752309969, 1752309969, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '兑换券', '4', 0, 1, '', 1752309978, 1752309978, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '现金红包', '5', 0, 1, '', 1752309984, 1752309984, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '金币', '6', 0, 1, '', 1752309993, 1752309993, 0);
INSERT INTO `db_dreame_admin`.`t_sys_dict_data` (`dict_type_id`, `dict_type_code`, `label`, `value`, `sort`, `status`, `remark`, `create_time`, `update_time`, `delete_time`) VALUES (11, 'draw_prize_type', '再来一次', '7', 0, 1, '', 1752309993, 1752309993, 0);


-- 邀请金币
INSERT INTO db_dreame_goods.user_invite_gift_rules (id, invite_type, required_count, rule_name, rule_details, grant_mode, is_active, start_time, end_time, created_at, updated_at, max_claims) VALUES (7, 7, 1, '金币邀请注册', '金币邀请注册', 1, 1, 0, 0, 1, 1, 0);
INSERT INTO db_dreame_goods.user_invite_rule_gift_items (id, rule_id, gift_id, gift_type, quantity, created_at, updated_at) VALUES (8, 7, '0', 8, 10000, 1, 1);
