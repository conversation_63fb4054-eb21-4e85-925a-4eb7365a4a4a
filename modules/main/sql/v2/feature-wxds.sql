ALTER TABLE `db_dreame_goods`.`t_uo_0`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_1`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_2`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_3`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_4`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_5`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_6`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_7`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_8`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';
ALTER TABLE `db_dreame_goods`.`t_uo_9`
    ADD COLUMN `relation_type` varchar(50) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务类型',
    ADD COLUMN `relation_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '业务ID';