ALTER TABLE `db_dreame_goods`.`t_uo_0`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_1`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_2`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_3`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_4`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_5`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_6`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_7`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_8`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';

ALTER TABLE `db_dreame_goods`.`t_uo_9`
    ADD COLUMN `consume_money` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '消费金';



ALTER TABLE `db_dreame_goods`.`t_uo_g_0`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_1`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_2`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_3`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_4`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_5`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_6`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_7`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_8`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_9`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_10`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_11`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_12`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_13`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_14`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_15`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_16`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_17`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_18`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame_goods`.`t_uo_g_19`
    ADD `consume_money` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '消费金';
ALTER TABLE `db_dreame`.`user_shop_money`
    ADD COLUMN `money_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '金额类型' AFTER `activity_id`;

ALTER TABLE `db_dreame`.`user_shop_money_record`
    ADD COLUMN `money_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '金额类型' AFTER `money`;

UPDATE `db_dreame`.user_shop_money SET money_type = 1
UPDATE `db_dreame`.user_shop_money_record SET money_type = 1

INSERT INTO db_dreame_goods.user_invite_gift_rules (id, invite_type, required_count, rule_name, rule_details, grant_mode, is_active, start_time, end_time, created_at, updated_at, max_claims) VALUES (5, 5, 1, '消费金邀请注册', '金邀请注册获得消费金', 1, 1, 0, 0, 1, 1, 0);

INSERT INTO db_dreame_goods.user_invite_rule_gift_items (id, rule_id, gift_id, gift_type, quantity, created_at, updated_at) VALUES (4, 5, '0', 6, 500, 1, 1);