CREATE TABLE `db_dreame_goods`.one_yuan_seckill
(
    id             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '一元秒杀ID',
    activity_id    BIGINT          NOT NULL COMMENT '对应活动ID',
    gid            BIGINT          NOT NULL COMMENT '商品ID',
    order_no       VARCHAR(50)          COMMENT '订单号（创建订单时回写）',
    user_id        BIGINT          NOT NULL COMMENT '用户ID',
    invite_members BIGINT          DEFAULT 0 COMMENT '已邀请人数',
    status         TINYINT      DEFAULT 0 COMMENT '助力状态：0-进行中，1-助力成功',
    created_at     BIGINT COMMENT '创建时间(秒时间戳)',
    updated_at     BIGINT COMMENT '更新时间(秒时间戳)',
    UNIQUE KEY uk_order_no      (order_no),
    INDEX      idx_user_id      (user_id),
    INDEX      idx_activity_gid (activity_id, gid)
) COMMENT '1元秒杀表';

CREATE TABLE `db_dreame_goods`.user_invites
(
    id               BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    inviter_id       BIGINT NOT NULL COMMENT '邀请人ID',
    invitee_id       BIGINT NOT NULL COMMENT '被邀请人ID',
    invite_type      TINYINT NOT NULL COMMENT '邀请类型 1=一元秒杀 …',
    relate_id        BIGINT NOT NULL COMMENT '与邀请类型关联的外键ID，例如拼团ID、秒杀ID等',
    invited_at       BIGINT COMMENT '邀请时间（秒时间戳）',
    INDEX idx_inviter (inviter_id),
    UNIQUE KEY idx_invitee (invitee_id),
    INDEX idx_relate (invite_type, relate_id)
) COMMENT = '用户邀请表';

CREATE TABLE `db_dreame_goods`.user_invite_rank
(
    id               BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    user_id          BIGINT NOT NULL COMMENT '用户ID',
    invite_count     INT    NOT NULL DEFAULT 0 COMMENT '邀请人数',
    created_at       BIGINT COMMENT '创建时间(秒时间戳)',
    updated_at       BIGINT COMMENT '更新时间(秒时间戳)',
    UNIQUE KEY uk_user_id (user_id),
    INDEX idx_invite_count (invite_count)
) COMMENT = '用户邀请排行榜';

