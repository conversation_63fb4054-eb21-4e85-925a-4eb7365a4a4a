CREATE TABLE `db_dreame_goods`.`point_config`
(
    `id`                   INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `reward_rate`          TINYINT NOT NULL DEFAULT 1 COMMENT '奖励比例系数（实付金额 × 系数 ÷ 100 = 获得积分）',
    `employee_reward_rate` TINYINT NOT NULL DEFAULT 1 COMMENT '微笑大使奖励比例系数（实付金额 × 系数 ÷ 100 = 获得积分）',
    `deduction_rate`       TINYINT NOT NULL DEFAULT 10 COMMENT '抵扣比例（1积分 = 1 ÷ 系数 元现金，默认10表示10积分=1元）',
    `status`               TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态: 0=禁用, 1=启用',
    `ctime`                INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
    `utime`                INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分比例配置表';