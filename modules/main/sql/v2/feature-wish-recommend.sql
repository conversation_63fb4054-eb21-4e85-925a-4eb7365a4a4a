-- 创建商城商品心愿单表
CREATE TABLE `db_dreame_goods`.`wishlist`
(
    `id`          bigint NOT NULL AUTO_INCREMENT COMMENT '心愿单唯一ID',
    `user_id`     bigint NOT NULL COMMENT '关联用户ID',
    `goods_id`    bigint NOT NULL COMMENT '关联商品ID',
    `specs_id`    bigint NOT NULL COMMENT '商品规格ID',
    `add_time`    int(10) NOT NULL DEFAULT 0 COMMENT '加入时间',
    `expire_time` int(10) DEFAULT NULL COMMENT '过期时间',
    `is_notified` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已通知',
    `is_deleted`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '逻辑删除标识',
    `remark`      varchar(500) DEFAULT NULL COMMENT '用户备注',
    `created_time`  int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_product` (`user_id`,`goods_id`) COMMENT '用户商品唯一索引',
    KEY           `idx_user_id` (`user_id`) COMMENT '用户ID索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商城商品心愿单表';