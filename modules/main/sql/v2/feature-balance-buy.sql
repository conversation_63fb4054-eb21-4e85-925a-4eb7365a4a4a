-- 为现有表添加新字段
ALTER TABLE `db_dreame_goods`.`user_invites`
    ADD COLUMN device_id varchar(50) DEFAULT NULL COMMENT '设备ID';

-- remark
ALTER TABLE `db_dreame_goods`.`user_invites`
ADD COLUMN remark varchar(255) DEFAULT NULL COMMENT '备注信息';

-- 新增表：邀请奖励规则表
-- 此表定义了邀请活动的基本规则，例如"邀请3人"
CREATE TABLE `db_dreame_goods`.`user_invite_gift_rules`
(
    id             BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    invite_type    TINYINT NOT NULL COMMENT '邀请类型',
    required_count INT     NOT NULL COMMENT '所需邀请数',
    max_claims INT DEFAULT 1 NOT NULL COMMENT '每个用户可领取该规则奖励的最大次数，0表示不限制',
    rule_name      VARCHAR(255) NULL COMMENT '规则名称，方便管理和理解',
    rule_details   TEXT NULL COMMENT '活动规则详情，接口返回无需鉴权',
    grant_mode     TINYINT NOT NULL DEFAULT 1 COMMENT '奖品发放方式：1=自动发放，2=手动领取，3=定时发放',
    is_active      TINYINT NOT NULL DEFAULT 1 COMMENT '规则是否激活 0=否, 1=是',
    start_time     BIGINT NOT NULL DEFAULT 0 COMMENT '活动开始时间（秒时间戳），0表示不限制',
    end_time       BIGINT NOT NULL DEFAULT 0 COMMENT '活动结束时间（秒时间戳），0表示不限制',
    created_at     BIGINT NOT NULL COMMENT '创建时间（秒时间戳）',
    updated_at     BIGINT NOT NULL COMMENT '最后修改时间（秒时间戳）'
) COMMENT '邀请奖励规则表';

-- 新增表：邀请规则奖品详情表
-- 此表存储每个邀请规则下具体包含的奖品项，一个规则可以有多个奖品项
CREATE TABLE `db_dreame_goods`.`user_invite_rule_gift_items`
(
    id          BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    rule_id     BIGINT NOT NULL COMMENT '关联 invite_gift_rules 表的ID',
    gift_id     VARCHAR(50) NOT NULL COMMENT '奖品ID，关联奖品表',
    gift_type   INT NOT NULL DEFAULT 0 COMMENT '奖品类型，区分不同类型的礼品ID',
    quantity    INT NOT NULL COMMENT '奖品数量',
    -- 如果需要，可以添加一个字段来表示这个奖品项是否可以重复领取
    created_at  BIGINT NOT NULL COMMENT '创建时间（秒时间戳）',
    updated_at  BIGINT NOT NULL COMMENT '最后修改时间（秒时间戳）',
    KEY idx_rule_id (rule_id),
    -- 一个规则下的某个具体奖品项只能配置一次，添加唯一约束
    UNIQUE KEY uk_rule_gift_item (rule_id, gift_id, gift_type)
) COMMENT '邀请规则奖品详情表';

-- 新增表：邀请活动礼品发放表
-- 此表记录了用户实际获得的每个奖品实例
CREATE TABLE `db_dreame_goods`.`user_invite_gifts`
(
    id                  BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键',
    inviter_id          BIGINT NOT NULL COMMENT '邀请人ID',
    -- 关联到 invite_gift_rules 表的ID，用于了解是哪个规则触发的奖励
    rule_id             BIGINT NULL COMMENT '关联 invite_gift_rules 表的ID',
    -- 关联到 invite_rule_gift_items 表的ID，明确是规则中的哪个具体奖品项
    rule_gift_item_id   BIGINT NOT NULL COMMENT '关联 invite_rule_gift_items 表的ID',
    claim_sequence INT DEFAULT 1 NOT NULL COMMENT '用户对该规则的领取次数序列，例如第一次领取为1，第二次为2',
    gift_id             VARCHAR(50) NOT NULL COMMENT '奖品ID',
    gift_type           INT NOT NULL DEFAULT 0 COMMENT '奖品类型',
    quantity            INT NOT NULL COMMENT '奖品数量',
    issued_at           BIGINT NULL COMMENT '发放时间（秒时间戳）',
    status              TINYINT NOT NULL DEFAULT 0 COMMENT '状态 0=未发放, 1=已发放',
    created_at          BIGINT NOT NULL COMMENT '创建时间（秒时间戳）',
    updated_at          BIGINT NOT NULL COMMENT '最后修改时间（秒时间戳）',
    KEY idx_inviter_id (inviter_id),
    KEY idx_rule_id (rule_id),
    KEY idx_rule_gift_item_id (rule_gift_item_id)

) COMMENT '邀请活动礼品发放表';

--  会员商城邀请注册配置
INSERT INTO db_dreame_goods.user_invite_gift_rules (id, invite_type, required_count, rule_name, rule_details, grant_mode, is_active, start_time, end_time, created_at, updated_at, max_claims) VALUES (1, 2, 1, '好朋友一起购', '<p><strong>1. 活动时间：</strong>每天0:00:00-23:59:59<br />
<strong>2. 参与条件：<br />
&nbsp; </strong>- 邀请人需为追觅APP已注册用户<br />
&nbsp; - 被邀请人必须是全新未注册用户（以手机号/设备为唯一识别）<br />
&nbsp; - 被邀请人需为未注册过DREAME APP的新用户，被邀请人必须通过邀请人的专属链接完成注册<br />
<strong>3. 奖励机制：<br />
&nbsp; </strong>- 邀请人每成功邀请1位新用户注册，<strong>可获得100元购物金（购物金可叠加使用，但不超过商品价格的50%）</strong><br />
&nbsp; - 购物金最高可抵扣商品价格的50%，例：商品原价2000，成功邀请20人，获得2000优惠，但该商品只能抵1000元，剩余的优惠可用于购买其他商品<br />
&nbsp; - 提前邀请奖励<br />
&nbsp; - 用户可提前邀请好友注册，每成功邀请1人即可获得100元购物抵扣金。<br />
&nbsp; - 抵扣金自动累积至账户，长期有效，无过期限制。<br />
&nbsp; - 例如：成功邀请10人，即可获得1000元抵扣金，后续购物可直接使用<br />
&nbsp; - 先买后邀奖励<br />
&nbsp; - 若您在支付时邀请好友，但好友尚未完成注册，可先按原价支付购买商品。<br />
&nbsp; - 好友在您下单后完成注册的，每成功注册1人，系统将自动返还100元至您的账户。<br />
&nbsp; - 返还金额可随时用于下次购物抵扣<br />
<strong>4. 使用限制：<br />
&nbsp; </strong>- 100元购物金可叠加使用，但最终实付金额&ge;商品价格的50%。<br />
&nbsp; - 本券不可与商城其他优惠(包括国补，积分，优惠券，赠品，等活动）叠加使用。<br />
&nbsp; - 每位邀请人最多可邀请新用户人数上不封顶<br />
&nbsp; - 成功使用购物金购买机器后，如订单发生退货退款，则按照实际付款金额进行退款，购物金将自动取消，不再发放。<br />
<strong>5. 参与流程：</strong><br />
&nbsp; 1. 用户A在活动页点击&quot;邀请好友&quot;生成专属邀请码/链接<br />
&nbsp; 2. 用户B通过该链接下载注册追觅APP（必须实名认证）<br />
&nbsp; 3. 系统立刻自动发放购物金至用户A账户<br />
&nbsp; 4. 用户A在追觅APP内查看购物金，并在购物时使用<br />
<strong>6. 注意事项</strong><br />
&nbsp; a. 同一登录账号、手机号、同一终端设备号或其他指向同一用户的情形，均视为同一用户。<br />
&nbsp; b. 在参与本活动之前，仔细阅读本活动参与方式及规则，用户参与本活动即表示愿意遵守与本活动有关的规则。在参与活动期间，用户应严格遵守法律法规和本规则的规定，遵循诚实信用原则，不得从事任何违反国家法律法规、违反本规则，或任何扰乱DREAME APP，违反公序良俗、侵害社会公共利益、他人利益以及活动方利益的行为或任何舞弊、欺诈行为，包括但不限于通过不正当手段参与活动等行为。活动方将合理判定用户是否存在前述违规行为，若判定用户存在前述违规行为的，活动方有权在事先不通知的前提下取消或者限制用户参与本活动的资格，收回补贴（如有）并有权封禁账号。同时，活动方保留追究法律责任的权利；<br />
&nbsp; c. 本活动仅为用户提供网络活动式平台体验服务，用户不得利用相关网站发布侮辱与攻击性语言，不得侵犯他人隐私，不得发布任何违法内容，任何由用户发布、参与、传播互动内容而导致的法律责任，由用户自行承担。活动方将合理判定用户是否存在前述违规行为，若判定用户存在前述违规行为的，活动方有权在事先不通知的前提下取消或者限制用户参与本活动的资格，收回补贴（如有）并有权封禁账号。同时，活动方保留追究法律责任的权利;<br />
&nbsp; d. 如出现不可抗力或情势变更的情況（包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动遭受严重网络攻击或因系统故障需要暂停举办的），活动方有权临时中止/终止本活动。因网络、通讯线路故障、手机/电脑软件或系统升级等非活动方原因导致用户无法获取、使用补贴或遭受其他权利损失的，活动方恕不承担任何责任;<br />
&nbsp; e. 在法律法规允许的范围内，活动方有权根据实际需要对本活动内容、规则进行变动或调整，相关变动或调整将公布在活动规则页面上，并于公告指定时间生效。用户继续参与活动则视为同意井接受变动或者调整后的活动规则。<br />
&nbsp; f. 如遇问题，请联系DREAME APP的客服。</p>
', 1, 1, 0, 0, 1, 1, 0);
INSERT INTO db_dreame_goods.user_invite_gift_rules (id, invite_type, required_count, rule_name, rule_details, grant_mode, is_active, start_time, end_time, created_at, updated_at, max_claims) VALUES (3, 3, 1, '会员商城邀请注册', '会员商城邀请注册', 1, 1, 0, 0, 1, 1, 0);

ALTER TABLE `db_dreame_wares`.`t_gift_user_cards`
    ADD `is_restriction` int(11) NOT NULL DEFAULT '0' COMMENT '卡类型是否限制50%';


ALTER TABLE db_dreame.t_user_popup
    ADD COLUMN remark VARCHAR(255) NULL COMMENT '备注';
