CREATE TABLE `common_tags`
(
    `id`          INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '唯一标识ID',
    `name`        VARCHAR(100) NOT NULL COMMENT '标签名称，例如：类别名称、标签名、品牌名等',
    `description` TEXT         NULL     DEFAULT NULL COMMENT '标签的详细描述',
    `icon`        VARCHAR(255) NULL     DEFAULT NULL COMMENT '标签图标的URL或字体图标类名',
    `type`        VARCHAR(50)  NOT NULL DEFAULT 'default' COMMENT '标签类型',
    `parent_id`  INT UNSIGNED NULL DEFAULT 0 COMMENT '父标签ID，用于构建树形结构（例如：多级分类）',
    `sort_order`  INT          NOT NULL DEFAULT 0 COMMENT '排序值，用于控制展示顺序',
    `is_active`   TINYINT(1)   NOT NULL DEFAULT 1 COMMENT '是否活跃/可用，0-否，1-是',
    `created_at` BIGINT       NOT NULL COMMENT '创建时间',
    `updated_at` BIGINT       NOT NULL COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_name` (`name`),
    INDEX `idx_type` (`type`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT ='通用分类展示标签表';