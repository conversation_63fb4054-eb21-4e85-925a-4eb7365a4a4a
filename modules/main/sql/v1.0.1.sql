
CREATE TABLE  IF NOT EXISTS `db_dreame`.`t_address_0` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `nick` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `province` varchar(50) NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(50) NOT NULL DEFAULT '' COMMENT '市',
  `area` varchar(50) NOT NULL DEFAULT '' COMMENT '区',
  `detail` varchar(500) NOT NULL DEFAULT '' COMMENT '详细地址',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否是默认收获地址0否1是',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
  PRIMARY KEY (`id`),
  KEY `index_uid` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户配送地址';

create table if not EXISTS `db_dreame`.`t_address_1` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_2` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_3` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_4` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_5` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_6` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_7` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_8` LIKE `db_dreame`.`t_address_0`;
create table if not EXISTS `db_dreame`.`t_address_9` LIKE `db_dreame`.`t_address_0`;

CREATE TABLE IF NOT EXISTS  `db_dreame`.`t_retailers` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `shop_img` varchar(200) NOT NULL COMMENT '门店图片',
  `shop_name` varchar(100) NOT NULL COMMENT '门店名称',
  `shop_phone` varchar(20) NOT NULL COMMENT '门店电话',
  `start_time` varchar(20) NOT NULL COMMENT '门店营业时间',
  `end_time` varchar(20) NOT NULL COMMENT '门店休息时间',
  `shop_addr` varchar(200) NOT NULL COMMENT '门店详细地址',
  `pos_l` decimal(10,6) NOT NULL COMMENT '门店经度(地理位置)',
  `pos_b` decimal(10,6) NOT NULL COMMENT '门店维度(地理位置)',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否下架 0否 1是',
  `orders` int(10) NOT NULL DEFAULT '1' COMMENT '排序号',
  `ctime` int(11) unsigned NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='门店信息表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_banner`  (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `admin_id` int(11) NOT NULL COMMENT '创建人id',
    `title` varchar(100) NOT NULL COMMENT '标题',
    `image` varchar(255) NOT NULL COMMENT '图片地址',
    `jump_type` tinyint(1) unsigned NOT NULL COMMENT '跳转类型（1跳转链接 2跳转商城动态 3跳转小程序连接）',
    `jump_url` varchar(255) NOT NULL COMMENT '跳转url',
    `sort` int(11) unsigned NOT NULL DEFAULT '1' COMMENT '排序(位置)',
    `vtime_start` int(11) unsigned NOT NULL COMMENT '有效期起始时间',
    `vtime_end` int(11) unsigned NOT NULL COMMENT '有效期结束时间',
    `ctime` int(11) unsigned NOT NULL COMMENT '创建时间',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '审核（1审核通过 0未审核 2审核中 3审核不通过）',
    `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '删除（1删除 0正常）',
    `update_time` int(11) NOT NULL DEFAULT '0' COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='banner表';





CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_0` (
    `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `market_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联营销资源id',
    `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1：优惠券  2：泡泡卡  3：排除卡  4：免邮卡',
    `get_channel` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '获得方式 1 签到获得 2 新人礼包',
    `get_relation` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '获得时关联的业务表id',
    `use_channel` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '使用的渠道 1 购买商品 2 抽取盲盒',
    `use_relation` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '使用时关联的业务表id',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未使用 1 锁定或已使用 2 过期',
    `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '获得时间',
    `use_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '使用时间',
    `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '卡券开始时间',
    `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '卡券结束时间',
    PRIMARY KEY (`id`),
    KEY `user_id` (`user_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='用户卡券表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_1` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_2` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_3` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_4` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_5` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_6` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_7` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_8` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_9` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_10` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_11` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_12` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_13` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_14` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_15` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_16` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_17` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_18` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_19` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_20` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_21` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_22` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_23` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_24` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_25` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_26` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_27` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_28` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_29` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_30` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_31` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_32` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_33` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_34` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_35` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_36` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_37` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_38` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_39` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_40` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_41` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_42` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_43` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_44` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_45` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_46` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_47` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_48` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_49` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_50` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_51` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_52` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_53` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_54` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_55` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_56` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_57` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_58` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_59` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_60` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_61` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_62` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_63` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_64` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_65` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_66` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_67` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_68` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_69` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_70` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_71` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_72` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_73` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_74` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_75` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_76` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_77` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_78` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_79` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_80` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_81` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_82` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_83` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_84` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_85` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_86` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_87` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_88` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_89` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_90` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_91` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_92` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_93` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_94` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_95` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_96` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_97` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_98` LIKE `db_dreame`.`t_user_card_0`;
CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_card_99` LIKE `db_dreame`.`t_user_card_0`;




ALTER TABLE `db_dreame`.`t_user_card_0` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_1` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_2` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_3` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_4` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_5` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_6` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_7` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_8` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_9` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_10` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_11` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_12` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_13` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_14` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_15` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_16` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_17` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_18` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_19` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_20` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_21` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_22` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_23` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_24` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_25` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_26` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_27` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_28` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_29` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_30` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_31` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_32` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_33` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_34` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_35` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_36` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_37` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_38` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_39` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_40` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_41` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_42` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_43` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_44` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_45` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_46` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_47` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_48` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_49` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_50` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_51` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_52` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_53` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_54` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_55` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_56` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_57` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_58` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_59` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_60` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_61` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_62` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_63` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_64` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_65` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_66` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_67` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_68` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_69` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_70` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_71` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_72` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_73` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_74` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_75` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_76` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_77` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_78` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_79` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_80` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_81` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_82` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_83` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_84` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_85` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_86` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_87` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_88` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_89` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_90` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_91` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_92` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_93` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_94` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_95` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_96` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_97` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_98` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;
ALTER TABLE `db_dreame`.`t_user_card_99` DROP INDEX `user_id`,ADD INDEX `i_u_e`(`user_id`, `expire_time`) USING BTREE;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_market_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `admin_id` int(10) unsigned NOT NULL COMMENT '管理员ID',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `images` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
  `type` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '资源类型 1 商品 ',
  `resource` text NOT NULL COMMENT '资源内容 json数据',
  `stock_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '库存数量',
  `send_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发放数量',
  `use_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用数量',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未审批 1审批通过 2 审批中 3 审批失败',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='营销配置表';


CREATE TABLE IF NOT EXISTS `db_dreame`.`t_products`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `name` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '产品名称',
    `image` varchar(200) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '产品图片',
    `m_name` varchar(50) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '市场名称',
    `sn` varchar(10) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'sn编码',
    `ctime` int(11) NOT NULL DEFAULT '0' COMMENT '创建时间',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1删除0正常',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品表';


CREATE TABLE IF NOT EXISTS `db_dreame`.`t_activity_config` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `market_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '关联营销配置ID',
  `name` varchar(255) NOT NULL COMMENT '名称',
  `surplus_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '库存数（剩余库存）',
  `last_data` varchar(255) NOT NULL DEFAULT '',
  `grant_type` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '发放方式 1 新用户',
  `start_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `is_delete` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0 未删除 1删除',
  `update_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  `create_time` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  `img` varchar(255) NOT NULL DEFAULT '' COMMENT '图片',
  `a_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '抽盒分享活动（1 分享得奖励 2 拉新得奖励）助力活动（1 新用户助力 2 用户助力）',
  `btn_img` varchar(255) NOT NULL DEFAULT '' COMMENT '按钮图片',
  `weight` int(11) NOT NULL DEFAULT '0' COMMENT '资源比重',
  `resource` text NOT NULL COMMENT '资源内容 json数据',
  `help_order` text NOT NULL COMMENT '助力下单奖励内容 json数据',
  `sales_num` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '销量',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8mb4 COMMENT='活动配置表';




CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_market_send` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `market_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联营销配置ID',
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联商品ID',
  `name` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
  `stock_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '库存数',
  `send_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '发放数量',
  `start_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '开始时间',
  `end_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '结束时间',
  `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '修改时间',
  `create_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '0 未删除 1 删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `market_id` (`market_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COMMENT='活动营销资源发放表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_0`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品id',
    `phone` varchar(20) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
    `sn` varchar(100) COLLATE utf8_unicode_ci NOT NULL COMMENT 'sn编码',
    `buy_time` int(11) NOT NULL COMMENT '购买时间',
    `create_time` int(11) NOT NULL COMMENT '注册时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `sn` (`sn`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品注册表';

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_1` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_2` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_3` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_4` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_5` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_6` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_7` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_8` LIKE `db_dreame_log`.`t_p_reg_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_9` LIKE `db_dreame_log`.`t_p_reg_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2022`  (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
    `reg_id` int(11) NOT NULL DEFAULT '0' COMMENT '注册id',
    `product_id` int(11) NOT NULL DEFAULT '0' COMMENT '产品id',
    `phone` varchar(20) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
    `sn` varchar(100) COLLATE utf8_unicode_ci NOT NULL DEFAULT '' COMMENT 'sn编码',
    `create_time` int(11) NOT NULL DEFAULT '0' COMMENT '注册时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `sn` (`sn`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品注册后台表';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2023` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2024` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2025` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2026` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2027` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2028` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2029` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_p_reg_detail_2030` LIKE `db_dreame_log`.`t_p_reg_detail_2022`;

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_extend`  (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `source` int(10) NOT NULL DEFAULT '0' COMMENT '来源',
    `guide_id` int(11) NOT NULL DEFAULT '0' COMMENT '导购员id（只是邀请注册，并非实际导购关系）',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
    `tag` varchar(50) NOT NULL COMMENT '标签',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户扩展表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_guide`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT '0'  COMMENT '用户id',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名字',
  `store` varchar(50) NOT NULL DEFAULT '' COMMENT '门店',
  `status` tinyint(1) NOT NULL DEFAULT '0'  COMMENT '状态',
   PRIMARY KEY (`id`),
   UNIQUE KEY `index_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='导购表';

CREATE TABLE IF NOT EXISTS `db_dreame`.`t_user_guide`  (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL DEFAULT '0' COMMENT '用户id',
  `guide_id` int(11) NOT NULL DEFAULT '0' COMMENT '导购id',
  `ctime` int(11) NOT NULL DEFAULT '0' COMMENT '绑定时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_uid` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户绑定导购表';

-- 解决guide主键 问题
ALTER TABLE `db_dreame`.`t_user_guide` MODIFY `id` int(11) unsigned NOT NULL AUTO_INCREMENT;



ALTER TABLE `db_dreame`.`t_address_0`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_1`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_2`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_3`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_4`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_5`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_6`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_7`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_8`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;
ALTER TABLE `db_dreame`.`t_address_9`
CHANGE COLUMN `province` `pid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '省' AFTER `phone`,
CHANGE COLUMN `city` `cid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '市' AFTER `pid`,
CHANGE COLUMN `area` `aid` bigint UNSIGNED NOT NULL DEFAULT 0 COMMENT '区' AFTER `cid`;


CREATE TABLE IF NOT EXISTS `db_dreame_log`. `t_user_record` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `pv` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '访问次数',
  `uv` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '访问人数',
  `day` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '日期',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `day` (`day`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='用户类数据统计';

CREATE TABLE IF NOT EXISTS `db_dreame`. `t_oa_focus` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `oa_openid` varchar(100) NOT NULL DEFAULT '' COMMENT '公众号openid',
  `unionid` varchar(100) NOT NULL DEFAULT '' COMMENT '用户在开放平台的唯一标识符',
  `status` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '是否关注，0否1是',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '最后活动时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_openid` (`oa_openid`)
  ) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='关注公众号用户列表';


CREATE TABLE IF NOT EXISTS  `db_dreame`.`t_phone` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号码',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_uid_phone` (`user_id`,`phone`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户手机号（手机号和账号ID有一对多关系）';


CREATE TABLE if NOT EXISTS `db_dreame`.`t_p_m` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `p_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
    `mc_id` int(10) NOT NULL DEFAULT '0' COMMENT '资源id',
    `stock` int(10) NOT NULL DEFAULT '0' COMMENT '库存',
    `sales` int(10) NOT NULL DEFAULT '0' COMMENT '销量',
    `ctime` int(10) NOT NULL DEFAULT '0' COMMENT '加入时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `p_c` (`p_id`,`mc_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品与优惠券关联表';

CREATE TABLE if NOT EXISTS `db_dreame`.`t_p_m` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `p_id` int(10) NOT NULL DEFAULT '0' COMMENT '产品id',
    `mc_id` int(10) NOT NULL DEFAULT '0' COMMENT '资源id',
    `stock` int(10) NOT NULL DEFAULT '0' COMMENT '库存',
    `sales` int(10) NOT NULL DEFAULT '0' COMMENT '销量',
    `ctime` int(10) NOT NULL DEFAULT '0' COMMENT '加入时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `p_c` (`p_id`,`mc_id`) USING BTREE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品与优惠券关联表';

CREATE TABLE if NOT EXISTS `db_dreame`.`t_pm_config` (
    `id` int(10) NOT NULL AUTO_INCREMENT,
    `max_count` int(10) unsigned NOT NULL DEFAULT '10'  COMMENT '优惠券最大领取数量',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='产品优惠券相关配置';

INSERT INTO `db_dreame`.`t_pm_config` (`id`, `max_count`) VALUES (1, 10);
