
/*
* 上下水服务
*/
ALTER TABLE `db_dreame_goods`.`t_ofreight`
    MODIFY `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '1:满免运费 2：默认运费 3：运费地区配置 4：不可配送区域 5：勘探服务地区';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_plumbing_order` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
    `sn` varchar(100) NOT NULL DEFAULT '' COMMENT 'sn编码',
    `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
    `name` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
    `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
    `pid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '省',
    `cid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '市',
    `aid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '区',
    `detail` varchar(500) NOT NULL DEFAULT '' COMMENT '详细地址',
    `address` varchar(500) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '收获地址(json数据)',
    `type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '类型（1：勘探服务）',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '状态（0：待服务 1：已取消 2：已完成）',
    `context_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '环境类型（1：毛坯房 2：装修中 3：已入住）',
    `expect_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '期望时间',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_o` (`order_no`) USING BTREE,
    KEY `i_c` (`ctime`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='上下水服务订单';


CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_explore_search` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `pid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '省id',
    `cid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '市id',
    `cname` varchar(255) NOT NULL DEFAULT '' COMMENT '名称',
    `count` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '地区搜索次数',
    `update_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '记录时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `i_c` (`cid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='勘探服务地区搜索次数表';