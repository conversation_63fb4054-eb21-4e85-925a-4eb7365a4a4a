
CREATE DATABASE IF NOT EXISTS `db_dreame_goods`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gak` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gid` int(10) unsigned NOT NULL DEFAULT '0',
  `at_name` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '属性名',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `i_g` (`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='属性名表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gav` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ak_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'attr-id',
  `at_val` varchar(50) CHARACTER SET utf8 NOT NULL COMMENT '属性值',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `f_aid` (`ak_id`),
  CONSTRAINT `f_aid` FOREIGN KEY (`ak_id`) REFERENCES `db_dreame_goods`.`t_gak` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='属性值表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gmain` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `sku` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '商品名',
  `type` smallint(5) unsigned NOT NULL DEFAULT '0' COMMENT '商品类型（0：普通商品）',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否下架 0否 1是',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  `version` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '可见版本，0都可见',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `i_sku` (`sku`),
  KEY `i_st` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品主表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gspecs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
  `sku` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT 'sku',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '价格（分）',
  `av_ids` varchar(100) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '规格属性id ,分割',
  `image` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '规格图片',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
  PRIMARY KEY (`id`),
  KEY `i_g` (`gid`),
  KEY `i_s` (`sku`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多规格表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gstock` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gid` int(10) unsigned NOT NULL DEFAULT '0',
  `sid` int(10) unsigned NOT NULL DEFAULT '0',
  `stock` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '库存',
  `sales` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '销量',
  `wait` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '未付款数量',
  `is_del` tinyint(1) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_g_s` (`gid`,`sid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='商品库存表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gtag` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gid` int(10) unsigned NOT NULL DEFAULT '0',
  `tid` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '标签',
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_t_g` (`tid`,`gid`),
  KEY `i_g` (`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品标签表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_gtype_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
  `cover_image` varchar(255) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '封面图',
  `images` text CHARACTER SET utf8 NOT NULL COMMENT '图片',
  `mprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '市场价（分）',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '价格（分）',
  `atype` tinyint(4) NOT NULL COMMENT '商品属性（0：统一规格；1：多规格；2：自定义）',
  `is_coupons` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否可用优惠券(0：否 1：是)',
  `limit_num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '限购数',
  `t_status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '定时状态（0：否 1：启用）',
  `t_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '定时时间',
  `detail` text NOT NULL COMMENT '商品详情',
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_g` (`gid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='普通商品表';

CREATE TABLE IF NOT EXISTS `db_dreame_log`.`t_images` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `image` varchar(500) CHARACTER SET utf8mb4 NOT NULL DEFAULT '',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='图片表-临时';



CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(50) COLLATE latin1_general_ci NOT NULL DEFAULT '',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态...',
  `source` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '来源 0：普通下单 1：导购下单',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `i_o` (`order_no`) USING BTREE,
  KEY `i_s` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单主表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2023` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2024` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2025` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2026` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2027` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2028` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2029` LIKE `db_dreame_goods`.`t_om_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_om_2030` LIKE `db_dreame_goods`.`t_om_2022`;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) NOT NULL DEFAULT '',
  `prepay_id` varchar(300) NOT NULL DEFAULT '' COMMENT '微信统一下单返回(只有两小时的有效期)',
  `tid` varchar(50) NOT NULL DEFAULT '' COMMENT '微信支付订单号',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实付款',
  `ptime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'prepay_id最后生成时间',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '支付时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_o` (`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='订单支付流水';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2023` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2024` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2025` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2026` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2027` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2028` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2029` LIKE `db_dreame_goods`.`t_opay_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_opay_2030` LIKE `db_dreame_goods`.`t_opay_2022`;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_main` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `status` int(10) unsigned NOT NULL DEFAULT '1000' COMMENT '1000待审核 1010审核通过 1020 审核拒绝 100取消 20000000退款成功',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_no` (`refund_no`) USING BTREE,
  KEY `i_s` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='订单退款主表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `r_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '退款原因',
  `m_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '退款方式',
  `status` int(10) unsigned NOT NULL DEFAULT '1000' COMMENT '1000待审核 1010审核通过 1020 审核拒绝 100取消 20000000退款成功',
  `ostatus` int(10) unsigned NOT NULL COMMENT '退款时订单状态',
  `describe` varchar(200) NOT NULL DEFAULT '' COMMENT '补充描述',
  `images` varchar(500) NOT NULL DEFAULT '' COMMENT '图片',
  `a_reason` varchar(500) NOT NULL DEFAULT '' COMMENT '拒绝原因',
  `mail_no` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流单号',
  `express_code` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流公司代码',
  `express_name` varchar(50) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '退款物流公司名称',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款价格',
  `rtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '退款时间',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '申请时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_no` (`refund_no`) USING BTREE,
  KEY `i_u_g` (`user_id`) USING BTREE,
  KEY `i_o` (`order_no`) USING BTREE,
  KEY `i_s` (`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='订单退款表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_1` LIKE `db_dreame_goods`.`t_orefund_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_2` LIKE `db_dreame_goods`.`t_orefund_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_3` LIKE `db_dreame_goods`.`t_orefund_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_4` LIKE `db_dreame_goods`.`t_orefund_0`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `refund_no` varchar(50) NOT NULL DEFAULT '' COMMENT '退款单号',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `og_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单商品ID',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_r_g` (`refund_no`,`og_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='订单退款商品表';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2023` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2024` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2025` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2026` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2027` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2028` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2029` LIKE `db_dreame_goods`.`t_orefund_g_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_orefund_g_2030` LIKE `db_dreame_goods`.`t_orefund_g_2022`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `oprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原价（分）',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实付款（分）',
  `fprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '运费（分）',
  `coupon_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券ID',
  `cprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券使用金额（分）',
  `coin` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用货币数',
  `coin_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '货币抵扣金额（分）',
  `coin_logid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '积分使用log_id',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 0未付款 1付款 2订单已完成 3 订单超时',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '下单时间',
  `pay_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付款时间',
  `finish_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '完成时间',
  `cr_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '取消原因',
  `note` varchar(500) CHARACTER SET utf8mb4 NOT NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `order_no` (`order_no`) USING BTREE,
  KEY `i_u_s` (`user_id`,`status`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单表';




CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_1` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_2` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_3` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_4` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_5` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_6` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_7` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_8` LIKE `db_dreame_goods`.`t_uo_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_9` LIKE `db_dreame_goods`.`t_uo_0`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `nick` varchar(30) NOT NULL DEFAULT '' COMMENT '姓名',
  `phone` varchar(20) NOT NULL DEFAULT '' COMMENT '手机号',
  `pid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '省',
  `cid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '市',
  `aid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '区',
  `detail` varchar(500) NOT NULL DEFAULT '' COMMENT '详细地址',
  `address` varchar(500) CHARACTER SET utf8mb4 NOT NULL COMMENT '收获地址(json数据、冗余数据)',
  `mail_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流单号',
  `express_code` varchar(50) NOT NULL DEFAULT '' COMMENT '物流公司代码',
  `express_name` varchar(50) NOT NULL DEFAULT '' COMMENT '物流公司名称',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_o` (`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单收发货信息';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2023` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2024` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2025` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2026` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2027` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2028` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2029` LIKE `db_dreame_goods`.`t_uo_ad_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_ad_2030` LIKE `db_dreame_goods`.`t_uo_ad_2022`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `sid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '规格id',
  `cfg` text NOT NULL COMMENT '商品关键信息拷贝',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_ogs` (`order_no`,`gid`,`sid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='商品信息拷贝';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2023` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2024` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2025` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2026` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2027` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2028` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2029` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_cfg_2030` LIKE `db_dreame_goods`.`t_uo_cfg_2022`;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `order_no` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `sub_no` varchar(50) CHARACTER SET latin1 COLLATE latin1_general_ci NOT NULL DEFAULT '' COMMENT '子订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品ID',
  `sid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '规格id',
  `num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
  `oprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '原价（分）',
  `price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '实付款（分）',
  `cprice` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '优惠券使用金额（分）',
  `coin` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '使用货币数',
  `coin_price` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '货币抵扣金额（分）',
  `status` int(10) unsigned NOT NULL DEFAULT '10000' COMMENT '状态(...)',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `i_o` (`order_no`) USING BTREE,
  KEY `i_u_g` (`user_id`,`gid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_1` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_2` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_3` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_4` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_5` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_6` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_7` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_8` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_9` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_10` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_11` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_12` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_13` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_14` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_15` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_16` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_17` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_18` LIKE `db_dreame_goods`.`t_uo_g_0`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_uo_g_19` LIKE `db_dreame_goods`.`t_uo_g_0`;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_ofreight` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '1:满免运费 2：默认运费 3：运费地区配置 4：不可配送区域',
  `cnf` varchar(200) NOT NULL DEFAULT '' COMMENT '配置（金额或者备注）',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_of_cfg` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '类型（3：区域运费4：不可配送区域）',
  `of_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '运费表id',
  `pid` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '省id',
  `cid` bigint(10) unsigned NOT NULL DEFAULT '0' COMMENT '市id',
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_t_c` (`type`,`cid`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=49 DEFAULT CHARSET=utf8;

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2022` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ouid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '导购员id',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户id',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `status` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '状态同订单状态',
  `price` int(10) NOT NULL COMMENT '订单金额（分）',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `u_o` (`order_no`),
  KEY `i_ouid` (`ouid`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COMMENT='订单来源-导购';

CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2023` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2024` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2025` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2026` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2027` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2028` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2029` LIKE `db_dreame_goods`.`t_osource_2022`;
CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_osource_2030` LIKE `db_dreame_goods`.`t_osource_2022`;


CREATE TABLE IF NOT EXISTS `db_dreame_goods`.`t_ofinish` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(50) COLLATE latin1_general_ci NOT NULL DEFAULT '',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `i_o` (`order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=latin1 COLLATE=latin1_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单收货表（收货7天给积分）';

INSERT INTO `db_dreame_goods`.`t_ofreight` (`id`, `type`, `cnf`) VALUES (1, 1, '9900');
INSERT INTO `db_dreame_goods`.`t_ofreight` (`id`, `type`, `cnf`) VALUES (2, 2, '800');

CREATE TABLE IF NOT EXISTS `db_dreame_goods`. `t_gtc` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `tc_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '套餐码',
  `tc_sku` varchar(50) NOT NULL DEFAULT '' COMMENT '套餐sku',
  `sl` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '套餐商品数量',
  `sku` varchar(50) NOT NULL DEFAULT '' COMMENT '商品sku',
  `ctime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tc_unq` (`tc_sn`,`tc_sku`,`sku`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=COMPACT COMMENT='套餐商品关系表';

CREATE TABLE if NOT EXISTS `db_dreame_goods`.`t_cart_0` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `gid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品id',
  `sid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '属性id',
  `num` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '数量',
  `c_time` int(10) unsigned NOT NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_u_g_s_b` (`user_id`,`gid`,`sid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='购物车表';

create table `db_dreame_goods`.`t_cart_1` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_2` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_3` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_4` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_5` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_6` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_7` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_8` like `db_dreame_goods`.`t_cart_0`;
create table `db_dreame_goods`.`t_cart_9` like `db_dreame_goods`.`t_cart_0`;

