<?php

/**
 * @OA\Schema(
 *   schema="WaresGoodsEnumRequest",type="object",
 *   required = {"keyword"},
 *   @OA\Property(property="keyword", type="string",description="字段名,type商品类型,label 商品标签,tab 商品TAB,position 商品推荐位,online_time 周期上架,levels 用户等级"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresListRequest",type="object",
 *   required = {"source"},
 *   @OA\Property(property="source", type="integer",description="1：普通商品； 2：积分商品"),
 *   @OA\Property(property="position", type="integer",description="1：推荐位； 2：热门"),
 *   @OA\Property(property="tab", type="integer",description="1：新品；"),
 *   @OA\Property(property="sort_rule", type="json",description="{'type':'price','act':'up'} type: price,价格 point,积分 act:排序，up升序 down降序"),
 *   @OA\Property(property="is_point", type="integer",description=">1:仅积分 0：不是仅积分"),
 *   @OA\Property(property="is_my", type="integer",description=">1:我能兑换 0：所有"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresAttrStockRequest",type="object",
 *   required = {"gid","av_ids"},
 *   @OA\Property(property="gid", type="integer",description="商品ID"),
 *   @OA\Property(property="av_ids", type="json",description="规格值，例:[1]"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresAttrStockResponse",type="object",
 *   @OA\Property(property="id", type="integer",description="特征ID"),
 *   @OA\Property(property="gid", type="integer",description="商品ID"),
 *   @OA\Property(property="at_name", type="string",description="特征名称"),
 *   @OA\Property(property="val", type="object",ref="#/components/schemas/WaresAttrValueResponse",description="特征值数据")
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresAttrValueResponse",type="object",
 *   @OA\Property(property="id", type="integer",description="特征值ID"),
 *   @OA\Property(property="ak_id", type="integer",description="特征ID"),
 *   @OA\Property(property="at_val", type="string",description="特征值名称"),
 *   @OA\Property(property="image", type="string",description="特征值图片"),
 *   @OA\Property(property="in_stock", type="string",description="是否有库存，1有库存 0 没有库存"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresSpecsInfoRequest",type="object",
 *   required = {"gid","av_ids","num"},
 *   @OA\Property(property="gid", type="integer",description="商品ID"),
 *   @OA\Property(property="av_ids", type="json",description="规格值，例:[1]"),
 *   @OA\Property(property="num", type="integer",description="购买商品数量"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresSpecsInfoResponse",type="object",
 *   @OA\Property(property="id", type="integer",description="特征值ID"),
 *   @OA\Property(property="av_ids", type="string",description="特征值集合"),
 *   @OA\Property(property="exchange", type="integer",description="1：积分+钱 2：全积分 3：优惠券"),
 *   @OA\Property(property="price", type="string",description="price 价格"),
 *   @OA\Property(property="point", type="string",description="point 积分"),
 *   @OA\Property(property="coupon_id", type="string",description="coupon_id 优惠券ID"),
 *   @OA\Property(property="image", type="string",description="图片"),
 *   @OA\Property(property="num", type="integer",description="购买数量"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresAttrInfoRequest",type="object",
 *   required = {"gid"},
 *   @OA\Property(property="gid", type="integer",description="商品ID"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresOrderBuyInfoRequest",type="object",
 *   required = {"gcombines","aid"},
 *   @OA\Property(property="gcombines", type="json",description="商品组合：[{'gid':159,'sid':'0','num':1}]"),
 *   @OA\Property(property="aid", type="integer",description="用户地址ID"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresOrderBuyRequest",type="object",
 *   required = {"gcombines","pay_source"},
 *   @OA\Property(property="gcombines", type="json",description="商品组合：[{'gid':159,'sid':'0','num':1}]"),
 *   @OA\Property(property="pay_source", type="integer",description="1:会员商品订单,2：上下水订单,3.定金订单,4.积分商城订单"),
 *   @OA\Property(property="aid", type="integer",description="用户地址ID"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresOrderPayRequest",type="object",
 *   required = {"order_no","pay_source","pay_type"},
 *   @OA\Property(property="order_no", type="string",description="订单编号"),
 *   @OA\Property(property="pay_source", type="integer",description="1:会员商品订单,2：上下水订单,3.定金订单,4.积分商城订单,5.先试后买订单"),
 *   @OA\Property(property="pay_type", type="integer",description="1:小程序支付，2：微信H5支付，6：支付宝芝麻信用支付"),
 *   @OA\Property(property="aid", type="integer",description="用户地址ID"),
 * )
 */
