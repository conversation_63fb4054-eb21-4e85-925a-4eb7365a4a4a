<?php

/**
 * @OA\Schema(
 *   schema="ProductListResponse",type="object",
 *   @OA\Property(property="apply_no", type="string",description="保修卡申请单号"),
 *   @OA\Property(property="apply_status", type="string",description="保修卡申请状态（-1 暂无申请记录，0申请中、1已通过、3未通过）"),
 *   @OA\Property(property="apply_show", type="integer",description="是否显示状态（1 显示 0 不显示）"),
 *   @OA\Property(property="buy_time", type="integer",description="购买时间"),
 *   @OA\Property(property="card_no", type="string",description="保修卡号"),
 *   @OA\Property(property="card_status", type="string",description="保修卡状态 0 可申请 1 申请中 2 已完成"),
 *   @OA\Property(property="card_year", type="string",description="保修卡生成年份"),
 *   @OA\Property(property="create_time", type="string",description="创建时间"),
 *   @OA\Property(property="image", type="string",description="图片"),
 *   @OA\Property(property="is_apply", type="string",description="是否申请"),
 *   @OA\Property(property="is_card", type="string",description="是否有保修卡"),
 *   @OA\Property(property="name", type="string",description="产品名称"),
 *   @OA\Property(property="period_time", type="string",description="保修卡截至时间"),
 *   @OA\Property(property="product_id", type="string",description="产品ID"),
 *   @OA\Property(property="reg_id", type="string",description="注册ID"),
 *   @OA\Property(property="sn", type="string",description="sn编号"),
 *   @OA\Property(property="sn_time", type="string",description="sn解析时间"),
 *   @OA\Property(property="word", type="string",description="提示"),
 *   @OA\Property(property="act", type="integer",description="0不参与 1参与"),
 *   @OA\Property(property="part_info", type="object",ref="#/components/schemas/PartInfo",description="配件信息")
 * )
 */

/**
 * @OA\Schema(
 *   schema="PartInfo",type="object",
 *   @OA\Property(property="compare_sku", type="string",description="对比SKU"),
 *   @OA\Property(property="id", type="string",description="关联ID"),
 *   @OA\Property(property="is_compare", type="integer",description="是否有对比信息 0 无 1 有"),
 *   @OA\Property(property="name", type="string",description="配件名称"),
 *   @OA\Property(property="card_status", type="string",description="保修卡状态 0 可申请 1 申请中 2 已完成"),
 *   @OA\Property(property="part_id", type="string",description="配件ID"),
 *   @OA\Property(property="reminder_copy", type="string",description=""),
 *   @OA\Property(property="short_code", type="string",description=""),
 *   @OA\Property(property="sku", type="string",description="SKU"),
 *   @OA\Property(property="sort", type="string",description="排序"),
 *   @OA\Property(property="ctime", type="string",description="创建时间")
 * )
 */







