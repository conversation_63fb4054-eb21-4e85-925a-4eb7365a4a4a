<?php


/**
 * @OA\Schema(
 *   schema="DataUploadFile",
 *   required = {"file"},
 *        @OA\Property(property="file", type="file", description="上传文件"),
 *        @OA\Property(property="sn", type="string", description="以旧换新-必传"),
 *        @OA\Property(property="type", type="string", default="COMM",description="类型，COMM：通用，默认 ；OLD_TO_NEW：以旧换新"),
 * )
 */


 /**
 * @OA\Schema(
 *   schema="AlipayPhoneAuth",
 *   required = {"encryptedData"},
 *        @OA\Property(property="encryptedData", type="string", description="手机加密字符串"),
 * )
 */

/**
 * @OA\Schema(
 *      schema="YearReportDataResponse",type="object",
 *        @OA\Property(property="purchase_nums", type="string", description="购买次数"),
 *        @OA\Property(property="purchase_sum_price", type="string", description="购买总金额"),
 *        @OA\Property(property="purchase_coupon_nums", type="string",description="优惠券数量"),
 *        @OA\Property(property="purchase_points", type="string",description="积分总数"),
 *        @OA\Property(property="purchase_discount_price", type="string",description="优惠总金额"),
 *        @OA\Property(property="double11_check_nums", type="string",description="双11打卡次数"),
 *        @OA\Property(property="checkin_rate", type="string",description="打卡比率（超越多少人）"),
 *        @OA\Property(property="double11_draw_nums", type="string",description="双11抽奖次数"),
 *        @OA\Property(property="double11_flash_nums", type="string",description="双11秒杀次数"),
 *        @OA\Property(property="double11_sum_points", type="string",description="双11积分总数"),
 *        @OA\Property(property="double11_flash_goods", type="string",description="秒杀商品名"),
 *        @OA\Property(property="product_dry_name", type="string",description="产品注册吹风机名称"),
 *        @OA\Property(property="product_cleaner_name", type="string",description="产品注册名称"),
 *        @OA\Property(property="product_reg_num", type="string",description="产品注册数量（购买件数）"),
 *        @OA\Property(property="product_reg_name", type="string",description="产品注册 产品名称"),
 *        @OA\Property(property="firstCleanModel", type="string",description="首次清扫的model"),
 *        @OA\Property(property="firstCleanModelName", type="string",description="首次清扫的model名称"),
 *        @OA\Property(property="firstCleanTime", type="string",description="首次清扫时间"),
 *        @OA\Property(property="activeTime", type="string",description="激活时间"),
 *        @OA\Property(property="activeRankNum", type="string",description="激活排名"),
 *        @OA\Property(property="activeModel", type="string",description="激活的model"),
 *        @OA\Property(property="activeModelName", type="string",description="激活model的名称"),
 *        @OA\Property(property="yearCs", type="string",description="首次使用的model全年面积"),
 *        @OA\Property(property="yearCt", type="string",description="首次使用的model全年时长"),
 *        @OA\Property(property="ctMaxModel", type="string",description="清扫时间最长的model"),
 *        @OA\Property(property="ctMaxModelName", type="string",description="清扫时间最长的model名称"),
 *        @OA\Property(property="ctMaxYearCs", type="string",description="清扫时间最长model全年最大面积"),
 *        @OA\Property(property="ctMaxYearCt", type="string",description="清扫时间最长model全年最大时间"),
 *        @OA\Property(property="earliestCleanTime", type="string",description="最早清扫时间"),
 *        @OA\Property(property="isEarliest", type="string",description="最早算 早上05:00-12:00 符合的1 不符合0"),
 *        @OA\Property(property="defeatUserPercent", type="string",description="当天清扫时间击败人数比例"),
 *        @OA\Property(property="latestCleanTime", type="string",description="最晚清扫时间"),
 *        @OA\Property(property="isLatest", type="string",description="最晚 深夜 18:00-05:00 符合的1 不符合0"),
 *        @OA\Property(property="dayCtLargeTime", type="string",description="清扫时长最大的日期"),
 *        @OA\Property(property="dayCtLargeCleanNum", type="string",description="清扫时长最大那天的清扫次数"),
 *        @OA\Property(property="dayCtLargeTotalCs", type="string",description="清扫时长最大那天的清扫面积"),
 *        @OA\Property(property="dayCsLargeTime", type="string",description="面积最大日期"),
 *        @OA\Property(property="dayCsTotalCs", type="string",description="面积最大那天总的面积"),
 *        @OA\Property(property="together_time", type="string",description="相伴时间"),
 *        @OA\Property(property="nick", type="string",description="用户名"),
 *        @OA\Property(property="milk_tea", type="string",description="x杯奶茶"),
 *        @OA\Property(property="add_card_date", type="string",description="加入购物车时间(年-月-日)"),
 *        @OA\Property(property="add_card_number", type="string",description="加入购物车数量"),
 *        @OA\Property(property="latest_purchase_time", type="string",description="最晚下单时间"),
 *        @OA\Property(property="keyword", type="string",description="关键词"),
 *        @OA\Property(property="serial_number", type="string",description="性格序号"),
 *        @OA\Property(property="economize_time", type="string",description="节省了X天（总使用时间）家务时间"),
 *        @OA\Property(property="level_name", type="string",description="用户等级"),
 * )
 */