<?php

/**
 * @OA\Schema(
 *   schema="WarrantyApplyAdd",type="object",
 *   @OA\Property(property="reg_id", type="string",description="注册ID"),
 *   @OA\Property(property="buy_time", type="string",description="购买时间"),
 *   @OA\Property(property="purchase_platform", type="integer",description="购买平台，根据平台列表参数填入"),
 *   @OA\Property(property="order_no", type="string",description="订单号"),
 *   @OA\Property(property="purchase_cert", type="string",description="购买凭证，按英文逗号分割")
 * )
 */

/**
 * @OA\Schema(
 *   schema="WarrantyApplyActive",type="object",
 *   @OA\Property(property="reg_id", type="string",description="注册ID")
 * )
 */


/**
 * @OA\Schema(
 *   schema="WarrantyApplyPlatformListResponse",type="object",
 *   @OA\Property(property="code", type="string",description="平台编号"),
 *   @OA\Property(property="name", type="string",description="平台名称")
 * )
 */


/**
 * @OA\Schema(
 *   schema="WarrantyApplyUpload",type="object",
 *   @OA\Property(property="reg_id", type="string",description="注册ID"),
 *   @OA\Property(property="file", type="string",description="上传图片数据")
 * )
 */

/**
 * @OA\Schema(
 *   schema="WarrantyApplyRulesResponse",type="object",
 *   @OA\Property(property="images", type="object",ref="#/components/schemas/WarrantyApplyRulesImagesResponse",description="图片信息")
 * )
 */

/**
 * @OA\Schema(
 *   schema="WarrantyApplyRulesImagesResponse",type="object",
 *   @OA\Property(property="order", type="string",description="订单示例"),
 *   @OA\Property(property="cert", type="string",description="购买凭证"),
 *   @OA\Property(property="rules", type="string",description="质保说明"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WarrantyApplyHideRequest",type="object",
 *   @OA\Property(property="apply_no", type="string",description="申请单号")
 * )
 */
