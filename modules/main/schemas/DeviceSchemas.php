<?php


/**
 * @OA\Schema(
 *   schema="DeviceRequest",type="object",
 *   @OA\Property(property="scene", type="integer", default="",description="场景值（LIST_V2、GET_DEVICE、DELETE_DEVICE、SEND_COMMAND、MARK_READ）"),
 *   @OA\Property(property="sharedStatus", type="integer", default="1",description="分享状态0 未确认，1 已确认 (LIST_V2传)"),
 *   @OA\Property(property="lang", type="integer",default="zh", description="语言编码 (LIST_V2传)"),
 *   @OA\Property(property="offset", type="integer", description="偏移量 (LIST_V2、GET_DEVICE 传)"),
 *   @OA\Property(property="limit", type="string",description="显示条数  (LIST_V2、GET_DEVICE 传)"),
 *   @OA\Property(property="did", type="string",description="设备did （GET_DEVICE、SEND_COMMAND传）"),
 *   @OA\Property(property="bind_id", type="string",description="设备列表接口设备的bindDomain值取得 （SEND_COMMAND传）"),
 *   @OA\Property(property="msgIds", type="string",description="msgIds （DELETE_DEVICE 必传）"),
 *   @OA\Property(property="deviceId", type="string",description="deviceId （DELETE_DEVICE、MARK_READ 传）"),
 *   @OA\Property(property="id", type="string",description="id （SEND_COMMAND 传）"),
 *   @OA\Property(property="data", type="string",description="data （SEND_COMMAND 传）")
 * )
 */




