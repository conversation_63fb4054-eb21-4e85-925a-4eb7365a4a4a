<?php


/**
 * @OA\Schema(
 *     schema="ActivityBaseInfo",
 *     type="object",
 *     description="活动基础信息",
 *     @OA\Property(property="id", type="string", example="142", description="活动ID"),
 *     @OA\Property(property="title", type="string", example="测试活动123123", description="活动标题"),
 *     @OA\Property(property="description", type="string", example="这是一个副标题666612", description="活动副标题"),
 *     @OA\Property(property="rule", type="string", example="大法师打发斯蒂芬...", description="活动规则"),
 *     @OA\Property(
 *         property="cycle_type",
 *         type="string",
 *         example="once",
 *         enum={"once", "daily", "weekly", "monthly"},
 *         description="活动周期类型"
 *     ),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="活动开始时间戳"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="活动结束时间戳")
 * )
 */

/**
 * @OA\Schema(
 *     schema="BigTitleModule",
 *     description="大标题模块",
 *     @OA\Property(property="id", type="string", example="270", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="1", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="模块大标题", description="模块主标题内容"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或摘要文案"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（时间戳，单位秒）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（时间戳，单位秒）"),
 *     @OA\Property(property="extra", type="string", example=null, nullable=true, description="模块扩展字段（可为结构化 JSON 或空）"),
 *     @OA\Property(property="module_name", type="string", example="大标题模块", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="BIG_TITLE", description="模块编码，用于识别模块类型")
 * )
 */

/**
 * @OA\Schema(
 *     schema="CheckinModule",
 *     description="积分打卡模块",
 *
 *     @OA\Property(property="id", type="string", example="271", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="2", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="积分打卡", description="模块主标题内容"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或摘要文案"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（时间戳，单位秒）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（时间戳，单位秒）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="模块扩展信息，包含积分打卡详细配置与用户打卡状态",
 *         @OA\Property(property="code", type="integer", example=1, description="状态码，1 表示正常"),
 *
 *         @OA\Property(
 *             property="list",
 *             type="array",
 *             description="打卡日期列表",
 *             @OA\Items(
 *                 @OA\Property(property="day", type="string", example="2025-04-21", description="打卡日期（格式：Y-m-d）"),
 *                 @OA\Property(property="dayView", type="string", example="04.21", description="展示用的日期格式（MM.DD）"),
 *                 @OA\Property(property="status", type="integer", example=0, description="打卡状态：0 未打卡，1 已打卡"),
 *                 @OA\Property(property="statusTag", type="integer", example=0, description="状态标签：0 普通，1 已打卡，2 达成目标奖励"),
 *                 @OA\Property(property="numValue", type="integer", example=100, description="该日可获取的积分数量"),
 *                 @OA\Property(property="canCheck", type="integer", example=0, description="是否允许打卡：0 否，1 是")
 *             )
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="积分打卡", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="CHECKIN", description="模块编码，用于识别模块类型")
 * )
 */


/**
 * @OA\Schema(
 *     schema="ReturnPointsModule",
 *     description="购物返积分模块",
 *
 *     @OA\Property(property="id", type="string", example="272", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="3", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="购物返积分", description="模块主标题内容"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或摘要文案"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（时间戳，单位秒）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（时间戳，单位秒）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="模块扩展信息，包含返积分配置",
 *         @OA\Property(property="return_start_time", type="integer", example=1744356288000, description="返积分开始时间（时间戳，单位毫秒）"),
 *         @OA\Property(property="return_end_time", type="integer", example=1746860916000, description="返积分结束时间（时间戳，单位毫秒）"),
 *         @OA\Property(property="return_multiple", type="integer", example=1, description="返积分倍数"),
 *         @OA\Property(property="member_center_save_id", type="string", example="1914175009022205953", description="会员中心跳转保存配置ID")
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="购物返积分", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="RETURN_POINTS", description="模块编码，用于识别模块类型")
 * )
 */


/**
 * @OA\Schema(
 *     schema="AdPositionModule",
 *     description="广告位模块",
 *
 *     @OA\Property(property="id", type="string", example="273", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="4", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="广告位", description="模块主标题内容"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或摘要文案"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（时间戳，单位秒）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（时间戳，单位秒）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="模块扩展信息，包含广告按钮配置",
 *         @OA\Property(property="ad_btn_name", type="string", example="点我团购", description="广告按钮名称"),
 *         @OA\Property(property="ad_url", type="string", example="http://www.baidu.com", description="广告跳转链接")
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="广告位", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="AD_POSITION", description="模块编码，用于识别模块类型")
 * )
 */


/**
 * @OA\Schema(
 *     schema="DrawModule",
 *     description="抽奖活动模块",
 *
 *     @OA\Property(property="id", type="string", example="274", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="5", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="抽奖活动0421123", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块摘要/副标题"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix 时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix 时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="抽奖模块扩展字段",
 *
 *         @OA\Property(property="draw_start_time", type="integer", example=1744356288, description="抽奖开始时间（Unix 时间戳）"),
 *         @OA\Property(property="draw_end_time", type="integer", example=1746860916, description="抽奖结束时间（Unix 时间戳）"),
 *
 *         @OA\Property(
 *             property="draw_task",
 *             type="array",
 *             description="任务列表，用于获取抽奖机会",
 *             @OA\Items(
 *                 @OA\Property(property="task_id", type="integer", example=1, description="任务ID"),
 *                 @OA\Property(property="task_name", type="string", example="每天免费抽奖任务", description="任务名称"),
 *                 @OA\Property(property="task_code", type="string", example="DAILY_FREE", description="任务编码"),
 *                 @OA\Property(property="task_desc", type="string", example="每人每天免费获得1次参与机会，当日不用就清零", description="任务描述"),
 *                 @OA\Property(property="task_limit", type="integer", example=1, description="任务次数限制"),
 *                 @OA\Property(property="task_limit_type", type="integer", example=1, description="任务限制类型（例如每日、每周等）"),
 *                 @OA\Property(
 *                     property="extra",
 *                     type="object",
 *                     description="任务扩展字段",
 *                     @OA\Property(property="gift_times", type="integer", example=1, description="完成任务后可获得的抽奖次数")
 *                 )
 *             )
 *         ),
 *
 *         @OA\Property(
 *             property="draw_prize",
 *             type="array",
 *             description="奖品列表",
 *             @OA\Items(
 *                 @OA\Property(property="prize_type", type="integer", example=1, description="奖品类型"),
 *                 @OA\Property(property="prize_name", type="string", example="谢谢参与", description="奖品名称"),
 *                 @OA\Property(property="prize_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="奖品图片 URL")
 *             )
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="抽奖活动", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="DRAW", description="模块编码")
 * )
 */


/**
 * @OA\Schema(
 *     schema="NewPersonModule",
 *     description="新人入会福利模块",
 *
 *     @OA\Property(property="id", type="string", example="275", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="6", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="新人入会福利", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或简要说明"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="扩展信息，包括优惠券和积分福利",
 *
 *         @OA\Property(
 *             property="coupon",
 *             type="array",
 *             description="优惠券列表",
 *             @OA\Items(
 *                 @OA\Property(property="web_name", type="string", example="测试2", description="前端展示名称"),
 *                 @OA\Property(property="type", type="string", example="1", description="优惠券类型"),
 *                 @OA\Property(property="market_id", type="integer", example=1004, description="券市场ID"),
 *                 @OA\Property(property="valid_type", type="string", example="1", description="有效期类型"),
 *                 @OA\Property(property="valid_val", type="string", example="2025-03-12 00:00:00~2025-04-30 00:00:00", description="有效期值"),
 *                 @OA\Property(property="resource_type", type="string", example="2", description="资源类型"),
 *                 @OA\Property(property="jump_goods_id", type="integer", example=0, description="跳转商品ID"),
 *                 @OA\Property(property="use_rule_note", type="string", example="", description="使用规则备注"),
 *                 @OA\Property(property="goods_type", type="string", example="1", description="适用商品类型"),
 *                 @OA\Property(property="goods_val", type="string", example="", description="适用商品值"),
 *                 @OA\Property(property="discount", type="integer", example=1, description="折扣金额"),
 *                 @OA\Property(property="type_name", type="string", example="优惠券", description="优惠券类型名称"),
 *                 @OA\Property(property="resource_name", type="string", example="1元优惠券", description="优惠券名称"),
 *                 @OA\Property(property="condition", type="string", example="无门槛", description="使用条件"),
 *                 @OA\Property(property="condition_scope", type="string", example="全场通用", description="适用范围"),
 *                 @OA\Property(property="start_time", type="integer", example=1741708800, description="开始时间（Unix时间戳）"),
 *                 @OA\Property(property="expire_time", type="integer", example=1745942400, description="到期时间（Unix时间戳）"),
 *                 @OA\Property(property="surplus_num", type="string", example="50", description="剩余数量")
 *             )
 *         ),
 *
 *         @OA\Property(
 *             property="point",
 *             type="object",
 *             description="积分奖励配置",
 *             @OA\Property(property="point_multiplier", type="string", example="2", description="积分倍数"),
 *             @OA\Property(property="point_value", type="string", example="2000", description="赠送积分数量"),
 *             @OA\Property(property="deductible_price", type="string", example="20.00", description="积分可抵扣金额")
 *         ),
 *
 *         @OA\Property(property="has_coupon", type="string", example="1", description="是否包含优惠券：1=是，0=否"),
 *         @OA\Property(property="has_point", type="string", example="1", description="是否包含积分：1=是，0=否")
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="新人入会福利", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="NEW_PERSON", description="模块编码")
 * )
 */


/**
 * @OA\Schema(
 *     schema="RecommendGoodsModule",
 *     description="机器推荐模块",
 *
 *     @OA\Property(property="id", type="string", example="276", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="7", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="机器推荐", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或简要说明"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="array",
 *         description="扩展信息，包括推荐商品列表",
 *         @OA\Items(
 *             @OA\Property(property="name", type="string", example="扫地机", description="商品分类名称"),
 *             @OA\Property(property="tid", type="string", example="1", description="分类ID"),
 *             @OA\Property(
 *                 property="arr",
 *                 type="array",
 *                 description="推荐商品详情",
 *                 @OA\Items(
 *                     @OA\Property(property="module_type", type="integer", example=1, description="模块类型，标识商品分类"),
 *                     @OA\Property(property="goods_id", type="integer", example=1, description="商品ID"),
 *                     @OA\Property(property="goods_name", type="string", example="测试", description="商品名称"),
 *                     @OA\Property(property="goods_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="商品图片URL"),
 *                     @OA\Property(property="goods_sku", type="string", example="testsku123", description="商品SKU"),
 *                     @OA\Property(property="sale_price", type="string", example="9.99", description="商品销售价格"),
 *                     @OA\Property(property="free_periods", type="integer", example=12, description="免费期数（如分期）"),
 *                     @OA\Property(property="trade_subsidy", type="string", example="99.99", description="商品补贴价格"),
 *                     @OA\Property(property="sort_order", type="integer", example=0, description="商品排序权重"),
 *                     @OA\Property(property="can_point", type="integer", example=299, description="商品是否可以积分购买：0=否，1=是"),
 *                     @OA\Property(property="point_price", type="string", example="2.99", description="商品积分兑换价格"),
 *                     @OA\Property(property="every_issue_price", type="string", example="0.58", description="每期商品的价格（如分期付款）")
 *                 )
 *             )
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="机器推荐", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="RECOMMEND_GOODS", description="模块编码")
 * )
 */


/**
 * @OA\Schema(
 *     schema="CouponModule",
 *     description="优惠券模块",
 *
 *     @OA\Property(property="id", type="string", example="277", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="8", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="优惠券", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或简要说明"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="object",
 *         description="扩展信息，包含优惠券相关的活动和优惠券详情",
 *         @OA\Property(property="coupon_activity_id", type="integer", example=1, description="优惠券活动ID"),
 *         @OA\Property(
 *             property="coupon",
 *             type="array",
 *             description="优惠券列表",
 *             @OA\Items(
 *                 @OA\Property(property="coupon_id", type="string", example="12345", description="优惠券ID"),
 *                 @OA\Property(property="coupon_name", type="string", example="满减优惠券", description="优惠券名称"),
 *                 @OA\Property(property="discount", type="string", example="10", description="优惠券折扣"),
 *                 @OA\Property(property="valid_from", type="string", example="2025-04-01 00:00:00", description="优惠券有效开始时间"),
 *                 @OA\Property(property="valid_to", type="string", example="2025-04-30 23:59:59", description="优惠券有效结束时间"),
 *                 @OA\Property(property="min_purchase", type="string", example="50", description="使用优惠券的最低消费金额"),
 *                 @OA\Property(property="coupon_type", type="string", example="discount", description="优惠券类型，如折扣券、满减券等")
 *             )
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="优惠券", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="COUPON", description="模块编码")
 * )
 */


/**
 * @OA\Schema(
 *     schema="PartGoodsModule",
 *     description="配件专区模块",
 *
 *     @OA\Property(property="id", type="string", example="278", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="9", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="配件专区", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或简要说明"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="array",
 *         description="配件专区模块的扩展信息，包含配件类型及其商品列表",
 *         @OA\Items(
 *             @OA\Property(property="name", type="string", example="扫地机", description="配件类型名称"),
 *             @OA\Property(property="tid", type="string", example="1", description="配件类型ID"),
 *             @OA\Property(
 *                 property="arr",
 *                 type="array",
 *                 description="配件商品列表",
 *                 @OA\Items(
 *                     @OA\Property(property="module_type", type="integer", example=2, description="商品模块类型"),
 *                     @OA\Property(property="goods_id", type="integer", example=1, description="商品ID"),
 *                     @OA\Property(property="goods_name", type="string", example="测试", description="商品名称"),
 *                     @OA\Property(property="goods_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="商品图片URL"),
 *                     @OA\Property(property="goods_sku", type="string", example="testsku123", description="商品SKU"),
 *                     @OA\Property(property="sale_price", type="string", example="9.99", description="商品售价"),
 *                     @OA\Property(property="show_name", type="string", example="asdfasdfasf", description="显示名称"),
 *                     @OA\Property(property="sort_order", type="integer", example=0, description="排序编号"),
 *                     @OA\Property(
 *                         property="extra",
 *                         type="object",
 *                         description="商品额外信息",
 *                         @OA\Property(property="type", type="integer", example=1, description="商品类型"),
 *                         @OA\Property(property="start_time", type="string", example="09:00:00", description="商品开始时间"),
 *                         @OA\Property(property="end_time", type="string", example="18:00:00", description="商品结束时间")
 *                     ),
 *                     @OA\Property(property="status", type="integer", example=3, description="商品状态"),
 *                     @OA\Property(property="status_tag", type="string", example="已售罄", description="商品状态标签")
 *                 )
 *             )
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="配件专区", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="PART_GOODS", description="模块编码")
 * )
 */


/**
 * @OA\Schema(
 *     schema="PointsExchangeModule",
 *     description="积分兑换模块",
 *
 *     @OA\Property(property="id", type="string", example="279", description="模块唯一ID"),
 *     @OA\Property(property="activity_id", type="string", example="142", description="所属活动ID"),
 *     @OA\Property(property="module_res_id", type="string", example="10", description="模块资源ID"),
 *     @OA\Property(property="title", type="string", example="积分兑换", description="模块标题"),
 *     @OA\Property(property="summary", type="string", example="文案文案啊文案", description="模块副标题或简要说明"),
 *     @OA\Property(property="start_time", type="string", example="1744356288", description="模块开始时间（Unix时间戳）"),
 *     @OA\Property(property="end_time", type="string", example="1746860916", description="模块结束时间（Unix时间戳）"),
 *
 *     @OA\Property(
 *         property="extra",
 *         type="array",
 *         description="积分兑换模块的扩展信息，包含商品信息和积分兑换的相关信息",
 *         @OA\Items(
 *             @OA\Property(property="module_type", type="integer", example=3, description="模块类型"),
 *             @OA\Property(property="category_id", type="integer", example=1, description="商品分类ID"),
 *             @OA\Property(property="category_name", type="string", example="扫地机", description="商品分类名称"),
 *             @OA\Property(property="goods_id", type="integer", example=1, description="商品ID"),
 *             @OA\Property(property="goods_name", type="string", example="测试", description="商品名称"),
 *             @OA\Property(property="goods_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="商品图片URL"),
 *             @OA\Property(property="goods_sku", type="string", example="testsku123", description="商品SKU"),
 *             @OA\Property(property="sale_price", type="string", example="9.99", description="商品售价"),
 *             @OA\Property(property="sale_points", type="integer", example=100, description="兑换该商品所需的积分数"),
 *             @OA\Property(property="sort_order", type="integer", example=0, description="商品排序编号"),
 *             @OA\Property(property="status", type="integer", example=1, description="商品状态"),
 *             @OA\Property(property="status_tag", type="string", example="参数错误", description="商品状态标签")
 *         )
 *     ),
 *
 *     @OA\Property(property="module_name", type="string", example="积分兑换", description="模块名称"),
 *     @OA\Property(property="module_code", type="string", example="POINTS_EXCHANGE", description="模块编码")
 * )
 */
