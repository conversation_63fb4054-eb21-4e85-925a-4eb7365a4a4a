<?php


/**
 * @OA\Schema(
 *   schema="BaseRequest",
 *   required = {"api","sign","sign_time"},
 *        @OA\Property(property="api", type="string", default="a_1643178000",description="api平台 必传；安卓 'a_1664246268'，后台：'p_1643028263' ，PC：p_1712037068"),
 *        @OA\Property(property="sign", type="string", default="test-environment",description="签名"),
 *        @OA\Property(property="sign_time", type="string", default="test-time",description="签名时间"),
 *        @OA\Property(property="version", type="string", default="2.1.3",description="小程序版本"),
 *        @OA\Property(property="provider", type="string", default="test",description="测试环境-是否核验签名"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="BaseOrderRequest",
 *          @OA\Property(property="union", type="string", default="", description="广告推广来源"),
 *          @OA\Property(property="euid", type="string", default="", description="广告推广来源euid"),
 *          @OA\Property(property="is_internal_purchase", type="integer", default="0", description="是否是内购"),
 *          @OA\Property(property="cps", type="string", default="", description="推广二维码参数"),
 *          @OA\Property(property="referer", type="string", default="", description="来源链接"),
 * )
 */


/**
* @OA\Schema(
*   schema="BaseResponse",
*   @OA\Property(property="id", type="integer", format="int64",description="ID自增"),
*   @OA\Property(property="market_id", type="integer",description="优惠券ID"),
*   @OA\Property(property="email", type="string")
* )
*/


/**
 * @OA\Schema(
 *   schema="PageRequest",
 *        @OA\Property(property="page", type="integer", default="1",description="当前页，默认1"),
 *        @OA\Property(property="page_size", type="integer", default="20",description="页数，默认20"),
 * )
 */
