<?php

/**
 * @OA\Schema(
 *   schema="NearRetailersRequest",type="object",
 *   required = {"pos_l","pos_b"},
 *   @OA\Property(property="pos_l", type="string",description="门店经度(地理位置)"),
 *   @OA\Property(property="pos_b", type="string",description="门店维度(地理位置)"),
 *   @OA\Property(property="province_id", type="integer",description="省ID"),
 *   @OA\Property(property="city_id", type="integer",description="市ID"),
 *   @OA\Property(property="district_id", type="integer",description="区ID)"),
 *   @OA\Property(property="is_open_activity", type="integer",description="是否开启活动 0:所有 1：开启 2：不开启 "),
 * )
 */

/**
 * @OA\Schema(
 *   schema="NearRetailersListResponse",
 *   type="object",
 *   @OA\Property(
 *       property="data",
 *       type="array",
 *       description="门店列表",
 *       @OA\Items(ref="#/components/schemas/NearRetailer")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="NearRetailer",
 *   type="object",
 *   required={"shop_name", "shop_addr", "shop_phone", "distance"},
 *   @OA\Property(property="shop_img", type="string", description="门店图片", example="https://wpm-cdn.dreame.tech/images/202409/108491-1726294823704.jpg"),
 *   @OA\Property(property="shop_name", type="string", description="门店名称", example="追觅芜湖八佰伴店"),
 *   @OA\Property(property="shop_phone", type="string", description="门店联系电话", example="18715323065"),
 *   @OA\Property(property="start_time", type="string", description="营业开始时间", example="10:00"),
 *   @OA\Property(property="end_time", type="string", description="营业结束时间", example="21:30"),
 *   @OA\Property(property="shop_addr", type="string", description="门店地址", example="安徽省芜湖市镜湖区中山北路33号八佰伴购物中心2楼"),
 *   @OA\Property(property="pos_l", type="number", format="float", description="门店经度", example=118.367094),
 *   @OA\Property(property="pos_b", type="number", format="float", description="门店纬度", example=31.340606),
 *   @OA\Property(property="status", type="integer", description="门店状态（0=正常）", example=0),
 *   @OA\Property(property="orders", type="integer", description="订单数量", example=1),
 *   @OA\Property(property="ctime", type="integer", format="int64", description="创建时间（时间戳）", example=1741588049),
 *   @OA\Property(property="shop_code", type="string", description="门店编码", example="JX-E-0127"),
 *   @OA\Property(property="shop_code_img", type="string", description="门店二维码", example="https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images/202503/202503240959391436.png"),
 *   @OA\Property(property="is_open_activity", type="integer", description="是否开启活动（1=是，0=否）", example=1),
 *   @OA\Property(property="distance", type="number", format="float", description="门店距离用户的位置（单位：米）", example=215.4322)
 * )
 */

