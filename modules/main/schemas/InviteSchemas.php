<?php

/**
 * @OA\Schema(
 *     schema="InviteJoinResponse",
 *     type="object",
 *     description="邀请加入响应数据",
 *     @OA\Property(property="message", type="string", description="提示信息", example="助力成功"),
 *     @OA\Property(property="current_invite_count", type="integer", description="当前助力人数", example=3),
 *     @OA\Property(property="required_invite_count", type="integer", description="需要助力人数", example=5),
 *     @OA\Property(property="is_success", type="boolean", description="是否助力成功", example=true)
 * )
 */

/**
 * @OA\Schema(
 *     schema="InviteCheckResponse",
 *     type="object",
 *     description="邀请检查响应数据",
 *     @OA\Property(property="can_invite", type="boolean", description="是否可以参与邀请", example=true),
 *     @OA\Property(property="reason", type="string", description="不能参与的原因（当can_invite为false时）", example="")
 * )
 */

/**
 * @OA\Schema(
 *     schema="OneYuanSeckillInviteModule",
 *     type="object",
 *     description="一元秒杀邀请返回数据",
 *     @OA\Property(property="message", type="string", description="提示信息", example="助力成功"),
 *     @OA\Property(property="current_invite_count", type="integer", description="当前助力人数", example=3),
 *     @OA\Property(property="required_invite_count", type="integer", description="需要助力人数", example=5),
 *     @OA\Property(property="is_success", type="boolean", description="是否助力成功", example=true)
 * )
 */
