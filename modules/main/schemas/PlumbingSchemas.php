<?php


/**
 * @OA\Schema(
 *   schema="ServiceAddressListResponse",
 *   type="array",
 *   @OA\Items(ref="#/components/schemas/AddressInfoResponse"),
 *   description="地址信息数组"
 * )
 */

/**
 * @OA\Schema(
 *   schema="AddressInfoResponse",
 *   type="object",
 *   description="地址信息结构",
 *   @OA\Property(property="$logicalname", type="string", description="类型值：new_province：省 new_city：市 new_county：区"),
 *   @OA\Property(property="$id", type="string", description="记录 ID"),
 *   @OA\Property(property="new_countyid", type="string", description="区级 ID"),
 *   @OA\Property(property="new_cityid", type="string", description="市级 ID"),
 *   @OA\Property(property="new_provinceid", type="string", description="省级 ID"),
 *   @OA\Property(property="new_name", type="string", description="名称")
 * )
 */


