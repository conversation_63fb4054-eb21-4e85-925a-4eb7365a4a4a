<?php


/**
 * @OA\Schema(
 *   schema="GoodsRecommendResources",type="object",
 *   @OA\Property(property="title", type="string", description="标题"),
 *   @OA\Property(property="tid", type="integer",description="标签ID"),
 *   @OA\Property(property="primary", type="object",ref="#/components/schemas/RecommendPrimaryResource",description="主推商品信息"),
 *   @OA\Property(property="secondary", type="array",@OA\Items(ref="#/components/schemas/RecommendSecondaryResource"),description="次推商品信息"),
 * )
 */


/**
 * @OA\Schema (
 *     schema="RecommendPrimaryResource",type="object",
 *   @OA\Property(property="id", type="string",description="标题"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="sku", type="string",description="商品SKU"),
 *   @OA\Property(property="price", type="string",description="商品价格"),
 *   @OA\Property(property="image", type="string",description="商品图片"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="RecommendSecondaryResource",  type="array",
 *   @OA\Items(
 *     type="object",
 *     @OA\Property(property="id", type="string", description="标题"),
 *     @OA\Property(property="name", type="string", description="商品名称"),
 *     @OA\Property(property="sku", type="string", description="商品SKU"),
 *     @OA\Property(property="price", type="string", description="商品价格"),
 *     @OA\Property(property="image", type="string", description="商品图片"),
 *     @OA\Property(property="type", type="string", description="类型（0普通 1预售 2热销）"),
 *   ),
 * )
 */