<?php


/**
 * @OA\Schema(
 *   schema="Coupon",type="object",
 *   @OA\Property(property="id", type="integer", format="int64",description="ID自增"),
 *   @OA\Property(property="name", type="string",description="优惠券名称"),
 *   @OA\Property(property="jump_goods_id", type="string",description="跳转商品ID"),
 * )
 */



/**
* @OA\Schema(
*   schema="ActivityDetail",type="object",
*   @OA\Property(property="id", type="string", format="int64",description="ID自增"),
*   @OA\Property(property="market_id", type="string",description="优惠券ID"),
*   @OA\Property(property="name", type="string",description="活动名称"),
*   @OA\Property(property="coupon", type="array",@OA\Items(ref="#/components/schemas/Coupon"),description="优惠券详细信息"),
* )
*/

/**
 * @OA\Schema(
 *   schema="ActivityGoodsList",type="object",
 *   @OA\Property(property="gid", type="integer", description="商品ID"),
 *   @OA\Property(property="sid", type="integer",description="多规格ID"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="mprice", type="string",description="商品原价"),
 *   @OA\Property(property="price", type="string",description="商品优惠价格"),
 *   @OA\Property(property="deprice", type="string",description="商品可抵扣"),
 *   @OA\Property(property="newprice", type="string",description="换新价"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="CateData",
 *   type="object",
 *   description="分类数据",
 *   @OA\Property(property="name", type="string", description="分类名称", example="滚刷"),
 *   @OA\Property(property="id", type="integer", description="分类ID", example=35),
 *   @OA\Property(property="image", type="string", format="uri", description="分类图片链接", example="https://wpm-cdn.dreame.tech/images/202308/456734-1691055988060.png")
 * )
 */

/**
 * @OA\Schema(
 *   schema="DeviceData",
 *   type="object",
 *   description="设备数据",
 *   @OA\Property(property="name", type="string", description="设备名称", example="X50 Ultra"),
 *   @OA\Property(property="use_day", type="integer", description="设备使用天数", example=20078),
 *   @OA\Property(
 *     property="cate_data",
 *     type="array",
 *     description="分类信息列表",
 *     @OA\Items(ref="#/components/schemas/CateData")
 *   )
 * )
 */