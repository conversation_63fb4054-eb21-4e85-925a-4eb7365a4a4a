<?php

/**
 * @OA\Schema(
 *   schema="SharedGiftCard",type="object",
 *   @OA\Property(property="id", type="string", description="ID"),
 *   @OA\Property(property="user_id", type="string", description="用户ID"),
 *   @OA\Property(property="nickname", type="string", description="用户昵称"),
 *   @OA\Property(property="avatar", type="string", description="用户头像"),
 *   @OA\Property(property="card_id", type="string", description="卡ID"),
 *   @OA\Property(property="card_no", type="string", description="卡号"),
 *   @OA\Property(property="card", type="object", ref="#/components/schemas/SharedCard", description="卡ID"),
 *   @OA\Property(property="ctime", type="string", description="领取时间，例子：2023-12-04 18:09:22"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="ValidateCard", type="object",
 *   @OA\Property(property="is_valid", type="boolean", description="true 通过 false 不通过"),
 *   @OA\Property(property="msg", type="string", description="提示信息"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="GiftCardExpendRecord",type="object",
 *   @OA\Property(property="card_no", type="string", description="卡号"),
 *   @OA\Property(property="card_type", type="string", description="卡类型：1存储卡、2兑换卡"),
 *   @OA\Property(property="list", type="array", @OA\Items(ref="#/components/schemas/ExpendRecord"),description="消费记录"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="ExpendRecord",type="object",
 *   @OA\Property(property="order_no", type="string", description="订单号"),
 *   @OA\Property(property="expend_amount", type="string", description="消费金额例子：-20"),
 *   @OA\Property(property="current_amount", type="string", description="剩余金额 例子：70"),
 *   @OA\Property(property="create_time", type="string", description="创建时间：例子：2023-12-08 13:38:15"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="SharedCard",type="object",
 *   @OA\Property(property="name", type="string", description="卡名称"),
 *   @OA\Property(property="web_name", type="string", description="卡外显名称"),
 *   @OA\Property(property="image", type="string", description="卡封面"),
 *   @OA\Property(property="type", type="string", description="类型：1 储值现金卡 2 指定兑换卡"),
 *   @OA\Property(property="amount", type="string", description="储值现金卡金额"),
 *   @OA\Property(property="goods_ids", type="string", description="指定兑换卡商品ID"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="UserCardInfo",type="object",
 *   @OA\Property(property="id", type="string", format="int64",description="ID自增"),
 *   @OA\Property(property="user_id", type="string",description="用户ID"),
 *   @OA\Property(property="sharer_id", type="string",description="分享者ID"),
 *   @OA\Property(property="card_id", type="string",description="卡资源ID"),
 *   @OA\Property(property="card_no", type="string",description="卡号"),
 *   @OA\Property(property="amount", type="string",description="金额"),
 *   @OA\Property(property="expire_time", type="string",description="过期时间"),
 *   @OA\Property(property="card_status", type="string",description="卡状态 （1：分享状态 2：过期状态 3：使用状态 4:分享待领取状态 5:分享已领取 0：正常状态），只有0可以分享"),
 *   @OA\Property(property="type", type="string",description="卡类型 （1：储值金额卡 2：指定兑换卡）"),
 *   @OA\Property(property="web_name", type="string",description="外显名称"),
 *   @OA\Property(property="image", type="string",description="图片"),
 *   @OA\Property(property="desc", type="string",description="规则"),
 * )
 */

