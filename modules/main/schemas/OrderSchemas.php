<?php


/**
 * @OA\Schema(
 *   schema="Gcombines",type="object",
 *          @OA\Property(property="gid", type="integer", default="154", description="商品ID"),
 *          @OA\Property(property="sid", type="integer", default="0", description="商品多规格ID"),
 *          @OA\Property(property="num", type="integer", default="1", description="商品数量"),
 *          @OA\Property(property="gini_id", type="integer", default="0", description="自定义价格ID"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="OrderCardList",type="object",
 *        @OA\Property(property="list", type="array",@OA\Items(ref="#/components/schemas/CardDetail"),description="优惠券详细信息"),
 *        @OA\Property(property="select", type="object",ref="#/components/schemas/CardSelect",description="优惠券详细信息"),
 *        @OA\Property(property="total", type="integer", description="优惠券数量"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="CardSelect",type="object",
 *          @OA\Property(property="id", type="integer", default="", description="用户优惠券ID"),
 *          @OA\Property(property="cprice", type="string", default="", description="优惠金额"),
 *          @OA\Property(property="m_id", type="integer", default="", description="优惠券ID"),
 *          @OA\Property(property="m_name", type="string", default="", description="优惠券名称"),
 * )
 */



/**
 * @OA\Schema(
 *   schema="OrderStatusResponse",type="object",
 *        @OA\Property(property="pay_status", type="integer", description="支付状态（1: 已支付 2: 未支付 3: 已取消）"),
 *        @OA\Property(property="msg", type="string", description="提示消息"),
 * )
 */