#!/bin/sh
<?php die();?>
###
###
######   修改文件后 记得执行命令 dos2unix  crontab.sh.php
###  -lt : 小于, -le : 小于等于, -eq : 等于, -ge : 大于等于, -gt : 大于, -ne : 不等于
###
###
#切换目录

readonly current_path=$(pwd)/;
echo $current_path

readonly cli_path=${current_path}../../../yii;
readonly minute=$((10#$(date +%M)))
readonly hour=$((10#$(date +%H)))
readonly day=$(date +%d)
readonly week=$(date +%w)

chmod a+x ${cli_path}

#每分钟执行一次
if [ "0" -eq "$(($minute % 1))" ] ; then
#/usr/bin/php -f ${cli_path} /service/data/wxoa-push > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_0  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_1  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_2  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_3  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_4  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_5  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_6  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_7  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_8  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_retry  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-crm_9  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_0  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_1  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_2  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_3  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_4  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_5  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_6  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_7  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_8  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-app_9  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/wiki/refresh  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_0  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_1  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_2  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_3  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_4  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_5  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_6  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_7  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_8  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/syn-rui-yun_9  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/data/repeat-live-list  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods-ini/syn-ini-list  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_0  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_1  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_2  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_3  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_4  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_5  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_6  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_7  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_8  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/adv/syn-adv_9  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/order-notify > /dev/null 2>&1 &
#用户消息相关
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_0 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_1 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_2 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_3 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_4 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_5 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_6 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_7 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_8 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg_9 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/user-msg/user-msg-send > /dev/null 2>&1 &
#预售商品相关
#/usr/bin/php -f ${cli_path} /service/goods/change-deposit-goods > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/cancel-tail-order > /dev/null 2>&1 &

#先试后买订单相关
#/usr/bin/php -f ${cli_path} /service/buy-after-try/end-try-orders  > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/buy-after-try/wait-deduct-orders  > /dev/null 2>&1 &
fi

#每3分钟执行一次 已弃用 240604
if [ "0" -eq "$(($minute % 3))" ] ; then
###/usr/bin/php -f ${cli_path} /service/log/order-zwx > /dev/null 2>&1 &
fi

#每10分钟执行一次
if [ "0" -eq "$(($minute % 5))" ] ; then
###/usr/bin/php -f ${cli_path} /service/goods/up-down > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/up-down99 > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/cancel-order > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/sync-stock > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/add-order > /dev/null 2>&1 &
#/usr/bin/php -f ${cli_path} /service/goods/detail-order > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/reject-refund > /dev/null 2>&1 &
#预售商品相关
#/usr/bin/php -f ${cli_path} /service/goods/cancel-deposit-order > /dev/null 2>&1 &
#广告推广
###/usr/bin/php -f ${cli_path} /service/adv/clear-adv  > /dev/null 2>&1 &
#刷新积分商品
###/usr/bin/php -f ${cli_path} /service/wares/refresh  > /dev/null 2>&1 &
fi

#每小时执行一次
if [ "0" -eq $minute ] ; then
###/usr/bin/php -f ${cli_path} /service/goods/finish-order > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/order-reward > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/finish-order-reward > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/data/rui-yun-final-retry > /dev/null 2>&1 &
#代码已经不执行 关闭 240604
#/usr/bin/php -f ${cli_path} /service/goods/detail-order > /dev/null 2>&1 &
#新ERP推送补偿
#/usr/bin/php -f ${cli_path} /service/buy-after-try/get-active-times-by-sns  > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/erp-retry > /dev/null 2>&1 &

fi

#每天凌晨03点0分钟执行一次
if [ "03" -eq $hour ] && [ "0" -eq $minute ] ; then
###/usr/bin/php -f ${cli_path} /service/data/create-db-tb > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/data/qps-to-db > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/goods/check-order > /dev/null 2>&1 &
/usr/bin/php -f ${cli_path} /service/buy-after-try/converse-user-try-orders  > /dev/null 2>&1 &
fi

#每天凌晨04点0分钟执行一次
if [ "04" -eq $hour ] && [ "0" -eq $minute ] ; then
/usr/bin/php -f ${cli_path} /service/buy-after-try/get-sns-by-order-nos  > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/data/statistics > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/data/final-retry > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/data/app-final-retry > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/wiki/del-dtopic  > /dev/null 2>&1 &
fi

#每天上午09点0分钟执行一次
if [ "09" -eq $hour ] && [ "0" -eq $minute ] ; then
###/usr/bin/php -f ${cli_path} /service/trade-in/notify-customer-service > /dev/null 2>&1 &
fi

#每天上午10点0分钟执行一次
if [ "10" -eq $hour ] && [ "0" -eq $minute ] ; then
###/usr/bin/php -f ${cli_path} /service/buy-after-try/activate-inform > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/buy-after-try/un-arrive-inform > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/buy-after-try/try-end-inform > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/buy-after-try/return-product-inform > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/activity/birthday-push > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} /service/activity/stock-alert > /dev/null 2>&1 &
###/usr/bin/php -f ${cli_path} main > /dev/null 2>&1 &
fi



