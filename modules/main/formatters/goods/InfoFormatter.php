<?php

namespace app\modules\main\formatters\goods;

use app\modules\main\formatters\Formatter;

class InfoFormatter implements Formatter
{
    public function format($data, $platform_id = null): array
    {
        // 和平台相关的格式化逻辑
        if (!empty($data['platforms'])) {
            $platforms = array_column($data['platforms'], null, 'platform_id');
            $platform = $platforms[$platform_id] ?? '';
            $data['cover_image'] = $platform['cover_image'];
            $data['images'] = $platform['images'];
            $data['detail'] = $platform['detail'];
        }
        // 删除多余的数据
        unset(
            $data['platforms'],
            $data['platform_ids'],
            $data['pc_cover_image'],
            $data['pc_images']
        );
        return $data;
    }
}