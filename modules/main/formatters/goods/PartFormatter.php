<?php

namespace app\modules\main\formatters\goods;

use app\modules\main\formatters\Formatter;
use app\modules\main\services\GoodsPlatformService;

class PartFormatter implements Formatter
{
    public function format($data, $platform_id = null): array
    {

        // 和平台相关的格式化逻辑
        if (is_array($data)) {
            foreach ($data as &$item) {
                $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platform_id, $item);

                $item['platform_image'] = [
                    'cover_image' => $platformInfo['cover_image'] ?? '',
                    'images'      => $platformInfo['images'] ?? ''
                ];

                // 删除多余的数据
                unset(
                    $item['platforms'],
                    $item['platform_ids'],
                    $item['pc_cover_image'],
                    $item['pc_images']);
            }
        }

        return $data;
    }
}