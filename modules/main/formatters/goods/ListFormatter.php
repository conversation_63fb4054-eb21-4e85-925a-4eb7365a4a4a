<?php

namespace app\modules\main\formatters\goods;

use app\modules\main\formatters\Formatter;
use app\modules\main\models\PlatformModel;

class ListFormatter implements Formatter
{
    public function format($data, $platform_id = null): array
    {
        // 和平台相关的格式化逻辑
        foreach ($data['list'] ?? [] as $index => $item) {
            if ($platform_id == PlatformModel::PLATFORM['PC']) {
                $data['list'][$index]['cover_image'] = $item['pc_cover_image'];
            }
            // 删除多余的数据
            unset(
                $data['list'][$index]['pc_cover_image'],
                $data['list'][$index]['pc_images']
            );
        }
        return $data;
    }
}