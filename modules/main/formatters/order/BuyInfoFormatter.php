<?php

namespace app\modules\main\formatters\order;

use app\modules\main\formatters\Formatter;
use app\modules\main\services\GoodsPlatformService;

class BuyInfoFormatter implements Formatter
{
    public function format($data, $platform_id = null): array
    {
        if (isset($data['list']) && is_array($data['list'])) {
            foreach ($data['list'] as &$item) {
                $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platform_id, $item);

                $item['platform_image'] = [
                    'cover_image' => $item['spec']['image'] ?? $platformInfo['cover_image'] ?? '',//多规格取当前规格图片
                    'images'      => $platformInfo['images'] ?? ''
                ];

                // 删除多余的数据
                unset(
                    $item['platforms'],
                    $item['platform_ids'],
                    $item['pc_cover_image'],
                    $item['pc_images']);
            }
        }

        return $data;
    }

}