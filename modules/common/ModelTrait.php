<?php

namespace app\modules\common;

use app\models\CUtil;
use yii\db\ActiveQuery;

trait ModelTrait
{
    // public $fillable = [];

    /**
     * 过滤新增或写入不存在的字段
     * @param array $data
     * @return void
     */
    protected function filterExecuteAttributes(array &$data)
    {
        $attrs = $this->getFillable();
        foreach ($data as $name => $val) {
            if (! in_array($name, $attrs)) {
                unset($data[$name]);
            }
        }
    }
    
    /**
     * 过滤查询不存在的字段
     * @param array $fields
     * @return void
     */
    protected function filterQueryAttributes(array $fields): array
    {
        $attrs = $this->getFillable();
        foreach ($fields as $key => $field) {
            if (! in_array($field, $attrs)) {
                unset($fields[$key]);
            } else {
                $fields[$key] = trim($field);
            }
        }

        return $fields;
    }

    /**
     * 获取可批量赋值字段
     * @return array
     */
    public function getFillable(): array
    {
        return $this->fillable;
    }

    /**
     * 获取单条数据
     * @param int $id 数据ID
     * @param array $fields
     * @return array
     */
    public function getOne(int $id, array $fields = []): array
    {
        $fields = empty($fields) ? $this->getFillable() : $fields;
        $fields = $this->filterQueryAttributes($fields);

        $query = self::find();

        $query->where(['id' => $id]);

        $res = $query->select($fields)->limit(1)->asArray()->one();
        return empty($res) ? [] : $res;
    }

    /**
     * 获取列表(无分页)
     * @param array $params 请求参数
     * @return array
     */
    public function getList(array $params = []): array
    {
        return $this->listQuerySetting($params)->asArray()->all();
    }

    /**
     * 获取分页列表
     * @param array $params 请求参数
     * @return array
     */
    public function getPageList(array $params = []): array
    {
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;

        return $this->setPagination($this->listQuerySetting($params), $page, $pageSize);
    }

    /**
     * 设置分页数据
     * @param ActiveQuery $query 查询构造器
     * @param int $page 页码
     * @param int $pageSize 条数
     * @return array
     */
    public function setPagination(ActiveQuery $query, int $page = 1, int $pageSize = 20): array
    {
        list($page, $pageSize) = CUtil::pagination($page, $pageSize);
        $list = $query->limit($pageSize)->offset($page)->asArray()->all();
        $total = $query->count();
        return [
            'list' => $list,
            'total' => (int) $total,
            'pages' => CUtil::getPaginationPages($total, $pageSize),
        ];
    }

    /**
     * 列表查询构造器
     * @param array $params 请求参数
     * @return ActiveQuery
     */
    public function listQuerySetting(array $params = []): ActiveQuery
    {
        $query = static::find();

        $fields = $params['__select_fields__'] ?? $this->getFillable();
        $query->select($this->filterQueryAttributes($fields));

        return $this->handleSearch($query, $params);
    }

    /**
     * 搜索处理器
     * @param ActiveQuery $query 查询构造器
     * @param array $params 请求参数
     * @return ActiveQuery
     */
    public function handleSearch(ActiveQuery $query, array $params): ActiveQuery
    {
        return $query;
    }
}