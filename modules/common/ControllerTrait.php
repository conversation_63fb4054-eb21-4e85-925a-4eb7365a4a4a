<?php

namespace app\modules\common;

use app\constants\RespStatusCodeConst;
use app\models\CUtil;

trait ControllerTrait
{
    /**
     * 成功响应
     * @param array|null $data
     * @param string $msg
     * @param int $code
     */
    public function success(?array $data = [], string $msg = 'OK', int $code = RespStatusCodeConst::SUCCESS_CODE)
    {
        CUtil::json_response($code, $msg, $data ?? []);
    }
    
    /**
     * 失败响应
     * @param string $msg
     * @param int $code
     */
    public function error(string $msg = '', int $code = RespStatusCodeConst::ERROR_CODE)
    {
        CUtil::json_response($code, $msg);
    }
}