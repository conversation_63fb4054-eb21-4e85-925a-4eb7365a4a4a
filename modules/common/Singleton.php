<?php

namespace app\modules\common;

/**
 * 单例模式
 *
 * 使用方法：
 * 1. 在需要使用单例的类中使用此 trait
 * 2. 将类声明为 final，因为单例模式通常不希望被继承，继承会破坏单例的特性
 *
 * 示例：
 * final class MyClass {
 *     use Singleton;
 * }
 */
trait Singleton
{
    private static $_instance;
    
    /**
     * 防止直接实例化
     */
    private function __construct() {}
    
    /**
     * 防止克隆
     */
    public function __clone()
    {
        throw new \RuntimeException('不允许克隆单例对象');
    }
    
    /**
     * 防止反序列化
     */
    public function __wakeup()
    {
        throw new \RuntimeException('不允许反序列化单例对象');
    }
    
    /**
     * 获取实例
     */
    public static function getInstance()
    {
        if (! isset(self::$_instance)) {
            self::$_instance = new static();
        }
        return self::$_instance;
    }
}