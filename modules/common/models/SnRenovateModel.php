<?php
namespace app\modules\common\models;


use app\models\by;
use app\modules\main\models\CommModel;

//翻新工厂SN
class SnRenovateModel extends CommModel
{
    public static function tbName()
    {
        return "`db_mall_common`.`t_sn_renovate`";
    }


    public function GetOldSnByNewSn($newSn)
    {
        $sql = "SELECT `old_sn` FROM " . self::tbName() . " WHERE `new_sn_code` = :new_sn limit 1";
        $params = [':new_sn' => $newSn];
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return $result['old_sn'] ?? '';
    }


}
