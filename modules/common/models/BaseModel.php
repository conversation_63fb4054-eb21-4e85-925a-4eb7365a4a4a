<?php

namespace app\modules\common\models;

use app\models\by;
use app\modules\common\ModelTrait;
use yii\db\ActiveRecord;

class BaseModel extends ActiveRecord
{
    use ModelTrait;

    const DISABLE = 0;
    const ENABLE = 1;

    // 状态
    const STATUS = [self::DISABLE => '禁用', self::ENABLE => '正常'];

    public static function getDb()
    {
        return by::dbMaster();
    }

    public static function getStatusText(int $status): string
    {
        return self::STATUS[$status] ?? 'unknown';
    }
}