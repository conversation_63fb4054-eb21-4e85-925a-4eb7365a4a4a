<?php

namespace app\modules\mall\models;


use app\components\Crm;
use app\components\PointCenter;
use app\components\WeiXin;
use app\jobs\SyncPointGrowJob;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\log\services\warranty\WarrantyCardService;
use app\modules\main\models\CrmLogModel;
use yii\db\Exception;

class OnceModel extends CommModel
{
    public function tuikuan($orderList)
    {
        //已退款的处于已拒绝状态
        foreach ($orderList as $order_no=>$refund_no){
            $mOmain         = by::Omain();
            $mOuser         = by::Ouser();
            //判断退款单状态
            $rm_info = by::OrefundMain()->GetInfoByRefundNo($refund_no);
            $user_id = $rm_info['user_id'];
            $r_info  = by::Orefund()->CommPackageInfo($user_id,$refund_no,false,true);
            if ($r_info['status'] != by::OrefundMain()::STATUS['P_REJECT']) {
                CUtil::debug($order_no.'|'.$refund_no.'|不是拒绝状态！','refund_order_modify_warn');
                continue;
            }
            //先变成待审核状态
            $rGoods         = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
            $ids            = array_column($rGoods, 'og_id');
            $mOgoods        = by::Ogoods();
            $o_info         = $mOuser->GetInfoByOrderId($user_id, $order_no);
            list($_, $ostatus) = by::Omain()->SplitOrderStatus($r_info['ostatus']);

            $next_st = $mOmain::ORDER_STATUS['REFUNDING'];
            $next_gst= $mOgoods::STATUS['REFUNDING'];

            //订单商品状态修改
            list($s, $m)    = $mOgoods->UpdateStatus($user_id, $order_no, $ids, $next_gst);
            if (!$s) {
                CUtil::debug($order_no.'|'.$refund_no.'|'.$m,'refund_order_modify_warn');
                continue;
            }

            //订单状态修改

            list($s, $m)    = $mOmain->SyncInfo($user_id, $order_no, $next_st);
            if (!$s) {
                CUtil::debug($order_no.'|'.$refund_no.'|'.$m,'refund_order_modify_warn');
                continue;
            }

            list($s, $m) = by::OrefundMain()->SyncInfo($user_id,$refund_no,by::OrefundMain()::STATUS['AUDIT']);
            if (!$s) {
                CUtil::debug($order_no.'|'.$refund_no.'|'.$m,'refund_order_modify_warn');
                continue;
            }

            //再次通过审核
            $a_reason = '线下通过退款';
            $status = 1010;
            $arr = [
                'order_no'=> $order_no,
                'refund_no'=> $refund_no,
                'a_reason'=> $a_reason,
                'status'=>  $status
            ];
            list($s,$data) = $this->AuditBu($arr);
            if(!$s){
                CUtil::debug($data.'|'.json_encode($arr),'audit-bu-warn');
                continue;
            }
            CUtil::debug($data.'|'.json_encode($arr),'audit-bu');
        }
    }

    /**
     * 退款操作
     * @param array $orders
     * @throws Exception
     * @throws \RedisException
     */
    public function refund(array $orders)
    {
        //模型
        $mOmain = by::Omain();
        $mOgoods = by::Ogoods();
        $mOrgoods = by::Orgoods();
        $mOrefund = by::Orefund();
        $mOrefundMain = by::OrefundMain();

        //已退款的处于待审核、已拒绝状态
        foreach ($orders as $order_no => $refund_no) {

            //1、判断退款单状态，获取用户ID
            $rm_info = $mOrefundMain->GetInfoByRefundNo($refund_no);
            if (empty($rm_info)) {
                $msg = sprintf('退款单号%s不存在！', $refund_no);
                CUtil::debug($msg, 'refund_order_modify_warn');
                echo $msg;
                continue;
            }
            $user_id = $rm_info['user_id'];

            //2、判断退款单状态，待审核、拒绝的订单才可以操作
            $r_info = $mOrefund->GetInfoByRefundNo($user_id, $refund_no);
            if (!in_array($r_info['status'], [$mOrefundMain::STATUS['AUDIT'], $mOrefundMain::STATUS['P_REJECT']])) {
                $msg = sprintf('订单号%s，退款单号%s的状态，非待审核、拒绝！', $order_no, $refund_no);
                CUtil::debug($msg, 'refund_order_modify_warn');
                echo $msg;
                continue;
            }

            //2、先变成待审核状态
            $rGoods = $mOrgoods->GetListByRefundNo($user_id, $refund_no, $order_no);
            $ids = array_column($rGoods, 'og_id');

            //订单商品状态修改
            list($status, $msg) = $mOgoods->UpdateStatus($user_id, $order_no, $ids, $mOgoods::STATUS['REFUNDING']);
            if (!$status) {
                $msg = sprintf('订单号%s，退款单号%s，更新状态为申请退款1，失败！失败原因：%s', $order_no, $refund_no, $msg);
                CUtil::debug($msg, 'refund_order_modify_warn');
                echo $msg;
                continue;
            }

            // 退款单状态为：拒绝，执行下面方法
            if ($r_info['status'] == $mOrefundMain::STATUS['P_REJECT']) {
                //订单状态修改
                list($status, $msg) = $mOmain->SyncInfo($user_id, $order_no, $mOmain::ORDER_STATUS['REFUNDING']);
                if (!$status) {
                    $msg = sprintf('订单号%s，退款单号%s，更新状态为申请退款2，失败！失败原因：%s', $order_no, $refund_no, $msg);
                    CUtil::debug($msg, 'refund_order_modify_warn');
                    echo $msg;
                    continue;
                }

                //更新为待审核
                list($status, $msg) = $mOrefundMain->SyncInfo($user_id, $refund_no, $mOrefundMain::STATUS['AUDIT']);
                if (!$status) {
                    $msg = sprintf('订单号%s，退款单号%s，更新状态为待审核，失败！失败原因：%s', $order_no, $refund_no, $msg);
                    CUtil::debug($msg, 'refund_order_modify_warn');
                    echo $msg;
                    continue;
                }
            }

            //再次通过审核
            $arr = [
                'order_no'  => $order_no,
                'refund_no' => $refund_no,
                'a_reason'  => '线下通过退款',
                'status'    => $mOrefundMain::STATUS['P_PASS'] // 审核通过
            ];
            list($status, $data) = $this->AuditBu($arr);
            if (!$status) {
                CUtil::debug($data . '|' . json_encode($arr), 'audit-bu-warn');
                $msg = sprintf('订单号%s，退款单号%s，更新状态，失败！失败原因：%s', $order_no, $refund_no, $data);
                echo $msg;
                continue;
            }
            CUtil::debug($data . '|' . json_encode($arr), 'audit-bu');
        }
    }



    public function GuideRecover()
    {
        //获取所有导购数据
        $tb_ruser_guide = by::RuserGuide()::tbName();
        $tb_ruser_extend = by::RuserExtend()::tbName();
        $tb_user_guide = by::userGuide()::tbName();
        $id = 0;
        $db = by::dbMaster();

        $sql1 = "SELECT `main_user_id`,`user_id` FROM {$tb_ruser_extend} WHERE `ctime` < 1676893328 ORDER BY `id`";
        $sql = "SELECT `gu`.`id`,`ex`.`main_user_id`,`gu`.`user_id`,`gu`.`guide_id`,`gu`.`ctime` FROM {$tb_ruser_guide} as `gu` INNER JOIN ({$sql1}) AS `ex`
                ON  `gu`.`user_id` = `ex`.`user_id` WHERE `gu`.`id` > :id AND `gu`.`ctime` < 1676893328 ORDER BY `gu`.`id` LIMIT 200";

        $realSql = "SELECT `user_id` FROM {$tb_user_guide} WHERE `ctime` < 1676893328";
        $guideInfos = $db->createCommand($realSql)->queryAll();
        $guideIds = array_column($guideInfos,'user_id');

        while (1) {
            $list = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($list)) {
                return 'end';
            }
            $end = end($list);
            $id = $end['id'];

            foreach ($list as $info) {
                $userId = $info['main_user_id'] ?? 0;
                if($userId && !in_array($userId,$guideIds)){
                    by::userGuide()->bound($userId,$info['guide_id'] ?? 0);
                }
            }
        }
    }



    /**
     * @param $arr
     * @return array
     * @throws Exception
     * 退款单审核
     */
    public function AuditBu($arr): array
    {
        $refund_no = $arr['refund_no'] ?? '';
        $order_no  = $arr['order_no']  ?? '';
        $status    = $arr['status']    ?? 0;
        $a_reason  = $arr['a_reason']  ?? '';

        if (empty($refund_no) || empty($order_no)) {
            return [false, '参数错误'];
        }

        if (mb_strlen($a_reason) > 500) {
            return [false, '拒绝原因最长500字'];
        }

        $status = CUtil::uint($status);
        if(!in_array($status,[by::OrefundMain()::STATUS['P_PASS'],by::OrefundMain()::STATUS['P_REJECT']])) {
            return [false, '参数错误(1)'];
        }

        $rm_info = by::OrefundMain()->GetInfoByRefundNo($refund_no);
        if (empty($rm_info)) {
            return [false, '退款订单异常'];
        }

        $user_id = $rm_info['user_id'];
        $r_info  = by::Orefund()->CommPackageInfo($user_id,$refund_no,false,true);
        if ($r_info['status'] != by::OrefundMain()::STATUS['AUDIT']) {
            return [false, '退款订单异常(2)'];
        }

        //记录生成后10分钟内无法操作同意退款
        $d_time     = $r_info['ctime'] + 60 - time();
        if ($status == by::OrefundMain()::STATUS['P_PASS'] && $d_time > 0) {
            return [false, "请{$d_time}秒后再操作"];
        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            list($s, $m) = by::OrefundMain()->SyncInfo($user_id,$refund_no,$status, ['a_reason' => $a_reason]);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 更改订单状态
            list($s, $ret) = $this->__auditByStatus($user_id, $order_no, $refund_no, $status, $r_info['ostatus']);
            if (!$s) {
                throw new MyExceptionModel($ret);
            }

            $trans->commit();

            //退款成功在回调中同步
            if ($status == by::OrefundMain()::STATUS['P_PASS']) {
                $aData = $this->dowellData($user_id,$order_no,$refund_no,$ret['r_freight'] ?? false);
                list($s,$data) = $this->refundNotify($aData);
                if(!$s){
                    return [$s,$data];
                }
            }

            return [true, 'ok'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit');

            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.raudit');

            return [false, '操作失败'];
        }
    }


    /**
     * @throws Exception
     * @throws \RedisException
     */
    private function __auditByStatus($user_id, $order_no, $refund_no, $status, $ostatus)
    {
        $mOmain         = by::Omain();
        $mOuser         = by::Ouser();
        $o_info         = $mOuser->GetInfoByOrderId($user_id, $order_no);

        $mOgoods        = by::Ogoods();
        //订单商品表数据
        $oGoods         = $mOgoods->GetListByOrderNo($user_id, $order_no);
        $oGoods = array_filter($oGoods, function ($v) use($mOgoods) {
            if ($v['status'] == $mOgoods::STATUS['REFUNDIED']) {
                return false;
            }
            return true;
        });

        //退款商品表数据
        $rGoods         = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        $ids            = array_column($rGoods, 'og_id');

        //是否全部退款
        $complete   = false;
        if (count($rGoods) == count($oGoods)) {
            $complete   = true;
        }

        $r_freight      = false;
        switch ($status) {
            case by::OrefundMain()::STATUS['P_PASS'] :
                $next_st = $mOmain->SetOrderStatus($o_info['status'], $mOmain::ORDER_STATUS['RERUNDED'], true, $complete);
                $next_gst= $mOgoods::STATUS['REFUNDIED'];

                //是否需要退邮费
                if ($next_st == $mOmain::ORDER_STATUS['RERUNDED']) {
                    $or_info = by::Orefund()->GetInfoByRefundNo($user_id,$refund_no);
                    if ($or_info['m_type'] == 1){//未收到货 才退运费
                        $r_freight  = true;
                    }
                }

                break;

            default :
                $next_st = $mOmain->SetOrderStatus($o_info['status'], $mOmain::RERUND_DTS, true, $complete, $ostatus);
                $next_gst= $mOgoods::STATUS['NORMAL'];
        }

        //订单商品状态修改
        list($s, $m)    = $mOgoods->UpdateStatus($user_id, $order_no, $ids, $next_gst);
        if (!$s) {
            return [false, $m];
        }

        //订单状态修改
        if ($next_st != $o_info['status']) {
            list($s, $m)    = $mOmain->SyncInfo($user_id, $order_no, $next_st);
            if (!$s) {
                return [false, $m];
            }
        }

        return [true, ['r_freight' => $r_freight, 'complete' => $complete]];
    }


    public function dowellData($user_id,$order_no,$refund_no,$r_freight)
    {
        $mOgoods    = by::Ogoods();
        $o_info     = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        $r_goods    = by::Orgoods()->GetListByRefundNo($user_id, $refund_no, $order_no);
        $o_goods    = $mOgoods->GetListByOrderNo($user_id, $order_no);
        $og_ids     = array_column($r_goods, 'og_id');
        $refund_fee = 0;

        foreach($o_goods as $val) {
            if (in_array($val['id'], $og_ids)) {
                $refund_fee = bcadd($refund_fee, $val['price']);
            }
        }

        if ($r_freight) {
            $refund_fee = bcadd($refund_fee, $o_info['fprice']);
        }

        $total_fee = bcadd($o_info['price'], $o_info['fprice']);

        $aData['amount']['refund'] = CUtil::uint($refund_fee ?? 0);
        $aData['amount']['total'] = CUtil::uint($total_fee ?? 0);
        $aData['order_no'] = $order_no;
        $aData['refund_no'] = $refund_no;
        return $aData;
    }

    /**
     * @throws Exception
     * @throws \RedisException
     */
    public function refundNotify($notify_data = [])
    {
        $order_no     = $notify_data['order_no'] ?? "";
        $refund_no    = $notify_data['refund_no'] ?? "";
        $success_time = intval(START_TIME);
        $amount       = $notify_data['amount'] ?? [];
        $refund_fee   = CUtil::uint($amount['refund'] ?? 0);//退款金额

        //todo 订单状态检测
        $mOrefundMain = by::OrefundMain();
        $rm_info      = $mOrefundMain->GetInfoByRefundNo($refund_no);
        if (empty($rm_info)) {
            CUtil::debug("退款订单不存在:{$refund_no}", 'err.nr.pay');
            return [false, '退款订单不存在'];
        }


        $r_info = by::Orefund()->GetInfoByRefundNo($rm_info['user_id'], $refund_no);
        if ($r_info['status'] != $mOrefundMain::STATUS['P_PASS']) {
            CUtil::debug("无效订单:" . json_encode($r_info), 'warn.nr.pay');
            return [false, '无效订单'];
        }

        //订单表数据
        $o_info = by::Ouser()->GetInfoByOrderId($r_info['user_id'], $order_no);

        //订单商品表数据
        $oGoods = by::Ogoods()->GetListByOrderNo($r_info['user_id'], $order_no);

        //退款商品表数据
        $rGoods = by::Orgoods()->GetListByRefundNo($r_info['user_id'], $refund_no, $order_no);
        $ids    = array_column($rGoods, 'og_id');


        $trans = by::dbMaster()->beginTransaction();

        try {
            //回退销量
            foreach ($oGoods as $val) {
                if (in_array($val['id'], $ids)) {
//                    by::Gstock()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'SALE', false);
                    by::GoodsStockModel()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'SALE', false,by::GoodsStockModel()::SOURCE['MAIN']);

                    $giniId = $val['gini_id'] ?? 0;
                    if ($giniId) {
                        by::Gini()->UpdateStock($giniId, $val['num'], 'SALE', false);
                    }
                }
            }

            //退消费券
            if (count($oGoods) == count($rGoods) && !empty($o_info['consume_id'])) {
                list($s, $m) = by::userCard()->UnLockCard($r_info['user_id'], $o_info['consume_id'], $trans);
                if (!$s) {
                    throw new \Exception($m);
                }
            }

            //退优惠券
            if (count($oGoods) == count($rGoods) && !empty($o_info['coupon_id'])) {
                list($s, $m) = by::userCard()->UnLockCard($r_info['user_id'], $o_info['coupon_id'], $trans);
                if (!$s) {
                    throw new \Exception($m);
                }
            }

            //更改退款申请表为退款成功
            $next_st = $mOrefundMain::STATUS['SUCCESS'];

            $save = [
                'rtime' => $success_time > 0 ? $success_time : time(),
                'price' => $refund_fee,
            ];
            list($s, $m) = $mOrefundMain->SyncInfo($r_info['user_id'], $refund_no, $next_st, $save);
            if (!$s) {
                throw new \Exception($m);
            }

            $trans->commit();

            // //todo 订单同步crm
            // Crm::factory()->push($r_info['user_id'], 'order', ['user_id' => $r_info['user_id'], 'order_no' => $order_no]);
            // Crm::factory()->push($r_info['user_id'], 'orderLine', ['user_id' => $r_info['user_id'], 'order_no' => $order_no]);
            //
            // //todo 退款crm
            // Crm::factory()->push($r_info['user_id'], 'refund', ['user_id' => $r_info['user_id'], 'refund_no' => $refund_no]);
            // Crm::factory()->push($r_info['user_id'], 'refundLine', ['user_id' => $r_info['user_id'], 'refund_no' => $refund_no]);
            PointCenter::factory()->refundPush($r_info['user_id'], $refund_no);

            return [true, 'ok'];
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage(), 'err.nr.pay');

            $trans->rollBack();

            return [false, '退款失败'];
        }


    }


    public function transferOpenid()
    {
        $limitNum = 60;
        $oaTable = by::OaFocus()::tbName();
        $db = by::dbMaster();
        $redisKey = 'userOa';
        $redis = by::redis('core');
        $config =  CUtil::getConfig('wxoa', 'common', MAIN_MODULE);
        $fromAppid =  $config['appId']     ?? "";
        while (true) {
            $id = CUtil::uint($redis->get($redisKey));
            $sql = "SELECT * FROM {$oaTable} WHERE `is_new`=0 AND `id`>:id  ORDER BY `id` ASC LIMIT ".$limitNum;
            $oaInfo = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($oaInfo)) {
                break;
            }
            $end = end($oaInfo);
            $id = $end['id'];
            $redis->set($redisKey, $id, 7200);
            $openidList = array_column($oaInfo,'oa_openid');
            $oriData = array_column($oaInfo,'id','oa_openid');
            $this->requestOa($fromAppid,$openidList,$oriData);
        }
        echo 'done';exit();
    }


    public function requestOa($fromAppid,$openidList,$oriData)
    {
        list($status, $token)  = WeiXin::factory()->getUniqueAccessToken(WeiXin::UQ_TOKEN['OANEW']);
        if(!$status) {
            return [false,$token];
        }
        $body = [
          'from_appid'=>$fromAppid,
          'openid_list'=>$openidList,
        ];
        $header = [
            "Content-Type:application/json",
            "Expect: "
        ];
        $url                 = "https://api.weixin.qq.com/cgi-bin/changeopenid?access_token={$token}";
        $ret                 = CUtil::curl_post($url,json_encode($body),$header);
        // CUtil::debug("|url:{$url}|header:".json_encode($header)."|data：".json_encode($body)." | res:" . json_encode($ret), "change.oa");
        $ret                 = (array)json_decode($ret, true);
        CUtil::setLogMsg(
            "change.oa",
            $body,
            $ret,
            $header,
            $url,
            ''
        );
        $errcode = $ret['errcode'] ?? -1;
        $resultList = $ret['result_list'] ?? [];
        $batchArray = [];
        $tb = by::OaFocus()::tbName();
        if($errcode == 0 && $resultList){
            foreach ($resultList as $item){
                $ori_openid = $item['ori_openid'] ?? '';
                $new_openid = $item['new_openid'] ?? '';
                if($ori_openid && $new_openid){
                    $id = $oriData[$ori_openid] ?? 0;
                    if($id){
                        $batchArray[] = [
                            'id'        => $id,
                            'oa_openid' => $new_openid,
                            'is_new'    => 1
                        ];
                    }
                }
            }
        }

        if($batchArray){
            $uSql   = CUtil::batchUpdate($batchArray, 'id', $tb);
            by::dbMaster()->createCommand($uSql)->execute();
        }
        return [true,'OK'];
    }


    /**
     * @throws Exception
     * @throws \app\exceptions\ProductSnException
     * 审批过得一定要过滤
     *
     */
    public function warrantyModify()
    {
        $ctime = intval(START_TIME);
        $db         = by::dbMaster();
        $tb         = by::WarrantyCard()::getTable($ctime);
        $tbApp      = by::WarrantyApplyDetail()::getTable($ctime);
        $id         = 0;
        //todo 审批过得进行过滤

        $sql        = "SELECT `a`.`id`,`a`.`card_no`,`b`.`apply_status` FROM {$tb}  as `a`
              left join {$tbApp} as `b` on `a`.`user_id` = `b`.`user_id` and `a`.`sn` = `b`.`sn` and `b`.`apply_status` = 1              
              WHERE `a`.`id` > :id AND `a`.`is_del` = 0 AND `b`.`apply_status` is null ORDER BY `a`.`id` ASC LIMIT 500";

        $sevice = WarrantyCardService::getInstance();

        while (true) {
            $list   = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $id     = $end['id'];

            foreach($list as $v) {
                  $cardInfo = by::WarrantyCard()->GetOneByCardNo($v['card_no']);
                  //获取保修卡时间
                  $aData['card_no'] = $cardInfo['card_no'];
                  $period_time = $sevice->GetPeriodTimeBySn($cardInfo['user_id'], $cardInfo['sn'], $cardInfo['buy_time']);
                  $aData['period_time'] = $period_time;
                  if($period_time !== $cardInfo['period_time']){
                      $s = $sevice->SaveWarrantyCard($aData);
                  }
                  usleep(100);
                var_dump(time(),$cardInfo['id'],$period_time);
            }
        }
    }


    public function syncStockWares()
    {
        $db         = by::dbMaster();
        $tb         = by::Gstock()::tbName();
        $tbW      = "`db_dreame_wares`.`t_goods_stock`";

        $sql = " SELECT * FROM {$tb} WHERE `is_del` = 0";
        $list   = $db->createCommand($sql)->queryAll();

        $need_fid  = ['sku', 'stock', 'sales','wait','ctime','utime'];
        $field = implode(',', $need_fid);

        $arr =[];
        foreach ($list as $li){
            $stock = $li['stock'];
            $info = by::Gmain()->GetOneByGidSid($li['gid'],$li['sid']);
            $sku = $info['spec']['sku'] ?? ($info['sku'] ?? '');
            if($sku){
                $arr[]=[
                    'sku'   => CUtil::removeXss($sku),
                    'stock' => $stock,
                    'sales' => $li['sales'] ?? 0,
                    'wait' => $li['wait'] ?? 0
                ];
            }
        }
        $values = '';
        $ctime = $utime = time();
        foreach ($arr as $item){
            $values .= "('{$item['sku']}', {$item['stock']},{$item['sales']}, {$item['wait']},{$ctime},{$utime}),";
        }
        $values = rtrim($values, ',');
        $sql   = "INSERT INTO {$tbW} ({$field})  
                        VALUES {$values} 
                        ON DUPLICATE KEY UPDATE `stock` = values(`stock`),`sales` = values(`sales`),`wait` = values(`wait`),`ctime` = values(`ctime`), `utime` = values(`utime`)";
        by::dbMaster()->createCommand($sql)->execute();
        var_dump('OK');
    }
}
