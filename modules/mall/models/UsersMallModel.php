<?php

namespace app\modules\mall\models;

use app\components\AppCRedisKeys;
use app\components\Mall;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\PhoneModel;
use app\modules\main\models\UserModel;
use yii\behaviors\TimestampBehavior;
use yii\db\Exception;

class UsersMallModel extends CommModel
{
    public function behaviors(): array
    {
        return [
            [
                'class' => TimestampBehavior::className(),
                'attributes' => [
                    self::EVENT_BEFORE_INSERT => ['create_time', 'update_time'],
                    self::EVENT_BEFORE_UPDATE => ['update_time'],
                ],
            ],
        ];
    }

    public static $expire = 1800;

    const ERR_CODE = [
        'OK' => 1,     //成功
        'REPEAT' => 100,   //已被绑定
        'ERR' => -1,    //其他错误
    ];

    public static function tbName(): string
    {
        return '`db_dreame`.`t_users_mall`';
    }

    public static function tableName(): string
    {
        return self::tbName();
    }

    public $tb_fields = [
        'id', 'nick_name', 'uid', 'phone', 'phone_code', 'avatar', 'register_type', 'status', 'is_deleted', 'create_time', 'update_time'
    ];

    private function __getMallInfo($mallId): string
    {
        return AppCRedisKeys::getMallInfoById($mallId);
    }

    private function __getMallsByUserId($userId): string
    {
        return AppCRedisKeys::getMallsByUserId($userId);
    }

    private function __getMallInfoByUserId($userId): string
    {
        return AppCRedisKeys::getMallInfoByUserId($userId);
    }

    private function __getMallInfoByUid($uid): string
    {
        return AppCRedisKeys::getMallInfoByUid($uid);
    }

    private function __getMallInfoByUidWithDeleted($uid, $include_deleted): string
    {
        return AppCRedisKeys::getMallInfoByUidWithDeleted($uid, $include_deleted);
    }

    private function __delCache($mallId = 0, $userId = 0, $uid = '')
    {
        if ($mallId) {
            $redis_key = $this->__getMallInfo($mallId);
            by::redis()->del($redis_key);
        }
        if ($userId) {
            $redis_key = $this->__getMallsByUserId($userId);
            $redis_key1 = $this->__getMallInfoByUserId($userId);
            by::redis()->del($redis_key, $redis_key1);
        }
        if ($uid) {
            $redis_key = $this->__getMallInfoByUid($uid);
            $redis_key1 = $this->__getMallInfoByUidWithDeleted($uid, false);
            $redis_key2 = $this->__getMallInfoByUidWithDeleted($uid, true);
            by::redis()->del($redis_key, $redis_key1, $redis_key2);
        }
    }

    public function deleteRedisCache($mallId = 0, $userId = 0, $uid = '')
    {
        $this->__delCache($mallId, $userId, $uid);
    }


    //合规化参数，方便注册与更新
    private function __doWellFields($data): array
    {
        $returnData = [];
        $fields = $this->tb_fields;
        foreach ($data as $key => $item) {
            if (in_array($key, $fields)) {
                $returnData[$key] = $item;
            }
        }
        return $returnData;
    }

    /**
     * @throws Exception
     */
    public function getMallInfoByUserId($user_id, $cache = true)
    {
        $redis = by::redis('core');
        $redis_key = AppCRedisKeys::getMallInfoByUserId($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName();
            $tbPhone = PhoneModel::tbName();
            $tbUserMain = UserModel::userMainTb();
            $sqlMain = "SELECT `user_id` FROM {$tbUserMain} WHERE `user_id` = :user_id AND locate('|',`openudid`)=0 LIMIT 1 ";
            $sqlPhone = "SELECT `mall_id`,`phone` FROM {$tbPhone} as `phone` INNER JOIN ({$sqlMain}) AS `main` WHERE `phone`.`user_id`= `main`.`user_id` ORDER BY `phone`.`id` DESC LIMIT 1";
            $sql = "SELECT a.`id`,a.`uid`,a.`phone`,a.`nick_name`FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`id` = b.`mall_id` WHERE a.`is_deleted`= 0 ORDER BY a.`id` DESC LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 600]);
        }
        return $aData;
    }

    /**
     * @param $user_id
     * @param $cache
     * @return array|\yii\db\DataReader
     * @throws Exception
     * 根据用户信息获取mallinfo 包括注销的
     */
    public function getInfoByUserId($user_id, $cache = true)
    {
        $redis = by::redis('core');
        $redis_key = AppCRedisKeys::getInfoByUserId($user_id);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName();
            $tbPhone = PhoneModel::tbName();
            $sqlPhone = "SELECT `mall_id`,`phone` FROM {$tbPhone} as `phone`  WHERE `phone`.`user_id`= :user_id ORDER BY `phone`.`id` DESC LIMIT 1";
            $sql = "SELECT a.`id`,a.`uid`,a.`phone`,a.`nick_name`,avatar FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`id` = b.`mall_id`  ORDER BY a.`id` DESC LIMIT 1";
            $aData = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryOne();
            $aData = empty($aData) ? [] : $aData;
            !empty($aData) && $redis->set($redis_key, json_encode($aData), ['EX' => 600]);
        }
        return $aData;
    }


    /**
     * @throws Exception
     */
    public function getMallInfoByUid(string $uid = '', $cache = true)
    {
        if (empty($uid)) return [];
        $redis = by::redis('core');
        $redis_key = $this->__getMallInfoByUid($uid);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName();
            $sql = "SELECT * FROM {$tb} WHERE `uid`=:uid AND `is_deleted` = 0 LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":uid", $uid);

            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            $redis->set($redis_key, json_encode($user_info), ['EX' => empty($user_info) ? 10 : self::$expire]);
        }
        return $user_info;

    }

    /**
     * @param string $uid
     * @param bool $include_deleted // 是否包含已删除的
     * @param bool $cache
     * @return array
     * @throws Exception
     */
    public function getMallInfoByUidWithDeleted(string $uid = '', bool $include_deleted = false, bool $cache = true)
    {
        if (empty($uid)) return [];

        $redis = by::redis('core');
        $redis_key = $this->__getMallInfoByUidWithDeleted($uid, $include_deleted);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb = self::tbName();
            $sql = "SELECT * FROM {$tb} WHERE `uid` = :uid";

            if (!$include_deleted) {
                $sql .= " AND `is_deleted` = 0";
            }

            $sql .= " LIMIT 1";

            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":uid", $uid);

            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;

            $redis->set($redis_key, json_encode($user_info), ['EX' => empty($user_info) ? 10 : self::$expire]);
        }

        return $user_info;
    }

    /**
     * @param int $mallId
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 根据商城ID获取用户信息
     */
    public function getMallInfoById(int $mallId = 0, $cache = true)
    {
        $mallId = CUtil::uint($mallId);
        if ($mallId <= 0) {
            return [];
        }

        $redis = by::redis('core');
        $redis_key = AppCRedisKeys::getMallInfoById($mallId);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $user_info = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName();
            $sql = "SELECT * FROM {$tb} WHERE `id`=:mallId AND `is_deleted` = 0 LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":mallId", $mallId);

            $user_info = $command->queryOne();
            $user_info = empty($user_info) ? [] : $user_info;
            $redis->set($redis_key, json_encode($user_info), ['EX' => empty($user_info) ? 10 : self::$expire]);
        }
        return $user_info;
    }


    /**
     * 同步商城数据
     * @param $data
     * @param $registerType
     * @return array
     * @throws Exception
     */
    public function saveMallInfo($data, $registerType): array
    {
        $tb = self::tbName();
        $uid = $data['uid'] ?? 0;
        $phone = $data['phone'] ?? 0;
        if (!CUtil::reg_valid($phone, CUtil::REG_PHONE)) {
            return [false, "手机号格式不正确"];
        }
        // 查询是否有数据
        $sql1 = "SELECT `id`,`phone`,`nick_name`,`avatar` FROM {$tb} WHERE `uid`=:uid AND `is_deleted`= 0 AND `register_type`=:registerType LIMIT 1";
        $aData = by::dbMaster()->createCommand($sql1, [':uid' => $uid, ':registerType' => $registerType])->queryOne();
        $mallId = $aData['id'] ?? 0;
        $phoneNew = $aData['phone'] ?? 0;
        if ($mallId && $phoneNew != $phone) {
            return [false, '账户和电话号码不一致！'];
        }
        $data['register_type'] = CUtil::uint($registerType);
        $appId = $data['app_id'] ?? '';
        unset($data['app_id']);//去除无需存储的参数
        //生日 性别补充
        $sex = $data['sex'] ?? 0;
        $birthday = $data['birthday'] ?? 0;
        //合规化参数
        $data = $this->__doWellFields($data);

        $transaction = by::dbMaster()->beginTransaction();
        try {
            if ($mallId) {
                //存在数据进行更新
                $data['update_time'] = time();
                //数据确认有没有更新
                if ($aData['nick_name'] == $data['nick_name'] && $aData['avatar'] == $data['avatar']) {

                } else {
                    $s = by::dbMaster()->createCommand()->update($tb, $data, ['id' => $mallId])->execute();
                    CUtil::debug(by::dbMaster()->createCommand()->update($tb, $data, ['id' => $mallId])->getRawSql(), 'wx.user');
                    CUtil::debug($s, 'wx.user');
                }
            } else {
                //不存在数据进行创建
                $data['create_time'] = time();
                $data['update_time'] = time();
                $s = by::dbMaster()->createCommand()->insert($tb, $data)->execute();
                if (!$s) {
                    throw new MyExceptionModel('创建用户失败！');
                }
                $mallId = by::dbMaster()->getLastInsertID();
            }
            //更新t_phone 信息
            $data['mall_id'] = $mallId;
            $data['sex'] = $sex; //性别
            $data['birthday'] = $birthday;//生日

            list($s, $m) = $this->saveWxUser($phone, $data);
            if (!$s) {
                throw new MyExceptionModel($m);
            }
            $transaction->commit();

            $data = array_merge($data, $m);

            //补充需要返回的数据
            $data['id'] = $appId;
            $user_id = $data['user_id'] ?? 0;

            //更新本地数据
            $this->__updateUserDetail($user_id,$data);

            //清除缓存
            $this->deleteRedisCache($mallId, $user_id, $data['uid'] ?? 0);

            //加密数据
            $data = Mall::factory()->_appCryptReturnData($data, $registerType);

            return [true, $data, self::ERR_CODE['OK']];
        } catch (\exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'link_run_dreamehome_err');
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }
    }


    /**
     * @param $user_id
     * @param $data
     * @return bool
     * @throws Exception
     */
    private function __updateUserDetail($user_id,$data)
    {
        //获取用户信息
        $user_id = CUtil::uint($user_id);
        $userDetailInfo = by::users()->getOneByUid($user_id);
        $data['nick'] = $data['nick_name'] ?? '';
        if($userDetailInfo){
            if ((empty($data['birthday']) && empty($data['sex'])) && (!empty($userDetailInfo['birthday']) || !empty($userDetailInfo['sex']))) {
                //推送对方更新
                Mall::factory()->push($user_id, 'centerUpdate', ['user_id' => $user_id]);
            }else{
                //更新本地数据
                by::users()->updateMembersInfo($user_id,$data);
                //清除缓存
                by::users()->delOneCache($user_id);
            }
        }
        return true;
    }

    /**
     * 注册小程序数据
     * @param $phone
     * @param $data
     * @return array
     * @throws Exception
     */
    public function saveWxUser($phone, $data)
    {
        $mallId = $data['mall_id'] ?? 0;
        //1.查找是否存在对应的user_id（不存在创建，存在更新mall_id）
        $userData = by::users()->getWxUserByPhone($phone, false);
        $userId = $userData['user_id'] ?? 0;
        $isRegisterFlag = 0;
        if (!$userData) {
            //创建数据
            $data['nick'] = $data['nick_name'] ?? '';
            $data['openudid'] = 'dreamehome-openudid-' . $mallId . '-' . time();
            $data['unionid'] = 'dreamehome-unionid-' . $mallId . '-' . time();
            $data['reg_time'] = time();
            $data['user_type'] = 1;
            list($status, $user_info) = by::users()->register($data, false);
            if (!$status || empty($user_info)) {
                return [false, '用户注册失败！'];
            }
            $userId = $user_info['user_id'] ?? 0;
            $isRegisterFlag = $user_info['isRegisterFlag'] ?? 0;
            //用户phone创建
            list($status, $msg) = by::Phone()->SaveRelation($userId, $phone);
            if (!$status) {
                return [false, $msg];
            }
        }
        //更新关联的数据
        if ($userId) {
            $saveData = [
                'mall_id' => $mallId
            ];
            //更新手机关联数据
            list($status, $msg) = by::Phone()->updateRelation($userId, $saveData);
            if (!$status) {
                return [false, $msg];
            }
            //更新用户扩展表
            $extendData['is_sync_app'] = 1;
            $extendData['sync_app_time'] = time();
            //todo  如果没有用户扩展信息或者扩展信息来源为空更新来源和记录时间
            $extendInfo = by::userExtend()->getUserExtendInfo($userId);
            if (!$extendInfo) {
                $extendData['ctime'] = time();
            }
            list($status, $msg) = by::userExtend()->saveUserExtend($userId, $extendData);
            if ($msg === false) {
                return [false, false];
            }

            $data['user_id'] = $userId;
            $data['isRegisterFlag'] = $isRegisterFlag;
            return [true, $data];
        } else {
            return [false, '获取user_id失败！'];
        }
    }


    /**
     * 更新商城数据
     * @param $data
     * @param $registerType
     * @return array
     * @throws Exception
     */
    public function updateMallInfo($data, $registerType, $isCreate = 1, $ifCenter = 0): array
    {
        $tb = self::tbName();
        $uid = $data['uid'] ?? 0;
        $phone = $data['phone'] ?? 0;
        if (empty($uid)) {
            return [false, "uid不存在!"];
        }
        if ($phone && !CUtil::reg_valid($phone, CUtil::REG_PHONE)) {
            return [false, "手机号格式不正确"];
        }
        // 查询是否有数据
        $sql1 = "SELECT `id`,`phone` FROM {$tb} WHERE `uid`=:uid AND `is_deleted`= 0 AND `register_type`=:registerType ORDER BY `id` desc LIMIT 1";
        $aData = by::dbMaster()->createCommand($sql1, [':uid' => $uid, ':registerType' => $registerType])->queryOne();
        $mallId = $aData['id'] ?? 0;
        $data['register_type'] = CUtil::uint($registerType);
        $appId = $data['app_id'] ?? '';
        unset($data['app_id']);//去除无需存储的参数
        $sex      = $data['sex'] ?? 0;
        $birthday = $data['birthday'] ?? 0;
        //合规化参数
        $data = $this->__doWellFields($data);

        if (empty($mallId) && empty($isCreate)) return [true, ['用户不存在']];
        $transaction = by::dbMaster()->beginTransaction();
        if (empty($mallId)) {
            //执行插入方法
            try {
                if (!$phone) {
                    throw new MyExceptionModel('新增用户，手机号不存在！');
                }
                //不存在数据进行创建
                $data['create_time'] = time();
                $data['update_time'] = time();
                $s = by::dbMaster()->createCommand()->insert($tb, $data)->execute();
                if (!$s) {
                    throw new MyExceptionModel('创建用户失败！');
                }
                $mallId = by::dbMaster()->getLastInsertID();
                //查询是否已经存在该手机号码，如果存在，并且mallId不等于该mallId
                $existPhone = by::usersMall()->hasExistPhone($mallId, $phone);
                if ($existPhone) {
                    throw new MyExceptionModel('该手机号码已经被他人注册，注册失败！');
                }
                //更新t_phone 信息
                $data['mall_id'] = $mallId;
                $data['sex'] = $sex;
                $data['birthday'] = $birthday;
                list($s, $m) = $this->saveWxUser($phone, $data);
                if (!$s) {
                    throw new MyExceptionModel($m);
                }
                $transaction->commit();
                $data = array_merge($data, $m);
                //补充需要返回的数据
                $data['id'] = $appId;

                $user_id = $data['user_id'] ?? 0;
                //清除缓存
                by::Phone()->deleteRedisCache($user_id, $phone);
                by::users()->deleteRedisCache($user_id, $phone);
                by::userExtend()->deleteRedisCache($user_id);
                $this->deleteRedisCache($mallId, $user_id, $data['uid'] ?? 0);

                //加密数据
                $data = Mall::factory()->_appCryptReturnData($data, $registerType, $ifCenter);
                return [true, $data, self::ERR_CODE['OK']];
            } catch (\exception $e) {
                $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
                CUtil::debug($error, 'link_run_dreamehome_err');
                $transaction->rollBack();
                return [false, $e->getMessage()];
            }
        } else {
            try {
                //存在数据进行更新
                $data['update_time'] = time();
                $s = by::dbMaster()->createCommand()->update($tb, $data, ['id' => $mallId])->execute();
                if (!$s && $s !== 0) {
                    throw new MyExceptionModel(json_encode($s).'|更新商城用户失败！');
                }
                if ($phone) {
                    //查询是否已经存在该手机号码，如果存在，并且mallId不等于该mallId
                    $existPhone = by::usersMall()->hasExistPhone($mallId, $phone);
                    if ($existPhone) {
                        throw new MyExceptionModel('该手机号码已经被他人注册，修改失败！|'.$mallId.'|'.$phone);
                    }
                }
                //获取有关mallId所有的user_id
                $userList = by::users()->getUsersByMallId($mallId);
                CUtil::debug('用户信息|' . json_encode($userList), 'update_mall_user');
                $userIds = [];
                //更新用户信息
                if ($userList) {
                    $data['nick'] = $data['nick_name'] ?? '';
                    $data['sex'] = $sex;
                    $data['birthday'] = $birthday;
                    !YII_ENV_PROD && CUtil::debug(json_encode($data),'center_update');

                    foreach ($userList as $user) {
                        //todo 用户详情确认更新
                        $userParam = by::users()->getOneByUid($user['user_id']);
                        if ($userParam) {
                            //更新
                            by::users()->updateMembersInfo($user['user_id'], $data);
                        } else {
                            //新建
                            by::users()->saveMembersInfo($user['user_id'], $data);
                        }
                        by::usersMall()->updateMallsByIds([$mallId], $data);
                    }
                    $userIds = array_column($userList, 'user_id');
                }
                if ($userIds && $phone) {
                    //更新t_phone 信息
                    $updatePhoneData = [
                        'phone' => $phone,
                        'ctime' => time()
                    ];
                    //用户phone更新
                    list($status, $msg) = by::Phone()->updateRelationByMallId($mallId, $updatePhoneData, $userIds);
                    if (!$status) {
                        throw new MyExceptionModel('更新手机号码失败');
                    }
                }
                $transaction->commit();

                //补充需要返回的数据
                $data['id'] = $appId;
                //加密数据
                $data = Mall::factory()->_appCryptReturnData($data, $registerType);
                $data['user_ids'] = $userIds;

                //清除缓存
                foreach ($userIds as $item) {
                    by::Phone()->deleteRedisCache($item, $phone);
                    by::users()->deleteRedisCache($item, $phone);
                    by::userExtend()->deleteRedisCache($item);
                    $this->deleteRedisCache($mallId, $item, $data['uid'] ?? 0);
                }

                return [true, $data, self::ERR_CODE['OK']];
            } catch (\exception $e) {
                $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
                CUtil::debug($error, 'link_run_dreamehome_err');
                $transaction->rollBack();
                return [false, $e->getMessage()];
            }
        }
    }


    /**
     * @throws Exception
     */
    public function forceDelUserByPhone($phone): array
    {
        //1.查询主表数据
        $userInfo = by::users()->getWxUserByPhone($phone, false);
        $user_id = $userInfo['user_id'] ?? 0;
        if (empty($userInfo)) {
            return [false, "用户不存在或者为游客数据,不需要删除"];
        }
        $transaction = by::dbMaster()->beginTransaction();
        try {
            //2.删除附表数据
            $ruser_id = by::Rusers()->getUserIdByOpenUdId($userInfo['openudid'] ?? '', 1, false);
            if ($ruser_id) {
                $r1 = by::Rguide()->delDataById($ruser_id);
                $r2 = by::RuserExtend()->delDataById($ruser_id);
                $r3 = by::RuserGuide()->delDataById($ruser_id);
                $r4 = by::Rusers()->delDataById($ruser_id);
                $r5 = by::Rusers()->delDataDetailById($ruser_id);
                $r6 = by::RuserRecommend()->delDataById($ruser_id);
                CUtil::debug($r1 . $r2 . $r3 . $r4 . $r5 . $r6);
            }
            //3.删除主表数据
            if ($user_id) {
                $m1 = by::guide()->delDataById($user_id);
                $m2 = by::userExtend()->delDataById($user_id);
                $m3 = by::userGuide()->delDataById($user_id);
                $m4 = by::userRecommend()->delDataById($user_id);
                $m5 = by::users()->delDataById($user_id);
                $m6 = by::users()->delDataDetailById($user_id);
                $m7 = by::Phone()->delDataById($user_id);
                //4.清除缓存
                CUtil::debug($m1 . $m2 . $m3 . $m4 . $m5 . $m6 . $m7);
            }
            $transaction->commit();
            //删除缓存
            by::Rusers()->deleteRedisCache($ruser_id, true);
            by::RuserExtend()->deleteRedisCache($ruser_id);
            by::users()->deleteRedisCache($user_id, $phone);
            by::userExtend()->deleteRedisCache($user_id);
            by::Phone()->deleteRedisCache($user_id, $phone);
            by::login()->deleteRedisCache($user_id);
        } catch (\exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'wx_self_transfer');
            $transaction->rollBack();
            return [false, "删除用户失败"];
        }
        return [true, 'OK'];
    }

    /**
     *
     * 软删除（注销） 用户
     * @throws Exception
     */
    public function deprecated($id)
    {
        $id = CUtil::uint($id, 0);
        if ($id <= 0) {
            return [false, "用户不存在"];
        }

        $mallInfo = $this->getMallInfoById($id);
        if (empty($mallInfo)) {
            return [false, "用户不存在(2)"];
        }
        //将账号废弃
        $row = by::dbMaster()->createCommand()->update(self::tbName(), [
            'is_deleted' => 1
        ], ['id' => $id])->execute();

        if ($row <= 0) {
            return [false, "用户不存在(3)"];
        }
        //清除缓存
        $this->__delCache($id);

        return [true, 'OK'];
    }


    /**
     * @throws Exception
     */
    public function getMallsByUserId($userId, $cache = true)
    {
        $redis = by::redis('core');
        $redis_key = AppCRedisKeys::getMallsByUserId($userId);
        $aJson = $cache ? $redis->get($redis_key) : false;
        $aData = (array)json_decode($aJson, true);
        if ($aJson === false) {
            $tb = self::tbName();
            $tbPhone = PhoneModel::tbName();
            $sqlPhone = "SELECT `mall_id`,`phone` FROM {$tbPhone} WHERE `user_id`=:userId ORDER BY `ctime` DESC";
            $sql = "SELECT a.`id`,a.`uid`,a.`phone`,a.`nick_name`FROM {$tb} AS a INNER JOIN ({$sqlPhone}) AS b ON a.`id` = b.`mall_id` WHERE a.`is_deleted`= 0 ORDER BY a.`create_time` DESC";
            $aData = by::dbMaster()->createCommand($sql, [':userId' => $userId])->queryAll();
            CUtil::debug(by::dbMaster()->createCommand($sql, [':userId' => $userId])->getRawSql());
            $aData = empty($aData) ? [] : $aData;
            $redis->set($redis_key, json_encode($aData), ['EX' => empty($aData) ? 10 : 600]);
        }
        return $aData;
    }

    public function updateMallsByIds($mallIds, $userDetail): array
    {
        $userDetail = $this->__doWellFields($userDetail);
        if (count($userDetail) == 0){
            return [true, 'ok'];
        }
        $tb = self::tbName();
        foreach ($mallIds as $id) {
            by::dbMaster()->createCommand()->update($tb, $userDetail, ['id' => $id])->execute();
        }
        return [true, 'ok'];
    }


    /**
     * @param $mallId
     * @param $phone
     * @return string
     * @throws Exception
     */
    public function hasExistPhone($mallId, $phone): string
    {
        $tb = self::tbName();
        $sql = "SELECT `id` FROM {$tb} WHERE `phone`=:phone AND `is_deleted` = 0 AND `id` <>:mallId LIMIT 1";
        $command = by::dbMaster()->createCommand($sql);
        $command->bindParam(":phone", $phone);
        $command->bindParam(":mallId", $mallId);
        $info = $command->queryOne();
        $mallOldId = $info['id'] ?? '';
        if (intval($mallOldId) > intval($mallId)) {
            return $mallOldId;
        }
        return '';
    }


    public function GetDistinctUidsByUidOrPhone($uidArr=[], $phoneArr=[])
    {
        if(empty($uidArr) && empty($phoneArr)){
            return [];
        }

        // 获取表名
        $tb = self::tbName();

        // 构造SQL查询语句
        $sql = "SELECT MAX(`id`) as `max_id` FROM {$tb} WHERE `is_deleted` = 0";

        // 将uid数组和phone数组转换为逗号分隔的字符串，并添加到查询语句中
        $conditions = [];
        if (!empty($uidArr)) {
            $uidIn = implode(',', array_map(function ($item) {
                return "'$item'";
            }, $uidArr));
            $conditions[] = "`uid` IN ({$uidIn})";
        }
        if (!empty($phoneArr)) {
            $phoneIn = implode(',', array_map(function ($item) {
                return "'$item'";
            }, $phoneArr));
            $conditions[] = "`phone` IN ({$phoneIn})";
        }

        if (!empty($conditions)) {
            $sql .= " AND (" . implode(' OR ', $conditions) . ")";
        }

        $sql .= " GROUP BY `phone`";

        // 执行查询并获取结果
        $maxIds = by::dbMaster()->createCommand($sql)->queryAll();

        // 如果没有找到任何数据，返回一个空数组
        if (empty($maxIds)) {
            return [];
        }

        // 然后，根据这些id来获取完整的记录
        $maxIdIn = implode(',', array_map(function ($item) {
            return "'{$item['max_id']}'";
        }, $maxIds));
        $sql = "SELECT * FROM {$tb} WHERE `id` IN ({$maxIdIn})";
        return by::dbMaster()->createCommand($sql)->queryAll();
    }

    /**
     * 根据手机号获取uid
     * @param $phone
     * @return array
     */
    public function getUidByPhone($phone): array
    {
        // 转为字符串，用于查询时使用索引
        $phone = strval($phone);

        if (empty($phone)) {
            return [];
        }

        $items = self::find()->select('uid')->where(['phone' => $phone])->asArray()->all();

        return array_column($items, 'uid');
    }

    /**
     * 根据uid批量获取手机号
     * @param array $uids
     * @return array
     * @throws Exception
     */
    public function getPhoneListByUids(array $uids): array
    {
        // 转为字符串，用于查询时使用索引
        $uidStr = implode("','", $uids);

        $tb = self::tbName();
        $sql = "SELECT `uid`, `phone` FROM {$tb} WHERE `uid` IN ('$uidStr')";
        $command = by::dbMaster()->createCommand($sql);
        $info = $command->queryAll();

        $list = [];
        foreach ($info as $row) {
            $list[$row['uid']] = $row['phone'];
        }

        return $list;
    }

}
