CREATE TABLE `db_dreame`.`t_users_mall`
(
    `id`            int unsigned NOT NULL AUTO_INCREMENT,
    `nick_name`     varchar(50) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '昵称',
    `uid`           varchar(18) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '其他平台 UID',
    `phone`         varchar(20) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '电话号码',
    `phone_code`    varchar(8) COLLATE utf8mb4_general_ci  NOT NULL DEFAULT '' COMMENT '电话代码',
    `avatar`        varchar(200) COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '头像URL',
    `register_type` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '注册类型（dreamehome：1，游客：99）',
    `status`        tinyint DEFAULT '0' COMMENT '状态',
    `is_deleted`    tinyint unsigned NOT NULL DEFAULT '0' COMMENT '删除状态（0：未删除，1：已删除）',
    `create_time`   int unsigned NOT NULL DEFAULT  '0' COMMENT '注册时间',
    `update_time`   int unsigned NOT NULL DEFAULT  '0' COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `index_uid_phone` (`uid`,`phone`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=COMPACT COMMENT='追觅内部注册用户表';

ALTER TABLE `db_dreame`.`t_phone` ADD mall_id int unsigned NOT NULL DEFAULT '0' COMMENT '商城用户ID' after `user_id`;
