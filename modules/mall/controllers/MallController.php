<?php


namespace app\modules\mall\controllers;

use app\components\LockRedis;
use app\components\Mall;
use app\components\MallAuth;
use app\jobs\UpdateConsumeMoneyRankJob;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\CateService;
use RedisException;
use Yii;
use yii\db\Exception;
use yii\web\Controller;

class MallController extends Controller
{

    public $layout = false;
    public $enableCsrfValidation = false;
    public $headers;


    public function actionCenter()
    {
        $request = Yii::$app->request;
        $post = json_decode($request->getRawBody(), true);
        $this->headers = $request->headers;
        CUtil::debug('center-register：' . $request->getRawBody() . '|' . json_encode($this->headers), 'update_mall_user');
        //1.参数校验
        $headerData = $post['header'] ?? [];
        $payload = $post['payload'] ?? [];
        //返回参数处理
        $headerRes = $headerData;
        $headerRes['responseId'] = $headerRes['requestId'] ?? '';
        unset($headerRes['requestId']);
        if (empty($headerData)) {
            CUtil::json_center_response(401, "缺少请求参数", $headerRes);
        }
        //2.方法校验
        $payloadType = CUtil::getConfig('centerPayloadType', 'common', MALL_MODULE);
        $methodTag = $headerData['payloadType'] ?? '';
        $method = $payloadType[$methodTag];

        if (empty($methodTag) || empty($method)) {
            CUtil::json_center_response(401, "未找到对应方法", $headerRes);
        }
        $register_action = '_center_' . $method;
        list($status, $msg) = $this->$register_action($payload, $this->headers);
        if (!$status) {
            CUtil::json_center_response(401, $msg, $headerRes);
        }
        CUtil::json_center_response(200, 'OK', $headerRes, $msg);
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function _center_getThirdCate($data, $header): array
    {
        list($status, $data) = Mall::factory()->_format_dreamehome_data($data, 2, $header, 0, ['cate']);
        if (!$status) return [false, $data];
        // 获取请求中的类目别名
        $cate = $data['cate'] ?? 0;

        if (empty($cate)) {
            return [false, 'Invalid category'];
        }
        // 获取配件类目列表
        list($status, $ret) = CateService::getInstance()->getList(2, true, false);
        if (!$status) {
            return [false, 'No categories found'];
        }
        // 初始化响应数据
        $results = [];
        // 如果配件类目列表不为空，则继续处理
        if (!empty($ret['children'])) {
            // 将配件类目重新组织为以ID为索引的关联数组
            $formattedArray = [];
            foreach ($ret['children'] as $child) {
                $formattedArray[$child['cate_alias']] = $child;
            }

            // 根据请求的类目ID获取对应的子类目数据
            $partCateData = $formattedArray[$cate] ?? [];

            // 如果子类目数据不为空，则提取其子类目信息
            if (!empty($partCateData['children'])) {
                foreach ($partCateData['children'] as $value) {
                    $results[] = [
                        'id' => $value['id'],
                        'name' => $value['name']
                    ];
                }
            }
        }

        // 输出结果
        return [true, $results];
    }

    public function _center_SyncConsumeMoney($data, $header): array
    {
        $post = $data;
        list($status, $data) = Mall::factory()->_format_dreamehome_data($data, 2, $header, 0, [], false);
        if (!$status) return [false, $data];

        CUtil::debug('同步消费金请求参数' . json_encode(array_merge($post, ['pass' => '****']), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'info.sync.consume_money');

        $request_id = $post['request_id'] ?? '';
        $uid = $post['uid'] ?? '';
        $user_id = (int) ($post['user_id'] ?? 0);
        $invitee_id = (int) ($post['invitee_id'] ?? 0);
        $event_name = $post['event_name'] ?? '';                 // 事件名称
        $event_code = $post['event_code'] ?? '';                 // 事件编号
        $op_type = (int) ($post['op_type'] ?? 0);               // 操作类型 -1=减少，1=增加
        $consume_money = (int) ($post['consume_money'] ?? 0);   // 消费金（单位分） 增加时正整数，减少时负数
        if (empty($request_id)) {
            CUtil::debug('API-处理消费金失败: request_id不能为空', 'err.SyncConsumeMoney');
            return [false, 'request_id不能为空'];
        }
        if (empty($uid)) {
            CUtil::debug(sprintf('API-处理消费金失败: uid不能为空 request_id：%s', $request_id), 'err.SyncConsumeMoney');
            return [false, 'uid不能为空'];
        }
        // if (empty($user_id)) {
        //     CUtil::debug(sprintf('API-处理消费金失败: user_id不能为空 request_id：%s', $request_id), 'err.SyncConsumeMoney');
        //     return [false, 'user_id不能为空'];
        // }
        if (empty($op_type)) {
            CUtil::debug(sprintf('API-处理消费金失败: 操作类型错误 request_id：%s', $request_id), 'err.SyncConsumeMoney');
            return [false, '操作类型错误'];
        }
        if (empty($consume_money)) {
            CUtil::debug(sprintf('API-处理消费金失败: 消费金不能为空 request_id：%s', $request_id), 'err.SyncConsumeMoney');
            return [false, '消费金不能为空'];
        }
        $redis = by::redis();
        
        $result_key = CUtil::getAllParams(__FUNCTION__, 'result', $request_id);
        // 设置分布式锁key
        $unique_key = CUtil::getAllParams(__FUNCTION__, 'lock', $request_id);
        // 设置当前请求的唯一值，用于当前请求只能释放自己的锁
        $unique_val = md5($unique_key.microtime(true).uniqid());
        $mux = new LockRedis();
        
        try {
            // 加锁
            $lock = $mux->lock($unique_key, $unique_val, 10);
            if (! $lock) {
                throw new BusinessException('处理中，请勿重复操作');
            }
            
            $result = $redis->get($result_key);
            if (! $result) {
                // 没传user_id的使用uid查询
                if (empty($user_id)) {
                    $mallInfo = by::usersMall()->getMallInfoByUid($uid, false);
                    $mallId = $mallInfo['id'] ?? 0;
                    if ($mallId) {
                        $userInfo = by::users()->getUsersByMallId($mallId, false);
                        
                        if ($userInfo) {
                            $user_id = $userInfo[0]['user_id'] ?? 0;
                            if (empty($user_id)) {
                                CUtil::debug(sprintf('API-处理消费金失败: %s 未查询到uid对应的用户 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                                throw new BusinessException('未查询到uid对应的用户1');
                            }
                        } else {
                            CUtil::debug(sprintf('API-处理消费金失败: %s 未找到mallId对应的用户 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                            throw new BusinessException('未查询到uid对应的用户2');
                        }
                    } else {
                        CUtil::debug(sprintf('API-处理消费金失败: %s 未找到对应的mall表记录 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                        throw new BusinessException('uid不存在');
                    }
                }
                
                $invitee_id = empty($invitee_id) ? $user_id : $invitee_id;
                if ($op_type > 0) {
                    $status = byNew::UserShopMoneyModel()->AddOrSubtract($user_id, 'add', 2, $consume_money, $invitee_id, sprintf('%s:%s', $event_code, $event_name));
                    \Yii::$app->queue->push(new UpdateConsumeMoneyRankJob([
                        'user_id' => $user_id,
                        'consume_money' => $consume_money,
                        'request_id' => $request_id,
                    ]));
                } else {
                    $status = byNew::UserShopMoneyModel()->AddOrSubtract($user_id, 'subtract', 2, $consume_money, $invitee_id, sprintf('%s:%s', $event_code, $event_name));
                }

                if (! $status) {
                    CUtil::debug(sprintf('API-处理消费金失败 status：false request_id：%s', $request_id), 'err.SyncConsumeMoney');
                    throw new BusinessException('处理消费金失败');
                }
                $redis->set($result_key, json_encode($post, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), ['EX' => 1800]);
                $mux->freed($unique_key, $unique_val);
                // CUtil::json_response(1, 'ok');
                return [true, []];
            } else {
                $mux->freed($unique_key, $unique_val);
                // CUtil::json_response(1, 'ok');
                return [true, []];
            }
        } catch (\Throwable $e) {
            $mux->freed($unique_key, $unique_val);
            // CUtil::json_response(-1, sprintf('处理消费金失败：%s', $e->getMessage()));
            return [false, sprintf('处理消费金失败：%s request_id：%s', $e->getMessage(), $request_id)];
        }
    }
}
