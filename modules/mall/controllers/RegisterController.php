<?php

namespace app\modules\mall\controllers;

use app\components\Crm;
use app\components\Mall;
use app\models\by;
use app\models\CUtil;
use Yii;
use yii\db\Exception;
use yii\web\Controller;


class RegisterController extends Controller
{
    public $layout = false;
    public $enableCsrfValidation = false;
    public $register_type;
    public $user_info;
    public $headers;
    public $code;

    /**
     * @param int $register_type
     * @return string
     * 获得注册方式
     */
    protected function _getLoginMethod(int $register_type = 0)
    {
        $loginControl = CUtil::getConfig('registerControl', 'common', MALL_MODULE);
        $method = $loginControl[$register_type] ?? "";
        $method = "_register_by_{$method}";
        if (!method_exists($this, $method)) {
            return false;
        }

        return $method;
    }

    /**
     * @throws Exception
     * 注册中央处理器
     */
    public function actionIndex()
    {
        $request = Yii::$app->request;
        $post = json_decode($request->getRawBody(),true);
        CUtil::debug($request->getRawBody());
        $this->headers =  $request->headers;
        $this->user_info = $post['userInfo'] ?? '';
        $this->register_type = $post['registerType'] ?? '';
        if (empty($this->user_info) || empty($this->register_type) || empty($this->headers)) {
            CUtil::json_response(-1, "缺少注册参数");
        }
        $register_action = $this->_getLoginMethod($this->register_type);
        if (!$register_action) {
            CUtil::json_response(-1, "不支持的注册方式");
        }

        $tenantId = CUtil::getTenantIdByHeaders();
        $commonTenantId = CUtil::getConfig('tenantId', 'member', MAIN_MODULE);
        if($tenantId && $tenantId != $commonTenantId){
            CUtil::json_response(1, "不支持的用户类型");
        }

        $user_info = $this->$register_action($this->user_info, $this->register_type, $this->headers);

        CUtil::json_response(1, "OK", $user_info);
    }



    /**
     * @param $userInfo
     * @param $registerType
     * @param $headers
     * @return mixed
     * @throws Exception
     */
    protected function _register_by_dreamehome($userInfo, $registerType,$headers)
    {
        list($status,$data)=Mall::factory()->_format_dreamehome_data($userInfo, $registerType,$headers);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        //数据处理
        list($status,$msg)	= by::usersMall()->saveMallInfo($data,$registerType);
        if(!$status){
            CUtil::json_response(-1, $msg);
        }

        return $msg;
    }

    /**
     * @throws Exception
     */
    public function actionBatchRegister()
    {
        $request = Yii::$app->request;
        $post = json_decode($request->getRawBody(),true);
        $headers =  $request->headers;
        $user_info = $post['userInfo'] ?? '';
        $register_type = $post['registerType'] ?? '';
        if (empty($user_info) || empty($register_type) || empty($headers)) {
            CUtil::json_response(-1, "缺少注册参数");
        }
        $register_action = $this->_getLoginMethod($register_type);
        if (!$register_action) {
            CUtil::json_response(-1, "不支持的注册方式");
        }
        $returnData = [];
        if (is_array($user_info)) {
            foreach ($user_info as $user) {
                //数据解密
                list($status, $data) = Mall::factory()->_format_dreamehome_data($user, $register_type, $headers);
                if (!$status) {
                    $returnData[] = [
                        'uid' => $user['uid'] ?? '',
                        'id' => $user['id'] ?? '',
                        'userId' => '',
                        'status' => false,
                        'msg' => $data
                    ];
                    continue;
                }
                //数据处理
                list($status, $msg) = by::usersMall()->saveMallInfo($data, $register_type);
                if (!$status) {
                    $returnData[] = [
                        'uid' => $user['uid'] ?? '',
                        'id' => $user['id'] ?? '',
                        'userId' => '',
                        'status' => false,
                        'msg' => $msg
                    ];
                    continue;
                }

                $returnData[] = [
                    'uid' => $user['uid'] ?? '',
                    'id' => $user['id'] ?? '',
                    'userId' => $msg['userId'] ?? '',
                    'status' => $msg && isset($msg['userId']) && isset($msg['uid']),
                    'msg' => 'OK'
                ];
            }
        }
        CUtil::json_response(1, "OK", $returnData);
    }

}
