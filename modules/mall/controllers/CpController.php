<?php

/**
 * 内部第三方接口
 */

namespace app\modules\mall\controllers;

use app\components\WeiXin;
use Yii;
use app\models\by;
use app\models\CUtil;
use yii\web\Controller;

class CpController extends Controller
{

    const API_CONFIG = YII_ENV_PROD ?
        [
            '10001' => [
                'SECURITY_KEY' => 'ZHJlYW1lX21hbGw6NEw2cmY1U29CZXN4bE1nYw==',
                'IPS' => [],
                'LIMIT' => 1000,
                'API_LIST' => ['base-info', 'get-token'],
            ],
        ] : [
            //自己公司用
            '10001' => [
                'SECURITY_KEY' => 'ZHJlYW1lX21hbGw6NEw2cmY1U29CZXN4bE1nYw==',
                'IPS' => [],
                'LIMIT' => 1000,
                'API_LIST' => ['base-info', 'get-token'],
            ],
        ];

    const REDIRECT_URI = YII_ENV_PROD ? '' : ' ';
    //无需验证方法
    const NO_AUTH_ACTINO = ['redirect-uri'];

    protected $_client_id = null;                         //渠道id
    protected $_security_key = null;                         //签名私钥
    protected $_ips = [];                           //ip白名单
    protected $_limit = 0;                            //频率限制
    protected $_api_list = [];                           //接口权限
    protected $_redirect_uri = '';                           //授权后回调地址
    protected $_aPost = [];                           //临时接收参数
    protected $_headers = [];                           //临时接收参数

    /**
     * 初始化基础参数
     */
    protected function _initBase()
    {
        $this->_client_id = CUtil::getRequestParam("body", 'client_id');
        if (empty($this->_client_id)) $this->_client_id = 10001;

        $api_config = self::API_CONFIG[$this->_client_id] ?? [];

        if (empty($api_config)) {
            CUtil::json_response(-1, '无效的授权信息');
        }

        $this->_security_key = $api_config['SECURITY_KEY'] ?? '';
        $this->_ips = $api_config['IPS'] ?? [];
        $this->_limit = $api_config['LIMIT'] ?? 10000;
        $this->_api_list = $api_config['API_LIST'] ?? [];
        $this->_redirect_uri = $api_config['REDIRECT_URI'] ?? '';
        $this->_headers = Yii::$app->request->headers;

        $this->_aPost = CUtil::getRequestParam(null, null, null);
        if (Yii::$app->request->isGet) {
            if (isset($this->_aPost['r'])) {
                unset($this->_aPost['r']);
            }

            if (isset($this->_aPost['redirect_uri_params'])) {
                $this->_aPost['redirect_uri_params'] = urlencode($this->_aPost['redirect_uri_params']);
            }
        }
        $this->_client_id && $this->_aPost['client_id'] = $this->_client_id;
    }


    protected function _isValidAuth(string $security_key = '', $postHeaders = [])
    {
        if (empty($security_key)) {
            CUtil::json_response(-1, '设置权限验证获取失败~');
        }
        $authData = explode(' ', $postHeaders['Authorization'])[1] ?? '';
        if (empty($authData)) {
            CUtil::json_response(-1, '缺少权限验证~');
        }
        //字符串截取
        $encodedData = empty($authData) ? '' : str_replace(' ', '+', $authData);
        //权限判断
        if ($encodedData != $security_key) {
            CUtil::json_response(-1, '权限验证失败~');
        }
    }


    /**
     * @param $action_id
     * 频率限制
     */
    protected function _isValidLimit($action_id)
    {
        $unique_key = CUtil::getAllParams(__FUNCTION__, $action_id);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->_client_id, $unique_key, 1, "EX", $this->_limit);

        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候~');
        }
    }


    /**
     * ip白名单校验
     */
    protected function _isValidIp()
    {
        if (!empty($this->_ips)) {
            $client_ip = CUtil::get_client_ip();
            if (!in_array($client_ip, $this->_ips)) {
                CUtil::debug("{$this->_client_id}|{$client_ip}", 'cp.ip.white');
                CUtil::json_response(-1, '请联系管理员添加白名单');
            }
        }
    }

    /**
     * @param $action_id
     * 接口权限校验
     */
    protected function _isValidPermissions($action_id)
    {
        if (!empty($this->_api_list)) {
            if (!in_array($action_id, $this->_api_list)) {
                CUtil::debug("{$this->_client_id}|{$action_id}", 'inter-cp.api.white');
                CUtil::json_response(-1, '请联系管理员添加权限');
            }
        }
    }

    /**
     * @param $action_id
     * 记录请求日志
     */
    protected function _saveReqLog($action_id)
    {
        $client_ip = CUtil::get_client_ip();
        $params = json_encode($this->_aPost);
        CUtil::debug("{$client_ip}|$action_id|$params", 'inter-cp.req.log');
    }

    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action)
    {
        $action_id = $action->controller->action->id;

        if (in_array($action_id, self::NO_AUTH_ACTINO)) {
            return true;
        }

        $this->_initBase();

        $this->_saveReqLog($action_id);

        $this->_isValidPermissions($action_id);

        $this->_isValidLimit($action_id);

        if ($action_id != 'authorize') {

            $this->_isValidIp();
        }

        //权限验证
        $this->_isValidAuth($this->_security_key, $this->_headers);

        return true;
    }


    /**
     * @return void
     * 微信小程序基础配置信息
     *
     */
    public function actionBaseInfo()
    {
        $data = [
            'wx_env' => YII_ENV,
        ];
        //1.小程序APPID
        $config = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
        $data['wx_sdk_config'] = $config;
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * 统一获取AccessToken
     */
    public function actionGetToken()
    {
        list($status, $ret) = WeiXin::factory()->getUniqueAccessToken();
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok', ['token' => $ret]);
    }


}
