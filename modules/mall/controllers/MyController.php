<?php

namespace app\modules\mall\controllers;


use app\components\Crm;
use app\components\Mall;
use app\jobs\UpdateCoCreateUserJob;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\mall\models\CommModel;
use Yii;
use yii\db\Exception;
use yii\web\Controller;


class MyController extends Controller
{
    public $layout = false;
    public $enableCsrfValidation = false;
    public $user_info;
    public $headers;
    public $code;
    public $wxSyncCenterLock = 1;



    /**
     * 批量更新APP用户信息
     * @throws Exception
     */
    public function actionBatchUpdateUsers()
    {
        return false;
        $request = Yii::$app->request;
        $post = json_decode($request->getRawBody(),true);
        $headers =  $request->headers;
        CUtil::debug('batch-update-user:'.json_encode($post).'|'.json_encode($headers));
        $user_info = $post['userInfo'] ?? '';
        $register_type = $post['registerType'] ?? '';
        if (empty($user_info) || empty($register_type) || empty($headers)) {
            CUtil::json_response(-1, "缺少更新参数");
        }
        $loginControl = CUtil::getConfig('registerControl', 'common', MALL_MODULE);
        $method = $loginControl[$register_type] ?? "";
        if(empty($method)){
            CUtil::json_response(-1, "不支持的更新方式");
        }

        $returnData = [];
        if (is_array($user_info)) {
            foreach ($user_info as $user) {
                //数据解密
                list($status, $data) = Mall::factory()->_format_dreamehome_data($user, $register_type, $headers);
                if (!$status) {
                    $returnData[] = [
                        'uid' => $user['uid'] ?? '',
                        'id' => $user['id'] ?? '',
                        'userId' => '',
                        'status' => false,
                        'msg' => $data
                    ];
                    continue;
                }

                //数据处理
                list($status, $msg) = by::usersMall()->updateMallInfo($data, $register_type);
                if (!$status) {
                    $returnData[] = [
                        'uid' => $user['uid'] ?? '',
                        'id' => $user['id'] ?? '',
                        'userId' => '',
                        'status' => false,
                        'msg' => $msg
                    ];
                    continue;
                }
                $userIds = $msg['user_ids']??[];
                $returnData[] = [
                    'uid' => $user['uid'] ?? '',
                    'id' => $user['id'] ?? '',
                    'userId' => implode(',',$userIds),
                    'status' => $msg && isset($msg['userId']) && isset($msg['uid']),
                    'msg' => 'OK'
                ];
            }
        }
        CUtil::json_response(1, "OK", $returnData);
    }

    /**
     *
     * 强制删除用户，谨慎操作
     * @return void
     * @throws Exception
     */
    public function actionDeleteUserByPhone()
    {
        $provider = CUtil::getRequestParam("post", 'provider', '');
        $phone = CUtil::getRequestParam("post", 'phone', '');
        if (YII_ENV_PROD || empty($provider) || empty($phone)) {
            CUtil::json_response(-1, "非法操作！");
        }
        if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
            CUtil::json_response(-1, "非法手机号！");
        }

        list($status,$msg) = by::usersMall()->forceDelUserByPhone($phone);
        if(!$status){
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, "删除用户成功");
    }


    /**
     * @throws Exception
     */
    public function actionCenter()
    {
        $request = Yii::$app->request;
        $post = json_decode($request->getRawBody(),true);
        $this->headers =  $request->headers;

        //租户区分
        $tenantId = CUtil::getTenantIdByHeaders();
        $commonTenantId = CUtil::getConfig('tenantId', 'member', MAIN_MODULE);
        if($tenantId && $tenantId != $commonTenantId){
            CUtil::json_response(1, "不支持的用户类型");
        }

        CUtil::debug('center-register：'.$request->getRawBody().'----'.json_encode($this->headers),'update_mall_user');
        //1.参数校验
        $headerData = $post['header']??[];
        $payload = $post['payload']??[];
        //返回参数处理
        $headerRes = $headerData;
        $headerRes['responseId'] = $headerRes['requestId']??'';
        unset($headerRes['requestId']);
        if(empty($headerData)||empty($payload)){
            CUtil::json_center_response(401, "缺少请求参数",$headerRes);
        }
        //2.方法校验
        $payloadType = CUtil::getConfig('centerPayloadType', 'common', MALL_MODULE);
        $methodTag = $headerData['payloadType'] ?? '';
        $method = $payloadType[$methodTag];
        if(empty($methodTag)||empty($method)){
            CUtil::json_center_response(401, "未找到对应方法",$headerRes);
        }
        $register_action = '_center_'.$method;
        list($status,$msg) = $this->$register_action($payload, 1, $this->headers);
        if(!$status){
            CUtil::json_center_response(401, $msg,$headerRes);
        }
        CUtil::json_center_response(200, 'OK', $headerRes, $msg);
    }


    /**
     * 更新用户信息
     * @throws Exception
     */
    public function _center_infoChanged($data, $register_type, $header): array
    {
        //3.参数处理
        list($status,$data) = Mall::factory()->_format_dreamehome_data($data, $register_type,$header,1);
        if(!$status)  return [false,$data];
        !YII_ENV_PROD && CUtil::debug(json_encode($data),'center_update');
        //4.数据推送
        list($status,$msg)	= by::usersMall()->updateMallInfo($data,1,0,1);
        if(!$status){
           return [false,$msg];
        }

        //6.清除token缓存
        Mall::factory()->__delGenerateJwtTokenKey($msg['userId'] ?? 0);

        //7.变更共创人员信息
        if($msg['userId']??0) {
            usleep(200);
            \Yii::$app->queue->push(new UpdateCoCreateUserJob(['user_id' => $msg['userId'], 'type' => "change_phone"]));
            \Yii::$app->queue->push(new UpdateCoCreateUserJob(['user_id' => $msg['userId'], 'type' => "change_nick"]));
        }

        return [true, $msg];

    }


    /**
     * @throws Exception
     */
    public function _center_closeAccount($data, $register_type, $header): array
    {
        $config = CUtil::getConfig('wx_version_ctrl','common',MAIN_MODULE);
        $this->wxSyncCenterLock = $config['wx_sync_center_lock']??1;
        //3.参数处理
        list($status,$data) = Mall::factory()->_format_dreamehome_data($data, $register_type,$header,1,['uid']);
        if(!$status)  return [false,$data];
        if(empty($data['uid'])) return [false,'参数不存在'];
        //4.数据注销
        //a.根据uid获取最新的user_id
        $mallInfo = by::usersMall()->getMallInfoByUid($data['uid'],false);
        $mallId = $mallInfo['id'] ?? 0;
        $returnData = [];
        $statusList = [];
        if($mallId) {
            $userInfo = by::users()->getUsersByMallId($mallId,false);

            if($userInfo){
                foreach ($userInfo as $user){
                       list($status,$msg) = by::users()->Deprecated($user['user_id'],1,$this->wxSyncCenterLock);
                       $statusList[] = $status;
                        $returnData[] = [
                            'uid' => $data['uid'],
                            'user_id' => $user['user_id'],
                            'status' => empty($status)?$msg:'success',
                        ];
                }
            }
        }
        if(in_array(false,$statusList)){
            return [false,'注销用户失败！'];
        }else{
            return [true,$returnData];
        }
    }

}


