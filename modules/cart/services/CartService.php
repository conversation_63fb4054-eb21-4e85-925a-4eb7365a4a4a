<?php

namespace app\modules\cart\services;

use app\components\RateLimiter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\goods\models\GmainModel;
use app\modules\main\services\GoodsPlatformService;
use RedisException;
use yii\db\Exception;

class CartService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    const MACHINE_TYPE = [
        'main' => 'main',
        'part' => 'part',
    ];

    /**
     * @param array $arr
     * @param int $sPriceType
     * @return array
     * @throws Exception 批量加入购物车
     */
    public function batchAddCart(array $arr = [], int $sPriceType = 0): array
    {
        $user_id = $arr['user_id'] ?? '';
        $user_id = strlen($user_id) > 11 ? 0 : CUtil::uint($user_id);
        if (empty($user_id)) {
            return [false, "用户未授权，无法加入购物车"];
        }

        // 生成唯一的请求标识并检查请求频率
        $unique_key  = CUtil::getAllParams(__FUNCTION__, $user_id);
        $rateLimiter = new RateLimiter();
        $isAllowed   = $rateLimiter->checkRateLimit($unique_key, $user_id, 10, 1);

        if (!$isAllowed) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        if (!$arr) {
            return [false, '参数有误,请重试！'];
        }

        $gcombines = $arr['gcombines'] ?? [];
        if (!$gcombines) {
            return [false, '参数有误,请重试！'];
        }

        $gcombines = json_decode($gcombines, true) ?? null;
        if (empty($gcombines)) {
            return [false, '参数有误,请重试！'];
        }

        // 购物车中的商品数量
        $cartGoodsNums = by::cart()->getCartGoodsNum($user_id);

        $row = [];
        foreach ($gcombines as $item) {
            $gid = CUtil::uint($item['gid'] ?? 0);
            $sid = CUtil::uint($item['sid'] ?? 0);
            $num = CUtil::uint($item['num'] ?? 0);

            $ginfo = by::Gmain()->GetOneByGidSid($gid, $sid, true, false, $sPriceType);
            //预售商品不校验库存
            $is_presale = $ginfo['is_presale'] ?? 0;
            $type       = $item['type'] ?? '';

            if (empty($ginfo)) {
                return [false, '商品不存在'];
            }
            $num = CUtil::uint($num);
            if (empty($num)) {
                return [false, '请选择购买数量'];
            }
            // if ($ginfo['type'] != by::Gmain()::TYPE['COMMON']) {
            if (! in_array($ginfo['type'], [GmainModel::TYPE['COMMON'], GmainModel::TYPE['YOUXUAN'], GmainModel::TYPE['YANXUAN']])) {
                return [false, $ginfo['name'] . '请直接购买'];
            }

            if ($sid && empty($ginfo['spec'])) {
                return [false, '商品属性不存在'];
            }

            if ($ginfo['atype'] != by::Gtype0()::ATYPE['SPEC'] && empty($sid)) {
                return [false, '请选择商品属性'];
            }

            // 增加查询购物车数量，校验限购数量
            $cartGoodsNum = $cartGoodsNums[$gid][$sid] ?? 0;
            if ($ginfo['limit_num'] != 0 && ($num + $cartGoodsNum) > $ginfo['limit_num']) {
                return [false, $ginfo['name'] . '超出购买限制'];
            }

            //主机给出提示  配件不给提示 每次添加直接取最大库存
            if ($type == self::MACHINE_TYPE['main']) {
                if (empty($is_presale)) {
                    $sku       = $ginfo['spec']['sku'] ?? $ginfo['sku'];
                    $stock_num = by::GoodsStockModel()->OptStock($sku);
                    //库存不足给最大库存
                    if ($stock_num < $num) {
                        return [false, $ginfo['name'] . '库存不足'];
                    }
                }
            } else {
                if (empty($is_presale)) {
                    $sku       = $ginfo['spec']['sku'] ?? $ginfo['sku'];
                    $stock_num = by::GoodsStockModel()->OptStock($sku);
                    //配件库存不足给最大库存
                    if ($stock_num < $num) {
                        $num = $stock_num;
                    }
                }
            }

            $row[] = [
                'user_id' => $user_id,
                'gid'     => $gid,
                'sid'     => $sid,
                'num'     => $num,
                'c_time'  => intval(START_TIME)
            ];

        }

        //添加购物车
        list($status, $msg) = by::cart()->batchSaveData($user_id, $row);
        if (!$status) {
            return [false, $msg];
        }
        return [true, '操作成功'];
    }


    /**
     * @param array $params
     * @param int $priceType
     * @return array
     * @throws Exception
     */
    public function plugBatchAddCart(array $params = [], int $priceType = 0): array
    {
        // 参数校验
        if (empty($params)) {
            return [false, '参数有误，请重试！'];
        }

        $userId = $params['user_id'] ?? 0;
        $productCombinations = json_decode($params['gcombines'] ?? '[]', true);
        if (empty($userId) || empty($productCombinations)) {
            return [false, '参数有误，请重试！'];
        }

        // 获取购物车中的商品数量
        $existingCartItems = by::cart()->getCartGoodsNum($userId);

        // 组装待添加的商品数据
        $cartItemsToAdd = [];
        foreach ($productCombinations as $product) {
            $productId = CUtil::uint($product['gid'] ?? 0);
            $specId = CUtil::uint($product['sid'] ?? 0);
            $quantity = CUtil::uint($product['num'] ?? 0);

            // 商品已在购物车中，跳过
            if (isset($existingCartItems[$productId])) {
                continue;
            }

            // 获取商品信息
            $productInfo = by::Gmain()->GetOneByGidSid($productId, $specId, true, false, $priceType);
            if (empty($productInfo)) {
                return [false, '商品不存在'];
            }

            // 校验购买数量
            if ($quantity <= 0) {
                return [false, '请选择购买数量'];
            }

            // 校验商品属性
            if ($specId && empty($productInfo['spec'])) {
                return [false, '商品属性不存在'];
            }

            if ($productInfo['atype'] != by::Gtype0()::ATYPE['SPEC'] && empty($specId)) {
                return [false, '请选择商品属性'];
            }

            // 添加商品到批量添加列表
            $cartItemsToAdd[] = [
                    'user_id' => $userId,
                    'gid'     => $productId,
                    'sid'     => $specId,
                    'num'     => $quantity,
                    'c_time'  => intval(START_TIME),
            ];
        }

        // 如果有待添加的商品，批量保存到购物车
        if (!empty($cartItemsToAdd)) {
            list($saveStatus, $message) = by::cart()->batchSaveData($userId, $cartItemsToAdd);
            if (!$saveStatus) {
                return [false, $message];
            }
        }

        return [true, '操作成功'];
    }



    /**
     * @param $user_id
     * @param $page
     * @param $page_size
     * @param int $platformId
     * @param string $ids
     * @param int $sPriceType
     * @return array
     * @throws Exception
     * @throws RedisException 获取购物车列表
     */
    public function getCartList($user_id, $page, $page_size, int $platformId = 0, string $ids = '', int $sPriceType = 0): array
    {
        // 获取购物车列表
        $aData           = by::cart()->getList($user_id, $page, $page_size, '', $platformId);

        $goodsStockModel = by::GoodsStockModel();

        foreach ($aData as $k => &$val) {
            $ginfo = by::Gmain()->GetOneByGidSid($val['gid'], $val['sid'], true, true, $sPriceType);

            if (empty($ginfo)) {
                unset($aData[$k]);
                continue;
            }

            // 获取平台信息
            $platform_ids = $ginfo['platform_ids'] ?? [];
            if (!empty($platform_ids) && !in_array($platformId, $platform_ids)) {
                unset($aData[$k]);
                continue;
            }

            // 获取 SKU 和库存
            $sku       = $ginfo['spec']['sku'] ?? $ginfo['sku'];
            $stock_num = $goodsStockModel->OptStock($sku);

            // 处理预售商品库存
            $is_presale = $ginfo['is_presale'] ?? 0;
            if ($is_presale) {
                $stock_num = by::Gprestock()->OptStock($val['gid'], $val['sid']);
            }

            // 组装商品数据
            $val = $this->assembleProductData($val, $ginfo, $stock_num, $platformId);
        }

        return array_values($aData);
    }

    /**
     * @param $val
     * @param $ginfo
     * @param $stock_num
     * @param $platform_id
     * @return array
     * @throws Exception
     * 组装商品数据
     */
    private function assembleProductData($val, $ginfo, $stock_num, $platform_id): array
    {
        // 获取属性配置
        $av_ids   = $ginfo['spec']['av_ids'] ?? '';
        $attr_cnf = [];
        if ($av_ids) {
            $av_ids = json_decode($av_ids, true);
            foreach ($av_ids as $v1) {
                $cnf = by::Gav()->IdToName($v1);
                if (!empty($cnf)) {
                    $attr_cnf[] = $cnf;
                }
            }
            $ginfo['spec']['attr'] = $attr_cnf;
        }

        // 获取平台商品信息
        $platformInfo = GoodsPlatformService::getInstance()->getCurrentPlatform($platform_id, $ginfo);

        // 构建商品数据
        $g_data = [
            'name'                 => $ginfo['name'] ?? '',
            'images'               => $platformInfo['images'] ?? '',
            'cover_image'          => $platformInfo['cover_image'] ?? '',
            'market_image'         => $ginfo['market_image'] ?? '',
            'is_internal'          => $ginfo['is_internal'] ?? 0,
            'is_internal_purchase' => $ginfo['is_internal_purchase'] ?? 0,
            'is_ini'               => $ginfo['is_ini'] ?? 0,
            'is_presale'           => $ginfo['is_presale'] ?? 0,
            'presale_time'         => $ginfo['presale_time'] ?? 0,
            'deposit'              => $ginfo['deposit'] ?? 0,
            'expand_price'         => $ginfo['expand_price'] ?? 0,
            'start_payment'        => $ginfo['start_payment'] ?? 0,
            'end_payment'          => $ginfo['end_payment'] ?? 0,
            'surplus_time'         => $ginfo['surplus_time'] ?? 0,
            'gini_id'              => $ginfo['gini_id'] ?? 0,
            'gini_tag'             => $ginfo['gini_tag'] ?? '',
            'gini_etime'           => $ginfo['gini_etime'] ?? 0,
            'atype'                => $ginfo['atype'] ?? 0,
            'spec'                 => $ginfo['spec'] ?? [],
            'prod_status'          => (int)boolval($stock_num),
            'status'               => $ginfo['status'],
            'limit_num'            => $ginfo['limit_num'] ?? 0,
            'is_del'               => $ginfo['is_del'],
            'price'                => $ginfo['spec']['price'] ?? $ginfo['price'] ?? 0,
            'jd_baitiao_support'   => $ginfo['jd_baitiao_support'] ?? 0,
        ];

        $g_data['interest_free'] = $g_data['jd_baitiao_support']==1?byNew::PaymentActivityModel()->getBaiTiaoInst([$val['gid']]):0;

        // 预售信息处理
        if ($g_data['is_presale'] == 1) {
            $g_data['presale_info'] = [
                'is_presale'        => 1,
                'presale_time'      => $ginfo['presale_time'],
                'deposit'           => $ginfo['deposit'],
                'start_payment'     => $ginfo['start_payment'],
                'end_payment'       => $ginfo['end_payment'],
                'surplus_time'      => $ginfo['surplus_time'],
                'deposit_status'    => 0,
                'tail_order_status' => 0,
                'expand_price'      => $ginfo['expand_price'] ?? 0,
                'tail_price'        => sprintf('%.2f', bcsub($g_data['price'], $ginfo['expand_price'] ?? 0, 2))
            ];

            // 输出封装
            $g_data['presale_info'] = Response::responseList($g_data['presale_info'], [
                'is_presale'    => 'int',
                'presale_time'  => 'time|ms',
                'start_payment' => 'time|ms',
                'end_payment'   => 'time|ms',
                'surplus_time'  => 'is_int',
            ]);
        }

        // 输出封装
        $g_data = Response::responseList($g_data, [
            'is_presale'    => 'int',
            'presale_time'  => 'time|ms',
            'start_payment' => 'time|ms',
            'end_payment'   => 'time|ms',
            'surplus_time'  => 'is_int',
        ]);

        // 合并商品数据
        return array_merge($val, $g_data);
    }

    /**
     * @param $user_id
     * @param $platform_id
     * @return int
     * @throws Exception
     * @throws RedisException
     * 获取购物车数量
     */
    public function getCount($user_id, $platform_id): int
    {
        return by::cart()->getCount($user_id, '', $platform_id);
    }


}
