<?php

namespace app\modules\cart\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\goods\models\GmainModel;
use app\modules\main\models\UserCardModel;
use app\modules\main\models\CommModel;
use app\modules\main\services\TradeInService;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;

class CartModel extends CommModel
{
    protected $expire_time = 600;
    public static function tbName($user_id)
    {
        $mod = $user_id % 10;
        return "`db_dreame_goods`.`t_cart_{$mod}`";
    }

    private function __delCache($user_id)
    {
        $r_key = AppCRedisKeys::userCart($user_id);
        by::redis()->del($r_key);
    }

    /**
     * @param $user_id
     * @param string $ids
     * @param int $platform_id
     * @return int
     * @throws Exception
     * @throws RedisException
     * 获取总条数
     */
    public function getCount($user_id, string $ids = '', int $platform_id = 0): int
    {
        $r_key = AppCRedisKeys::userCart($user_id);
        $s_key = CUtil::getAllParams(__FUNCTION__, $ids, $platform_id);
        $redis = by::redis();
        $count = $redis->hGet($r_key, $s_key);

        if ($count === false) {
            // 构建 SQL 查询条件
            list($where, $param) = $this->__condition([
                'user_id'     => $user_id,
                'id'          => $ids,
                'platform_id' => $platform_id
            ]);

            $tb = self::tbName($user_id);

            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count = by::dbMaster()->createCommand($sql, $param)->queryScalar();

            $count = $count ?: 0;

            $redis->hSet($r_key, $s_key, $count);
        }

        return intval($count);
    }

    /**
     * @param $user_id
     * @param $page
     * @param $page_size
     * @param string $ids
     * @param int $platform_id
     * @return array
     * @throws Exception
     * @throws RedisException 获取购物车列表
     */
    public function getList($user_id, $page, $page_size, string $ids = '', int $platform_id = 0): array
    {
        $r_key = AppCRedisKeys::userCart($user_id);
        $s_key = CUtil::getAllParams($page, $page_size, $ids, $platform_id);
        $redis = by::redis();
        $aJson = $redis->hGet($r_key, $s_key);
        $aData = json_decode($aJson, true);

        if ($aJson === false) {
            list($offset, $page_size) = CUtil::pagination($page, $page_size);

            // 构建 SQL 查询条件
            list($where, $param) = $this->__condition([
                'user_id'     => $user_id,
                'id'          => $ids,
                'platform_id' => $platform_id
            ]);

            $tb  = self::tbName($user_id);  // 获取表名
            $sql = "SELECT `id`, `gid`, `sid`, `num` FROM {$tb} WHERE {$where} 
                ORDER BY `id` DESC LIMIT {$offset}, {$page_size}";  // 构建 SQL 查询语句

            // 执行查询
            $aData = by::dbMaster()->createCommand($sql, $param)->queryAll();
            $aData = $aData ?: [];  // 如果查询结果为空，返回空数组

            // 将结果存入 Redis
            $redis->hSet($r_key, $s_key, json_encode($aData));

            // 设置 Redis 键的过期时间
            CUtil::ResetExpire($r_key, empty($aData) ? 10 : $this->expire_time);
        }

        return $aData;  // 返回数据
    }

    /**
     * 获取购物车商品的数量，并根据gid、sid分组
     * @param $user_id
     * @return array
     * @throws Exception
     */
    public function getCartGoodsNum($user_id): array
    {
        $tb    = self::tbName($user_id);
        $sql   = "SELECT gid, sid, num FROM {$tb} WHERE user_id = :user_id";
        $items = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryAll();
        $data  = [];
        foreach ($items as $item) {
            $data[$item['gid']][$item['sid']] = $item['num'];
        }
        return $data;
    }

    private function __condition($input = []): array
    {
        $user_id     = $input['user_id'] ?? 0;
        $platform_id = $input['platform_id'] ?? 0;
        $id          = $input['id'] ?? '';


        $where = "`user_id` = :user_id";
        $param = [':user_id' => $user_id];


        if (!empty($id)) {
            $where .= " AND id IN ({$id})";
        }

        if (!empty($platform_id)) {
            $gIdsData = byNew::GoodsPlatformModel()->getDataList(['gid'], ['platform_id' => $platform_id]);
            $gIds     = array_column($gIdsData, 'gid');
            if (empty($gIds)) {
                $where .= " AND 1=2";
            } else {
                $where    .= " AND gid IN (" . implode(',', $gIds) . ")";
            }
        }
        return [$where, $param];
    }

    /**
     * @param $user_id
     * @param $gid
     * @param $sid
     * @param $num
     * @return array
     * @throws Exception
     * @throws RedisException
     * 添加数据
     */
    public function saveData($user_id, $gid, $sid, $num, $spriceType = 0)
    {
        $user_id = CUtil::uint($user_id);
        if (empty($user_id)) {
            return [false, '用户信息不存在'];
        }

        $ginfo = by::Gmain()->GetOneByGidSid($gid, $sid, true, false, $spriceType);

        $num   = CUtil::uint($num);
        if (empty($num)) {
            return [false, '请选择购买数量'];
        }

        if (empty($ginfo)) {
            return [false, '该商品不存在'];
        }

        // if ($ginfo['type'] != by::Gmain()::TYPE['COMMON']) {
        if (! in_array($ginfo['type'], [GmainModel::TYPE['COMMON'], GmainModel::TYPE['YOUXUAN'], GmainModel::TYPE['YANXUAN']])) {
            return [false, '该商品请直接购买'];
        }

        if ($sid && empty($ginfo['spec'])) {
            return [false, '该商品属性不存在'];
        }

        if ($ginfo['atype'] != by::Gtype0()::ATYPE['SPEC'] && empty($sid)) {
            return [false, '请选择商品属性'];
        }

        //预售商品不校验库存
        $is_presale = $ginfo['is_presale'] ?? 0;
        if(empty($is_presale)){
            $sku = $ginfo['spec']['sku'] ?? $ginfo['sku'];
            $stock_num  = by::GoodsStockModel()->OptStock($sku);
            if ($stock_num < $num) {
                return [false, '库存不足'];
            }
        }

        //事务改造
        $db = by::dbMaster();
        $trans = $db->beginTransaction();

        $tb = self::tbName($user_id);
        $sql = "INSERT INTO {$tb} (`user_id`, `gid`, `sid`,`num`, `c_time`) 
                    VALUES (:user_id, :gid, :sid,  :num, :c_time) 
                    ON DUPLICATE KEY UPDATE `num` = num + :num";
        $bind = [
            ':user_id'  => $user_id,
            ':gid'      => $gid,
            ':sid'      => $sid,
            ':num'      => $num,
            ':c_time'   => intval(START_TIME),
        ];
        try {
            $db->createCommand($sql, $bind)->execute();
            $trans->commit();
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.create_cart');
            return [false, '操作失败'];
        }

        $this->__delCache($user_id);
        return [true, 'ok'];
    }

    /**
     * @param $user_id
     * @param $id
     * @param $sid
     * @param $num
     * @return array
     * @throws Exception
     * 修改数量
     */
    public function modifyNum($user_id, $id, $sid, $num, $spriceType=0): array
    {
        $id      = CUtil::uint($id);
        $num     = CUtil::uint($num);
        $user_id = CUtil::uint($user_id);
        if (empty($id) || empty($num) || empty($user_id)) {
            return [false, '数据错误'];
        }

        $tb         = self::tbName($user_id);
        $save       = ['num' => $num];
        $del_id     = 0;
        $db         = by::dbMaster();

        if ($sid > 0) {
            $aList  = $this->getList($user_id, 1, 1, $id);
            $info   = reset($aList);
            $ginfo  = by::Gmain()->GetOneByGidSid($info['gid'], $sid, true, false, $spriceType);
            if (empty($ginfo)) {
                return [false, '该商品规格不存在'];
            }
            $save['sid'] = $sid;
            // 同一商品同一规格若已经存在 则需删除旧的
            $sql     = "SELECT `id`,`sid` FROM {$tb} WHERE `user_id`=:user_id AND `gid`=:gid AND `sid`=:sid AND `id`<>:id LIMIT 1";

            $exist   = $db->createCommand($sql, [':user_id'=>$user_id,':gid'=>$info['gid'],':sid'=>$sid,':id'=>$id])->queryOne();
            $exist && $del_id = $exist['id'];
        }

        $trans      = $db->beginTransaction();

        try {
            $del_id && $db->createCommand()->delete($tb, ['id' => $del_id])->execute();
            $db->createCommand()->update($tb, $save, ['id' => $id, 'user_id' => $user_id])->execute();
            $trans->commit();
            $this->__delCache($user_id);
            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.cart');
            return [false, '操作失败'];
        }
    }

    /**
     * @param $user_id
     * @param $ida
     * @return array
     * @throws Exception
     * 购物车删除
     */
    public function del($user_id, $ida)
    {
        $tb         = self::tbName($user_id);
        $ida        = array_unique($ida);
        $user_id    = CUtil::uint($user_id);
        if (empty($user_id)) {
            return [false, '请求重新登录'];
        }

        if (array_sum($ida) <= 0) {
            return [false, '无数据删除'];
        }

        $condition  = [
            'id'        => $ida,
            'user_id'   => $user_id,
        ];
        by::dbMaster()->createCommand()->delete($tb, $condition)->execute();

        $this->__delCache($user_id);
        return [true, 'ok'];
    }


    /**
     * @param string $user_id
     * @param array $arr
     * @param int $getChannel
     * @param int $spriceType
     * @param mixed $card_type
     * @param string $sub_price
     * @return array
     * @throws Exception 计算最优优惠劵
     */
    public function canUseCard(string $user_id, array $arr, int $getChannel = 0, int $spriceType = 0, $card_type = -1, $sub_price = null): array
    {
        $gcombines = $arr['gcombines'] ?? ""; //商品组合 [{"gid":1,"sid":0,"num":1,"gini_id":0}]
        $coupon_id = $arr['coupon_id'] ?? 0;
        $userCardModel = by::userCard();
        $gcombines = (array) json_decode($gcombines, true);

        if (empty($gcombines)) {
            return [true, ['id' => 0, 'consume_id'=> 0,'source_price' => 0, 'discount_price' => 0, 'cprice'=>0, 'consume_price'=> 0, 'real_price' => 0, 'type'=>0, 'consume_type'=>0]];
        }

        //校验商品是否有预售商品，有预售不显示优惠详情
        $containPresale = false;
        if (!empty($gcombines)) {
            foreach ($gcombines as $gcombine) {
                $goods_info = by::Gmain()->GetOneByGidSid($gcombine['gid'], $gcombine['sid']);
                $is_presale = $goods_info['is_presale'] ?? 0;
                $is_presale && $containPresale = true;
            }
        }

        if (!$containPresale) {
            list($status, $result) = $userCardModel->canUseCard($user_id, $card_type, $gcombines, $coupon_id, $getChannel, $spriceType, false, $sub_price);
            // 不报错，直接返回
        } else {
            $result = [];
        }

        $source_price = 0;
        foreach ($gcombines as $val) {
            $price = $this->getGoodsAllPrice($val['gid'], $val['sid'], $val['num'], $spriceType);
            $source_price = bcadd($source_price, $price, 2);
        }

        $consume_id = $result['consume']['id'] ?? 0;
        $consume_price = $result['consume']['cprice'] ?? 0;
        $consume_type = $result['consume']['type'] ?? by::userCard()::TYPE['consume'];
        //保障数据一致
        if(empty($consume_id)){
            $consume_price = 0;
        }

        $id = $result['select']['id'] ?? 0;
        $cprice = $result['select']['cprice'] ?? 0;
        //保障数据一致
        if(empty($id)){
            $cprice = 0;
        }

        $type = $result['select']['type'] ?? -1;

        $real_price = bcsub($source_price, $cprice, 2);
        $real_price = bcsub($real_price, $consume_price, 2);
        if (bccomp($real_price, 0, 2) < 0) { //最优选择价格不能小于0
            $real_price = 0;
        }

        return [true, ['id' => $id, 'consume_id' => $consume_id, 'source_price' => $source_price, 'discount_price' => bcadd($consume_price, $cprice, 2), 'cprice' => $cprice, 'consume_price' => $consume_price, 'real_price' => $real_price, 'type' => $type, 'consume_type' => $consume_type]];
    }

    /**
     * @param $gid
     * @param $sid
     * @param $num
     * @return array|string
     * @throws Exception
     * 获取商品组合总价格
     */
    public function getGoodsAllPrice($gid, $sid, $num, $spriceType = 0)
    {
        $gid = CUtil::uint($gid);
        $sid = CUtil::uint($sid);
        $num = CUtil::uint($num);

        if (empty($gid) || empty($num)) {
            return 0;
        }

        $gInfo = by::Gmain()->GetOneByGidSid($gid, $sid, true, true, $spriceType);
        if (!$gInfo) {
            return 0;
        }


        $is_presale = $gInfo['is_presale'] ?? 0;
        if ($is_presale == by::Gmain()::PTYPE['PRESALE']) {
            $price = $gInfo['deposit'] ?? $gInfo['price'];
        } else {
            $price = $gInfo['spec']['price'] ?? $gInfo['price'];
        }

        return bcmul($price, $num, 2);
    }

    /**
     * @param $user_id
     * @param $row
     * @return array
     * 批量加入购物车
     */
    public function batchSaveData($user_id, $row): array
    {
        if (!$user_id || !$row) {
            return [false, '参数有误,请重试！'];
        }
        //事务改造
        $db = by::dbMaster();
        $trans = $db->beginTransaction();

        $tb = self::tbName($user_id);
        $fields = ['user_id', 'gid', 'sid', 'num', 'c_time'];
        $fields = implode("`,`", $fields);
        try {
            foreach ($row as $value) {
                $num = $value['num'] ?? 0;
                $row = implode("','", $value);
                $sql = "INSERT INTO {$tb}  (`{$fields}`)
                    VALUES ('{$row}')
                  ON DUPLICATE KEY UPDATE `num` = `num` + {$num}";
                $db->createCommand($sql)->execute();
            }

            $trans->commit();
        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.create_cart');
            return [false, '操作失败'];
        }
        $this->__delCache($user_id);
        return [true, 'ok'];
    }



}
