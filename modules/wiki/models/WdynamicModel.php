<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 产品内容表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class WdynamicModel extends CommModel {

    CONST ListExpire  = 600;//列表缓存

    public $tb_adm_fields = [
        'id','did','title','cover_image'
    ];

    public $tb_fields = [
        'id','did','role_id','type','title','cover_image','video','images','text','author','avatar','ctime'
    ];

    public static function tbName($did): string
    {
        $mod = intval($did) % 10;
        return  "`db_dreame_wiki`.`t_wdynamic_{$mod}`";
    }


    /**
     * @param $id
     * @return string
     */
    private function __getOneInfoKey($id): string
    {
        return AppCRedisKeys::getWdynamicByDid($id);
    }

    /**
     * @param $did
     * @return int
     * 缓存清理
     */
    private function __delCache($did): int
    {
        $r_key1 = $this->__getOneInfoKey($did);
        return by::redis('core')->del($r_key1);
    }

    /**
     * @param $did
     * @param bool $check_status :是否校验上下架状态
     * @return array|false
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetInfoByDid($did, $check_status = false)
    {
        $did = CUtil::uint($did);
        if(empty($did)) {
            return [];
        }

        if ($check_status) {
            $mInfo  = by::WdynamicMain()->GetStatusRel($did);
            $status = $mInfo['status'] ?? 0;
            if ($status != 1) {
                return [];
            }
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getOneInfoKey($did);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName($did);

            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `did`=:did LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':did'=>$did])->queryOne();
            $aData   = $aData ?: [];
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        if ($check_status) {
            $aData['rtime'] = $mInfo['rtime'] ?? 0;
        }

        return $aData;
    }

    /**
     * @param $aData
     * @return array
     * 参数校验
     */
    public function Check($aData)
    {
        $type           = $aData['type']        ?? 0;
        $status         = $aData['status']      ?? 0;
        $title          = $aData['title']       ?? '';
        $cover_image    = $aData['cover_image'] ?? '';
        $video          = $aData['video']       ?? '';
        $author         = $aData['author']      ?? '';
        $avatar         = $aData['avatar']      ?? '';

        $mWdynameicMain = by::WdynamicMain();

        if ( !in_array($status, $mWdynameicMain::STATUS) ) {
            return [false, '状态有误'];
        }

        if (mb_strlen($title) > 50) {
            return [false, '标题最多50字符'];
        }

        if (mb_strlen($author) > 20) {
            return [false, '作者名称最多20字符'];
        }

        if (empty($title) || empty($cover_image) || empty($author) || empty($avatar)) {
            return [false, '请检查必填参数'];
        }

        return [true, 'ok'];
    }
    /**
     * @param $did
     * @param $ctime
     * @param array $aData
     * @param string $act
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog($did, $ctime, $aData = [], $act = 'add')
    {
        if (empty($did)) {
            return [false, '网络繁忙'];
        }

        $type           = $aData['type']        ?? 0;
        $title          = $aData['title']       ?? '';
        $cover_image    = $aData['cover_image'] ?? '';
        $video          = $aData['video']       ?? '';
        $text           = $aData['text']        ?? '';
        $author         = $aData['author']      ?? '';
        $avatar         = $aData['avatar']      ?? '';
        $role_id        = $aData['role_id']     ?? 0;
        $images         = $aData['images']      ?? '';

        $save   = [
            'role_id'       => $role_id,
            'type'          => $type,
            'title'         => $title,
            'cover_image'   => $cover_image,
            'video'         => $video,
            'images'         => $images,
            'text'          => $text,
            'author'        => $author,
            'avatar'        => $avatar,
            'ctime'         => $ctime,
            'utime'         => $ctime,
        ];

        $db = by::dbMaster();
        $tb = self::tbName($did);

        if ($act == 'add') {
            $save['did']    = $did;
            $save['ctime']  = $ctime;
            $db->createCommand()->insert($tb, $save)->execute();
        } else {
            $db->createCommand()->update($tb, $save, ['did' => $did])->execute();
        }

        $this->__delCache($did);

        return [true, 'ok'];
    }


}
