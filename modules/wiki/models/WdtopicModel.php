<?php
/**
 * Created by IntelliJ IDEA.
 * Time: 14:45
 * 内容话题表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\Exception;


class WdtopicModel extends CommModel {

    public $tb_fields = [
        'id','did','tid1','tid2'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_wiki`.`t_wdtopic`";
    }

    const LIMIT_NUM = 5;

    /**
     * @param $did
     * @return string
     * 唯一数据缓存KEY
     */
    private function __getListByDidKey($did): string
    {
        return AppCRedisKeys::getDtagListByDid($did);
    }

    /**
     * @param $did
     * @return int
     * 缓存清理
     */
    private function __delCache($did): int
    {
        $r_key = $this->__getListByDidKey($did);

        return  by::redis('core')->del($r_key);
    }

    private function __getDidList(): string
    {
        return AppCRedisKeys::getDidList();
    }

    public function __delDidListCache()
    {
        $r_key = $this->__getDidList();

        return  by::redis('core')->del($r_key);
    }
    /**
     * @param int $did
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * 数据增删
     * [{"tid1":1,"tid2":[11,12,13]},{"tid1":2,"tid2":[21,22,23]}]
     */
    public function SaveLog(int $did, array $aData)
    {
        $did  = CUtil::uint($did);

        if(empty($did)) {
            return [false,"参数错误(2)"];
        }

        $tModel     = by::Wtopic();
        //参数过滤
        $tcnf       = $aData['tcnf'] ?? "";

        $tData      = (array)json_decode($tcnf, true);

        if (empty($tData)) {
            return [false, '请选择话题'];
        }

        $tids       = [];
        foreach($tData as $val) {
            if (empty($val['tid2'])) {
                return [false, '请选择对应的二级话题'];
            }

            $l1 = $tModel->GetList(1, $val['tid1']);
            $ids= array_column($l1, 'id');

            if (array_diff($val['tid2'], $ids)) {
                return [false, '请选择正确的二级话题'];
            }

            $tids = array_merge($tids, $val['tid2']);
        }

        //查修原来的标签
        $o_tids  = $this->GetListByGid($did);
        $o_tids  = array_column($o_tids,'tid2');

        //新增的标签
        $a_tids  = array_diff($tids, $o_tids);
        //删除的标签
        $d_tids  = array_diff($o_tids, $tids);

        $tb                 = $this->tbName();
        $db                 = by::dbMaster();
        if ($a_tids) {
            $save   = [];
            foreach($tData as $val) {
                foreach($val['tid2'] as $tid2) {
                    if (in_array($tid2, $a_tids)) {
                        $save[] = [
                            'did'       => $did,
                            'tid1'      => $val['tid1'],
                            'tid2'      => $tid2,
                        ];
                    }
                }
            }

            $db->createCommand()->batchInsert($tb, ['did', 'tid1', 'tid2'], $save)->execute();
        }

        if ($d_tids) {
            $db->createCommand()->delete($tb, ['did' => $did, 'tid2' => $d_tids])->execute();
        }

        $this->__delCache($did); //todo 清空缓存

        return [true, 'ok'];
    }

    /**
     * @param int $did
     * @param bool $check_del
     * @return array
     * @throws \yii\db\Exception
     * 根据did获取标签列表
     */
    public function GetListByGid(int $did, bool $check_del = false)
    {
        $did = CUtil::uint($did);
        if($did <= 0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getListByDidKey($did);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `did`=:did";
            $aData   = by::dbMaster()->createCommand($sql, [':did' => $did])->queryAll();

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        if (empty($aData)) {
            return [];
        }

        //todo ---------过滤已删除标签-----------
        if ($check_del) {
            $t1         = array_column($aData, 'tid1');

            $t1_list    = by::Wtopic()->GetList(0, -1);
            $t1_ids     = array_column($t1_list, 'id');

            $d_t1       = array_diff($t1, $t1_ids);
            if (!empty($d_t1)) {
                foreach($aData as $key => $val) {
                    if (in_array($val['tid1'], $d_t1)) {
                        unset($aData[$key]);
                    }
                }
            }

            if (empty($aData)) {
                return [];
            }

            $t2         = array_column($aData, 'tid2');
            $t2_list    = by::Wtopic()->GetList(1, -1);
            $t2_ids     = array_column($t2_list, 'id');

            $d_t2       = array_diff($t2, $t2_ids);
            if (!empty($d_t2)) {
                foreach($aData as $key => $val) {
                    if (in_array($val['tid2'], $d_t2)) {
                        unset($aData[$key]);
                    }
                }
            }
        }

        return $aData;
    }

    /**
     * @param $tid2
     * @throws \yii\db\Exception
     * 话题删除后，关联内容话题绑定关系也删除
     */
    public function ShellDel($tid2)
    {
        $db     = by::dbMaster();
        $tb     = self::tbName();
        $limit  = 2;
        $sql    = "DELETE FROM {$tb} WHERE `tid2` = :tid2 LIMIT :limit";

        while (true) {
            $ret    = $db->createCommand($sql, [':tid2' => $tid2, ':limit' => $limit])->execute();

            if (empty($ret)) {
                break;
            }

            usleep(1000);
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 一级话题下随机五条数据
     */
    public function GetDidByTid($tid1): array
    {
        $redis = by::redis();
        $redis_key = $this->__getDidList();
        $aJson = $redis->get($redis_key);
        $aData = (array)json_decode($aJson,true);

        if (!$aData){
            $tb       = self::tbName();
            $mainTb   = by::WdynamicMain()::tbName();
            $status   = by::WdynamicMain()::STATUS['PUBLISHED'];
            $limitNum = self::LIMIT_NUM;
            $sql      = "SELECT `id`,`clicks` FROM {$mainTb} WHERE `status`=:status AND `id` IN (SELECT `did` FROM {$tb} WHERE `tid1`= :t1) ORDER BY `rtime` DESC LIMIT {$limitNum}";
            $aData = by::dbMaster()->createCommand($sql, [":t1" => $tid1, ":status" => $status])->queryAll();
            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        return $aData ?? [];
    }




}
