<?php
/**
 * Created by IntelliJ IDEA.
 * Time: 14:45
 * 内容话题表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;
use app\models\MyExceptionModel;

class DocModel extends CommModel {


    public static function tbName(): string
    {
        return  "`db_dreame_wiki`.`t_doc`";
    }

    //是否删除 0否1是
    const IS_DEL = [
        'all' => -1,
        'no'  => 0,
        'yes' => 1,
    ];

    /**
     * @param string doc_name
     * @param string doc_code
     * @param int is_delete
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($id ,$doc_name = '', $doc_code = '', $is_delete = self::IS_DEL['no'])
    {
        $params                 = [];
        $where                  = "`is_del` = :is_delete";
        $params[':is_delete']   = $is_delete;

        if ($id > 0) {
            $where             .= " AND (`id` = :id )";
            $params[':id']      = $id;
        }

        if ($doc_name) {
            $where             .= " AND (`doc_name` like :name )";
            $params[':name']    = "%{$doc_name}%";
        }

        if ($doc_code) {
            $where             .= " AND (`doc_code` like :code )";
            $params[':code']    = "%{$doc_code}%";
        }

        return [$where, $params];
    }

    /**
     * @param string $doc_name
     * @param string $doc_code
     * @return array
     * @throws \yii\db\Exception
     * 获取文档列表
     */
    public function GetListByBack(string $doc_name = '', string $doc_code = '')
    {
        $tb = $this->tbName();
        list($where,$params) = $this->__getCondition(0, $doc_name, $doc_code);

        $sql = "SELECT `id`,`doc_name`,`doc_code`,`update_time` FROM {$tb} WHERE {$where}";
        $aData = by::dbMaster()->createCommand($sql,$params)->queryAll();

        return $aData;
    }

    public function GetListByMain(string $doc_name = '', string $doc_code = '')
    {
        $tb = $this->tbName();
        list($where,$params) = $this->__getCondition(0, $doc_name, $doc_code);

        $sql = "SELECT `id`,`doc_name`,`doc_code`,`img_list` FROM {$tb} WHERE {$where}";
        $aData = by::dbMaster()->createCommand($sql,$params)->queryAll();
        $aData = $aData ?: [];

        $return = [];
        if(!empty($aData)){
            foreach($aData as $k => $val){
                $return[$k]['id']     = $val['id'];
                $return[$k]['title']  = $val['doc_name'];
                $return[$k]['code']   = $val['doc_code'];
                $return[$k]['contents']   = explode('|', $val['img_list']);
            }
        }

        return $return;
    }

    /**
     * @param int $id
     * @param string $doc_code
     * @return array
     * @throws \yii\db\Exception
     * 获取文档详情
     */
    public function GetInfo(int $id = 0, $doc_code= '')
    {
        $tb = $this->tbName();
        list($where,$params) = $this->__getCondition($id,'',$doc_code);

        $sql = "SELECT `id`,`doc_name`,`doc_code`,`img_list` FROM {$tb} WHERE {$where}";
        $aData = by::dbMaster()->createCommand($sql,$params)->queryOne();
        $aData = $aData ?: [];
        if(!empty($aData)){
            $aData['img_list'] = explode('|', $aData['img_list']);
        }
        return $aData;
    }

    /**
     * @param int $id
     * @param string $doc_name
     * @param string $doc_code
     * @param string $img_list
     * @return array
     * @throws \yii\db\Exception
     * 获取文档列表
     */
    public function save_log($id, string $doc_name, string $doc_code, string $img_list)
    {
        $id     = CUtil::uint($id);

        $db     = by::dbMaster();
        $tb     = self::tbName();
        try {
            //如果id为空做新增，id存在做更新
            $params                 = [];
            if($id){
                //: 先做查询，看看doc_code是否被其他id已经占用
                $where                  = "`is_del` = :is_delete";
                $params[':is_delete']   = self::IS_DEL['no'];
                $where                  .= " AND `id` <> :id ";
                $params[':id']          = $id;
                $where                  .= " AND `doc_code` = :code ";
                $params[':code']        = $doc_code;
                $where                  .= " AND `is_del` = :del ";
                $params[':del']         = 0;

                $sql = "SELECT `id` FROM {$tb} WHERE {$where}";
                $aData = by::dbMaster()->createCommand($sql,$params)->queryOne();
                if(!empty($aData)){
                    throw new MyExceptionModel('该标识的文档已经存在');
                }

                //: 更新
                $updateData = [];
                $updateData['doc_name'] = $doc_name;
                $updateData['doc_code'] = $doc_code;
                $updateData['img_list'] = $img_list;
                $updateData['update_time'] = time();
                $status = $db->createCommand()->update($tb, $updateData, ['id' => $id])->execute();
                if($status){
                    return [true, 'ok'];
                }
            }

            $where                  = "`doc_code` = :code AND `is_del` = 0";
            $params[':code']        = $doc_code;
            $sql = "SELECT `id` FROM {$tb} WHERE {$where}";
            $aData = by::dbMaster()->createCommand($sql,$params)->queryOne();
            if(!empty($aData)){
                throw new MyExceptionModel('该标识的文档已经存在');
            }

            //做新增
            $insertData = [];
            $insertData['doc_name'] = $doc_name;
            $insertData['doc_code'] = $doc_code;
            $insertData['img_list'] = $img_list;
            $insertData['update_time'] = time();
            $db->createCommand()->insert($tb, $insertData)->execute();
        } catch (MyExceptionModel $e) {
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            CUtil::debug($e->getMessage(), 'err.doc');
            return [false, '操作失败'];
        }

        return [true, 'ok'];
    }

    /**
     * @param int $id
     * @return array
     * @throws \yii\db\Exception
     * 删除 假删
     */
    public function del(int $id = 0)
    {
        $id     = CUtil::uint($id);
        $db     = by::dbMaster();
        $tb     = self::tbName();
        try {
            $info = $this->GetInfo($id);
            if(empty($info)){
                throw new MyExceptionModel('没找到该数据');
            }
            //: 更新
            $updateData = [];
            $updateData['is_del'] = 1;
            $updateData['update_time'] = time();
            $status = $db->createCommand()->update($tb, $updateData, ['id' => $id])->execute();
            if($status){
                return [true, 'ok'];
            }
        } catch (MyExceptionModel $e) {
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            CUtil::debug($e->getMessage(), 'err.topic');
            return [false, '操作失败'];
        }

        return [true, 'ok'];
    }


}