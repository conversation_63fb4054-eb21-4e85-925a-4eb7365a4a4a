<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 产品内容表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class WdynamicMainModel extends CommModel {

    CONST ListExpire  = 600;//列表缓存

    //动态排序方式
    CONST DYNAMIC_SORT = [
        'TIME'          => 0, //按时间降序
        'ACTIVE'        => 1  //按热度
    ];

    CONST ACT_TYPE  = [
        'CLICK'         => 10001,
        'PRAISE'        => 10002,
        'SHARE'         => 10003,
    ];

    CONST TYPE      = [
        'ARTICLE'       => 0,
        'VIDEO'         => 1,
    ];

    CONST STATUS    = [
        'NOT_PUBLISH'   => 0, //未发布
        'PUBLISHED'     => 1, //已发布
        'DEL'           => 2, //已删除
    ];

    CONST STATUS_NAME = [
        0 => '未发布',
        1 => '已发布',
        2 => '已删除',
    ];

    CONST MAX_LIMIT = 1000;//小程序最多加载数

    public $tb_fields = [
        'id','type','clicks','praise','share','status','ctime'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_wiki`.`t_wdynamic_main`";
    }

    /**
     * @param $is_temp
     * @param int $t2
     * @return mixed
     * 内容列表
     */
    private function __getListKey($is_temp,$type=0,$t2=0) {
        return  AppCRedisKeys::getWdynamicsList($is_temp,$type,$t2);
    }

    /**
     * @param $id
     * @return string
     */
    private function __getStatusKey($id): string
    {
        return AppCRedisKeys::getWdynamicStatus($id);
    }

    /**
     * @param $id
     * @return int
     * 删除status缓存
     */
    private function __delStatusCache($id)
    {
        $r_key  = $this->__getStatusKey($id);
        return by::redis()->del($r_key);
    }

    /**
     * @param $id
     * @return array|false
     * @throws Exception
     * 获取指定详情信息
     */
    public function GetInfoByDid($id)
    {
        $id = CUtil::uint($id);
        if(empty($id)) {
            return [];
        }

        $tb      = $this->tbName();

        $fields  = implode("`,`",$this->tb_fields);
        $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `id`=:id LIMIT 1";
        $aData   = by::dbMaster()->createCommand($sql,[':id'=>$id])->queryOne();
        $aData   = $aData ?: [];

        return $aData;
    }

    /**
     * @param int $status
     * @param int $type
     * @param int $t1
     * @param int $t2
     * @param string $title
     * @return int
     * @throws Exception
     * 获取总数
     */
    public function GetListCount($status=-1, $type=-1, $t1=-1, $t2=-1, $title='') {

        $tb                  = $this->tbName();
        list($where,$params) = $this->__getCondition($status,$type,$t1,$t2,$title);
        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();
        $count               = empty($count) ? 0 : $count;

        return intval($count);
    }

    /**
     * @param int $status
     * @param int $type
     * @param int $t1
     * @param int $t2
     * @param string $title
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 获取列表
     */
    public function GetList($status=-1, $type=-1, $t1=-1, $t2=-1, $title='', $page=1, $page_size=20)
    {
        $page        = CUtil::uint($page,1);

        $tb                  = $this->tbName();
        list($offset)        = CUtil::pagination($page,$page_size);
        list($where,$params) = $this->__getCondition($status, $type, $t1, $t2, $title);

        $sql                 = "SELECT `id`,`rtime` FROM {$tb} WHERE {$where} ORDER BY `id` DESC 
                                    LIMIT {$offset},{$page_size}";

        $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

        return $aData;
    }

    /**
     * @param $status
     * @param $type
     * @param $t1
     * @param $t2
     * @param $title
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($status,$type,$t1,$t2,$title): array
    {
        //SQL初始化条件
        $where      = "1=1";
        $params     = [];

        if($status >= 0) {
            $where                .= " AND `status`=:status";
            $params[":status"]     = intval(!!$status);
        } else {
            $where                .= " AND `status` IN (0,1)";
        }

        if ($type > -1) {
            $where                .= " AND `type`=:type";
            $params[":type"]       = $type;
        }

        if ($t1 > 0) {
            $tb                    = by::Wdtopic()->tbName();
            $where                .= " AND `id` IN (SELECT `did` FROM {$tb} WHERE `tid1`={$t1})";
        }

        if ($t2 > 0) {
            $tb                    = by::Wdtopic()->tbName();
            $where                .= " AND `id` IN (SELECT `did` FROM {$tb} WHERE `tid2`={$t2})";
        }

        if (!empty($title)) {
            $where                .= " AND `title` LIKE :title";
            $params[":title"]      = "{$title}%";
        }

        return [$where,$params];
    }



    /**
     * @param array $aData
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog($aData = [])
    {
        $did            = $aData['did']         ?? 0;
        $type           = $aData['type']        ?? 0;
        $status         = $aData['status']      ?? 0;
        $title          = $aData['title']       ?? '';

        $did            = CUtil::uint($did);
        $type           = CUtil::uint($type);
        $status         = CUtil::uint($status);

        //其它参数校验
        list($s, $m) = by::Wdynamic()->Check($aData);
        if (!$s) {
            return [false, $m];
        }

        if ($did) {
            $aInfo  = $this->GetInfoByDid($did);
            if (empty($aInfo)) {
                return [false, '请选择正确的数据'];
            }
        }
        $ostatus    = $aInfo['status'] ?? 0;

        $ctime  = intval(START_TIME);

        $db = by::dbMaster();
        $tb = self::tbName();

        if ($status == self::STATUS['PUBLISHED'] && $status != $ostatus) {
            $rtime  = $ctime;
        }

        $trans  = $db->beginTransaction();

        try {

            if ($did) {
                $act    = 'edit';
                $save = [
                    'status'    => $status,
                    'title'     => $title,
                ];
                isset($rtime) && $save['rtime'] = $rtime;
                $db->createCommand()->update($tb, $save, ['id' => $did])->execute();

            } else {
                $act    = 'add';

                $save   = [
                    'title'     => $title,
                    'type'      => $type,
                    'status'    => $status,
                    'ctime'     => $ctime,
                ];

                isset($rtime) && $save['rtime'] = $rtime;
                $db->createCommand()->insert($tb, $save)->execute();

                $did = $db->getLastInsertID();
            }

            list($s, $m) = by::Wdynamic()->SaveLog($did, $ctime, $aData, $act);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 内容标签
            list($s, $m) = by::model('WtagModel', 'wiki')->SaveLog($did, $aData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }

            //todo 内容话题
            list($s, $m) = by::Wdtopic()->SaveLog($did, $aData);
            if (!$s) {
                throw new MyExceptionModel($m);
            }
            by::Wdtopic()->__delDidListCache();
            $trans->commit();

            return [true, $did];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();

            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.wiki');

            return [false, '操作失败'];
        }
    }

    /**
     * @param $did
     * @return array
     * @throws Exception
     * 删除
     */
    public function Del($did)
    {
        $aInfo = $this->GetInfoByDid($did);

        if (empty($aInfo)) {
            return [false, '参数有误'];
        }

        if ($aInfo['status'] == self::STATUS['DEL']) {
            return [false, '该数据已删除'];
        }

        $db = by::dbMaster();
        $tb = self::tbName();

        $trans  = $db->beginTransaction();

        try {

            $db->createCommand()->update($tb, ['status' => self::STATUS['DEL']], ['id' => $did])->execute();

            $trans->commit();

            //hack 缓存信息

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.wdynamicmain');
            return [false, '操作失败'];
        }

    }

    /**
     * @param $did
     * @return array
     * @throws Exception
     * 统一打包数据(注意：仅后台用)
     */
    public function CommPackageInfo($did)
    {
        $mWdym      = by::WdynamicMain();
        $mWdy       = by::Wdynamic();
        $mGtag      = by::model('GtagModel', 'goods');
        $mWtopic    = by::Wtopic();

        //主表数据
        $aInfo      = $mWdym->GetInfoByDid($did);

        //子表详细数据
        $info       = $mWdy->GetInfoByDid($did);
        //话题详细数据
        $topic      = by::Wdtopic()->GetListByGid($did, true);

        $info['t1'] =  array_values(array_unique(array_column($topic, 'tid1')));
        $info['t2'] =  array_values(array_unique(array_column($topic, 'tid2')));

        $info['t1_name'] = $mWtopic->GetTname($info['t1']);
        $info['t2_name'] = $mWtopic->GetTname($info['t2']);

        //标签详细数据
        $tids       = by::model('WtagModel', 'wiki')->GetListByGid($did);
        $info['tid']= array_column($tids, 'tid');
        $info['tid_name'] = $mGtag->GetTagName($info['tid']);

        return array_merge($aInfo, $info);
    }

    /**
     * @param int $did
     * @param int $status
     * @return array
     * @throws Exception
     * 上下架
     */
    public function UpdateStatus(int $did, int $status)
    {
        if (!in_array($status, [0, 1])) {
            return [false, '状态错误'];
        }

        $info   = $this->GetInfoByDid($did);
        if (empty($info)) {
            return [false, '参数错误'];
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($anti) = self::ReqAntiConcurrency($did, $unique_key, 2);
        if (!$anti) {
            return [false, '请勿频繁操作'];
        }

        if ($info['status'] == $status) {
            return [true, 'ok'];
        }

        $tb = self::tbName();
        $save   = [
            'status' => $status
        ];
        if ($status == self::STATUS['PUBLISHED']) {
            $save['rtime'] = time();
        }
        by::dbMaster()->createCommand()->update($tb, $save, ['id' => $did])->execute();
        by::Wdtopic()->__delDidListCache();
        $this->__delStatusCache($did);

        return [true, 'ok'];
    }

    /**
     * @param int $did
     * @return array|false|int
     * @throws Exception
     * 获取上下架状态相关
     */
    public function GetStatusRel(int $did)
    {
        $did = CUtil::uint($did);
        if(empty($did)) {
            return 0;
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getStatusKey($did);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $tb      = $this->tbName();

            $sql     = "SELECT `status`,`rtime` FROM {$tb} WHERE `id`=:did LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql,[':did'=>$did])->queryOne();

            $aData   = $aData ?: [];
            $redis->set($redis_key, json_encode($aData), ['EX'=> empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }

    /**
     * @param int $status
     * @param int $type
     * @param int $t1
     * @param int $t2
     * @param string $title
     * 导出
     */
    public function Export($status=-1, $type=-1, $t1=-1, $t2=-1, $title='')
    {
        $head   = [
            '内容标题','作者','一级话题','二级话题','产品类型','发布时间','状态','阅读数','转发数','点赞数'
        ];

        $f_name = '内容列表' . date('Ymd') . mt_rand(1000, 9999);

        list($where,$params) = $this->__getCondition($status, $type, $t1, $t2, $title);

        //导出
        CUtil::export_csv_new($head, function () use($where, $params) {

            $db         = by::dbMaster();
            $tb         = self::tbName();

            $s_name     = self::STATUS_NAME;

            $id         = 0;
            $sql        = "SELECT `id` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

            while (true) {
                $params['id']   = $id;
                $list = $db->createCommand($sql, $params)->queryAll();

                if (empty($list)) {
                    break;
                }

                $end        = end($list);
                $id         = $end['id'];

                $data       = [];
                foreach ($list as $val) {

                    $info       = $this->CommPackageInfo($val['id']);

                    //'内容标题','一级话题','二级话题','产品类型','发布时间','状态','阅读数','转发数','点赞数'
                    $data[] = [
                        'title'         => $info['title'],
                        'author'        => $info['author'],
                        't1_name'       => $info['t1_name'],
                        't2_name'       => $info['t2_name'],
                        'tid_name'      => $info['tid_name'],
                        'ctime'         => date('Y-m-d H:i:s', $info['ctime']),
                        'status'        => $s_name[$info['status']] ?? '未知',
                        'clicks'        => $info['clicks'],
                        'share'         => $info['share'],
                        'praise'        => $info['praise'],
                    ];
                }

                yield $data;
            }

        }, $f_name);
    }

    public function ExportData($status=-1, $type=-1, $t1=-1, $t2=-1, $title='')
    {
        $head   = [
            '内容标题','作者','一级话题','二级话题','产品类型','发布时间','状态','阅读数','转发数','点赞数'
        ];

        list($where,$params) = $this->__getCondition($status, $type, $t1, $t2, $title);

        //导出

        $db         = by::dbMaster();
        $tb         = self::tbName();

        $s_name     = self::STATUS_NAME;

        $id         = 0;
        $sql        = "SELECT `id` FROM {$tb} WHERE `id` > :id AND {$where} ORDER BY `id` LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id']   = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end        = end($list);
            $id         = $end['id'];

            foreach ($list as $val) {

                $info       = $this->CommPackageInfo($val['id']);

                //'内容标题','一级话题','二级话题','产品类型','发布时间','状态','阅读数','转发数','点赞数'
                $data[] = [
                    'title'         => $info['title'],
                    'author'        => '\''.$info['author'],
                    't1_name'       => $info['t1_name'],
                    't2_name'       => $info['t2_name'],
                    'tid_name'      => $info['tid_name'],
                    'ctime'         => date('Y-m-d H:i:s', $info['ctime']),
                    'status'        => $s_name[$info['status']] ?? '未知',
                    'clicks'        => $info['clicks'],
                    'share'         => $info['share'],
                    'praise'        => $info['praise'],
                ];
            }

        }

        return $data;
    }

    /**
     * @param int $page
     * @param int $t2
     * @param int $sort
     * @return array
     * 获取小程序数据
     */
    public function GetDynamicsList(int $page=1, int $t2=0, int $sort=0)
    {
        if ($t2 <= 0) {
            return [false, '网络繁忙', 0];
        }

        $redis  = by::redis();

        $r_key  = $this->__getListKey(0,$sort,$t2); //非临时缓存key


        $page   = CUtil::uint($page,1);
        $aJson  = $redis->hGet($r_key,$page);
        $aData  = (array)json_decode($aJson,true);

        if(empty($aData)) {
            return [false,"已加载完所有数据",0];
        }

        //总页数
        $pages = $redis->hLen($r_key);

        return [true,$aData,$pages];
    }

    /**
     * @param int $sort
     * @throws Exception
     * 小程序列表数据（备注：仅脚本刷新用）
     */
    public function SyncDynamicsList($sort=0)
    {
        //查出有效二级话题
        $tlist  = by::Wtopic()->GetList(1, -1, 1, 50);

        foreach($tlist as $val) {
            $this->__syncDynamicsList($val['id'], $sort);
        }

    }

    /**
     * @param int $t2
     * @param int $sort
     * @throws Exception
     * 刷入缓存
     */
    private function __syncDynamicsList($t2=0,$sort=0)
    {
        $redis          = by::redis();

        $r_key          = $this->__getListKey(0,$sort,$t2); //非临时缓存key

        $tb             = $this->tbName();

        list($where,$params) = $this->__getMCondition($t2);

        $sql_temp       = "SELECT `id`,`clicks` FROM {$tb} WHERE {$where} ";

        $page_size      = 25;

        $pages          = CUtil::getPaginationPages(self::MAX_LIMIT, $page_size);//总页数

        $hMSet          = [];

        for($page=1; $page<=$pages; $page++) {

            list($offset) = CUtil::pagination($page,$page_size);

            if ($sort == self::DYNAMIC_SORT['TIME']) {
                $sql    = $sql_temp. " ORDER BY `rtime` DESC LIMIT {$offset},{$page_size} ";
            } else {
                $sql    = $sql_temp. " ORDER BY `clicks` DESC LIMIT {$offset},{$page_size} ";
            }

            $aList = by::dbMaster()->createCommand($sql, $params)->queryAll();
            if(empty($aList)) {
                break;
            }

            $hMSet[$page] = json_encode($aList);
        }
        by::Wdtopic()->__delDidListCache();
        //换入临时缓存
        if(empty($hMSet)) {
            $redis->del($r_key);
        } else {
            $redis->hMSet($r_key,$hMSet);
            $redis->expire($r_key,86400);
        }

    }

    /**
     * @param $t2
     * @return array
     * 小程序条件
     */
    private function __getMCondition($t2): array
    {
        //SQL初始化条件

        $where                 = " `status`=:status";
        $params[":status"]     = self::STATUS['PUBLISHED'];

        if ($t2 > 0) {
            $tb                    = by::Wdtopic()->tbName();
            $where                .= " AND `id` IN (SELECT `did` FROM {$tb} WHERE `tid2`= :t2)";
            $params[':t2']         = $t2;
        }

        return [$where,$params];
    }

    /**
     * @param $type
     * @param array $data
     * @return array
     * @throws Exception
     * 更新热度相关字段
     */
    public function UpdateHeat($type, $data=[])
    {
        $did            = $data['id'] ?? 0;
        $did            = CUtil::uint($did);
        if (empty($did)) {
            return [false, '参数错误'];
        }

        $num    = $data['num'] ?? 1;

        switch ($type) {
            case self::ACT_TYPE['CLICK'] : $field = 'clicks';break;
            case self::ACT_TYPE['PRAISE']: $field = 'praise';break;
            case self::ACT_TYPE['SHARE'] : $field = 'share';break;
            default :
                return [false, 'type错误'];

        }

        $tb     = self::tbName();
        $sql    = "UPDATE {$tb} SET `{$field}` = `{$field}`+ :num WHERE `id` = :id LIMIT 1";

        try {
            by::dbMaster()->createCommand($sql, [':num' => $num, ':id'=>$did])->execute();
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage(), 'wiki.heat');
        }

        return [true, 'ok'];
    }


}
