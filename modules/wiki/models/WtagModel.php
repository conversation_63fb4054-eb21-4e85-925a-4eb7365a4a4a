<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/4
 * Time: 14:45
 * 内容标签表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;


class WtagModel extends CommModel {

    public $tb_fields = [
        'id','did','tid'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_wiki`.`t_wtag`";
    }

    /**
     * @param $did
     * @return string
     * 唯一数据缓存KEY
     */
    private function __getListByDidKey($did): string
    {
        return AppCRedisKeys::getWtagListByDid($did);
    }

    /**
     * @param $did
     * @return int
     * 缓存清理
     */
    private function __delCache($did): int
    {
        $r_key = $this->__getListByDidKey($did);

        return  by::redis('core')->del($r_key);
    }

    /**
     * @return array
     * 获取所有标签
     */
    public function GetTagCnfList()
    {
        $tModel = by::model('GtagModel', 'goods');
        $list   = $tModel::TAG_NAME;
        $return = [];
        foreach ($list as $tid=>$name) {
            if ($tid < 10) {
                continue;
            }

            $return[] = [
                'tid'  => (string)$tid,
                'name' => $name,
            ];
        }

        return $return;
    }

    /**
     * @param int $did
     * @param array $aData
     * @return array
     * @throws \yii\db\Exception
     * tag增删
     */
    public function SaveLog(int $did, array $aData)
    {
        $did  = CUtil::uint($did);

        if(empty($did)) {
            return [false,"参数错误(2)"];
        }

        // $tModel           = by::model('GtagModel', 'goods');
        $tagCode = by::Gtag()->GetTagCodeMap();
        //参数过滤
        $tid              = $aData['tid']           ?? "";

        $tids             = [];
        if (!empty($tid)) {
            $tids             = explode(',', $tid);
        }

        if (array_diff($tids, $tagCode)) {
            return [false, '请选择正确的标签'];
        }

        //查修原来的标签
        $o_tids  = $this->GetListByGid($did);
        $o_tids  = array_column($o_tids,'tid');

        //新增的标签
        $a_tids  = array_diff($tids, $o_tids);
        //删除的标签
        $d_tids  = array_diff($o_tids, $tids);

        $tb                 = $this->tbName();
        $db                 = by::dbMaster();
        if ($a_tids) {
            $sql = "INSERT IGNORE {$tb} (`did`,`tid`) VALUES ";
            foreach($a_tids as $tid) {
                $sql .= "({$did},{$tid}),";
            }
            $sql = rtrim($sql, ',');
            $db->createCommand($sql)->execute();
        }

        if ($d_tids) {
            $db->createCommand()->delete($tb, ['did' => $did, 'tid' => $d_tids])->execute();
        }

        $this->__delCache($did); //todo 清空缓存

        return [true, 'ok'];
    }

    /**
     * @param int $did
     * @return array
     * @throws \yii\db\Exception
     * 根据did获取标签列表
     */
    public function GetListByGid(int $did)
    {
        $did = CUtil::uint($did);
        if($did <= 0) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getListByDidKey($did);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if ($aJson === false) {
            $tb      = $this->tbName();
            $fields  = implode("`,`",$this->tb_fields);
            $sql     = "SELECT `{$fields}` FROM {$tb} WHERE `did`=:did ORDER BY `tid`";
            $aData   = by::dbMaster()->createCommand($sql, [':did' => $did])->queryAll();

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }



}
