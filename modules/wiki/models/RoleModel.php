<?php
/**
 * Created by IntelliJ IDEA.
 * Time: 14:45
 * 内容话题表
 */

namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use RedisException;
use yii\db\DataReader;
use yii\db\Exception;
use app\models\MyExceptionModel;

class RoleModel extends CommModel
{


    public static function tbName(): string
    {
        return "`db_dreame_wiki`.`t_role`";
    }

    public $tb_fields = [
        'id', 'name', 'icon', 'is_del', 'ctime', 'utime'
    ];

    //是否删除 0否1是
    const IS_DEL = [
        'no'  => 0,
        'yes' => 1,
    ];

    private function __getOneInfoById($id): string
    {
        return AppCRedisKeys::getWikiRoleById($id);
    }

    public function __delOneInfoById($id)
    {
        $redis    = by::redis();
        $redisKey = $this->__getOneInfoById($id);
        $redis->del($redisKey);
    }

    private function __getRoleListKey(): string
    {
        return AppCRedisKeys::getRoleList();
    }

    public function __delRoleList()
    {
        $redis    = by::redis();
        $redisKey = $this->__getRoleListKey();
        $redis->del($redisKey);
    }


    /**
     * @param $post
     * @return array
     */
    public function saveLog($post): array
    {
        $name = $post['name'] ?? '';
        $icon = $post['icon'] ?? '';
        if (empty($name)) {
            return [false, '角色名称为空！'];
        }

        if (mb_strlen($name) > 20) {
            return [false, '角色名称过长！'];
        }

        if (empty($icon)) {
            return [false, '角色图标为空！'];
        }

        $db = by::dbMaster();
        try {
            //新增
            $tb   = self::tbName();
            $data = [
                'name'   => $name,
                'icon'   => $icon,
                'is_del' => self::IS_DEL['no'],
                'ctime'  => intval(START_TIME),
                'utime'  => intval(START_TIME),
            ];
            $row  = $db->createCommand()->insert($tb, $data)->execute();
            $id   = $db->getLastInsertID();
            if (!$row) {
                throw new MyExceptionModel('添加内容管理角色失败');
            }
            $this->__delRoleList();
            return [true, $id];
        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("添加内容管理角色失败|{$error}", 'err.addRole.wiki');
            return [false, '操作失败'];
        }

    }

    /**
     * @param $id
     * @return array
     * 删除角色
     */
    public function deleteRole($id): array
    {
        $id = CUtil::uint($id);
        if (empty($id)) {
            return [false, '请选择删除数据！'];
        }
        $db = by::dbMaster();
        $tb = self::tbName();
        try {
            $info = $this->GetOneInfoById($id);
            if (empty($info)) {
                throw new MyExceptionModel('没找到该数据');
            }
            //: 更新
            $updateData           = [];
            $updateData['is_del'] = self::IS_DEL['yes'];
            $updateData['utime']  = intval(START_TIME);
            $status               = $db->createCommand()->update($tb, $updateData, ['id' => $id])->execute();
            $this->__delOneInfoById($id);
            $this->__delRoleList();
            if ($status) {
                return [true, 'ok'];
            }
        } catch (MyExceptionModel $e) {
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            CUtil::debug($e->getMessage(), 'err.deleteRole.wiki');
            return [false, '操作失败'];
        }

        return [true, 'ok'];
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * @throws RedisException
     * 根据id获取角色信息
     */
    public function GetOneInfoById($id): array
    {
        if (empty($id)) {
            return [];
        }
        $redis    = by::redis();
        $redisKey = $this->__getOneInfoById($id);
        $aJson    = $redis->get($redisKey);
        $aData    = (array)json_decode($aJson, true);

        if (!$aJson) {
            $db     = by::dbMaster();
            $tb     = self::tbName();
            $fields = implode("`,`", $this->tb_fields);
            $sql    = "SELECT `{$fields}` FROM {$tb} WHERE `id` =:id LIMIT 1";
            $aData  = $db->createCommand($sql, [':id' => $id])->queryOne();
            $aData  = empty($aData) ? [] : $aData;
            $redis->set($redisKey, json_encode($aData), ['EX' => empty($aData) ? 10 : 3600]);
        }

        return $aData;
    }


    /**
     * @return array
     * @throws Exception
     * @throws RedisException 获取所有id
     */
    public function getList(): array
    {
        $redis = by::redis();

        $redis_key = $this->__getRoleListKey();
        $sub_key   = CUtil::getAllParams(__FUNCTION__);
        $aJson     = by::redis('core')->hGet($redis_key, $sub_key);
        $aData     = (array)json_decode($aJson, true);
        if (!$aJson) {
            $tb = self::tbName();
            //list($offset)        = CUtil::pagination($page,$page_size);
            $sql   = "SELECT `id` FROM {$tb} WHERE `is_del`=:is_del ORDER BY `id` DESC";
            $aData = by::dbMaster()->createCommand($sql, [':is_del' => self::IS_DEL['no']])->queryAll();
            $redis->hSet($redis_key, $sub_key, json_encode($aData));
            CUtil::ResetExpire($redis_key, 600);
        }

        return empty($aData) ? [] : array_column($aData, "id");
    }

    /**
     * @return int
     * @throws Exception
     * @throws RedisException 获取总条数
     */
    public function getCount(): int
    {
        $redis     = by::redis();
        $redis_key = $this->__getRoleListKey();
        $sub_key   = CUtil::getAllParams(__FUNCTION__);
        $count     = $redis->hGet($redis_key, $sub_key);

        if (!$count) {
            $tb    = self::tbName();
            $sql   = "SELECT COUNT(*) FROM {$tb} WHERE `is_del`=:is_del";
            $count = by::dbMaster()->createCommand($sql, [':is_del' => self::IS_DEL['no']])->queryScalar();
            $count = empty($count) ? 0 : $count;

            $redis->hSet($redis_key, $sub_key, $count);
            CUtil::ResetExpire($redis_key, 600);
        }
        return intval($count);
    }


}