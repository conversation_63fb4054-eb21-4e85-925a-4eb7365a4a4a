<?php


namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;


class SconfigModel extends CommModel
{
    public static function tableName()
    {
        return "`db_dreame`.`t_s_config`";
    }

    /**
     * 获取配置缓存
     */
    private function __getContentConfig(){
        return AppCRedisKeys::getContentConfig();
    }

    //产品百科
    CONST WIKI =[
        [
            'image' => 'https://wpm-cdn.dreame.tech/images/202209/901478-1663916556202.jpg',
            'name'  => '扫拖机器人',
//            'url'   => 'https://mp.weixin.qq.com/s?__biz=MzkxNDE3ODA3Mg==&mid=2247499179&idx=1&sn=c94991889b6974e95e996ee27c8890be&chksm=c170d4dcf6075dcab214b9b0b25777e33b2c8f38f5714b78dedf6bbc5c7fdd56c1e4790caf34#rd',
            'url'   => 'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=2247498977&idx=1&sn=4eb67da13d85545c0ae464eaf3fc3bdf&chksm=ce51b0ccf92639da5f3f4a1db55c8fb942aa3793ebdafa8635f43c16ea848ee0fae31b6f3db0#rd'
        ],
        [
            'image' => 'https://wpm-cdn.dreame.tech/images/202209/862048-1663916597644.jpg',
            'name'  => '智能洗地机',
//            'url'   => 'https://mp.weixin.qq.com/s/cDtGsCPfO5NsrU0gU3Nmww',
            'url'   => 'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=2247498977&idx=1&sn=4eb67da13d85545c0ae464eaf3fc3bdf&chksm=ce51b0ccf92639da5f3f4a1db55c8fb942aa3793ebdafa8635f43c16ea848ee0fae31b6f3db0#rd'
        ],
        [
            'image' => 'https://wpm-cdn.dreame.tech/images/202209/424323-1663916577905.jpg',
            'name'  => '无线吸尘器',
//            'url'   => 'https://mp.weixin.qq.com/s?__biz=MzkxNDE3ODA3Mg==&mid=2247499175&idx=1&sn=62035083ee2911688f786820bf55e9e3&chksm=c170d4d0f6075dc6c84903a0d6098d669fe61caf7c83767776869ddbb821eef5f79cdde37b9c#rd',
            'url'   => 'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=2247498977&idx=1&sn=4eb67da13d85545c0ae464eaf3fc3bdf&chksm=ce51b0ccf92639da5f3f4a1db55c8fb942aa3793ebdafa8635f43c16ea848ee0fae31b6f3db0#rd'
        ],
        [
            'image' => 'https://wpm-cdn.dreame.tech/images/202209/137925-1663916409829.jpg',
            'name'  => '高速吹风机',
//            'url'   => 'https://mp.weixin.qq.com/s?__biz=MzkxNDE3ODA3Mg==&mid=2247499183&idx=1&sn=2bd74274c4d1c1a6596fb99e62aae999&chksm=c170d4d8f6075dce3f1476e61cc541d7749f6270253ae707e8b46788dc4c9865e3e5f8c9d32d#rd',
            'url'   => 'https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=2247498977&idx=1&sn=4eb67da13d85545c0ae464eaf3fc3bdf&chksm=ce51b0ccf92639da5f3f4a1db55c8fb942aa3793ebdafa8635f43c16ea848ee0fae31b6f3db0#rd'
        ],
    ];


    /**
     * 缓存清理
     */
    private function __delCache()
    {
        $r_key = $this->__getContentConfig();
        by::redis('core')->del($r_key);
    }

    /**
     * @param $data
     * @return array
     * @throws \yii\db\Exception
     * 保存
     */
    public function saveConfig($data){
        $tb = $this->tableName();
        $up = [
            's_url' => $data['s_url']
        ];

        by::dbMaster()->createCommand()->update($tb,
            $up,
            ['id'=>1]
        )->execute();

        $this->__delCache();
        return [true,'修改成功'];

    }

    /**
     * @return array|false|\yii\db\DataReader
     * @throws \yii\db\Exception
     * 查询
     */
    public function getConfig(){
        $redis        = by::redis('core');
        $redis_key    = $this->__getContentConfig();
        $aJson        = $redis->get($redis_key);
        $aData        = (array)json_decode($aJson, true);

        if($aJson === false) {
            $tb         = $this->tableName();
            $sql        = "SELECT `s_url` FROM {$tb} WHERE `id`= 1 LIMIT 1";

            $aData      = by::dbMaster()->createCommand($sql)->queryOne();
            $aData      = $aData ?: [];

            $redis->set($redis_key,json_encode($aData),['EX'=> empty($aData) ? 10 : 1800]);
        }

        return $aData;
    }

    /**
     * 用户百科 暂时写死
     */
    public function getWiki(): array
    {
        return self::WIKI;
    }

}
