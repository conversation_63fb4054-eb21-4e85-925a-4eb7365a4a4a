<?php
/**
 * Created by IntelliJ IDEA.
 * Time: 15:21
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

class WpraiseModel extends CommModel {

    /**
     * @return string
     * 基本表
     */
    private static function __baseTb(): string
    {
        return  "db_dreame_wiki.t_wpraise";
    }

    /**
     * @param null $time
     * @return string
     * 按月分表
     */
    public static function tbName($time=null): string
    {
        $time    = is_null($time) ? time() : CUtil::uint($time);
        $month   = date("Ym",intval($time));
        $tb      = self::__baseTb();
        $tableName = "{$tb}_{$month}";
        $sql = "CREATE TABLE IF NOT EXISTS {$tableName} LIKE `db_dreame_wiki`.`t_wpraise`;";
        by::dbMaster()->createCommand($sql)->execute();
        return $tableName;
    }

    /**
     * @param $user_id
     * @param $did
     * @return mixed
     * 是否点赞
     */
    private function __hasPraised($user_id,$did)
    {
        return AppCRedisKeys::hasPraised($user_id,$did);
    }

    /**
     * @param $did
     * @return mixed
     * 点赞数
     */
    private function __getPraise($did)
    {
        return AppCRedisKeys::getPraise($did);
    }

    /**
     * @param $user_id
     * @param $did
     * @return int
     * 删除缓存
     */
    private function __delCache($user_id,$did)
    {
        $r_key1 = $this->__hasPraised($user_id,$did);
        $r_key2 = $this->__getPraise($did);

        return by::redis()->del($r_key1, $r_key2);
    }

    /**
     * @param null $time
     * @throws \yii\db\Exception
     * 建表sql 提前创建上月，本月和下月的表
     */
    public function CreateDbTb($time=null) {
        $time    = is_null($time) ? time() : CUtil::uint($time);
        $base_tb = self::__baseTb();

        //提前创建上月，本月和下月的表
        for($i=-1; $i<=1; $i++) {
            $now     = strtotime("+{$i} month",intval($time));
            $month   = date("Ym",intval($now));
            $tb      = "{$base_tb}_{$month}";
            $tb_sql  = "CREATE TABLE IF NOT EXISTS {$tb} LIKE {$base_tb};";

            by::dbMaster()->createCommand($tb_sql)->execute();
        }
    }

    /**
     * @param int $did
     * @return array
     * @throws \yii\db\Exception
     * 根据内容发布时间 按月生成点赞表
     */
    public function GetTbPraise($did=0)
    {
        $did    = CUtil::uint($did);
        $aData = by::WdynamicMain()->GetInfoByDid($did); //hack
        if( empty($aData) ) {
            return [false, '网络繁忙'];
        }

        $d_time = $aData['ctime'] ?? 0;

        if ($d_time <= 0) {
            return [false,"这条内容已经跑路了"];
        }

        $table = $this->tbName($d_time);
        return [true,$table];
    }

    /**
     * @param $user_id
     * @param $did
     * @return array
     * @throws \yii\db\Exception
     * 验证某用户对某动态是否已点赞
     */
    public function HasPraised($user_id, $did) {
        $redis      = by::redis('core');
        $redis_key  = $this->__hasPraised($user_id,$did);
        $flag       = $redis->get($redis_key);

        if($flag === false) {
            list($s,$tb)  = $this->GetTbPraise($did);
            if(!$s) {
                return [false,$tb,0];
            }

            $sql        = "SELECT `id` AS `num` From {$tb} 
                           WHERE `did`=:did AND `user_id`=:user_id 
                           AND `is_del`=0 LIMIT 1";

            $flag       = by::dbMaster()->createCommand($sql, [':did'=>$did, ':user_id'=>$user_id])->queryOne();
            $flag       = empty($flag) ? 0 : 1;

            $redis->set($redis_key, $flag, ['EX' => 600]);
        }

        return [true,'ok',intval($flag)];
    }

    /**
     * @param int $did
     * @return array
     * @throws \yii\db\Exception
     * 获取某条动态点赞总数
     */
    public function GetPraiseCount($did=0){
        $redis      = by::redis('core');
        $redis_key  = $this->__getPraise($did);
        $count      = $redis->get($redis_key);

        if($count === false) {
            list($s,$tb) = $this->GetTbPraise($did);
            if(!$s) {
                return [false, $tb, 0];
            }
            $sql        = "SELECT COUNT(*) AS `num` From {$tb} 
                           WHERE `did`=:did AND `is_del`=0";
            $ret        = by::dbMaster()->createCommand($sql, [':did'=>$did])->queryOne();
            $count      = $ret['num'] ?? 0;

            $redis->set($redis_key,$count, ['EX'=>600]);
        }

        return [true, 'ok', intval($count)];
    }


    /**
     * @param $user_id
     * @param $did
     * @param int $is_del
     * @return array
     * @throws \yii\db\Exception
     * 点赞或取消赞
     */
    public function SetPraise($user_id,$did,$is_del=0)
    {
        $unique_key = CUtil::getAllParams(__FUNCTION__,$did,$is_del);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,3,'EX');
        if(!$anti) {
            return [false,"请勿频繁操作"];
        }

        $time     = intval(START_TIME);
        $is_del   = intval(!!$is_del);

        list($s,$tb) = $this->GetTbPraise($did);
        if(!$s) {
            return [false,$tb];
        }

        $query  = " INSERT INTO {$tb} (`did`,`user_id`,`ctime`,`is_del`) 
                    VALUE (:did,:user_id,:ctime,:is_del) 
                    ON DUPLICATE KEY UPDATE `is_del`=:is_del";

        $params = [
            ':did'      => $did,
            ':user_id'  => $user_id,
            ':ctime'    => $time,
            ':is_del'   => $is_del,
        ];
        $ret = by::dbMaster()->createCommand($query, $params)->execute();

        if ($ret) {
            //点赞数上报
            $num            = $is_del == 0 ? 1 : -1;
            $mWdynamicMain  = by::WdynamicMain();
            $mWdynamicMain->UpdateHeat($mWdynamicMain::ACT_TYPE['PRAISE'], ['id' => $did, 'num'=>$num]);
        }

        //删除缓存
        $this->__delCache($user_id, $did);

        self::ReqAntiConcurrency($user_id,$unique_key,0,'DEL');

        return [true,"OK"];
    }

}
