<?php
/**
 * Created by IntelliJ IDEA.
 * Date: 2021/6/4
 * Time: 14:45
 * 话题表
 */
namespace app\modules\wiki\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;


class WtopicModel extends CommModel {

    CONST ListExpire    = 600;//列表缓存

    CONST LIMIT_L1      = 4;
    CONST LIMIT_L2      = 20;

    public $tb_fields = [
        'id','pid','name','level','ctime'
    ];

    public static function tbName(): string
    {
        return  "`db_dreame_wiki`.`t_wtopic`";
    }


    /**
     * @param $id
     * @return string
     */
    private function __getOneByIdKey($id): string
    {
        return AppCRedisKeys::getWtopicById($id);
    }

    /**
     * @return string
     */
    private function __getListKey() :string
    {
        return AppCRedisKeys::getWtopicList();
    }

    /**
     * @param $id
     * @return int
     * 缓存清理
     */
    private function __delCache($id): int
    {
        $r_key1 = $this->__getOneByIdKey($id);
        return by::redis('core')->del($r_key1);
    }

    private function __delListCache()
    {
        $r_key  = $this->__getListKey();
        return by::redis('core')->del($r_key);
    }


    /**
     * @param $id
     * @return array|false
     * @throws Exception
     * 获取详情
     */
    public function GetOneById($id)
    {
        $id          = CUtil::uint($id);
        $redis       = by::redis('core');
        $redis_key   = $this->__getOneByIdKey($id);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb      = $this->tbName();
            $t_wdtopic                   = by::Wdtopic()->tbName();
            $mainTb   = by::WdynamicMain()::tbName();
            $fields  = implode("`,`",$this->tb_fields);
             $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:id LIMIT 1";
            $aData   = by::dbMaster()->createCommand($sql, [':id' => $id])->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key,json_encode($aData),['EX'=>empty($aData) ? 10 : 600]);
        }

        return $aData;
    }

    /**
     * @param int $level
     * @param int $pid
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 获取列表
     */
    public function GetList($level=-1,$pid=-1,$page=1,$page_size=20)
    {
        $page        = CUtil::uint($page,1);
        $redis       = by::redis('core');
        $r_key       = $this->__getListKey();
        $sub_key     = CUtil::getAllParams(__FUNCTION__,$level,$pid,$page,$page_size);
        $aJson       = $redis->hGet($r_key,$sub_key);
        $aData       = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = $this->tbName();
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where, $params)= $this->__getCondition($level, $pid);
            $sql                 = "SELECT `id` FROM {$tb} WHERE {$where} ORDER BY `id` 
                                    LIMIT {$offset},{$page_size}";

            $aData               = by::dbMaster()->createCommand($sql,$params)->queryAll();

            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key,self::ListExpire);
        }

        return $aData;
    }

    /**
     * @param int $level
     * @param int $pid
     * @return int
     * @throws Exception
     * 列表总数
     */
    public function GetListCount($level=-1,$pid = 0)
    {
        $tb                  = $this->tbName();
        list($where, $params)= $this->__getCondition($level, $pid);
        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
        $count               = by::dbMaster()->createCommand($sql, $params)->queryScalar();

        return intval($count);
    }

    /**
     * @param $level
     * @param $pid
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($level,$pid): array
    {
        //SQL初始化条件
        $where      = "is_del = :is_del";
        $params     = [':is_del' => 0];

        if ($level > -1) {
            $where                .= " AND `level`=:level";
            $params[":level"]      = $level;
        }

        if($pid > -1) {
            $where                .= " AND `pid`=:pid";
            $params[":pid"]        = $pid;
        }

        return [$where,$params];
    }


    /**
     * @param string $c_json
     * @return array
     * @throws Exception
     * 数据增改
     */
    public function SaveLog($c_json = '')
    {
        $data   = json_decode($c_json, true);

        $db     = by::dbMaster();
        $tb     = self::tbName();

        $trans  = $db->beginTransaction();
        try {
            foreach($data as $aData) {
                $id     = $aData['id']   ?? 0;
                $pid    = $aData['pid']  ?? 0;
                $name   = $aData['name'] ?? '';

                $id     = CUtil::uint($id);
                $pid    = CUtil::uint($pid);

                if (empty($name)) {
                    throw new MyExceptionModel('请输入必要参数');
                }

                if ($id) {
                    $aInfo  = $this->GetOneById($id);
                    if (empty($aInfo)) {
                        throw new MyExceptionModel('参数错误(1)');
                    }

                    $db->createCommand()->update($tb, ['name' => $name], ['id' => $id])->execute();

                } else {
                    if ($pid) {
                        $aPinfo = $this->GetOneById($pid);
                        if (empty($aPinfo) || $aPinfo['level'] != 0) {
                            throw new MyExceptionModel('请选择正确的一级话题');
                        }

                        $count = $this->GetListCount(1);
                        if ($count > self::LIMIT_L2) {
                            throw new MyExceptionModel('二级话题最多20个');
                        }

                        $level = 1;
                    } else {
                        $count = $this->GetListCount(0);
                        if ($count > self::LIMIT_L1) {
                            throw new MyExceptionModel('一级话题最多4个');
                        }
                    }

                    $save   = [
                        'pid'   => $pid,
                        'name'  => $name,
                        'level' => $level ?? 0,
                        'ctime' => time(),
                    ];

                    $db->createCommand()->insert($tb, $save)->execute();

                    $id = $db->getLastInsertID();
                }

                $this->__delCache($id); //todo 清空缓存
                by::Wdtopic()->__delDidListCache();
            }

            $trans->commit();

            $this->__delListCache();

            return [true,'ok'];

        } catch (MyExceptionModel $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];

        } catch (\Exception $e) {
            $trans->rollBack();
            CUtil::debug($e->getMessage(), 'err.topic');
            return [false, '操作失败'];

        }
    }

    /**
     * @param $id
     * @return array
     * @throws Exception
     * 删除
     */
    public function Del($id)
    {
        $aPinfo = $this->GetOneById($id);

        if (empty($aPinfo)) {
            return [false, '参数有误'];
        }

        $db     = by::dbMaster();
        $tb     = self::tbName();
        $time   = intval(START_TIME);

        $trans  = $db->beginTransaction();

        try {
            if ($aPinfo['level'] == 0) {
                $db->createCommand()->update($tb, ['is_del' => 1, 'dtime'=>$time], ['pid' => $id])->execute();
            }

            $db->createCommand()->update($tb, ['is_del' => 1, 'dtime'=>$time], ['id' => $id])->execute();

            $trans->commit();

            by::Wdtopic()->__delDidListCache();
            $this->__delListCache();

            return [true, 'ok'];

        } catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($e->getMessage(), 'err.topic');
            return [false, '操作失败'];

        }

    }

    /**
     * @param array $tids
     * @return string
     * @throws Exception
     * 获取话题名称
     */
    public function GetTname($tids = [])
    {
        if (empty($tids)) {
            return '';
        }

        $tids   = array_unique($tids);

        $t_name = [];
        foreach($tids as $tid) {
            $aInfo  = $this->GetOneById($tid);
            if (empty($aInfo)) {
                continue;
            }
            $t_name[] = $aInfo['name'];
        }

        return implode(',', $t_name);
    }

    /**
     * 脚本删除已删除的话题内容
     */
    public function ShellDel()
    {
        $db     = by::dbMaster();
        $tb     = self::tbName();

        $dtime  = strtotime("-10 days"); //硬删除10天前删除的数据
        $sql    = "SELECT `id`,`pid` FROM {$tb} WHERE `is_del` = :is_del AND `dtime` < {$dtime}  LIMIT 50";
        $list   = $db->createCommand($sql, [':is_del' => 1])->queryAll();

        foreach($list as $val) {
            if ($val['pid'] == 0) {
                //todo 删除本条记录
                $db->createCommand()->delete($tb, ['id' => $val['id']])->execute();
                continue;
            }

            //todo 删除内容和话题绑定关系
            by::Wdtopic()->ShellDel($val['id']);

            //todo 删除本条记录
            $db->createCommand()->delete($tb, ['id' => $val['id']])->execute();
        }
    }



}
