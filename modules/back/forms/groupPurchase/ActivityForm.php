<?php

namespace app\modules\back\forms\groupPurchase;

use app\modules\back\forms\BaseModel;

class ActivityForm extends BaseModel
{
    public $id;
    public $name;
    public $start_time;
    public $end_time;
    public $details;
    public $min_members;
    public $max_members;
    public $utime;
    public $goods;

    public function rules(): array
    {
        return [
                [['id','name', 'start_time', 'end_time', 'details', 'min_members', 'max_members', 'goods'], 'safe'],
                [['name', 'start_time', 'end_time', 'max_members', 'details', 'goods'], 'required', 'message' => '此项为必填项'],
                [['max_members'], 'integer', 'message' => '必须为整数'],
                ['name', 'string', 'max' => 255, 'message' => '名称必须为字符串，且长度不能超过255个字符'],
                [['start_time', 'end_time'], 'integer', 'message' => '日期格式不正确'],
                ['details', 'string', 'message' => '详情必须为字符串'],
//                ['min_members', 'compare', 'compareAttribute' => 'max_members', 'operator' => '<', 'enableClientValidation' => false, 'message' => '最小成员数必须小于最大成员数'],
                ['start_time', 'compare', 'compareAttribute' => 'end_time', 'operator' => '<', 'enableClientValidation' => false, 'message' => '开始时间必须小于结束时间'],
                ['utime', 'default', 'value' => time()],
                ['goods', 'validateGoods'],
        ];
    }

    public function validateGoods($attribute, $params)
    {
        // 判断 $this->goods 是否已经是数组
        if (is_array($this->goods)) {
            $goodsArray = $this->goods;
        } else {
            $goodsArray = json_decode($this->goods, true);
            if (!is_array($goodsArray)) {
                $this->addError($attribute, '商品必须是一个JSON数组。');
                return;
            }
        }

        $gids = [];
        foreach ($goodsArray as $index => $good) {
//            if (!isset($good['min_members']) || $good['max_members'] == "") {
//                $goodsArray[$index]['min_members'] = $this->min_members;
//                $good['min_members'] = $this->min_members;
//            }

            if (!isset($good['max_members']) || $good['max_members'] == "") {
                $goodsArray[$index]['max_members'] = $this->max_members;
                $good['max_members'] = $this->max_members;
            }

            $goodsArray[$index]['min_members'] = $good['max_members'];
            $goodsArray[$index]['min_group_qty'] = 0;


            $checkMap= ['gid' => '关联商品ID', 'purchase_limit' => '限购数量','max_members' => '最大成员数'];
            foreach ($checkMap as $key => $value) {
                $index_d = $index + 1;
                if (!isset($good[$key])) {
                    $this->addError($attribute, "第 $index_d 条的商品必需有 '$value'");
                }

                if (!is_numeric($good[$key])||$good[$key]<=0) {
                    $this->addError($attribute, "第 $index_d 条的商品的 $value 必须是正整数");
                }
            }

            // 验证 tag_id 字段
            if (isset($good['tag_id'])) {
                if (!is_numeric($good['tag_id']) || $good['tag_id'] < 0) {
                    $index_d = $index + 1;
                    $this->addError($attribute, "第 $index_d 条的商品的 标签ID 必须是非负整数");
                }
            } else {
                // 如果没有提供 tag_id，默认设置为 0
                $goodsArray[$index]['tag_id'] = 0;
            }

            // 检查gid重复
            if (isset($good['gid'])) {
                if (in_array($good['gid'], $gids)) {
                    $index_d = $index + 1;
                    $this->addError($attribute, "第 $index_d 条的商品的 关联商品ID(gid) 不能重复");
                } else {
                    $gids[] = $good['gid'];
                }
            }
        }

        $this->min_members=$this->max_members;

        $this->goods = $goodsArray;
    }

    public function attributeLabels(): array
    {
        return [
            'id' => 'ID',
            'name' => '活动名称',
            'start_time' => '开始时间',
            'end_time' => '结束时间',
            'details' => '活动详情',
            'min_members' => '最小成员数',
            'max_members' => '最大成员数',
            'goods' => '商品',
        ];
    }

}