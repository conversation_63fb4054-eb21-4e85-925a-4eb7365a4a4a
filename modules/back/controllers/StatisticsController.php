<?php
/*
 * @Author: linze
 * @Date: 2022-03-03 10:53:16
 * @LastEditors: linze
 * @LastEditTime: 2022-03-03 16:15:48
 * @Description: file content
 * @FilePath: \dreame\modules\back\controllers\StatisticsController.php
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;

/**
 * 数据报表统计类
 */
class StatisticsController extends CommController
{
    /**
     * 用户统计数据uvpv
     */
    public function actionUserData()
    {
        $sDay           = \Yii::$app->request->post('sDay', 0);
        $eDay           = \Yii::$app->request->post('eDay', 0);
        $name           = \Yii::$app->request->post('name', '');
        list($st, $data) = by::statistics()->getUserList($sDay, $eDay, $name);
        if (!$st) {
            CUtil::json_response(-1, '获取失败', $data);
        }

        CUtil::json_response(1, '获取成功', $data);
    }
}