<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/11/15
 * Time: 16:29
 */

namespace app\modules\back\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;

class MarketSendController extends CommController {

	/**
	 * 营销资源发放列表
	 * @throws \yii\db\Exception
	 */
    public function actionList() {
        $request = \Yii::$app->request;
        $name    = $request->post('name');                      //活动名称
        $s_time  = $request->post('start_time');                //开始时间
        $e_time  = $request->post('end_time');                  //结束时间
        $page    = $request->post('page', 1);     //页数

        $page_size       = 20;
        $count           = by::marketSend()->getMarketSendTotal($name, $s_time, $e_time); //列表总数
        $return['total'] = $count;
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $ids             = by::marketSend()->getMarketSendList($page, $page_size, $name, $s_time, $e_time);
        foreach($ids as $id) {
            $aData              = by::marketSend()->getOneById($id);
            $stock_num          = by::marketSend()->optStockNum($aData['id']);
            $aData['send_num']  = bcadd($aData['send_num'], bcsub($aData['stock_num'], $stock_num));
            $aData['stock_num'] = $stock_num;
            $aMarket            = by::marketConfig()->getOneById($aData['market_id']);
            $aData['m_name']    = $aMarket['name']??'';
            $return['list'][]   = $aData;
        }

        CUtil::json_response(1, 'ok', $return);
    }


	/**
	 * 添加 编辑
	 * @throws \yii\db\Exception
	 */
    public function actionAdd() {
        $request = \Yii::$app->request;
        $id      = $request->post("id", '');
        $data    = [
            'name'       => $request->post('name'),             //活动名称
            'market_id'  => $request->post('market_id', 0),     //营销资源id
            'start_time' => $request->post('start_time', 0),    //开始时间
            'end_time'   => $request->post('end_time', 0),      //结束时间
            'stock_num'  => $request->post('stock_num',0),        //库存
            'gid'        => CUtil::uint($request->post('gid',0)),        //关联商品
        ];
        list($status, $msg) = by::marketSend()->modify($id, $data);

        CUtil::json_response($status == 1 ? 1 : -1, $msg);

    }

	/**详情
	 * @throws \yii\db\Exception
	 */
    public function actionDetail() {
        $id = \Yii::$app->request->post('id');
        if (empty($id)) {
            CUtil::json_response(-1, 'id不能为空');
        }
        $data  = by::marketSend()->getOneById($id);
        $goods = by::Gmain()->GetOneByGid($data['gid']);
        $data['g_name']  = $goods['name'] ?? '';
        $data['g_image'] = $goods['cover_img'] ?? '';
        CUtil::json_response(1, 'ok', $data);
    }


	/**删除
	 * @throws \yii\db\Exception
	 */
    public function actionDel() {
        $request = \Yii::$app->request;
        $id      = $request->post("id");
        list($st, $msg) = by::marketSend()->del($id);
        CUtil::json_response($st == 1 ? 1 : -1, $msg);
    }


}
