<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2022/2/9
 * Time: 16:40
 */
namespace app\modules\back\controllers;

use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\back\services\RetailersService;
use app\modules\rbac\controllers\CommController;
use app\models\by;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
class RetailersController extends CommController{


    /**
     * @OA\Post(
     *     path="/back/retailers/save",
     *     summary="更新门店信息",
     *     description="更新门店信息",
     *     tags={"周边门店管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/RetailersSaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
	public function actionSave() {
		$data = \Yii::$app->request->post();
		list($status, $ret) = by::retailers()->saveAddr($data);
		if (!$status) {
			CUtil::json_response(-1, $ret);
		}
		$gid   = $data['id']   ?? 0;
		$name  = $data['shop_name'] ?? "";
		$msg   = $gid ? "修改了门店ID：{$gid}" : "新增了门店信息：{$name}";
		$msg  .= ",内容：".json_encode($ret,JSON_UNESCAPED_UNICODE);
		(new SystemLogsModel())->record($msg, RbacInfoModel::RETAILERS_MANAGER, $this->user_info['id']);
		CUtil::json_response(1, 'ok');
	}

    /**
     * @OA\Post(
     *     path="/back/retailers/list",
     *     summary="门店列表",
     *     description="门店列表",
     *     tags={"周边门店管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/RetailersListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/RetailersListResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post             = \Yii::$app->request->post();
        $page             = $post['page'] ?? 1;
        $name             = $post['name'] ?? '';
        $is_open_activity = intval($post['is_open_activity'] ?? 0);
        $page_size        = $post['page_size'] ?? 500;
        $count            = by::retailers()->getCount($name, [], $is_open_activity);
        $return['count']  = $count;
        $return['pages']  = CUtil::getPaginationPages($count, $page_size);
        $return['list']   = by::retailers()->getList($page, $page_size, $name, [], $is_open_activity);
        if ($return['list']) {
            $retailers      = by::retailers();
            $return['list'] = array_map(function ($item) use ($retailers) {
                list($s, $item['shop_code_str']) = empty($item['shop_code']) ? [0, ''] : $retailers->encrypt($item['shop_code']);
                return $item;
            }, $return['list']);
        }

        CUtil::json_response(1, 'ok', $return);
    }

	/**
	 * 门店详情
	 * @throws \yii\db\Exception
	 */
	public function actionInfo(){
		$post        = \Yii::$app->request->post();
		$id          = $post['id']??0;  //门店id
		$aData = by::retailers()->backInfo($id);
		if(empty($aData)) {
			CUtil::json_response(-1,"门店不存在");
		}
		CUtil::json_response(1, 'ok', $aData);
	}

	public function actionStatus(){
        $post        = \Yii::$app->request->post();
        $id          = $post['id']      ?? 0;  //门店id
        $status      = $post['status']  ?? 0;
        list($status,$ret) = by::retailers()->upStatus($id,$status);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }
        CUtil::json_response(1, 'ok');

    }


    /**
     * @OA\Post(
     *     path="/back/retailers/patch-activity",
     *     summary="门店活动配置",
     *     description="门店活动配置",
     *     tags={"周边门店管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/RetailersRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionPatchActivity()
    {
        try {
            $data = \Yii::$app->request->post();
            $id   = $data['id'] ?? 1; // 建议从请求中获取 ID，而不是写死 1

            // 调用 service 修改数据
            RetailersService::getInstance()->update($id, $data);

            // 记录日志
            $startTime = !empty($data['start_time']) ? date('Y-m-d H:i:s', $data['start_time']) : '1970-01-01 00:00:00';
            $endTime   = !empty($data['end_time']) ? date('Y-m-d H:i:s', $data['end_time']) : '1970-01-01 00:00:00';
            $msg       = "将门店下线活动开始时间：{$startTime} 结束时间为：{$endTime}";

            (new SystemLogsModel())->record($msg, RbacInfoModel::RETAILERS_MANAGER, $this->user_info['id']);

            CUtil::json_response(1, '操作成功');
        } catch (MyExceptionModel $e) {
            CUtil::json_response(-1, $e->getMessage());
        }
    }


    /**
     * @OA\Post(
     *     path="/back/retailers/batch-open",
     *     summary="批量开启门店活动",
     *     description="批量开启门店活动",
     *     tags={"周边门店管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/RetailersBatchOpenRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionBatchOpen()
    {
        $post = \Yii::$app->request->post();
        $ids  = $post['ids'] ?? '';
        $act  = $post['act'] ?? '';

        if (empty($ids) || empty($act)) {
            CUtil::json_response(-1, '参数错误');
        }

        if (!is_array($ids)) {
            $ids = array_filter(explode(',', $ids)); // 确保 ids 是数组
        }

        if (empty($ids)) {
            CUtil::json_response(-1, '门店 ID 不能为空');
        }

        $ret = RetailersService::getInstance()->batchOpen($ids, $act);

        if ($ret > 0) {
            // 记录日志
            $msg = "将门店ID：" . implode(',', $ids) . " 活动状态设置为 " . $act;
            (new SystemLogsModel())->record($msg, RbacInfoModel::RETAILERS_MANAGER, $this->user_info['id']);

            CUtil::json_response(1, '操作成功');
        }

        CUtil::json_response(-1, '操作失败');
    }


    /**
     * @OA\Post(
     *     path="/back/retailers/activity-info",
     *     summary="门店活动详情",
     *     description="门店活动详情",
     *     tags={"周边门店管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/RetailersActivityInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityInfo()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        if (empty($id)) {
            CUtil::json_response(-1, '活动 ID 不能为空');
        }
        $ret = RetailersService::getInstance()->activityInfo($id);
        CUtil::json_response(1, '', $ret);
    }


}