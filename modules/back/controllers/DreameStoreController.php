<?php

namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\modules\back\services\DreameStoreService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;

/**
 * 追觅小店 - 控制器 - 后台
 */
class DreameStoreController extends CommController
{
    use ControllerTrait;

    /** @var DreameStoreService */
    private $service;
    public function __construct($id, $module, $config = [])
    {
        $this->service = DreameStoreService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * 列表
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getList($post));
    }

    /**
     * 新增
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
//            $form = new GoodsCrontabStockForm();
//            $form->setScenario( 'create');
//            if (! ($form->load($post, '') && $form->validate())) {
//                $errors = $form->firstErrors;
//                $this->error(array_shift($errors));
//            }
//            $params = $form->toArray();
            $params = $post;

            $res = $this->service->create($params);
            if (! $res) {
                throw new BusinessException('添加失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 新增
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
//            $form = new GoodsCrontabStockForm();
//            $form->setScenario( 'update');
//            if (! ($form->load($post, '') && $form->validate())) {
//                $errors = $form->firstErrors;
//                $this->error(array_shift($errors));
//            }
//            $params = $form->toArray();
            $params = $post;

            $res = $this->service->update($params);
            if (! $res) {
                throw new BusinessException('修改失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 删除
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);

            $res =  $this->service->del($ids);
            if (!$res) {
                $this->error('失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 审核
     */
    public function actionAudit()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);
            $reason = $post['reason'] ?? null;
            $status = $post['status'] ?? null;
            if ($status === '' || $status === null) {
                $this->error('状态不能为空');
            }
            if (!in_array($status, ['0','1','2','3'])) {
                $this->error('状态参数错误');
            }

            $res = $this->service->audit($ids,$status, $reason);
            if (! $res) {
                $this->error('失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 详情
     */
    public function actionInfo()
    {
        try {
            $post = \Yii::$app->request->post();
            $id = $post['id'] ?? 0;
            if (empty($id)) {
                $this->error('缺少ID参数');
            }
            $this->success($this->service->info($id));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}