<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\back\services\GoodsParamService;
use app\modules\rbac\controllers\CommController;

/**
 * 商品参数
 */
class GoodsParamController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/goods-param/add-group",
     *     summary="添加分组",
     *     description="添加分组",
     *     tags={"商品参数"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="c_id", type="integer", required={"c_id"},description="商品二级类目ID"),
     *              @OA\Property(property="name", type="string", required={"name"},description="分组名称"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionAddGroup()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $cId = $params['c_id'] ?? '';
        $name = trim($params['name'] ?? '');

        // 验证
        if (empty($cId) || (mb_strlen($name) == 0)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 限制200
        if (mb_strlen($name) > 200) {
            CUtil::json_response(-1, '名称长度超过200');
        }

        // 添加分组
        list($res, $msg) = GoodsParamService::getInstance()->AddGroup($cId, $name);
        if (!$res) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok', ['id' => $msg]);
    }

    /**
     * @OA\Post(
     *     path="/back/goods-param/edit-group",
     *     summary="编辑分组名称",
     *     description="编辑分组名称",
     *     tags={"商品参数"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="id", type="integer", required={"id"},description="分组ID"),
     *              @OA\Property(property="name", type="string", required={"name"},description="分组名称"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionEditGroup()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $id = $params['id'] ?? '';
        $name = trim($params['name'] ?? '');

        // 验证
        if (empty($id) || (mb_strlen($name) == 0)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        if (mb_strlen($name) > 200) {
            CUtil::json_response(-1, '名称长度超过200');
        }

        // 修改分组名
        list($res, $msg) = GoodsParamService::getInstance()->EditGroup($id, $name);
        if (!$res) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok', ['id' => $msg]);
    }

    /**
     * @OA\Post(
     *     path="/back/goods-param/del-group",
     *     summary="删除分组",
     *     description="删除分组",
     *     tags={"商品参数"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="sign",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="sign-time",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="id", type="integer", required={"id"},description="参数分组ID"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"iRet","sMsg"},
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionDelGroup()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $id = $params['id'] ?? '';

        // 验证
        if (empty($id)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 删除分组
        list($res, $msg) = GoodsParamService::getInstance()->DelGroup($id);
        if (!$res) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * 参数列表
     */
    public function actionParamList()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $name = trim($params['name'] ?? '');

        // 获取参数列表
        $res = GoodsParamService::getInstance()->GetParamList($name);
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 获取参数类型详情
     */
    public function actionGroupParamDetail()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $cId = $params['c_id'] ?? '';

        // 验证参数
        if (empty($cId)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 获取参数分类详情
        $res = GoodsParamService::getInstance()->GetGroupParamDetail($cId);
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 编辑参数（提交添加、删除等）
     */
    public function actionEditGroupParam()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $data = $params['data'] ?? '';

        // 验证参数
        if (empty($data)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // JSON 转 数组
        $data = json_decode($data, true);

        // 编辑参数分类
        list($res, $msg) = GoodsParamService::getInstance()->EditGroupParam($data['cate_id'], $data['params']);
        if ($res) {
            CUtil::json_response(1, 'ok');
        }
        CUtil::json_response(-1, $msg);
    }

    /**
     * 获取商品参数详情
     */
    public function actionGoodsParamDetail()
    {
        // 请求参数
        $params = \Yii::$app->request->post();
        $cId = $params['c_id'] ?? '';
        $sku = $params['sku'] ?? '';

        // 验证参数
        if (empty($cId) || empty($sku)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 编辑参数分类
        $res = GoodsParamService::getInstance()->GetGoodsParamDetail($cId, $sku);
        CUtil::json_response(1, 'ok', $res);
    }


}