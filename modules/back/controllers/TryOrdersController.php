<?php

namespace app\modules\back\controllers;


use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\services\TryOrdersService;
use app\modules\goods\services\UserOrderTryService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;
use yii\db\Exception;


class TryOrdersController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/try-orders/list",
     *     summary="试用订单列表",
     *     description="试用订单列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryOrdersRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/TryOrdersResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post     = \Yii::$app->request->post();
        $page     = CUtil::uint($post['page'] ?? 1);
        $pageSize = CUtil::uint($post['page_size'] ?? 20);
        $input    = [
            'year'        => $post['year'] ?? date("Y"),
            'status'      => $post['status'] ?? -1,
            'order_type'  => $post['type'] ?? 0,
            'user_iden'   => $post['user_iden'] ?? "",
            'order_no'    => $post['order_no'] ?? "",
            'source'      => $post['source'] ?? -1,
            'p_sources'   => $post['p_sources'] ?? -1,
            'live_mark'   => $post['live_mark'] ?? '',
            'store'       => $post['store'] ?? '',
            'label'       => $post['label'] ?? '',
            'source_code' => $post['source_code'] ?? '',
            'goods_name'  => $post['goods_name'] ?? '',
            'sku'         => $post['sku'] ?? '',
            'type'        => [by::Omain()::USER_ORDER_TYPE['BAT']],
            'order_time'  => [
                'st' => $post['order_st'] ?? 0,
                'ed' => $post['order_ed'] ?? 0,
            ],
        ];
        $ret      = TryOrdersService::getInstance()->GetOrderList($input, $page, $pageSize);
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/try-orders/info",
     *     summary="用户-获取试用订单详情",
     *     description="用户-获取试用订单详情",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"uid","order_no"},
     *           @OA\Property(property="uid", type="string", default="", description="USER_ID 用户ID"),
     *           @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {

        $post                   = \Yii::$app->request->post();
        $post['view_sensitive'] = $this->viewSensitive;
        $ret                    = TryOrdersService::getInstance()->GetOrderInfo($post);
        CUtil::json_response(1, '获取成功', $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/try-orders/refund-list",
     *     summary="用户-获取试用退款列表",
     *     description="用户-获取试用退款列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryOrdersRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionRefundList()
    {
        $post      = \Yii::$app->request->post();
        $page      = $post['page'] ?? 1;
        $page_size = 20;
        $input     = [
            'status'     => $post['status'] ?? -1,
            'user_iden'  => $post['user_iden'] ?? "",
            'order_no'   => $post['order_no'] ?? "",
            'order_time' => [
                'st' => $post['order_st'] ?? 0,
                'ed' => $post['order_ed'] ?? 0,
            ],
        ];
        $status    = $post['status'] ?? -1;

        $return['list'] = [];

        if (!in_array($status, by::OrefundMain()::STATUS)) {
            CUtil::json_response(1, "OK", []);
        }

        $rets = byNew::TryOrdersRefundModel()->GetTryRefundOrderList($input, $page, $page_size);
        foreach ($rets as $ret) {
            $info    = by::Orefund()->CommPackageInfo($ret['user_id'], $ret['refund_no'], false, true);
            $source  = by::Omain()->getSourceByNo($info['order_no']);
            $tryInfo = byNew::UserOrderTry()->GetOneInfo([
                CUtil::buildCondition('user_id', '=', $ret['user_id']),
                CUtil::buildCondition('order_no', '=', $ret['order_no']),
            ]);
            $uidInfo = byNew::UserTryModel()->getInfoByUserId($ret['user_id']);
            list($_, $ostatus) = by::Omain()->SplitOrderStatus($info['ostatus']);
            $info = [
                'order_no'  => $info['order_no'],
                'refund_no' => $info['refund_no'],
                'user_id'   => $info['user_id'],
                'uid'       => $uidInfo['uid'] ?? '',
                'status'    => $info['status'],
                'ostatus'   => $ostatus,
                'mail_no'   => $info['mail_no'],
                'source'    => $source,
                'ctime'     => $info['ctime'],
                'goods'     => $info['goods'],
                'price'     => $info['price'],
                'rback'     => $info['rback'],
                'try_info'  => $tryInfo ?? [],
            ];

            $rback         = $info['status'] == by::OrefundMain()::STATUS['P_REJECT'] ? 1 : $info['rback'];
            $info['rback'] = $rback;

            $return['list'][] = $info;
        }

        $count           = byNew::TryOrdersRefundModel()->GetTryRefundOrderListCount($input);
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }



    /**
     * @OA\Post(
     *     path="/back/try-orders/refund-info",
     *     summary="用户-获取试用退款列表",
     *     description="用户-获取试用退款列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *             required = {"uid","refund_no"},
     *          @OA\Property(property="uid", type="string", default="", description="USER_ID 用户ID"),
     *          @OA\Property(property="refund_no", type="string", default="", description="退款订单号"),
     * )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */

    public function actionRefundInfo()
    {
        $post      = \Yii::$app->request->post();
        $user_id    = $post['uid'] ?? 0;
        $refund_no  = $post['refund_no'] ?? 0;
        if(empty($user_id) || empty($refund_no)){
            CUtil::json_response(1, 'ok', []);
        }
        $info       = by::Orefund()->CommPackageInfo($user_id,$refund_no,true,true);
        if (!empty($info)) {
            list($_, $info['ostatus'])  = by::Omain()->SplitOrderStatus($info['ostatus']);

            $tryInfo = UserOrderTryService::getInstance()->GetUserOrderTryInfo([
                CUtil::buildCondition('user_id', '=', $info['user_id']),
                CUtil::buildCondition('order_no', '=', $info['order_no']),
            ]);
            $uidInfo = byNew::UserTryModel()->getInfoByUserId($info['user_id']);
            $info['user']['uid'] = $uidInfo['uid'] ?? '';
            $info['try_info'] = $tryInfo ?? [];
            if ($info['try_info']) {
                $info['try_info'] = TryOrdersService::getInstance()->FormatExceptionData($info['try_info']);
            }
        }
        $arr = [
            'cid'    => 'tm',
            'city'   => 'tm',
            'aid'    => 'tm',
            'area'   => 'tm',
            'detail' => 'tm',
            'phone'  => 'tm',
        ];
        if(!$this->viewSensitive){
            $info['user'] = Response::responseList($info['user']??[], $arr);
        };

        CUtil::json_response(1, 'ok', $info);
    }


    /**
     * @OA\Post(
     *     path="/back/try-orders/exception-list",
     *     summary="用户-异常订单列表",
     *     description="用户-异常订单列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryOrdersRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionExceptionList()
    {
        $post     = \Yii::$app->request->post();
        $page     = CUtil::uint($post['page'] ?? 1);
        $pageSize = CUtil::uint($post['page_size'] ?? 20);
        $input = [
            'year'        => $post['year'] ?? date("Y"),
            'status'      => $post['status'] ?? -1,
            'try_status'  => $post['try_status'] ?? -1,
            'order_type'  => $post['type'] ?? 0,
            'user_iden'   => $post['user_iden'] ?? "",
            'order_no'    => $post['order_no'] ?? "",
            'source'      => $post['source'] ?? -1,
            'p_sources'   => $post['p_sources'] ?? -1,
            'live_mark'   => $post['live_mark'] ?? '',
            'store'       => $post['store'] ?? '',
            'label'       => $post['label'] ?? '',
            'refund_no'   => $post['refund_no'] ?? '',
            'source_code' => $post['source_code'] ?? '',
            'goods_name'  => $post['goods_name'] ?? '',
            'sku'         => $post['sku'] ?? '',
            'type'        => [by::Omain()::USER_ORDER_TYPE['BAT']],
            'order_time'  => [
                'st' => $post['order_st'] ?? 0,
                'ed' => $post['order_ed'] ?? 0,
            ],
        ];
        $ret      = TryOrdersService::getInstance()->GetExceptionList($input, $page, $pageSize);
        CUtil::json_response(1, 'ok', $ret);
    }



     /**
     * @OA\Post(
     *     path="/back/try-orders/exception-raudit",
     *     summary="先试后买完结/强制扣款订单",
     *     description="先试后买完结/强制扣款订单",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="refund_no", type="string",description="退款单号"),
     *              @OA\Property(property="order_no", type="string",description="订单号"),
     *              @OA\Property(property="status", type="string",description="审核状态 1010 订单完结 1020 强制扣款"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionExceptionRaudit()
    {
        $post           = \Yii::$app->request->post();
        list($lock,$msg) = CUtil::payLock(0);
        if($lock){
            CUtil::json_response(-1,$msg);
        }
        $post['force'] = true;

        $status = $post['status'] ?? 0;
        if(empty($post['refund_no']) && $status == 1020){
            // 仅强制扣款，订单状态不变
            list($status, $msg) = by::OrefundMain()->TryOrderAudit($post);
        }else{
            // 处理订单状态
            list($status, $msg) = by::OrefundMain()->Audit($post);
        }

        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("先试后买退款订单：{$post['order_no']}修改状态：{$post['status']}|1010 订单完结|1020 强制扣款", RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/try-orders/exception-begin",
     *     summary="先试后买开始试用",
     *     description="先试后买开始试用",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="order_no", type="string",description="订单号")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionExceptionBegin()
    {
        $post           = \Yii::$app->request->post();
        $order_no       = $post['order_no'] ?? '';
        if(empty($order_no)){
            CUtil::json_response(-1, "订单编号必传");
        }

        list($status, $msg) = TryOrdersService::getInstance()->BeginTry($order_no);
        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("先试后买退款订单：{$post['order_no']}|订单开始试用", RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/try-orders/exception-refund",
     *     summary="先试后买客服创建退款订单",
     *     description="先试后买客服创建退款订单",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="order_no", type="string",description="订单号"),
     *              @OA\Property(property="user_id", type="integer",description="用户ID必填")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     * @throws Exception
     */
    public function actionExceptionRefund()
    {
        $post           = \Yii::$app->request->post();
        $order_no       = $post['order_no'] ?? '';
        if(empty($order_no)){
            CUtil::json_response(-1, "订单编号必传");
        }

        $user_id        = $post['user_id'] ?? 0;
        if(empty($user_id)){
            CUtil::json_response(-1, "用户ID必传");
        }
        $adminUserId = by::adminUserModel()->getUserIDFromSession();
        $mOgoods = by::Ogoods();
        // 订单商品详情
        $oGoods = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);
        $oGoods = array_column($oGoods, 'status', 'id');
        $og_ids = array_keys($oGoods);

        $arr = [
            'order_no' => $order_no,
            'user_id'  => $user_id,
            'm_type'   => 2,
            'r_type'   => 9,
            'describe' => '先试后买客服创建退款订单，客服ID为：' . by::adminUserModel()->getUserIDFromSession(),
            'og_ids'   => json_encode($og_ids, 320),
            'images'   => '',
        ];

        list($status, $msg) = by::OrefundMain()->ApplyRefund($user_id, $arr, false);
        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("先试后买客服创建退款订单：{$post['order_no']}|客服ID：".$adminUserId, RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }
}
