<?php

namespace app\modules\back\controllers;

use app\constants\RespStatusCodeConst;
use app\models\CUtil;
use app\modules\back\forms\PaymentActivityForm;
use app\modules\back\services\PaymentActivityService;
use app\modules\rbac\controllers\CommController;
use yii\web\Response;
use Yii;

class PaymentActivityController extends CommController
{
    private $service;

    public function __construct($id, $module, PaymentActivityService $service, $config = [])
    {
        parent::__construct($id, $module, $config);
        $this->service = $service;
    }


    /**
     * @OA\Post(
     *     path="/back/payment-activity/list",
     *     summary="支付活动列表",
     *     description="获取支付活动的列表信息，可以根据时间和活动名称进行筛选",
     *     tags={"支付活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="activity_name", type="string", description="活动名称"),
     *                         @OA\Property(property="page", type="string", description="当前页"),
     *                         @OA\Property(property="page_size", type="string", description="每页条数")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功返回支付活动列表",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="活动ID"),
     *                         @OA\Property(property="activity_name", type="string", description="活动名称"),
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间")
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionList()
    {
        $params     = Yii::$app->request->post();
        $activities = $this->service->getAll($params);
        CUtil::Ret($activities);
    }

    /**
     * @OA\Post(
     *     path="/back/payment-activity/detail",
     *     summary="支付活动详情",
     *     description="根据活动ID获取支付活动的详细信息",
     *     tags={"支付活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功返回支付活动详情",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="string", description="活动ID"),
     *                 @OA\Property(property="activity_name", type="string", description="活动名称"),
     *                 @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                 @OA\Property(property="pay_type", type="string", description="支付类型"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(
     *                             property="gid",
     *                             type="string",
     *                             description="商品ID"
     *                         ),
     *                         @OA\Property(
     *                             property="interest_free",
     *                             type="string",
     *                             description="免息情况"
     *                         ),
     *                         @OA\Property(
     *                             property="goods_name",
     *                             type="string",
     *                             description="商品名称"
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionDetail()
    {
        $id       = Yii::$app->request->post('id');
        $activity = $this->service->getById($id);

        CUtil::Ret($activity);
    }


    /**
     * @OA\Post(
     *     path="/back/payment-activity/save",
     *     summary="支付活动新增/编辑",
     *     description="新增或编辑支付活动信息",
     *     tags={"支付活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"activity_name", "start_time", "end_time", "pay_type", "goods"},
     *                         @OA\Property(property="id", type="string", description="活动ID 编辑时必传"),
     *                         @OA\Property(property="activity_name", type="string", description="活动名称"),
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                         @OA\Property(property="pay_type", type="string", description="支付活动类型 1：京东白条"),
     *                         @OA\Property(property="goods", type="string", description="商品信息的JSON字符串 [{interest_free:12,gids:[111,112]}]")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功新增或编辑支付活动",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="string", description="活动ID"),
     *                 @OA\Property(property="activity_name", type="string", description="活动名称"),
     *                 @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                 @OA\Property(property="pay_type", type="string", description="支付类型"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="interest_free", type="integer", description="免息情况下的信息"),
     *                         @OA\Property(property="gid", type="integer", description="商品ID"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="id", type="integer", description="商品ID")
     *                     )
     *                 ),
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionSave()
    {
        $form     = CUtil::VdForm(new PaymentActivityForm());
        $activity = $this->service->save($form);
        CUtil::Ret($activity);
    }


    /**
     * @OA\Post(
     *     path="/back/payment-activity/delete",
     *     summary="支付活动删除",
     *     description="根据活动ID删除指定的支付活动",
     *     tags={"支付活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功删除支付活动",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="status", type="boolean", description="操作状态"),
     *                 @OA\Property(property="message", type="string", description="提示消息")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id');
        CUtil::Ret($this->service->delete($id));
    }
    /**
     * @OA\Post(
     *     path="/back/payment-activity/goods-search",
     *     summary="支付活动商品搜索",
     *     description="搜索支付活动中的商品信息",
     *     tags={"支付活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="name", type="string", description="商品名称")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="name", type="string", description="商品名称"),
     *                         @OA\Property(property="price", type="string", description="商品价格"),
     *                         @OA\Property(property="cover_image", type="string", description="商品封面图片")
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionGoodsSearch()
    {
        $name = Yii::$app->request->post('name');
        $goods = $this->service->goodsSearch($name);
        CUtil::Ret($goods);
    }
}