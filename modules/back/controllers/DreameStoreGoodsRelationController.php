<?php
/*
 * @Author: linze
 * @Date: 2022-03-03 10:53:16
 * @LastEditors: linze
 * @LastEditTime: 2022-03-03 16:15:48
 * @Description: file content
 * @FilePath: \dreame\modules\back\controllers\StatisticsController.php
 */

namespace app\modules\back\controllers;

use app\modules\back\services\DreameStoreGoodsRelationService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;

/**
 * 追觅小店店铺商品 - 控制器 - 后台
 */
class DreameStoreGoodsRelationController extends CommController
{

    use ControllerTrait;

    /* @var DreameStoreGoodsRelationService */
    private $service;

    public function init()
    {
        parent::init();
        $this->service = DreameStoreGoodsRelationService::instance();
    }

    /**
     * 列表
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getPageList($post));
    }

    /**
     * 获取用户所有添加的商品
     */
    public function actionUserGoodsAll()
    {
        $post = \Yii::$app->request->post();
        $user_id = $post['user_id'] ?? '';
        if (empty($user_id)) {
            $this->error('缺少user_id字段');
        }
        $all = $this->service->getAll($post);
        $this->success(['list' => $all]);
    }


    /**
     * 新增
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $params = $post;
            $this->service->create($params);
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 新增
     */
    public function actionBatchCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $params = $post;
            $this->service->batchCreate($params);
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * 删除
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);

            $res =  $this->service->del($ids);
            if (!$res) {
                $this->error('失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * 详情
     */
    public function actionInfo()
    {
        try {
            $post = \Yii::$app->request->post();
            $id = $post['id'] ?? 0;
            if (empty($id)) {
                $this->error('缺少ID参数');
            }
            $this->success($this->service->info($id));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}