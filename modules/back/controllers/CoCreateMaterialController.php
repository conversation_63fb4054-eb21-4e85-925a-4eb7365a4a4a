<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\wares\services\cocreate\CoCreateMaterialService;
use yii\db\Exception;

class CoCreateMaterialController extends Comm<PERSON>ontroller
{

    /**
     * @OA\Post(
     *     path="/back/co-create-material/list",
     *     summary="后台-获取共创素材列表",
     *     description="后台-获取共创素材列表",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionList()
    {
        $post  = \Yii::$app->request->post();
        $input = [
            'id'       => trim($post['id'] ?? ""),
            'name'        => trim($post['name'] ?? ""),
            'topic'        => trim($post['topic'] ?? ""),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = CoCreateMaterialService::getInstance()->GetList($input,$page,$page_size);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-material/save",
     *     summary="后台-添加共创素材数据",
     *     description="后台-添加共创素材数据",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialSaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSave()
    {
        $file = $_FILES['file'] ?? "";
        //用户卡号或者电话号码
        $topic = \Yii::$app->request->post('topic','');
        if(empty($topic))   CUtil::json_response(-1, "主题必选");
        if(empty($file))  CUtil::json_response(-1, "图片不存在");
        //上传图片
        list($status,$data) = CoCreateMaterialService::getInstance()->Save($topic,$file,$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1,'ok',$data);
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-material/del",
     *     summary="后台-共创空间素材删除",
     *     description="后台-共创空间素材删除",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialDelRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionDel()
    {
        $post = \Yii::$app->request->post();
        $ids  = $post['ids'] ?? '';
        if (empty($ids)) CUtil::json_response(-1, 'ids 不能为空');
        $return = CoCreateMaterialService::getInstance()->Del($ids);
        $return == false && CUtil::json_response(-1, '删除失败');
        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-material/edit",
     *     summary="后台-共创空间素材编辑",
     *     description="后台-共创空间素材编辑",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialEditRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionEdit()
    {
        $post = \Yii::$app->request->post();
        list($s,$msg) = CoCreateMaterialService::getInstance()->Edit($post);
        if(!$s)  CUtil::json_response(-1, $msg);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-material/topic-list",
     *     summary="后台-获取共创素材主题列表",
     *     description="后台-获取共创素材主题列表",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionTopicList()
    {
        $list =  byNew::CoCreateMaterialModel()::TOPIC_LIST;
        CUtil::json_response(1, 'ok', $list);
    }

}
