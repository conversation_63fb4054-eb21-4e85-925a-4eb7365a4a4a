<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\asset\models\PointLogModel;
use app\modules\rbac\controllers\CommController;
use Yii;
use yii\db\Exception;


class PointController extends CommController
{
    /**
     * 后台积分列表
     */
    public function actionList()
    {
        $post      = Yii::$app->request->post();
        $user_id   = intval($post['user_id'] ?? 0);
        $page      = intval($post['page'] ?? 1);
        $page_size = intval($post['page_size'] ?? by::pointLog()::PAGE_SIZE);

        $data = by::pointLog()->getScoreLogByUserId($user_id, $page, $page_size);

        $return['count'] = $data['count'] ?? 0;
        $return['pages'] = CUtil::getPaginationPages($return['count'], $page_size);
        $return['list']  = $data['list'] ?? [];

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * 积分比例展示
     */
    public function actionRule(){
        $return = by::point()->rule();
        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * 导出
     */
    public function actionExport(){
        $year       = $post['year']         ??  date('Y',time());
        $user_id    = $post['user_id']      ??  0;
        $phone      = $post['phone']        ??  0;
        $type       = $post['type']         ?? -1;
        $ctime_start= $post['ctime_start']  ??  0;
        $ctime_end  = $post['ctime_end']    ??  0;

        by::model('PointDetailModel', 'asset')->export($year,$user_id,$phone,$type,$ctime_start,$ctime_end);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/point/update-config",
     *     summary="积分-更新配置",
     *     description="积分-更新配置",
     *     tags={"积分"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"reward_rate", "deduction_rate", "employee_reward_rate"},
     *                         @OA\Property(property="reward_rate", type="integer", description="奖励比例系数（实付金额 × 系数 ÷ 100 = 获得积分）"),
     *                         @OA\Property(property="employee_reward_rate", type="integer", description="微笑大使奖励比例系数（实付金额 × 系数 ÷ 100 = 获得积分）"),
     *                         @OA\Property(property="deduction_rate", type="integer", description="抵扣比例系数（1积分 = 1 ÷ 系数 元现金，默认10表示10积分=1元）"),
     *                         @OA\Property(property="exchange_gold_rate", type="integer", description="金币兑换积分比例系数（10000金币兑换1积分 此参数填10000）"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUpdateConfig()
    {
        $post = Yii::$app->request->post();
        $data = [
                'reward_rate'            => intval($post['reward_rate'] ?? 1),
                'deduction_rate'         => intval($post['deduction_rate'] ?? 10),
                'employee_reward_rate'   => intval($post['employee_reward_rate'] ?? 2),
                'exchange_gold_rate'     => intval($post['exchange_gold_rate'] ?? 1000),
                'gold_conversion'        => intval($post['gold_conversion'] ?? 10000),
                'status'                 => 1, // 永远开启此规则
        ];

        // 验证数据 正整数
        if ($data['reward_rate'] <= 0 || $data['deduction_rate'] <= 0 || $data['employee_reward_rate'] <= 0 || $data['exchange_gold_rate'] <= 0 || $data['gold_conversion'] <= 0) {
            CUtil::json_response(0, '奖励比例和抵扣比例必须为正整数');
        }

        try {
            byNew::PointConfigModel()->updateConfig($data);
            byNew::PointConfigModel()->_delConfigCache();
            CUtil::json_response(1, '更新成功');
        } catch (Exception $e) {
            CUtil::json_response(0, '更新失败: ' . $e->getMessage());
        }
    }
}