<?php


namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;

class MarketDefineController extends CommController
{
    /**
     * 商品自定义价格列表
     * @throws \yii\db\Exception
     */
    public function actionList()
    {
        $post       = \Yii::$app->request->post();
        $name       = $post['name'] ?? '';//名称
        $sort       = $post['sort'] ?? 0; //排序
        $marketId   = $post['market_id'] ?? 0;//优惠券ID
        $defineType = $post['define_type'] ?? 0;//优惠券ID
        $page       = $post['page'] ?? 1;
        $page_size  = $post['page_size'] ?? 20;

        $input = [
            'name'        => $name,
            'sort'        => $sort,
            'market_id'   => $marketId,
            'define_type' => $defineType,
        ];

        $return          = [];
        $return['list']  = [];
        $return['pages'] = 0;
        //先查询数量
        $count = by::marketDefine()->GetListCount($input);
        if ($count > 0) {
            $ids = by::marketDefine()->GetList($input,
                $page, $page_size
            );
            foreach ($ids as $id) {
                $aMarketDefine        = by::marketDefine()->GetOneById($id);
                $return['list'][] = $aMarketDefine;
            }
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @throws \yii\db\Exception
     * 自定义价格更改
     */
    public function actionSave() {
        $post        = \Yii::$app->request->post();
        list($status,$ret) = by::marketDefine()->SaveDefine($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }
        $id    = $post['id']   ?? 0;
        $msg   = $id ? "修改了优惠券：{$id}" : "新增了优惠券：{$ret}";
        $msg  .= ",内容：".json_encode($post,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_DEFINE, $this->user_info['id']);
        CUtil::json_response(1,'OK');
    }

    /**
     * @throws \yii\db\Exception
     * 删除商品
     */
    public function actionDel()
    {
        $id        = \Yii::$app->request->post('id', '');

        list($s, $m) = by::marketDefine()->Del($id);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("删除自定义优惠券：{$id}；", RbacInfoModel::MARKET_DEFINE);
        CUtil::json_response(1, 'ok', []);
    }

}
