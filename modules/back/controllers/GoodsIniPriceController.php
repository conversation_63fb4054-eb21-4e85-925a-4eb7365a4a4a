<?php


namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;

class GoodsIniPriceController extends CommController
{
    /**
     * 商品自定义价格列表
     * @throws \yii\db\Exception
     */
    public function actionList()
    {
        $post      = \Yii::$app->request->post();
        $status    = $post['status'] ?? -1; //状态
        $sku       = $post['sku'] ?? 0; //商品SKU
        $tab       = $post['tab'] ?? 0;
        $tag       = $post['tag'] ?? "";
        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $input = [
            'status' => $status,
            'tab'    => $tab,
            'tag'    => $tag,
            'sku'    => $sku,
        ];

        $return          = [];
        $return['list']  = [];
        $return['pages'] = 0;
        //先查询数量
        $count = by::Gini()->GetListCount($input);
        if ($count > 0) {
            $ids = by::Gini()->GetList($input,
                $page, $page_size
            );
            foreach ($ids as $id) {
                $aGoodsIni        = by::Gini()->GetOneById($id);
                $return['list'][] = $aGoodsIni;
            }
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @throws \yii\db\Exception
     * 锁定所有后续未起效的自定义列表
     */
    public function actionLock() {
        $id = \Yii::$app->request->post("id", 0);
        if(empty($id)){
            CUtil::json_response(-1, '自定义字段ID不能为空！', []);
        }
        list($status,$aData) = by::Gini()->LockIniPriceList($id);
        if(!$status){
            CUtil::json_response(-1, $aData);
        }
        (new SystemLogsModel())->record("锁定自定义价格：{$id}；", RbacInfoModel::GOODS_INI_PRICE);
        CUtil::json_response(1,"OK",$aData);
    }

    /**
     * @throws \yii\db\Exception
     * 强制中断
     */
    public function actionForceBreak()
    {
        $id = \Yii::$app->request->post("id", 0);
        if(empty($id)){
            CUtil::json_response(-1, '自定义字段ID不能为空！', []);
        }
        list($status,$aData) = by::Gini()->forceBreakDown($id);
        if(!$status){
            CUtil::json_response(-1, $aData);
        }
        (new SystemLogsModel())->record("强制中断自定义价格ID：{$id}；", RbacInfoModel::GOODS_INI_PRICE);
        CUtil::json_response(1,"OK",$aData);
    }


    /**
     * @throws \yii\db\Exception
     * 自定义价格更改
     */
    public function actionSave() {
        $post        = \Yii::$app->request->post();
        list($status,$ret) = by::Gini()->SaveIni($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }
        $id    = $post['id']   ?? 0;
        $msg   = $id ? "修改了商品自定义价格：{$id}" : "新增了自定义价格：{$ret}";
        $msg  .= ",内容：".json_encode($post,JSON_UNESCAPED_UNICODE);
        $msg   = CUtil::filterEmoji($msg);
        (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_INI_PRICE, $this->user_info['id']);
        CUtil::json_response(1,'OK');
    }

    /**
     * @throws \yii\db\Exception
     * 删除商品
     */
    public function actionDel()
    {
        $id        = \Yii::$app->request->post('id', '');

        list($s, $m) = by::Gini()->Del($id);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("删除自定义价格：{$id}；", RbacInfoModel::GOODS_INI_PRICE);
        CUtil::json_response(1, 'ok', []);
    }


}
