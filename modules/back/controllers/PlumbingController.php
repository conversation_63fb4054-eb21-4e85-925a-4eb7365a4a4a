<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 11:47
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\plumbing\OrderSearchForm;
use app\modules\back\services\PlumbingService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;

/**
 * Class PlumbingController
 * @package app\modules\back\controllers
 * 上下水服务控制器
 */
class PlumbingController extends CommController
{

    /**
     * @param $list
     * @return mixed
     */
    public function _hideSensitive($list){
        if(empty($list)) return $list;
        if($this->viewSensitive) return $list;
        $result = array_filter($list,"is_array");
        $result = array_map("is_array",$result);
        if(!(bool)array_sum($result)){
            foreach ($list as &$li){
                $li = self::_hideItemSensitive($li);
            }
        }else{
            $list = self::_hideItemSensitive($list);
        }
        return $list;
    }

    /**
     * @param $li
     * @return array|mixed
     */
    protected static function _hideItemSensitive($li){
        $arr = [
            'cid'    => 'tm',
            'city'   => 'tm',
            'aid'    => 'tm',
            'area'   => 'tm',
            'detail' => 'tm',
            'phone'  => 'tm',
        ];
        $li = Response::responseList($li,$arr);
        if(isset($li['address'])) $li['address'] = Response::responseList($li['address'],$arr);
        if(isset($li['user'])) $li['user'] = Response::responseList($li['user'],$arr);
        return $li;
    }

    /**
     * @throws Exception
     * 勘测订单列表
     */
    public function actionOrderList()
    {
        $data           = Yii::$app->request->post();
        $id             = $data['id']               ?? 0;
        $order_no       = $data['order_no']         ?? '';
        $user_msg       = $data['user_msg']         ?? '';
        $status         = $data['status']           ?? -1;
        $s_time         = $data['s_time']           ?? 0;
        $e_time         = $data['e_time']           ?? 0;
        $refund_status  = $data['refund_status']    ?? '';
        $is_export      = $data['is_export']        ?? -1;
        $phones         = $data['phones']           ?? '';
        $page           = $data['page']             ?? 1;
        $plumbing_order = by::plumbingOrder();
        $page_size      = $data['page_size']        ?? $plumbing_order::PAGE_SIZE;
        $source         = $plumbing_order::SOURCE['ADMIN'];
        $source_page    = $plumbing_order::SOURCE_PAGE['ALL_ORDER_PAGE'];
        $type           = $plumbing_order::TYPE['EXPLORE'];

        $list           = [];
        $list['list']   = $plumbing_order->getList($source, $type, $source_page, $page, $page_size, $order_no, $user_msg, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id);
        $count          = $plumbing_order->getCount($type, $order_no, $user_msg, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id);
        $list['count']  = $count;
        $list['pages']  = CUtil::getPaginationPages($count, $page_size);

        $phone_arr = explode(',', $phones);
        $no_phones = [];
        foreach ($phone_arr as $phone) {
            $ids = $plumbing_order->getIdsByPhone($phone, $type);
            if (empty($ids)) {
                $no_phones[] = CUtil::uint($phone);
            }
        }

        $list['no_phones'] = array_values(array_filter($no_phones));
        $list['list'] = $this->_hideSensitive($list['list']);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 安装订单列表
     */
    public function actionInstallOrder()
    {
        $data           = Yii::$app->request->post();
        $id             = $data['id']               ?? 0;
        $order_no       = $data['order_no']         ?? '';
        $user_msg       = $data['user_msg']         ?? '';
        $status         = $data['status']           ?? -1;
        $s_time         = $data['s_time']           ?? 0;
        $e_time         = $data['e_time']           ?? 0;
        $refund_status  = $data['refund_status']    ?? -1;
        $is_export      = $data['is_export']        ?? 0;
        $phones         = $data['phones']           ?? '';
        $page           = $data['page']             ?? 1;
        $plumbing_order = by::plumbingOrder();
        $page_size      = $data['page_size']        ?? $plumbing_order::PAGE_SIZE;
        $source         = $plumbing_order::SOURCE['ADMIN'];
        $source_page    = $plumbing_order::SOURCE_PAGE['ALL_ORDER_PAGE'];
        $type           = $plumbing_order::TYPE['INSTALL'];

        $list           = [];
        $list['list']   = $plumbing_order->getList($source, $type, $source_page, $page, $page_size, $order_no, $user_msg, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id);
        $count          = $plumbing_order->getCount($type, $order_no, $user_msg, $status, $s_time, $e_time, $refund_status, $is_export, $phones, $id);
        $list['count']  = $count;
        $list['pages']  = CUtil::getPaginationPages($count, $page_size);

        $phone_arr = explode(',', $phones);
        $no_phones = [];
        foreach ($phone_arr as $phone) {
            $ids = $plumbing_order->getIdsByPhone($phone, $type);
            if (empty($ids)) {
                $no_phones[] = CUtil::uint($phone);
            }
        }

        $list['no_phones'] = array_values(array_filter($no_phones));
        $list['list'] = $this->_hideSensitive($list['list']);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 服务订单列表
     */
    public function actionServiceOrder()
    {
        // 请求参数
        $params = Yii::$app->request->post();

        // 初始化参数
        $plumbingOrder = by::plumbingOrder();
        $params['type'] = $plumbingOrder::TYPE['SERVICE'];
        $params['source'] = $plumbingOrder::SOURCE['ADMIN'];
        $params['source_page'] = $plumbingOrder::SOURCE_PAGE['ALL_ORDER_PAGE'];
        $params['page_size'] = $params['page_size'] ?? $plumbingOrder::PAGE_SIZE;

        // 查询条件
        $form = new OrderSearchForm();
        $form->load($params, '');

        // 验证条件
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        // 获取订单列表
        $res = PlumbingService::getInstance()->getOrderList($form);

        $res['list'] = $this->_hideSensitive($res['list']);
        CUtil::json_response(1, "OK", $res);
    }

    /**
     * 上门服务的产品类型，如扫地机、洗地机
     */
    public function actionProductType()
    {
        $model = by::plumbingOrder();
        // 产品类型（上门服务的产品类型）
        $data = [];
        $types = $model::PRODUCT_TYPE;
        foreach ($types as $type) {
            $data[] = [
                'id'   => $type,
                'name' => $model::PRODUCT_TYPE_NAME[$type],
            ];
        }
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @throws Exception
     * 订单唯一数据
     */
    public function actionOrderInfo()
    {
        $order_no = Yii::$app->request->post("order_no",'');

        $info     = by::plumbingOrder()->getInfoByOrderNo($order_no, true);

        $info = $this->_hideSensitive($info);
        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 修改字段
     */
    public function actionUpdateFields()
    {
        $ids     = Yii::$app->request->post('ids', '');
        $field   = Yii::$app->request->post('field', '');
        $val     = Yii::$app->request->post('val', -1);
        $ids_arr = !empty($ids) ? explode(',', $ids) : [];

        list($status, $ret) = by::plumbingOrder()->updateFields($ids_arr, $field, $val);
        if(!$status){
            CUtil::json_response(-1, $ret);
        }

        $msg = "修改工单ID：{$ids}，修改字段：{$ret['field_name']}，修改之前值：{$ret['b_val_name']}，修改之后值：{$ret['val_name']}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }
    /**
     * @throws Exception
     * 退款订单列表
     */
    public function actionRefundList()
    {
        $data          = Yii::$app->request->post();
        $order_no      = $data['order_no']         ?? '';
        $user_msg      = $data['user_msg']         ?? '';
        $type          = $data['type']             ?? 0;
        $status        = $data['status']           ?? 0;
        $s_time        = $data['s_time']           ?? 0;
        $e_time        = $data['e_time']           ?? 0;
        $page          = $data['page']             ?? 1;
        $page_size     = by::plumbingRefund()::PAGE_SIZE;
        $source        = by::plumbingOrder()::SOURCE['ADMIN'];

        $list          = [];
        $list['list']  = by::plumbingRefund()->getList($source, $page, $page_size, $order_no, $user_msg, $type, $status, $s_time, $e_time);
        $count         = by::plumbingRefund()->getCount($order_no, $user_msg, $type, $status, $s_time, $e_time);
        $list['count'] = $count;
        $list['pages'] = CUtil::getPaginationPages($count, $page_size);

        $list['list'] = $this->_hideSensitive($list['list']);
        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 退款订单唯一数据
     */
    public function actionRefundInfo()
    {
        $refund_no = Yii::$app->request->post("refund_no", '');

        $info      = by::plumbingRefund()->getInfoByRefundNo($refund_no);

        $info = $this->_hideSensitive($info);
        CUtil::json_response(1, "OK", $info);
    }

    /**
     * @throws Exception
     * 退款审核
     */
    public function actionRefundAudit()
    {
        $data      = Yii::$app->request->post();
        $refund_no = $data['refund_no'] ?? '';
        $status    = $data['status']    ?? '';

        list($s, $res) = by::plumbingRefund()->audit($data);
        if(!$s) {
            CUtil::json_response(-1, $res);
        }

        $status_name = by::plumbingOrder()::REFUND_STATUS_NAME[$status];
        $msg         = "退款工单：{$refund_no}，修改状态为：{$status_name}，退款金额：{$res}元";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * 价格配置
     */
    public function actionPriceConfig()
    {
        $info = by::plumbingPrice()->getPriceConfig();

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 保存价格配置
     */
    public function actionPriceSave()
    {
        $data = Yii::$app->request->post();

        list($status, $res) = by::plumbingPrice()->priceSave($data);
        if(!$status) {
            CUtil::json_response(-1, $res);
        }

        //写入日志
        $id   = $data['id'] ?? 0;
        $msg  = $id ? "修改了上下水服务价格配置：{$id}" : "新增了上下水服务价格配置";
        $msg .= ",内容：".json_encode($data,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }

    /**
     * @throws Exception
     * sn配置列表
     */
    public function actionSnConfig()
    {
        $type = Yii::$app->request->post('type', 0);
        // 产品类型
        $product_type = Yii::$app->request->post('product_type', 0);

        $list = by::plumbingSn()->getConfig($type, '', $product_type);

        CUtil::json_response(1,"OK", $list);
    }

    /**
     * @throws Exception
     * 保存sn编码配置
     */
    public function actionSnSave()
    {
        $data = Yii::$app->request->post();

        list($status, $res) = by::plumbingSn()->snSave($data);
        if(!$status) {
            CUtil::json_response(-1, $res);
        }

        //写入日志
        $msg = "新增了上下水服务sn编码配置,内容：".json_encode($data,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }

    /**
     * @throws Exception
     * @throws StaleObjectException
     * 编辑sn编码配置
     */
    public function actionSnEdit()
    {
        $type   = Yii::$app->request->post('type', 0);
        $product_type   = Yii::$app->request->post('product_type', 0);
        $id     = Yii::$app->request->post('id', 0);
        $p_id   = Yii::$app->request->post('p_id', 0);
        $alias  = Yii::$app->request->post('alias', '');
        $images = Yii::$app->request->post('images', '');

        list($status, $ret) = by::plumbingSn()->edit($type, $product_type, $id, $p_id, $alias, $images);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $type_name = by::plumbingOrder()::TYPE_NAME[$type];
        $msg       = $id ? '修改' : '新增';
        $msg      .= "上下水服务sn编码配置：,内容：类型={$type_name},产品id={$p_id},产品名称={$alias},图片={$images}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * @throws Exception
     * 删除sn编码配置
     */
    public function actionSnDel(){
        $id = Yii::$app->request->post('id', 0);

        list($status, $ret)  = by::plumbingSn()->del($id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $msg = "删除上下水服务sn编码配置,id为:{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * @throws Exception
     * 产品列表
     */
    public function actionProductList()
    {
        $list = by::product()->getProductList();

        CUtil::json_response(1,"OK", $list);
    }

    /**
     * @throws Exception
     * 评价列表
     */
    public function actionCommentList()
    {
        $data             = Yii::$app->request->post();
        $order_no         = $data['order_no']  ?? '';
        $user_msg         = $data['user_msg']  ?? '';
        $type             = $data['type']      ?? 0;
        $page             = $data['page']      ?? 1;
        $plumbing_comment = by::plumbingComment();
        $page_size        = $plumbing_comment::PAGE_SIZE;

        $list           = [];
        $count          = $plumbing_comment->getCount($order_no, $user_msg, $type);
        $list['count']  = $count;
        $list['pages']  = CUtil::getPaginationPages($count, $page_size);
        $list['list']   = $plumbing_comment->getList($page, $page_size, $order_no, $user_msg, $type);

        $list['list'] = $this->_hideSensitive($list['list']);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * @throws Exception
     * 评价唯一数据
     */
    public function actionCommentInfo()
    {
        $order_no = Yii::$app->request->post("order_no",'');

        $info     = by::plumbingComment()->getInfoByOrderNo($order_no, true);

        $info = $this->_hideSensitive($info);

        CUtil::json_response(1,"OK", $info);
    }

    /**
     * @throws Exception
     * 标签配置列表
     */
    public function actionTagConfig()
    {
        $list = by::CommentTag()->getConfig();

        CUtil::json_response(1,"OK", $list);
    }

    /**
     * @throws Exception
     * @throws StaleObjectException
     * 编辑标签配置
     */
    public function actionTagEdit()
    {
        $id   = Yii::$app->request->post('id', 0);
        $name = Yii::$app->request->post('name', '');

        list($status, $ret) = by::CommentTag()->edit($id, $name);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $msg  = $id ? '修改' : '新增';
        $msg .= "上下水评价标签配置：,内容：标签名称={$name}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * @throws Exception
     * 删除标签配置
     */
    public function actionTagDel(){
        $id = Yii::$app->request->post('id', 0);

        list($status, $ret)  = by::CommentTag()->del($id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $msg = "删除上下水服务标签配置,id为:{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * @throws Exception
     * 勘探服务地区搜索记录列表
     */
    public function actionSearchList()
    {
        $data      = Yii::$app->request->post();
        $cname     = $data['cname'] ?? '';
        $page      = $data['page'] ?? 1;
        $page_size = by::exploreSearch()::PAGE_SIZE;

        $list          = [];
        $list['list']  = by::exploreSearch()->getList(2, $page, $page_size, $cname);
        $count         = by::exploreSearch()->getCount($cname);
        $list['count'] = $count;
        $list['pages'] = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $list);
    }

    /**
     * 地址管理
     */
    public function actionAddress()
    {
        $of_model = by::model('OfreightModel', 'goods');
        $list     = $of_model->GetList($of_model::TYPE['EXPLORE']);

        CUtil::json_response(1, "OK", $list);
    }

    /**
     * @throws Exception
     * 勘探服务地区增改
     */
    public function actionAddressSave()
    {
        $post         = Yii::$app->request->post();
        $of_model     = by::model('OfreightModel', 'goods');
        $post['type'] = $of_model::TYPE['EXPLORE'];

        list($status,$ret) = $of_model->SaveLog($post);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        $id   = $post['id']   ?? 0;
        $msg  = $id ? "修改了勘探服务地区配置ID：{$id}" : "新增了勘探服务地区配置";
        $msg .= ",内容：".json_encode($post,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }

    /**
     * @throws Exception
     * 勘探服务地区配置删除
     */
    public function actionAddressDel()
    {
        $id = Yii::$app->request->post('id', 0);

        list($status, $ret) = by::model('OfreightModel', 'goods')->Del($id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        $msg = "删除勘探服务地区配置ID：{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SUBSCRIBE_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }
}
