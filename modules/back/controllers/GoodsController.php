<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\models\Request;
use app\models\Response;
use app\modules\back\forms\goods\GetGoodsListForm;
use app\modules\back\services\GmainService;
use app\modules\back\services\GoodsService;
use app\modules\goods\services\StatisticService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\wares\services\goods\GoodsMainService;
use RedisException;
use yii\db\Exception;

class GoodsController extends CommController
{
    public function actionTagList()
    {
        $post = \Yii::$app->request->post();
        // 1=普通商品标签 ，2=优选商品标签，3=严选商品标签
        $type = $post['type'] ?? 1;
        $list = by::Gtag()->getTagList(0);
        CUtil::json_response(1,"OK",$list);

    }

    /**
     * 商品详情
     * @return void
     */
    public function actionInfo()
    {
        $id = \Yii::$app->request->post('id', 0);
        if (empty($id)) {
            CUtil::json_response(-1, '商品id不能为空');
        }

        try {
            $data = GoodsService::getInstance()->getGoodsDetail($id);
            CUtil::json_response(1, 'ok', $data);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '商品详情查询失败');
        }
    }

    /**
     * 商品列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $post = \Yii::$app->request->post();
        // 加载数据
        $form = new GetGoodsListForm();
        $form->load($post, '');
        // 校验参数
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, implode('', $errors));
        }
        // 请求参数
        $params = $form->toArray();

        // 查询数据
        try {
            $data = GoodsService::getInstance()->getGoodsList($params);
            CUtil::json_response(1, 'ok', $data);
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug("$error", 'err.back_goods_list');
            CUtil::json_response(-1, '商品列表查询失败');
        }
    }

    /**
     * @return void
     * @throws Exception
     * @throws \RedisException
     * 查看预售信息
     */
    public function actionPresale()
    {
        $gid                    = \Yii::$app->request->post('id', 0);
        $aGoods                 = by::Gmain()->GetAllOneByGid($gid);
        $statisticService = new StatisticService();
        $statisticData = $statisticService->statisticGoods($gid);
        $info = [
            'd_status'        => $aGoods['d_status'] ?? 0,
            'deposit_num'     => $statisticData['deposit_paid_num'] ?? 0,
            'tail_num'        => $statisticData['tail_paid_num'] ?? 0,
            'cancel_tail_num' => $statisticData['tail_refund_num'] ?? 0,
            'start_payment'   => $aGoods['start_payment'] ?? 0,
            'end_payment'     => $aGoods['end_payment'] ?? 0,
            'presale_time'    => $aGoods['presale_time'] ?? 0,

        ];
        $info = Response::responseList($info, ['deposit_num' => 'int', 'tail_num' => 'int', 'stock' => 'int', 'sales' => 'int']);
        CUtil::json_response(1,'ok', $info);
    }

    /**
     * @throws \yii\db\Exception
     * 商品增改
     */
    public function actionSave()
    {
        // 增加频率限制
        // 生成唯一的请求标识并检查请求频率
        $unique_key = CUtil::getAllParams(__FUNCTION__, $this->user_info['id']);
        list($isAllowed, $message, $code) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_info['id'], $unique_key, 5, "EX", 1);
        if (!$isAllowed) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        $post = \Yii::$app->request->post();

        $post = Request::requestList($post, [
                'presale_time'  => 'del_ms',
                'start_payment' => 'del_ms',
                'end_payment'   => 'del_ms',
        ]);

        list($status, $ret) = by::Gmain()->SaveLog($post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $id  = $post['id'] ?? 0;
        $msg = $id ? "修改了商品ID：{$id}" : "新增了商品ID：{$ret}";
        $msg .= ",内容：" . json_encode($post, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }

    /**
     * 判断sku是否存在
     */
    public function actionIsSku() {
        $post       = \Yii::$app->request->post();
        $ck_code    = $post['ck_code']  ?? 0;
        $skus       = $post['skus']    ?? '';

        list($s, $m) = by::Gmain()->IsSku($ck_code, $skus);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok', $m);
    }

    /**
     * 修改排序
     */
    public function actionSort()
    {
        $post      = \Yii::$app->request->post();
        $id        = $post['id'] ?? 0;
        $sortValue = $post['sort'] ?? 0;
        // 指定要更新的排序字段，默认为sort
        $sortField = in_array($post['field'] ?? '', ['sort', 'member_mall_sort'])
                ? $post['field']
                : 'sort';

        // 确保排序值为非负整数
        $sortValue = CUtil::uint($sortValue);

        // 更新指定的排序字段
        list($status, $ret) = by::Gmain()->UpdateData($id, [$sortField => $sortValue]);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, '更新成功');
    }

    /**
     * @throws \yii\db\Exception
     * 商品状态批量更新
     */
    public function actionBatch()
    {
        $ids        = \Yii::$app->request->post('ids', '');
        $act        = \Yii::$app->request->post('act', 0);

        list($s, $m) = by::Gmain()->batchPut($ids, $act);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("批量修改普通商品：{$ids}；操作码：{$act}；", RbacInfoModel::GOODS_MANAGER);
        CUtil::json_response(1, 'ok', ['num' => $m]);
    }

    /**
     * @throws \yii\db\Exception
     * 删除商品
     */
    public function actionDel()
    {
        $id        = \Yii::$app->request->post('id', '');

        list($s, $m) = by::Gmain()->Del($id);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("删除普通商品：{$id}；", RbacInfoModel::GOODS_MANAGER);
        CUtil::json_response(1, 'ok', ['num' => $m]);
    }

    /**
     * @throws \yii\db\Exception
     * 同步库存
     */
    public function actionSyncStock()
    {
        $id        = \Yii::$app->request->post('id', '');

        list($s, $m) = by::Gstock()->SyncStock($id);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 配件列表
     */
    public function actionPartList()
    {
        $partData = by::partsSalesModel()->getInfo(by::partsSalesModel()::PART_TYPE);
        CUtil::json_response(1, 'ok', $partData);
    }

    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 主机列表
     */
    public function actionMainList()
    {
        $hostData = by::partsSalesModel()->getInfo(by::partsSalesModel()::HOST_TYPE);
        CUtil::json_response(1, 'ok', $hostData);
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 对比列表
     */
    public function actionCompareList()
    {
        $cateId = \Yii::$app->request->post('cate_id', '');
        if (empty($cateId)) {
            CUtil::json_response(-1, '类目id不能为空');
        }
        $res = GmainService::getInstance()->GetCompareList($cateId);
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * @OA\Post(
     *     path="/back/goods/sku-list-by-gid",
     *     summary="根据商品ID获取SKU列表",
     *     description="根据商品ID获取SKU列表",
     *     tags={"商品"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gid"},
     *                         @OA\Property(property="gid", type="integer", description="商品Id")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="返回数据",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="string", description="ID"),
     *                     @OA\Property(property="sku", type="string", description="sku")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionSkuListByGid(){
        $gid = \Yii::$app->request->post('gid', '');
        if (empty($gid)) {
            CUtil::json_response(-1, '商品id不能为空');
        }
        if (!is_numeric($gid)){
            CUtil::json_response(-1, '请填写正确的商品id');
        }
        // 直接获取商品详情，从详情里取sku列表，减少代码影响，暂不考虑性能问题
        $data = GoodsService::getInstance()->getGoodsDetail($gid);
        if (empty($data)) {
            CUtil::json_response(-1, '商品不存在');
        }
        $res = [];
        // 判断商品是否为多规格
        if ($data['atype'] == 0){
            $res[] = ['id'=>'0','sku'=>$data['sku']];
        }elseif ($data['atype'] == 1){
            if (count($data['specs']) > 0){
                foreach ($data['specs'] as $item) {
                    $res[] = ['id'=>(string)$item['id'],'sku'=>$item['sku']];
                }
            }
        }
        CUtil::json_response(1, 'ok', $res);
    }
}
