<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\main\services\CateService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use yii\db\Exception;


class CateController extends CommController
{
    /**
     * @return void
     * @throws RedisException
     * @throws Exception
     * 获取类目信息
     */
    public function actionList()
    {
        $type = \Yii::$app->request->post('type') ?? 0;

        list($status, $aData) = CateService::getInstance()->getList($type);
        if ($status) {
            CUtil::json_response(1, '获取成功', $aData);
        } else {
            CUtil::json_response(-1, '获取失败', []);
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function actionModify()
    {
        $post = \Yii::$app->request->post();
        $id = $post['id'] ?? 0;
        $user_info = $this->user_info;

        list($status, $ret) = by::cateModel()->modify($id, $post,$user_info['id'] ?? 0);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $id = $post['id'] ?? 0;
        $msg = $id ? "修改了类目ID：{$id}" : "新增了类目ID：{$ret}";
        $msg .= ",内容：" . json_encode($post, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_CATE, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 删除类目
     */
    public function actionDelete()
    {
        $post = \Yii::$app->request->post();
        $id = $post['id'] ?? '';

        list($status,$ret, $data) = by::cateModel()->del($id);

        if ($status) {
            $msg                = "删除类目ID：{$id}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_CATE);
            CUtil::json_response(1, $ret,$data);
        } else {
            CUtil::json_response(-1, $ret,$data);
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 修改类目删除时类目下有商品的
     */
    public function actionUpdate()
    {
        $post = \Yii::$app->request->post();
        $c_id = $post['c_id'] ?? '';
        $u_id = $post['u_id'] ?? '';
        list($status,$ret) = by::gCateModel()->updateByCid($c_id,$u_id);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        $msg                = "将类目ID:{$c_id}的商品修改为类目ID:{$u_id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_CATE);
        CUtil::json_response(1, '保存成功');
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 拖拽排序
     */
    public function actionCateDrag()
    {
        $post = \Yii::$app->request->post();
        list($status,$ret) = by::cateModel()->drag($post);
        if (!$status){
            CUtil::json_response(-1,$ret);
        }
        CUtil::json_response(1,$ret);

    }


    /**
     * @throws Exception
     * @throws RedisException
     * 获取类目详情+商品信息
     */
    public function actionDetail()
    {
        $id =  \Yii::$app->request->post('id',0);
        $data = by::cateModel()->getOneInfoById($id);
        $info = by::gCateModel()->getCateInfoByCid($id);

        $goods = [];
        foreach ($info as $v){
            $goods[] = by::Gmain()->GetOneBySku($v['sku']);
        }
        $data['goods'] = $goods;
        CUtil::json_response(1,'ok',$data);
    }

}