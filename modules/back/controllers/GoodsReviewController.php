<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\back\forms\review\AuditReportForm;
use app\modules\back\forms\review\AuditReviewForm;
use app\modules\back\forms\review\GetReportListForm;
use app\modules\back\forms\review\GetReviewListForm;
use app\modules\back\services\GoodsReviewService;
use app\modules\rbac\controllers\CommController;

/**
 * 商品评价
 */
class GoodsReviewController extends CommController
{
    // 评价列表
    public function actionList()
    {
        // 获取参数
        $post = \Yii::$app->request->post();

        // 加载数据
        $form = new GetReviewListForm();
        $form->load($post, '');

        // 校验参数
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, implode('', $errors));
        }

        // 逻辑处理
        list($status, $res) = GoodsReviewService::getInstance()->getReviewList($form->toArray());

        if (!$status) {
            CUtil::json_response(-1, '获取评价列表失败');
        }

        CUtil::json_response(1, 'ok', $res);

    }

    // 评价列表
    public function actionDetail()
    {
        // 获取参数
        $post = \Yii::$app->request->post();

        // 评价id
        $review_id = $post['review_id'];
        // 举报ID
        $report_id = $post['report_id'] ?? 0;
        // 审核列表
        $show_audit_list = $post['show_audit_list'] ?? 0;
        // 举报列表
        $show_report_list = $post['show_report_list'] ?? 0;
        // 评价类型
        $type = $post['type'] ?? 1; // 1主评 2追评


        // 逻辑处理
        list($status, $res) = GoodsReviewService::getInstance()->getReviewDetail($review_id);

        if ($status) {
            // 获取审核列表
            if ($show_audit_list) {
                $audit_review_id = $review_id;
                // 主评还是追评
                if ($type != 1 && !empty($res['review']['append']['review_id'])) {
                    $audit_review_id = $res['review']['append']['review_id'];
                }
                $audit_list = GoodsReviewService::getInstance()->getAuditList($audit_review_id);
                $res['audit_list'] = $audit_list;
            }
            // 获取举报列表
            if ($show_report_list) { // 详情列表，默认展示10条已处理的举报
                // 已处理的举报列表
                $params = [
                    'review_id' => $review_id,
                    'status'    => [GoodsReviewService::REPORT_STATUS['PASS'], GoodsReviewService::REPORT_STATUS['REJECT'], GoodsReviewService::REPORT_STATUS['NOT_NEED_HANDLE']],
                    'page'      => 1,
                    'page_size' => 10,
                ];
                list($status, $report_list) = GoodsReviewService::getInstance()->getReportList($params);
                $res['report_list'] = $report_list['list'] ?? [];
            }
            if ($report_id) {
                $report = GoodsReviewService::getInstance()->getReportById($report_id);
                $res['report'][] = $report;
            }
            CUtil::json_response(1, 'ok', $res);
        }
        CUtil::json_response(-1, $res);
    }

    public function actionAudit()
    {
        // 获取参数
        $post = \Yii::$app->request->post();

        // 加载数据
        $form = new AuditReviewForm();
        $form->load($post, '');

        // 校验参数
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, implode('', $errors));
        }

        $params = $form->toArray();
        $params['account'] = $this->user_info['account'] ?? '';

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->auditReview($params);
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '审核失败');
        }
    }

    // 下架
    public function actionRemove()
    {
        // 获取参数
        $post = \Yii::$app->request->post();
        $review_id = $post['review_id'];
        $type = $post['type'] ?? 1; // 1主评 2追评

        if ($review_id <= 0) {
            CUtil::json_response(-1, '请选择要下架的评价');
        }

        // 逻辑处理
        try {
            // 选择下架主评时，同时下架追评
            if ($type == 1) {
                // 查询追评id
                $append_review = GoodsReviewService::getInstance()->getAppendDetailByMainReviewId($review_id);
                if ($append_review && !empty($append_review['review_id'])) {
                    $review_ids = [$review_id, $append_review['review_id']];
                } else {
                    $review_ids = [$review_id];
                }
            } else { // 追评直接删除当前的评价
                $review_ids = [$review_id];
            }

            list($status, $res) = GoodsReviewService::getInstance()->removeReview($review_ids);
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '下架失败');
        }
    }

    // 举报列表
    public function actionReportList()
    {
        // 获取参数
        $post = \Yii::$app->request->post();

        // 加载数据
        $form = new GetReportListForm();
        $form->load($post, '');

        // 校验参数
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, implode('', $errors));
        }

        // 逻辑处理
        list($status, $res) = GoodsReviewService::getInstance()->getReportList($form->toArray());

        if (!$status) {
            CUtil::json_response(-1, '获取举报列表失败');
        }

        CUtil::json_response(1, 'ok', $res);

    }

    // 审核举报
    public function actionAuditReport()
    {
        // 获取参数
        $post = \Yii::$app->request->post();

        // 加载数据
        $form = new AuditReportForm();
        $form->load($post, '');

        // 校验参数
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, implode('', $errors));
        }

        $params = $form->toArray();
        $params['account'] = $this->user_info['account'] ?? '';

        // 逻辑处理
        try {
            list($status, $res) = GoodsReviewService::getInstance()->auditReport($params);
            // 请求成功
            $status && CUtil::json_response(1, 'ok', $res);
            // 请求失败
            CUtil::json_response(-1, $res);
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            CUtil::json_response(-1, '审核失败');
        }

    }

    // 筛选项
    public function actionLabels()
    {
        $data = [];

        // 举报类型
        $data['report_type'] = GoodsReviewService::getInstance()->getReasonList();

        // 风险等级
        $report_level_list = GoodsReviewService::REPORT_LEVEL_NAME;
        foreach ($report_level_list as $k => $v) {
            $data['risk_level'][] = ['id' => $k, 'name' => $v];
        }

        CUtil::json_response(1, 'ok', $data);
    }
}