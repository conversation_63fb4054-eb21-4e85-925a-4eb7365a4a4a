<?php

namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\modules\back\forms\goodsCrontabStock\GoodsCrontabStockForm;
use app\modules\back\services\GoodsCrontabStockService;
use app\modules\back\services\MemberActivityService;
use app\modules\common\ControllerTrait;
use app\modules\goods\models\GoodsCrontabStockModel;
use app\modules\rbac\controllers\CommController;

class GoodsCrontabStockController extends CommController
{
    use ControllerTrait;

    /** @var GoodsCrontabStockService */
    private $service;
    public function __construct($id, $module, $config = [])
    {
        $this->service = GoodsCrontabStockService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * 列表
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getList($post));
    }

    /**
     * 新增
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new GoodsCrontabStockForm();
            $form->setScenario( 'create');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status,$id,  $msg) = $this->service->create($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success(['id' => (int) $id]);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 新增
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new GoodsCrontabStockForm();
            $form->setScenario( 'update');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $msg) = $this->service->update($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 删除
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                return $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);

            list($status, $msg) = $this->service->delete($ids);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 详情
     */
    public function actionDetail()
    {
        try {
            $post = \Yii::$app->request->post();
            $id = $post['id'] ?? 0;
            if (empty($id)) {
                return $this->error('缺少活动ID参数');
            }
            $this->success($this->service->getDetail($id));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}