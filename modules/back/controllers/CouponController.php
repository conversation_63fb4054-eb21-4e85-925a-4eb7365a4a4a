<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\back\forms\CouponForm;
use app\modules\back\services\CouponService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use Throwable;
use yii\db\Exception;
use yii\db\StaleObjectException;


class CouponController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/coupon/list",
     *     summary="获取卡券列表",
     *     description="获取符合条件的卡券列表信息",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="page", type="integer", description="当前页"),
     *                         @OA\Property(property="page_size", type="integer", description="每页条数"),
     *                         @OA\Property(property="name", type="string", description="卡券名称"),
     *                         @OA\Property(property="code", type="string", description="卡券编码"),
     *                         @OA\Property(property="create_begin_at", type="integer", description="创建时间开始"),
     *                         @OA\Property(property="create_end_at", type="integer", description="创建时间结束"),
     *                         @OA\Property(property="begin_validity", type="integer", description="有效时间开始"),
     *                         @OA\Property(property="end_validity", type="integer", description="有效时间结束")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="卡券列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="卡券ID"),
     *                         @OA\Property(property="name", type="string", description="卡券名称"),
     *                         @OA\Property(property="image", type="string", description="图片"),
     *                         @OA\Property(property="code", type="string", description="券码"),
     *                         @OA\Property(property="validity", type="string", description="有效期"),
     *                         @OA\Property(property="type", type="string", description="卡券类型"),
     *                         @OA\Property(property="stock_num", type="string", description="库存数量"),
     *                         @OA\Property(property="appid", type="string", description="appid"),
     *                         @OA\Property(property="app_path", type="string", description="app路径"),
     *                         @OA\Property(property="originid", type="string", description="原始ID"),
     *                         @OA\Property(property="origin_path", type="string", description="原始路径"),
     *                         @OA\Property(property="short_chain", type="string", description="短链"),
     *                         @OA\Property(property="create_at", type="string", description="创建时间"),
     *                         @OA\Property(property="update_at", type="string", description="更新时间")
     *                     )
     *                 ),
     *                 @OA\Property(property="total", type="string", description="总数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionList()
    {
        $post = CUtil::VdForm(new CouponForm(), 'list');

        $result= CouponService::getInstance()->getList($post->toArray());
        CUtil::Ret($result);
    }

    /**
     * @OA\Post(
     *     path="/back/coupon/del",
     *     summary="卡券删除",
     *     description="根据提供的卡券ID删除指定的卡券。",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="integer", description="卡券ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="请求成功，卡券已删除",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="操作信息"),
     *             @OA\Property(property="data", type="null", description="返回数据")
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionDel()
    {
        $post = CUtil::VdForm(new CouponForm(), 'delete');
        $id   = $post->id;
        list($status, $ret, $data) = CouponService::getInstance()->delete($id);

        if ($status) {
            $msg = "删除类目ID：{$id}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::GOODS_CATE);
            CUtil::json_response(1, $ret, $data);
        } else {
            CUtil::json_response(-1, $ret, $data);
        }
    }

    /**
     * @OA\Post(
     *     path="/back/coupon/save",
     *     summary="卡券新增/编辑",
     *     description="新增或编辑卡券信息，通过上传Excel文件来批量管理卡券。",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="multipart/form-data",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"name", "type"},
     *                         @OA\Property(property="id", type="integer", description="卡券ID"),
     *                         @OA\Property(property="file", type="string", format="binary", description="上传Excel文件"),
     *                         @OA\Property(property="name", type="string", description="卡券名称"),
     *                         @OA\Property(property="type", type="string", description="卡券类型，目前只有类型1可用")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="操作成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="返回状态码, 1为成功，-1为失败"),
     *             @OA\Property(property="sMsg", type="string", description="返回消息，保存结果描述"),
     *             @OA\Property(property="data", type="array", description="返回数据", @OA\Items(type="string"))
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="请求错误，例如参数缺失",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", example=-1),
     *             @OA\Property(property="sMsg", type="string", example="卡券名称不能为空。;卡券类型不能为空。;"),
     *             @OA\Property(property="data", type="array", @OA\Items(type="string"))
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionSave()
    {
        $post = CUtil::VdForm(new CouponForm(), 'save');
        $file = $_FILES['file'] ?? null;

        list($status, $ret) = CouponService::getInstance()->save($post->toArray(), $file);
        if ($status) {
            CUtil::json_response(1, '保存成功');
        } else {
            CUtil::json_response(-1, $ret, []);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/coupon/code-list",
     *     summary="获取券码列表",
     *     description="获取指定条件下的券码列表信息",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"code"},
     *                         @OA\Property(property="page", type="integer", description="当前页"),
     *                         @OA\Property(property="page_size", type="integer", description="每页条数"),
     *                         @OA\Property(property="code", type="string", description="卡券编码")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="请求成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="响应数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="券码列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="券码ID"),
     *                         @OA\Property(property="coupon_code", type="string", description="卡券编码"),
     *                         @OA\Property(property="ticket_code", type="string", description="券码"),
     *                         @OA\Property(property="status", type="string", description="是否已领取 1 领取 0 未领取"),
     *                         @OA\Property(property="receive_user_id", type="string", description="领取用户"),
     *                         @OA\Property(property="receive_at", type="string", description="领取时间"),
     *                         @OA\Property(property="order_no", type="string", description="对应订单号")
     *                     )
     *                 ),
     *                 @OA\Property(property="total", type="integer", description="总数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionCodeList()
    {
        $post = CUtil::VdForm(new CouponForm(), 'codeList');
        $result = CouponService::getInstance()->getCodeList($post->toArray());
        CUtil::Ret($result);
    }

    /**
     * @OA\Post(
     *     path="/back/coupon/send",
     *     summary="券码发放",
     *     description="向指定用户发放券码。",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                   required={"ticket_code", "user_id", "order_no"},
     *                   @OA\Property(property="ticket_code", type="string", description="卡券编码（多条以逗号隔开）"),
     *                   @OA\Property(property="user_id", type="integer", description="用户ID"),
     *                   @OA\Property(property="order_no", type="string", description="订单号")
     *                 )
     *             }
     *          )
     *        )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="结果数据",
     *                 @OA\Items(type="string")
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=400,
     *         description="请求错误",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="错误信息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="结果数据",
     *                 @OA\Items(type="string")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionSend()
    {
        $post    = CUtil::VdForm(new CouponForm(), 'send');
        $code   = $post->code;
        $user_id = $post->user_id;
        $order_no = $post->order_no;

        list($status,$msg) = CouponService::getInstance()->send($code, $user_id, $order_no);
        if ($status) {
            CUtil::json_response(1, $msg);
        } else {
            CUtil::json_response(-1, $msg);
        }

    }

    /**
     * @OA\Post(
     *     path="/back/coupon/invalid",
     *     summary="券码失效",
     *     description="将指定的券码标记为失效。",
     *     tags={"停车券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"ticket_code"},
     *                         @OA\Property(property="ticket_code", type="string", description="卡券编码（多条以逗号隔开）")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="请求成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="数据内容",
     *                 @OA\Items(type="string")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionInvalid()
    {
        $post = CUtil::VdForm(new CouponForm(), 'invalid');
        $ids = explode(',', $post->ids);

        list($status,$msg) = CouponService::getInstance()->invalid($ids);
        if ($status) {
            CUtil::json_response(1, '操作成功');
        } else {
            CUtil::json_response(-1, $msg);
        }
    }

}