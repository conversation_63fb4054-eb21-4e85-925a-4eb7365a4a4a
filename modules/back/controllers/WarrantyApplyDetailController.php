<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\log\services\warranty\WarrantyApplyDetailService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use Yii;
use yii\db\Exception;

class WarrantyApplyDetailController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/warranty-apply-detail/audit",
     *     summary="电子保修卡审核",
     *     description="电子保修卡审核",
     *     tags={"电子保修卡-申请"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyDetailAuditRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAudit()
    {
        $post         = Yii::$app->request->post();
        $apply_no = trim($post['apply_no'] ?? '');
        $apply_status = trim($post['apply_status'] ?? '');
        $post['admin_user_id'] = $this->user_info['id'] ?? 0;

        list($s, $msg) = WarrantyApplyDetailService::getInstance()->Audit($post);
        if (!$s) {
            CUtil::json_response(-1, $msg);
        }

        //写入日志
        $msg = ($apply_status == WarrantyApplyDetailService::getInstance()->status['PASS']['CODE']) ? '通过' : '拒绝';
        (new SystemLogsModel())->record("申请编号：{$apply_no}修改状态：{$msg}", RbacInfoModel::WARRANTY_APPLY);

        CUtil::json_response(1, 'OK');
    }

    /**
     * @OA\Post(
     *     path="/back/warranty-apply-detail/list",
     *     summary="待审核记录列表",
     *     description="待审核记录列表",
     *     tags={"电子保修卡-申请"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WarrantyApplyListResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post      = Yii::$app->request->post();
        $page      = CUtil::uint($post['page'] ?? 1);
        $page_size = CUtil::uint($post['page_size'] ?? 20);
        $list      = WarrantyApplyDetailService::getInstance()->GetWarrantyApplyDetailList($post, $page, $page_size);
        $count     = WarrantyApplyDetailService::getInstance()->GetListCount($post);
        CUtil::json_response(1, 'ok', ['list' => $list, 'count' => $count]);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-apply-detail/get-period-time",
     *     summary="获取保修卡时间",
     *     description="获取保修卡时间",
     *     tags={"电子保修卡-申请"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyApplyDetailTimeRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="integer",description="保修期时间")
     *         )
     *     )
     * )
     */
    public function actionGetPeriodTime()
    {
        $apply_no = trim(Yii::$app->request->post('apply_no',''));
        if(empty($apply_no))  CUtil::json_response(-1, '申请单号必填！');
        //获取时间
        $time = WarrantyApplyDetailService::getInstance()->GetPeriodTimeByApplyNo($apply_no);
        CUtil::json_response(1, $time);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-apply-detail/status-list",
     *     summary="待审核记录状态列表",
     *     description="待审核记录状态列表",
     *     tags={"电子保修卡-申请"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionStatusList()
    {
        $data = WarrantyApplyDetailService::getInstance()->status;
        unset($data['ING']);
        $result = array_map(function ($item) {
            return array_change_key_case($item, CASE_LOWER);
        }, array_values($data));
        CUtil::json_response(1, 'ok', $result);
    }


}
