<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\back\forms\employee\OptScoreForm;
use app\modules\back\forms\employee\ScoreRecordListForm;
use app\modules\back\forms\employee\UserEmployeeListForm;
use app\modules\back\services\UserEmployeeService;
use app\modules\rbac\controllers\CommController;

class UserEmployeeController extends CommController
{
    /**
     * 用户列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new UserEmployeeListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->getUserEmployeeList($form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 用户信息
     * @return void
     */
    public function actionInfo()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $uid    = $params['uid'] ?? ''; // 用户UID
        // 参数校验
        if (empty($uid)) {
            CUtil::json_response(-1, '用户不存在');
        }
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->getUserEmployeeInfo($uid);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 加入黑名单
     * @return void
     */
    public function actionAddBlacklist()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $uid    = $params['uid'] ?? ''; // 用户UID
        // 参数校验
        if (empty($uid)) {
            CUtil::json_response(-1, '用户不存在');
        }
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->addBlackList($uid);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok');
    }

    /**
     * 移除黑名单
     * @return void
     */
    public function actionRemoveBlacklist()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $uid    = $params['uid'] ?? ''; // 用户UID
        // 参数校验
        if (empty($uid)) {
            CUtil::json_response(-1, '用户不存在');
        }
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->removeBlackList($uid);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok');
    }

    /**
     * 操作员工分数
     * @return void
     */
    public function actionOptScore()
    {
        // 并发限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 3, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '频繁请求，请稍候！');
        }

        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new OptScoreForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 请求参数
        $params            = $form->toArray();
        $params['handle_name'] = $this->user_info['account'] ?? '';
        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->optScore($params);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok');
    }

    /**
     * 积分记录
     * @return void
     */
    public function actionScoreRecord()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new ScoreRecordListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        // 调用服务
        list($status, $res) = UserEmployeeService::getInstance()->getScoreRecord($form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }
}