<?php

namespace app\modules\back\controllers;


use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\wares\services\goods\GoodsMainService;
use yii\db\Exception;


class TryGoodsController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/try-goods/save",
     *     summary="商品-新建/编辑商品",
     *     description="商品-新建/编辑商品",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryGoodsSaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        $post           = \Yii::$app->request->post();
        $post['source'] = $post['source'] ?? 3;//先用后付来源

        list($status, $ret) = GoodsMainService::getInstance()->modify($post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $id  = $post['id'] ?? 0;
        $msg = $id ? "修改了先试后买商品ID：{$id}" : "新增了先试后买商品ID：{$ret}";
        $msg .= ",内容：" . json_encode($post, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::TRY_BEFORE_BUY, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/try-goods/list",
     *     summary="商品-商品列表",
     *     description="商品-商品列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryGoodsListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post  = \Yii::$app->request->post();
        $input = [
            'id'     => intval($post['id'] ?? 0),
            'sku'    => trim($post['sku'] ?? ''),
            'name'   => trim($post['name'] ?? ''),
            'label'  => trim($post['label'] ?? ''),
            'status' => intval($post['status'] ?? -1),
            'source' => intval($post['source'] ?? 3),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = GoodsMainService::getInstance()->GetList($input, $page, $page_size);

        foreach ($return['list'] as $k => $value) {
            $return['list'][$k] = [
                'id'     => $value['id'],
                'sku'    => $value['sku'],
                'name'   => $value['name'],
                'label'  => $value['label'],
                'stock'  => $value['stock'],
                'sales'  => $value['sales'],
                'status' => $value['status']
            ];
        }
        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/back/try-goods/info",
     *     summary="商品-商品详情",
     *     description="商品-商品详情",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="商品ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        $id    = \Yii::$app->request->post("id", 0);
        $aData = GoodsMainService::getInstance()->GetOneByGid($id);
        CUtil::json_response(1, "OK", $aData);
    }


    /**
     * @OA\Post(
     *     path="/back/try-goods/sale",
     *     summary="商品-商品上下架",
     *     description="商品-商品上下架",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"act","id"},
     *          @OA\Property(property="act", type="string", default="", description="上下架状态 up上架 down下架"),
     *          @OA\Property(property="id", type="integer", default="", description="商品ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSale()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        $act  = $post['act'] ?? '';
        $id   = CUtil::uint($id);
        $act  = strtolower(trim($act));

        if (!in_array($act, ['up', 'down'])) {
            CUtil::json_response(-1, '上下架操作参数错误');
        }
        $arr = [];
        switch ($act) {
            case 'up':
                $arr = ['status' => 0, 'utime' => intval(START_TIME)];
                break;
            case 'down':
                $arr = ['status' => 1, 'utime' => intval(START_TIME)];
                break;
            default:
                break;
        }

        list($status, $ret) = GoodsMainService::getInstance()->UpdateData($id, $arr);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        (new SystemLogsModel())->record("批量修改先试后买商品：{$id}；操作码：{$act}；", RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/try-goods/del",
     *     summary="商品-商品删除",
     *     description="商品-商品删除",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="商品ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDel()
    {
        $id = \Yii::$app->request->post('id', '');
        list($s, $m) = GoodsMainService::getInstance()->Del($id);
        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("删除先试后买商品：{$id}；", RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, 'ok');
    }
}