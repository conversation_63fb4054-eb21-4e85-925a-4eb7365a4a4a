<?php

namespace app\modules\back\controllers;


use app\modules\back\services\SubsidyService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;

/**
 * 字典数据
 * 接口文档：https://doc.apipost.net/docs/detail/45575e29b0e0000?target_id=1160852035d018&locale=zh-cn
 * 文档密码：593336
 */
class SubsidyController extends CommController
{
    use ControllerTrait;

    /** @var SubsidyService */
    private $service;

    public function __construct($id, $module, $config = [])
    {
        $this->service = SubsidyService::getInstance();
        parent::__construct($id, $module, $config);
    }


}