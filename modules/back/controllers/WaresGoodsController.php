<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\wares\services\goods\GoodsMainService;
use yii\db\Exception;


class WaresGoodsController extends CommController
{


    /**
     * @OA\Post(
     *     path="/back/wares-goods/list",
     *     summary="后台-获取新商品列表",
     *     description="后台-获取新商品列表",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post  = \Yii::$app->request->post();
        $input = [
            'name'       => trim($post['name'] ?? ""),
            'sku'        => trim($post['sku'] ?? ""),
            'id'        =>  intval($post['id'] ?? ""),
            'short_code' => trim($post['short_code'] ?? ""),
            'status'     => intval($post['status'] ?? -1),
            'source'     => intval($post['source'] ?? 1),
            'type'       => intval($post['type'] ?? -1),
            'version'    => trim($post['version'] ?? ''),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = GoodsMainService::getInstance()->GetList($input,$page,$page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @OA\Post(
     *     path="/back/wares-goods/save",
     *     summary="新建/编辑商品",
     *     description="新建/编辑商品",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsSaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        $post = \Yii::$app->request->post();

        list($status,$ret) = GoodsMainService::getInstance()->modify($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        $id    = $post['id']   ?? 0;
        $msg   = $id ? "修改了商品ID：{$id}" : "新增了商品ID：{$ret}";
        $msg  .= ",内容：".json_encode($post,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::WARES_GOODS_MANAGER, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/wares-goods/points-enum",
     *     summary="获取积分商品枚举内容",
     *     description="获取积分商品枚举内容",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsEnumRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionPointsEnum()
    {
        $post = \Yii::$app->request->post();

        list($status,$ret) = GoodsMainService::getInstance()->getPointsEnum($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,'OK', $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/wares-goods/info",
     *     summary="后台-获取新商品详情",
     *     description="后台-获取新商品详情",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WaresGoodsInfoResponse",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     * @throws \RedisException
     */
    public function actionInfo()
    {
        $id    = \Yii::$app->request->post("id", 0);
        $aData = GoodsMainService::getInstance()->GetOneByGid($id);
        CUtil::json_response(1, "OK", $aData);
    }




    /**
     * @OA\Post(
     *     path="/back/wares-goods/sort",
     *     summary="后台-新商品排序",
     *     description="后台-新商品排序",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsSortRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSort()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        $sort = $post['sort'] ?? 0;
        $id   = CUtil::uint($id);
        $sort = CUtil::uint($sort);

        list($status, $ret) = GoodsMainService::getInstance()->UpdateData($id, ['sort' => $sort,'utime'=>intval(START_TIME)]);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/wares-goods/sale",
     *     summary="后台-新商品上下架",
     *     description="后台-新商品上下架",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsSaleRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSale()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        $act = $post['act'] ?? '';
        $id   = CUtil::uint($id);
        $act = strtolower(trim($act));

        if(!in_array($act,['up','down'])){
            CUtil::json_response(-1, '上下架操作参数错误');
        }
        $arr =[];
        switch ($act){
            case 'up':
                $arr = ['status' => 0,'utime'=>intval(START_TIME)];
                break;
            case 'down':
                $arr = ['status' => 1,'utime'=>intval(START_TIME)];
                break;
            default:break;
        }

        list($status, $ret) = GoodsMainService::getInstance()->UpdateData($id, $arr);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        (new SystemLogsModel())->record("批量修改普通商品：{$id}；操作码：{$act}；", RbacInfoModel::WARES_GOODS_MANAGER);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/wares-goods/del",
     *     summary="后台-商品删除",
     *     description="后台-商品删除",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionDel()
    {
        $id        = \Yii::$app->request->post('id', '');
        list($s, $m) = GoodsMainService::getInstance()->Del($id);
        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        (new SystemLogsModel())->record("删除普通商品：{$id}；", RbacInfoModel::WARES_GOODS_MANAGER);
        CUtil::json_response(1, 'ok');
    }



    /**
     * @OA\Post(
     *     path="/back/wares-goods/sync-stock",
     *     summary="后台-同步库存",
     *     description="后台-同步库存",
     *     tags={"新商品模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresGoodsInfoRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSyncStock()
    {
        $id        = \Yii::$app->request->post('id', '');
        list($s, $m) = GoodsMainService::getInstance()->SyncWaresStock($id);
        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        CUtil::json_response(1, 'ok');
    }

}
