<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/7/1
 * Time: 11:47
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;

class DepositController extends CommController
{
    /**
     * @throws Exception
     * @throws \RedisException
     * 定金订单列表
     */
    public function actionDepositOrderList()
    {
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = 20;

        $year       = $post['year']       ?? date("Y");
        $order_no   = $post['order_no']   ?? "";
        $status     = $post['status']     ?? -1;
        $s_time     = $post['s_time']     ?? 0;
        $e_time     = $post['e_time']     ?? 0;

        //无数据
        if($year < by::Odeposit()::DB_TIMEZONE['ST'] || $year > by::Odeposit()::DB_TIMEZONE['ED']) {
            $list['pages']    = 1;
            CUtil::json_response(1, "OK", $list);
        }


        $list['list']   = by::Odeposit()->getList($year,$order_no,$status,$s_time,$e_time,$page,$page_size);
        $count          = by::Odeposit()->getListCount($year,$order_no, $status, $s_time, $e_time);
        $list['count']  = $count;
        $list['pages']  = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $list);
    }
}