<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\back\forms\employee\UserBindListForm;
use app\modules\back\services\UserBindService;
use app\modules\rbac\controllers\CommController;

class UserBindController extends CommController
{
    /**
     * 绑定列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        // 参数校验
        $form = new UserBindListForm();
        $form->load($params, '');
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }
        // 调用服务
        list($status, $res) = UserBindService::getInstance()->getBindListByBoundUserId($form->toArray());
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        // 返回结果
        CUtil::json_response(1, 'ok', $res);
    }
}