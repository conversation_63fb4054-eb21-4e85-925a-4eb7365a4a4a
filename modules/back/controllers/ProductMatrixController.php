<?php

namespace app\modules\back\controllers;

use app\constants\RespStatusCodeConst;
use app\exceptions\ProductMatrixException;
use app\models\CUtil;
use app\modules\back\forms\productMatrix\MatrixModifyForm;
use app\modules\back\services\ProductMatrixService;
use app\modules\goods\models\ProductMatrixModel;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;


class ProductMatrixController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/product-matrix/modify",
     *     summary="后台--产品站信息保存",
     *     description="后台--产品站信息保存",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ProductMatrixModifyRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionModify()
    {
        // 获取POST请求数据
        $post = \Yii::$app->request->post();

        // 创建并加载表单数据
        $form = new MatrixModifyForm();
        if (!$form->load($post, '')) {
            CUtil::json_response(-1, '加载表单数据失败');
        }

        // 验证表单数据
        if (!$form->validate()) {
            // 获取并返回第一个验证错误信息
            $errors = $form->firstErrors;
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, array_shift($errors));
        }

        // 获取保存的数据
        $save = $form->toArray();

        // 保存产品矩阵信息
        try {
            list($status, $result) = ProductMatrixService::getInstance()->modify($save);
            $categoryId = $result['category_id'];
            // 保存失败时，返回失败信息
            if (!$status) {
                // 保存失败时，返回失败信息
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
            }

            // 记录日志
            $msg = $result['utime'] ? "修改了产品站数据：{$categoryId}" : "新增了产品站数据：{$categoryId}";
            $msg .= ",内容：" . json_encode($result, JSON_UNESCAPED_UNICODE);
            (new SystemLogsModel())->record($msg, RbacInfoModel::PRODUCT_MATRIX, $this->user_info['id']);

            // 返回成功响应
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE);
        } catch (ProductMatrixException $productMatrixException) {
            // 捕获异常并记录日志
            CUtil::setLogMsg(
                    "err.modify_product_matrix",                                    // 日志标识
                    $save,                                                          // 请求数据
                    [],                                                             // 可选的附加数据
                    [],                                                             // 可选的额外信息
                    \Yii::$app->controller->route,                                  // 当前路由
                    '保存产品站信息失败: ' . $productMatrixException->getMessage()  // 错误信息
            );

            // 返回失败响应
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }

    /**
     * @OA\Post(
     *     path="/back/product-matrix/detail",
     *     summary="后台--产品站信息详情",
     *     description="后台--产品站信息详情",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"category_id"},
     *                         @OA\Property(property="category_id", type="integer", description="商品分类ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixDetailResponse")
     *         )
     *     )
     * )
     */

    public function actionDetail()
    {
        // 获取请求参数
        $categoryId = \Yii::$app->request->post('category_id', 0);

        // 检查 category_id 是否有效
        if (!$categoryId) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }

        try {
            // 调用服务层获取产品站信息详情
            $productMatrixDetail = ProductMatrixService::getInstance()->getDetail($categoryId);
            // 返回成功响应，携带产品站信息详情
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $productMatrixDetail);
        } catch (\Exception $e) {
            // 捕获异常并记录日志
            CUtil::setLogMsg(
                    "err.detail_product_matrix",
                    $categoryId,
                    [],
                    [],
                    \Yii::$app->controller->route,
                    '获取产品站详情失败: ' . $e->getMessage()
            );
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/product-matrix/category-modify",
     *     summary="后台--产品站商品品类保存",
     *     description="后台--产品站商品品类保存",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ProductMatrixCategoryModifyRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCategoryModify()
    {
        // 获取POST请求数据
        $post = \Yii::$app->request->post();

        // 获取id和name字段
        $categoryJson = $post['category_data'] ?? '';

        $categoryData = json_decode($categoryJson, true);
        if (empty($categoryData)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }

        // 调用服务层处理分类修改
        list($status, $result) = ProductMatrixService::getInstance()->categoryModify($categoryData);

        // 判断返回结果，如果修改失败
        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result);
        }

        // 记录日志
        $logData = [
                '新增产品站品类' => $result['categoryAdd'] ?? [],
                '更新产品站品类' => $result['categoryUpdate'] ?? [],
                '删除产品站品类' => $result['deleteIds'] ?? []
        ];

        $logMessage = array_map(function ($key, $value) {
            return sprintf("%s: %s", $key, json_encode($value, JSON_UNESCAPED_UNICODE));
        }, array_keys($logData), $logData);

        $finalLogMessage = implode(" | ", $logMessage);

        (new SystemLogsModel())->record($finalLogMessage, RbacInfoModel::PRODUCT_MATRIX, $this->user_info['id']);

        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE);
    }


    /**
     * @OA\Post(
     *     path="/back/product-matrix/category-list",
     *     summary="后台--产品站商品品类列表",
     *     description="后台--产品站商品品类列表",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixCategoryListResponse")
     *         )
     *     )
     * )
     */
    public function actionCategoryList()
    {
        $result = ProductMatrixService::getInstance()->categoryList();
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $result);
    }

    /**
     * @OA\Post(
     *     path="/back/product-matrix/intro-modify",
     *     summary="后台--官方介绍保存",
     *     description="后台--官方介绍保存",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ProductMatrixIntroModifyRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionIntroModify()
    {
        $post = \Yii::$app->request->post();

        $id     = $post['id'] ?? 0;
        $title  = $post['title'] ?? '';
        $intro  = $post['intro'] ?? '';
        $status = $post['status'] ?? 0;

        if (empty($title) || empty($intro)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }

        try {
            list($status, $result) = ProductMatrixService::getInstance()->introModify($id, $title, $intro, $status);

            if (!$status) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
            }

            $msg = $result['utime'] ? "修改了产品站官网介绍，ID：{$result['id']}" : "新增了产品站官网介绍，ID：{$result['id']}";
            $msg .= ",内容：" . json_encode($result, JSON_UNESCAPED_UNICODE);
            (new SystemLogsModel())->record($msg, RbacInfoModel::PRODUCT_MATRIX, $this->user_info['id']);
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE);
        } catch (ProductMatrixException $e) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }

    /**
     * @OA\Post(
     *     path="/back/product-matrix/intro-status",
     *     summary="后台--官方介绍上下架",
     *     description="后台--官方介绍上下架",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="integer", description="ID"),
     *                         @OA\Property(property="status", type="string", description="UP:上架、DOWN:下架")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionIntroStatus()
    {
        $post = \Yii::$app->request->post();

        $id     = $post['id'] ?? 0;
        $status = $post['status'] ?? '';

        if (empty($id) || empty($status)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }

        try {
            list($status, $result) = ProductMatrixService::getInstance()->introStatus($id, $status);

            if (!$status) {
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
            }

            $msg = "修改了产品站官网介绍状态，ID：{$result['id']} 为：{$status}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::PRODUCT_MATRIX, $this->user_info['id']);
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE);
        } catch (ProductMatrixException $e) {

            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/product-matrix/intro-detail",
     *     summary="后台--官方介绍详情",
     *     description="后台--官方介绍详情",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="integer", description="ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixIntroDetailResponse")
     *         )
     *     )
     * )
     */
    public function actionIntroDetail()
    {
        $id = \Yii::$app->request->post('id', 0);
        if (empty($id) || !is_numeric($id) || $id <= 0) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE);
        }
        $result = ProductMatrixService::getInstance()->introDetail($id);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $result);
    }

    /**
     * @OA\Post(
     *     path="/back/product-matrix/intro-list",
     *     summary="后台--官方介绍列表",
     *     description="后台--官方介绍列表",
     *     tags={"产品站信息"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"page", "size"},
     *                         @OA\Property(property="title", type="string", description="标题"),
     *                         @OA\Property(property="page", type="integer", description="页码"),
     *                         @OA\Property(property="size", type="integer", description="每页数量")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", ref="#/components/schemas/ProductMatrixIntroListResponse")
     *         )
     *     )
     * )
     */
    public function actionIntroList()
    {
        $post = \Yii::$app->request->post();

        $page = $post['page'] ?? 1;
        $size = $post['size'] ?? 10;

        $result = ProductMatrixService::getInstance()->introList($post, $page, $size);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, '', $result);
    }

}