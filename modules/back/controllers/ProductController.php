<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\log\services\reg\RegWhiteService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;


class ProductController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/product/product-save",
     *     summary="产品注册管理-新增/编辑产品",
     *     description="产品注册管理-新增/编辑产品",
     *     tags={"产品注册管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ProductSaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionProductSave(){
        $post       = \Yii::$app->request->post();
        $id         = $post['id']           ??  0;
        $name       = $post['name']         ?? '';
        $image      = $post['image']        ?? '';
        $sn         = $post['sn']           ?? '';
        $m_name     = $post['m_name']       ?? '';
        $tid        = $post['tid']          ?? '';
        $period     = CUtil::uint($post['period']       ?? 0);
        $is_support_care = $post['is_support_care'] ?? 0;
        
        $care_info = [
            'is_automatic' => $post['is_automatic'] ?? '',
            'care_extend_month' => $post['care_extend_month'] ? : 0,
            'care_title' => $post['care_title'] ?? '',
            'care_change_year' => $post['care_change_year'] ?? '',
            'care_used_rule' => $post['care_used_rule'] ?? '',
        ];
        


        $is_market  = $post['is_market']    ??  0;
        $mConfig    = $post['mConfig']      ?? '';
        $ext        = [
            'is_market' => $is_market,
            'mConfig'   => json_decode($mConfig, true),
        ];
        list($status, $ret) = by::product()->saveProduct($name, $image, $m_name, $period, $sn, $id, $ext,$tid,$is_support_care,$care_info);
        if (!$status){
            CUtil::json_response(-1, $ret);
        }

        $msg   = $id ? "修改了产品：{$id}" : "新增了产品：{$name}";
        $msg  .= ",内容：".json_encode($ret,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::PRODUCT_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'ok');
    }

    /**
     * 产品列表
     */
    public function actionProductList(){
        $post       = \Yii::$app->request->post();
        $name       = $post['name']         ?? "";
        $sn         = $post['sn']           ?? "";
        $tid        = intval($post['tid'] ?? 0);
        $page       = $post['page']         ??  1;
        $page_size  = 20;

        $count              = by::product()->getCount($sn,$name,$tid);
        $return['pages']    = CUtil::getPaginationPages($count, $page_size);
        $ids                = by::product()->getList($sn,$name,$tid,$page,$page_size);
        foreach ($ids as $id){
            $info             = by::product()->getOneById($id['id']);
            $info['market']   = by::pMarket()->getListByPid($id['id']);
            $return['list'][] = $info;
        }

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * 删除产品
     */
    public function actionProductDel(){
        $id                 = \Yii::$app->request->post('id', null);
        list($status, $ret) = by::product()->del($id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("删除了产品,id为:{$id}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }

    /**
     * 产品绑定优惠券列表
     */
    public function actionMarketList(){
        $id                 = \Yii::$app->request->post('id', null);
        $list               = by::pMarket()->getListByPid($id);
        CUtil::json_response(1,'OK',$list);
    }

    /**
     * 删除产品下的优惠券
     */
    public function actionMarketDel(){
        $pm_id              = \Yii::$app->request->post('pm_id', null);
        list($status,$ret)  = by::pMarket()->del($pm_id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("删除产品绑定优惠券:".json_encode($ret), RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }

    /**
     * 添加产品下的优惠券
     */
    public function actionMarketSave(){
        $id                 = \Yii::$app->request->post('id', null);
        $p_id               = \Yii::$app->request->post('p_id', null);
        $mc_id              = \Yii::$app->request->post('mc_id', null);
        $stock              = \Yii::$app->request->post('stock', null);
        if ($id){
            $smg = '修改';
            list($status,$ret)  = by::pMarket()->edit($id,$p_id,$mc_id,$stock);
        }else{
            $smg = '新增';
            list($status,$ret)  = by::pMarket()->add($p_id,$mc_id,$stock);
        }

        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("{$smg}产品绑定优惠券，产品id={$p_id}资源id={$mc_id}库存={$stock}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK',$ret);
    }

    /**
     * 查询最大领取张数
     */
    public function actionConfig(){
        $config              = by::model('PmConfigModel', 'main')->getConfig();
        CUtil::json_response(1,'OK',$config);
    }

    /**
     * 修改最大领取张数
     */
    public function actionConfigUp(){
        $count                 = \Yii::$app->request->post('count', null);
        by::model('PmConfigModel', 'main')->edit($count);

        //写入日志
        (new SystemLogsModel())->record("修改最大领取张数，{$count}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }

    /**
     * 注册产品列表
     */
    public function actionRegList(){
        $post        = \Yii::$app->request->post();
        $year        = intval($post['year'] ?? date('Y', time()));
        $user_id     = $post['user_id'] ?? 0;
        $phone       = $post['phone'] ?? 0;
        $sn          = $post['sn'] ?? '';
        $ctime_start = intval($post['ctime_start'] ?? 0);
        $ctime_end   = intval($post['ctime_end'] ?? 0);
        $page        = $post['page'] ?? 1;
        $page_size   = 20;

        if (! empty($user_id) && ! CUtil::isIntegerString($user_id)) {
            CUtil::json_response(1, 'ok', ['pages' => 1, 'list' => []]);
        }

        $count              = by::model('ProductDetailModel', 'main')->getCount($year,(int) $user_id,$phone,$sn,$ctime_start,$ctime_end);

        $return['pages']    = CUtil::getPaginationPages($count, $page_size);
        $list               = by::model('ProductDetailModel', 'main')->getList($year,(int) $user_id,$phone,$sn,$ctime_start,$ctime_end,$page,$page_size);
        $return['list']     = by::model('ProductDetailModel', 'main')->getBackData($list);

        !$this->viewSensitive && $return['list'] = Response::responseList($return['list'],['phone'=>'tm']);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * 注册产品编辑
     */
    public function actionRegEdit() {
        $post       = \Yii::$app->request->post();
        $create_time= $post['create_time']  ?? 0;
        $id         = $post['id']           ?? 0;

        list($s, $m)= by::model('ProductDetailModel', 'main')->updateRegDetail($create_time, $id, $post);

        if(!$s) {
            CUtil::json_response(-1, $m);
        }

        //写入日志
        (new SystemLogsModel())->record("产品注册记录编辑sn，{$create_time}|{$id}|{$m}=>{$post['sn']}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * 同步crm
     */
    public function actionCrmSync()
    {
        CUtil::json_response(1,'无需同步CRM');
    }

    /**
     * 批量导入
     */
    public function actionEntrance(){
        $file = $_FILES['file'] ?? "";
        list($status,$ret) = by::product()->entrance($file);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1,'OK');
    }

    /**
     * 导出
     */
    public function actionExport(){
        $year       = $post['year']         ??  date('Y',time());
        $user_id    = $post['user_id']      ??  0;
        $phone      = $post['phone']        ??  0;
        $sn         = $post['sn']           ?? '';
        $ctime_start= $post['ctime_start']  ??  0;
        $ctime_end  = $post['ctime_end']    ??  0;

        by::model('ProductDetailModel', 'main')->export($year,$user_id,$phone,$sn,$ctime_start,$ctime_end);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/product/exception-list",
     *     summary="产品注册异常订单",
     *     description="产品注册异常订单",
     *     tags={"产品注册管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          @OA\Property(property="user_id", type="integer", default="", description="用户ID"),
     *          @OA\Property(property="tid", type="integer", default="", description="产品类型"),
     *          @OA\Property(property="sn", type="integer", default="", description="sn"),
     *          @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *          @OA\Property(property="nick", type="integer", default="", description="昵称"),
     *          @OA\Property(property="buy_start_time", type="integer", default="", description="购买开始时间"),
     *          @OA\Property(property="buy_end_time", type="integer", default="", description="购买结束时间"),
     *          @OA\Property(property="register_start_time", type="integer", default="", description="注册开始时间"),
     *          @OA\Property(property="register_end_time", type="integer", default="", description="注册结束时间"),
     *          @OA\Property(property="fail_reason", type="integer", default="", description="失败原因（100 => 'sn编码已被绑定', 200 => '用户未激活设备', 300 => 'sn码不匹配'）"),
     *          @OA\Property(property="page", type="integer", default="", description="page"),
     *          @OA\Property(property="page_size", type="integer", default="", description="page_size"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionExceptionList()
    {
        $post     = \Yii::$app->request->post();
        $input    = [
            'user_id'             => $post['user_id'] ?? 0,
            'tid'                 => $post['tid'] ?? 0,
            'sn'                  => $post['sn'] ?? '',
            'phone'               => $post['phone'] ?? 0,
            'nick'                => $post['nick'] ?? 0,
            'buy_start_time'      => $post['buy_start_time'] ?? 0,
            'buy_end_time'        => $post['buy_end_time'] ?? 0,
            'register_start_time' => $post['register_start_time'] ?? 0,
            'register_end_time'   => $post['register_end_time'] ?? 0,
            'fail_reason'         => $post['fail_reason'] ?? '',
        ];
        $page     = $post['page'] ?? 1;
        $pageSize = $post['page_size'] ?? 10;

        list($total, $pages, $list) = byNew::RegisterExceptionModel()->getList($input, $page, $pageSize);
        CUtil::json_response(1, 'ok', [
            'total' => $total, 'pages' => $pages, 'list' => $list
        ]);
    }




    /**
     * @OA\Post(
     *     path="/back/product/white-list",
     *     summary="产品注册白名单",
     *     description="产品注册白名单",
     *     tags={"产品注册管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="sn", type="string", default="", description="SN码"),
     *              @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *              @OA\Property(property="page", type="integer", default="", description="page"),
     *              @OA\Property(property="page_size", type="integer", default="", description="page_size"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionWhiteList()
    {
        $post     = \Yii::$app->request->post();
        $post['mo_sn'] = trim($post['sn'] ?? '');
        unset($post['sn']);
        $input    = [
            'mo_sn'               => $post['mo_sn'],
            'phone'               => $post['phone'] ?? 0,
        ];
        $page     = $post['page'] ?? 1;
        $pageSize = $post['page_size'] ?? 10;
        $data = RegWhiteService::getInstance()->GetList($input,$page,$pageSize);
        CUtil::json_response(1, 'ok', ['list' => $data[1], 'count' => $data[0],'pages' => $data[2]]);
    }


    /**
     * @OA\Post(
     *     path="/back/product/white-save",
     *     summary="产品注册白名单保存",
     *     description="产品注册白名单保存",
     *     tags={"产品注册管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="id", type="integer", default="", description="id 编辑时必传"),
     *              @OA\Property(property="sn", type="string", default="", description="SN码集合，多个用逗号隔开"),
     *              @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionWhiteSave()
    {
        $post     = \Yii::$app->request->post();
        $input    = [
            'sn'                  => trim($post['sn'] ?? ''),
            'id'                  => $post['id'] ?? 0, // 产品注册白名单id
            'phone'               => $post['phone'] ?? '',
        ];
        if(empty($input['sn'])){
            CUtil::json_response(-1, 'SN码不能为空');
        }

        list($s,$msg) = RegWhiteService::getInstance()->WhiteSave($input);
        if(!$s){
            CUtil::json_response(-1, $msg);
        }

        //写入日志
        (new SystemLogsModel())->record("产品注册白名单添加编辑sn，{$input['id']}|{$input['sn']}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);

        CUtil::json_response(1, 'ok', null);
    }


    /**
     * @OA\Post(
     *     path="/back/product/white-del",
     *     summary="产品注册白名单删除",
     *     description="产品注册白名单删除",
     *     tags={"产品注册管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="ids", type="string", default="", description="ids 删除时必传,支持多个用逗号隔开，批量删除"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionWhiteDel()
    {
        $post     = \Yii::$app->request->post();
        $ids      = trim($post['ids'] ?? '');
        if(empty($ids)){
            CUtil::json_response(-1, 'ids不能为空');
        }
        list($s,$msg) = RegWhiteService::getInstance()->WhiteDel($ids);
        if(!$s){
            CUtil::json_response(-1, $msg);
        }

        //写入日志
        (new SystemLogsModel())->record("产品注册白名单批量删除，{$post['ids']}", RbacInfoModel::PRODUCT_MANAGER,$this->user_info['id']);

        CUtil::json_response(1, 'ok', null);
    }

}
