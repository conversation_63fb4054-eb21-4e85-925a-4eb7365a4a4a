<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\back\services\CommentAuditService;
use app\modules\rbac\controllers\CommController;

class CommentAuditController extends CommController
{
    /**
     * 发放卡券
     */
    public function actionSend()
    {
        $rewardRemark = \Yii::$app->request->post('reward_remark', '');
        $id = \Yii::$app->request->post('id', 0);

        if (empty($id)) {
            CUtil::json_response(-1, '缺少必要参数');
        }

        $result = CommentAuditService::getInstance()->sendReward($id, $rewardRemark,1,$this->user_id);
        CUtil::json_response($result['code'], $result['message']);
    }
}