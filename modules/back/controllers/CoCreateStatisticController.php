<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\wares\services\cocreate\CoCreateStatisticService;
use yii\db\Exception;

class CoCreateStatisticController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/co-create-statistic/list",
     *     summary="后台-获取共创数据列表",
     *     description="后台-获取共创数据列表",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionList()
    {
        $post  = \Yii::$app->request->post();
        $input = [
            'id'       => trim($post['id'] ?? ""),
            'name'        => trim($post['name'] ?? ""),
            'topic'        => trim($post['topic'] ?? ""),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = CoCreateStatisticService::getInstance()->GetList($input,$page,$page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @OA\Post(
     *     path="/back/co-create-statistic/detail",
     *     summary="后台-获取共创数据详情",
     *     description="后台-获取共创数据详情",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateMaterialDetailRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionDetail()
    {
        $post  = \Yii::$app->request->post();
        $material_id = $post['material_id'] ?? 0;
        if(empty($material_id)){
            CUtil::json_response(-1, '素材ID不能为空');
        }
        $input = [
            'user_id'        => trim($post['user_id'] ?? ""),
            'enjoy'        => trim($post['enjoy'] ?? ""),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = CoCreateStatisticService::getInstance()->GetDetailList($material_id,$input,$page,$page_size);

        CUtil::json_response(1, 'ok', $return);
    }


}
