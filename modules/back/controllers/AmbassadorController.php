<?php

namespace app\modules\back\controllers;

use app\exceptions\AmbassadorException;
use app\modules\back\forms\ambassador\AmbassadorEditForm;
use app\modules\back\forms\ambassador\AmbassadorListForm;
use app\modules\back\services\AmbassadorService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;
use Yii;

class AmbassadorController extends CommController
{

    use ControllerTrait;

    /**
     * @OA\Post(
     *     path="/back/ambassador/info",
     *     summary="追觅大使详情",
     *     description="追觅大使详情",
     *     tags={"追觅大使"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id", "uid"},
     *                         @OA\Property(property="id", type="integer", default="", description="记录ID"),
     *                         @OA\Property(property="uid", type="string", default="", description="UID"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", ref="#/components/schemas/AmbassadorInfo", description="数据集合"),
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        try {
            $id  = Yii::$app->request->post("id");
            $uid = Yii::$app->request->post("uid");
            if (empty($id) || empty($uid)) {
                throw new AmbassadorException("参数错误");
            }

            $service = AmbassadorService::getInstance();
            $info    = $service->getAmbassadorInfo($id);
            if (empty($info) || $info["uid"] != $uid) {
                throw new AmbassadorException("数据不存在，请核实~");
            }

            $this->success(["info" => $info]);
        } catch (AmbassadorException $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/ambassador/list",
     *     summary="追觅大使列表",
     *     description="追觅大使列表",
     *     tags={"追觅大使"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="name", type="string", default="", description="姓名"),
     *                         @OA\Property(property="phone", type="string", default="", description="手机号"),
     *                         @OA\Property(property="uid", type="string", default="", description="UID"),
     *                         @OA\Property(property="page", type="integer", default="1", description="页码"),
     *                         @OA\Property(property="page_size", type="integer", default="20", description="每页数量"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", ref="#/components/schemas/AmbassadorList", description="数据集合"),
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        try {
            $form = new AmbassadorListForm();
            $form->load(Yii::$app->request->post(), "");

            if (!$form->validate()) {
                throw new AmbassadorException(implode(',', $form->firstErrors));
            }

            $service = AmbassadorService::getInstance();
            $data    = $service->getAmbassadorList($form->toArray());

            $this->success($data);
        } catch (AmbassadorException $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/ambassador/edit",
     *     summary="追觅大使添加/编辑",
     *     description="追觅大使添加/编辑",
     *     tags={"追觅大使"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"uid", "phone", "name"},
     *                         @OA\Property(property="id", type="integer", default="", description="记录ID"),
     *                         @OA\Property(property="uid", type="string", default="", description="UID"),
     *                         @OA\Property(property="phone", type="string", default="", description="手机号"),
     *                         @OA\Property(property="name", type="string", default="", description="姓名"),
     *                         @OA\Property(property="id_card", type="string", default="", description="身份证号"),
     *                         @OA\Property(property="bank_name", type="string", default="", description="开户行"),
     *                         @OA\Property(property="bank_card", type="string", default="", description="银行卡号"),
     *                         @OA\Property(property="contact_name", type="string", default="", description="内部对接人"),
     *                         @OA\Property(property="level", type="integer", default="", description="级别（1：初级大使；2：高级大使；3：超级大使）"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionEdit()
    {
        try {
            $form = new AmbassadorEditForm();
            $form->load(Yii::$app->request->post(), "");

            if (!$form->validate()) {
                throw new AmbassadorException(implode(',', $form->firstErrors));
            }

            $service = AmbassadorService::getInstance();
            $service->editAmbassadorInfo($form->toArray());

            $this->success();
        } catch (AmbassadorException $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/ambassador/delete",
     *     summary="追觅大使删除",
     *     description="追觅大使删除",
     *     tags={"追觅大使"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id", "uid"},
     *                         @OA\Property(property="id", type="integer", default="", description="记录ID"),
     *                         @OA\Property(property="uid", type="string", default="", description="UID"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        try {
            $id  = Yii::$app->request->post("id");
            $uid = Yii::$app->request->post("uid");
            if (empty($id) || empty($uid)) {
                throw new AmbassadorException("参数错误");
            }

            $service = AmbassadorService::getInstance();
            $service->deleteAmbassadorInfo($id, $uid);

            $this->success();
        } catch (AmbassadorException $e) {
            $this->error($e->getMessage());
        }
    }

}