<?php
namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\DreameStoreGoodsService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;

/**
 * 追觅小店可选商品 - 控制器 - 后台
 */
class DreameStoreGoodsController extends CommController
{
    use ControllerTrait;

    /* @var DreameStoreGoodsService */
    private $service;

    public function init()
    {
        parent::init();
        $this->service = DreameStoreGoodsService::instance();
    }

    /**
     * 列表
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getPageList($post));
    }

    /**
     * 新增
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $params = [];
            $params['goods_id'] = $post['goods_id'] ?? 0;
            $params['rate'] = $post['rate'] ?? 0.1;
            if ($params['rate'] === '' || $params['rate'] === null) {
                $this->error('缺少分佣比例参数');
            }
            if (!preg_match('/^0(\.\d{1,2})?$/', $params['rate'])) {
                $this->error('分佣比例必须是0到1之间的小数,且两位小数');
            }
            $this->service->create($params);
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 修改
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
            $params = $post;
            $params = [
                'id' => $params['id'] ?? null,
                'rate' => $params['rate'] ?? null,
            ];
            if (empty($params['id']) || ($params['rate'] === '' || $params['rate'] === null)) {
                $this->error('缺少参数');
            }
            if (!preg_match('/^0(\.\d{1,2})?$/', $params['rate'])) {
                $this->error('分佣比例必须是0到1之间的小数,且两位小数');
            }
            $this->service->update($params);
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * 新增
     */
    public function actionBatchCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $params = $post;
            $this->service->batchCreate($params);
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * 删除
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                $this->error('缺少ids字段');
            }
            $ids = explode(',', (string) $ids);
            $ids = array_unique($ids);

            $res =  $this->service->del($ids);
            if (!$res) {
                $this->error('失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }


    /**
     * 详情
     */
    public function actionInfo()
    {
        try {
            $post = \Yii::$app->request->post();
            $id = $post['id'] ?? 0;
            if (empty($id)) {
                $this->error('缺少ID参数');
            }
            $this->success($this->service->info($id));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}