<?php

namespace app\modules\back\controllers;


use app\models\CUtil;
use app\modules\back\services\UserTryService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;


class TryUserController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/try-user/shield",
     *     summary="用户-拉黑/移除黑名单",
     *     description="用户-拉黑/移除黑名单",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryUserShieldRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionShield()
    {
        $userId     = \Yii::$app->request->post('user_id', 0);
        $userStatus = \Yii::$app->request->post('status', 0);
        $reason     = \Yii::$app->request->post('reason', '');

        list($status, $result) = UserTryService::getInstance()->Shield($userId, $userStatus, $reason);
        if (!$status) {
            CUtil::json_response(-1, $result);
        }
        $message = $userStatus == 1 ? "加入黑名单" : "移除黑名单";
        (new SystemLogsModel())->record("将先试后买用户：{$userId}：{$message}", RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, "操作成功");
    }


    /**
     * @OA\Post(
     *     path="/back/try-user/list",
     *     summary="用户-获取用户列表",
     *     description="用户-获取用户列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/TryUserListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $page      = Yii::$app->request->post('page', 1);
        $pageSize  = Yii::$app->request->post('page_size', 20);
        $uid       = Yii::$app->request->post('uid', '');
        $phone     = Yii::$app->request->post('phone', '');
        $goodsName = Yii::$app->request->post('goods_name', '');
        $label     = Yii::$app->request->post('label') ?? -1;
        $status    = Yii::$app->request->post('status') ?? -1;
        $tryStatus = Yii::$app->request->post('try_status') ?? -1;


        $input = [
            'uid'        => $uid,
            'phone'      => $phone,
            'goods_name' => $goodsName,
            'label'      => $label,
            'status'     => $status,
            'try_status' => $tryStatus,
        ];

        $data = UserTryService::getInstance()->GetTryUserList($input, $page, $pageSize);
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/try-user/info",
     *     summary="用户-获取用户详情",
     *     description="用户-获取用户详情",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *           @OA\Property(property="id", type="integer", default="", description="ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        $id = Yii::$app->request->post('id', 0);
        list($status, $data) = UserTryService::getInstance()->GetTryUserInfo($id);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/try-user/user-path-list",
     *     summary="用户-用户链路",
     *     description="用户-用户链路",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *           @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
     *           @OA\Property(property="uid", type="integer", default="", description="uid"),
     *           @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *           @OA\Property(property="nick_name", type="integer", default="", description="昵称"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUserPathList()
    {
        $page     = Yii::$app->request->post('page', 1);
        $pageSize = Yii::$app->request->post('page_size', 20);
        $acId     = Yii::$app->request->post('ac_id', 0);
        $uid      = Yii::$app->request->post('uid', '');
        $phone    = Yii::$app->request->post('phone', '');
        $nickName = Yii::$app->request->post('nick_name', '');

        $input = [
            'ac_id'     => $acId,
            'uid'       => $uid,
            'phone'     => $phone,
            'nick_name' => $nickName,
        ];
        $data  = UserTryService::getInstance()->GetUserPathList($input, $page, $pageSize);
        CUtil::json_response(1, '获取成功', $data);
    }

}