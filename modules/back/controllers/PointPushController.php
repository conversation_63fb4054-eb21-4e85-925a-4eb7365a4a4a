<?php

namespace app\modules\back\controllers;


use app\models\CUtil;
use app\modules\back\services\PointPushService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;


class PointPushController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/point-push/list",
     *     summary="积分发放列表",
     *     description="积分/觅享分发放记录列表",
     *     tags={"积分模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/PointPushListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post           = \Yii::$app->request->post();
        $page           = $post['page'] ?? 1;
        $page_size      = $post['page_size'] ?? 20;
        $input = [
            'user_id'            => $post['user_id'] ?? '',
            'phone'              => $post['phone'] ?? '',
            'uid'                => $post['uid'] ?? '',
            'score'              => $post['score'] ?? '',
            'model'              => $post['model'] ?? '',
            'type'               => $post['type'] ?? '',
            'source'             => $post['source'] ?? '',
            'sub_source'         => $post['sub_source'] ?? '',
            'event'              => $post['event'] ?? '',
            'excel_no'           => $post['excel_no'] ?? '',
            'push_no'            => $post['push_no'] ?? '',
            'status'             => $post['status'] ?? '',
            'release_start_time' => $post['release_start_time'] ?? '',
            'release_end_time'   => $post['release_end_time'] ?? '',
            'admin_user'         => $post['admin_user'] ?? '',
            'start_time'         => $post['start_time'] ?? '',
            'end_time'           => $post['end_time'] ?? '',
        ];
        $data            = PointPushService::getInstance()->GetPointPushList($input, $page, $page_size);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/point-push/template",
     *     summary="积分发放模版URL",
     *     description="积分发放模板下载",
     *     tags={"积分模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionTemplate()
    {
        $data = [
            'url'=>'https://wpm-cdn.dreame.tech/files/202407/批量手动发放积分觅享分-20240729.csv',
        ];
        CUtil::json_response(1,'ok',$data);
    }


    /**
     * @OA\Post(
     *     path="/back/point-push/give-points",
     *     summary="批量上传积分/觅享分",
     *     description="批量上传积分/觅享分",
     *     tags={"积分模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="multipart/form-data",
     *       @OA\Schema(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/PointPushGivePointsRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionGivePoints()
    {
        $file = $_FILES['file'] ?? "";
        $post = \Yii::$app->request->post();
        $post['type'] = $post['type'] ?? 'add';
        list($status,$data) = PointPushService::getInstance()->BatchGivePoints($post, $file, $this->user_info['id'] ?? 0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        //写入日志
        (new SystemLogsModel())->record("上传文件且推送积分或觅享分|成功", RbacInfoModel::POINT_PUSH,$this->user_info['id']);
        CUtil::json_response(1,'ok',$data);
    }



    /**
     * @OA\Post(
     *     path="/back/point-push/push-points",
     *     summary="批量推送积分/觅享分",
     *     description="批量推送积分/觅享分",
     *     tags={"积分模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="multipart/form-data",
     *       @OA\Schema(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/PointPushPushPointsRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionPushPoints()
    {
        $post = \Yii::$app->request->post();
        $ids = $post['ids'] ?? '';
        if(empty($ids)){
            CUtil::json_response(-1, '请选择要推送的记录ID');
        }
        list($status,$data) = PointPushService::getInstance()->PushDataByIds($ids);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        //写入日志
        (new SystemLogsModel())->record("推送记录，{$ids}|成功", RbacInfoModel::POINT_PUSH,$this->user_info['id']);
        CUtil::json_response(1,'ok',$data);
    }


    /**
     * @OA\Post(
     *     path="/back/point-push/query-upload",
     *     summary="查询用户上传错误记录",
     *     description="查询用户上传错误记录",
     *     tags={"积分模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="multipart/form-data",
     *       @OA\Schema(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionQueryUpload()
    {
        list($status, $data) = PointPushService::getInstance()->GetUserUploadRecord($this->user_info['id']);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, 'ok', $data);
    }

}