<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\models\by;
use app\models\byNew;
use app\components\WeiXin;
use app\components\AliYunOss;
use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\jobs\EmployeeInviteJob;
use app\jobs\EmployeeOptScoreJob;
use app\jobs\EmployeeStatisticsJob;
use app\jobs\UserSmileJob;
use app\modules\main\models\UserBindModel;
use app\modules\main\services\UserBindService;
use app\modules\main\services\UserWithdrawRecordService;
use app\modules\rbac\controllers\CommController;

class UserWithdrawRecordController extends CommController
{
    /**
     * 绑定列表
     * @return void
     */
    public function actionList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $user_id = $params['user_id'] ?? 0;
        $status = $params['status'] ?? -1;
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 10;
        list($status,$res) = UserWithdrawRecordService::getInstance()->getList($user_id,$status,$page,$pageSize);
        if (!$status){
            CUtil::json_response(-1, $res,[]);
        }else{
            CUtil::json_response(1, 'ok', $res);
        }
    }
    
    public function actionAudit(){
        $post = \Yii::$app->request->post();
        $user_id = $this->user_id;
        $id = $post['id'] ?? 0;
        $status = $post['status'] ?? 0;
        if (empty($id) || empty($status)){
            CUtil::json_response(-1, '参数错误',[]);
        }
        $remark = $post['remark'] ?? '';

        list($status,$res) = UserWithdrawRecordService::getInstance()->audit($id, $status,$remark);
        if (!$status){
            CUtil::json_response(-1, $res,[]);
        }else{
            CUtil::json_response(1, 'ok', []);
        }
    }
}