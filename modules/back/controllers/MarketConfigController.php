<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\main\models\MarketConfigModel;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;
use yii\db\Exception;

class MarketConfigController extends CommController
{

    /**TODO 获取营销配置列表
     * @throws Exception
     * @throws \RedisException
     */
    public function actionList()
    {
        $status     = Yii::$app->request->post('status', -1);
        $name       = Yii::$app->request->post('name', '');
        $type       = Yii::$app->request->post('type', -1);
        $page       = Yii::$app->request->post('page', 1);
        $tab        = Yii::$app->request->post('tab', 0);
        $page_size  = 20;

        $count = by::marketConfig()->getCount($status, $name, $type, 0, $tab);
        $pages = CUtil::getPaginationPages($count, $page_size);
        $list  = by::marketConfig()->getList($page, $page_size, $status, $name, $type, 0,$tab);

        CUtil::json_response(1, 'ok', [
            'pages'       => $pages,
            'list'        => $list
        ]);
    }


	/**
	 * TODO 获取指定配置详情
	 * @throws Exception
	 */
    public function actionDetail()
    {
        $id = Yii::$app->request->post('id');
        $id = CUtil::uint($id);
        if (empty($id)) {
            CUtil::json_response(-1, 'id不能为空');
        }
        $data = by::marketConfig()->getOneById($id);
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/market-config/detail-by-code",
     *     summary="通过卡券编码获取详情",
     *     description="获取符合条件的卡券信息",
     *     tags={"优惠券"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"code"},
     *                         @OA\Property(property="code", type="string", description="卡券编码")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(property="id", type="string", description="卡券ID"),
     *                 @OA\Property(property="name", type="string", description="卡券名称"),
     *                 @OA\Property(property="image", type="string", description="图片"),
     *                 @OA\Property(property="code", type="string", description="券码"),
     *                 @OA\Property(property="create_at", type="string", description="创建时间"),
     *                 @OA\Property(property="update_at", type="string", description="更新时间") 
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionDetailByCode()
    {
        $code = Yii::$app->request->post('code');
        if (empty($code)) {
            CUtil::json_response(-1, '卡券编码不能为空');
        }
        $data = by::marketConfig()->getOneByCode($code);
        if (empty($data)) {
            CUtil::json_response(-1, '卡券不存在');
        }
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/market-config/modify",
     *     summary="优惠券-创建/编辑",
     *     description="优惠券-创建/编辑",
     *     tags={"优惠券"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(ref="#/components/schemas/MarketConfigModify")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */

    public function actionModify()
    {
        $request = Yii::$app->request;
        $web_name               = $request->post('web_name', '');//外显名称
        $id                     = $request->post('id', 0);
        $name                   = $request->post('name', '');       // 资源名称
        $data['images']         = $request->post('images', '');     //
        $data['freight_free']   = $request->post('freight_free', 0);// 兑换券配置是否免邮
        $type                   = $request->post('type', 0);        // 资源类型(1:优惠券2:G积分3:提示泡泡4:排除卡5:免邮卡6:兑换券)
        if ($type == by::marketConfig()::TYPE['coupon'] || $type == by::marketConfig()::TYPE['voucher'] || $type == by::marketConfig()::TYPE['consume']) {
            // 当选择资源类型为优惠券或兑换券时，资源内容选择如下：
            // 折扣优惠N%、固定金额N元---并且可选择适用金额：全部金额/最低订单金额元
            // 并且可选择适用条件分别为：全部标签/适用标签/排除标签，当选择为全部标签时，没有添加；当选择为适用标签/排除标签时，则可以添加标签（最多可添加3个，添加后可删除）下方商品选择可选择“适用商品/排除商品”，后方格子内填入适用/排除的商品，若不填则默认该处为空
            // 并且可选择适用条件分别为：全部商品系列、特定商品系列（选择）或者最低订单金额N元（删除）
            //{"resource_type":1,"discount":0.20,"condition":1,"condition_ext":20}
            if ($type == by::marketConfig()::TYPE['coupon'] || $type == by::marketConfig()::TYPE['consume']) {
                $resource['resource_type'] = $request->post('resource_type', 0);   // 资源内容类型(1:折扣优惠N%;2:固定金额N元;)
                $resource['discount']      = $request->post('discount', 0);        //折扣优惠N%->转小数
                $resource['c_amount_type'] = $request->post('c_amount_type', 0);   //使用金额（1：全部金额；2：最低订单金额）
                $resource['c_amount_val']  = $request->post('c_amount_val', 0);    //最低订单金额\元
            }

            $resource['c_tag_type']    = $request->post('c_tag_type', 0);                             //使用条件（1：全部标签；2：适用标签；3：排除标签）
            $resource['c_tag_val']     = $request->post('c_tag_val', '');                             //选择标签（多个用逗号分隔）
            $resource['c_goods_type']  = $request->post('c_goods_type', 0);                           //商品条件（1：适用商品；2：排除商品）
            $resource['c_goods_val']   = str_replace('，', ',', $request->post('c_goods_val', ''));    //选择商品（多个用逗号分隔）--(中文逗号替换为英文逗号)
            $resource['jump_goods_id'] = $request->post('jump_goods_id', 0);                          //跳转商品ID
            $resource['use_rule_note'] = $request->post('use_rule_note', '');                         //使用规则
            $resource['c_meet_type']   = $request->post('c_meet_type', 1);                            //优惠券满足的类型（1：最少多少件，2：最多多少件）
            $resource['c_meet_num']    = $request->post('c_meet_num', 1);                             //优惠券满足的商品数量

//            $condition      = $request->post('condition', 0);       //使用条件（1：全部商品；2：特定商品；3：最低订单金额N元）
//            $condition_ext  = $request->post('condition_ext', 0);   //使用条件后限制
        } else {

            // 当选择资源类型为G积分/提示泡泡/排除卡/免邮卡时，资源内容需选择数量
            $resource_num   = $request->post('resource_num', 0);

            $resource = [
                'resource_num' => CUtil::uint($resource_num),
            ];
        }

        $resource['valid_type']     = $request->post('valid_type', 0);      //有效期类型（1：固定时间；2：领取后多少天）
        $resource['valid_val']      = $request->post('valid_val', '');      //有效期配置 固定时间配置 过期时间

        $data['stock_num']  = $request->post('stock_num', 0);       //库存数量 -99为无限库存
        $data['stock_num']  = $data['stock_num'] == by::marketConfig()::BIG_STOCK ? $data['stock_num'] : CUtil::uint($data['stock_num']);

        $status             = $request->post('status', 0);          //审批状态（0：未审批；2：申请审批）

        $resource           = json_encode($resource);

        list($status, $ret) = by::marketConfig()->modify($this->user_info, $name, $type, $resource, $id, $status, $data, $web_name);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        $msg   = $id ? "修改了资源ID：{$id}" : "新增了资源：{$name}";
        $msg  .= ",内容：{$ret}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

        CUtil::json_response(1, 'ok');
    }

	/**
	 * TODO 删除营销配置
	 * @throws Exception
	 */
    public function actionDelete()
    {
        $ids                = Yii::$app->request->post('id', '');
        if (empty($ids)) {
            CUtil::json_response(-1, '请选择数据');
        }

        $ida                = explode(',', $ids);
        list($status, $ret) = by::marketConfig()->del($ida);
        if(!$status){
            CUtil::json_response(-1, '删除失败');
        }

        $msg                = "删除资源ID：{$ids},已成功删除{$ret}个";
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

        if ($ret < count($ida)) {
            CUtil::json_response(-1, "已成功删除{$ret}个，其它审核通过的数据不能删除");
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * TODO 获取营销配置下拉列表  用户签到 活动配置选择
     */
    public function actionPickList()
    {
        $page_size    = 20;
        $marketConfig = by::marketConfig();
        $count        = $marketConfig->getCount(by::marketConfig()::STATUS['check_success']);
        $pages        = CUtil::getPaginationPages($count, $page_size);
        $num          = $pages;
        $data         = [];

        for ($i = 1; $i <= $num; $i++) {
            $configList = $marketConfig->getList($i, 25, $marketConfig::STATUS['check_success']);
            $configList = array_map(function ($config) use ($marketConfig) {
                if ($marketConfig->checkOverdue($config)) {
                    return [
                        'id'        => $config['id'],
                        'name'      => $config['name'],
                        'stock_num' => $config['stock_num'],
                        'type'      => $config['type'],
                    ];
                } else {
                    return [];
                }
            }, $configList);
            $configList = array_values(array_filter($configList));
            $data       = array_merge($data, $configList);
        }

        CUtil::json_response(1, 'ok', $data);
    }

	/**
	 * 提交审核
	 * @throws Exception
	 */
    public function actionApplyAudit()
    {
        $id     = Yii::$app->request->post('id', 0);
        $code   = Yii::$app->request->post('code', 0);

        if (empty($id)) {
            CUtil::json_response(-1, '请选择数据');
        }

        $act = [
            by::marketConfig()::ACT['apply'],
            by::marketConfig()::ACT['cancel'],
        ];

        if ( !in_array($code, $act) ) {
            CUtil::json_response(-1, '操作码有误！');
        }

        list($status, $r) = by::marketConfig()->audit($id, $code, $this->user_info);

        if ($status == false) {
            CUtil::json_response(-1, $r);
        }

        $msg  = "资源id：{$id}；修改状态：{$code}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

        CUtil::json_response(1, 'ok');
    }

	/**
	 * 审核操作
	 * @throws Exception
	 */
    public function actionAudit()
    {
        $id     = Yii::$app->request->post('id', 0);
        $code   = Yii::$app->request->post('code', 0);

        if (empty($id)) {
            CUtil::json_response(-1, '请选择数据');
        }
        $act = by::marketConfig()::ACT;
        if ( !in_array($code, $act) ) {
            CUtil::json_response(-1, '操作码有误！');
        }

        list($status, $r) = by::marketConfig()->audit($id, $code, $this->user_info);

        if ($status == false) {
            CUtil::json_response(-1, $r);
        }

        $msg  = "资源id：{$id}；修改状态：{$code}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

        CUtil::json_response(1, 'ok');
    }

	/**
	 * 获取营销资料配置信息
	 */
    public function actionGetCfg(){
	    // $tag_info = by::Gtag()::TAG;
	    $tag_info = by::Gtag()->GetTagCodeMap();
	    $list = [];
        $tagMap = by::Gtag()->GetTagNameMap();
	    foreach ($tag_info as $k=>$v){
		    if($v<0){
			    continue;
		    }
		    $list[] =[
			    'value'=>strval($v),
			    'text' =>$tagMap[$v] ?? '',
		    ];
	    }
	    $data =[
		    'tag'=>$list,
	    ];
	    CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @throws Exception
     * 产品注册券是否统计
     */
    public function actionUpIsStat()
    {
        $id              = Yii::$app->request->post('id', 0);
        $product_is_stat = Yii::$app->request->post('product_is_stat', 0);

        list($status,$ret) = by::marketConfig()->upIsStat($id, $product_is_stat);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        $msg  = "资源id：{$id}；修改是否统计为：{$product_is_stat}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

        CUtil::json_response(1, 'ok');
    }
}
