<?php

namespace app\modules\back\controllers;

use app\constants\RespStatusCodeConst;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\forms\groupPurchase\ActivityForm;
use app\modules\back\forms\groupPurchase\ActivityListForm;
use app\modules\back\forms\groupPurchase\GroupRecordForm;
use app\modules\back\services\GroupPurchaseService;
use app\modules\rbac\controllers\CommController;
use app\modules\wares\services\goods\GoodsMainService;

class GroupPurchaseController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/group-purchase/activity-list",
     *     summary="拼团活动列表",
     *     description="拼团活动列表",
     *     tags={"拼团活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/GroupPurchaseActivityList"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/GroupPurchaseActivityListResponse",description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivityList()
    {
        // 参数验证
        $form = CUtil::VdForm(new ActivityListForm());
        $data = byNew::GroupPurchaseActivityModel()->getActivityList($form->activity_id, $form->name, $form->start_time, $form->end_time);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/group-purchase/activity-save",
     *     summary="拼团活动新增/编辑",
     *     description="根据是否有传入id判断为新增还是编辑",
     *     tags={"拼团活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/GroupPurchaseActivitySave"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */
    public function actionActivitySave()
    {
        $form   = CUtil::VdForm(new ActivityForm());
        $result = byNew::GroupPurchaseActivityModel()->saveActivity($form->attributes);

        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, $result['message'], $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message'], $result['errors'] ?? []);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/group-purchase/activity-detail",
     *     summary="获取拼团活动详情",
     *     description="通过活动ID获取拼团活动的详细信息，包括活动时间、参与人数限制及相关商品信息。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *      @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *        @OA\JsonContent(
     *           allOf={
     *           @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *           @OA\Schema(
     *                 required={"id"},
     *                 @OA\Property(property="id", type="integer",  description="活动ID"),
     *               )
     *           }
     *        )
     *      )
     *  ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", description="活动ID"),
     *                 @OA\Property(property="name", type="string", description="活动名称"),
     *                 @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                 @OA\Property(property="details", type="string", description="活动详情"),
     *                 @OA\Property(property="min_members", type="integer", description="默认最小人数"),
     *                 @OA\Property(property="max_members", type="integer", description="默认最大人数"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="商品关联ID"),
     *                         @OA\Property(property="gid", type="integer", description="商品ID"),
     *                         @OA\Property(property="activity_id", type="integer", description="活动ID"),
     *                         @OA\Property(property="min_members", type="integer", description="商品最小人数"),
     *                         @OA\Property(property="max_members", type="integer", description="商品最大人数"),
     *                         @OA\Property(property="purchase_limit", type="integer", description="每人商品限购数量"),
     *                         @OA\Property(property="min_group_qty", type="integer", description="最小成团数量"),
     *                         @OA\Property(property="discount_activity_id", type="integer", description="折扣活动ID"),
     *                         @OA\Property(property="discount_gid", type="integer", description="折扣商品ID"),
     *                         @OA\Property(
     *                             property="specs",
     *                             description="商品最大人数",
     *                             type="array",
     *                             @OA\Items(
     *                                 @OA\Property(property="stock", type="integer", description="库存数量"),
     *                                 @OA\Property(property="sku", type="string", description="SKU编号")
     *                             )
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionActivityDetail()
    {
        $id     = $this->request->post('id');
        $result = byNew::GroupPurchaseActivityModel()->getActivityGoodsList($id);

        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $result['data']);
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/group-purchase/activity-delete",
     *     summary="拼团活动删除",
     *     description="删除指定的拼团活动",
     *     tags={"拼团活动"},
     *    @OA\RequestBody(
     *       @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *            allOf={
     *            @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *            @OA\Schema(
     *                  required={"id"},
     *                  @OA\Property(property="id", type="integer",  description="活动ID"),
     *                )
     *            }
     *         )
     *       )
     *   ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="array", @OA\Items(type="string"), description="数据")
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="none",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionActivityDelete()
    {
        $id     = $this->request->post('id');
        $result = byNew::GroupPurchaseActivityModel()->deleteActivity($id);

        if ($result['status']) {
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success');
        } else {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result['message']);
        }
    }


    /**
     * @OA\Post(
     *     path="/back/group-purchase/goods-search",
     *     summary="拼团活动商品搜索",
     *     description="搜索拼团活动中的商品信息",
     *     tags={"拼团活动"},
     *         @OA\RequestBody(
     *         @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                   required={"name"},
     *                  @OA\Property(property="name", type="string", description="商品名称")
     *                 )
     *             }
     *          )
     *        )
     *    ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="数据",
     *                 @OA\Items(
     *                     @OA\Property(property="gid", type="string", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称"),
     *                     @OA\Property(property="price", type="number", format="float", description="商品价格"),
     *                     @OA\Property(property="cover_image", type="string", description="图片"),
     *
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionGoodsSearch()
    {
        $goodsName = $this->request->post('name');
        $data = GroupPurchaseService::getInstance()->getGoodsList($goodsName);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);

    }

    /**
     * @OA\Post(
     *     path="/back/group-purchase/goods-stock",
     *     summary="拼团活动商品库存",
     *     description="搜索拼团活动中的商品库存",
     *     tags={"拼团活动"},
     *         @OA\RequestBody(
     *         @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                   required={"gid"},
     *                  @OA\Property(property="gid", type="string", description="商品ID")
     *                 )
     *             }
     *          )
     *        )
     *    ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="数据",
     *                 @OA\Items(
     *                     @OA\Property(property="stock", type="string", description="库存"),
     *                     @OA\Property(property="sku", type="string", description="sku码")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionGoodsStock()
    {
        $gid = $this->request->post('gid');
        if (empty($gid)) {
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE, '商品id不能为空');
        }
        $data = GoodsMainService::getInstance()->GetMainStockByGid($gid);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);

    }

    /**
     * @OA\Post(
     *     path="/back/group-purchase/group-record-list",
     *     summary="获取拼团记录列表",
     *     description="获取指定条件下的拼团记录列表。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *          @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\JsonContent(
     *              allOf={
     *              @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *              @OA\Schema(
     *              @OA\Property(property="activity_id", type="string", description="活动ID"),
     *              @OA\Property(property="uid", type="string", description="用户ID"),
     *              @OA\Property(property="nick_name", type="string", description="用户昵称"),
     *              @OA\Property(property="gid", type="string", description="商品ID"),
     *              @OA\Property(property="start_time", type="string", description="开始时间"),
     *              @OA\Property(property="end_time", type="string", description="结束时间"),
     *                  )
     *              }
     *           )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="uid", type="string", description="用户UID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="total_members", type="string", description="已有团员数"),
     *                         @OA\Property(property="max_members", type="string", description="最大团位数"),
     *                         @OA\Property(property="purchase_limit", type="string", description="商品限购数量"),
     *                         @OA\Property(property="ctime", type="string", description="创建时间"),
     *                         @OA\Property(property="status", type="string", description="状态 （0：拼团中 1：拼团成功  2: 团结束已拼满）"),
     *                         @OA\Property(property="total_items", type="string", description="总购买件数"),
     *                         @OA\Property(property="id", type="string", description="拼团ID"),
     *                         @OA\Property(property="nick_name", type="string", description="昵称"),
     *                         @OA\Property(property="phone", type="string", description="手机号"),
     *                         @OA\Property(property="max_purchases", type="integer", description="最大可购数")
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="分页信息")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionGroupRecordList()
    {
        $form = CUtil::VdForm(new GroupRecordForm());
        $data = GroupPurchaseService::getInstance()->getGroupRecordList($form->activity_id, $form->start_time, $form->end_time, $form->uid, $form->nick_name, $form->gid);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/group-purchase/group-detail",
     *     summary="获取拼团记录详情",
     *     description="获取指定拼团活动的详细信息，包括团长信息和订单信息。",
     *     tags={"拼团活动"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="拼团ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回数据",
     *                 @OA\Property(
     *                     property="leader",
     *                     type="object",
     *                     description="团长信息",
     *                     @OA\Property(property="uid", type="string", description="团长UID"),
     *                     @OA\Property(property="gid", type="string", description="商品ID"),
     *                     @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                     @OA\Property(property="ctime", type="string", description="创建时间"),
     *                     @OA\Property(property="status", type="string", description="状态 （0：拼团中 1：拼团成功  2：拼团失败）"),
     *                     @OA\Property(property="total_items", type="string", description="总购买件数"),
     *                     @OA\Property(property="id", type="string", description="拼团ID"),
     *                     @OA\Property(property="nick_name", type="string", description="团长昵称"),
     *                     @OA\Property(property="phone", type="string", description="团长手机号")
     *                 ),
     *                 @OA\Property(
     *                     property="orders",
     *                     type="array",
     *                     description="订单信息",
     *                     @OA\Items(
     *                         @OA\Property(property="price", type="string", description="订单总额"),
     *                         @OA\Property(property="pay_time", type="string", description="支付时间"),
     *                         @OA\Property(property="ctime", type="string", description="创建时间"),
     *                         @OA\Property(property="status", type="string", description="订单状态 0：待支付 100：已取消 300：待发货 400：待收货 500：已完成 10000000：申请退款 10000300：申请退款&待发货 10000400：申请退款&待收货 10000500：申请退款&已完成 20000000：退款完成 20000300：退款完成&待发货 20000400：退款完成&待收货 20000500：退款完成&已完成"),
     *                         @OA\Property(property="num", type="integer", description="购买数量"),
     *                         @OA\Property(property="total_amount", type="integer", description="商品总额"),
     *                         @OA\Property(property="id", type="string", description="拼团团员记录ID"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="group_purchase_id", type="string", description="拼团ID"),
     *                         @OA\Property(property="user_id", type="string", description="团员用户ID"),
     *                         @OA\Property(property="uid", type="string", description="团员用户ID"),
     *                         @OA\Property(property="order_no", type="string", description="订单ID"),
     *                         @OA\Property(property="utime", type="string", description="更新时间")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */


    public function actionGroupDetail()
    {
        $id   = $this->request->post('id');
        $data = GroupPurchaseService::getInstance()->getGroupRecordDetail($id);
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/async-export/export",
     *     summary="导出拼团记录",
     *     description="导出符合条件的拼团记录数据。",
     *     tags={"拼团活动"},
     *          @OA\RequestBody(
     *           @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *           @OA\JsonContent(
     *               allOf={
     *               @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *               @OA\Schema(
     *               required={"act_type"},
     *               @OA\Property(property="activity_id", type="string", description="活动ID"),
     *              @OA\Property(property="uid", type="string", description="团长UID"),
     *              @OA\Property(property="nick_name", type="string", description="团长昵称"),
     *              @OA\Property(property="gid", type="string", description="商品ID"),
     *              @OA\Property(property="start_time", type="string", description="开始时间"),
     *              @OA\Property(property="end_time", type="string", description="结束时间"),
     *              @OA\Property(property="act_type", type="string", description="导出类型ID值（1041）"),
     *                   )
     *               }
     *            )
     *          )
     *      ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             type="array",
     *             @OA\Items(
     *                 type="array",
     *                 @OA\Items(type="string")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */


    public function actionTags()
    {
        $data = CUtil::dictValue('group_purchase_tags');
        CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE, 'success', $data);
    }


}