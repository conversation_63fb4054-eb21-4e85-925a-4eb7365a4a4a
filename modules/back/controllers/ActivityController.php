<?php

namespace app\modules\back\controllers;


use app\components\Survey;
use app\exceptions\ActivityException;
use app\exceptions\ActivityWaresException;
use app\models\CUtil;
use app\modules\back\forms\activity\ActivityWaresModifyForm;
use app\modules\back\services\ActivityWaresService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;


class ActivityController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/activity/list",
     *     summary="活动-活动列表",
     *     description="活动-活动列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresActivityListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $page            = Yii::$app->request->post('page', 1);
        $page_size       = Yii::$app->request->post('page_size', 20);
        $acId            = Yii::$app->request->post('id', 0);
        $name            = Yii::$app->request->post('name', '');
        $goodsName       = Yii::$app->request->post('goods_name', '');
        $status          = Yii::$app->request->post('status', -1);
        $start_time      = Yii::$app->request->post('start_time', 0);
        $end_time        = Yii::$app->request->post('end_time', 0);
        $input           = [
            'id'         => $acId,
            'name'       => $name,
            'goods_name' => $goodsName,
            'status'     => $status,
            'start_time' => $start_time,
            'end_time'   => $end_time,
        ];
        $activityService = new ActivityWaresService();
        $data            = $activityService->getActivityList($input, $page, $page_size);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/activity/detail",
     *     summary="活动-活动详情",
     *     description="活动-活动详情",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="活动ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        $id              = Yii::$app->request->post('id', 0);
        $activityService = new ActivityWaresService();
        list($status, $data) = $activityService->getActivityDetail($id);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/activity/modify",
     *     summary="活动-新建/编辑活动",
     *     description="活动-新建/编辑活动",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WaresActivitySaveRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionModify()
    {
        // 请求参数
        $data       = \Yii::$app->request->post();
        $data['id'] = CUtil::uint($data['id'] ?? 0);

        // 验证参数
        $form = new ActivityWaresModifyForm();
        $form->load($data, '');
        if (!$form->validate()) {
            // 错误信息
            $errors = $form->firstErrors;
            CUtil::json_response(-1, array_shift($errors));
        }

        // 执行业务
        try {
            $activityService = new ActivityWaresService();
            $activityService->modify($data);
        } catch (ActivityWaresException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            CUtil::json_response(-1, '添加/编辑活动暂不可用');
        }

        if (isset($data['rule'])) {
            unset($data['rule']);
        }
        // 记录日志
        $msg  = json_encode($data, JSON_UNESCAPED_UNICODE);
        $msg1 = $data['id'] ? "修改了活动配置ID：{$data['id']}" : "新增了活动配置：{$data['name']}";
        $msg1 .= ",内容：{$msg}";
        (new SystemLogsModel())->record($msg1, RbacInfoModel::TRY_BEFORE_BUY);

        // 返回结果
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/activity/status",
     *     summary="活动-活动上下架",
     *     description="活动-活动上下架",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"act","id"},
     *          @OA\Property(property="act", type="string", default="", description="上下架状态 up上架 down下架"),
     *          @OA\Property(property="id", type="integer", default="", description="活动ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionStatus()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        $act  = $post['act'] ?? '';
        $id   = CUtil::uint($id);
        $act  = strtolower(trim($act));

        if (!in_array($act, ['up', 'down'])) {
            CUtil::json_response(-1, '上下架操作参数错误');
        }

        $activityService = new ActivityWaresService();
        list($status, $ret) = $activityService->updateStatus($id, $act);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        (new SystemLogsModel())->record("批量修改先试后买活动：{$id}；操作码：{$act}；", RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/activity/del",
     *     summary="活动-活动删除",
     *     description="活动-活动删除",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="活动ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDel()
    {
        $id              = \Yii::$app->request->post('id', '');
        $activityService = new ActivityWaresService();
        list($status, $ret) = $activityService->del($id);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        (new SystemLogsModel())->record("删除先试后买活动：{$id}；", RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/activity/survey-key",
     *     summary="活动-问卷列表",
     *     description="活动-问卷列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSurveyKey()
    {
        list($status, $data) = Survey::factory()->surveyList();
        if (!$status) {
            CUtil::json_response(-1, '获取失败');
        }
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/activity/condition",
     *     summary="活动-限制与参与条件",
     *     description="活动-限制与参与条件",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}}
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object", description="数据")
     *         )
     *     )
     * )
     */

    public function actionCondition()
    {
        $activityService = new ActivityWaresService();
        $data            = $activityService->condition();
        CUtil::json_response(1, '获取成功', $data);
    }
}