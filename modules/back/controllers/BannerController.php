<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/21
 * Time: 11:11
 */

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\banner\BannerModifyForm;
use app\modules\back\services\BannerService;
use app\modules\main\models\BannerModel;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;


class BannerController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/banner/list",
     *     summary="banner列表",
     *     description="banner列表",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/BannerListResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post = \Yii::$app->request->post();

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = BannerService::getInstance()->getBannerList($post, $page, $page_size);

        $check_url = 'back/banner/audit'; //banner审核权限
        if (by::adminUserModel()->checkAuth($this->user_info, $check_url)) {
            $return['jurisdiction'] = 1; //有权限
        } else {
            $return['jurisdiction'] = 2; //无权限
        }
        //当前账号用户id
        $return['current_id'] = $this->user_info['id'];
        $return['list']       = Response::responseList($return['list'] ?? [], ['banner_version' => 'int']);
        CUtil::json_response(1, 'ok', $return);
    }





    /**
     * @OA\Post(
     *     path="/back/banner/save",
     *     summary="后台增改",
     *     description="后台增改",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/BannerSaveResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        $post = \Yii::$app->request->post();

        // 验证参数
        $form = new BannerModifyForm();
        $form->load($post, '');
        if (!$form->validate()) {
            // 错误信息
            $errors = $form->firstErrors;
            CUtil::json_response(-1, array_shift($errors));
        }

        $save = $form->toArray();
        list($status, $ret) = BannerService::getInstance()->save($save, $this->user_info);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $id    = $post['id'] ?? 0;
        $title = $post['title'] ?? "";

        CUtil::json_response(1, 'OK');
    }



    /**
     * @OA\Post(
     *     path="/back/banner/audit",
     *     summary="banner审核",
     *     description="banner审核",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="Banner图ID"),
     *          @OA\Property(property="check", type="integer", default="", description="审核（1通过 3未通过）"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAudit()
    {
        $id             = \Yii::$app->request->post('id', null);
        $check          = \Yii::$app->request->post('check', null);
        list($status, $ret) = BannerService::getInstance()->audit($id,$check);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $msg = ($check == BannerModel::STATUS['PASS']['CODE']) ? '通过' : '拒绝';
        (new SystemLogsModel())->record("{$msg}了banner,id为:{$id}的审批申请", RbacInfoModel::BANNER_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/banner/delete",
     *     summary="banner删除",
     *     description="banner删除",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="Banner图ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        $id             = \Yii::$app->request->post('id', null);
        list($status, $ret) = BannerService::getInstance()->delete($id);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("删除了banner,id为:{$id}", RbacInfoModel::BANNER_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/banner/sort",
     *     summary="banner排序",
     *     description="banner排序",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id","sort"},
     *          @OA\Property(property="id", type="integer", default="", description="Banner图ID"),
     *          @OA\Property(property="sort", type="integer", default="", description="排序")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSort()
    {
        $id             = \Yii::$app->request->post('id', null);
        $sort           = \Yii::$app->request->post('sort', null);
        list($status, $ret) = BannerService::getInstance()->sort($id,$sort);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("修改banner,id为:{$id} 的排序：{$sort}", RbacInfoModel::BANNER_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/banner/sub-audit",
     *     summary="提交、撤销审核",
     *     description="提交、撤销审核",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="Banner图ID"),
     *          @OA\Property(property="check", type="integer", default="", description="审核（0撤销 2提交）"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSubAudit(){
        $id             = \Yii::$app->request->post('id', null);
        $check          = \Yii::$app->request->post('check', null);
        list($status, $ret) = BannerService::getInstance()->subAudit($id,$check,$this->user_info);
        if(!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        $msg = ($check == BannerModel::STATUS['ING']['CODE']) ? '提交' : '撤销';
        (new SystemLogsModel())->record("{$msg}了banner,id为:{$id}的审批申请", RbacInfoModel::BANNER_MANAGER,$this->user_info['id']);
        CUtil::json_response(1,'OK');
    }



    /**
     * @OA\Post(
     *     path="/back/banner/release",
     *     summary="banner投放位置",
     *     description="banner投放位置",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionRelease()
    {
        $list = BannerService::getInstance()->release();
        CUtil::json_response(1,"OK",$list);
    }

    /**
     * @OA\Post(
     *     path="/back/banner/banner-version",
     *     summary="banner版本",
     *     description="banner版本",
     *     tags={"banner管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionBannerVersion()
    {
        $list = BannerService::getInstance()->bannerVersion();
        CUtil::json_response(1,"OK",$list);
    }
}
