<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\log\services\reg\RegBlackService;
use app\modules\log\services\warranty\WarrantyCardService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use Yii;
use yii\db\Exception;

class WarrantyCardController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/warranty-card/set-period",
     *     summary="设置电子保修卡保修时间",
     *     description="设置电子保修卡保修时间",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyCardSetPeriod"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionSetPeriod()
    {
        $post        = Yii::$app->request->post();
        $card_no = trim($post['card_no'] ?? '');
        $period_time = CUtil::uint($post['period_time'] ?? '');
        list($s,$msg) = WarrantyCardService::getInstance()->SaveWarrantyCard($post);
        if (!$s) {
            CUtil::json_response(-1, $msg);
        }
        $period_time = empty($period_time)?'':date('Y-m-d H:i:s',$period_time);
        $pre_period_time = empty($msg['pre_period_time'] ?? '') ? '':date('Y-m-d H:i:s',$msg['pre_period_time']);
        //写入日志
        (new SystemLogsModel())->record("保修卡号：{$card_no}设置保修卡时间：从{$pre_period_time} 改为 {$period_time}", RbacInfoModel::WARRANTY_CARD);

        CUtil::json_response(1, 'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-card/list",
     *     summary="电子保修卡记录",
     *     description="电子保修卡记录",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/WarrantyList"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/WarrantyCardResponse",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionList()
    {
        $post  = Yii::$app->request->post();
        $page =  CUtil::uint($post['page'] ?? 1);
        $page_size = CUtil::uint($post['page_size'] ?? 20);
        $list  = WarrantyCardService::getInstance()->GetList($post,$page,$page_size);
        $count = WarrantyCardService::getInstance()->GetListCount($post);
        CUtil::json_response(1, 'ok', ['list' => $list, 'count' => $count]);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-card/detail",
     *     summary="电子保修卡信息详情",
     *     description="电子保修卡信息详情",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"card_no"},
     *          @OA\Property(property="card_no", type="integer", default="", description="会员卡号")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        $card_no = Yii::$app->request->post('card_no') ?? '';

        list($s, $ret) = WarrantyCardService::getInstance()->getWarrantyDetail($card_no);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }



    /**
     * @OA\Post(
     *     path="/back/warranty-card/black-list",
     *     summary="电子保修卡黑名单",
     *     description="电子保修卡白名单",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="sn", type="string", default="", description="SN码"),
     *              @OA\Property(property="page", type="integer", default="", description="page"),
     *              @OA\Property(property="page_size", type="integer", default="", description="page_size"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionBlackList()
    {
        $post     = \Yii::$app->request->post();
        $post['mo_sn'] = trim($post['sn'] ?? '');
        unset($post['sn']);
        $input    = [
            'mo_sn'               => $post['mo_sn'],
        ];
        $page     = $post['page'] ?? 1;
        $pageSize = $post['page_size'] ?? 10;
        $data = RegBlackService::getInstance()->GetList($input,$page,$pageSize);
        CUtil::json_response(1, 'ok', ['list' => $data[1], 'count' => $data[0],'pages' => $data[2]]);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-card/black-save",
     *     summary="电子保修卡黑名单保存",
     *     description="电子保修卡黑名单保存",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="id", type="integer", default="", description="id 编辑时必传"),
     *              @OA\Property(property="sn", type="string", default="", description="SN码集合，多个用逗号隔开"),
     *              @OA\Property(property="type", type="integer", default="1", description="枚举，1：保修卡黑名单 2：电子保修卡黑名单"),
     *              @OA\Property(property="file", type="file",  description="上传文件"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionBlackSave()
    {
        $post     = \Yii::$app->request->post();
        $file     = $_FILES['file'] ?? "";
        $input    = [
            'sn'                  => trim($post['sn'] ?? ''),
            'id'                  => $post['id'] ?? 0, // 电子保修卡白名单id
            'type'                => $post['type'] ?? 1,
        ];
        if(empty($input['sn']) && empty($file)){
            CUtil::json_response(-1, 'SN码或文件不能为空');
        }

        list($s,$msg) = RegBlackService::getInstance()->BlackSaveWithFile($input,$file);
        if(!$s){
            CUtil::json_response(-1, $msg);
        }
        //写入日志
        (new SystemLogsModel())->record("电子保修卡黑名单添加编辑sn，{$input['id']}|{$input['sn']}", RbacInfoModel::WARRANTY_CARD,$this->user_info['id']);

        CUtil::json_response(1, 'ok', null);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-card/black-del",
     *     summary="电子保修卡黑名单删除",
     *     description="电子保修卡黑名单删除",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *              @OA\Property(property="ids", type="string", default="", description="ids 删除时必传,支持多个用逗号隔开，批量删除"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionBlackDel()
    {
        $post     = \Yii::$app->request->post();
        $ids      = trim($post['ids'] ?? '');
        if(empty($ids)){
            CUtil::json_response(-1, 'ids不能为空');
        }
        list($s,$msg) = RegBlackService::getInstance()->BlackDel($ids);
        if(!$s){
            CUtil::json_response(-1, $msg);
        }

        //写入日志
        (new SystemLogsModel())->record("电子保修卡黑名单批量删除，{$post['ids']}", RbacInfoModel::WARRANTY_CARD,$this->user_info['id']);

        CUtil::json_response(1, 'ok', null);
    }


    /**
     * @OA\Post(
     *     path="/back/warranty-card/black-template",
     *     summary="电子保修卡黑名单模板下载",
     *     description="电子保修卡黑名单模板下载",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionBlackTemplate()
    {
        $data = [
            'url'=>'https://wpm-cdn.dreame.tech/files/202407/批量手动添加产品注册黑名单-20240713.csv',
        ];
        CUtil::json_response(1,'ok',$data);
    }

    /**
     * @OA\Post(
     *     path="/back/warranty-card/upgrade",
     *     summary="电子保修卡升级Care+服务",
     *     description="电子保修卡升级Care+服务",
     *     tags={"电子保修卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUpgrade()
    {
        $post     = \Yii::$app->request->post();
        $card_no  = trim($post['card_no'] ?? '');
        if(empty($card_no)){
            CUtil::json_response(-1, 'card_no不能为空');
        }

        list($status, $m) = WarrantyCardService::getInstance()->upgradeCare($card_no);
        if(!$status){
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1,'ok',$m);
    }
}
