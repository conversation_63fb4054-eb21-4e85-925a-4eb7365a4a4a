<?php

namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\modules\back\forms\system\SystemDictTypeForm;
use app\modules\back\services\system\SystemDictTypeService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;
use OpenApi\Annotations as OA;

/**
 * 字典类型
 * 接口文档：https://doc.apipost.net/docs/detail/45575e29b0e0000?target_id=1160852035d018&locale=zh-cn
 * 文档密码：593336
 */
class DictTypeController extends CommController
{
    use ControllerTrait;

    /** @var SystemDictTypeService */
    private $service;

    public function __construct($id, $module, $config = [])
    {
        $this->service = SystemDictTypeService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * @OA\Post(
     *     path="/back/dict-type/index",
     *     summary="获取字典列表",
     *     description="获取字典列表",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="数据列表",
     *                  @OA\Items(
     *                      type="object",
     *                      @OA\Property(property="id", type="string", example="1", description="活动ID"),
     *                      @OA\Property(property="name", type="string", example="123", description="字典名称"),
     *                      @OA\Property(property="code", type="string", example="123", description="字典标识"),
     *                      @OA\Property(property="status", type="string", description="状态 0=禁用，1=正常"),
     *                      @OA\Property(property="remark", type="string", example="once", description="备注"),
     *                  )
     *              )
     *         )
     *     )
     * )
     */
    public function actionIndex()
    {
        $post = \Yii::$app->request->post();
        $this->success($this->service->getPageList($post));
    }

    /**
     * @OA\Post(
     *     path="/back/dict-type/create",
     *     summary="新增字典",
     *     description="新增字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="name", type="string", description="字典名称"),
     *                 @OA\Property(property="code", type="string", description="字典标识"),
     *                 @OA\Property(property="status", type="integer", description="状态 0=禁用，1=正常"),
     *                 @OA\Property(property="remark", type="string", description="备注"),
     *                 required={"name", "code"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", description="数据ID")
     *             )
     *         )
     *     )
     * )
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new SystemDictTypeForm();
            $form->setScenario('create');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $id, $msg) = $this->service->create($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success(['id' => (int)$id], $msg);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-type/update",
     *     summary="更新字典",
     *     description="更新字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="字典ID"),
     *                 @OA\Property(property="name", type="string", description="字典名称"),
     *                 @OA\Property(property="code", type="string", description="字典标识"),
     *                 @OA\Property(property="status", type="integer", description="状态 0=禁用，1=正常"),
     *                 @OA\Property(property="remark", type="string", description="备注"),
     *                 required={"id", "name", "code"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new SystemDictTypeForm();
            $form->setScenario('update');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $msg) = $this->service->update($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-type/delete",
     *     summary="删除字典",
     *     description="删除字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="ids", type="string", description="字典ID，多个ID用英文逗号分隔"),
     *                 required={"ids"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                return $this->error('缺少ids字段');
            }
            $ids = explode(',', (string)$ids);
            $ids = array_unique($ids);

            list($status, $msg) = $this->service->delete($ids);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-type/change-status",
     *     summary="更新状态",
     *     description="更新状态",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="字典ID"),
     *                 @OA\Property(property="status", type="integer", description="状态值"),
     *                 required={"id", "status"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionChangeStatus()
    {
        try {
            $post = \Yii::$app->request->post();
            list($status, $msg) = $this->service->changeStatus($post);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}