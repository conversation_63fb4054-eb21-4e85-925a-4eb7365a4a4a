<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\wares\services\cocreate\CoCreateUserService;
use yii\db\Exception;

class CoCreateUserController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/co-create-user/list",
     *     summary="后台-获取合作空间人员列表",
     *     description="后台-获取合作空间人员列表",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateUserListRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionList()
    {
        $post  = \Yii::$app->request->post();
        $input = [
            'uid'       => trim($post['uid'] ?? ""),
            'nick'        => trim($post['nick'] ?? ""),
            'phone'        => trim($post['phone'] ?? ""),
        ];

        $page      = $post['page'] ?? 1;
        $page_size = $post['page_size'] ?? 20;

        $return = CoCreateUserService::getInstance()->GetList($input,$page,$page_size);

        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-user/add",
     *     summary="后台-添加共创空间人员数据",
     *     description="后台-添加共创空间人员数据",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateUserAddRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionAdd()
    {
        $file = $_FILES['file'] ?? "";
        //用户卡号或者电话号码
        $userPhone = \Yii::$app->request->post('phone','');
        list($status,$data) = CoCreateUserService::getInstance()->batchAdd($userPhone,$file,$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1,'ok',$data);
    }


    /**
     * @OA\Post(
     *     path="/back/co-create-user/del",
     *     summary="后台-共创空间人员删除",
     *     description="后台-共创空间人员删除",
     *     tags={"共创空间"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CoCreateUserDelRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     * @throws Exception
     */
    public function actionDel()
    {
        $post = \Yii::$app->request->post();
        $ids  = $post['ids'] ?? '';
        if (empty($ids)) CUtil::json_response(-1, 'ids 不能为空');
        $return = CoCreateUserService::getInstance()->Del($ids);
        $return == false && CUtil::json_response(-1, '删除失败');
        CUtil::json_response(1, 'ok', $return);
    }

}
