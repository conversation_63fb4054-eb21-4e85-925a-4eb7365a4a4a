<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/14
 * Time: 15:32
 */
namespace app\modules\back\controllers;

use app\components\ErpNew;
use app\constants\RespStatusCodeConst;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\services\SmsService;
use app\modules\goods\models\GmainModel;
use app\modules\main\services\OrderService;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use yii\db\Exception;

class  OrderController extends CommController
{

    /**
     * 运费配置
     */
    public function actionFreight()
    {
        $tab        = \Yii::$app->request->post('tab', 1);
        $aData      = by::model('OfreightModel', 'goods')->GetList($tab);

        CUtil::json_response(1, "OK", $aData);
    }

    /**
     * 运费配置增改
     */
    public function actionFreightSave()
    {
        $post              = \Yii::$app->request->post();
        list($status,$ret) = by::model('OfreightModel', 'goods')->SaveLog($post);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        $id    = $post['id']   ?? 0;
        $msg   = $id ? "修改了运费配置ID：{$id}" : "新增了运费配置";
        $msg  .= ",内容：".json_encode($post,JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::FREIGHT_CFG, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }

    /**
     * 运费配置删除
     */
    public function actionFreightDel()
    {
        $id                = \Yii::$app->request->post('id', 0);

        list($status,$ret) = by::model('OfreightModel', 'goods')->Del($id);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        $msg   = "删除运费配置ID：{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::FREIGHT_CFG, $this->user_info['id']);

        CUtil::json_response(1,'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/order/list",
     *     summary="订单列表",
     *     description="订单列表",
     *     tags={"订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="status", type="integer", default="-1", description="订单状态"),
     *                         @OA\Property(property="year", type="integer", default="", description="年份"),
     *                         @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *                         @OA\Property(property="order_st", type="integer", default="", description="订单时间范围开始"),
     *                         @OA\Property(property="order_ed", type="integer", default="", description="订单时间范围结束"),
     *                         @OA\Property(property="user_iden", type="string", default="0", description="用户Id"),
     *                         @OA\Property(property="source", type="integer", default="0", description="订单子类"),
     *                         @OA\Property(property="live_mark", type="string", default="0", description="直播来源"),
     *                         @OA\Property(property="type", type="integer", default="0", description="订单类型"),
     *                         @OA\Property(property="source_code", type="string", default="", description="渠道"),
     *                         @OA\Property(property="store", type="string", default="", description="门店"),
     *                         @OA\Property(property="sku", type="string", default="", description="商品编号"),
     *                         @OA\Property(property="goods_name", type="string", default="", description="商品名称"),
     *                         @OA\Property(property="order_tid", type="string", default="", description="订单流水号"),
     *                         @OA\Property(property="p_sources", type="string", default="", description="平台来源"),
     *                         @OA\Property(property="is_sync_erp", type="integer", default="", description="E3+同步结果")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = 20;

        // 所有订单类型（不包括先试后买）
        $orderTypeArray = [
                by::Omain()::USER_ORDER_TYPE['COMMON'],
                by::Omain()::USER_ORDER_TYPE['TAIL'],
                by::Omain()::USER_ORDER_TYPE['DEPOSIT'],
                by::Omain()::USER_ORDER_TYPE['POINT'],
                by::Omain()::USER_ORDER_TYPE['OTN'],
                by::Omain()::USER_ORDER_TYPE['INTERNAL'],
                by::Omain()::USER_ORDER_TYPE['GROUP_PURCHASE'],
        ];

        // 将订单类型转换为逗号分隔的字符串
        $orderTypeString = implode(",", $orderTypeArray);

        // 判断POST请求中的type
        $order_type  = !empty($post['type']) ? $post['type'] : $orderTypeString;
        $year        = $post['year'] ?? date("Y");
        $status      = $post['status'] ?? -1;
        $user_iden   = $post['user_iden'] ?? "";
        $order_no    = $post['order_no'] ?? "";
        $order_tid   = $post['order_tid'] ?? "";
        $source      = $post['source'] ?? -1;
        $p_sources   = $post['p_sources'] ?? "-1";
        $live_mark   = $post['live_mark'] ?? '';
        $store       = $post['store'] ?? '';
        $source_code = $post['source_code'] ?? '';
        $goods_name  = $post['goods_name'] ?? '';
        $sku         = $post['sku'] ?? '';
        $is_sync_erp = $post['is_sync_erp'] ?? -1;
        $p_sources   = empty(intval($p_sources)) ? '-1' : $p_sources;
        $order_time  = [
                'st' => $post['order_st'] ?? 0,
                'ed' => $post['order_ed'] ?? 0,
        ];

        $return['list'] = [];

        if (!in_array($status, by::Omain()::ORDER_STATUS)) {
            $return['pages']    = 1;
            CUtil::json_response(1, "OK", $return);
        }

        //无数据
        if($year < by::Omain()::DB_TIMEZONE['ST'] || $year > by::Omain()::DB_TIMEZONE['ED']) {
            $return['pages']    = 1;
            CUtil::json_response(1, "OK", $return);
        }

        $mOuser = by::Ouser();

        $rets = by::Omain()->GetList($year, $order_no, $order_tid, $user_iden, $status, $source, $order_time, $p_sources, $live_mark, $order_type, $store, $source_code, $goods_name, $sku,$is_sync_erp, $page, $page_size);

        foreach ($rets as $ret) {
            $info              = $mOuser->CommPackageInfo($ret['user_id'],$ret['order_no'],true,false,false,false,true,false,true,true);
            $oPayInfo = byNew::OPayModel()->GetOneInfo($ret['order_no']);

            $info = [
                    'order_no'             => $info['order_no'],
                    'order_tid'            => $oPayInfo['tid'], // 支付单号
                    'user_id'              => $info['user_id'],
                    'status'               => $info['status'],
                    'source'               => $ret['source'],
                    'platform_source'      => $ret['platform_source'],
                    'store'                => $ret['store'] ?? '',
                    'is_sync_erp'          => $ret['is_sync_erp'] ?? '',
                    'platform_source_name' => by::userExtend()->platformSourceConfig($ret['platform_source'] ?? 0),
                    'ctime'                => $info['ctime'],
                    'user'                 => $info['user'],
                    'goods'                => $info['goods'],
                    'coupon_id'            => $info['coupon_id'],
                    'deposit_order_no'     => $info['deposit_order_no'] ?? '',
                    'user_order_type'      => CUtil::uint($info['user_order_type'] ?? '')
            ];

            //获取直播属性
            $osourceMInfo      = by::osourceM()->getInfoByOrderNo($ret['user_id'] ?? '', $ret['order_no'] ?? '');
            $euid              = $osourceMInfo['euid'] ?? '';
            $liveMark          = $osourceMInfo['live_mark'] ?? '';
            $info['live_mark'] = $liveMark;
            $info['euid']      = $euid;
            $sourceInfo        = by::SourceConfig()->getParamByCode($euid);
            $info['param']     = $sourceInfo['param'] ?? '';
        
            $return['list'][]  = $info;
        }

        if ($page == 1) {
            $count           = by::Omain()->GetListCount($year, $order_no, $order_tid, $user_iden, $status, $source, $order_time, $p_sources, $live_mark, $order_type, $store, $source_code, $goods_name, $sku,$is_sync_erp);
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws Exception
     * 订单详情
     */
    public function actionInfo()
    {
        $user_id  = \Yii::$app->request->post('uid', 0);
        $order_no = \Yii::$app->request->post('order_no', 0);

        $info     = by::Ouser()->CommPackageInfo($user_id,$order_no,true,true,true,false,true,false,true,true);

        $express_data = [];
        if (! empty( $info['mail'])) {
            if (! empty($info['mail']['mail_no']) && ! empty($info['mail']['express_code']) && ! empty($info['mail']['express_name'])) {
                $express_data[] = $info['mail'];
            }
        }

        if (! empty($info['goods'])) {
            foreach ($info['goods'] as $goods_item) {
                if ($goods_item['is_third'] == 1 && ! empty($goods_item['mail_no']) && ! empty($goods_item['express_code']) && ! empty($goods_item['express_name'])) {
                    $express_data[] = [
                        'mail_no'       => $goods_item['mail_no'],
                        'express_name'  => $goods_item['express_name'],
                        'express_code'  => $goods_item['express_code'] ?? '',
                    ];
                }
            }
        }
        
        $info['express_data'] = $express_data;
        
        //支付单号
        $tid      = '';
        $pay_type = '0';
        if ($info['status'] > by::Omain()::ORDER_STATUS['CANCELED']) {
            $pay_log    = by::model('OPayModel','goods')->GetOneInfo($order_no);
            $tid        = $pay_log['tid'] ?? '';
            $pay_type   = $pay_log['pay_type'] ?? '0';
        }
        $info['tid']    = $tid;
        $info['pay_type'] = $pay_type; // 支付类型： 1、2、3微信，4支付宝，6先用后付，0、99无

        //手机号
        $info['user']['phone']          = by::Phone()->GetPhoneByUid($user_id);
        //来源
        $source                         = by::userExtend()->getUserExtendInfo($user_id);

        list($info['user']['source'])   = !empty($source['source']) ? by::userExtend()->sourceConfig($source['source']) : '其他';

        //导购
        $info['guide_id']               = by::Osource()->getOuidByOrder($user_id,$order_no);

        //导购员信息
        $info['guide_user'] =  empty($info['guide_id']) ? [] : by::guide()->getGuideInfo($info['guide_id']) ?? [];

        //推荐人
        $or_info      = by::osourceR()->getInfoByOrderNo($user_id, $order_no);
        $info['r_id'] = $or_info['r_id'] ?? 0;

        //订单渠道来源
        $or_m_info     = by::osourceM()->getInfoByOrderNo($user_id, $order_no);
        $info['union'] = $or_m_info['union'] ?? '';
        $info['euid']  = $or_m_info['euid'] ?? '';
        //门店信息
        $info['store'] = $source['store'] ?? '';
        //是否直播
        //获取直播属性
        $osourceMInfo = by::osourceM()->getInfoByOrderNo($user_id,$order_no);
        $info['live_mark'] = $osourceMInfo['live_mark'] ?? '';
        $arr = [
            'cid'    => 'tm',
            'city'   => 'tm',
            'aid'    => 'tm',
            'area'   => 'tm',
            'detail' => 'tm',
            'phone'  => 'tm',
        ];
        $aOpay      = by::model('OPayModel','goods')->GetOneInfo($order_no);
        $info['payment_plan'] = by::oPay()->getPayPlan($aOpay['payment_plan'] ?? 'NO_INST');//分期支付计划
        if(!$this->viewSensitive){
            $info['address'] = Response::responseList($info['address']??[], $arr);
            $info['user'] = Response::responseList($info['user']??[], $arr);
        };

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @OA\Post(
     *     path="/back/order/refund-list",
     *     summary="订单退款列表",
     *     description="订单退款列表",
     *     tags={"订单"},
     *     security={
     *         {"userid": {}},
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="status", type="integer", default=-1, description="订单状态"),
     *                         @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *                         @OA\Property(property="order_st", type="integer", default=0, description="订单时间范围开始"),
     *                         @OA\Property(property="order_ed", type="integer", default=0, description="订单时间范围结束"),
     *                         @OA\Property(property="user_iden", type="string", default="", description="用户Id"),
     *                         @OA\Property(property="goods_name", type="string", default="", description="商品名称")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             required={"iRet", "sMsg", "data"},
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *         )
     *     )
     * )
     */
    public function actionRefundList()
    {
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = 20;

        $status     = $post['status']     ?? -1;
        $user_iden  = $post['user_iden']  ?? "";
        $order_no   = $post['order_no']   ?? "";
        $goods_name = $post['goods_name'] ?? '';
        $order_time = [
            'st' => $post['order_st'] ?? 0,
            'ed' => $post['order_ed'] ?? 0,
        ];
        $return['list'] = [];

        if (!in_array($status, by::OrefundMain()::STATUS)) {
            CUtil::json_response(1, "OK", []);
        }

        $rets = by::OrefundMain()->GetList($order_no, $user_iden, $status, $order_time,$goods_name,$page, $page_size);
        foreach ($rets as $ret) {

            $info              = by::Orefund()->CommPackageInfo($ret['user_id'],$ret['refund_no'],false,true);
            $source            = by::Omain()->getSourceByNo($info['order_no']);
            list($_, $ostatus) = by::Omain()->SplitOrderStatus($info['ostatus']);

            $info = [
                'order_no'        => $info['order_no'],
                'refund_no'       => $info['refund_no'],
                'user_id'         => $info['user_id'],
                'status'          => $info['status'],
                'ostatus'         => $ostatus,
                'mail_no'         => $info['mail_no'],
                'source'          => $source,
                'ctime'           => $info['ctime'],
                'goods'           => $info['goods'],
                'price'           => $info['price'],
                'rback'           => $info['rback'],
                'm_type'          => $info['m_type'] ?? 0,
                'user_order_type' => $info['user_order_type'],
            ];

            $rback          = $info['status'] == by::OrefundMain()::STATUS['P_REJECT'] ? 1 : $info['rback'];
            $info['rback']  = $rback;

            $return['list'][]  = $info;
        }

        $count           = by::OrefundMain()->GetListCount($order_no, $user_iden, $status,$order_time,$goods_name);
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws Exception
     * 退款详情
     */
    public function actionRefundInfo()
    {
        $user_id    = \Yii::$app->request->post('uid', 0);
        $refund_no  = \Yii::$app->request->post('refund_no', 0);

        $info       = by::Orefund()->CommPackageInfo($user_id,$refund_no,true,true);

        if (!empty($info)) {
            list($_, $info['ostatus'])  = by::Omain()->SplitOrderStatus($info['ostatus']);
        }
        $arr = [
            'cid'    => 'tm',
            'city'   => 'tm',
            'aid'    => 'tm',
            'area'   => 'tm',
            'detail' => 'tm',
            'phone'  => 'tm',
        ];
        if(!$this->viewSensitive){
            $info['user'] = Response::responseList($info['user']??[], $arr);
        };

        CUtil::json_response(1, 'ok', $info);
    }

    /**
     * @throws Exception
     * 修改退款物流单号
     */
    public function actionRefundExpress()
    {
        $post           = \Yii::$app->request->post();
        $uid            = $post['uid']          ?? 0;
        $refund_no      = $post['refund_no']    ?? '';
        $mail_no        = $post['mail_no']      ?? '';
        $express_name   = $post['express_name'] ?? '';
        $express_code   = $post['express_code'] ?? '';

        if (!empty($express_name) && empty($express_code)) {
            CUtil::json_response(-1, '参数错误');
        }

        $save           = [];
        !empty($mail_no)        && $save['mail_no']      = $mail_no;
        !empty($express_name)   && $save['express_name'] = $express_name;
        !empty($express_code)   && $save['express_code'] = $express_code;

        list($s, $m)    = by::Orefund()->UpdateData($uid, $refund_no, $save);
        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');

    }

    /**
     * @OA\Post(
     *     path="/back/order/raudit",
     *     summary="ORDER 退款（先试后买完结订单）",
     *     description="订单退款（先试后买完结订单）",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="refund_no", type="string",description="退款单号"),
     *              @OA\Property(property="order_no", type="string",description="订单号"),
     *              @OA\Property(property="status", type="string",description="审核状态"),
     *              @OA\Property(property="a_reason", type="string",description="退款原因"),
     *              @OA\Property(property="is_complete", type="boolean",description="是否守约，先试后买订单必填"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionRaudit()
    {
        $post           = \Yii::$app->request->post();

        list($lock,$msg) = CUtil::payLock(0);
        if($lock){
            CUtil::json_response(-1,$msg);
        }

        list($status, $msg) = by::OrefundMain()->Audit($post);
        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("退款订单：{$post['refund_no']}修改状态：{$post['status']}", RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/order/force-raudit",
     *     summary="ORDER 强制退款（先试后买完结订单）",
     *     description="订单强制退款（先试后买完结订单）",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="refund_no", type="string",description="退款单号"),
     *              @OA\Property(property="order_no", type="string",description="订单号"),
     *              @OA\Property(property="status", type="string",description="审核状态"),
     *              @OA\Property(property="a_reason", type="string",description="退款原因"),
     *              @OA\Property(property="is_complete", type="boolean",description="是否守约，先试后买订单必填"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionForceRaudit()
    {
        $post          = \Yii::$app->request->post();
        list($lock,$msg) = CUtil::payLock(0);
        if($lock){
            CUtil::json_response(-1,$msg);
        }
        $post['force'] = 1;
        list($status, $msg) = by::OrefundMain()->Audit($post);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("强制-退款订单：{$post['refund_no']}修改状态：{$post['status']}", RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws Exception
     * 退单
     */
    public function actionRback()
    {
        $post           = \Yii::$app->request->post();
        $user_id        = $post['uid']       ?? 0;
        $refund_no      = $post['refund_no'] ?? '';

        list($status, $msg) = by::OrefundMain()->Rback($user_id, $refund_no);
        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * 导购订单列表
     */
    public function actionSlist()
    {
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = 20;

        $year       = $post['year']       ?? date("Y");
        $user_iden  = $post['user_iden']  ?? "";
        $name       = $post['name']  ??  "";
        $job_no     = $post['job_no']  ??  "";
        if($job_no && empty($name)) $name = $job_no;
        $order_no   = $post['order_no']   ?? "";
        $order_time = [
            'st' => $post['order_st'] ?? 0,
            'ed' => $post['order_ed'] ?? 0,
        ];

        $return['list'] = [];

        //无数据
        if($year < by::Omain()::DB_TIMEZONE['ST'] || $year > by::Omain()::DB_TIMEZONE['ED']) {
            $return['pages']    = 1;
            CUtil::json_response(1, "OK", $return);
        }

        $mOuser = by::Ouser();

        $rets = by::Osource()->GetAdmList($year,$order_no, $user_iden, $order_time, $name, $page, $page_size);
        foreach ($rets as $ret) {

            //导购员信息
            $user           = by::users()->getOneByUid($ret['ouid']);
            //手机信息
            $phone          = by::Phone()->GetPhoneByUid($ret['ouid']);

            $info           = $mOuser->GetInfoByOrderId($ret['user_id'],$ret['order_no']);

            //所属门店
            $s_info         = by::guide()->getGuideInfo($ret['ouid']);

            //订单状态
            list(,$status)  = by::Omain()->SplitOrderStatus($info['status']);

            $info = [
                'ouid'     => $ret['ouid'],
                'onick'    => $user['nick'],
                'ophone'   => $phone,
                'user_id'  => $ret['user_id'],
                'store'    => $s_info['store'] ?? '未知',
                'job_no'   => $s_info['job_no'] ?? '未知',
                'order_no' => $info['order_no'],
                'status'   => $status,
                'price'    => by::Gtype0()->totalFee($info['price'], 1),
                'ctime'    => $info['ctime'],
            ];
            $return['list'][]  = $info;
        }

        if ($page == 1) {
            $count           = by::Osource()->GetAdmListCount($year,$order_no, $user_iden, $order_time, $name);
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }

        CUtil::json_response(1, 'ok', $return);
    }



    /**
     * @OA\Post(
     *     path="/back/order/sync-e3",
     *     summary="订单手动同步e3+",
     *     description="订单手动同步e3+",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *           required = {"order_no","user_id"},
     *           @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *           @OA\Property(property="user_id", type="integer", default="", description="用户ID")
     *         )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSyncE3()
    {
        $request = \Yii::$app->request;
        $orderNo = $request->post('order_no');
        $userId  = $request->post('user_id');

        // 参数有效性验证
        if (empty($orderNo) || empty($userId)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, '订单号和用户ID不能为空');
        }

        try {
            // ERP系统调用
            $erpService = ErpNew::factory();
            list($status, $result) = $erpService->runFunc('addOrder', [
                    'user_id'  => $userId,
                    'order_no' => $orderNo,
            ]);

            // 处理ERP系统返回结果
            if (!$status) {
                // 保存同步结果
                by::Omain()->updateDataByOrderNo($orderNo, ['is_sync_erp'=>2]);
                CUtil::json_response(RespStatusCodeConst::ERROR_CODE, $result);
            }

            (new SystemLogsModel())->record("手动同步e3+订单：{$orderNo}", RbacInfoModel::ORDER_MANAGER);

            // 保存同步结果
            by::Omain()->updateDataByOrderNo($orderNo, ['is_sync_erp'=>1]);
            // 成功响应
            CUtil::json_response(RespStatusCodeConst::SUCCESS_CODE);
        } catch (\Exception $e) {
            // 异常处理及日志记录
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug("{$error}", 'error.sync-e3+');
            CUtil::json_response(RespStatusCodeConst::ERROR_CODE);
        }
    }
    /**
     * @OA\Post(
     *     path="/back/user/send-msg",
     *     summary="给用户发短信",
     *     description="给用户发短信",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *           required = {"refund_no","user_id"},
     *           @OA\Property(property="refund_no", type="string", default="", description="退款订单号"),
     *           @OA\Property(property="user_id", type="integer", default="", description="用户ID")
     *         )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSendMsg(){
        $request = \Yii::$app->request;
        $orderNo = $request->post('refund_no');
        $userId  = $request->post('user_id');

        // 参数有效性验证
        if (empty($orderNo) || empty($userId)) {
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, '订单号和用户ID不能为空');
        }
        $info = by::Orefund()->GetInfoByRefundNo($userId,$orderNo);
        if (empty($info)){
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, '退款订单不存在');
        }
        if ($info['mail_no'] != '' || $info['status'] != 1000){
            CUtil::json_response(RespStatusCodeConst::PARAM_ERROR_CODE, '订单状态不对或物流单号不为空');
        }
        
        list($status, $res) = SmsService::getInstance()->sendSmsByRefundOrder($orderNo,$userId);
        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::SMS_SEND_ERROR_CODE, $res);
        }
        CUtil::json_response(1, 'ok');

    }
    /**
     * @OA\Post(
     *     path="/back/order/apply-refund",
     *     summary="后台申请退款",
     *     description="后台申请退款",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *           required = {"order_no","user_id"},
     *           @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *           @OA\Property(property="user_id", type="integer", default="", description="用户ID")
     *         )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionApplyRefund(){
        $post           = \Yii::$app->request->post();
        $order_no       = $post['order_no'] ?? '';
        if(empty($order_no)){
            CUtil::json_response(-1, "订单编号必传");
        }

        $user_id        = $post['user_id'] ?? 0;
        if(empty($user_id)){
            CUtil::json_response(-1, "用户ID必传");
        }
        $adminUserId = by::adminUserModel()->getUserIDFromSession();
        $mOgoods = by::Ogoods();
        // 订单商品详情
        $oGoods = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);
        $oGoods = array_column($oGoods, 'status', 'id');
        $og_ids = array_keys($oGoods);
        if (empty($og_ids)) {
            CUtil::json_response(-1,'订单号和用户ID不匹配或者订单已发起退款');
        }

        $arr = [
            'order_no' => $order_no,
            'user_id'  => $user_id,
            'm_type'   => 2,
            'r_type'   => 9,
            'describe' => '兑换券订单，由客服创建退款订单，客服ID为：' . $adminUserId,
            'og_ids'   => json_encode($og_ids, 320),
            'images'   => '',
        ];

        list($status, $msg) = by::OrefundMain()->ApplyRefund($user_id, $arr, false);
        if(!$status) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("兑换券订单，由客服创建退款订单：{$post['order_no']}|客服ID：".$adminUserId, RbacInfoModel::ORDER_MANAGER);

        CUtil::json_response(1, 'ok');
    }
    
    /**
     * @OA\Post(
     *     path="/back/order/manual-delivery",
     *     summary="后台手动发货",
     *     description="后台手动发货",
     *     tags={"订单"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *           required = {"order_no","user_id"},
     *           @OA\Property(property="order_no", type="string", default="", description="订单号"),
     *           @OA\Property(property="ogid", type="integer", default="", description="订单商品ID"),
     *           @OA\Property(property="delivery_type", type="integer", default="", description="发货类型 1=物流发货"),
     *           @OA\Property(property="express_no", type="integer", default="", description="物流单号"),
     *           @OA\Property(property="express_code", type="integer", default="", description="物流公司编码"),
     *           @OA\Property(property="express_name", type="integer", default="", description="物流名称")
     *         )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionManualDelivery()
    {
        try {
            $post = \Yii::$app->request->post();
            $service = OrderService::getInstance();
            $service->manualDelivery($post);
            CUtil::json_response(1, 'ok');
        } catch (BusinessException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Throwable $e) {
            CUtil::json_response(-1, '发货失败');
        }
        
    }
}
