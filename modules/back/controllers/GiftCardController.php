<?php


namespace app\modules\back\controllers;


namespace app\modules\back\controllers;

use app\exceptions\GiftCardGoodsException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\giftcard\CardGoodsAddForm;
use app\modules\back\forms\giftcard\CardGoodsEditForm;
use app\modules\back\forms\giftcard\CardResourceDisCardForm;
use app\modules\back\forms\giftcard\CardResourceListForm;
use app\modules\main\services\GiftCardService;
use app\modules\rbac\controllers\CommController;
use app\modules\wares\services\GiftCard\GiftCardGoodsResourceService;
use app\modules\wares\services\GiftCard\GiftCardGoodsService;
use app\modules\wares\services\GiftCard\GiftCardResourcesService;
use app\modules\wares\services\GiftCard\GiftCardsService;
use PhpOffice\PhpSpreadsheet\IOFactory;

class GiftCardController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/gift-card/card-list",
     *     summary="礼品卡列表",
     *     description="礼品卡列表",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/GiftCardListResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardList()
    {
        $post      = \Yii::$app->request->post();
        $page      = intval($post['page'] ?? 1);
        $page_size = intval($post['page_size'] ?? 20);
        list($status, $ret) = GiftCardsService::getInstance()->getGiftCardList($post, $page, $page_size);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'OK', $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-info",
     *     summary="礼品卡详情",
     *     description="礼品卡详情",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="礼品卡ID"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardInfo()
    {
        $id = \Yii::$app->request->post('id', '');
        list($status, $ret) = GiftCardsService::getInstance()->getGiftCardInfo($id);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'OK', $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/gift-card/card-save",
     *     summary="礼品卡保存",
     *     description="礼品卡保存",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/GiftCardSaveResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardSave()
    {
        $post = \Yii::$app->request->post();
        list($status, $ret) = GiftCardsService::getInstance()->save($post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, '保存成功');
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-audit",
     *     summary="礼品卡审核",
     *     description="礼品卡审核",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="礼品卡ID"),
     *          @OA\Property(property="status", type="string", default="", description="审核状态：AWAIT_AUDIT 待审核 PASS_AUDIT 审核通过 REFUSE_AUDIT 审核拒绝"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardAudit()
    {
        $post   = \Yii::$app->request->post();
        $status = $post['status'] ?? '';
        $id     = $post['id'] ?? '';
        list($status, $ret) = GiftCardsService::getInstance()->audit($id, $status);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, '审核成功');
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-goods-list",
     *     summary="卡商品列表",
     *     description="卡商品列表",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="page", type="integer", default="1", description="页数"),
     *          @OA\Property(property="page_size", type="integer", default="20", description="每页大小"),
     *          @OA\Property(property="name", type="string", default="", description="商品名称"),
     *          @OA\Property(property="sku", type="string", default="", description="商品编码"),
     *          @OA\Property(property="type", type="int", default="", description="卡类型：1 实体卡 2 虚拟卡"),
     *          @OA\Property(property="status", type="int", default="", description="卡状态： 1 上架 2 下架"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/GiftCardGoodsList",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardGoodsList()
    {
        // 获取参数
        $params = \Yii::$app->request->post();
        $page = $params['page'] ?? 1;
        $pageSize = $params['page_size'] ?? 20;

        // 查询数据
        $list = GiftCardGoodsService::getInstance()->getCardGoodsList($params, $page, $pageSize);
        CUtil::json_response(1, "ok", $list);
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-goods-detail",
     *     summary="卡商品详情",
     *     description="卡商品详情",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="1", description="卡商品id"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/GiftCardGoodsDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardGoodsDetail()
    {
        $params = \Yii::$app->request->post();
        $id = $params['id'] ?? 0;
        if (empty($id)) {
            CUtil::json_response(-1, "缺少参数商品ID");
        }
        $data = GiftCardGoodsService::getInstance()->getCardGoodsDetail($id);
        CUtil::json_response(1, "ok", $data);
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-goods-save",
     *     summary="添加、编辑卡商品",
     *     description="添加卡商品",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id","sku","name","web_name","type","images","cover_image","start_time","end_time","price","status","card_resource"},
     *          @OA\Property(property="id", type="integer", default="", description="ID"),
     *          @OA\Property(property="sku", type="string", default="", description="编号"),
     *          @OA\Property(property="name", type="string", default="", description="名称"),
     *          @OA\Property(property="web_name", type="string", default="", description="外显名称"),
     *          @OA\Property(property="type", type="integer", default="", description="卡类型：1 实体卡 2 虚拟卡"),
     *          @OA\Property(property="cover_image", type="string", default="", description="封面图片"),
     *          @OA\Property(property="images", type="string", default="", description="主图片，多张逗号分隔"),
     *          @OA\Property(property="start_time", type="string", default="", description="开始时间"),
     *          @OA\Property(property="end_time", type="string", default="", description="结束时间"),
     *          @OA\Property(property="price", type="string", default="", description="价格"),
     *          @OA\Property(property="status", type="string", default="", description="状态：1 上架 2 下架"),
     *          @OA\Property(property="card_resource", type="string", default="", description="资源：JSON格式的数据"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionCardGoodsSave()
    {
        // 获取参数
        $params = \Yii::$app->request->post();

        // 添加条件
        $form = new CardGoodsAddForm();
        $form->load($params, '');

        // 验证条件
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        try {
            // 添加数据
            GiftCardGoodsService::getInstance()->saveCardGoods($form->toArray());
            // 礼品卡添加
            CUtil::json_response(1, "ok");
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.gift-card');
            CUtil::json_response(-1, "添加礼品卡失败");
        }
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/card-goods-resource-upload",
     *     summary="导入卡商品资源",
     *     description="导入卡商品资源",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id","file"},
     *          @OA\Property(property="id", type="integer", default="", description="ID"),
     *          @OA\Property(property="file", type="file", description="卡资源"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionCardGoodsResourceUpload()
    {
        // 校验参数
        $goods_id = \Yii::$app->request->post('goods_id');
        if (!$goods_id) {
            CUtil::json_response(-1, "缺少参数商品ID");
        }

        $file = $_FILES['file'] ?? null;
        if (!$file) {
            CUtil::json_response(-1, "未上传文件");
        }

        // 处理数据
        $spreadsheet = IOFactory::load($file['tmp_name']);
        $sheet = $spreadsheet->getActiveSheet();
        $data = [];
        foreach ($sheet->getRowIterator() as $index => $row) {
            if ($index == 1) continue;
            // 读取行数据
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false); // 获取所有单元格，即使它们没有值
            $rowData = [];
            foreach ($cellIterator as $cell) {
                $val = $cell->getValue();
                if (empty($val)) { // 判空
                    CUtil::json_response(-1, "文件内容存在空值");
                }
                $rowData[] = $val;
            }
            if (count($rowData) != 2) {
                CUtil::json_response(-1, "文件内容格式错误");
            }
            $data[] = $rowData;
        }

        try {
            // 插入卡号
            GiftCardGoodsResourceService::getInstance()->addCards($goods_id, $data);
            CUtil::json_response(1, "ok");
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.gift-card');
            CUtil::json_response(-1, "插入卡号失败");
        }

    }

    /**
     * @OA\Post(
     *     path="/back/gift-card/card-goods-resource-generate",
     *     summary="生成卡商品资源",
     *     description="生成卡商品资源",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id","num"},
     *          @OA\Property(property="id", type="integer", default="", description="ID"),
     *          @OA\Property(property="num", type="integer", description="生成卡资源数量"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionCardGoodsResourceGenerate()
    {
        // 校验参数
        $goods_id = \Yii::$app->request->post('goods_id');
        if (!$goods_id) {
            CUtil::json_response(-1, "缺少参数商品ID");
        }

        // 防止重复提交
        list($anti) = by::model("CommModel", MAIN_MODULE)->ReqAntiConcurrency(0, $goods_id, 3, "EX");
        if (!$anti) {
            CUtil::json_response(-1, "操作频繁");
        }

        $num = \Yii::$app->request->post('num');
        $num = intval($num);
        if ($num <= 0 || $num > 10000) {
            CUtil::json_response(-1, "数量范围为1-10000");
        }

        try {
            $data = GiftCardGoodsResourceService::getInstance()->generateCardResources('DM', $num);
            // 插入卡号
            GiftCardGoodsResourceService::getInstance()->addCards($goods_id, $data);
            CUtil::json_response(1, "ok");
        } catch (GiftCardGoodsException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::debug($e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine(), 'err.gift-card');
            CUtil::json_response(-1, "插入卡号失败");
        }

    }
    
    /**
     * @OA\Post(
     *     path="/back/gift-card/card-resources-list",
     *     summary="礼品卡资源列表",
     *     description="礼品卡资源列表",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/CardResourceListResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionCardResourcesList()
    {
        $params = \Yii::$app->request->post();


        // 添加条件
        $form = new CardResourceListForm();
        $form->load($params, '');

        // 验证条件
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        $page     = intval($params['page'] ?? 1);
        $pageSize = intval($params['page_size'] ?? 20);
        list($status, $ret) = GiftCardResourcesService::getInstance()->getCardResourcesList($params, $page, $pageSize);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        //卡密加密
        $ret['list'] = Response::responseList($ret['list'], ['card_password' => 'tm']);
        CUtil::json_response(1, '获取成功', $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/gift-card/discard-resource",
     *     summary="礼品卡资源作废",
     *     description="礼品卡资源作废",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="礼品卡资源ID"),
     *          @OA\Property(property="status", type="string", default="", description="状态：1 未激活 2 已激活 3 作废"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDiscardResource()
    {
        // 获取参数
        $params = \Yii::$app->request->post();

        // 添加条件
        $form = new CardResourceDisCardForm();
        $form->load($params, '');

        // 验证条件
        if (!$form->validate()) {
            CUtil::json_response(-1, implode(',', $form->firstErrors));
        }

        $id     = $params['id'] ?? 0;
        $status = $params['status'] ?? '';

        list($status, $ret) = GiftCardResourcesService::getInstance()->discardResource($id, $status);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, '作废成功');
    }


    /**
     * @OA\Post(
     *     path="/back/gift-card/gift-cards",
     *     summary="礼品卡下拉列表",
     *     description="礼品卡下拉列表（所有审核通过的礼品卡）",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest")
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",@OA\Items(ref="#/components/schemas/GiftCard"),description="数据")
     *         )
     *     )
     * )
     */
    public function actionGiftCards()
    {
        list($status, $ret) = GiftCardsService::getInstance()->getGiftCards();
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    // 批量激活礼品卡
    /**
     * @OA\Post(
     *     path="/back/gift-card/batch-activate",
     *     summary="批量激活礼品卡",
     *     description="批量激活礼品卡",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"file"},
     *          @OA\Property(property="file", type="string", default="", description="文件")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionBatchActivate(){
        $params = \Yii::$app->request->post();
        $file = $_FILES['file'] ?? null;
        if (!$file) {
            CUtil::json_response(-1, "未上传文件");
        }

        // 处理数据
        $spreadsheet = IOFactory::load($file['tmp_name']);
        $sheet = $spreadsheet->getActiveSheet();
        $data = [];
        foreach ($sheet->getRowIterator() as $index => $row) {
            if ($index == 1) continue;
            // 读取行数据
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false); // 获取所有单元格，即使它们没有值
            $rowData = [];
            foreach ($cellIterator as $cell) {
                $val = $cell->getValue();
                if (empty($val)) { // 判空
                    CUtil::json_response(-1, "文件内容存在空值");
                }
                $rowData[] = $val;
            }
            if (count($rowData) != 2) {
                CUtil::json_response(-1, "文件内容格式错误");
            }
            $data[] = $rowData;
        }
        $ret = GiftCardService::getInstance()->batchActivate($data,$this->user_id);
        
        CUtil::json_response(1, 'ok', $ret);
    }
    // 批量激活记录
    /**
     * @OA\Post(
     *     path="/back/gift-card/batch-activate-log",
     *     summary="批量激活记录",
     *     description="批量激活记录",
     *     tags={"礼品卡"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"page"},
     *          @OA\Property(property="page", type="integer", default=1, description="页码")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionBatchActivateLog(){
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = $post['page_size'] ?? 20;
        list($status, $ret) = GiftCardService::getInstance()->batchActivateLog($page,$page_size);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        $return = [
            'list'=>$ret['list'],
            'pages'=>CUtil::getPaginationPages($ret['count'], $page_size)
        ];
        CUtil::json_response(1, 'ok', $return);
    }

}