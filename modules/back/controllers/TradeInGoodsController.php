<?php

namespace app\modules\back\controllers;


use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\forms\tradeIn\TradeInGoodsForm;
use app\modules\back\services\TradeInGoodsService;
use app\modules\goods\models\TradeInCategoryModel;
use app\modules\rbac\controllers\CommController;

class TradeInGoodsController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/category-list",
     *     summary="获取商品分类列表",
     *     description="获取所有商品的分类列表，用于商品分类展示和选择。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         required=false,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest")
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功返回商品的分类列表",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 description="商品分类数据",
     *                 @OA\Items(
     *                     @OA\Property(property="id", type="string", description="分类ID"),
     *                     @OA\Property(property="name", type="string", description="分类名称")
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */


    public function actionCategoryList()
    {
        $data = TradeInGoodsService::getInstance()->getCategoryList();
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/list",
     *     summary="获取以旧换新商品列表",
     *     description="根据提供的过滤参数获取以旧换新活动中的商品列表",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="name", type="string", description="商品名称"),
     *                         @OA\Property(property="start_time", type="string", description="开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="结束时间"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="pos", type="string", description="位置")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="响应数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="列表项ID"),
     *                         @OA\Property(property="category_id", type="string", description="类别ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="sids", type="string", description="店铺ID"),
     *                         @OA\Property(property="ctime", type="string", description="创建时间"),
     *                         @OA\Property(property="utime", type="string", description="更新时间"),
     *                         @OA\Property(property="is_del", type="string", description="是否删除"),
     *                         @OA\Property(
     *                             property="category",
     *                             type="object",
     *                             description="商品所属类别",
     *                             @OA\Property(property="id", type="string", description="类别ID"),
     *                             @OA\Property(property="name", type="string", description="类别名称")
     *                         ),
     *                         @OA\Property(property="activity_status", type="integer", description="活动状态"),
     *                         @OA\Property(
     *                             property="goods",
     *                             type="object",
     *                             description="商品信息",
     *                             @OA\Property(property="name", type="string", description="商品名称"),
     *                             @OA\Property(property="status", type="string", description="商品状态"),
     *                             @OA\Property(property="id", type="string", description="商品ID"),
     *                             @OA\Property(property="is_del", type="string", description="是否删除")
     *                         )
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionList()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInGoodsForm(), 'list');
        $data = TradeInGoodsService::getInstance()->list($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/detail",
     *     summary="商品详情查询",
     *     description="根据活动ID获取商品的详细信息，包括商品、分类和规格等信息。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功返回商品的详细信息",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="商品详细数据",
     *                 @OA\Property(property="id", type="string", description="活动ID"),
     *                 @OA\Property(property="category_id", type="string", description="分类ID"),
     *                 @OA\Property(property="gid", type="string", description="商品ID"),
     *                 @OA\Property(property="sids", type="string", description="商品库存ID"),
     *                 @OA\Property(property="ctime", type="string", description="创建时间"),
     *                 @OA\Property(property="utime", type="string", description="更新时间"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="object",
     *                     description="商品信息",
     *                     @OA\Property(property="id", type="string", description="商品ID"),
     *                     @OA\Property(property="name", type="string", description="商品名称")
     *                 ),
     *                 @OA\Property(
     *                     property="category",
     *                     type="object",
     *                     description="分类信息",
     *                     @OA\Property(property="id", type="string", description="分类ID"),
     *                     @OA\Property(property="name", type="string", description="分类名称")
     *                 ),
     *                 @OA\Property(
     *                     property="specs",
     *                     type="array",
     *                     description="商品规格列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="规格ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="sku", type="string", description="SKU编号"),
     *                         @OA\Property(property="price", type="string", description="价格"),
     *                         @OA\Property(property="av_ids", type="string", description="属性值ID列表"),
     *                         @OA\Property(property="image", type="string", description="规格图片"),
     *                         @OA\Property(property="original_price", type="string", description="原价"),
     *                         @OA\Property(
     *                             property="pcombines",
     *                             type="array",
     *                             description="规格组合信息",
     *                             @OA\Items(
     *                                 @OA\Property(property="id", type="string", description="组合ID"),
     *                                 @OA\Property(property="sku", type="string", description="组合SKU"),
     *                                 @OA\Property(property="sprice", type="string", description="组合价格"),
     *                                 @OA\Property(property="sprice_type", type="string", description="价格类型"),
     *                                 @OA\Property(property="ctime", type="string", description="创建时间"),
     *                                 @OA\Property(property="utime", type="string", description="更新时间")
     *                             )
     *                         ),
     *                         @OA\Property(property="is_ini", type="integer", description="初始化标志"),
     *                         @OA\Property(property="gini_info", type="array", @OA\Items(type="string"), description="初始化信息"),
     *                         @OA\Property(property="gini_id", type="integer", description="初始化ID"),
     *                         @OA\Property(property="gini_tag", type="string", description="初始化标签"),
     *                         @OA\Property(property="gini_etime", type="integer", description="初始化结束时间"),
     *                         @OA\Property(property="is_internal", type="integer", description="内部标志"),
     *                         @OA\Property(
     *                             property="attr_cnf",
     *                             type="array",
     *                             description="属性配置信息",
     *                             @OA\Items(
     *                                 @OA\Property(property="at_name", type="string", description="属性名称"),
     *                                 @OA\Property(property="at_val", type="string", description="属性值")
     *                             )
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionDetail()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInGoodsForm(), 'detail');
        $data = TradeInGoodsService::getInstance()->detail($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/store",
     *     summary="商品新增、编辑",
     *     description="用于新增或编辑电子商务平台中的商品信息",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"category_id"},
     *                         @OA\Property(
     *                             property="id",
     *                             type="string",
     *                             description="活动ID"
     *                         ),
     *                         @OA\Property(
     *                             property="category_id",
     *                             type="string",
     *                             description="分类ID"
     *                         ),
     *                         @OA\Property(
     *                             property="gid",
     *                             type="string",
     *                             description="商品ID"
     *                         ),
     *                         @OA\Property(
     *                             property="sids",
     *                             type="string",
     *                             description="规格ID，多个以逗号隔开"
     *                         )
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="请求成功",
     *         @OA\JsonContent(
     *             type="object",
     *             description="空的响应体表示操作成功"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionStore()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInGoodsForm(), 'store');
        $data = TradeInGoodsService::getInstance()->store($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/del",
     *     summary="商品删除",
     *     description="删除指定的商品信息",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="操作成功，商品已删除",
     *         @OA\JsonContent(
     *             type="object",
     *             additionalProperties=false,
     *             description="删除操作不返回任何具体数据对象"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionDel()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInGoodsForm(), 'detail');
        $data = TradeInGoodsService::getInstance()->delete($form->id);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-goods/relate-activity",
     *     summary="商品关联活动",
     *     description="将指定的商品关联到活动中，支持多个活动关联。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gid", "activity_ids"},
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="activity_ids", type="string", description="活动ID列表，用逗号分隔")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             description="请求成功返回空对象",
     *             example={}
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionRelateActivity()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInGoodsForm(), 'relateActivity');
        $data = TradeInGoodsService::getInstance()->relateActivityGoods($form);
        CUtil::Ret($data);

    }

}