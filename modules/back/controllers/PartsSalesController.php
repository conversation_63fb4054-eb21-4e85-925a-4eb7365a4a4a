<?php

namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\models\Request;
use app\models\Response;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use Yii;
use app\modules\rbac\controllers\CommController;
use yii\db\Exception;

class PartsSalesController extends CommController
{
    /**
     * @throws Exception
     * @throws RedisException
     * 主机信息
     */
    public function actionMachine()
    {
        $main_skus = by::mainPartModel()->getList([],1,0);
        $main_info = [];
        foreach ($main_skus as $sku) {
            $info = by::mainPartModel()->getInfoByMainSku($sku);

            if (array_filter($info) && isset($info['info']) && $info['info']) {
                $main_info [] = $info;
            }
        }
        CUtil::json_response(1, 'ok', $main_info);

    }

    /**
     * @throws Exception
     * @throws RedisException
     * 配件信息
     */
    public function actionParts()
    {
        $post = Yii::$app->request->post();
        $main_sku = $post['main_sku'] ?? '';
        if (!$main_sku){
            CUtil::json_response(-1,'请选择主机');
        }
        $info = by::mainPartModel()->getInfoByMainSku($main_sku);

        $partData = $info['info'] ?? [];
        //下架不展示
        foreach ($partData as $k=>$v){
            if ($v['status']==1){
                unset($partData[$k]);
            }
        }
        CUtil::json_response(1, 'ok', array_values($partData));
    }

    //[{"part_sku": "20010100000201", "reminder_copy": "提醒文案1", "sort": 1,"part_id":9},{"part_sku": "20010100000242", "reminder_copy": "提醒文案2", "sort": 2,"part_id":""}]

    /**
     * @return void
     * 添加智能配件
     * @throws Exception
     */
    public function actionModify()
    {
        $post = Yii::$app->request->post();
        $id   = $post['id'] ?? '';
        $partInfo = $post['part_info'] ?? [];
        $host_sku = $post['host_sku'] ?? '';
        $partInfo = $partInfo ? json_decode($partInfo, true) : [];
        list($status, $ret) = by::mainSalesModel()->modify($id, $host_sku,$partInfo);
        $msg   = $id ? "修改了资源ID：{$id}" : "新增了资源：{$host_sku}";
        $msg  .= ",内容：{$ret}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SMART_ACCESSORIES);
        if ($status) {
            CUtil::json_response(1, $ret);
        } else {
            CUtil::json_response(-1, $ret);
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 详情
     */
    public function actionDetail()
    {
        $id = Yii::$app->request->post('id');
        $id = CUtil::uint($id);
        if (empty($id)) {
            CUtil::json_response(-1, '请选择主机');
        }
        $detail = by::mainSalesModel()->getMainInfoById($id);
        CUtil::json_response(1,'ok',$detail);
    }




    /**
     * @throws Exception
     * @throws RedisException
     * 智能配件列表
     */
    public function actionList()
    {
        $post      = \Yii::$app->request->post();
        $main_sku  = $post['main_sku']  ?? '';
        $name      = $post['name']      ?? '';
        $tid       = $post['tid']       ?? '';
        $page      = $post['page']      ?? 1;
        $page_size = $post['page_size'] ?? 50;

        $input = [
            'main_sku' => $main_sku,
            'name'     => $name,
            'tid'      => $tid,
        ];

        $return = [];
        $return['list'] = [];
        $return['pages'] = 0;
        //先查询数量
        $count = by::mainSalesModel()->GetListCount($input);
        if ($count > 0) {
            $ids = by::mainSalesModel()->GetList($input,
                $page, $page_size
            );
            $notMainTag = by::Gtag()->GetNotMainTag();
            $tagMap = by::Gtag()->GetTagNameMap();
            foreach ($ids as $id) {
                $info = by::mainSalesModel()->getMainInfoById($id) ?? [];
                // $tidFilter = by::Gtag()::NOT_MAIN_TAG;
                $tidFilter = $notMainTag;

                $goods = by::Gmain()->GetOneBySku($info['main_sku'] ?? '',1);
                $gid = $goods['id'] ?? 0;
                $tid = by::Gtag()->GetListByGid($gid);
                $tid = array_column($tid, 'tid');

                // 使用 array_diff 函数来过滤需要的 tid 值
                $filteredTid = array_diff($tid, $tidFilter);

                // 根据过滤后的 tid 值获取对应的 TAG_NAME
                $tagNamesFiltered = array_intersect_key($tagMap, array_flip($filteredTid));

                $info['tag'] = implode(',', $tagNamesFiltered);
                $return['list'][] = $info;
            }
            $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        }
        CUtil::json_response(1, 'ok', $return);
    }


    /**
     * @return void
     * 删除整个产品
     * @throws Exception
     */
    public function actionDelete()
    {
        $ids                = Yii::$app->request->post('id', '');
        if (empty($ids)) {
            CUtil::json_response(-1, '请选择数据');
        }
        $ida = explode(',', $ids);
        list($status,$ret) = by::mainSalesModel()->del($ida);
        if(!$status){
            CUtil::json_response(-1, $ret);
        }

        $msg                = "删除资源ID：{$ids},已成功删除{$ret}个";
        (new SystemLogsModel())->record($msg, RbacInfoModel::SMART_ACCESSORIES);

        if ($ret < count($ida)) {
            CUtil::json_response(-1, "已成功删除{$ret}个");
        }

        CUtil::json_response(1, '删除成功');
    }



}
