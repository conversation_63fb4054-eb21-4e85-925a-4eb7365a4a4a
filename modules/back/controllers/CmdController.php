<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/6/3
 * Time: 11:56
 */
namespace app\modules\back\controllers;

use app\models\by;
use app\models\CodeModel;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;

class CmdController extends CommController {

    /**
     * @param $err
     * @param $out_put
     * 构建失败通知
     */
    private function __buildErr($err,$out_put) {
        $arr =  [
            "<font color='info'>研发团队请注意:管理后台执行【自动化构建】失败！！！</font>",
            "<font color='info'>ERR：</font>请联系管理员按错误日志紧急处理",
            "<font color='info'>STEP：</font>{$err}",
            "<font color='info'>项目：</font>".\Yii::$app->name,
            "<font color='info'>构建者：</font>". $this->user_info['account'] ?? "--",
            "<font color='info'>详情：</font>\n".  implode("<br />",$out_put),
            "<font color='comment'>时间：</font>".date("Y-m-d H:i:s"),
        ];
//        CUtil::sendMsgToUdp($arr, '', false,'m_1622804151');
    }

    private function __recheck() {
        if(!YII_ENV_TEST) {
            CUtil::json_response(-1,"当前环境异常");
        }

        $ret = by::model("SyncCodeModel",'back')->CmdLock(1);
        if($ret) {
            $code = CodeModel::STATUS['BETA_ERR'];
            CUtil::json_response($code,"封闭测试中、功能暂停使用，紧急问题请联系【测试组】！");
        }
    }

    /**
     * 重构管理后台
     */
    public function actionBmRebuild() {

        $this->__recheck();

        $time_out   = 600;
        $unique_key = __FUNCTION__;
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,$time_out,"EX");
        if(!$anti) {
            list($status,$ttl) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"TTL");
            CUtil::json_response(-1,"构建中，请稍后({$ttl}s)");
        }

        ignore_user_abort(true);

        set_time_limit($time_out);

        $prod       = \Yii::$app->request->post('prod',0); //构建环境
        $tag        = \Yii::$app->request->post('tag',''); //目标tag
        $prod       = intval(!!$prod);//0测试1正式
        if($prod && empty($tag)) {
            CUtil::json_response(-1,"请指定要构建的正式环境Tag");
        }

        //todo --------------- 前端源码更新 ---------------------------------------
        list($exec0,$output0,$exec0_ret) = by::model("BmBuildModel",'back')->VersionUpdate($prod);
        if($exec0_ret !== 0) {

            by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

            $err = "Git源码更新失败";
            $this->__buildErr($err,$output0);
            CUtil::debug("{$err}|{$exec0_ret}|{$exec0}|".json_encode($output0),'cmd_exec_err');

            CUtil::json_response(-1,$err,[
                'ret'       => $exec0_ret,
                'content'   => $output0,
                'cmd'       => $exec0,
            ]);
        }

        CUtil::debug("STEP0:{$exec0}",'cmd_exec');

        //todo --------------- 构建代码 ---------------------------------------
        list($exec1,$output1,$exec1_ret) = by::model("BmBuildModel",'back')->CodeBuild($prod,$tag);
        if($exec1_ret !== 0) {

            by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

            $err = "代码构建异常";
            $this->__buildErr($err,$output1);
            CUtil::debug("{$err}|{$exec1_ret}|{$exec1}|".json_encode($output1),'cmd_exec_err');

            CUtil::json_response(-1,$err,[
                'ret'       => $exec1_ret,
                'content'   => $output1,
                'cmd'       => $exec1,
            ]);
        }

        CUtil::debug("STEP1:{$exec1}",'cmd_exec');

        //todo --------------- 代码部署 ---------------------------------------
        list($exec2,$output2,$exec2_ret) = by::model("BmBuildModel",'back')->BuiltCodeArrange($prod);
        if($exec2_ret !== 0) {

            by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

            $err = "代码部署异常";
            $this->__buildErr($err,$output2);
            CUtil::debug("{$err}|{$exec2_ret}|{$exec2}|".json_encode($output2),'cmd_exec_err');

            CUtil::json_response(-1,$err,[
                'ret'       => $exec2_ret,
                'content'   => $output2,
                'cmd'       => [ $exec1, $exec2 ]
            ]);
        }

        CUtil::debug("STEP2:{$exec2}",'cmd_exec');

        //正式环境的构建需要提交到git仓库
        $exec3 = '';
        if($prod) {
            list($exec3,$output3,$exec3_ret) = by::model("BmBuildModel",'back')->CommitAndReBuildToTest($prod);
            if($exec3_ret !== 0) {

                by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

                $err = "代码提交和测试服重构异常";
                $this->__buildErr($err,$output3);
                CUtil::debug("{$err}|{$exec3_ret}|{$exec3}|".json_encode($output3),'cmd_exec_err');

                CUtil::json_response(-1,$err,[
                    'ret'       => $exec3_ret,
                    'content'   => $output3,
                    'cmd'       => $exec3
                ]);
            }
        }

        by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

        //udp 消息推送
        if($prod) {
            $arr = [
                "<font color='info'>研发团队请注意、正式服-管理后台【自动化构建】完成</font>",
                "<font color='info'>Tips：</font>请联系管后端同学、及时提交合并请求并发布！！！\n"
            ];
        } else {
            $arr = [
                "<font color='info'>研发团队请注意、测试服-管理后台【自动化构建和部署】完成</font>\n",
            ];
        }

        $details0   = implode("<br />",explode("&&",$exec0));
        $details1   = implode("<br />",explode("&&",$exec1));
        $details2   = implode("<br />",explode("&&",$exec2));
        $details3   = implode("<br />",explode("&&",$exec3));
        $detail     = implode("<br />",[$details0,$details1,$details2,$details3]);

        $time_used  = bcsub(microtime(true),START_TIME,2);//总耗时
        $arr[]      = "<font color='info'>项目：</font>".\Yii::$app->name;
        $arr[]      = "<font color='info'>构建者：</font>". $this->user_info['account'] ?? "--";
        $arr[]      = "<font color='info'>总耗时：</font>{$time_used}s";
        $arr[]      = "<font color='info'>更新命令：</font><br />{$detail}\n";
        $arr[]      = "<font color='comment'>时间：</font>".date("Y-m-d H:i:s");

//        CUtil::sendMsgToUdp($arr, '', false,'m_1622804151');

        CUtil::json_response(1,"OK",[
            'ret'       => $exec2_ret,
            'content'   => $output2,
            'cmd'       => [$exec0, $exec1, $exec2, $exec3 ],
            'time_used' => $time_used,//总耗时
        ]);
    }


    /**
     * 服务器代码更新
     */
    public function actionHotUpdate() {

        $this->__recheck();

        $time_out   = 600;
        $unique_key = __FUNCTION__;
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,$time_out,"EX");
        if(!$anti) {
            list($status,$ttl) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"TTL");
            CUtil::json_response(-1,"服务器代码更新中，请稍后({$ttl}s)");
        }

        ignore_user_abort(true);

        set_time_limit($time_out);

        list($exec,$output,$exec_ret) = by::model("BmBuildModel",'back')->HotUpdate(false,0);
        if($exec_ret !== 0) {

            by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

            CUtil::json_response(-1,"代码更新失败",[
                'ret'       => $exec_ret,
                'content'   => $output,
                'cmd'       => $exec
            ]);
        }

        by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

        $details   = implode("<br />",array_slice($output,-10));

        $time_used =  bcsub(microtime(true),START_TIME,4);//总耗时
        $arr       =  [
            "<font color='info'>研发团队请注意、测试服-服务器代码【热更新】完成</font>\n",
            "<font color='info'>项目：</font>".\Yii::$app->name,
            "<font color='info'>更新者：</font>". $this->user_info['account'] ?? "--",
            "<font color='info'>总耗时：</font>{$time_used}s",
            "<font color='info'>详情：</font><br />...<br />{$details}\n",
            "<font color='comment'>时间：</font>".date("Y-m-d H:i:s"),
        ];

//        CUtil::sendMsgToUdp($arr, '', false,'m_1622804151');

        CUtil::json_response(1,"OK",[
            'ret'       => $exec_ret,
            'content'   => $output,
            'cmd'       => $exec,
            'time_used' => $time_used,
        ]);

    }

    /**
     * 正式服代码同步
     */
    public function actionSyncCode() {

        $this->__recheck();

        $time_out   = 600;
        $unique_key = __FUNCTION__;
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,$time_out,"EX");
        if(!$anti) {
            list($status,$ttl) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"TTL");
            CUtil::json_response(-1,"代码发布中，请稍后({$ttl}s)");
        }

        ignore_user_abort(true);

        set_time_limit($time_out);

        $bg_user = $this->user_info['account'] ?? "";
        $opt     = \Yii::$app->request->post('opt',1);
        $tag     = \Yii::$app->request->post('tag','');
        $msg     = \Yii::$app->request->post('msg','');
        $id      = \Yii::$app->request->post('id',0);

        list($status,$output) = by::model("SyncCodeModel",'back')->SyncCode($opt,$tag,$msg,$bg_user,$id);

        by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"DEL");

        if(!$status) {
            CUtil::json_response(-1,"代码同步失败",['err'=>$output]);
        }

        CUtil::json_response(1,"各服务器代码同步完成");
    }

    /**
     * 代码发布列表
     */
    public function actionSyncCodeList()
    {
        $post       = \Yii::$app->request->post();
        $page       = $post['page'] ?? 1;
        $page_size  = 50;
        $model      = by::model("SyncCodeModel",'back');
        $rets       = $model->GetList($page, $page_size);
        foreach ($rets as $ret) {

            $info   = $model->GetSyncCodeInfo($ret['id']);

            $return['list'][] = $info;
        }

        $count = $model->GetListCount();
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * 判断功能是否锁定
     */
    public function actionIsLock() {
        $val = by::model("SyncCodeModel",'back')->CmdLock(1);
        CUtil::json_response(1, 'ok', ['val'=>$val]);
    }

    /**
     * 功能锁定开关
     */
    public function actionLock() {
        $val  = \Yii::$app->request->post('val',0);
        $val  = intval(!!$val);
        $ret  = by::model("SyncCodeModel",'back')->CmdLock(0,$val);
        if(!$ret) {
            CUtil::json_response(-1,"功能锁定失败");
        }

        CUtil::json_response(1, "OK", ['val'=>$val]);
    }
}
