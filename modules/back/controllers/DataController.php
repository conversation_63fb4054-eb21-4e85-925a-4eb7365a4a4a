<?php
/**
 * Created by IntelliJ IDEA.
 * User: clarence
 * Date: 2021/4/22
 * Time: 15:31
 */

namespace app\modules\back\controllers;

use app\components\AdminRedisKeys;
use app\components\AliYunOss;
use app\components\MemberCenter;
use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\guide\GuideListForm;
use app\modules\back\services\DataService;
use app\modules\back\services\UserService;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\rbac\controllers\CommController;
use app\modules\main\models\CommModel;
use app\modules\rbac\models\RbacInfoModel;
use RedisException;
use Yii;
use yii\db\Exception;

class DataController extends CommController
{
    public $fre_time = 5;//接口频率限制

    CONST UPLOAD_TEMPLATE_ID = [
         '1001'=> 'https://wpm-cdn.dreame.tech/files/202311/656469832019c1311724761.csv',
         '1002'=> 'https://cdn-cn-oss-dreame-store.dreame.tech/dreame-php-mall/uat/images202501/280111-*************.xlsx'
    ];
    /**
     * 代码执行
     */
    public function actionCodeExec() {

        if (YII_ENV_PROD) {
            $admin_id = by::adminUserModel()->getUserIDFromSession();

            if ($admin_id != 1) {
                CUtil::json_response(-1,'仅限测试用');
            }
        }

        $code = Yii::$app->request->post('code','');
        if(!empty($code)) {

            $account = $this->user_info['account'] ?? "";
            CUtil::debug("{$account}|{$code}","code_exec");

            $code = str_replace(["rm ",'exec(','chmod ','/bin/bash ','/bin/sh ','system('],"",$code);
            $code = str_replace(["by::"],"app\models\by::",$code);//追加命名空间
            $code = str_replace(["CUtil::"],"app\models\CUtil::",$code);
            return eval("{$code};");
        }

        return "";
    }

    /**
     * 再来一次
     */
    public function actionAgainExec()
    {
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '仅限测试用');
        }
        try {
            $str    = Yii::$app->request->post('html',0);
            $data   = json_decode($str, true);
            $heads  = null;
            if(!empty($data['cookies'])) {
                $cookies = "Cookie: ";
                foreach ($data['cookies'] as $cookie) {
                    $cookies .= "{$cookie['name']}={$cookie['value']}; ";
                }
                $heads[] = $cookies;
            }

            $return = CUtil::curl_post($data['url'],$data['body'],$heads,10,TRUE);
            if(isset($data['body']['teaccaaa'])) {
                CUtil::json_response(1,'ok',$return);
            } else {
                CUtil::json_response(1,'ok',['data' => $return]);
            }
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::json_response(-1,$error);
        }

    }

    /**
     * 接口并发请求监控
     */
    public function actionApiRequest(){
        $data = [];
        list($status,$request)  = by::model('MonitorModel',MAIN_MODULE)->apiRequest(2);
        $request                = $status ? $request : [];
        list($status,$timeUsed) = by::model('MonitorModel',MAIN_MODULE)->apiRequestTimeUsed(2);
        $timeUsed               = $status ? $timeUsed : [];

        $date                   = date("Ymd");
        if($date == 20190520) {
            $date = date("Ymd 15:24:00");
        }
        $time_pass              = time() - strtotime($date);//今日已过去时间

        // RT：响应时间，处理一次请求所需要的平均处理时间
        // QPS: 每秒钟处理完请求的次数
        $data[0]['api'] = '总计:';
        $data[0]['req_nums']  = 0;
        $data[0]['time_used'] = 0;

        foreach ($request as $req_api=>$req_nums) {
            $time_used = $timeUsed[$req_api] ?? 0;//毫秒数
            $data[0]['time_used'] += $time_used;
            $data[0]['req_nums']  += $req_nums;

            $data[] = [
                'api'       => $req_api,
                'time_used' => $time_used > 0 ? sprintf('%.4f',$time_used/1000) :  "--",//转换成s
                'rt'        => $req_nums  > 0 ? sprintf('%.4f',$time_used/$req_nums) : "--",
                'req_nums'  => $req_nums,//总请求次数
                'qps'       => sprintf('%.4f',$req_nums/$time_pass),//总次数 / 今日已过去时间
            ];
        }

        $data[0]['rt']  = $data[0]['req_nums'] > 0 ? sprintf('%.4f', $data[0]['time_used']/$data[0]['req_nums']) : "--";//所有接口

        $data[0]['qps'] = sprintf('%.4f', $data[0]['req_nums']/$time_pass);//所有接口

        $data[0]['time_used'] = sprintf('%.4f',$data[0]['time_used']/1000); //转换成s

        CUtil::json_response(1,'ok',$status ? $data : []);
    }

    /**
     * 获取APP发起的请求记录
     */
    public function actionPost() {
        $data = by::model('CommModel',MAIN_MODULE)->AppPostLog(MAIN_MODULE,300);
        CUtil::json_response(1,'ok',$data);
    }

    /**
     * 下发服务器列表
     */
    public function actionServerList() {
        $hosts = CUtil::getConfig('hostname','common',MAIN_MODULE);
        $ips   = array_values($hosts);
        CUtil::json_response(1,"OK",$ips);
    }

    /**
     * Redis监控
     */
    public function actionRedisInfo() {

        $info  = (array)by::redis('core')->INFO('Memory');
        $data['Memory']  = [
            'used_memory'        =>  $info['used_memory'] ?? 0,
            'used_memory_human'  =>  $info['used_memory_human'] ?? 0,
            'maxmemory'          =>  $info['maxmemory'] == 0 ? $info['total_system_memory']       : $info['maxmemory'],
            'maxmemory_human'    =>  $info['maxmemory'] == 0 ? $info['total_system_memory_human'] : $info['maxmemory_human'],
        ];

        CUtil::json_response(1,'ok',$data);
    }

    /**
     * @param string $Host
     * @param string $uri
     * @return bool|string
     * 执行指定内网服务器接口
     */
    private function __internalApi($Host='',$uri='') {

        list($status,$api_secret) = CommModel::getApiKey($this->api);
        if(!$status) {
            CUtil::json_response(-1,"缺少API参数");
        }

        if(empty($Host)) {
            CUtil::json_response(-1,"无效服务器IP");
        }

        $sessid       = Yii::$app->request->post('sessid');
        $config       = CUtil::getConfig('ports','common',MAIN_MODULE);
        $port         = $config['prod'] ?? 80;
        $module       = Yii::$app->controller->module->id;
        $prd_uri      = "{$Host}:{$port}/{$module}/{$uri}";
        $body         = [
            'uri'          => $prd_uri,
            'proxy'        => 0,
            'sign_time'    => time(),
            'host'         => $Host,
            'sessid'       => $sessid,
            'version'      => $this->version,
            'api'          => $this->api,
        ];

        $body['sign'] = CommModel::getSign($body,$api_secret);


        return CUtil::curl_post($prd_uri,$body);
    }

    /**
     * 服务器负载
     */
    private function __avg() {
        $data['servers'] = sys_getloadavg();
        $data['cpus']    = 1;
        $cpuinfo         = @file('/proc/cpuinfo');
        if ($cpuinfo !== false) {
            $cpuinfo = implode("", $cpuinfo);
            @preg_match_all("/model\s+name\s{0,}\:+\s{0,}([\w\s\)\(\@.-]+)([\r\n]+)/s", $cpuinfo, $model);
            if (is_array($model[1])) {
                $data['cpus'] = count($model[1]);
            }
        }

        $meminfo = @file("/proc/meminfo");
        if ($meminfo !== false) {
            $meminfo = implode("", $meminfo);
            preg_match_all("/MemTotal\s{0,}\:+\s{0,}([\d\.]+).+?MemFree\s{0,}\:+\s{0,}([\d\.]+).+?Cached\s{0,}\:+\s{0,}([\d\.]+).+?SwapTotal\s{0,}\:+\s{0,}([\d\.]+).+?SwapFree\s{0,}\:+\s{0,}([\d\.]+)/s", $meminfo, $buf);
            $data['memTotal'] = round($buf[1][0] / 1024 / 1024, 2);
            $data['memFree']  = round($buf[2][0] / 1024 / 1024, 2);
        }

        CUtil::json_response(1,'ok',$data);
    }

    /**
     * 服务器负载
     */
    public function actionLoadAvg() {
        $proxy      = Yii::$app->request->post('proxy',1);//是否走内网代理
        $host       = Yii::$app->request->post('host','');//目标服务器
        $unique_key = CUtil::getAllParams(__FUNCTION__,$proxy,$host);
        $acc        = $this->user_info['id'] ?? 0;
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency($acc,$unique_key,$this->fre_time,"EX");
        if(!$anti) {
            list($status,$ttl) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"TTL");
            CUtil::json_response(-1,"服务器繁忙，请稍后({$ttl}s)");
        }

        if(!YII_ENV_PROD) {
            $this->__avg();
        } else {
            if($proxy) {
                return $this->__internalApi($host,'data/load-avg'); //必须返回
            } else {
                $this->__avg();//必须返回
            }
        }
    }

    /**
     * 服务器分支信息
     */
    private function __branch() {
        //正式环境的构建需要提交到server git仓库
        $cmd = " cd ".WEB_PATH."/../ && git branch 2>&1" ;

        exec($cmd,$output,$ret);
        //代码提交失败
        if($ret !== 0) {
            CUtil::json_response(-1,"获取分支信息异常",$output);
        }

        CUtil::json_response(1,"OK",$output);
    }

    /**
     * 远程服务器所在分支状态
     */
    public function actionBranch() {
        $proxy      = Yii::$app->request->post('proxy',1);//是否走内网代理
        $host       = Yii::$app->request->post('host','');//目标服务器
        $unique_key = CUtil::getAllParams(__FUNCTION__,$proxy,$host);
        $acc        = $this->user_info['id'] ?? 0;
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency($acc,$unique_key,$this->fre_time,"EX");
        if(!$anti) {
            list($status,$ttl) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,"TTL");
            CUtil::json_response(-1,"服务器繁忙，请稍后({$ttl}s)");
        }

        if(!YII_ENV_PROD) {
            $this->__branch();
        } else {
            if($proxy) {
                return $this->__internalApi($host,'data/branch'); //必须返回
            } else {
                $this->__branch();
            }
        }
    }


    /**
     * @throws Exception
     * 获取指定用户信息
     */
    public function actionUser() {
        $param       = Yii::$app->request->post('uid', 0);

        $len                = strlen($param);
        $user_ids   =  $users  = [];
        $user_id            = 0;
        switch (true) {
            case $len == 11 :
                //手机号查询
                $param          = CUtil::uint($param);
                $user_ids       = by::Phone()->GetUidsByPhone($param);
                break;
            default :
                //用户id查询
                $user_id             = CUtil::uint($param);
        }

        if (count($user_ids) > 1) {
            foreach($user_ids as $uid) {
                $main = by::users()->getUserMainInfo($uid);
                if ($main['user_type'] == 10){
                    continue;
                }
                $status = 0;
                if (strpos($main['openudid'], '|')) {
                    $status = 10;
                }

                $users[] = [
                    'status'  => $status,
                    'user_id' => $uid
                ];
            }
            if (count($users) == 1 ){
                $user_id = current($users)['user_id'] ?? 0;
            }else{
                CUtil::json_response(1,'ok',$users);
            }
        } elseif( count($user_ids) == 1 ) {
            $user_id = current($user_ids) ?? 0;
        }

        if (empty($user_id)) {
            CUtil::json_response(-1, '用户不存在');
        }
        list($status,$data) = by::userExtend()->getUserInfo($user_id);
        if(!$status) {
            CUtil::json_response(-1,$data);
        }
        //获取会员中心权益相关数据
        list($s, $dataCenter) = by::memberCenterModel()->CenterMessage('basicInfo', ['user_id' => $user_id]);
        $userSource = by::SourceExtend()->getLatestSourceList(['user_id' => $user_id]);
        $sourceCode = $userSource[0]['source_euid'] ?? '';
        $sourceInfo = by::SourceConfig()->getParamByCode($sourceCode);
        list($status, $mdata) = MemberCenter::factory()->scoreGet($user_id);
        $gold = 0;
        if ($status) {
            $gold = (int) ($mdata['totalGold'] ?? 0);
        }

        $currentCenter          = $dataCenter['currentLevelInfo'] ?? '';
        $levelInfo              = $currentCenter['level'] ?? '';
        $data['level_name']     = $levelInfo['name'] ?? '';
        $data['level_order']    = $levelInfo['order'] ?? '';
        $data['level_min_grow'] = $levelInfo['minGrow'] ?? '';
        $data['level_max_grow'] = $levelInfo['maxGrow'] ?? '';
        $data['level_picture']  = $levelInfo['picture'] ?? '';
        $data['level_uid']      = $dataCenter['uid'] ?? '';
        $data['param']          = $sourceInfo['param'] ?? '';
        $data['channel']        = $sourceInfo['source_code'] ?? '';
        $data['grow']           = $dataCenter['totalGrow'] ?? '';
        $data['gold']           = $gold;


        if(!$this->viewSensitive) {
            $data = Response::responseList($data, ['phone' => 'tm']);
            isset($data['address']) && $data['address'] = Response::responseList($data['address'], ['phone' => 'tm', 'detail' => 'tm']);
        }
        CUtil::json_response(1, 'OK', $data);
    }

    /**
     * 文件上传
     */
    public function actionUpload()
    {
        $file    = isset($_FILES['file']) ? $_FILES['file'] : "";//得到传输的数据

        list($status,$ret) = AliYunOss::factory()->uploadFileToOss($file);

        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        //hack oss先用bgoods的 把url先记录下来，后期删除
        $save = [
            'image' => $ret['bigImg'],
        ];
        by::model('ImagesModel', 'back')->SaveLog($save);

        CUtil::json_response(1,'OK',$ret);
    }

    public function actionUploadSign() {
        $ret = AliYunOss::factory()->sign();
        CUtil::json_response(1,'OK', $ret);
    }

    /**
     * @throws Exception
     * 废弃指定账号
     */
    public function actionDiscard() {
        $user_id            = Yii::$app->request->post('uid',0);
        list($status,$ret)  = by::users()->Deprecated($user_id,0,$this->wxSyncCenterLock);
        if(!$status) {
            CUtil::json_response(-1,$ret);
        }

        CUtil::json_response(1,'OK',$ret);
    }

    /**
     * 获取指定日期的QPS数据
     */
    public function actionQps() {
        $date   = Yii::$app->request->post('date',0);
        $aData  = by::model("MonitorModel",MAIN_MODULE)->getQpsByDate($date);
        CUtil::json_response(1,'OK',$aData);
    }

    /**
     * 测试边写入边导出
     */
    public function test()
    {
        $head  = ['字段1','字段2','字段3','字段4'];
        CUtil::export_csv_new($head, function () {
            for ($i = 0; $i <500;$i++) {
                $row = [
                    [
                        'nick' => $i,
                        'phone' => $i,
                        'consume' => $i,
                        'tickets' => $i,
                    ]
                ];
                usleep(50000);
                yield $row;
            }

        }, 'test');

    }

    /**
     * 获取导出token
     */
    public function actionExportToken()
    {
        $post   = Yii::$app->request->post();

        if (empty($post['act_type'])) {
            CUtil::json_response(-1, '参数错误');
        }

        unset($post['sign']);

        $sessid      = CUtil::getRequestParam("cookie",'sessid','');
        $sessid      = empty($sessid) ? CUtil::getRequestParam("post",'sessid','') : $sessid;
        $sessid      = substr($sessid,0,32); // md5长度

        $post['sessid']     = $post['sessid'] ?? $sessid;

        $params = CUtil::encrypt(json_encode($post), 'EXPORT_TOKEN');
        $params = urlencode($params);

        $redis  = by::redis();

        $token  = md5($params. microtime(true));
        $r_key  = AdminRedisKeys::exportToken($token);

        $redis->set($r_key, $params, ['ex' => 60]);

        CUtil::json_response(1, 'ok', ['token' => $token]);
    }

    /**
     * 地址库
     */
    public function actionAreaList()
    {
        $pid        = Yii::$app->request->post('pid', 1);

        //hack 等管理后台发了可以删掉
        $pid        = $pid == 0 ? 1 : $pid;

        $aData      = by::model('AreaModel', MAIN_MODULE)->GetList($pid);

        CUtil::json_response(1,'OK',$aData);
    }

    /**
     * 在后台给用户发放卡券
     */
    public function actionGiveUserCard()
    {
        $user_id           = Yii::$app->request->post('uid',0);
        $market_id         = Yii::$app->request->post('market_id',0);
        $num               = Yii::$app->request->post('num',0);
        list($status,$ret) = by::userCard()->backSend(
            $user_id,
            $market_id,
            $num);

        if(!$status) {
            CUtil::json_response(-1,$ret);
        }
        //写入日志
        (new SystemLogsModel())->record("给用户{$user_id}发放了{$num}张优惠券{$market_id}", RbacInfoModel::MARKET_MANAGER,$this->user_info['id']);

        CUtil::json_response(1,'OK',$ret);
    }

    /**
     * 在后台给用户发放积分
     */
    public function actionGiveUserPoint()
    {
        CUtil::json_response(-1,'功能已关闭');
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 用户列表
     */
    public function actionUserList()
    {
        $page        = Yii::$app->request->post('page', 1);
        $page_size   = 20;
        $user_id     = Yii::$app->request->post('user_id', 0);
        $store       = Yii::$app->request->post('store', '');
        $phone       = Yii::$app->request->post('phone', 0);
        $source      = Yii::$app->request->post('source', 0);
        $s_time      = Yii::$app->request->post('s_time', 0);
        $e_time      = Yii::$app->request->post('e_time', 0);
        $vip_s_time  = Yii::$app->request->post('vip_s_time', 0);
        $vip_e_time  = Yii::$app->request->post('vip_e_time', 0);
        $union       = Yii::$app->request->post('union', '');
        $source_code = Yii::$app->request->post('source_code', '');
        $p_sources   = Yii::$app->request->post('p_sources', '-1');
        $p_sources   = empty(intval($p_sources)) ? '-1' : $p_sources;
        $r_id        = CUtil::uint(Yii::$app->request->post('r_id', '0'));
        $guide_id    = CUtil::uint(Yii::$app->request->post('guide_id', '0'));
        if(!empty($user_id) && intval($user_id) == 0) {
            CUtil::json_response(-1,'只支持商城用户ID查询,不支持UID查询');
        }

        $count           = by::userExtend()->getUserTotal($user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources, $r_id, $guide_id,$store,$source_code);
        $return['count'] = $count;
        $return['pages'] = CUtil::getPaginationPages($count, $page_size);
        $list            = by::userExtend()->getUserList($page, $page_size, $user_id, $phone, $source, $s_time, $e_time, $vip_s_time, $vip_e_time, $union, $p_sources, $r_id, $guide_id,$store,$source_code);
        $return['list']  = by::userExtend()->getUserData($list);

        // 员工绑定状态
        $return['list'] = UserService::getInstance()->setBindStatus($return['list']);

        !$this->viewSensitive && $return['list'] = Response::responseList($return['list'],['phone'=>'tm']);

        CUtil::json_response(1,'OK',$return);
    }

    /**
     * @throws Exception
     * 导购列表
     */
    public function actionGuideList()
    {
        $data = \Yii::$app->request->post();

        // 初始化表单验证
        $form = new GuideListForm();
        $form->load($data, '');

        // 验证表单数据
        if (!$form->validate()) {
            CUtil::json_response(-1, array_values($form->firstErrors)[0]);
        }

        // 接收请求参数，设定默认值
        $page      = $data['page'] ?? 1;
        $page_size = 20;
        $user_id   = $data['user_id'] ?? 0;
        $name      = $data['name'] ?? '';
        $job_no    = $data['job_no'] ?? '';
        $status    = $data['status'] ?? -1;

        // 如果提供了 job_no 但未提供 name，使用 job_no 作为 name 查询条件
        $name = $name ?: $job_no;

        // 获取导购总数和分页信息
        $count  = by::guide()->getTotal($user_id, $name, $status);
        $return = [
                'count' => $count,
                'pages' => CUtil::getPaginationPages($count, $page_size),
        ];

        // 获取导购列表数据
        $list           = by::guide()->getList($page, $page_size, $user_id, $name, $status);
        $return['list'] = Response::responseList(
                by::guide()->getGuideData($list),
                ['phone' => 'tm']
        );

        // 返回结果
        CUtil::json_response(1, 'OK', $return);
    }

    public function actionExportUser(){
        $post      = Yii::$app->request->post();
        $user_id   = $post['user_id'] ?? 0;
        $phone     = $post['phone'] ?? 0;
        $source    = $post['source'] ?? 0;
        $union     = $post['union'] ?? 0;
        $p_sources = $post['p_sources'] ?? '-1';
        by::userExtend()->export($user_id, $phone, $source, 0, 0, 0, 0,0,$union,$p_sources);
    }

    /**
     * @throws Exception
     * 获取用户卡券
     */
    public function actionUserCard(){
        $user_id    = Yii::$app->request->post('u_id','');
        list($status,$data) = by::userCard()->getList($user_id, by::userCard()::SOURCE['ADMIN']);
        if (!$status){
            CUtil::json_response(0, '获取失败');
        }
        CUtil::json_response(1, '获取成功', $data);
    }

    /**
     * @throws Exception
     * 删除卡券
     */
    public function actionDelCard()
    {
        $user_id    = Yii::$app->request->post('uid',0);
        $id         = Yii::$app->request->post('id',0);
        $type       = Yii::$app->request->post('type',0);
        $expire_time= Yii::$app->request->post('expire_time',0);

        list($status, $ret) = by::userCard()->delCard($user_id, $id, $type, $expire_time);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * 重置新人礼包
     */
    public function actionResetNewGift()
    {
        $user_id   = Yii::$app->request->post('uid',0);
        $status    = Yii::$app->request->post('status',0);
        if(!in_array($status,[0,1]) || $user_id<=0 ) {
            CUtil::json_response(-1,'参数错误');
        }

        $ret       = by::users()->updateMembersInfo($user_id,['is_new_gift'=>$status]);
        if(!$ret) {
            CUtil::json_response(-1,'修改失败');
        }

        CUtil::json_response(1,'OK');
    }

    /**
     * 获取小程序urllink
     */
    public function actionWxUlink()
    {
        $post   = Yii::$app->request->post();

        list($s, $res) = by::model('WxUlinkModel', 'main')->CreateLink($post);

        if (!$s) {
            CUtil::json_response(-1,$res);
        }

        CUtil::json_response(1,'OK', $res);
    }

    /**
     * 快递公司
     */
    public function actionExpCom()
    {
        $com = CUtil::getConfig('com', 'express', MAIN_MODULE);
        CUtil::json_response(1,'OK',$com);
    }

    /**
     * crm 同步记录
     */
    public function actionCrmLog(){
        $page       = Yii::$app->request->post('page', 1);
        $page_size  = 20;
        list($pages,$list) = by::model('CrmLogModel', 'main')->getList($page,$page_size);
        CUtil::json_response(1,'OK',['pages'=>$pages,'list'=>$list]);
    }

    /**
     * crm再来一次
     */
    public function actionCrmRetry(){
        $id       = Yii::$app->request->post('id', 0);
        list(,$ret) = by::model('CrmLogModel', 'main')->retry($id);
        CUtil::json_response(1,'OK',$ret);
    }

    /**
     * @OA\Post(
     *     path="/back/data/user-level",
     *     summary="用户等级列表",
     *     description="用户等级列表",
     *     tags={"会员中心"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/UserLevel",description="数据")
     *          )
     *     )
     * )
     */
    public function actionUserLevel()
    {
        $data = by::users()::USER_LEVEL;
        CUtil::json_response(1, 'OK', $data);
    }




    /**
     * @OA\Post(
     *     path="/back/data/store-list",
     *     summary="企业微信门店列表",
     *     description="企业微信门店列表",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/UserLevel",description="数据")
     *          )
     *     )
     * )
     */
    public function actionStoreList()
    {
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 2);
        if (!$s) {
            CUtil::json_response(-1, '获取门店名称过于频繁，请稍后再试');
        }



        $stores = DataService::getInstance()->getStoreList();
        CUtil::json_response(1, 'OK', $stores);
    }



    /**
     * @OA\Post(
     *     path="/back/data/list",
     *     summary="用户来源列表",
     *     description="用户来源列表",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest")
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        $return = DataService::getInstance()->getUserSourceList();
        CUtil::json_response(1, 'OK', $return);
    }

    /**
     * @OA\Post(
     *     path="/back/data/source-save",
     *     summary="配置用户来源",
     *     description="配置用户来源",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/UserSourceResource"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSourceSave()
    {
        $post = Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        list($status, $ret) = DataService::getInstance()->saveSource($id, $post);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/data/source-del",
     *     summary="删除用户来源",
     *     description="删除用户来源",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="来源ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSourceDel()
    {
        $id = Yii::$app->request->post('id',0);

        list($status, $ret) = DataService::getInstance()->delSource($id);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/data/source-list",
     *     summary="用户来源筛选列表",
     *     description="用户来源筛选列表",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSourceList()
    {
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 1, "EX", 2);
        if (!$s) {
            CUtil::json_response(-1, '获取用户来源过于频繁，请稍后再试');
        }
        $list = DataService::getInstance()->sourceSelect();
        CUtil::json_response(1,'ok', $list);
    }

    /**
     * @OA\Post(
     *     path="/back/data/update-member-grow",
     *     summary="手动增加觅享分",
     *     description="更新会员信息",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"uid","number"},
     *          @OA\Property(property="uid", type="integer", default="", description="用户user_id"),
     *          @OA\Property(property="number", type="integer", default="", description="增加数量"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUpdateMemberGrow()
    {
        $userId = Yii::$app->request->post('uid', 0);
        $number = Yii::$app->request->post('number', 0);
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($userId, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '用户增加觅享分过于频繁，请稍后再试');
        }
        list($s, $ret) = DataService::getInstance()->updateMemberGrow($userId, $number);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/data/update-member-point",
     *     summary="手动增加积分",
     *     description="更新会员信息",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"uid","number"},
     *          @OA\Property(property="uid", type="integer", default="", description="用户user_id"),
     *          @OA\Property(property="number", type="integer", default="", description="增加数量"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUpdateMemberPoint()
    {
        $userId = Yii::$app->request->post('uid', 0);
        $number = Yii::$app->request->post('number', 0);
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($userId, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '用户增加积分过于频繁，请稍后再试');
        }
        list($s, $ret) = DataService::getInstance()->updateMemberPoint($userId, $number);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/data/update-member-gold",
     *     summary="手动增加金币",
     *     description="更新会员信息",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"uid","number"},
     *          @OA\Property(property="uid", type="integer", default="", description="用户user_id"),
     *          @OA\Property(property="number", type="integer", default="", description="增加数量"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionUpdateMemberGold()
    {
        $userId = Yii::$app->request->post('uid', 0);
        $number = Yii::$app->request->post('number', 0);
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($userId, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(-1, '用户增加金币过于频繁，请稍后再试');
        }
        list($s, $ret) = DataService::getInstance()->updateMemberGold($userId, $number);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/data/template-down",
     *     summary="后台公共模板下载",
     *     description="后台公共模板下载",
     *     tags={"用户管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"template_id"},
     *          @OA\Property(property="template_id", type="integer", default="", description="模板ID；1001 共创人员模板下载"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionTemplateDown()
    {
        $template = Yii::$app->request->post('template_id', 0);
        if(empty($template)) CUtil::json_response(-1, '请填写模板ID');
        $url = self::UPLOAD_TEMPLATE_ID[$template] ?? '';
        if(empty($url)) CUtil::json_response(-1, '没有改模板文件');
        CUtil::json_response(1, 'OK',$url);
    }

    /**
     * @OA\Post(
     *     path="/back/data/zfb-password",
     *     summary="支付宝口令维护",
     *     description="支付宝口令维护",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"zfb_password","qr_code","zfb_link"},
     *          @OA\Property(property="id", type="integer", default="", description="ID 修改时必传"),
     *          @OA\Property(property="zfb_password", type="string", default="", description="支付宝口令"),
     *          @OA\Property(property="qr_code", type="string", default="", description="二维码落地页"),
     *          @OA\Property(property="zfb_link", type="string", default="", description="支付宝链接"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionZfbPassword()
    {
        $post = Yii::$app->request->post();
        list($s, $ret) = DataService::getInstance()->SaveZfbPassword($post);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }

        // 记录日志
        $msg  = json_encode($post, JSON_UNESCAPED_UNICODE);
        $msg1 = $post['id'] ?? '' ? "修改了支付宝口令ID：{$post['id']}" : "新增了支付宝口令：";
        $msg1 .= ",内容：{$msg}";
        (new SystemLogsModel())->record($msg1, RbacInfoModel::TRY_BEFORE_BUY);
        CUtil::json_response(1, $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/data/zfb-password-info",
     *     summary="支付宝口令详情",
     *     description="支付宝口令详情",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionZfbPasswordInfo()
    {
        list($s, $ret) = DataService::getInstance()->GetZfbPasswordInfo();
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }


    /**
     * @OA\Post(
     *     path="/back/data/survey-audit-list",
     *     summary="问卷审核列表",
     *     description="问卷审核列表",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          @OA\Property(property="audit_status", type="integer", default="", description="审核状态 -1全部 0待审核 1审核通过 2审核不通过"),
     *          @OA\Property(property="uid", type="string", default="", description="uid"),
     *          @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
     *          @OA\Property(property="name", type="string", default="", description="活动名称"),
     *          @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *          @OA\Property(property="label", type="integer", default="", description="标签（同商品标签）"),
     *          @OA\Property(property="goods_name", type="string", default="", description="商品名称"),
     *          @OA\Property(property="start_time", type="integer", default="", description="开始时间"),
     *          @OA\Property(property="end_time", type="integer", default="", description="结束时间"),
     *          @OA\Property(property="page", type="integer", default="", description="页码"),
     *          @OA\Property(property="page_size", type="integer", default="", description="每页显示条数"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSurveyAuditList()
    {
        $post     = Yii::$app->request->post();
        $page     = intval($post['page'] ?? 1);
        $pageSize = intval($post['page_size'] ?? 20);
        list($status, $data) = DataService::getInstance()->GetSurveyAuditList($post, $page, $pageSize);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, 'ok', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/data/survey-audit",
     *     summary="问卷审核",
     *     description="问卷审核",
     *     tags={"先用后付模块"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id","audit_status"},
     *          @OA\Property(property="id", type="integer", default="", description="ID"),
     *          @OA\Property(property="audit_status", type="string", default="", description="审核状态（WAIT_AUDIT待审核 AUDIT_PASS审核通过 AUDIT_NO_PASS审核不通过）"),
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionSurveyAudit()
    {
        $id          = Yii::$app->request->post('id', 0);
        $auditStatus = Yii::$app->request->post('audit_status', 'AUDIT_PASS');
        list($status, $data) = DataService::getInstance()->SurveyAudit($id, $auditStatus);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, 'ok', $data);
    }
}
