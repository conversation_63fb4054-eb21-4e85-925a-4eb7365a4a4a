<?php

namespace app\modules\back\controllers;

use app\constants\RespStatusCodeConst;
use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\sms\SendSmsForm;
use app\modules\back\forms\sms\SmsSearchForm;
use app\modules\back\services\SmsService;
use app\modules\rbac\controllers\CommController;

class SmsController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/sms/sms-tmpl-list",
     *     summary="短信模板列表",
     *     description="短信模板列表",
     *     tags={"批量发送短信"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionSmsTmplList()
    {
        // 获取短信模板信息
        $data = SmsService::getInstance()->getSmsTmplList();
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/sms/sms-tmpl",
     *     summary="根据模板ID，获取短信模板",
     *     description="根据模板ID，获取短信模板",
     *     tags={"批量发送短信"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              required={"code"},
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="code", type="string", default="", description="短信模板ID，例如：SMS_199796299"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionSmsTmpl()
    {
        // 短信模板ID
        $code = \Yii::$app->request->post('code');

        // 判空
        if (empty($code)) {
            CUtil::json_response(-1, '请求参数错误');
        }

        // 获取短信模板信息
        $data = SmsService::getInstance()->getSmsTmplByCode($code);
        CUtil::json_response(1, 'ok', $data);
    }

    /**
     * @OA\Post(
     *     path="/back/sms/send-sms",
     *     summary="发送短信",
     *     description="发送短信",
     *     tags={"批量发送短信"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="multipart/form-data",
     *          @OA\Schema(
     *              required={"code", "file"},
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="code", type="string", default="", description="短信模板ID，例如：SMS_199796299"),
     *              @OA\Property(property="file", type="file", default="", description="上传的导入文件"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionSendSms()
    {
        // 频率限制
        $unique_key = CUtil::getAllParams(__FUNCTION__);
        list($s) = by::model("CommModel", MAIN_MODULE)->AccFrequency($this->user_id, $unique_key, 5, "EX", 1);
        if (!$s) {
            CUtil::json_response(RespStatusCodeConst::SMS_SEND_ERROR_CODE, '5秒内不可频繁操作，避免重复发送');
        }

        // 请求参数
        $params = \Yii::$app->request->post();
        $params['file'] = $_FILES['file'] ?? null;

        // 验证参数
        $form = new SendSmsForm();
        $form->load($params, '');

        // 验证条件
        if (!$form->validate()) {
            // 错误信息
            $errors = $form->firstErrors;
            CUtil::json_response(RespStatusCodeConst::SMS_SEND_ERROR_CODE, array_shift($errors));
        }

        // 读取文件内容
        list($status, $data) = CUtil::readCsv($params['file']['tmp_name'] ?? '');
        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::SMS_SEND_ERROR_CODE, $data);
        }

        // 发送短信
        list($status, $res) = SmsService::getInstance()->sendSms($form->code, $data);
        if (!$status) {
            CUtil::json_response(RespStatusCodeConst::SMS_SEND_ERROR_CODE, $res);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/sms/sms-list",
     *     summary="短信列表",
     *     description="短信列表",
     *     tags={"批量发送短信"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              required={"code", "file"},
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="phone", type="integer", default="", description="手机号"),
     *              @OA\Property(property="s_send_time", type="integer", default="", description="发送开始时间"),
     *              @OA\Property(property="e_send_time", type="integer", default="", description="发送结束时间"),
     *              @OA\Property(property="page", type="integer", default="0", description="页码"),
     *              @OA\Property(property="page_size", type="integer", default="20", description="每页数量"),
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionSmsList()
    {
        // 查询条件
        $form = new SmsSearchForm();
        $form->load(\Yii::$app->request->post(), '');

        // 验证条件
        if (!$form->validate()) {
            $errors = $form->firstErrors;
            CUtil::json_response(-1, array_shift($errors));
        }

        // 查询数据
        $data  = SmsService::getInstance()->getSmsSendRecordList($form->toArray());
        if(isset($data['items'])) $data['items'] = Response::responseList($data['items'],['phone'=>'tm']);
        CUtil::json_response(1, 'ok', $data);
    }
}
