<?php

/**
 * Created by PhpStorm.
 * User: Administrator
 * 发放优惠活动
 * Date: 2022/10/13
 * Time: 16:29
 */

namespace app\modules\back\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use Yii;

class DiscountController extends CommController {



    /**
     * 获取已发券用户的日志
     * @throws \yii\db\Exception
     */
    public function actionGetUserCardLog()
    {
        $userId = $this->user_info['id'] ?? 0;
        if(empty($userId))  CUtil::json_response(-1,'用户不存在');
        list($status,$data) = by::userCard()->__pushCardRedis([],[],$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1,'ok',$data);
    }

    /**
     * 获取已废券用户的日志
     * @throws \yii\db\Exception
     */
    public function actionDelUserCardLog()
    {
        $userId = $this->user_info['id'] ?? 0;
        if(empty($userId))  CUtil::json_response(-1,'用户不存在');
        list($status,$data) = by::userCard()->__pushDelCardRedis([],[],$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1,'ok',$data);
    }



    /**
     * 在后台给用户批量发放卡券
     * @throws \yii\db\Exception
     */
    public function actionBatchGiveUserCard()
    {
        $file = $_FILES['file'] ?? "";
        //用户卡号或者电话号码
        $userNo = Yii::$app->request->post('user_no','');
        //发放卡券ID
        $market_id         = Yii::$app->request->post('market_id',0);

        list($status,$data) = by::userCard()->batchSendUserCard($userNo,$file,$market_id,$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1,'ok',$data);
    }

    /**
     * 在后台给用户批量废除卡券
     * @throws \yii\db\Exception
     */
    public function actionBatchDelUserCard()
    {
        $file = $_FILES['file'] ?? "";
        //用户卡号或者电话号码
        $userNo = Yii::$app->request->post('user_no','');
        //发放卡券ID
        $market_id         = Yii::$app->request->post('market_id',0);

        //初步核验参数
        if($userNo && (!is_string($userNo)||!is_numeric($userNo))){
            CUtil::json_response(-1, '用户卡号或者电话号码不正确！');
        }
        list($status,$data) = by::userCard()->batchDelUserCard($userNo,$file,$market_id,$this->user_info['id']??0);
        if(!$status){
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1,'ok',$data);

    }

    public function actionUserCardTemplate()
    {
        $templateId   = intval(Yii::$app->request->post('template_id',1));
//        if($templateId == 1){
//            $headList = [
//                "会员卡号/手机号",
////            "优惠券个数",
////            "限制优惠券个数"
//            ];
//            $data[0][0] = '不要存在空行或重复行（勿删）';
//            $fileName   = '批量手动发券模板-' . date('Ymd') . mt_rand(1000, 9999);
//        }else{
//            $headList = [
//                "会员卡号/手机号",
////            "废券个数"
//            ];
//            $data[0][0] = '不要存在空行或重复行（勿删）';
//            $fileName   = '批量手动废券模板-' . date('Ymd') . mt_rand(1000, 9999);
//        }
//        list($status,$data) = by::userCard()->exportCardTemplate($headList,$data,$fileName);
//        if(!$status){
//            CUtil::json_response(-1, $data);
//        }
        $fileName = ($templateId==1)?'批量手动发券模板-202210211657.csv':'批量手动废券模板-202210212542.csv';
        $data = [
          'url'=>'https://wpm-cdn.dreame.tech/files/202210/'.$fileName,
        ];
        CUtil::json_response(1,'ok',$data);
    }

}
