<?php

namespace app\modules\back\controllers;


use app\models\CUtil;
use app\modules\back\forms\tradeIn\TradeInActivityForm;
use app\modules\back\services\TradeInActivityService;
use app\modules\rbac\controllers\CommController;

class TradeInActivityController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/trade-in-activity/list",
     *     summary="活动列表获取",
     *     description="获取当前交易活动的列表信息，包括活动的名称、时间及状态等信息。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="name", type="string", description="活动名称"),
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="活动列表获取成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="返回码"),
     *             @OA\Property(property="sMsg", type="string", description="返回信息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="活动列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="活动ID"),
     *                         @OA\Property(property="name", type="string", description="活动名称"),
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                         @OA\Property(property="status", type="string", description="活动状态"),
     *                         @OA\Property(property="has_goods", type="string", description="是否包含商品")
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于验证用户身份",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionList()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInActivityForm(), 'list');
        $data = TradeInActivityService::getInstance()->list($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-activity/detail",
     *     summary="获取活动详情",
     *     description="检索指定活动的详细信息，包括商品列表、优惠券、时间等信息。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                   required={"id"},
     *                  @OA\Property(property="id", type="string", description="活动ID")
     *                 )
     *             }
     *          )
     *        )
     *    ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="活动详细数据",
     *                 @OA\Property(property="id", type="string", description="活动ID"),
     *                 @OA\Property(property="name", type="string", description="活动名称"),
     *                 @OA\Property(property="picture", type="string", description="活动图片"),
     *                 @OA\Property(property="products", type="string", description="活动商品列表"),
     *                 @OA\Property(property="coupons", type="string", description="活动优惠券"),
     *                 @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                 @OA\Property(property="extras", type="string", description="活动附加信息"),
     *                 @OA\Property(property="status", type="string", description="活动状态，1: 上架, 2: 下架"),
     *                 @OA\Property(property="is_del", type="string", description="活动是否删除"),
     *                 @OA\Property(property="ctime", type="string", description="创建时间"),
     *                 @OA\Property(property="utime", type="string", description="更新时间"),
     *                 @OA\Property(property="dtime", type="string", description="删除时间"),
     *                 @OA\Property(property="has_goods", type="string", description="是否有商品"),
     *                 @OA\Property(
     *                     property="goodsList",
     *                     type="array",
     *                     description="商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="category", type="string", description="商品类别"),
     *                         @OA\Property(
     *                             property="list",
     *                             type="array",
     *                             @OA\Items(
     *                                 @OA\Property(property="id", type="string", description="商品ID"),
     *                                 @OA\Property(property="gid", type="string", description="商品全局ID"),
     *                                 @OA\Property(property="sid", type="string", description="商品规格ID"),
     *                                 @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                                 @OA\Property(property="price", type="string", description="商品价格"),
     *                                 @OA\Property(property="underline_price", type="string", description="商品划线价格"),
     *                                 @OA\Property(property="tadeInGoods", type="object", description="兑换商品信息"),
     *                                 @OA\Property(property="spec", type="array", @OA\Items(type="string"), description="商品规格")
     *                             )
     *                         )
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionDetail()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInActivityForm(), 'detail');
        $data = TradeInActivityService::getInstance()->detail($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-activity/store",
     *     summary="活动新增、编辑",
     *     description="用于新增或编辑电子商务平台的促销活动。",
     *     tags={"以旧换新"},
     *         @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *         @OA\JsonContent(
     *             allOf={
     *             @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *             @OA\Schema(
     *                  @OA\Property(property="name", type="string", description="活动名称"),
     *                  @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                  @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                  @OA\Property(property="id", type="string", description="活动ID"),
     *                  @OA\Property(property="picture", type="string", description="活动图片URL"),
     *                  @OA\Property(property="extras", type="string", description="附加信息，包括详细的参与规则和常见问题"),
     *                  @OA\Property(property="goods", type="string", description="参与活动的商品信息")
     *                 )
     *             }
     *          )
     *        )
     *    ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             type="object",
     *             description="响应成功时返回的信息，通常为空对象"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionStore()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInActivityForm(), 'store');
        $data = TradeInActivityService::getInstance()->store($form);
        CUtil::Ret($data);
    }


    public function actionDel()
    {
        // 参数验证
        $form = CUtil::VdForm(new TradeInActivityForm(), 'detail');
        $data = TradeInActivityService::getInstance()->delete($form->id);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/trade-in-activity/status",
     *     summary="活动上下架",
     *     description="用于更改活动的上下架状态。",
     *     tags={"以旧换新"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id", "status"},
     *                         @OA\Property(property="id", type="string", description="活动ID"),
     *                         @OA\Property(property="status", type="string", description="状态，1表示上架，2表示下架")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             description="操作成功，没有额外数据返回"
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionStatus()
    {
        $form = CUtil::VdForm(new TradeInActivityForm(), 'status');
        $data = TradeInActivityService::getInstance()->status($form);
        CUtil::Ret($data);
    }

}