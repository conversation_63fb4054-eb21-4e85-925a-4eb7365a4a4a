<?php

namespace app\modules\back\controllers;

use app\exceptions\UploadFileException;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\AsyncExportService;
use app\modules\rbac\controllers\CommController;

/**
 * 异步导出任务
 */
class AsyncExportController extends CommController
{
    /**
     * @OA\Post(
     *     path="/back/async-export/log-list",
     *     summary="导出任务列表",
     *     description="导出任务列表",
     *     tags={"导出任务"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *                @OA\Property(property="page", type="integer", default="1", description="第几页"),
     *                @OA\Property(property="page_size", type="integer", default="20", description="每页多少数据")
     *              )
     *          }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionLogList()
    {
        // 参数
        $params = \Yii::$app->request->post();
        $params['page'] = $params['page'] ?? 1;
        $params['page_size'] = $params['page_size'] ?? 20;

        // 操作用户
        $params['admin_user_id'] = $this->user_info['id'] ?? 0;

        // 获取列表数据
        $asyncExportService = new AsyncExportService();
        $res = $asyncExportService->getAsyncExportLogList($params);
        CUtil::json_response(1, 'ok', $res);
    }

    /**
     * 统一导出接口
     */
    public function actionExport()
    {
        // 参数
        $params = \Yii::$app->request->post();
        $act_type = $params['act_type'] ?? '';
        // 用户信息
        $user_info = $this->user_info;

        // 数据脱敏
        $check_ret = by::adminUserModel()->checkAuth($user_info, 'back/data/view_sensitive');
        $is_sensitive = false;
        if ($check_ret) {
            $is_sensitive = true;
        }

        // 操作
        try {
            $asyncExportService = new AsyncExportService();
            $asyncExportService->export($user_info['id'], $act_type, $params, $is_sensitive);
            // 返回ok
            CUtil::json_response(1, 'ok');
        } catch (UploadFileException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            // 写入日志文件
            CUtil::debug(sprintf("异步导出失败，失败原因：%s，参数：%s。", $e->getMessage(), json_encode($params)), 'async_export.buy');
            CUtil::json_response(-1, '导出失败');
        }
    }
}