<?php

namespace app\modules\back\controllers;

use app\models\BusinessException;
use app\modules\back\forms\system\SystemDictDataForm;
use app\modules\back\services\system\SystemDictDataService;
use app\modules\common\ControllerTrait;
use app\modules\rbac\controllers\CommController;
use OpenApi\Annotations as OA;

/**
 * 字典数据
 * 接口文档：https://doc.apipost.net/docs/detail/45575e29b0e0000?target_id=1160852035d018&locale=zh-cn
 * 文档密码：593336
 */
class DictDataController extends CommController
{
    use ControllerTrait;

    /** @var SystemDictDataService */
    private $service;

    public function __construct($id, $module, $config = [])
    {
        $this->service = SystemDictDataService::getInstance();
        parent::__construct($id, $module, $config);
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/index",
     *     summary="获取字典数据列表-有分页",
     *     description="获取字典数据列表-有分页",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="活动列表数据",
     *                  @OA\Items()
     *              )
     *         )
     *     )
     * )
     */
    public function actionIndex()
    {
        try {
            $post = \Yii::$app->request->post();
            $this->success($this->service->getPageList($post));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/list",
     *     summary="获取字典数据列表-无分页",
     *     description="获取字典数据列表-无分页",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="array",description="活动列表数据",
     *                  @OA\Items()
     *              )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        try {
            $post = \Yii::$app->request->post();
            $this->success($this->service->getList($post));
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/create",
     *     summary="新增字典",
     *     description="新增字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="name", type="string", description="字典名称"),
     *                 @OA\Property(property="code", type="string", description="字典标识"),
     *                 @OA\Property(property="status", type="integer", description="状态 0=禁用，1=正常"),
     *                 @OA\Property(property="remark", type="string", description="备注"),
     *                 required={"name", "code"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(property="data", type="object",
     *                 @OA\Property(property="id", type="integer", description="数据ID")
     *             )
     *         )
     *     )
     * )
     */
    public function actionCreate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new SystemDictDataForm();
            $form->setScenario('create');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $id, $msg) = $this->service->create($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success(['id' => (int)$id], $msg);
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/update",
     *     summary="更新字典",
     *     description="更新字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="字典ID"),
     *                 @OA\Property(property="name", type="string", description="字典名称"),
     *                 @OA\Property(property="code", type="string", description="字典标识"),
     *                 @OA\Property(property="status", type="integer", description="状态 0=禁用，1=正常"),
     *                 @OA\Property(property="remark", type="string", description="备注"),
     *                 required={"id", "name", "code"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionUpdate()
    {
        try {
            $post = \Yii::$app->request->post();
            $form = new SystemDictDataForm();
            $form->setScenario('update');
            if (! ($form->load($post, '') && $form->validate())) {
                $errors = $form->firstErrors;
                $this->error(array_shift($errors));
            }

            list($status, $msg) = $this->service->update($form->toArray());
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/delete",
     *     summary="删除字典",
     *     description="删除字典",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="ids", type="string", description="字典ID，多个ID用英文逗号分隔"),
     *                 required={"ids"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        try {
            $post = \Yii::$app->request->post();
            $ids = $post['ids'] ?? '';
            if (empty($ids)) {
                $this->error('缺少ids字段');
            }
            $ids = explode(',', (string)$ids);
            $ids = array_unique($ids);

            list($status, $msg) = $this->service->delete($ids);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/change-status",
     *     summary="更新状态",
     *     description="更新状态",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="电字典ID"),
     *                 @OA\Property(property="status", type="integer", description="商品状态值"),
     *                 required={"id", "status"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionChangeStatus()
    {
        try {
            $post = \Yii::$app->request->post();
            list($status, $msg) = $this->service->changeStatus($post);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/change-sort",
     *     summary="更新排序",
     *     description="更新排序",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 },
     *                 @OA\Property(property="id", type="integer", description="电字典ID"),
     *                 @OA\Property(property="sort", type="integer", description="排序值"),
     *                 required={"id", "status"}
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionChangeSort()
    {
        try {
            $post = \Yii::$app->request->post();
            list($status, $msg) = $this->service->changeSort($post);
            if (! $status) {
                throw new BusinessException($msg);
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }

    /**
     * @OA\Post(
     *     path="/back/dict-data/clear-cache",
     *     summary="清除字典缓存",
     *     description="清除字典缓存",
     *     tags={"数据字典"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息")
     *         )
     *     )
     * )
     */
    public function actionClearCache()
    {
        try {
            $post = \Yii::$app->request->post();
            $status = $this->service->clearCache($post);
            if (! $status) {
                throw new BusinessException('清除缓存失败');
            }
            $this->success();
        } catch (\Throwable $e) {
            $this->error($e->getMessage());
        }
    }
}