<?php

namespace app\modules\back\controllers;


use app\models\by;
use app\models\CUtil;
use app\modules\back\services\GoodsParamService;

use app\modules\back\services\GrecommendService;
use app\modules\rbac\controllers\CommController;
use RedisException;
use yii\db\Exception;

/**
 * 推荐商品
 */
class GoodsRecommendController extends CommController
{

    /**
     * @OA\Post(
     *     path="/back/goods-recommend/goods-list",
     *     summary="二级类目下商品列表",
     *     description="二级类目下商品列表",
     *     tags={"类目"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"pid"},
     *                         @OA\Property(property="pid", type="integer", default="", description="二级类目ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/RecommendListResource",description="数据")
     *         )
     *     )
     * )
     */
    public function actionGoodsList()
    {
        $pid = \Yii::$app->request->post('pid') ?? 0;
        list($status, $ret) = GrecommendService::getInstance()->GetCateGoodsDataService($pid);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/goods-recommend/save",
     *     summary="添加、编辑-推荐商品",
     *     description="添加、编辑-推荐商品",
     *     tags={"类目"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"gid","image","title","cate_id","tid"},
     *                         @OA\Property(property="gid", type="integer", default="", description="主推商品ID"),
     *                         @OA\Property(property="image", type="string", default="", description="主推商品图片"),
     *                         @OA\Property(property="pc_image", type="string", default="", description="PC主推商品图片"),
     *                         @OA\Property(property="h5_image", type="string", default="", description="H5主推商品图片"),
     *                         @OA\Property(property="title", type="string", default="", description="标题"),
     *                         @OA\Property(property="pcombines", type="string", default="", description="次推商品ID（,分割）"),
     *                         @OA\Property(property="cate_id", type="integer", default="", description="二级类目ID"),
     *                         @OA\Property(property="tid", type="integer", default="", description="标签（10洗地机、11扫地机、12吸尘器、13吹风机）"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code","msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        $post    = \Yii::$app->request->post();
        $cate_id = $post['cate_id'] ?? '';
        list($status, $ret) = GrecommendService::getInstance()->saveGoodsService($post, $cate_id);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/goods-recommend/info",
     *     summary="详情-推荐商品",
     *     description="详情-推荐商品",
     *     tags={"类目"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"cate_id"},
     *                         @OA\Property(property="cate_id", type="integer", default="", description="二级类目ID"),
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/RecommendDetailResource",description="数据")
     *         )
     *     )
     * )
     */
    public function actionInfo()
    {
        $post    = \Yii::$app->request->post();
        $cateId = $post['cate_id'] ?? '';
        list($status,$ret) = GrecommendService::getInstance()->getRecommendInfoService($cateId);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok', $ret);
    }

    /**
     * @OA\Post(
     *     path="/back/goods-recommend/goods-tag",
     *     summary="标签-推荐商品",
     *     description="标签-推荐商品",
     *     tags={"类目"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *             required={"code","msg"},
     *             @OA\Property(property="code", type="integer", description="状态码"),
     *             @OA\Property(property="msg", type="string", description="提示消息"),
     *             @OA\Property(property="success", type="boolean", description="是否成功"),
     *             @OA\Property(property="data", type="array", description="数据")
     *         )
     *     )
     * )
     */
    public function actionGoodsTag()
    {
        // $goodsTag = file_get_contents(__DIR__ . '/../../main/enums/GoodsRecommend/PcGoodsTag.json');
        // $goodsTag = json_decode($goodsTag, true);
        $tagMap = by::Gtag()->GetTagNameMap();
        $tagImageMap = by::Gtag()->GetTagImageMap();
        $recommendTag = by::Gtag()->GetRecommendTag();
        $data = [];
        foreach ($tagMap as $tagId => $tagName) {
            if (in_array($tagId, $recommendTag)) {
                $data[] = [
                    'text' => $tagName,
                    'tid' => $tagId,
                    'image' => $tagImageMap[$tagId] ?? '',
                ];
            }
        }
        CUtil::json_response(1,'ok', $data);
    }

}