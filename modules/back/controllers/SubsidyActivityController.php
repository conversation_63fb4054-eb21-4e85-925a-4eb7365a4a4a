<?php

namespace app\modules\back\controllers;

use app\models\CUtil;
use app\modules\back\forms\SubsidyActivityForm;
use app\modules\back\services\SubsidyActivityService;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\rbac\controllers\CommController;

/**
 *
 * 国补活动控制器
 */
class SubsidyActivityController extends CommController
{
    /**
     * 获取活动列表
     * @OA\Post(
     *     path="/back/subsidy-activity/list",
     *     summary="获取国补活动列表",
     *     description="获取国补活动列表，支持分页和搜索",
     *     tags={"国补活动管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="page", type="integer", description="页码", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=10),
     *                 @OA\Property(property="search_name", type="string", description="活动名称搜索", example="双11国补活动"),
     *                 @OA\Property(property="search_status", type="integer", description="活动状态：1-未开始，2-进行中，3-已结束", example=2)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="ok"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="total", type="integer", description="总数", example=100),
     *                 @OA\Property(property="page", type="integer", description="当前页", example=1),
     *                 @OA\Property(property="page_size", type="integer", description="每页数量", example=10),
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="integer", description="活动ID", example=1),
     *                         @OA\Property(property="name", type="string", description="活动名称", example="双11国补活动"),
     *                         @OA\Property(property="start_time", type="integer", description="开始时间戳", example=1672531200),
     *                         @OA\Property(property="end_time", type="integer", description="结束时间戳", example=1672617600),
     *                         @OA\Property(property="desc", type="string", description="活动描述", example="双11国补活动描述"),
     *                         @OA\Property(property="status", type="integer", description="活动状态", example=2),
     *                         @OA\Property(property="create_time", type="integer", description="创建时间戳", example=1672531200),
     *                         @OA\Property(property="time_status", type="string", description="时间状态文本", example="进行中"),
     *                         @OA\Property(property="is_active", type="boolean", description="是否正在进行", example=true)
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionList()
    {
        try {
            $form    = CUtil::VdForm(new SubsidyActivityForm(['scenario' => 'list']));
            $service = SubsidyActivityService::getInstance();
            $result  = $service->getActivityList($form);

            CUtil::json_response(1, 'ok', $result);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '获取列表失败');
        }
    }

    /**
     * 获取活动详情
     * @OA\Post(
     *     path="/back/subsidy-activity/detail",
     *     summary="获取国补活动详情",
     *     description="根据活动ID获取国补活动详情信息，包括关联商品",
     *     tags={"国补活动管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="id", type="integer", description="活动ID", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="ok"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", description="活动ID", example=1),
     *                 @OA\Property(property="name", type="string", description="活动名称", example="双11国补活动"),
     *                 @OA\Property(property="start_time", type="integer", description="开始时间戳", example=1672531200),
     *                 @OA\Property(property="end_time", type="integer", description="结束时间戳", example=1672617600),
     *                 @OA\Property(property="desc", type="string", description="活动描述", example="双11国补活动描述"),
     *                 @OA\Property(property="status", type="integer", description="活动状态", example=2),
     *                 @OA\Property(property="create_time", type="integer", description="创建时间戳", example=1672531200),
     *                 @OA\Property(property="time_status", type="string", description="时间状态文本", example="进行中"),
     *                 @OA\Property(property="is_active", type="boolean", description="是否正在进行", example=true),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     description="关联商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="gid", type="integer", description="商品ID", example=123),
     *                         @OA\Property(property="sku", type="string", description="商品SKU", example="DM001"),
     *                         @OA\Property(property="name", type="string", description="商品名称", example="追觅扫地机器人"),
     *                         @OA\Property(property="cover_image", type="string", description="商品封面图", example="https://example.com/image.jpg"),
     *                         @OA\Property(property="price", type="number", description="商品价格", example=1999.00),
     *                         @OA\Property(property="subsidy_ratio", type="number", description="国补比例", example=15.50)
     *                     )
     *                 )
     *             )
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        $form = CUtil::VdForm(new SubsidyActivityForm(['scenario' => 'detail']));

        try {
            $service = SubsidyActivityService::getInstance();
            $result  = $service->getActivityDetail($form->id);

            if (empty($result)) {
                CUtil::json_response(-1, '活动不存在');
            }

            CUtil::json_response(1, 'ok', $result);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '获取详情失败：' . $e->getMessage());
        }
    }

    /**
     * 保存活动（新增/编辑）
     * @OA\Post(
     *     path="/back/subsidy-activity/save",
     *     summary="保存国补活动",
     *     description="新增或编辑国补活动，包括关联商品信息",
     *     tags={"国补活动管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="id", type="integer", description="活动ID，为空表示新增", example=1),
     *                 @OA\Property(property="name", type="string", description="活动名称", example="双11国补活动"),
     *                 @OA\Property(property="start_time", type="integer", description="开始时间戳", example=1672531200),
     *                 @OA\Property(property="end_time", type="integer", description="结束时间戳", example=1672617600),
     *                 @OA\Property(property="desc", type="string", description="活动描述", example="双11国补活动描述"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     description="参与活动的商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="gid", type="integer", description="商品ID", example=123),
     *                         @OA\Property(property="subsidy_ratio", type="number", description="国补比例（0.01-100）", example=15.50)
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="保存成功"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 @OA\Property(property="id", type="integer", description="活动ID", example=1)
     *             )
     *         )
     *     )
     * )
     */
    public function actionSave()
    {
        $form = CUtil::VdForm(new SubsidyActivityForm(['scenario' => 'save']));

        try {
            $service = SubsidyActivityService::getInstance();

            // 编辑时检查是否可以编辑
            if ($form->id) {
                list($can_edit, $message) = $service->canEditActivity($form->id);
                if (!$can_edit) {
                    CUtil::json_response(-1, $message);
                }
            }

            list($success, $result) = $service->saveActivity($form);

            if (!$success) {
                CUtil::json_response(-1, $result);
            }

            // 记录操作日志
            $action = $form->id ? '编辑' : '新增';
            $msg    = "{$action}国补活动：{$form->name}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

            CUtil::json_response(1, '保存成功', ['id' => $result]);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '保存失败：' . $e->getMessage());
        }
    }

    /**
     * 删除活动
     * @OA\Post(
     *     path="/back/subsidy-activity/delete",
     *     summary="删除国补活动",
     *     description="根据活动ID删除国补活动（软删除）",
     *     tags={"国补活动管理"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\Schema(
     *                 @OA\Property(property="id", type="integer", description="活动ID", example=1)
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码，1成功，-1失败", example=1),
     *             @OA\Property(property="sMsg", type="string", description="提示信息", example="删除成功"),
     *             @OA\Property(property="data", type="object", description="返回数据，一般为空")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        $form = CUtil::VdForm(new SubsidyActivityForm(['scenario' => 'delete']));

        try {
            $service = SubsidyActivityService::getInstance();

            // 检查是否可以删除
            list($can_delete, $message) = $service->canDeleteActivity($form->id);
            if (!$can_delete) {
                CUtil::json_response(-1, $message);
            }


            list($success, $message) = $service->deleteActivity((int)$form->id);

            if (!$success) {
                CUtil::json_response(-1, $message);
            }

            // 记录操作日志
            $msg = "删除国补活动 ID：{$form->id}";
            (new SystemLogsModel())->record($msg, RbacInfoModel::MARKET_MANAGER);

            CUtil::json_response(1, $message);
        } catch (\Exception $e) {
            CUtil::json_response(-1, '删除失败');
        }
    }




}
