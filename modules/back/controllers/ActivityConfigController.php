<?php


namespace app\modules\back\controllers;


use app\exceptions\ActivityException;
use app\jobs\AutoCouponActivityJob;
use app\models\by;
use app\models\CUtil;
use app\models\Response;
use app\modules\back\forms\activity\ActivityModifyForm;
use app\modules\back\services\activities\Activity;
use app\modules\back\services\activities\AutoCouponActivity;
use app\modules\back\services\activities\NewGiftActivity;
use app\modules\back\services\ActivityService;
use app\modules\main\models\ActivityConfigModel;
use app\modules\main\models\AcType7Model;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use Yii;
use yii\db\Exception;
use yii\db\StaleObjectException;

class ActivityConfigController extends CommController
{

    /**
     * TODO 获取配置列表页
     * @throws Exception
     */
    public function actionList()
    {
        $page       = Yii::$app->request->post('page', 1);
        $page_size  = 20;
        $name       = Yii::$app->request->post('name', '');
        $start_time = Yii::$app->request->post('start_time', 0);
        $end_time   = Yii::$app->request->post('end_time', 0);
        $grant_type = Yii::$app->request->post('grant_type', '-1');
        $status     = Yii::$app->request->post('status', '');
        $input      = [
                'name'       => $name,
                'start_time' => $start_time,
                'end_time'   => $end_time,
                'grant_type' => $grant_type,
                'status'     => $status,
        ];

        $data = by::activityConfigModel()->getList($input, $page, $page_size);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/activity-config/modify",
     *     summary="新建活动",
     *     description="新建活动",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ActivityDetailModify"),
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionModify()
    {
        // 请求参数
        $data       = \Yii::$app->request->post();
        $data['id'] = CUtil::uint($data['id'] ?? 0);

        // 验证参数
        $form = new ActivityModifyForm();
        $form->load($data, '');
        if (!$form->validate()) {
            // 错误信息
            $errors = $form->firstErrors;
            CUtil::json_response(-1, array_shift($errors));
        }

        // 执行业务
        try {
            $activityService = new ActivityService();
            $activityService->modify($data);
        } catch (ActivityException $e) {
            CUtil::json_response(-1, $e->getMessage());
        } catch (\Exception $e) {
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            CUtil::json_response(-1, '添加/编辑活动暂不可用');
        }

        if (isset($data['rule_note'])) {
            unset($data['rule_note']);
        }
        // 记录日志
        $msg  = json_encode($data, JSON_UNESCAPED_UNICODE);
        $msg1 = $data['id'] ? "修改了活动配置ID：{$data['id']}" : "新增了活动配置：{$data['name']}";
        $msg1 .= ",内容：{$msg}";
        (new SystemLogsModel())->record($msg1, RbacInfoModel::ACTIVITY_MANAGER);

        // 返回结果
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/activity-config/detail",
     *     summary="活动详情",
     *     description="活动详情",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="活动ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDetail()
    {
        $id = Yii::$app->request->post('id');
        if (empty($id)) {
            CUtil::json_response(-1, 'ID不能为空');
        }

        $data = by::activityConfigModel()->getActivityOne($id);
        $data = Response::responseList($data, ['end_time' => 'int', 'start_time' => 'int', 'attributes' => 'array']);
        CUtil::json_response(1, '获取成功', $data);
    }


    /**
     * @OA\Post(
     *     path="/back/activity-config/delete",
     *     summary="删除活动配置",
     *     description="删除活动配置",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"id"},
     *          @OA\Property(property="id", type="integer", default="", description="活动ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",ref="#/components/schemas/ActivityConfigDetail",description="数据")
     *         )
     *     )
     * )
     */
    public function actionDelete()
    {
        $id = Yii::$app->request->post('id', null);

        list($st, $msg) = by::activityConfigModel()->deleteData($id);
        if ($st == false) {
            CUtil::json_response(-1, $msg);
        }

        (new SystemLogsModel())->record("删除活动id为:{$id}", RbacInfoModel::ACTIVITY_MANAGER);

        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/activity-config/am-list",
     *     summary="活动下的优惠券列表",
     *     description="活动下的优惠券列表",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"ac_id"},
     *          @OA\Property(property="ac_id", type="integer", default="", description="活动ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAmList()
    {
        $ac_id = Yii::$app->request->post('ac_id', null);

        $list = by::aM()->getListByAid($ac_id);
        // 将level转为数组
        foreach ($list as &$value) {
            $level          = $value['level'] ?? '';
            $value['level'] = $level ? explode(',', $level) : [];
        }
        CUtil::json_response(1, 'OK', $list);
    }

    /**
     * @OA\Post(
     *     path="/back/activity-config/am-del",
     *     summary="删除活动下的优惠券",
     *     description="删除活动下的优惠券",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(
     *          required = {"am_id"},
     *          @OA\Property(property="am_id", type="integer", default="", description="活动与有优惠卷关联ID")
     *        )
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAmDel()
    {
        $am_id = Yii::$app->request->post('am_id', null);

        list($status, $ret) = by::aM()->del($am_id);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("删除活动绑定优惠券:" . json_encode($ret), RbacInfoModel::ACTIVITY_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK');
    }


    /**
     * @OA\Post(
     *     path="/back/activity-config/am-save",
     *     summary="添加、编辑活动下的优惠券",
     *     description="添加、编辑活动下的优惠券",
     *     tags={"后台活动"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *       @OA\JsonContent(
     *          allOf={
     *          @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *          @OA\Schema(ref="#/components/schemas/ActivityAmSaveResource")
     *       }
     *      )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *        @OA\JsonContent(
     *             type="object",
     *              @OA\Property(property="iRet",type="integer",description="状态码"),
     *              @OA\Property(property="sMsg",type="string",description="提示消息"),
     *              @OA\Property(property="data",type="object",description="数据")
     *         )
     *     )
     * )
     */
    public function actionAmSave()
    {
        $id    = Yii::$app->request->post('id', null);
        $ac_id = Yii::$app->request->post('ac_id', null);
        $mc_id = Yii::$app->request->post('mc_id', null);
        $stock = Yii::$app->request->post('stock', null);
        $level = Yii::$app->request->post('level', '');

        if ($id) {
            $smg = '修改';
            list($status, $ret) = by::aM()->edit($id, $ac_id, $mc_id, $stock, $level);
        } else {
            $smg = '新增';
            list($status, $ret) = by::aM()->add($ac_id, $mc_id, $stock, $level);
        }

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        //写入日志
        (new SystemLogsModel())->record("{$smg}活动绑定优惠券，活动id={$ac_id}资源id={$mc_id}库存={$stock}", RbacInfoModel::ACTIVITY_MANAGER, $this->user_info['id']);

        CUtil::json_response(1, 'OK', $ret);
    }

    /**
     * @throws Exception
     * 推荐有礼统计
     */
    public function actionCensus()
    {
        $list = by::UserRecommend()->census();

        CUtil::json_response(1, 'OK', $list);
    }


    public function actionActivityLink()
    {
        $post = Yii::$app->request->post();

        list($s, $res) = by::model('WxUlinkModel', 'main')->activityLink($post);

        if (!$s) {
            CUtil::json_response(-1, $res);
        }

        CUtil::json_response(1, 'OK', $res);
    }

    /**
     * 活动类型
     * @return void
     */
    public function actionActivityTypes()
    {
        $data  = [];
        $types = by::activityConfigModel()::GRANT_TYPE_NAMES;
        foreach ($types as $id => $name) {
            $data[] = [
                    'id'   => $id,
                    'name' => $name,
            ];
        }
        CUtil::json_response(1, 'OK', $data);
    }

    /**
     * @throws Exception
     * @throws \Throwable
     */
    public function actionTest()
    {

//        $redis = by::redis('core');
//        $keys  = $redis->keys('dreame|getAutoCouponSkuKey|*');
//        foreach ($keys as $key) {
//           $data = $redis->get($key);
//           $ttl = $redis->ttl($key);
//           $redis->del($key);
//
//           dump($key, $data, $ttl);
//        }


//        $gids=[['gid'=>'837','sid'=>'0']];
        $activity = new AutoCouponActivity();
        $couponList = by::acType7()->getAvailableCoupons(['202412070003','202412070004','202412070005']);
        dd($couponList);

        exit;
//        $skus = $activity->getSku($gids);
//        $couponList = by::acType7()->getAvailableCoupons($skus);

        $activity->send(7995, '20241210624130156115622412135');
        CUtil::json_response(1, 'OK', []);
    }

}
