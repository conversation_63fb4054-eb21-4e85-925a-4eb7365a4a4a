<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/4/30
 * Time: 18:29
 */
namespace app\modules\back\controllers;

use app\models\by;
use app\models\CUtil;
use app\modules\rbac\controllers\CommController;
use app\modules\rbac\models\RbacInfoModel;
use app\modules\rbac\models\SystemLogsModel;
use RedisException;
use yii\db\Exception;


class WikiController extends CommController {

    /**
     * 话题列表
     */
    public function actionTlist() {
        $post       = \Yii::$app->request->post();
        $page       = $post['page']     ?? 1;
        $level      = $post['level']    ?? 0;
        $pid        = -1;
        $page_size  = 50;

        $mWtopic    = by::Wtopic();
        $list       = $mWtopic->GetList($level, $pid, $page, $page_size);
        foreach($list as $val) {
            $data[] = $mWtopic->GetOneById($val['id']);
        }

        $return['list']     = $data ?? [];

        $count              = by::Wtopic()->GetListCount($level, $pid);
        $return['pages']    = CUtil::getPaginationPages($count, $page_size);

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * 话题增改
     */
    public function actionTsave() {
        $post        = \Yii::$app->request->post();
        $json        = $post['cnf'] ?? '';

        list($s, $m) = by::Wtopic()->SaveLog($json);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');
    }

    /**
     * 话题删除
     */
    public function actionTdel() {
        $post       = \Yii::$app->request->post();
        $id         = $post['id'] ?? 0;

        list($s, $m)=by::Wtopic()->Del($id);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }

        CUtil::json_response(1, 'ok');
    }


    /**
     * @throws \yii\db\Exception
     * 内容列表
     */
    public function actionWlist() {
        $post       = \Yii::$app->request->post();
        $page       = $post['page']     ?? 1;
        $title      = $post['title']    ?? '';
        $status     = $post['status']   ?? -1;
        $type       = $post['type']     ?? -1;
        $t1         = $post['t1']       ?? -1;
        $t2         = $post['t2']       ?? -1;

        $page_size  = 20;

        $mWdym      = by::WdynamicMain();
        $list       = $mWdym->GetList($status, $type, $t1, $t2, $title, $page, $page_size);

        foreach($list as $val) {

            $info       = $mWdym->CommPackageInfo($val['id']);
            unset($info['text']);

            $info['rtime'] = $val['rtime'];

            $data[]     = $info;
        }

        $return['list']     = $data ?? [];

        if ($page == 1) {
            $count              = $mWdym->GetListCount($status, $type, $t1, $t2, $title);
            $return['pages']    = CUtil::getPaginationPages($count, $page_size);
        }

        CUtil::json_response(1, 'ok', $return);
    }

    /**
     * @throws \yii\db\Exception
     * 内容增改
     */
    public function actionWsave()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['did'] ?? 0;
        list($s, $m) = by::WdynamicMain()->SaveLog($post);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        $msg = $id ? "修改了内容ID：{$id}" : "新增了内容ID：{$m}";
        $msg .= ",内容：" . json_encode($post, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::CONTENT, $this->user_info['id']);
        CUtil::json_response(1, 'ok');
    }

    /**
     * @throws \yii\db\Exception
     * 内容删除
     */
    public function actionWdel()
    {
        $post = \Yii::$app->request->post();
        $did  = $post['did'] ?? 0;

        list($s, $m) = by::WdynamicMain()->Del($did);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        $msg = "删除内容ID：{$did}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::CONTENT);
        CUtil::json_response(1, 'ok');
    }

    public function actionSaveSurvey(){
        $post       = \Yii::$app->request->post();
        list($s, $m)=by::Sconfig()->saveConfig($post);
        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        CUtil::json_response(1, 'ok');
    }

    public function actionGetSurvey(){
        $data=by::Sconfig()->getConfig();
        CUtil::json_response(1, 'ok',$data);
    }

    /**
     * @throws \yii\db\Exception
     * 内容详情
     */
    public function actionWinfo() {
        $post       = \Yii::$app->request->post();
        $did        = $post['did'] ?? 0;

        $aData      = by::WdynamicMain()->CommPackageInfo($did);

        CUtil::json_response(1, 'ok', $aData);
    }

    /**
     * 上下架
     */
    public function actionWestatus()
    {
        $post   = \Yii::$app->request->post();
        $did    = $post['did'] ?? 0;
        $status = $post['status'] ?? -1;

        list($s, $m) = by::WdynamicMain()->UpdateStatus($did, $status);

        if (!$s) {
            CUtil::json_response(-1, $m);
        }
        $status == 1 ? $msg = '上架' : $msg = '下架';
        (new SystemLogsModel())->record("修改内容ID：{$did}；状态：{$msg}；", RbacInfoModel::CONTENT);
        CUtil::json_response(1, 'ok');
    }

    /**
     * 获取文档详情
     */
    public function actionDocInfo()
    {
        $post   = \Yii::$app->request->post();
        $id     = $post['id']     ?? 0;
        if(!CUtil::uint($id)){
            CUtil::json_response(-1, 'id不能为空');
        }

        $info = by::Doc()->GetInfo($id);
        CUtil::json_response(1, 'ok',$info);
    }

    /**
     * 获取文档列表
     */
    public function actionDocList()
    {
        $post   = \Yii::$app->request->post();
        $code   = $post['doc_code']     ?? '';
        $name   = $post['doc_name']     ?? '';

        $info = by::Doc()->GetListByBack($name,$code);
        CUtil::json_response(1, 'ok',$info);
    }

    /**
     * 获取新增/更新
     */
    public function actionDocSave()
    {
        $post   = \Yii::$app->request->post();
        $code   = $post['doc_code']     ?? '';
        $name   = $post['doc_name']     ?? '';
        $id     = $post['id']     ?? 0;
        $img    = $post['img_list']     ?? '';

        list($status,$msg) = by::Doc()->save_log($id, $name, $code, $img);
        if(!$status){
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * 删除文档 假删除
     */
    public function actionDocDel()
    {
        $post   = \Yii::$app->request->post();
        $id     = $post['id']     ?? 0;

        list($status,$msg) = by::Doc()->del($id);
        if(!$status){
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/wiki/role-save",
     *     summary="添加角色",
     *     description="添加角色",
     *     tags={"角色管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="name", type="string",description="角色名称"),
     *              @OA\Property(property="icon", type="string",description="角色图标")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionRoleSave()
    {
        $post = \Yii::$app->request->post();

        list($status, $ret) = by::WikiRole()->saveLog($post);

        if (!$status) {
            CUtil::json_response(-1, $ret);
        }

        $msg = "修改了内容ID：{$ret}";
        $msg .= ",内容：" . json_encode($post, JSON_UNESCAPED_UNICODE);
        (new SystemLogsModel())->record($msg, RbacInfoModel::CONTENT_ROLE, $this->user_info['id']);
        CUtil::json_response(1, 'ok');
    }


    /**
     * @OA\Post(
     *     path="/back/wiki/role-del",
     *     summary="删除角色",
     *     description="删除角色",
     *     tags={"角色管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名"),
     *              @OA\Property(property="id", type="string",description="角色ID")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionRoleDel()
    {
        $post = \Yii::$app->request->post();
        $id   = $post['id'] ?? 0;
        list($status, $ret) = by::WikiRole()->deleteRole($id);
        if (!$status) {
            CUtil::json_response(-1, $ret);
        }
        $msg = "删除内容角色ID：{$id}";
        (new SystemLogsModel())->record($msg, RbacInfoModel::CONTENT_ROLE);
        CUtil::json_response(1, 'ok');
    }

    /**
     * @OA\Post(
     *     path="/back/wiki/role-list",
     *     summary="角色列表",
     *     description="角色列表",
     *     tags={"角色管理"},
     *     security={
     *         {"sessid": {}},
     *     },
     *     @OA\RequestBody(
     *     @OA\MediaType(  mediaType="application/x-www-form-urlencoded",
     *          @OA\Schema(
     *              @OA\Property(property="api",  type="string", default="a_1664246268",description="平台API,安卓:a_1664246268,IOS:i_1666147923"),
     *              @OA\Property(property="sign", type="string", default="",description="签名"),
     *              @OA\Property(property="sign_time", type="string", default="",description="签名时间"),
     *              @OA\Property(property="provider", type="string", default="test", description="测试环境-填写test，不校验签名")
     *          )
     *     )
     * ),
     *     @OA\Response(
     *         response="200",
     *         description="状态码、提示消息",
     *         @OA\Schema(
     *              required={"code","msg"},
     *              @OA\Property(property="code",type="integer",description="状态码"),
     *              @OA\Property(property="msg",type="string",description="提示消息"),
     *              @OA\Property(property="success",type="boolen",description="是否成功"),
     *              @OA\Property(property="data",type="array",description="数据")
     *          )
     *     )
     * )
     */
    public function actionRoleList()
    {
        $post   = \Yii::$app->request->post();
        $ids    = by::WikiRole()->getList();
        $return = [];
        foreach ($ids as $id) {
            $info             = by::WikiRole()->GetOneInfoById($id);
            $return['list'][] = $info;
        }
        $return['count'] = by::WikiRole()->getCount();
        CUtil::json_response(1, 'ok', $return);
    }


}
