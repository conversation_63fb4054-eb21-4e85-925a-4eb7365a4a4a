<?php


namespace app\modules\back\controllers;


use app\models\CUtil;
use app\modules\back\forms\ActivityAtmosphereForm;
use app\modules\back\services\ActivityAtmosphereService;
use app\modules\rbac\controllers\CommController;

class ActivityAtmosphereController extends CommController
{


    /**
     * @OA\Post(
     *     path="/back/activity-atmosphere/list",
     *     summary="活动气氛列表查询",
     *     description="获取活动气氛的列表信息。",
     *     tags={"活动气氛"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="name", type="string", description="名称"),
     *                         @OA\Property(property="start_time", type="string", description="开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="结束时间"),
     *                         @OA\Property(property="activity_id", type="string", description="活动ID"),
     *                         @OA\Property(property="gid", type="string", description="商品ID"),
     *                         @OA\Property(property="pos", type="string", description="位置")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="获取成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="返回的数据",
     *                 @OA\Property(
     *                     property="list",
     *                     type="array",
     *                     description="活动气氛列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="活动ID"),
     *                         @OA\Property(property="name", type="string", description="名称"),
     *                         @OA\Property(property="start_time", type="string", description="开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="结束时间"),
     *                         @OA\Property(property="img", type="string", description="图片链接"),
     *                         @OA\Property(property="pos", type="string", description="位置"),
     *                         @OA\Property(property="ctime", type="string", description="创建时间"),
     *                         @OA\Property(property="utime", type="string", description="更新时间"),
     *                         @OA\Property(property="dtime", type="string", description="删除时间"),
     *                         @OA\Property(property="is_del", type="string", description="是否已删除")
     *                     )
     *                 ),
     *                 @OA\Property(property="pages", type="integer", description="总页数")
     *             )
     *         )
     *     )
     * )
     */

    public function actionList()
    {
        // 参数验证
        $form = CUtil::VdForm(new ActivityAtmosphereForm(), 'list');
        $data = ActivityAtmosphereService::getInstance()->list($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/activity-atmosphere/detail",
     *     summary="获取活动气氛详情",
     *     description="此接口用于获取特定活动的详细信息，包括活动的名称、开始时间、结束时间、图片、位置等，以及该活动关联的商品信息。",
     *     tags={"活动气氛"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                 @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                 @OA\Schema(
     *                     required={"id"},
     *                     @OA\Property(property="id", type="string", description="活动ID"),
     *                 )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(
     *             @OA\Property(property="iRet", type="integer", description="状态码"),
     *             @OA\Property(property="sMsg", type="string", description="提示消息"),
     *             @OA\Property(
     *                 property="data",
     *                 type="object",
     *                 description="活动详情数据",
     *                 @OA\Property(property="id", type="string", description="活动ID"),
     *                 @OA\Property(property="name", type="string", description="活动名称"),
     *                 @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                 @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                 @OA\Property(property="img", type="string", description="活动图片URL"),
     *                 @OA\Property(property="pos", type="string", description="活动位置"),
     *                 @OA\Property(property="ctime", type="string", description="创建时间"),
     *                 @OA\Property(property="utime", type="string", description="更新时间"),
     *                 @OA\Property(property="dtime", type="string", description="删除时间"),
     *                 @OA\Property(property="is_del", type="string", description="是否删除"),
     *                 @OA\Property(
     *                     property="goods",
     *                     type="array",
     *                     description="活动关联的商品列表",
     *                     @OA\Items(
     *                         @OA\Property(property="id", type="string", description="商品ID"),
     *                         @OA\Property(property="activity_id", type="string", description="关联的活动ID"),
     *                         @OA\Property(property="gid", type="string", description="商品的全局ID"),
     *                         @OA\Property(property="ctime", type="string", description="商品关联的创建时间"),
     *                         @OA\Property(property="utime", type="string", description="商品关联的更新时间")
     *                     )
     *                 )
     *             )
     *         )
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */

    public function actionDetail()
    {
        // 参数验证
        $form = CUtil::VdForm(new ActivityAtmosphereForm(), 'detail');
        $data = ActivityAtmosphereService::getInstance()->detail($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/activity-atmosphere/store",
     *     summary="活动气氛新增或编辑",
     *     description="创建或编辑活动气氛，包含活动的基本信息和商品列表。",
     *     tags={"活动气氛"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         @OA\Property(property="id", type="string", description="活动ID"),
     *                         @OA\Property(property="name", type="string", description="活动名称"),
     *                         @OA\Property(property="start_time", type="string", description="活动开始时间"),
     *                         @OA\Property(property="end_time", type="string", description="活动结束时间"),
     *                         @OA\Property(property="img", type="string", description="活动图片URL"),
     *                         @OA\Property(property="goods", type="string", description="活动商品列表")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="成功",
     *         @OA\JsonContent(type="object", description="空对象，表示操作成功")
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionStore()
    {
        // 参数验证
        $form = CUtil::VdForm(new ActivityAtmosphereForm(), 'store');
        $data = ActivityAtmosphereService::getInstance()->store($form);
        CUtil::Ret($data);
    }

    /**
     * @OA\Post(
     *     path="/back/activity-atmosphere/del",
     *     summary="删除活动气氛",
     *     description="根据提供的活动ID删除特定的活动气氛。",
     *     tags={"活动气氛"},
     *     @OA\RequestBody(
     *         @OA\MediaType(
     *             mediaType="application/x-www-form-urlencoded",
     *             @OA\JsonContent(
     *                 allOf={
     *                     @OA\Schema(ref="#/components/schemas/BaseRequest"),
     *                     @OA\Schema(
     *                         required={"id"},
     *                         @OA\Property(property="id", type="string", description="活动ID")
     *                     )
     *                 }
     *             )
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="操作成功，活动气氛已删除",
     *         @OA\JsonContent(type="object")
     *     ),
     *     @OA\Parameter(
     *         name="sessid",
     *         in="cookie",
     *         required=false,
     *         description="会话ID，用于身份验证",
     *         @OA\Schema(type="string")
     *     )
     * )
     */
    public function actionDel()
    {
        // 参数验证
        $form = CUtil::VdForm(new ActivityAtmosphereForm(), 'detail');
        $data = ActivityAtmosphereService::getInstance()->delete($form->id);
        CUtil::Ret($data);
    }

}