<?php
/**
 * @OA\Schema(
 *   schema="MarketConfigModify",
 *   required = {"api","sign","sign_time"},
 *     required={"type","status","name","images"},
 *        @OA\Property(property="name", type="string", default="",description="名称"),
 *        @OA\Property(property="images", type="string", default="",description="图片"),
 *        @OA\Property(property="type", type="integer", default="",description="资源类型 1 优惠券 2 G积分 3 提示泡泡 4 排除卡 5 免邮卡 6 兑换券 【必要】"),
 *        @OA\Property(property="resource_type", type="integer", default="",description="资源内容类型(1:折扣优惠N%;2:固定金额N元)-- type=1传"),
 *        @OA\Property(property="discount", type="string", default="",description="折扣优惠N%、固定金额  配置值-- type=1传"),
 *        @OA\Property(property="c_amount_type", type="string", default="",description="使用金额（1：全部金额；2：最低订单金额）-- type=1传 "),
 *        @OA\Property(property="c_amount_val", type="string", default="",description="最低订单金额 -- type=1传 "),
 *        @OA\Property(property="c_tag_type", type="string", default="",description="使用条件（1：全部标签；2：适用标签；3：排除标签） -- type=1传"),
 *        @OA\Property(property="c_tag_val", type="string", default="",description="选择标签（多个用逗号分隔） -- type=1传"),
 *        @OA\Property(property="c_goods_type", type="integer", default="",description="商品条件（1：适用商品；2：排除商品） -- type=1传"),
 *        @OA\Property(property="c_goods_val", type="string", default="1,2",description="选择商品（多个用逗号分隔） -- type=1传"),
 *        @OA\Property(property="jump_goods_id", type="integer", default="",description="跳转商品ID -- type=6传"),
 *        @OA\Property(property="use_rule_note", type="string", default="1.规则1,2.规则2",description="用规则（多个用逗号分隔） -- type=6传"),
 *        @OA\Property(property="valid_type", type="integer", default="",description="使用期限 1:固定时间；2：领取后多少天 -- type=1传"),
 *        @OA\Property(property="valid_val", type="string", default="2021-01-01~2021-06-02",description="过期时间配置值 -- type=1传"),
 *        @OA\Property(property="resource_num", type="integer", default="",description="资源内容 --type = 2，3，4, 5 传"),
 *        @OA\Property(property="stock_num", type="integer", default="",description="库存"),
 *        @OA\Property(property="status", type="integer", default="1",description=" 0 保存 2 提交审批        【必要】"),
 *        @OA\Property(property="web_name", type="string", default="1",description="外显名称"),
 * )
 */
