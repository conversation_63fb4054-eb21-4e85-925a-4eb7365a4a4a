<?php
/**
 * @OA\Schema(
 *   schema="WaresActivitySaveRequest",type="object",
 *   required = {"name","grant_type","start_time","end_time","status","poster_image","share_image","try_goods_ids","try_quota","validity","return_period","delivery_period","join_condition","rule"},
 *   @OA\Property(property="id", type="integer",description="商品ID，编辑时必填"),
 *   @OA\Property(property="name", type="string",description="活动名称"),
 *   @OA\Property(property="grant_type", type="integer",description="活动类型 1 先用后买"),
 *   @OA\Property(property="start_time", type="integer",description="开始时间"),
 *   @OA\Property(property="end_time", type="integer",description="结束时间"),
 *   @OA\Property(property="status", type="integer",description="是否下架 0否 1是"),
 *   @OA\Property(property="poster_image", type="string",description="活动海报"),
 *   @OA\Property(property="share_image", type="string",description="分享海报"),
 *   @OA\Property(property="try_goods_ids", type="integer",description="试用商品ID"),
 *   @OA\Property(property="try_quota", type="integer",description="试用名额"),
 *   @OA\Property(property="validity", type="integer",description="有效期x天"),
 *   @OA\Property(property="return_period", type="integer",description="退机有效期 单位天"),
 *   @OA\Property(property="delivery_period", type="integer",description="收货有效期 单位天"),
 *   @OA\Property(property="survey_key", type="string",description="问卷key"),
 *   @OA\Property(property="pass_mark", type="integer",description="及格分数"),
 *   @OA\Property(property="join_condition", type="integer",description="参与条件 1：申请过活动的 2：通过过问卷的活动 3：体验过机器的活动"),
 *   @OA\Property(property="ac_ids", type="string",description="限制条件活动ID(逗号“,”分割)"),
 *   @OA\Property(property="rule", type="string",description="活动规则"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresActivityListRequest",type="object",
 *   @OA\Property(property="id", type="integer",description="活动ID"),
 *   @OA\Property(property="name", type="string",description="活动名称"),
 *   @OA\Property(property="goods_name", type="string",description="试用商品名称"),
 *   @OA\Property(property="start_time", type="integer",description="开始时间"),
 *   @OA\Property(property="end_time", type="integer",description="结束时间"),
 *   @OA\Property(property="status", type="integer",description="活动状态 0上架 1下架 -1全部"),
 * )
 */