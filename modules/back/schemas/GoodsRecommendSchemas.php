<?php
/**
 * @OA\Schema(
 *   schema="RecommendListResource",type="object",
 *   @OA\Property(property="gid", type="string",description="商品ID"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="cover_image", type="string",description="商品封面图"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="RecommendDetailResource",
 *   type="object",
 *   @OA\Property(property="title", type="string", description="标题"),
 *   @OA\Property(property="tid", type="string", description="标签ID"),
 *   @OA\Property(property="primary", type="object", ref="#/components/schemas/RecommendPrimaryResource", description="主推数据"),
 *   @OA\Property(
 *     property="secondary",
 *     type="array",
 *     @OA\Items(ref="#/components/schemas/RecommendSecondaryResource"),
 *     description="次推数据"
 *   ),
 * )
 */


/**
 * @OA\Schema(
 *   schema="RecommendPrimaryResource",type="object",
 *   @OA\Property(property="id", type="string",description="标题"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="sku", type="string",description="商品SKU"),
 *   @OA\Property(property="price", type="string",description="商品价格"),
 *   @OA\Property(property="image", type="string",description="商品图片"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="RecommendSecondaryResource",  type="array",
 *   @OA\Items(
 *     type="object",
 *     @OA\Property(property="id", type="string", description="标题"),
 *     @OA\Property(property="name", type="string", description="商品名称"),
 *     @OA\Property(property="sku", type="string", description="商品SKU"),
 *     @OA\Property(property="price", type="string", description="商品价格"),
 *     @OA\Property(property="image", type="string", description="商品图片"),
 *     @OA\Property(property="type", type="string", description="类型（0普通 1预售 2热销）"),
 *   ),
 * )
 */
