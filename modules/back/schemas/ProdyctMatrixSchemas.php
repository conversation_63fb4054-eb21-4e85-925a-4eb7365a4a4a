<?php


/**
 * @OA\Schema(
 *   schema="ProductMatrixModifyRequest",
 *   type="object",
 *   required={"category_id", "main_product", "sub_products"},
 *   @OA\Property(property="category_id", type="int", description="商品分类ID"),
 *   @OA\Property(
 *       property="sub_products",
 *       type="string",
 *       default="",
 *       description="底部卡片信息，例：[{'image':'https://img.alicdn.com/imgextra/i1/2200720000000/O1CN01Zz1Z2o1Q5Q6Q6Q1Zz_!!2200720000000.jpg','detail_link':'https://www.baidu.com','intro_link':'https://www.baidu.com'}]"
 *   ),
 *   @OA\Property(
 *       property="main_product",
 *       type="string",
 *       default="",
 *       description="顶部卡片信息，例：{'image':'https://img.alicdn.com/imgextra/i1/2200720000000/O1CN01Zz1Z2o1Q5Q6Q6Q1Zz_!!2200720000000.jpg','detail_link':'https://www.baidu.com','intro_link':'https://www.baidu.com11111'}"
 *   ),
 * )
 */


/**
 * @OA\Schema(
 *     schema="ProductMatrixDetailResponse",
 *     type="object",
 *     @OA\Property(property="category_id", type="integer", description="商品分类ID", example=1),
 *     @OA\Property(
 *         property="main_product",
 *         type="object",
 *         description="主商品列表",
 *         @OA\Property(property="image", type="string", description="主商品图片链接", example="https://img.alicdn.com/imgextra/i1/2200720000000/O1CN01Zz1Z2o1Q5Q6Q6Q1Zz_!!2200720000000.jpg"),
 *         @OA\Property(property="detail_link", type="string", description="详情链接", example="https://www.baidu.com"),
 *         @OA\Property(property="intro_link", type="string", description="介绍链接", example="https://www.baidu.com11111")
 *     ),
 *     @OA\Property(
 *         property="sub_products",
 *         type="array",
 *         description="子商品列表",
 *         @OA\Items(
 *             type="object",
 *             @OA\Property(property="image", type="string", description="子商品图片链接", example="https://img.alicdn.com/imgextra/i1/2200720000000/O1CN01Zz1Z2o1Q5Q6Q6Q1Zz_!!2200720000000.jpg"),
 *             @OA\Property(property="detail_link", type="string", description="详情链接", example="https://www.baidu.com"),
 *             @OA\Property(property="intro_link", type="string", description="介绍链接", example="https://www.baidu.com")
 *         )
 *     )
 * )
 */


/**
 * @OA\Schema(
 *   schema="ProductMatrixCategoryModifyRequest",
 *   type="object",
 *   required={"category_data"},
 *   @OA\Property(
 *     property="category_data",
 *     type="string",
 *     description="商品分类数据，JSON 字符串格式：
 *     - 有 ID 为编辑
 *     - 无 ID 为创建
 *     - 已存在的分类未传递则视为删除
 *     示例：[{'id':'2','name':'add2','sort':99},{'id':'','name':'add3','sort':3}]"
 *   )
 * )
 */



/**
 * @OA\Schema(
 *     schema="ProductMatrixCategoryDetailResponse",
 *     type="object",
 *     @OA\Property(property="id", type="integer", description="记录ID", example=1),
 *     @OA\Property(property="name", type="string", description="名称", example="name"),
 *     @OA\Property(property="sort", type="string", description="排序", example="1"),
 * )
 */


/**
 * @OA\Schema(
 *     schema="ProductMatrixCategoryListResponse",
 *     type="object",
 *     @OA\Property(
 *         type="array",
 *         description="分类记录数组",
 *         @OA\Items(ref="#/components/schemas/ProductMatrixCategoryDetailResponse")
 *     )
 * )
 */



/**
 * @OA\Schema(
 *   schema="ProductMatrixIntroModifyRequest",
 *   type="object",
 *   required={"title", "intro"},
 *   @OA\Property(property="title", type="string", description="标题"),
 *   @OA\Property(property="status", type="int", description="状态 0：上架 1：下架"),
 *   @OA\Property(property="intro", type="string", description="简介富文本"),
 *   @OA\Property(property="id", type="integer", description="ID，修改时必传", nullable=true)
 * )
 */


/**
 * @OA\Schema(
 *     schema="ProductMatrixIntroDetailResponse",
 *     type="object",
 *     @OA\Property(property="id", type="integer", description="记录ID", example=1),
 *     @OA\Property(property="title", type="string", description="标题", example="title"),
 *     @OA\Property(property="intro", type="string", description="简介", example="intro简介"),
 *     @OA\Property(property="ctime", type="integer", description="创建时间戳", example=1732691283),
 *     @OA\Property(property="utime", type="integer", description="更新时间戳", example=1732844485)
 * )
 */


/**
 * @OA\Schema(
 *     schema="ProductMatrixIntroListResponse",
 *     type="object",
 *     @OA\Property(property="total", type="integer", description="总记录数", example=1),
 *     @OA\Property(
 *         property="list",
 *         type="array",
 *         description="记录列表",
 *         @OA\Items(ref="#/components/schemas/ProductMatrixIntroDetailResponse")
 *     )
 * )
 */
