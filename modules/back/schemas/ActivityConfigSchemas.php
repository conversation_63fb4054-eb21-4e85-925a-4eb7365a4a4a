<?php
/**
 * @OA\Schema(
 *   schema="ActivityConfigDetail",type="object",
 *   @OA\Property(property="a_type", type="string",description="活动类型"),
 *   @OA\Property(property="ac_image", type="string",description="活动图片"),
 *   @OA\Property(property="back_image", type="string",description="活动背景图"),
 *   @OA\Property(property="btn_img", type="string",description=""),
 *   @OA\Property(property="end_time", type="string",description="活动截止时间"),
 *   @OA\Property(property="grant_type", type="string",description="活动类型"),
 *   @OA\Property(property="help_order", type="string",description=""),
 *   @OA\Property(property="id", type="string",description="活动ID"),
 *   @OA\Property(property="img", type="string",description=""),
 *   @OA\Property(property="market_id", type="string",description="优惠券ID"),
 *   @OA\Property(property="middleware_image", type="string",description="中间图片"),
 *   @OA\Property(property="name", type="string",description="活动名称"),
 *   @OA\Property(property="platform", type="string",description="活动平台"),
 *   @OA\Property(property="remark", type="string",description="备注"),
 *   @OA\Property(property="resource",type="object",ref="#/components/schemas/ActivityDetailResource",description="数据资源"),
 *   @OA\Property(property="reward_type", type="string",description=""),
 *   @OA\Property(property="rule_note", type="string",description="活动类型"),
 *   @OA\Property(property="sales_num", type="string",description="卖出数量"),
 *   @OA\Property(property="share_image", type="string",description="分享图片"),
 *   @OA\Property(property="share_title", type="string",description="分享标题"),
 *   @OA\Property(property="start_time", type="string",description="活动开始时间"),
 *   @OA\Property(property="surplus_num", type="string",description="库存数"),
 *   @OA\Property(property="url", type="string",description="链接"),
 *   @OA\Property(property="url_type", type="string",description="链接类型"),
 *   @OA\Property(property="user_type", type="string",description="活动限制用户"),
 *   @OA\Property(property="weight", type="string",description=""),
 * )
 */

/**
 * @OA\Schema(
 *   schema="ActivityDetailResource",type="object",
 *   @OA\Property(property="share_image", type="string",description="分享图片"),
 *   @OA\Property(property="share_title", type="string",description="分享标题"),
 * )
 */



/**
 * @OA\Schema(
 *   schema="ActivityDetailModify",type="object",
 *   @OA\Property(property="id", type="string",description="新增时传0，修改时传指定的值"),
 *   @OA\Property(property="name", type="string",description="活动名称"),
 *   @OA\Property(property="grant_type", type="string",description="活动类型（1：新用户 2：推荐有礼 3：优惠卷领取活动 4：生日礼包，5 每月领券）"),
 *   @OA\Property(property="rule_note", type="string",description="规则说明"),
 *   @OA\Property(property="start_time", type="string",description="grant_type（1、2、3、4、5）必传"),
 *   @OA\Property(property="end_time", type="string",description="grant_type（1、2、3、4、5）必传"),
 *   @OA\Property(property="market_config", type="string",default="",description="grant_type（1、2、3、4、5）必传"),
 *   @OA\Property(property="url", type="string",description="链接(1H5,2小程序,3web)"),
 *   @OA\Property(property="reward_type", type="string",description="奖励类型（1：优惠券 2：积分）grant_type（2）必传"),
 *   @OA\Property(property="user_type", type="string",description="用户类型 （1消费过 2未消费过） grant_type（3）必传"),
 *   @OA\Property(property="platform", type="string",description="平台来源 （1app，2小程序，99全平台）"),
 *   @OA\Property(property="ac_image", type="string",description="活动图片 grant_type（3）必传"),
 *   @OA\Property(property="back_image", type="string",description="背景图片 grant_type（3）必传"),
 *   @OA\Property(property="share_image", type="string",description="分享图片 grant_type（3）必传"),
 *   @OA\Property(property="share_title", type="string",description="分享标题 grant_type（3）必传"),
 *   @OA\Property(property="middleware_image", type="string",description="中间图片 grant_type（3）必传"),
 *   @OA\Property(property="has_point", type="int",description="礼包类型积分 grant_type（1）传"),
 *   @OA\Property(property="has_coupon", type="int",description="礼包类型优惠券 grant_type（1）传"),
 *   @OA\Property(property="point_value", type="int",description="积分数 grant_type（1）、has_point必传传"),
 *   @OA\Property(property="point_multiplier", type="int",description="积分倍数 grant_type（1）、has_point必传传"),
 *   @OA\Property(property="multiplier_start_time", type="int",description="积分倍数开始时间 grant_type（1）、has_point必传传"),
 *   @OA\Property(property="multiplier_end_time", type="int",description="积分倍数数结束时间 grant_type（1）、has_point必传传"),
 * )
 */





/**
 * @OA\Schema(
 *     schema="ActivityConfigResource",type="object",
 *     required={"name", "grant_type", "remark","share_image","ac_image","middleware_image",
 *              "back_image","share_image","rule_note","start_time","end_time","user_type","market_config"},
 *                         @OA\Property(property="name", type="string", default="swagger测试", description="活动名称"),
 *                         @OA\Property(property="grant_type", type="integer", default="3", description="活动类型（1：新用户 2：推荐有礼 3：优惠卷领取活动）"),
 *                         @OA\Property(property="remark", type="integer", default="备注", description="活动备注"),
 *                         @OA\Property(property="rule_note", type="string", default="无规则", description="规则说明"),
 *                         @OA\Property(property="start_time", type="string", default="1686020733", description="开始时间"),
 *                         @OA\Property(property="end_time", type="string", default="1686029999", description="结束时间"),
 *                         @OA\Property(property="market_config", type="string", default="", description="优惠券列表"),
 *                         @OA\Property(property="url", type="string", default="2", description="链接(1H5,2小程序,3web)"),
 *                         @OA\Property(property="reward_type", type="string", default="1", description="奖励类型（1：优惠券 2：积分）"),
 *                         @OA\Property(property="user_type", type="string", default="3", description="用户类型 （1消费过 2未消费过 3全部用户）"),
 *                         @OA\Property(property="platform", type="string", default="99", description="平台来源 （1app，2小程序，99全平台）"),
 *                         @OA\Property(property="ac_image", type="string", default="https://wpm-cdn.dreame.tech/images/202304/6440f172e961f9552172831.jpeg", description="活动图片"),
 *                         @OA\Property(property="back_image", type="string", default="https://wpm-cdn.dreame.tech/images/202304/6440f172e961f9552172831.jpeg", description="背景图片"),
 *                         @OA\Property(property="share_image", type="string", default="https://wpm-cdn.dreame.tech/images/202304/6440f172e961f9552172831.jpeg", description="分享图片"),
 *                         @OA\Property(property="share_title", type="string", default="分享标题", description="分享标题"),
 *                         @OA\Property(property="middleware_image", type="string", default="https://wpm-cdn.dreame.tech/images/202304/6440f172e961f9552172831.jpeg", description="中间图片")
 *                     )
 */


/**
 * @OA\Schema(
 *     schema="ActivityAmSaveResource",type="object",
 *     required={"id", "ac_id","mc_id","stock"},
 *                         @OA\Property(property="id", type="string", default="", description="活动与优惠卷的关联ID(修改时传)"),
 *                         @OA\Property(property="ac_id", type="integer", default="", description="活动ID"),
 *                         @OA\Property(property="mc_id", type="integer", default="", description="优惠卷ID"),
 *                         @OA\Property(property="stock", type="integer", default="", description="库存")
 *                     )
 */