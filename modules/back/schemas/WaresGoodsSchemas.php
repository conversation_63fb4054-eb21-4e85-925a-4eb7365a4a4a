<?php
/**
 * @OA\Schema(
 *   schema="WaresGoodsSaveRequest",type="object",
 *   required = {"source","name","sku","type","cover_image","images","mprice","is_send","atype","levels","notice","platform","online_time","shipping","detail"},
 *   @OA\Property(property="source", type="string",description="商品来源 1：普通商品；2：积分商品"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="sku", type="string",description="商品编码"),
 *   @OA\Property(property="type", type="string",description="商品类型 默认 1：实体商品；2：虚拟商品"),
 *   @OA\Property(property="cover_image", type="string",description="商品封面（1：小程序；2：APP；json数据；例：[{'platform':1,'image':'http:\/\/abc.jpg'},{'platform':2,'image':'http:\/\/abc2.jpg'}]"),
 *   @OA\Property(property="images", type="string",description="图片集，'|'分割，10个以内，例：https://abc.jpg|https://abc2.jpg"),
 *   @OA\Property(property="video", type="string",description="视频URL"),
 *   @OA\Property(property="cover_image_3d", type="string",description="3D 封面图"),
 *   @OA\Property(property="model_3d", type="string",description="3D 模型图"),
 *   @OA\Property(property="introduce", type="string",description="商品简介，200个字以内"),
 *   @OA\Property(property="label", type="string",description="商品标签，1：新品、2：限时、3：爆品， 可多选 以'|' 分割"),
 *   @OA\Property(property="tab", type="string",description="商品TAB 1：新品 可多选 以'|' 分割"),
 *   @OA\Property(property="position", type="string",description="展示位置（多选） 1：推荐位 可多选 以'|' 分割"),
 *   @OA\Property(property="mprice", type="decimal",description="原价，截止2位小数"),
 *   @OA\Property(property="stock", type="integer",description="商品库存"),
 *   @OA\Property(property="is_send", type="integer",description="是否发货 默认 1：发货 2：不发货，发货时库存不能填写，不发货库存可写"),
 *   @OA\Property(property="atype", type="integer",description="默认0：统一规格 1：自定义/多规格"),
 *   @OA\Property(property="attr_cnf", type="string",description="商品属性 多规格必填 json数据 [{'at_name':'口味','at_val':[{'val':'甜','image':'http:\/\/abc1.jpg'},{'val':'酸','image':'http:\/\/abc2.jpg'}]}, {'at_name':'颜色','at_val':[{'val':'红色','image':'http:\/\/abc3.jpg'},{'val':'橙色','image':'http:\/\/abc4.jpg'}]}]"),
 *   @OA\Property(property="specs", type="string",description="多规格数据 多规格必填 json数据 [{'at_val':['口味:甜','颜色:红色'],'sku':'12211','price':'0.01','point':'500','exchange':'1','coupon_id':'0','stock':'1000','image':'http://abc.jpg'}]"),
 *   @OA\Property(property="levels", type="string",description="会员等级V1|V2,用户等级，以'|'分割 "),
 *   @OA\Property(property="exchange", type="integer",description="1:钱+积分 2:全积分 3:指定兑换券"),
 *   @OA\Property(property="price", type="decimal",description="兑换价格，截止2位小数"),
 *   @OA\Property(property="point", type="integer",description="兑换积分，整形"),
 *   @OA\Property(property="coupon_id", type="integer",description="兑换券ID"),
 *   @OA\Property(property="limit_num", type="integer",description="限兑数量 0 代表不限数量 >0 代表限兑数量"),
 *   @OA\Property(property="notice", type="string",description="兑换须知 json数据 例：[{'title':'标题1','content':'内容1'},{'platform':'标题2','image':'内容2'}]"),
 *   @OA\Property(property="platform", type="string",description="投放平台 99：全平台 1：小程序 2：APP"),
 *   @OA\Property(property="status", type="integer",description="上架状态：默认：0 上架 1：下架"),
 *   @OA\Property(property="online_time", type="string",description="上架时间，立即上架：当前时间时间戳，s级；预告上架：定时上架 定时时间时间戳，s级；周期上架：1：周一 2:周二等 可多选 以'|'分割 "),
 *   @OA\Property(property="shipping", type="integer",description="是否包邮 默认 1：否 2：是"),
 *   @OA\Property(property="detail", type="string",description="商品详情"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsEnumRequest",type="object",
 *   required = {"keyword"},
 *   @OA\Property(property="keyword", type="string",description="字段名,type商品类型,label 商品标签,tab 商品TAB,position 商品推荐位,online_time 周期上架,levels 用户等级"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsInfoRequest",type="object",
 *   required = {"id"},
 *   @OA\Property(property="id", type="integer",description="商品ID"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsSortRequest",type="object",
 *   required = {"id","sort"},
 *   @OA\Property(property="id", type="integer",description="商品ID"),
 *   @OA\Property(property="sort", type="integer",description="排序"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsSaleRequest",type="object",
 *   required = {"id","act"},
 *   @OA\Property(property="id", type="integer",description="商品ID"),
 *   @OA\Property(property="act", type="string",description="上下架 up:上架 down：下架"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresGoodsListRequest",type="object",
 *   required = {"source"},
 *   @OA\Property(property="source", type="integer",description="商品来源，1：普通商品（暂不支持） 2.积分商品"),
 *   @OA\Property(property="id", type="integer",description="商品ID"),
 *   @OA\Property(property="name", type="string",description="商品名称"),
 *   @OA\Property(property="type", type="integer",description="商品类型，-1 全部 1：实体商品，2：虚拟商品"),
 *   @OA\Property(property="status", type="integer", default = "-1", description="商品状态，-1 全部，0：下架， 1上架"),
 *   @OA\Property(property="page", type="integer",default = "1",description="页码"),
 *   @OA\Property(property="page_size", type="integer",default = "20",description="每页个数"),
 *
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsInfoResponse",type="object",
 *   @OA\Property(property="id", type="integer",description="商品ID"),
 *   @OA\Property(property="sku", type="string",description="sku"),
 *   @OA\Property(property="short_code", type="string",description="短编码"),
 *   @OA\Property(property="name", type="string",description="名称"),
 *   @OA\Property(property="status", type="string",description="上架状态 0 上架 1 下架"),
 *   @OA\Property(property="sort", type="string",description="排序"),
 *   @OA\Property(property="atype", type="integer",description="是否是多规格 0 非 1 是"),
 *   @OA\Property(property="source", type="integer",description="总商品类型 1：会员商品 2：积分商品"),
 *   @OA\Property(property="version", type="string",description="版本 0.0.0 默认不区分"),
 *   @OA\Property(property="is_del", type="integer",description="1 删除 0 非删除"),
 *   @OA\Property(property="ctime", type="integer",description="创建时间"),
 *   @OA\Property(property="utime", type="integer",description="更新时间"),
 *   @OA\Property(property="dtime", type="integer",description="删除时间"),
 *   @OA\Property(property="type", type="integer",description="商品类型（1：实体商品 2：虚拟商品）"),
 *   @OA\Property(property="images", type="string",description="图片集，以 '|'分割 "),
 *   @OA\Property(property="mprice", type="string",description="原价"),
 *   @OA\Property(property="introduce", type="string",description="简介/卖点"),
 *   @OA\Property(property="label", type="string",description="商品标签 爆品、卖品等"),
 *   @OA\Property(property="position", type="string",description="推荐位 1热门位"),
 *   @OA\Property(property="is_send", type="integer",description="是否发货"),
 *   @OA\Property(property="detail", type="string",description="商品详情信息"),
 *   @OA\Property(property="tab", type="integer",description="展示TAB 1新品"),
 *   @OA\Property(property="levels", type="string",description="售卖等级 v1|v2|v3"),
 *   @OA\Property(property="limit_num", type="string",description="限购"),
 *   @OA\Property(property="platform", type="string",description="销售平台"),
 *   @OA\Property(property="online_time", type="string",description="上线时间"),
 *   @OA\Property(property="online", type="integer",description="1：立即上线 2：预期上架"),
 *   @OA\Property(property="stock", type="string",description="库存"),
 *   @OA\Property(property="sales", type="string",description="已售出"),
 *   @OA\Property(property="price", type="string",description="售价"),
 *   @OA\Property(property="point", type="string",description="积分"),
 *   @OA\Property(property="coupon_id", type="string",description="优惠券ID"),
 *   @OA\Property(property="exchange", type="integer",description="1 积分+钱 2 全积分 3 优惠券"),
 *   @OA\Property(property="cover_image", type="object",ref="#/components/schemas/WaresGoodsCoverImageReponse",description="封面图"),
 *   @OA\Property(property="notice", type="object",ref="#/components/schemas/WaresGoodsNoticeReponse",description="规则"),
 *   @OA\Property(property="specs", type="object",ref="#/components/schemas/WaresGoodsSpecsReponse",description="多规格"),
 *   @OA\Property(property="attr_cnf", type="object",description="多规格参数值"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresGoodsCoverImageReponse",type="object",
 *   @OA\Property(property="platform", type="integer",description="1小程序 2APP"),
 *   @OA\Property(property="image", type="string",description="图片URL"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WaresGoodsNoticeReponse",type="object",
 *   @OA\Property(property="title", type="integer",description="标题"),
 *   @OA\Property(property="content", type="string",description="内容"),
 * )
 */

/**
 * @OA\Schema(
 *   schema="WaresGoodsSpecsReponse",type="object",
 *   @OA\Property(property="sid", type="integer",description="规格值ID"),
 *   @OA\Property(property="image", type="string",description="图片"),
 *   @OA\Property(property="gid", type="string",description="商品ID"),
 *   @OA\Property(property="av_ids", type="string",description="规格值"),
 *   @OA\Property(property="attr_cnf", type="string",description="特征值"),
 *   @OA\Property(property="price", type="string",description="售价"),
 *   @OA\Property(property="point", type="string",description="积分"),
 *   @OA\Property(property="coupon_id", type="string",description="优惠券ID"),
 *   @OA\Property(property="exchange", type="integer",description="1 积分+钱 2 全积分 3 优惠券"),
 * )
 */


