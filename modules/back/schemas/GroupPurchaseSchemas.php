<?php
/**
 * @OA\Schema(
 *     schema="GroupPurchaseActivityList",type="object",
 *     required={"activity_id"},
 *     @OA\Property(property="activity_id", type="string",description="活动ID"),
 *     @OA\Property(property="name", type="string",description="活动名称"),
 *     @OA\Property(property="start_time", type="string",description="开始时间"),
 *     @OA\Property(property="end_time", type="string",description="结束时间"),
 * )
 */

/**
 * @OA\Schema(
 *     schema="GroupPurchaseActivitySave",type="object",
 *     required={"name","start_time","end_time","min_members","max_members","goods"},
 *     @OA\Property(property="id", type="integer",description="活动ID , 新增：id不传或者为空；编辑：传id值"),
 *     @OA\Property(property="name", type="string",description="活动名称"),
 *     @OA\Property(property="start_time", type="string",description="开始时间"),
 *     @OA\Property(property="end_time", type="string",description="结束时间"),
 *     @OA\Property(property="details", type="string",description="活动详情"),
 *     @OA\Property(property="min_members", type="integer",description="最小成员数"),
 *     @OA\Property(property="max_members", type="integer",description="最大成员数"),
 *     @OA\Property(property="goods", type="json",description="关联商品
 *     示例：[{'gid':1,'purchase_limit':1,'min_group_qty':1,'min_members':1,'max_members':1,'discount_activity_id':1,'discount_gid':1,'tag_id':0}]
 *     purchase_limit:最大限购数
 *     min_group_qty:成团购买数量
 *     discount_activity_id:折扣活动ID
 *     discount_gid:折扣商品ID
 *     tag_id:标签ID(0为全部)
 *     gid:商品ID"),
 *
 * )
 */

/**
 * @OA\Schema(
 *     schema="GroupPurchaseActivityListResponse",type="object",
 *     @OA\Property(property="id", type="string",description="活动ID"),
 *     @OA\Property(property="name", type="string",description="活动名称"),
 *     @OA\Property(property="start_time", type="string",description="开始时间"),
 *     @OA\Property(property="end_time", type="string",description="结束时间"),
 * )
 */

//GroupPurchaseActivityDetailResponse
/**
 * @OA\Schema(
 *     schema="GroupPurchaseActivityDetailResponse",type="object",
 *     @OA\Property(property="id", type="string",description="活动ID"),
 *     @OA\Property(property="name", type="string",description="活动名称"),
 *     @OA\Property(property="start_time", type="string",description="开始时间"),
 *     @OA\Property(property="end_time", type="string",description="结束时间"),
 *     @OA\Property(property="details", type="string",description="活动详情"),
 *     @OA\Property(property="min_members", type="integer",description="最小成员数"),
 *     @OA\Property(property="max_members", type="integer",description="最大成员数"),
 *     @OA\Property(property="goods", type="array", @OA\Items(),description="关联商品 示例：[{'id':21,'gid':787,'activity_id':26,'min_members':5,'max_members':9,'purchase_limit':9,'min_group_qty':5,'stocks':[{'stock':10,'sku':'00100200300401'}]}]
 *      id:活动与商品关联ID
 *      gid:商品ID
 *      purchase_limit:最大限购数
 *      min_group_qty:成团购买数量
 *      min_members:最小成员数
 *      max_members:最大成员数
 *      stocks:库存"),
 * )
 */