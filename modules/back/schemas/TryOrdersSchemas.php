<?php
/**
 * @OA\Schema(
 *   schema="TryOrdersRequest",type="object",
 *   @OA\Property(property="year", type="string",description="年份"),
 *   @OA\Property(property="status", type="string",description="订单状态"),
 *   @OA\Property(property="order_type", type="string",description="订单类型"),
 *   @OA\Property(property="p_sources", type="string",description="平台类型，多选逗号分割"),
 *   @OA\Property(property="label", type="string",description="商品标签-1 全部， 11 扫地机， 10 洗地机 "),
 *   @OA\Property(property="user_iden", type="string",description="手机号码，用户UID,用户USER_ID"),
 *   @OA\Property(property="order_no", type="string",description="订单号"),
 *   @OA\Property(property="goods_name", type="string",description="商品名称"),
 *   @OA\Property(property="sku", type="string",description="商品编号"),
 *   @OA\Property(property="order_st", type="integer",description="下单开始时间"),
 *   @OA\Property(property="order_ed", type="integer",description="下单结束时间"),
 *   @OA\Property(property="page", type="integer",description="页码"),
 *   @OA\Property(property="page_size", type="integer",description="每页数量"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="TryOrdersResponse",type="object",
 *   @OA\Property(property="order_no", type="string",description="订单编号"),
 *   @OA\Property(property="goods", type="string",description="商品信息"),
 *   @OA\Property(property="user", type="string",description="用户信息"),
 *   @OA\Property(property="status", type="string",description="订单状态"),
 *   @OA\Property(property="label", type="string",description="商品标签 11 扫地机， 10 洗地机 "),
 *   @OA\Property(property="platform_source_name", type="string",description="订单平台"),
 *   @OA\Property(property="ctime", type="string",description="下单时间"),
 *   @OA\Property(property="try_info", type="object",ref="#/components/schemas/TryInfoResponse",description="试用信息"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="TryInfoResponse",type="object",
 *   @OA\Property(property="order_no", type="string",description="订单编号"),
 *   @OA\Property(property="user_id", type="string",description="用户ID"),
 *   @OA\Property(property="ac_id", type="string",description="活动ID"),
 *   @OA\Property(property="amount", type="string",description="押金金额"),
 *   @OA\Property(property="label", type="string",description="商品标签 11 扫地机， 10 洗地机 "),
 *   @OA\Property(property="arrival_time", type="string",description="确认收货时间（开始试用时间）"),
 *   @OA\Property(property="register_time", type="string",description="注册时间"),
 *   @OA\Property(property="back_time", type="string",description="退机时间"),
 *   @OA\Property(property="try_status", type="string",description="试用状态 0 待支付 1 待体验 2 体验中  3 待退机 4已退机 5 待扣款 6 已完成 7 扣款完成 8扣款失败"),
 * )
 */


 /**
 * @OA\Schema(
 *   schema="TryExceptionOrdersRequest",type="object",
 *   @OA\Property(property="year", type="string",description="年份"),
 *   @OA\Property(property="status", type="string",description="订单状态"),
 *   @OA\Property(property="label", type="string",description="商品标签-1 全部， 11 扫地机， 10 洗地机 "),
 *   @OA\Property(property="user_iden", type="string",description="手机号码，用户UID,用户USER_ID"),
 *   @OA\Property(property="order_no", type="string",description="订单号"),
 *   @OA\Property(property="refund_no", type="string",description="退款订单号"),
 *   @OA\Property(property="goods_name", type="string",description="商品名称"),
 *   @OA\Property(property="sku", type="string",description="商品编号"),
 *   @OA\Property(property="order_st", type="integer",description="下单开始时间"),
 *   @OA\Property(property="order_ed", type="integer",description="下单结束时间"),
 *   @OA\Property(property="try_status", type="integer",description="试用状态 0 待支付 1 待体验 2 体验中  3 待退机 4已退机 5 待扣款 6 已完成 7 扣款完成 8扣款失败"),
 *   @OA\Property(property="page", type="integer",description="页码"),
 *   @OA\Property(property="page_size", type="integer",description="每页数量"),
 * )
 */