<?php


/**
 * @OA\Schema(
 *   schema="WarrantyList",type="object",
 *   @OA\Property(property="user_id", type="string",description="用户ID"),
 *   @OA\Property(property="phone", type="string",description="手机号"),
 *   @OA\Property(property="sn", type="string",description="产品SN码"),
 *   @OA\Property(property="product_name", type="string",description="产品名称"),
 *   @OA\Property(property="buy_st", type="string",description="购买开始时间"),
 *   @OA\Property(property="buy_ed", type="string",description="购买结束时间"),
 *   @OA\Property(property="warranty_st", type="string",description="保修开始时间"),
 *   @OA\Property(property="warranty_ed", type="string",description="保修结束时间"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WarrantyCardSetPeriod",type="object",
 *   @OA\Property(property="card_no", type="string",description="保修卡号"),
 *   @OA\Property(property="period_time", type="string",description="保修卡时间，单位秒级"),
 * )
 */


/**
 * @OA\Schema(
 *   schema="WarrantyCardResponse",type="object",
 *   @OA\Property(property="user_id", type="string",description="用户ID"),
 *   @OA\Property(property="phone", type="string",description="手机号"),
 *   @OA\Property(property="product_id", type="string",description="产品ID"),
 *   @OA\Property(property="apply_no", type="string",description="申请号"),
 *   @OA\Property(property="sn", type="string",description="SN码"),
 *   @OA\Property(property="period_time", type="string",description="保修期"),
 *   @OA\Property(property="product_name", type="string",description="产品名称"),
 *   @OA\Property(property="product", type="object",ref="#/components/schemas/ProductInfo",description="产品信息")
 * )
 */



