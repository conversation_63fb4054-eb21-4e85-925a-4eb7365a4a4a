<?php

use OpenApi\Annotations as OA;

/**
 * @OA\Schema(
 *   schema="MemberActivityModule",
 *   type="object",
 *   @OA\Property(property="activity_id", type="integer", example=142, description="活动ID"),
 *   @OA\Property(property="module_relation_id", type="integer", example=270, description="模块关联ID"),
 *   @OA\Property(property="module_res_id", type="integer", example=1, description="模块资源ID"),
 *   @OA\Property(property="module_code", type="string", example="BIG_TITLE", description="模块编码"),
 *   @OA\Property(property="module_title", type="string", example="模块大标题", description="模块标题"),
 *   @OA\Property(property="module_summary", type="string", example="文案文案啊文案", description="模块文案"),
 *   @OA\Property(property="module_start_time", type="string", example="1744356288", description="模块开始时间时间戳"),
 *   @OA\Property(property="module_end_time", type="string", example="1746860916", description="模块结束时间时间戳"),
 *   @OA\Property(property="module_data", type="object", description="模块数据，根据不同模块类型有不同结构")
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleBigTitle",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="array",
 *     description="大标题模块数据，通常为空数组",
 *     @OA\Items(type="object")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleCheckin",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="object",
 *     description="打卡模块数据",
 *     @OA\Property(property="checkin_start_time", type="integer", example=1745164800, description="打卡开始时间"),
 *     @OA\Property(property="checkin_end_time", type="integer", example=1746806400, description="打卡结束时间"),
 *     @OA\Property(property="checkin_daily_points", type="integer", example=100, description="每日打卡积分"),
 *     @OA\Property(property="checkin_type", type="integer", example=1, description="打卡类型"),
 *     @OA\Property(property="checkin_days", type="integer", example=7, description="连续打卡天数"),
 *     @OA\Property(property="checkin_reward_points", type="integer", example=618, description="连续打卡奖励积分"),
 *     @OA\Property(property="member_center_save_id", type="string", example="1914175008858628098", description="会员中心保存ID")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleReturnPoints",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="object",
 *     description="购物返积分模块数据",
 *     @OA\Property(property="return_start_time", type="integer", example=1744356288000, description="返积分开始时间"),
 *     @OA\Property(property="return_end_time", type="integer", example=1746860916000, description="返积分结束时间"),
 *     @OA\Property(property="return_multiple", type="integer", example=1, description="返积分倍数"),
 *     @OA\Property(property="member_center_save_id", type="string", example="1914175009022205953", description="会员中心保存ID")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleAdPosition",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="object",
 *     description="广告位模块数据",
 *     @OA\Property(property="ad_btn_name", type="string", example="点我团购", description="广告按钮名称"),
 *     @OA\Property(property="ad_url", type="string", example="http://wwww.baidu.com", description="广告链接地址")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="DrawTask",
 *   type="object",
 *   @OA\Property(property="data_id", type="integer", example=465, description="数据ID"),
 *   @OA\Property(property="task_id", type="integer", example=1, description="任务ID"),
 *   @OA\Property(property="task_name", type="string", example="每天免费抽奖任务", description="任务名称"),
 *   @OA\Property(property="task_code", type="string", example="DAILY_FREE", description="任务编码"),
 *   @OA\Property(property="task_limit", type="integer", example=1, description="任务限制次数"),
 *   @OA\Property(property="task_limit_type", type="integer", example=1, description="任务限制类型"),
 *   @OA\Property(
 *     property="extra",
 *     type="object",
 *     description="额外参数",
 *     oneOf={
 *       @OA\Schema(
 *         @OA\Property(property="gift_times", type="string", example="1", description="赠送次数")
 *       ),
 *       @OA\Schema(
 *         @OA\Property(
 *           property="gift_times",
 *           type="array",
 *           @OA\Items(
 *             type="object",
 *             @OA\Property(property="invite_person", type="integer", example=1, description="邀请人数"),
 *             @OA\Property(property="gift_times", type="integer", example=1, description="赠送次数")
 *           )
 *         )
 *       )
 *     }
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="DrawPrize",
 *   type="object",
 *   @OA\Property(property="data_id", type="integer", example=254, description="数据ID"),
 *   @OA\Property(property="prize_id", type="integer", example=205, description="奖品ID"),
 *   @OA\Property(property="prize_type", type="integer", example=1, description="奖品类型"),
 *   @OA\Property(property="prize_name", type="string", example="谢谢参与", description="奖品名称"),
 *   @OA\Property(property="prize_value", type="string", example="", description="奖品价值"),
 *   @OA\Property(property="prize_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="奖品图片"),
 *   @OA\Property(property="prize_num", type="integer", example=999, description="奖品数量"),
 *   @OA\Property(property="prize_limit", type="integer", example=1, description="奖品限制"),
 *   @OA\Property(property="prize_rate", type="string", example="50.00", description="中奖概率")
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleDraw",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="object",
 *     description="抽奖活动模块数据",
 *     @OA\Property(property="draw_start_time", type="integer", example=1744356288, description="抽奖开始时间"),
 *     @OA\Property(property="draw_end_time", type="integer", example=1746860916, description="抽奖结束时间"),
 *     @OA\Property(
 *       property="draw_task",
 *       type="array",
 *       description="抽奖任务列表",
 *       @OA\Items(ref="#/components/schemas/DrawTask")
 *     ),
 *     @OA\Property(
 *       property="draw_prize",
 *       type="array",
 *       description="抽奖奖品列表",
 *       @OA\Items(ref="#/components/schemas/DrawPrize")
 *     )
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleNewPerson",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="array",
 *     description="新人入会福利模块数据，通常为空数组",
 *     @OA\Items(type="object")
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleGoods",
 *   type="object",
 *   @OA\Property(property="data_id", type="integer", example=113, description="数据ID"),
 *   @OA\Property(property="module_type", type="integer", example=1, description="模块类型"),
 *   @OA\Property(property="category_id", type="integer", example=1, description="分类ID"),
 *   @OA\Property(property="category_name", type="string", example="扫地机", description="分类名称"),
 *   @OA\Property(property="goods_id", type="integer", example=1, description="商品ID"),
 *   @OA\Property(property="goods_name", type="string", example="测试", description="商品名称"),
 *   @OA\Property(property="goods_image", type="string", example="https://wpm-cdn.dreame.tech/images/202408/66c5a0978026a5252030020.png", description="商品图片"),
 *   @OA\Property(property="goods_sku", type="string", example="testsku123", description="商品SKU"),
 *   @OA\Property(property="sale_price", type="string", example="9.99", description="销售价格"),
 *   @OA\Property(property="sort_order", type="integer", example=0, description="排序顺序"),
 *   @OA\Property(property="status", type="integer", example=1, description="状态")
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleRecommendGoods",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="array",
 *     description="机器推荐模块数据",
 *     @OA\Items(
 *       allOf={
 *         @OA\Schema(ref="#/components/schemas/ModuleGoods"),
 *         @OA\Schema(
 *           @OA\Property(property="show_name", type="string", example="", description="显示名称"),
 *           @OA\Property(property="free_periods", type="integer", example=12, description="免费期数"),
 *           @OA\Property(property="trade_subsidy", type="string", example="99.99", description="交易补贴")
 *         )
 *       }
 *     )
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="Coupon",
 *   type="object",
 *   @OA\Property(property="coupon_id", type="integer", example=1, description="优惠券ID"),
 *   @OA\Property(property="coupon_name", type="string", example="100", description="优惠券名称"),
 *   @OA\Property(property="coupon_image_price", type="string", example="100", description="优惠券图片显示金额"),
 *   @OA\Property(property="coupon_image_summary", type="string", example="满199可用", description="优惠券图片显示描述")
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModuleCoupon",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="object",
 *     description="优惠券模块数据",
 *     @OA\Property(property="coupon_activity_id", type="integer", example=1, description="优惠券活动ID"),
 *     @OA\Property(
 *       property="coupon",
 *       type="array",
 *       description="优惠券列表",
 *       @OA\Items(ref="#/components/schemas/Coupon")
 *     )
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModulePartGoods",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="array",
 *     description="配件专区模块数据",
 *     @OA\Items(
 *       allOf={
 *         @OA\Schema(ref="#/components/schemas/ModuleGoods"),
 *         @OA\Schema(
 *           @OA\Property(property="show_name", type="string", example="asdfasdfasf", description="显示名称"),
 *           @OA\Property(
 *             property="extra",
 *             type="object",
 *             description="额外参数",
 *             oneOf={
 *               @OA\Schema(
 *                 @OA\Property(property="type", type="integer", example=1, description="类型"),
 *                 @OA\Property(property="start_time", type="string", example="09:00:00", description="开始时间"),
 *                 @OA\Property(property="end_time", type="string", example="18:00:00", description="结束时间")
 *               ),
 *               @OA\Schema(
 *                 @OA\Property(property="type", type="integer", example=2, description="类型"),
 *                 @OA\Property(property="start_time", type="string", example="1744356288", description="开始时间时间戳"),
 *                 @OA\Property(property="end_time", type="string", example="1746860916", description="结束时间时间戳")
 *               )
 *             }
 *           )
 *         )
 *       }
 *     )
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="ModulePointsExchange",
 *   type="object",
 *   allOf={
 *     @OA\Schema(ref="#/components/schemas/MemberActivityModule")
 *   },
 *   @OA\Property(
 *     property="module_data",
 *     type="array",
 *     description="积分兑换模块数据",
 *     @OA\Items(
 *       allOf={
 *         @OA\Schema(ref="#/components/schemas/ModuleGoods"),
 *         @OA\Schema(
 *           @OA\Property(property="sale_points", type="integer", example=100, description="兑换所需积分")
 *         )
 *       }
 *     )
 *   )
 * )
 */

/**
 * @OA\Schema(
 *   schema="TaskListItem",
 *   type="object",
 *   @OA\Property(property="id", type="string", example="1", description="任务ID"),
 *   @OA\Property(property="name", type="string", example="每天免费抽奖任务", description="任务名称"),
 *   @OA\Property(property="task_code", type="string", example="DAILY_FREE", description="任务编码"),
 *   @OA\Property(property="type", type="string", example="1", description="任务类型：1-每日任务，2-累计任务"),
 *   @OA\Property(property="desc", type="string", example="每人每天免费获得1次参与机会，当日不用就清零", description="任务描述")
 * )
 */