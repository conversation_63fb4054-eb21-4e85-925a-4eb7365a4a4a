<?php

namespace app\modules\back\services;

use app\components\AppCRedisKeys;
use app\components\MessagePush;
use app\components\Review;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\wares\models\GoodsMainModel;

class GoodsReviewService
{
    private static $_instance = null;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 评价类型
    const REVIEW_TYPE = [
        'MAIN'   => 1, // 首次评价
        'APPEND' => 2, // 追加评价
    ];

    // 审核状态
    const AUDIT_STATUS = [
        'PASS'   => 0, // 通过
        'REJECT' => 1, // 拒绝
    ];

    const AUDIT_STATUS_NAME = [
        0 => '通过', // 通过
        1 => '拒绝', // 拒绝
    ];

    // 举报等级描述
    const REPORT_LEVEL_NAME = [
        1 => '轻度违规',
        2 => '中度违规',
        3 => '重度违规',
        4 => '紧急违规'
    ];

    // 举报状态
    const REPORT_STATUS = [
        'DEFAULT' => 1,
        'WAIT'    => 2,
        'PASS'    => 3,
        'REJECT'  => 4,
        'NOT_NEED_HANDLE'  => 5, // 无需处理
    ];

    // 举报状态描述
    const REPORT_STATUS_NAME = [
        1 => '已提交',
        2 => '待处理',
        3 => '成立',
        4 => '不成立',
        5 => '已有客服审核成立'
    ];

    // 评价类型
    const TYPE_NAME = [
        1 => '首次评价',
        2 => '追加评价',
    ];

    // 获取评价列表
    public function getReviewList(array $params): array
    {
        // 1.处理请求参数
        $uids[] = $params['uid'];

        if (!empty($params['phone'])) {
            $phoneUids = by::usersMall()->getUidByPhone($params['phone']);
            if (empty($phoneUids)) {
                return [true, []];
            }

            if (!empty($params['uid'])) {
                if (in_array($params['uid'], $phoneUids)) {
                    $uids[] = $params['uid'];
                } else {
                    return [true, []];
                }
            } else {
                $uids = $phoneUids;
            }
        }

        // 构建请求数据
        $data = [
            'reviewer_id'        => array_filter($uids),
            'entity_relation_id' => $params['order_no'],
            'types'              => $params['type'],
            'status'             => $params['status'],
            'start_create_time'  => $params['start_create_time'],
            'end_create_time'    => $params['end_create_time'],
            'sort_field'         => $params['sort_field'] ?? '',
            'sort_type'          => $params['sort_type'] ?? '',
            'page'               => intval($params['page']),
            'page_size'          => intval($params['page_size']),
        ];

        // 2.调用评价中台接口获取评价列表
        list($status, $res) = Review::factory()->backList($data);
        if (!$status || empty($res['list'])) {
            return [true, ['list' => [], 'total' => 0]];
        }

        // 获取sku、uid、order
        $uidArr = [];
        $orderArr = [];
        // 订单-SKU
        $orderSkuArr = [];

        // 3.处理返回数据
        $items = [];
        foreach ($res['list'] as $item) {
            $uid = $item['reviewer_id'];
            $sku = $item['entity_id'];
            $order_no = $item['entity_relation_id'];
            $items[] = [
                'review_id'       => $item['id'],
                'main_review_id'  => $item['first_review_id'] ?? 0,
                'order_no'        => $order_no,
                'sku'             => $sku,
                'uid'             => $uid,
                'content'         => $item['content'],
                'type'            => $item['types'],
                'type_name'       => self::TYPE_NAME[$item['types']] ?? '',
                'status'          => $item['status'],
                'refuse_reason'   => $item['refuse_reason'] ?? '',
                'create_time'     => $item['created_at'],
                'update_time'     => $item['updated_at'],
                'is_deleted'      => $item['is_deleted'],
            ];
            // 获取sku、uid、order
            $uidArr[] = $uid;
            $orderArr[] = $order_no;
            $orderSkuArr[$order_no][] = $sku;
        }

        // 获取用户手机号
        $phoneList = by::usersMall()->getPhoneListByUids($uidArr);
        // 订单创建时间
        $orderList = by::Omain()->getListByOrderNos($orderArr, ['order_no', 'ctime']);
        $createTimeList = [];
        foreach ($orderList as $item) {
            $createTimeList[$item['order_no']] = date('Y-m-d H:i:s', $item['ctime']);
        }
        // 商品名称
        $nameList = $this->getGoodsName($orderSkuArr);
        // 商品类型
        $typeList = $this->getGoodsType($orderSkuArr);

        foreach ($items as $key => $item) {
            $items[$key]['phone'] = $phoneList[$item['uid']] ?? '';
            $items[$key]['order_create_time'] = $createTimeList[$item['order_no']] ?? '';
            $items[$key]['goods_name'] = $nameList[$item['order_no']][$item['sku']] ?? '';
            $items[$key]['goods_type'] = $typeList[$item['order_no']][$item['sku']] ?? '';
        }

        // 返回处理后的数据
        return [true, ['list' => $items, 'total' => $res['total'] ?? 0]];
    }

    public function getReviewDetail(int $review_id): array
    {
        $data = [];

        // 调用评价中台，获取评价详情
        list($status, $res) = Review::factory()->detail($review_id);
        if (!$status || empty($res)) {
            return [false, '未获取到详情'];
        }

        $uid = '';
        $sku = '';
        $order_no = '';

        // 评价信息
        foreach ($res as $item) {
            if ($item['types'] == self::REVIEW_TYPE['MAIN']) {
                $data['review']['main'] = $this->formatReviewItem($item);

                // 获取uid、sku、order
                $uid = $item['reviewer_id'];
                $sku = $item['entity_id'];
                $order_no = $item['entity_relation_id'];

            } elseif ($item['types'] == self::REVIEW_TYPE['APPEND']) {
                $data['review']['append'] = $this->formatReviewItem($item);
            } else {
                return [false, '评价类型错误'];
            }
        }

        if (empty($data['review']['main'])) {
            return [false, '未获取到详情'];
        }

        // 用户信息
        $user = $this->getUserData($uid);
        $data['user'] = $user;

        // 订单信息
        $goods = $this->getGoodsData($user['user_id'], $order_no, $sku);
        $data['order'] = $goods;

        return [true, $data];
    }

    /**
     * 根据主评id获取追评详情
     * @param int $review_id
     * @return array
     */
    public function getAppendDetailByMainReviewId(int $review_id): array
    {
        $data = [];

        list($status, $res) = Review::factory()->detail($review_id);
        if (!$status || empty($res)) {
            return [];
        }

        foreach ($res as $item) {
            if ($item['types'] == self::REVIEW_TYPE['APPEND']) {
                $data = $this->formatReviewItem($item);
            }
        }

        return $data;
    }

    /**
     * 审核列表
     * @param $review_id
     * @return array
     */
    public function getAuditList($review_id): array
    {
        // 调用评价中台，获取审核记录列表
        list($status, $res) = Review::factory()->auditList($review_id, 1); // 不通过的审核记录
        if (!$status || empty($res) || !is_array($res)) {
            return [];
        }

        $data = [];
        foreach ($res as $item) {
            $data[] = [
                'audit_id'    => $item['id'],
                'review_id'   => $item['review_id'],
                'auditor_id'  => $item['auditor_id'] ?? '',
                'status'      => $item['status'], // 0 通过 1 拒绝
                'status_name' => self::AUDIT_STATUS_NAME[$item['status']] ?? '',
                'reason_id'   => $item['reason_id'],
                'content'     => $item['content'],
                'create_time' => $item['created_at'],
            ];
        }
        return $data;
    }

    /**
     * 审核评价
     * @param array $params
     * @return array
     */
    public function auditReview(array $params): array
    {
        try {
            // 审核数据
            if ($params['status'] == self::AUDIT_STATUS['PASS']) {
                $audit_data = [
                    'review_id' => $params['review_id'],
                    'content'   => '',
                    'status'    => $params['status'],
                    'reason_id' => $params['reason_id'],
                    'account'   => $params['account'],
                ];
            } else {
                $audit_data = [
                    'review_id' => $params['review_id'],
                    'content'   => $params['content'], // 拒绝理由
                    'status'    => $params['status'],
                    'reason_id' => $params['reason_id'],
                    'account'   => $params['account'],
                ];
            }
            // 审核评价
            list($status, $res) = Review::factory()->auditReview($audit_data);
            if (!$status) {
                return [false, '审核失败'];
            }

            // 审核通过
            if ($params['status'] == self::AUDIT_STATUS['PASS']) {
                // 有内容，商家回复
                if ($params['content']) {
                    $reply_data = [
                        'review_id' => $params['review_id'],
                        'account'   => $params['account'],
                        'content'   => $params['content'],
                    ];
                    Review::factory()->reply($reply_data);
                }
            } else {
                // 审核拒绝，通知用户
                list($status, $reviews) = Review::factory()->detail($params['review_id'], 2);
                // 校验
                if ($status && !empty($reviews[0])) {
                    $msg = [
                        'msgConfigId'  => intval(MessagePush::MSG_CONFIG_ID['REVIEW_AUDIT_REJECT']), // 评价审核拒绝
                        'pushStrategy' => 'crowd_push',
                        'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
                        'uids'         => [
                            $reviews[0]['reviewer_id']
                        ],
                        'ext'          => json_encode([
                            'link_url' => 'pagesB/evaluate/myEvaluate?index=1', // 跳转到已评价
                            'order_no' => substr($reviews[0]['entity_relation_id'], -4)
                        ], 320)
                    ];
                    MessagePush::factory()->run('sendPush', $msg);
                }
            }
            return [true, ''];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '审核失败'];
        }
    }

    // 下架评价
    public function removeReview(array $review_ids): array
    {
        try {
            list($status, $res) = Review::factory()->remove($review_ids);
            if (!$status) {
                return [false, '下架失败'];
            }
            return [true, ''];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '下架失败'];
        }
    }

    // 举报列表
    public function getReportList(array $params): array
    {
        // TODO 校验参数：订单号

        $skus = [];
        if (!empty($params['product_name'])) {
            // 根据商品名称获取sku
            $skus = $this->getSkusByProductName($params['product_name']);
            if (empty($skus)) {
                return [true, ['list' => [], 'total' => 0]];
            }
        }

        // 构建请求数据
        $data = [
            'id'                 => intval($params['id'] ?? 0),
            'review_id'          => intval($params['review_id'] ?? 0),
            'entity_relation_id' => $params['order_no'] ?? '',
            'entity_id'          => $skus,
            'reporter_id'        => $params['reporter_id'] ?? '',
            'reason_id'          => intval($params['type'] ?? 0),
            'level'              => intval($params['level'] ?? 0),
            'status'             => $params['status'] ?? [],
            'start_time'         => $params['start_create_time'] ?? '',
            'end_time'           => $params['end_create_time'] ?? '',
            'sort_field'         => $params['sort_field'] ?? '',
            'sort_type'          => $params['sort_type'] ?? '',
            'page'               => intval($params['page'] ?? 1),
            'page_size'          => intval($params['page_size'] ?? 20),
        ];


        // 2.调用评价中台接口获取评价列表
        list($status, $res) = Review::factory()->reportList($data);
        if (!$status || empty($res['list'])) {
            return [true, ['list' => [], 'total' => 0]];
        }

        $list = $res['list'];
        // 获取商品名称
        $orderSku = [];
        foreach ($list as $item) {
            $sku = $item['entity_id'];
            $order_no = $item['entity_relation_id'];
            $orderSku[$order_no][] = $sku;
        }
        $productNameList = $this->getGoodsName($orderSku);

        // 3.处理返回数据
        $items = [];
        foreach ($list as $item) {
            $items[] = [
                'report_id'             => $item['id'],                                     // 举报ID
                'review_id'             => $item['review_id'],                              // 评价ID（首评）
                'review_content'        => $item['review_content'],                         // 评价内容（首评）
                'append_review_content' => $item['append_review_content'],                  // 评价内容（追评）
                'reviewer_id'           => $item['reviewer_id'],                            // 被举报人ID
                'order_no'              => $item['entity_relation_id'],                     // 订单号
                'product_name'          => $productNameList[$item['entity_relation_id']][$item['entity_id']] ?? '', // 关联商品
                'type'                  => $item['reason_id'],                              // 举报类型
                'description'           => $item['description'],                            // 举报类型描述
                'content'               => $item['content'],                                // 举报内容
                'created_time'          => $item['created_at'],                             // 举报时间
                'reporter_id'           => $item['reporter_id'] ?? '',                      // 举报者ID
                'level'                 => $item['level'] ?? 1,                             // 风险等级
                'level_name'            => self::REPORT_LEVEL_NAME[$item['level']] ?? '',   // 风险等级
                'status'                => $item['status'],                                 // 状态
                'status_name'           => self::REPORT_STATUS_NAME[$item['status']] ?? '', // 状态描述
                'auditor_id'            => $item['auditor_id'],                             // 审核人
                'audit_time'            => $item['audit_time'],                             // 审核时间
                'review_is_deleted'     => $item['review_is_deleted'] ?? 0,                 // 评价是否删除
            ];
        }

        // 返回处理后的数据
        return [true, ['list' => $items, 'total' => $res['total'] ?? 0]];
    }

    // 举报详情
    public function getReportById(int $report_id): array
    {
        // 构建请求数据
        $params = [
            'id' => $report_id,
        ];

        // 2.调用评价中台接口获取评价列表
        list($status, $res) = Review::factory()->reportList($params);
        if (!$status || empty($res['list'][0])) {
            return [];
        }

        // 3.处理返回数据
        $item = $res['list'][0];
        $data= [
            'report_id'             => $item['id'],                                     // 举报ID
            'review_id'             => $item['review_id'],                              // 评价ID（首评）
            'review_content'        => $item['review_content'],                         // 评价内容（首评）
            'append_review_content' => $item['append_review_content'],                  // 评价内容（追评）
            'reviewer_id'           => $item['reviewer_id'],                            // 被举报人ID
            'order_no'              => $item['entity_relation_id'],                     // 订单号
            'type'                  => $item['reason_id'],                              // 举报类型
            'description'           => $item['description'],                            // 举报类型描述
            'content'               => $item['content'],                                // 举报内容
            'created_time'          => $item['created_at'],                             // 举报时间
            'reporter_id'           => $item['reporter_id'] ?? '',                      // 举报者ID
            'level'                 => $item['level'] ?? 1,                             // 风险等级
            'level_name'            => self::REPORT_LEVEL_NAME[$item['level']] ?? '',   // 风险等级
            'status'                => $item['status'],                                 // 状态
            'status_name'           => self::REPORT_STATUS_NAME[$item['status']] ?? '', // 状态描述
        ];

        // 返回处理后的数据
        return $data;
    }

    /**
     * 审核举报
     * @param array $params
     * @return array
     */
    public function auditReport(array $params): array
    {
        try {
            list($status, $res) = Review::factory()->auditReport($params);
            if (!$status) {
                return [false, '审核失败'];
            }
            return [true, ''];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('评价异常：' . json_encode($msg, 320), "err.review");
            return [false, '审核失败'];
        }
    }

    // 获取举报原因列表
    public function getReasonList(): array
    {
        $redis = by::redis('core');
        $cacheKey = AppCRedisKeys::getReportReasonListCacheKey();
        $cachedData = $redis->get(AppCRedisKeys::getReportReasonListCacheKey());

        if ($cachedData === false) {

            // 业务获取举报原因
            list($status, $res) = Review::factory()->reasonList();
            // 处理数据
            $data = array_map(function ($item) {
                return [
                    'id'   => $item['reason_id'],
                    'name' => $item['description']
                ];
            }, $res);

            // 存入缓存
            $expiration = empty($data) ? 10 : 3600;
            $redis->set($cacheKey, json_encode($data), ['EX' => $expiration]);
        } else {
            $data = json_decode($cachedData, true);
        }
        return $data;
    }

    // 批量创建系统评价
    public function batchCreateSystemReview($data)
    {
        if (empty($data)) {
            return;
        }

        $result = [];
        foreach ($data as $item) {
            $result[] = [
                'entity_id'          => $item['sku'],
                'reviewer_id'        => $item['uid'],
                'entity_relation_id' => $item['order_no'],
                'content'            => '好评！',
                'rating'             => [
                    1 => 5,
                    2 => 5,
                    3 => 5,
                    4 => 5,
                ],
                'is_anonymous'       => 1, // 匿名
                'is_default'         => 1, // 默认评价
                // 默认值
                'types'              => 1,
                'label'              => '',
                'video_url'          => '',
                'image_url'          => '',
            ];
        }
        Review::factory()->batchCreate($result);
    }

    // 获取商品名称
    // 数据格式：['order_001'=>['sku_001', 'sku_002'], 'order_002'=>['sku_003', 'sku_004']]
    public function getGoodsName(array $orderSkus): array
    {
        $items = by::Ocfg()->getListByOrderNos(array_keys($orderSkus));
        $orderSkuGidMap = [];
        $gids1 = [];
        $gids2 = [];

        // 构建订单号和SKU到商品ID的映射关系
        foreach ($items as $item) {
            $order = $item['order_no'];
            $sku = ($item['atype'] == 1) ? $item['spec']['sku'] : $item['sku'];
            $orderSkuGidMap[$order][$sku] = ['gid' => $item['gid'], 'goods_type' => $item['goods_type']];
            if ($item['goods_type'] == 1){
                $gids1[] = $item['gid'];
            } else {
                $gids2[] = $item['gid'];
            }
        }

        // 获取商品信息1
        $goods1 = by::Gmain()->getDataByIds($gids1, ['id', 'name']);
        $goodsName1 = array_column($goods1, 'name', 'id');

        // 获取商品信息1
        $goods2 = byNew::GoodsMainModel()->getDataByIds($gids2, ['id', 'name']);
        $goodsName2 = array_column($goods2, 'name', 'id');

        // 构建订单号和SKU到商品名称的映射关系
        $result = [];

        foreach ($orderSkus as $order => $skus) {
            foreach ($skus as $sku) {
                $gid = $orderSkuGidMap[$order][$sku]['gid'] ?? 0;
                $goods_type = $orderSkuGidMap[$order][$sku]['goods_type'] ?? 1;
                if ($goods_type == 1) {
                    $result[$order][$sku] = $goodsName1[$gid] ?? '';
                } else {
                    $result[$order][$sku] = $goodsName2[$gid] ?? '';
                }
            }
        }

        return $result;
    }


    // 获取商品类型，通过sku
    public function getGoodsType(array $orderSkus): array
    {
        // 获取主sku
        $items = by::Ocfg()->getListByOrderNos(array_keys($orderSkus));
        $orderSkuMainMap = [];
        $mainSkus = [];
        $gids2 = [];

        // 构建订单号和SKU到商品ID的映射关系
        foreach ($items as $item) {
            $order = $item['order_no'];
            $sku = ($item['atype'] == 1) ? $item['spec']['sku'] : $item['sku'];
            $orderSkuMainMap[$order][$sku] = [
                'gid'        => $item['gid'],
                'sku'        => $item['sku'],
                'goods_type' => $item['goods_type'],
            ];
            if ($item['goods_type'] == 1) {
                $mainSkus[] = $item['sku'];
            } else {
                $gids2[] = $item['gid'];
            }
        }


        // 通过主sku获取c_id
        $cate_arr = [];
        $cate_ids = by::gCateModel()->getDataBySkus($mainSkus, ['c_id as cate_id', 'sku']);
        foreach ($cate_ids as $cate_id) {
            $sku = $cate_id['sku'];
            $cate_id = $cate_id['cate_id'];
            $cate_arr[$sku] = $cate_id;
        }

        // 通过cate_id获取分类
        $cate_names = $this->getCateNameByIds(array_values($cate_arr));

        // 将sku和分类对应
        $mainSkuName = [];
        foreach ($cate_arr as $main_sku => $cate_arr_id) {
            $cate_name = $cate_names[$cate_arr_id];
            unset($cate_name[3]); // 去掉第三级分类
            // 找sku的key

            $mainSkuName[$main_sku] = implode('-', $cate_name);
        }


        // 获取商品信息1
        $goods2 = byNew::GoodsMainModel()->getDataByIds($gids2, ['id', 'name', 'source']);
        $goodsName2 = array_column($goods2, null, 'id');

        $result = [];
        foreach ($orderSkus as $order => $skus) {
            foreach ($skus as $sku) {
                $item = $orderSkuMainMap[$order][$sku] ?? '';
                $main_sku = $item['sku'] ?? '';
                if (($item['goods_type'] ?? 1) == 1) { // 默认为1
                    $result[$order][$sku] = $mainSkuName[$main_sku] ?? '';
                } else {
                    $source = $goodsName2[$item['gid']]['source'] ?? '';
                    if (GoodsMainModel::SOURCE['POINTS'] == $source) { // 积分商城
                        $result[$order][$sku] = '积分商城';
                    } else {
                        $result[$order][$sku] = '其他';
                    }

                }
            }
        }
        return $result;
    }

    /**
     * 根据商品名称获取sku
     * @param string $product_name
     * @return array
     */
    public function getSkusByProductName(string $product_name): array
    {
        if (empty($product_name)) {
            return [];
        }

        // 获取主sku
        $skus = array_column(by::Gmain()->getProductInfoByName($product_name, ['sku']), 'sku');

        if (empty($skus)) {
            return [];
        }

        // 获取主sku和多规格sku，有多规格的取多规格sku，没有取主sku
        $items = by::Gmain()->getProductInfoBySku($skus, ['main.sku as main_sku', 'specs.sku as specs_sku']);

        return array_map(function ($item) {
            return $item['specs_sku'] ?? $item['main_sku'];
        }, $items);
    }

    // 格式化评价数据
    private function formatReviewItem($item): array
    {
        return [
            'review_id'     => $item['id'],
            'sku'           => $item['entity_id'],
            'uid'           => $item['reviewer_id'],
            'order_no'      => $item['entity_relation_id'],
            'label'         => $item['label'] ?: [],
            'content'       => $item['content'],
            'image_url'     => $item['image_url'] ?: [],
            'video_url'     => $item['video_url'] ?: [],
            'rating'        => $item['rating'] ?: [],
            'is_anonymous'  => $item['is_anonymous'],
            'status'        => $item['status'],
            'refuse_reason' => $item['refuse_reason'] ?? '', // 拒绝原因
            'create_time'   => $item['created_at'],
            'updated_at'    => $item['updated_at'],
            'is_deleted'    => $item['is_deleted'],
            'reply'         => $this->formatReply($item['reply']),
        ];
    }

    // 格式化回复数据
    private function formatReply($reply): array
    {
        $data = [];
        if (!empty($item = $reply[0])) {
            $data = [
                'reply_id'    => $item['id'],
                'content'     => $item['content'],
                'create_time' => $item['created_at'],
            ];
        }
        return $data;
    }

    // 获取用户信息
    private function getUserData($uid): array
    {
        try {
            //用户信息
            $user = by::usersMall()->getMallInfoByUidWithDeleted($uid, true, true);
            $data = [
                'uid'       => $uid,
                'nick_name' => $user['nick_name'] ?? '',
                'phone'     => $user['phone'] ?? '',
            ];
            // 获取用户id
            $phone = by::Phone()->getDataByMallId($user['id']);
            $data['user_id'] = $phone['user_id'] ?? 0;
        } catch (\Exception $e) {
            $data = [];
        }
        return $data;
    }

    private function getGoodsData($user_id, $order_no, $sku): array
    {
        $order = by::Ouser()->CommPackageInfo($user_id, $order_no, false, true, false, false, true, false, true, true);
        if (!$order) {
            return [];
        }
        foreach ($order['goods'] as $item) {
            if ($item['sku'] == $sku) {
                $goods = $item;
            }
        }
        // 商品类型
        $product_types = $this->getGoodsType([$order_no=>[$sku]]);
        $data = [
            'order_no'     => $order_no,
            'create_time'  => date('Y-m-d H:i:s', $order['ctime']),
            'product'      => $goods['name'] ?? '',
            'product_type' => $product_types[$order_no][$sku] ?? '',
        ];
        return $data;
    }

    // 获取分类名称
    private function getCateNameByIds(array $ids): array
    {
        if (empty($ids)) {
            return [];
        }

        // 分类数据
        $items = by::cateModel()->getCateByIds($ids, ['id', 'name', 'pid', 'level']);

        // 将数据按照 level 从大到小排序
        usort($items, function ($a, $b) {
            return $a['level'] - $b['level'];
        });

        // id 作为键
        $items = array_column($items, null, 'id');

        // 重排数据
        $cate_data = [];

        foreach ($items as $item) {
            $pid = $item['pid'];
            $item['parent'] = [];
            if (!empty($cate_data[$pid])) {
                $item['parent'] = $cate_data[$pid];
            }
            $cate_data[$item['id']] = $item;
        }

        $cate_name = [];
        foreach ($ids as $id) {
            $result = [];
            if (!empty($item = $cate_data[$id])) {
                $currentItem = $item;
                $nestedArray = [];
                while (!empty($currentItem)) {
                    $nestedArray[$currentItem['level']] = $currentItem['name'];
                    $currentItem = $currentItem['parent'];
                }
                ksort($nestedArray); // 按照 level 排序
                $result = $nestedArray;
            }
            $cate_name[$id] = $result;
        }

        return $cate_name;
    }
}

// [phone] =>               转为uid，转为【reviewer_id】
// [uid] =>                 uid，转为【reviewer_id】
// [order_no] =>            转为【entity_relation_id】
// [type] =>                转为【types】
// [status] => 0            转为【status】
// [start_create_time] =>   转为【created_at】
// [end_create_time] =>     转为【created_at】
// [page] => 1              转为【page】
// [page_size] => 20        转为【page_size】
