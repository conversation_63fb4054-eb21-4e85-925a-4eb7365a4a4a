<?php

namespace app\modules\back\services\activitiesWares;

use app\exceptions\ActivityWaresException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use yii\helpers\Json;

/**
 * 通用活动
 */
class TryBeforeBuyActivity extends Activity
{

    /**
     * @param array $data
     * @return void
     * @throws ActivityWaresException
     * 验证数据
     */
    protected function validate(array $data)
    {
        // 只能存在一个上架活动
        $status = $data['status'] ?? 0;
        if ($status == byNew::ActivityModel()::STATUS['UP']) {
            $exists = byNew::ActivityModel()::find()->where(['status' => byNew::ActivityModel()::STATUS['UP'], 'grant_type' => byNew::ActivityModel()::GRANT_TYPE['tryBeforeBuy'], 'is_delete' => 0])->exists();
            if ($exists) {
                throw new ActivityWaresException('只能存在一个上架活动');
            }
        }

        // 时间不能为空、开始时间小于结束时间
        $start_time = $data['start_time'] ?? 0;
        $end_time   = $data['end_time'] ?? 0;
        if (empty($start_time)) {
            throw new ActivityWaresException("开始时间不能为空111");
        }

        if (empty($end_time)) {
            throw new ActivityWaresException("结束时间不能为空");
        }

        if ($start_time > $end_time) {
            throw new ActivityWaresException("开始时间不能大于结束时间");
        }

        // 活动海报
        if (empty($data['poster_image'])) {
            throw new ActivityWaresException('活动海报不能为空');
        }

        // 分享海报
//        if (empty($data['share_image'])) {
//            throw new ActivityWaresException('分享海报不能为空');
//        }

        // 试用商品
        if (empty($data['try_goods_ids'])) {
            throw new ActivityWaresException('试用商品不能为空');
        }


        //体验时间
        if (empty($data['validity'])) {
            throw new ActivityWaresException('体验时间不能为空');
        }

        //退机有效期
        if (empty($data['return_period'])) {
            throw new ActivityWaresException('退机有效期不能为空');
        }

        //是否审核
        if (isset($data['is_audit']) && !in_array($data['is_audit'], byNew::ActivityTypeModel()::IS_AUDIT)) {
            throw new ActivityWaresException('是否审核参数错误');
        }

        //收货有效期
        if (empty($data['delivery_period'])) {
            throw new ActivityWaresException('收货有效期不能为空');
        }

        // 限制条件
        if (!isset($data['join_condition']) || $data['join_condition'] == '') {
            throw new ActivityWaresException('限制条件不能为空');
        }

        // 活动背景图
        if (empty($data['rule'])) {
            throw new ActivityWaresException('规则详情不能为空');
        }
    }

    /**
     * 添加/编辑通用活动
     * @param array $data
     * @throws ActivityWaresException
     */
    protected function addOrUpdate(array $data)
    {
        // 开启事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 1、添加/更新活动配置表
            $id = CUtil::uint($data['id'] ?? 0);

            $item1 = [
                'name'        => $data['name'],
                'grant_type'  => $data['grant_type'],
                'start_time'  => $data['start_time'],
                'end_time'    => $data['end_time'],
                'status'      => $data['status'],
                'update_time' => time(),
            ];
            if (!$id) { // 新增数据时才有创建时间
                $item1['create_time'] = time();
            }
            $acId = byNew::activityModel()->saveLog($id, $item1);

            // 2、添加/更新 活动配置表(先用后买)
            $item2 = [
                'ac_id'           => $acId,
                'poster_image'    => $data['poster_image'],
                'share_image'     => $data['share_image'],
                'try_goods_ids'   => $data['try_goods_ids'],
                'validity'        => $data['validity'],
                'return_period'   => $data['return_period'],
                'delivery_period' => $data['delivery_period'],
                'survey_key'      => $data['survey_key'] ?? '',
                'pass_mark'       => empty($data['pass_mark']) ? 0 : $data['pass_mark'],
                'is_audit'        => $data['is_audit'] ?? 0,
                'join_condition'  => $data['join_condition'],
                'ac_ids'          => $data['ac_ids'] ?? '',
                'rule'            => $data['rule'],
                'update_time'     => time(),
            ];
            if (!$id) { // 新增数据时有创建时间
                $item2['create_time'] = time();
            }
            byNew::ActivityTypeModel()->saveLog($id, $item2);


            $transaction->commit();
            // 3、删除缓存
            byNew::ActivityModel()->delWActivityInfoById($acId);
            byNew::ActivityModel()->delWAListCacheKey();

        } catch (\Exception $e) {
            $transaction->rollBack();
            // 记录日志
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage(),'err.try_buy_activity');
            throw new ActivityWaresException('添加/编辑先用后付活动失败');
        }
    }
}
