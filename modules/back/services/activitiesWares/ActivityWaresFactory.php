<?php

namespace app\modules\back\services\activitiesWares;

use app\exceptions\ActivityException;
use app\modules\wares\models\ActivityModel;

/**
 * 活动的工厂类
 */
class ActivityWaresFactory
{
    /**
     * 创建活动
     * @param int $type
     * @return Activity
     * @throws \Exception
     */
    public function createActivity(int $type): Activity
    {
        switch ($type) {
            case ActivityModel::GRANT_TYPE['tryBeforeBuy'] :           // 1、先试后买
                $activity = new TryBeforeBuyActivity();
                break;
            default :
                throw new ActivityException("活动不存在");       // 活动不存在
        }
        return $activity;
    }
}
