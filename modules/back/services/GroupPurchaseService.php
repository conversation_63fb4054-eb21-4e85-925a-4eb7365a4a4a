<?php

namespace app\modules\back\services;

use app\components\collection\Collection;
use app\components\ErpNew;
use app\components\MessagePush;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\services\GiftCardService;
use yii\db\Exception;

class GroupPurchaseService
{

    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     *  获取商品列表筛选
     *  过滤：非预售 t_gtype_0.is_presale
     *       非内购 t_gtype_0.is_internal_purchase
     *       非自定义价格 t_gini.sku is null
     *  查询：商品名称：t_gmain.name
     */
    public function getGoodsList($goodsName): array
    {
        $query = by::Gmain()->find()
            ->select(['t_gmain.id', 't_gmain.name', 'price' => 'COALESCE(t_gtype_0.price)', 't_gtype_0.cover_image', 'atype'])
            ->leftJoin('`db_dreame_goods`.t_gtype_0', 't_gmain.id = t_gtype_0.gid')
                //            ->leftJoin('`db_dreame_goods`.t_gini', 't_gmain.sku = t_gini.sku')
            ->where([
                't_gtype_0.is_presale'           => by::Gtype0()::IS_PRESALE['no'],
                't_gtype_0.is_internal_purchase' => by::Gtype0()::IS_INTERNAL_PURCHASE['no'],
                't_gmain.is_del'                 => by::Gmain()::IS_DEL['no'],
                't_gmain.status'                 => by::Gmain()::STATUS['ON_SALE'],
                't_gmain.version'                => [0, 999999999]
            ]);

        if ($goodsName) {
            $query->andFilterWhere(['like', 't_gmain.name', $goodsName]);
        }

        $query->orderBy(['sort' => SORT_DESC, 't_gmain.id' => SORT_DESC]);

        return CUtil::Pg($query, function ($item) {
            return [
                'gid'         => $item['id'],
                'name'        => $item['name'],
                'price'       => CUtil::totalFee($item['price'], 1),
                'cover_image' => $item['cover_image']
            ];
        });

    }

    /**
     * 获取团购记录列表
     * @param $activity_id
     * @param $start_time
     * @param $end_time
     * @param $uid
     * @param $nick_name
     * @param $gid
     * @return array
     */
    public function getGroupRecordList($activity_id, $start_time, $end_time, $uid, $nick_name, $gid): array
    {
        $query = byNew::GroupPurchaseModel()->getGroupRecordQuery($activity_id, $start_time, $end_time, $uid, $nick_name, $gid);
        return CUtil::Pg($query, function ($item) {
            $item['max_purchases'] = $item['max_members'] * $item['purchase_limit'];
            $item['max_members']   = $item['max_members'] ?? 0;
            $end_time              = $item['activity']['end_time'] ?? '';

            // 如果当前时间大于活动结束时间且未拼团成功，则标记为拼团失败
            if ($end_time && time() > $end_time && $item['status'] == 0) {
                $item['status'] = "2";
            }
            return $item;
        });
    }

    /** 获取团购记录详情
     * @throws Exception
     */
    public function getGroupRecordDetail($id): array
    {
        // 获取团长信息
        $leader = byNew::GroupPurchaseModel()->getLeaderInfo($id);
        if (empty($leader)) {
            return [];
        }

        // 获取成员订单信息
        $members   = byNew::GroupPurchaseMemberModel()->getGroupMembers($leader['activity_id'], $leader['id']);
        $orderData = $this->getOrderData($members);


        return [
            'leader' => $leader,
            'orders' => $orderData
        ];
    }

    /**
     * 获取订单数据
     * @throws Exception
     */
    public function getOrderData($members): array
    {
        $orderData = [];
        foreach ($members as $member) {
            $orders = by::Ouser()->getOrders($member['user_id'], ['group_purchase_id' => $member['group_purchase_id'], 'user_id' => $member['user_id'],'order_no'=>$member['order_no']], ['price', 'pay_time', 'ctime', 'status', 'order_no', 'fprice']);
            foreach ($orders as $orderItem) {
                $goodsDetail               = by::Ogoods()->GetListByOrderNo($member['user_id'], $orderItem['order_no']);
                $orderItem['status_value'] = $orderItem['status'];
                $orderItem['status']       = by::Omain()::STATUS_NAME[$orderItem['status']] ?? '未知';
                $orderItem['num']          = array_sum(array_column($goodsDetail, 'num'));
                $orderItem['total_amount'] = array_reduce($goodsDetail, function ($carry, $item) {
                    // 计算商品总额=商品单价*购买数量
                    return CUtil::totalFee($item['oprice'], 1);
                }, 0);

                $orderItem['price'] = CUtil::totalFee($orderItem['price'] + $orderItem['fprice'], 1);
                unset($member['items_qty']);
                unset($member['ctime']);
                unset($member['order_no']);

                if ($orderItem['status'] == '申请退款') {
                    $orderItem['status'] = '退款中';
                }

                $orderData[] = array_merge($orderItem, $member);
            }
        }

        // 按照创建时间倒序排序
        $orderData = Collection::make($orderData)->sortBy('ctime')->values()->toArray();
        return $orderData;
    }


    /**
     * 导出团购记录
     */
    public function exportGroupRecord($activity_id = '', $start_time = '', $end_time = '', $uid = '', $nick_name = '', $gid = ''): array
    {
        $shellData[] = [
            '团长昵称', '团长手机号', '团ID', '团要求人数', '团状态', '商品ID', '活动ID', '团员昵称', '团员UID', '订单编号', '购买数量', '商品总额', '订单金额', '订单状态', '创建时间', '付款时间', '商品名称', '奖品ID', '奖品名称', '奖品发放状态'];

        $query = byNew::GroupPurchaseModel()->getGroupRecordQuery($activity_id, $start_time, $end_time, $uid, $nick_name, $gid);
        $count = $query->count();
        // 分页获取数据每次5000条
        $pageSize  = 5000;
        $pageCount = ceil($count / $pageSize);

        $groupPurchaseStatusMap = [
            1 => "拼团中",
            2 => "拼团成功",
            3 => "拼团结束",
        ];

        for ($i = 1; $i <= $pageCount; $i++) {
            $data = $query->offset(($i - 1) * $pageSize)->limit($pageSize)->asArray()->all();
            $data = Collection::make($data)->map(function ($item) {
                $item['max_purchases'] = $item['total_members'] * $item['purchase_limit'];
                return $item;
            })->toArray();

            foreach ($data as $item) {
                $status_name = $groupPurchaseStatusMap[$item["status"]] ?? "";

                $members   = byNew::GroupPurchaseMemberModel()->getGroupMembers($item['activity_id'], $item['id']);
                $orderData = $this->getOrderData($members);
                foreach ($orderData as $orderItem) {
                    if ($orderItem['is_reward'] == 1) {
                        $reward_status = '已发放';
                    } else {
                        $reward_status = '未发放';
                    }

                    $ctime       = date('Y-m-d H:i:s', $orderItem['ctime']);
                    $pay_time    = $orderItem['pay_time'] ? date('Y-m-d H:i:s', $orderItem['pay_time']) : '';
                    $shellData[] = [
                        $item['nick_name'] ?? '', $item['phone'] ?? '', $item["group_purchase_id"], $item["max_members"] ?? 0, $status_name, $item['gid'], $item['activity_id'],
                        $orderItem['nick_name'] ?? '', $orderItem['uid'], $orderItem['order_no'], $orderItem['num'], $orderItem['total_amount'],
                        $orderItem['price'], $orderItem['status'], $ctime, $pay_time, $item['goods_name'], $item['member_gift_card_id'] ?? '', $item['gift_card_name'] ?? '', $reward_status
                    ];
                }
            }
        }

        return $shellData;
    }

    public function autoSuccessFail()
    {
        $groupList = byNew::GroupPurchaseModel()->getListByFail();
        $logs      = [];

        $success_uids = [];
        $member_uids = [];
        if (count($groupList) > 0) {

            foreach ($groupList as $gItem) {

                $PushUrl = 'pagesB/inGroupPurchase/indexNew?group_purchase_id=' . $gItem['id'];

                // 获取团内所有成员订单
                $memberOrders = byNew::GroupPurchaseMemberModel()->getGroupList($gItem['activity_id'], $gItem['id']);
                $allRefunded = true; // 假设所有订单都已退款

                // 收集订单信息用于日志记录
                $orderStatusInfo = [];

                foreach ($memberOrders as $order) {
                    $member_uids [] = $order['uid'] ?? '';

                    $orderInfo = by::Omain()->getInfoByOrderNo($order['user_id'], $order['order_no']);

                    // 记录每个订单的状态信息
                    $orderStatusInfo[] = [
                        'order_no'    => $order['order_no'],
                        'user_id'     => $order['user_id'],
                        'status'      => $orderInfo['status'],
                        'is_refunded' => !($orderInfo && $orderInfo['status'] == 300)
                    ];

                    // 如果订单都等于待发货，则标记为全部退款为false （status 300 待发货 、400 待收货、 500 已完成、10000000 申请退款 ）
                    if ($orderInfo && in_array($orderInfo['status'], [300, 400, 500, 10000000])) {
                        $success_uids[] = $order['uid'] ?? '';
                        $allRefunded = false;
                    }
                }

                // 准备日志记录数据
                $logData = [
                    'group_id'         => $gItem['id'] ?? '',
                    'activity_id'      => $gItem['activity_id'] ?? '',
                    'status'           => $gItem['status'] ?? '',
                    'group_leader_uid' => $gItem['user_id'] ?? '',
                    'member_count'     => count($memberOrders),
                    'order_details'    => $orderStatusInfo,
                    'all_refunded'     => $allRefunded,
                    'action_time'      => date('Y-m-d H:i:s')
                ];

                // 如果订单全部退款,将状态更新为拼团失败(2)
                if ($allRefunded) {
                    $db = by::dbMaster();
                    $tb = byNew::GroupPurchaseModel()->tbName();
                    $db->createCommand()->update($tb, ['status' => 2, 'utime' => time()], ['id' => $gItem['id']])->execute();

                    // 记录拼团失败日志
                    $logData['result']    = 'failed';
                    $logData['reason']    = '所有订单已退款，拼团失败';
                    $order_nos            = array_column($memberOrders, 'order_no');
                    $logData['order_nos'] = implode(',', $order_nos);

                    // 失败推送
                    \app\modules\main\services\GroupPurchaseService::getInstance()->sendPush(
                        MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_LEADER_FAIL'],
                        array_unique($member_uids),
                        $PushUrl
                    );

                    CUtil::debug(json_encode($logData, JSON_UNESCAPED_UNICODE), 'group-purchase-auto-fail');

                } else {
                    // 拼团时间大于24小时且已有成员参加的团购,如果有未退款订单则设为成功状态
                    $db = by::dbMaster();
                    $tb = byNew::GroupPurchaseModel()->tbName();
                    $db->createCommand()->update($tb, ['status' => 1, 'utime' => time()], ['id' => $gItem['id']])->execute();

                    // 记录拼团成功日志
                    $logData['result'] = 'success';
                    $logData['reason'] = '超时但存在未退款订单，拼团成功';

                    // 成功推送
                    \app\modules\main\services\GroupPurchaseService::getInstance()->sendPush(
                        MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_MEMBER_REWARD_SUCCESS'],
                        array_unique($success_uids),
                        $PushUrl
                    );


                    CUtil::debug(json_encode($logData, JSON_UNESCAPED_UNICODE), 'group-purchase-auto-success');
                }

                $logs[] = $logData;
            }
        }
        return $logs;
    }


    /**
     * 拼团失败自动退款
     */
    public function autoRefund()
    {
        // 记录团长uid和团员uid，用于消息推送
        $leaderList = [];
        $memberUids = [];
        $refundList = [];
        // 获取已经结束的活动列表，结束时间大于当前时间，并且is_end=0的活动列表。
        /* $activityList = byNew::GroupPurchaseActivityModel()->getListByEndTime();
        if (count($activityList) > 0) {
            foreach ($activityList as $aItem) {
                // 查询所有的没有拼成功的团，即活动已结束，但是还处于拼团中的数据
                $groupList = byNew::GroupPurchaseModel()->getListByActivityId($aItem['id'],0);
                foreach ($groupList as $gItem) {
                    $allGoodsNum = 0;
                    // 查询团内所有成员的订单，并进行退款操作
                    $memberOrderList = byNew::GroupPurchaseMemberModel()->getGroupList($aItem['id'], $gItem['id']);
                    foreach ($memberOrderList as $mItem) {
                        // 理论上应该查询一下订单状态，如果订单已经发起退款或者已经退款成功，则不进行操作。暂时不写
                        $user_id = $mItem['user_id'];
                        $order_no = $mItem['order_no'];
                        $orderInfo = by::Omain()->getInfoByOrderNo($user_id, $order_no);
                        if (empty($orderInfo)) {
                            continue;
                        }
                        if ($orderInfo['status'] >= 10000000) {
                            continue;
                        }
                        try{
                            $mOgoods = by::Ogoods();
                            // 订单商品详情
                            $oGoods = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);
                            $oGoods = array_column($oGoods, 'status', 'id');
                            $og_ids = array_keys($oGoods);
                            if (empty($og_ids)) {
                                throw new Exception('订单号和用户ID不匹配或者订单已发起退款');
                            }
                            // 理论上发起退款的数据都是拼团失败的订单，这些订单没有发货，直接发起退款即可。
                            $arr = [
                                'order_no' => $order_no,
                                'user_id'  => $user_id,
                                'm_type'   => 1,
                                'r_type'   => 9,
                                'describe' => '拼团失败，系统自动发起退款',
                                'og_ids'   => json_encode($og_ids, 320),
                                'images'   => '',
                            ];
                            // 写入退款申请表，发起退款操作。
                            list($status, $msg) = by::OrefundMain()->ApplyRefund($user_id, $arr, false);
                            if(!$status) {
                                throw new Exception($msg);
                            }
                            // 队长不发消息
                            if ($mItem['uid'] != $gItem['uid']){
                                $memberUids[] = ['uid'=>$mItem['uid'],'order_no'=>$order_no];
                            }
                            $refundList[] = ['refund_no'=>$msg,'order_no'=>$order_no];
                            $allGoodsNum += $mItem['items_qty'];
                            // 退款成功
                        }catch (Exception $e) {
                            CUtil::debug($e->getMessage(), 'err.purchase.order.refund');
                        }
                    }
                    $leaderList[] = [
                        'uid'=>$gItem['uid'],
                        'all_num' => $allGoodsNum,
                        'create_time' => $gItem['ctime'],
                        'group_purchase_id' => $gItem['id']
                    ];
                    // 这里需要修改status=2字段，防止重复执行
                    $db = by::dbMaster();
                    $tb = byNew::GroupPurchaseModel()->tbName();
                    $id = $gItem['id'];
                    $resp = $db->createCommand()->update($tb, ['status' => 2], ['id' => $id])->execute();
                }

                // 这里需要修改is_end=1字段，防止重复执行
                $db = by::dbMaster();
                $tb = byNew::GroupPurchaseActivityModel()->tbName();
                $id = $aItem['id'];
                $resp = $db->createCommand()->update($tb, ['is_end' => 1], ['id' => $id])->execute();
            }
        }
        // 推送消息
        if (!empty($leaderList)) {
            $this->sendLeaderRefundMessage($leaderList);
        }
        if (!empty($memberUids)) {
            $this->sendMemberRefundMessage($memberUids);
        }*/

        // 查询所有的没有拼成功的团，即活动已结束，但是还处于拼团中的数据
        $groupList = byNew::GroupPurchaseModel()->getListByFail();
        foreach ($groupList as $gItem) {
            $allGoodsNum = 0;
            // 查询团内所有成员的订单，并进行退款操作
            $memberOrderList = byNew::GroupPurchaseMemberModel()->getGroupList($gItem['activity_id'], $gItem['id']);
            foreach ($memberOrderList as $mItem) {
                // 理论上应该查询一下订单状态，如果订单已经发起退款或者已经退款成功，则不进行操作。暂时不写
                $user_id   = $mItem['user_id'];
                $order_no  = $mItem['order_no'];
                $orderInfo = by::Omain()->getInfoByOrderNo($user_id, $order_no);
                if (empty($orderInfo)) {
                    continue;
                }
                if ($orderInfo['status'] >= 10000000) {
                    continue;
                }
                try {
                    $mOgoods = by::Ogoods();
                    // 订单商品详情
                    $oGoods = $mOgoods->GetListByOrderNo($user_id, $order_no, $mOgoods::STATUS['NORMAL']);
                    $oGoods = array_column($oGoods, 'status', 'id');
                    $og_ids = array_keys($oGoods);
                    if (empty($og_ids)) {
                        throw new Exception('订单号和用户ID不匹配或者订单已发起退款');
                    }
                    // 理论上发起退款的数据都是拼团失败的订单，这些订单没有发货，直接发起退款即可。
                    $arr = [
                        'order_no' => $order_no,
                        'user_id'  => $user_id,
                        'm_type'   => 1,
                        'r_type'   => 9,
                        'describe' => '拼团失败，系统自动发起退款',
                        'og_ids'   => json_encode($og_ids, 320),
                        'images'   => '',
                    ];
                    // 写入退款申请表，发起退款操作。
                    list($status, $msg) = by::OrefundMain()->ApplyRefund($user_id, $arr, false);
                    if (!$status) {
                        throw new Exception($msg);
                    }
                    // 队长不发消息
                    if ($mItem['uid'] != $gItem['uid']) {
                        $memberUids[] = ['uid' => $mItem['uid'], 'order_no' => $order_no];
                    }
                    $refundList[] = ['refund_no' => $msg, 'order_no' => $order_no];
                    $allGoodsNum  += $mItem['items_qty'];
                    // 退款成功
                } catch (Exception $e) {
                    CUtil::debug($e->getMessage(), 'err.purchase.order.refund');
                }
            }
            $leaderList[] = [
                'uid'               => $gItem['uid'],
                'all_num'           => $allGoodsNum,
                'create_time'       => $gItem['ctime'],
                'group_purchase_id' => $gItem['id']
            ];
            // 这里需要修改status=2字段，防止重复执行
            $db   = by::dbMaster();
            $tb   = byNew::GroupPurchaseModel()->tbName();
            $id   = $gItem['id'];
            $resp = $db->createCommand()->update($tb, ['status' => 2, 'utime' => time()], ['id' => $id])->execute();
        }
        // 写入Redis
        if (!empty($refundList)) {
            $r_key = 'group-purchase-auto-refund';
            $redis = by::redis();
            $redis->set($r_key, json_encode($refundList), ['ex' => 1200]);
        }
    }

    //
    public function autoRefundAudit()
    {
        $r_key = 'group-purchase-auto-refund';
        $redis = by::redis();
        if ($redis->exists($r_key)) {
            $aJson = $redis->get($r_key);
            if ($aJson) {
                $refundList = json_decode($aJson, true);
                foreach ($refundList as $item) {
                    $refund_no = $item['refund_no'];
                    $order_no  = $item['order_no'];
                    // 系统自动模拟后台手动退款操作,写入退款申请表成功时，msg为退款订单号
                    $refundData['refund_no'] = $refund_no;
                    $refundData['order_no']  = $order_no;
                    $refundData['status']    = 1010;
                    $refundData['force']     = 0;
                    $refundData['is_check']  = false;
                    list($status, $msg) = by::OrefundMain()->Audit($refundData);
                    if (!$status) {
                        CUtil::debug($msg, 'err.purchase.order.refund');
                    }
                }
            }
        }
    }

    /**
     * 拼团成功的订单统一发货
     */
    public function autoShipment()
    {
        // 获取拼团成功的团，即status=1，is_shipment=0的团列表
        $groupList = byNew::GroupPurchaseModel()->getListByStatus(1, 0);
        if (count($groupList) > 0) {
            foreach ($groupList as $gItem) {
                // 查询团内所有成员的订单，并进行发货操作
                $memberOrderList = byNew::GroupPurchaseMemberModel()->getGroupList($gItem['activity_id'], $gItem['id']);
                foreach ($memberOrderList as $mItem) {
                    try {
                        $user_id  = $mItem['user_id'];
                        $order_no = $mItem['order_no'];
                        // 理论上应该查询一下订单状态，如果订单已经发货或者已经退款成功，则不进行操作。暂时不写
                        $orderInfo = by::Omain()->getInfoByOrderNo($user_id, $order_no);
                        if ($orderInfo) {
                            if ($orderInfo['status'] == 300 || $orderInfo['status'] == 10000300) {
                                //todo 同步erp
                                $mOuser = by::Ouser();
                                //新旧erp锁
                                $lockOldErp = CUtil::omsLock($user_id, $order_no);
                                !$lockOldErp && $mOuser->ErpAddOrder($user_id, $order_no, 0, [], false);
                                $lockOldErp && ErpNew::factory()->synErp('addOrder', ['user_id' => $user_id, 'order_no' => $order_no]);
                            }
                        } else {
                            throw new Exception('订单不存在：' . $order_no);
                        }
                    } catch (Exception $e) {
                        CUtil::debug($e->getMessage(), 'err.purchase.order.shipment');
                    }
                }

                // 这里需要修改is_shipment=1字段，防止重复执行
                $db   = by::dbMaster();
                $tb   = byNew::GroupPurchaseModel()->tbName();
                $id   = $gItem['id'];
                $resp = $db->createCommand()->update($tb, ['is_shipment' => 1], ['id' => $id])->execute();
                // byNew::GroupPurchaseModel()->update(['is_shipment' => 1], ['id' => $gItem['id']]);
            }
        }
    }

    /**
     * 订单完成时，给团长和团员发放奖励
     */
    public function autoReward()
    {
        // 记录团长uid和团员uid，用于消息推送
        $leaderUids = [];
        $memberUids = [];
        // 查询团内所有已完成又没有发放奖励的订单
        // $last15day = strtotime('-15 day');
        $expire_time     = YII_ENV_PROD ? strtotime("-15 days") : strtotime("-1 minutes");
        $memberOrderList = byNew::GroupPurchaseMemberModel()->getList(['finish_time' => $expire_time, 'is_reward' => 0]);
        // 理论上已完成的订单不会退款，可以直接发放奖励
        if (count($memberOrderList) > 0) {
            foreach ($memberOrderList as $mItem) {

                try {
                    $trans = by::dbMaster()->beginTransaction();
                    //获取拼团数据
                    $groupInfo = byNew::GroupPurchaseModel()->getInfoById($mItem['group_purchase_id']);
                    //获取活动数据
                    $activityInfo = byNew::GroupPurchaseActivityModel()->getInfoById($mItem['activity_id']);
                    if (empty($groupInfo) || empty($activityInfo)) {
                        throw new Exception('拼团数据或活动数据不存在,订单号：' . $mItem['order_no']);
                    }
                    // // 团长不发奖励
                    // if ($mItem['user_id'] == $groupInfo['user_id']) {
                    //     // 这里需要修改is_reward=1字段，防止重复执行
                    //     $db = by::dbMaster();
                    //     $tb = byNew::GroupPurchaseMemberModel()->tbName();
                    //     $id = $mItem['id'];
                    //     $resp = $db->createCommand()->update($tb, ['is_reward' => 2,'utime'=> time()], ['id' => $id])->execute();
                    //     $trans->commit();
                    //     continue;
                    // }
                    $goodsInfo = byNew::GroupPurchaseActivityGoodsModel()->getInfoByGIdAndAid($groupInfo['gid'], $mItem['activity_id']);
                    if (empty($goodsInfo)) {
                        $trans->rollBack();
                        throw new Exception('拼团商品数据不存在,订单号：' . $mItem['order_no']);
                    }
                    // 给团员发放奖励
                    // 判断一下有没有设置团员奖励
                    if ($goodsInfo['member_gift_card_id'] > 0) {
                        list($status, $msg) = $this->reward($goodsInfo['member_gift_card_id'], $mItem['user_id'], $mItem['order_no']);
                        if (!$status) {
                            $trans->rollBack();
                            throw new Exception($msg);
                        }
                    }

                    // 给团长发放奖励
                    // 判断一下有没有设置团长奖励
                    // 团长自己下单不发团长奖励
                    if ($mItem['user_id'] != $groupInfo['user_id']) {
                        if ($goodsInfo['leader_gift_card_id'] > 0) {
                            list($status, $msg) = $this->reward($goodsInfo['leader_gift_card_id'], $groupInfo['user_id'], $mItem['order_no']);
                            if (!$status) {
                                $trans->rollBack();
                                throw new Exception($msg);
                            }
                        }
                        $leaderUids[] = $groupInfo['uid'];
                    }

                    $memberUids[] = $mItem['uid'];
                    // 这里需要修改is_reward=1字段，防止重复执行
                    $db   = by::dbMaster();
                    $tb   = byNew::GroupPurchaseMemberModel()->tbName();
                    $id   = $mItem['id'];
                    $resp = $db->createCommand()->update($tb, ['is_reward' => 1, 'utime' => time()], ['id' => $id])->execute();
                    $trans->commit();
                } catch (Exception $e) {
                    CUtil::debug($e->getMessage(), 'err.purchase.order.reward');
                }
            }
        }
        // 推送消息
        if (!empty($leaderUids)) {
            $this->sendLeaderRewardMessage($leaderUids);
        }
        if (!empty($memberUids)) {
            $this->sendMemberRewardMessage($memberUids);
        }

    }

    /**
     * 发放奖励通用方法
     */
    public function reward($giftCardId, $userId, $orderNo)
    {
        // 通过cardid随机获取一个卡密
        $cardInfo = byNew::GiftCardResources()->getOneUnusedCardResource($giftCardId);
        if (empty($cardInfo)) {
            return [false, '没有足够的礼品卡，无法发放奖励，请尽快联系管理员添加'];
        }
        // 发放奖励逻辑,模拟前台用户激活卡密操作
        GiftCardService::getInstance()->activateCard($userId, $cardInfo['card_password']);

        // 记录发放日志
        $insertData = [
            'user_id'      => $userId,
            'gift_card_id' => $giftCardId,
            'code'         => $cardInfo['card_password'],
            'order_no'     => $orderNo,
            'ctime'        => time(),
            'utime'        => time()
        ];
        byNew::GroupPurchaseRewardModel()->saveLog($insertData);
        //todo 发放奖励逻辑
        return [true, '发放成功'];
    }

    // 下面为发送消息推送的接口
    public function sendLeaderRefundMessage($leaderList)
    {
        foreach ($leaderList as $item) {
            $msg = [
                'msgConfigId'  => MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_LEADER_FAIL'], // 拼团失败
                'pushStrategy' => 'crowd_push',
                'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
                'uids'         => [$item['uid']],
                'ext'          => json_encode(['link_url' => 'pagesB/inGroupPurchase/index?group_purchase_id=' . $item['group_purchase_id']])
            ];
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团长拼团失败消息推送失败，原因：' . $data, 'err.purchase.order.refund');
            }
        }
    }

    public function sendMemberRefundMessage($memberUids)
    {
        foreach ($memberUids as $uid) {
            $msg = [
                'msgConfigId'  => MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_MEMBER_FAIL'], // 拼团失败
                'pushStrategy' => 'crowd_push',
                'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
                'uids'         => [$uid['uid']],
                'ext'          => json_encode(['link_url' => 'pagesA/orderDetail/orderDetail?order_no=' . $uid['order_no'] . '&user_order_type=8'])
            ];
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团员拼团失败消息推送失败，原因：' . $data, 'err.purchase.order.refund');
            }
        }
    }

    public function sendLeaderRewardMessage($leaderUids)
    {
        foreach ($leaderUids as $uid) {
            $msg = [
                'msgConfigId'  => MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_LEADER_REWARD_SUCCESS'], // 拼团成功
                'pushStrategy' => 'crowd_push',
                'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
                'uids'         => [$uid],
                'ext'          => json_encode(['link_url' => 'pagesB/giftCard/index'])
            ];
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团长奖励发放消息推送失败，原因：' . $data, 'err.purchase.order.reward');
            }
        }
    }

    public function sendMemberRewardMessage($memberUids)
    {
        foreach ($memberUids as $uid) {
            $msg = [
                'msgConfigId'  => MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_MEMBER_REWARD_SUCCESS'], // 拼团成功
                'pushStrategy' => 'crowd_push',
                'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
                'uids'         => [$uid],
                'ext'          => json_encode(['link_url' => 'pagesB/giftCard/index'])
            ];
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团员奖励发放消息推送失败，原因：' . $data, 'err.purchase.order.reward');
            }
        }
    }

    // 处理拼团退款发送消息推送
    public function sendRefundMessage($orderNo, $type = 1)
    {
        // 通过订单号获取拼团信息
        $memberInfo = byNew::GroupPurchaseMemberModel()->getInfoByOrderNo(['order_no' => $orderNo]);
        if (empty($memberInfo)) {
            return;
        }
        $groupInfo = byNew::GroupPurchaseModel()->getInfoById(['id' => $memberInfo['group_purchase_id']]);
        if (empty($groupInfo)) {
            return;
        }
        if ($groupInfo['uid'] == $memberInfo['uid']) {
            return;
        }
        if ($type == 1) {
            $uid = $groupInfo['uid'];
        } else {
            $uid = $memberInfo['uid'];
        }

        $msg = [
            'msgConfigId'  => MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_REFUND'], // 拼团退款
            'pushStrategy' => 'crowd_push',
            'pushScope'    => MessagePush::SCOPE['PART'], // 推送给部分用户
            'uids'         => [$uid],
            'ext'          => json_encode(['link_url' => 'pagesB/inGroupPurchase/index?group_purchase_id=' . $memberInfo['group_purchase_id']])
        ];
        if ($type == 1) {
            // 团长退款
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团长退款消息推送失败，原因：' . $data, 'err.purchase.order.refund');
            }
        } else {
            // 团员退款
            list($status, $data) = MessagePush::factory()->run('sendPush', $msg);
            if (!$status) {
                CUtil::debug('团员退款消息推送失败，原因：' . $data, 'err.purchase.order.refund');
            }
        }
    }

    /**
     * 发送拼团最后三小时提醒消息
     * @return void
     */
    public function sendLastThreeHoursNotification()
    {
        try {
            // 获取最后三小时即将到期的拼团列表
            $groupList = byNew::GroupPurchaseModel()->getListByLastThreeHours();

            if (empty($groupList)) {
                CUtil::debug('没有需要发送最后三小时提醒的拼团', 'group-purchase-last-three-hours');
                return;
            }

            $totalGroups = count($groupList);
            $successCount = 0;
            $failCount = 0;

            foreach ($groupList as $group) {
                try {
                    // 获取该团的所有成员
                    $memberList = byNew::GroupPurchaseMemberModel()->getGroupList($group['activity_id'], $group['id']);

                    if (empty($memberList)) {
                        continue;
                    }

                    // 收集所有成员的uid
                    $memberUids = [];
                    foreach ($memberList as $member) {
                        $uid = by::Phone()->getUidByUserId($member['user_id']);
                        if (!empty($uid)) {
                            $memberUids[] = $uid;
                        }
                    }

                    if (empty($memberUids)) {
                        continue;
                    }

                    // 去重
                    $memberUids = array_unique($memberUids);

                    // 发送消息推送
                    $pushUrl = 'pagesB/inGroupPurchase/indexNew?group_purchase_id=' . $group['id'];

                    list($status, $data) = \app\modules\main\services\GroupPurchaseService::getInstance()->sendPush(
                        MessagePush::MSG_CONFIG_ID['GROUP_PURCHASE_IN_PROCESS'],
                        $memberUids,
                        $pushUrl
                    );

                    if ($status) {
                        $successCount++;
                        CUtil::debug(sprintf(
                            '拼团ID: %d, 成功发送最后三小时提醒给 %d 个用户',
                            $group['id'],
                            count($memberUids)
                        ), 'group-purchase-last-three-hours.success');
                    } else {
                        $failCount++;
                        CUtil::debug(sprintf(
                            '拼团ID: %d, 发送最后三小时提醒失败，原因: %s',
                            $group['id'],
                            $data
                        ), 'group-purchase-last-three-hours.fail');
                    }

                } catch (\Exception $e) {
                    $failCount++;
                    CUtil::debug(sprintf(
                        '拼团ID: %d, 发送最后三小时提醒异常: %s',
                        $group['id'] ?? 0,
                        $e->getMessage()
                    ), 'err.group-purchase-last-three-hours');
                }
            }

            // 记录汇总日志
            CUtil::debug(sprintf(
                '最后三小时提醒推送完成，总团数: %d, 成功: %d, 失败: %d',
                $totalGroups,
                $successCount,
                $failCount
            ), 'group-purchase-last-three-hours.summary');

        } catch (\Exception $e) {
            CUtil::debug('发送拼团最后三小时提醒失败: ' . $e->getMessage(), 'err.group-purchase-last-three-hours');
        }
    }

}