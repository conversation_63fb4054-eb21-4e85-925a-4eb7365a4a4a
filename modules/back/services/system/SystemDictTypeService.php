<?php

namespace app\modules\back\services\system;

use app\models\BusinessException;
use app\models\by;
use app\modules\back\models\system\SystemDictDataModel;
use app\modules\back\models\system\SystemDictTypeModel;
use app\modules\common\Singleton;

final class SystemDictTypeService
{
    use Singleton;

    /**
     * 获取分页列表
     * @param array $params 请求参数
     * @return array
     */
    public function getPageList(array $params = []): array
    {
        $model = SystemDictTypeModel::getInstance();
        $params['__select_fields__'] = ['id', 'name', 'code', 'status', 'remark', 'create_time'];
        return $model->getPageList($params);
    }

    /**
     * 新增字典类型
     * @param array $data 新增数据
     * @return array
     */
    public function create(array $data): array
    {
        $model = SystemDictTypeModel::getInstance();
        try {
            if ($model->isExistsCode($data['code'])) {
                throw new BusinessException('字典标识已存在');
            }

            list($status, $id, $msg) = $model->doCreate($data);
            if (! $status) {
                throw new BusinessException($msg);
            }

            return [true, $id, '新增成功'];
        } catch (\Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

    /**
     * 更新字典类型
     * @param array $data 更新数据
     * @return array
     */
    public function update(array $data): array
    {
        $id = (int) ($data['id'] ?? 0);
        if (empty($id)) {
            return [false, '字典ID不能为空'];
        }
        $model = SystemDictTypeModel::getInstance();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $one = $model->getOne($id);
            if (empty($one)) {
                throw new BusinessException(sprintf('字典ID[%d]不存在', $id));
            }

            if ($model->isExistsCode($data['code'], $id)) {
                throw new BusinessException('字典标识已存在');
            }

            list($status, $msg) = $model->doUpdate($id, $data);
            if (! $status) {
                throw new BusinessException($msg);
            }

            $dictDataModel = SystemDictDataModel::getInstance();
            // 如果更新了code，对应字典数据也要一起更新
            if ($one['code'] != $data['code']) {
                list($status, $msg) = $dictDataModel->doUpdateCode($one['code'], $data['code']);
                if (! $status) {
                    throw new BusinessException($msg);
                }
            }

            // 同步字典数据状态
            if (isset($data['status']) && $one['status'] != $data['status']) {
                list($status, $msg) = $dictDataModel->doUpdateData(['status' => $data['status'] == 1 ? 1 : 0], ['dict_type_id' => $id]);
                if (! $status) {
                    throw new BusinessException($msg);
                }
            }

            $trans->commit();

            $dictDataModel->__delDictDataCacheByCode($one['code']);

            return [true, '更新活动成功'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 删除字典类型
     * @param array $ids 字典IDs
     * @return array
     */
    public function delete(array $ids): array
    {
        if (empty($ids)) {
            return [false, '字典ID不能为空'];
        }

        $model = SystemDictTypeModel::getInstance();
        list($status, $msg) = $model->doDelete($ids);

        if (! $status) {
            return [false, $msg];
        }

        return [true, '删除成功'];
    }

    /**
     * 更新状态
     * @param array $post 请求数据
     * @return array
     */
    public function changeStatus(array $post): array
    {
        $id = $post['id'] ?? 0;
        $status = $post['status'] ?? 0;
        if (empty($id)) {
            return [false, '字典ID不能为空'];
        }

        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $model = SystemDictTypeModel::getInstance();
            $one = $model->getOne($id);
            if (empty($one)) {
                throw new BusinessException(sprintf('字典ID[%d]不存在', $id));
            }

            list($s, $msg) = $model->doUpdate($id, ['status' => $status == 1 ? 1 : 0]);
            if (! $s) {
                throw new BusinessException($msg);
            }

            // 同步字典数据状态
            $dictDataModel = SystemDictDataModel::getInstance();
            list($s, $msg) = $dictDataModel->doUpdateData(['status' => $status == 1 ? 1 : 0], ['dict_type_id' => $id]);
            if (! $s) {
                throw new BusinessException($msg);
            }

            $trans->commit();

            $dictDataModel->__delDictDataCacheByCode($one['code']);

            return [true, 'success'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];
        }
    }
}