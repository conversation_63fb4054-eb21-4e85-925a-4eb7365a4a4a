<?php

namespace app\modules\back\services\system;

use app\models\BusinessException;
use app\models\by;
use app\modules\back\models\system\SystemDictDataModel;
use app\modules\common\Singleton;

final class SystemDictDataService
{
    use Singleton;

    /**
     * 获取分页列表
     * @param array $params 请求参数
     * @return array
     */
    public function getPageList(array $params = []): array
    {
        $model = SystemDictDataModel::getInstance();
        $params['page_size'] = 999;
        $params['__select_fields__'] = ['id', 'dict_type_id', 'dict_type_code', 'label', 'value', 'sort', 'status', 'remark', 'create_time', 'update_time'];
        return $model->getPageList($params);
    }

    /**
     * 获取列表(无分页)
     * @param array $params 请求参数
     * @return array
     * @throws BusinessException
     */
    public function getList(array $params = []): array
    {
        if (empty($params['code'])) {
            throw new BusinessException('字典标识不能为空');
        }
        $model = SystemDictDataModel::getInstance();
        return $model->getList($params);
    }
    
    /**
     * 获取所有列表(无分页)
     * @param array $params 请求参数
     * @return array
     * @throws BusinessException
     */
    public function getAllList(array $params = []): array
    {
        if (empty($params['code'])) {
            throw new BusinessException('字典标识不能为空');
        }
        $model = SystemDictDataModel::getInstance();
        return $model->getAllList($params);
    }

    /**
     * 新增字典数据
     * @param array $data 新增数据
     * @return array
     */
    public function create(array $data): array
    {
        $model = SystemDictDataModel::getInstance();
        try {
            if ($model->isExistsLabelValue($data['dict_type_id'], $data['label'], $data['value'])) {
                throw new BusinessException(sprintf('%s-%s 已存在', $data['label'], $data['value']));
            }

            list($status, $id, $msg) = $model->doCreate($data);
            if (! $status) {
                throw new BusinessException($msg);
            }

            return [true, $id, '新增成功'];
        } catch (\Throwable $e) {
            return [false, 0, $e->getMessage()];
        }
    }

    /**
     * 更新字典数据
     * @param array $data 更新数据
     * @return array
     */
    public function update(array $data): array
    {
        $id = (int) ($data['id'] ?? 0);
        if (empty($id)) {
            return [false, '字典ID不能为空'];
        }
        $model = SystemDictDataModel::getInstance();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            if ($model->isExistsLabelValue($data['dict_type_id'], $data['label'], $data['value'], $id)) {
                throw new BusinessException(sprintf('%s-%s 已存在', $data['label'], $data['value']));
            }


            list($status, $msg) = $model->doUpdate($id, $data);
            if (! $status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            return [true, '更新活动成功'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 删除字典数据
     * @param array $ids 字典IDs
     * @return array
     */
    public function delete(array $ids): array
    {
        if (empty($ids)) {
            return [false, '字典ID不能为空'];
        }

        $model = SystemDictDataModel::getInstance();
        list($status, $msg) = $model->doDelete($ids);

        if (! $status) {
            return [false, $msg];
        }

        return [true, '删除成功'];
    }

    /**
     * 更新状态
     * @param array $post 请求数据
     * @return array
     */
    public function changeStatus(array $post): array
    {
        $id = $post['id'] ?? 0;
        $status = $post['status'] ?? 0;
        if (empty($id)) {
            return [false, '字典ID不能为空'];
        }

        $model = SystemDictDataModel::getInstance();
        list($status, $msg) = $model->doUpdate($id, ['status' => $status == 1 ? 1 : 0]);

        if (! $status) {
            return [false, $msg];
        }

        return [true, 'success'];
    }

    /**
     * 更新排序
     * @param array $post 请求数据
     * @return array
     */
    public function changeSort(array $post): array
    {
        $id = $post['id'] ?? 0;
        $sort = $post['sort'] ?? 0;
        if (empty($id)) {
            return [false, '字典ID不能为空'];
        }

        $model = SystemDictDataModel::getInstance();
        list($status, $msg) = $model->doUpdate($id, ['sort' => $sort]);

        if (! $status) {
            return [false, $msg];
        }

        return [true, 'success'];
    }

    /**
     * 清除字典缓存
     * @param array $params 请求参数
     * @return bool
     */
    public function clearCache(array $params = []): bool
    {
        $code = $params['code'] ?? '';
        $model = SystemDictDataModel::getInstance();
        if (empty($code)) {
            return $model->__delDictDataCache();
        }
        return $model->__delDictDataCacheByCode($code);
    }
}