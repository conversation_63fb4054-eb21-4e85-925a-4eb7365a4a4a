<?php

namespace app\modules\back\services;

use app\models\BusinessException;
use app\models\by;
use app\modules\common\Singleton;
use app\modules\goods\models\GoodsCrontabStockModel;
use app\modules\main\models\MemberActivityModel;

class GoodsCrontabStockService
{
    use Singleton;


    public function getList(array $params = []): array
    {
        $model = GoodsCrontabStockModel::getInstance();
        $ret = $model->getPageList($params);
        $gids = array_column($ret['list'], 'gid');
        $goodInfos = by::Gmain()->getDataByIds($gids, ['id','name']);
        $goodInfos = array_column($goodInfos, null, 'id');
        foreach ($ret['list'] as $k => $v) {
            $ret['list'][$k]['good_name'] = $goodInfos[$v['gid']]['name'] ?? '';
        }
        return $ret;
    }

    /**
     * 新增|保存
     * @param array $data
     * @return array
     */
    public function create(array $data): array
    {
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            list($st,  $id, $msg) =GoodsCrontabStockModel::getInstance()->doCreate($data);
            if (!$st) {
                throw new BusinessException($msg);
            }
            $trans->commit();
            return [true, $id, '活动创建成功'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, 0, $e->getMessage()];
        }
    }

    public function update(array $data): array
    {
        $id = (int)($data['id'] ?? 0);
        if (empty($id)) {
            return [false, '活动ID不能为空'];
        }
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            //更新
            list($st, $msg) = GoodsCrontabStockModel::getInstance()->doUpdate($id, $data);
            if (!$st) {
                throw new BusinessException($msg);
            }
            $trans->commit();
            return [true, $msg];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 删除
     * @param array $ids IDs
     * @return array
     */
    public function delete(array $ids): array
    {
        if (empty($ids)) {
            return [false, 'IDs不能为空'];
        }

        $model = GoodsCrontabStockModel::getInstance();
        foreach ($ids as $id) {
            $res = $model->getOne($id);
            if (empty($res)) {
                return [false, sprintf('ID[%s]不存在', $id)];
            }
        }

        list($status, $msg) = $model->doDelete($ids);

        if (! $status) {
            return [false, $msg];
        }

        return [true, '删除成功'];
    }

    /**
     * 详情
     * @param int $id
     * @return array
     */
    public function getDetail(int $id): array
    {
        $info = GoodsCrontabStockModel::find()->where(['id' => $id])->asArray()->one();
        if (empty($info)) {
            throw new BusinessException(sprintf('ID[%s]数据不存在', $id));
        }
        $goodInfo = by::Gmain()->GetOneByGid($info['gid']);
        $info['good_name'] = $goodInfo['name'] ?? '';
        return $info;
    }


}