<?php

namespace app\modules\back\services;

use app\components\Employee;
use app\models\by;
use app\models\byNew;
use app\modules\main\models\UserBindModel;

/**
 * 用户绑定服务
 */
class UserBindService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    const MAX_BIND_COUNT = 100; // 最大绑定数量

    /**
     * 绑定列表
     * @return void
     */
    public function getBindListByBoundUserId(array $params): array
    {
        // 分页信息
        $page      = $params['page'];
        $page_size = $params['page_size'];

        // 获取用户uid
        $bound_uid= by::Phone()->getUidByUserId($params['bound_user_id']);

        $param = [
            'bound_uid'   => $bound_uid,                           // 被绑定用户ID
            'bind_status' => $params['bind_status'] ?? null,       // 绑定状态
        ];
        $items = byNew::UserBindModel()->getBindList($param, $page, $page_size);

        // 获取用户员工id
        $user_employee_ids = $this->getEmployeeIds(array_column($items, 'uid'));
        if (empty($user_employee_ids)) {
            return [true, ['list' => [], 'total' => 0]];
        }

        // 查询员工信息
        $employees = $this->getEmployeeList(['uid' => array_values($user_employee_ids)]);

        // 整合数据
        $data  = $this->formatData($items, $employees, $user_employee_ids);
        $count = byNew::UserBindModel()->getBindCount($param);
        return [true, ['list' => $data, 'total' => $count]];
    }

    /**
     * 获取当前的绑定状态
     * @return void
     */
    public function getCurrentBindStatus(array $bound_uids): array
    {
        // 获取当前绑定用户
        $param = [
            'bound_uid'   => $bound_uids,                              // 被绑定用户ID
            'bind_status' => UserBindModel::BIND_STATUS['BIND'],       // 绑定状态：绑定
        ];

        $items                   = byNew::UserBindModel()->getBindList($param, 1, PHP_INT_MAX);
        $current_bind_bound_uids = array_column($items, 'bound_uid');

        $data = [];
        foreach ($bound_uids as $bound_uid) {
            $data[$bound_uid] = in_array($bound_uid, $current_bind_bound_uids) ? 1 : 0; // 1: 已绑定，0: 未绑定;
        }
        return $data;
    }

    /**
     * 获取员工列表
     * @param array $param
     * @return array
     */
    private function getEmployeeList(array $param): array
    {
        list($status, $employee) = Employee::factory()->employeeList($param);
        // 未获取到数据时，直接返回
        if (!$status || empty($employee['list'])) {
            return [];
        }
        return $employee['list'];
    }

    /**
     * 格式化数据
     * @param array $items
     * @param array $employees
     * @param array $employees_ids
     * @return array
     */
    private function formatData(array $items, array $employees, array $employees_ids): array
    {
        $employees = array_column($employees, null, 'uid');

        $data = [];
        foreach ($items as $item) {
            $uid         = $item['uid'];
            $employee_id = $employees_ids[$uid] ?? '';
            $employee    = $employees[$employee_id] ?? [];
            $data[]      = [
                'uid'         => $uid,
                'name'        => $employee['real_name'] ?? '',
                'employee_no' => $employee['employee_no'] ?? '',
                'bind_status' => $item['bind_status'],
                'bind_time'   => date('Y-m-d H:i:s', $item['bind_time']),
            ];
        }
        return $data;
    }

    /**
     * 获取员工id
     * @param array $uids
     * @return array
     */
    private function getEmployeeIds(array $uids): array
    {
        $param = [
            'uid' => $uids
        ];

        $employees = byNew::UserEmployeeModel()->getEmployeeList($param, 1, self::MAX_BIND_COUNT);
        return array_column($employees, 'employee_id', 'uid');
    }
}