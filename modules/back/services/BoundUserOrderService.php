<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\modules\goods\models\BoundUserOrderModel;
use app\modules\goods\models\OmainModel;

class BoundUserOrderService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 更新订单状态
     * @param int $id
     * @param int $limit
     * @return int
     * @throws \yii\db\Exception
     */
    public function updateStatus(int $id, int $limit): int
    {
        // 获取员工列表
        $list = byNew::BoundUserOrderModel()->getOrderListForUpdateStatus($id, $limit);
        if (empty($list)) {
            return $id;
        }

        // 待更新的订单号
        $update_order_nos = [];

        // 获取订单号
        $order_nos = array_column($list, 'order_no');
        // 获取订单信息
        $orders = by::Omain()->getListByOrderNos($order_nos, ['order_no', 'status']);
        foreach ($orders as $order) {
            if (in_array($order['status'], [
                OmainModel::ORDER_STATUS['CANCELED'],
                OmainModel::ORDER_STATUS['RERUNDED'],
                OmainModel::ORDER_STATUS['RERUNDED_WAIT_SEND'],
                OmainModel::ORDER_STATUS['RERUNDED_WAIT_RECEIVE'],
                OmainModel::ORDER_STATUS['RERUNDED_FINISHED'],
            ])) {
                $update_order_nos[] = $order['order_no'];
            }
        }

        // 批量更新订单状态
        if ($update_order_nos) {
            byNew::BoundUserOrderModel()->updateStatus($update_order_nos, BoundUserOrderModel::SCORE_STATUS['CANCEL']);
        }

        // 返回最后一个员工的id
        return end($list)['id'] ?? $id;
    }
}