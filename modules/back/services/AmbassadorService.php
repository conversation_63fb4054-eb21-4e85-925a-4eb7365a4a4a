<?php

namespace app\modules\back\services;

use app\exceptions\AmbassadorException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\enums\ambassador\AmbassadorEnum;
use app\modules\main\models\AmbassadorModel;

class AmbassadorService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function getAmbassadorInfo($id)
    {
        $model = byNew::AmbassadorModel();
        $info  = $model->getInfoById($id);

        if (!empty($info)) {
            $info = $info->toArray();

            // 获取用户手机号
            $info["phone"] = by::Phone()->GetPhoneByUid($info["user_id"]);
        }

        return $info;
    }

    /**
     * 获取大使列表数据
     * @param array $params
     * @return array
     */
    public function getAmbassadorList($params): array
    {
        $model = byNew::AmbassadorModel();
        $count = $model->getAmbassadorCount($params);

        $list  = [];
        $pages = 0;
        if ($count > 0) {
            list($offset, $limit) = CUtil::pagination($params["page"], $params["page_size"]);
            $pages = CUtil::getPaginationPages($count, $limit);

            $list = $model->getAmbassadorList($params, $offset, $limit);

            foreach ($list as $key => &$item) {
                $item["level_name"] = AmbassadorEnum::getLevelName($item["level"]);
            }
        }

        return [
            "count" => $count,
            "pages" => $pages,
            "list"  => $list,
        ];
    }

    /**
     * 编辑大使
     * @param array $params
     * @throws AmbassadorException
     */
    public function editAmbassadorInfo($params)
    {
        // 检查uid是否已存在
        $model     = byNew::AmbassadorModel();
        $checkInfo = $model->getInfoByUid($params["uid"], $params["id"] ?? 0);
        if (!empty($checkInfo)) {
            throw new AmbassadorException("当前uid已在列表中，请核实~");
        }

        // 根据手机号获取用户信息
        $phoneInfo = by::Phone()->GetInfoByPhone($params["phone"]);
        if (empty($phoneInfo)) {
            throw new AmbassadorException("手机号不存在，请核实~");
        }

        if (empty($params["id"])) {
            $info = new AmbassadorModel();

            // 创建时间
            $info->ctime = time();
        } else {
            $info = $model->getInfoById($params["id"]);
            if (empty($info)) {
                throw new AmbassadorException("数据不存在，请核实~");
            }
        }

        $level = $params["level"] ?? AmbassadorEnum::LEVEL_HIGH;
        if ($info->level != $level) {
            $info->update_level_month = date("Y-m-d");
        }

        $info->uid          = $params["uid"];
        $info->user_id      = $phoneInfo["user_id"];
        $info->name         = $params["name"] ?? "";
        $info->id_card      = $params["id_card"] ?? "";
        $info->bank_name    = $params["bank_name"] ?? "";
        $info->bank_card    = $params["bank_card"] ?? "";
        $info->contact_name = $params["contact_name"] ?? "";
        $info->level        = $level;
        $info->utime        = time();

        $info->save();
    }

    /**
     * 删除大使
     * @param int    $id
     * @param string $uid
     * @return void
     * @throws AmbassadorException
     */
    public function deleteAmbassadorInfo($id, $uid)
    {
        $model = byNew::AmbassadorModel();
        $info  = $model->getInfoById($id);
        if (empty($info) || $info->uid != $uid) {
            throw new AmbassadorException("数据不存在，请核实~");
        }

        $info->is_delete = 1;
        $info->utime     = time();
        $info->save();
    }

}