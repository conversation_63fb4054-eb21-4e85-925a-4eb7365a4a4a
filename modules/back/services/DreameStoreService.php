<?php

namespace app\modules\back\services;

use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\modules\common\Singleton;
use app\modules\goods\models\DreameStoreModel;
use app\modules\goods\models\StoreGoodsRelationModel;

/**
 * 追觅小店 - 服务层 - 后台
 */
class DreameStoreService
{
    use Singleton;


    /**
     * 列表
     */
    public function getList(array $params = []): array
    {
        $model = byNew::DreameStoreModel();
        $ret = $model->getPageList($params);
        $user_Ids = array_column($ret['list'], 'user_id');
        $uids = by::Phone()->getUidByUserIds($user_Ids);
        foreach ($ret['list'] as $k => $v) {
            $appData = [];
            $appData['uid'] = $uids[$v['user_id']] ?? '';
            $ret['list'][$k] = $v + $appData;
        }
        return $ret;
    }

    /**
     * 详情
     */
    public function info($id): array
    {
        //$info  = DreameStoreModel::instance()->findOne($id);
        $info  = byNew::DreameStoreModel()->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        return $info->toArray();
    }

    /**
     * 创建
     */
    public function create(array $data)
    {
        $user_id = (int)($data['user_id'] ?? 0);
        if (empty($user_id)) {
            throw new BusinessException('user_id不能为空');
        }
        $info  = byNew::DreameStoreModel()->getInfo(['user_id' => $user_id, 'is_delete' => 0]);
        if ($info) {
            throw new BusinessException(sprintf('当前用户店铺已存在'));
        }
        unset($data['id']);
        return byNew::DreameStoreModel()->saveData($data);
    }

    /**
     * 更新
     */
    public function update(array $data)
    {
        $id = (int)($data['id'] ?? 0);
        if (empty($id)) {
            throw new BusinessException('ID不能为空');
        }
        $info  = byNew::DreameStoreModel()->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        $where = ['id' => $id];
        $up = $data;
        unset($up['id'], $up['user_id']);  //不能修改店主
        $up = array_intersect_key($up, array_flip(['store_name', 'store_photo','max', 'status']));
        return byNew::DreameStoreModel()->updateInfo($where, $up);
    }

    /**
     * 审核
     */
    public function audit($ids,$status, $reason)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = byNew::DreameStoreModel();
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $where = ['id' => $ids];
        $up = ['status' => $status, 'reason' => $reason];
        $up = array_filter($up, function ($v) {return (string)$v !== '';});
        return $model->updateInfo($where, $up);
    }

    /**
     * 删除
     */
    public function del($ids, $soft = true)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = byNew::DreameStoreModel();
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $db = by::dbMaster();
        $trans = $db->beginTransaction();

        $where = ['id' => $ids];
        $up = ['is_delete' => 1];
        try {
            if ($soft) {
                $res = $model->updateInfo($where, $up);
            } else {
                $res = $model->deleteInfo($where);
            }
            if (!$res) {
                $trans->rollBack();
                return false;
            }
            //清理用户店铺商品
            $user_ids = array_column($infos, 'user_id');
            StoreGoodsRelationModel::instance()->deleteInfo(['user_id' => $user_ids]);
            $trans->commit();
        }catch (\Throwable $e){
            $trans->rollBack();
            throw new BusinessException($e->getMessage());
        }
        return $res;
    }
}