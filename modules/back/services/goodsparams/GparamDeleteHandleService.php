<?php

namespace app\modules\back\services\goodsparams;

use app\models\by;

/**
 * 删除分组参数
 */
class GparamDeleteHandleService implements GparamHandleService
{
    /**
     * 删除逻辑：删除 t_gparam_categroup_detail 后，会同步删除 t_gparam_goods 的数据
     * @param array $data
     * @return array
     * @throws \yii\db\Exception
     */
    public function operation(array $data): array
    {
        // 判空
        if (empty($data)) {
            return [true, 'ok'];
        }

        // 删除的id
        $ids = array_column($data, 'id');

        // 1、软删除 t_gparam_categroup_detail
        $cateGroupDetail = by::GparamCateGroupDetailModel();
        $cateGroupDetail->BatchDeleteLog($ids);

        // 2、软删除 t_gparam_goods
        $goods = by::GparamGoodsModel();
        $goods->BatchDeleteLog($ids);

        return [true, 'ok'];
    }
}