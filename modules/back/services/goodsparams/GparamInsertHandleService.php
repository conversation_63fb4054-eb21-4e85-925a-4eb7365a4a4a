<?php

namespace app\modules\back\services\goodsparams;

use app\models\by;

/**
 * 插入分组参数
 */
class GparamInsertHandleService implements GparamHandleService
{
    /**
     * 插入逻辑：插入不关联其他逻辑
     * @param array $data
     * @return array
     * @throws \yii\db\Exception
     */
    public function operation(array $data): array
    {
        // 判空
        if (empty($data)) {
            return [true, 'ok'];
        }

        // 模型
        $cateGroupDetail = by::GparamCateGroupDetailModel();

        // 处理插入的数据
        $rows = [];
        $time = time();
        foreach ($data as $value) {
            $rows[] = [
                'categroup_id' => $value['group_id'],
                'gparam_id' => $value['param_id'],
                'is_del' => 0,
                'ctime' => $time,
                'utime' => $time
            ];
        }
        $columns = ['categroup_id', 'gparam_id', 'is_del', 'ctime', 'utime'];
        $cateGroupDetail->BatchInsertLog($columns, $rows);

        return [true, 'ok'];
    }
}