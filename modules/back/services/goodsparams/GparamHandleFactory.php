<?php

namespace app\modules\back\services\goodsparams;

class GparamHandleFactory
{
    /**
     * 创建处理对象
     * @param string $operate
     * @return GparamHandleService
     * @throws \Exception
     */
    public static function createHandle(string $operate): GparamHandleService
    {
        $handle = null;
        switch ($operate) {
            case 'INSERT' :
                $handle = new GparamInsertHandleService();
                break;
            case 'DELETE' :
                $handle = new GparamDeleteHandleService();
                break;
            default :
                throw new \Exception("处理对象不存在");
        }
        return $handle;
    }
}