<?php

namespace app\modules\back\services;

use app\exceptions\ActivityException;
use app\modules\back\services\activities\ActivityFactory;
use app\modules\main\models\ActivityConfigModel;

/**
 * 活动配置的服务
 */
class ActivityService
{
    /**
     * @var ActivityFactory
     */
    protected $factory;

    public function __construct()
    {
        $this->factory = new ActivityFactory();
    }

    /**
     * 添加/编辑活动
     * @param $data
     * @return void
     * @throws \Exception
     */
    public function modify($data)
    {
        // 编辑时，活动类型不可修改
        $id = $data['id'];
        $type = $data['grant_type'];
        if ($id) {
            $activityConfig = ActivityConfigModel::findOne($id);
            if (!$activityConfig) {
                throw new ActivityException("活动记录不存在");
            }
            if ($activityConfig['grant_type'] != $type) {
                throw new ActivityException("活动类型不可修改");
            }
        }
        // 处理业务
        $activity = $this->factory->createActivity($type);
        return $activity->modify($data);
    }
}