<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

/**
 * 先试后买用户
 */
class UserTryService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }


    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $userId
     * @param $status
     * @param $reason
     * @return array
     * 拉黑/移除拉黑
     */
    public static function Shield($userId, $status, $reason): array
    {
        // 检查ID是否有效
        if (!$userId) {
            return [false, '无效的参数: user_id'];
        }

        // 检查状态是否不在允许的范围内
        if (!in_array($status, array_keys(byNew::UserTryModel()::STATUS))) {
            return [false, '无效的参数: status'];
        }

        // 尝试更新用户状态
        list($success, $message) = byNew::UserTryModel()->UpdateUser($userId, ['status' => $status, 'utime' => time(), 'reason' => $reason]);
        if (!$success) {
            return [false, $message];
        }

        return [true, '操作成功'];
    }

    /**
     * @param $input
     * @param $page
     * @param $pageSize
     * @return array
     * 获取用户列表
     */
    public static function GetTryUserList($input, $page, $pageSize): array
    {
        try {
            // 从模型获取用户列表及分页信息
            list($total, $pages, $list) = byNew::UserTryModel()->getList($input, $page, $pageSize);

            // 如果列表不为空，为每个用户获取其所有订单
            if (!empty($list)) {
                foreach ($list as &$value) {
                    // 获取特定用户的所有订单
                    $orderList = byNew::UserOrderTry()->GetUserAllOrder($value['user_id']);

                    // 将订单数据添加到用户信息中， 只取第一个订单数据
                    $order          = $orderList[0] ?? [];
                    $value['order'] = $order;

                    // 参与次数
                    $value['try_number'] = count($orderList);

                    // 试用后是否购买 0未购买 1已购买
                    $buyInfo = byNew::UserOrderTryConversion()->GetOneInfo([
                        CUtil::buildCondition('user_id', '=', $value['user_id']),
                        CUtil::buildCondition('order_no', '=', $order['order_no'] ?? '')
                    ]);

                    $value['is_buy'] = empty($buyInfo) ? 0 : 1;
                }
                // 释放引用，防止后续引用造成的潜在问题
                unset($value);
            }

            // 返回总数，分页信息，和用户列表
            return ['total' => $total, 'pages' => $pages, 'list' => $list];
        } catch (\Exception $e) {
            // 日志记录异常
            $error = $e->getMessage() . " | " . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.try_user');
            // 返回错误信息或空结果集
            return ['total' => 0, 'pages' => 0, 'list' => []];
        }
    }



    /**
     * @param $id
     * @return array
     * 获取用户详情
     */
    public static function GetTryUserInfo($id): array
    {
        if (empty($id)) {
            return [false, '无效的参数: id'];
        }

        try {
            // 获取用户详情
            $userDetail = byNew::UserTryModel()->getUserDetail($id);
            if (!$userDetail) {
                return [false, '用户不存在']; // 确保用户存在
            }

            // 获取用户的所有订单
            $orderList = byNew::UserOrderTry()->GetUserAllOrder($userDetail['user_id']);
            if (!empty($orderList)) {
                // 遍历订单列表并更新金额
                foreach ($orderList as $key => $order) {
                    $orderList[$key]['amount'] = CUtil::totalFee($order['amount'], 1);
                }
            }

            // 将订单信息添加到用户详情中
            $userDetail['order'] = $orderList;

            // 新机绑定数据
            $userDetail['new_machine'] = byNew::UserOrderTryConversion()->GetList([
                CUtil::buildCondition('user_id', '=', $userDetail['user_id']),
            ]);
            if($userDetail['new_machine']){
                $models = array_column($userDetail['new_machine'], 'model');
                $products = byNew::IotProductModel()->GetList([CUtil::buildCondition('model', 'IN', $models)]);
                $productNames = array_column($products,'name','model');
                $tagMap = by::Gtag()->GetTagNameMap();
                foreach ($userDetail['new_machine'] as &$item){
                    $item['label_name'] = $tagMap[$item['label']] ?? '';
                    // 机器名称
                    $item['model_name'] = $productNames[$item['model']] ?? '';
                }
            }

            return [true, $userDetail];
        } catch (\Exception $e) {
            // 日志记录异常
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.try_user');
            return [false, '获取失败'];
        }
    }


    /**
     * @param $userId
     * @return bool
     * 保存用户链路
     */
    public function SveUserPath($userId): bool
    {
        try {
            // 1.查询当前生效活动ID
            $currentTime = time();
            $activityId  = byNew::ActivityModel()::find()
                ->select(['id'])
                ->where(['<', 'start_time', $currentTime])
                ->andWhere(['>', 'end_time', $currentTime])
                ->andWhere([
                    'grant_type' => 1,
                    'is_delete'  => 0,
                    'status'     => 0
                ])
                ->scalar();

            if (empty($activityId)){
                return true;
            }

            // 2.检查根据ac_id和user_id是否存在记录
            if (byNew::UserTryPathModel()->getUserPathInfo(['user_id' => $userId, 'ac_id' => $activityId])) {
                return true;
            }

            // 3.获取用户信息
            $userInfo = $this->getUserInfo($userId);

            // 4.插入数据
            $save = [
                'uid'                                    => $userInfo['uid'],
                'user_id'                                => $userId,
                'ac_id'                                  => $activityId,
                'phone'                                  => $userInfo['phone'],
                'nick_name'                              => $userInfo['nickName'],
                'avatar'                                 => $userInfo['avatar'],
                'source'                                 => $userInfo['source'],
                byNew::UserTryPathModel()::SCENE['auth'] => 1, //是否授权
                'ctime'                                  => $currentTime,
                'utime'                                  => $currentTime
            ];
            list($status, $ret) = byNew::UserTryPathModel()->saveUserPath($save);
            if (!$status) {
                throw new \Exception($ret);
            }
            return true;
        } catch (\Exception $e) {
            // 处理异常
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.try-users-path');
            return false;
        }
    }

    /**
     * @param $userId
     * @return array
     * @throws RedisException
     * @throws Exception
     * 获取用户相关数据
     */
    private function getUserInfo($userId): array
    {
        // 1.根据user_id查询手机号、uid
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        $uid      = $mallInfo['uid'] ?? '';
        $phone    = $mallInfo['phone'] ?? '';

        // 2.根据user_id查询unionId
        $user    = by::users()->getRealMainUserById($userId);
        $unionId = $user['unionid'] ?? '';

        // 3.根据unionId查询昵称头像+来源
        $oaInfo    = by::WeFocus()->getOaInfoByUnionId($unionId);
        $resources = json_decode($oaInfo['resources'] ?? '', true);
        $nickName  = $resources['external_contact']['name'] ?? '';
        $avatar    = $resources['external_contact']['avatar'] ?? '';
        $source    = $oaInfo['source'] ?? '';

        return [
            'uid'      => $uid,
            'phone'    => $phone,
            'nickName' => $nickName,
            'avatar'   => $avatar,
            'source'   => $source
        ];
    }



    public function GetUserPathList($input=[], $page=1, $pageSize=20): array
    {
        list($total, $pages, $list)  = byNew::UserTryPathModel()->getUserPathList($input, $page, $pageSize);
        return ['total' => $total, 'pages' => $pages, 'list' => $list];
    }



}