<?php
namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use Exception;
use yii\db\ActiveRecord;

class ActivityAtmosphereService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function list($from): array
    {
        $query = byNew::ActivityAtmosphereModel()::find();
        $query->with('goods');
        if ($from->gid) {
            $query->leftJoin(byNew::ActivityAtmosphereGoodsModel()::tableName(), 'activity_atmosphere_goods.activity_id = activity_atmosphere.id');
            $query->andWhere(['gid' => $from->gid]);
        }

        $query->andFilterWhere(['like', 'name', $from->name]);
        $query->andFilterWhere(['pos' => $from->pos]);

        // 包含在时间范围内的所有
        $startTime = $from->start_time;
        $endTime   = $from->end_time;
        if ($startTime && $endTime) {
            $query->andFilterWhere(['or',
                                    ['and', ['<=', 'start_time', $startTime], ['>=', 'end_time', $startTime]],
                                    ['and', ['<=', 'start_time', $endTime], ['>=', 'end_time', $endTime]],
                                    ['and', ['>=', 'start_time', $startTime], ['<=', 'end_time', $endTime]],
                                    ['and', ['<=', 'start_time', $startTime], ['>=', 'end_time', $endTime]],
            ]);
        }

        $query->andFilterWhere(['activity_atmosphere.is_del' => 0]);
        $query->orderBy('activity_atmosphere.id desc');

        return CUtil::Pg($query,function ($data) {
            $data['goods'] = array_map(function ($item) {
                $item['name'] = by::Gmain()->GetOneByGid($item['gid'])['name'] ?? '--';
                return $item;
            }, $data['goods']);
            return $data;
        });
    }

    public function detail($from)
    {
        $query = byNew::ActivityAtmosphereModel()::find();
        $query->with('goods');
        $query->andFilterWhere(['id' => $from->id]);
        $data = $query->asArray()->one();

        $data['goods'] = array_map(function ($item) {
            $item['name'] = by::Gmain()->GetOneByGid($item['gid'])['name'] ?? '--';
            return $item;
        }, $data['goods']);

        return $data;
    }

    public function store($from): array
    {
        $transaction = by::dbMaster()->beginTransaction();
        try {
            $activity   = CUtil::mSave(byNew::ActivityAtmosphereModel(), $from->toArray());
            $activityId = $activity['data']['id'];
            // 清除老的缓存
            byNew::ActivityAtmosphereModel()->delCache($activityId);

            $goodsData = array_map(function ($item) use ($activityId) {
                return ['activity_id' => $activityId, 'gid' => $item];
            }, $from->goods);

            $activityGoods = CUtil::mSaveMultiple(byNew::ActivityAtmosphereGoodsModel(), $goodsData, ['activity_id' => $activityId]);

            if (!$activity['status'] || !$activityGoods['status']) {
                throw new Exception('保存失败');
            }
            $transaction->commit();
            // 清除新的缓存
            byNew::ActivityAtmosphereModel()->delCache($activityId);
        } catch (Exception $e) {
            $transaction->rollBack();
            return ['status' => false, 'msg' => $e->getMessage()];
        }

        return array_merge($activity['data'], ['goods' => $activityGoods['data']]);
    }

    public function delete($id): array
    {
        byNew::ActivityAtmosphereModel()->delCache($id);
        CUtil::mDelete(byNew::ActivityAtmosphereModel(), $id);
        byNew::ActivityAtmosphereGoodsModel()::updateAll(['is_del' => 1, 'dtime' => time()], ['activity_id' => $id]);
        return ['status' => true];
    }

    /**
     * 获取商品图片
     * @param $gid
     * @param $pos
     * @return mixed|string
     */
    public function getUrlByGid($gid,$pos)
    {
        $goods = byNew::ActivityAtmosphereModel()->getByGid($gid,$pos);
        return $goods['img'] ?? '';
    }


}