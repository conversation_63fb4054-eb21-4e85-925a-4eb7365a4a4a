<?php

namespace app\modules\back\services;

use app\models\by;

/**
 * 用户服务
 */
class UserService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 设置绑定状态
     * @param array $items
     * @return array
     */
    public function setBindStatus(array $items): array
    {
        // 获取用户id
        $user_ids = array_column($items, 'user_id');
        $uids = by::Phone()->getUidByUserIds($user_ids);
        // 获取绑定状态
        $bindStatus = UserBindService::getInstance()->getCurrentBindStatus($uids);
        foreach ($items as &$item) {
            $item['bind_status'] = $bindStatus[$uids[$item['user_id']] ?? ''] ?? 0;
        }
        return $items;
    }
}