<?php

namespace app\modules\back\services;

use app\exceptions\ExportFileException;
use app\jobs\AsyncExportJob;
use app\models\by;

class AsyncExportService
{
    /**
     * 操作类型
     */
    const ACT_TYPE = [
        '1002' => [
            'module'    => 'asset',
            'class'     => 'PointDetailModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => 2022, 'user_id' => 0, 'phone' => '', 'type' => -1, 's_time' => 0, 'e_time' => 0, 'viewSensitive' => false
            ],
            'filename'  => '积分记录',
            'path_info' => 'back/export-1002'
        ], //积分记录
        '1003' => [
            'module'    => MAIN_MODULE,
            'class'     => 'ProductDetailModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => 2022, 'user_id' => 0, 'phone' => '', 'sn' => '', 's_time' => 0, 'e_time' => 0, 'viewSensitive' => false
            ],
            'filename'  => '产品注册记录',
            'path_info' => 'back/export-1003'
        ], //产品注册记录
        '1004' => [
            'module'    => MAIN_MODULE,
            'class'     => 'UserExtendModel',
            'fun'       => 'exportData',
            'params'    => [
                'user_id' => 0, 'phone' => 0, 'source' => 0, 's_time' => 0, 'e_time' => 0, 'vip_s_time' => 0, 'vip_e_time' => 0, 'is_export_point' => 0, 'viewSensitive' => false
            ],
            'filename'  => '用户列表',
            'path_info' => 'back/export-1004'
        ], //用户列表
        '1005' => [
            'module'    => 'goods',
            'class'     => 'OmainModel',
            'fun'       => 'batchExportData',
            'params'    => [
                    'year' => '', 'order_no' => '', 'user_iden' => '', 'status' => -1, 'source' => -1, 'order_st' => 0, 'order_ed' => 0, 'p_sources' => '-1', 'viewSensitive' => false, 'store' => '', 'type' => '', 'order_tid' => ''
            ],
            'filename'  => '订单列表',
            'path_info' => 'back/export-1005'
        ], //订单管理->订单列表->订单导出
        '1006' => [
            'module'    => 'goods',
            'class'     => 'OmainModel',
            'fun'       => 'batchExportGoodsData',
            'params'    => [
                    'year' => '', 'order_no' => '', 'user_iden' => '', 'status' => -1, 'source' => -1, 'order_st' => 0, 'order_ed' => 0, 'viewSensitive' => false, 'type' => '', 'order_tid' => ''
            ],
            'filename'  => '订单商品',
            'path_info' => 'back/export-1006'
        ], //订单管理->订单列表->订单商品导出
        '1007' => [
            'module'    => 'goods',
            'class'     => 'GmainModel',
            'fun'       => 'exportData',
            'params'    => [
                'version' => '', 'type' => -1, 'status' => -1, 'name' => '', 'sku' => '', 'tid' => -1, 'viewSensitive' => false
            ],
            'filename'  => '商品列表',
            'path_info' => 'back/export-1007'
        ], //商品管理->商品列表->导出
        '1008' => [
            'module'    => 'goods',
            'class'     => 'OsourceModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => '', 'order_no' => '', 'order_st' => 0, 'order_ed' => 0, 'user_iden' => '','job_no'=>'', 'viewSensitive' => false
            ],
            'filename'  => '导购记录',
            'path_info' => 'back/export-1008'
        ], //导购管理->导购记录->导出
        '1028' => [
            'module'    => 'goods',
            'class'     => 'OsourceModel',
            'fun'       => 'exportGoodsData',
            'params'    => [
                'year' => '', 'order_no' => '', 'order_st' => 0, 'order_ed' => 0, 'user_iden' => '','job_no'=>'','viewSensitive'=> false
            ],
            'filename'  => '导购订单商品记录',
            'path_info' => 'back/export-1028'
        ], //导购管理->导购订单商品记录->导出
        '1009' => [
            'module'    => 'main',
            'class'     => 'GuideModel',
            'fun'       => 'exportData',
            'params'    => [
                'user_id' => 0, 'name' => '', 'status' => 0, 'viewSensitive' => false
            ],
            'filename'  => '导购列表',
            'path_info' => 'back/export-1009'
        ], //导购管理->导购列表->导出
        '1010' => [
            'module'    => 'goods',
            'class'     => 'OrefundMainModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_iden' => '', 'status' => -1, 'order_st' => 0, 'order_ed' => 0, 'viewSensitive' => false
            ],
            'filename'  => '退款订单列表',
            'path_info' => 'back/export-1010'
        ], //订单管理->退款订单列表->退款订单导出
        '1011' => [
            'module'    => 'goods',
            'class'     => 'OrefundMainModel',
            'fun'       => 'exportGoodsData',
            'params'    => [
                'order_no' => '', 'user_iden' => '', 'status' => -1, 'order_st' => 0, 'order_ed' => 0, 'viewSensitive' => false
            ],
            'filename'  => '退款订单商品',
            'path_info' => 'back/export-1011'
        ], //内容管理->内容列表->导出
        '1012' => [
            'module'    => 'wiki',
            'class'     => 'WdynamicMainModel',
            'fun'       => 'ExportData',
            'params'    => [
                'status' => -1, 'type' => -1, 't1' => -1, 't2' => -1, 'title' => '', 'viewSensitive' => false
            ],
            'filename'  => '内容列表',
            'path_info' => 'back/export-1012'
        ], //订单管理->退款订单列表->退款商品导出
        '1013' => [
            'module'    => 'main',
            'class'     => 'MarketConfigModel',
            'fun'       => 'exportData',
            'params'    => [
                'name' => '', 'status' => -1, 'type' => -1, 'is_delete' => 0, 'viewSensitive' => false
            ],
            'filename'  => '营销列表',
            'path_info' => 'back/export-1013'
        ], //营销配置-营销资源-列表导出
        '1014' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingOrderModel',
            'fun'       => 'exploreExportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'status' => -1, 's_time' => 0, 'e_time' => 0, 'refund_status' => '', 'is_export' => -1, 'phones' => '', 'id' => 0, 'viewSensitive' => false
            ],
            'filename'  => '勘测工单列表',
            'path_info' => 'back/export-1014'
        ], //预约管理-勘测订单-列表导出
        '1015' => [
            'module'    => 'plumbing',
            'class'     => 'ExploreSearchModel',
            'fun'       => 'exportData',
            'params'    => [
                'cname' => '', 'viewSensitive' => false
            ],
            'filename'  => '查询记录列表',
            'path_info' => 'back/export-1015'
        ], //预约管理-查询记录-列表导出
        '1016' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingOrderModel',
            'fun'       => 'installExportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'status' => -1, 's_time' => 0, 'e_time' => 0, 'is_export' => -1, 'phones' => '', 'id' => 0, 'viewSensitive' => false
            ],
            'filename'  => '安装工单列表',
            'path_info' => 'back/export-1016'
        ],//预约管理-安装订单-列表导出
        '1017' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingRefundModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'type' => 0, 'status' => 0, 's_time' => 0, 'e_time' => 0, 'viewSensitive' => false
            ],
            'filename'  => '退款工单列表',
            'path_info' => 'back/export-1017'
        ],//预约管理-退款订单-列表导出
        '1018' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingCommentModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'type' => 0, 'viewSensitive' => false
            ],
            'filename'  => '评论中心列表',
            'path_info' => 'back/export-1018'
        ],//预约管理-评论中心-列表导出
        '1019' => [
            'module'    => 'rbac',
            'class'     => 'SystemLogsModel',
            'fun'       => 'exportData',
            'params'    => [
                'admin_id' => '', 'start_time' => 0, 'end_time' => 0, 'module_id' => 0, 'year' => 2022, 'viewSensitive' => false
            ],
            'filename'  => '日志列表',
            'path_info' => 'back/export-1019'
        ],//系统设置-系统日志-列表导出
        '1020' => [
            'module'    => 'wares',
            'class'     => 'GiftCardModel',
            'fun'       => 'exportData',
            'params'    => [
                'name' => '', 'type' => '', 'status' => '','viewSensitive' => false
            ],
            'filename'  => '礼品卡管理列表',
            'path_info' => 'back/export-1020'
        ],//营销配置-礼品卡管理-列表导出
        '1021' => [
            'module'    => 'wares',
            'class'     => 'GiftCardResourcesModel',
            'fun'       => 'exportData',
            'params' => [
                'begin_time' => '', 'end_time' => '', 'card_no' => '', 'card_goods_id' => '', 'act_user_id' => '', 'status' => '', 'viewSensitive' => false
            ],
            'filename'  => '礼品卡资源列表',
            'path_info' => 'back/export-1021'
        ],//营销配置-礼品卡管理-卡商品列表导出
        '1022' => [
            'module'    => 'wares',
            'class'     => 'GiftCardGoodsModel',
            'fun'       => 'exportData',
            'params'    => [
                'name' => '', 'sku' => '', 'status' => '', 'type' => '', 'viewSensitive' => false
            ],
            'filename'  => '礼品卡商品列表',
            'path_info' => 'back/export-1022'
        ],
        '1023' => [
            'module'    => 'wares',
            'class'     => 'GiftCardResourcesModel',
            'fun'       => 'exportGenRecordData',
            'params'    => [
                'card_no' => '', 'card_goods_id' => 0, 'status' => 0, 'sync_status' => 0, 'begin_create_time' => 0, 'end_create_time' => 0, 'viewSensitive' => false
            ],
            'filename'  => '卡生成记录',
            'path_info' => 'back/export-1023'
        ],//营销配置-生成卡信息-列表导出
        '1030' => [
            'module' => 'back',
            'class'  => 'TryOrdersService',
            'fun'    => 'exportData',
            'params' => [
                'year'       => '',
                'status'     => '',
                'user_iden'  => '',
                'order_no'   => '',
                'p_sources'  => '',
                'goods_name' => '',
                'sku'        => '',
                'order_st'   => '',
                'order_ed'   => '',
                'viewSensitive' => false,
            ],
            'filename'  => '先用后付订单导出',
            'path_info' => 'back/export-1030'
        ],
        '1031' => [
            'module' => 'back',
            'class'  => 'TryOrdersService',
            'fun'    => 'exportRefundData',
            'params' => [
                'order_no' => '', 'user_iden' => '', 'status' => -1, 'order_st' => 0, 'order_ed' => 0, 'viewSensitive' => false
            ],
            'filename'  => '先用后付退款订单导出',
            'path_info' => 'back/export-1031'
        ],
        '1032' => [
            'module'    => 'wares',
            'class'     => 'SurveyRecordModel',
            'fun'       => 'exportData',
            'params'    => [
                'uid' => '', 'phone' => '', 'ac_id' => '', 'name' => '', 'label' => '', 'goods_name' => '', 'start_time' => '', 'end_time' => '', 'viewSensitive' => false
            ],
            'filename'  => '问卷审核列表',
            'path_info' => 'back/export-1032'
        ],
        '1033' => [
            'module'    => 'main',
            'class'     => 'UserTryModel',
            'fun'       => 'exportData',
            'params'    => [
                'uid' => '', 'phone' => '', 'goods_name' => '', 'label' => '', 'status' => '', 'viewSensitive' => false
            ],
            'filename'  => '先用后付用户列表',
            'path_info' => 'back/export-1033'
        ],
        '1034' => [
            'module' => 'back',
            'class'  => 'TryOrdersService',
            'fun'    => 'exportExceptionData',
            'params' => [
                'year'          => '',
                'order_no'      => '',
                'refund_no'     => '',
                'goods_name'    => '',
                'status'        => -1,
                'try_status'    => -1,
                'label'         => -1,
                'viewSensitive' => false
            ],
            'filename'  => '先用后付异常订单导出',
            'path_info' => 'back/export-1034'
        ],
        '1035' => [
            'module'    => 'main',
            'class'     => 'UserTryPathModel',
            'fun'       => 'exportData',
            'params'    => [
                'uid' => '', 'phone' => '', 'ac_id' => '', 'nick_name' => '', 'viewSensitive' => false
            ],
            'filename'  => '用户链路导出列表',
            'path_info' => 'back/export-1035'
        ],
        '1036' => [
            'module' => 'back',
            'class'  => 'PointPushService',
            'fun'    => 'exportPointPushData',
            'params' => [
                'phone'              => '',
                'uid'                => '',
                'score'              => '',
                'model'              => '',
                'type'               => '',
                'source'             => '',
                'event'              => '',
                'excel_no'           => '',
                'status'             => 0,
                'release_start_time' => 0,
                'release_end_time'   => 0,
                'admin_user'         => '',
                'start_time'         => 0,
                'end_time'           => 0,
                'viewSensitive'      => false
            ],
            'filename'  => '积分或觅享分发放导出',
            'path_info' => 'back/export-1036'
        ],
        '1037' => [
            'module' => 'back',
            'class'  => 'UserEmployeeService',
            'fun'    => 'exportData',
            'params' => [
                'uid'              => '',
                'phone'            => '',
                'name'             => '',
                'employee_no'      => '',
                'is_blacklist'     => 0,
                'employee_status'  => 0,
                'viewSensitive'      => false
            ],
            'filename'  => '员工列表',
            'path_info' => 'back/export-1037'
        ],
        '1038' => [
            'module' => 'log',
            'class'  => 'WarrantyApplyDetailModel',
            'fun'    => 'exportData',
            'params' => [
                'phone'              => '',
                'year'               => '',
                'sn'                 => '',
                'apply_status'       => -1,
                'product_name'       => '',
                'tid'                => '',
                'apply_no'           => '',
                'is_support_care'    => 0,
                'is_automatic'       => 0,
                'viewSensitive'      => false
            ],
            'filename'  => '保修卡待审核记录导出',
            'path_info' => 'back/export-1038'
        ],
        '1040' => [
            'module'    => 'log',
            'class'     => 'RegisterExceptionModel',
            'fun'       => 'exportData',
            'params' => [
                'user_id' => '', 'tid' => '', 'sn' => '', 'phone' => '', 'nick' => '', 'buy_start_time' => 0, 'buy_end_time' => 0, 'register_start_time' => 0, 'register_end_time' => 0, 'fail_reason' => '', 'viewSensitive' => false,
            ],
            'filename'  => '产品注册异常列表',
            'path_info' => 'back/export-1040'
        ],
        '1041'=>[
            'module'    => 'back',
            'class'     => 'GroupPurchaseService',
            'fun'       => 'exportGroupRecord',
            'params' => [
                'activity_id' => '', 'start_time' => '', 'end_time' => '', 'uid' => '', 'nick_name' => '', 'gid' => '', 'viewSensitive' => false
            ],
            'filename'  => '拼团记录导出',
            'path_info' => 'back/export-1041'
        ]
    ];

    /**
     * 获取异步导出任务列表
     * @param array $params
     * @return array
     */
    public function getAsyncExportLogList(array $params): array
    {
        // 分页数据
        $params['page'] = $params['page'] ?? 1;
        $params['page_size'] = $params['page_size'] ?? 20;
        $res = by::AsyncExportLog()->getExportLogList($params);

        // 处理数据
        $items = $res['items'];
        // 管理员信息
        $adminUserIds = array_column($items, 'admin_user_id');
        $adminUsers   = by::adminUserModel()->getListByIds($adminUserIds, ['id', 'account']);
        $adminUsers   = array_column($adminUsers, 'account', 'id');
        foreach ($items as &$item) {
            // 管理员
            $item['admin_user_name'] = $adminUsers[$item['admin_user_id']] ?? '';
            // 导出时间
            $item['export_time'] = date('Y-m-d H:i:s', $item['export_time']);
            // 状态名称
            $item['status_name'] = by::AsyncExportLog()::STATUS_NAME[$item['status']] ?? '';
        }

        // 返回结果
        return ['items' => $items, 'total' => $res['total'], 'page_size' => $params['page_size']];
    }

    /**
     * 导出操作
     * @param int $user_id
     * @param $act_type
     * @param $params
     * @param $is_sensitive
     * @throws ExportFileException
     * @throws \yii\db\Exception
     */
    public function export(int $user_id, $act_type, $params, $is_sensitive)
    {
        // 验证操作
        if (!isset(self::ACT_TYPE[$act_type])) {
            throw new ExportFileException("操作类型不存在");
        }

        // 导出行为
        $act = self::ACT_TYPE[$act_type];

        // 请求参数
        $param_values = [];
        $param_attributes = $act['params'];
        foreach ($param_attributes as $attribute => $default) {
            $param_values[$attribute] = $params[$attribute] ?? $default;
        }

        // 数据脱敏
        $param_values['viewSensitive'] = $is_sensitive;

        // 异步处理
        $filename = sprintf('%s-%s-%d.xlsx', $act['filename'], date('ymd'), mt_rand(1000, 9999));
        $time = time();
        $data = [
            'name'          => $filename,
            'export_time'   => $time,
            'admin_user_id' => $user_id,
            'status'        => by::AsyncExportLog()::STATUS['DOING'], // 进行中
            'ctime'         => $time,
            'utime'         => $time,
        ];

        // 添加导出记录
        $id = by::AsyncExportLog()->saveLog(0, $data);

        // 插入队列
        $action = [
            '_class'  => $act['class'],
            '_module' => $act['module'],
            '_fun'    => $act['fun'],
            '_params' => $param_values,
        ];
        \Yii::$app->queue->push(new AsyncExportJob([
            'id'       => $id,
            'filename' => $filename,
            'action'   => $action
        ]));
    }
}
