<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\models\by;
use app\models\CUtil;
use yii\helpers\Json;

/**
 * 推荐有礼
 */
class RecommendActivity extends Activity
{
    // 分享海报数量上限
    const SHARE_IMAGE_LIMIT = 3;

    // 奖励类型
    const REWARD_TYPE = [
        'coupon' => 1,  //优惠券
        'point'  => 2,  //积分
    ];

    /**
     * 验证
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     * @throws \yii\db\Exception
     */
    protected function validate(array $data)
    {
        // 推荐有礼活动，只能添加1条
        $id = CUtil::uint($data['id']);
        if ($id) { // 编辑活动，判断是否存在此活动
            $res = by::activityConfigModel()->getActivityByType($data['grant_type'], $id);
            if (!$res) {
                throw new ActivityException('活动不存在，无法编辑');
            }
        } else { // 添加活动，判断是否已存在活动
            $res = by::activityConfigModel()->getActivityByType($data['grant_type']);
            if ($res) {
                throw new ActivityException('活动已存在，无法添加');
            }
        }

        // 页面海报
        if (empty($data['poster_image'])) {
            throw new ActivityException('页面海报不能为空');
        }

        // 分享海报
        if (empty($data['share_image'])) {
            throw new ActivityException('分享海报不能为空');
        }

        // 分享海报最多3张
        if (substr_count($data['share_image'], "|") > self::SHARE_IMAGE_LIMIT) {
            throw new ActivityException(sprintf('分享海报数量最多%d张', self::SHARE_IMAGE_LIMIT));
        }

        // 注册奖励
        $is_reg_reward = isset($data['is_reg_reward']) ? CUtil::uint($data['is_reg_reward']) : 0;
        if ($is_reg_reward == 0) {
            throw new ActivityException('邀请新用户注册，是否奖励积分配置不正确');
        }

        // 注册奖励
        $inviter_reg_limit = isset($data['inviter_reg_limit']) ? CUtil::uint($data['inviter_reg_limit']) : 0;
        if ($inviter_reg_limit == 0) {
            throw new ActivityException('邀请新用户注册，奖励积分次数配置不正确');
        }

        // 下单金额
        $order_min_money = by::Gtype0()->totalFee($data['order_min_money'] ?? 0); //分
        if ($order_min_money == 0) {
            throw new ActivityException('下单金额配置不正确');
        }

        // 绑定成功
        $is_bind_reward = isset($data['is_bind_reward']) ? CUtil::uint($data['is_bind_reward']) : 0;
        if ($is_bind_reward == 0) {
            throw new ActivityException('被邀请人绑定是否奖励积分配置不正确');
        }

        // 绑定成功
        $invitee_bind_limit = isset($data['invitee_bind_limit']) ? CUtil::uint($data['invitee_bind_limit']) : 0;
        if ($invitee_bind_limit == 0) {
            throw new ActivityException('被邀请人绑定奖励次数配置不正确');
        }

        // 奖励类型
        $reward_type = isset($data['reward_type']) ? CUtil::uint($data['reward_type']) : 0;
        if (!in_array($reward_type, self::REWARD_TYPE)) {
            throw new ActivityException('下单奖励类型不合法');
        }

        // 规则说明
        if (empty($data['rule_note'])) {
            throw new ActivityException('使用规则不能为空');
        }

        // 优惠券配置
        if ($reward_type == self::REWARD_TYPE['coupon']) {
            if (empty($data['market_config'])) {
                throw new ActivityException('优惠券配置不正确');
            }

            $market_config = (array)json_decode($data['market_config'], true);
            if (count($market_config) > by::Am()::MAX_COUNT) {
                throw new ActivityException('最多配置三张优惠券');
            }
        }
    }

    /**
     * 添加/编辑生日活动
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     */
    protected function addOrUpdate(array $data)
    {
        // 开启事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 1、添加/更新活动配置表
            $id = CUtil::uint($data['id'] ?? 0);
            $item1 = [
                'name'        => $data['name'],
                'grant_type'  => $data['grant_type'],
                'resource'    => json_encode($data['resource'] ?? '', 256),
                'help_order'  => $data['help_order'] ?? '',
                'update_time' => time()
            ];
            if (!$id) { // 新增数据时才有创建时间
                $item1['create_time'] = time();
            }
            $acId = by::activityConfigModel()->saveLog($id, $item1);

            // 2、添加/更新通用礼包表
            $order_min_money = by::Gtype0()->totalFee($data['order_min_money'] ?? 0);
            $item2 = [
                'ac_id'              => $acId,
                'poster_image'       => $data['poster_image'],
                'share_image'        => $data['share_image'],
                'is_reg_reward'      => $data['is_reg_reward'],
                'inviter_reg_limit'  => $data['inviter_reg_limit'],
                'order_min_money'    => $order_min_money,
                'is_bind_reward'     => $data['is_bind_reward'],
                'invitee_bind_limit' => $data['invitee_bind_limit'],
                'reward_type'        => $data['reward_type'],
                'rule_note'          => $data['rule_note'],
                'update_time'        => time(),
            ];
            by::acType2()->saveLog($id, $item2);


            // 3、保存优惠券
            if ($data['reward_type'] == self::REWARD_TYPE['point']) { // 3.1、奖励类型为积分，则删除所有优惠券
                list($status, $amIds) = by::aM()->getIdsByAid($acId);
                foreach ($amIds as $amId) {
                    by::aM()->del($amId);
                }
            } else { // 3.2、奖励类型为优惠券，1、有id值的数据修改、2、无id值的数据添加、3、表里已存在，但本次编辑时没有的数据删除
                list($status, $amIds) = by::aM()->getIdsByAid($acId);
                if (!$status) {
                    $amIds = [];
                }
                $editAmIds = []; // 被编辑的优惠券的id集合
                $markets = Json::decode($data['market_config']);
                foreach ($markets as $market) {
                    // 添加、编辑参数
                    $ac_id = $acId;
                    $mc_id = $market['mc_id'];
                    $stock = $market['stock'] ?? 0;

                    // 添加优惠券
                    if (empty($market['id'])) {
                        list($status, $res) = by::aM()->add($ac_id, $mc_id, $stock);
                        if (!$status) { // 添加失败
                            throw new ActivityException($res);
                        }
                        continue;
                    }

                    // 编辑优惠券
                    if (in_array($market['id'], $amIds)) {
                        list($status, $res) = by::aM()->edit($market['id'], $ac_id, $mc_id, $stock);
                        if (!$status) { // 编辑失败
                            throw new ActivityException($res);
                        }
                        $editAmIds[] = $market['id'];
                    }
                }

                // 删除优惠券
                foreach (array_diff($amIds, $editAmIds) as $id) {
                    by::aM()->del($id);
                }
            }
            $transaction->commit();
            // 删除缓存
            by::activityConfigModel()->delCache($acId, $data['grant_type']);
        } catch (ActivityException $e) {
            $transaction->rollBack();
            throw new ActivityException($e->getMessage());
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 记录日志
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            throw new ActivityException('添加/编辑推荐有礼失败');
        }
    }
}
