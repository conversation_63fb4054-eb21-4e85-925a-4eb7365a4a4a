<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\jobs\AutoCouponActivityJob;
use app\models\by;
use app\models\CUtil;
use Exception;
use Throwable;
use Yii;
use yii\helpers\Json;

/**
 * 商品详情页领券
 */
class AutoCouponActivity extends Activity
{
    /**
     * 验证
     * @param array $data
     * @return void
     * @throws ActivityException
     * @throws \yii\db\Exception
     */
    protected function validate(array $data)
    {
        // 时间不能为空、开始时间小于结束时间
        $start_time = $data['start_time'] ?? 0;
        $end_time   = $data['end_time'] ?? 0;
        if (empty($start_time)) {
            throw new ActivityException("开始时间不能为空");
        }

        if (empty($end_time)) {
            throw new ActivityException("结束时间不能为空");
        }

        if ($start_time > $end_time) {
            throw new ActivityException("开始时间不能大于结束时间");
        }

        // 校验名称
        if (empty($data['market_config'])) {
            throw new ActivityException('优惠券为空');
        }

        //  发放时间校验
        $attributes = Json::decode($data['attributes']);
        if (empty($data['attributes']) && json_last_error() == JSON_ERROR_NONE) {
            throw new ActivityException('发放时间不能为空');
        }

        if (!isset($attributes['grant_time']) || $attributes['grant_time'] < 0) {
            throw new ActivityException('发放时间只能为正整数');
        }

        // 验证优惠券
        $markets = Json::decode($data['market_config']);
        // 优惠券ID
        $mcIds = [];
        foreach ($markets as $market) {
            // 不允许存在相同的优惠券
            if (isset($mcIds[$market['mc_id']])) {
                throw new ActivityException('存在相同的优惠券，无法操作');
            } else {
                $mcIds[$market['mc_id']] = true;
            }
            // 检查库存
            $marketConfigInfo = by::marketConfig()->getOneById($market['mc_id']);
            if ($marketConfigInfo['stock_num'] <= 0) {
                throw new ActivityException(sprintf('%s库存不足，请检查', $marketConfigInfo['name']));
            }
            if ($market['stock'] <= 0 || $market['stock'] > $marketConfigInfo['stock_num']) {
                throw new ActivityException(sprintf('%s库存不合理，请检查', $marketConfigInfo['name']));
            }
        }

        // sku校验
        if (!isset($data['skus']) || !CUtil::isJson($data['skus']) || empty($data['skus'])) {
            throw new ActivityException('商品SKU不能为空');
        }
        $skus = Json::decode($data['skus']);
        if (empty($skus) || !is_array($skus)) {
            throw new ActivityException('商品SKU不能为空');
        }
        $skus = array_column($skus, 'sku');
        // skus重复校验
        if (count($skus) != count(array_unique($skus))) {
            throw new ActivityException('商品SKU重复');
        }
        // 检查sku是否存在
        $items = by::Gmain()->getProductInfoBySku($skus, ['main.sku as main_sku', 'specs.sku as specs_sku']);

        if (count($items) < count($skus)) {
            throw new ActivityException('SKU商品不存在请校验');
        }

    }

    /**
     * 添加/编辑生日活动
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     */
    protected function addOrUpdate(array $data)
    {
        // 开启事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 1、添加/更新活动配置表
            $id    = CUtil::uint($data['id'] ?? 0);
            $item1 = [
                    'name'        => $data['name'],
                    'grant_type'  => $data['grant_type'],
                    'start_time'  => $data['start_time'],
                    'end_time'    => $data['end_time'],
                    'resource'    => json_encode($data['resource'] ?? '', 256),
                    'attributes'  => $data['attributes'],
                    'help_order'  => $data['help_order'] ?? '',
                    'update_time' => time()
            ];
            if (!$id) { // 新增数据时才有创建时间
                $item1['create_time'] = time();
            }
            // 保存活动配置
            $acId = by::activityConfigModel()->saveLog($id, $item1);

            // 2、添加/更新通用礼包表
            $skus = Json::decode($data['skus']);
            by::acType7()->saveLog($acId, $skus, $data['start_time'], $data['end_time']);

            // 3、保存优惠券
            list($status, $amIds) = by::aM()->getIdsByAid($acId);
            if (!$status) {
                $amIds = [];
            }
            $editAmIds = []; // 被编辑的优惠券的id集合
            $markets   = Json::decode($data['market_config']);
            foreach ($markets as $market) {
                // 添加、编辑参数
                $ac_id = $acId;
                $mc_id = $market['mc_id'];
                $stock = $market['stock'] ?? 0;

                // 添加优惠券
                if (empty($market['id'])) {
                    list($status, $res) = by::aM()->add($ac_id, $mc_id, $stock);
                    if (!$status) { // 添加失败
                        throw new ActivityException($res);
                    }
                    continue;
                }

                // 编辑优惠券
                if (in_array($market['id'], $amIds)) {
                    list($status, $res) = by::aM()->edit($market['id'], $ac_id, $mc_id, $stock);
                    if (!$status) { // 编辑失败
                        throw new ActivityException($res);
                    }
                    $editAmIds[] = $market['id'];
                }
            }

            // 删除优惠券
            foreach (array_diff($amIds, $editAmIds) as $id) {
                by::aM()->del($id);
            }
            $transaction->commit();
            // 删除缓存
            by::activityConfigModel()->delCache($acId, $data['grant_type']);
        } catch (ActivityException $e) {
            $transaction->rollBack();
            throw new ActivityException($e->getMessage());
        } catch (Exception $e) {
            $transaction->rollBack();
            // 记录日志
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            throw new ActivityException('添加/编辑自动发放优惠券活动失败');
        }
    }


    /**
     * 发放优惠券
     * @param int $userId 用户ID
     * @param string $orderNo 订单号
     * @param array $skus SKU列表
     * @throws Throwable
     */
    public function send(int $userId, string $orderNo, array $skus = [])
    {
        try {
            $this->validateParams($userId, $orderNo, $skus);

            $couponList = by::acType7()->getAvailableCoupons($skus);
            if (empty($couponList)) {
                return;
            }

            foreach ($couponList as $list) {
                $this->processCouponList($list, $userId, $orderNo);
            }

        } catch (Exception $e) {
            CUtil::debug("AutoCouponActivityJob send error: {$e->getMessage()}",'err.AutoCouponActivityJob');
        }
    }

    /**
     * 验证参数
     * @throws Exception
     */
    private function validateParams($userId, $orderNo, $skus)
    {
        if (empty($userId) || empty($orderNo)) {
            throw new Exception('AutoCouponActivityJob parameters missing');
        }

        if (empty($skus)) {
            throw new Exception('AutoCouponActivityJob sku parameters missing');
        }
    }

    /**
     * 处理优惠券列表
     */
    private function processCouponList(array $list, $userId, $orderNo)
    {
        $coupons = $list['coupons'] ?? [];
        // 同一活动，同一活动 多个sku 只发一次
        $coupons = array_map("unserialize", array_unique(array_map("serialize", $coupons)));

        if (empty($coupons)) {
            return;
        }

        $grantTime = $this->getGrantTime($list['grant_time'] ?? 0);
        $jobData   = [
                'order_no' => $orderNo,
                'user_id'  => $userId,
                'coupons'  => $coupons
        ];

        // 记录日志
        $this->logAndPushJob($grantTime, $orderNo, $userId, $coupons);

        if ($grantTime > 0) {
            Yii::$app->queue->delay($grantTime)->push(new AutoCouponActivityJob($jobData));
        } else {
            Yii::$app->queue->push(new AutoCouponActivityJob($jobData));
        }
    }

    /**
     * 获取发放延迟时间(分钟转秒)
     */
    private function getGrantTime($time): int
    {
        return is_numeric($time) ? $time * 60 : 0;
    }

    /**
     * 记录日志
     */
    private function logAndPushJob($grantTime, $orderNo, $userId, $coupons)
    {
        CUtil::debug(
                "AutoCouponActivityJob send delay: {$grantTime} " .
                "order_no: {$orderNo} " .
                "user_id: {$userId} " .
                "coupons: " . json_encode($coupons),
                'AutoCouponActivityJob'
        );
    }

    /**
     * @param $gids
     * @return array
     * @throws \yii\db\Exception
     * @throws Exception
     */
    public function getSku($gids): array
    {
        $skus = [];
        foreach ($gids as $item) {
            if (empty($item)) {
                throw new Exception('AutoCouponActivityJob sku parameters missing'.json_encode($gids,320));
            }
            $gid = $item['gid'] ?? '';
            $sid = $item['sid'] ?? 0;
            if (empty($gid)) {
                throw new Exception('AutoCouponActivityJob gid sid parameters missing'.json_encode($gids,320));
            }

            if ($sid == 0) {
                $skus = array_merge($skus, by::Gmain()->GetSkusByGid($gid));
            } else {
                $spec = by::Gspecs()->GetOneById($gid, $sid);
                if (empty($spec) || !isset($spec['sku'])) {
                    throw new Exception('AutoCouponActivityJob spec parameters missing'.json_encode($gids,320));
                }
                $skus[] = $spec['sku'];
            }
        }
        return $skus;
    }
}
