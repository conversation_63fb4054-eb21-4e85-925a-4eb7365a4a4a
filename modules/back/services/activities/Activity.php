<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;

/**
 * 活动的抽象类
 */
abstract class Activity
{
    /**
     * 添加/编辑活动
     * @param array $data
     * @return mixed
     */
    public function modify(array $data)
    {
        // 基础验证
        $this->baseValidate($data);

        // 校验数据
        $this->validate($data);

        // 处理业务（添加/编辑活动）
        $this->addOrUpdate($data);
    }


    /**
     * @param array $data
     * @throws ActivityException
     */
    private function baseValidate(array $data)
    {
        // 名称不为空
        if (empty($data['name'])) {
            throw new ActivityException('活动名称为空');
        }
    }

    /**
     * 校验数据
     * @return mixed
     */
    abstract protected function validate(array $data);

    /**
     * 添加/编辑活动
     * @return mixed
     */
    abstract protected function addOrUpdate(array $data);
}