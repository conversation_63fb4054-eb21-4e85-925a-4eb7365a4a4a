<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\modules\main\models\ActivityConfigModel;
use Exception;

/**
 * 活动的工厂类
 */
class ActivityFactory
{
    /**
     * 创建活动
     * @param int $type
     * @return Activity
     * @throws Exception
     */
    public function createActivity(int $type): Activity
    {
        switch ($type) {
            case ActivityConfigModel::GRANT_TYPE['newUser'] :           // 1、新人礼包
                $activity = new NewGiftActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['recommend_gift'] :    // 2、推荐有礼
                $activity = new RecommendActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['common_activity'] :   // 3、通用活动
                $activity = new CommonActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['birthday']:           // 4、生日活动
                $activity = new BirthdayActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['monthly'] :           // 5、每月领券
                $activity = new MonthlyActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['goods_detail'] :      // 6、商品详情
                $activity = new GoodsDetailActivity();
                break;
            case ActivityConfigModel::GRANT_TYPE['auto_coupon'] :   // 7、自动发券
                $activity = new AutoCouponActivity();
                break;
            default :
                throw new ActivityException("活动不存在");       // 活动不存在
        }
        return $activity;
    }
}
