<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\models\by;
use app\models\CUtil;
use yii\helpers\Json;

/**
 * 生日活动
 */
class BirthdayActivity extends Activity
{

    /**
     * 验证
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     * @throws \yii\db\Exception
     */
    protected function validate(array $data)
    {
        // 时间不能为空、开始时间小于结束时间
        $start_time = $data['start_time'] ?? 0;
        $end_time = $data['end_time'] ?? 0;
        if (empty($start_time)) {
            throw new ActivityException("开始时间不能为空");
        }

        if (empty($end_time)) {
            throw new ActivityException("结束时间不能为空");
        }

        if ($start_time > $end_time) {
            throw new ActivityException("开始时间不能大于结束时间");
        }

        // 生日活动，只能添加1条
        $id = $data['id'];
        if ($id) { // 编辑活动，判断是否存在此活动
            $res = by::activityConfigModel()->getActivityByType($data['grant_type'], $id);
            if (!$res) {
                throw new ActivityException('活动不存在，无法编辑');
            }
        } else { // 添加活动，判断是否已存在活动
            $res = by::activityConfigModel()->getActivityByType($data['grant_type']);
            if ($res) {
                throw new ActivityException('活动已存在，无法添加');
            }
        }

        // 优惠券选择
        if (empty($data['market_config'])) {
            throw new ActivityException('优惠券为空');
        }

        // 验证优惠券
        $markets = Json::decode($data['market_config']);
        // 优惠券ID
        $mcIds = [];
        foreach ($markets as $market) {
            // 可领取等级不能为空
            if (empty($market['level'])) {
                throw new ActivityException('可领取等级为空，无法操作');
            }
            // 不允许存在相同的优惠券
            if (isset($mcIds[$market['mc_id']])) {
                throw new ActivityException('存在相同的优惠券，无法操作');
            } else {
                $mcIds[$market['mc_id']] = true;
            }

            // 检查库存
            $marketConfigInfo = by::marketConfig()->getOneById($market['mc_id']);
            if ($marketConfigInfo['stock_num'] <= 0) {
                throw new ActivityException(sprintf('%s库存不足，请检查', $marketConfigInfo['name']));
            }
            if ($market['stock'] <= 0 || $market['stock'] > $marketConfigInfo['stock_num']) {
                throw new ActivityException(sprintf('%s库存不合理，请检查', $marketConfigInfo['name']));
            }
        }

    }

    /**
     * 添加/编辑生日活动
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     */
    protected function addOrUpdate(array $data)
    {
        // 开启事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 保存活动
            $id = $data['id'];
            $item = [
                'name'        => $data['name'],
                'grant_type'  => $data['grant_type'],
                'start_time'  => $data['start_time'],
                'end_time'    => $data['end_time'],
                'resource'    => '',
                'help_order'  => '',
                'update_time' => time(),
            ];
            if (!$id) { // 新增数据时才有创建时间
                $item['create_time'] = time();
            }
            $acId = by::activityConfigModel()->saveLog($id, $item);

            // 保存优惠券，1、有id值的数据修改、2、无id值的数据添加、3、表里已存在，但本次编辑时没有的数据删除
            list($status, $amIds) = by::aM()->getIdsByAid($acId);
            if (!$status) {
                $amIds = [];
            }
            $editAmIds = []; // 被编辑的优惠券的id集合
            $markets = Json::decode($data['market_config']);
            foreach ($markets as $market) {
                // 添加、编辑参数
                $ac_id = $acId;
                $mc_id = $market['mc_id'];
                $stock = $market['stock'] ?? 0;
                $level = implode(',', $market['level'] ?? []);

                // 添加优惠券
                if (empty($market['id'])) {
                    list($status, $res) = by::aM()->add($ac_id, $mc_id, $stock, $level);
                    if (!$status) { // 添加失败
                        throw new ActivityException($res);
                    }
                    continue;
                }

                // 编辑优惠券
                if (in_array($market['id'], $amIds)) {
                    list($status, $res) = by::aM()->edit($market['id'], $ac_id, $mc_id, $stock, $level);
                    if (!$status) { // 编辑失败
                        throw new ActivityException($res);
                    }
                    $editAmIds[] = $market['id'];
                }
            }

            // 删除优惠券
            foreach (array_diff($amIds, $editAmIds) as $id) {
                by::aM()->del($id);
            }

            $transaction->commit();
            // 删除缓存
            by::activityConfigModel()->delCache($acId, $data['grant_type']);
        } catch (ActivityException $e) {
            $transaction->rollBack();
            throw new ActivityException($e->getMessage());
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 记录日志
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            throw new ActivityException('添加/编辑生日活动失败');
        }
    }
}
