<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\ActivityConfigModel;
use Exception;

/**
 * 生日活动
 */
class NewGiftActivity extends Activity {

    /**
     * @throws Exception
     */
    protected function validate(array $data) {
        if (empty($data)) {
            throw new ActivityException("数据不存在");
        }

        // 礼包类型必须选择
        if (empty($data['has_point']) && empty($data['has_coupon'])) {
            throw new ActivityException("请选择礼包类型");
        }

        // 公共参数校验: 开始时间和结束时间校验
        $start_time = $data['start_time'] ?? 0;
        $end_time   = $data['end_time'] ?? 0;

        if (empty($start_time) || empty($end_time)) {
            throw new ActivityException("时间不能为空");
        }

        if ($start_time > $end_time) {
            throw new ActivityException("开始时间不能大于结束时间");
        }

        // 活动类型与ID校验
        $grant_type = isset($data['grant_type']) ? CUtil::uint($data['grant_type']) : 0;
        $id         = $data['id'] ?? 0;

        $activityInfo = ActivityConfigModel::find()->where(['grant_type' => $grant_type, 'is_delete' => 0])->one();

        if ($id && $grant_type != ActivityConfigModel::GRANT_TYPE['newUser']) {
            throw new ActivityException("新人大礼包活动不支持更改类型");
        }

        if (empty($id) && $activityInfo) {
            throw new ActivityException("新人大礼包活动已存在");
        }

        // 积分校验
        if (!empty($data['has_point']) && $data['has_point'] == byNew::AcType1Model()::HAS_POINT_OR_COUPON['YES']) {
            if (empty($data['point_value']) || !preg_match('/^\d+$/', strval($data['point_value'])) || intval($data['point_value']) <= 0) {
                throw new ActivityException("积分值必须为正整数");
            }

            if (empty($data['point_multiplier']) || !preg_match('/^\d+$/', strval($data['point_multiplier'])) || intval($data['point_multiplier']) <= 0) {
                throw new ActivityException("积分倍数必须为正整数");
            }

            if (empty($data['multiplier_start_time']) || empty($data['multiplier_end_time'])) {
                throw new ActivityException("积分倍数生效时间不能为空");
            }

            if ($data['multiplier_start_time'] >= $data['multiplier_end_time']) {
                throw new ActivityException("积分倍数生效开始时间必须小于结束时间");
            }
        }

        // 优惠券校验
        if (!empty($data['has_coupon']) && $data['has_coupon'] == byNew::AcType1Model()::HAS_POINT_OR_COUPON['YES']) {
            $market_config = (array) json_decode($data['market_config'] ?? '', true);

            foreach ($market_config as $market) {
                $stock = $market['stock'] ?? 0;
                $mc_id = $market['mc_id'] ?? 0;

                if (empty($mc_id)) {
                    throw new ActivityException("营销ID不能为空");
                }

                if (!is_numeric($stock) || $stock <= 0) {
                    throw new ActivityException("库存配置不正确");
                }
            }
        }

    }


    /**
     * 添加/编辑新人大礼包活动
     * @param array $data
     * @return void
     * @throws ActivityException
     */
    protected function addOrUpdate(array $data) {
        $db           = by::dbMaster();
        $id           = $data['id'] ?? '';
        $grant_type   = $data['grant_type'] ?? '';
        $marketConfig = (array) json_decode($data['market_config'] ?? '', true);
        $currentTime  = intval(START_TIME);

        // 是否勾选积分
        $pointValue          = $data['has_point'] ? $data['point_value'] : 0;
        $pointMultiplier     = $data['has_point'] ? $data['point_multiplier'] : 0;
        $multiplierStartTime = $data['has_point'] ? $data['multiplier_start_time'] : 0;
        $multiplierEndTime   = $data['has_point'] ? $data['multiplier_end_time'] : 0;

        $tran = $db->beginTransaction();
        try {
            // 保存主记录
            $saveData = [
                    'market_id'   => 0,
                    'name'        => $data['name'] ?? '',
                    'grant_type'  => $grant_type,
                    'start_time'  => $data['start_time'],
                    'end_time'    => $data['end_time'],
                    'update_time' => $currentTime,
            ];

            if (empty($id)) {
                $saveData['create_time'] = $currentTime;
                $saveData['resource']    = '';
                $saveData['help_order']  = '';
            } else {
                $record = ActivityConfigModel::findOne($id);
                if (empty($record)) {
                    throw new ActivityException('记录不存在，无法修改', 1001);
                }
            }

            // 保存主记录并获取 acId
            $acId = by::activityConfigModel()->saveLog($id, $saveData);

            // 准备新人大礼包副表数据
            $acType1Data = [
                    'ac_id'                 => $acId,
                    'has_point'             => $data['has_point'],
                    'point_value'           => $pointValue,
                    'point_multiplier'      => $pointMultiplier,
                    'multiplier_start_time' => $multiplierStartTime,
                    'multiplier_end_time'   => $multiplierEndTime,
                    'has_coupon'            => $data['has_coupon'],
                    'utime'                 => $currentTime,
            ];

            if (empty($id)) {
                $acType1Data['ctime'] = $currentTime;
            }

            // 保存新人大礼包副表数据
            byNew::AcType1Model()->saveLog($id, $acType1Data);

            // 处理优惠券
            list($status, $amIds) = by::aM()->getIdsByAid($acId);
            if ($data['has_coupon']) {
                if (!$status) {
                    $amIds = [];
                }

                $editAmIds = []; // 存放编辑的优惠券ID
                foreach ($marketConfig as $market) {
                    $mcId  = $market['mc_id'];
                    $stock = $market['stock'] ?? 0;
                    $level = implode(',', $market['level'] ?? []);

                    if (empty($market['id'])) {
                        // 添加优惠券
                        list($status, $res) = by::aM()->add($acId, $mcId, $stock, $level);
                        if (!$status) {
                            throw new ActivityException($res);
                        }
                    } elseif (in_array($market['id'], $amIds)) {
                        // 编辑优惠券
                        list($status, $res) = by::aM()->edit($market['id'], $acId, $mcId, $stock, $level);
                        if (!$status) {
                            throw new ActivityException($res);
                        }
                        $editAmIds[] = $market['id'];
                    }
                }

                // 删除未编辑的优惠券
                foreach (array_diff($amIds, $editAmIds) as $delId) {
                    by::aM()->del($delId);
                }
            } else {
                // 如果没有选择优惠券，则删除所有相关优惠券
                if ($status && !empty($amIds)) {
                    foreach ($amIds as $delId) {
                        by::aM()->del($delId);
                    }
                }
            }

            // 提交事务
            $tran->commit();

            // 清除缓存
            by::activityConfigModel()->delCache($acId);
            by::activityConfigModel()->delCache($acId, $grant_type);

        } catch (ActivityException $e) {
            $tran->rollBack();
            throw $e;
        } catch (Exception $e) {
            $tran->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, ActivityConfigModel::GRANT_TYPE['newUser'] . '.activity.error');
            throw new ActivityException('添加/编辑新人活动失败');
        }
    }

}
