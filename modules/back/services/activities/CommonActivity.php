<?php

namespace app\modules\back\services\activities;

use app\exceptions\ActivityException;
use app\models\by;
use app\models\CUtil;
use yii\helpers\Json;

/**
 * 通用活动
 */
class CommonActivity extends Activity
{
    // 奖励类型
    const REWARD_TYPE = [
        'coupon' => 1,  //优惠券
        'point'  => 2,  //积分
    ];

    /**
     * 验证
     * @param array $data
     * @return mixed|void
     * @throws ActivityException
     * @throws \yii\db\Exception
     */
    protected function validate(array $data)
    {
        // 时间不能为空、开始时间小于结束时间
        $start_time = $data['start_time'] ?? 0;
        $end_time = $data['end_time'] ?? 0;
        if (empty($start_time)) {
            throw new ActivityException("开始时间不能为空");
        }

        if (empty($end_time)) {
            throw new ActivityException("结束时间不能为空");
        }

        if ($start_time > $end_time) {
            throw new ActivityException("开始时间不能大于结束时间");
        }

        // 活动备注
        if (empty($data['remark'])) {
            throw new ActivityException('活动备注不能为空');
        }

        // 奖励类型
        if (empty($data['reward_type'])) {
            throw new ActivityException('奖励类型不能为空');
        }

        if (!in_array($data['reward_type'], self::REWARD_TYPE)) {
            throw new ActivityException('奖励类型不合法');
        }

        // 优惠券选择
        if (empty($data['market_config'])) {
            throw new ActivityException('优惠券为空');
        }

        // 验证优惠券
        $markets = Json::decode($data['market_config']);
        // 优惠券ID
        $mcIds = [];
        foreach ($markets as $market) {
            // 不允许存在相同的优惠券
            if (isset($mcIds[$market['mc_id']])) {
                throw new ActivityException('存在相同的优惠券，无法操作');
            } else {
                $mcIds[$market['mc_id']] = true;
            }
            // 检查库存
            $marketConfigInfo = by::marketConfig()->getOneById($market['mc_id']);
            if ($marketConfigInfo['stock_num'] <= 0) {
                throw new ActivityException(sprintf('%s库存不足，请检查', $marketConfigInfo['name']));
            }
            if ($market['stock'] <= 0 || $market['stock'] > $marketConfigInfo['stock_num']) {
                throw new ActivityException(sprintf('%s库存不合理，请检查', $marketConfigInfo['name']));
            }
        }

        // 分享标题
        if (empty($data['share_title'])) {
            throw new ActivityException('分享标题不能为空');
        }

        // 活动图片
        if (empty($data['ac_image'])) {
            throw new ActivityException('活动图片不能为空');
        }

        // 中间图片
        if (empty($data['middleware_image'])) {
            throw new ActivityException('中间图片不能为空');
        }

        // 活动背景图
        if (empty($data['back_image'])) {
            throw new ActivityException('活动背景图不能为空');
        }

        // 分享海报
        if (empty($data['share_image'])) {
            throw new ActivityException('分享海报不能为空');
        }

        // 活动规则
        if (empty($data['rule_note'])) {
            throw new ActivityException('活动规则不能为空');
        }
    }

    /**
     * 添加/编辑通用活动
     * @param array $data
     */
    protected function addOrUpdate(array $data)
    {
        // 开启事务
        $transaction = by::dbMaster()->beginTransaction();
        try {
            // 1、添加/更新活动配置表
            $id = CUtil::uint($data['id'] ?? 0);
            // 资源
            $resource = [
                'share_title' => $data['share_title'],
                'share_image' => $data['share_image']
            ];
            // 供应数量
            $markets = (array)Json::decode($data['market_config']);
            $surplusNum = array_sum(array_column($markets, 'stock'));
            $item1 = [
                'name'        => $data['name'],
                'grant_type'  => $data['grant_type'],
                'resource'    => json_encode($resource, 256),
                'help_order'  => $data['help_order'] ?? '',
                'user_type'   => $data['user_type'] ?? 0,
                'platform'    => $data['platform'] ?? 99,
                'surplus_num' => $surplusNum,
                'start_time'  => $data['start_time'],
                'end_time'    => $data['end_time'],
                'update_time' => time(),
            ];
            if (!$id) { // 新增数据时才有创建时间
                $item1['create_time'] = time();
            }
            $acId = by::activityConfigModel()->saveLog($id, $item1);

            // 2、添加/更新通用礼包表
            $item2 = [
                'ac_id'            => $acId,
                'ac_image'         => $data['ac_image'],
                'back_image'       => $data['back_image'],
                'middleware_image' => $data['middleware_image'],
                'rule_note'        => $data['rule_note'],
                'remark'           => $data['remark'],
                'reward_type'      => $data['reward_type'],
                'update_time'      => time(),
            ];
            if (!$id) { // 新增数据时才创建短链、有创建时间
                $item2['url'] = !YII_ENV_DEV ? $this->createUrl($acId) : 'DEV/DDD';
                $item2['create_time'] = time();
            }
            by::acType3()->saveLog($id, $item2);

            // 3、保存优惠券，1、有id值的数据修改、2、无id值的数据添加、3、表里已存在，但本次编辑时没有的数据删除
            list($status, $amIds) = by::aM()->getIdsByAid($acId);
            if (!$status) {
                $amIds = [];
            }
            $editAmIds = []; // 被编辑的优惠券的id集合
            foreach ($markets as $market) {
                // 添加、编辑参数
                $ac_id = $acId;
                $mc_id = $market['mc_id'];
                $stock = $market['stock'] ?? 0;

                // 添加优惠券
                if (empty($market['id'])) {
                    list($status, $res) = by::aM()->add($ac_id, $mc_id, $stock);
                    if (!$status) { // 添加失败
                        throw new ActivityException($res);
                    }
                    continue;
                }

                // 编辑优惠券
                if (in_array($market['id'], $amIds)) {
                    list($status, $res) = by::aM()->edit($market['id'], $ac_id, $mc_id, $stock);
                    if (!$status) { // 编辑失败
                        throw new ActivityException($res);
                    }
                    $editAmIds[] = $market['id'];
                }
            }

            // 删除优惠券
            foreach (array_diff($amIds, $editAmIds) as $id) {
                by::aM()->del($id);
            }

            $transaction->commit();
            // 删除缓存
            by::activityConfigModel()->delActivityCache($acId);
            by::activityConfigModel()->delCache($acId, $data['grant_type']);
        } catch (ActivityException $e) {
            $transaction->rollBack();
            throw new ActivityException($e->getMessage());
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 记录日志
            CUtil::debug('添加/编辑活动异常：' . $e->getMessage());
            throw new ActivityException('添加/编辑通用活动失败');
        }
    }

    /**
     * 创建短链
     * @param $ac_id
     * @return string
     */
    private function createUrl($ac_id): string
    {
        //生成短链
        $urlData = ['path' => "pagesA/couponReceive/couponReceive", 'query' => "id={$ac_id}", 'type' => 1];
        list($s, $res) = by::model('WxUlinkModel', 'main')->CreateLink($urlData);
        return $res['url_link'] ?? '';
    }
}
