<?php

namespace app\modules\back\services;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use Exception;


class PaymentActivityService
{
    public function getAll($params = []): array
    {
        $activity_name = $params['activity_name'] ?? '';
        $activity_id   = $params['activity_id'] ?? '';
        $startTime     = $params['start_time'] ?? '';
        $endTime       = $params['end_time'] ?? '';

        $query = byNew::PaymentActivityModel()::find()
                ->where(['is_del' => 0]);

        $query->andFilterWhere(['like', 'activity_name', $activity_name]);

        $query->andFilterWhere(['id' => $activity_id]);

        $query->andFilterWhere(['or',
                                ['and', ['<=', 'start_time', $startTime], ['>=', 'start_time', $endTime]],
                                ['and', ['<=', 'end_time', $endTime], ['>=', 'end_time', $startTime]],
                                ['and', ['>=', 'start_time', $startTime], ['<=', 'end_time', $endTime]],
        ]);

        // order by id desc
        $query->orderBy(['id' => SORT_DESC]);

        return CUtil::Pg($query, function ($item) {
            return [
                    'id'            => $item['id'],
                    'activity_name' => $item['activity_name'],
                    'start_time'    => $item['start_time'],
                    'end_time'      => $item['end_time']
            ];
        });
    }

    public function getById($id): array
    {
        $activity = byNew::PaymentActivityModel()::find()
                ->where(['id' => $id, 'is_del' => 0])
                ->with('goods')
                ->asArray()
                ->one();

        if (!$activity) {
            return ['status' => false, 'message' => '未找到活动'];
        }

        $activity = $this->dataFormat($activity);

        return ['status' => true, 'data' => $activity];
    }

    /**
     *  数据格式化
     * @param $data
     * @return array
     */
    public function dataFormat($data): array
    {
        $goodsData = Collection::make($data['goods'])->map(function ($item) {
            return [
                    'gid'           => $item['gid'],
                    'interest_free' => $item['interest_free'],
            ];
        })->groupBy('interest_free')->map(function ($item, $key) {
            return ['interest_free' => $key, 'gids' => array_column($item->toArray(), 'gid')];
        })->sort()->values()->toArray();

        return [
                'id'            => $data['id'],
                'activity_name' => $data['activity_name'],
                'start_time'    => $data['start_time'],
                'end_time'      => $data['end_time'],
                'pay_type'      => $data['pay_type'],
                'goods'         => $goodsData
        ];

    }

    public function save($data): array
    {
        $transaction = by::dbMaster()->beginTransaction();
        try {
            $activity = CUtil::mSave(byNew::PaymentActivityModel(), $data->attributes);

            if (!$activity['status']) {
                $transaction->rollBack();
                return $activity;
            }

            $goods = CUtil::mSaveMultiple(byNew::PaymentActivityGoodsModel(), $data->goods, ['activity_id' => $activity['data']['id']]);
            $transaction->commit();

            $activity['data']['goods'] = $goods['data'] ?? [];
            return $activity;
        } catch (Exception $e) {
            $transaction->rollBack();
            return ['status' => false, 'message' => '保存失败'];
        }
    }

    public function delete($id): array
    {
        return CUtil::mDelete(byNew::PaymentActivityModel(), $id);
    }


    public function goodsSearch($goodsName): array
    {
        //支持 ： 预售尾款订单、内购、普通订单、以旧换新、礼品卡。
        $query = by::Gmain()->find()
                ->select(['t_gmain.id', 't_gmain.name', 'price' => 'COALESCE(t_gtype_0.price)', 't_gtype_0.cover_image', 'atype'])
                ->leftJoin('`db_dreame_goods`.t_gtype_0', 't_gmain.id = t_gtype_0.gid')
                ->where([
//                        't_gtype_0.is_presale'           => by::Gtype0()::IS_PRESALE['no'],
//                        't_gtype_0.is_internal_purchase' => by::Gtype0()::IS_INTERNAL_PURCHASE['no'],
//                        't_gini.sku'                     => null,
't_gmain.is_del'               => by::Gmain()::IS_DEL['no'],
//'t_gmain.status'               => by::Gmain()::STATUS['ON_SALE'],
't_gmain.version'              => [0, 999999999],
't_gtype_0.jd_baitiao_support' => 1

                ]);

        $query->andFilterWhere(['like', 't_gmain.name', $goodsName]);

        $query->orderBy(['sort' => SORT_DESC, 't_gmain.id' => SORT_DESC]);

        return CUtil::Pg($query, function ($item) {
            return [
                    'gid'         => $item['id'],
                    'name'        => $item['name'],
                    'price'       => CUtil::totalFee($item['price'], 1),
                    'cover_image' => $item['cover_image']
            ];
        });

    }
}
