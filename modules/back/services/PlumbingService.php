<?php

namespace app\modules\back\services;

use app\exceptions\PlumbingException;
use app\models\by;
use app\models\CUtil;
use app\modules\back\forms\plumbing\OrderSearchForm;

/**
 * 上下水服务、上门服务
 */
class PlumbingService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取订单列表
     * @param OrderSearchForm $form
     * @return array
     * @throws \yii\db\Exception
     */
    public function getOrderList(OrderSearchForm $form): array
    {
        $plumbingOrder = by::plumbingOrder();
        $params = $form->toArray();

        // TODO 处理订单信息（待优化）
        $data = [];
        $items = $plumbingOrder->getList($form->source, $form->type, $form->source_page, $form->page, $form->page_size, $form->order_no, $form->user_msg, $form->status, $form->s_time, $form->e_time, $form->refund_status, $form->is_export, $form->phones, $form->id, $form->product_type);
        $count = $plumbingOrder->getCount($form->type, $form->order_no, $form->user_msg, $form->status, $form->s_time, $form->e_time, $form->refund_status, $form->is_export, $form->phones, $form->id, $form->product_type);

        // 产品类型
        $productTypeName = $plumbingOrder::PRODUCT_TYPE_NAME;

        foreach ($items as $key => $item) {
            $item['product_type_name'] = $productTypeName[$item['product_type'] ?? 0] ?? '';
            $items[$key] = $item;
        }

        $data['list'] = $items;
        $data['count'] = $count;
        $data['pages'] = CUtil::getPaginationPages($count, $form->page_size);

        return $data;
    }

    /**
     * 退款操作，只针对于班牛已退款的情况
     * @param array $items
     */
    public function refund(array $items)
    {
        $db = by::dbMaster()->beginTransaction();
        try {
            // 模型
            $plumbingOrder = by::plumbingOrder();
            $plumbingRefund = by::plumbingRefund();
            // 真实的处理
            foreach ($items as $order_no => $refund_no) {
                // 参数验证
                if (empty($order_no) || empty($refund_no)) {
                    throw new PlumbingException('参数错误，订单或退款单值为空，订单：' . $order_no . '退款单：' . $refund_no);
                }
                // 工单信息验证
                $refund_info = $plumbingRefund->getInfoByRefundNo($refund_no);
                if (empty($refund_info) || ($refund_info['order_no'] != $order_no)) {
                    throw new PlumbingException('退款工单异常，退款单：' . $refund_no);
                }
                // 工单状态验证
                if (empty($refund_info['refund_status']) || ($refund_info['refund_status'] != $plumbingOrder::REFUND_STATUS['WAIT_AUDIT'])) {
                    throw new PlumbingException('退款工单状态异常，退款单：' . $refund_no . '状态：' . $plumbingOrder::REFUND_STATUS_NAME[$refund_info['refund_status']] ?? '');
                }

                // 退款操作
                $update_data = [
                    'utime' => time(),
                    'rtime' => time(),
                    'price' => by::Gtype0()->totalFee($refund_info['price'] ?? 0)
                ];
                $refund_status = by::plumbingOrder()::REFUND_STATUS['REFUND_SUCCESS'];
                list($status, $msg) = $plumbingRefund->syncInfo($refund_info['user_id'], $refund_no, $order_no, $refund_status, $update_data);
                if (!$status) {
                    throw new PlumbingException($msg);
                }
            }
            $db->commit();
        } catch (\Exception $e) {
            $db->rollBack();
            $err = [
                '错误信息' => $e->getMessage(),
                '错误文件' => $e->getLine(),
                '错误行'  => $e->getLine(),
            ];
            echo $e->getMessage();
            CUtil::debug(json_encode($err, 320), 'plumbing-refund-audit.err');
        }
    }

}