<?php

namespace app\modules\back\services;

use app\components\MessagePush;
use app\models\by;
use app\models\CUtil;

class MessagePushService
{
    private static $_instance = NULL;

    const JUMP_TYPE = [
        'WEB'    => 'Web页面',
        'MALL'   => '商城页面',
        'MEMBER' => '会员页面'
    ];

    const STATUS = [
        'Testing'   => '测试中',
        'Approving' => '审核中',
        'Live'      => '上线',
        'Offline'   => '下线'
    ];

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    //获取跳转类型
    public function getJumpType(): array
    {
        $jumpType = self::JUMP_TYPE;
        $return   = [];
        foreach ($jumpType as $key => $item) {
            $return[] = [
                'code' => $key,
                'name' => $item
            ];
        }
        return [true, $return];
    }

    //获取消息状态
    public function getStatus(): array
    {
        $status = self::STATUS;
        $return = [];
        foreach ($status as $key => $item) {
            $return[] = [
                'code' => $key,
                'name' => $item
            ];
        }
        return [true, $return];
    }


    //消息列表
    public function getMessageList($post)
    {
        return MessagePush::factory()->run('messageList', $post);
    }

    //创建、编辑推送消息
    public function saveMessage($post)
    {
        $id       = $post['id'] ?? '';
        $category = $post['category'] ?? 'activity_msg';//商城后台只配置活动消息  固定写死就可以
        $jumpType = $post['jumpType'] ?? '';            //WEB("WEB", "Web页面"), MALL("MALL", "商城页面"), MEMBER("MEMBER", "会员页面");
        $name     = $post['name'] ?? '';
        $content  = $post['content'] ?? '';
        $link     = $post['link'] ?? '';
        $imgUrl   = $post['imgUrl'] ?? '';
        if (empty($name)) {
            return [false, '消息名称不能为空'];
        }
        if (empty($content)) {
            return [false, '消息内容不能为空'];
        }
        if (empty($jumpType)) {
            return [false, '跳转类型不能为空'];
        }
        $multiLangDisplay = [
            'zh' => [
                'name'    => $name,
                'content' => $content,
                'link'    => $link,
            ],
            'en' => [
                'name'    => $name,
                'content' => $content,
                'link'    => $link,
            ]
        ];


        if ($id) {
            //编辑推送消息
            $input = [
                'id'               => $id,
                'jumpType'         => $jumpType,
                'category'         => $category,
                'imgUrl'           => $imgUrl,
                'multiLangDisplay' => json_encode($multiLangDisplay, 320)
            ];
            return MessagePush::factory()->run('editMessage', $input);
        } else {
            //创建推送消息
            $input = [
                'jumpType'         => $jumpType,
                'category'         => $category,
                'imgUrl'           => $imgUrl,
                'multiLangDisplay' => json_encode($multiLangDisplay, 320)
            ];
            return MessagePush::factory()->run('createMessage', $input);
        }

    }


    //删除推送消息
    public function deleteMessage($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('deleteMessage', $post);
    }

    //按照id获取消息配置
    public function getMessageInfo($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('messageInfo', $post);
    }


    //消息提交审核
    public function submitAuditMessage($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('submitAuditMessage', $post);
    }


    //审核消息
    public function auditMessage($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('auditMessage', $post);
    }


    //消息下线
    public function offlineMessage($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('offlineMessage', $post);
    }


    //转为测试
    public function toTestingMessage($post)
    {
        $id = intval($post['id'] ?? '');
        if (empty($id)) {
            return [false, '推送消息ID不能为空'];
        }
        return MessagePush::factory()->run('toTestingMessage', $post);
    }


    //上传图片
    public function uploadImage($file)
    {
        return MessagePush::factory()->run('uploadImage', ['file' => $file]);
    }


    //消息推送
    public function sendPush($post)
    {
        try {
            $msgConfigId  = $post['msgConfigId'] ?? '';
            $pushStrategy = $post['pushStrategy'] ?? 'crowd_push';
            $pushScope    = $post['pushScope'] ?? '';

            if (empty($msgConfigId)) {
                return [false, '请选择推送消息'];
            }

            if (empty($pushScope)) {
                return [false, '请选择推送用户'];
            }

            if (empty($pushStrategy)) {
                return [false, '请选择推送人群'];
            }

            switch ($pushScope) {
                case '1'://全部用户
                    return MessagePush::factory()->run('sendPush', $post);
                case '2': //指定用户
                    // 读取文件内容
                    list($status, $data) = CUtil::readCsv($post['userFile']['tmp_name'] ?? '');

                    if (!$status) {
                        return [false, $data];
                    }

                    // 处理手机号和user_id
                    $phoneUsers = array_filter($data, function ($item) {
                        return strlen($item[0]) == 11;
                    });

                    $userIds = array_filter($data, function ($item) {
                        return strlen($item[0]) != 11;
                    });

                    // 去重并统一转为user_id
                    $phoneUsers = array_unique(array_column(by::users()->getWxUsersByPhones(array_column($phoneUsers, 0)), 'user_id', 'phone'));
                    $userIds    = array_unique(array_merge(array_column($userIds, 0), $phoneUsers));

                    // 获取uids
                    $uid = array_map(function ($userId) {
                        $mallInfo = by::usersMall()->getInfoByUserId($userId);
                        return $mallInfo['uid'] ?? '';
                    }, $userIds);
                    $uid = array_values(array_filter($uid));
                    // 构建发送数据
                    $sendData = [
                        'msgConfigId'  => $msgConfigId,
                        'pushStrategy' => $pushStrategy,
                        'pushScope'    => $pushScope,
                        'uids'         => $uid
                    ];

                    // 调用消息推送
                    return MessagePush::factory()->run('sendPush', $sendData);

                default:
                    return [false, '请选择推送用户'];
            }
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.send_message');
            return [false, '推送失败'];
        }
    }

}
