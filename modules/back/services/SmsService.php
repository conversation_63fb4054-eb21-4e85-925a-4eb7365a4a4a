<?php

namespace app\modules\back\services;

use app\components\AliYunSms;
use app\components\AppCRedisKeys;
use app\components\collection\Arr;
use app\models\by;
use app\models\CUtil;
use DateTime;

/**
 * 短信服务
 */
class SmsService
{
    // 批量发送短信
    const BATCH_SEND_LIMIT = 100;
    // 缓存有效期
    const EXPIRE_TIME = 10 * 60; // 10分钟

    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取模板列表
     * @return array
     */
    public function getSmsTmplList(): array
    {
        $columns = ['code', 'name', 'tmpl_url'];
        return by::smsTmplModel()->getSmsTmplList($columns);
    }

    /**
     * 根据 code 获取模板
     * @param string $code
     * @return array
     */
    public function getSmsTmplByCode(string $code): array
    {
        $columns = ['id', 'code', 'name', 'content', 'tmpl_url'];
        $item = by::smsTmplModel()->getSmsTmplByCode($code, $columns);
        if (empty($item)) {
            return [];
        }
        return Arr::first($item);
    }

    /**
     * 发送短信
     * @param string $code
     * @param array $data
     * @return array
     * @throws \yii\db\Exception
     */
    public function sendSms(string $code, array $data): array
    {
        // 判空
        if (empty($data)) {
            return [false, '数据为空'];
        }

        // 验证数据
        $items = array_slice($data, 1, count($data) - 1);
        // 判内容空
        if (empty($items)) {
            return [false, '数据为空'];
        }

        // 短信模板
        $smsTmpl = $this->getSmsTmplByCode($code);
        if (empty($smsTmpl)) {
            return [false, '短信模板不存在'];
        }

        // 获取短信模板里的参数
        $params = $this->__getSmsTmplParams($smsTmpl['content']);

        // 首行标题，获取文件里的短信模板参数
        $title = $data[0] ?? [];
        $codeKeys = array_slice($title, 1, count($title) - 1);

        // 校验短信模板的参数
        if ($params != $codeKeys) {
            return [false, '短信模板中参数错误'];
        }

        // 短信数据
        $dataList = [];

        foreach ($items as $item) {
            $phone = $item[0] ?? '';
            // 验证手机号是否合法
            if (!CUtil::reg_valid($phone, CUtil::REG_PHONE)) {
                return [false, sprintf("存在非法手机号:%s", $phone)];
            }
            // 验证手机号是否重复
            if (isset($dataList[$phone])) {
                return [false, sprintf("存在重复手机号:%s", $phone)];
            }
            // 校验参数、参数值
            $value = array_slice($item, 1, count($item) - 1);
            $value = array_filter($value, function ($value) {
                return mb_strlen(trim($value)) != 0; // 过滤长度不为0的字符
            });
            if (count($codeKeys) != count($value)) {
                return [false, sprintf("手机号:%s的参数错误", $phone)];
            }
            $dataList[$phone] = array_combine($codeKeys, $value);
        }

        // 验证用户是否 10分钟内 发送过
//        list($status, $res) = $this->__getSendSmsUserList($code, array_keys($dataList));
//        if (!$status) {
//            return [false, $res];
//        }

        // 批量发送短信
        return $this->batchSendSms($code, $dataList);
    }

    /**
     * 获取短信记录列表
     * @param array $params
     * @return array
     */
    public function getSmsSendRecordList(array $params): array
    {
        // 查询数据
        $columns = ['id', 'sms_code', 'phone', 'content', 'send_time'];
        $res = by::smsSendRecordModel()->getSmsSendRecordList($params, $columns);
        $data = [];
        foreach ($res['items'] as $item) {
            // 时间戳，转时间
            $item['send_time'] = date('Y-m-d H:i:s', $item['send_time']);
            $data[] = $item;
        }
        return ['items' => $data, 'total' => $res['total']];
    }

    /**
     * 批量发送短信
     * @param string $code
     * @param array $data
     * @return array
     * @throws \yii\db\Exception
     */
    public function batchSendSms(string $code, array $data, $isSaveLog = true): array
    {
        // 发送短信，每次 100 条
        $items = array_chunk($data, self::BATCH_SEND_LIMIT, true);
        foreach ($items as $item) {
            // 中文去转义
            $item = array_map(function ($value) {
                // 检测当前编码
                $currentEncoding = mb_detect_encoding($value['name'] ?? '', ["ASCII", "GB2312", "GBK", "UTF-8", "BIG5"]);
                // 如果检测到有效的编码并且当前编码与目标编码不同，则进行转换
                if ($currentEncoding && $currentEncoding !== 'UTF-8') {
                    $value['name'] = mb_convert_encoding($value['name'] ?? '', 'UTF-8', $currentEncoding);
                }
                return $value;
            }, $item);
            // 发送短信
            list($status, $res) = AliYunSms::sendBatchSms(array_keys($item), $code, array_values($item));
            if (!$status) {
                // 错误记录日志
                CUtil::debug(sprintf("发送短信失败，模板ID：%s，参数：%s ，失败原因: %s", $code, json_encode($item), $res), 'send-sms-error-log');
                return [false, $res];
            }
            // 保存记录
            $isSaveLog && $this->addSendSmsRecord($code, $item);
            // 保存到缓存
            $this->__setSendSmsUserList($code, array_keys($item));
            // 正确记录日志
            CUtil::debug(sprintf("发送短信成功，模板ID：%s，参数：%s", $code, json_encode($item)), 'send-sms-success-log');
        }
        return [true, ''];
    }

    /**
     * 添加记录
     * @param string $code
     * @param array $items
     * @throws \yii\db\Exception
     */
    protected function addSendSmsRecord(string $code, array $items)
    {
        // 记录发送的内容
        $smsTmpl = $this->getSmsTmplByCode($code);
        if (empty($smsTmpl)) {
            CUtil::debug(sprintf("添加短信记录失败，模板ID：%s，参数：%s", $code, json_encode($items)), 'sms-record-error-log');
            return;
        }

        $time = time();
        $rows = [];
        foreach ($items as $phone => $item) {
            // 短信内容
            $content = $this->__getSmsContent($item, $smsTmpl['content']);
            // 短信记录
            $rows[] = [
                'sms_code'    => $code,
                'phone'       => $phone,
                'content'     => $content,
                'send_time'   => $time,
                'ctime'       => $time,
                'utime'       => $time,
            ];
        }
        // 批量插入短信记录
        $columns = ['sms_code', 'phone', 'content', 'send_time', 'ctime', 'utime'];
        by::smsSendRecordModel()->batchInsertLog($columns, $rows);
    }

    /**
     * 获取已经发送的用户ID
     * @param string $code
     * @param array $data
     * @return array
     */
    private function __getSendSmsUserList(string $code, array $data): array
    {
        $redis = by::redis();
        $key = AppCRedisKeys::getSendSmsUserList($code);
        $userList = $redis->sMembers($key);
        $exists = array_intersect($userList, $data);
        if (!empty($exists)) {
            return [false, sprintf("已在10分钟内向手机号：%s发送过此短信模板，不可重复发送！", implode(',', $exists))];
        }
        return [true, ''];
    }

    /**
     * 设置已经发送的用户ID
     * @param string $code
     * @param array $data
     */
    private function __setSendSmsUserList(string $code, array $data)
    {
        $redis = by::redis();
        $key = AppCRedisKeys::getSendSmsUserList($code);
        // 保存到缓存
        $redis->sAddArray($key, $data);
        // 有效期
        $redis->expire($key, self::EXPIRE_TIME);
    }

    /**
     * 获取短信内容：
     * 例如：验证码：${code}，有效期10分钟。如非本人操作，请忽略。
     * 替换为：验证码：123456，有效期10分钟。如非本人操作，请忽略。
     * @param $items
     * @param $content
     * @return array|string|string[]
     */
    private function __getSmsContent($items, $content)
    {
        $search = null;
        $replace = null;
        foreach ($items as $key => $item) {
            $search[] = sprintf('${%s}', $key);
            $replace[] = $item;
        }
        return str_replace($search, $replace, $content);
    }

    /**
     * 获取短信模板中的参数
     * @param $content
     * @return array
     */
    private function __getSmsTmplParams($content): array
    {
        $pattern = '/\$\{(.*?)\}/';
        preg_match_all($pattern, $content, $matches);
        if (!empty($matches[1])) {
            return $matches[1];
        }
        return [];
    }

    public function sendSmsByRefundOrder($orderNo,$userId){
        // 每天只能发一次短信，防止重复发送
        $redis       = by::redis('core');
        $session_key = AppCRedisKeys::sendSmsByRefundOrder('send_msg', $orderNo);
        $bool = $redis->get($session_key);
        if ($bool) {
            return [false, '每日短信发送次数限制已达，请明天重试'];
        }
        
        // 获取用户手机号
        $user = by::usersMall()->getInfoByUserId($userId);
        if (!$user){
            return [false, '用户不存在'];
        }
        $phone = $user['phone'] ?? '';
        if (!$phone){
            return [false, '手机号为空'];
        }
        $code = by::model("SmsModel",MAIN_MODULE)::SCENES_ID['REFUND_SMS'];
        $item = ['order_no'=>$orderNo];
        // 发送短信
        list($status, $res) = AliYunSms::sendSms($phone, $code , []);
        if (!$status) {
            // 错误记录日志
            CUtil::debug(sprintf("发送短信失败，模板ID：%s，参数：%s ，失败原因: %s", $code, json_encode($item), $res), 'send-sms-error-log');
            return [false, $res];
        }
        // 保存记录
        $rows = [
            [
                'sms_code'    => $code,
                'phone'       => $phone,
                'content'     => '尊敬的追觅会员，您好！您发起的退货订单还差一步就能完成啦，请及时填写退回包裹单号，以便我们快速为您处理哦！',
                'send_time'   => time(),
                'ctime'       => time(),
                'utime'       => time(),
            ]
        ];
        $columns = ['sms_code', 'phone', 'content', 'send_time', 'ctime', 'utime'];
        by::smsSendRecordModel()->batchInsertLog($columns, $rows);
        // 设置缓存
        $existsTime = 1;
        $date = new DateTime();
        // 修改日期为明天
        $date->modify('+1 day');
        // 将时间设置为 00:00:00
        $date->setTime(0, 0, 0);
        // 获取 Unix 时间戳
        $tomorrowMidnightTimestamp = $date->getTimestamp();
        $existsTime = $tomorrowMidnightTimestamp - time();
        $redis->set($session_key, 1, ['EX' => $existsTime]);

        return [true, ''];
    }

}