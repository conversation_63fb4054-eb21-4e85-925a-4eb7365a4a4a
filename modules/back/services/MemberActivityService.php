<?php

namespace app\modules\back\services;

use app\components\AppCRedisKeys;
use app\components\AppNRedisKeys;
use app\components\MemberCenter;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\common\Singleton;
use app\modules\goods\models\DrawActivityModel;
use app\modules\goods\models\DrawActivityPrizeModel;
use app\modules\goods\models\DrawActivityPrizeRecordModel;
use app\modules\goods\models\DrawActivityTaskModel;
use app\modules\goods\models\DrawPrizeModel;
use app\modules\goods\models\DrawTaskModel;
use app\modules\main\models\DrawCustomFormRecordModel;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\MemberActivityModuleGoodsModel;
use app\modules\main\models\MemberActivityModuleRelationModel;
use app\modules\main\models\MemberActivityModuleResourceModel;
use PhpOffice\PhpSpreadsheet\IOFactory;
use RedisException;

final class MemberActivityService
{
    use Singleton;

    private $moduleHandlers = [
            'BIG_TITLE'       => 'saveBigModule',
            'CHECKIN'         => 'saveCheckInModule',
            'RETURN_POINTS'   => 'saveReturnPointsModule',
            'AD_POSITION'     => 'saveAdPositionModule',
            'DRAW'            => 'saveDrawModule',
            'NEW_PERSON'      => 'saveNewPersonModule',
            'RECOMMEND_GOODS' => 'saveRecommendGoodsModule',
            'COUPON'          => 'saveCouponModule',
            'PART_GOODS'      => 'savePartGoodsModule',
            'POINTS_EXCHANGE' => 'savePointsExchangeModule',
            'RED_ENVELOPE'    => 'saveRedEnvelopeModule',
            'SNAP_UP'         => 'saveSnapUpModule',
            'HALF_PRICE_BUY'  => 'saveHalfPriceBuyModule',
            'CONSUME_MONEY'   => 'saveConsumeMoneyBuyModule',
    ];


    /**
     * 系统模块配置常量
     * 格式：[模块代码 => ['id' => 模块ID, 'title' => 模块标题]]
     */
    const MODULE_RED_ID = [
            'BIG_TITLE'       => [
                    'id'    => 1,
                    'title' => '大标题模块'
            ],
            'CHECKIN'         => [
                    'id'    => 2,
                    'title' => '积分打卡'
            ],
            'RETURN_POINTS'   => [
                    'id'    => 3,
                    'title' => '购物返积分'
            ],
            'AD_POSITION'     => [
                    'id'    => 4,
                    'title' => '广告位'
            ],
            'DRAW'            => [
                    'id'    => 5,
                    'title' => '抽奖活动'
            ],
            'NEW_PERSON'      => [
                    'id'    => 6,
                    'title' => '新人入会福利'
            ],
            'RECOMMEND_GOODS' => [
                    'id'    => 7,
                    'title' => '机器推荐'
            ],
            'COUPON'          => [
                    'id'    => 8,
                    'title' => '优惠券配置'
            ],
            'PART_GOODS'      => [
                    'id'    => 9,
                    'title' => '配件专区'
            ],
            'POINTS_EXCHANGE' => [
                    'id'    => 10,
                    'title' => '积分兑换'
            ],
            'RED_ENVELOPE'    => [
                    'id'    => 11,
                    'title' => '现金红包'
            ],
    ];
    // 模块类型:1-机器推荐 2-配件专区 3-积分兑换
    const MODULE_TYPE = [
            'RECOMMEND_GOODS' => 1,
            'PART_GOODS'      => 2,
            'POINTS_EXCHANGE' => 3,
    ];


    const USER_TYPE = [
            'ALL'      => 1, // 全部用户
            'NEW_USER' => 2, // 新用户
    ];
    /**
     * 获取活动列表
     * @param array $params
     * @return array
     */
    public function getList(array $params = []): array
    {
        $model = MemberActivityModel::getInstance();
        $params['__select_fields__'] = ['id', 'title', 'description', 'rule', 'cycle_type', 'start_time', 'end_time', 'status'];
        return $model->getList($params);
    }
    
    /**
     * 获取活动详情
     * @param int $id 活动ID
     * @param bool $fromCache 是否从缓存获取，默认否false
     * @return array
     * @throws BusinessException
     */
    public function getDetail(int $id, bool $fromCache = false): array
    {
        if ($fromCache) {
            $redis = by::redis();
            $cacheKey = AppCRedisKeys::getMemberActivityDetail($id);
            $cachedData = $redis->get($cacheKey);

            if ($cachedData) {
                return json_decode($cachedData, true);
            }
        }
        
        $activityRes = MemberActivityModel::find()->where(['id' => $id, 'delete_time' => 0])->select(['id', 'title', 'description', 'rule', 'cycle_type', 'start_time', 'end_time', 'create_time', 'update_time'])->asArray()->one();

        if (empty($activityRes)) {
            throw new BusinessException(sprintf('活动ID[%s]数据不存在', $id));
        }

        $relationRes = MemberActivityModuleRelationModel::find()->where(['activity_id' => $id, 'delete_time' => 0])->orderBy('sort ASC, id ASC')->asArray()->all();

        $moduleResModel = MemberActivityModuleResourceModel::getInstance();
        $drawActivityTaskModel = DrawActivityTaskModel::getInstance();
        $drawPrizeModel = DrawPrizeModel::getInstance();
        $drawActivityPrizeModel = DrawActivityPrizeModel::getInstance();
        $memberActivityModuleGoodsModel = MemberActivityModuleGoodsModel::getInstance();
        $moduleResIdMap = $moduleResModel->getAllModuleResIdMap();
        $relationData = [];
        foreach ($relationRes as $key => $item) {
            $moduleData = empty($item['extra']) ? [] : json_decode($item['extra'], true);

            $tmp = [
                'activity_id' => (int) $item['activity_id'],
                'module_relation_id' => (int) $item['id'],
                'module_res_id' => (int) $item['module_res_id'],
                'module_code' => $moduleResIdMap[$item['module_res_id']]['module_code'] ?? '',
                'module_title' => $item['title'],
                'module_summary' => $item['summary'],
                'module_start_time' => $item['start_time'],
                'module_end_time' => $item['end_time'],
                'module_data' => $moduleData,
                'module_sort' => $item['sort'],
            ];

            // 抽奖模块
            if ($tmp['module_code'] == 'DRAW') {
                $drawActivityTaskRes = $drawActivityTaskModel->getListByRelationId($item['id']);
                $drawPrizeRes = $drawPrizeModel->getListByRelationId($item['id']);
                $drawPrizeMap = array_column($drawPrizeRes, null, 'id');
                $drawActivityPrizeRes = $drawActivityPrizeModel->getListByRelationId($item['id']);

                $drawTaskData = [];
                $drawPrizeData = [];
                foreach ($drawActivityTaskRes as $taskItem) {
                    $extra = json_decode($taskItem['extra'], true);
                    $drawTaskData[] = [
                        'data_id' => (int) $taskItem['id'],
                        'draw_activity_id' => (int) $taskItem['activity_id'],
                        'task_id' => (int) $taskItem['task_id'],
                        'task_name' => $extra['task_name'],
                        'task_code' => $extra['task_code'],
                        'task_desc' => $extra['task_desc'] ?? '',
                        'task_limit' => (int) $taskItem['task_limit'],
                        'task_limit_type' => (int) $taskItem['task_limit_type'],
                        'consume_type' => (int) ($taskItem['consume_type'] ?? 0),
                        'draw_times' => (int) ($taskItem['draw_times'] ?? 0),
                        'consume_gold' => empty($taskItem['consume_gold']) ? '0' : bcdiv($taskItem['consume_gold'], 100, 2),
                        'extra' => $extra['task_code'] == 'INVITE_NEW_USER' ? $extra['extra'] : ['gift_times' => ($extra['task_code'] == 'GOLD_DRAW' ? (empty($taskItem['consume_gold']) ? '0' : bcdiv($taskItem['consume_gold'], 100, 2)) : $taskItem['draw_times'])],
                    ];
                }
                
                foreach ($drawActivityPrizeRes as $prizeItem) {
                    $drawPrizeData[] = [
                        'data_id' => (int) $prizeItem['id'],
                        'prize_id' => (int) $prizeItem['prize_id'],
                        'prize_type' => (int) ($drawPrizeMap[$prizeItem['prize_id']]['type'] ?? 0),
                        'prize_name' => $drawPrizeMap[$prizeItem['prize_id']]['name'] ?? '',
                        'prize_value' => $drawPrizeMap[$prizeItem['prize_id']]['value'] ?? '',
                        'prize_image' => $drawPrizeMap[$prizeItem['prize_id']]['image'] ?? '',
                        'prize_num' => (int) $prizeItem['prize_num'],
                        'prize_limit' => (int) $prizeItem['prize_limit'],
                        'prize_rate' => $prizeItem['rate'],
                        'is_default' => (int) $prizeItem['is_default'],
                    ];
                }

                $tmp['module_data']['draw_task'] = $drawTaskData;
                $tmp['module_data']['draw_prize'] = $drawPrizeData;
            // } elseif (in_array($tmp['module_code'], ['RECOMMEND_GOODS', 'PART_GOODS', 'POINTS_EXCHANGE','SNAP_UP', 'HALF_PRICE_BUY'])) {
            } elseif (in_array($tmp['module_code'], ['RECOMMEND_GOODS', 'PART_GOODS', 'POINTS_EXCHANGE','SNAP_UP'])) { //HALF_PRICE_BUY 没写入到relation_goods表所以不需要这里
                $moduleGoodsRes = $memberActivityModuleGoodsModel->getListByRelationId($item['id']);
                $moduleGoodsData = [];
                foreach ($moduleGoodsRes as $moduleGoodsItem) {
                    switch ($tmp['module_code']) {
                        case 'RECOMMEND_GOODS':
                            $moduleGoodsData[] = [
                                    'data_id'       => (int) $moduleGoodsItem['id'],
                                    'module_type'   => (int) $moduleGoodsItem['module_type'],
                                    'category_id'   => (int) $moduleGoodsItem['category_id'],
                                    'category_name' => $moduleGoodsItem['category_name'],
                                    'goods_id'      => (int) $moduleGoodsItem['goods_id'],
                                    'goods_name'    => $moduleGoodsItem['goods_name'],
                                    'show_name'     => $moduleGoodsItem['show_name'],
                                    'goods_image'   => $moduleGoodsItem['goods_image'],
                                    'goods_sku'     => $moduleGoodsItem['goods_sku'],
                                    'sale_price'    => $moduleGoodsItem['sale_price'],
                                    'free_periods'  => (int) $moduleGoodsItem['free_periods'],
                                    'trade_subsidy' => $moduleGoodsItem['trade_subsidy'],
                                    'sort_order'    => (int) $moduleGoodsItem['sort_order'],
                                    'status'        => (int) $moduleGoodsItem['status'],
                            ];
                            break;
                        case 'PART_GOODS':
                            $moduleGoodsData[] = [
                                    'data_id'       => (int) $moduleGoodsItem['id'],
                                    'module_type'   => (int) $moduleGoodsItem['module_type'],
                                    'category_id'   => (int) $moduleGoodsItem['category_id'],
                                    'category_name' => $moduleGoodsItem['category_name'],
                                    'goods_id'      => (int) $moduleGoodsItem['goods_id'],
                                    'goods_name'    => $moduleGoodsItem['goods_name'],
                                    'goods_image'   => $moduleGoodsItem['goods_image'],
                                    'goods_sku'     => $moduleGoodsItem['goods_sku'],
                                    'sale_price'    => $moduleGoodsItem['sale_price'],
                                    'show_name'     => $moduleGoodsItem['show_name'],
                                    'sort_order'    => (int) $moduleGoodsItem['sort_order'],
                                    'status'        => (int) $moduleGoodsItem['status'],
                                    'extra'         => empty($moduleGoodsItem['extra']) ? [] : json_decode($moduleGoodsItem['extra'], true)
                            ];
                            break;
                        case 'POINTS_EXCHANGE':
                            $extra2            = json_decode($moduleGoodsItem['extra'], true);
                            $moduleGoodsData[] = [
                                    'data_id'         => (int) $moduleGoodsItem['id'],
                                    'module_type'     => (int) $moduleGoodsItem['module_type'],
                                    'category_id'     => (int) $moduleGoodsItem['category_id'],
                                    'category_name'   => $moduleGoodsItem['category_name'],
                                    'goods_id'        => (int) $moduleGoodsItem['goods_id'],
                                    'goods_name'      => $moduleGoodsItem['goods_name'],
                                    'goods_image'     => $moduleGoodsItem['goods_image'],
                                    'goods_sku'       => $moduleGoodsItem['goods_sku'],
                                    'sale_price'      => $moduleGoodsItem['sale_price'],
                                    'sale_points'     => (int) $moduleGoodsItem['sale_points'],
                                    'sort_order'      => (int) $moduleGoodsItem['sort_order'],
                                    'old_sale_price'  => $extra2['old_sale_price'] ?? '',
                                    'old_sale_points' => (int) ($extra2['old_sale_points'] ?? 0),
                                    'status'          => (int) $moduleGoodsItem['status'],
                            ];
                            break;
                        case 'SNAP_UP':
                            $moduleGoodsData[] = [
                                    'data_id'       => (int) $moduleGoodsItem['id'],
                                    'module_type'   => (int) $moduleGoodsItem['module_type'],
                                    'category_id'   => (int) $moduleGoodsItem['category_id'],
                                    'category_name' => $moduleGoodsItem['category_name'],
                                    'goods_id'      => (int) $moduleGoodsItem['goods_id'],
                                    'goods_name'    => $moduleGoodsItem['goods_name'],
                                    'goods_image'   => $moduleGoodsItem['goods_image'],
                                    'goods_sku'     => $moduleGoodsItem['goods_sku'],
                                    'sale_price'    => $moduleGoodsItem['sale_price'],
                                    'show_name'     => $moduleGoodsItem['show_name'],
                                    'sort_order'    => (int) $moduleGoodsItem['sort_order'],
                                    'status'        => (int) $moduleGoodsItem['status'],
                                    'extra'         => empty($moduleGoodsItem['extra']) ? [] : json_decode($moduleGoodsItem['extra'], true),
                            ];
                            break;

                        case 'HALF_PRICE_BUY':
                            $moduleGoodsData[] = [
                                    'data_id'       => (int) $moduleGoodsItem['id'],
                                    'module_type'   => (int) $moduleGoodsItem['module_type'],
                                    'category_id'   => (int) $moduleGoodsItem['category_id'],
                                    'category_name' => $moduleGoodsItem['category_name'],
                                    'goods_id'      => (int) $moduleGoodsItem['goods_id'],
                                    'goods_name'    => $moduleGoodsItem['goods_name'],
                                    'goods_image'   => $moduleGoodsItem['goods_image'],
                                    'goods_sku'     => $moduleGoodsItem['goods_sku'],
                                    'sale_price'    => $moduleGoodsItem['sale_price'],
                                    'show_name'     => $moduleGoodsItem['show_name'],
                                    'sort_order'    => (int) $moduleGoodsItem['sort_order'],
                                    'status'        => (int) $moduleGoodsItem['status'],
                            ];
                            break;
                    }
                }
                
                $tmp['module_data'] = $moduleGoodsData;
            } elseif ($tmp['module_code'] == 'RED_ENVELOPE') {
                // 红包模块 处理数据
                $acRelationId = $tmp['module_relation_id'];
                $key          = AppNRedisKeys::envelopeActivity($acRelationId);

                $redis = by::redis();

                // 获取库存和列表数据（合并操作减少Redis请求）
                $listLength = $redis->lLen($key);
                $listData   = $redis->lRange($key, 0, 9);

                // 构建模块数据（确保resource字段始终为数组）
                $tmp['module_data']['envelope_stock'] = $listLength;
                if ($listData) {
                    // 加盐
                    $listData = array_map(function ($item) use ($acRelationId) {
                        $item = json_decode($item, true);

                        $item['link'] = str_replace($item['link'], "********", $item['link']);
                        return $item;
                    }, $listData);
                }
                $tmp['module_data']['envelope_resource'] = $listData ?: [];
            }

            $relationData[] = $tmp;
        }

        $activityRes['modules'] = $relationData;
        
        if ($fromCache) {
            $redis = by::redis();
            $cacheKey = AppCRedisKeys::getMemberActivityDetail($id);
            $redis->set($cacheKey, json_encode($activityRes, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), ['EX' => empty($activityRes) ? 10 : 7200]);
        }
        
        return $activityRes;
    }

    /**
     * 获取模块资源列表
     * @param array $params
     * @return array
     */
    public function getModuleResList(array $params = []): array
    {
        $model = MemberActivityModuleResourceModel::getInstance();
        return $model->getList($params);
    }

    /**
     * 创建活动
     * @param array $data
     * @return array
     */
    public function create(array $data): array
    {
        $memberActivityModel = MemberActivityModel::getInstance();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 创建活动主记录
            list($status, $activityId, $msg) = $memberActivityModel->doCreate($data);
            if (!$status) {
                throw new BusinessException($msg);
            }

            // 处理活动模块
            if (!empty($data['modules'])) {
                $modules = json_decode($data['modules'], true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new BusinessException('活动模块数据格式错误');
                }

                foreach ($modules as $module) {
                    $moduleCode = $module['module_code'] ?? '';

                    // 验证模块编码
                    if (empty($moduleCode)) {
                        throw new BusinessException('缺少模块编号[modules.*.module_code]');
                    }

                    // 检查处理器是否存在
                    if (!isset($this->moduleHandlers[$moduleCode])) {
                        throw new BusinessException("未定义的模块类型: {$moduleCode}");
                    }

                    // 调用对应的处理器
                    $handler = $this->moduleHandlers[$moduleCode];
                    list($status, $relationId, $msg) = $this->$handler($activityId, $module);

                    if (!$status) {
                        throw new BusinessException($msg);
                    }
                }
            }

            $trans->commit();
            return [true, $activityId, '活动创建成功'];
        } catch (BusinessException $e) {
            return [false, 0, $e->getMessage()];
        } catch (\Throwable $e) {
            $trans->rollBack();
            CUtil::debug('活动创建失败: ' . $e->getMessage(), 'err.MemberActivityService.create');
            return [false, 0, '活动创建失败'];
        }
    }
    
    /**
     * 更新活动
     * @param array $data
     * @return array
     */
    public function update(array $data): array
    {
        $id = (int) ($data['id'] ?? 0);
        if (empty($id)) {
            return [false, '活动ID不能为空'];
        }
        $memberActivityModel = MemberActivityModel::getInstance();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 更新活动主记录
            list($status, $msg) = $memberActivityModel->doUpdate($id, $data);
            if (!$status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            $memberActivityModel->__delDetailCache($id);
            return [true, '更新活动成功'];
        } catch (BusinessException $e) {
            return [false, $e->getMessage()];
        } catch (\Throwable $e) {
            $trans->rollBack();
            CUtil::debug('更新活动失败: ' . $e->getMessage(), 'err.MemberActivityService.update');
            return [false, '更新活动失败'];
        }
    }

    /**
     * 更新活动v2
     * @param array $data
     * @return array
     */
    public function updateV2(array $data): array
    {
        $id = (int) ($data['id'] ?? 0);
        if (empty($id)) {
            return [false, '活动ID不能为空'];
        }
        $memberActivityModel = MemberActivityModel::getInstance();
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 更新活动主记录
            list($status, $msg) = $memberActivityModel->doUpdate($id, $data);
            if (!$status) {
                throw new BusinessException($msg);
            }

            // 处理活动模块
            if (! empty($data['modules'])) {
                $modules = json_decode($data['modules'], true);

                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new BusinessException('活动模块数据格式错误');
                }

                $model = MemberActivityModuleRelationModel::getInstance();
                foreach ($modules as $module) {
                    $moduleCode = $module['module_code'] ?? '';
                    $moduleRelationId = (int) ($module['module_relation_id'] ?? 0);

                    // if (empty($moduleRelationId)) {
                    //     return [false, '活动模块ID不能为空'];
                    // }

                    // $relationData = $model->getOneById($moduleRelationId);
                    // if (empty($relationData)) {
                    //     throw new BusinessException(sprintf('活动模块ID[%s]不存在', $moduleRelationId));
                    // }

                    // 验证模块编码
                    if (empty($moduleCode)) {
                        throw new BusinessException('缺少模块编号[modules.*.module_code]');
                    }

                    // 检查处理器是否存在
                    if (!isset($this->moduleHandlers[$moduleCode])) {
                        throw new BusinessException("未定义的模块类型: {$moduleCode}");
                    }

                    // 调用对应的处理器
                    $handler = $this->moduleHandlers[$moduleCode];
                    list($status, $relationId, $msg) = $this->$handler($id, $module, $moduleRelationId);

                    if (!$status) {
                        throw new BusinessException($msg);
                    }
                }
            }

            $trans->commit();
            // 清除活动模块缓存
            MemberActivityModuleRelationModel::getInstance()->__delRedisKey($id);
            $memberActivityModel->__delDetailCache($id);
            return [true, '更新活动成功'];
        } catch (BusinessException $e) {
            return [false, $e->getMessage()];
        } catch (\Throwable $e) {
            $trans->rollBack();
            CUtil::debug('更新活动失败: ' . $e->getMessage(), 'err.MemberActivityService.updateV2');
            return [false, '更新活动失败'];
        }
    }

    /**
     * 打包基础模块数据
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @return array
     */
    private function packBaseModuleData(int $activity_id, array $data): array
    {
        return [
                'activity_id'   => $activity_id,
                'module_res_id' => (int) ($data['module_res_id'] ?? 0),
                'title'         => htmlspecialchars(trim($data['module_title'] ?? '')),
                'summary'       => htmlspecialchars(trim($data['module_summary'] ?? '')),
                'start_time'    => (int) ($data['module_start_time'] ?? 0),
                'end_time'      => (int) ($data['module_end_time'] ?? 0),
                'sort'          => (int) ($data['module_sort'] ?? 0),
        ];
    }


    /**
     * @param int $activity_id
     * @param array $data
     * @param int $module_relation_id
     * @return array
     */
    private function saveSnapUpModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $data['module_data'] ?? [];
        foreach ($moduleData as $goods){
            if (empty($goods['extra']['invite_number']) || !CUtil::isNumeric($goods['extra']['invite_number'])) {
                return [false, 0, '邀请人数配置有误，请检查！'];
            }

            if (empty($goods['extra']['buy_goods_number']) || !CUtil::isNumeric($goods['extra']['buy_goods_number'])) {
                return [false, 0, '购买商品数量配置有误，请检查！'];
            }
        }
        return $this->saveModuleGoods($activity_id, $data, $module_relation_id);
    }

    private function saveHalfPriceBuyModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra      = $data['module_data'] ?? [];


        if (empty($extra['shopping_price']) || !CUtil::isNumeric($extra['shopping_price'])) {
            return [false, 0, '原价配置有误，请检查！'];
        }

        if (empty($extra['shopping_price_rate']) || !CUtil::isNumeric($extra['shopping_price_rate'])) {
            return [false, 0, '折扣配置有误，请检查！'];
        }

        $moduleData['extra'] = CUtil::jsonEncode($extra);

        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }

    private function saveConsumeMoneyBuyModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra      = $data['module_data'] ?? [];

        if (empty($extra['consume_money_return']) || !CUtil::isNumeric($extra['consume_money_return'])) {
            return [false, 0, '返还消费金配置有误，请检查！'];
        }
        
        if (empty($extra['consume_money_rate']) || !CUtil::isNumeric($extra['consume_money_rate'])) {
            return [false, 0, '消费金比例配置有误，请检查！'];
        }
        
        $moduleData['extra'] = CUtil::jsonEncode($extra);
        
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }

    /**
     * 保存活动模块数据
     * @param array $moduleData 模块数据
     * @param int $moduleRelationId 活动模块ID
     * @return array
     */
    public function saveModuleRelationData(array $moduleData, int $moduleRelationId = 0): array
    {
        if (empty($moduleData['title'])) {
            return [false, 0, '活动模块标题不能为空'];
        }

        if ($moduleData['end_time'] < $moduleData['start_time']) {
            return [false, 0, '活动模块结束时间不能小于开始时间'];
        }

        // **特殊处理：红包模块不允许重复添加**
        if ($moduleData['module_res_id'] == self::MODULE_RED_ID['RED_ENVELOPE']['id']) {
            $model = MemberActivityModuleRelationModel::getInstance();
            if ($model->isExistsModuleResId($moduleData['activity_id'], $moduleData['module_res_id'], $moduleRelationId)) {
                return [false, 0, '不允许添加重复的活动模块'];
            }
        }

        // 新增或更新活动模块
        $memberActivityModuleRelationModel = MemberActivityModuleRelationModel::getInstance();
        if (!empty($moduleRelationId)) {
            return $memberActivityModuleRelationModel->doUpdate($moduleRelationId, $moduleData);
        }
        return $memberActivityModuleRelationModel->doCreate($moduleData);
    }

    /**
     * 保存模块-大模块标题
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveBigModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);

        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }

    /**
     * 保存模块-积分打卡
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveCheckInModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }

        $beginDate = $extra['checkin_start_time'] ?? 0;
        $endDate = $extra['checkin_end_time'] ?? 0;
        $dailyPoints = $extra['checkin_daily_points'] ?? 0;
        $checkDays = $extra['checkin_days'] ?? 0;
        $checkInRewardPoints = $extra['checkin_reward_points'] ?? 0;

        if (empty($beginDate) || empty($endDate)) {
            return [false, 0, '请设置打卡时间'];
        }

        if ($beginDate >= $endDate) {
            return [false, 0, '打卡开始时间不能大于或等于结束时间'];
        }

        if (empty($dailyPoints)) {
            return [false, 0, '每日打卡积分不能为空'];
        }

        if (! CUtil::isNumeric($dailyPoints)) {
            return [false, 0, '每日打卡积分只能是数字'];
        }

        if (empty($checkDays)) {
            return [false, 0, '打卡天数不能为空'];
        }

        if (! CUtil::isNumeric($checkDays)) {
            return [false, 0, '打卡天数只能是数字'];
        }

        if (empty($checkInRewardPoints)) {
            return [false, 0, '奖励积分不能为空'];
        }

        if (! CUtil::isNumeric($checkInRewardPoints)) {
            return [false, 0, '奖励积分只能是数字'];
        }

        // 会员中心返回的ID
        list($status, $member_center_save_id, $msg) = $this->getMemberCenterSaveId($module_relation_id);
        if (! $status) {
            return [false, 0, $msg];
        }

        // 调用会员中心接口，返回ID并保存
        list($status, $id) = MemberCenter::factory()->memberActivityCheckin($member_center_save_id, $moduleData['title'], $beginDate, $endDate, $dailyPoints, $checkDays, $checkInRewardPoints);
        if (! $status) {
            return [false, 0, '保存积分打卡活动失败'];
        }

        $extra['member_center_save_id'] = (string) $id;
        $moduleData['extra'] = CUtil::jsonEncode($extra);

        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }
    
    /**
     * 保存模块-购物返积分
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveReturnPointsModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }

        $beginDate = $extra['return_start_time'] ?? 0;
        $endDate = $extra['return_end_time'] ?? 0;
        
        if (empty($beginDate) || empty($endDate)) {
            return [false, 0, '请设置返积分时间'];
        }

        if ($beginDate >= $endDate) {
            return [false, 0, '购物返积分开始时间不能大于或等于结束时间'];
        }

        $beginDate = mb_strlen($beginDate) > 10 ? $beginDate : $beginDate * 1000;
        $endDate = mb_strlen($endDate) > 10 ? $endDate : $endDate * 1000;
        $returnMultiple = $extra['return_multiple'] ?? 0;

        if (empty($returnMultiple)) {
            return [false, 0, '返积分倍数不能为空'];
        }

        if (! CUtil::isNumeric($returnMultiple)) {
            return [false, 0, '返积分倍数只能是数字'];
        }

        // 会员中心返回的ID
        list($status, $member_center_save_id, $msg) = $this->getMemberCenterSaveId($module_relation_id);
        if (! $status) {
            return [false, 0, $msg];
        }

        // 调用会员中心接口，返回ID并保存
        list($status, $id) = MemberCenter::factory()->memberActivityReturnPoints($member_center_save_id, $moduleData['title'], $beginDate, $endDate, $returnMultiple);
        if (! $status) {
            return [false, 0, '创建购物返积分活动失败'];
        }
        $extra['member_center_save_id'] = (string) $id;
        $moduleData['extra'] = CUtil::jsonEncode($extra);

        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }
    
    /**
     * 查询会员中心保存的ID
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function getMemberCenterSaveId(int $module_relation_id): array
    {
        if (empty($module_relation_id)) {
            return [true, 0, 'success'];
        }

        $memberActivityModuleRelationModel = MemberActivityModuleRelationModel::getInstance();
        // 查询已保存的member_center_save_id
        $query = $memberActivityModuleRelationModel->find();
        $res = $query->where(['id' => $module_relation_id, 'delete_time' => 0])->limit(1)->asArray()->one();
        if (empty($res)) {
            return [false, 0, sprintf('活动模块ID[%s]不存在', $module_relation_id)];
        }
        $extra = json_decode($res['extra'], true);
        $member_center_save_id = (string) ($extra['member_center_save_id'] ?? 0);
        return [true, $member_center_save_id, 'success'];
    }
    
    /**
     * 保存模块-广告位
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveAdPositionModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }
        
        if (empty($extra['ad_btn_name'])) {
            return [false, 0, '请设置广告位按钮名称'];
        }
        
        if (empty($extra['ad_url'])) {
            return [false, 0, '请设置广告位跳转链接'];
        }

        if (empty($extra['ad_image'])) {
            return [false, 0, '请设置广告位图片'];
        }

        $extra['ad_link'] = $extra['ad_url'];
        $moduleData['extra'] = CUtil::jsonEncode($extra);
        
        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }
    
    /**
     * 保存模块-抽奖活动
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveDrawModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }

        if (empty($extra['draw_start_time']) || empty($extra['draw_end_time'])) {
            return [false, 0, '抽奖时间不能为空'];
        }

        if (! CUtil::isValidTimeStamp($extra['draw_start_time']) || ! CUtil::isValidTimeStamp($extra['draw_end_time'])) {
            return [false, 0, '抽奖时间格式错误'];
        }
        if ($extra['draw_start_time'] >= $extra['draw_end_time']) {
            return [false, 0, '抽奖结束时间不能小于或等于开始时间'];
        }

        // 插入抽奖活动表
        $drawActivityModel = DrawActivityModel::getInstance();
        $drawTask = $extra['draw_task'] ?? [];
        $daily_free_times = 1;

        foreach ($drawTask as $key => $value) {
            if (! empty($value['data_id']) && ! CUtil::isNumeric($value['data_id'])) {
                return [false, 0, 'data_id只能是数字'];
            }

            if (empty($value['task_id'])) {
                return [false, 0, '任务ID不能为空'];
            }

            if (! CUtil::isNumeric($value['task_id'])) {
                return [false, 0, '任务ID只能是数字'];
            }

            if (empty($value['task_name'])) {
                return [false, 0, '任务名称不能为空'];
            }

            if (empty($value['task_code'])) {
                return [false, 0, '任务编号不能为空'];
            }

            if (! empty($value['task_limit']) && ! CUtil::isNumeric($value['task_limit'])) {
                return [false, 0, '任务限制次数只能是数字'];
            }

            if (empty($value['task_limit_type']) || ! in_array($value['task_limit_type'], [1, 2])) {
                return [false, 0, 'task_limit_type数据范围不合法'];
            }


            if ($value['task_code'] == 'INVITE_NEW_USER') {
                $giftTimes = $value['extra']['gift_times'] ?? [];
                if (empty($giftTimes)) {
                    return [false, 0, 'extra.*.gift_times不能为空'];
                }

                // foreach ($giftTimes as $v) {
                //     if (! CUtil::isNumeric($v['invite_person'])) {
                //         return [false, 0, 'invite_person只能是数字'];
                //     }
                //
                //     if (! CUtil::isNumeric($v['gift_times'])) {
                //         return [false, 0, 'gift_times只能是数字'];
                //     }
                // }

                $defaultDrawTimes = $giftTimes[0]['gift_times'] ?? 1;
                $specialInvitedNum = $giftTimes[1]['invite_person'] ?? 3;
                $extraDrawTimes = $giftTimes[1]['gift_times'] ?? 5;
                if (empty($defaultDrawTimes)) {
                    return [false, 0, '赠送次数不能为空'];
                }

                if (! CUtil::isNumeric($defaultDrawTimes)) {
                    return [false, 0, '赠送次数只能是数字'];
                }

                if (empty($specialInvitedNum)) {
                    return [false, 0, '邀请人数不能为空'];
                }

                if (! CUtil::isNumeric($specialInvitedNum)) {
                    return [false, 0, '邀请人数只能是数字'];
                }

                if (empty($extraDrawTimes)) {
                    return [false, 0, '额外赠送次数不能为空'];
                }

                if (! CUtil::isNumeric($extraDrawTimes)) {
                    return [false, 0, '额外赠送次数只能是数字'];
                }

            } else {
                $giftTimes = $value['extra']['gift_times'] ?? 1;
                
                if ($value['task_code'] == 'GOLD_DRAW') {
                    // 元转分
                    $giftTimes = (int) bcmul($giftTimes, 100);

                    if (empty($giftTimes)) {
                        return [false, 0, '消耗金币不能为空'];
                    }

                    if (! CUtil::isNumeric($giftTimes)) {
                        return [false, 0, '消耗金币只能是数字'];
                    }

                    $drawTask[$key]['consume_type'] = 2;   // 金币抽奖消耗类型为2
                    $drawTask[$key]['consume_gold'] = $giftTimes;   // 金币抽奖消耗金币数
                    $drawTask[$key]['draw_times'] = 0;   // 消耗金币的，抽奖次数置为0
                    $drawTask[$key]['extra']['gift_times'] = $giftTimes;
                } else {
                    if (empty($giftTimes)) {
                        return [false, 0, '赠送次数不能为空'];
                    }
                    
                    if (! CUtil::isNumeric($giftTimes)) {
                        return [false, 0, '赠送次数只能是数字'];
                    }
                }
            }

            // 获取每日免费抽奖次数
            if ($value['task_code'] == 'DAILY_FREE') {
                $daily_free_times = (int) ($value['extra']['gift_times'] ?? 1);
            }
        }
        
        $extra['draw_task'] = $drawTask;
        $moduleData['extra'] = CUtil::jsonEncode($extra);

        // 保存活动模块数据
        list($status, $relation_id, $msg) = $this->saveModuleRelationData($moduleData, $module_relation_id);
        
        if (! $status) {
            return [false, 0, $msg];
        }

        // $exists = $drawActivityModel->checkDrawActivityNameExists($moduleData['title']);
        if (! empty($module_relation_id)) {
            // if (! empty($exists) && $exists['module_relation_id'] != $module_relation_id) {
            //     return [false, 0, '已存在重复的抽奖活动名称'];
            // }
            list($status, $draw_activity_id, $msg) = $drawActivityModel->doUpdate($module_relation_id, [
                'module_relation_id' => $module_relation_id,
                'name' => $moduleData['title'],
                'start_time' => $extra['draw_start_time'],
                'end_time' => $extra['draw_end_time'],
                'daily_free_times' => $daily_free_times,
                'desc' => '',
                'status' => 1,
            ]);
            
            $drawActivityModel->__delActivityDetailByAcId($draw_activity_id);
        } else {
            // if ($exists) {
            //     return [false, 0, '已存在重复的抽奖活动名称'];
            // }
            list($status, $draw_activity_id, $msg) = $drawActivityModel->doCreate([
                'module_relation_id' => $relation_id,
                'name' => $moduleData['title'],
                'start_time' => $extra['draw_start_time'],
                'end_time' => $extra['draw_end_time'],
                'daily_free_times' => $daily_free_times,
                'desc' => '',
                'status' => 1,
            ]);
        }

        if (! $status) {
            return [false, 0, $msg];
        }

        // 更新抽奖活动ID到活动模块表
        if (empty($extra['draw_activity_id'])) {
            $extra['draw_activity_id'] = $draw_activity_id;
            // $moduleData['extra'] = CUtil::jsonEncode($extra);
            // list($status, $relation_id, $msg) = $this->saveModuleRelationData($moduleData, $relation_id);
            // if (! $status) {
            //     return [false, 0, $msg];
            // }
        }
 
        // 插入或更新抽奖活动任务、奖品表
        $drawPrizes = $extra['draw_prize'] ?? [];
        $drawPrizeModel = DrawPrizeModel::getInstance();
        $drawActivityTaskModel = DrawActivityTaskModel::getInstance();
        $drawActivityPrizeModel = DrawActivityPrizeModel::getInstance();
        if (empty($drawPrizes)) {
            return [false, 0, '奖品不能为空'];
        }

        // 校验中奖率
        $totalDrawRate = array_reduce($drawPrizes, function ($carry, $item) {
            $rate = bcmul((string) $item['prize_rate'], '10000');
            return bcadd((string) $carry, $rate);
        }, 0);
        if ($totalDrawRate != 1000000) {
            return [false, 0, '中奖概率错误，总概率必须等于100'];
        }

        if (! empty($module_relation_id)) {
            // 已存在IDs
            $oldDataIds = $drawActivityTaskModel->getIdsByRelationId($module_relation_id);
            
            // 待更新IDs
            $editDataIds = array_filter(array_column($drawTask, 'data_id'), function ($item) {
                return ! empty($item);
            });

            // 待删除IDs
            $delDataIds = array_diff($oldDataIds, $editDataIds);
            if (! empty($delDataIds)) {
                list($status, $msg) = $drawActivityTaskModel->doDelete($delDataIds);
                if (! $status) {
                    return [false, 0, $msg];
                }
            }
        }

        // 遍历抽奖活动任务
        $taskData = [];
        foreach ($drawTask as $item) {
            if ($item['task_code'] == 'INVITE_NEW_USER') {
                $item['extra']['gift_times'][0]['invite_person'] = 1;
            }

            $draw_times = (int) ($item['extra']['gift_times'] ?? 1);
            $tmp = [
                'activity_id' => $draw_activity_id,
                'module_relation_id' => $relation_id,
                'task_id' => $item['task_id'] ?? 0,
                'task_limit_type' => $item['task_limit_type'] ?? 0, //任务限制周期类型：1每天 2活动期间
                'task_limit' => $item['task_limit'] ?? 1,
                'draw_times' => $item['task_code'] == 'INVITE_NEW_USER' ? 1 : $draw_times,
                'consume_type' => 1,
                'consume_gold' => 0,
                'extra' => CUtil::jsonEncode($item),
                'ctime' => time(),
                'utime' => time(),
            ];
            
            // 金币抽奖
            if ($item['task_code'] == 'GOLD_DRAW') {
                $tmp['consume_type'] = 2;   // 抽奖消耗类型为2
                $tmp['consume_gold'] = $draw_times;   // 抽奖消耗的消费金
                $item['consume_gold'] = $draw_times;  // 抽奖消耗的消费金
                $tmp['draw_times'] = 0;   // 消耗消费金的，抽奖次数置为0
                $tmp['extra'] = CUtil::jsonEncode($item);
            }

            if (! empty($module_relation_id)) {
                // 添加或更新
                if (empty($item['data_id'])) {
                    // 插入抽奖活动任务表
                    $taskData[] = $tmp;
                } else {
                    // 更新抽奖活动任务表
                    list($status, $msg) = $drawActivityTaskModel->doUpdate((int) $item['data_id'], [
                        'task_id' => $tmp['task_id'],
                        'task_limit_type' => $tmp['task_limit_type'],
                        'task_limit' => $tmp['task_limit'],
                        'draw_times' => $tmp['draw_times'],
                        'consume_type' => $tmp['consume_type'],
                        'consume_gold' => $tmp['consume_gold'],
                        'extra' => $tmp['extra'],
                    ]);
                    if (! $status) {
                        return [false, 0, $msg];
                    }
                    
                }
            } else {
                // 插入抽奖活动任务表
                $taskData[] = $tmp;
            }
            
            $drawActivityTaskModel->__delActivityIdByTaskIdKey($item['task_id'] ?? 0);
            $drawActivityTaskModel->__delTaskIdsByAcIdKey($draw_activity_id);
        }

        if (! empty($taskData)) {
            // 可能是新增，也可能是编辑时新增
            list($status, $msg) = $drawActivityTaskModel->doCreate($taskData);
        } elseif (! empty($module_relation_id)) {
            // 如果taskData为空，则为编辑但没新增
            list($status, $msg) = [true, 'success'];
        } else {
            list($status, $msg) = [false, '数据不能为空'];
        }
        
        if (! $status) {
            return [false, 0, $msg];
        }

        if (! empty($module_relation_id)) {
            // 已存在IDs
            $oldPrizeIds = $drawPrizeModel->getIdsByRelationId($module_relation_id);

            // 待更新IDs
            $editPrizeIds = array_filter(array_column($drawPrizes, 'prize_id'), function ($item) {
                return ! empty($item);
            });

            // 待删除IDs
            $delPrizeIds = array_diff($oldPrizeIds, $editPrizeIds);
            if (! empty($delPrizeIds)) {
                list($status, $msg) = $drawPrizeModel->doDelete($delPrizeIds);
                if (! $status) {
                    return [false, 0, $msg];
                }
            }

            // 已存在IDs
            $oldDataIds = $drawActivityPrizeModel->getIdsByRelationId($module_relation_id);
            
            // 待更新IDs
            $editDataIds = array_filter(array_column($drawPrizes, 'data_id'), function ($item) {
                return ! empty($item);
            });
            
            // 待删除IDs
            $delDataIds = array_diff($oldDataIds, $editDataIds);
            if (! empty($delDataIds)) {
                list($status, $msg) = $drawActivityPrizeModel->doDelete($delDataIds);
                if (! $status) {
                    return [false, 0, $msg];
                }
            }

            $drawActivityPrizeModel->resetDefaultPrize($module_relation_id);
        }

        // 遍历抽奖活动奖品
        if (! in_array(1, array_unique(array_column($drawPrizes, 'is_default')))) {
            return [false, 0, '必须设置一个默认奖品'];
        }

        foreach (array_count_values(array_column($drawPrizes, 'is_default')) as $count_key => $count_value) {
            if ($count_key == 1 && $count_value > 1) {
                return [false, 0, '只能设置一个默认奖品'];
            }
        }

        foreach ($drawPrizes as $key => $drawPrize) {
            if (empty($drawPrize['prize_name'])) {
                return [false, 0, '奖品名称不能为空'];
            }

             if (empty($drawPrize['prize_image'])) {
                 return [false, 0, '奖品图片不能为空'];
             }

            if (! empty($drawPrize['prize_num']) && ! CUtil::isNumeric($drawPrize['prize_num'])) {
                return [false, 0, '奖品库存只能是数字'];
            }

            if (! CUtil::isNumeric($drawPrize['prize_rate'], true, true)) {
                return [false, 0, '奖品概率只能是整数或小数'];
            }

            if (! empty($drawPrize['prize_limit']) && ! CUtil::isNumeric($drawPrize['prize_limit'])) {
                return [false, 0, '奖品中奖次数限制只能是数字'];
            }

            if (! empty($module_relation_id)) {
                if (! empty($drawPrize['data_id'])) {
                    // 更新抽奖奖品表
                    list($status, $prize_id, $msg) = $drawPrizeModel->doUpdate((int) $drawPrize['prize_id'], [
                        'name' => $drawPrize['prize_name'],
                        'type' => $drawPrize['prize_type'] ?? 0,
                        'value' => $drawPrize['prize_value'] ?? '',
                        'image' => $drawPrize['prize_image'] ?? '',
                    ]);

                    if (! $status) {
                        return [false, 0, $msg];
                    }

                    $drawPrizes[$key]['prize_id'] = $prize_id;

                    // 更新抽奖活动奖品表
                    list($status, $activity_prize_id, $msg) = $drawActivityPrizeModel->doUpdate((int) $drawPrize['data_id'], [
                        'prize_num' => empty($drawPrize['prize_num']) ? 0 : $drawPrize['prize_num'],
                        'prize_limit' => empty($drawPrize['prize_limit']) ? 0 : $drawPrize['prize_limit'],
                        'rate' => $drawPrize['prize_rate'] ?? 0,
                        'is_default' => $drawPrize['is_default'] ?? 0,
                    ]);

                    if (! $status) {
                        return [false, 0, $msg];
                    }
                }
            }

            // 两种情况新增，一种是新增，一种是编辑时新增
            if (empty($module_relation_id) || empty($drawPrize['data_id'])) {
                list($status, $prize_id, $msg) = $drawPrizeModel->doCreate([
                    'activity_id' => $draw_activity_id,
                    'module_relation_id' => $relation_id,
                    'name' => $drawPrize['prize_name'],
                    'type' => $drawPrize['prize_type'] ?? 0,
                    'value' => $drawPrize['prize_value'] ?? '',
                    'image' => $drawPrize['prize_image'] ?? '',
                    'default_image' => '',
                    'active_image' => '',
                    'desc' => '',
                    'remark' => '',
                    'ctime' => time(),
                    'utime' => time(),
                ]);

                if (! $status) {
                    return [false, 0, $msg];
                }

                $drawPrizes[$key]['prize_id'] = $prize_id;

                // 插入抽奖活动奖品表
                list($status, $activity_prize_id, $msg) = $drawActivityPrizeModel->doCreate([
                    'activity_id' => $draw_activity_id,
                    'module_relation_id' => $relation_id,
                    'prize_id' => $prize_id,
                    'prize_num' => empty($drawPrize['prize_num']) ? 0 : $drawPrize['prize_num'],
                    'prize_limit' => empty($drawPrize['prize_limit']) ? 0 : $drawPrize['prize_limit'],
                    'rate' => $drawPrize['prize_rate'] ?? 0,
                    'is_default' => $drawPrize['is_default'] ?? 0,
                    'ctime' => time(),
                    'utime' => time(),
                ]);

                if (! $status) {
                    return [false, 0, $msg];
                }
            }
        }

        $extra['draw_prize'] = $drawPrizes;
        $moduleData['extra'] = CUtil::jsonEncode($extra);
        $moduleData['update_time'] = time() + 1;
        // 更新活动模块数据
        list($status, $relation_id, $msg) = $this->saveModuleRelationData($moduleData, $relation_id);
        if (! $status) {
            return [false, 0, $msg];
        }

        $drawActivityPrizeModel->__deletePrizeListCache($draw_activity_id);
        $prizeIds = array_unique(array_column($drawPrizes, 'prize_id'));
        $drawPrizeModel->__delPrizeListByIdKey($prizeIds);

        return [true, $relation_id, $msg];
    }
    
    /**
     * 保存模块-新人入会福利
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveNewPersonModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        
        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }
    
    /**
     * 保存模块-机器推荐
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveRecommendGoodsModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        return $this->saveModuleGoods($activity_id, $data, $module_relation_id);
    }
    
    /**
     * 保存模块-优惠券配置
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveCouponModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }

        if (empty($extra['coupon_activity_id'])) {
            return [false, 0, '优惠券活动ID不能为空'];
        }

        if (! CUtil::isNumeric($extra['coupon_activity_id'])) {
            return [false, 0, '优惠券活动ID只能是数字'];
        }

        if (empty($extra['coupon'])) {
            return [false, 0, '请设置活动优惠券'];
        }

        foreach ($extra['coupon'] as $item) {
            if (empty($item['coupon_id'])) {
                return [false, 0, '优惠券ID不能为空'];
            }

            if (! CUtil::isNumeric($item['coupon_id'])) {
                return [false, 0, '优惠券ID只能是数字'];
            }

            if (empty($item['coupon_name'])) {
                return [false, 0, '优惠券名称不能为空'];
            }
        }

        $moduleData['extra'] = CUtil::jsonEncode($extra);

        // 保存活动模块数据
        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }
    
    /**
     * 保存模块-配件专区
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function savePartGoodsModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        return $this->saveModuleGoods($activity_id, $data, $module_relation_id);
    }
    
    /**
     * 保存模块-积分兑换
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function savePointsExchangeModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        return $this->saveModuleGoods($activity_id, $data, $module_relation_id);
    }

    /**
     * 保存模块-红包
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    public function saveRedEnvelopeModule(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra      = $data['module_data'] ?? [];


        if (empty($extra['collect_start_time']) || empty($extra['collect_end_time'])) {
            return [false, 0, '领取时间不能为空'];
        }

        if (!CUtil::isValidTimeStamp($extra['collect_start_time']) || !CUtil::isValidTimeStamp($extra['collect_end_time'])) {
            return [false, 0, '领取时间格式错误'];
        }

        if ($extra['collect_start_time'] >= $extra['collect_end_time']) {
            return [false, 0, '领取结束时间不能小于或等于开始时间'];
        }

        if (empty($extra['red_envelope_image'])) {
            return [false, 0, '红包封面不能为空'];
        }

        if (empty($extra['is_check_user']) || !in_array($extra['is_check_user'], [self::USER_TYPE['ALL'], self::USER_TYPE['NEW_USER']])) {
            return [false, 0, '请选择参与用户类型'];
        }

        $moduleData['extra'] = CUtil::jsonEncode($extra);

        return $this->saveModuleRelationData($moduleData, $module_relation_id);
    }

    /**
     * 保存活动商品
     * @param int $activity_id 活动ID
     * @param array $data 模块数据
     * @param int $module_relation_id 活动模块ID
     * @return array
     */
    private function saveModuleGoods(int $activity_id, array $data, int $module_relation_id = 0): array
    {
        $moduleData = $this->packBaseModuleData($activity_id, $data);
        $extra      = $data['module_data'] ?? [];
        if (empty($extra)) {
            return [false, 0, '模块扩展数据不能为空'];
        }

        $moduleData['extra'] = CUtil::jsonEncode($extra);

        // 保存活动模块数据
        list($status, $relation_id, $msg) = $this->saveModuleRelationData($moduleData, $module_relation_id);

        if (!$status) {
            return [false, 0, $msg];
        }

        $memberActivityModuleGoodsModel = MemberActivityModuleGoodsModel::getInstance();
        $goodsData                      = [];

        if (!empty($module_relation_id)) {
            // 已存在IDs
            $oldDataIds = $memberActivityModuleGoodsModel->getIdsByRelationId($module_relation_id);

            // 待更新IDs
            $editDataIds = array_filter(array_column($extra, 'data_id'), function ($item) {
                return !empty($item);
            });

            // 待删除IDs
            $delIds = array_diff($oldDataIds, $editDataIds);
            if (!empty($delIds)) {
                list($status, $msg) = $memberActivityModuleGoodsModel->doDelete($delIds);
                if (!$status) {
                    return [false, 0, $msg];
                }
            }
        }

        foreach ($extra as $item) {
            if (empty($item['goods_id'])) {
                return [false, 0, '请选择商品'];
            }

            if (empty($item['module_type']) || !in_array($item['module_type'],self::MODULE_TYPE)) {
                return [false, 0, '模块类型不能为空/不合法'];
            }

            if (empty($item['goods_name'])) {
                return [false, 0, '商品名称不能为空'];
            }

            if ($item['module_type'] == self::MODULE_TYPE['PART_GOODS']){
                if (empty($item['extra']['start_time']) || empty($item['extra']['end_time'])) {
                    return [false, 0, '活动时间不能为空'];
                }

                if ($item['extra']['start_time'] >= $item['extra']['end_time']) {
                    return [false, 0, '活动开始时间不能大于或等于结束时间'];
                }
            }


            if ($item['module_type'] != self::MODULE_TYPE['POINTS_EXCHANGE']) {
                if (empty($item['sale_price'])) {
                    return [false, 0, '销售价格不能为空'];
                }

                if (!CUtil::isNumeric($item['sale_price'], true, true)) {
                    return [false, 0, '销售价格只能是数字'];
                }
            } else {
                if (!empty($item['free_periods']) && !CUtil::isNumeric($item['sale_price'], true, true)) {
                    return [false, 0, '销售价格只能是数字'];
                }
            }

            if (!empty($item['free_periods']) && !CUtil::isNumeric($item['free_periods'])) {
                return [false, 0, '免息期数只能是数字'];
            }

            if (!empty($item['trade_subsidy']) && !CUtil::isNumeric($item['trade_subsidy'], true, true)) {
                return [false, 0, '换新补贴只能是数字'];
            }

            if (!empty($item['sale_points']) && !CUtil::isNumeric($item['sale_points'])) {
                return [false, 0, '销售积分只能是数字'];
            }

            $tmp = [
                    'module_relation_id' => $relation_id,
                    'module_type'        => $item['module_type'] ?? 0, // 模块类型:1-机器推荐 2-配件专区 3-积分兑换
                    'category_id'        => $item['category_id'] ?? 0,
                    'category_name'      => $item['category_name'] ?? '',
                    'goods_id'           => $item['goods_id'],
                    'goods_name'         => $item['goods_name'],
                    'show_name'          => $item['show_name'] ?? '',
                    'goods_sku'          => $item['goods_sku'] ?? '',
                    'goods_image'        => $item['goods_image'] ?? '',
                    'sale_price'         => $item['sale_price'] ?? 0,
                    'free_periods'       => $item['free_periods'] ?? 0,
                    'trade_subsidy'      => $item['trade_subsidy'] ?? 0,
                    'sale_points'        => $item['sale_points'] ?? 0,
                    'sort_order'         => $item['sort_order'] ?? 0,
                    'status'             => $item['status'] ?? 1,
                    'extra'              => null,
                    'create_time'        => time(),
                    'update_time'        => time(),
            ];

            if ($tmp['module_type'] == self::MODULE_TYPE['PART_GOODS']) {
                if (empty($item['extra'])) {
                    return [false, 0, '配件专区需要设置秒杀配置'];
                }
                $tmp['extra'] = CUtil::jsonEncode($item['extra']);
            } elseif ($tmp['module_type'] == self::MODULE_TYPE['POINTS_EXCHANGE']) {
                $tmp['extra'] = CUtil::jsonEncode([
                        'old_sale_price'  => $item['old_sale_price'] ?? 0,
                        'old_sale_points' => $item['old_sale_points'] ?? 0,
                ]);
            }

            if (!empty($module_relation_id)) {
                // 添加或更新
                if (empty($item['data_id'])) {
                    // 添加
                    $goodsData[] = $tmp;
                } else {
                    // 更新
                    unset($goodsData['module_relation_id']);
                    unset($goodsData['create_time']);
                    unset($goodsData['update_time']);
                    list($status, $msg) = $memberActivityModuleGoodsModel->doUpdate((int) $item['data_id'], $tmp);

                    if (!$status) {
                        return [false, 0, $msg];
                    }
                }
            } else {
                // 添加
                $goodsData[] = $tmp;
            }
        }

        if (!empty($goodsData)) {
            // 可能是新增，也可能是编辑时新增
            list($status, $msg) = $memberActivityModuleGoodsModel->doCreate($goodsData);
        } elseif (!empty($module_relation_id)) {
            // 如果goodsData为空，则为编辑但没新增
            list($status, $msg) = [true, 'success'];
        } else {
            list($status, $msg) = [false, '数据不能为空'];
        }

        if (!$status) {
            return [false, 0, $msg];
        }

        return [true, $relation_id, $msg];
    }

    /**
     * 获取任务列表
     * @return array|mixed|\yii\db\DataReader
     */
    public function getTaskList()
    {
        $model = DrawTaskModel::getInstance();
        return $model->getTaskList();
    }
    
    /**
     * 删除活动
     * @param array $ids 活动IDs
     * @return array
     */
    public function delete(array $ids): array
    {
        if (empty($ids)) {
            return [false, '活动IDs不能为空'];
        }

        $model = MemberActivityModel::getInstance();
        foreach ($ids as $id) {
            $res = $model->getInfo($id);
            if (empty($res)) {
                return [false, sprintf('活动ID[%s]不存在', $id)];
            }
            
            if (time() >= $res['start_time'] && time() <= $res['end_time']) {
                return [false, sprintf('活动[%s]正在进行中，不能删除', $res['title'])];
            }
            $model->__delDetailCache($id);
        }
        
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            list($status, $msg) = $model->doDelete($ids);

            $relationIds = [];
            $relationModel = MemberActivityModuleRelationModel::getInstance();
            foreach ($ids as $id) {
                $aData = MemberActivityModuleRelationModel::getInstance()->getModuleListByActivityId($id, ['id']);
                $relationIds = array_merge($relationIds, array_column($aData, 'id'));
                
            }
            $relationIds = array_unique($relationIds);

            if (! empty($relationIds)) {
                // 删除活动模块
                $relationModel->doDelete($relationIds);
  
                // 删除活动模块资源
                $moduleGoodsIds = [];
                foreach ($relationIds as $relationId) {
                    $goodsIds = MemberActivityModuleGoodsModel::getInstance()->getIdsByRelationId($relationId);
                    $moduleGoodsIds = array_merge($moduleGoodsIds, $goodsIds);
                }

                // 删除活动模块相关的商品数据
                ! empty($moduleGoodsIds) && MemberActivityModuleGoodsModel::getInstance()->doDelete($moduleGoodsIds);
            }
            
            if (! $status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            return [true, '删除成功'];
        } catch (\Throwable $e) {
            $trans->rollback();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 新增活动模块
     * @param array $data
     * @return array
     */
    public function createModuleRelation(array $data): array
    {
        $moduleResId = (int) ($data['module_res_id'] ?? 0);
        $activityId = (int) ($data['activity_id'] ?? 0);
        $moduleData = json_decode($data['module_data'], true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [false, 0, '模块数据格式错误'];
        }

        if (! empty($moduleData)) {
            $data['module_data'] = $moduleData;
        }
        
        if (empty($activityId)) {
            return [false, 0, '活动ID不能为空'];
        }
        
        $activityModel = MemberActivityModel::getInstance();
        $res = $activityModel->getInfo($activityId);
        if (empty($res) || ! empty($res['delete_time'])) {
            return [false, 0, '活动不存在'];
        }

        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $moduleResModel = MemberActivityModuleResourceModel::getInstance();
            $moduleResMap = $moduleResModel->getBatchInfoByModuleIds([$moduleResId]);
            $moduleCode = $moduleResMap[$data['module_res_id']]['module_code'] ?? '';

            // 验证模块编码
            if (empty($moduleCode)) {
                throw new BusinessException('模块编号不能为空');
            }

            // 检查处理器是否存在
            if (! isset($this->moduleHandlers[$moduleCode])) {
                throw new BusinessException("未定义的模块类型: {$moduleCode}");
            }

            // 调用对应的处理器
            $handler = $this->moduleHandlers[$moduleCode];
            list($status, $relationId, $msg) = $this->$handler($activityId, $data);
            
            if (! $status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            return [true, $relationId, '活动模块添加成功'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, 0, $e->getMessage()];
        }
    }

    /**
     * 更新活动模块
     * @param array $data
     * @return array
     * @throws BusinessException
     */
    public function updateModuleRelation(array $data): array
    {
        $moduleResId = (int) ($data['module_res_id'] ?? 0);
        $activityId = (int) ($data['activity_id'] ?? 0);
        $moduleRelationId = (int) ($data['module_relation_id'] ?? 0);
        $moduleData = json_decode($data['module_data'], true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return [false, '模块数据格式错误'];
        }

        if (! empty($moduleData)) {
            $data['module_data'] = $moduleData;
        }

        if (empty($moduleRelationId)) {
            return [false, '活动模块ID不能为空'];
        }
        
        $activityModel = MemberActivityModel::getInstance();
        $res = $activityModel->getInfo($activityId);
        if (empty($res) || ! empty($res['delete_time'])) {
            return [false, '活动不存在'];
        }

        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $model = MemberActivityModuleRelationModel::getInstance();
            $relationData = $model->getOneById($moduleRelationId);
            if (empty($relationData)) {
                throw new BusinessException(sprintf('活动模块ID[%s]不存在', $moduleRelationId));
            }
            
            if ($activityId != $relationData['activity_id']) {
                throw new BusinessException(sprintf('活动模块ID[%s]与当前更新的活动ID不一致', $moduleRelationId));
            }
            
            if ($moduleResId != $relationData['module_res_id']) {
                throw new BusinessException(sprintf('活动模块ID[%s]与当前更新的资源ID不一致', $moduleRelationId));
            }

            $moduleResModel = MemberActivityModuleResourceModel::getInstance();
            $moduleResMap = $moduleResModel->getBatchInfoByModuleIds([$moduleResId]);
            $moduleCode = $moduleResMap[$data['module_res_id']]['module_code'] ?? '';

            // 验证模块编码
            if (empty($moduleCode)) {
                throw new BusinessException('模块编号不能为空');
            }
            
            // 检查处理器是否存在
            if (! isset($this->moduleHandlers[$moduleCode])) {
                throw new BusinessException("未定义的模块类型: {$moduleCode}");
            }
            
            // 调用对应的处理器
            $handler = $this->moduleHandlers[$moduleCode];
            list($status, $relationId, $msg) = $this->$handler($activityId, $data, $moduleRelationId);

            if (! $status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            return [true, '活动模块更新成功'];
        } catch (\Throwable $e) {
            $trans->rollBack();
            return [false, $e->getMessage()];
        }
    }
    
    /**
     * 删除活动模块
     * @param array $ids
     * @return array
     */
    public function deleteModuleRelation(array $ids): array
    {
        if (empty($ids)) {
            return [false, '活动模块IDs不能为空'];
        }
        
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            $model = MemberActivityModuleRelationModel::getInstance();
            list($status, $msg) = $model->doDelete($ids);

            // 删除活动模块
            $moduleGoodsIds = [];
            foreach ($ids as $relationId) {
                $goodsIds = MemberActivityModuleGoodsModel::getInstance()->getIdsByRelationId($relationId);
                $moduleGoodsIds = array_merge($moduleGoodsIds, $goodsIds);
            }

            // 删除活动模块相关的商品数据
            ! empty($moduleGoodsIds) && MemberActivityModuleGoodsModel::getInstance()->doDelete($moduleGoodsIds);

            if (! $status) {
                throw new BusinessException($msg);
            }

            $trans->commit();
            return [true, '删除成功'];
        } catch (\Throwable $e) {
            $trans->rollback();
            return [false, $e->getMessage()];
        }
    }

    /**
     * 更新活动商品状态
     * @param array $post 请求数据
     * @return array
     */
    public function changeModuleGoodsStatus(array $post): array
    {
        $id = $post['id'] ?? 0;
        $status = $post['status'] ?? 0;
        if (empty($id)) {
            return [false, '缺少活动商品ID'];
        }

        $memberActivityModuleGoodsModel = MemberActivityModuleGoodsModel::getInstance();
        list($status, $msg) = $memberActivityModuleGoodsModel->doUpdate($id, ['status' => $status]);
        
        if (! $status) {
            return [false, $msg];
        }

        return [true, 'success'];
    }


    /**
     * 抽奖活动导入券码
     * @param int $acRelationId  此ID为 member_activity_module_relation表中主键ID
     * @param  $file
     * @return array
     */
    public function drawActivity(int $acRelationId, $file): array
    {
        // 验证活动ID
        if (empty($acRelationId)) {
            return [false, '活动ID不能为空或不合法'];
        }
        // 查看活动是否存在
        $model        = MemberActivityModuleRelationModel::getInstance();
        $activityInfo = $model->getOneById($acRelationId);
        if (empty($activityInfo) || !empty($activityInfo['delete_time'])) {
            return [false, '活动不存在或已被删除'];
        }

        if (empty($file)) {
            return [false, '导入失败:文件不存在'];
        }

        $codeList = [];
        // 奖品队列
        $key = AppNRedisKeys::envelopeActivity($acRelationId);
        // 奖品库存
        $key1 = AppNRedisKeys::envelopeActivityStock($acRelationId);

        try {
            $redis = by::redis('core');
            // 解析券码文档
            $spreadsheet = IOFactory::load($file['tmp_name']);
            $sheet       = $spreadsheet->getActiveSheet();

            foreach ($sheet->getRowIterator() as $index => $row) {
                if ($index === 1) continue; // 跳过表头

                $rowData      = [];
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false);

                foreach ($cellIterator as $cell) {
                    $val       = $cell->getValue();
                    $rowData[] = trim($val ?? '');
                }

                // 构建奖品数据，默认取前两列
                $codeList[] = [
                        'name' => $rowData[0] ?? '',
                        'link' => $rowData[1] ?? ''
                ];
            }

            if (empty($codeList)) {
                return [false, '导入失败:文件内容为空'];
            }

            // 打乱奖品顺序
            shuffle($codeList);

            // 开启Redis事务批量写入
            $redis->multi();

            foreach ($codeList as $val) {
                $redis->lpush($key, json_encode($val));
            }
            $redis->exec();

            // 获取当前库存数量
            $codeCount = $redis->get($key1);
            // 记录库存数量，设置24小时过期
            $redis->setex($key1, 86400, count($codeList) + $codeCount);
            return [true, "导入成功: 共导入" . count($codeList) . "条奖品数据"];
        } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
            // 处理Excel解析异常
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.import_red_envelopes");
            return [false, "导入失败"];
        } catch (\RedisException $e) {
            // 处理Redis操作异常
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.import_red_envelopes");
            return [false, "导入失败"];
        } catch (\Exception $e) {
            // 处理其他异常
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.import_red_envelopes");
            return [false, "导入失败"];
        }
    }
    
    /**
     * 获取抽奖表单提交记录
     * @param array $params 请求参数
     * @return array
     */
    public function getDrawFormSubmitRecord(array $params): array
    {
        $res = DrawCustomFormRecordModel::getInstance()->getPageList($params);
        $list = $res['list'] ?? [];
        $dptIds = array_column($list, 'dpr_id');
        
        $prizeRecord = byNew::DrawActivityPrizeRecord()->getListByIds($dptIds);
        $prizeRecordMap = array_column($prizeRecord, null, 'id');
        foreach ($list as $k => $v) {
            $list[$k]['prize_data'] = [];
            if (isset($prizeRecordMap[$v['dpr_id']])) {
                $list[$k]['prize_data'] = $prizeRecordMap[$v['dpr_id']];
            }
        }
        $res['list'] = $list;
        return $res;
    }

    /**
     * 发放中奖奖品
     * @param array $data
     * @return array
     */
    public function drawFormSubmitDelivery(array $data): array
    {
        $id = (int) ($data['id'] ?? 0);
        
        $model = DrawCustomFormRecordModel::getInstance();
        $res = $model->getOne($id);
        if (empty($res)) {
            return [false, '数据不存在'];
        }
        if ($res['status'] == 1) {
            return [false, '已发放，请勿重复操作'];
        }
        list($status, $msg) = $model->doUpdate($id, ['status' => 1]);
        if (! $status) {
            return [false, '操作失败，请稍后再试'];
        }
        return [true, 'success'];
    }
}