<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\CUtil;
use yii\db\ActiveRecord;

/**
 * 评论审核服务
 */
class CommentAuditService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 发送评论奖励
     * @param int $id 审核ID
     * @param string $rewardRemark 奖励备注
     * @param bool $sendType 发送类型 1 手动发送 2 自动发送
     * @return array
     */
    public function sendReward(int $id, string $rewardRemark,$sendType = 1,$user_id=0): array
    {
        // 获取审核内容
        $commentAudit = $this->getCommentAudit($id);
        if (!$commentAudit) {
            return ['code' => -1,'审核内容不存在'];
        }

        if ($commentAudit->is_reward_sent == 1) {
            return ['code' => -1, 'message' => '已发放过奖励'];
        }

        // 获取评论任务
        $commentTask = $this->getCommentTask($commentAudit->comment_task_id);
        if (!$commentTask) {
            return ['code' => -1, 'message' => '任务不存在'];
        }

        if ($sendType==2) {
            if (empty($commentTask->coupon_id)) {
                return ['code' => -1, 'message' => '无法获取到卡券ID'];
            }

            // 发放奖品
            list($status, $msg) = CouponService::getInstance()->send($commentTask->coupon_id, $commentAudit->user_id, '', $commentAudit->phone);
            if (!$status) {
                return ['code' => -1, 'message' => $msg];
            }
        }

        // 更新审核状态
        if (!$this->updateAuditStatus($commentAudit, $rewardRemark,$user_id)) {
            return ['code' => -1, 'message' => '更新状态失败'];
        }

        return ['code' => 1, 'message' => '审核完成'];
    }


    /**
     * 获取评论审核信息
     * @param int $id
     * @return array|ActiveRecord|null
     */
    private function getCommentAudit(int $id)
    {
        return by::CommentAuditModel()::find()->where(['id' => $id])->one();
    }

    /**
     * 获取评论任务信息
     * @param int $taskId
     * @return ActiveRecord|array|null
     */
    private function getCommentTask(int $taskId)
    {
        return by::CommentTaskModel()::find()->where(['id' => $taskId])->one();
    }

    /**
     * 更新审核状态
     * @param $commentAudit
     * @param $rewardRemark
     * @param $user_id
     * @return bool
     */
    private function updateAuditStatus($commentAudit, $rewardRemark,$user_id): bool
    {
        $commentAudit->reward_remark = $rewardRemark;
        $commentAudit->is_reward_sent = 1;
        $commentAudit->reward_time = time();
        $commentAudit->status = 4;
        $commentAudit->audit_user_id = $user_id;
        return $commentAudit->save();
    }
}