<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\enums\banner\BannerEnum;
use app\modules\main\models\BannerModel;
use RedisException;

class BannerService
{
    private static $_instance = NULL;

    protected $bannerModel;

    private function __construct()
    {
        $this->bannerModel = byNew::BannerModel();
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @return array
     * 获取banner投放位置
     */
    public function release(): array
    {
        $dropPosition  = BannerEnum::DROP_POSITION_PLATFORM;
        $formattedData = [];

        foreach ($dropPosition as $key => $positions) {
            $formattedData[$key] = array_map(function ($id, $name) {
                return [
                    'id'   => $id,
                    'name' => $name
                ];
            }, array_keys($positions), $positions);
        }

        return $formattedData;
    }


    public function bannerVersion(): array
    {
        $bannerVersion = BannerEnum::BANNER_VERSION;
        $return       = [];
        foreach ($bannerVersion as $key => $name) {
            $return[] = [
                'id'   => $key,
                'name' => $name,
            ];
        }

        return $return;
    }

    /**
     * @param $post
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws RedisException
     * banner列表
     */
    public function getBannerList($post, int $page = 1, int $page_size = 20): array
    {
        // 获取总数和分页信息
        $count      = $this->bannerModel->getCount($post);
        $pagination = CUtil::getPaginationPages($count, $page_size);

        // 查询当前页的 banner 数据
        $banners = $this->bannerModel->getList($post, $page, $page_size);

        // 获取当前页所有 banner 的详细信息
        $bannerList       = [];
        $platformMapping  = [
            byNew::PlatformModel()::PLATFORM['WX'],
            byNew::PlatformModel()::PLATFORM['APP']
        ];
        $platformCombined = byNew::PlatformModel()::PLATFORM['WX_AND_APP'];

        foreach ($banners as $bannerId) {
            $banner = $this->bannerModel->getOneById($bannerId);
            if (!empty($banner)) {
                $platform = byNew::BannerPlatformModel()->getPlatformByBannerId($bannerId);

                // 检查平台是否为 [WX, APP]，并进行相应转换
                if (empty(array_diff($platformMapping, $platform))) {
                    $banner['platform'] = strval($platformCombined);
                } else {
                    $banner['platform'] = implode(",", $platform);
                }

                $bannerList[] = $banner;
            }
        }

        return [
            'pages' => $pagination,
            'list'  => $bannerList,
        ];
    }




    /**
     * @param $aData
     * @param $admin
     * @return array
     * 后台增改
     * @throws \Exception
     */
    public function save($aData, $admin): array
    {
        // 提取参数
        $id            = CUtil::uint($aData['id'] ?? 0);
        $title         = $aData['title'] ?? "";
        $image         = $aData['image'] ?? "";
        $newImage      = $aData['new_image'] ?? "";
        $jumpUrl       = $aData['jump_url'] ?? "";
        $jumpType      = CUtil::uint($aData['jump_type'] ?? 0);
        $dropPosition  = CUtil::uint($aData['drop_position'] ?? 0);
        $bannerVersion = CUtil::uint($aData['banner_version'] ?? 1);
        $appid         = $aData['appid'] ?? "";
        $vtimeStart    = CUtil::uint($aData['vtime_start'] ?? 0);
        $vtimeEnd      = CUtil::uint($aData['vtime_end'] ?? 0);
        $status        = CUtil::uint($aData['status'] ?? 0);
        $video         = $aData['video'] ?? "";
        $platform      = $aData['platform'] ?? ""; // 平台ID

        // 获取管理员ID
        $admin_id = CUtil::uint($admin['id'] ?? 0);

        // 判断权限
        $check_url = 'back/banner/audit'; // 资源审核权限
        if ($status == BannerModel::STATUS['ING']['CODE'] && by::adminUserModel()->checkAuth($admin, $check_url)) {
            list($ret, $msg) = $this->bannerModel->checkPublishTotal();
            if (!$ret) {
                return [false, $msg];
            }
            $status = BannerModel::STATUS['PASS']['CODE']; // 更新状态为通过审核
        }
        // 保存参数
        $save = [
            'title'          => $title,
            'image'          => $image,
            'new_image'      => $newImage,
            'jump_type'      => $jumpType,
            'jump_url'       => $jumpUrl,
            'drop_position'  => $dropPosition,
            'appid'          => $appid,
            'vtime_start'    => $vtimeStart,
            'vtime_end'      => $vtimeEnd,
            'ctime'          => intval(START_TIME),
            'status'         => $status,
            'admin_id'       => $admin_id,
            'video'          => $video,
            'banner_version' => $bannerVersion,
        ];

        $transaction = by::dbMaster()->beginTransaction();
        try {
            list($status, $saveData) = $this->bannerModel->saveBanner($id, $save);
            if (!$status) {
                throw new \Exception($saveData);
            }

            $id = $id ?: ($saveData['id'] ?? '');

            // 获取数据库中的平台ID列表
            $bannerPlatformIds = byNew::BannerPlatformModel()->getPlatformByBannerId($id);

            // 将前端传来的平台ID字符串转换为数组
            $platformArray = explode(',', $platform);

            // 判断需要新增和删除的平台项
            if ($bannerPlatformIds) {
                $platformToAdd    = array_diff($platformArray, $bannerPlatformIds);
                $platformToRemove = array_diff($bannerPlatformIds, $platformArray);
            } else {
                $platformToAdd    = $platformArray;
                $platformToRemove = [];
            }

            // 批量新增需要添加的项
            if (!empty($platformToAdd)) {
                byNew::BannerPlatformModel()->saveLogs($id, $platformToAdd);
            }

            // 批量删除需要删除的项
            if (!empty($platformToRemove)) {
                byNew::BannerPlatformModel()->deleteLogs($id, $platformToRemove);
            }

            // 提交事务
            $transaction->commit();
            return [true, '保存成功'];
        } catch (\Exception $e) {
            // 回滚事务
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.save_banner');
            return [false, '保存失败'];
        }
    }


    /**
     * @param $id
     * @param $check
     * @return mixed
     * banner审核
     */
    public function audit($id, $check)
    {
        return $this->bannerModel->audit($id, $check);
    }

    /**
     * @param $id
     * @return mixed
     * banner删除
     */
    public function delete($id)
    {
        return $this->bannerModel->del($id);
    }

    /**
     * @param $id
     * @param $sort
     * @return mixed
     * banner排序
     */
    public function sort($id, $sort)
    {
        return $this->bannerModel->sort($id, $sort);
    }

    /**
     * @param $id
     * @param $check
     * @param $user_info
     * @return mixed
     * 提交、撤销审核
     */
    public function subAudit($id, $check, $user_info)
    {
        return $this->bannerModel->subAudit($id, $check, $user_info);
    }
}