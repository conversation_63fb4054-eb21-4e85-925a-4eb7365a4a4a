<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\CUtil;
use app\modules\main\models\PlatformModel;
use app\modules\wares\models\GoodsStockModel;
use app\modules\wares\services\goods\GoodsMainService;
use yii\db\Exception;

/**
 * 商品服务层
 */
class GoodsService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取商品详情
     * @param $id
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getGoodsDetail($id): array
    {
        // 获取商品信息
        $data = by::Gmain()->GetAllOneByGid($id, false);
        if (empty($data)) {
            return [];
        }
        $data = $this->handleGoodsInfo($data);

        // 商品对比
        if ($data['compare_sku'] ?? false) {
            $gmain = by::Gmain();
            $goods = $gmain->GetOneBySku($data['compare_sku']);
            if (empty($goods) || $goods['status'] == $gmain::STATUS['OFF_SALE'] || $goods['is_del'] == $gmain::IS_DEL['yes']) {
                $data['compare_sku'] = "";
            }
        }

        // 商品规格
        if (isset($data['atype']) && $data['atype'] == by::Gtype0()::ATYPE['SPECS']) {
            // 规格属性
            $specs = by::Gspecs()->GetListByGid($id);
            $specs = by::Gspecs()->AttrToName($id, $specs);
            $attr = [];
            foreach ($specs as $spec) {
                if (!empty($spec['attr_cnf'])) {
                    $key = implode('', array_column($spec['attr_cnf'], 'at_val'));
                    $attr[$key] = $spec;
                }
            }
            ksort($attr);
            $data['specs'] = array_values($attr);
        }

        return $data;
    }

    /**
     * 查询商品列表
     * @param array $params
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     */
    public function getGoodsList(array $params): array
    {
        // 查询符合条件的gid
        $gids = $this->getGids($params);

        $result = ['list'=>[]];
        foreach ($gids as $gid) {
            $goods = by::Gmain()->GetAllOneByGid($gid, true, true, 0, false);
            // 处理商品信息
            $goods = $this->handleGoodsInfo($goods);
            // 获取库存销量
            list($goods['stock'], $goods['sales']) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);
            list($goods['pre_stock'], $goods['pre_sales']) = by::Gprestock()->GetSumData($gid);
            // 多规格
            if (($goods['atype'] ?? -1) == by::Gtype0()::ATYPE['SPECS']) {
                $specs          = by::Gspecs()->GetListByGid($gid);
                $goods['specs'] = by::Gspecs()->AttrToName($gid, $specs);
            }
            $result['list'][] = $goods;
        }
        $result['pages'] = $this->getGoodsPages($params);
        return $result;
    }




    /**
     * 获取商品gids
     * @param array $params
     * @return array
     * @throws \yii\db\Exception
     */
    private function getGids(array $params): array
    {
        $detailData = [];
        if (intval($params['is_recommend']) >= 0) {
            $detailData['is_recommend'] = $params['is_recommend'];
        }
        if (intval($params['is_internal_purchase']) >= 0) {
            $detailData['is_internal_purchase'] = $params['is_internal_purchase'];
        }

        if ($params['platform'] > 0) {
            $detailData['platformIds'] = [$params['platform']];
        }

        if (isset($params['is_presale']) && in_array($params['is_presale'], [0, 1])) {
            $detailData['is_presale'] = $params['is_presale'];
        }

        return by::Gmain()->GetList(
            $params['page'], $params['page_size'], $params['version'], $params['type'],
            $params['status'], $params['name'], $params['sku'], $params['tid'],
            $detailData
        );
    }

    /**
     * 获取商品总页数
     * @param array $params
     * @return int
     * @throws \yii\db\Exception
     */
    private function getGoodsPages(array $params): int
    {
        $detailData = [];
        if (intval($params['is_recommend']) >= 0) {
            $detailData['is_recommend'] = $params['is_recommend'];
        }
        if (intval($params['is_internal_purchase']) >= 0) {
            $detailData['is_internal_purchase'] = $params['is_internal_purchase'];
        }

        if ($params['platform'] > 0) {
            $detailData['platformIds'] = [$params['platform']];
        }

        $count = by::Gmain()->GetListCount($params['version'], $params['type'], $params['status'], $params['name'], $params['sku'], $params['tid'], $detailData);
        return CUtil::getPaginationPages($count, $params['page_size']);
    }

    /**
     * 处理商品信息
     * @param array $goods
     * @return array
     */
    private function handleGoodsInfo(array $goods): array
    {
        // 初始化数据
        $goods['pc_cover_image'] = '';
        $goods['pc_images'] = '';
        $goods['pc_detail'] = '';

        // 处理平台数据
        $platforms = $goods['platforms'] ?? [];
        $goods['platform_ids'] = array_column($platforms, 'platform_id');

        // 处理图片数据
        foreach ($platforms as $platform) {
            // 平台id
            $platform_id = $platform['platform_id'];
            // 填充信息
            if ($platform_id == PlatformModel::PLATFORM['PC']) { // PC
                $goods['pc_cover_image'] = $platform['cover_image'];
                $goods['pc_images']      = $platform['images'];
                $goods['pc_detail']      = $platform['detail'];
            } elseif ($platform_id == PlatformModel::PLATFORM['APP']) {
                $goods['cover_image'] = $platform['cover_image'];
                $goods['images']      = $platform['images'];
                $goods['detail']      = $platform['detail'];
            } else {
                $goods['market_image'] = $platform['cover_image'];
                $goods['images']       = $platform['images'];
                $goods['detail']       = $platform['detail'];
            }
        }

        return $goods;
    }

    /**
     * 查询指定商品的商品详情
     */
    public function getGoodsDetails($gids): array
    {
        $list = [];
        foreach ($gids as $gid) {
            $goods = by::Gmain()->GetAllOneByGid($gid, true, true, 0, false);
            if ( !$goods ) {
                continue;
            }
            // 处理商品信息
            $goods = $this->handleGoodsInfo($goods);
            // 获取库存销量
            list($goods['stock'], $goods['sales']) = GoodsMainService::getInstance()->GetSumByGid($gid, GoodsStockModel::SOURCE['MAIN'], false);
            list($goods['pre_stock'], $goods['pre_sales']) = by::Gprestock()->GetSumData($gid);
            // 多规格
            if (($goods['atype'] ?? -1) == by::Gtype0()::ATYPE['SPECS']) {
                $specs          = by::Gspecs()->GetListByGid($gid);
                $goods['specs'] = by::Gspecs()->AttrToName($gid, $specs);
            }
            $list[$gid] = $goods;
        }

        return $list;
    }


}