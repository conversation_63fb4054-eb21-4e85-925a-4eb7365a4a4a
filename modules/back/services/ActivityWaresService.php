<?php

namespace app\modules\back\services;

use app\exceptions\ActivityWaresException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\activitiesWares\ActivityWaresFactory;
use app\modules\main\enums\tryBeforeBuying\ActivityConfigEnum;
use app\modules\wares\models\ActivityModel;
use Exception;

/**
 * 活动配置的服务
 */
class ActivityWaresService
{
    /**
     * @var ActivityWaresFactory
     */
    protected $factory;

    public function __construct()
    {
        $this->factory = new ActivityWaresFactory();
    }

    /**
     * @param $data
     * @return null
     * @throws ActivityWaresException
     * @throws Exception
     * 添加/编辑活动
     */
    public function modify($data)
    {
        // 编辑时，活动类型不可修改
        $id   = $data['id'];
        $type = $data['grant_type'];
        if ($id) {
            $activityConfig = ActivityModel::findOne($id);
            if (!$activityConfig) {
                throw new ActivityWaresException("活动记录不存在");
            }
            if ($activityConfig['grant_type'] != $type) {
                throw new ActivityWaresException("活动类型不可修改");
            }
        }
        // 处理业务
        $activity = $this->factory->createActivity($type);
        return $activity->modify($data);
    }


    /**
     * @throws \yii\db\Exception
     * 活动上下架
     */
    public function updateStatus($id, $act): array
    {
        $arr = [];
        switch ($act) {
            case 'up':
                $arr = ['status' => 0, 'utime' => intval(START_TIME)];
                break;
            case 'down':
                $arr = ['status' => 1, 'utime' => intval(START_TIME)];
                break;
            default:
                break;
        }

        // 只能存在一个上架活动
        $status = $arr['status'] ?? 0;
        if ($status == byNew::ActivityModel()::STATUS['UP']) {
            $exists = byNew::ActivityModel()::find()->where(['status' => byNew::ActivityModel()::STATUS['UP'], 'grant_type' => byNew::ActivityModel()::GRANT_TYPE['tryBeforeBuy'], 'is_delete' => 0])->exists();
            if ($exists) {
                return [false, '只能存在一个上架活动'];
            }
        }

        return byNew::ActivityModel()->updateData($id, $arr);
    }


    public function updateData($id, $data): array
    {
        return byNew::ActivityModel()->updateData($id, $data);
    }

    public function del($id): array
    {
        return byNew::ActivityModel()->updateData($id, ['is_delete' => 1, 'delete_time' => time(), 'update_time' => time()]);
    }

    public function getActivityList(array $input, $page, int $page_size): array
    {
        // 获取活动列表数据
        $data = byNew::ActivityModel()->getList($input, $page, $page_size);
        $list = [];

        // 检查返回的列表是否不为空
        if (!empty($data['list'])) {
            foreach ($data['list'] as $key => $value) {
                // 获取每个活动的详细信息
                $detail = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($value['id'], false);

                // 获取与活动相关的商品信息
                $goods = by::GoodsMainModel()->GetOneByGid($detail['try_goods_ids']);

                // 构建返回的活动列表项
                $list[] = [
                    'id'         => $value['id'],
                    'name'       => $value['name'],
                    'goods_name' => $goods['name'] ?? 'N/A', // 假设可能存在没有商品信息的情况
                    'grant_type' => $value['grant_type'],
                    'status'     => $value['status'],
                    'start_time' => $value['start_time'],
                    'end_time'   => $value['end_time'],
                ];
            }
        }

        // 返回包括总数、页数和列表的结果数组
        return [
            'total' => intval($data['total'] ?? 0),
            'pages' => $data['pages'] ?? 1,
            'list'  => $list
        ];
    }

    public function getActivityDetail($id): array
    {
        try {
            if (empty($id)) {
                throw new ActivityWaresException("活动ID为空");
            }

            $activityMain = byNew::ActivityModel()->getOneById($id);
            $detail       = byNew::ActivityTypeModel()->getActivityTypeInfoByAcId($id);

            if (empty($activityMain) || empty($detail)){
                throw new ActivityWaresException("活动不存在");
            }
            // 将活动主要信息和详情合并到一个数组中
            return [true, [
                'id'              => $activityMain['id'],
                'name'            => $activityMain['name'],
                'grant_type'      => $activityMain['grant_type'],
                'status'          => $activityMain['status'],
                'start_time'      => $activityMain['start_time'],
                'end_time'        => $activityMain['end_time'],
                'poster_image'    => $detail['poster_image'],
                'share_image'     => $detail['share_image'],
                'try_goods_ids'   => $detail['try_goods_ids'],
                'try_quota'       => $detail['try_quota'],
                'apply_number'    => $detail['apply_number'],
                'validity'        => $detail['validity'],
                'return_period'   => $detail['return_period'],
                'delivery_period' => $detail['delivery_period'],
                'survey_key'      => $detail['survey_key'],
                'pass_mark'       => $detail['pass_mark'],
                'join_condition'  => $detail['join_condition'],
                'ac_ids'          => $detail['ac_ids'],
                'rule'            => $detail['rule'],
                'is_audit'        => $detail['is_audit'],
            ]];
        } catch (ActivityWaresException $activityWaresException) {
            CUtil::debug($activityWaresException->getMessage(), 'warn.activity-wares');
            return [false, $activityWaresException->getMessage()];
        }
    }



    public function condition(): array
    {
        $conditions = ActivityConfigEnum::CONDITION;
        $result     = [];

        foreach ($conditions as $key => $condition) {
            $result[] = [
                'code' => $key,
                'name' => $condition
            ];
        }

        return $result;
    }

}