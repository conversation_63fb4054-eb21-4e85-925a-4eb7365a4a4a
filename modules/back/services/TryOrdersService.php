<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\Response;
use app\modules\goods\services\UserOrderTryService;
use yii\db\Exception;
use yii\helpers\Json;

class TryOrdersService
{

    private static $_instance = NULL;


    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param $input
     * @param $page
     * @param $pageSize
     * @return array
     * @throws \RedisException
     * @throws \yii\db\Exception
     * 获取订单列表
     */
    public function GetOrderList($input, $page, $pageSize)
    {
        $label = $input['label'] ?? '';
        $type = $input['type'] ?? [];
        if ($label && in_array(by::Omain()::USER_ORDER_TYPE['BAT'], $type)) {
            //查询所有该标签的订单
            $orderInfos = byNew::UserOrderTry()->GetList([
                CUtil::buildCondition('label', '=', $label),
            ], 1);
            $orderNos = array_column($orderInfos, 'order_no');
            $input['order_nos'] = empty($orderNos) ? [0] : $orderNos;
        }

        $rets = byNew::TryOrdersModel()->GetList($input, $page, $pageSize);
        $return['list'] = [];
        $mOuser = by::Ouser();
        foreach ($rets as $ret) {
            $info = $mOuser->CommPackageInfo($ret['user_id'], $ret['order_no'], true, false, false, false, true, false, true, true);
            $tryInfo = byNew::UserOrderTry()->GetOneInfo([
                CUtil::buildCondition('user_id', '=', $ret['user_id']),
                CUtil::buildCondition('order_no', '=', $ret['order_no']),
            ]);
            $uidInfo = byNew::UserTryModel()->getInfoByUserId($ret['user_id']);
            $data = [
                'order_no' => $info['order_no'],
                'user_id' => $info['user_id'],
                'status' => $info['status'],
                'source' => $ret['source'],
                'platform_source' => $ret['platform_source'],
                'store' => $ret['store'] ?? '',
                'platform_source_name' => by::userExtend()->platformSourceConfig($ret['platform_source'] ?? 0),
                'ctime' => $info['ctime'],
                'user' => $info['user'],
                'goods' => $info['goods'],
                'deposit_order_no' => $info['deposit_order_no'] ?? '',
                'try_info' => $tryInfo ?? [],
                'user_order_type' => CUtil::uint($info['user_order_type'] ?? '')
            ];
            if ($data['user']) $data['user']['uid'] = $uidInfo['uid'] ?? '';
            //获取直播属性
            $osourceMInfo = by::osourceM()->getInfoByOrderNo($ret['user_id'] ?? '', $ret['order_no'] ?? '');
            $euid = $osourceMInfo['euid'] ?? '';
            $liveMark = $osourceMInfo['live_mark'] ?? '';
            $data['live_mark'] = $liveMark;
            $data['euid'] = $euid;
            $sourceInfo = by::SourceConfig()->getParamByCode($euid);
            $data['param'] = $sourceInfo['param'] ?? '';

            $return['list'][] = $data;
        }
        if ($page == 1) {
            $count = byNew::TryOrdersModel()->GetListCount($input);
            $return['pages'] = CUtil::getPaginationPages($count, $pageSize);
        }
        return $return;
    }


    public function GetOrderInfo($input)
    {
        $user_id = CUtil::uint($input['uid'] ?? 0);
        $order_no = $input['order_no'] ?? 0;
        if (empty($user_id) || empty($order_no)) return [];
        $viewSensitive = $input['view_sensitive'] ?? 0;

        // 获取订单信息
        $info = by::Ouser()->CommPackageInfo($user_id, $order_no, true, true, true, false, true, false, true, true);

        // 如果订单状态大于已取消，则获取支付单号
        if ($info['status'] > by::Omain()::ORDER_STATUS['CANCELED']) {
            $pay_log = by::model('OPayModel', 'goods')->GetOneInfo($order_no);
            $info['tid'] = $pay_log['tid'] ?? '';
        }

        // 获取试用信息
        $tryInfo = UserOrderTryService::getInstance()->GetUserOrderTryInfo([
            CUtil::buildCondition('user_id', '=', $user_id),
            CUtil::buildCondition('order_no', '=', $order_no),
        ]);
        $info['try_info'] = $tryInfo ?? [];
        if ($info['try_info']) {
            $info['try_info'] = $this->FormatExceptionData($info['try_info']);
        }

        // 退款信息
        $refund_no = $info['try_info']['refund_no'] ?? '';
        $refund_no && $refund_info       = by::Orefund()->CommPackageInfo($user_id,$refund_no,true,true);
        $info['refund_info'] = $refund_info ?? [];

        // 获取用户手机号
        $info['user']['phone'] = by::Phone()->GetPhoneByUid($user_id);

        // 获取用户来源
        $source = by::userExtend()->getUserExtendInfo($user_id);
        $info['user']['source'] = !empty($source['source']) ? by::userExtend()->sourceConfig($source['source']) : '其他';

        // 获取导购ID和导购用户信息
        $info['guide_id'] = by::Osource()->getOuidByOrder($user_id, $order_no);
        $info['guide_user'] = empty($info['guide_id']) ? [] : by::guide()->getGuideInfo($info['guide_id']) ?? [];

        // 获取推荐人ID
        $or_info = by::osourceR()->getInfoByOrderNo($user_id, $order_no);
        $info['r_id'] = $or_info['r_id'] ?? 0;

        // 获取订单渠道来源
        $or_m_info = by::osourceM()->getInfoByOrderNo($user_id, $order_no);
        $info = array_merge($info, [
            'union' => $or_m_info['union'] ?? '',
            'euid' => $or_m_info['euid'] ?? '',
            'store' => $source['store'] ?? '',
            'live_mark' => $or_m_info['live_mark'] ?? ''
        ]);

        // 如果不查看敏感信息，响应列表
        if (!$viewSensitive) {
            $arr = array_fill_keys(['cid', 'city', 'aid', 'area', 'detail', 'phone'], 'tm');
            $info['address'] = Response::responseList($info['address'] ?? [], $arr);
            $info['user'] = Response::responseList($info['user'] ?? [], $arr);
        }

        return $info;

    }

    /**
     * @throws Exception
     */
    public function exportData($year = '', $status = -1, $user_iden = '', $order_no = '', $p_sources = -1, $goods_name = '', $sku = '', $order_st = 0, $order_ed = 0, $viewSensitive = false, $label = -1)
    {
        // 设置脚本的最大执行时间为10分钟
        ini_set('max_execution_time', '600');
        $input = [
            'year' => $year,
            'status' => $status,
            'user_iden' => $user_iden,
            'order_no' => $order_no,
            'p_sources' => $p_sources,
            'goods_name' => $goods_name,
            'sku' => $sku,
            'type' => [by::Omain()::USER_ORDER_TYPE['BAT']],
            'overId' => 0,
            'order_time' => [
                'st' => $order_st,
                'ed' => $order_ed,
            ],
        ];
        if ($label > -1 ) {
            //查询所有该标签的订单
            $orderInfos = byNew::UserOrderTry()->GetList([
                CUtil::buildCondition('label', '=', $label),
            ], 1);
            $orderNos = array_column($orderInfos, 'order_no');
            $input['order_nos'] = empty($orderNos) ? [0] : $orderNos;
        }
        $head = [
            '订单号', 'sn编号','状态', '试用状态', '类型', '子类型', '订单平台来源', '付款时间', '订单总金额', '商品总金额', '运费', '优惠券名称', '优惠券金额', '积分抵扣金额', '实付金额','用户UID',
            '用户昵称', '用户手机号', '用户来源', '用户标签', '收货人姓名', '收货人手机号', '收货人地址', '物流公司', '快递单号', '备注', '绑定人ID', '门店名称'
        ];
        $db = by::dbMaster();
        $orderUserModel = by::Ouser();
        $userModel = by::users();
        $marketConfigModel = by::marketConfig();
        $userCardModel = by::userCard();
        $phoneModel = by::Phone();
        $userExtendModel = by::userExtend();
        $orderSourceModel = by::osourceR();
        $orderAddressModel = by::Oad();
        $orderMainModel = by::Omain();
        $goodsTypeModel = by::Gtype0();
        $orderTryModel = byNew::UserOrderTry();
        $userTryModel = byNew::UserTryModel();
        $data[] = $head;
        $id = 0;
        list($where, $params) = byNew::TryOrdersModel()->__getCondition($input);
        $time = empty($year) ? time() : strtotime("{$year}0101");
        $tb = byNew::TryOrdersModel()::tbName($time);
        $sql = "SELECT `id`,`order_no`,`user_id`,`source`,`platform_source`,`store` FROM {$tb} WHERE `id` > :id AND {$where} 
                   ORDER BY `id` LIMIT 1000";
        while (true) {
            $params['id'] = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            CUtil::debug($db->createCommand($sql, $params)->getRawSql(),'KKYUANQU');

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id = $end['id'];

            // 订单列表
            $orderItems = $list;

            // 订单号集合
            $orderNos = array_column($list, 'order_no');

            // 用户集合
            $userIds = array_unique(array_column($list, 'user_id'));

            // 获取用户订单数据
            $userOrderItems = $orderUserModel->getListByUserIdsAndOrderNos($userIds, $orderNos);
            $userOrderItems = array_column($userOrderItems, null, 'order_no');

            // 获取用户手机号数据
            $userPhoneItems = $phoneModel->getListByUserIds($userIds);
            $userPhoneItems = array_column($userPhoneItems, null, 'user_id');

            // 获取用户来源数据
            $userExtendItems = $userExtendModel->getListByUserIds($userIds, ['user_id', 'source', 'tag']);
            $userExtendItems = array_column($userExtendItems, null, 'user_id');

            // 获取用户的数据
            $userItems = $userModel->getListByUserIds($userIds, ['user_id', 'nick']);
            $userItems = array_column($userItems, null, 'user_id');

            // 获取用户UID
            list($total, $pages, $userUids) = $userTryModel->getList(['user_ids'=>$userIds],1,10000);
            $userUids = array_column($userUids, 'uid', 'user_id');

            // 获取订单的地址
            $orderAddressItems = $orderAddressModel->getListByOrderNos($orderNos, ['order_no', 'nick', 'phone', 'address', 'detail', 'express_name', 'mail_no']);
            $orderAddressItems = array_column($orderAddressItems, null, 'order_no');

            // 获取优惠券名称
            $userCoupons = $orderMainModel->getUserCoupon($userOrderItems, 100);
            $userCardItems = $userCardModel->getListByIds($userCoupons, ['id', 'user_id', 'market_id']);

            // 优惠券的名称
            $marketIds = [];
            foreach ($userCardItems as $userCardItem) {
                $ids = array_column($userCardItem, 'market_id');
                $marketIds = array_merge($marketIds, $ids);
            }

            $marketItems = $marketConfigModel->getListByIds($marketIds, ['id', 'name']);
            $marketItems = array_column($marketItems, 'name', 'id');

            // 订单号优惠券名称
            $orderMarketName = [];
            $orderCouponRelationItems = $orderMainModel->getOrderCouponRelation($userOrderItems, 100);
            foreach ($orderCouponRelationItems as $orderNo => $orderCouponRelationItem) {
                $orderMarketName[$orderNo] = '';
                $index = $orderCouponRelationItem['index'];
                $coupon_id = $orderCouponRelationItem['coupon_id'];
                if (isset($userCardItems[$index][$coupon_id]['market_id'])) {
                    $market_id = $userCardItems[$index][$coupon_id]['market_id'];
                    $orderMarketName[$orderNo] = $marketItems[$market_id] ?? '';
                }
            }

            // 订单的推荐人（推荐有礼）
            $orderSourceItems = $orderSourceModel->getListByOrderNos($orderNos, ['order_no', 'r_id']);
            $orderSourceItems = array_column($orderSourceItems, 'r_id', 'order_no');

            // 订单试用状态
            $orderTryItems = $orderTryModel->getList([CUtil::buildCondition('order_no', 'IN', $orderNos)]);
            $orderTryItems1 = array_column($orderTryItems, 'try_status', 'order_no');
            $orderTryItems2 = array_column($orderTryItems, 'fund_amount', 'order_no');

            // 订单SNS
            $orderTrySns =  array_column($orderTryItems, 'sn', 'order_no');

            // 拼装数据
            foreach ($orderItems as $item) {
                // 订单号、用户ID
                $orderNo = $item['order_no'];
                $userId = $item['user_id'];
                $store = $item['store'] ?? '';
                $userOrderItem = $userOrderItems[$orderNo];
                // 状态
                list(, $status) = $orderMainModel->SplitOrderStatus($userOrderItem['status']);
                $status = $orderMainModel::STATUS_NAME[$status] ?? '未知';
                // 平台来源
                $platformSourceName = $userExtendModel->platformSourceConfig($item['platform_source']) ?? '未知';
                // 付款时间
                $payTime = empty($userOrderItem['pay_time']) ? '' : date('Y-m-d H:i:s', $userOrderItem['pay_time']);
                // 订单实际金额
                $orealPrice = bcadd($userOrderItem['oprice'], $userOrderItem['fprice'], 2);
                $orealPrice = bcsub($orealPrice, $userOrderItem['exprice'], 2);
                $orealPrice = bcadd($orealPrice, $userOrderItem['deposit_price'], 2);
                // 订单实付金额
                $realPrice = bcadd($userOrderItem['price'], $userOrderItem['fprice'], 2);
                if ($userOrderItem['status'] >= $orderMainModel::ORDER_STATUS['WAIT_SEND']) {
                    $realPrice = bcadd($realPrice, $userOrderItem['deposit_price'], 2);
                }
                // 用户昵称
                $userNick = $userItems[$userId]['nick'] ?? '';
                // 手机号
                $phone = $userPhoneItems[$userId]['phone'] ?? '';
                // 用户来源
                list($source) = $userExtendModel->sourceConfig($userExtendItems[$userId]['source'] ?? '');
                // 收货地址
                $address = $orderAddressItems[$orderNo] ?? [];
                $address['address'] = (array)Json::decode($address['address'] ?? '');

                // 结果集合
                $data[] = [
                    'order_no' => $orderNo . "\t",
                    'sn' => $orderTrySns[$orderNo] ?? '',
                    'status' => $status,
                    'try_status' => $orderTryModel::TRY_STATUS_NAME[$orderTryItems1[$orderNo] ?? 0] ?? '',
                    'type' => '商城', // 目前只有一种类型
                    'source' => $orderMainModel::SOURCE[$item['source']] ?? '',
                    'platform_source_name' => $platformSourceName,
                    'pay_time' => $payTime,
                    'oprice' => $goodsTypeModel->totalFee($orealPrice, 1), // 金额类型转换
                    'gprice' => $goodsTypeModel->totalFee($userOrderItem['oprice'], 1),
                    'fprice' => $goodsTypeModel->totalFee($userOrderItem['fprice'], 1),
                    'cname' => $orderMarketName[$orderNo] ?? '',
                    'cprice' => $goodsTypeModel->totalFee($userOrderItem['cprice'], 1),
                    'coin_price' => $goodsTypeModel->totalFee($userOrderItem['coin_price'], 1),
                    'real_price' => $goodsTypeModel->totalFee($orderTryItems2[$orderNo] ?? 0, 1),
                    'uid' => $userUids[$userId] ?? '未知',
                    'nick' => $userNick,
                    'phone' => $phone,
                    'u_source' => $source,
                    'tag' => $userExtendItems[$userId]['tag'] ?? '',
                    'ad_name' => $address['nick'] ?? '',
                    'ad_phone' => $address['phone'] ?? '',
                    'ad_detail' => ($address['address']['province'] ?? '') . ($address['address']['city'] ?? '') . ($address['address']['area'] ?? '') . ($address['detail'] ?? ''),
                    'express_name' => $address['express_name'] ?? '',
                    'mail_no' => empty($address['mail_no'] ?? '') ? '' : $address['mail_no'] . "\t",
                    'note' => $userOrderItem['note'],
                    'r_id' => $orderSourceItems[$orderNo] ?? 0,
                    'store' => $store
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data, ['phone' => 'tm', 'ad_phone' => 'tm', 'ad_detail' => 'tm']);
        return $data;
    }


    public function exportRefundData($order_no = '', $user_iden = '', $status = -1, int $order_st = 0, int $order_ed = 0, $viewSensitive = false)
    {
        $head = [
            '订单编号', '退款单号', '用户id', '退款金额', '申请时间', '退款时间', '退款状态', '试用状态', '退款前状态', '类型', '子类型', '买家姓名', '手机号', '退款原因', '邮费', '申请件数', '补充描述', '退款物流单号', '退款物流公司'
        ];

        $tb = byNew::TryOrdersRefundModel()::tbName();
        $tryOrderTb = byNew::UserOrderTry()->tbName();

        $order_time = [
            'st' => $order_st,
            'ed' => $order_ed,
        ];
        $input = [
            'order_no' => $order_no,
            'user_iden' => $user_iden,
            'status' => $status,
            'order_time' => $order_time,
        ];
        list($where, $params) = byNew::TryOrdersRefundModel()->__getTryCondition($input);
        //导出

        $db = by::dbMaster();
        $mOrefund = by::Orefund();
        $mPhone = by::Phone();
        $mOmain = by::Omain();
        $mOreMain = by::OrefundMain();
        $orderTryModel = byNew::UserOrderTry();
//            $mOsource   = by::Osource();

        $id = 0;
        $sql = "SELECT `a`.`id`,`a`.`user_id`,`a`.`order_no`,`a`.`refund_no`,`b`.`try_status`,`b`.`try_status`,`b`.`fund_amount`
        FROM {$tb} as `a` 
        INNER JOIN {$tryOrderTb} as `b` 
        ON a.`order_no`=b.`order_no` 
        WHERE `a`.`id` > :id AND {$where} 
        ORDER BY `a`.`id` ASC 
        LIMIT 200";

        $data[] = $head;
        while (true) {
            $params['id'] = $id;
            $list = $db->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id = $end['id'];


            foreach ($list as $val) {
                $info = $mOrefund->CommPackageInfo($val['user_id'], $val['refund_no'], true);
                $phone = $mPhone->GetPhoneByUid($val['user_id']);
                list(, $ostatus) = $mOmain->SplitOrderStatus($info['ostatus']);
                //订单来源
                $omainInfo = $mOmain->getInfoByOrderNo($val['user_id'], $info['order_no']);
                $source = $omainInfo['source'] ?? 0;

                $data[] = [
                    'order_no' => $info['order_no'] . "\t",
                    'refund_no' => $info['refund_no'] . "\t",
                    'user_id' => $info['user_id'] . "\t",
                    'price' => CUtil::totalFee($val['fund_amount'],1). "\t",
                    'ctime' => date('Y-m-d H:i:s', $info['ctime']),
                    'rtime' => !empty($info['rtime']) ? date('Y-m-d H:i:s', $info['rtime']) : '',
                    'status' => $mOreMain::STATUS_NAME[$info['status']] ?? '未知',
                    'try_status' => $orderTryModel::TRY_STATUS_NAME[$val['try_status'] ?? -1] ?? '未知',
                    'ostatus' => $mOmain::STATUS_NAME[$ostatus] ?? '未知',
                    'type' => $mOmain::USER_ORDER_TYPE_NAME[$info['user_order_type']] ?? '未知',
                    'source' => '先试后买订单',//这边只有先试后买订单
                    'name' => $info['user']['nick'] ?? '',
                    'phone' => $phone ?? '' . "\t",
                    'r_msg' => $info['r_msg'],
                    'fprice' => $info['fprice'] ?? "",
                    'num' => $info['num'],
                    'describe' => $info['describe'] . "\t",
                    'mail_no' => $info['mail_no'] . "\t",
                    'express_name' => $info['express_name'] . "\t",
                ];
            }
        }
        !$viewSensitive && $data = Response::responseList($data, ['phone' => 'tm']);

        return $data;
    }


    /**
     * 异常订单列表
     *
     */
    public function GetExceptionList($input, $page, $pageSize)
    {
        $rets = byNew::UserOrderTry()->GetExceptionOrderList($input, $page, $pageSize);
        $return['list'] = [];

        foreach ($rets as $ret) {
            $data = $this->FormatExceptionData($ret);
            $return['list'][] = $data;
        }

        if ($page == 1) {
            $count = byNew::UserOrderTry()->GetExceptionListCount($input);
            $return['pages'] = CUtil::getPaginationPages($count, $pageSize);
        }
        return $return;
    }


    /**
     * 异常订单信息封装
     *
     */
    public function FormatExceptionData($data)
    {
        // 收货到期时间
        $data['arrival_end_time'] = $data['pay_time'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $data['delivery_period'];

        // 试用到期时间
        $data['try_end_time'] = 0;
        if ($data['arrival_time'] != 0) {
            $data['try_end_time'] = $data['arrival_time'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $data['validity'];
        }
        // 应扣款时间
        $data['deduct_time'] = 0;
        if ($data['try_end_time'] != 0) {
            $data['deduct_time'] = $data['try_end_time'] + byNew::UserOrderTry()::TRY_TIME_PERIOD * $data['return_period'];
        }
        // 异常信息
        $data['exception_status'] = 0;
        if ($data['try_status'] == byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY'] && $data['arrival_end_time'] < time()) {
            if ($data['arrival_time'] == 0) {
                $data['exception_status'] = byNew::UserOrderTry()::EXCEPETION_STATUS['TIMEOUT_NOT_TRY'];
            } elseif ($data['register_time'] == 0) {
                $data['exception_status'] = byNew::UserOrderTry()::EXCEPETION_STATUS['NOT_ACTIVATE'];
            }
        }

        if ($data['try_status'] == byNew::UserOrderTry()::TRY_STATUS['WAIT_DEDUCT']) {
            $data['exception_status'] = byNew::UserOrderTry()::EXCEPETION_STATUS['TIMEOUT_NOT_BACK'];
        }

        if ($data['try_status'] == byNew::UserOrderTry()::TRY_STATUS['DEDUCT_FAIL']) {
            $data['exception_status'] = byNew::UserOrderTry()::EXCEPETION_STATUS['DEDUCT_FAIL'];
        }

        $data['exception_status_name'] = byNew::UserOrderTry()::EXCEPETION_STATUS_NAME[$data['exception_status']] ?? '未知';
        return $data;
    }


    public function BeginTry($order_no)
    {
        // 1.查询该订单是否存在
        $tryOrderInfo = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no)]);
        if (empty($tryOrderInfo)) {
            return [false, '订单不存在！'];
        }
        // 2.状态确认
        $status = $tryOrderInfo['try_status'] ?? -1;
        if ($status != byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY']) {
            return [false, '订单状态不是待试用状态'];
        }

        // 3.确认收货
        list($s,$msg) = by::Omain()->Finish($tryOrderInfo['user_id'], $order_no);
        if(!$s){
            return [false, $msg];
        }


        return [true, 'OK'];
    }


    public function exportExceptionData($year = '', $order_no = '', $refund_no = '', $goods_name = '', $status = '-1', $try_status = '-1',$label = '-1', $viewSensitive = false)
    {
        $head = [
            '订单编号', '退款单号', '商品名称', '订单状态', '订单试用状态', '商品标签', '试用到期时间', '应扣款时间', '异常信息原因'
        ];
        $year = empty($year) ? time('Y') : intval($year);
        $input = [
            'year'       => $year,
            'status'     => $status,
            'try_status' => $try_status,
            'order_no'   => $order_no,
            'label'      => $label,
            'goods_name' => $goods_name,
            'type'       => [by::Omain()::USER_ORDER_TYPE['BAT']],
            'refund_no'  => $refund_no
        ];

        $tb     = byNew::UserOrderTry()::tbName();
        $tbMain = byNew::TryOrdersModel()->getStr($input);
        $tbAct  = byNew::ActivityTypeModel()::tableName();
        $db     = by::dbMaster();
        $mOreMain = by::OrefundMain();
        $omain    = by::Omain();
        $orderTryModel = byNew::UserOrderTry();
        $tagModel = by::Gtag();

        list($where, $params) = byNew::UserOrderTry()->__getCondition($input);

        $id = 0;
        //查询数据
        $sql = "SELECT `try`.*,`main`.`status`,`act`.`validity`,`act`.`delivery_period`,`act`.`return_period`
                FROM {$tb} AS `try` INNER JOIN {$tbMain} AS `main` ON `try`.`order_no` = `main`.`order_no` and `try`.`user_id` = `main`.`user_id`
                INNER JOIN {$tbAct} AS `act` ON `try`.`ac_id` = `act`.`ac_id` 
                WHERE `try`.`id` > :id AND {$where} ORDER BY `try`.`id` ASC LIMIT 200";

        $data[] = $head;
        $tagMap = by::Gtag()->GetTagNameMap();
        while (true) {
            $params['id'] = $id;
            $list = $db->createCommand($sql, $params)->queryAll();
            CUtil::debug($db->createCommand($sql, $params)->getRawSql(),'exception_data');

            if (empty($list)) {
                break;
            }

            $end = end($list);
            $id = $end['id'];

            foreach ($list as &$val) {
                $val = $this-> FormatExceptionData($val);
                list(, $status) = $omain->SplitOrderStatus($val['status']);
                $data[] = [
                    'order_no'              => $val['order_no'] . "\t",
                    'refund_no'             => $val['refund_no'] . "\t",
                    'goods_name'            => $val['goods_name'] . "\t",
                    'status'                => $omain::STATUS_NAME[$status] ?? '未知' . "\t",
                    'try_status'            => ($orderTryModel::TRY_STATUS_NAME[$val['try_status'] ?? 0] ?? '') . "\t",
                    'label_name'            => ($tagMap[$val['label'] ?? -1] ?? "") . "\t",
                    'try_end_time'          => !empty($val['try_end_time']) ? date('Y-m-d H:i:s', $val['try_end_time']) : '',
                    'deduct_time'           => !empty($val['deduct_time']) ? date('Y-m-d H:i:s', $val['deduct_time']) : '',
                    'exception_status_name' => ($val['exception_status_name'] ?? '') . "\t",
                ];
            }
        }
        return $data;

    }
}
