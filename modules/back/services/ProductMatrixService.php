<?php

namespace app\modules\back\services;


use app\exceptions\ProductMatrixException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\OfficialIntroModel;


/**
 * 全系产品站
 */
class ProductMatrixService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }
    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * @param array $save
     * @return array
     * @throws ProductMatrixException
     * 保存产品站信息
     */
    public function modify(array $save): array
    {
        return byNew::ProductMatrixModel()->saveProductMatrix($save);
    }

    /**
     * @param $categoryId
     * @return array
     * 获取产品站详情
     */
    public function getDetail($categoryId): array
    {
        // 获取数据
        $detail = byNew::ProductMatrixModel()->getDetail($categoryId);

        // 如果数据为空，返回空数组
        if (empty($detail)) {
            return [];
        }
        // 顶部卡片数据
        if (!empty($detail['main_product'])) {
            $detail['main_product'] = json_decode($detail['main_product'], true);
        }
        // 底部卡片数据
        if (!empty($detail['sub_products'])) {
            $detail['sub_products'] = json_decode($detail['sub_products'], true);
        }

        return $detail;
    }


    /**
     * @param $categoryData
     * @return array
     * 保存分类详情
     */
    public function categoryModify($categoryData): array
    {
        // 开启事务
        $transaction =by::dbMaster()->beginTransaction();
        try {
            // 已有分类，从数据库中获取所有分类记录
            $categories = byNew::GoodsCategoryModel()->getList(); // 获取所有分类的原始数据（假设包含 'id'）

            // 初始化数组
            $categoryAdd    = [];
            $categoryUpdate = [];

            // 现有分类 ID 列表
            $existingIds = array_column($categories, 'id');

            // 提交的分类 ID
            $submitIds = array_filter(array_column($categoryData, 'id'));

            // 找出需要删除的分类（从已有分类中找出未包含在提交数据中的 ID）
            $deleteIds = array_diff($existingIds, $submitIds);

            // 区分新增和更新的数据
            foreach ($categoryData as $item) {
                if (empty($item['name'])) {
                    return [false, '分类名称不能为空'];
                }
                if (empty($item['sort']) || !is_numeric($item['sort'])) {
                    return [false, '分类排序不能为空且必须为数字'];
                }

                if (empty($item['id'])) {
                    $categoryAdd[] = [
                            'name'  => $item['name'],
                            'sort'  => $item['sort'],
                            'ctime' => time()
                    ];  // 没有 ID，表示新增
                } else {
                    $categoryUpdate[] = [
                            'id'    => $item['id'],
                            'name'  => $item['name'],
                            'sort'  => $item['sort'],
                            'utime' => time()
                    ];  // 有 ID，表示更新
                }
            }

            // 批量插入新增数据
            if (!empty($categoryAdd)) {
                byNew::GoodsCategoryModel()->batchInsertCategory($categoryAdd); // 假设有批量插入方法
            }

            // 批量更新数据
            if (!empty($categoryUpdate)) {
                byNew::GoodsCategoryModel()->batchUpdateCategory($categoryUpdate); // 假设有批量更新方法，基于主键 ID 更新
            }

            // 批量删除数据
            if (!empty($deleteIds)) {
                byNew::GoodsCategoryModel()->deleteByIds($deleteIds); // 假设有批量删除方法
            }

            // 提交事务
            $transaction->commit();
            return [true, [
                    'categoryAdd'    => $categoryAdd,
                    'categoryUpdate' => $categoryUpdate,
                    'deleteIds'      => $deleteIds
            ]];
        } catch (\Exception $e) {
            // 回滚事务
            $transaction->rollBack();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.save_banner');
            return [false, '操作失败'];
        }
    }


    public function categoryList()
    {
        return byNew::GoodsCategoryModel()->getList();
    }

    /**
     * @param $id
     * @return array
     * @throws ProductMatrixException
     * 删除分类详情
     */
    public function categoryDelete($id): array
    {
        $data = [
                'id'     => $id,
                'is_del' => 1
        ];
        return byNew::GoodsCategoryModel()->deleteCategory($data);
    }


    /**
     * @param $id
     * @param $title
     * @param $intro
     * @param $status
     * @return array
     * @throws ProductMatrixException 保存官网介绍
     */
    public function introModify($id, $title, $intro, $status): array
    {
        $save = [
                'id'     => $id,
                'title'  => $title,
                'intro'  => $intro,
                'status' => $status
        ];
        return byNew::OfficialIntroModel()->saveIntro($save);
    }


    /**
     * @param $id
     * @param $status
     * @return array
     * @throws ProductMatrixException 修改官网介绍状态
     */
    public function introStatus($id, $status): array
    {
        if (!in_array($status, array_keys(OfficialIntroModel::STATUS))) {
            return [false, '状态错误'];
        }
        $save = [
                'id'     => $id,
                'status' => OfficialIntroModel::STATUS[$status]
        ];

        return byNew::OfficialIntroModel()->saveIntro($save);
    }

    /**
     * @param $id
     * @return array
     * 获取官网介绍详情
     */
    public function introDetail($id): array
    {
        return byNew::OfficialIntroModel()->getDetail($id);
    }


    /**
     * @param $input
     * @param $page
     * @param $size
     * @return array
     * 获取官网介绍列表
     */
    public function introList($input, $page, $size): array
    {
        return byNew::OfficialIntroModel()->getList($input, $page, $size);
    }
}