<?php

namespace app\modules\back\services;

use app\components\MemberCenter;
use app\jobs\TryBuyUserPathJob;
use app\jobs\TryBuyUserTagJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\main\models\SourceConfigModel;
use RedisException;
use yii\db\Exception;

class DataService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @throws Exception
     * 门店列表
     */
    public function getStoreList()
    {
        $store = by::WeFocus()->storeList();

        // 使用 array_filter 去除空值
        $result = array_filter($store, function ($item) {
            return !empty($item['name']);
        });

        // 转换为索引数组
        return array_values($result);
    }

    //来源列表
    public function getUserSourceList(): array
    {
        return by::SourceConfig()->getList();
    }

    /**
     * @param $id
     * @param $input
     * @return array
     * 保存用户来源配置
     */
    public function saveSource($id, $input): array
    {
        $sourceCode = $input['source_code'] ?? '';
        $param      = $input['param'] ?? '';
        $remark     = $input['remark'] ?? '';

        if (empty($sourceCode)) {
            return [false, 'code值不能为空'];
        }
        if (empty($param)) {
            return [false, '参数不能为空'];
        }

        if (!$id) {
            $record = by::SourceConfig()::findOne(['source_code' => $sourceCode,'is_del'=>0]);
            if ($record) {
                return [false, 'code值已存在，请检查！'];
            }
            //添加
            $save = [
                'source_code' => $sourceCode,
                'param'       => $param,
                'remark'      => $remark,
                'ctime'       => intval(START_TIME),
                'utime'       => intval(START_TIME)
            ];
        } else {
            //编辑
            $save = [
                'param'  => $param,
                'remark' => $remark,
                'utime'  => intval(START_TIME),
            ];
        }
        try {
            by::SourceConfig()->saveLog($id, $save);
            return [true, '保存成功'];
        } catch (\Exception $e) {
            CUtil::debug('用户来源配置失败|' . json_encode($save) . '|' . $e->getMessage(), 'err.source_save');
            return [false, '保存失败'];
        }

    }


    public function delSource($id): array
    {
        $id = intval($id);
        if (empty($id)) {
            return [false, '参数错误，请检查！'];
        }

        $record = SourceConfigModel::findOne(['id' => $id, 'is_del' => 0]);
        if (empty($record)) {
            return [false, '数据不存在，请检查！'];
        }

        try {
            by::SourceConfig()->deleteSource($id);
            return [true, '删除成功'];
        } catch (\Exception $e) {
            CUtil::debug('用户来源删除失败|' . $id . '|' . $e->getMessage(), 'err.source_delete');
            return [false, '删除失败'];
        }
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function sourceSelect(): array
    {
        $list   = by::SourceConfig()->getList();
        $result = [];
        foreach ($list as $item) {
            $result[] = [
                'name' => $item['param'],
                'code' => $item['source_code'],
            ];
        }
        return $result;
    }

    public function updateMemberGrow($userId, $number)
    {
        $userId = intval($userId);
        $number = intval($number);
        if (empty($userId)) {
            return [false, "无效的用户ID"];
        }
        if (empty($number)) {
            return [false, "无效的觅享分数量"];
        }
        return MemberCenter::factory()->run('updateMemberGrow', ['user_id' => $userId, 'number' => $number]);
    }

    public function updateMemberPoint($userId, $number)
    {
        $userId = intval($userId);
        $number = intval($number);
        if (empty($userId)) {
            return [false, "无效的用户ID"];
        }
        if (empty($number)) {
            return [false, "无效的积分数量"];
        }
        return MemberCenter::factory()->run('updateMemberPoint', ['user_id' => $userId, 'number' => $number]);
    }


    public function updateMemberGold($userId, $number)
    {
        $userId = intval($userId);
        $number = intval($number);
        if (empty($userId)) {
            return [false, "无效的用户ID"];
        }
        if (empty($number)) {
            return [false, "无效的金币数量"];
        }
        return MemberCenter::factory()->run('updateMemberGold', ['user_id' => $userId, 'number' => $number]);
    }

    /**
     * @param $input
     * @return array
     * @throws Exception
     * 保存支付宝口令
     */
    public function SaveZfbPassword($input): array
    {
        // 1、获取输入参数
        $id          = $input['id'] ?? 0;
        $zfbPassword = $input['zfb_password'] ?? '';
        $qrCode      = $input['qr_code'] ?? '';
        $zfbLink     = $input['zfb_link'] ?? '';

        // 2、验证必填字段
        if (empty($zfbPassword)) {
            return [false, '支付宝口令不能为空'];
        }
        if (empty($qrCode)) {
            return [false, '二维码落地页不能为空'];
        }
        if (empty($zfbLink)) {
            return [false, '支付宝链接不能为空'];
        }

        // 3、设置过期时间 如果修改了数据才会修改过期时间
        list($status, $zfbPasswordData) = byNew::ZfbPasswordModel()->getLatestInfo();
        $currentTime       = time();
        $defaultExpireTime = $currentTime + 86400 * 30;

        $passwordExpireTime = $defaultExpireTime;
        $linkExpireTime     = $defaultExpireTime;

        if ($zfbPasswordData) {
            $passwordExpireTime = ($zfbPasswordData['zfb_password'] != $zfbPassword) ? $defaultExpireTime : $zfbPasswordData['password_expire_time'];
            $linkExpireTime     = ($zfbPasswordData['zfb_link'] != $zfbLink) ? $defaultExpireTime : $zfbPasswordData['link_expire_time'];
        }

        // 4、构建保存的数据
        $save = [
            'zfb_password'         => $zfbPassword,
            'password_expire_time' => $passwordExpireTime,
            'qr_code'              => $qrCode,
            'zfb_link'             => $zfbLink,
            'link_expire_time'     => $linkExpireTime,
            'utime'                => time(), // 更新操作需要更新 utime
        ];

        // 5、保存数据
        list($status, $message) = byNew::ZfbPasswordModel()->saveLog($id, $save);

        return [$status, $message];
    }


    /**
     * @return array
     * @throws RedisException
     * 支付宝口令信息
     */
    public function GetZfbPasswordInfo(): array
    {
        list($status, $message) = byNew::ZfbPasswordModel()->getLatestInfo();
        return [$status, $message];
    }


    /**
     * @param array $input
     * @param int $page
     * @param int $pageSize
     * @return array
     * @throws Exception
     * @throws RedisException
     * 获取问卷审核列表
     */
    public function GetSurveyAuditList(array $input = [], int $page = 1, int $pageSize = 10): array
    {
        // 1.获取列表
        $list = byNew::SurveyRecordModel()->getSurveyList($input, $page, $pageSize);

        if (empty($list)) {
            return [true, []];
        }

        // 2.获取总数
        $total = byNew::SurveyRecordModel()->getCount($input);

        foreach ($list as &$value) {
            $acId                = $value['ac_id'] ?? '';
            $activityInfo        = byNew::ActivityModel()->getActivityDetail($acId);
            $value['phone']      = by::Phone()->GetPhoneByUid($value['user_id']) ?? '';
            $value['name']       = $activityInfo['name'] ?? '';
            $value['label']      = $activityInfo['goods']['label'] ?? '';
            $value['goods_name'] = $activityInfo['goods']['goods_name'] ?? '';
            $value['is_audit']   = $activityInfo['is_audit'] ?? 0;
        }

        // 3.获取分页
        $pages = CUtil::getPaginationPages($total, $pageSize);

        return [true, ['total' => intval($total ?? 0), 'pages' => $pages, 'list' => $list]];
    }


    /**
     * @param int $id
     * @param string $auditStatus
     * @return array
     * 问卷审核
     * @throws Exception
     */
    public function SurveyAudit(int $id, string $auditStatus): array
    {
        if ($id <= 0) {
            return [false, '参数错误，请检查！'];
        }

        $validStatuses = array_keys(byNew::SurveyRecordModel()::AUDIT_STATUS);
        if (!in_array($auditStatus, $validStatuses)) {
            return [false, '审核状态错误，请检查！'];
        }

        // 查询问卷信息
        $surveyData = byNew::SurveyRecordModel()->getSurveyRecord(['id' => $id]);
        if (empty($surveyData)) {
            return [false, '问卷信息不存在，请检查！'];
        }

        $userId = $surveyData['user_id'] ?? '';
        // 企微标签值 默认不通过
        $tagScene = 'NO_PASS_SURVEY';

        if ($userId && byNew::SurveyRecordModel()::AUDIT_STATUS[$auditStatus] == byNew::SurveyRecordModel()::AUDIT_STATUS['AUDIT_PASS']) {
            // 异步更新用户链路 通过问卷
            \Yii::$app->queue->delay(\app\modules\main\services\DataService::PAY_EXPIRE)->push(new TryBuyUserPathJob([
                'user_id' => $userId,
                'scene'   => 'manual_audit'
            ]));
            $tagScene = 'PASS_SURVEY';
        }

        // 异步打标签
        \Yii::$app->queue->push(new TryBuyUserTagJob([
            'userId'   => $userId,
            'tagScene' => $tagScene,
        ]));

        $result = byNew::SurveyRecordModel()->UpdateAuditStatus($id, byNew::SurveyRecordModel()::AUDIT_STATUS[$auditStatus]);
        return $result ? [true, '审核成功'] : [false, '审核失败'];
    }


}