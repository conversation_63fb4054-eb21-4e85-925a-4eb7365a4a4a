<?php

namespace app\modules\back\services;

use app\models\by;
use RedisException;
use yii\db\Exception;

class GrecommendService
{
    //后台商品推荐service
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @throws Exception
     */
    public function saveGoodsService($post, $cate_id = ''): array
    {
        $gid       = $post['gid'] ?? '';
        $image     = $post['image'] ?? '';
        $pcImage   = $post['pc_image'] ?? '';
        $h5Image   = $post['h5_image'] ?? '';
        $title     = $post['title'] ?? '';
        $tid       = $post['tid'] ?? '';
        $pcombines = $post['pcombines'] ?? '';
        $pcombines = explode(',', $pcombines);

        if (empty($cate_id)) {
            return [false, '请选择商品类目'];
        }

        if (empty($tid)) {
            return [false, '请选择标签'];
        }

        if (empty($gid)) {
            return [false, '请选择主推商品'];
        }

        if (empty($image)) {
            return [false, '请配置主推图片'];
        }

        if (empty($pcImage)) {
            return [false, '请配置PC主推图片'];
        }
        
        if (empty($h5Image)) {
            return [false, '请配置PC/H5图片'];
        }

        if (empty($title)) {
            return [false, '请配置标题'];
        }

        if (count($pcombines) > 5) {
            return [false, '次主推商品最多只可以配置五个~'];
        }
        list($s, $ret) = by::GoodsRecommend()->saveLog($post, $cate_id);
        if ($s) {
            return [true, []];
        } else {
            return [false, '操作失败'];
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 获取二级类目下所有的商品
     */
    public function GetCateGoodsDataService($pid): array
    {
        if (empty($pid)) {
            return [false, '参数错误'];
        }
        $cateModel = by::cateModel();
        $cate      = $cateModel->getCateById($pid, ['*'], ['children']);
        $children  = $cate['children'] ?? [];
        $cIds      = array_column($children, 'id');
        $sku       = [];
        foreach ($cIds as $cId) {
            $info = by::gCateModel()->getCateInfoByCid($cId);
            $sku  = array_merge($sku, array_column($info, 'sku'));

        }
        $ret = [];

        foreach ($sku as $value) {
            $aMain = by::Gmain()->GetOneBySku($value);
            $aMain = by::Gmain()->GetAllOneByGid($aMain['id'] ?? '', false);
            if (!$aMain) {
                continue;
            }

            $ret[] = [
                'gid'          => $aMain['id'] ?? '',
                'name'         => $aMain['name'] ?? '',
                'cover_image'  => $aMain['cover_image'] ?? '',
                'market_image' => empty($aMain['market_image'] ?? '') ? $aMain['cover_image'] : $aMain['market_image'] ?? '',
            ];
        }
        return [true, $ret];
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getRecommendInfoService($cateId): array
    {
        if (empty($cateId)) {
            return [false, '参数错误'];
        }
        $aData          = by::GoodsRecommend()->getRecommendInfoByCateId($cateId) ?? [];
        $pcombines      = $aData['pcombines'] ?? '';
        $pcombines      = explode(',', $pcombines);
        $gid            = $aData['gid'] ?? 0;
        $oGoods         = by::Gmain()->GetAllOneByGid($gid);
        $ret            = [];
        $ret['title']   = $aData['title'] ?? '';
        $ret['sort']    = $aData['sort'] ?? 0;
        $ret['tid']     = intval($aData['tid'] ?? '');
        $ret['primary'] = [
            'id'       => $oGoods['id'] ?? '',
            'name'     => $oGoods['name'] ?? '',
            'sku'      => $oGoods['sku'] ?? '',
            'price'    => $oGoods['price'] ?? 0,
            'image'    => $aData['image'] ?? '',
            'pc_image' => $aData['pc_image'] ?? '',
            'h5_image' => $aData['h5_image'] ?? '',
        ];
        $ret['secondary'] = [];
        foreach ($pcombines as $pcombine) {
            $info = by::Gmain()->GetAllOneByGid($pcombine);
            if (!$info) {
                continue;
            }
            $tag                = by::Gtag()->GetListByGid(intval($pcombine));
            $tag                = array_column($tag, 'tid');
            $is_presale         = $info['is_presale'] ?? 0;
            $ret['secondary'][] = [
                'id'    => $info['id'] ?? '',
                'name'  => $info['name'] ?? '',
                'sku'   => $info['sku'] ?? '',
                'price' => $info['price'] ?? 0,
                'image' => $info['cover_image'] ?? '',
                'type'  => $is_presale ? : (in_array(2, $tag) ? 2 : 0)
            ];
        }
        return [true, $ret];
    }
}