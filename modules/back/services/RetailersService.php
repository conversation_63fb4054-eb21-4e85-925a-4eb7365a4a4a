<?php
/**
 * <AUTHOR>
 * @date 31/3/2025 下午 6:07
 */

namespace app\modules\back\services;

use app\models\by;
use app\models\MyExceptionModel;
use app\modules\main\models\CommonActivityModel;
use app\modules\main\models\RetailersModel;
use yii\db\Exception;

class RetailersService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @throws MyExceptionModel|Exception
     */
    public function update(int $id, array $data): bool
    {
        // 1. 校验参数
        if (!$id) {
            throw new MyExceptionModel('活动ID不能为空');
        }

        $status = $data['status'] ?? 1; // 1=开启 2=关闭

        // 2. 设置时间戳
        if ($status == RetailersModel::OPEN_ACTIVITY_STATUS['OPEN']['STATUS']) {
            $startTime = filter_var($data['start_time'] ?? null, FILTER_VALIDATE_INT);
            $endTime   = filter_var($data['end_time'] ?? null, FILTER_VALIDATE_INT);

            if ($startTime === false || $endTime === false) {
                throw new MyExceptionModel('开始时间和结束时间必须为有效的整数');
            }

            if ($startTime >= $endTime) {
                throw new MyExceptionModel('开始时间不能大于或等于结束时间');
            }

        } else {
            // 状态为关闭时，清空时间
            $startTime = $endTime = 0;

            $isOpenActivity = RetailersModel::OPEN_ACTIVITY_STATUS['CLOSE']['STATUS'];
            by::retailers()->updateAllStoreActivityStatus($isOpenActivity);
        }

        // 3. 准备更新数据
        $updateData = [
                'start_time' => $startTime,
                'end_time'   => $endTime,
        ];

        // 4. 执行更新
        if (!CommonActivityModel::updateActivity($id, $updateData)) {
            throw new MyExceptionModel('更新失败或数据未变更');
        }

        return true;
    }



    /**
     * @param $ids
     * @param string $act OPEN:开启活动 CLOSE:关闭活动
     * @return int
     * @throws Exception
     */
    public function batchOpen($ids, string $act): int
    {
        if (empty($ids) || !isset(RetailersModel::OPEN_ACTIVITY_STATUS[$act])) {
            return 0; // 避免 SQL 语法错误或非法操作
        }

        if (!is_array($ids)) {
            $ids = array_filter(explode(',', $ids)); // 过滤空值，确保是数组
        }

        if (empty($ids)) {
            return 0; // 避免无效 SQL 执行
        }

        $isOpenActivity = RetailersModel::OPEN_ACTIVITY_STATUS[$act]['STATUS'];

        return by::retailers()->batchOpenActivity($ids, $isOpenActivity); // 确保返回影响的行数
    }


    /**
     * 获取活动详情信息
     *
     * @param int $id 活动ID
     * @return array
     */
    public function activityInfo(int $id): array
    {
        $activity = CommonActivityModel::getActivityById($id);
        if (!$activity) {
            return [];
        }

        $activity = $activity->toArray();

        $startTime   = $activity['start_time'] ?? 0;
        $endTime     = $activity['end_time'] ?? 0;
        $currentTime = time();

        // 活动状态初始化为 0（未配置时间）
        $activity['status'] = 0;

        if ($startTime > 0 && $endTime > 0) {
            if ($currentTime < $startTime) {
                $activity['status'] = 1; // 未开始
            } elseif ($currentTime < $endTime) {
                $activity['status'] = 2; // 进行中
            } else {
                $activity['status'] = 3; // 已结束
            }
        }

        return $activity;
    }




}