<?php

namespace app\modules\back\services;

use app\models\BusinessException;
use app\models\by;
use app\modules\goods\models\StoreGoodsRelationModel;
use yii\base\StaticInstanceTrait;

/**
 * 追觅小店店铺商品商品 - 服务层 - 后台
 */
class DreameStoreGoodsRelationService
{
    use StaticInstanceTrait;

    /**
     * @var StoreGoodsRelationModel
     */
    public $model;

    public  function __construct()
    {
        $this->model = StoreGoodsRelationModel::instance();
    }


    /**
     * 列表
     */
    public function getPageList(array $params = []): array
    {
        $ret = $this->model->getPageList($params);
        $gids = array_column($ret['list'], 'goods_id');
        $goodInfos = by::Gmain()->getDataByIds($gids, ['id','name']);
        $goodInfos = array_column($goodInfos, null, 'id');
        foreach ($ret['list'] as $k => $v) {
            $ret['list'][$k]['goods_name'] = $goodInfos[$v['goods_id']]['name'] ?? '';
        }
        return $ret;
    }

    /**
     * 列表
     */
    public function getAll(array $params = []): array
    {
        $query = $this->model::find();
        $query = $this->model->handleSearch($query, $params);
        $list = $query->orderBy([ 'ctime' => SORT_DESC])
            ->asArray()->all();
        $gids = array_column($list, 'goods_id');
//        $goodInfos = by::Gmain()->getDataByIds($gids, ['id','name']);
//        $goodInfos = array_column($goodInfos, null, 'id');
        $goodInfos = GoodsService::getInstance()->getGoodsDetails($gids);
        foreach ($list as $k => $v) {
            $appData = [];
            $appData['good_name'] = $goodInfos[$v['goods_id']]['name'] ?? ''; //商品名
            $appData['goods_name'] = $goodInfos[$v['goods_id']]['name'] ?? ''; //商品名
            $appData['sku'] = $goodInfos[$v['goods_id']]['sku'] ?? '';   //商品编号
            $appData['price'] = $goodInfos[$v['goods_id']]['price'] ?? '';   //价格
            $appData['tids_name'] = $goodInfos[$v['goods_id']]['tids_name'] ?? [];   //标签
            $appData['image'] = $goodInfos[$v['goods_id']]['cover_image'] ?? '';   //封面图
            $appData['tag_name'] =implode(',', array_column($appData['tids_name'], 'name'));
            $list[$k] = array_merge($v, $appData);
        }
        return $list;
    }



    /**
     * 详情
     */
    public function info($id): array
    {
        $info  = $this->model->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        return $info->toArray();
    }

    /**
     * 创建
     */
    public function create(array $data)
    {
        $goods_id = (int)($data['goods_id'] ?? 0);
        $user_id = $data['user_id'] ?? 0;
        if (empty($goods_id) || empty($user_id)) {
            throw new BusinessException('缺少参数');
        }
        $info  = $this->model->getInfo(['user_id' => $user_id,'goods_id' => $goods_id]);
        if ($info) {
            throw new BusinessException(sprintf('已存在'));
        }
        unset($data['id']);
        return $this->model->saveData($data);
    }

    /**
     * 创建 只保存不存在的商品
     */
    public function batchCreate(array $data)
    {
        $goods_ids = $data['goods_ids'] ?? '';
        $user_id = $data['user_id'] ?? '';
        if (empty($goods_ids) || empty($user_id)) {
            throw new BusinessException('缺少参数');
        }
        $goods_ids = explode(',', (string) $goods_ids);
        $goods_ids = array_unique($goods_ids);
        $existGoodsIds = $this->model->find()
            ->where(['user_id' => $user_id,'goods_id' => $goods_ids])
            ->select('goods_id')
            ->column();

        $newGoodIds = array_diff($goods_ids, $existGoodsIds);
        if (! $newGoodIds) {
            throw new BusinessException('商品已经存在列表');
        }
        $now = time();
        $rows = [];
        $fields  = ['user_id', 'goods_id', 'ctime', 'utime'];
        if (!empty($rate)) $fields[] = 'rate';
        foreach ($newGoodIds as $goods_id) {
            $row = [
                'user_id' => $user_id,
                'goods_id' => $goods_id,
                'ctime' => $now,
                'utime' => $now,
            ];
            $rows[] = $row;
        }
        return $this->model->batch($fields, $rows);
    }

    /**
     * 更新
     */
    public function update(array $data)
    {
        $id = (int)($data['id'] ?? 0);
        if (empty($id)) {
            throw new BusinessException('ID不能为空');
        }
        $info  = $this->model->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        $where = ['id' => $id];
        $up = $data;
        //可修改的数据
        $up = array_intersect_key($up, array_flip(['user_id', 'goods_id']));
        return $this->model->updateInfo($where, $up);
    }

    /**
     * 审核
     */
    public function audit($ids,$status, $reason)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = $this->model;
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $where = ['id' => $ids];
        $up = ['status' => $status, 'reason' => $reason];
        $up = array_filter($up, function ($v) {return (string)$v !== '';});
        return $model->updateInfo($where, $up);
    }

    /**
     * 删除
     */
    public function del($ids)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = $this->model;
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $where = ['id' => $ids];
        return $model->deleteInfo($where);
    }
}