<?php

namespace app\modules\back\services;

use app\components\Employee;
use app\models\byNew;
use phpDocumentor\Reflection\Types\This;

/**
 * 员工管理服务
 */
class UserEmployeeService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 积分操作类型
    const SCORE_HANDLE_TYPE = [
        'ADD'    => 1,
        'REDUCE' => 2
    ];

    // 积分类型
    const SCORE_TYPE = [
        'SCORE' => 1, // 积分
        'ENJOY' => 2, // 觅享分
        'SMILE' => 3  // 微笑分
    ];

    // 员工等级
    const EMPLOYEE_LEVEL = [
        'NORMAL' => 1,  // 普通微笑大使
        'SUPER'  => 2   // 超级微笑大使
    ];

    // 员工状态
    const EMPLOYEE_STATUS = [
        'NORMAL' => 1, // 正常
        'STOP'   => 2  // 停用
    ];

    /**
     * 员工列表
     * @param array $params
     * @return array
     */
    public function getUserEmployeeList(array $params): array
    {
        // 分页信息
        $page      = $params['page'];
        $page_size = $params['page_size'];

        // 没有查询条件，先查询用户关联表，再查询用户平台
        $count = 0;
        if (empty($params['uid']) && empty($params['phone']) && empty($params['name']) && empty($params['employee_no']) && empty($params['employee_status'])) {
            // 查询用户关联表
            $param = [
                'is_blacklist' => $params['is_blacklist']
            ];
            $users = byNew::UserEmployeeModel()->getEmployeeList($param, $page, $page_size);
            if (empty($users)) {
                return [true, []];
            }
            $count = byNew::UserEmployeeModel()->getEmployeeCount($param);

            // 获取用户平台数据
            $param    = [
                'uid' => array_column($users, 'employee_id')
            ];
            $employee = $this->getEmployeeList($param);

            // 整合数据
            $data = $this->formatData($users, $employee);
            return [true, ['list' => $data, 'total' => $count]];
        }

        // 有查询条件
        // 优先查询用户关联表
        $users = [];
        if (!empty($params['uid']) || !empty($params['employee_status']) || !empty($params['employee_no'])) {
            $param = [
                'uid'             => $params['uid'],
                'employee_no'     => $params['employee_no'],
                'employee_status' => $params['employee_status'],
                'is_blacklist'    => $params['is_blacklist']
            ];
            $users = byNew::UserEmployeeModel()->getEmployeeList($param, $page, $page_size);
            // 未获取到数据时，直接返回
            if (empty($users)) {
                return [true, []];
            }
            $count = byNew::UserEmployeeModel()->getEmployeeCount($param);
        }

        // 其次查询用户平台
        $employee = [];
        if (!empty($params['phone']) || !empty($params['name'])) {
            $param    = [
                'uid'         => array_column($users, 'employee_id'), // 员工id
                'phone'       => $params['phone'],
                'real_name'   => $params['name']
            ];
            $employee = $this->getEmployeeList($param);
            if (empty($employee)) {
                return [true, []];
            }
        }

        // 其他查询条件
        if (!empty($employee)) {
            $uids  = array_column($employee, 'uid');
            $param = [
                'employee_id'  => $uids,
                'is_blacklist' => $params['is_blacklist']
            ];
            $users = byNew::UserEmployeeModel()->getEmployeeList($param, $page, $page_size);
            $count = byNew::UserEmployeeModel()->getEmployeeCount($param);
        } elseif (!empty($users)) {
            $param    = [
                'uid' => array_column($users, 'employee_id')
            ];
            $employee = $this->getEmployeeList($param);
        }

        // 整合数据
        $data = $this->formatData($users, $employee);

        return [true, ['list' => $data, 'total' => $count]];
    }

    /**
     * 获取员工信息
     * @param string $uid
     * @return array
     */
    public function getUserEmployeeInfo(string $uid): array
    {
        // 通过uid在员工关联表中查询是否存在
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
        if (empty($user)) {
            return [false, '用户不存在'];
        }

        list($status, $res) = Employee::factory()->employeeInfo(['uid' => $user['employee_id']]);
        // 未获取到数据时，直接返回
        if (!$status || empty($res)) {
            return [true, []];
        }

        // 整合数据
        $data = [
            'uid'                  => $user['uid'],
            'join_time'            => date('Y-m-d', $user['ctime']),
            'employee_status'      => $user['employee_status'],
            'employee_status_name' => $this->getEmployeeStatusName($user['employee_status']),
            'level'                => $user['level'],
            'level_name'           => $this->getLevelName($user['level']),
            'name'                 => $res['real_name'],
            'phone'                => $res['phone'],
            'employee_no'          => $res['employee_no'],
            'department'           => explode('<br/>', $res['department']),
            'score'                => $res['score'],
            'current_month_score'  => $res['current_month_score']
        ];
        return [true, $data];
    }
    public function exportData($uid = '',$phone = '',$name = '',$employee_no = '',$is_blacklist = 0,$employee_status = 0,$viewSensitive = false){
        $head = [
            '工号', '姓名', '所在部门1','所在部门2', '入职时间','微笑大使注册时间', '当前等级', '当月微笑分', '过去3个月的平均分值', '累计历史微笑分值','邀请注册人数','邀请购买人数'
        ];
        $data = [];
        $data[] = $head;
        $list = [];

        //获取列表
        if (empty($uid) && empty($phone) && empty($name) && empty($employee_no) && empty($employee_status)) {
            // 查询用户关联表
            $param = [
                'is_blacklist' => $is_blacklist
            ];
            $users = byNew::UserEmployeeModel()->getEmployeeAll($param);
            if (empty($users)) {
                return [$head];
            }

            // 获取用户平台数据
            // $param    = [
            //     'uid' => array_column($users, 'employee_id')
            // ];
            $uids = array_column($users, 'employee_id');
            // 数量太多的时候，分页获取
            if (count($uids) > 100){
                $employee = [];
                $uidSlice = array_chunk($uids,100);
                foreach($uidSlice as $k=>$v){
                    $paramSlice['uid'] = $v;
                    $employeePortion = $this->getEmployeeList($paramSlice);
                    $employee = array_merge($employee,$employeePortion);
                }
            }else{
                $employee = $this->getEmployeeList(['uid'=>$uids]);
            }
            // 整合数据
            $list = $this->formatData($users, $employee);
        }else{
            // 有查询条件
            // 优先查询用户关联表
            $users = [];
            if (!empty($uid) || !empty($employee_status) || !empty($employee_no)) {
                $param = [
                    'uid'             => $uid,
                    'employee_no'     => $employee_no,
                    'employee_status' => $employee_status,
                    'is_blacklist'    => $is_blacklist
                ];
                $users = byNew::UserEmployeeModel()->getEmployeeAll($param);
                // 未获取到数据时，直接返回
                if (empty($users)) {
                    return [$head];
                }
            }

            // 其次查询用户平台
            $employee = [];
            if (!empty($phone) || !empty($name)) {
                $param    = [
                    'uid'         => array_column($users, 'employee_id'), // 员工id
                    'phone'       => $phone,
                    'real_name'   => $name,
                    // 'employee_no' => $employee_no
                ];
                $employee = $this->getEmployeeList($param);
                if (empty($employee)) {
                    return [$head];
                }
            }

            // 其他查询条件
            if (empty($users) && !empty($employee)) {
                $uids  = array_column($employee, 'uid');
                $param = [
                    'employee_id'  => $uids,
                    'is_blacklist' => $is_blacklist
                ];
                $users = byNew::UserEmployeeModel()->getEmployeeAll($param);
            } elseif (!empty($users) && empty($employee)) {
                $param    = [
                    'uid' => array_column($users, 'employee_id')
                ];
                $employee = $this->getEmployeeList($param);
            }

            // 整合数据
            $list = $this->formatData($users, $employee);
        }

        
        if (empty($list)) {
            return [$head];
        }
        $registerCount = byNew::UserBindModel()->getRecommendRegisterCount();
        $buyCount = byNew::BoundUserOrderModel()->getRecommendBuyCount();;
        // 获取所有的后台用户ID
        foreach ($list as $val) {
            $data[] = [
                'employee_no'          => $val['employee_no'] ?? '',
                'name'                 => $val['name'] ?? '',
                'department1'          => $val['department'][0] ?? '-',
                'department2'          => $val['department'][1] ?? '-',
                'hire_date'            => $val['hire_date'] ?? '',
                'join_time'            => $val['join_time'] ?? '',
                'level_name'           => $val['level_name'] ?? '',
                'current_month_score'  => $val['current_month_score'] ?? '0',
                'avg_score_3_months'   => $val['avg_score_3_months'] ?? '0',
                'score'                => $val['score'] ?? '0',
                'recommend_register'   => isset($registerCount[$val['uid']]) ? $registerCount[$val['uid']] : '0',
                'recommend_buy'        => isset($buyCount[$val['uid']]) ? $buyCount[$val['uid']] : '0'
            ];
        }
        
        return $data;
    }


    /**
     * 加入黑名单
     * @param string $uid
     * @return array
     */
    public function addBlackList(string $uid): array
    {
        if (empty($uid)) {
            return [false, '用户不存在'];
        }
        $status = byNew::UserEmployeeModel()->addBlacklist($uid);
        if ($status) {
            return [true, ''];
        }
        return [false, '加入黑名单失败'];
    }

    /**
     * 移除黑名单
     * @param string $uid
     * @return array
     */
    public function removeBlackList(string $uid): array
    {
        if (empty($uid)) {
            return [false, '用户不存在'];
        }
        $status = byNew::UserEmployeeModel()->removeBlacklist($uid);
        if ($status) {
            return [true, ''];
        }
        return [false, '移除黑名单失败'];
    }

    /**
     * 操作员工分数
     * @param array $params
     * @return array
     */
    public function optScore(array $params): array
    {
        // 参数为uid
        $uid = $params['uid'];
        // 通过uid在员工关联表中查询是否存在
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
        if (empty($user)) {
            return [false, '用户不存在'];
        }

        // 操作
        $param = [
            'uid'         => $user['employee_id'],
            'score'       => $params['score'],
            'handle_type' => $params['handle_type'],
            'score_type'  => $params['score_type'],
            'handle_name' => $params['handle_name'],
            'source'      => $params['source'] ?? '',

        ];
        list($status, $res) = Employee::factory()->optScore($param);
        // 未获取到数据时，直接返回
        if (!$status) {
            return [false, $res];
        }
        return [true, ''];
    }

    /**
     * 获取积分记录
     * @param array $params
     * @return array
     */
    public function getScoreRecord(array $params): array
    {
        // 参数为uid
        $uid = $params['uid'];
        // 通过uid在员工关联表中查询是否存在
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($uid);
        if (empty($user)) {
            return [false, '用户不存在'];
        }

        // 操作
        $param = [
            'uid'        => $user['employee_id'],
            'score_type' => $params['score_type'],
            'page'       => $params['page'],
            'page_size'  => $params['page_size'],
        ];
        list($status, $res) = Employee::factory()->scoreRecord($param);
        // 未获取到数据时，直接返回
        if (!$status || empty($res['list'])) {
            return [true, []];
        }

        // 整合数据
        $list = [];
        foreach ($res['list'] as $item) {
            $list[] = [
                'score'       => $this->formatScoreData($item['score'], $item['handle_type']),
                'source'      => $item['source'],
                'create_time' => date('Y-m-d H:i:s', $item['create_time']),
                'handle_name' => $item['handle_name']

            ];
        }
        return [true, ['list' => $list, 'total' => $res['total']]];
    }

    /**
     * 更新员工等级
     * @param int $id
     * @param int $limit
     * @return int
     * @throws \yii\db\Exception
     */
    public function updateLevel(int $id, int $limit): int
    {
        // 获取员工列表
        $list = byNew::UserEmployeeModel()->getEmployeeListForUpdateLevel($id, $limit);
        if (empty($list)) {
            return $id;
        }

        // 初始化需要更新的员工ID数组
        $upgrade   = [];
        $downgrade = [];

        // 遍历员工列表，确定升级或降级
        foreach ($list as $item) {
            $level                   = $item['level'];
            $last_month_score        = $item['last_month_score'];
            $avg_score_last_3_months = $item['four_average_score'];

            if ($level == self::EMPLOYEE_LEVEL['NORMAL'] && $avg_score_last_3_months >= 4000) {
                // 普通员工，过去3个月平均分值 >= 4000，则升级
                $upgrade[] = $item['id'];
            } elseif ($level == self::EMPLOYEE_LEVEL['SUPER'] && $last_month_score < 2000) {
                // 超级员工，过去一个月分值 < 2000，则降级
                $downgrade[] = $item['id'];
            }
        }

        // 批量更新员工等级
        $employeeModel = byNew::UserEmployeeModel();

        if (!empty($upgrade)) { // 升级，同时设置提醒
            $employeeModel->updateEmployeeLevel($upgrade, self::EMPLOYEE_LEVEL['SUPER'], $employeeModel::IS_TIP['YES']);
        }

        if (!empty($downgrade)) { // 降级
            $employeeModel->updateEmployeeLevel($downgrade, self::EMPLOYEE_LEVEL['NORMAL'], -1);
        }

        // 返回最后一个员工的id
        return end($list)['id'] ?? $id;
    }

    /**
     * 获取员工列表
     * @param array $param
     * @return array
     */
    private function getEmployeeList(array $param): array
    {
        list($status, $employee) = Employee::factory()->employeeList($param);
        // 未获取到数据时，直接返回
        if (!$status || empty($employee['list'])) {
            return [];
        }
        return $employee['list'];
    }

    /**
     * 格式化数据
     * @param array $users
     * @param array $employees
     * @return array
     */
    private function formatData(array $users, array $employees): array
    {
        // 将员工数据索引化，便于查找
        $employee_map = [];
        foreach ($employees as $e) {
            $employee_map[$e['uid']] = $e;
        }
        // 合并数据
        $data = [];
        foreach ($users as $user) {
            $item = [
                'uid'                  => $user['uid'],
                'level'                => $user['level'],
                'level_name'           => $this->getLevelName($user['level']),
                'employee_id'          => $user['employee_id'],
                'employee_status'      => $user['employee_status'],
                'employee_status_name' => $this->getEmployeeStatusName($user['employee_status']),
                'is_blacklist'         => $user['is_blacklist'],
                'blacklist_time'       => $user['blacklist_time'] ? date('Y-m-d', $user['blacklist_time']) : '',
                'join_time'            => date('Y-m-d', $user['ctime']),
            ];
            if (isset($employee_map[$user['employee_id']])) {
                $employee                    = $employee_map[$user['employee_id']];
                $item['name']                = $employee['real_name'];
                $item['phone']               = $employee['dtt_phone'];
                $item['employee_no']         = $employee['employee_no'];
                $item['department']          = explode('<br/>', $employee['department']);
                $item['score']               = $employee['score'];
                $item['current_month_score'] = $employee['current_month_score'];
                $item['hire_date']           = $employee['hire_date'];
            }
            // 前3个月平均分
            $item['avg_score_3_months'] = $user['four_average_score'];
            $data[] = $item;
        }
        return $data;
    }

    /**
     * 格式化积分数据
     * @param $score
     * @param $handle_type
     * @return int
     */
    private function formatScoreData($score, $handle_type): int
    {
        if ($handle_type == self::SCORE_HANDLE_TYPE['REDUCE']) { // 减分
            return -$score;
        }
        return $score;
    }

    /**
     * 获取等级名称
     * @param $level
     * @return string
     */
    private function getLevelName($level): string
    {
        switch ($level) {
            case self::EMPLOYEE_LEVEL['NORMAL']:
                $level_name = '普通微笑大使';
                break;
            case self::EMPLOYEE_LEVEL['SUPER']:
                $level_name = '超级员工';
                break;
            default:
                $level_name = '未知等级';
        }
        return $level_name;
    }

    /**
     * 员工状态
     * @param $employee_status
     * @return string
     */
    private function getEmployeeStatusName($employee_status): string
    {
        switch ($employee_status) {
            case self::EMPLOYEE_STATUS['NORMAL']:
                $employee_status_name = '有效';
                break;
            case self::EMPLOYEE_STATUS['STOP']:
                $employee_status_name = '离职';
                break;
            default:
                $employee_status_name = '未知状态';
        }
        return $employee_status_name;
    }
}