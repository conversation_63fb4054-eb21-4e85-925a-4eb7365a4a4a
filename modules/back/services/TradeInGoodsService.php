<?php
namespace app\modules\back\services;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\TradeInActivityGoodsModel;
use Exception;

class TradeInGoodsService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取分类列表
     * @return array
     *
     */
    public function getCategoryList(): array
    {
        return byNew::TradeInCategoryModel()::find()->where(['is_del' => 0])->select(['id', 'name'])->asArray()->all();
    }

    /**
     * 列表
     * @param $form
     * @return array
     */
    public function list($form): array
    {
        $query = byNew::TradeInGoodsModel()::find();
        $query->leftJoin(by::Gmain()::tableName(), 't_gmain.id = t_trade_in_goods.gid');
        $query->with(['category']);

        $query->andFilterWhere(['like', 'name', $form->name]);
        $query->andFilterWhere(['gid' => $form->gid]);
        $query->andFilterWhere(['category_id' => $form->category_id]);
        $query->select(['t_trade_in_goods.*', 't_gmain.name','t_gmain.is_del', 't_gmain.status as status']);
        $query->orderBy('id desc');

        $activity_goods = byNew::TradeInActivityGoodsModel()->getActivityGoods();

        return CUtil::Pg($query, function ($item) use ($activity_goods) {
            $item['activity_status'] = isset($activity_goods[$item['gid']]) ? 1 : 0;
            $item['goods']           = [
                    'name'   => $item['name'],
                    'status' => $item['status'],
                    'id'     => $item['gid'],
                    'is_del'     => $item['is_del']
            ];
            unset($item['name']);
            unset($item['status']);
            return $this->__format($item);

        });
    }

    /**
     * 详情
     * @param $form
     * @return array
     * @throws \yii\db\Exception
     */
    public function detail($form): array
    {
        $data = byNew::TradeInGoodsModel()::find()
                ->with(['goods', 'category'])
                ->andWhere(['id' => $form->id])
                ->asArray()
                ->one();

        if (empty($data)) {
            return [];
        }

        $data['specs'] = by::Gspecs()->getSpecNameByGid($data['gid']);

        return $this->__format($data);
    }

    /**
     * 保存
     * @param $form
     * @return array
     * @throws Exception
     */
    public function store($form): array
    {
        $activity = CUtil::mSave(byNew::TradeInGoodsModel(), $form->toArray());

        if (!$activity['status']) {
            throw new Exception('保存失败');
        }

        $sids = explode(',', $form->sids);

        $activityGids = byNew::TradeInActivityGoodsModel()::find()->where(['gid' => $form->gid, 'is_del' => 0])->asArray()->all();
        $modify       = Collection::make($activityGids)->groupBy('activity_id')
                ->map(function ($specs, $activity_id) use ($sids, $form) {
                    $old_sids = $specs->pluck('sid')->toArray();
                    $add_sids = array_diff($sids, $old_sids);
                    $del_sids = array_diff($old_sids, $sids);
                    $add_list = array_map(function ($sid) use ($activity_id, $form) {
                        return [
                                'activity_id' => $activity_id,
                                'gid'         => $form->gid,
                                'sid'         => $sid,
                                'ctime'       => time(),
                                'utime'       => time(),
                        ];
                    }, $add_sids);
                    $del_list = array_map(function ($sid) use ($activity_id, $form) {
                        return [
                                'activity_id' => $activity_id,
                                'gid'         => $form->gid,
                                'sid'         => $sid,
                                'ctime'       => time(),
                                'utime'       => time(),
                                'is_del'      => 1,
                        ];
                    }, $del_sids);
                    return ['add' => $add_list, 'del' => $del_list];
                })
                ->toArray();

        $addData = Collection::make($modify)->pluck('add')->collapse()->toArray();
        $delData = Collection::make($modify)->pluck('del')->collapse()->toArray();


        // 添加
        if (!empty($addData)) {
            byNew::TradeInActivityGoodsModel()::getDb()->createCommand()
                    ->batchInsert(
                            byNew::TradeInActivityGoodsModel()::tableName(),
                            ['activity_id', 'gid', 'sid', 'ctime', 'utime'],
                            $addData
                    )
                    ->execute();

            // 更新活动状态 has_goods' => 0
            $activity_ids = Collection::make($addData)->pluck('activity_id')->toArray();
            byNew::TradeInActivityModel()::updateAll(['has_goods' => 0], ['id' => $activity_ids]);
        }

        // 删除
        if (!empty($delData)) {
            byNew::TradeInActivityGoodsModel()::updateAll(['is_del' => 1], ['gid' => $form->gid, 'sid' => $delData]);
        }

        return $activity['data'];
    }


    /**
     * 删除
     * @param $id
     * @return array
     */
    public function delete($id): array
    {
        $gid = byNew::TradeInGoodsModel()::find()->select('gid')->where(['id' => $id])->scalar();
        if (empty($gid)) {
            return ['status' => false, 'msg' => '商品不存在'];
        }
        try {
            byNew::TradeInGoodsModel()::deleteAll(['id' => $id]);
            // 删除活动关联
            byNew::TradeInActivityGoodsModel()::updateAll(['is_del' => 1], ['gid' => $gid]);
            return ['status' => true];
        } catch (Exception $e) {
            return ['status' => false, 'msg' => '删除失败'];
        }

    }

    /**
     * 关联活动商品
     */
    public function relateActivityGoods($form): array
    {
        $activityIds = explode(',', $form->activity_ids);
        $gids        = explode(',', $form->gids);

        $goods = byNew::TradeInGoodsModel()::find()
                ->select(['gid', 'sids'])
                ->where(['gid' => $gids])
                ->asArray()
                ->all();

        if (count($goods) != count($gids)) {
            return ['status' => false, 'msg' => '商品不存在'];
        }

        $data = [];

        foreach ($activityIds as $activityId) {
            foreach ($goods as $good) {
                $gid  = $good['gid'];
                $sids = explode(',', $good['sids']);
                foreach ($sids as $sid) {
                    $data[] = [
                            'activity_id' => $activityId,
                            'gid'         => $gid,
                            'sid'         => $sid,
                            'ctime'       => time(),
                            'utime'       => time(),
                    ];
                }
            }
            // 更新活动状态 has_goods = 0
            byNew::TradeInActivityModel()::updateAll(['has_goods' => 0], ['id' => $activityId]);
        }

        $res = byNew::TradeInActivityGoodsModel()::getDb()->createCommand()
                ->batchInsert(
                        byNew::TradeInActivityGoodsModel()::tableName(),
                        ['activity_id', 'gid', 'sid', 'ctime', 'utime'],
                        $data
                )
                ->execute();


        return $res ? ['status' => true] : ['status' => false, 'msg' => '关联失败'];

    }


    /**
     * 格式化数据
     */
    public function __format($item): array
    {
        return $item;
    }
}