<?php

namespace app\modules\back\services;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\SubsidyActivityModel;
use app\modules\goods\models\SubsidyActivityGoodsModel;
use app\modules\back\forms\SubsidyActivityForm;
use yii\db\Exception;

/**
 * 国补活动服务类
 */
class SubsidyActivityService
{
    private static $_instance = null;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取活动列表
     * @param SubsidyActivityForm $form
     * @return array
     */
    public function getActivityList(SubsidyActivityForm $form): array
    {
        $model = byNew::SubsidyActivityModel();
        $params = [
                'name' => $form->search_name,
        ];

        $query = $model->getActivityList($params);

        return CUtil::pg($query, function ($item) {
            return $this->formatActivityItem($item);
        });

    }

    /**
     * 获取活动详情
     * @param int $id
     * @return array
     * @throws Exception
     */
    public function getActivityDetail(int $id): array
    {
        $model = SubsidyActivityModel::find()
                ->where(['id' => $id, 'is_deleted' => SubsidyActivityModel::IS_DELETED['NO']])
                ->one();

        if (!$model) {
            return [];
        }

        $activity = $model->toArray();
        $activity = $this->formatActivityItem($activity);

        // 获取关联商品
        $goodsModel        = new SubsidyActivityGoodsModel();
        $activity['goods'] = $goodsModel->getActivityGoods($id);

        return $activity;
    }

    /**
     * 保存活动
     * @param SubsidyActivityForm $form
     * @return array
     */
    public function saveActivity(SubsidyActivityForm $form): array
    {
        $data = [
                'id'         => $form->id,
                'name'       => $form->name,
                'start_time' => $form->start_time,
                'end_time'   => $form->end_time,
                'desc'       => $form->desc,
                'goods'      => $form->goods,
        ];

        $model = byNew::SubsidyActivityModel();
        return $model->saveActivity($data);
    }

    /**
     * 删除活动
     * @param int $id
     * @return array
     */
    public function deleteActivity(int $id): array
    {
        $model = byNew::SubsidyActivityModel();
        return $model->deleteActivity($id);
    }



    /**
     * 格式化活动数据
     * @param array $item
     * @return array
     */
    private function formatActivityItem(array $item): array
    {
        // 活动时间状态
        $item['time_status'] = SubsidyActivityForm::getTimeStatusText($item['start_time'], $item['end_time']);
        $item['is_active']   = SubsidyActivityForm::isActivityActive($item['start_time'], $item['end_time']);
        return $item;
    }

    /**
     * 获取当前生效的国补活动
     * @return array|null
     * @throws Exception
     * @throws \Throwable
     */
    public function getCurrentActivity()
    {
        $model    = byNew::SubsidyActivityModel();
        $activity = $model->getCurrentActivity();

        if (!$activity) {
            return null;
        }

//        $result = $this->formatActivityItem($activity);
//
//        // 获取关联商品
//        $goodsModel      = new SubsidyActivityGoodsModel();
//        $result['goods'] = $goodsModel->getActivityGoods($activity['id']);

        $result['tids'] = byNew::SubsidyActivityGoodsModel()->getActivityGoodsTid($activity['id']);
        $result['activity_id'] = $activity['id'];

        return $result;
    }

    /**
     * 获取商品的国补比例
     * @param int $gid
     * @param int|null $activity_id
     * @return float
     * @throws \Throwable
     */
    public function getGoodsSubsidyRatio(int $gid, int $activity_id = null): float
    {
        $goodsModel = new SubsidyActivityGoodsModel();
        return $goodsModel->getGoodsSubsidyRatio($gid, $activity_id);
    }


    /**
     * 获取商品的国补金额
     * @param int $gid
     * @param float $price
     * @return float
     * @throws \Throwable
     */
    public function getGoodsSubsidyAmount($gid, $price): float
    {
      return  byNew::SubsidyActivityGoodsModel()->getGoodsSubsidyAmount((int)$gid,(float)$price);
    }

    /**
     * 检查商品是否参与国补活动
     * @param int $gid
     * @param int|null $activity_id
     * @return bool
     * @throws \Throwable
     */
    public function isGoodsInSubsidy(int $gid, int $activity_id = null): bool
    {
        $goodsModel = new SubsidyActivityGoodsModel();
        return $goodsModel->isGoodsInSubsidy($gid, $activity_id);
    }


    /**
     * 验证活动是否可以编辑
     * @param int $id
     * @return array [bool, string]
     */
    public function canEditActivity(int $id): array
    {
        $activity = SubsidyActivityModel::findOne($id);
        if (!$activity || $activity->is_deleted == SubsidyActivityModel::IS_DELETED['YES']) {
            return [false, '活动不存在'];
        }

        // 已开始的活动不能编辑时间和商品
//        if ($activity->start_time <= time()) {
//            return [false, '已开始的活动不能修改'];
//        }

        return [true, ''];
    }

    /**
     * 验证活动是否可以删除
     * @param int $id
     * @return array [bool, string]
     */
    public function canDeleteActivity(int $id): array
    {
        $activity = SubsidyActivityModel::findOne($id);
        if (!$activity || $activity->is_deleted == SubsidyActivityModel::IS_DELETED['YES']) {
            return [false, '活动不存在'];
        }

        // 进行中的活动不能删除
        if (SubsidyActivityForm::isActivityActive($activity->start_time, $activity->end_time)) {
            return [false, '进行中的活动不能删除'];
        }

        return [true, ''];
    }

    /**
     * 获取活动商品列表
     * @param $activity_id
     * @param $tid
     * @return array
     * @throws \Throwable
     */
    public function getActivityGoods($activity_id,$tid)
    {
        return byNew::SubsidyActivityGoodsModel()->getActivityGoods($activity_id, $tid);
    }


    public function getActivityGoodsTid($activity_id)
    {
        return byNew::SubsidyActivityGoodsModel()->getActivityGoodsTid($activity_id);

    }
}
