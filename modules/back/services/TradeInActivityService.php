<?php
namespace app\modules\back\services;

use app\components\collection\Collection;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use Exception;

class TradeInActivityService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 列表
     * @param $form
     * @return array
     */
    public function list($form): array
    {

        $query = byNew::TradeInActivityModel()::find();

        $query->where(['is_del' => 0]);
        $query->select(['id', 'name', 'start_time', 'end_time', 'status', 'has_goods']);

        $query->andFilterWhere(['id' => $form->id]);
        $query->andFilterWhere(['status' => $form->status]);
        $query->andFilterWhere(['like', 'name', $form->name]);
        // 包含在时间范围内的所有
        $startTime = $form->start_time;
        $endTime   = $form->end_time;
        if ($startTime && $endTime) {
            $query->andFilterWhere(['or',
                                    ['and', ['<=', 'start_time', $startTime], ['>=', 'end_time', $startTime]],
                                    ['and', ['<=', 'start_time', $endTime], ['>=', 'end_time', $endTime]],
                                    ['and', ['>=', 'start_time', $startTime], ['<=', 'end_time', $endTime]],
                                    ['and', ['<=', 'start_time', $startTime], ['>=', 'end_time', $endTime]]
            ]);
        }
        $query->orderBy('id desc');

        return CUtil::Pg($query);
    }

    /**
     * 详情
     * @param $form
     * @return array
     */
    public function detail($form): array
    {
        $data = byNew::TradeInActivityModel()::find()
                ->with(['goodsList' => function ($query) {
                    $query->with(['tadeInGoods' => function ($query) {
                        $query->with(['category', 'goods' => function ($query) {
                            $query->with(['type0' => function ($query) {
                                $query->select(['gid', 'price', 'mprice']);
                            }]);
                        }]);
                    }]);
                }])
                ->andWhere(['id' => $form->id])
                ->asArray()
                ->one();

        return $this->__format($data);
    }

    /**
     * 保存
     * @param $form
     * @return array
     * @throws Exception
     */
    public function store($form): array
    {
        $saveData = $form->toArray();
        $saveData['has_goods'] = empty($form->goods)?0:1;

        $activityGoods = CUtil::mSaveMultiple(byNew::TradeInActivityGoodsModel(), $form->goods, ['activity_id' => $form->id]);
        if (!$activityGoods['status']) {
            throw new Exception('保存失败');
        }

        // 新增活动
        if (empty($form->id)) {
            $saveData['products'] = '[]';
            $saveData['coupons']  = '{}';
            $saveData['extras']   = '{"rules":"亲爱的用户，您好:<br/>追觅会员官方商城(以下简称「追觅」)旨在交易服务以及您（即“用户”）的交易体验。在您仔细阅读、了解并同意本政策之前，请不要向追觅提交任何信息。<br/>一、交易流程<br/>1.旧机回收交易流程<br/>回收服务及技术支持由熊洞智家（北京）科技有限公司提供(以下简称「熊洞智家」)<br/>1.1 用户选择确认参与以旧换新活动的旧机，追觅根据熊洞智家评估模型实时报价;<br/>1.2 用户填写旧机取货信息，提交旧机回收订单;<br/>1.3 用户填写指定新机收货信息，提交新机购买订单；<br/>1.4 新机购买订单按商城服务规则正常流转发货至用户收货地址；<br/>1.5 熊洞智家合作快递上门收取旧机;<br/>1.6 等待熊洞智家旧机质检核对;<br/>1.7 回收价格确认：如旧机估值大于等于评估报价，旧机回收订单将流转至用户提现流程；如旧机估值小于评估报价，用户需确认是否接受以实际报价回收旧机/或关闭回收;<br/>1.8 提现流程：用户确认回收金额后，可通过回收订单页面，跳转熊洞智家小程序账户完成提现（具体以您与回收方约定执行）。<br/><br/>2.选择机型<br/>您应该在追觅官网商城上选择准确的产品型号;您应保证自己填写的产品型号信息准确、真实。因您填写的产品型号信息错误导致估价和/或最终成交价格低于评估价格的，您应自行承担后果;<br/>3 新机购买：目前仅开放指定机型参加以旧换新购买政策；您在填写旧机信息后，仍需支付新机全价完成购买；旧机质检核对无误后，将开放回收金额提现流程；<br/>4.质检流程估价是追觅与熊洞智家根据您提供的产品型号做出的预估价格，并非最终价格。熊洞智家在收到物品后会根据您之前选择的产品型号进行核对。如因您选择的产品型号与实物不符，根据产品的实际情况，与您协商确认最终价格及解决方式。<br/>5.确认旧机回收价<br/>如您选择的产品型号与实物不符，客服会将联系您，根据产品的实际价格与您进行确认后，如果您不愿意接受实际回收价格，选择关闭回收订单;<br/>您知晓并理解，追觅与熊洞智家在收到旧机并与您完成回收金额确认后，将不支持再向您退回旧机。<br/><br/>二、交易规则<br/>每位用户限参与2次以旧旧换新活动；<br/>交易过程中碰到任何问题，可在线联系追觅会员官方商城客服或致电追觅客服热线400-875-9511进行咨询。<br/><br/>三、退换规则<br/>若您通过以旧换新活动购买追觅指定新机器，并在无理由退换货期限内进行退货，追觅将按照您实际支付的金额对您退款。您知晓并理解，您无法再次通过提交已回收的旧机信息，用于参与以旧换新活动购买新机。<br/><br/>相关FAQ<br/>1.找不到机器型号怎么办？<br/>将您的机器翻转至背面，可找到产品的对应型号。如无法确认型号，可联系客服协助。<br/>2.为什么要确认回收产品型号？<br/>确认型号，才能对产品准确估价;如果不如实描述，需要追觅根据实物重新估价，与您重新确认价格。<br/>3.旧机回收用户，提交订单后需要做什么？<br/>等待合作方联系快递上门收件。<br/>4.旧机回收快递费谁来承担<br/>京东物流覆盖范围内:快递上门回收;<br/>京东物流覆盖范围外:需要顾客使用其他快递寄回，并先垫付运费，寄出后联系客服提供运费凭证(发票或面单运费截图)登记退运费；<br/>非活动型号追觅均不承担任何费用。<br/>5.如果不想回收了，可以取消订单吗?<br/>订单新机未发货前，可直接取消订单; 此时取消新机订单会同步取消您的旧机回收订单；新机发货后，烦请联系客服做退货退款。<br/>熊洞智家未收到机器前或未确认实际报价前，您可以随时取消订单。熊洞智家提供旧机报价并与您确认后，不做退回处理。<br/><br/>四、免责申明<br/>1.交易应秉承合法、公平、透明、合理的原则进行。<br/>2.交易物品应为追觅正规销售渠道产品<br/>3.您应保证对寄回的旧机拥有合法、完整的处分权利。如追觅发现您提交的旧机为非法渠道物品，有权依法向公安司法机关报案，将物品转交公安司法机关，由您承担一切法律责任。<br/>4.交易完成后，追觅及合作方有权对寄回的旧机进行处理，对旧机不作保留和预留。","questions":[{"question":"支持以旧换新的品类有哪些？","answer":"目前支持扫地机器人,洗地机,吸尘器,吹风机品类的以旧换新,具体的回收类目以旧机信息展示页面为准。"},{"question":"以旧换新能叠加其他优惠吗？","answer":"享受以旧换新的机器, 可以叠加积分、优惠券等类型的活动优惠,购买后返的积分和觅享分将正常发放。但无法与预售活动，团购活动等特殊活动类型叠加权益。"},{"question":"旧机的质检将收取我费用吗？","answer":"不会，我们收到您的旧机后，将进行免费专业的质检，除此之外我们将根据质检结果核实给您最终的换新补贴价，若与评估价格有差异，我们将联系您，根据您的意愿进行估价变更或者旧机退回处理。"},{"question":"我可以只回收旧机不购买新机吗？","answer":"不能，新机订单与旧机订单为关联订单，若旧机不完成或者各类原因导致无法顺利回收，新机订单也将取消。"}]}';
        }

        $activity = CUtil::mSave(byNew::TradeInActivityModel(), $saveData);
        if (!$activity['status']) {
            throw new Exception('保存失败');
        }

        return $activity['data'];
    }


    /**
     * 删除
     * @param $id
     * @return array
     */
    public function delete($id): array
    {
        return CUtil::mDelete(byNew::TradeInActivityModel(), $id);
    }

    /**
     * 格式化数据
     * @param $item
     * @return array
     */
    public function __format($item): array
    {
        $item['goodsList'] = Collection::make($item['goodsList'])->map(function ($goods) {
            $goods['spec'] = $goods['sid'] > 0 ? by::Gspecs()->getSpecNameByGid($goods['gid']) : [];
            return $goods;
        })->sortBy('tadeInGoods.id')->groupBy('tadeInGoods.category.name')->map(function ($item, $key) {
            return ['category' => $key, 'list' => $item];
        })->values()->toArray();

        // goodsList.*.list.*.tadeInGoods.goods.type0.price 价格分转为元
        $item['goodsList'] = Collection::make($item['goodsList'])->map(function ($goods) {
            $goods['list'] = Collection::make($goods['list'])->map(function ($goods) {
                $goods['tadeInGoods']['goods']['type0']['price']  = CUtil::totalFee($goods['tadeInGoods']['goods']['type0']['price'], 1);
                $goods['tadeInGoods']['goods']['type0']['mprice'] = CUtil::totalFee($goods['tadeInGoods']['goods']['type0']['mprice'], 1);
                return $goods;
            })->toArray();
            return $goods;
        })->toArray();

        return $item;
    }

    /**
     * 设置活动上下架状态
     * @throws Exception
     */
    public function status($form): array
    {
        $id       = $form->id;
        $status   = $form->status;
        $activity = byNew::TradeInActivityModel()::findOne($id);

        // 上架商品
        if ($status == 1) {
            $goodsCount = byNew::TradeInActivityGoodsModel()::find()->select(['gid'])->where(['activity_id' => $id])->andWhere(['or', ['price' => 0], ['underline_price' => 0]])->count();
            if ($goodsCount > 0) {
                return ['status' => false, 'msg' => '请先配置商品信息！！'];
            }
        }

        // 请先配置商品
        if ($activity->has_goods == 0 && $status == 1) {
            return ['status' => false, 'msg' => '请先配置商品信息!'];
        }


        if (!$activity) {
            return ['status' => false, 'msg' => '活动不存在'];
        }
        $activity->status = $status;
        $activity->save();

        byNew::TradeInActivityModel()->delCacheList();

        return ['status' => true];
    }

}