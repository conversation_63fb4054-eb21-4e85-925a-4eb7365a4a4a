<?php

namespace app\modules\back\services;



use app\components\AliYunOss;
use app\components\MemberCenter;
use app\jobs\PointPushJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\forms\points\PointsGiveForm;
use yii\db\Exception;

/**
 * 积分/觅享分发放服务
 */
class PointPushService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * @param array $arr
     * @param int $page
     * @param int $pageSize
     * @return array
     * 获取积分/觅享分发放记录列表
     */
    public function GetPointPushList(array $arr, $page = 1, $pageSize = 20): array
    {
        $pointPushModel = byNew::PointPushModel();
        $module     = by::adminUserModel();
        // 查询封装
        if(!empty($arr['admin_user'])){
            $adminUsers = $module->GetUsersList(['nick' => $arr['admin_user']],1,1000);
            $adminIds = array_column($adminUsers, 'id');
            $arr['admin_ids'] = $adminIds;
            if(empty($adminIds)){//没有数据
                return ['pages' => 0, 'list' => [], 'page' => $page, 'pageSize' => $pageSize, 'count' => 0];
            }
            unset($arr['admin_user']);
        }


        $count = $pointPushModel->getCount($arr);
        $paginationPages = CUtil::getPaginationPages($count, $pageSize);
        $list = $pointPushModel->getList($arr, $page, $pageSize);

        $pointEvent = $pointPushModel::POINT_EVENT;
        $modelType = $pointPushModel::MODEL_TYPE;
        $pushStatus = $pointPushModel::PUSH_STATUS;

        if($list){
            // 获取所有的后台用户ID
            $adminIds = array_filter(array_unique(array_column($list, 'admin_id')));
            $adminUsers = $module->GetUsersList(['ids' => $adminIds],1,1000);
            $adminUsers = array_column($adminUsers, 'nick', 'id');

            foreach ($list as $key => $value) {
                $list[$key]['event_name'] = $pointEvent[$value['event']];
                $list[$key]['model_name'] = $modelType[$value['model']];
                $list[$key]['status_name'] = $pushStatus[$value['status']];
                $list[$key]['admin_user'] = $adminUsers[$value['admin_id']] ?? '';
            }
        }

        return ['pages' => $paginationPages, 'list' => $list, 'page' => $page, 'pageSize' => $pageSize, 'count' => $count];
    }


    /**
     * @throws Exception
     * @throws \RedisException
     */
    public function BatchGivePoints($post, $file, $backUserId): array
    {
        $pointPushModel = byNew::PointPushModel();
        $redis = by::redis();
        //防止多重点击上传，单人限制3s
        $unique_key = CUtil::getAllParams(__FUNCTION__, $backUserId);
        list($anti) = $pointPushModel->ReqAntiConcurrency($backUserId, $unique_key, 3, 'EX');
        if (!$anti) {
            return [false, '3s内请勿重复点击！'];
        }

        if (empty($file) && empty($post['user_no'])) {
            return [false,'请上传文件或者填写用户UID或者手机号！'];
        }

        $modelType = $pointPushModel::MODEL_TYPE;
        $pointEvent = $pointPushModel::POINT_EVENT;

        $modelTypeRev = array_flip($modelType);
        $pointEventRev = array_flip($pointEvent);

        $data = [];
        //数据整合
        if(!empty($post['user_no'])){
            $data[] = [
                'user_no'    => strtoupper((string)($post['user_no'])),
                'score'      => $post['score'] ?? '',
                'model'      => $post['model'] ?? '',
                'source'     => $post['source'] ?? '',
                'sub_source' => $post['sub_source'] ?? '',
                'type'       => $post['type'], //默认增加
                'event'      => $post['event'] ?? '',
            ];
        }

        if($file){
            $filename = $file['tmp_name'] ?? '';
            if (!$filename) {
                return [false, '请选择文件'];
            }

            $name = explode('.', $file['name']);
            if (array_pop($name) != 'csv') {
                return [false, '请上传csv类型文件'];
            }

            $handle = fopen($filename, 'r');
            list($s, $dataCsv) = $this->__anCsv($handle);
            if (!$s) {
                return [false, $dataCsv];
            }

            if (!count($dataCsv)) {
                return [false, '没有任何数据'];
            }
            foreach ($dataCsv as $key => $item) {
                $dataCsv[$key]['type'] = $post['type'];//增减
            }
            $data = array_merge($data, $dataCsv);
        }

        //1. 数据校验重复user_no
        $userNoArr = array_column($data, 'user_no');
        $userNoArr = array_unique($userNoArr);
        if(count($userNoArr) != count($data)){
            return [false, '用户UID或手机号重复！'];
        }

        $failData = [];
        //2. 数据逐行验证
        foreach ($data as $key => $item) {
            list($s, $msg) = PointsGiveForm::validateInput($item, $modelType, $pointEvent);
            if (!$s) {
                $failData[$key]['msg'] = $msg;
            }
        }

        //3. 校验用户是否存在
       //a.把$userNoArr中11位的分为一组，不是11位的分为一组
        $userNoArr11 = [];
        $userNoArrNot11 = [];
        $userNoArrUserIds = [];
        foreach ($userNoArr as $value) {
            if(strlen($value) == 11){
                $userNoArr11[] = $value;
            }elseif(intval($value)==0){
                $userNoArrNot11[] = $value;
            }else{
                $userNoArrUserIds[] = intval($value);
            }
        }
        $userNoArr11 = array_filter(array_unique($userNoArr11));
        $userNoArrNot11 = array_filter(array_unique($userNoArrNot11));
        $userNoArrUserIds = array_filter(array_unique($userNoArrUserIds));

        $userPhones = [];
        if(!empty($userNoArrUserIds)){
            // 查出手机号码集合
            $phoneInfos = by::phone()->getListByUserIds($userNoArrUserIds);
            $userPhones = array_unique(array_column($phoneInfos,'phone'));
        }

        $userNoArr11 = array_merge($userNoArr11,$userPhones);

        if(empty($userNoArr11) && empty($userNoArrNot11)){
            return [false, '所有用户UID或手机号不存在！'];
        }

        $userAll = by::usersMall()->GetDistinctUidsByUidOrPhone($userNoArrNot11,$userNoArr11);
        if(empty($userAll)){
            return [false, '所有用户不存在！'];
        }
        $userUids = array_column($userAll,'uid','phone');
        $userPhones = array_column($userAll,'phone','uid');
        $userDatas = by::phone()->GetInfosByMallIds(array_column($userAll,'id')); //获取用户信息
        $userIds = array_column($userDatas,'user_id','phone');
        $userPhones2 = array_column($userDatas,'phone','user_id');

        //4. 数据封装
        $insertData = [];
        $excelNo = 'U'.$backUserId.'E'.date('YmdHis').rand(100,999);
        $pushNos = [];
        foreach ($data as $key => $item) {
            $user_no = $item['user_no'];
            if (strlen($user_no) == 11) {
                $uid     = $userUids[$user_no] ?? '';
                $phone   = $user_no;
                $user_id = $userIds[$phone] ?? 0;
            } elseif(intval($user_no)==0) {
                $phone   = $userPhones[$user_no] ?? '';
                $uid     = $user_no;
                $user_id = $userIds[$phone] ?? 0;
            } else{
                $phone    = $userPhones2[$user_no] ?? '';
                $user_id      = $user_no;
                $uid      = $userUids[$phone] ?? '';
            }

            if (empty($uid) || empty($phone) || empty($user_id)) {
                $failData[$key] = $item;
                $failData[$key]['msg'] = ($failData[$key]['msg'] ?? '') . '用户不存在或已经注销！';
            }
            $pushNo = 'U' . $user_id . 'P' . date('YmdHis') . rand(100, 999);
            $insertData[] = [
                'uid'        => $uid,
                'user_id'    => $user_id,
                'phone'      => $phone,
                'score'      => $item['score'],
                'model'      => $modelTypeRev[$item['model']] ?? '',
                'type'       => $item['type'],//积分
                'source'     => $item['source'],
                'sub_source' => $item['sub_source'],
                'event'      => $pointEventRev[$item['event']] ?? '',
                'status'     => $pointPushModel::STATUS['wait'],//待发放
                'ctime'      => time(),
                'excel_no'   => $excelNo,
                'push_no'    => $pushNo,
                'admin_id'   => $backUserId,
            ];
            $pushNos[] = $pushNo;
        }

        //如果有相同的用户UID，30s内不允许存在相同的用户UID相同的数据
        $userUids = array_column($insertData,'uid');
        $userUids = array_filter(array_unique($userUids));
        CUtil::debug("uids:".json_encode($userUids,320).'|'.json_encode($insertData,320),'pointPush');
        if(empty($failData) && count($userUids) < count($insertData)){
            return [false, '存在重复手机号用户~'];
        }
        foreach ($insertData as $key => $item) {
            $giveKey  = $pointPushModel->__getGivePointByUid($item['uid']);
            $dataItem = $redis->get($giveKey);
            $jsonItem = json_encode([
                'user_id'    => $item['user_id'],
                'score'      => $item['score'],
                'model'      => $item['model'],
                'type'       => $post['type'],//增减
                'source'     => $item['source'],
                'sub_source' => $item['sub_source'],
                'event'      => $item['event'],
            ]);
            if ($dataItem === $jsonItem) {
                $failData[$key]['msg'] = ($failData[$key]['msg'] ?? '') . '30s内不允许存在相同的用户UID相同的数据！';
            }
        }

        if(!empty($failData)){
            // 有错误数据，组装导出到excel,并且存入redis
            $this->exportWrongData($data,$failData,$backUserId);
            return [false, '上传文件存在问题，具体查看下方列表~'];
        }

        //5. 数据入库
        $s = $pointPushModel->BatchInsertPoints($insertData);
        if(!$s){
            return [false, '数据上传失败！'];
        }
        foreach ($insertData as $key => $item) {
            $giveKey = $pointPushModel->__getGivePointByUid($item['uid']);
            $jsonItem = json_encode([
                'user_id'    => $item['user_id'],
                'score'      => $item['score'],
                'model'      => $item['model'],
                'type'       => $post['type'],//增减
                'source'     => $item['source'],
                'sub_source' => $item['sub_source'],
                'event'      => $item['event'],
            ]);
            $redis->set($giveKey, $jsonItem, 30);
        }

        // 6. 异步推送IOT添加积分
        \Yii::$app->queue->push(new PointPushJob(['data'=>implode(',',$pushNos)]));

        return [true, '数据上传成功！'];
    }


    private function __anCsv($handle): array
    {
        $out = [];
        $n = 1;

        while ($data = fgetcsv($handle, 1000)) {
            //模版校验
            if ($n == 1 && (count($data) < 3 || count($data) > 7)) {
                return [false, '模版错误~！'];
            }

            if ($n == 2 && !empty($data[1]) && !empty($data[2])) {
                return [false, '模版错误！'];
            }

            if ($n > 2 && !empty($data[0])) {
                $itemData = [
                    'user_no'    => mb_strtoupper(mb_convert_encoding((string)trim($data[0]), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"])),
                    'score'      => trim($data[1] ?? 0),
                    'model'      => mb_convert_encoding((string)trim($data[2] ?? ''), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                    'source'     => mb_convert_encoding((string)trim($data[3] ?? ''), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                    'sub_source' => mb_convert_encoding((string)trim($data[4] ?? ''), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                    'event'      => mb_convert_encoding((string)trim($data[5] ?? ''), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]),
                ];

                $out[]    = $itemData;
            }
            $n++;
        }

        return [true, array_values($out)];
    }


    private function exportWrongData($sourceData,$failData,$backUserId): array
    {
        $headList = [
            "会员UID/手机号码",
            "数值（不超过10000）",
            "类型(积分/觅享分）",
            "来源",
            "子来源",
            "积分事件（购物积分/活动积分/福利积分)",
            "错误信息"
        ];
        $data[0][0] = '注：不能存在空行/重复数据行，单次总行数不得超过1000';

        foreach ($sourceData as $key => $item) {
            unset($item['type']);
            $data[$key + 1] = $item;
            isset($failData[$key]['msg']) && $data[$key + 1]['msg'] = $failData[$key]['msg'];
        }

        $fileName = '积分或觅享分发放上传错误-' . $backUserId . '-' . date('Ymd') . mt_rand(1000, 9999);
        //临时存入本地缓存
        $data = CUtil::saveTmpFile($headList, $data, $fileName);
        if (isset($data['filename'])) {
            //读取文件上传阿里云oss
            if (file_exists($data['filename'])) {
                $file['name'] = $fileName . '.csv';
                $file['temp_name'] = $data['filename'];
                list($status, $link) = AliYunOss::factory()->uploadFileDirectToOss($file);
                //如果正确塞入redis
                if (!$status) {
                    return [false, '文件上传oss服务器失败！'];
                }
                $this->__pushCardRedis([
                    'url'          => $link['link'] ?? '',
                    'ctime'        => date('Y-m-d H:i:s'),
                    'back_user_id' => $backUserId
                ], $backUserId);
            }
        }
        return [true, '数据导出成功！'];
    }


    private function __pushCardRedis($data, $backUserId, $query = false)
    {
        $redis = by::redis();
        $key = byNew::PointPushModel()->__wrongExcelList($backUserId);
        if(!$query) {
            // 集合，时间作为sort
           return $redis->zAdd($key, time(), json_encode($data));
        }
        // 查询 降序
        return $redis->zRevRange($key, 0, -1);
    }


    public function GetUserUploadRecord($backUserId): array
    {
        $data = $this->__pushCardRedis([], $backUserId, true);
        if (empty($data)) {
            return [true, []];
        }
        $list = [];
        foreach ($data as $key => $item) {
            $list[] = json_decode($item, true);
        }
        return [true, $list];
    }


    public function BatchPushData($data): array
    {
        if(empty($data)){
            return [false,'数据为空~'];
        }
        $pointPushModel = byNew::PointPushModel();
        $body = [];
        foreach ($data as $item){
            $event = $item['event'] ?? '';
            if(!in_array($event,array_keys($pointPushModel::POINT_EVENT))){
                return [false,'数据积分事件错误~操作失败'];
            }
            $body[$event][] = $item;
        }

        $reData = [];
        foreach ($body as $key => $value){
            //进行推送
            list($status, $iotData) = MemberCenter::factory()->run('pointGrowSave', ['event' => $key,'data' => $value]);
            if($status){
                $reData = array_merge($reData,$iotData);
            }
        }
        if($reData){//批量更新
            //1.组合数据
            $upData = [];
            foreach ($reData as $key => $item){
                if(!empty($item['serialNo'])){
                    $upData[] = [
                        'push_no'      => $item['serialNo'],
                        'status'       => $pointPushModel::STATUS['success'],
                        'release_time' => time(),
                        'utime'        => time(),
                    ];
                }
            }
            $tb = $pointPushModel::tbName();
            $sql = CUtil::batchUpdate($upData,'push_no',$tb);
            $s = by::dbMaster()->createCommand($sql)->execute();
            if($s === false){
                return [false,'推送失败~'];
            }
        }else{
            return [false,'推送失败~'];
        }
        return [true,'操作成功'];
    }


    public function PushDataByIds($ids): array
    {
        if(empty($ids)){
            return [false,'数据为空~'];
        }
        $pointPushModel = byNew::PointPushModel();
        $data = $pointPushModel->GetList(['ids'=>$ids,'status'=>1],1,1000);
        if(empty($data)){
            return [false,'数据为空~'];
        }
        list($s,$res) = $this->BatchPushData($data);
        if(!$s){
            return [false,$res];
        }
        return [true,'操作成功'];
    }


    public function PushDataByPushNos($pushNo): array
    {
        if(empty($pushNo)){
            return [false,'数据为空~'];
        }
        $pointPushModel = byNew::PointPushModel();
        $data = $pointPushModel->GetList(['push_nos'=>$pushNo,'status'=>1],1,1000);
        if(empty($data)){
            return [false,'数据为空~'];
        }
        list($s,$res) = $this->BatchPushData($data);
        if(!$s){
            return [false,$res];
        }
        return [true,'操作成功'];
    }

    public function exportPointPushData($phone = '', $uid = '', $score = '', $model = '', $type = '', $source = '',  $event = '', $excel_no = '', $status = '', $release_start_time = '', $release_end_time = '', $admin_user = '', $start_time = '', $end_time = '', $viewSensitive = false)
    {
        $head = [
            '批次号', '手机号', '用户UID', '数值', '类型', '来源', '子来源', '发放事件', '上传时间', '上传人', '发放状态', '发放时间'
        ];
        $input = [
            'phone'      => $phone,
            'uid'        => $uid,
            'score'      => $score,
            'model'      => $model,
            'type'       => $type,
            'source'     => $source,
            'event'      => $event,
            'excel_no'   => $excel_no,
            'status'     => $status,
            'release_start_time' => $release_start_time,
            'release_end_time'   => $release_end_time,
            'admin_user' => $admin_user,
            'start_time' => $start_time,
            'end_time'   => $end_time,
        ];
        $id = 0;
        $pointPushModel = byNew::PointPushModel();
        $module     = by::adminUserModel();
        // 查询封装
        if(!empty($input['admin_user'])){
            $adminUsers = $module->GetUsersList(['nick' => $input['admin_user']],1,1000);
            $adminIds = array_column($adminUsers, 'id');
            $arr['admin_ids'] = $adminIds;
            unset($arr['admin_user']);
        }

        list($where, $params) = $pointPushModel->__getCondition($input);

        //查询数据
        $tb  = $pointPushModel::tbName();
        $sql = "SELECT * FROM {$tb} WHERE  `id` > :id AND {$where} ORDER BY `id` LIMIT 200";
        $db  = by::dbMaster();

        $pointEvent = $pointPushModel::POINT_EVENT;
        $modelType = $pointPushModel::MODEL_TYPE;
        $pushStatus = $pointPushModel::PUSH_STATUS;

        $data[] = $head;
        while (true) {
            $params['id'] = $id;
            $list = $db ->createCommand($sql, $params)->queryAll();

            if (empty($list)) {
                break;
            }


            $end = end($list);
            $id = $end['id'];

            // 获取所有的后台用户ID
            $adminIds = array_filter(array_unique(array_column($list, 'admin_id')));
            $adminUsers = $module->GetUsersList(['ids' => $adminIds],1,1000);
            $adminUsers = array_column($adminUsers, 'nick', 'id');
            foreach ($list as $val) {
                $data[] = [
                    'excel_no'     => $val['excel_no'],
                    'phone'        => $val['phone'],
                    'uid'          => $val['uid'],
                    'score'        => $val['score'],
                    'model_name'   => $modelType[$val['model']],
                    'source'       => $val['source'],
                    'sub_source'   => $val['sub_source'],
                    'event_name'   => $pointEvent[$val['event']],
                    'ctime'        => date('Y-m-d H:i:s', $val['ctime']),
                    'admin_user'   => $adminUsers[$val['admin_id']] ?? '',
                    'status_name'  => $pushStatus[$val['status']],
                    'release_time' => $val['status'] == 2 ? date('Y-m-d H:i:s', $val['release_time']) : '',
                ];
            }
        }
        return $data;
    }

}