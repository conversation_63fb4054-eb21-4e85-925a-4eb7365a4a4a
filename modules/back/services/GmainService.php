<?php

namespace app\modules\back\services;

use app\models\by;

class GmainService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 根据 cate_id 获取对比的商品 sku 、 name
     * @param $cateId
     * @return array
     */
    public function GetCompareList($cateId): array
    {
        // 判空
        if (empty($cateId)) {
            return [];
        }

        // 模型
        $cateModel = by::cateModel();
        $cate = $cateModel->getCateById($cateId);
        if (empty($cate)) {
            return [];
        }

        // 分类ID
        $cateIds = [];
        switch ($cate['level']) {
            case $cateModel::LEVEL['level_two']:
                $cateItems = by::cateModel()->getCateByPids([$cateId]);
                $cateIds = array_column($cateItems, 'id');
                break;
            case $cateModel::LEVEL['level_three']:
                $cateIds = [$cateId];
                break;
            default: // 不处理
                return [];
        }

        // 获取商品
        return by::Gmain()->getDataByCateIds($cateIds);
    }
}