<?php

namespace app\modules\back\services;

use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\common\Singleton;
use app\modules\goods\models\DreameStoreModel;
use app\modules\goods\models\StoreGoodsModel;
use yii\base\StaticInstanceTrait;

/**
 * 追觅小店可选商品 - 服务层 - 后台
 */
class DreameStoreGoodsService
{
    use StaticInstanceTrait;

    /**
     * @var StoreGoodsModel
     */
    public $model;

    public  function __construct()
    {
        $this->model = StoreGoodsModel::instance();
    }


    /**
     * 列表
     */
    public function getPageList(array $params = []): array
    {
        $ret = $this->model->getPageList($params);
        $gids = array_column($ret['list'], 'goods_id');
        $goodInfos = GoodsService::getInstance()->getGoodsDetails($gids);
        foreach ($ret['list'] as $k => $v) {
            $ret['list'][$k]['good_name'] = $goodInfos[$v['goods_id']]['name'] ?? ''; //商品名
            $ret['list'][$k]['goods_name'] = $goodInfos[$v['goods_id']]['name'] ?? ''; //商品名
            $ret['list'][$k]['sku'] = $goodInfos[$v['goods_id']]['sku'] ?? '';   //商品编号
            $ret['list'][$k]['price'] = $goodInfos[$v['goods_id']]['price'] ?? '';   //价格
            $ret['list'][$k]['tids_name'] = $goodInfos[$v['goods_id']]['tids_name'] ?? [];   //标签
            $ret['list'][$k]['image'] = $goodInfos[$v['goods_id']]['cover_image'] ?? '';   //封面图
            $ret['list'][$k]['tag_name'] =implode(',', array_column($ret['list'][$k]['tids_name'], 'name'));
        }
        return $ret;
    }

    /**
     * 详情
     */
    public function info($id): array
    {
        $info  = $this->model->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        return $info->toArray();
    }

    /**
     * 创建
     */
    public function create(array $data)
    {
        $goods_id = (int)($data['goods_id'] ?? 0);
        if (empty($goods_id)) {
            throw new BusinessException('缺少参数');
        }
        $info  = $this->model->getInfo(['goods_id' => $goods_id]);
        if ($info) {
            throw new BusinessException(sprintf('已存在'));
        }
        unset($data['id']);
        return $this->model->doCreate($data);
    }

    /**
     * 创建 只保存不存在的商品
     */
    public function batchCreate(array $data)
    {
        $goods_ids = $data['goods_ids'] ?? '';
        if (empty($goods_ids)) {
            throw new BusinessException('缺少参数');
        }
        $goods_ids = explode(',', (string) $goods_ids);
        $goods_ids = array_unique($goods_ids);
        $existGoodsIds = $this->model->find()
            ->where(['goods_id' => $goods_ids])
            ->select('goods_id')
            ->column();

        $newGoodIds = array_diff($goods_ids, $existGoodsIds);
        if (! $newGoodIds) {
            throw new BusinessException('商品已经存在列表');
        }
        $now = time();
        $rows = [];
        $rate = $data['rate'] ?? null;
        $fields  = ['goods_id', 'ctime', 'utime'];
        if (!empty($rate)) $fields[] = 'rate';
        foreach ($newGoodIds as $goods_id) {
            $row = [
                'goods_id' => $goods_id,
                'ctime' => $now,
                'utime' => $now,
            ];
            if (! empty($rate)) $row['rate'] = $rate;
            $rows[] = $row;
        }
        return $this->model->batch($fields, $rows);
    }

    /**
     * 更新
     */
    public function update(array $data)
    {
        $id = (int)($data['id'] ?? 0);
        if (empty($id)) {
            throw new BusinessException('ID不能为空');
        }
        $info  = $this->model->findOne($id);
        if (!$info) {
            throw new BusinessException(sprintf('ID[%s]不存在', $id));
        }
        $where = ['id' => $id];
        $up = $data;
        //可修改的数据
        unset($up['id'], $up['good_id']);
        $up = array_intersect_key($up, array_flip(['rate']));
        $up = array_filter($up, function ($v) { return $v !== null; });
        return $this->model->updateInfo($where, $up);
    }

    /**
     * 审核
     */
    public function audit($ids,$status)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = $this->model;
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $where = ['id' => $ids];
        $up = ['status' => $status];
        $up = array_filter($up, function ($v) {return (string)$v !== '';});
        return $model->updateInfo($where, $up);
    }

    /**
     * 删除
     */
    public function del($ids)
    {
        if (empty($ids)) {
            throw new BusinessException('IDs不能为空');
        }

        $model = $this->model;
        $infos = $model->getInfos($ids);
        foreach ($ids as $id) {
            if (empty($infos[$id])) {
                throw new BusinessException(sprintf('ID[%s]不存在', $id));
            }
        }
        $where = ['id' => $ids];
        return $model->deleteInfo($where);
    }
}