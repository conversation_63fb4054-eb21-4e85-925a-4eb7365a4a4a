<?php

namespace app\modules\back\services;

use app\components\AliYunSms;
use app\components\Employee;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\CouponModel;
use app\modules\main\models\UserBindModel;
use Exception;
use PhpOffice\PhpSpreadsheet\IOFactory;
use yii\db\StaleObjectException;

/**
 * 用户绑定服务
 */
class CouponService
{
    private static $_instance = NULL;

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    const MAX_BIND_COUNT = 100; // 最大绑定数量

    const STATUS_NAMES = [
            0 => '未领取',
            1 => '已领取',
            2 => '已作废'
    ];


    /**
     * 卡券列表
     * @param array $params
     * @return array
     */
    public function getList(array $params): array
    {
        $query = by::CouponModel()::find()
                ->where(['is_deleted' => 0])
                ->andFilterWhere(['like', 'name', $params['name'] ?? ''])
                ->andFilterWhere(['code' => $params['code'] ?? ''])
                ->andFilterWhere(['>=', 'create_at', $params['create_begin_at'] ?? ''])
                ->andFilterWhere(['<=', 'create_at', $params['create_end_at'] ?? ''])
                ->andFilterWhere(['>=', 'validity', $params['begin_validity'] ?? ''])
                ->andFilterWhere(['<=', 'validity', $params['end_validity'] ?? ''])
                ->orderBy(['id' => SORT_DESC]);

        return CUtil::Pg($query);
    }

    /**
     * 保存卡券
     * @param array $params
     * @return array
     * @throws Exception
     * @throws \yii\base\Exception
     */
    public function save(array $params, $file): array
    {
        // 数据验证
        $id       = $params['id'] ?? 0;
        $name     = $params['name'] ?? '';
        $type     = $params['type'] ?? 0;
        $validity = $params['validity'] ?? 0;
        $image    = $params['image'] ?? '';
        if (empty($name)) {
            return [false, '卡券名称不能为空!'];
        }
        if (empty($type)) {
            return [false, '卡券类型不能为空!'];
        }
        $save                = [];
        $save['name']        = $name;
        $save['type']        = $type;
        $save['validity']    = $validity;
        $save['image']       = $image;
        $save['appid']       = $params['appid'] ?? '';
        $save['app_path']    = $params['app_path'] ?? '';
        $save['originid']    = $params['originid'] ?? '';
        $save['origin_path'] = $params['origin_path'] ?? '';
        $save['short_chain'] = $params['short_chain'] ?? '';
        $save['update_at']   = time();


        $codeList = [];

        if ($file) {
            //解析券码文档
            $spreadsheet = IOFactory::load($file['tmp_name']);
            $sheet       = $spreadsheet->getActiveSheet();

            foreach ($sheet->getRowIterator() as $index => $row) {
                if ($index == 1) continue;
                // 读取行数据
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 获取所有单元格，即使它们没有值
                $rowNumber = $row->getRowIndex();

                $rowData = [];
                foreach ($cellIterator as $cell) {
                    $val = $cell->getValue();
                    // 去除两端空格
                    $val = trim($val);

                    if (empty($val)) { // 判空
                        return [false, '第' . $rowNumber . '行' . '文件内容存在空值'];
                    }

                    // 判断券码是否为数字+字母且小于等于35位
                    if (!preg_match('/^[0-9a-zA-Z]{1,35}$/', $val)) {
                        return [false, '券码格式不正确：(' . $val . ') 仅支持数字+字母且小于等于35位'];
                    }

                    $rowData[] = $val;
                }
                if (isset($rowData[0])) {
                    $codeList[] = $rowData[0];
                }
            }
            if (count($codeList) == 0) {
                return [false, '文件内容为空'];
            }

            // 判断券码是否有重复
            $uniqueArray    = array_unique($codeList);
            $duplicateArray = array_diff_assoc($codeList, $uniqueArray);
            if (!empty($duplicateArray)) {
                // Group duplicates by their values
                $duplicates = array_count_values(array_intersect($codeList, $duplicateArray));
                $duplicateDetails = [];
                foreach ($duplicates as $value => $count) {
                    $duplicateDetails[] = $value . ' (出现' . $count . '次)';
                }
                return [false, '文件内容存在重复值: ' . implode('、', $duplicateDetails)];
            }


        }

        $db    = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 卡券存在即更新，不存在即新增
            if ($id) {
                $info = by::CouponModel()->getInfoById($id);
                if (empty($info)) {
                    $save['create_at'] = time();
                    $save['code']      = 'KQ' . date('YmdHms') . rand(100000, 999999);
                    $code              = $save['code'];
                    $status            = by::CouponModel()->add($save);
                } else {
                    $code              = $info['code'];
                    $save['update_at'] = time();
                    $save['id']        = $id;
                    $status            = CUtil::mSave(by::CouponModel(), $save);
                }
            } else {
                $save['create_at'] = time();
                $save['code']      = 'KQ' . date('YmdHms') . rand(100000, 999999);
                $code              = $save['code'];
                $status            = by::CouponModel()->add($save);
            }

            if (!$status) {
                throw new Exception('卡券保存失败!');
            }
            // 保存券码子表
            $codeList = array_chunk($codeList, 1000);
            foreach ($codeList as $list) {
                $status = by::CouponCodeModel()->addBatch($code, $list);
                if (!$status) {
                    throw new Exception('券码保存失败!');
                }
            }

            // 更新卡券库存
            $this->updateCouponStock($code);

            $trans->commit();
            return [true, '操作成功!'];
        } catch (Exception $e) {
            $trans->rollBack();
            // CUtil::debug('保存操作失败:'.$e->getMessage() ,'coupon.info');
            return [false, $e->getMessage()];
        }
    }

    public function getCodeList(array $params): array
    {
        $code  = $params['code'] ?? '';
        $query = by::CouponCodeModel()::find()
                ->where(['coupon_code' => $code, 'is_deleted' => 0]);
        return CUtil::Pg($query, function ($val) {
            $val['status_name'] = self::STATUS_NAMES[$val['status']] ?? '未知状态';
            // ticket_code 中间替换为* 两头5位显示
            $val['ticket_code'] = substr($val['ticket_code'], 0, 5) . str_repeat('*', strlen($val['ticket_code']) - 10) . substr($val['ticket_code'], -5);
            return $val;
        });
    }

    /**
     * 删除卡券
     * @param $id
     * @return array
     */
    public function delete($id): array
    {
        $result = byNew::CouponModel()::updateAll(['is_deleted' => 1], ['id' => $id]);

        if (!$result) {
            return [false, '删除失败'];
        }
        return [true, '删除成功'];
    }

    /**
     * 发放优惠券
     * @param $code
     * @param $user_id
     * @param string $order_no
     * @param string $phone
     * @return array
     */
    public function send($code, $user_id, string $order_no='', string $phone = ''): array
    {
        // 验证卡券有效期
        $coupon = byNew::CouponModel()::find()
                ->where(['code' => $code, 'is_deleted' => 0])->andWhere(['>=', 'validity', time()])
                ->one();
        if (!$coupon) {
            return [false, '卡券不存在或已过期'];
        }

        $coupon_name     = $coupon->name ?? '卡券';
        $coupon_validity = $coupon->validity ?? '';
        $ticketType      = $coupon->type ?? 0;

        // 验证卡券码
        return $this->sendTicketCode($code,$ticketType, $user_id, $order_no, $coupon_name, $coupon_validity, $phone);
    }

    /**
     * 获取停车券码（乐观锁版本）
     * @param string $code
     * @param $ticketType
     * @param int $user_id
     * @param string $order_no
     * @param $coupon_name
     * @param $coupon_validity
     * @param string $phone
     * @return array
     */
    private function sendTicketCode(string $code,$ticketType, int $user_id, string $order_no, $coupon_name, $coupon_validity, string $phone = ''): array
    {
        $maxRetries   = 6;  // 最大尝试次数
        $attemptedIds = []; // 记录已尝试的ID

        for ($retry = 0; $retry < $maxRetries; $retry++) {
            // 查询可用的优惠券（排除已尝试的ID）
            $ticketCode = byNew::CouponCodeModel()::find()
                    ->where([
                            'coupon_code' => $code,
                            'status'      => 0,
                            'is_deleted'  => 0
                    ])
                    ->andWhere(['NOT IN', 'id', $attemptedIds])
                    ->orderBy(['id' => SORT_ASC])
                    ->one();

            if (!$ticketCode) {
                return [false, '优惠券已领完'];
            }

            // 记录尝试的ID
            $attemptedIds[] = $ticketCode->id;
            try {
                // 使用乐观锁进行更新
                $rowsUpdated = byNew::CouponCodeModel()::updateAll(
                        [
                                'status'          => 1,
                                'receive_at'      => time(),
                                'receive_user_id' => $user_id,
                                'order_no'        => $order_no
                        ],
                        [
                                'id'     => $ticketCode->id,
                                'status' => 0 // 原子条件检查
                        ]
                );

                if ($rowsUpdated > 0) {
                    // 更新库存
                    $this->updateCouponStock($code);
                    // 短信通知
                    $this->sendSms($ticketCode->ticket_code,$ticketType, $user_id, $coupon_name, date('Y-m-d H:i:s', $coupon_validity), $phone);
                    return [true, $ticketCode->ticket_code];
                }

                // 更新失败继续尝试下一个
            } catch (Exception $e) {
                return [false, $e->getMessage()];
            }
        }

        return [false, '系统繁忙，请稍后重试'];
    }

    /**
     * 作废优惠券
     * @param $ids
     * @return array
     * @throws Exception
     */
    public function invalid($ids): array
    {
        $coupon_codes = byNew::CouponCodeModel()::find()->where(['id' => $ids])->asArray()->all();
        $codes = array_column($coupon_codes, 'coupon_code');
        if (!empty($ids)) {
            foreach ($coupon_codes as $coupon) {
                if ($coupon['status'] == 1) {
                    $ticket_code=$coupon['ticket_code']??'';
                    return [false, '【'.$ticket_code.'】已领取状态,不可操作失效'];
                }
            }
        }

        byNew::CouponCodeModel()::updateAll(['is_deleted' => 1], ['id' => $ids, 'is_deleted' => 0, 'status' => 0]);
        // 更新库存
        foreach ($codes as $code) {
            $this->updateCouponStock($code);
        }
        return [true, '操作成功'];
    }

    /**
     * 优惠券是否存在
     */
    public function check($sub_type, $code, $use_stock = 0): array
    {
        if (intval($sub_type) <= 0) {
            return [true, ''];
        }
        $coupon = null;
        if ($sub_type == 1) {
            $coupon = by::marketConfig()::find()
                    ->where(['code' => $code, 'is_delete' => 0])
                    ->one();

            if (!$coupon) {
                return [false, '优惠券不存在'];
            }

            $resource = json_decode($coupon['resource'], true);
            if (isset($resource['valid_type']) && $resource['valid_type'] == 1) {
                $validVal = $resource['valid_val'] ?? 0;
                list($start_time, $expire_time) = by::marketConfig()->expireTime($resource['valid_type'], $validVal);
                if ($expire_time < time()) {
                    return [false, '优惠券已过期'];
                }
            }

        } else if ($sub_type == 2) {
            $coupon = byNew::CouponModel()::find()
                    ->where(['code' => $code, 'is_deleted' => 0])
                    ->one();

            // 卡券已过期
            if ($coupon && $coupon->validity < time()) {
                return [false, '停车券已过期'];
            }
            // 卡券库存
            if ($coupon) {
                $coupon->stock_num = byNew::CouponCodeModel()::find()
                        ->where(['coupon_code' => $code, 'status' => 0, 'is_deleted' => 0])
                        ->count();
            }
        }

        $sub_msg = $sub_type == 1 ? '优惠券' : '停车券';

        if (!$coupon) {
            return [false, $sub_msg . '不存在'];
        }

        if (isset($coupon->stock_num) && $coupon->stock_num <= 0) {
            return [false, $sub_msg . '已领完'];
        }

        if ($use_stock > 0 && isset($coupon->stock_num) && $coupon->stock_num < $use_stock) {
            return [false, $sub_msg . '库存不足'];
        }
        return [true, ''];
    }

    public static function info($order_no, $userId): array
    {
        $codeModel = by::CouponCodeModel()::find()->where(['order_no' => $order_no])->orderBy(['receive_at' => SORT_DESC])->one();

        if (!$codeModel) {
            return ['status' => false, 'msg' => '停车券码不存在 订单号：' . $order_no];
        }

        $couponModel = by::CouponModel()::find()->select(by::CouponModel()::$get_fields)->where(['code' => $codeModel['coupon_code']])->one();

        if (!$couponModel) {
            return ['status' => false, 'msg' => '停车券不存在 订单号：' . $order_no];
        }
        return [
                'status' => true,
                'data'   => [
                        'coupon'      => $couponModel->toArray(),
                        'ticket_code' => $codeModel['ticket_code'] ?? '',
                        'phone'       => by::Phone()->GetPhoneByUid($userId)
                ]
        ];
    }

    /**
     * 更新卡券库存
     * @param $code
     * @return void
     * @throws Exception
     */
    public function updateCouponStock($code)
    {
        // 更新卡券库存
        $count = by::CouponCodeModel()::find()->where(['coupon_code' => $code, 'status' => 0, 'is_deleted' => 0])->count();
        byNew::CouponModel()::updateAll(['stock_num' => $count], ['code' => $code]);
    }

    /**
     * @param $ticketCode
     * @param $ticketType
     * @param $userId
     * @param $coupon_name
     * @param $coupon_validity
     * @return void
     * @throws \yii\db\Exception
     */
    public function sendSms($ticketCode,$ticketType, $userId, $coupon_name, $coupon_validity, string $phone = '')
    {
        if (empty($phone)) {
            $phone = by::Phone()->GetPhoneByUid($userId);
        }

        $ret = false;
        $msg = '';

        // 停车券
        if ($ticketType==1){
            list($ret, $msg) = AliYunSms::sendSms($phone, AliYunSms::TEMPLATE_CODE['parking_coupon'], ['project' => $coupon_name, 'order_code' => $ticketCode, 'date' => $coupon_validity]);
        }

        // 京东E卡
        if ($ticketType==2){
            list($ret, $msg) = AliYunSms::sendSms($phone, AliYunSms::TEMPLATE_CODE['jd_e_card'], ['name' => $coupon_name, 'content' => $ticketCode]);
        }

        if (!$ret) {
            CUtil::debug((CouponModel::TYPE[$ticketType]??'卡券')."短信发送失败: " . "UserId: {$userId} 卡券: {$coupon_name} 券码: {$ticketCode} 有效日期: {$coupon_validity} 手机号: {$phone}  错误信息: {$msg}", 'parking.sms');
        }
    }

    /**
     * 获取卡券库存数量
     * @param string $code 卡券编码
     * @return int 库存数量
     */
    public function getStock(string $code): int
    {
        return by::CouponCodeModel()::find()
            ->where(['coupon_code' => $code, 'status' => 0, 'is_deleted' => 0])
            ->count();
    }

}