<?php

namespace app\modules\back\services;

use app\components\AppCRedisKeys;
use app\components\collection\Collection;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\goodsparams\GparamHandleFactory;

class GoodsParamService
{
    private static $_instance = NULL;

    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }


    /**
     * 添加分组
     * @param $cId
     * @param $name
     * @return array
     */
    public function AddGroup($cId, $name): array
    {
        // 获取类目信息
        $cateModel = by::cateModel();
        $item = $cateModel->getCateById($cId, ['*'], ['parent']);

        // 验证类目是否存在
        if (empty($item)) {
            return [false, '商品类目不存在'];
        }

        // 验证类目为二级分类、类目为主机类目
        if (($item['level'] != 2) ||
            (empty($item['parent'])) ||
            ($item['parent']['level'] != 1) ||
            ($item['parent']['tag_name'] != $cateModel::MACHINE_TYPE[1])) {
            return [false, '商品类目有错误'];
        }

        // 参数类型名不能重复
        $cateGroupModel = by::GparamCateGroupModel();
        $isExist = $cateGroupModel->isExistCateGroup($cId, $name);
        if ($isExist) {
            return [false, '参数类型已经存在'];
        }

        // 添加参数类型
        $data = [
            'pid' => 0,
            'cate_id' => $cId,
            'name' => $name
        ];
        list($res, $id) = $cateGroupModel->saveLog($data);
        return [$res, $id];
    }

    /**
     * 修改参数分类
     * @param $id
     * @param $name
     * @return array
     */
    public function EditGroup($id, $name): array
    {
        // 获取参数分类信息
        $cateGroupModel = by::GparamCateGroupModel();
        $item = $cateGroupModel->getCateGroupById($id);

        // 验证参数分类是否存在
        if (empty($item)) {
            return [false, '参数分类不存在'];
        }

        // 参数分类名相同，不修改
        if ($item['name'] == $name) {
            return [true, $id];
        }

        // 验证同商品类目下，是否存在同名参数分类
        $cateId = $item['cate_id'];
        $isExist = $cateGroupModel->isExistCateGroup($cateId, $name);
        if ($isExist) {
            return [false, '参数类型已经存在'];
        }

        // 修改参数类型
        $data = [
            'id' => $id,
            'name' => $name
        ];
        list($res, $id) = $cateGroupModel->saveLog($data);
        return [$res, $id];
    }

    /**
     * 获取参数库数据
     * @param $name
     * @return array
     */
    public function GetParamList($name): array
    {
        // 获取参数信息
        $paramModel = by::GparamModel();
        $columns = ['id', 'name'];
        if (empty($name)) { // 查找全部
            return $this->GetAllParamList();

        }

        // 模糊查询
        $items = $paramModel->getParamByName($name, $columns);
        $data = [];
        foreach ($items as $item) {
            $data[] = [
                'id' => $item['id'],
                'name' => $item['name']
            ];
        }
        return $data;
    }

    /**
     * 获取所有参数
     * @return array
     */
    public function GetAllParamList(): array
    {
        // 获取参数信息
        $paramModel = by::GparamModel();
        $columns = ['id', 'name'];
        $items = $paramModel->getParamList($columns);

        // 处理数据
        $data = [];
        foreach ($items as $item) {
            $data[] = [
                'id' => $item['id'],
                'name' => $item['name']
            ];
        }
        return $data;
    }

    /**
     * 获取参数详情
     * @param int $cId
     * @return array
     */
    public function GetGroupParamDetail(int $cId): array
    {
        // 获取参数数据
        $cateGroupModel = by::GparamCateGroupModel();
        $items = $cateGroupModel->getCateGroupByCateIdNoCache($cId, ['*'], ['detail.param']);

        // 处理数据
        $data = [];
        foreach ($items as $item) {
            $data[] = [
                'group_id'   => $item['id'],
                'group_name' => $item['name'],
                'items'      => $this->__getParamDetail($cId, $item['id'], $item['detail']),
            ];
        }
        return $data;
    }

    /**
     * 修改参数组的参数
     * @param int $cateId
     * @param array $params
     * @return array
     */
    public function EditGroupParam(int $cateId, array $params): array
    {
        // 处理参数
        $params = $this->__formatParams($params);

        // 处理排序，放入 redis 中
        $this->__setSortToCache($cateId, $params);

        // 获取组的详情
        $cateGroupDetailModel = by::GparamCateGroupDetailModel();
        $columns = ['id', 'categroup_id', 'gparam_id'];
        $items = $cateGroupDetailModel->getCateGroupDetailByGroupIds(array_keys($params), $columns);

        // 给要处理的数据打 tag
        $operates = [
            'DELETE' => [], // 删除的
            'INSERT' => []  // 插入的
        ];

        // 1、参数不存在：加入删除集合
        foreach ($items as $item) {
            if (!isset($params[$item['categroup_id']][$item['gparam_id']])) {
                $operates['DELETE'][] = [
                    'id' => $item['id']
                ];
            } else {
                // unset 判断过的数据
                unset($params[$item['categroup_id']][$item['gparam_id']]);
            }
        }

        // 2、加入插入集合
        foreach ($params as $groupId => $param) {
            foreach ($param as $paramId => $val) {
                $operates['INSERT'][] = [
                    'group_id' => $groupId,
                    'param_id' => $paramId
                ];
            }
        }

        // 处理 删除、插入逻辑
        $transaction = by::dbMaster()->beginTransaction();
        try {
            foreach ($operates as $operate => $data) {
                $handle = GparamHandleFactory::createHandle($operate);
                $handle->operation($data);
            }
            $transaction->commit();
            return [true, 'ok'];
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 写入日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'edit_group_param_err');
            return [false, '编辑失败'];
        }
    }

    /**
     * 删除分组
     * @param $id
     * @return array
     */
    public function DelGroup($id): array
    {
        // 获取参数分类信息
        $cateGroupModel = by::GparamCateGroupModel();
        $item = $cateGroupModel->getCateGroupById($id);

        // 验证参数分类是否存在
        if (empty($item)) {
            return [false, '参数分类不存在'];
        }

        // 删除
        $db = by::dbMaster();
        $trans = $db->beginTransaction();
        try {
            // 1、软删除 t_gparam_categroup
            $cateGroupModel->DeleteLog($id);

            // 获取categroup_detail_id
            $cateGroupDetails = by::GparamCateGroupDetailModel()->getCateGroupDetailByGroupIds([$id], ['id']);
            $groupDetailIds = array_column($cateGroupDetails, 'id');

            // 2、软删除 t_gparam_categroup_detail
            $cateGroupDetail = by::GparamCateGroupDetailModel();
            $cateGroupDetail->BatchDeleteLog($groupDetailIds);

            // 3、软删除 t_gparam_goods
            $goods = by::GparamGoodsModel();
            $goods->BatchDeleteLog($groupDetailIds);

            $trans->commit();
            return [true, 'ok'];
        } catch (\Exception $e) {
            $trans->rollBack();
            // 写入日志
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'del_group_err');
            return [false, '删除失败'];
        }
    }

    /**
     * 获取商品参数详情
     * @param int $cId
     * @param string $sku
     * @return array
     */
    public function GetGoodsParamDetail(int $cId, string $sku): array
    {
        // 查询数据（组）
        $columns = ['id', 'cate_id', 'name'];
        $cateGroupItems = by::GparamCateGroupModel()->getCateGroupByCateId($cId, $columns);
        // 参数组ID
        $cateGroupIds = array_column($cateGroupItems, 'id');

        // 查询数据（所有参数信息）
        $columns = ['id', 'name'];
        $paramItems = by::GparamModel()->getParamList($columns);
        // 参数ID
        $paramIds = array_column($paramItems, 'id');

        // 查询数据（参数详情）
        $condition = ['categroup_id' => $cateGroupIds, 'gparam_id' => $paramIds];
        $columns = ['id', 'categroup_id', 'gparam_id'];
        $cateGroupDetailItems = by::GparamCateGroupDetailModel()->getCateGroupDetailList($condition, $columns);

        // 处理数据
        $paramName = array_column($paramItems, 'name', 'id');

        // redis 获取参数的 sort
        $redis = by::redis('core');

        $cateGroupDetailData = [];
        foreach ($cateGroupDetailItems as $item) {
            // 排序
            $key = AppCRedisKeys::getGoodsParamSort($cId, $item['categroup_id']);
            $sort = $redis->zscore($key, $item['gparam_id']);
            if (!$sort) {
                $sort = 0;
            }
            $cateGroupDetailData[$item['categroup_id']][] = [
                'categroup_detail_id' => $item['id'],
                'param_id'            => $item['gparam_id'],
                'param_name'          => $paramName[$item['gparam_id']],
                'param_sort'          => $sort
            ];
        }

        $cateGroupData = [];
        foreach ($cateGroupItems as $item) {
            // 排序
            $items = $cateGroupDetailData[$item['id']] ?? [];
            $items = $this->__goodsParamsOrderBySort($items);
            $cateGroupData[] = [
                'group_id'   => $item['id'],
                'group_name' => $item['name'],
                'items'      => $items
            ];
        }

        // 商品的参数值
        $columns = ['sku', 'categroup_detail_id', 'param_info', 'sort', 'is_show'];
        $goodsParamItems = by::GparamGoodsModel()->getParamsBySku($sku, [0, 1], $columns);
        $goodsParamItems = array_column($goodsParamItems, null, 'categroup_detail_id');

        // 处理结果集
        $data = [];
        foreach ($cateGroupData as $item) {
            foreach ($item['items'] as $value) {
                $param = $goodsParamItems[$value['categroup_detail_id']] ?? [];
                $data[] = [
                    'group_id'            => $item['group_id'],
                    'group_name'          => $item['group_name'],
                    'categroup_detail_id' => $value['categroup_detail_id'],
                    'param_id'            => $value['param_id'],
                    'param_name'          => $value['param_name'],
                    'param_info'          => $param['param_info'] ?? '',
                    'sort'                => $param['sort'] ?? 0,
                    'is_show'             => $param['is_show'] ?? 0,
                ];
            }
        }
        return ['items' => $data, 'total' => count($data)];
    }

    /**
     * 获取参数详情
     * @param $cId
     * @param $groupId
     * @param $items
     * @return  array
     */
    private function __getParamDetail($cId, $groupId, $items): array
    {
        // 从 redis 获取 sort，并按照 sort 排序。
        $redis = by::redis('core');
        $data = [];
        foreach ($items as $item) {
            if (!empty($item['param'])) { // 参数有效（即：参数在参数库gparam表中存在，没有被删除）
                // 获取 sort
                $key = AppCRedisKeys::getGoodsParamSort($cId, $groupId);
                $sort = $redis->zscore($key, $item['gparam_id']);
                if (!$sort) {
                    $sort = 0;
                }
                $data[] = [
                    'categroup_detail_id' => $item['id'],
                    'param_id' => $item['gparam_id'],
                    'param_name' => $item['param']['name'],
                    'sort' => $sort
                ];
            }
        }
        // 按照 sort 排序输出
        array_multisort(array_column($data, 'sort'), SORT_ASC, $data);
        return $data;
    }

    /**
     * 格式化参数
     * @param array $items
     * @return array
     */
    private function __formatParams(array $items): array
    {
        $data = [];
        foreach ($items as $item) {
            $groupId = $item['group_id'];
            $data[$groupId] = [];
            foreach ($item['items'] as $val) {
                $data[$groupId][$val['param_id']] = $val['sort'];
            }
        }
        return $data;
    }

    /**
     * 保存 sort 到缓存
     * @param $cateId
     * @param $params
     */
    private function __setSortToCache($cateId, $params)
    {
        // 缓存
        $redis = by::redis('core');

        // 清空缓存
        foreach ($params as $key => $param) {
            $r_key = AppCRedisKeys::getGoodsParamSort($cateId, $key);
            $redis->del($r_key);
        }

        // 插入缓存
        foreach ($params as $key => $param) {
            $r_key = AppCRedisKeys::getGoodsParamSort($cateId, $key);
            foreach ($param as $p => $sort) {
                $redis->zAdd($r_key, $sort, $p);
            }
        }
    }

    /**
     * 根据 sort 排序
     * @param array $items
     * @return array
     */
    private function __goodsParamsOrderBySort(array $items): array
    {
        return Collection::wrap($items)->sortBy('param_sort')->all();
    }
}