<?php
namespace app\modules\asset\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\modules\main\models\CommModel;

class PointConfigModel extends CommModel
{

    public static function tableName(): string
    {
        return "`db_dreame_goods`.`point_config`";
    }

    public function rules(): array
    {
        return [
                [['reward_rate', 'deduction_rate', 'employee_reward_rate', 'exchange_gold_rate', 'gold_conversion','shopping_money_rate', 'status', 'ctime', 'utime'], 'integer'],
                [['reward_rate', 'deduction_rate', 'employee_reward_rate', 'exchange_gold_rate', 'gold_conversion','shopping_money_rate', 'status'], 'default', 'value' => 1],
                [['status'], 'in', 'range' => array_keys(self::STATUS)],
        ];
    }

    const STATUS = [
            1 => '开启',
            2 => '关闭',
    ];

    /**
     * 获取积分配置
     * @return array
     */
    public static function getConfig(): array
    {
        $config = self::find()->asArray()->one();
        if (empty($config)) {
            return [];
        }
        return $config;
    }

    public function _delConfigCache()
    {
        $redis = by::redis();
        $cacheKey = AppCRedisKeys::getPointConfig();
        $redis->del($cacheKey);
    }


    /**
     * 更新积分配置
     * @param array $data
     * @return bool
     */
    public static function updateConfig(array $data): bool
    {
        $config = self::getConfig();
        if (empty($config)) {
            $model = new self();
        } else {
            $data['utime'] = time();
            $model         = self::findOne($config['id']);
        }

        $model->setAttributes($data);

        if ($model->save()) {
            return true;
        }

        return false;
    }

    public function getPointConfigByCache()
    {
        $redis = by::redis();
        $cacheKey = AppCRedisKeys::getPointConfig();
        $cachedData = $redis->get($cacheKey);

        if ($cachedData) {
            return json_decode($cachedData, true);
        }

        $config = $this->getConfig();

        $redis->set($cacheKey, json_encode($config), ['EX' => empty($config) ? 10 : 7200]);
        return $config;
    }

    /**
     * 获取金币兑换比例配置
     * @return int
     */
    public function getExchangeGoldRate(): int
    {
        $default = 1000;

        return (int) ($this->getPointConfigByCache()['exchange_gold_rate'] ?? $default);
    }

    /**
     * 获取金币换算比例配置
     * @return int
     */
    public function getGoldConversion(): int
    {
        $default = 10000;

        return (int) ($this->getPointConfigByCache()['gold_conversion'] ?? $default);
    }
}
