<?php
namespace app\modules\asset\models;

use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class PointDetailModel extends CommModel
{

    public static function getTable($time=null): string
    {
        $time = $time ?: START_TIME;
        $year = strlen($time) == 4 ? $time : date("Y",intval($time));
        return "`db_dreame_log`.`t_point_detail_{$year}`";
    }

    private function __getPointDetailList() {
        return AppCRedisKeys::getPointDetailList();
    }

    private function __delCache(){
        $r_key = $this->__getPointDetailList();
        return by::redis('core')->del($r_key);
    }

    /**
     * @param $str
     * 导出csv 字符串处理
     */
    private function __exportStr($str){
        //去除，和空格
        $str = str_replace(',','*',$str);
        $str = str_replace(' ','',$str);
        return trim($str)."\t";
    }

    CONST YEAR = 2022;

    /**
     * @param $user_id
     * @param $point
     * @param $log_id
     * @param $act_id
     * @return array
     * @throws Exception
     * 写入后台列表
     */
    public function addLogDetail($user_id,$point,$log_id,$act_id){
        $user_id    = CUtil::uint($user_id);
        $point      = CUtil::uint($point);
        $act_id     = CUtil::uint($act_id);
        $log_id     = CUtil::uint($log_id);

        if (empty($user_id) || empty($point) || empty($act_id) || empty($log_id)){
            return [false,'缺少必要参数'];
        }

        $tb     = $this->getTable();

        $data  = [
            'log_id'        => $log_id,
            'user_id'       => $user_id,
            'act_jf_ids'    => $act_id,
            'point'         => $point,
            'ctime'         => intval(START_TIME)
        ];

        $row    = by::dbMaster()->createCommand()->insert($tb, $data)->execute();

        if (!$row){
            return [false,'操作失败'];
        }

        $this->__delCache();
        return [true,$log_id];
    }

    /**
     * @param int $year
     * @param int $user_id
     * @param string $phone
     * @param int $type
     * @param int $ctime_start
     * @param int $ctime_end
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 获取后台列表
     */
    public function getList(int $year,int $user_id,string $phone, int $type=-1,int $ctime_start=0, int $ctime_end=0,int $page=1, int $page_size=10): array
    {
        if ($year<self::YEAR){
            return [];
        }

        $r_key   = $this->__getPointDetailList();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$year,$user_id,$phone,$type,$ctime_start,$ctime_end,$page,$page_size);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = $this->getTable($year);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($user_id,$phone,$type,$ctime_start,$ctime_end);
            $sql                 = "SELECT `id`,`user_id`,`log_id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $command             = by::dbMaster()->createCommand($sql,$params);
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;
            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key);
        }

        return empty($aData) ? [] : $aData;
    }

    /**
     * @param int $year
     * @param int $user_id
     * @param string $phone
     * @param int $type
     * @param int $ctime_start
     * @param int $ctime_end
     * @return int
     * @throws Exception
     * 获取总数
     */
    public function getCount(int $year,int $user_id,string $phone, int $type=-1,int $ctime_start=0, int $ctime_end=0) {
        if ($year<self::YEAR){
            return 0;
        }

        $r_key   = $this->__getPointDetailList();
        $sub_key = CUtil::getAllParams(__FUNCTION__,$year,$user_id,$phone,$type,$ctime_start,$ctime_end);
        $count   = by::redis('core')->hGet($r_key,$sub_key);

        if($count === false) {
            $tb                  = $this->getTable($year);
            list($where,$params) = $this->__getCondition($user_id,$phone,$type,$ctime_start,$ctime_end);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key);
        }

        return intval($count);
    }

    /**
     * @param $user_id
     * @param $phone
     * @param $type
     * @param $ctime_start
     * @param $ctime_end
     * @return array
     * 规范化查询条件
     */
    private function __getCondition($user_id, $phone, $type, $ctime_start, $ctime_end): array
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if(!empty($user_id)) {
            $where              .= " AND `user_id` LIKE :user_id";
            $params[":user_id"]  = "%{$user_id}%";
        }

        if(!empty($phone)) {
            $uids = by::Phone()->GetUidsByPhone($phone);
            if(!empty($uids)) {
                $uids    = implode(',',$uids);
                $where  .= " AND `user_id` IN ({$uids})";
            }else{
                //如果输入手机号但是没查到相关用户就会把所有用户返回，所以没查到手机号的固定一个不存在的用户
                $where  .= " AND `user_id` = -1";
            }
        }

        if($type>0) {
            $act_type_ids =array_column(PointLogModel::ACT_FJ_TYPE,'IDS','TYPE');
            $ids    = $act_type_ids[$type];
            $ids    = array_map(function ($v){
                return CUtil::uint($v);
            },$ids);
            $ids    = implode(",",$ids);

            $where .= " AND `act_jf_ids` IN ({$ids})";
        }

        if($ctime_start && $ctime_end) {
            $where                 .= " AND (`ctime` BETWEEN :ctime_start AND :ctime_end)";
            $params[":ctime_start"] = CUtil::uint($ctime_start);
            $params[":ctime_end"]   = CUtil::uint($ctime_end);
        }

        return [$where, $params];
    }

    /**
     * @deprecated gezhiqiang-export-VER2177-3874 版本上线后，将废弃。2023-07-18
     * @param $year
     * @param $user_id
     * @param $phone
     * @param $type
     * @param $s_time
     * @param $e_time
     * @throws Exception
     * 积分记录导出
     */
    public function export($year=0, $user_id=0, $phone=0, $type=-1, $s_time=0, $e_time=0)
    {

        if ($year<self::YEAR){
            return '无数据';
        }

        $headList = [
            '用户名称',
            '用户ID',
            '用户手机号',
            '积分个数',
            '类型',
            '发放/消耗时间',
            '发放/消耗原因',
            '备注',
        ];
        $fileName   = '积分记录' . date('Ymd') . mt_rand(1000, 9999) . '.csv';
        $year ?: date('Y');
        $tb                     = self::getTable($year);
        list($where,$params)    = $this->__getCondition($user_id, $phone, $type, $s_time, $e_time);
        $total      = by::dbMaster()->createCommand("SELECT count(*) FROM {$tb} WHERE {$where} LIMIT 1", $params)->queryScalar();

        //导出
        CUtil::export_csv_new($headList, function () use($tb, $where, $params, $total) {

            $page_size          = 100;
            $pages              = CUtil::getPaginationPages($total,$page_size);
            $pointLog           = by::pointLog();
            $userModel          = by::users();
            $dataList           = [];
            for($page = 1; $page <= $pages; $page++) {
                list($offset)       = CUtil::pagination($page, $page_size);
                $sql                = "SELECT `id`,`user_id`,`log_id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
                $logList            = by::dbMaster()->createCommand($sql, $params)->queryAll();

                foreach ($logList as $value) {
                    $log            = $pointLog->getInfo($value['user_id'],$value['log_id']);
                    $user           = $userModel->getOneByUid($value['user_id']);
                    $phone          = by::Phone()->GetPhoneByUid($value['user_id']);
                    $nick           = preg_replace_callback(
                        '/./u',
                        function (array $match) {
                            return strlen($match[0]) >= 4 ? '' : $match[0];
                        },
                        $user['nick']??'');

                    $dataList[]         = [
                        'nick'          => $nick ?? '',
                        'user_id'       => $value['user_id'],
                        'phone'         => $phone."\t" ?? '',
                        'point'         => $log['point']."\t" ?? '0',
                        'type'          => $log['type'] == 1 ? '增加积分': '扣除积分',
                        'time'          => $log['ctime'] ? date('Y-m-d H:i:s',$log['ctime']) : '',
                        'reason'        => $log['reason'] ?? '',
                        'remark'        => $log['remark'] ?? '',
                    ];
                }
            }
            yield $dataList;

        }, $fileName);
    }

    public function exportData($year=0, $user_id=0, $phone=0, $type=-1, $s_time=0, $e_time=0)
    {

        if ($year<self::YEAR){
            return '无数据';
        }

        $headList = [
            '用户名称',
            '用户ID',
            '用户手机号',
            '积分个数',
            '类型',
            '发放/消耗时间',
            '发放/消耗原因',
            '备注',
        ];
        $year = $year ?: date('Y');
        $tb                     = self::getTable($year);
        list($where,$params)    = $this->__getCondition($user_id, $phone, $type, $s_time, $e_time);
        $total      = by::dbMaster()->createCommand("SELECT count(*) FROM {$tb} WHERE {$where} LIMIT 1", $params)->queryScalar();

        //导出
        $page_size          = 100;
        $pages              = CUtil::getPaginationPages($total,$page_size);
        $pointLog           = by::pointLog();
        $userModel          = by::users();
        $dataList[]         = $headList;
        for($page = 1; $page <= $pages; $page++) {
            list($offset)       = CUtil::pagination($page, $page_size);
            $sql                = "SELECT `id`,`user_id`,`log_id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $logList            = by::dbMaster()->createCommand($sql, $params)->queryAll();

            foreach ($logList as $value) {
                $log            = $pointLog->getInfo($value['user_id'],$value['log_id']);
                $user           = $userModel->getOneByUid($value['user_id']);
                $phone          = by::Phone()->GetPhoneByUid($value['user_id']);
                $nick           = preg_replace_callback(
                    '/./u',
                    function (array $match) {
                        return strlen($match[0]) >= 4 ? '' : $match[0];
                    },
                    $user['nick']??'');

                $dataList[]         = [
                    'nick'          => '\''.($nick ?? ''),
                    'user_id'       => $value['user_id'],
                    'phone'         => $phone."\t" ?? '',
                    'point'         => $log['point']."\t" ?? '0',
                    'type'          => $log['type'] == 1 ? '增加积分': '扣除积分',
                    'time'          => $log['ctime'] ? date('Y-m-d H:i:s',$log['ctime']) : '',
                    'reason'        => $log['reason'] ?? '',
                    'remark'        => $log['remark'] ?? '',
                ];
            }
        }
        return $dataList;
    }
}
