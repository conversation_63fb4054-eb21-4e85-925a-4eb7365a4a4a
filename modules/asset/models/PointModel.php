<?php
namespace app\modules\asset\models;


use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\MemberCenter;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\Exception;

class PointModel extends CommModel
{

    CONST YEAR = 2022;//2022之前无数据
    CONST ST_YEAR = 2;//积分有效期最多两年，2022年只能用2021年和2022年的积分

    CONST AWARD_P    = YII_ENV_PROD ? 1 : 1;  //奖励比例 （实付金额*奖励比例=活动的积分）保留整数
    CONST DETUCT_P   = YII_ENV_PROD ? 10 : 10;//抵扣比例 （X积分=1元现金） 目前1积分=一元现金

    /**
     * @param $user_id
     * @param $time
     * @return string
     * 根据用户id获得积分
     */
    private function __getPointByUser($user_id,$time) {
        $year = date('Y',$time);
        return AppCRedisKeys::getPointByUser($user_id,$year);
    }

    private function __getExpiredUserSet($year,$i) {
        return AppCRedisKeys::getExpiredUserSet($year,$i);
    }

    private function __delCache($user_id,$time){
        $r_key = $this->__getPointByUser($user_id,$time);
        return by::redis('core')->del($r_key);
    }

    public static function getTable($user_id): string
    {
        $mod = $user_id % 10;
        return "`db_dreame`.`t_point_{$mod}`";
    }

    /**
     * @param $user_id
     * @param $time
     * @return array
     * @throws Exception
     * 积分db读取
     */
    private function __getToDb($user_id,$time): array
    {
        $tb    = self::getTable($user_id);

        $years = self::ST_YEAR;
        $st    = $years-1;
        $sql   = "SELECT `id`,`point`,`year`,`user_id` FROM {$tb} WHERE `user_id` = :user_id AND `year` BETWEEN :s_year AND :e_year LIMIT {$years}";

        $aData = by::dbMaster()->createCommand($sql,
            [
                ':s_year'  => date('Y',strtotime("-{$st} year",$time)),
                ':e_year'  => date('Y',$time),
                ':user_id' => $user_id,
            ])->queryAll();

        $points = array_column($aData,'point');
        $total  = array_sum($points);

        return [$total,$aData];
    }

    /**
     * @param int $user_id
     * @param int $point    : 需要扣除的积分
     * @param int $time
     * @return array
     * 扣除积分
     */
    private function __deduct(int $user_id, int $point, int $time): array
    {
        $user_id = CUtil::uint($user_id);
        $point   = CUtil::uint($point);
        $oPoint  = $this->get($user_id);//当前数额

        if ((abs($point) > $oPoint)) {
            return [false, "用户积分不足"];
        }

        list(,$aData) = $this -> __getToDb($user_id,$time);
        if (empty($aData)){
            return [false, "用户积分不足~"];
        }

        $detail = [];
        $into   = array_column($aData,'point','year');
        $diff   = $point;
        $tb     = self::getTable($user_id);
        $years  = self::ST_YEAR-1;
        for ($i = $years; $i >= 0; $i--) {

            if ($diff <= 0 ) {
                break;
            }

            $year = date('Y',strtotime("-{$i} year",$time));
            if (!isset($into[$year])){
                continue;
            }

            $cost = min($into[$year],$diff);
            if ($cost <= 0 ) {
                continue;
            }

            $diff = $diff - $cost;

            $sql  = "UPDATE {$tb} SET `point` = `point` - :point WHERE `user_id`=:user_id AND `year`=:year AND `point` >= abs(:point)  LIMIT 1";

            $row  = by::dbMaster()->createCommand($sql, [':point' => $cost, ':user_id' => $user_id, ':year' => $year])->execute();
             if (!$row){
                return [false,'扣除积分失败'];
            }

            $detail[$year] = $cost;
        }
        return [true,$detail];

    }

    /**
     * @param int $user_id
     * @param int $point
     * @return array
     * @throws Exception
     * 增加积分
     */
    private function __add(int $user_id, int $point, int $time){
        $user_id  = CUtil::uint($user_id);
        $point    = CUtil::uint($point);
        $time     = CUtil::uint($time);

        $year     = strlen($time) == 4 ? $time : date('Y',$time);
        $tb       = self::getTable($user_id);
        $sql      = "INSERT INTO {$tb} (`user_id`,`point`,`year`) VALUE (:user_id,:point,:year) ON DUPLICATE KEY UPDATE `point` = `point` + :point";
        $row      = by::dbMaster()->createCommand($sql, [':point' => $point, ':user_id' => $user_id, ':year' => $year])->execute();

        if (!$row){
            return [false,'增加积分失败'];
        }

        return [true,[$year=>$point]];

    }

    /**
     * @param int $user_id
     * @param int $log_id 回滚的记录id
     * @param int $rpoint
     * @return array
     * @throws Exception
     * 积分回滚
     */
    private function __rollback(int $user_id, int $log_id,int $rpoint = 0)
    {
        $user_id  = CUtil::uint($user_id);
        $log_id   = CUtil::uint($log_id);

        $l_rpoint = $rpoint;

        $info = by::pointLog()->getInfo($user_id,$log_id);
        if (empty($info['detail'])){
            return [false,'记录不存在'];
        }

        if (!in_array($info['act_jf_ids'],PointLogModel::ACT_FJ_TYPE['USE']['IDS'])){
            return [false,'非消费记录无法回滚操作'];
        }
        $new_detail =[];
        $detail     = json_decode($info['detail'],true);

        $a_rpoint   = $info['rpoint'];

        foreach ($detail as $year=>$point){

            if (bccomp($a_rpoint, $point) >= 0) {
                $a_rpoint   = bcsub($a_rpoint, $point);
                continue;
            }

            if ($a_rpoint > 0) {
                $point      = bcsub($point, $a_rpoint); //剩余可回滚数
                $a_rpoint   = 0;
            }

            //当前年积分大于等于需要回滚的积分
            if ( bccomp($point, $rpoint) >= 0 ) {
                list($s) = $this->__add($user_id,intval($rpoint),$year);
                CUtil::debug("回滚1：userid = {$user_id},point={$rpoint},year={$year}'",'link1123');
                if (!$s){
                    return [false,'积分回滚失败~~'];
                }

                $new_detail[$year]  = $rpoint;
                $rpoint             = 0;
                break;
            }

            //当前年积分不够回滚
            list($s) = $this->__add($user_id,intval($point),$year);
            CUtil::debug("回滚2：userid = {$user_id},point={$point},year={$year}'",'link1123');
            if (!$s){
                return [false,'积分回滚失败~'];
            }

            $rpoint   = bcsub($rpoint, $point);

            $new_detail[$year] = $point;
        }

        if ($rpoint > 0) {
            return [false, '积分回滚失败~~~'];
        }

        //更新已回滚数
        by::pointLog()->UpdateRpoint($user_id, $log_id, $l_rpoint);


        return [true,$new_detail];
    }

    /**
     * @param $now_time
     * @return array
     * @throws Exception
     * 将要有积分过期的用户写入集合
     */
    private function __goExpiredSet($now_time,$i){
        $now_time   = CUtil::uint($now_time) ?: time();
        $st_year    = self::ST_YEAR-1;
        $year       = date('Y',strtotime("-{$st_year} year",$now_time));
        $page_size  = 50;
        $params     = [':year'=>$year];
        $r_key      = $this->__getExpiredUserSet($year,$i);
        $redis      = by::redis('core');
        $tb = self::getTable($i);

        $count_sql = "SELECT COUNT(*) FROM {$tb} WHERE `year`=:year";
        $count     = by::dbMaster()->createCommand($count_sql,$params)->queryScalar();

        if (empty($count)){
           return [false,'无数据'];
        }

        $pages      = CUtil::getPaginationPages($count,$page_size);
        $id         = 0;
        for($page=1;$page<=$pages;$page++) {
            //分页查询
            $ret_sql = "SELECT `id`,`user_id` FROM {$tb} WHERE `year`=:year  AND `point` > 0 AND `id` > {$id} ORDER BY `id` ASC LIMIT {$page_size}";
            $rets    = by::dbMaster()->createCommand($ret_sql,$params)->queryAll();

            $end     = end($rets);
            $id      = $end['id'];

            $uid     = array_column($rets,"user_id");
            $redis->sAdd($r_key,...$uid);
            usleep(10000);
        }

        return [true,'ok'];
    }

    /**
     * @param $now_time
     * @return array
     * @throws Exception
     * 遍历集合写入过期记录
     */
    private function __addExpiredLog($now_time,$i){
        $now_time   = CUtil::uint($now_time) ?: time();
        $st_year    = self::ST_YEAR;

        $time       = strtotime("-{$st_year} year",$now_time);
        $year       = date('Y',$time);

        $r_key      = $this->__getExpiredUserSet($year,$i);
        $redis      = by::redis('core');

        $redis->setOption(\Redis::OPT_SCAN,\Redis::SCAN_RETRY);
        $redis->setOption(\Redis::OPT_READ_TIMEOUT, -1);//防止超时

        $logModel = by::pointLog();
        $iterator = NULL;
        while ($user_ids = $redis->sScan($r_key,$iterator,NULL,100)) {
            foreach ($user_ids as $user_id){
                list(,$aData) = $this->__getToDb($user_id,$time);
                $aData = array_column($aData,'point','year');
                $point = $aData[$year] ?? 0;
                if (empty($point)){
                    continue;
                }
                list($status,$ret) = $logModel->addLog($user_id,$point,2000002);
                if (!$status){
                    CUtil::debug("积分过期记录失败,原因：{$ret}|user_id={$user_id},point={$point}",'link_expired_log');
                    continue;
                }
                $redis->sRem($r_key,$user_id);
            }
        }

        return [true,'ok'];
    }

    /**
     * @param $user_id
     * @param null $time
     * @return int
     * @throws Exception
     * 获取用户积分
     */
    public function get($user_id, $time = null): int
    {
        list($s, $data) = MemberCenter::factory()->run('scoreGet', ['user_id'=>$user_id]);
        $point = $data['totalPoints'] ?? 0;

        if ($point <= 0) {
            return 0;
        }

        //小程序订单未同步到crm的积分
        $oPoint = by::Ouser()->getUsePoint($user_id);
        return intval(bcsub($point,$oPoint));
    }

    /**
     * @param $user_id          :用户id
     * @param int $point        :积分数量
     * @param int $act_id       :流水行为id 参照asset/config/my_common
     * @param int $remark       :备注
     * @return array            :[ture,'log_id'] 成功返回log_id
     * @throws Exception
     * 积分操作统一入口(追觅积分改为crm处理此功能无效)
     */
	public function set($user_id, int $point=0, int $act_id=0, $log_id=0, $remark='')
    {
        $time    = time();
        $act_id  = CUtil::uint($act_id);
        $log_id  = CUtil::uint($log_id);
        $point   = CUtil::uint($point);
        $user_id = CUtil::uint($user_id);
        if (empty($user_id)) {
            return [false, '无效用户'];
        }

        if ($point == 0) {
            return [false, '无效资产操作'];
        }

        $unique_key = CUtil::getAllParams(__FUNCTION__, $act_id);
        list($anti) = self::ReqAntiConcurrency($user_id,$unique_key,5,'EX');
        if(!$anti) {
            return [false, "请勿频繁操作"];
        }

        $tran   = by::dbMaster()->beginTransaction();

        try {
            switch ($act_id) {
                case 100:
                case 101:
                    list($status,$arr) = $this->__add($user_id, $point,$time);
                    break;
                case 2000000:
                case 2000001:
                    list($status,$arr) = $this->__deduct($user_id, $point,$time);
                    break;
                case 102:
                case 103:
                    if (empty($log_id)){
                        throw new MyExceptionModel('缺少log_id');
                    }
                    list($status,$arr) = $this->__rollback($user_id,$log_id,$point);
                    break;
                default:
                    throw new MyExceptionModel('未知操作');
            }

            if(!$status){
                throw new MyExceptionModel($arr);
            }

            //写入日志
            list($status,$log_id) = by::pointLog()->addLog($user_id,$point,$act_id,$arr,$remark,$tran);
            if (!$status){
                throw new MyExceptionModel($log_id);
            }

            $tran->commit();
            $this->__delCache($user_id,$time);
            self::ReqAntiConcurrency($user_id,$unique_key,0,'DEL');

            return [true,$log_id];
        }catch (MyExceptionModel $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("修改积分失败|{$error}", 'link-point_set');

            $tran->rollBack();
            //积分操作失败后 10分钟内不能操作
            self::ReqAntiConcurrency($user_id,$unique_key,10,'EX');
            return [false,$_e->getMessage()];
        } catch (\Exception $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("修改积分失败|{$error}", 'link-point_set');

            $tran->rollBack();
            //积分操作失败后 10分钟内不能操作
            self::ReqAntiConcurrency($user_id,$unique_key,10,'EX');
            return [false,'操作失败'];
        }

    }

    /**
     * @return array
     * 获取积分规则
     */
    public function rule(): array
    {
        //获取积分配置规则
        $deduction           = byNew::PointConfigModel()::getConfig();
        $reward_rate         = intval($deduction['reward_rate'] ?? 1);
        $employee_rate       = intval($deduction['employee_reward_rate'] ?? 1);
        $deduction_rate      = intval($deduction['deduction_rate'] ?? 10);
        $exchange_gold_rate  = intval($deduction['exchange_gold_rate'] ?? 1000);
        $gold_conversion     = intval($deduction['gold_conversion'] ?? 10000);
        $shopping_money_rate = intval($deduction['shopping_money_rate'] ?? 10);

        return ['award' => $reward_rate, 'deduct' => $deduction_rate, 'employee_reward_rate' => $employee_rate, 'exchange_gold_rate' => $exchange_gold_rate, 'gold_conversion' => $gold_conversion, 'shopping_money_rate' => $shopping_money_rate];
    }

    /**
     * @param string $result :RMB = 积分转人民币 POINT=人民币转积分
     * @param $currency
     * @return string
     * 积分换算
     */
    public function convert($currency, string $result = 'RMB',$user_id = 0): string
    {
        if (empty($currency)) {
            return '0';
        }

        //获取积分配置规则
        $deduction     = byNew::PointConfigModel()::getConfig();
        $deductionRate = $deduction['deduction_rate'] ?? 10;
        if ($result == 'RMB') {
            return bcdiv($currency, $deductionRate, 2);
        } else {
            return bcmul($currency, $deductionRate);
        }
    }

    /**
     * @param null $time
     * @return array
     * @throws Exception
     * 积分过期（加入过期积分到记录）
     */
    public function expired($time=null,$index){
        $time = $time ?: time();
        $date = date('md',$time);

        switch ($date){
            case '1231':
                //提前一天把将要处理的userid写入集合
                return $this->__goExpiredSet($time,$index);

            case '0101':
                //遍历集合中的userid 添加过期记录
                return $this->__addExpiredLog($time,$index);

            default:
                //仅12月31日  和 1月1日执行
                return [false,'当天无业务处理'];
        }
    }


}
