<?php
namespace app\modules\asset\models;


use app\components\AppWRedisKeys;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\CommModel;

//积分、觅享分发放记录
class PointPushModel extends CommModel
{
    public static function tbName()
    {
        return "`db_dreame`.`t_point_push`";
    }

    public function __getGivePointByUid($uid): string
    {
        return AppWRedisKeys::getGivePointByUid($uid);
    }

    public function __wrongExcelList($backUserId): string
    {
        return AppWRedisKeys::wrongExcelList($backUserId);
    }



    // 积分事件
    CONST POINT_EVENT = [
      'purchase'=>'购物',
      'activity'=>'活动',
      'benifits'=>'福利',
    ];

    //类型
    CONST MODEL_TYPE = [
        'point'=>'积分',
        'grow'=>'觅享分',
    ];

    const STATUS = [
        'wait'    => 1,
        'success' => 2,
        'cancel'  => 3,
    ];

    //发放状态
    CONST PUSH_STATUS = [
        1=>'待发放',
        2=>'已发放',
        3=>'已取消',
    ];

    public function GetOneInfo(array $input)
    {
        $tb     = self::tbName();
        list($where, $params)= $this->__getCondition($input);
        $sql                 = "SELECT * FROM {$tb} WHERE {$where} ORDER BY `id` LIMIT 1";

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($sql, $params)->queryOne();
        return empty($result) ? [] : $result;
    }

    public function GetList(array $input, int $page = 1, int $pageSize = 10)
    {
        $tb     = self::tbName();
        //查询数据
        list($offset)        = CUtil::pagination($page,$pageSize);
        list($where, $params)= $this->__getCondition($input);
        $sql                 = "SELECT * FROM {$tb} WHERE {$where} ORDER BY `id` DESC
                                    LIMIT {$offset},{$pageSize}";

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($sql, $params)->queryAll() ?? [];
        return empty($result) ? [] : $result;
    }

    public function GetCount(array $input): int
    {
        $tb     = self::tbName();
        list($where, $params)= $this->__getCondition($input);
        $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";

        // 执行查询并返回结果
        $result = by::dbMaster()->createCommand($sql, $params)->queryScalar();
        return intval($result);
    }


    public function __getCondition($input): array
    {
        //SQL初始化条件
        $where  = "1 = 1";
        $params = [];

        if (!empty($input['id'])) {
            $where         .= " AND `id`=:id";
            $params[":id"] = intval($input['id']);
        }

        if (!empty($input['ids'])) { // $ids 是数组
            if(!is_array($input['ids'])){
                $input['ids'] = explode(',', $input['ids']);
            }
            $where         .= " AND `id` IN (".implode(',', $input['ids']).")";
        }

        if (!empty($input['user_id'])) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"] = intval($input['user_id']);
        }

        if (!empty($input['type'])) {
            $where           .= " AND `type`=:type";
            $params[":type"] = trim($input['type']);
        }

        if (!empty($input['status'])) {
            $where             .= " AND `status`=:status";
            $params[":status"] = intval($input['status']);
        }

        if (!empty($input['start_time']) && !empty($input['end_time'])) {
            $where                 .= " AND `ctime` BETWEEN :start_time AND :end_time";
            $params[":start_time"] = trim($input['start_time']);
            $params[":end_time"]   = trim($input['end_time']);
        }

        if (!empty($input['release_start_time']) && empty($input['release_end_time'])) {
            $where                         .= " AND `release_time` BETWEEN :release_start_time AND :release_end_time";
            $params[":release_start_time"] = trim($input['release_start_time']);
            $params[":release_end_time"]   = trim($input['release_end_time']);
        }

        if (!empty($input['model'])) {
            $where            .= " AND `model`=:model";
            $params[":model"] = trim($input['model']);
        }

        //uid 模糊查询
        if(!empty($input['uid'])){
            $where .= " AND `uid` LIKE :uid";
            $params[":uid"] = "%".trim($input['uid'])."%";
        }

        //phone 模糊查询
        if(!empty($input['phone'])){
            $where .= " AND `phone` LIKE :phone";
            $params[":phone"] = "%".trim($input['phone'])."%";
        }

        //source 模糊查询
        if(!empty($input['source'])){
            $where .= " AND `source` LIKE :source";
            $params[":source"] = "%".trim($input['source'])."%";
        }

        //sub_source 模糊查询
        if(!empty($input['sub_source'])){
            $where .= " AND `sub_source` LIKE :sub_source";
            $params[":sub_source"] = "%".trim($input['sub_source'])."%";
        }

        //event 模糊查询
        if(!empty($input['event'])){
            $where .= " AND `event` LIKE :event";
            $params[":event"] = "%".trim($input['event'])."%";
        }

        //excel_no 模糊查询
        if(!empty($input['excel_no'])){
            $where .= " AND `excel_no` LIKE :excel_no";
            $params[":excel_no"] = "%".trim($input['excel_no'])."%";
        }

        //push_no 模糊查询
        if(!empty($input['push_no'])){
            $where .= " AND `push_no` LIKE :push_no";
            $params[":push_no"] = "%".trim($input['push_no'])."%";
        }

        if(!empty($input['push_nos'])){
            if(!is_array($input['push_nos'])){
                $input['push_nos'] = explode(',', $input['push_nos']);
            }
            $input['push_nos'] = implode("','", $input['push_nos']);
            $where .= " AND `push_no` IN ('{$input['push_nos']}')";
        }

        if (!empty($input['status'])) {//发放状态
            $where             .= " AND `status` = :status";
            $params[":status"] = $input['status'];
        }

        if(!empty($input['admin_ids'])){
            if(!is_array($input['admin_ids'])){
                $input['admin_ids'] = explode(',', $input['admin_ids']);
            }
            $input['admin_ids'] = implode("','", $input['admin_ids']);
            $where .= " AND `admin_id` IN ('{$input['admin_ids']}')";
        }

        return [$where, $params];
    }


    public function  BatchInsertPoints($data)
    {
        $tb     = self::tbName();
        $fields = array_keys($data[0]);
        return by::dbMaster()->createCommand()->batchInsert($tb, $fields,$data)->execute();
    }


}
