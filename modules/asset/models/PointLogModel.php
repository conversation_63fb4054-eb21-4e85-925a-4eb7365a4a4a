<?php
namespace app\modules\asset\models;

use app\components\AppCRedisKeys;
use app\components\Crm;
use app\components\PointCenter;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use yii\db\DataReader;
use yii\db\Exception;

class PointLogModel extends CommModel
{
    CONST PAGE_SIZE = 20;

    public $tb_fields = [
        'id','user_id','act_jf_ids','point','ctime','detail','remark','rpoint'
    ];

    public static function getTable($user_id): string
    {
        $mod = $user_id % 10;
        return "`db_dreame_log`.`t_point_log_{$mod}`";
    }

    CONST YEAR = 2022;

    CONST ACT_FJ_TYPE = [
        'RECEIVE' => ['TYPE'=>1,'IDS'=>[100,101,102,103]], //获取
        'USE' => ['TYPE'=>2,'IDS'=>[2000000,2000001,2000002]], //消费
    ];

    CONST MANUAL_TYPE = [100,2000000];//手动操作

    /**
     * @param $user_id
     * @return string
     * 积分记录哈希列表
     */
    private function __getPointLogList($user_id): string
    {
        return AppCRedisKeys::getPointLogList($user_id);
    }

    /**
     * @param $user_id
     * @param $log_id
     * @return string
     * 积分记录详情
     */
    private function __getPointLog($user_id,$log_id): string
    {
        return AppCRedisKeys::getPointLog($user_id,$log_id);
    }

    /**
     * @param $user_id
     * @return int
     * 缓存清理列表
     */
    private function __delCacheList($user_id): int
    {
        $key = $this->__getPointLogList($user_id);
        return  by::redis('core')->del($key);
    }

    /**
     * @param $user_id
     * @param $log_id
     * @return int
     * 缓存清理详情
     */
    private function __delCacheInfo($user_id,$log_id): int
    {
        $key = $this->__getPointLog($user_id,$log_id);
        return  by::redis('core')->del($key);
    }


    /**
     * @param $user_id
     * @param $point
     * @param $act_id
     * @param string $remark
     * @param array $detail
     * @param null $tran
     * @return array
     * @throws Exception
     * 添加日志
     */
    public function addLog($user_id, $point, $act_id, array $detail=[], string $remark='', $tran = null){
        $user_id    = CUtil::uint($user_id);
        $point      = CUtil::uint($point);
        $act_id     = CUtil::uint($act_id);

        if (empty($user_id) || empty($point) || empty($act_id)){
            return [false,'addLog缺少必要参数'];
        }

        $tb         = $this->getTable($user_id);
        $db         = by::dbMaster();

        $new_tran   = is_null($tran) ? $db->beginTransaction() : $tran;

        try {
            //积分过期时间记录为 0点时间
            $ctime = ($act_id == 2000002) ? strtotime(date('Y-m-d',time())) : intval(START_TIME);

            $data  = [
                'user_id'       => $user_id,
                'act_jf_ids'    => $act_id,
                'point'         => $point,
                'ctime'         => $ctime,
                'detail'        => json_encode($detail),
                'remark'        => $remark
            ];

            $row    = $db->createCommand()->insert($tb, $data)->execute();
            if (!$row){
                throw new MyExceptionModel('添加积分日志失败~~');
            }

            $log_id = $db->getLastInsertID();

            list($status,$msg) = by::model('PointDetailModel', 'asset')->addLogDetail($user_id,$point,$log_id,$act_id);
            if (!$status){
                throw new MyExceptionModel($msg);
            }

            is_null($tran) && $new_tran->commit();
            $this->__delCacheList($user_id);
            return [true,$log_id];

        }catch (MyExceptionModel $_e) {
            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("添加日志失败|{$error}", 'link-add-log');

            is_null($tran) && $new_tran->rollBack();
            return [false,$_e->getMessage()];

        } catch (\Exception $_e) {

            $error = $_e->getMessage() . "|" . $_e->getFile() . ":" . $_e->getLine();
            CUtil::debug("添加日志失败|{$error}", 'link-add-log');

            is_null($tran) && $new_tran->rollBack();
            return [false,'操作失败'];

        }
    }

    /**
     * @param int $user_id
     * @param int $type
     * @param int $page
     * @param int $page_size
     * @return array
     * @throws Exception
     * 记录列表
     */
    public function getList(int $user_id, int $type=-1,int $page=1, int $page_size=10): array
    {
        $user_id = CUtil::uint($user_id);
        $r_key   = $this->__getPointLogList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__,$type,$page,$page_size);
        $aJson   = by::redis('core')->hGet($r_key,$sub_key);
        $aData   = (array)json_decode($aJson,true);

        if($aJson === false) {
            $tb                  = $this->getTable($user_id);
            list($offset)        = CUtil::pagination($page,$page_size);
            list($where,$params) = $this->__getCondition($user_id,$type);
            $sql                 = "SELECT `id`,`user_id` FROM {$tb} WHERE {$where} ORDER BY `id` DESC LIMIT {$offset},{$page_size}";
            $command             = by::dbMaster()->createCommand($sql,$params);
            $aData               = $command->queryAll();
            $aData               = empty($aData) ? [] : $aData;
            by::redis('core')->hSet($r_key,$sub_key,json_encode($aData));
            CUtil::ResetExpire($r_key);
        }

        return empty($aData) ? [] : $aData;
    }

    /**
     * @param int $user_id
     * @param int $type
     * @return int
     * @throws Exception
     * 记录总数
     */
    public function getCount(int $user_id, int $type=-1) {
        $user_id = CUtil::uint($user_id);
        $r_key   = $this->__getPointLogList($user_id);
        $sub_key = CUtil::getAllParams(__FUNCTION__,$type);
        $count   = by::redis('core')->hGet($r_key,$sub_key);

        if($count === false) {
            $tb                  = $this->getTable($user_id);
            list($where,$params) = $this->__getCondition($user_id,$type);
            $sql                 = "SELECT COUNT(*) FROM {$tb} WHERE {$where}";
            $count               = by::dbMaster()->createCommand($sql,$params)->queryScalar();

            by::redis('core')->hSet($r_key,$sub_key,$count);
            CUtil::ResetExpire($r_key);
        }

        return intval($count);
    }

    /**
     * @param $user_id
     * @param $log_id
     * @return array|false|DataReader
     * @throws Exception
     * 积分记录详细数据
     */
    public function getInfo($user_id,$log_id){
        $log_id  = CUtil::uint($log_id);
        $user_id = CUtil::uint($user_id);
        if(empty($user_id) || empty($log_id)) {
            return [];
        }

        $redis       = by::redis('core');
        $redis_key   = $this->__getPointLog($user_id,$log_id);
        $aJson       = $redis->get($redis_key);
        $aData       = (array)json_decode($aJson,true);

        if(empty($aData)) {
            $tb      = $this->getTable($user_id);
            $fields  = implode("`,`",$this->tb_fields);

            $sql     = "SELECT `{$fields}` FROM  {$tb} WHERE `id`=:log_id LIMIT 1";
            $command = by::dbMaster()->createCommand($sql);
            $command->bindParam(":log_id", $log_id);
            $aData   = $command->queryOne();
            $aData   = empty($aData) ? [] : $aData;

            $redis->set($redis_key, json_encode($aData), 1800);
        }

        if ($aData) {
            $act_jf_ids   = CUtil::getConfig('act_jf_ids', 'common', 'asset');
            $aData['reason'] = $act_jf_ids[$aData['act_jf_ids']];
            $aData['type'] = in_array($aData['act_jf_ids'], self::ACT_FJ_TYPE['RECEIVE']['IDS'])  ? self::ACT_FJ_TYPE['RECEIVE']['TYPE'] : self::ACT_FJ_TYPE['USE']['TYPE'];
        }

        return $aData;
    }

    /**
     * @param $user_id
     * @param $id
     * @param $rpoint
     * @return array
     * @throws Exception
     * 更新已回滚数
     */
    public function UpdateRpoint($user_id, $id, $rpoint)
    {
        $tb     = self::getTable($user_id);

        $sql    = "UPDATE {$tb} SET `rpoint` = `rpoint` + :rpoint WHERE `id`=:id AND `user_id`=:user_id LIMIT 1";

        by::dbMaster()->createCommand($sql, [':rpoint'=>$rpoint,':id'=>$id,":user_id"=>$user_id])->execute();

        $this->__delCacheInfo($user_id, $id);

        return [true, 'ok'];
    }

    /**
     * @param int $user_id
     * @param int $type
     * @return array
     * 规范化查询条件
     */
    private function __getCondition(int $user_id=0, int $type=-1): array
    {

        $where           = "1=:init";
        $params[':init'] = 1;

        if(!empty($user_id)) {
            $where              .= " AND `user_id`=:user_id";
            $params[":user_id"]  = $user_id;
        }

        if($type>0) {
            $act_type_ids =array_column(PointLogModel::ACT_FJ_TYPE,'IDS','TYPE');
            $ids    = $act_type_ids[$type];
            $ids    = array_map(function ($v){
                return CUtil::uint($v);
            },$ids);
            $ids    = implode(",",$ids);

            $where .= " AND `act_jf_ids` IN ({$ids})";
        }

        return [$where, $params];
    }



    public function crmLog($user_id,$page,$page_size=20,$type=0){
        list($status,$ret) = Crm::factory()->getScoreLog($user_id,$page);
        if (!$status || empty($ret['Data']['list'])){
            return [0,[]];
        }
        $list = [];
        foreach ($ret['Data']['list'] as $value){
            $list[] = [
                'point'  => $value['score'],
                'type'   => $value['type'],
                'reason' => $value['scenes'] == 3 ? $value['behaviorType']  : Crm::SCENES[$value['scenes']],
                'ctime'  => strtotime($value['date'])
            ];
        }
        $count = $ret['Data']['totalcount'] ?? 0;

        $newList = [];
        $type1List = [];
        $type2List = [];
        if($list){
            foreach ($list as $key=>$li){
                if($li['type'] == 1){
                    $type1List[] = $li;
                }
                if(in_array($li['type'],[2,3])){
                    $type2List[] = $li;
                }
            }
        }
        if($type == 1){
            $newList = $type1List;
        }elseif ($type == 2){
            $newList = $type2List;
        }else{
            $newList = $list;
        }
        $count = count($newList);

        return [$count,$newList];
    }

    /**
     * @param $page
     * @param $page_size
     * @param $card
     * @return array
     * 后台获取积分账户记录
     */
    public function crmLogByCard($page, $page_size, $card): array
    {
        $page = CUtil::uint($page);
        if ($page == 0 || empty($card)) {
            return [];
        }

        list($status, $res) = Crm::factory()->getScoreLogByCard($page, $page_size, $card);
        if (!$status || empty($res['Data']['list'])){
            return [];
        }

        $list = [];
        foreach ($res['Data']['list'] as $v){
            $list[] = [
                'code'   => $v['code'], //积分记录单号
                'point'  => $v['score'], //变动数量
                'type'   => Crm::POINT_TYPE_NAME[$v['type']], //积分变动类型
                'reason' => $v['scenes'] == 3 ? $v['behaviorType'] : Crm::SCENES[$v['scenes']], //积分变动场景
                'ctime'  => strtotime($v['date']), //变动时间
                'source' => $v['source'] //渠道
             ];
        }

        $data['count'] = $res['Data']['totalcount'];
        $data['list']  = $list;

        return $data;
    }

    /**
     * 后台获取积分账户记录
     * @param int $user_id 用户user_id
     * @param int $page 页码
     * @param int $page_size 条数
     * @return array
     * @throws Exception
     */
    public function getScoreLogByUserId(int $user_id, int $page = 1, int $page_size = 10): array
    {
        $page = CUtil::uint($page);
        if ($page == 0 || empty($user_id)) {
            return [];
        }

        list($status, $res) = PointCenter::factory()->getScoreLogByUserId($user_id, $page, $page_size);
        if (!$status || empty($res['data']['records'])) {
            return [];
        }

        $list = [];
        foreach ($res['data']['records'] as $v) {
            $list[] = [
                    'code'   => $v['serialNo'] ?? '',                                                               //积分记录单号
                    'point'  => number_format(($v['numValue'] ?? ''), 2),                                  //变动数量
                    'type'   => PointCenter::POINT_CHANGE_TYPE[$v['status']] ?? '',                                 //积分变动类型
                    'reason' => $v['eventName'] ?? '',                                                              //积分变动场景
                    'ctime'  => strlen($v['updateTime']) > 10 ? intval($v['updateTime'] / 1000) : $v['updateTime'], //变动时间
                    'source' => $v['eventType'] ?? ''                                                               //渠道
            ];
        }

        $data['count'] = intval($res['data']['total'] ?? 0);
        $data['list']  = $list;

        return $data;
    }
}
