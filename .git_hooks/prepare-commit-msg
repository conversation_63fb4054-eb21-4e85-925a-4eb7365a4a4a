#!/bin/bash

regex_issue_id="[A-Z]+[A-Z0-9]*-[0-9]+"
git_root=`git rev-parse --show-toplevel`
warnning='\033[33m'
red='\033[0;31m'
green_bg='\033[42m'
message_bg='\033[44m'
no_color='\033[0m'

username=`git config user.name`
email=`git config user.email`
email_prefix=$(echo $email | awk -F"[@]" '{print $1}')

echo -e "${message_bg}当前用户名：$username, 邮箱： $email, 邮箱前缀是：$email_prefix ${no_color}"
# check username & email
if [[ $email == *@dreame.tech ]]; then
    echo ""
else
    echo -e "${red}Error!!!:git 邮箱必须配置为dreame邮箱${no_color}"
    exit 1
fi

if [[ $username = $email_prefix ]]; then
    echo ""
else
    echo -e "${red}Error!!!:git 用户名必须等于邮箱前缀${no_color}"
    exit 1
fi

echo -e "${green_bg}-------------------Start check commit message-----------------  ${no_color}"
issue_list=$(head -n1 "$1"  | grep -o -E "$regex_issue_id")
if [ -z "$issue_list" ]; then
    echo -e "${red}Error!!!:commit message 中必须关联jira issue${no_color}"
    exit 1
else
    for i in $issue_list; do
        echo "当前检测：$i"
        issue_msg=$(curl --user p_mall_ci_autobuild:vunGEoHDNktKTQ1GVxjYHbwa7UWLX6exlru115  https://jira-mall.dreame.tech/rest/agile/1.0/issue/$i)
        if [[ $issue_msg == *"errorMessages"* ]]; then
            echo -e "${red}Error!!!:issue $i 不存在${no_color}"
            exit 1
        fi
    done
fi
