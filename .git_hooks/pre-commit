#!/bin/bash

git_root=`git rev-parse --show-toplevel`

title='\033[46;30m'
red='\033[0;31m'
no_color='\033[0m'

# update local-gate-mall
if [ ! -d "$git_root/local-gate-mall" ]; then
    echo -e "${title}---------------Repo:local-gate-mall not exist, start clone --------------${no_color}"
    unset GIT_WORK_TREE
    <NAME_EMAIL>:devops/access_control/local-gate-mall.git
    if [ "$?" -ne "0" ]; then
        echo -e "${red}(ssh)git clone access_control/local-gate-mall.git faild !!!${no_color}"
        exit 1
    fi
else
    echo -e "${title}---------------update Repo:local-gate-mall --------------${no_color}"
    cd $git_root/local-gate-mall
    git pull
    cd $git_root
    if [ "$?" -ne "0" ]; then
        echo -e "${red} update access_control/local-gate-mall.git faild !!!${no_color}"
        exit 1
    fi
fi

echo -e "${title}---------------start update .git_hooks --------------${no_color}"
cp -a local-gate-mall/.git_hooks .
rm -rf local-gate-mall/