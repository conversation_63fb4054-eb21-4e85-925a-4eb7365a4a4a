# 系统基础环境配置
prod xshell 101.133.234.71:22 dreame/8HcRk8MWSKAZqFjp
mysql mall-cn.rwlb.rds.aliyuncs.com:3306  dreame/!q1QAZ1qaz@dreame!@#  直接改
redis内/外网地址：r-uf6n4flguyf6vz1ywq.redis.rds.aliyuncs.com/r-mall-cn.redis.rds.aliyuncs.com
端口：6379  Rds6!QAZ1qaxz!@&^%&dreame*&^%$

test xshell 121.36.193.66:10022   dreame/3^HKa!QnRPUAWr
mysql 121.37.172.116:32736     mall-uat/MoyGkZ8nCHnmeFBo
redis 121.37.172.116:32379  oju#wjM&w3!Fn^   15


uat   xshell  124.71.172.209:10022  root/Y%7#UWP!Kf^D9y
mysql 116.63.34.3：3306   root/dreame@2020
redis 121.37.172.116:32379  oju#wjM&w3!Fn^   14


dev redis: 10.10.37.100  6379  dreame@2020 开发环境
mysql：10.10.37.108:13306  dev  dreame@2020 开发环境
root / dreame@2020
xshell: 10.10.38.105  10022   rd/123456


# OMS E3+ 疑难问题解答
Q：用户下单立即退款，订单还未同步，后来同步时会不会导致OMS拨付发货，导致后续流程产生问题？
A：用户申请退款后，即使订单未及时同步到OMS，商城系统有补偿和重试机制，如果重试时订单已经不是待发货状态，商城系统选择不予再次推送，申请退款时的推送信息将会在OMS中进行无效运转，达到次数后自动停止。

Q：用户下的订单伴随着OMS很多的赠品服务订单，如果主订单无效后赠品订单会不会找回？
A：OMS 相关人员承诺主订单失效之后，赠品订单也会置为无效，如果发货，跟主品订单一样都会被找回。

Q：用户下单时商品库存不是实时查询OMS的，会不会导致用户超买的问题
A：OMS只有库存发生变化时才会对商城系统进行商品库存的回写，同时商城系统需要加库存相关的锁，以避免商品的超卖（暂时不处理，超卖的商品将在oms 处于挂起状态）

Q：订单退款的审核要不要跟OMS回写的订单物流状态强绑定，只有OMS回写不发货或者找回货物后，才能同意退款
A：不需要，退货退款订单审核时，相关人员将会和仓库确认订单情况，确认后可以进行退款，同时为了防止误点，后台提供退单按钮，方便相关人员进行强制退单推送

Q：由于运营人员的创建商品时SKU填写错误导致无法获取商品库存的相关信息怎么办？
A：CRM直接从SAP获取相关SKU，为防止订单同步CRM失败，建议将商品下架后重新创建订单，OMS也是从SAP同步SKU相关信息。

Q：由于商城系统本地的地址库过于老旧，导致推送的订单无法发货的问题？
A：临时解决方案是更新OMS的地址数据到本地，推送时按照OMS地址数据进行推送,长期解决方案是建立相关的实时同步和展示地址方案，比如中国地理API实时获取地址信息

Q：商品已在物流过程中，用户是否可以申请仅退款，然后我们推送OMS时是推送哪个接口？
A：用户可以申请仅退款，OMS推送A003-return退货申请推送

# 熊洞对接
wiki https://wiki.dreame.tech/pages/viewpage.action?pageId=88207596
AES秘钥 m9m)ksGmjU4N+9n# CLIENT_ID：xd_yjhx CLIENT_SECRET:vZm6eUNiv7wWba4n


# 省市区最新json
省份：https://wpm-cdn.dreame.tech/files/202305/646f1638cd66b8410004211.json
城市：https://wpm-cdn.dreame.tech/files/202305/646f16dc7fae05230005938.json
区域：https://wpm-cdn.dreame.tech/files/202305/646f19573242c2052299635.json


# PHP子进程不够的问题
看 https://wiki.dreame.tech/pages/viewpage.action?pageId=56207706 PHP使用注意事项


# 修改queues.php 时 需要重启listen  
生产：supervisorctl reload


php yii queue/clear #清除队列
php yii queue/exec #执行队列任务
php yii queue/info #显示有关队列状态的信息(默认为此命令)
php yii queue/listen #监听队列并运行队列任务
php yii queue/remove #按照id删除队列任务
php yii queue/run #运行所有队列任务

# 切换7.2 版本
locale-gen en_US.UTF-8
sudo apt-get install software-properties-common
sudo LC_ALL=en_US.UTF-8 add-apt-repository ppa:ondrej/nginx-mainline
sudo apt-get update
sudo apt-get -y install php7.2
sudo apt-get -y install php7.2-mysql
sudo apt-get install php7.2-fpm

apt-get install ibapache2-mod-php7.2 php7.2 php7.2-curl php7.2-xml php7.2-mcrypt php7.2-json php7.2-gd php7.2-mbstring php7.2-redis  php7.2-sockets
php7.2-mysql php7.2-curl php7.2-gd php7.2-mbstring php7.2-xml php7.2-xmlrpc php7.2-zip php7.2-opcache php7.2-gmp php7.2-bcmath php7.2-dom
# 默认7.2版本
update-alternatives --set php /usr/bin/php7.2

sudo service nginx restart
sudo service php7.2-fpm restart


# Docker容器化
###  构建镜像
`docker build -t dreame-mall-v1.0.0 .`
###  运行容器
`docker run -d --privileged -p 8081:8080 --name=dreame-mall dreame-mall-v1.0.0`
### 测试
> docker ps    
> 或者  
>`curl 127.0.0.1:8081`  



