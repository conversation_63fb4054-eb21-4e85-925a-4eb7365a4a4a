{"name": "yiisoft/yii2-app-basic", "description": "Yii 2 Basic Project Template", "keywords": ["yii2", "framework", "basic", "project template"], "homepage": "http://www.yiiframework.com/", "type": "project", "license": "BSD-3-<PERSON><PERSON>", "support": {"issues": "https://github.com/yiisoft/yii2/issues?state=open", "forum": "http://www.yiiframework.com/forum/", "wiki": "http://www.yiiframework.com/wiki/", "irc": "irc://irc.freenode.net/yii", "source": "https://github.com/yiisoft/yii2"}, "minimum-stability": "stable", "require": {"php": ">=7.0.0", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-libxml": "*", "ext-curl": "*", "ext-zlib": "*", "ext-bcmath": "*", "ext-sockets": "*", "ext-redis": "*", "yiisoft/yii2": ">=2.0.5", "yiisoft/yii2-bootstrap": "*", "yiisoft/yii2-swiftmailer": "*", "qtt/game-center": "^1.0", "qcloud_sts/qcloud-sts-sdk": "^3.0", "ext-iconv": "*", "ext-openssl": "*", "nmred/kafka-php": "v0.2.0.8", "monolog/monolog": "^1.27", "ext-ctype": "*", "zircote/swagger-php": "^3.0", "yiisoft/yii2-queue": "^2.3", "phpoffice/phpspreadsheet": "1.15.0", "ext-calendar": "*", "promphp/prometheus_client_php": "^2.7", "promphp/prometheus_push_gateway_php": "^1.1", "alipaysdk/easysdk": "^2.2", "firebase/php-jwt": "6.4"}, "require-dev": {"yiisoft/yii2-codeception": "*", "yiisoft/yii2-debug": "*", "yiisoft/yii2-gii": "*", "yiisoft/yii2-faker": "*"}, "config": {"process-timeout": 1800, "allow-plugins": {"yiisoft/yii2-composer": true}}, "scripts": {"post-create-project-cmd": ["yii\\composer\\Installer::postCreateProject"]}, "extra": {"yii\\composer\\Installer::postCreateProject": {"setPermission": [{"runtime": "0777", "web/assets": "0777", "yii": "0755"}], "generateCookieValidationKey": ["config/web.php"]}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}}