{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "5c24d381b19cad6eeeae6a42f301297f", "packages": [{"name": "adbario/php-dot-notation", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/adbario/php-dot-notation.git", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/adbario/php-dot-notation/zipball/081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "reference": "081e2cca50c84bfeeea2e3ef9b2c8d206d80ccae", "shasum": ""}, "require": {"ext-json": "*", "php": "^5.5 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.7|^6.6|^7.5|^8.5|^9.5", "squizlabs/php_codesniffer": "^3.6"}, "type": "library", "autoload": {"files": ["src/helpers.php"], "psr-4": {"Adbar\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "PHP dot notation access to arrays", "homepage": "https://github.com/adbario/php-dot-notation", "keywords": ["ArrayA<PERSON>ess", "dotnotation"], "support": {"issues": "https://github.com/adbario/php-dot-notation/issues", "source": "https://github.com/adbario/php-dot-notation/tree/2.5.0"}, "time": "2022-10-14T20:31:46+00:00"}, {"name": "alibabacloud/tea", "version": "3.2.1", "source": {"type": "git", "url": "https://github.com/aliyun/tea-php.git", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/tea-php/zipball/1619cb96c158384f72b873e1f85de8b299c9c367", "reference": "1619cb96c158384f72b873e1f85de8b299c9c367", "shasum": ""}, "require": {"adbario/php-dot-notation": "^2.4", "ext-curl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": "^6.3|^7.0", "php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "*", "symfony/dotenv": "^3.4", "symfony/var-dumper": "^3.4"}, "suggest": {"ext-sockets": "To use client-side monitoring"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>", "homepage": "http://www.alibabacloud.com"}], "description": "Client of Tea for PHP", "homepage": "https://www.alibabacloud.com/", "keywords": ["alibabacloud", "client", "cloud", "tea"], "support": {"issues": "https://github.com/aliyun/tea-php/issues", "source": "https://github.com/aliyun/tea-php"}, "time": "2023-05-16T06:43:41+00:00"}, {"name": "alibabacloud/tea-fileform", "version": "0.3.4", "source": {"type": "git", "url": "https://github.com/alibabacloud-sdk-php/tea-fileform.git", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alibabacloud-sdk-php/tea-fileform/zipball/4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "reference": "4bf0c75a045c8115aa8cb1a394bd08d8bb833181", "shasum": ""}, "require": {"alibabacloud/tea": "^3.0", "php": ">5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35|^5.4.3"}, "type": "library", "autoload": {"psr-4": {"AlibabaCloud\\Tea\\FileForm\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Alibaba Cloud SDK", "email": "<EMAIL>"}], "description": "Alibaba Cloud Tea File Library for PHP", "support": {"issues": "https://github.com/alibabacloud-sdk-php/tea-fileform/issues", "source": "https://github.com/alibabacloud-sdk-php/tea-fileform/tree/0.3.4"}, "time": "2020-12-01T07:24:35+00:00"}, {"name": "alipaysdk/easysdk", "version": "2.2.3", "source": {"type": "git", "url": "https://github.com/alipay/alipay-easysdk.git", "reference": "c6008839a22a5fca08e9f8536730f7abfed522d5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/alipay/alipay-easysdk/zipball/c6008839a22a5fca08e9f8536730f7abfed522d5", "reference": "c6008839a22a5fca08e9f8536730f7abfed522d5", "shasum": ""}, "require": {"alibabacloud/tea": "^3.1", "alibabacloud/tea-fileform": "^0.3.2", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-simplexml": "*", "ext-xmlwriter": "*", "guzzlehttp/guzzle": ">=6.3", "php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Alipay\\EasySDK\\": "php/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "junying.wjy", "email": "<EMAIL>"}], "description": "支付宝官方 Alipay Easy SDK", "support": {"source": "https://github.com/alipay/alipay-easysdk/tree/v2.2.3"}, "time": "2022-11-28T14:04:57+00:00"}, {"name": "amphp/amp", "version": "v1.2.2", "source": {"type": "git", "url": "https://github.com/amphp/amp.git", "reference": "4f2161da5f68f274f116985635aea63b5c0f54d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/amphp/amp/zipball/4f2161da5f68f274f116985635aea63b5c0f54d2", "reference": "4f2161da5f68f274f116985635aea63b5c0f54d2", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"fabpot/php-cs-fixer": "~1.9", "phpunit/phpunit": "~4.8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"files": ["lib/functions.php"], "psr-4": {"Amp\\": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Creator / Lead Developer"}], "description": "A non-blocking concurrency framework for PHP applications", "homepage": "https://github.com/amphp/amp", "keywords": ["async", "concurrency", "event", "non-blocking", "promise"], "time": "2016-05-12T12:54:59+00:00"}, {"name": "bower-asset/bootstrap", "version": "v3.3.5", "source": {"type": "git", "url": "https://github.com/twbs/bootstrap.git", "reference": "16b48259a62f576e52c903c476bd42b90ab22482"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twbs/bootstrap/zipball/16b48259a62f576e52c903c476bd42b90ab22482", "reference": "16b48259a62f576e52c903c476bd42b90ab22482", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.9.1"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["less/bootstrap.less", "dist/js/bootstrap.js"], "bower-asset-ignore": ["/.*", "_config.yml", "CNAME", "composer.json", "CONTRIBUTING.md", "docs", "js/tests", "test-infra"]}, "license": ["MIT"], "description": "The most popular front-end framework for developing responsive, mobile first projects on the web.", "keywords": ["css", "framework", "front-end", "js", "less", "mobile-first", "responsive", "web"]}, {"name": "bower-asset/inputmask", "version": "3.3.11", "source": {"type": "git", "url": "https://github.com/RobinHerbots/Inputmask.git", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/RobinHerbots/Inputmask/zipball/5e670ad62f50c738388d4dcec78d2888505ad77b", "reference": "5e670ad62f50c738388d4dcec78d2888505ad77b", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.7"}, "type": "bower-asset-library", "extra": {"bower-asset-main": ["./dist/inputmask/inputmask.js", "./dist/inputmask/inputmask.extensions.js", "./dist/inputmask/inputmask.date.extensions.js", "./dist/inputmask/inputmask.numeric.extensions.js", "./dist/inputmask/inputmask.phone.extensions.js", "./dist/inputmask/jquery.inputmask.js", "./dist/inputmask/global/document.js", "./dist/inputmask/global/window.js", "./dist/inputmask/phone-codes/phone.js", "./dist/inputmask/phone-codes/phone-be.js", "./dist/inputmask/phone-codes/phone-nl.js", "./dist/inputmask/phone-codes/phone-ru.js", "./dist/inputmask/phone-codes/phone-uk.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jqlite.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.jquery.js", "./dist/inputmask/dependencyLibs/inputmask.dependencyLib.js", "./dist/inputmask/bindings/inputmask.binding.js"], "bower-asset-ignore": ["**/*", "!dist/*", "!dist/inputmask/*", "!dist/min/*", "!dist/min/inputmask/*"]}, "license": ["http://opensource.org/licenses/mit-license.php"], "description": "Inputmask is a javascript library which creates an input mask.  Inputmask can run against vanilla javascript, jQuery and jqlite.", "keywords": ["form", "input", "inputmask", "j<PERSON>y", "mask", "plugins"], "time": "2017-11-21T11:46:23+00:00"}, {"name": "bower-asset/jquery", "version": "2.2.4", "source": {"type": "git", "url": "https://github.com/jquery/jquery-dist.git", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jquery/jquery-dist/zipball/c0185ab7c75aab88762c5aae780b9d83b80eda72", "reference": "c0185ab7c75aab88762c5aae780b9d83b80eda72", "shasum": ""}, "type": "bower-asset-library", "extra": {"bower-asset-main": "dist/jquery.js", "bower-asset-ignore": ["package.json"]}, "license": ["MIT"], "keywords": ["browser", "javascript", "j<PERSON>y", "library"]}, {"name": "bower-asset/punycode", "version": "v1.3.2", "source": {"type": "git", "url": "https://github.com/bestiejs/punycode.js.git", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/bestiejs/punycode.js/zipball/38c8d3131a82567bfef18da09f7f4db68c84f8a3", "reference": "38c8d3131a82567bfef18da09f7f4db68c84f8a3", "shasum": ""}, "type": "bower-asset-library", "extra": {"bower-asset-main": "punycode.js", "bower-asset-ignore": ["coverage", "tests", ".*", "component.json", "Gruntfile.js", "node_modules", "package.json"]}}, {"name": "bower-asset/yii2-pjax", "version": "v2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/jquery-pjax.git", "reference": "60728da6ade5879e807a49ce59ef9a72039b8978"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/jquery-pjax/zipball/60728da6ade5879e807a49ce59ef9a72039b8978", "reference": "60728da6ade5879e807a49ce59ef9a72039b8978", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.8"}, "type": "bower-asset-library", "extra": {"bower-asset-main": "./jquery.pjax.js", "bower-asset-ignore": [".travis.yml", "Gem<PERSON>le", "Gemfile.lock", "CONTRIBUTING.md", "vendor/", "script/", "test/"]}, "license": ["MIT"]}, {"name": "cebe/markdown", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/cebe/markdown.git", "reference": "54a2c49de31cc44e864ebf0500a35ef21d0010b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cebe/markdown/zipball/54a2c49de31cc44e864ebf0500a35ef21d0010b2", "reference": "54a2c49de31cc44e864ebf0500a35ef21d0010b2", "shasum": ""}, "require": {"lib-pcre": "*", "php": ">=5.4.0"}, "require-dev": {"cebe/indent": "*", "facebook/xhprof": "*@dev", "phpunit/phpunit": "4.1.*"}, "bin": ["bin/markdown"], "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"cebe\\markdown\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cebe.cc/", "role": "Creator"}], "description": "A super fast, highly extensible markdown parser for PHP", "homepage": "https://github.com/cebe/markdown#readme", "keywords": ["extensible", "fast", "gfm", "markdown", "markdown-extra"], "time": "2015-03-06T05:28:07+00:00"}, {"name": "doctrine/annotations", "version": "1.14.3", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "reference": "fb0d71a7393298a7b232cbf4c8b1f73f3ec3d5af", "shasum": ""}, "require": {"doctrine/lexer": "^1 || ^2", "ext-tokenizer": "*", "php": "^7.1 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^1.11 || ^2.0", "doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "~1.4.10 || ^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^4.4 || ^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/1.14.3"}, "time": "2023-02-01T09:20:38+00:00"}, {"name": "doctrine/deprecations", "version": "v1.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "reference": "0e2a4f1f8cdfc7a92ec3b01c9334898c806b30de", "shasum": ""}, "require": {"php": "^7.1|^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5|^8.5|^9.5", "psr/log": "^1|^2|^3"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/v1.0.0"}, "time": "2022-05-02T15:47:09+00:00"}, {"name": "doctrine/lexer", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "reference": "39ab8fcf5a51ce4b85ca97c7a7d033eb12831124", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9 || ^10", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^4.11 || ^5.0"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/2.1.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2022-12-14T08:49:07+00:00"}, {"name": "ezyang/htmlpurifier", "version": "v4.7.0", "source": {"type": "git", "url": "https://github.com/ezyang/htmlpurifier.git", "reference": "ae1828d955112356f7677c465f94f7deb7d27a40"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezyang/htmlpurifier/zipball/ae1828d955112356f7677c465f94f7deb7d27a40", "reference": "ae1828d955112356f7677c465f94f7deb7d27a40", "shasum": ""}, "require": {"php": ">=5.2"}, "type": "library", "autoload": {"psr-0": {"HTMLPurifier": "library/"}, "files": ["library/HTMLPurifier.composer.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://ezyang.com"}], "description": "Standards compliant HTML filter written in PHP", "homepage": "http://htmlpurifier.org/", "keywords": ["html"], "time": "2015-08-05T01:03:42+00:00"}, {"name": "firebase/php-jwt", "version": "v6.4.0", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "reference": "4dd1e007f22a927ac77da5a3fbb067b42d3bc224", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1||^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^6.5||^7.4", "phpspec/prophecy-phpunit": "^1.1", "phpunit/phpunit": "^7.5||^9.5", "psr/cache": "^1.0||^2.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.4.0"}, "time": "2023-02-09T21:01:23+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.7.0", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "fb7566caccf22d74d1ab270de3551f72a58399f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/fb7566caccf22d74d1ab270de3551f72a58399f5", "reference": "fb7566caccf22d74d1ab270de3551f72a58399f5", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.7.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-05-21T14:04:53+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "3a494dc7dc1d7d12e511890177ae2d0e6c107da6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/3a494dc7dc1d7d12e511890177ae2d0e6c107da6", "reference": "3a494dc7dc1d7d12e511890177ae2d0e6c107da6", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-05-21T13:50:22+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.5.0", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "b635f279edd83fc275f822a1188157ffea568ff6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/b635f279edd83fc275f822a1188157ffea568ff6", "reference": "b635f279edd83fc275f822a1188157ffea568ff6", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.5.0"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-04-17T16:11:26+00:00"}, {"name": "maennchen/zipstream-php", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/c4c5803cc1f93df3d2448478ef79394a5981cc58", "reference": "c4c5803cc1f93df3d2448478ef79394a5981cc58", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"myclabs/php-enum": "^1.5", "php": ">= 7.1", "psr/http-message": "^1.0", "symfony/polyfill-mbstring": "^1.0"}, "require-dev": {"ext-zip": "*", "guzzlehttp/guzzle": ">= 6.3", "mikey179/vfsstream": "^1.6", "phpunit/phpunit": ">= 7.5"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/2.1.0"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2020-05-30T13:11:16+00:00"}, {"name": "markbaker/complex", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "6f724d7e04606fd8adaa4e3bb381c3e9db09c946"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/6f724d7e04606fd8adaa4e3bb381c3e9db09c946", "reference": "6f724d7e04606fd8adaa4e3bb381c3e9db09c946", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"files": ["classes/src/functions/abs.php", "classes/src/functions/acos.php", "classes/src/functions/acosh.php", "classes/src/functions/acot.php", "classes/src/functions/acoth.php", "classes/src/functions/acsc.php", "classes/src/functions/acsch.php", "classes/src/functions/argument.php", "classes/src/functions/asec.php", "classes/src/functions/asech.php", "classes/src/functions/asin.php", "classes/src/functions/asinh.php", "classes/src/functions/atan.php", "classes/src/functions/atanh.php", "classes/src/functions/conjugate.php", "classes/src/functions/cos.php", "classes/src/functions/cosh.php", "classes/src/functions/cot.php", "classes/src/functions/coth.php", "classes/src/functions/csc.php", "classes/src/functions/csch.php", "classes/src/functions/exp.php", "classes/src/functions/inverse.php", "classes/src/functions/ln.php", "classes/src/functions/log2.php", "classes/src/functions/log10.php", "classes/src/functions/negative.php", "classes/src/functions/pow.php", "classes/src/functions/rho.php", "classes/src/functions/sec.php", "classes/src/functions/sech.php", "classes/src/functions/sin.php", "classes/src/functions/sinh.php", "classes/src/functions/sqrt.php", "classes/src/functions/tan.php", "classes/src/functions/tanh.php", "classes/src/functions/theta.php", "classes/src/operations/add.php", "classes/src/operations/subtract.php", "classes/src/operations/multiply.php", "classes/src/operations/divideby.php", "classes/src/operations/divideinto.php"], "psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/2.0.3"}, "time": "2021-06-02T09:44:11+00:00"}, {"name": "markbaker/matrix", "version": "2.1.3", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "174395a901b5ba0925f1d790fa91bab531074b61"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/174395a901b5ba0925f1d790fa91bab531074b61", "reference": "174395a901b5ba0925f1d790fa91bab531074b61", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "phpcompatibility/php-compatibility": "^9.0", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.3", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.4"}, "type": "library", "autoload": {"files": ["classes/src/Functions/adjoint.php", "classes/src/Functions/antidiagonal.php", "classes/src/Functions/cofactors.php", "classes/src/Functions/determinant.php", "classes/src/Functions/diagonal.php", "classes/src/Functions/identity.php", "classes/src/Functions/inverse.php", "classes/src/Functions/minors.php", "classes/src/Functions/trace.php", "classes/src/Functions/transpose.php", "classes/src/Operations/add.php", "classes/src/Operations/directsum.php", "classes/src/Operations/subtract.php", "classes/src/Operations/multiply.php", "classes/src/Operations/divideby.php", "classes/src/Operations/divideinto.php"], "psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/2.1.3"}, "time": "2021-05-25T15:42:17+00:00"}, {"name": "monolog/monolog", "version": "1.27.1", "source": {"type": "git", "url": "https://github.com/Seldaek/monolog.git", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Seldaek/monolog/zipball/904713c5929655dc9b97288b69cfeedad610c9a1", "reference": "904713c5929655dc9b97288b69cfeedad610c9a1", "shasum": ""}, "require": {"php": ">=5.3.0", "psr/log": "~1.0"}, "provide": {"psr/log-implementation": "1.0.0"}, "require-dev": {"aws/aws-sdk-php": "^2.4.9 || ^3.0", "doctrine/couchdb": "~1.0@dev", "graylog2/gelf-php": "~1.0", "php-amqplib/php-amqplib": "~2.4", "php-console/php-console": "^3.1.3", "phpstan/phpstan": "^0.12.59", "phpunit/phpunit": "~4.5", "ruflin/elastica": ">=0.90 <3.0", "sentry/sentry": "^0.13", "swiftmailer/swiftmailer": "^5.3|^6.0"}, "suggest": {"aws/aws-sdk-php": "Allow sending log messages to AWS services like DynamoDB", "doctrine/couchdb": "Allow sending log messages to a CouchDB server", "ext-amqp": "Allow sending log messages to an AMQP server (1.0+ required)", "ext-mongo": "Allow sending log messages to a MongoDB server", "graylog2/gelf-php": "Allow sending log messages to a GrayLog2 server", "mongodb/mongodb": "Allow sending log messages to a MongoDB server via PHP Driver", "php-amqplib/php-amqplib": "Allow sending log messages to an AMQP server using php-amqplib", "php-console/php-console": "Allow sending log messages to Google Chrome", "rollbar/rollbar": "Allow sending log messages to Rollbar", "ruflin/elastica": "Allow sending log messages to an Elastic Search server", "sentry/sentry": "Allow sending log messages to a Sentry server"}, "type": "library", "autoload": {"psr-4": {"Monolog\\": "src/Monolog"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Sends your logs to files, sockets, inboxes, databases and various web services", "homepage": "http://github.com/Seldaek/monolog", "keywords": ["log", "logging", "psr-3"], "support": {"issues": "https://github.com/Seldaek/monolog/issues", "source": "https://github.com/Seldaek/monolog/tree/1.27.1"}, "funding": [{"url": "https://github.com/Seldaek", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/monolog/monolog", "type": "tidelift"}], "time": "2022-06-09T08:53:42+00:00"}, {"name": "myclabs/php-enum", "version": "1.7.7", "source": {"type": "git", "url": "https://github.com/myclabs/php-enum.git", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/myclabs/php-enum/zipball/d178027d1e679832db9f38248fcc7200647dc2b7", "reference": "d178027d1e679832db9f38248fcc7200647dc2b7", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-json": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "^7", "squizlabs/php_codesniffer": "1.*", "vimeo/psalm": "^3.8"}, "type": "library", "autoload": {"psr-4": {"MyCLabs\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP Enum contributors", "homepage": "https://github.com/myclabs/php-enum/graphs/contributors"}], "description": "PHP Enum implementation", "homepage": "http://github.com/myclabs/php-enum", "keywords": ["enum"], "support": {"issues": "https://github.com/myclabs/php-enum/issues", "source": "https://github.com/myclabs/php-enum/tree/1.7.7"}, "funding": [{"url": "https://github.com/mnapoli", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/myclabs/php-enum", "type": "tidelift"}], "time": "2020-11-14T18:14:52+00:00"}, {"name": "nmred/kafka-php", "version": "v0.2.0.8", "source": {"type": "git", "url": "https://github.com/weiboad/kafka-php.git", "reference": "d5b395aeb9d069dce49fc1dceff0a6f45750d981"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/weiboad/kafka-php/zipball/d5b395aeb9d069dce49fc1dceff0a6f45750d981", "reference": "d5b395aeb9d069dce49fc1dceff0a6f45750d981", "shasum": ""}, "require": {"amphp/amp": "v1.2.2", "php": ">=5.5", "psr/log": "1.0.2"}, "require-dev": {"kmelia/monolog-stdout-handler": "1.2.1", "monolog/monolog": "1.22.1", "phpunit/phpcov": "*", "phpunit/phpunit": "~4.0", "satooshi/php-coveralls": "dev-master"}, "type": "library", "autoload": {"psr-0": {"Kafka\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "Kafka client for php", "homepage": "http://www.swanlinux.net", "keywords": ["client", "kafka"], "time": "2017-10-23T09:33:08+00:00"}, {"name": "paragonie/random_compat", "version": "v9.99.100", "source": {"type": "git", "url": "https://github.com/paragonie/random_compat.git", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/paragonie/random_compat/zipball/996434e5492cb4c3edcb9168db6fbb1359ef965a", "reference": "996434e5492cb4c3edcb9168db6fbb1359ef965a", "shasum": ""}, "require": {"php": ">= 7"}, "require-dev": {"phpunit/phpunit": "4.*|5.*", "vimeo/psalm": "^1"}, "suggest": {"ext-libsodium": "Provides a modern crypto API that can be used to generate random bytes."}, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Paragon Initiative Enterprises", "email": "<EMAIL>", "homepage": "https://paragonie.com"}], "description": "PHP 5.x polyfill for random_bytes() and random_int() from PHP 7", "keywords": ["csprng", "polyfill", "pseudorandom", "random"], "time": "2020-10-15T08:29:30+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "1.15.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "a8e8068b31b8119e1daa5b1eb5715a3a8ea8305f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/a8e8068b31b8119e1daa5b1eb5715a3a8ea8305f", "reference": "a8e8068b31b8119e1daa5b1eb5715a3a8ea8305f", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1", "markbaker/complex": "^1.5|^2.0", "markbaker/matrix": "^1.2|^2.0", "php": "^7.2|^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0"}, "require-dev": {"dompdf/dompdf": "^0.8.5", "friendsofphp/php-cs-fixer": "^2.16", "jpgraph/jpgraph": "^4.0", "mpdf/mpdf": "^8.0", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^8.5|^9.3", "squizlabs/php_codesniffer": "^3.5", "tecnickcom/tcpdf": "^6.3"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)", "jpgraph/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer (doesn't yet support PHP8)"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/1.15.0"}, "time": "2020-10-11T13:20:59+00:00"}, {"name": "promphp/prometheus_client_php", "version": "v2.7.1", "source": {"type": "git", "url": "https://github.com/PromPHP/prometheus_client_php.git", "reference": "82e42b5a491ee94127110548ee835052b9765a78"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PromPHP/prometheus_client_php/zipball/82e42b5a491ee94127110548ee835052b9765a78", "reference": "82e42b5a491ee94127110548ee835052b9765a78", "shasum": ""}, "require": {"ext-json": "*", "php": "^7.2|^8.0"}, "replace": {"endclothing/prometheus_client_php": "*", "jimdo/prometheus_client_php": "*", "lkaemmerling/prometheus_client_php": "*"}, "require-dev": {"guzzlehttp/guzzle": "^6.3|^7.0", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5.4", "phpstan/phpstan-phpunit": "^1.1.0", "phpstan/phpstan-strict-rules": "^1.1.0", "phpunit/phpunit": "^9.4", "squizlabs/php_codesniffer": "^3.6", "symfony/polyfill-apcu": "^1.6"}, "suggest": {"ext-apc": "Required if using APCu.", "ext-redis": "Required if using Redis.", "promphp/prometheus_push_gateway_php": "An easy client for using Prometheus PushGateway.", "symfony/polyfill-apcu": "Required if you use APCu on PHP8.0+"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0-dev"}}, "autoload": {"psr-4": {"Prometheus\\": "src/Prometheus/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Prometheus instrumentation library for PHP applications.", "support": {"issues": "https://github.com/PromPHP/prometheus_client_php/issues", "source": "https://github.com/PromPHP/prometheus_client_php/tree/v2.7.1"}, "time": "2023-04-24T18:11:52+00:00"}, {"name": "promphp/prometheus_push_gateway_php", "version": "v1.1.1", "source": {"type": "git", "url": "https://github.com/PromPHP/prometheus_push_gateway_php.git", "reference": "0129c78cfccdae0851359ac69f1a92e53b5f5126"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PromPHP/prometheus_push_gateway_php/zipball/0129c78cfccdae0851359ac69f1a92e53b5f5126", "reference": "0129c78cfccdae0851359ac69f1a92e53b5f5126", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^6.3|^7.0", "php": "^7.2|^8.0", "promphp/prometheus_client_php": "^1.0|^2.0"}, "require-dev": {"phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^0.12.50", "phpstan/phpstan-phpunit": "^0.12.16", "phpstan/phpstan-strict-rules": "^0.12.5", "phpunit/phpunit": "^8.4|^9.4", "squizlabs/php_codesniffer": "^3.5"}, "type": "library", "autoload": {"psr-4": {"PrometheusPushGateway\\": "src/PrometheusPushGateway/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Prometheus Push Gateway client for PHP applications.", "support": {"issues": "https://github.com/PromPHP/prometheus_push_gateway_php/issues", "source": "https://github.com/PromPHP/prometheus_push_gateway_php/tree/v1.1.1"}, "time": "2022-04-11T18:49:08+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/http-client", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "0955afe48220520692d2d09f7ab7e0f93ffd6a31"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/0955afe48220520692d2d09f7ab7e0f93ffd6a31", "reference": "0955afe48220520692d2d09f7ab7e0f93ffd6a31", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/1.0.2"}, "time": "2023-04-10T20:12:12+00:00"}, {"name": "psr/http-factory", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "e616d01114759c4c489f93b099585439f795fe35"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/e616d01114759c4c489f93b099585439f795fe35", "reference": "e616d01114759c4c489f93b099585439f795fe35", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/1.0.2"}, "time": "2023-04-10T20:10:41+00:00"}, {"name": "psr/http-message", "version": "1.1", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "reference": "cb6ce4845ce34a8ad9e68117c10ee90a29919eba", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/1.1"}, "time": "2023-04-04T09:50:52+00:00"}, {"name": "psr/log", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "reference": "4ebe3a8bf773a19edfe0a84b6585ba3d401b724d", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "time": "2016-10-10T12:19:37+00:00"}, {"name": "psr/simple-cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "reference": "408d5eafb83c57f6365a3ca330ff23aa4a5fa39b", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/master"}, "time": "2017-10-23T01:57:42+00:00"}, {"name": "qcloud_sts/qcloud-sts-sdk", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/tencentyun/qcloud-cos-sts-php-sdk.git", "reference": "d6b12a0bdebc025f039c958979714298d408bf92"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/tencentyun/qcloud-cos-sts-php-sdk/zipball/d6b12a0bdebc025f039c958979714298d408bf92", "reference": "d6b12a0bdebc025f039c958979714298d408bf92", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "autoload": {"psr-4": {"QCloud\\COSSTS\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "qcloudterminal", "email": "<EMAIL>"}], "description": "PHP SDK for QCloud STS", "homepage": "https://github.com/tencentyun/qcloud-cos-sts-sdk", "keywords": ["cos", "php", "qcloud", "sts"], "time": "2020-06-03T11:55:39+00:00"}, {"name": "qtt/game-center", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/shenlihust/qttgame.git", "reference": "10217b7174a8dad22b04bc3f72f8bc9ea115c180"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/shenlihust/qttgame/zipball/10217b7174a8dad22b04bc3f72f8bc9ea115c180", "reference": "10217b7174a8dad22b04bc3f72f8bc9ea115c180", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "type": "sdk", "autoload": {"psr-4": {"QttGame\\": "./src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "game open sdk", "time": "2019-03-13T08:37:03+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "swiftmailer/swiftmailer", "version": "v5.4.3", "source": {"type": "git", "url": "https://github.com/swiftmailer/swiftmailer.git", "reference": "4cc92842069c2bbc1f28daaaf1d2576ec4dfe153"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/swiftmailer/swiftmailer/zipball/4cc92842069c2bbc1f28daaaf1d2576ec4dfe153", "reference": "4cc92842069c2bbc1f28daaaf1d2576ec4dfe153", "shasum": ""}, "require": {"php": ">=5.3.3"}, "require-dev": {"mockery/mockery": "~0.9.1"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.4-dev"}}, "autoload": {"files": ["lib/swift_required.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Swiftmailer, free feature-rich PHP mailer", "homepage": "http://swiftmailer.org", "keywords": ["email", "mail", "mailer"], "abandoned": "symfony/mailer", "time": "2016-07-08T11:51:25+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v2.5.2", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v2.5.2"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "symfony/finder", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/66bd787edb5e42ff59d3523f623895af05043e4f", "reference": "66bd787edb5e42ff59d3523f623895af05043e4f", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-07-29T07:35:46+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/5bbc823adecdae860bb64756d639ecfec17b050a", "reference": "5bbc823adecdae860bb64756d639ecfec17b050a", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "reference": "8ad114f6b39e2c98a8b0e3bd907732c207c2b534", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.27.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "reference": "7a6ff3f1959bb01aefccb463a0f2cd3d3d2fd936", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.27-dev"}, "thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.27.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-11-03T14:55:06+00:00"}, {"name": "symfony/process", "version": "v4.4.44", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "reference": "5cee9cdc4f7805e2699d9fd66991a0e6df8252a2", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=7.1.3", "symfony/polyfill-php80": "^1.16"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v4.4.44"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-06-27T13:16:42+00:00"}, {"name": "symfony/yaml", "version": "v4.4.45", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "reference": "aeccc4dc52a9e634f1d1eebeb21eacfdcff1053d", "shasum": ""}, "require": {"php": ">=7.1.3", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/console": "<3.4"}, "require-dev": {"symfony/console": "^3.4|^4.0|^5.0"}, "suggest": {"symfony/console": "For validating YAML files using the lint command"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v4.4.45"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-08-02T15:47:23+00:00"}, {"name": "yiisoft/yii2", "version": "2.0.46", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-framework.git", "reference": "d73259c3bc886648a6875109f9f09cddeff03708"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-framework/zipball/d73259c3bc886648a6875109f9f09cddeff03708", "reference": "d73259c3bc886648a6875109f9f09cddeff03708", "shasum": ""}, "require": {"bower-asset/inputmask": "~3.2.2 | ~3.3.5", "bower-asset/jquery": "3.6.*@stable | 3.5.*@stable | 3.4.*@stable | 3.3.*@stable | 3.2.*@stable | 3.1.*@stable | 2.2.*@stable | 2.1.*@stable | 1.11.*@stable | 1.12.*@stable", "bower-asset/punycode": "1.3.*", "bower-asset/yii2-pjax": "~2.0.1", "cebe/markdown": "~1.0.0 | ~1.1.0 | ~1.2.0", "ext-ctype": "*", "ext-mbstring": "*", "ezyang/htmlpurifier": "~4.6", "lib-pcre": "*", "paragonie/random_compat": ">=1", "php": ">=5.4.0", "yiisoft/yii2-composer": "~2.0.4"}, "bin": ["yii"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.yiiframework.com/", "role": "Founder and project lead"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://rmcreative.ru/", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "http://mdomba.info/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://www.cebe.cc/", "role": "Core framework development"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://resurtm.com/", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Core framework development"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://dynasource.eu", "role": "Core framework development"}], "description": "Yii PHP Framework Version 2", "homepage": "https://www.yiiframework.com/", "keywords": ["framework", "yii2"], "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2", "type": "tidelift"}], "time": "2022-08-18T22:18:45+00:00"}, {"name": "yiisoft/yii2-bootstrap", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-bootstrap.git", "reference": "3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-bootstrap/zipball/3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5", "reference": "3fd2b8c950cce79d60e9702d6bcb24eb3c80f6c5", "shasum": ""}, "require": {"bower-asset/bootstrap": "3.3.* | 3.2.* | 3.1.*", "yiisoft/yii2": ">=2.0.6"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"yii\\bootstrap\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Twitter Bootstrap extension for the Yii framework", "keywords": ["bootstrap", "yii2"], "time": "2016-03-17T03:29:28+00:00"}, {"name": "yiisoft/yii2-composer", "version": "2.0.10", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-composer.git", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-composer/zipball/94bb3f66e779e2774f8776d6e1bdeab402940510", "reference": "94bb3f66e779e2774f8776d6e1bdeab402940510", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 | ^2.0"}, "require-dev": {"composer/composer": "^1.0 | ^2.0@dev", "phpunit/phpunit": "<7"}, "type": "composer-plugin", "extra": {"class": "yii\\composer\\Plugin", "branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\composer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The composer plugin for Yii extension installer", "keywords": ["composer", "extension installer", "yii2"], "support": {"forum": "http://www.yiiframework.com/forum/", "irc": "irc://irc.freenode.net/yii", "issues": "https://github.com/yiisoft/yii2-composer/issues", "source": "https://github.com/yiisoft/yii2-composer", "wiki": "http://www.yiiframework.com/wiki/"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-composer", "type": "tidelift"}], "time": "2020-06-24T00:04:01+00:00"}, {"name": "yiisoft/yii2-queue", "version": "2.3.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-queue.git", "reference": "c1bf0ef5dbe107dc1cf692c1349b9ddd2485a399"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-queue/zipball/c1bf0ef5dbe107dc1cf692c1349b9ddd2485a399", "reference": "c1bf0ef5dbe107dc1cf692c1349b9ddd2485a399", "shasum": "", "mirrors": [{"url": "https://mirrors.aliyun.com/composer/dists/%package%/%reference%.%type%", "preferred": true}]}, "require": {"php": ">=5.5.0", "symfony/process": "^3.3||^4.0||^5.0||^6.0", "yiisoft/yii2": "~2.0.14"}, "require-dev": {"aws/aws-sdk-php": ">=2.4", "enqueue/amqp-lib": "^0.8||^0.9.10", "enqueue/stomp": "^0.8.39", "opis/closure": "*", "pda/pheanstalk": "v3.*", "php-amqplib/php-amqplib": "*", "phpunit/phpunit": "~4.4", "yiisoft/yii2-debug": "*", "yiisoft/yii2-gii": "*", "yiisoft/yii2-redis": "*"}, "suggest": {"aws/aws-sdk-php": "Need for aws SQS.", "enqueue/amqp-lib": "Need for AMQP interop queue.", "enqueue/stomp": "Need for Stomp queue.", "ext-gearman": "Need for Gearman queue.", "ext-pcntl": "Need for process signals.", "pda/pheanstalk": "Need for Beanstalk queue.", "php-amqplib/php-amqplib": "Need for AMQP queue.", "yiisoft/yii2-redis": "Need for Redis queue."}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.x-dev"}}, "autoload": {"psr-4": {"yii\\queue\\": "src", "yii\\queue\\db\\": "src/drivers/db", "yii\\queue\\sqs\\": "src/drivers/sqs", "yii\\queue\\amqp\\": "src/drivers/amqp", "yii\\queue\\file\\": "src/drivers/file", "yii\\queue\\sync\\": "src/drivers/sync", "yii\\queue\\redis\\": "src/drivers/redis", "yii\\queue\\stomp\\": "src/drivers/stomp", "yii\\queue\\gearman\\": "src/drivers/gearman", "yii\\queue\\beanstalk\\": "src/drivers/beanstalk", "yii\\queue\\amqp_interop\\": "src/drivers/amqp_interop"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "z<PERSON>av<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Yii2 Queue Extension which supported DB, Redis, RabbitMQ, Beanstalk, SQS and Gearman", "keywords": ["async", "beanstalk", "db", "gearman", "gii", "queue", "rabbitmq", "redis", "sqs", "yii"], "support": {"docs": "https://github.com/yiisoft/yii2-queue/blob/master/docs/guide", "issues": "https://github.com/yiisoft/yii2-queue/issues", "source": "https://github.com/yiisoft/yii2-queue"}, "funding": [{"url": "https://github.com/yiisoft", "type": "github"}, {"url": "https://opencollective.com/yiisoft", "type": "open_collective"}, {"url": "https://tidelift.com/funding/github/packagist/yiisoft/yii2-queue", "type": "tidelift"}], "time": "2022-11-18T17:16:47+00:00"}, {"name": "yiisoft/yii2-swiftmailer", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-swiftmailer.git", "reference": "e2c6315caff30a9271a7afad4d684627721dc69a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-swiftmailer/zipball/e2c6315caff30a9271a7afad4d684627721dc69a", "reference": "e2c6315caff30a9271a7afad4d684627721dc69a", "shasum": ""}, "require": {"swiftmailer/swiftmailer": "~5.0", "yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\swiftmailer\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The SwiftMailer integration for the Yii framework", "keywords": ["email", "mail", "mailer", "swift", "swiftmailer", "yii2"], "time": "2016-03-17T03:58:49+00:00"}, {"name": "zircote/swagger-php", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/zircote/swagger-php.git", "reference": "f10ab7f81d89dba97653a980cc90cf4b7b73f543"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/zircote/swagger-php/zipball/f10ab7f81d89dba97653a980cc90cf4b7b73f543", "reference": "f10ab7f81d89dba97653a980cc90cf4b7b73f543", "shasum": ""}, "require": {"doctrine/annotations": "*", "php": ">=7.0", "symfony/finder": ">=2.2", "symfony/yaml": ">=3.3"}, "require-dev": {"phpunit/phpunit": ">=6.3", "squizlabs/php_codesniffer": ">=3.3", "zendframework/zend-form": "<2.8"}, "bin": ["bin/openapi"], "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"OpenApi\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.zircote.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://bfanger.nl"}], "description": "swagger-php - Generate interactive documentation for your RESTful API using phpdoc annotations", "homepage": "https://github.com/zircote/swagger-php/", "keywords": ["api", "json", "rest", "service discovery"], "support": {"issues": "https://github.com/zircote/swagger-php/issues", "source": "https://github.com/zircote/swagger-php/tree/master"}, "time": "2018-11-16T15:04:29+00:00"}], "packages-dev": [{"name": "bower-asset/typeahead.js", "version": "v0.11.1", "source": {"type": "git", "url": "https://github.com/twitter/typeahead.js.git", "reference": "588440f66559714280628a4f9799f0c4eb880a4a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/twitter/typeahead.js/zipball/588440f66559714280628a4f9799f0c4eb880a4a", "reference": "588440f66559714280628a4f9799f0c4eb880a4a", "shasum": ""}, "require": {"bower-asset/jquery": ">=1.7"}, "require-dev": {"bower-asset/jasmine-ajax": "~1.3.1", "bower-asset/jasmine-jquery": "~1.5.2", "bower-asset/jquery": "~1.7"}, "type": "bower-asset-library", "extra": {"bower-asset-main": "dist/typeahead.bundle.js"}}, {"name": "fzaninotto/faker", "version": "v1.6.0", "source": {"type": "git", "url": "https://github.com/fzaninotto/Faker.git", "reference": "44f9a286a04b80c76a4e5fb7aad8bb539b920123"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fzaninotto/Faker/zipball/44f9a286a04b80c76a4e5fb7aad8bb539b920123", "reference": "44f9a286a04b80c76a4e5fb7aad8bb539b920123", "shasum": ""}, "require": {"php": "^5.3.3|^7.0"}, "require-dev": {"ext-intl": "*", "phpunit/phpunit": "~4.0", "squizlabs/php_codesniffer": "~1.5"}, "type": "library", "extra": {"branch-alias": []}, "autoload": {"psr-4": {"Faker\\": "src/Faker/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "Faker is a PHP library that generates fake data for you.", "keywords": ["data", "faker", "fixtures"], "abandoned": true, "time": "2016-04-29T12:21:54+00:00"}, {"name": "phpspec/php-diff", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/phpspec/php-diff.git", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpspec/php-diff/zipball/0464787bfa7cd13576c5a1e318709768798bec6a", "reference": "0464787bfa7cd13576c5a1e318709768798bec6a", "shasum": ""}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-0": {"Diff": "lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "homepage": "http://github.com/chrisboulton"}], "description": "A comprehensive library for generating differences between two hashable objects (strings or arrays).", "time": "2016-04-07T12:29:16+00:00"}, {"name": "yiisoft/yii2-codeception", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-codeception.git", "reference": "c916a36d09fc128b05a374e7922bc56854334d56"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-codeception/zipball/c916a36d09fc128b05a374e7922bc56854334d56", "reference": "c916a36d09fc128b05a374e7922bc56854334d56", "shasum": ""}, "require": {"yiisoft/yii2": ">=2.0.4"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\codeception\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Codeception integration for the Yii framework", "keywords": ["codeception", "yii2"], "abandoned": "codeception/codeception", "time": "2016-03-17T03:41:26+00:00"}, {"name": "yiisoft/yii2-debug", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-debug.git", "reference": "55ed2e853ed8050a34415f63a4da84f88a56f895"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-debug/zipball/55ed2e853ed8050a34415f63a4da84f88a56f895", "reference": "55ed2e853ed8050a34415f63a4da84f88a56f895", "shasum": ""}, "require": {"yiisoft/yii2": ">=2.0.4", "yiisoft/yii2-bootstrap": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\debug\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The debugger extension for the Yii framework", "keywords": ["debug", "debugger", "yii2"], "time": "2016-03-17T03:50:19+00:00"}, {"name": "yiisoft/yii2-faker", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-faker.git", "reference": "b88ca69ee226a3610b2c26c026c3203d7ac50f6c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-faker/zipball/b88ca69ee226a3610b2c26c026c3203d7ac50f6c", "reference": "b88ca69ee226a3610b2c26c026c3203d7ac50f6c", "shasum": ""}, "require": {"fzaninotto/faker": "*", "yiisoft/yii2": "*"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"yii\\faker\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Fixture generator. The Faker integration for the Yii framework.", "keywords": ["Fixture", "faker", "yii2"], "time": "2015-03-01T06:22:44+00:00"}, {"name": "yiisoft/yii2-gii", "version": "2.0.5", "source": {"type": "git", "url": "https://github.com/yiisoft/yii2-gii.git", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/yiisoft/yii2-gii/zipball/1bd6df6804ca077ec022587905a0d43eb286f507", "reference": "1bd6df6804ca077ec022587905a0d43eb286f507", "shasum": ""}, "require": {"bower-asset/typeahead.js": "0.10.* | ~0.11.0", "phpspec/php-diff": ">=1.0.2", "yiisoft/yii2": ">=2.0.4", "yiisoft/yii2-bootstrap": "~2.0"}, "type": "yii2-extension", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}, "asset-installer-paths": {"npm-asset-library": "vendor/npm", "bower-asset-library": "vendor/bower"}}, "autoload": {"psr-4": {"yii\\gii\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The Gii extension for the Yii framework", "keywords": ["code generator", "gii", "yii2"], "time": "2016-03-18T14:09:46+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.0.0", "ext-json": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-libxml": "*", "ext-curl": "*", "ext-zlib": "*", "ext-bcmath": "*", "ext-sockets": "*", "ext-redis": "*", "ext-iconv": "*", "ext-openssl": "*", "ext-ctype": "*", "ext-calendar": "*"}, "platform-dev": [], "plugin-api-version": "2.6.0"}