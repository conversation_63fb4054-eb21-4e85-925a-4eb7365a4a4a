<?php

$params = require(__DIR__ . '/params.php');
$queues = require(__DIR__ . '/queues.php');

$config = [
    'id' => 'basic',
    'language' => 'zh-CN',
    'name' => PRO_NAME,
    'basePath' => dirname(__DIR__)."/../",
    'bootstrap' => ['log', 'queue', 'queueLog'],
    'modules' => require(__DIR__ . '/modules.php'),
    'components' => [
        'request' => [
            'enableCookieValidation' => false,
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'fgvsafad',
        ],
        'errorHandler' => [
            //            'class' => 'app\components\ErrorHandler',
        ],
        'db_app'  => require(__DIR__ . '/db_app.php'),
        'urlManager' => [
            'enablePrettyUrl' => true,
            'enableStrictParsing' => false, //不启用严格解析
            'showScriptName' => false, //隐藏index.php
            'rules' => [
                '<module:\w+>/<controller:\w+>/<id:\d+>' => '<module>/<controller>/view',
                '<controller:\w+>/<id:\d+>' => '<controller>/view',
            ],
        ],
        'elasticsearch' => require(__DIR__ . '/elasticsearch.php'),
        'log'          => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets'    => [

                //                [
                //                    'class'         => 'notamedia\sentry\SentryTarget',
                //                    'dsn'           => 'https://<EMAIL>/4505079827595264',
                //                    'levels'        => ['error', 'warning'],
                //                    'context'       => true,
                //                    'clientOptions' => ['release' => 'v2.0.0', 'environment' => YII_ENV,]
                //                ],

                [
                    'class'      => 'yii\log\FileTarget',
                    'levels'     => ['error', 'warning', 'info', 'trace'],
                    'categories' => [
                        'yii\db\*',
                        'yii\web\HttpException:*',
                        ' app\modules\goods\models\*'
                    ],
                    'logFile'    => '@runtime/../runtime/logs/sql_' . date('y_m_d') . '.log',
                ]
            ],
        ],
        'queue'     => $queues['common'],
        'queueLog'  => $queues['log'],
    ],
    'params' => $params,
    'timeZone' => 'Asia/Chongqing',
];


function dd()
{
    if (!isset($_REQUEST['dump_header'])) {
        header('Content-Type: text/html; charset=utf-8');
        $_REQUEST['dump_header'] = 1;
    }
    $args = func_get_args();
    foreach ($args as $arg) {
        \yii\helpers\VarDumper::dump($arg, 10, true) . PHP_EOL;
        echo '<br>';
        // 添加红色的细红线dash
        echo '<hr style="border: 1px dashed rgba(248,66,66,0.41);">';
    }
    die;
}


function dump()
{
    // 设置在本次请求中的变量
    if (!isset($_REQUEST['dump_header'])) {
        header('Content-Type: text/html; charset=utf-8');
        $_REQUEST['dump_header'] = 1;
    }
    $args = func_get_args();
    foreach ($args as $arg) {
        \yii\helpers\VarDumper::dump($arg, 10, true) . PHP_EOL;
        echo '<br>';
        echo '<hr style="border: 1px dashed rgba(248,66,66,0.41);">';
    }
}

return $config;

