<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;



use app\models\by;
use app\models\CUtil;
use app\jobs\UserMsgJob;

class UserMsg
{

    protected static $_instance = null;

    private function __construct() {}

    private function __clone() {}



    private function __userMsgList($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return AppCRedisKeys::userMsgList($mod);
    }


    /**
     * @return UserMsg
     */
    public static function factory(): UserMsg
    {

        if (!isset(self::$_instance) || !is_object (self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    /**
     * @param int $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * 插入队列
     */
    public function push(int $user_id, string $function, array $args, int $time = 0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function, $methods)) {
            return [false, '方法不存在'];
        }

        if (empty($args)) {
            return [false, '缺少参数'];
        }

        //注释下面redis，改用supervisors维护进程
        \Yii::$app->queue->push(new UserMsgJob(['user_id' => $user_id, 'function' => $function, 'args' => $args, 'time' => $time]));

        // $r_key = $this->__userMsgList($user_id);

        // by::redis('core')->rPush($r_key,
        //     json_encode(
        //         [
        //             'function' => $function,
        //             'args' => $args,
        //             'time' => $time ?: time(),
        //         ]
        //     )
        // );

        return [true, 'ok'];
    }

    /**
     * @deprecated 定时任务处理userMsg，废弃
     * 改用上面函数push的supervisors维护进程
     * Undocumented function
     *
     * @param [type] $index
     * @return void
     */
    public function synUserMsg($index){
        try {
            $redis_key = $this->__userMsgList($index);
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $exist_key  = AppCRedisKeys::ProcessExistKey("usermsg");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true){
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'link_run_usermsg');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'link_run_usermsg');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);

                $data    = $ret[1] ?? "";
                if(!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data,true);
                if(empty($aData)) {
                    continue;
                }

                //todo 三次重试
                $msg = '';
                for ($i = 1; $i <= 3; $i++) {
                    list($status,$msg) = $this->run($aData['function'],$aData['args'],$aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i*2);
                }

                if(!$status) {
                    //重试也失败 加入到mysql记录
                    by::model('UserMsgLogModel', 'main')->saveLog($aData['function'],$aData['args'],$msg,$aData['time']);
                }
            }


        }catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'link_run_usermsg_err');
            exit($error);
        }
    }




    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'saveUserMsg':
                return $this->$function($args['user_id'],$args['send_time'],$args['arr']);
            default:
                return [false, 'function 不存在'];
        }
    }





    /**
     * @param $user_id
     * @param $send_time
     * @param $arr
     * @return array
     * @throws \yii\db\Exception
     * 保存推送消息信息
     */
    public function saveUserMsg($user_id,$send_time,$arr): array
    {
        list($s,$data) = by::UserMsg()->SaveLog($user_id,$send_time,$arr);
        return [$s,$data];
    }


}
