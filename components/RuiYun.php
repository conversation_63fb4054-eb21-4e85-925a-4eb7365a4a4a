<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use app\jobs\RuiYunJob;
use Faker\Provider\Uuid;
use yii\db\Exception;

/**
 * RuiYun 公共调用方法
 */
class RuiYun
{
    private static $_instance;


    CONST DOMAIN = 'https://fspro60-openapi.fscloud.com.cn/open';

    const TENANT_NAME  = YII_ENV_PROD ? 'dreame' : 'dreametst'; //租户名
    const APP_ID       = YII_ENV_PROD ? 'cx_ruiyun' : 'cx_ruiyun'; //appid
    const REGISTER_KEY = 'jO7mfdrykq';//登录密码
    const SIGN_KEY     = 'uBQtJgPu08xHO2YWlFDGqdH8JYlZdGTIISpCSjta';


    const MOVA_TENANT_NAME = YII_ENV_PROD ? 'dreame' : 'dreametst'; //租户名

    const MOVA_APP_ID = YII_ENV_PROD ? 'mova' : 'mova';
    const MOVA_REGISTER_KEY = YII_ENV_PROD ? '7hI!gOS_QK' : 'RP6JCZq@vK';//登录密码
    const MOVA_SIGN_KEY = YII_ENV_PROD ? '5me4zLz1AiB24SHytQDHcs5WocFM3lDv5MVBXvmK' : 'Vo5crr3tqvk2c9TpxaxV1f1HYopaefakmIPqq8e8';


    public static function getConfigByTenant($tenant = '000000')
    {
        $configs = [
            '000000' => [
                'tenant_name'  => self::TENANT_NAME,
                'app_id'       => self::APP_ID,
                'register_key' => self::REGISTER_KEY,
                'sign_key'     => self::SIGN_KEY,
            ],
            by::plumbingOrder()::TENANT['MOVA'] => [
                'tenant_name'  => self::MOVA_TENANT_NAME,
                'app_id'       => self::MOVA_APP_ID,
                'register_key' => self::MOVA_REGISTER_KEY,
                'sign_key'     => self::MOVA_SIGN_KEY,
            ],
        ];
    
        return $configs[$tenant] ?? $configs['000000'];
    }


    //产品信息对应产品线
    const RUIYUN_PRODUCT_VALUE = [
        'S10'          => '010204',
        'S10 Pro'      => '010204',
        '上下水模组'        => '010204',
        'S10 Plus'     => '010204',
        'S10 Pro Plus' => '010204',
        'S10皮克斯草莓熊限定款' => '010204',
        'X10 Pro'      => '010204',
        'S10系列模块'      => '010204',
        '勘测（需联系客服）'    => '010204',
        'X10'          => '010204',
        'W10S'         => '010201',
        'W10S Pro'     => '010201',
        'W10S系列模块'     => '010201',
    ];

    //产品信息对应服务类型
    const RUIYUN_NEW_TYPE_6 = ['勘测（需联系客服）'];

    // 瑞云服务类型
    const RUIYUN_TYPES = [
        'UNKNOWN' => -1, // 未知
        'INSTALL' => 2, // 安装
        'SURVEY' => 6, // 勘测
        'GUIDE'   => 201, // 指导/服务
    ];

    const RUIYUN_TYPE_NAMES = [
        ' -1' => '未知', // 未知
        '2'   => '安装', // 安装
        '6'   => '勘测', // 勘测
        '201' => '指导', // 指导/服务
    ];

    const RUIYUN_SOURCE = [
      '000002' => 300
    ];

    /**
     * @return RuiYun
     */
    public static function factory(): RuiYun
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    const URL = [
            'SAVE'         => '/api/dynamic/new_srv_workorder/save',       //瑞云创建实体数据
            'SAVE_DETAIL'  => '/api/dynamic/new_srv_productline/save',     //瑞云补充实体明细
            'PROVINCE'     => '/api/dynamic/new_province',                 //省
            'PROVINCE_GET' => '/api/dynamic/new_province/get',             //省详情
            'CITY'         => '/api/dynamic/new_city',                     //市
            'CITY_GET'     => '/api/dynamic/new_city/get',                 //市详情
            'COUNTY'       => '/api/dynamic/new_county',                   //区
            'COUNTY_GET'   => '/api/dynamic/new_county/get',               //区详情
    ];

    const ADDRESS_EVENT = [
            'PROVINCE',
            'CITY',
            'COUNTY',
    ];
    private function __ruiYunList($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return AppCRedisKeys::ruiYunList($mod);
    }


    /**
     * @param int $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * 插入队列
     */
    public function push($user_id,string $function, array $args, $time=0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function,$methods)){
            return [false,'方法不存在'];
        }

        if (empty($args)){
            return [false,'缺少参数'];
        }

        $user_id = intval($user_id);

        //注释下面redis，改用supervisors维护进程
        \Yii::$app->queue->push(new RuiYunJob(['user_id' => $user_id, 'function' => $function, 'args' => $args, 'time' => $time]));

        // $r_key = $this->__ruiYunList($user_id);

        // by::redis('core')->rPush($r_key,
        //     json_encode(
        //         [
        //             'function' => $function,
        //             'args'     => $args,
        //             'time'     => $time ?: time(),
        //         ]
        //     )
        // );

        return [true,'ok'];
    }

    /**
     * 定时任务处理RuiYun，废弃
     * 改用上面函数push的supervisors维护进程
     * Undocumented function
     *
     * @param [type] $index
     * @return void
     */
    public function synRuiYun($index){
        try {
            $redis_key = $this->__ruiYunList($index);
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $exist_key  = AppCRedisKeys::ProcessExistKey("ruiyun");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true){
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'link_run_ruiyun');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'link_run_ruiyun');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);

                $data    = $ret[1] ?? "";
                if(!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data,true);
                if(empty($aData)) {
                    continue;
                }

                //todo 三次重试
                $msg = '';
                for ($i = 1; $i <= 3; $i++) {
                    list($status,$msg) = $this->run($aData['function'],$aData['args'],$aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i*2);
                }

                if(!$status) {
                    //重试也失败 加入到mysql记录
                    by::model('RuiYunLogModel', 'main')->saveLog($aData['function'],$aData['args'],$msg,$aData['time']);
                }
            }
        }catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'link_run_ruiyun_err');
            exit($error);
        }
    }

    public function run(string $function, array $args, int $time){
        switch ($function){
            case 'save':
            case 'save_detail':
                return $this->$function($args['order_no'],$args['user_id']);
            default:
                return [false,'function 不存在'];
        }
    }


    public function getRuiYunSign(array $query, string $signKey): string
    {
        ksort($query);
        $stringA = '';
        foreach ($query as $item){
            $stringA .= is_array($item) ? json_encode($item) : $item;
        }
        return strtoupper(md5($stringA . $signKey));
    }

    private function generateQueryParams(array $data): array
    {
        $tenant    = $data['tenant'] ?? 0;
        $config    = self::getConfigByTenant($tenant);
        $uuid      = Uuid::uuid();
        $timeStamp = time() . '000';

        $result = [
                '$tenant'    => $config['tenant_name'] ?? "",
                '$reqid'     => $uuid,
                '$appid'     => $config['app_id'] ?? "",
                '$timestamp' => $timeStamp,
        ];

        if (!empty($data['$filter'])) {
            $result['$filter'] = $data['$filter'];
        }

        if (!empty($data['$select'])) {
            $result['$select'] = $data['$select'];
        }

        $result['$sign'] = $this->getRuiYunSign($result, $config['sign_key'] ?? '');

        return $result;
    }

    

    private function getOrderData($order_no)
    {
        if(empty($order_no)){
            return [false, 'order_no为空'];
        }
        $orderData = by::plumbingOrder()->getInfoByOrderNo($order_no,true,false,false);
        if(empty($orderData)){
            CUtil::debug(" 工单信息不存在 | ret:{$order_no}" , "ruiyun.save");
            return [false, '数据不存在'.$order_no];
        }

        return [true, $orderData];
    }

    public function save($order_no,$user_id)
    {
        list($status, $orderData) = $this->getOrderData($order_no);
        if (!$status) {
            return [true, $orderData];
        }

        $expect_time = $orderData['expect_time'] ?? '';
        $ruiyunid = $orderData['ruiyun_id'] ?? '';
        $snAlias = $orderData['sn_alias'] ?? '';
        $tenant = $orderData['tenant'] ?? '000000';


        $type = $this->getServeType($orderData['type'], $snAlias);
        $new_source = self::RUIYUN_SOURCE[$tenant] ?? '';


        $data = [
                'new_ordernumber'     => $order_no,
                'new_contact'         => $orderData['name'] ?? '',
                'new_newcustomers'    => $orderData['name'] ?? '',
                'new_feedbacktel'     => $orderData['phone'] ?? '',
                'new_province_id'     => ['logicalname' => 'new_province', 'id' => $orderData['pid'] ?? ''],
                'new_city_id'         => ['logicalname' => 'new_city', 'id' => $orderData['cid'] ?? ''],
                'new_county_id'       => ['logicalname' => 'new_county', 'id' => $orderData['aid'] ?? ''],
                'new_address'         => $orderData['detail'] ?? '',
                'new_type'            => $type,
                'new_accepttime'      => date('Y-m-d H:i:s'),
                'new_appointmenttime' => empty($expect_time) ? '' : date('Y-m-d H:i:s', $expect_time),
                'new_memo'            => self::RUIYUN_TYPE_NAMES[$type],
                'dreame_channel'      => 11, //瑞云服务单加一个渠道
        ];
        if($ruiyunid) $data['$id'] = $ruiyunid;
        if($new_source) $data['new_source'] = $new_source;

        $query = $this->generateQueryParams($orderData);

        list($s, $ret) = $this->_request('SAVE', CUtil::encodeUrlQuery($query),$data);
        if (!$s) {
            return [false, $ret];
        }
        if (isset($ret['Data']) && $ret['Data']) {
            by::plumbingOrder()->savePlumbingOrder($order_no, ['ruiyun_id' => $ret['Data']]);
        }
        self::factory()->push($user_id,'save_detail',['order_no'=>$order_no,'user_id'=>$user_id]);

        return [true, $ret];
    }

    public function save_detail($order_no,$user_id)
    {
        list($status, $orderData) = $this->getOrderData($order_no);
        if (!$status) {
            return [true, $orderData];
        }

        $ruiyunid = $orderData['ruiyun_id'] ?? '';
        if(empty($ruiyunid)){
            CUtil::debug(" 瑞云数据还未创建 | ret:{$order_no}" , "ruiyun.save");
            return [false,'瑞云数据还未创建！|'.$order_no];
        }

        $ruiyundetailid = $orderData['ruiyun_detail_id'] ?? '';
        $snAlias        = $orderData['sn_alias'] ?? '';
        $sn             = $orderData['sn'] ?? '';
        $sku            = $orderData['sku'] ?? '';

        $data = [
            'new_product_id'         => ['logicalname' => 'product', 'keyattributes' => ['productnumber' => $sku]],
            'new_userprofile_number' => $sn,
            'new_workorder_id'       => ['logicalname' => 'new_srv_workorder', 'id' => $ruiyunid]
        ];

        if($ruiyundetailid) $data['$id'] = $ruiyundetailid;

        $query = $this->generateQueryParams($orderData);

        list($s, $ret) = $this->_request('SAVE_DETAIL', CUtil::encodeUrlQuery($query),$data);
        if (!$s) {
            return [false, $ret];
        }
        if (isset($ret['Data']) && $ret['Data']) {
            by::plumbingOrder()->savePlumbingOrder($order_no, ['ruiyun_detail_id' => $ret['Data']]);
        }

        return [true, $ret];
    }


    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @return array
     * 请求
     */
    protected function _request($event, $urlExtra='', array $body = [], string $method = 'POST'): array
    {
        $url = self::URL[$event];
        if (empty($url)) {
            return [false, 'event不存在'];
        }

        $url = self::DOMAIN.$url;

        if($urlExtra) $url .= '?'.$urlExtra;

        $header = [
            'Accept: application/json',
            'Content-Type: application/json; charset=utf-8',
            'User-Agent:*/*',
            'Expect: '
        ];

        list($status,$httpCode,$ret,$err) = CommCurl::factory()->Send($url, json_encode($body,JSON_UNESCAPED_UNICODE), $header, $method,10,['https'=> YII_ENV_PROD]);

        // CUtil::debug("event:{$event}|url:{$url}|httpcode:{$httpCode}|err:{$err}|data：".json_encode($body,320)." | ret:" . json_encode($ret,320), "ruiyun.{$event}");
        CUtil::setLogMsg(
            "ruiyun.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );

        if (!$status) {
            return [false, $err];
        }

        return [$ret['ErrorCode'] === 0, $ret];
    }

    /**
     * 获取服务类型
     * @param int $type
     * @param string $name
     * @return int
     */
    private function getServeType(int $type, string $name): int
    {
        // 上门指导/服务
        if ($type == 3) {
            return self::RUIYUN_TYPES['GUIDE'];
        }

        // 上门勘测、安装
        if ($type == 2) {
            return in_array($name, self::RUIYUN_NEW_TYPE_6) ? self::RUIYUN_TYPES['SURVEY'] : self::RUIYUN_TYPES['INSTALL'];
        }

        // 未知
        return self::RUIYUN_TYPES['UNKNOWN'];
    }

    public function getAddress($event = '', $params = []): array
    {
        if (empty($event)) {
            return [false, 'event不能为空'];
        }

        if (!in_array($event, self::ADDRESS_EVENT)) {
            return [false, 'event不存在'];
        }

        // 定义事件映射规则
        $eventMap = [
                'PROVINCE' => [
                        'required' => [],
                        'filter'   => function ($p) {
                            return 'statecode eq 0';
                        },
                ],
                'CITY'     => [
                        'required' => ['province_id'],
                        'filter'   => function ($p) {
                            return 'statecode eq 0 and new_province_id/id eq ' . $p['province_id'];
                        },
                ],
                'COUNTY'   => [
                        'required' => ['city_id'],
                        'filter'   => function ($p) {
                            return 'statecode eq 0 and new_city_id/id eq ' . $p['city_id'];
                        },
                ],
        ];

        $conf = $eventMap[$event];

        // 校验必填参数
        foreach ($conf['required'] as $field) {
            if (empty($params[$field])) {
                return [false, "$field 不能为空"];
            }
        }

        // 组装请求参数
        $where = [
                '$filter' => $conf['filter']($params),
                '$select' => 'new_name',
                'tenant'  => '000000',
        ];

        $query = $this->generateQueryParams($where);
        // 使用 http_build_query() 将数组转为 URL 编码格式的查询字符串
        $query = http_build_query($query, '', '&', PHP_QUERY_RFC3986);

        list($status, $ret) = $this->_request($event, $query, [], 'GET');
        if (!$status) {
            return [false, '获取失败！'];
        }
        return [true, $ret['Data']['Entities'] ?? []];
    }


}
