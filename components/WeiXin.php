<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;

use app\models\by;
use app\models\CUtil;
use app\models\WXBizDataCrypt;

class WeiXin extends Tencent
{

	CONST BIZ_CODE = [
		'OK'                    => 0,
		'IllegalAesKey'         => -41001,
		'IllegalIv'             => -41002,
		'IllegalBuffer'         => -41003,
		'DecodeBase64Error'     => -41004,
	];


    public static $me = array();

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}

    /**
     * @return WeiXin
     * */
    public static function factory()
    {

        if (!isset(self::$_instance['wx']) || !is_object(self::$_instance['wx'])) {
            self::$_instance ['wx'] = new self();
        }

        return self::$_instance ['wx'];
    }


    /**
     * @param $a_id //活动用不同的appid
     * @return bool|mixed|string
     * 获取全局jsapi_ticket
     * https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62
     */
    public function getUniqueJsapiTicket($a_id = 0)
    {
        $key = AppCRedisKeys::WXUniqueJsapiTicket($a_id);
        $redis = by::redis('core');
        $ticket = $redis->get($key);

        if (empty($ticket)) {

            list($status, $token)  = $this->getUniqueAccessToken($a_id);

            $url = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={$token}&type=jsapi";

            $aJson  = CUtil::curl_get($url);
            $ret    = (array)json_decode($aJson, true);

            $ticket = $ret['ticket'] ?? "";
            if (empty($ticket)) {
                trigger_error("获取ticket 失败:{$url}|{$aJson}", E_USER_ERROR);
            }

            $expire = isset($ret['expires_in']) ? intval($ret['expires_in']) : 7200;

            $redis->SETEX($key, $expire, $ticket);
        }

        return $ticket;
    }

    /**
     * @param string $code
     * @return mixed
     * 获取Access Token
     */
    public function getAccessInfo($code = '')
    {
        if (!$code) {
            return [];
        }
        $key = AppCRedisKeys::weiXinAccessToken($code);
        $redis = by::redis('core');
        $ret = $redis->get($key);
        $ret = json_decode($ret, true);
        if (empty($ret)) {
            $weixin_config = CUtil::getConfig('weixin', 'common', 'main');
            $appId = isset($weixin_config['appId']) ? $weixin_config['appId'] : "";
            $appSecret = isset($weixin_config['appSecret']) ? $weixin_config['appSecret'] : "";
            $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appId}&secret={$appSecret}&code={$code}&grant_type=authorization_code";

            $retJson = CUtil::curl_get($url);
            $ret = json_decode($retJson, true);

            if (empty($ret['access_token']) || empty($ret['openid'])) {
                trigger_error("获取Token 失败:{$url}|{$retJson}", E_USER_ERROR);
            }

            $expire = isset($ret['expires_in']) ? intval($ret['expires_in']) : 7200;
            $redis->SETEX($key, $expire, json_encode($ret));
        }

        return $ret;
    }

    /**
     * @param $access_token
     * @param $openid
     * 获取微信用户信息--用来验证合法性
     */
    public function getWeiXinUserInfo($access_token, $openid)
    {
        //$get_user_info = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$access_token}&openid={$openid}&lang=zh_CN";

        $get_user_info = "https://api.weixin.qq.com/sns/userinfo?access_token={$access_token}&openid={$openid}";
        $info = CUtil::curl_get($get_user_info);
        $arr = json_decode($info, true);
        self::$me = $arr;
    }

    /**
     * @param $js_code
     * @param array $weixin_config
     * @return array
     * 根据code获取数据
     * https://developers.weixin.qq.com/minigame/dev/document/open-api/login/code2accessToken.html
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
     */
    public function code2accessToken($js_code, $weixin_config = [])
    {
        !YII_ENV_PROD && CUtil::debug($js_code, 'code2.access.token.record');
        $weixin_config = empty($weixin_config) ? CUtil::getConfig('weixin', 'common', 'main') : $weixin_config;
        $appId         = isset($weixin_config['appId']) ? $weixin_config['appId'] : "";
        $appSecret     = isset($weixin_config['appSecret']) ? $weixin_config['appSecret'] : "";
        $url           = "https://api.weixin.qq.com/sns/jscode2session?appid={$appId}&secret={$appSecret}&js_code={$js_code}&grant_type=authorization_code";

        $ret = CUtil::curl_get($url);
        $ret = json_decode($ret, true);

        CUtil::debug($js_code . '|' . json_encode($ret), 'code2.access.token');
        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     :
                    $return = [true, $ret];
                    break;
                case -1    :
                    $return = [false, "系统繁忙，请稍候再试 (errcode:{$ret['errcode']})"];
                    break;
                case 40029 :
                    $return = [false, "code 无效 (errcode:{$ret['errcode']})"];
                    break;
                default :
                    $return = [false, "未知错误 (errcode:{$ret['errcode']})"];
                    break;
            }

            if ($ret['errcode'] != 0) {
                CUtil::debug($js_code . '|' . json_encode($ret), 'err.code2.access.token');
            }
        } else {
            $return = [true, $ret];
        }

        return $return;
    }

    /**
     * @param string $path
     * @param int $width
     * @return array
     * 获取小程序二维码，适用于需要的码数量较少的业务场景。
     * 通过该接口生成的小程序码，永久有效，有数量限制！！！！
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/qr-code/wxacode.createQRCode.html
     */
    public function createWxAQrCode($path = "", $width = 280)
    {
        //return [false,"有数量限制"];

        list($status, $token)  = $this->getUniqueAccessToken();

        $url = "https://api.weixin.qq.com/cgi-bin/wxaapp/createwxaqrcode?access_token={$token}";

        $body = [
            'access_token' => $token,
            'path' => $path,
            'width' => $width,
        ];

        $buffer = CUtil::curl_post($url, json_encode($body), null, 10);
        if (is_string($buffer)) {
            $ret = (array)json_decode($buffer, true);
            $errcode = $ret['errcode'] ?? "";
            $errmsg = $ret['errmsg'] ?? "";
            if (!empty($errmsg)) {
                return [false, "{$errmsg}({$errcode})"];
            }
        }

        return [true, base64_encode($buffer)];
    }

    /**
     * @param string $page
     * @param $scene
     * @param bool $is_hyaline
     * @param int $width
     * @return array
     * 获取小程序码，适用于需要的码数量极多的业务场景。
     * 通过该接口生成的小程序码，永久有效，数量暂无限制
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/qr-code/wxacode.getUnlimited.html
     */
    public function getWxACodeUnLimit($page, $scene, $is_hyaline=false,$width = 280)
    {
        list($status, $token)  = $this->getUniqueAccessToken();
        if(!$status) {
            return [false,$token];
        }

        $url    = "https://api.weixin.qq.com/wxa/getwxacodeunlimit?access_token={$token}";

        $header = ["Content-Type：application/json"];

        $env_version = YII_ENV_PROD ? 'release' : 'release';

        $body   = [
            'scene'         => $scene,
            'page'          => $page,
            'width'         => CUtil::uint($width),
            'env_version'   => $env_version,
            'check_path'    => false, //hack-小程序发布后要删除
            'is_hyaline'    => $is_hyaline,
        ];

        $buffer = CUtil::curl_post($url, json_encode($body), $header, 10,true);
        $ret = json_decode($buffer, true);
        if (isset($ret['errcode'])) {
            $ret     = json_decode($buffer, true) ?: [];
            $errcode = $ret['errcode'] ?? '';
            $errmsg  = $ret['errmsg'] ?? '';

            // 记录调试信息
            CUtil::debug('body:' . json_encode($body) . '|buffer:' . $buffer . '|errmsg:' . "{$errmsg}({$errcode})", 'err.qrcode1');

            // 如果存在错误消息，返回错误信息
            if (!empty($errmsg)) {
                return [false, "{$errmsg}({$errcode})"];
            }
        }

        return [true, $buffer];
//        return [true, base64_encode($buffer)];
    }

    /**
     * @param $openid
     * @param $data
     * @param $tpl_id
     * @param string $page
     * @param string $mini
     * @return array
     * 推送消息给用户
     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/subscribe-message.html
     */
    public function sendMessage($openid,$data,$tpl_id,$page = '',$mini = ''): array
    {
        $mini                = empty($mini) ? (!YII_ENV_PROD ? 'developer' : $mini) : $mini;
        list($status, $token)  = $this->getUniqueAccessToken(self::UQ_TOKEN['MINI']);
        if(empty($token)) {
            return [false,'没获取到token'];
        }

        $url      = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token={$token}";
        $postData = [
            'access_token'      => $token,
            'touser'            => $openid,
            'template_id'       => $tpl_id,
            'page'              => $page,
            'data'              => $data,
            'miniprogram_state' => $mini,
        ];

        $ret = CUtil::curl_post($url, json_encode($postData), null, 1);
        $ret = (array)json_decode($ret, true);

        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     :
                    $return = [true, $ret];
                    break;
                case -1    :
                    $return = [false, "系统繁忙，请稍候再试 (errcode:{$ret['errcode']})"];
                    break;
                case 40029 :
                    $return = [false, "code 无效 (errcode:{$ret['errcode']})"];
                    break;
                default    :
                    $return = [false, "未知错误 (errcode:{$ret['errcode']})"];
                    break;
            }
        } else {
            $return = [true, $ret];
        }

        return $return;
    }


    /**
     * TODO 微信数据解密
     */
    public function decryptData($encrypted_data, $iv, $session_key)
    {
        $weixin_config = CUtil::getConfig('weixin', 'common', 'main');
        $appid = isset($weixin_config['appId']) ? $weixin_config['appId'] : "";

        $pc = new WXBizDataCrypt($appid, $session_key);
        $errCode = $pc->decryptData($encrypted_data, $iv, $data);

        if ($errCode == 0) {
            return [true, json_decode($data, true)['phoneNumber'] ?? null];
        }

        CUtil::debug('手机号解密失败'.$errCode, 'clarence.decrypt.phone');
        return [false, $errCode];
    }

    /**
     * @param $js_code
     * @return array
     * 根据code获取数据
     * https://developers.weixin.qq.com/minigame/dev/document/open-api/login/code2accessToken.html
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
     */
    public function code2accessTokenSns($js_code, $weixin_config = []) {
        $weixin_config = empty($weixin_config) ? CUtil::getConfig('weixin', 'common', 'main') : $weixin_config;
        $appId     = isset($weixin_config['appId'])     ? $weixin_config['appId'] : "";
        $appSecret = isset($weixin_config['appSecret']) ? $weixin_config['appSecret'] : "";
        $url       = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appId}&secret={$appSecret}&code={$js_code}&grant_type=authorization_code";

        //$ret = '{ "session_key":"w/5Brgmf0fRWnvH9GoIHBg==","openid":"o7a5J5DmQX7RpEduCfXL6HZtBDRQ"}';
        $ret = CUtil::curl_get($url);
        $ret = json_decode($ret,true);

        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     : $return = [true, $ret]; break;
                case -1    : $return = [false,"系统繁忙，请稍候再试 (errcode:{$ret['errcode']})"]; break;
                case 40029 : $return = [false,"code 无效 (errcode:{$ret['errcode']})"];break;
                default    : $return = [false,"未知错误 (errcode:{$ret['errcode']})"];break;
            }
        } else {
            $return = [true, $ret];
        }

        return $return;
    }

    /**
     * @param $access_token
     * @return array
     * 获取标签列表
     */
    public function getTags($a_id)
    {
        list($status, $token)  = $this->getUniqueAccessToken($a_id);
        $url = "https://api.weixin.qq.com/cgi-bin/tags/get?access_token={$token}";

        $info = CUtil::curl_get($url);
        $ret = json_decode($info, true);

        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     :
                    $return = [true, $ret];
                    break;
                default :
                    $return = [false, var_export($ret, true)];
                    break;
            }
        } else {
            $return = [true, $ret];
        }

        return $return;
    }

    /**
     * @param $access_token
     * @param $openid
     * @return array
     * 获取微信用户信息
     */
    public function getWeiXinCgiUserInfo($access_token, $openid)
    {
        $get_user_info = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$access_token}&openid={$openid}&lang=zh_CN";

        $info = CUtil::curl_get($get_user_info);
        $arr = json_decode($info, true);

        return $arr;
    }

    /**
     * @param $access_token
     * @param $openid
     * @return array
     * 获取用户标签列表
     */
    public function getTagsByUid($openid, $a_id)
    {
        list($status, $token)  = $this->getUniqueAccessToken($a_id);
        $url = "https://api.weixin.qq.com/cgi-bin/tags/getidlist?access_token={$token}";

        $post = [
            'openid' => $openid
        ];
        $info = CUtil::curl_post($url, json_encode($post));
        $ret = json_decode($info, true);

        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     :
                    $return = [1, $ret];
                    break;
                default :
                    $return = [$ret['errcode'], var_export($ret, true)];
                    break;
            }
        } else {
            $return = [1, $ret];
        }

        return $return;

    }

    /**
     * 生成JS-SDK权限验证的签名
     * */
    public function createJsSdkSign($a_id = 0, $url = '')
    {
        $jsapi_ticket   = $this->getUniqueJsapiTicket($a_id);

        $noncestr = CUtil::createVerifyCode(16, 1);
        $now_time = intval(START_TIME);

        $arr = [
            'noncestr'      => $noncestr,
            'jsapi_ticket'  => $jsapi_ticket,
            'timestamp'     => $now_time,
            'url'           => $url,
        ];

        //对关联数组按照键名进行升序排序：
        ksort($arr,SORT_STRING); //SORT_STRING - 把每一项作为字符串来处理。
        $target_Arr = [];
        foreach ($arr as $key => $a) {
            if(!is_array($a)) {
                $target_Arr[] = "{$key}={$a}";
            }
        }

        $target_str = implode('&',$target_Arr);
        $sign = sha1($target_str);

        $data = [
            'noncestr'      => $noncestr,
            'timestamp'     => $now_time,
            'signature'     => $sign,
        ];

        return $data;
    }

    /**
     * @param $unionid
     * @param int $a_id
     * @return array
     * 创建被分享动态消息或私密消息的 activity_id
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/updatable-message/updatableMessage.createActivityId.html
     *
     * 小程序私密消息
     * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/share/private-message.html
     *
     */
    public function createActivityId($unionid,$a_id=self::UQ_TOKEN['MINI']) {

        list($status, $token)  = $this->getUniqueAccessToken($a_id);
        if(empty($token)) {
            return [false,'获取小程序通信凭证失败'];
        }

        $url     = "https://api.weixin.qq.com/cgi-bin/message/wxopen/activityid/create?access_token={$token}&unionid={$unionid}";
        $aJson   = CUtil::curl_get($url);
        $aData   = (array)json_decode($aJson,true);

        $errcode = $aData['errcode'] ?? -1;
        $errmsg  = $aData['errmsg']  ?? "ERR";
        if($errcode != 0) {
            return [false,"{$errmsg}({$errcode})"];
        }

        return [true,$aData];
    }



	/**
	 * @param $appid
	 * @param $sessionKey
	 * 检验数据的真实性，并且获取解密后的明文.
	 * @param $encryptedData string 加密的用户数据
	 * @param $iv string 与用户数据一同返回的初始向量
	 * @param $data string 解密后的原文
	 * @return int 成功0，失败返回对应的错误码
	 */
	private function __BizDataCrypt($appid,$sessionKey,$encryptedData,$iv,&$data)
	{
		if (strlen($sessionKey) != 24) {
			return self::BIZ_CODE['IllegalAesKey'];
		}

		if (strlen($iv) != 24) {
			return self::BIZ_CODE['IllegalIv'];
		}

		$aesKey     = base64_decode($sessionKey);
		$aesIV      = base64_decode($iv);
		$aesCipher  = base64_decode($encryptedData);
		$result     = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);

		$dataObj    = json_decode( $result );
		if( $dataObj  == NULL ) {
			return self::BIZ_CODE['IllegalBuffer'];
		}

		if( $dataObj->watermark->appid != $appid ){
			return self::BIZ_CODE['IllegalBuffer'];
		}

		$data       = $result;

		return self::BIZ_CODE['OK'];
	}

	/**
	 * @param $encrypted_data
	 * @param $iv
	 * @param $session_key
	 * @return array
	 * 微信数据解密
	 * https://developers.weixin.qq.com/miniprogram/dev/framework/open-ability/getPhoneNumber.html
	 */
	private function __decryptData($encrypted_data, $iv, $session_key): array
	{
		$config  = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
		$appid   = $config['appId'] ?? "";
		$errCode = $this->__BizDataCrypt($appid, $session_key,$encrypted_data, $iv, $data);

		if ($errCode == 0) {
			$arr         = (array)json_decode($data,true);
			if(empty($arr)) {
				return [false,"解码失败({$errCode})"];
			}
			return [true, $arr];
		}

		return [false, "解码失败({$errCode})"];
	}

	/**
	 * @param $aCode
	 * @param $iv
	 * @param $session_key
	 * @return array
	 * 解密手机号
	 */
	public function GetPhone($aCode,$iv,$session_key): array
	{
		list($status,$ret) = $this->__decryptData($aCode,$iv,$session_key);
		if(!$status){
			return [$status,$ret];
		}

		$phoneNumber = $ret['phoneNumber'] ?? "";

		if(empty($phoneNumber)) {
			return [false,"手机号获取失败"];
		}

		return [true,$phoneNumber];
	}

    /**
     * @param string $path
     * @param string $query
     * @return array
     * 获取小程序 URL Link
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/url-link/urllink.generate.html
     */
	public function UrlLink($path = '', $query = '')
    {
        if (mb_strlen($query) > 1024) {
            return [false, 'query超过1024字符'];
        }

        list($status, $token)  = $this->getUniqueAccessToken();
        if(empty($token)) {
            return [false,'获取小程序通信凭证失败'];
        }

        $url    = "https://api.weixin.qq.com/wxa/generate_urllink?access_token={$token}";

        $data   = [
            'path'              => $path,
            'query'             => $query,
            'env_version'       => YII_ENV_PROD ? 'release' : 'release',
            'is_expire'         => true,
            'expire_type'       => 1,
            'expire_interval'   => 30,
        ];
        // CUtil::debug(json_encode($data), 'info.wx.ulink');

        $ret = CUtil::curl_post($url, json_encode($data), null);

        $res = (array)json_decode($ret, true);

        if (empty($res) || empty($res['url_link'])) {
            $msg    = ($res['errmsg'] ?? '请求失败') . '('. ($res['errcode'] ?? -1) .')';
//            CUtil::debug($ret, 'err.wx.ulink');
            return [false, $msg];
        }

        return [true, $res['url_link']];

    }

    /**
     * 获取微信授权信息
     *
     * @param array $postData 请求数据
     * @return array [状态(bool), 数据或错误消息]
     */
    public function getWxAuthByOpenid(array $postData = []): array
    {
        $js_code = $postData['openudid'] ?? '';

        // 如果没有 js_code，则直接返回
        if (empty($js_code)) {
            return [true, [
                    'openudid' => '',
                    'unionid'  => ''
            ]];
        }

        // 开发环境下模拟数据
        if (!YII_ENV_PROD && !empty($postData['provider']) && strpos($postData['provider'], 'test') !== false) {
            return [true, [
                    'openudid' => $js_code,
                    'unionid'  => $js_code
            ]];
        }

        // 获取微信 openid 和 unionid
        list($status, $ret) = $this->code2accessToken($js_code);
        if (!$status || empty($ret['session_key']) || empty($ret['openid'])) {
            return [false, '微信授权认证失败!'];
        }
//        $ret['openid']='oCRWu5E0z95I9AK6k9QtXJeGsYwU';
//        $ret['unionid']='oGGvJ54o_LzDrBwKkc7mv7d0E4sQ';

        return [true, [
                'openudid' => $ret['openid'],
                'unionid'  => $ret['unionid'] ?? ''
        ]];
    }

}
