<?php

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/6/11
 * Time: 16:41
 * http://openapi.baison.net/index.html#/doc
 */

namespace app\components;

use app\models\by;
use app\models\CUtil;

class Erp
{
    const URL           = YII_ENV_PROD ? "http://39.103.170.7/e3/webopm/web/?app_act=api/ec&app_mode=func" : "http://39.103.170.7/e3_test/webopm/web/?app_act=api/ec&app_mode=func";
    const KEY           = YII_ENV_PROD ? "wxsc" : "test";
    const SECRET        = YII_ENV_PROD ? "rVNk8xwsufWUXwhzUQmbgGv4S7VUF4" : "1a2b3c4d5e6f7g8h9i10j11k12l";
    const VERSION       = "3.0";
    const SHOP_CODE     = YII_ENV_PROD ? "300000058" : "300103";    //店铺
    const CK_CODE       = YII_ENV_PROD ? ['DS08', 'DS09'] : ['11003013', '16006000','11003007'];      //仓库

    protected static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return Erp
     * */
    public static function factory()
    {
        if (!isset(self::$_instance['erp']) || !is_object(self::$_instance['erp'])) {
            self::$_instance['erp'] = new self();
        }

        return self::$_instance['erp'];
    }


    /**
     * @param $sku
     * @param $ck_code
     * @return array
     * 获得仓库库存列表
     */
    public function getStock($sku,$ck_code)
    {
        $service_type = 'stock.list.get';

        $data   = [
            'sku'           => $sku,
            'ckCode'        => $ck_code,
        ];

        return $this->_request($service_type, $data);
    }

    /**
     * @param $sku
     * @param string $ck_code
     * @param bool $g_add
     * @return array
     * @throws \yii\db\Exception
     * 获取库存兼容商品sku、套餐sn
     */
    public function getStockNew($sku,$ck_code,$g_add=false)
    {
        list($s, $ret)  = $this->getStock($sku, $ck_code);
        if (!$s) {
            return [false, '查询失败'];
        }

        $stock   = $ret['data']['stockListGet'][0]['kysl'] ?? -1;

        if ($stock > -1) {
            return [true, $stock];
        }

        //todo 判断是否为套餐
        if ($g_add) {
            list($s, $m)    = by::GtcModel()->checkIsTc($sku);
            if (!$s) {
                return [false, $m];
            }
        }

        $tc_data        = by::GtcModel()->getListBySn($sku);

        if (empty($tc_data)) {
            return [true, 0];
        }

        // todo 是套餐则需要通过套餐下的所有商品查询比对出最小库存
        $sku_str        = implode(',', array_column($tc_data, 'sku'));

        list($s, $temp) = $this->getStock($sku_str, $ck_code);
        if (!$s) {
            return [false, '查询失败'];
        }

        //套餐sku正确,但是没库存 hack 等产品确认
        if (empty($temp['data']['stockListGet'])) {
            if ($g_add) {
                return [false, '查询失败(1)'];
            }

            return [true , 0];
        }

        $stocks     = [];
        $tc_list    = array_column($tc_data, 'sl', 'sku');

        foreach ($temp['data']['stockListGet'] as $stock) {
            $stocks[] = bcdiv($stock['kysl'], $tc_list[$stock['sku']]);
        }

        $stock = min($stocks);
        return [true, $stock];

    }

    /**
     * @param array $order_info
     * @return array
     * @throws \yii\db\Exception
     * 7.1 销售订单添加
     */
    public function addOrder(array $order_info)
    {
        $service_type = 'order.detail.add';

        //TODO 订单未支付时显示的实际金额要加上定金金额，其他状态外面已经加好
        if($order_info['status'] < by::Omain()::ORDER_STATUS['WAIT_SEND']){
            $order_info['real_price'] = bcadd($order_info['real_price']??0,  $order_info['deposit_price']??0, 2);
        }

        $oprice = bcadd($order_info['oprice'], $order_info['fprice'], 2);
        $data   = [
            'add_time'      => date('Y-m-d H:i:s', $order_info['ctime']),
            'order_sn'      => $order_info['order_no'],
            'sd_code'       => self::SHOP_CODE,
            'order_status'  => 1,
            "pay_status"    => 2,
            'consignee'     => $order_info['address']['nick'],
            'province_name' => $order_info['address']['province'],
            'city_name'     => $order_info['address']['city'],
            'district_name' => $order_info['address']['area'],
            'address'       => $order_info['address']['detail'],
            'mobile'        => $order_info['address']['phone'],
            'pos_code'      => '',
            'pay_code'      => 'weixin',
            'vip_no'        => '',
            'shipping_code' => '',
            'shipping_fee'  => $order_info['fprice'],
            'order_amount'  => $oprice,
            'payment'       => $order_info['real_price'] > 0 ? $order_info['real_price'] : $oprice,
            'postscript'    => $order_info['note'],
        ];

        $items  = [];
        foreach ($order_info['goods'] as $goods) {
            // 若传递的sku是套餐sn 则需要获取到套餐sku
            $price = bcadd($goods['price'],$goods['deposit_price']??0,2);

            $tc_data = by::GtcModel()->getListBySn($goods['sku']);
            if (empty($tc_data)) {
                $items[] = [
                    'sku_sn'            => $goods['sku'],
                    'goods_price'       => $goods['oprice']/$goods['num'],
                    'transaction_price' => $price/$goods['num'],
                    'goods_number'      => $goods['num'],
                ];
            } else {
                // 防止套餐下有多个sku 循环一次
                $tc_sku_list = array_unique(array_column($tc_data, 'tc_sku'));
                foreach ($tc_sku_list as $tc_sku) {
                    $items[] = [
                        'sku_sn'            => $tc_sku,
                        'goods_price'       => $goods['oprice']/$goods['num'],
                        'transaction_price' => $price/$goods['num'],
                        'goods_number'      => $goods['num'],
                    ];
                }
            }
        }

        $data['items']  = $items;

        //todo 优惠信息
        $promotions     = [];
        if ($order_info['cprice'] > 0) {
            $promotions[] = [
                'promotion_name'    => '优惠券',
                'discount_fee'      => $order_info['cprice'],
                'promotion_desc'    => '优惠券抵扣'
            ];
        }

        // todo 积分
        if ($order_info['coin_price'] > 0) {
            $promotions[] = [
                'promotion_name'    => '积分',
                'discount_fee'      => $order_info['coin_price'],
                'promotion_desc'    => '积分抵扣'
            ];
        }

        //todo 膨胀金额
        if ($order_info['exprice'] > 0) {
            $promotions[] = [
                'promotion_name' => '膨胀',
                'discount_fee'   => bcsub($order_info['exprice'], $order_info['deposit_price'] ?? 0, 2),
                'promotion_desc' => '膨胀抵扣'
            ];
        }

        $data['promotions'] = $promotions;

        return $this->_request($service_type, $data);
    }

    /**
     * @param $order_no
     * @return array
     * 7.18 获取销售订单详情
     */
    public function getOrderList($order_no)
    {
        $service_type = 'order.list.get';

        //todo 查一年内的数据，测试到没传时间可能查不到数据
        $end    = date('Y-m-d H:i:s');
        $start  = date('Y-m-d H:i:s', strtotime('-1 years'));

        $data   = [
            'startModified' => $start,
            'endModified'   => $end,
            'time_type'     => 2,
            'sd_code'       => self::SHOP_CODE,
            'deal_code'     => $order_no,
        ];

        list($s, $ret)  = $this->_request($service_type, $data);
        if (!$s) {
            return [false, $ret];
        }

        if (empty($ret['data']['orderListGets'])) {
            return [false, 'OMS无数据'];
        }

        $list   = [];
        foreach($ret['data']['orderListGets'] as $val) {
            //过滤已作废
            if ($val['order_status'] == 3 ) {
                continue;
            }
            unset($val['orderDetailGets']);

            $list       = $list ?: $val;

            $ftime1      = empty($val['shipping_time_fh']) ? 0 : strtotime($val['shipping_time_fh']);
            if ($ftime1 == 0) {
                continue;
            }

            //取最早发货的那一条
            $o_ftime    = $ftime ?? 0;
            $ftime      = $ftime1;
            if ( $o_ftime == 0 || $ftime < $o_ftime ) {
                $list = $val;
            }
        }

        return [true, $list];
    }

    /**
     * @param $r_info
     * @return array
     * @throws \yii\db\Exception
     * 7.9 销售退单添加
     */
    public function orderReturnAdd($r_info)
    {
        $service_type = 'order.return.add';

        $data   = [
            'sell_return_record' => [
                'order_sn'      => $r_info['order_no'],
                'return_type'   => 1,
                'shipping_name' => $r_info['express_name'], //退货物流公司
                'shipping_sn'   => $r_info['mail_no'], //退货物流单号
                'shipping_code' => $r_info['express_code'], //退货快递公司编码
            ]
        ];

        $items  = [];
        foreach ($r_info['goods'] as $goods) {
            // 若传递的sku是套餐sn 则需要获取到套餐sku
            $price = bcadd($goods['price'],$goods['deposit_price']??0,2);

            $tc_data = by::GtcModel()->getListBySn($goods['sku']);
            if (empty($tc_data)) {
                $items[] = [
                    'sku'             => $goods['sku'],
                    'goods_number'    => $goods['num'],
                    'shop_price'      => $price,
                    'goods_price'     => $price
                ];
            } else {
                // 防止套餐下有多个sku 循环一次
                $tc_sku_list = array_unique(array_column($tc_data, 'tc_sku'));
                foreach ($tc_sku_list as $tc_sku) {
                    $items[] = [
                        'sku'             => $tc_sku,
                        'goods_number'    => $goods['num'],
                        'shop_price'      => $price,
                        'goods_price'     => $price
                    ];
                }
            }
        }

        $data['sell_return_record']['order_return_goods'] = $items;

        !YII_ENV_PROD && CUtil::debug(var_export($data,1), 'refund.request');

        //        exit(var_export($data,1));

        return $this->_request($service_type, $data);
    }

    /**
     * @param $order_sn
     * @return array
     * 7.4 销售订单置无效
     */
    public function orderZwx($order_sn)
    {
        $service_type = 'order.zwx';

        $data   = [
            'deal_code' => $order_sn
        ];

        return $this->_request($service_type, $data);
    }

    /**
     * @param $tc_sn
     * @return array
     * 获取套餐列表
     */
    public function getTcList($tc_sn)
    {
        $service_type = 'taocan.list.get';

        $data   = [
            'pageSize'  => 50,
            'tc_sn'     => $tc_sn,
        ];

        return $this->_request($service_type, $data);
    }

    /**
     * @param $service_type
     * @param array $post
     * @return array
     * 统一请求
     */
    protected function _request($service_type, $post = [])
    {
        $request                = json_encode($post);
        $body['key']            = self::KEY;
        $body['requestTime']    = date('YmdHis', time());
        $body['version']        = self::VERSION;
        $body['sign']           = $this->_getSign($service_type, $request, $body);

        $body['serviceType']    = $service_type;
        $body['data']           = $request;

        // todo 本地使用 ************ **************
        $headers    = [];
        if (YII_ENV_DEV) {
            $headers[]          = 'X-FORWARDED-FOR:************';
            $headers[]          = 'CLIENT-IP:************';
        }

        $res = CUtil::curl_post(self::URL, $body, $headers, 10 ,true);

        $data   = (array)json_decode($res, true);

        if (empty($data['status']) || $data['status'] != 'api-success') {
            $json   = json_encode($data, JSON_UNESCAPED_UNICODE);
            // CUtil::debug("request:{$request}|requestTime:{$body['requestTime']}|return:{$json}", "eerp.{$service_type}");

            CUtil::setLogMsg(
                "eerp.{$service_type}",
                $body,
                $data,
                $headers,
                self::URL,
                '',
                [],
                200
            );
            $message  = $data['data'][0]['message'] ?? '';
            $order_sn = $post['order_sn']           ?? '';
            if ($service_type == "order.detail.add" && $message == "交易号{$order_sn}在系统中已存在") {
                return [true, 'ok'];
            }

//            //发送消息到飞书
//            CUtil::sendMsgToUdp([
//                "**项目 :** dreame\n",
//                "**摘要 :** erp请求失败\n",
//                "**SERVICE_TYPE :** {$service_type}\n",
//                "**详情 :** {$json}\n",
//                "**环境 :** " . (YII_ENV_PROD ? 'release' : 'test') . "\n",
//                "**时间 :** " . date("Y-m-d H:i:s") . "\n",
//            ], '', '', 'p_1644549157');

            return [false, 'erp请求失败'];
        }

        // CUtil::debug("request:{$request}|requestTime:{$body['requestTime']}|return:{$res}", "erp.{$service_type}");
        CUtil::setLogMsg(
            "erp.{$service_type}",
            $body,
            $data,
            $headers,
            self::URL,
            '',
            [],
            200
        );

        return [true, $data];
    }

    /**
     * @param string $service_type
     * @param string $request
     * @param array $data
     * @return string
     * 统一签名
     * key=test&requestTime=20110714101701&secret=1a2b3c4d5e6f7g8h9i10j11k1 2l&version=3.0&serviceType=goods.list.get&data={json}
     */
    protected function _getSign($service_type = '', $request = '', $data = [])
    {
        if (empty($data)) {
            return "";
        }

        $data['secret'] = self::SECRET;
        unset($data['sign']);

        //对关联数组按照键名进行升序排序：
        ksort($data, SORT_STRING); //SORT_STRING - 把每一项作为字符串来处理。
        $target_Arr = [];
        foreach ($data as $key => $a) {
            if (!is_array($a)) {
                $target_Arr[] = "{$key}={$a}";
            }
        }

        $target_Arr[] = "serviceType={$service_type}";
        $target_Arr[] = "data={$request}";

        $target_str = implode('&', $target_Arr);

        return md5($target_str);
    }
}
