<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\UserModel;
use yii\base\Model;

/**
 * 微信数据迁移
 */
class WxTransfer
{
    protected static $_instance = [];

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}

    const ERR_CODE = [
        'OK' => 1,     //成功
        'REPEAT' => 100,   //已被绑定
        'ERR' => -1,    //其他错误
    ];
    //转移转整型字段
    const INT_TRANSFER_FIELDS =[
      'guide_id','r_id'
    ];
    //忽略转移字段
    const IGNORE_TRANSFER_FIELDS = [
        'main_user_id','card'
    ];
    //用户详情忽略字段
    const IGNORE_USER_DETAIL_TRANSFER_FIELDS = [
        'is_new_gift','user_id',
        'reg_reward_point','reg_reward_count','reg_popup_count','coupon_reward_count',
        'coupon_popup_count','point_reward_count','point_popup_count'
    ];

    private $_obj = [];

    /**
     * @return WxTransfer
     * */
    public static function factory(): WxTransfer
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance [__CLASS__] = new static();
        }
        return self::$_instance [__CLASS__];
    }

    public function transferRegisterData($user_id,$phone)
    {
        if(empty(CUtil::checkUuid($user_id))||empty($phone)){
            return [false, '参数不正确'];
        }
        $transaction = by::dbMaster()->beginTransaction();
        try {
            //1.查询附表注册数据
            $userData = by::Rusers()->getUserMainInfo($user_id);
            $userDetail = by::Rusers()->getOneByUid($user_id);
            $newUserId = 0;
            if($userData){
                //2.判断电话号码是否已存在主表中
                $realUserData = by::users()->getWxUserByPhone($phone,false);
                if($realUserData){
                    //APP注册的可以更新，否则直接报错
                    $openudid = $realUserData['openudid'] ?? '';
                    if(empty($openudid) || strpos($openudid,'dreamehome')===false){
                        throw new MyExceptionModel('当前手机号已有绑定的微信账号,授权失败！');
                    }
                    $newUserId = $realUserData['user_id'];
                    // app先注册后，小程序再授权登录时不更新reg_time，只更新update_time
                    if (! empty($userData['reg_time'])) {
                        unset($userData['reg_time']);
                    }
                    $userData['update_time'] = time();
                    //更新用户主要信息
                    by::users()->updateMainInfo($newUserId, $userData,UserModel::SCENARIOS['MAIN']);
                    //更新用户详情信息 (忽略一些字段)
                    foreach (self::IGNORE_USER_DETAIL_TRANSFER_FIELDS as $value){
                        if(isset($userDetail[$value])) unset($userDetail[$value]);
                    }
                    CUtil::debug('wx_transfer_log:用户详情信息：'.json_encode($userDetail),'wx_transfer_log');
                    if($userDetail) by::users()->updateMembersInfo($newUserId, $userDetail);
                }else{
                    //创建主表信息
                    $userData = array_merge($userData,$userDetail);
                    unset($userData['user_id']);

                    //迁移游客数据不限制高并发
                    list($status, $user_info) = by::users()->register($userData,false);
                    if (!$status || empty($user_info)) {
                        throw new MyExceptionModel($user_info);
                    }
                    $newUserId = $user_info['user_id']??0;
                    //绑定电话号码
                    list($status, $msg) = by::Phone()->SaveRelation($newUserId, $phone);
                    if (!$status) {
                        throw new MyExceptionModel($msg.':'.$phone);
                    }
                }

                if(empty($newUserId)){
                    throw new MyExceptionModel('迁移用户信息失败,未获取到新用户ID');
                }

                //迁移相关信息
                list($a, $data) = $this->_updateWxData(by::RuserGuide(), by::userGuide(), $user_id, $newUserId);
                list($b, $data) = $this->_updateWxData(by::RuserExtend(), by::userExtend(), $user_id, $newUserId);
                list($c, $data) = $this->_updateWxData(by::Rguide(), by::guide(), $user_id, $newUserId);
                list($d, $data) = $this->_updateWxData(by::RuserRecommend(), by::userRecommend(), $user_id, $newUserId);
                if (!$a || !$b || !$c || !$d) {
                    throw new MyExceptionModel('迁移用户信息失败');
                }

                //转移绑定标识
                $transferData['main_user_id'] = $newUserId;
                by::RuserExtend()->saveUserExtend($user_id,$transferData);

                $transaction->commit();
                //清除redis
                $this->_removeOldRedisData($user_id);
                by::users()->deleteRedisCache($newUserId, $phone);
                by::userExtend()->deleteRedisCache($newUserId);
                by::Phone()->deleteRedisCache($newUserId, $phone);

            }

            return [true, $newUserId, self::ERR_CODE['OK']];
        } catch (\exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'wx_self_transfer');
            $transaction->rollBack();
            return [false, $e->getMessage()];
        }

    }


    /**
     * @throws \yii\db\Exception
     */
    protected function _removeOldRedisData($user_id)
    {
        by::Rguide()->__delCache($user_id);
        by::RuserExtend()->deleteRedisCache($user_id);
        by::RuserGuide()->__delCache($user_id);
        by::RuserRecommend()->__delInfoCache($user_id);
        by::Rusers()->deleteRedisCache($user_id,true);
    }


    /**
     * @param $rmodel
     * @param $model
     * @param $userId
     * @param $newUserId
     * @return array
     * @throws \yii\db\Exception
     */
    protected function _updateWxData($rmodel,$model,$userId,$newUserId): array
    {
        $originDataDb = $rmodel::tbName();
        $storeDataDb = $model::tbName();
        $sql = "SELECT * FROM {$originDataDb} WHERE `user_id`=:user_id LIMIT 1";
        $aData      = by::dbMaster()->createCommand($sql, [':user_id' => $userId])->queryOne();
        //忽略同步字段
        foreach (self::IGNORE_TRANSFER_FIELDS as $value) {
            if (isset($aData[$value])) unset($aData[$value]);
        }

        if($aData){
            $sql1 = "SELECT `user_id` FROM {$storeDataDb} WHERE `user_id`=:user_id LIMIT 1";
            $bData = by::dbMaster()->createCommand($sql1, [':user_id' => $newUserId])->queryOne();
            $aData['user_id'] = $newUserId;
            $aData['ctime'] = time(); //游客成为会员时ctime改为当前授权时间而非创建游客时间
            unset($aData['id']);
            foreach (self::INT_TRANSFER_FIELDS as $v){
                if(isset($aData[$v])){
                    $aData[$v] = CUtil::uint($aData[$v]);
                }
            }

            if($bData){
                // *************如果user_extend主表中有数据了代表用户已经授权登陆过，不修改任何数据***************
                return [true, '用户已经授权登录过，无需更新'];
                //更新
                $connection     = by::dbMaster();
                $sql = " UPDATE {$storeDataDb} SET ";
                $bindParam = [];
                foreach ($aData as $field => $val) {
                    $escape = ":{$field}";
                    $update_sql[] = "`{$field}`={$escape}";
                    $bindParam[$escape] = strip_tags($val);
                }
                if (empty($update_sql) || empty($bindParam)) {
                    return [true,'不需要更新'];
                }
                $sql .= implode(' , ', $update_sql);
                $sql .= " WHERE `user_id`=:user_id LIMIT 1 ";
                $command = $connection->createCommand($sql);

                //bindParam的第二个参数要求是引用变量！
                foreach ($bindParam as $special_param => &$special_bind) {
                    $command->bindParam($special_param, $special_bind);
                }
                $command->bindParam(":user_id", $newUserId);
                CUtil::debug('wx_transfer_update:'.$command->getRawSql());
                $res = $command->execute();
                if(!$res){
                    return [false,'更新失败！'];
                }
            }else{
                //插入
                $fields = array_keys($aData);
                $fields = implode("`,`",$fields);
                $rows   = implode("','",$aData);
                $dup    = [];
                foreach ($aData as $key => $value){
                    $dup[] = is_int($value)?"`{$key}` = {$value}": "`{$key}` = '{$value}'";
                }
                $dup    = implode(' , ',$dup);
                $sql    = "INSERT INTO {$storeDataDb} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup}";
                $res    = by::dbMaster()->createCommand($sql)->execute();
                if(!$res){
                    return [false,'创建失败！'];
                }
            }
        }
        return [true,'ok'];
    }

}
