<?php
namespace app\components;

use app\models\by;
use app\models\CUtil;
use RedisException;


class WeiXinPc extends Tencent
{

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}


    protected static $_instance = [];

    public static function factory(): WeiXinPc
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new static();
        }
        return self::$_instance[__CLASS__];
    }

    /**
     * @param string $code
     * @return array
     * @throws RedisException
     * 获取Access Token
     */
    public function getAccessInfo(string $code = ''): array
    {
        // 如果 code 为空，直接返回空数组
        if (empty($code)) {
            return [false, '缺少参数'];
        }

        // 构建 Redis 键
        $key   = AppCRedisKeys::weiXinPcAccessToken($code);
        $redis = by::redis('core');

        // 从 Redis 获取缓存的 access token 信息
        $cachedData = $redis->get($key);
        $accessInfo = json_decode($cachedData, true);

        // 如果 Redis 中没有缓存，则向微信服务器请求新的 access token
        if ($accessInfo) {
            // 返回缓存的 access token 信息
            return [true, $accessInfo];
        }

        // 获取微信配置参数
        $weixinConfig = CUtil::getConfig('pc-weixin', 'common', 'main');
        $appId        = $weixinConfig['login']['appId'] ?? '';
        $appSecret    = $weixinConfig['login']['appSecret'] ?? '';

        // 检查配置参数是否存在
        if (empty($appId) || empty($appSecret)) {
            CUtil::debug('微信配置参数缺失', 'wx_config_error');
            return [false, '微信配置参数缺失'];
        }

        // 构建微信 access token 请求 URL
        $url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={$appId}&secret={$appSecret}&code={$code}&grant_type=authorization_code";

        try {
            // 发送请求并解析响应
            $response = CUtil::curl_get($url);
            $data     = json_decode($response, true);

            // 检查响应中是否包含 access token 和 openid
            if (empty($data['access_token']) || empty($data['openid'])) {
                CUtil::debug("获取 Token 失败: {$url} | {$response}", 'wx_access_token_fetch_error');
                return [false, '获取 Token 失败'];
            }

            // 获取 token 过期时间，默认为 7200 秒
            $expire = $data['expires_in'] ?? 7200;

            // 将新的 access token 信息存储到 Redis 中
            $redis->SETEX($key, $expire, json_encode($data));
            return [true, $data];
        } catch (\Exception $e) {
            // 记录错误信息
            CUtil::debug(
                sprintf('错误信息: %s, 文件: %s, 行号: %d', $e->getMessage(), $e->getFile(), $e->getLine()),
                'err.wx_access_token_error'
            );
            return [false, '获取 Token 失败'];
        }
    }




    public function getUserInfo($accessToken, $openid): array
    {
        // 检查参数有效性
        if (empty($accessToken) || empty($openid)) {
            return [false, 'accessToken 和 openid 不能为空'];
        }

        // 构建请求 URL
        $urlUserinfo = sprintf(
            "https://api.weixin.qq.com/sns/userinfo?access_token=%s&openid=%s&lang=zh_CN",
            urlencode($accessToken),
            urlencode($openid)
        );

        // 发送请求并解析响应
        try {
            $retJson = CUtil::curl_get($urlUserinfo);
            $ret = json_decode($retJson, true);

            // 检查 JSON 解析是否成功
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('解析 JSON 响应失败: ' . json_last_error_msg());
            }

            // 检查是否成功获取用户信息
            if (isset($ret['errcode'])) {
                throw new \Exception('获取用户信息失败: ' . $ret['errmsg']);
            }

            return [true, $ret];
        } catch (\Exception $e) {
            // 处理请求或解析异常
            $error = sprintf(
                '错误信息: %s, 文件: %s, 行号: %d',
                $e->getMessage(),
                $e->getFile(),
                $e->getLine()
            );

            // 记录详细错误信息
            CUtil::debug($error, 'err.wx-user-info');

            return [false, '获取用户信息失败'];
        }
    }




}
