<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

/**
 * Roboter 公共调用方法
 */
class Roboter
{
    private static $_instance;
    const SEND_TYPE_URL = [
        'REISSUE_INTEGRAL' => 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=de74937b-e8f3-45a6-b1e1-0d13d04d492f'
    ];

    /**
     * @return Roboter
     */
    public static function factory(): Roboter
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    public function run($function = 'text', $body = [])
    {
        switch ($function) {
            case 'text':
                return $this->$function($body);
            default:
                return [false, 'function 不存在'];
        }
    }


    /**
     * @param $body
     * 文本方式发送
     */
    public function text($body)
    {
        $url = self::SEND_TYPE_URL['REISSUE_INTEGRAL'];
        $sendMessage = [
            'msgtype' => "text",
            'text' => [
                'content' => json_encode($body, 320) . '此条数据同步失败'
            ]
        ];
        CUtil::curl_post($url, $body, null, 10, true);
    }
}
