<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;

use app\jobs\SyncAdvJob;
use app\models\by;
use app\models\CUtil;

class AdvAscribe
{
    private static $_instance = [];


    const TX_ACTION_TYPE = [
        'REGISTER',//注册
        'CONSULT',//咨询
        'PURCHASE',//付费
        'COMPLETE_ORDER',//下单
        'VIEW_CONTENT',//关键页面浏览
        'VIEW_CONTENT',//商品页浏览 object=PRODUCT
        'ADD_TO_CART',//加入购物车
        'AD_CLICK',//广告点击
        'AD_IMPRESSION',//广告曝光
    ];


    const URL = [
        'tencent' => 'http://tracking.e.qq.com/conv', //POST 数据上报
    ];


    private function __advList($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return AppCRedisKeys::advList($mod);
    }

    /**
     * @return AdvAscribe
     */
    public static function factory(): AdvAscribe
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    public function run(string $function, array $args, int $time = 0): array
    {
        switch ($function) {
            case 'tencent':
                return $this->$function($args['user_id'],$args['event'],$args['url'],$args['click_id'] ?? '',$args['extra'] ?? []);
            default:
                return [false, 'function 不存在'];
        }
    }


    /**
     * @param string $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * 插入队列
     */
    public function push(string $user_id,string $function, array $args, int $time = 0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function,$methods)){
            return [false,'方法不存在'];
        }

        if (empty($args)){
            return [false,'缺少参数'];
        }

//        $r_key = $this->__advList($user_id);
//
//        by::redis('core')->rPush($r_key,
//            json_encode(
//                [
//                    'function' => $function,
//                    'args'     => $args,
//                    'time'     => $time ?: time(),
//                ]
//            )
//        );

        // 写入队列
        \Yii::$app->queue->push(new SyncAdvJob(
            [
                'function' => $function,
                'args'     => $args,
                'time'     => $time ?: time(),
            ]
        ));

        return [true,'ok'];
    }


    /**
     * @deprecated 放入queue消费
     * @param $index
     * @return void
     */
    public function synAdv($index){
        try {
            $redis_key = $this->__advList($index);
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $exist_key  = AppCRedisKeys::ProcessExistKey("adv");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true){
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'link_run_adv');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'link_run_adv');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);

                $data    = $ret[1] ?? "";
                if(!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data,true);
                if(empty($aData)) {
                    continue;
                }

                //todo 三次重试
                $msg = '';
                for ($i = 1; $i <= 3; $i++) {
                    list($status,$msg) = $this->run($aData['function'],$aData['args'],$aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i*2);
                }

                if(!$status) {
                    //重试也失败 加入到mysql记录
                    by::model('AdvLogModel', 'main')->saveLog($aData['function'], $aData['args'], $msg, $aData['time']);
                }
            }


        }catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'link_run_adv_err');
            exit($error);
        }
    }


    public function tencent($user_id, $event, $url, $click_id, $extra)
    {
        //腾讯事件判断
        $actionType = self::TX_ACTION_TYPE ?? [];
        if (!in_array($event, $actionType)) {
            CUtil::debug("腾讯归因方法不存在", "err.adv-ascribe.{$event}");
            return [true, "腾讯归因方法不存在"];
        }

        $actionParam = [];
        //判断用户获取用户信息
        if (is_string($user_id) && strlen($user_id) > 10) {
            $userInfo = by::Rusers()->getUserMainInfo($user_id);
        }else{
            $user_id = CUtil::uint($user_id);
            $userInfo = by::users()->getUserMainInfo($user_id);
        }

        //用户信息
        $openudid = $userInfo['openudid'] ?? '';
        $unionid  = $userInfo['unionid'] ?? '';

        if(empty($openudid)){
            CUtil::debug("用户的openudid不存在|".$user_id, "err.adv-ascribe.{$event}");
            return [true, "用户的openudid不存在"];
        }


        //订单信息
        $price = 0;
        $orderNo = '';
        if($extra){
            $orderNo = (string)($extra['order_no'] ?? '');
            if($orderNo){
                //定金订单和尾款订单
                $orderData = by::Odeposit()->CommDepositPackageInfo($user_id,$orderNo);

                if(empty($orderData)){
                    $orderData = by::Ouser()->CommPackageInfo($user_id, $orderNo);
                }
                $price = by::Gtype0()->totalFee($orderData['price'] ?? 0, 0);
                $actionParam['value'] = $price;
//                //订单refer信息
//                $osourceInfo = by::osourceM()->getInfoByOrderNo($user_id, $orderNo) ?? '';
//                $url         = $osourceInfo['referer'] ?? '';
            }
            $object = $extra['object'] ?? '';
            if($object){
                $actionParam['object'] = $object;
            }
        }


        if($url){
            $urlParam = by::advAscribeModel()->GetRefererParam($url);
            $click = $urlParam['click_id'] ?? '';
            if($click) $click_id = $click;
        }

        $config                    = CUtil::getConfig('weixin','common',MAIN_MODULE);
        $appid                     = $config['appId'] ?? "";

        $actions = [
            [
                "outer_action_id" => empty($orderNo) ? $user_id : ($user_id . '-' . $orderNo),
                "action_time"     => intval(START_TIME),
                "user_id"         => [
                    "hash_imei"       => "",
                    "hash_idfa"       => "",
                    "hash_android_id" => "",
                    "wechat_openid"   => $openudid,
                    "wechat_unionid"   => "",
                    "wechat_app_id"   => $appid,
                    "oaid"            => "",
                ],
                "action_type"     => $event,
                "url"             => "",
                "trace"           => [
                    "click_id" => $click_id,
                ],
                "action_param"    => $actionParam,
            ]
        ];

        if(empty($actionParam)){
            unset($actions[0]['action_param']);
        }

        list($s, $ret)      = $this->_request('tencent', ['actions'=>$actions]);
        if (!$s) {
            return [false, $ret];
        }
        return [true, $ret];
    }


    /**
     * @param $platform
     * @param array $body
     * @param string $method
     * @return array
     * 请求
     */
    protected function _request($platform, array $body = [], string $method = 'POST'): array
    {
        $url = self::URL[$platform] ?? '';
        if (empty($url)) {
            return [false, 'event不存在'];
        }

        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Expect: "
        ];

        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body,320), $header, $method);

        // CUtil::debug("url:{$url}|httpcode:{$httpCode}|err:{$err}|data：" . json_encode($body,320) . " | ret:" . json_encode($ret), "adv-ascribe.{$platform}");
        CUtil::setLogMsg(
            "adv-ascribe.{$platform}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );

        if (!$status) {
            return [false, $err];
        }

        return [$ret['code'] === 0, $ret];
    }

}
