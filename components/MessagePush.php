<?php

namespace app\components;


use app\models\CUtil;

class MessagePush
{
    private static $_instance = [];
    protected $expire_time = YII_ENV_PROD ? 3600 : 60;

    const MSG_CONFIG_ID = [
        'REVIEW_AUDIT_REJECT'                  => 1808418568475815937, //评价审核拒绝
        'GROUP_PURCHASE_JOIN'                  => 1913047364381495297, //用户成功参团 （使用中）
        'GROUP_PURCHASE_REFUND'        => 1925471203650023426, //团员退款成功
        'GROUP_PURCHASE_LEADER_REWARD_SUCCESS' => 1913051060326682626, //团购成功，团长奖励发放成功
        'GROUP_PURCHASE_MEMBER_FAIL'           => 1913051332734144514, //拼团失败
        'GROUP_PURCHASE_LEADER_FAIL'           => 1925474017843458049, //团长团购失败 （使用中）
        'GROUP_PURCHASE_MEMBER_REWARD_SUCCESS' => 1925474216406003714, //团购成功（使用中）
        'GROUP_PURCHASE_IN_PROCESS'            => 1956634681884475394, //拼团待成团（使用中）
    ];

    const SCOPE  =[
        'ALL' => 1,
        'PART' => 2
    ];
    private $dreame_home_domain;

    const URL = [
        'MESSAGE_LIST'             => '/dreame-system/mall/message-config',                               // GET 分页查询消息列表
        'CREATE_MESSAGE'           => '/dreame-system/mall/message-config',                               // POST 创建消息配置
        'EDIT_MESSAGE'             => '/dreame-system/mall/message-config',                               // PUT 编辑消息配置 带id
        'DELETE_MESSAGE'           => '/dreame-system/mall/message-config',                               // DELETE 按照id删除消息配置      /dreame-system/mall/message-config/{id}
        'GET_MESSAGE_BY_ID'        => '/dreame-system/mall/message-config',                               // GET 按照id删除消息配置         /dreame-system/mall/message-config/{id}
        'MESSAGE_SUBMIT_APPROVING' => '/dreame-system/mall/message-config',                               // PUT 消息提交审核              /dreame-system/mall/message-config/{id}/approving
        'MESSAGE_APPROVING'        => '/dreame-system/mall/message-config',                               // PUT 消息审核                 /dreame-system/mall/message-config/{id}/approve/1
        'MESSAGE_OFFLINE'          => '/dreame-system/mall/message-config',                               // PUT 消息下线                 /dreame-system/mall/message-config/{id}/offline
        'MESSAGE_TOTESTING'        => '/dreame-system/mall/message-config',                               // PUT 转为测试                 /dreame-system/message-config/{id}/toTesting
        'UPLOAD_IMAGE'             => '/dreame-system/mall/message-config/image/upload',                  //上传图片
        'SEND_PUSH'                => '/dreame-system/mall/message-config/push',                          //发起推送
    ];

    // 租户ID 追觅000000  mova 000002
    const TENANT_ID = [
        'DREAME' => '000000',
        'MOVA'   => '000002'
    ];

    private function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    public static function factory(): MessagePush
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    public function run($function, $input)
    {
        switch ($function) {
            case 'messageList':
                return $this->$function($input['current'] ?? 1, $input['size'] ?? 10, $input['name'] ?? '', $input['status'] ?? 'Live');
            case 'createMessage':
                return $this->$function($input['jumpType'] ?? 1, $input['multiLangDisplay'] ?? '', $input['category'] ?? '', $input['imgUrl'] ?? '');
            case 'editMessage':
                return $this->$function($input['jumpType'] ?? 1, $input['multiLangDisplay'] ?? '', $input['category'] ?? '', $input['id'] ?? '', $input['imgUrl'] ?? '');
            case 'deleteMessage':
            case 'messageInfo':
            case 'submitAuditMessage':
            case 'auditMessage':
            case 'offlineMessage':
            case 'toTestingMessage':
                return $this->$function($input['id'] ?? '');
            case 'uploadImage':
                return $this->$function($input['file'] ?? '');
            case 'sendPush':
                return $this->$function($input['msgConfigId'] ?? '', $input['pushStrategy'] ?? '', $input['pushScope'] ?? 1, $input['uids'] ?? '', $input['ext'] ?? '');
            default:
                return [false, '方法不存在'];

        }
    }

    /**
     * @param $msgConfigId
     * @param $pushStrategy
     * @param $pushScope
     * @param $uids
     * @param $ext
     * @return array
     * 发起消息推送
     */
    public function sendPush($msgConfigId, $pushStrategy, $pushScope, $uids = [], $ext = ''): array
    {
        $body = [
            'msgConfigId'  => $msgConfigId,
            'pushStrategy' => $pushStrategy,
            'pushScope'    => $pushScope,
            'uids'         => $uids,
            'ext'          => $ext, // 附加信息
            'tenantId'     => strval(self::TENANT_ID['DREAME']) //追觅000000  mova 000002
        ];

        list($status, $ret) = $this->__request('SEND_PUSH', $body, 'POST');

        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $file
     * @return array
     * 上传图片
     */
    public function uploadImage($file): array
    {
        $header = [
            "Content-Type:multipart/form-data",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "uid:admin",
            "Expect: "
        ];

        $body = [
            'file'     => $file,
            'tenantId' => self::TENANT_ID['DREAME'] //追觅000000  mova 000002
        ];

        list($status, $ret) = $this->__request('UPLOAD_IMAGE', $body, 'FILE_POST', $header, [], 'file');
        if (!$status) {
            return [false, '上传失败'];
        }
        return [true, $ret['data'] ?? []];
    }

    /**
     * @param $id
     * @return array
     * 转为测试
     */
    public function toTestingMessage($id): array
    {
        list($status, $ret) = $this->__request('MESSAGE_TOTESTING', [], 'PUT', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $id
     * @return array
     * 消息下线
     */
    public function offlineMessage($id): array
    {
        list($status, $ret) = $this->__request('MESSAGE_OFFLINE', [], 'PUT', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $id
     * @return array
     * 审核消息
     */
    public function auditMessage($id): array
    {
        list($status, $ret) = $this->__request('MESSAGE_APPROVING', [], 'PUT', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $id
     * @return array
     * 消息提交审核
     */
    public function submitAuditMessage($id): array
    {
        list($status, $ret) = $this->__request('MESSAGE_SUBMIT_APPROVING', [], 'PUT', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }

    /**
     * @param $id
     * @return array
     * 按照id获取消息配置
     */
    public function messageInfo($id): array
    {
        list($status, $ret) = $this->__request('GET_MESSAGE_BY_ID', [], 'GET', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }

    /**
     * @param $id
     * @return array
     * 删除推送消息
     */
    public function deleteMessage($id): array
    {
        list($status, $ret) = $this->__request('DELETE_MESSAGE', [], 'DELETE', [], ['id' => $id]);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, []];
    }

    /**
     * @param $current
     * @param $size
     * @param $name
     * @param $status
     * @return array
     * 推送消息列表
     */
    public function messageList($current, $size, $name, $status): array
    {
        $arr = [
            'current' => $current,
            'size'    => $size,
            'name'    => $name,
            'status'  => $status
        ];

        list($status, $ret) = $this->__request('MESSAGE_LIST', [], 'GET', [], $arr);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $jumpType
     * @param $multiLangDisplay
     * @param $category
     * @return array
     * 创建推送消息
     */
    public function createMessage($jumpType, $multiLangDisplay, $category,$imgUrl): array
    {
        $body = [
            'jumpType'         => $jumpType,
            'multiLangDisplay' => $multiLangDisplay,
            'category'         => $category,
            'imgUrl'           => $imgUrl,
            'tenantId'         => self::TENANT_ID['DREAME'] //追觅000000  mova 000002
        ];

        list($status, $ret) = $this->__request('CREATE_MESSAGE', $body);
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }


    /**
     * @param $jumpType
     * @param $multiLangDisplay
     * @param $category
     * @param $id
     * @return array
     * 编辑推送消息
     */
    public function editMessage($jumpType, $multiLangDisplay, $category, $id,$imgUrl): array
    {
        $arr = [
            'jumpType'         => $jumpType,
            'multiLangDisplay' => $multiLangDisplay,
            'category'         => $category,
            'imgUrl'           => $imgUrl,
            'id'               => $id
        ];

        list($status, $ret) = $this->__request('EDIT_MESSAGE', $arr, 'PUT');
        if (!$status) {
            return [false, $ret['msg'] ?? ''];
        }
        return [true, $ret['data'] ?? []];
    }

    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param array $header
     * @param array $arr
     * @param string $type
     * @return array
     * 根据URL转接数据
     */
    private function __request($event, array $body = [], string $method = 'POST', array $header = [], array $arr = [], string $type = 'json'): array
    {
        $requestUrl = self::URL[$event] ?? '';
        if (empty($requestUrl)) {
            return [false, '事件不存在！'];
        }
        $url = $this->dreame_home_domain . $requestUrl;


        if ($arr) {
            //特殊处理url
            if ($event == 'DELETE_MESSAGE' || $event == 'GET_MESSAGE_BY_ID') {
                $url .= '/' . $arr['id'];
            } elseif ($event == 'MESSAGE_SUBMIT_APPROVING') {
                $url .= '/' . $arr['id'] . '/approving';
            } elseif ($event == 'MESSAGE_APPROVING') {
                $url .= '/' . $arr['id'] . '/approve/1';
            } elseif ($event == 'MESSAGE_OFFLINE') {
                $url .= '/' . $arr['id'] . '/offline';
            } elseif ($event == 'MESSAGE_TOTESTING') {
                $url .= '/' . $arr['id'] . '/toTesting';
            } else {
                $url .= '?' . http_build_query($arr);
            }
        }


        $header = empty($header) ? [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "uid:admin",
            "Expect: "
        ] : $header;

        $type == 'json' && $body = json_encode($body, 320);
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method, 20);
        // CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|data：" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "message_push.{$event}");
        CUtil::setLogMsg(
            "message_push.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
        // ($httpCode !== 200 || !empty($err)) && CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|data：" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "message_push.{$event}");
        if (!$status) {
            return [false, $err];
        }
        return [$ret['code'] === 0, $ret];
    }


}
