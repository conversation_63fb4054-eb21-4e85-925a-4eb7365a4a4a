<?php


namespace app\components;

use app\jobs\SyncCrmJob;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\UserExtendModel;
use yii\db\Exception;

/**
 * @deprecated 此方法已废弃，计划于 2025-03-27 移除
 * 原因：为了解耦 CRM 模块
 * <AUTHOR>
 */
class Crm extends Tencent
{

    public static $me = array();

    //开发：http://dreamepodap01.dreame.tech:50000
    //测试：http://dreamepoqap01.dreame.tech:50000
    //正式：http://dreamepopap01.dreame.tech:50000

    private $crm_host;

    CONST PORT = YII_ENV_PROD ? '8084' : (YII_ENV_TEST ? '8086' :(YII_ENV_DEV ? '8085' : '8086'));


    /**
     * https://docs.qq.com/sheet/DTktEWGZXbmxHdnhl?tab=BB08J2
     */
    const URL = [
        'CRM_COMMON'        => '/RESTAdapter/CRM_COMMON',   //通用接口
        'USER'              => '/RESTAdapter/MEM_CRM_002/', //会员注册/更新至CRM
        'SALE_ORDER'        => '/RESTAdapter/MEM_CRM_011/', //销售订单同步至CRM
        'SALE_ORDER_LINE'   => '/RESTAdapter/MEM_CRM_012/', //销售订单明细同步至CRM
        'REFUND_ORDER'      => '/RESTAdapter/MEM_CRM_013/', //退货订单同步至CRM
        'REFUND_ORDER_LINE' => '/RESTAdapter/MEM_CRM_014/', //退货订单明细同步至CRM
        'PRODUCT'           => '/RESTAdapter/MEM_CRM_004/', //产品注册
        'SCORE_ACCOUNT'     => '/RESTAdapter/MEM_CRM_006/', //积分账户
        'PROFITS'           => '/RESTAdapter/MEM_CRM_007/', //权益信息
        'ADDRESS'           => '/RESTAdapter/MEM_CRM_003/',
        'BEHAVIOR'          => '/RESTAdapter/MEM_CRM_005/',
        'SCORE_LOG'         => '/RESTAdapter/MEM_CRM_008/',
    ];
    const GET_URL = [
        'CANCEL_OUT'         => 'api/Account/MemberCancel',//会员注销
        'APPLY_GIFT_CARD'    => 'api/GiftcardMgmt/ApplyGiftCard',    //申请礼品卡
        'ACTIVATE_GIFT_GARD' => 'api/GiftcardMgmt/ActivateGiftCard', //激活礼品卡
        'USE_GIFT_CARD'      => 'api/GiftcardMgmt/UseGiftCard',      //使用礼品卡
    ];


    CONST CONFIG = YII_ENV_PROD ? [
        'PORT'      => '8084',
        'NAME'      => 'PO_CRM',
        'PWD'       => 'zm123456',
        'APPID'     => 'q6fxup4nme6d7ns3',
        'APPSECRET' => 'BDCTffEZCQOLX0COjsHrLBDBxQ4yjYkXH0AcEJZx6XKgdhF5ODGe5O6YumkItQU8'
    ] : (YII_ENV_TEST ? [
        'NAME'      => 'PO_USER',
        'PWD'       => 'zm123456',
        'PORT'      => '8086',
        'APPID'     => 'kczikeuptqdrhzuh',
        'APPSECRET' => 'cGXjrJorlDCEr60B750Nn7GvcU226w4UVq09MaQeaWqvHTGAFHnjhKPZVrOC0stV'
    ] :
        (YII_ENV_DEV ? [//dev
            'NAME' =>'PO_USER',
            'PWD'  =>'zm123456',
            'PORT' => '8085',
            'APPID'=>'d8bbwav3r6wbmec3',
            'APPSECRET'=>'er5RIgGCY5yTOINBDmSuRRJlGVAdNay39RSmo9G9fKVs2DstEzVyAjLErUFkZRjm'
        ] : [//uat
            'NAME'      => 'PO_USER',
            'PWD'       => 'zm123456',
            'PORT'      => '8086',
            'APPID'     => 'kczikeuptqdrhzuh',
            'APPSECRET' => 'cGXjrJorlDCEr60B750Nn7GvcU226w4UVq09MaQeaWqvHTGAFHnjhKPZVrOC0stV'
        ])
    );


    private function __crmList($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return AppCRedisKeys::crmList($mod);
    }

    public function __crmRetry():string{
        return AppCRedisKeys::crmRetry();
    }

    const SOURCE = 8;//小程序的订单来源默认8

    /**
     * 小程序订单状态 对应 crm平台状态
     */
    const ORDER_STATUS = [
        300 => 3,     //待发货
        400 => 5,     //待收货
        500 => 7,     //已完成
        10000000 => 10,//申请退款
        10000300 => 10,//申请退款&待发货
        10000400 => 10,//申请退款&待收货
        10000500 => 10,//申请退款&已完成
        20000000 => 11,//退款完成
        20000300 => 11,//退款完成&待发货
        20000400 => 11,//退款完成&待收货
        20000500 => 11,//退款完成&已完成
    ];

    /**
     * 小程序订单商品状态 对应 crm平台状态
     */
    const LINE_STATUS = [
        10000    => 1,   //正常状态
        10000000 => 2,//申请退款
        20000000 => 3,//退款成功
    ];

    const REFUND_STATUS = [
        1000     => 2,   //正常状态
        1010     => 11,//通过
        1020     => 6,//拒绝
        20000000 => 11
    ];

    const POINT_TYPE = [
        'ADD' => 1,
        'USE' => 2,
        'EXP' => 3,
    ];

    const POINT_TYPE_NAME = [
        1 => '发放',
        2 => '消耗',
        3 => '失效',
    ];

    const SCENES = [
        1 => '消费',
        2 => '促销',
        3 => '行为',
        4 => '调整',
        5 => '兑换',
        6 => '退货',
        7 => '抵扣',
        8 => '失效',
    ];


    const POINT_ACTION = [
        'REG'   => 'B001',
        'BIND'  => 'B002',
        'ORDER' => 'B003'
    ];

    //必要的单例模式
    private function __construct()
    {
        $this->crm_host = CUtil::getConfig('host', 'config', \Yii::$app->id)['crm_host'] ?? '';
    }

    private function __clone()
    {
    }

    /**
     * @return Crm
     * */
    public static function factory()
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * @return array
     * 获取token
     */
    public function getToken()
    {
        $key = AppCRedisKeys::crmAccessToken();
        $redis = by::redis('core');
        $ret = $redis->get($key);
        $ret = json_decode($ret, true);
        if (empty($ret)) {

            // 配置
            $config = self::CONFIG;
            // URL
            $url = $this->crm_host . "/RESTAdapter/CRM_COMMON?url=token&port={$config['PORT']}";
            // 请求参数
            $body = "grant_type=application&appId={$config['APPID']}&appSecret={$config['APPSECRET']}";

            $header = [
                "Content-Type:application/json",
                "Authorization: Basic " . base64_encode("{$config['NAME']}:{$config['PWD']}")
            ];

            $res = CUtil::curl_post($url, $body, $header);

            !YII_ENV_PROD && CUtil::debug("|url:{$url}|header:".json_encode($header)."|data：".json_encode($body,320)." | res:" . json_encode($res,320), "crm.token");

            $ret = json_decode($res,true);
            if (empty($ret['access_token'])) {
                CUtil::debug($res, "ecrm.token");
                trigger_error("crm：获取Token 失败", E_USER_ERROR);
            }

            $expire = isset($ret['expires_in']) ? intval($ret['expires_in']) : 7000;
            $redis->SETEX($key, $expire, json_encode($ret));
        }

        return $ret;

    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 录入会员
     */
    public function user($user_id): array
    {
        usleep(200);//防止超级并发sleep 200ms
        $extend     = by::userExtend()->getUserExtendInfo($user_id, false);
        $phone      = by::Phone()->GetPhoneByUid($user_id, false);
        $p_time     = by::Phone()->GetCtimeByUid($user_id);
        $user       = by::users()->getOneByUid($user_id,false);
        $main       = by::users()->getUserMainInfo($user_id);

        if (empty($phone)) {
            return [true, '忽略无手机号用户'];
        }

        $source     = empty($extend['source']) ? 12 : by::userExtend()->getCrmSource($extend['source']);

        $p_id       = $this->getCityId($user['area']['province'] ?? 0);
        $c_id       = $this->getCityId($user['area']['city'] ?? 0,$p_id);
        $a_id       = $this->getCityId($user['area']['area'] ?? 0,$c_id);

        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id,false);

        $body['card']       = $extend['card']??'';
        $body['uid'] = $mallInfo['uid'] ?? '';
        $body['nickName']   = ($user['nick']??'')."_{$user_id}";
        $body['phone']      = $phone;
        $body['sex']        = $user['sex'] ?? 3;
        $body['name']       = $user['real_name'] ?? $user_id;
        $body['province']   = $p_id;
        $body['city']       = $c_id;
        $body['county']     = $a_id;
        $body['birthday']   = !empty($user['birthday']) ? date('Y-m-d', $user['birthday']) : '';
        $body['openid']     = $main['openudid'] ?? '';
        $body['unionid']    = $main['unionid']  ?? '';
        $body['source']     = $source;
        $body['time']       = !empty($p_time) ? date('Y-m-d', $p_time) : date('Y-m-d');
        //查完数据就清除缓存 防止缓存过多
        by::Phone()->deleteRedisCache($user_id, $phone);
        by::usersMall()->deleteRedisCache($mallInfo['id']??0,$user_id);
//        by::users()->deleteRedisCache($user_id,$phone);

        list($s, $ret)      = $this->_request('USER', $body);
        if (!$s) {
            return [false, $ret];
        }
        if (empty($extend['card'])) {
            by::userExtend()->saveUserExtend($user_id, ['card' => $ret['Data']]);
        }
        return [true, $ret];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 录入订单
     */
    public function order($user_id, $order_no): array
    {
        $user_id  = CUtil::uint($user_id);
        $order_no = trim(strval($order_no));

        $extend = by::userExtend()->getUserExtendInfo($user_id, false);
        $phone  = by::Phone()->GetPhoneByUid($user_id);
        $order  = by::Ouser()->CommPackageInfo($user_id, $order_no, true, true, true,false,false);

        if (empty($order)) {
            !YII_ENV_PROD && CUtil::debug("{$user_id}|{$order_no}|".var_export($order, 1), 'test.order');
            return [false, '...'];
        }

        if (!isset(Crm::ORDER_STATUS[$order['status']])) {
            return [true, '无需同步crm'];
        }

        $qty = 0;
        foreach ($order['goods'] as $goods){
            $items = $this->tc($goods);
            foreach ($items as $item){
                $qty = bcadd($qty,$item['num']);
            }
        }

        $order_body = [
            'card' => $extend['card'] ?? '',
            'telephone' => (string)$phone,
            'payDate' => !empty($order['pay_time']) ? date('Y-m-d H:i:s', $order['pay_time']) : '',
            'completeTime' => !empty($order['finish_time']) ? date('Y-m-d H:i:s', $order['finish_time']) : '',
            'appletCode' => $order_no,
            'orderStatus' => Crm::ORDER_STATUS[$order['status']],
            'source' => Crm::SOURCE,
            'totalAmount' => $order['oreal_price'],
            'discountFee' =>$order['dprice'],
            'payedFee' => $order['real_price'],
            'isCoupon' => $order['coupon_id'] ? 1 : 0,
            'coupon' =>   $user_id.'_'.$order['coupon_id'],
            'couponAmount' => $order['cprice'],
            'isPoint' => $order['coin'] ? 1 : 0,
            'pointQuantity' => $order['coin'],
            'pointAmount' => $order['coin_price'],
            'freightAmount' => $order['fprice'],
            'productQty' => $qty,
            'receiverName' => $order['address']['nick']??'',
            'receiverMobile' => $order['address']['phone']??'',
            'receiverProvince' => $order['address']['pid']??'',
            'receiverCity' => $order['address']['cid']??'',
            'receiverCounty' => $order['address']['aid']??'',
            'receiverAddress' => $order['address']['detail']??'',
        ];

        list($s,$ret) = $this->_request('SALE_ORDER', $order_body);

        if (!$s) {
            //清理查询的CRM积分锁（有进bug）
            $r_key = AppCRedisKeys::scoreAccount($user_id);
            by::redis('core')->del($r_key);
            return [false,$ret];
        }

        by::Ouser()->upCrm($user_id,$order_no);

        return [true,$ret];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 录入订单明细
     */
    public function orderLine($user_id, $order_no): array
    {
        $user_id = CUtil::uint($user_id);
        $order_no = trim(strval($order_no));

        $extend = by::userExtend()->getUserExtendInfo($user_id, false);
        $phone = by::Phone()->GetPhoneByUid($user_id);
        $order = by::Ouser()->CommPackageInfo($user_id, $order_no, true, true, true,false,false);

        if (empty($order['goods'])) {
            return [true, '无需同步crm'];
        }

        $line = [];
        foreach ($order['goods'] as $goods) {
            $items = $this->tc($goods);
            foreach ($items as $item){
                if (in_array($item['sku_sn'], ['HY20250402001000', 'HY20250402002000', 'HY20250402003000', 'HY20250402004000', 'HY20250402005000', 'HY20250402006000', 'HY20250402007000', 'HY20250402008000', 'HY20250402009000'])) {
                    $item['sku_sn'] = '20010100000596';//指定sku 原因：会员所配商品sku在crm不存在 需要替换为指定存在的sku 20010100000596
                }
                $line['body'][] = [
                    'card'              => $extend['card'] ?? '',
                    'telephone'         => $phone,
                    'appletOrderCode'   => $order_no,
                    'appletLineCode'    => $order_no . '_' . $goods['id'].'_'.$item['key'],
                    'productType'       => '',
                    'productName'       => $item['sku_sn'],
                    'qty'               => $item['num'],
                    'pricet'            => $item['pricet'],
                    'totalAmount'       => $item['totalAmount'],
                    'discountAmount'    => $item['discountAmount'],
                    'cactualAmount'     => $item['cactualAmount'],
                    'isPoint'           => $item['pointQuantity'] ? 1 : 0,
                    'pointQuantity'     => $item['pointQuantity'],
                    'pointAmount'       => $item['pointAmount'],
                    'status'            => Crm::LINE_STATUS[$goods['status']] ?? 1
                ];
            }

        }

        return $this->_request('SALE_ORDER_LINE', $line);
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array
     * @throws Exception
     * 录入退款订单
     */
    public function refund($user_id,$refund_no): array
    {
        $user_id    = CUtil::uint($user_id);
        $refund_no  = trim(strval($refund_no));

        $extend     = by::userExtend()->getUserExtendInfo($user_id, false);
        $phone      = by::Phone()->GetPhoneByUid($user_id);
        $forder     = by::Orefund()->CommPackageInfo($user_id,$refund_no,true,true);

        if (empty($forder)){
            return [false,'无数据'];
        }

        if (!isset(Crm::REFUND_STATUS[$forder['status']])) {
            return [true, '无需同步crm'];
        }

        $order = by::Ouser()->CommPackageInfo($user_id, $forder['order_no'] ,true,true,true);

        $qty = 0;
        foreach ($forder['goods'] as $goods){
            $items = $this->tc($goods);
            foreach ($items as $item){
                $qty = bcadd($qty,$item['num']);
            }
        }

        $body = [
            'card' => $extend['card'] ?? '',
            'telephone' => $phone,
            'orderCode' =>$forder['order_no'],
            'appletCode' =>$forder['refund_no'],
            'returnDate' =>date('Y-m-d H:i:s',$forder['ctime']),
            'totalAmount' =>$forder['oprice'],
            'coupon' =>   $user_id.'_'.$order['coupon_id'],
            'couponAmount' => $order['cprice'],
            'discountFee' =>bcsub(bcsub($order['oprice'],$order['price'],2),$order['deposit_price']??0,2),
            'returnAmount' =>$forder['real_price']??0,
            'refundCoupons' =>count((array)$forder['goods']) == count((array)$order['goods']) ? 1 : 0,
            'freightAmount' =>$forder['fprice'],
            'returnQuantity' => $qty,
            'orderState' => Crm::REFUND_STATUS[$forder['status']],
            'source' => Crm::SOURCE,
        ];

        // return $this->_request('REFUND_ORDER',$body);
        return $this->_request('REFUND_ORDER',$body);
    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array
     * @throws Exception
     * 退款订单明细
     */
    public function refundLine($user_id,$refund_no): array
    {
        $user_id   = CUtil::uint($user_id);
        $refund_no = trim(strval($refund_no));

        $extend =  by::userExtend()->getUserExtendInfo($user_id,false);
        $phone  = by::Phone()->GetPhoneByUid($user_id);
        $forder = by::Orefund()->CommPackageInfo($user_id,$refund_no,true,true);

        if (empty($forder)){
            return [true,'无需同步crm'];
        }

        $order = by::Ouser()->CommPackageInfo($user_id, $forder['order_no'],true,true,true);

        if (empty($order['goods'])){
            return [true,'无需同步crm'];
        }

        $line = [];
        foreach ($forder['goods'] as $goods){
            $items = $this->tc($goods);
            foreach ($items as $item){
                $line['body'][] = [
                    'card' => $extend['card'] ?? '',
                    'telephone' => $phone,
                    'appletOrderCode' => $refund_no,
                    'appletLineCode' => $refund_no.'_'.$goods['id'].'_'.$item['key'],
                    'productType' => '',
                    'product' => $item['sku_sn'],
                    'quantity' => $item['num'],
                    'price' =>$item['totalAmount'],
                    'totalAmount' => $forder['oprice'],
                    'discountFee' => $item['discountAmount'],
                    'actualReturn'=>$item['cactualAmount'],
                    'point' =>  $item['pointQuantity'],
                    'pointAmount' => (bool)$item['pointQuantity'],
                    'status'=>Crm::LINE_STATUS[$goods['goods_status']] ?? 1
                ];
            }

        }

        return $this->_request('REFUND_ORDER_LINE',$line);
    }

    /**
     * @deprecated 产品注册无需同步CRM
     * @param $user_id
     * @param $id
     * @param string $act
     * @param string $before_sn
     * @return array
     * @throws Exception
     * 产品注册
     */
    public function productReg($user_id, $id, $act = 'add', $before_sn = ''): array
    {
        return [true, '无需同步crm'];

        $user_id = CUtil::uint($user_id);
        $id      = CUtil::uint($id);

        $r_info = by::productReg()->getOneById($user_id, $id);
        if (empty($r_info)) {
            return [true, '无需同步crm'];
        }

        $extend = by::userExtend()->getUserExtendInfo($user_id, false);
        if (empty($extend['card'])) {
            return [false, 'crm用户不存在'];
        }

        $p_info = by::product()->getOneById($r_info['product_id']);
        $m_name = $p_info['m_name'] ?? '';
        $pname  = $p_info['name'] ?? '';

        $body = [
                'card'        => $extend['card'],
                'phone'       => $r_info['phone'],
                'productName' => empty($m_name) ? trim($pname) : trim($m_name),
                'mktName'     => empty($m_name) ? trim($pname) : trim($m_name),
                'sncode'      => trim($r_info['sn']),
                'rdate'       => date('Y-m-d H:i:s', $r_info['create_time']),
                'bdate'       => date('Y-m-d', $r_info['buy_time']),
        ];

        if ($act == 'edit') {
            $body['presncode'] = $before_sn;
            $body['scenes']    = '2';
        } else {
            $body['scenes'] = '1';
        }


        list($s, $ret) = $this->_request('PRODUCT', $body);
        if (!$s) {
            return [false, $ret];
        }


        //修改同步状态
        by::model('ProductDetailModel', 'main')->updateByRegId($r_info['create_time'], $user_id, $r_info['id'], 1);

        return [$s, $ret];
    }

    /**
     * @param $user_id
     * @return bool|int|mixed|string
     * @throws Exception
     * 获取积分账户
     */
    public function getScoreAccount($user_id){

        $redis = by::redis();
        $r_key = AppCRedisKeys::scoreAccount($user_id);
        $point = $redis->get($r_key);

        if ($point === false) {
            $user_id = CUtil::uint($user_id);
            $extend  =  by::userExtend()->getUserExtendInfo($user_id, false);

            if (empty($extend['card'])) {
                $point = 0;
                $redis->set($r_key, $point, ['EX' => 60]);
                return $point;
            }

            $phone = by::Phone()->GetPhoneByUid($user_id);
            $body  = [
                'card'      => $extend['card'],
                'telephone' => $phone,
            ];
            list($s, $ret)  = $this->_request('SCORE_ACCOUNT',$body,'GET');

            if (!$s) {
                $point  = 0;
                $redis->set($r_key, $point, ['EX' => 30]);
                return $point;
            } else {
                $point  = $ret['Data']['availablScores'] ?? 0;
                $point  = intval($point);
                $redis->set($r_key, $point, ['EX' => 60]);
                return $point;
            }
        }

        return intval($point);

    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 获取积分账户
     */
    public function getProfits(): array
    {
        return $this->_request('PROFITS',[],'GET');
    }

    /**
     * @param $user_id
     * @param int $page
     * @return array
     * @throws Exception
     * 获取积分账户记录
     */
    public function getScoreLog($user_id, $page=1): array
    {
        $user_id  = CUtil::uint($user_id);
        $extend =  by::userExtend()->getUserExtendInfo($user_id, false);
        $body   = [
            'card'      => $extend['card'] ?? '',
            'pageIndex' => $page,
            'pageSize'  => 20,
        ];
        return $this->_request('SCORE_LOG',$body,'GET');
    }

    /**
     * @param $page
     * @param $page_size
     * @param $card
     * @return array
     * 后台获取积分账户记录
     */
    public function getScoreLogByCard($page, $page_size, $card): array
    {
        $body = [
            'card'      => $card,
            'pageIndex' => $page,
            'pageSize'  => $page_size
        ];

        return $this->_request('SCORE_LOG', $body, 'GET');
    }

    /**
     * @param $user_id
     * @param $id
     * @return array
     * @throws Exception
     * 地址
     */
    public function address($user_id,$id,$address=[]): array
    {
        $user_id = CUtil::uint($user_id);
        $id      = CUtil::uint($id);

        if (empty($address)) {
            $address = by::Address()->GetOneAddress($user_id,$id,false);
        }

        $extend =  by::userExtend()->getUserExtendInfo($user_id,false);
        if (empty($extend['card'])) {
            return [false,'crm用户不存在'];
        }
        $body       = [
            'card'      => $extend['card'],
            'phone'     => $address['phone']  ?? '',
            'name'      => $address['nick']   ?? '',
            'province'  => $address['pid']    ?? '',
            'city'      => $address['cid']    ?? '',
            'county'    => $address['aid']    ?? '',
            'address'   => $address['detail'] ?? '',
            'default'   => $address['status'] ?? ''
        ];

        if (!empty($address['crm_code'])){
            $body['code'] = $address['crm_code'];
        }

        list($s, $ret) = $this->_request('ADDRESS',$body);
        if (!$s) {
            return [false, $ret];
        }

        if (empty($address['crm_code'])){
            by::Address()->upCode($user_id,$id,$ret['Data']??'');
        }

        return [true,$ret];
    }

    /**
     * @param $user_id
     * @param $type
     * @param string $time
     * @param string $desc
     * @return array
     * @throws Exception
     * 行为
     */
    public function behavior($user_id, $type, string $time = '', string $desc='')
    {
        $user_id = CUtil::uint($user_id);
        $extend  = by::userExtend()->getUserExtendInfo($user_id, false);
        $phone   = by::Phone()->GetPhoneByUid($user_id);

        $body = [
            'card'      => $extend['card'] ?? '',
            'telephone' => $phone,
            'type'      => $type,
            'time'      => date('Y-m-d H:i:s', $time ?: time()),
            'desc'      => $desc
        ];

        if($type == self::POINT_ACTION['BIND']){
            $body['key'] = $body['type'].'|'.$body['card'].'|'.$body['telephone'];
        }

        // $lockCrmSend     = CUtil::getConfig('lockCrmSend','member',MAIN_MODULE) ?? false;

        // if ($type == self::POINT_ACTION['REG'] && $lockCrmSend) {
        if ($type == self::POINT_ACTION['REG']) {
            return EventMsg::factory()->run('inviteReg', ['user_id' => $user_id]);
        }

        // if($type == self::POINT_ACTION['ORDER'] && $lockCrmSend){
        if($type == self::POINT_ACTION['ORDER']){
             return [true,'ok'];
        }

        return $this->_request('BEHAVIOR', $body);

    }

    /**
     * 申请礼品卡
     * @param array $cards
     * @return array
     */
    public function applyGiftCard(array $cards)
    {
        return $this->_newRequest('APPLY_GIFT_CARD', $cards);
    }

    /**
     * 激活礼品卡
     * @param array $body
     * @return array
     */
    public function activateGiftGard(array $body)
    {
        return $this->_newRequest('ACTIVATE_GIFT_GARD', $body);
    }

    /**
     * 使用礼品卡
     * @param array $body
     * @return array
     */
    public function useGiftCard(array $body)
    {
        return $this->_newRequest('USE_GIFT_CARD', $body);
    }

    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @return array
     * 请求
     */
    protected function _request($event, array $body = [], string $method = 'POST'): array
    {
        $url = self::URL[$event];

        if (empty($url)) {
            return [false, 'event不存在'];
        }

        $url = $this->crm_host.$url;

        $token = $this->getToken();

        $body['Authorization'] = $token['token_type'] . ' ' . $token['access_token'];

        $config = self::CONFIG;

        $header = [
            "Content-Type:application/json",
            "Expect: ",
            "Authorization: Basic " . base64_encode("{$config['NAME']}:{$config['PWD']}")
        ];

        list($status,$httpCode,$ret,$err) = CommCurl::factory()->Send($url, json_encode($body,320), $header, $method, 10, ['p'=>'crm']);

        // CUtil::debug("event:{$event}|httpcode:{$httpCode}|url:{$url}|err:{$err}|data：".json_encode($body,320)." | ret:" . json_encode($ret,320), "crm.{$event}");
        CUtil::setLogMsg(
            "crm.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );

        if (!$status) {
            return [false, $err];
        }

        return [$ret['ErrorCode'] === 0, $ret];

        /*switch ($method) {
            case 'POST' :
                $ret = CUtil::curl_post($url, json_encode($body), $header);
                break;
            case 'GET' :
                $ret = CUtil::curl_get($url, 10, $header, '', json_encode($body));
                break;
            default :
                return [false, '无法理解method'];
        }*/
    }


    protected function _newRequest($event, array $body = [], string $method = 'POST'): array
    {
        $url = self::URL[$event] ?? '';
        $get_url = self::GET_URL[$event] ?? '';
        $port = self::PORT;
        if (empty($url) || empty($get_url)) {
            return [false, 'event或url不存在'];
        }
        $token = $this->getToken();
        $access_token = $token['access_token'] ?? '';
        if (empty($access_token)) {
            // CUtil::debug("获取token失败：event:{$event}|url:{$url}|get_url:{$get_url}|data：" . json_encode($body,320) . " | ret:", "warn.crm.{$event}");
            CUtil::setLogMsg(
                "warn.crm.{$event}",
                $body,
                [],
                [],
                $url,
                '获取token失败',
                [],
                200
            );
        }

        $urlData = [
            'port' => $port,
            'url' => $get_url,
            'crmtoken' => "Bearer " . $access_token,
        ];

        $urlData = http_build_query($urlData);
        $url = $this->crm_host . $url . '?' . $urlData;

        $config = self::CONFIG;
        $header = [
            "Content-Type : application/json",
            "Authorization : Basic " . base64_encode("{$config['NAME']}:{$config['PWD']}")
        ];
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body,320), $header, $method, 10, ['p'=>'crm']);

        // CUtil::debug("event:{$event}|httpcode:{$httpCode}|err:{$err}|url:{$url}|data：" . json_encode($body,320) . " | ret:" . json_encode($ret,320), "crm.{$event}");
        CUtil::setLogMsg(
            "crm.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );

        if (!$status) {
            return [false, $err];
        }

        return [$ret['ErrorCode'] === 0, $ret];
    }

    /**
     * 插入队列
     * @param int $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     */
    public function push(int $user_id, string $function, array $args, int $time = 0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function, $methods)) {
            return [false, '方法不存在'];
        }

        if (empty($args)) {
            return [false, '缺少参数'];
        }

        // 写入队列
        \Yii::$app->queue->push(new SyncCrmJob(
            [
                'function' => $function,
                'args'     => $args,
                'time'     => $time ?: time(),
            ]
        ));

        return [true, 'ok'];
    }


    public function run(string $function, array $args, int $time){
        switch ($function){
            case 'user':
            case 'crmDeprecated':
                return $this->$function($args['user_id']);
            case 'order':
            case 'orderLine':
                return $this->$function($args['user_id'],$args['order_no']);
            case 'refund':
            case 'refundLine':
                return $this->$function($args['user_id'],$args['refund_no']);
            case 'productReg':
                return $this->$function($args['user_id'],$args['id'],$args['act']??'add',$args['before_sn']??'');
            case 'address':
                return $this->$function($args['user_id'],$args['id'],$args['address']??[]);
            case 'behavior':
                return $this->$function($args['user_id'],$args['type'],$time,$args['desc']);
            default:
                return [false,'function 不存在'];
        }
    }

    /**
     * @param $user_id
     * @throws Exception
     * crm会员注销
     */
    public function crmDeprecated($user_id)
    {
        $extend     = by::userExtend()->getUserExtendInfo($user_id);

        $cardNumber = $extend['card'] ?? '';
        $phone      = by::Phone()->GetPhoneByUid($user_id);
        $body = [
            'memberCard'=>$cardNumber,
            'telephone'=>$phone,
            'uid'=>''
        ];
        return $this->_newRequest('CANCEL_OUT',$body);
    }

    public function getCityId($name,$p_id=1)
    {
        if (is_array($name)|| empty(trim($name))) {
            return '';
        }

        $model    = by::model('AreaModel', MAIN_MODULE);
        $province = $model->GetList($p_id);
        $province = array_column($province,null,'name');

        return $province[$name]['id'] ?? '';
    }

    /**
     * @param $card
     * @param $type
     * @param $score
     * @param $date
     * @return array
     * @throws Exception
     * 积分推送
     */
    public function pointPush($card,$type,$score,$balance,$date): array
    {
        $user_id = by::userExtend()->getIDbyCard($card);
        if (empty($user_id)){
            return [false,'card用户不存在'];
        }

        switch ($type){
            case Crm::POINT_TYPE['ADD']:
                $push_type = by::WxNotice()::TYPE['POINT_ADD'];
                break;
            case Crm::POINT_TYPE['USE']:
                $push_type = by::WxNotice()::TYPE['POINT_USE'];
                break;
            case Crm::POINT_TYPE['EXP']:
                $push_type = by::WxNotice()::TYPE['POINT_EXP'];
                break;
            default:
                return [false,'type无法识别'];
        }

        $data = [
            'card'          => $card,
            'type'          => $push_type,
            'user_id'       => $user_id,
            'point'         => $score,
            'all_point'     => $balance,
            'date'          => $date
        ];

        $status = by::WxNotice()->AddPush($data);
        if (!$status){
            return [false,'推送入队失败'];
        }

        return [true,'ok'];
    }

    /**
     * @param $goods
     * @return array
     * @throws Exception
     */
    public function tc($goods){
        // 若传递的sku是套餐sn 则需要获取到套餐sku
        $tc_data = by::GtcModel()->getListBySn($goods['sku']);
        $items   = [];

        if (empty($tc_data)) {
            $price = bcadd($goods['price'],$goods['deposit_price']??0,2);

            $items[] = [
                'sku_sn'            => $goods['sku'],
                'cactualAmount'     => $price,
                'pointQuantity'     => $goods['coin'],
                'pointAmount'       => $goods['coin_price'],
                'num'               => $goods['num'],
                'pricet'            => bcdiv($goods['oprice'], $goods['num'], 2),
                'totalAmount'       => $goods['oprice'],
                'discountAmount'    => bcsub($goods['oprice'], $price, 2),
                'key'               => 0
            ];
        } else {
            // 防止套餐下有多个sku 循环一次
            $sl = array_column($tc_data,'sl','sku');
            $sl_num = array_sum($sl);
            $sl_count = count($tc_data);
            $ca = $pq = $pa = $ta= 0;

            $price = bcadd($goods['price'],$goods['deposit_price']??0,2);

            foreach ($tc_data as $key=> $value) {
                if ($key == $sl_count-1){
                    $cactualAmount = bcsub($price,$ca,2);
                    $pointQuantity = bcsub($goods['coin'],$pq);
                    $pointAmount   = bcsub($goods['coin_price'],$pa,2);
                    $totalAmount   = bcsub($goods['oprice'],$ta,2);
                }else{
                    $cactualAmount = bcmul(bcdiv($price,$sl_num,2),$value['sl'],2);
                    $pointQuantity = bcmul(bcdiv($goods['coin'],$sl_num),$value['sl']);
                    $pointAmount   = bcmul(bcdiv($goods['coin_price'],$sl_num,2),$value['sl'],2);
                    $totalAmount   = bcmul(bcdiv($goods['oprice'],$sl_num,2),$value['sl'],2);
                }
                $ca = bcadd($ca,$cactualAmount,2);
                $pq = bcadd($pq,$pointQuantity,2);
                $pa = bcadd($pa,$pointAmount,2);
                $ta = bcadd($ta,$totalAmount,2);
                $items[] = [
                    'sku_sn'            => $value['sku'],
                    'cactualAmount'     => $cactualAmount,
                    'pointQuantity'     => $pointQuantity,
                    'pointAmount'       => $pointAmount,
                    'totalAmount'       => $totalAmount,
                    'pricet'            => bcdiv($totalAmount, $value['sl'], 2),
                    'num'               => $value['sl'],
                    'discountAmount'    => bcsub($totalAmount,$cactualAmount, 2),
                    'key'               => $key
                ];
            }
        }

        return $items;
    }

}
