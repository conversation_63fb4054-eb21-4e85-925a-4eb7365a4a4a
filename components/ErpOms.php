<?php


namespace app\components;


use app\models\CUtil;

class ErpOms
{
    const URL    = YII_ENV_PROD ? "https://oms-out.dreame.tech/gateway/e3plus-dispatch" : "https://e3test-gateway-out.dreame.tech/gateway/e3plus-dispatch";
    const APP_ID = YII_ENV_PROD ? 10008 : 10008;

    const METHOD_CODE = [
        'querySn' => 'out.stock.getSnByDealCode',
    ];

    const private_key_path = YII_ENV_PROD ? WEB_PATH . '/../modules/main/config/erpOms/erp_oms_private_prod.pem' :
        WEB_PATH . '/../modules/main/config/erpOms/erp_oms_private_test.pem';


    protected static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return Erp
     * */
    public static function factory()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new self();
        }

        return self::$_instance[__CLASS__];
    }


    public function run(string $function, array $args): array
    {
        switch ($function) {
            case 'querySn':
                return $this->$function($args['user_id'], $args['order_nos']);
            default:
                return [false, 'function 不存在'];
        }
    }


    /**
     * @param $user_id
     * @param $order_nos
     * @return array
     * 要保证订单号是一个订单号一个商品
     */
    public function querySn($user_id, $order_nos)
    {
        $item      = [
            'dealCode' => $order_nos,
        ];
        $method    = self::METHOD_CODE['querySn'];
        $arr       = [
            'app_id'      => self::APP_ID,
            'charset'     => 'utf-8',
            'sign_type'   => 'RSA2',
            'format'      => 'JSON',
            'timestamp'   => date('Y-m-d H:i:s'),
            'biz_content' => json_encode($item, 320),
            'method'      => $method,
            'is_sync'     => 1,
        ];
        $arr       = array_merge(self::addOmsSign($arr), $arr);
        $body      = http_build_query($arr);
        $url       = self::URL;
        // CUtil::debug("url:{$url}|body:{$body}|arr:" . json_encode($arr) . "|only-data", "erp_oms.{$method}");
        
        $headers   = [];
        $headers[] = 'Content-Type：application/x-www-form-urlencoded';
        $headers[] = 'Expect: ';
        $res       = CUtil::curl_post($url, $body, $headers, 10, true, '', '', true);
        $data      = (array)json_decode($res, true);
        $code      = CUtil::uint($data['code'] ?? 0);
        if ($code != 100000) {
            // CUtil::debug("url:{$url}|body:{$body}|arr:" . json_encode($arr) . "|res:{$res}", "err.erp_oms.{$method}");
            CUtil::setLogMsg(
                "err.erp_oms.{$method}",
                $arr,
                $res,
                $headers,
                $url,
                '',
                [],
                200
            );
        }
        $res_content = (array)json_decode($data['rsp_content'], true);
        $code2       = CUtil::uint($res_content['code'] ?? 0);
        if ($code2 != 100000) {
            // CUtil::debug("url:{$url}|body:{$body}|arr:" . json_encode($arr) . "|res:{$res}", "err.erp_oms.{$method}");
            CUtil::setLogMsg(
                "err.erp_oms.{$method}",
                $arr,
                $res,
                $headers,
                $url,
                '',
                [],
                200
            );
        }
        // !YII_ENV_PROD && CUtil::debug("url:{$url}|body:{$body}|arr:" . json_encode($arr) . "|res:{$res}", "erp_oms.{$method}");
        !YII_ENV_PROD && CUtil::setLogMsg(
            "erp_oms.{$method}",
            $arr,
            $res,
            $headers,
            $url,
            '',
            [],
            200
        );
        return [true, $res_content['data'] ?? ''];
    }

    /**
     * @param $data
     * @return mixed
     * 增加数据签名(私钥加密)
     */
    public static function addOmsSign($data)
    {
        ksort($data, SORT_STRING);
        openssl_sign(urldecode(http_build_query($data)), $signature, self::getPrivateKey(self::private_key_path), defined('OPENSSL_ALGO_SHA256') ? OPENSSL_ALGO_SHA256 : 'sha256WithRSAEncryption');
        $data['sign'] = base64_encode($signature);
        return $data;
    }


    /**
     * @param $filepath
     * @return bool
     * 获取私钥
     */
    public static function getPrivateKey($filepath)
    {
        return openssl_pkey_get_private(file_get_contents($filepath));
    }

    /**
     * @param $service_type
     * @param array $data
     * @return array
     */
    protected function _request($service_type, array $data = [])
    {
        $body = [
            'method' => $service_type,
            'data'   => json_encode($data, 320)
        ];

        $header = [
            "Content-Type:multipart/form-data",
            "Expect: "
        ];
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send(self::URL, $body, $header, 'POST', 20);
        CUtil::debug("httpcode:{$httpCode}|err:{$err}|data：" . json_encode($body, 320) . " | ret:" . json_encode($ret), "erpNew.{$service_type}");

        return [CUtil::uint($ret['code'] ?? 0) === 100000, $ret];
    }

}
