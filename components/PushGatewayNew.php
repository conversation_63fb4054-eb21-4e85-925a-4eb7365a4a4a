<?php


namespace app\components;

use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use GuzzleHttp\Exception\GuzzleException;
use Prometheus\CollectorRegistry;
use Prometheus\RenderTextFormat;
use RuntimeException;

class PushGatewayNew
{
    const HTTP_PUT    = "PUT";
    const HTTP_POST   = "POST";
    const HTTP_DELETE = "DELETE";
    /**
     * @var PushGatewayNew
     */
    private static $_instance;
    /**
     * @var string
     */
    private $address;

    /**
     * @var ClientInterface
     */
    private $client;


    //必要的单例模式
    public function __construct(string $address, ClientInterface $client = null)
    {
        date_default_timezone_set('PRC');
        $this->address = strpos($address, 'http') === false ? 'http://' . $address : $address;
        $this->client  = $client ?? new Client(['connect_timeout' => 10, 'timeout' => 20]);
    }

    private function __clone()
    {

    }

    /**
     * @param string $address
     * @param ClientInterface|null $client
     * @return PushGatewayNew
     */
    public static function factory(string $address, ClientInterface $client = null): PushGatewayNew
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self($address,$client);
        }

        return self::$_instance;
    }


    /**
     * @throws GuzzleException
     */
    public function push(CollectorRegistry $collectorRegistry, string $job, array $groupingKey = [], array $header = []): void
    {
        $this->doRequest($collectorRegistry, $job, $groupingKey, self::HTTP_PUT, $header);
    }

    /**
     * @throws GuzzleException
     */
    public function pushAdd(CollectorRegistry $collectorRegistry, string $job, array $groupingKey = [], array $header = []): void
    {
        $this->doRequest($collectorRegistry, $job, $groupingKey, self::HTTP_POST, $header);
    }

    /**
     * @throws GuzzleException
     */
    public function delete(string $job, array $groupingKey = [], array $header = []): void
    {
        $this->doRequest(null, $job, $groupingKey, self::HTTP_DELETE, $header);
    }

    /**
     * @throws GuzzleException
     */
    private function doRequest(CollectorRegistry $collectorRegistry, string $job, array $groupingKey, string $method, array $header): void
    {
        $url = $this->address . "/metrics/job/" . $job;
        if ($groupingKey !== []) {
            foreach ($groupingKey as $label => $value) {
                $url .= "/" . $label . "/" . $value;
            }
        }
        $header = array_merge(['Content-Type' => RenderTextFormat::MIME_TYPE], $header);

        $requestOptions = [
            'headers' => $header,
        ];

        if ($method !== self::HTTP_DELETE) {
            $renderer               = new RenderTextFormat();
            $requestOptions['body'] = $renderer->render($collectorRegistry->getMetricFamilySamples());
        }
        $response   = $this->client->request($method, $url, $requestOptions);
        $statusCode = $response->getStatusCode();
        if (!in_array($statusCode, [200, 202], true)) {
            $msg = "Unexpected status code "
                . $statusCode
                . " received from push gateway "
                . $this->address . ": " . $response->getBody();
            throw new RuntimeException($msg);
        }
    }
}
