<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/12/21
 * Time: 16:49
 * 字符串迭代器，拓展PHP只支持数组、对象foreach
 *
 * @example
 * <pre>
 * $string = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";//任意字符串
 * $string = new app\components\StringObj($string);
 * foreach ($string as $key => $value) {
 *  echo "{$key} => {$value} \n";
 * }
 * </pre>
 */
namespace app\components;

class StringObj implements \Iterator {
    private $__step = 0;
    private $__key;
    private $__val;
    private $__string;
    private $__debug = false;
    private $__size = 1;//步长

    public function __construct(string $str, bool $debug=false){
        $this->__string = $str;
        $this->__debug  = $debug;
        if($this->__debug) {
            echo "第{$this->__step}步:对象初始化.\n";
            $this->__step++;
        }
    }

    public function rewind(){
        $this->__key = 0;
        $this->__val = $this->__string[$this->__key];
        if($this->__debug) {
            echo "第{$this->__step}步:".__FUNCTION__."被调用.\n";
            $this->__step++;
        }
    }

    public function next() {

        $ord = ord($this->__string[$this->__key+1] ?? "");
        if($ord > 128 )  {
            $this->__size = 3;//汉字三字符
        } else {
            $this->__size = 1;
        }

        $this->__val = "";
        for ($i=1;$i<=$this->__size;$i++) {
            $val = $this->__string[$this->__key+$i] ?? null;
            if(is_null($val)) {
                break;
            }

            $this->__val .= $val ;
        }

        $this->__key += $this->__size;
        if($this->__debug) {
            echo "第{$this->__step}步:".__FUNCTION__."被调用.\n";
            $this->__step++;
        }
    }

    public function current(){
        if($this->__debug) {
            echo "第{$this->__step}步:".__FUNCTION__."被调用.\n";
            $this->__step++;
        }
        return $this->__val;
    }

    public function key(){
        if($this->__debug) {
            echo "第{$this->__step}步:".__FUNCTION__."被调用.\n";
            $this->__step++;
        }

        return $this->__size == 1 ? $this->__key : $this->__key - $this->__size;
    }

    /**
     * @return bool
     * 当前是否在合法位置
     */
    public function valid(): bool
    {
        if($this->__debug) {
            echo "第{$this->__step}步:".__FUNCTION__."被调用.\n";
            $this->__step++;
        }

        return $this->__key < strlen($this->__string);
    }
}