<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\OgoodsModel;
use RedisException;
use yii\db\Exception;

class Statistics
{

    const LIMIT_NUM = 500;
    const LEVEL = [
        'v1' => 'VIP1',
        'v2' => 'VIP2',
        'v3' => 'VIP3',
        'v4' => 'VIP4',
        'v5' => 'VIP5',
    ];

    const FREQUENCY = [
        1 => '当月',
        2 => '1-3个月',
        3 => '4-6个月',
        4 => '6个月以上',
    ];

    const PURCHASE_COUNT = [
        1 => '1次',
        2 => '2-3次',
        3 => '3次以上',
        4 => '6次以上',
    ];

    private $dreame_home_domain;

    const EVENT = [
        'BATCH_GET_MEMBER_KEYINFO' => '/dreame-member-center/int/memberinfo/batch-get-member-keyInfo',
        'BASIC_INFO'               => '/dreame-member-center/int/memberinfo/basic-info'
    ];

    public function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    public function __request($event,$body, $header = [],$method = 'POST')
    {
        $url    = $this->dreame_home_domain . self::EVENT[$event];
        $header = empty($header) ? [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "Expect: "
        ] : $header;
        $body   = json_encode($body, 320);
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method, 20);
        if (!$status) {
            return [false, $err];
        }
        return [$ret['code'] === 0, $ret['data']];
    }

    public function truncate()
    {
        $db        = by::dbMaster();
        $sql = "truncate table `db_dreame_log`.`t_marketing_information`";
        $db->createCommand($sql)->execute();
    }

    /**
     * @return string
     * @throws Exception
     * @throws RedisException
     */
    public function userLevelInfo(): string
    {
        $redis     = by::redis();
        $redis_key = 'statistics_user_info';
        $id        = CUtil::uint($redis->get($redis_key));
        $db        = by::dbMaster();
        $marketTb  = "`db_dreame_log`.`t_marketing_information`";

        while (1) {
            // 查询用户信息
            $sql = <<<SQL
SELECT
  `phone`.`user_id`,
  `mall`.`uid`,
  `mall`.`id`,
  `mall`.`phone`,
  `mall`.`create_time`
FROM
  `db_dreame`.`t_phone` AS `phone`
  INNER JOIN `db_dreame`.`t_users_mall` as `mall` on `phone`.`phone` = `mall`.`phone`
  and `phone`.`mall_id` = `mall`.`id`
where
  `mall`.`id` > :id limit 500;
SQL;

            $userInfo = $db->createCommand($sql, [":id" => $id])->queryAll();

            // 如果没有用户信息，结束循环
            if (empty($userInfo)) {
                return 'statistics-over';
            }
            $end = end($userInfo);
            $id  = $end['id'];

            // 将 uid 作为 key 方便后续处理
            $map = [];
            foreach ($userInfo as $item) {
                $map[$item['uid']] = $item;
            }
            $uIds = array_keys($map);

            // 获取用户中心等级、积分、觅享分
            list($status, $data) = $this->__request('BATCH_GET_MEMBER_KEYINFO', ['uids' => $uIds]);

            if (!$status) {
                continue;
            }

            $responseUids = array_column($data, 'uid');
            $diffUids     = array_diff($uIds, $responseUids);
            $rows         = [];
            $fields       = [];

            foreach ($data as $value) {
                $valueUid    = $value['uid'];
                $mapUserInfo = $map[$valueUid];
                $orderTb     = by::Ogoods()::tbName($mapUserInfo['user_id']);
                $orderSql    = "SELECT SUM(`coin`) as `use_point` FROM {$orderTb} WHERE `user_id`=:user_id";
                $orderInfo   = $db->createCommand($orderSql, [":user_id" => $mapUserInfo['user_id']])->queryOne();

                $row    = [
                    'user_id'   => $mapUserInfo['user_id'],
                    'phone'     => $mapUserInfo['phone'],
                    'uid'       => $mapUserInfo['uid'],
                    'level'     => $value['level'] ?? '',
                    'point'     => $value['totalPoints'] ?? 0,
                    'use_point' => $orderInfo['use_point'] ?? 0,
                    'grow'      => $value['totalGrow'] ?? 0,
                    'reg_time'  => empty(($value['regTime'] ?? 0) / 1000) ? $mapUserInfo['create_time'] : ($value['regTime'] ?? 0) / 1000,
                    'ctime'     => intval(START_TIME),
                    'utime'     => intval(START_TIME),
                ];
                $fields = array_keys($row);
                $rows[] = $row;
            }

            if ($diffUids) {
                foreach ($diffUids as $diffUid) {
                    $mapUserInfo = $map[$diffUid];
                    list($diffStatus, $diffData) = $this->__request('BASIC_INFO', ['uid' => $diffUid], [], 'GET');

                    $orderTb   = by::Ogoods()::tbName($mapUserInfo['user_id']);
                    $orderSql  = "SELECT SUM(`coin`) as `use_point` FROM {$orderTb} WHERE `user_id`=:user_id";
                    $orderInfo = $db->createCommand($orderSql, [":user_id" => $mapUserInfo['user_id']])->queryOne();

                    $row = [
                        'user_id'   => $mapUserInfo['user_id'],
                        'phone'     => $mapUserInfo['phone'],
                        'uid'       => $mapUserInfo['uid'],
                        'level'     => $diffData['currentLevelInfo']['level']['level'] ?? '',
                        'point'     => $diffData['totalPoints'] ?? 0,
                        'use_point' => $orderInfo['use_point'] ?? 0,
                        'grow'      => $diffData['totalGrow'] ?? 0,
                        'reg_time'  => $mapUserInfo['create_time'],
                        'ctime'     => intval(START_TIME),
                        'utime'     => intval(START_TIME),
                    ];

                    $fields = array_keys($row);
                    $rows[] = $row;
                }
            }

            // 批量插入数据库
            by::dbMaster()->createCommand()->batchInsert($marketTb, $fields, $rows)->execute();
            // 记录偏移量 ID
            $redis->set($redis_key, $id, 86400);
        }
    }

    /**
     * @throws Exception
     * @throws RedisException
     * 统计会员信息
     */
    public function memberBuyInfo($machine_type = 'part')
    {
        $redis = by::redis();
        //判断获取主机数据还是配件数据
        $machine_type == 'main' ? $types = ['10', '11', '12', '13', '14'] : $types = ['20'];

        $db       = by::dbMaster();
        $tbTag    = by::Gtag()::tbName();
        $memberTb = by::marketInformation()::tableName();
        $userIds  = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        $types    = implode("','", $types);

        foreach ($userIds as $userId) {
            while (1) {
                $redis_key  = 'member-buy-info-' . $machine_type . '-' . $userId;
                $id         = CUtil::uint($redis->get($redis_key));
                $uoGTb      = by::Ogoods()::tbName($userId);
                $sql        = "SELECT `uog`.`id`,`uog`.`user_id`,`uog`.`gid`,`uog`.`num`,`uog`.`oprice`,`uog`.`price`,`member`.`level` FROM {$uoGTb} as `uog` 
    INNER JOIN {$tbTag} as `gtag` on `uog`.`gid` = `gtag`.`gid` 
    INNER JOIN ((SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2022` WHERE `status` = 500 and `user_id`% 10 = {$userId} ) UNION (SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2023` WHERE `status` = 500 and `user_id`% 10 = {$userId} )) AS om ON uog.order_no = om.order_no
        and `uog`.`user_id` = `om`.`user_id` 
    INNER JOIN (SELECT `phone`,`user_id`,`level` FROM {$memberTb}  where `user_id`% 10 = {$userId} ) as `member` on `member`.`user_id` = `uog`.`user_id`
        where `uog`.`id` >{$id} and `gtag`.`tid` in ('{$types}') LIMIT " . self::LIMIT_NUM;
                $memberInfo = $db->createCommand($sql)->queryAll();

                if (!$memberInfo) {
                    break;
                }
                $end = end($memberInfo);
                $id  = $end['id'];
                $redis->set($redis_key, $id, 7200);
                $data = [];
                foreach ($memberInfo as $item) {
                    if (!$item['level']) {
                        continue;
                    }
                    if ($machine_type == 'main') {
                        if (isset($data[$item['level']])) {
                            $data[$item['level']] = [
                                'level'                => $item['level'],
                                'buy_main_number'      => bcadd($data[$item['level']]['buy_main_number'], $item['num'] ?? 0),
                                'buy_main_price'       => bcadd($data[$item['level']]['buy_main_price'], $item['price'] ?? 0),
                                'main_deduction_price' => bcadd($data[$item['level']]['main_deduction_price'], bcsub($item['oprice'] ?? 0, $item['price'] ?? 0)),
                            ];
                        } else {
                            $data[$item['level']] = [
                                'level'                => $item['level'],
                                'buy_main_number'      => $item['num'] ?? 0,
                                'buy_main_price'       => $item['price'] ?? 0,
                                'main_deduction_price' => bcsub($item['oprice'] ?? 0, $item['price'] ?? 0),
                            ];
                        }
                    } else {
                        if (isset($data[$item['level']])) {
                            $data[$item['level']] = [
                                'level'                => $item['level'],
                                'buy_part_people'      => bcadd($data[$item['level']]['buy_part_people'], 1),
                                'buy_part_number'      => bcadd($data[$item['level']]['buy_part_number'], $item['num'] ?? 0),
                                'buy_part_price'       => bcadd($data[$item['level']]['buy_part_price'], $item['price'] ?? 0),
                                'part_deduction_price' => bcadd($data[$item['level']]['part_deduction_price'], bcsub($item['oprice'] ?? 0, $item['price'] ?? 0)),
                            ];
                        } else {
                            $data[$item['level']] = [
                                'level'                => $item['level'],
                                'buy_part_people'      => 1,
                                'buy_part_number'      => $item['num'] ?? 0,
                                'buy_part_price'       => $item['price'] ?? 0,
                                'part_deduction_price' => bcsub($item['oprice'] ?? 0, $item['price'] ?? 0),
                            ];
                        }
                    }
                }

                //处理数据入库
                foreach ($data as $value) {
                    $level = $value['level'];

                    $levelName = self::LEVEL[$level];
                    if (!$levelName) {
                        continue;
                    }
                    if ($machine_type == 'main') {
                    } else {
                        CUtil::debug('  购买主机数量:' . $value['buy_main_number']
                            . ' | 购买主机金额:' . $value['buy_main_price'] . ' | 购买主机抵扣金额:' . $value['main_deduction_price']
                            , $levelName . '_' . $machine_type . '_member_buy_info');
                        CUtil::debug('  购买配件数量:' . $value['buy_part_number']
                            . ' | 购买配件金额:' . $value['buy_part_price'] . ' | 购买配件抵扣金额:' . $value['part_deduction_price']
                            , $levelName . '_' . $machine_type . '_member_buy_info');
                    }
                }

            }
        }
    }


    /**
     * @param array $types
     * @param bool $isUid
     * @return void
     * @throws Exception
     * 购买数据---订单相关
     */
    public function buy_data(array $types = ['10', '11', '12', '13', '14'], bool $isUid = false)
    {
        $db    = by::dbMaster();
        $tbTag = by::Gtag()::tbName();
        foreach ($types as $type) {
            //获取tag
            $tagSql = "SELECT `id`,`gid`,`tid` FROM {$tbTag} WHERE `tid`=:tid";
            $tag    = $db->createCommand($tagSql, [':tid' => $type])->queryAll();
            $gid    = array_column($tag, 'gid');
            $gid    = implode("','", $gid);

            $uoGSql = "SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_1`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_2`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_3`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_4`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_5`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_6`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_7`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_8`
                                WHERE `gid` IN ('{$gid}')
                                
                                UNION
                                
                                SELECT `id`, `order_no`, `user_id`, `num`
                                FROM `db_dreame_goods`.`t_uo_g_9`
                                WHERE `gid` IN ('{$gid}')";

            $orders   = $db->createCommand($uoGSql)->queryAll();
            $order    = array_column($orders, null, 'order_no');
            $orderNos = implode("','", array_keys($order));

            //查询已完成订单
            $mainSql    = "SELECT
                            `order_no`,
                            `user_id` 
                        FROM
                            (
                            SELECT
                                `order_no`,
                                `user_id` 
                            FROM
                                `db_dreame_goods`.`t_om_2023` 
                            WHERE
                                `status` = 500 
                                AND `order_no` IN ( '{$orderNos}' ) UNION ALL
                            SELECT
                                `order_no`,
                                `user_id` 
                            FROM
                                `db_dreame_goods`.`t_om_2022` 
                            WHERE
                                `status` = 500 
                            AND `order_no` IN ( '{$orderNos}' ) 
                            ) AS subquery";
            $finishData = $db->createCommand($mainSql)->queryAll();


            $userId = array_column($finishData, 'user_id');

            //获取购买 扫地机 洗地机 吹风机 吸尘器  净饮一体机  五种类型 用户信息
            $buyPeopleNumber = count(array_unique($userId));

            $buyNumber = 0;
            foreach ($finishData as $datum) {
                $order_no = $datum['order_no'];
                if (isset($order[$order_no])) { // 有效得订单，取num
                    $buyNumber += $order[$order_no]['num'];
                }
            }
            $data = [
                'tid'               => $type,
                'buy_people_number' => $buyPeopleNumber,
                'buy_number'        => $buyNumber,
            ];
            $tagMap = by::Gtag()->GetTagNameMap();
            CUtil::debug($tagMap[$data['tid']] . "购买人数：" . $data['buy_people_number'] . "|购买数量:" . $data['buy_number'], 'register_data');
        }

    }


    /**
     * @param array $types
     * @return void
     * @throws Exception
     * 注册数据----产品相关
     */
    public function register_data(array $types = ['10', '11', '12', '13', '14'])
    {
        $db = by::dbMaster();
        foreach ($types as $type) {
            //获取tag
            $tbTag     = by::Gtag()::tbName();
            $tagSql    = "SELECT `id`,`gid`,`tid` FROM {$tbTag} WHERE `tid`=:tid";
            $tag       = $db->createCommand($tagSql, [':tid' => $type])->queryAll();
            $gids      = array_column($tag, 'gid');
            $goodsInfo = [];
            foreach ($gids as $gid) {
                $goodsInfo[] = by::Gmain()->GetOneByGid($gid);
            }

            $mainSku = array_column($goodsInfo, 'sku');

            $userIds = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
            $regInfo = [];
            foreach ($userIds as $id) {
                $regTb   = by::productReg()::getTable($id);
                $regSql  = "SELECT `id`,`user_id`,`product_id`  FROM {$regTb}";
                $regInfo = array_merge($regInfo, $db->createCommand($regSql)->queryAll());
            }

            $regMachineData = [];
            foreach ($regInfo as $value) {
                $product = by::product()->getOneById($value['product_id']);
                $sku     = by::Gspecs()->GetMainSkuBySpecsSku($product['m_name'] ?? '');
                if (in_array($sku, $mainSku)) {
                    $regMachineData[] = $value;
                }
            }
            $number       = count($regMachineData);
            $regUserId    = array_column($regMachineData, 'user_id');
            $peopleNumber = count(array_unique($regUserId));

            $data = [
                'tid'                    => $type,
                'register_people_number' => $peopleNumber,
                'register_number'        => $number,
                'utime'                  => intval(START_TIME)
            ];

            $dup = [];
            foreach ($data as $key => $value) {
                $dup[] = "`{$key}` = '{$value}'";
            }
            $dup = implode(' , ', $dup);

            $data['ctime'] = intval(START_TIME);

            $fields = "`tid`,`register_people_number`,`register_number`,`ctime`,`utime`";
            $rows   = implode("','", $data);
            $tagMap = by::Gtag()->GetTagNameMap();
            CUtil::debug($tagMap[$data['tid']] . "注册人数：" . $data['register_people_number'] . "|注册数量:" . $data['register_number'], 'register_data');
        }
    }

    /**
     * @throws Exception
     * 客单价
     */
    public function customerPrice()
    {
        $db      = by::dbMaster();
        $userIds = range(0, 9);
        $where   = [
            ['min' => 0, 'max' => 0],
            ['min' => 100, 'max' => 5000],
            ['min' => 5100, 'max' => 10000],
            ['min' => 10000, 'max' => 99999999999]
        ];
        foreach ($where as $value) {
            $this_year = date("Y");
            $years     = [$this_year, date("Y", strtotime("-365 days"))];
            $years     = array_unique($years);
            $people    = 0;
            $price     = 0;
            foreach ($years as $year) {
                $date   = $year == $this_year ? date("{$year}md") : date("{$year}0101");
                $ctime  = strtotime($date);
                $mainTb = by::Omain()::tbName($ctime);
                foreach ($userIds as $userId) {
                    $goodsTb = by::Ogoods()::tbName($userId);
                    $sql     = "SELECT COUNT(DISTINCT(`uog`.`user_id`)) AS `people` , COALESCE(SUM(`uog`.`price`),0) AS `price` FROM {$goodsTb} AS `uog` INNER JOIN {$mainTb} AS om ON `uog`.`order_no` = `om`.`order_no`  WHERE  `om`.`status`=500 AND `uog`.`price` BETWEEN  {$value['min']}  AND {$value['max']}";
                    $data    = $db->createCommand($sql)->queryOne();
                    $price   = bcadd($price, $data['price'] ?? 0);
                    $people  = bcadd($people, $data['people'] ?? 0);
                }
            }
            CUtil::debug("配件客单价:" . bcdiv($value['min'], 100) . '-' . bcdiv($value['max'], 100) . "|人数：" . $people . "|金额:" . $price, 'customer_price');
        }
    }


    /**
     * @return void
     * 统计配件复购频次
     * @throws Exception
     */
    public function repeatBuyTime($type = '1')
    {
        $db      = by::dbMaster();
        $userIds = range(0, 9);

        $tagTb   = by::Gtag()::tbName();
        $sql     = "SELECT `gid` FROM {$tagTb} WHERE `tid`=20 ";
        $goodsId = $db->createCommand($sql)->queryAll();
        $goodsId = implode("','", array_column($goodsId, 'gid'));

        foreach ($userIds as $userId) {
            $goodsTb = by::Ogoods()::tbName($userId);
            //统计复购订单的第一条订单
            $sql  = "SELECT *
FROM (
    SELECT 
        `uog`.`order_no`,
        `uog`.`user_id`,
        `uog`.`price`,
        `uog`.`gid`,
        `om`.`status`,
        LEFT(`uog`.`order_no`, 6) AS `date`,
        IF(@prev_user_id = `uog`.`user_id`, @row_num := @row_num + 1, @row_num := 1) AS `row_num`,
        @prev_user_id := `uog`.`user_id`
    FROM
        {$goodsTb} AS `uog`
        INNER JOIN (
            (SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = {$userId} AND `status` = 500)
            UNION
            (SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = {$userId} AND `status` = 500)
        ) AS `om` ON `uog`.`user_id` = `om`.`user_id` AND `uog`.`order_no` = `om`.`order_no`
    WHERE
        `uog`.`user_id` IN (
            SELECT `user_id`
            FROM (
                SELECT `user_id`, COUNT(DISTINCT `order_no`) AS `order_count`
                FROM (
                    (SELECT `order_no`, `user_id` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = 0 AND `status` = 500)
                    UNION ALL
                    (SELECT `order_no`, `user_id` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = 0 AND `status` = 500)
                ) AS `all_orders`
                GROUP BY `user_id`
                HAVING `order_count` >= 2
            ) AS `repurchase_users`
        )
    ORDER BY `uog`.`user_id`, `uog`.`order_no`
) AS `first_repurchase_orders`
WHERE
    `row_num` = 1;";
            $data = $db->createCommand($sql)->queryAll();

            if (!$data) {
                continue;
            }
            $count  = count($data);
            $name   = '';
            $price  = 0;
            $people = 0;
            foreach ($data as $k => $value) {
                $date = $this->setDate($value['date']);
                switch ($type) {
                    case "1":
                        $frequency = "left(`uog`.`order_no`,6)={$date}";//当月
                        break;
                    case "2":
                        $frequency = $this->setDate($value['date'] + 1) . "<=left(`uog`.`order_no`,6) AND  left(`uog`.`order_no`,6)<=" . $this->setDate($value['date'] + 3);//1-3个月
                        break;
                    case "3":
                        $frequency = $this->setDate($value['date'] + 4) . "<=left(`uog`.`order_no`,6) AND  left(`uog`.`order_no`,6)<=" . $this->setDate($value['date'] + 6);//4-6个月
                        break;
                    case "4":
                        $frequency = "left(`uog`.`order_no`,6)>" . $this->setDate($value['date'] + 6);//6个月以上
                        break;
                    default:
                        $frequency = "left(`uog`.`order_no`,6)={$date}";//当月
                }
                $name       = self::FREQUENCY[$type] . 'db|uo_g_' . $userId;
                $buySql     = "SELECT COUNT(DISTINCT(`uog`.`order_no`)) AS `count`,COALESCE(SUM(`uog`.`price`),0) AS `price` FROM {$goodsTb}  as `uog` INNER JOIN ((SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2022` where `status` = 500 and `user_id` % 10 = {$userId}) UNION (SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2023` where `status` = 500 and `user_id` % 10 = {$userId})) AS `om` ON  `uog`.`user_id` = `om`.`user_id` and `uog`.`order_no` = `om`.`order_no` where `uog`.`user_id`={$value['user_id']} AND `uog`.`gid` IN ('{$goodsId}') and `uog`.`order_no`!={$value['order_no']} and  {$frequency}";
                $repeatData = $db->createCommand($buySql)->queryOne();
                if (empty($repeatData['count'])) {
                    continue;
                }
                $bfb = round($k / $count * 100, 2) . "%";
                print_r($bfb);
                print_r(PHP_EOL);
                $price  = bcadd($price, $repeatData['price']);
                $people = bcadd($people, 1);
            }
            CUtil::debug("频次:" . $name . " |人数：" . $people . " |金额:" . $price, 'repeat_buy');
        }
    }

    //日期转化
    /**
     * @throws Exception
     * 复购次数
     */
    public function repeatTime($type = 1)
    {
        $db           = by::dbMaster();
        $userIds      = range(0, 9);
        $peopleNumber = 0;
        $price        = 0;
        $name         = self::PURCHASE_COUNT[$type];
        foreach ($userIds as $userId) {
            $goodsTb = by::Ogoods()::tbName($userId);
            switch ($type) {
                case 1:
                    $where = " `purchase_count`=2";//1次
                    break;
                case 2:
                    $where = " 3<=`purchase_count` and  `purchase_count`<=4 ";//2-3次
                    break;
                case 3:
                    $where = " `purchase_count`>=4";//3次以上
                    break;
                case 4:
                    $where = " `purchase_count`>=7";//6次以上
                    break;
                default:
                    $where = "  `purchase_count` = 1";//1次
            }
            $buySql = "SELECT
                            COUNT(*) AS `repeat_people`,
                            SUM(price) AS `total_price`
                         FROM (
                            SELECT
                                uog.user_id,
                                SUM(uog.price) AS price,
                                COUNT(DISTINCT uog.order_no) AS purchase_count
                            FROM
                                {$goodsTb} AS `uog`
                            INNER JOIN (
                                (SELECT user_id, order_no FROM db_dreame_goods.t_om_2022 WHERE STATUS = 500 AND user_id % 10 = {$userId})
                                UNION
                                (SELECT user_id, order_no FROM db_dreame_goods.t_om_2023 WHERE STATUS = 500 AND user_id % 10 = {$userId})
                            ) AS om ON uog.user_id = om.user_id AND uog.order_no = om.order_no
                            GROUP BY uog.user_id
                            HAVING {$where}
                         ) AS subquery;";

            $repeatData   = $db->createCommand($buySql)->queryOne();
            $peopleNumber = bcadd($peopleNumber, $repeatData['repeat_people'] ?? 0);
            $price        = bcadd($price, $repeatData['total_price'] ?? 0);
        }
        CUtil::debug("次数:" . $name . "|人数：" . $peopleNumber . "|金额:" . $price, 'repeat_time');
    }



    /**
     * @param string $machine_type
     * @return void
     * 会员复购
     * @throws Exception
     */
    public function memberRepeatBuy(string $machine_type = 'part')
    {
        $levels    = self::LEVEL;
        $levelKeys = array_keys($levels);
        //判断获取主机数据还是配件数据
        $machine_type == 'main' ? $types = ['10', '11', '12', '13'] : $types = ['20'];

        $db       = by::dbMaster();
        $userIds  = range(0, 9);
        $types    = implode("','", $types);
        $tagTb   = by::Gtag()::tbName();
        $tagSql  = "SELECT `gid` FROM {$tagTb} WHERE `tid` IN ('{$types}')";
        $goodsId = $db->createCommand($tagSql)->queryAll();
        $goodsId = implode("','", array_column($goodsId, 'gid'));
        foreach ($levelKeys as $level) {
            foreach ($userIds as $userId) {
                $goodsTb = by::Ogoods()::tbName($userId);
                $sql     = "SELECT *
FROM (
    SELECT `uog`.`order_no`, `uog`.`user_id`, `uog`.`price`, `uog`.`gid`, `om`.`status`, `mi`.`level`, @row_num := IF(@prev_user_id = `uog`.`user_id`, @row_num + 1, 1) AS `row_num`, @prev_user_id := `uog`.`user_id`
    FROM {$goodsTb} AS `uog`
    INNER JOIN (
        SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = {$userId} AND `status` = 500
        UNION DISTINCT
        SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = {$userId} AND `status` = 500
    ) AS `om` ON `uog`.`user_id` = `om`.`user_id` AND `uog`.`order_no` = `om`.`order_no`
    INNER JOIN `db_dreame_log`.`t_marketing_information` AS `mi` ON `uog`.`user_id` = `mi`.`user_id`
    WHERE `uog`.`user_id` IN (
        SELECT `user_id`
        FROM (
            SELECT `user_id`, COUNT(DISTINCT `order_no`) AS `order_count`
            FROM (
                SELECT `order_no`, `user_id` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = 0 AND `status` = 500
                UNION DISTINCT
                SELECT `order_no`, `user_id` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = 0 AND `status` = 500
            ) AS `all_orders`
            GROUP BY `user_id`
            HAVING `order_count` >= 2
        ) AS `repurchase_users`
    )
    AND `mi`.`level` = '{$level}'
    ORDER BY `uog`.`user_id`, `uog`.`order_no`
) AS `first_repurchase_orders_v1`
WHERE `row_num` = 1;";
                $data    = $db->createCommand($sql)->queryAll();
                if (!$data){
                    continue;
                }
                $price   = 0;
                $people  = 0;
                $count   = count($data);
                foreach ($data as $key => $value) {
                    $buySql  = "SELECT COUNT(DISTINCT(`uog`.`order_no`)) AS `count`,COALESCE(SUM(`uog`.`price`),0) AS `price` FROM {$goodsTb}  as `uog` INNER JOIN ((SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2022` where `status` = 500 and `user_id` % 10 = {$userId}) UNION (SELECT `user_id`,`order_no` FROM `db_dreame_goods`.`t_om_2023` where `status` = 500 and `user_id` % 10 = {$userId})) AS `om` ON  `uog`.`user_id` = `om`.`user_id` and `uog`.`order_no` = `om`.`order_no` where `uog`.`user_id`={$value['user_id']} AND `uog`.`gid` IN ('{$goodsId}') and `uog`.`order_no`!={$value['order_no']}";
                    $buyData = $db->createCommand($buySql)->queryOne();

                    if (empty($buyData['count'])) {
                        continue;
                    }
                    $bfb = round($key / $count * 100, 2) . "%";
                    print_r($bfb);
                    print_r(PHP_EOL);
                    $price  = bcadd($price, $buyData['price']);
                    $people = bcadd($people, 1);
                }
                CUtil::debug("数据库：".$goodsTb." |类型：".$machine_type." |等级：".$level." |人数：".$people." |金额：".$price,'member_repeat');
            }

        }

    }


    /**
     * @param string $level
     * @return void
     * @throws Exception
     * 统计会员复购信息
     */
    public function memberRepeatPeople()
    {
        $db      = by::dbMaster();
        $userIds = range(0, 9);
        $levels  = array_keys(self::LEVEL);
        foreach ($levels as $level) {
            $people = 0;
            $price  = 0;
            foreach ($userIds as $userId) {
                $goodsTb = by::Ogoods()::tbName($userId);
                $sql = "SELECT 
    COUNT(DISTINCT subquery.user_id) AS count,
    SUM(subquery.price) AS price
FROM (
    SELECT 
        uog.user_id,
        uog.price
    FROM 
        {$goodsTb} AS uog
    INNER JOIN (
        SELECT 
            order_no, user_id, status 
        FROM 
            db_dreame_goods.t_om_2022 
        WHERE 
            user_id % 10 = {$userId} AND status = 500
        UNION ALL
        SELECT 
            order_no, user_id, status 
        FROM 
            db_dreame_goods.t_om_2023 
        WHERE 
            user_id % 10 = {$userId} AND status = 500
    ) AS om ON uog.user_id = om.user_id AND uog.order_no = om.order_no
    INNER JOIN db_dreame_log.t_marketing_information AS mi ON uog.user_id = mi.user_id
    WHERE EXISTS (
        SELECT 1
        FROM (
            SELECT 
                user_id, COUNT(DISTINCT order_no) as order_count
            FROM (
                (
                    SELECT 
                        order_no, user_id 
                    FROM 
                        db_dreame_goods.t_om_2022 
                    WHERE 
                        user_id % 10 = {$userId} AND status = 500
                )
                UNION ALL
                (
                    SELECT 
                        order_no, user_id 
                    FROM 
                        db_dreame_goods.t_om_2023 
                    WHERE 
                        user_id % 10 = {$userId} AND status = 500
                )
            ) AS all_orders
            GROUP BY user_id
            HAVING order_count >= 2
        ) AS repurchase_users
        WHERE uog.user_id = repurchase_users.user_id
    )
    AND mi.level = '{$level}'
    GROUP BY uog.user_id, uog.price
) AS subquery;";

                $data    = $db->createCommand($sql)->queryOne();
                $people = bcadd($people, $data['count']);
                $price  = bcadd($price, $data['price']);
            }
            CUtil::debug("等级：" . $level . "|人数:" . $people . " |金额：" . $price, 'repeat_people');
        }
    }

    /**
     * @throws Exception
     * 会员等级+购买主机配件数量
     */
    public function getMemberBuyMainAndPart($machine_type='part')
    {
        $db       = by::dbMaster();
        $userIds = range(0, 9);
        $levels  = array_keys(self::LEVEL);
        $tagTb   = by::Gtag()::tbName();

        $machine_type == 'main' ? $types = ['10', '11', '12', '13'] : $types = ['20'];
        $types    = implode("','", $types);
        $tagSql  = "SELECT `gid` FROM {$tagTb} WHERE `tid` IN ('{$types}')";
        $goodsId = $db->createCommand($tagSql)->queryAll();
        $goodsId = implode("','", array_column($goodsId, 'gid'));

        foreach ($levels as $level) {
            foreach ($userIds as $userId) {
                $goodsTb = by::Ogoods()::tbName($userId);
                $sql     = "SELECT
                                count( DISTINCT `uog`.`user_id` ) AS `count` 
                            FROM
                                {$goodsTb} AS uog
                                INNER JOIN (
                                    ( SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = {$userId} AND `status` = 500 ) UNION
                                    ( SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = {$userId} AND `status` = 500 ) 
                                ) AS `om` ON `uog`.`user_id` = `om`.`user_id` 
                                AND `uog`.`order_no` = `om`.`order_no`
                                INNER JOIN `db_dreame_log`.`t_marketing_information` AS `mi` ON `uog`.`user_id` = `mi`.`user_id` 
                            WHERE
                                `mi`.`level` = '{$level}'  AND `uog`.`gid` IN ('{$goodsId}')";
                $count   = $db->createCommand($sql)->queryScalar();
                CUtil::debug("机型：".$machine_type."|数据库：".$goodsTb."|等级：" . $level . "|人数:" . $count , 'main_part_people');
            }

        }

    }


    /**
     * @return void
     * 购买一元链接人数
     * @throws Exception
     */
    public function naryLink()
    {
        $db      = by::dbMaster();
        $userIds = range(0, 9);
        $levels  = array_keys(self::LEVEL);
        $goodsId = 118;

        foreach ($levels as $level) {
            foreach ($userIds as $userId) {
                $goodsTb = by::Ogoods()::tbName($userId);
                $sql     = "SELECT
                                count( DISTINCT `uog`.`user_id` ) AS `count` ,
                                SUM(uog.num) as `number`
                            FROM
                                {$goodsTb} AS uog
                                INNER JOIN (
                                    ( SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2022` WHERE `user_id` % 10 = {$userId} AND `status` = 500 ) UNION
                                    ( SELECT `order_no`, `user_id`, `status` FROM `db_dreame_goods`.`t_om_2023` WHERE `user_id` % 10 = {$userId} AND `status` = 500 ) 
                                ) AS `om` ON `uog`.`user_id` = `om`.`user_id` 
                                AND `uog`.`order_no` = `om`.`order_no`
                                INNER JOIN `db_dreame_log`.`t_marketing_information` AS `mi` ON `uog`.`user_id` = `mi`.`user_id` 
                            WHERE
                                `mi`.`level` = '{$level}'  AND `uog`.`gid` = {$goodsId}";
                $data    = $db->createCommand($sql)->queryOne();
                $count   = $data['count'] ?? 0;
                $number  = $data['number'] ?? 0;
                CUtil::debug("数据库：".$goodsTb."|等级：" . $level . "|人数:" . $count . '|数量：' . $number, 'nary-link');
            }

        }
    }


    public function setDate($date = '202204')
    {
        $month = substr($date, -2);
        if ($month > 12) {
            $year  = substr($date, 0, 4);
            $year  = $year + 1;
            $month = $month - 12;
            $date  = $year . sprintf("%02d", $month);
        }
        return $date;
    }

    /**
     * @return void
     * @throws Exception
     * 截断推荐表
     */
    public function truncateRecommend()
    {
        $db        = by::dbMaster();
        $sql = "truncate table `db_dreame_log`.`t_statistics_recommend`";
        $db->createCommand($sql)->execute();
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 推荐有礼订单数据
     */
    public function recommendData()
    {
        $tb       = by::userRecommend()::tbName();
        $db       = by::dbMaster();
        $redisKey = 'statistics_recommend_data';
        $redis    = by::redis();

        while (true) {
            $id            = $redis->get($redisKey);
            $recommendSql  = "SELECT `id`,`user_id` FROM {$tb} WHERE `id` > :id LIMIT 500";
            $recommendData = $db->createCommand($recommendSql, [":id" => $id])->queryAll();

            if (!$recommendData) {
                exit('statistics-over');
            }

            $end   = end($recommendData);
            $id    = $end['id'];
            $gmain = by::Gmain()::tbName();

            foreach ($recommendData as $k => $recommendDatum) {
                unset($recommendData[$k]['id']);
                $goodsTb = by::Ogoods()::tbName($recommendDatum['user_id']);
                $is_buy  = 0;//每购买

                $sql = "SELECT g.name, uog.price,om.order_no,om.ctime
                FROM {$goodsTb} AS uog
                INNER JOIN (
                    SELECT `order_no`, `user_id`,`ctime`
                    FROM `db_dreame_goods`.`t_om_2022`
                    WHERE `status` = 500
                    UNION
                    SELECT `order_no`, `user_id`,`ctime`
                    FROM `db_dreame_goods`.`t_om_2023`
                    WHERE `status` = 500
                ) AS `om` ON uog.user_id = om.user_id AND uog.order_no = om.order_no
                INNER JOIN {$gmain} AS g ON uog.gid = g.id
                INNER JOIN `db_dreame_goods`.`t_gtag` AS `tag` ON `uog`.`gid` = `tag`.`gid`
                WHERE uog.user_id = :userId AND `tag`.`tid` NOT IN (2, 20)";

                $data   = $db->createCommand($sql, [':userId' => $recommendDatum['user_id']])->queryAll();
                $insert = [];
                if ($data) {
                    $is_buy = 1;//购买过
                    foreach ($data as $value) {
                        $insert[] = [
                            'user_id'   => $recommendDatum['user_id'],    //用户ID
                            'is_buy'    => $is_buy,                       //是否购买
                            'main_name' => $value['name'],                //主机名称
                            'buy_price' => $value['price'],               //购买金额
                            'order_no'  => $value['order_no'],            //订单号
                            'buy_time'  => date('Y-m-d H:i:s',$value['ctime'])              //购买时间
                        ];
                        //  $name[] =  $value['name'] .' | 订单号:'.$value['order_no'].' | 购买时间:'.date('Y-m-d H:i:s',$value['ctime']);
                    }

                } else {
                    $insert[] = [
                        'user_id'   => $recommendDatum['user_id'],    //用户ID
                        'is_buy'    => $is_buy,                       //是否购买
                        'main_name' => '',                            //主机名称
                        'buy_price' => 0,                            //购买金额
                        'order_no'  => '',                            //订单号
                        'buy_time'  => ''                             //购买时间
                    ];

                }
                // 入库
                $recommendTb = "`db_dreame_log`.`t_statistics_recommend`";
                $result      = $db->createCommand()->batchInsert($recommendTb, ['user_id', 'is_buy', 'main_name', 'buy_price','order_no', 'buy_time'], $insert)->execute();
                if ($result) {
                    $redis->set($redisKey, $id, 7200);
                }
            }

        }
    }


    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 统计会员积分信息（明细）
     */
    public function operPlatForm()
    {
        $redis = by::redis();
        $dbIot = by::dbMaster('db_app_iot', 1);
        $keys  = range(0, 1023);

        foreach ($keys as $key) {
            $redisKey      = 'member_point_id_' . $key;
            $id            = intval($redis->get($redisKey));
            $memberPointDb = "`dreame_oper_platform`.`dreame_member_points_{$key}`";

            while (true) {
                $sql = <<<SQL
                SELECT *
                FROM {$memberPointDb}
                WHERE `type` = 1 AND `id` > :id
                ORDER BY `id` ASC
                LIMIT 5000
SQL;

                $stmt = $dbIot->createCommand($sql);
                $stmt->bindParam(':id', $id);
                $memberInfo = $stmt->queryAll();

                if (empty($memberInfo)) {
                    break;
                }

                $this->syncMemberData($memberInfo);

                // 更新最后处理的ID
                $end = end($memberInfo);
                $id  = $end['id'];

                // 将最后处理的ID设置到Redis
                $redis->set($redisKey, $id, 7200);
            }
        }
    }


    //处理会员数据 批量入库
    public function syncMemberData($memberInfo)
    {
        $db     = by::dbMaster();
        $fields = [
            'uid', 'type', 'num_value', 'status', 'event_type', 'event_name', 'event_time', 'remark', 'collect_expire', 'create_time', 'update_time', 'is_deleted',
        ];
        $save   = [];

        foreach ($memberInfo as $value) {
            unset($value['id']);
            $formattedDate          = date('Y-m', strtotime($value['collect_expire']));
            $formattedDate          = str_replace('-', '', $formattedDate);
            $save[$formattedDate][] = $value;
        }

        foreach ($save as $key => $item) {
            $mallMemberDb = "`db_dreame_log`.`dreame_member_points_$key`";

            // 使用LIKE创建空表
            $sourceTable = "`db_dreame_log`.`dreame_member_points`";
            $tbSql       = "CREATE TABLE IF NOT EXISTS {$mallMemberDb} LIKE {$sourceTable};";
            $db->createCommand($tbSql)->execute();

            // 批量插入数据
            $db->createCommand()->batchInsert($mallMemberDb, $fields, $item)->execute();
        }
    }




    public function operPlatFormCsv()
    {

        $currentPath = getcwd();

        $redis = by::redis();
        $dbIot = by::dbMaster('db_app_iot', 1);
        $keys  = range(0, 1023);

        $csvFilePath = $currentPath.'/runtime/my_temp_file/202401/03/member.csv'; // 请将路径替换为实际路径
        // 获取目录路径
        $csvDir = dirname($csvFilePath);

        // 递归创建目录
        if (!file_exists($csvDir)) {
            mkdir($csvDir, 0777, true);
        }

        // 打开文件
        $csvFile = fopen($csvFilePath, 'a');

        // 检查文件是否为空，如果为空写入表头
        if (filesize($csvFilePath) == 0) {
            fputcsv($csvFile, [
                'uid',
                'type',
                'num_value',
                'status',
                'event_type',
                'event_name',
                'event_time',
                'remark',
                'collect_expire',
                'create_time',
                'update_time',
                'is_deleted',
            ]);
        }

        foreach ($keys as $key) {
            $redisKey      = 'member_point_id_' . $key;
            $id            = intval($redis->get($redisKey));
            $memberPointDb = "`dreame_oper_platform`.`dreame_member_points_{$key}`";

            while (true) {
                $sql = <<<SQL
            SELECT *
            FROM {$memberPointDb}
            WHERE `type` = 1 AND `id` > :id
            ORDER BY `id` ASC
            LIMIT 500
SQL;

                $stmt = $dbIot->createCommand($sql);
                $stmt->bindParam(':id', $id);
                $memberInfo = $stmt->queryAll();
                if (empty($memberInfo)) {
                    break;
                }

                foreach ($memberInfo as $member) {
                    // 将数据写入CSV文件
                    fputcsv($csvFile, [
                        $member['uid'],
                        $member['type'],
                        $member['num_value'],
                        $member['status'],
                        $member['event_type'],
                        $member['event_name'],
                        $member['event_time'],
                        $member['remark'],
                        $member['collect_expire'],
                        $member['create_time'],
                        $member['update_time'],
                        $member['is_deleted'],
                    ]);
                }

                // 更新最后处理的ID
                $end = end($memberInfo);
                $id  = $end['id'];

                // 将最后处理的ID设置到Redis
                $redis->set($redisKey, $id, 7200);
            }
        }

        // 关闭文件
        fclose($csvFile);
    }


    public function memberCsv()
    {
        $mallMemberDb = "`db_dreame_log`.`dreame_member_points`";
        $path = "C:/Users/<USER>/Desktop/member.csv";
        $fp = fopen($path, 'r');

        // 设置计数器和批量插入的阈值
        $counter = 0;
        $batchSize = 2000;
        $batchData = [];

        if ($fp !== false) {
            while ($line = fgetcsv($fp)) {
                // 将每个字段转换为 UTF-8 编码
                $save = [
                    'uid'            => $line[0],
                    'type'           => $line[1],
                    'num_value'      => $line[2],
                    'status'         => $line[3],
                    'event_type'     => $line[4],
                    'event_name'     => iconv('//IGNORE', 'UTF-8', $line[5]),
                    'event_time'     => $line[6],
                    'remark'         => $line[7],
                    'collect_expire' => $line[8] !== '' ? $line[8] : null,
                    'create_time'    => $line[9],
                    'update_time'    => $line[10],
                    'is_deleted'     => empty($line[11]) ? 0 : $line[11],
                ];

                // 将数据添加到批量插入数组
                $batchData[] = $save;

                // 每达到批量插入阈值，执行一次插入操作并重置计数器和批量插入数组
                if (++$counter >= $batchSize) {
                    // 直接插入数据库
                    by::dbMaster()->createCommand()->batchInsert($mallMemberDb, array_keys($save), $batchData)->execute();

                    // 重置计数器和批量插入数组
                    $counter = 0;
                    $batchData = [];
                }
            }

            // 处理剩余的数据
            if ($counter > 0) {
                // 直接插入数据库
                by::dbMaster()->createCommand()->batchInsert($mallMemberDb, array_keys($save), $batchData)->execute();
            }

            fclose($fp);
        } else {
            // 处理文件打开失败的情况
        }
    }



    //用户积分数据统计
    public function syncMemberOrderPoint()
    {
        $redis    = by::redis();
        $orderTbs = ["`db_dreame_goods`.`t_om_2022`", "`db_dreame_goods`.`t_om_2023`"];
        $db       = by::dbMaster();

        foreach ($orderTbs as $orderTb) {
            while (1) {
                $redisKey  = 'MemberOrderPoint' . $orderTb;
                $id        = intval($redis->get($redisKey));
                $sql       = <<<SQL
        SELECT `id`,`order_no`,`user_id` from {$orderTb} WHERE `status` = 500 AND `id` > {$id} LIMIT 500;
SQL;
                $orderData = $db->createCommand($sql)->queryAll();
                $end       = end($orderData);
                $id        = $end['id'];
                if (empty($orderData)) {
                    break;
                }

                $orderData = $this->spiltArr($orderData);
                $userData  = $this->getUserOrderPrice($orderData, $db);
                $this->getOrderPoint($userData, $db);

                $redis->set($redisKey, $id, 7200);
            }

        }

    }


    public function spiltArr($orderData)
    {
        $organizedData = [];

        foreach ($orderData as $item) {
            $userIdMod = $item['user_id'] % 10;
            if (!isset($organizedData[$userIdMod])) {
                $organizedData[$userIdMod] = [];
            }

            $organizedData[$userIdMod][] = $item['order_no'];
        }

        return $organizedData;
    }


    public function getUserOrderPrice($orderData, $db): array
    {

        $userData = [];
        foreach ($orderData as $key => $orderNos) {
            $userOrderTb = by::Ouser()::tbName($key);
            $orderNoStr  = implode("','", $orderNos);
            $sql         = <<<SQL
    SELECT `user_id`,SUM(`price`) AS `price` FROM {$userOrderTb} WHERE `order_no` IN ('{$orderNoStr}') GROUP BY `user_id`;
SQL;
            $userInfo    = $db->createCommand($sql)->queryAll();
            $userData    = array_merge($userData, $userInfo);
        }
        return $userData;
    }


    const LEVEL_MULTIPLE = [
        'v1' => 1,
        'v2' => 1,
        'v3' => 1.2,
        'v4' => 1.5,
        'v5' => 2,
    ];

    public function getOrderPoint($userData, $db)
    {
        $redis    = by::redis();
        $redisKey = 'totalPointNumber';

        $levelTb = "`db_dreame_log`.`t_marketing_information`";
        foreach ($userData as $data) {
            $pointNumber = $redis->get($redisKey);
            $userId      = $data['user_id'];
            $sql         = <<<SQL
        SELECT `level` FROM {$levelTb} WHERE `user_id` = {$userId};
SQL;
            $level       = $db->createCommand($sql)->queryOne();
            $level       = empty($level) ? 'v1' : $level['level'];
            $multiple    = self::LEVEL_MULTIPLE[$level];
            $point       = bcmul($multiple, $data['price']);
            $redis->set($redisKey, bcadd($point, $pointNumber), 7200);
        }
    }
    //根据订单主表 查询出每个用户已完成的订单号



}
