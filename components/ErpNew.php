<?php


namespace app\components;


use app\jobs\OmsOrderJob;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\GmainModel;
use app\modules\goods\models\OmainModel;
use app\modules\wares\models\GiftUserCardsModel;
use app\modules\main\services\OmsService;
use RedisException;
use yii\db\Exception;

class ErpNew
{
    const SHOP_CODE = YII_ENV_PROD ? "300000058" : (YII_ENV_TEST ? '300000058' :
        (YII_ENV_DEV ? '300000058':'3000000581'));    //店铺

    // 追觅微信商城店-第三方开发平台  300000058  524  国内大区-消费者运营中心-消费者运营中心-消费者运营中心-微信小程序      220009S003
    const MINI_SHOP_CODE = YII_ENV_PROD ? "300000058" : (YII_ENV_TEST ? '300000058' :
            (YII_ENV_DEV ? '300000058' : '3000000581'));    //店铺

    // 追觅超级APP-第三方开发平台      300000341  6D3  国内大区-消费者运营中心-消费者运营中心-消费者运营中心-追觅超级APP    220009S003
    const APP_SHOP_CODE = "300000341";
    // 追觅官方商城-第三方开发平台     300000342  6D4  国内大区-消费者运营中心-消费者运营中心-消费者运营中心-追觅官方商城    220009S003
    const PC_SHOP_CODE = "300000342";

    const ORDER_STATUS = [
        0        => 'WAIT_BUYER_PAY',//等待买家付款
        100      => 'TRADE_CANCELED',//付款以前，卖家或买家主动关闭交易
        300      => 'WAIT_SELLER_SEND_GOODS',//等待卖家发货
        400      => 'WAIT_BUYER_CONFIRM_GOODS',//卖家已发货
        500      => 'TRADE_FINISHED',//交易成功
    ];

    const NEED_RECORD_FUNCTIONS = [
        'addOrder','orderReturnAdd','refundPush'
    ];
    /**
     * @param $status
     * @return string
     * 获取ERP对应订单状态
     */
    private function _getErpOrderStatus($status){
        $status = intval($status);
        if($status >=  10000000 && $status<20000000){
            return 'LOCKED';
        }elseif ($status >= 20000000){
            return 'TRADE_CLOSED';
        }else{
            return self::ORDER_STATUS[$status] ?? '';
        }
    }

    const PLATFORM = 'TP';

    const REFUND_STATUS = [
        1000     => 'WAIT_SELLER_AGREE',//买家已经申请退款，等待卖家同意
        1020     => 'SELLER_REFUSE_BUYER',//卖家拒绝退款
        1010     => 'REFUND_PROCESSING',//正在退款中
        100      => 'CLOSED',//退款关闭
        20000000 => 'SUCCESS',//退款成功
    ];

    const REFUND_GOODS_STATUS = [
        1000     => 'WAIT_SELLER_AGREE',//买家已经申请退款，等待卖家同意
        1010     => 'WAIT_BUYER_RETURN_GOODS',//卖家已经同意退款，等待买家退货
        1030     => 'WAIT_SELLER_CONFIRM_GOODS',//买家已经退货，等待卖家确认收货
        1020     => 'SELLER_REFUSE_BUYER',//卖家拒绝退款
        100      => 'CLOSED',//退款关闭
        20000000 => 'SUCCESS',//退款成功
    ];

    const TYPE = [
        'fixed' => 'fixed',
        'step'  => 'step'
    ];
    protected static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    /**
     * @return ErpNew
     * */
    public static function factory()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new self();
        }

        return self::$_instance[__CLASS__];
    }


    /**
     * @param $function
     * @param $args
     * 同步ERP
     */
    public function synErp($function,$args){
       //OMS 异步
          \Yii::$app->queue->delay(10)->push(new OmsOrderJob(['function'=>$function,'args'=>$args]));
//        YII_ENV_PROD && $this->runFunc($function,$args);
    }

    public function runFunc($function,$args)
    {
        //todo 三次重试
        $m = '';
        for ($i = 1; $i <= 3; $i++) {
            list($s, $m) = $this->run($function,$args);
            if ($s) {
                break;
            }
            sleep(1);
        }

        if(in_array($function,self::NEED_RECORD_FUNCTIONS)){
            by::model('ErpAllLogModel', 'main')->saveLog($function,$args,$m,time());
        }

        if(!$s) {
            //重试也失败 加入到mysql记录
            by::model('ErpLogModel', 'main')->saveLog($function,$args,$m,time());
        }

        return [$s,$m];
    }



    public function run(string $function, array $args): array
    {
        switch ($function){
            case 'addOrder':
                return $this->$function($args['user_id'],$args['order_no']);
            case 'orderReturnAdd':
            case 'refundPush':
            return $this->$function($args['user_id'],$args['refund_no']);
            default:
                return [false,'function 不存在'];
        }
    }


    /**
     * @param $platformSource
     * @return string
     * 获取店铺code
     */
    private function getShopCode($platformSource): string
    {
        $platformSourceConfig = CUtil::getConfig('platformSource', 'common', MAIN_MODULE) ?? [];
        $miniProgram          = [
                $platformSourceConfig['a_1643178000'],
                $platformSourceConfig['i_1643178026'],
                $platformSourceConfig['a_1712037350'],
                $platformSourceConfig['i_1712037455']
        ];
        $app                  = [
                $platformSourceConfig['a_1664246268'],
                $platformSourceConfig['i_1666147923'],
        ];

        $pc = [
                $platformSourceConfig['p_1712037068'],
                $platformSourceConfig['p_1744612441'],
        ];

        if (in_array($platformSource, $miniProgram)) {
            return self::MINI_SHOP_CODE; // 小程序
        } elseif (in_array($platformSource, $app)) {
            return self::APP_SHOP_CODE; // APP
        } elseif (in_array($platformSource, $pc)) {
            return self::PC_SHOP_CODE; // PC
        } else {
            return self::MINI_SHOP_CODE; //默认
        }
    }
    /**
     * @throws Exception
     * A001-trade订单推送 adaptor.trade.push
     */
    public function addOrder($user_id, $order_no): array
    {
        $order_info     = by::Ouser()->CommPackageInfo($user_id, $order_no,false,false,true,false,false);
        if(empty($order_info['address'])){
            return [false,'订单地址不存在！'];
        }
        $sellerMemo = '';
        $user_order_type = $order_info['user_order_type'];
        if($user_order_type == by::Omain()::USER_ORDER_TYPE['BAT']){
            $sellerMemo = '先试后买订单;订单号:'.$order_no.';';
        }

        // 改到下面计算，因为不推送的商品不算在里面
        // $num            = array_column($order_info['goods'], 'num');
        // $num            = intval(array_sum($num));
        $is_send        = $order_info['goods'][0]['is_send'] ?? 1;
        if(empty($is_send)) return [true,'OK'];

        $service_type   = 'adaptor.trade.push';

        //TODO 订单未支付时显示的实际金额要加上定金金额，其他状态外面已经加好
        if($order_info['status'] < by::Omain()::ORDER_STATUS['WAIT_SEND']){
            $order_info['real_price'] = bcadd($order_info['real_price']??0,  $order_info['deposit_price']??0, 2);
        }

        // 查询支付流水
        $pay_log = by::model('OPayModel', 'goods')->GetOneInfo($order_no);
        $pay_type = $pay_log['pay_type'] ?? '';
        if (OmainModel::PAY_BY_ALIPAY == $pay_type) {
            $pay_type = 'alipay';
        } else {
            $pay_type = 'weixin';
        }

        // 获取 shop_code
        $platformSource = $order_info['platform_source'] ?? 0;
        $shopCode = $this->getShopCode($platformSource);

        $oprice         = bcadd($order_info['oprice'], $order_info['fprice'], 2);
        $data = [
            'created'          => date('Y-m-d H:i:s', $order_info['ctime']),
            'tid'              => $order_info['order_no'],
            'platform'         => self::PLATFORM,
            'shopCode'         => $shopCode,
            'totalFee'         => $order_info['oprice'],
            'discountFee'      => $order_info['dprice'] ?? 0,
            'payment'          => $order_info['real_price'] ?? 0,
            'payNo'            => $pay_log['tid'] ?? '',
            'payType'          => $pay_type,
            'payStatus'        => 2,
            'postFee'          => $order_info['fprice'] ?? 0,
            'receiverName'     => $order_info['address']['nick'] ?? '',
            'receiverState'    => $order_info['address']['province'] ?? '',
            'receiverCity'     => $order_info['address']['city']  ?? '',
            'receiverDistrict' => $order_info['address']['area']  ?? '',
            'receiverAddress'  => $order_info['address']['detail']  ?? '',
            'receiverMobile'   => $order_info['address']['phone']  ?? '',
            'buyerMessage'     => $order_info['note'] ?? '',
            'status'           => $this->_getErpOrderStatus($order_info['status']??''),
            'type'             => self::TYPE['fixed'],
            'sellerMemo'       => $sellerMemo,
            'otherDiscountFee' => 0,
            'isCod'            => 0,
            // 'num'              => $num, // 改到下面计算
            'orderExtType'     => 'NORMAL',
            'orderExtTypeExt'  => 7,
        ];
        $items          = [];
        $num = 0;
        foreach ($order_info['goods'] as $key=>$goods) {
            // 是否自动发货，0=否(不推)，1=是（推e3）
            $is_auto_delivery = by::Gmain()->isAutoDelivery($goods['gid'] ?? 0);
            if (empty($is_auto_delivery)) {
                continue;
            }
            $num = bcadd($num, (string) ($goods['num'] ?? 0));
            // 若传递的sku是套餐sn 则需要获取到套餐sku
            $tc_data = by::GtcModel()->getListBySn($goods['sku']);
            $price   = bcadd($goods['price'], $goods['deposit_price'] ?? 0, 2);

            if (empty($tc_data)) {
                $items[] = [
                        'rowIndex'        => $key,
                        'oid'             => md5($order_info['order_no'] . '-' . $goods['sku']),
                        'outerSkuId'      => $goods['sku'],
                        'price'           => round($goods['oprice'] / $goods['num'], 2),
                        'num'             => $goods['num'],
                        'totalFee'        => $goods['oprice'],
                        'payment'         => $price,
                        'partMjzDiscount' => bcsub($goods['oprice'], $goods['price'], 2), // 折扣金额
                        'discountFee'     => bcsub($goods['oprice'], $goods['price'], 2), // 折扣金额
                        'divideOrderFee'  => $price,                                      // 实付金额
                ];
            } else {
                // 防止套餐下有多个sku 循环一次
                $tc_sku_list = array_unique(array_column($tc_data, 'tc_sku'));
                foreach ($tc_sku_list as $keyT => $tc_sku) {
                    $items[] = [
                            'rowIndex'        => intval($key) + intval($keyT),
                            'oid'             => md5($order_info['order_no'] . '-' . $tc_sku),
                            'outerSkuId'      => $tc_sku,
                            'price'           => round($goods['oprice'] / $goods['num'], 2),
                            'num'             => $goods['num'],
                            'totalFee'        => $goods['oprice'],
                            'payment'         => $price,
                            'partMjzDiscount' => bcsub($goods['oprice'], $goods['price'], 2), // 折扣金额
                            'discountFee'     => bcsub($goods['oprice'], $goods['price'], 2), // 折扣金额
                            'divideOrderFee'  => $price,                                      // 实付金额
                    ];
                }
            }
        }
        
        if (empty($items)) {
            return [true, '非品牌商品订单不推送E3+'];
        }
        
        $data['num'] = $num;
        $data['items']             = $items;
        //todo 优惠信息
        $promotions = [];
        if ($order_info['cprice'] > 0) {
            $promotions[] = [
                'promotionName' => '优惠券',
                'discountFee'   => $order_info['cprice'],
                'promotionDesc' => '优惠券抵扣',
                'promotionType' => 'shop_discount'
            ];
        }

        // todo 积分
        if ($order_info['coin_price'] > 0) {
            $promotions[] = [
                'promotion_name' => '积分',
                'discountFee'    => $order_info['coin_price'],
                'promotionDesc'  => '积分抵扣',
                'promotionType'  => 'shop_discount'
            ];
        }

        //todo 膨胀金额
        if ($order_info['exprice'] > 0) {
            $promotions[] = [
                'promotion_name' => '膨胀',
                'discount_fee'   => bcsub($order_info['exprice'], $order_info['deposit_price'] ?? 0, 2),
                'promotion_desc' => '膨胀抵扣',
                'promotionType'  => 'shop_discount'
            ];
        }

        // todo 消费券
        if (($order_info['consume_price'] ?? 0) > 0) {
            $promotions[] = [
                'promotion_name' => '消费券抵扣',
                'discountFee'    => $order_info['consume_price'],
                'promotionDesc'  => '消费券抵扣',
                'promotionType'  => 'shop_discount'
            ];
        }

        $acdeprice = $order_info['acdeprice'] ?? 0;
        if($acdeprice >0){
            $promotions[] = [
                'promotion_name' => '活动抵扣',
                'discount_fee'   => $acdeprice,
                'promotion_desc' => '活动抵扣',
                'promotionType'  => 'shop_discount'
            ];
        }

        //todo 礼品卡金额
        if ($order_info['gift_card_price'] > 0) {
            $promotions[] = [
                'promotion_name' => '礼品卡',
                'discount_fee'   => $order_info['gift_card_price'],
                'promotion_desc' => '礼品卡抵扣',
                'promotionType'  => 'shop_discount'
            ];
        }

        // 国补 自补
        if ($order_info['subsidy_price'] > 0) {
            $promotions[] = [
                    'promotion_name' => '补贴',
                    'discount_fee'   => $order_info['subsidy_price'],
                    'promotion_desc' => '补贴抵扣',
                    'promotionType'  => 'shop_discount'
            ];
        }

        $data['promotions'] = $promotions;

        //发票 0=不开发票、1=增值税普通发票（纸质发票）、2=增值税专用发票（纸质发票）、3=电子发票
        $data['invoice']['invoiceType'] = 0;

        return $this->_request($service_type, $data);
    }


    /**
     * @param $user_id
     * @param $refund_no
     * @return array
     * 退款申请
     * @throws Exception
     * A002-refund退款申请推送
     */
    public function refundPush($user_id, $refund_no): array
    {
        $service_type   = 'adaptor.refund.push';
        $r_info         = by::Orefund()->CommPackageInfo($user_id, $refund_no, true, true);

        // 获取 shop_code
        $platformSource = $r_info['platform_source'] ?? 0;
        $shopCode       = $this->getShopCode($platformSource);

        $data = [
                'refundId'  => $r_info['refund_no'],
                'platform'  => self::PLATFORM,
                'shopCode'  => $shopCode,
                'tid'       => $r_info['order_no'],
                'refundFee' => $r_info['real_price'],
                'created'   => date('Y-m-d H:i:s', $r_info['ctime'] ?? 0),
                'status'    => self::REFUND_STATUS[$r_info['status']] ?? '',
        ];
        $stdRefundItems = [];
        foreach ($r_info['goods'] as $key => $v) {
            $price            = bcadd($v['price'], $v['deposit_price'] ?? 0, 2);
            $stdRefundItems[] = [
                'rowIndex'   => $key,
                'outerSkuId' => $v['sku'],
                'oid'        => md5($r_info['order_no'] . '-' . $v['sku']),
                'reason'     => $r_info['r_msg'],
                'refundFee'  => $price,
                'num'        => $v['num']
            ];
        }
        $data['stdRefundItems'] = $stdRefundItems;

        return $this->_request($service_type, $data);

    }

    /**
     * @param $user_id
     * @param $refund_no
     * @return array
     * 退货申请推送
     * @throws Exception
     * A003-return退货申请推送
     */
    public function orderReturnAdd($user_id, $refund_no): array
    {
        $service_type = 'adaptor.return.push';
        $r_info       = by::Orefund()->CommPackageInfo($user_id, $refund_no, true, true);

        // 获取 shop_code
        $platformSource = $r_info['platform_source'] ?? 0;
        $shopCode       = $this->getShopCode($platformSource);

        //todo 判断用户是否填写物流信息
        $r_info['status'] = CUtil::uint($r_info['status']);
        if($r_info['status'] == 1000 &&  $r_info['mail_no']){
            $r_info['status'] = 1030;
        }

        $data = [
            'refundId'    => $r_info['refund_no'],
            'created'     => date('Y-m-d H:i:s', $r_info['ctime'] ?? 0),
            'platform'    => self::PLATFORM,
            'shopCode'    => $shopCode,
            'companyCode' => $r_info['express_code'],
            'companyName' => $r_info['express_name'],
            'sid'         => $r_info['mail_no'],
            'trDesc'      => $r_info['a_reason'],
            'refundFee'   => $r_info['real_price'],
            'tid'         => $r_info['order_no'],
            'status'      => self::REFUND_GOODS_STATUS[$r_info['status']] ?? '',
        ];


        $items = [];
        foreach ($r_info['goods'] as $k => $v) {
            $price   = bcadd($v['price'], $v['deposit_price'] ?? 0, 2);
            $items[] = [
                'rowIndex'   => $k,
                'outerSkuId' => $v['sku'],
                'oid'        => md5($r_info['order_no'] . '-' . $v['sku']),
                'reason'     => $r_info['r_msg'],
                'refundFee'  => $price,
                'num'        => $v['num']
            ];
        }
        $data['items'] = $items;

        return $this->_request($service_type, $data);
    }

    /**
     * @param $gid
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function pushGoods($gid): array
    {
        $service_type = 'adaptor.item.push';
        $mGmain       = by::Gmain();
        $mGtype0      = by::Gtype0();

        // 是否自动发货，0=否(不推)，1=是（推e3）
        $is_auto_delivery = by::Gmain()->isAutoDelivery($gid);
        
        if (empty($is_auto_delivery)) {
            return [false, '该商品不支持自动发货（推送E3+）'];
        }

        // 获取商品主信息
        $aMain = $mGmain->GetOneByGid($gid);
        if (empty($aMain)) {
            return [false, '数据不存在'];
        }

        // 验证商品类型
        // if ($aMain['type'] !== $mGmain::TYPE['COMMON']) {
        if (! in_array($aMain['type'], [GmainModel::TYPE['COMMON'], GmainModel::TYPE['YOUXUAN'], GmainModel::TYPE['YANXUAN']])) {
            return [false, 'type不存在'];
        }

        // 获取基础商品数据
        $aData = $mGtype0->GetOneBaseByGid($gid);
        if (empty($aData)) {
            return [false, 'gtype不存在'];
        }

        // 定义三个店铺代码
        $shopCodes = [self::MINI_SHOP_CODE, self::APP_SHOP_CODE, self::PC_SHOP_CODE];

        // 公共推送数据基础结构
        $basePushData = [
                'platform'      => self::PLATFORM,
                'title'         => $aMain['name'] ?? '',
                'approveStatus' => 'onsale',
        ];

        // 遍历所有店铺进行推送
        foreach ($shopCodes as $shopCode) {
            if ($aData['atype'] === $mGtype0::ATYPE['SPEC']) {
                // 处理单规格商品
                $pushData = array_merge($basePushData, [
                        'numIid'     => $gid,
                        'skuId'      => $aMain['sku'] ?? '',
                        'outerId'    => $aMain['sku'] ?? '',
                        'outerSkuId' => $aMain['sku'] ?? '',
                        'shopCode'   => $shopCode,
                        'price'      => $aData['price'] ?? 0,
                ]);

                $result = $this->_request($service_type, $pushData);
                if (!$result[0]) {
                    return [false, $result[1]];
                }
            } else {
                // 处理多规格商品
                $aSpecs = by::Gspecs()->GetListByGid($gid, 0, true);

                foreach ($aSpecs as $spec) {
                    $pushData = array_merge($basePushData, [
                            'numIid'     => $gid . '-' . $spec['id'],
                            'skuId'      => $spec['sku'] ?? '',
                            'outerId'    => $spec['sku'] ?? '',
                            'outerSkuId' => $spec['sku'] ?? '',
                            'shopCode'   => $shopCode,
                            'price'      => $spec['price'] ?? 0,
                            'isDelete'   => $spec['is_del'] ?? 0,
                    ]);

                    $result = $this->_request($service_type, $pushData);
                    if (!$result[0]) {
                        continue; // 单个规格推送失败，继续处理其他规格
                    }
                }
            }
        }

        return [true, '推送成功！'];
    }


    /**
     * @param $gid
     * @return array
     * @throws Exception
     *
     */
    public function pushWaresGoods($gid): array
    {
        $service_type = 'adaptor.item.push';
        $goodsMain    = by::GoodsMainModel();
        $specsModel   = by::GoodsSpecsModel();
        $aMain        = $goodsMain->GetOneByGid($gid);
        if (empty($aMain)) {
            return [false, '数据不存在'];
        }

        $shopCodes = [
                self::MINI_SHOP_CODE, self::APP_SHOP_CODE, self::PC_SHOP_CODE
        ];
        // 三个店铺都要推送
        foreach ($shopCodes as $code) {
            switch ($aMain['atype']) {
                case $goodsMain::ATYPE['SPEC']:
                    $pushData = [
                            'numIid'        => $gid,
                            'skuId'         => $aMain['sku'] ?? '',
                            'outerId'       => $aMain['sku'] ?? '',
                            'outerSkuId'    => $aMain['sku'] ?? '',
                            'title'         => $aMain['name'] ?? '',
                            'platform'      => self::PLATFORM,
                            'shopCode'      => $code,
                            'approveStatus' => 'onsale',//(intval($aMain['status']) == 1) ? 'onsale' : 'instock',
                    ];

                    list($s, $m) = $this->_request($service_type, $pushData);
                    if (!$s) {
                        return [false, $m];
                    }
                    break;
                default:
                    $aSpecs = $specsModel->GetSpecsListByGid($gid, true, true);
                    foreach ($aSpecs as $spec) {
                        $pushData = [
                                'numIid'        => $gid . '-' . $spec['id'],
                                'skuId'         => $spec['sku'] ?? '',
                                'outerId'       => $spec['sku'] ?? '',
                                'outerSkuId'    => $spec['sku'] ?? '',
                                'title'         => $gid . '-' . $spec['id'] . '-' . $aMain['name'] ?? '',
                                'platform'      => self::PLATFORM,
                                'shopCode'      => $code,
                                'approveStatus' => 'onsale',             //(intval($aMain['status']) == 1) ? 'onsale' : 'instock',
                                'isDelete'      => $spec['is_del'] ?? 0, //是否删除  0：不删除  1：删除
                        ];
                        list($s, $m) = $this->_request($service_type, $pushData);
                        if (!$s) {
                            continue;
                        }
                    }
            }
        }
        return [true, '推送成功！'];
    }

    /**
     * @param $service_type
     * @param array $data
     * @return array
     */
    protected function _request($service_type, array $data = [])
    {
        $body = [
            'method' => $service_type,
            'data'   => json_encode($data, 320)
        ];

        list($s, $ret) = by::oms()->pushWxData($body);

        return [$s, $ret];
    }

}
