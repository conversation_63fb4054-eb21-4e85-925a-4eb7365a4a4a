<?php

declare(strict_types=1);

namespace app\components;

use app\models\by;

/**
 * 基于Redis分布式锁
 */
class LockRedis
{
    private $redis;

    public function __construct()
    {
        $this->redis = by::redis();
    }

    /**
     * 添加锁
     * @param string $key 锁键名
     * @param mixed $value 锁键值
     * @param int $expired 锁过期时间
     * @return bool
     */
    public function lock(string $key, $value = 1, int $expired = 10): bool
    {
        return $this->redis->set($key, $value, ['nx', 'ex' => $expired]);
    }

    /**
     * 释放锁
     * @param string $key 锁键名
     * @param mixed $value 锁键值
     * @return bool
     */
    public function freed(string $key, $value = 1): bool
    {
        $luaScript = <<<'Lua'
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
Lua;

        return $this->redis->eval($luaScript, [$key, $value], 1) > 0;
    }
}
