<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;

use app\models\by;
use app\models\CUtil;
use Faker\Provider\Uuid;
use RedisException;
use yii\db\Exception;

class ReissueIntegral
{
    private static $_instance = [];
    private $dreame_home_domain;

    const URL = '/dreame-point-center/syncmall/data';
    const LIMIT_NU = 500;

    const INTEGRAL_TYPE = [
        'BUY_MAIN_MACHINE' => 'mall/dreame/buy_main_machine',//购买主机
        'BUY_MAIN_PARTS' => 'mall/dreame/buy_parts',//购买配件
        'REG_SN' => 'mall/wx/reg_sn',//产品注册
        'INVITE_REG' => 'mall/dreame/invite_reg',//邀请好友注册
        'INVITE_BUY' => 'mall/dreame/invite_buy',//邀请好友购买
        'PHONE' => 'oper/user_event_app/grow_distribute',//+86手机号
    ];

    private function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return ReissueIntegral
     */
    public static function factory(): ReissueIntegral
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    private function __scorePushList(): string
    {
        return AppCRedisKeys::reissueIntegralPushList();
    }

    /**
     * @param  $args
     * @param int $time
     * @return array
     * @throws RedisException
     * 插入队列
     */
    private function pushRedisList($args, int $time = 0): array
    {
        $r_key = $this->__scorePushList();
        by::redis('core')->rPush($r_key, is_array($args) ?
            json_encode(
                $args
            ) : $args
        );
        return [true, 'ok'];
    }

    /**
     * @param $function
     * @param array $body
     * @param string $method
     * @return array
     * 封装统一请求
     */
    private function __request($function, array $body = [], string $method = 'POST')
    {
        $url = $this->dreame_home_domain . self::URL;
        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Authorization:Basic ZHJlYW1lX21hbGw6NEw2cmY1U29CZXN4bE1nYw==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ];
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body, 320), $header, $method,60);
        //如果返回的不是成功加入到重试队列中
        if ($ret['code'] != 0 || $ret['success'] === false) {
            $this->pushRedisList($body);
        }
        // CUtil::debug("httpcode:{$httpCode}|err:{$err}|data：" . json_encode($body, 320) . " | ret:" . json_encode($ret), "reissue_integral.{$function}");
        CUtil::setLogMsg(
            "reissue_integral.{$function}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
        if ($httpCode != 200) {
            $this->retry('all', $body);
            return [true, []];
        }
        if (!$status) {
            $this->retry('all', $body);
            return [false, $err];
        }
        return [$ret['code'] === 0, $ret];
    }

    public function run(string $function, array $args = [], int $time = 0)
    {
        switch ($function) {
            case 'pushOrderGoods':

            case 'inviteReg':

            case 'inviteBuy':

            case 'regSn':

            case 'phone':

            case 'retrySend':
                return $this->$function();
            default:
                return [false, 'function 不存在'];
        }
    }

    /**
     * @throws Exception
     * 同步 购买主机/配件
     */
    public function pushOrderGoods()
    {
        $redis = by::redis('core');
        $redisKey = 'pushOrderGoods2023';
        $finish = by::Omain()::ORDER_STATUS['FINISHED'];
        $id = CUtil::uint($redis->get($redisKey));
        $where = "`order`.`status` = {$finish}";
        $where .= " AND `main`.`user_id` > 0";
        $db = by::dbMaster();
        //分表可能跨年
        $this_year = date("Y");
//        $years = [2022, $this_year];
        $years = [$this_year];
        $years = array_unique($years);
        foreach ($years as $year) {
            if ($years < 2022) {
                continue;
            }
            //确认分表
            $date = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime = strtotime($date);
            //订单主表t_om_2022/2023
            $tb_main = by::Omain()::tbName($ctime);
            //用户主表t_users_main
            $tb_users_main = by::users()::userMainTb();
            while (1) {
                $sql = "SELECT `order`.`id`,`order`.`order_no`,`order`.`user_id`,`order`.`ctime` FROM {$tb_main} as `order` INNER JOIN {$tb_users_main} as `main`
                        ON `order`.`user_id` = `main`.`user_id` WHERE {$where} AND `order`.`id`>:id  ORDER BY `order`.`id` ASC LIMIT ".self::LIMIT_NU;
                $list = $db->createCommand($sql, [':id' => $id])->queryAll();
                if (empty($list)) {
                    return 'pushOrderGoods-over';
                }
                $end = end($list);
                $id = $end['id'];
                $redis->set($redisKey, $id, 7200);
                CUtil::debug('push_order_goods|' . $id, 'syn.log');
                $userIds = array_column($list, 'user_id') ?? [];
                $uidArr = $this->getUidsByUserIds($userIds) ?? [];
                $result = [];
                foreach ($list as  $key=>$v) {
                    //uid
                    $uid = $uidArr[$v['user_id']] ?? '';
                    if (empty($uid)) {
                        CUtil::debug('data:' . json_encode($v, 320) . '无uid', 'warn.score_push.pushOrderGoods');
                        continue;
                    }
                    //订单号
                    $order_no = $v['order_no'] ?? '';
                    //创建时间
                    $create_time = date('Y-m-d H:i:s') ?? '';
                    $orderInfo = by::Ouser()->CommPackageInfo($v['user_id'], $v['order_no']);
                    $data = [];
                    foreach ($orderInfo['goods'] as $k => $value){
                        if (empty($value['sku']) || empty($value['gid'])) {
                            CUtil::debug('无sku或gid', 'warn.score_push.pushOrderGoods');
                            continue;
                        }
                        $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
                        $data['sign'] = sha1($order_no . $uid . $value['sku'] . $create_time);
                        $data['data']['uid'] = $uid;
                        $data['data']['pay'] = $value['price'];
                        $data['data']['sku'] = $value['sku'];
                        $data['data']['order_sn'] = $order_no;
                        $data['data']['create_time'] = $create_time;
                        $tids = array_column($tidArr, 'tid') ?? [];
                        if (in_array(20, $tids)) {
                            //配件
                            $data['type'] = self::INTEGRAL_TYPE['BUY_MAIN_PARTS'] ?? '';
                        } else {
                            //主机
                            $data['type'] = self::INTEGRAL_TYPE['BUY_MAIN_MACHINE'] ?? '';
                        }
                        $result[] = $data;
                    }
                }
                list($status, $ret) = $this->__request('pushOrderGoods', $result);
                //如果失败加入队列
                if ($status && !empty($ret)) {
                    $this->retry('portion', $ret['data'] ?? []);
                }

            }
        }
        return 'pushOrderGoods';
    }

    /**
     * @return string
     * @throws Exception
     * @throws RedisException
     */
    public function inviteReg()
    {
        $redis = by::redis('core');
        $tb_user_recommend = by::userRecommend()::tbName();
        $tb_main = by::users()::userMainTb();
        $redisKey = 'inviteReg';
        while (1) {
            $id = CUtil::uint($redis->get($redisKey));
            $where = " `main`.`user_id` > 0";
            $sql = "SELECT  `recommend`.`id`,`recommend`.`user_id`,`recommend`.`r_id`,`recommend`.`ctime` FROM {$tb_user_recommend} as `recommend` INNER JOIN {$tb_main} as `main` ON `recommend`.`user_id` = `main`.`user_id` where{$where} AND `recommend`.`id`>:id  ORDER BY `recommend`.`id` ASC LIMIT ".self::LIMIT_NU;
            $inviteInfo = by::dbMaster()->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($inviteInfo)) {
                return 'inviteReg-over';
            }
            $end = end($inviteInfo);
            $id = $end['id'];
            $redis->set($redisKey, $id, 7200);
            CUtil::debug('invite_reg|' . $id, 'syn.log');
            //被邀请人user_id
            $userIds = array_column($inviteInfo, 'user_id') ?? [];
            //邀请人user_id
            $rIds = array_column($inviteInfo, 'r_id') ?? [];
            //被邀请人uid
            $beInvitedUids = $this->getUidsByUserIds($userIds) ?? [];
            //邀请人uid
            $inviteUids = $this->getUidsByUserIds($rIds) ?? [];
            $data = [];
            foreach ($inviteInfo as $k => $value) {
                $uid = $inviteUids[$value['r_id']] ?? '';
                if (empty($uid)) {
                    CUtil::debug('data:' . json_encode($value, 320) . '无uid', 'warn.score_push.inviteReg');
                    continue;
                }
                $invite_uid = $beInvitedUids[$value['user_id']] ?? '';
                $create_time = date('Y-m-d H:i:s', $value['ctime']) ?? '';
                $data[$k]['type'] = self::INTEGRAL_TYPE['INVITE_REG'] ?? '';
                $data[$k]['sign'] = sha1($uid . $invite_uid . $create_time);
                $data[$k]['data']['uid'] = $uid;
                $data[$k]['data']['invite_uid'] = $invite_uid;
                $data[$k]['data']['create_time'] = $create_time;
            }
            list($status, $ret) = $this->__request('inviteReg', array_values($data));
            //如果失败加入队列
            if ($status && !empty($ret)) {
                $this->retry('portion', $ret['data'] ?? []);
            }
        }
    }


    /**
     * @throws Exception
     * 邀请好友购买
     */
    public function inviteBuy()
    {
        $redis = by::redis('core');
        $finish = by::Omain()::ORDER_STATUS['FINISHED'];
        $redisKey = 'inviteBuy2023';
        $db = by::dbMaster();
        $tb_main = by::users()::userMainTb();
        //分表可能跨年
        $this_year = date("Y");
//        $years = [2022, $this_year];
        $years = [$this_year];
        $years = array_unique($years);
        foreach ($years as $year) {
            if ($years < 2022) {
                continue;
            }
            //确认分表
            $date = $year == $this_year ? date("{$year}md") : date("{$year}0101");
            $ctime = strtotime($date);
            $o_source_r = by::osourceR()::tbName($ctime);
            while (1) {
                $id = CUtil::uint($redis->get($redisKey));
                $where = "`source`.`status` = {$finish}";
                $where .= " AND `main`.`user_id` > 0";
                //推荐订单表 o_source_r_2022/2023
                $sql = "SELECT `source`.`id`,`source`.`user_id`,`source`.`r_id`,`source`.`order_no`,`source`.`ctime` FROM {$o_source_r} as `source` INNER JOIN {$tb_main} as `main` ON `source`.`r_id` = `main`.`user_id` WHERE {$where} AND `source`.`id`>:id  ORDER BY `source`.`id` ASC LIMIT ".self::LIMIT_NU;
                $sourceInfo = $db->createCommand($sql, [':id' => $id])->queryAll();
                if (empty($sourceInfo)) {
                    return 'inviteBuy-over';
                }
                $end = end($sourceInfo);
                $id = $end['id'];
                $redis->set($redisKey, $id, 7200);
                CUtil::debug('invite_buy|' . $id, 'syn.log');
                //邀请人user_id
                $rIds = array_column($sourceInfo, 'r_id') ?? [];
                //被邀请人user_id
                $userIds = array_column($sourceInfo, 'user_id') ?? [];
                //邀请人uid
                $inviteUids = $this->getUidsByUserIds($rIds) ?? [];
                //被邀请人uid
                $beInvitedUids = $this->getUidsByUserIds($userIds) ?? [];
                $result = [];
                foreach ($sourceInfo as $k => $value) {
                    //邀请人id
                    $uid = $inviteUids[$value['r_id']] ?? '';
                    if (empty($uid)) {
                        CUtil::debug('data:' . json_encode($value, 320) . '无uid', 'warn.score_push.inviteBuy');
                        continue;
                    }
                    //被邀请人id
                    $invite_uid = $beInvitedUids[$value['user_id']] ?? '';
                    $order_no = $value['order_no'] ?? '';
                    $create_time = date('Y-m-d H:i:s', $value['ctime']) ?? '';
                    $orderInfo = by::Ouser()->CommPackageInfo($value['user_id'], $value['order_no']);
                    $data = [];
                    foreach ($orderInfo['goods'] as $key => $v){
                        $tidArr = by::Gtag()->GetListByGid($v['gid']) ?? [];
                        $tids = array_column($tidArr, 'tid') ?? [];
                        if (in_array(20, $tids)) {
                            //配件
                            $data['data']['productType'] = 'parts';
                        } else {
                            //主机
                            $data['data']['productType'] = 'machine';
                        }
                        $data['type'] = self::INTEGRAL_TYPE['INVITE_BUY'] ?? '';
                        $data['sign'] = sha1($uid . $invite_uid . $order_no . $create_time);
                        $data['data']['uid'] = $uid;
                        $data['data']['invite_uid'] = $invite_uid;
                        $data['data']['order_no'] = $order_no;
                        $data['data']['create_time'] = $create_time;
                        $result[] = $data;
                    }
                }
                list($status, $ret) = $this->__request('inviteBuy', array_values($result));
                //如果失败加入队列
                if ($status && !empty($ret)) {
                    $this->retry('portion', $ret['data'] ?? []);
                }

            }
        }
        return 'inviteBuy';
    }


    /**
     * @return string
     * @throws Exception
     * @throws RedisException
     */
    public function regSn()
    {
        $redis = by::redis('core');
        $tb_sn_reg = by::model('SnRegModel', 'main')::getTable();
        $tb_main = by::users()::userMainTb();
        $redisKey = "regSnPush";
        $db = by::dbMaster();
        while (1) {
            $id = CUtil::uint($redis->get($redisKey));
            $where = " `main`.`user_id` > 0";
            $sql = "SELECT `sn_reg`.`id`,`sn_reg`.`sn`,`sn_reg`.`user_id`,`sn_reg`.ctime FROM {$tb_sn_reg} as `sn_reg` INNER JOIN {$tb_main} as `main` ON `sn_reg`.`user_id` = `main`.`user_id` WHERE {$where} AND `sn_reg`.`id`>:id  ORDER BY `sn_reg`.`id` ASC LIMIT ".self::LIMIT_NU;
            $snRegInfo = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($snRegInfo)) {
                return 'regSn-over';
            }
            $end = end($snRegInfo);
            $id = $end['id'];
            $redis->set($redisKey, $id, 7200);
            CUtil::debug('reg_sn|' . $id, 'syn.log');
            //用户user_id
            $userIds = array_column($snRegInfo, 'user_id') ?? [];
            //用户uid
            $userUid = $this->getUidsByUserIds($userIds) ?? [];
            $data = [];
            foreach ($snRegInfo as $k => $value) {
                $uid = $userUid[$value['user_id']] ?? '';
                if (empty($uid)) {
                    CUtil::debug('data:' . json_encode($value, 320) . '无uid', 'warn.score_push.regSn');
                    continue;
                }
                $sn_no = $value['sn'] ?? '';
                $create_time = date('Y-m-d H:i:s', $value['ctime']) ?? '';
                $data[$k]['type'] = self::INTEGRAL_TYPE['REG_SN'] ?? '';
                $data[$k]['sign'] = sha1($sn_no . $uid . $create_time);
                $data[$k]['data']['sn_no'] = $sn_no;
                $data[$k]['data']['uid'] = $uid;
                $data[$k]['data']['create_time'] = $create_time;
            }
            list($status, $ret) = $this->__request('regSn', array_values($data));
            //如果失败加入队列
            if ($status && !empty($ret)) {
                $this->retry('portion', $ret['data'] ?? []);
            }
        }
    }


    /**
     * @throws Exception
     * @throws RedisException
     * +86 手机号积分同步
     */
    public function phone()
    {
        $redis = by::redis('core');
        $tb_main = by::users()::userMainTb();
        $tb_phone = by::Phone()::tbName();
        $redisKey = 'phoneIntegral';
        $id = CUtil::uint($redis->get($redisKey));
        $where = " `main`.`user_id` > 0";
        while (1) {
            $sql = "SELECT `phone`.`id`,`phone`.`user_id`,`phone`.`ctime` FROM {$tb_phone} as `phone`  INNER JOIN  {$tb_main} as `main` ON `phone`.`user_id`  = `main`.`user_id` WHERE {$where} AND `phone`.`id`>:id  ORDER BY `phone`.`id` ASC LIMIT ".self::LIMIT_NU;
            $phoneInfo = by::dbMaster()->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($phoneInfo)) {
               return 'phone-over';
            }
            $end = end($phoneInfo);
            $id = $end['id'];
            CUtil::debug('phone|' . $id, 'syn.log');
            $redis->set($redisKey, $id, 7200);
            $userIds = array_column($phoneInfo, 'user_id');
            $uids = $this->getUidsByUserIds($userIds) ?? [];
            $data = [];
            foreach ($phoneInfo as $k => $value) {
                $uid = $uids[$value['user_id']] ?? '';
                if (empty($uid)) {
                    CUtil::debug('data:' . json_encode($value, 320) . '无uid', 'warn.score_push.phone');
                    continue;
                }
                $create_time = date('Y-m-d H:i:s', $value['ctime']) ?? '';
                $data[$k]['type'] = self::INTEGRAL_TYPE['PHONE'];
                $data[$k]['sign'] = sha1($uid . $create_time);
                $data[$k]['data']['uid'] = $uid;
                $data[$k]['data']['create_time'] = $create_time;
            }
            list($status, $ret) = $this->__request('phone', array_values($data));
            //如果失败加入队列
            if ($status && !empty($ret)) {
                $this->retry('portion', $ret['data'] ?? []);
            }
        }
    }

    /**
     *
     * @param $userIds
     * @return array
     * @throws Exception
     * 通过user_id获取uid
     */
    private function getUidsByUserIds($userIds)
    {
        $userIds = implode(',', $userIds);
        $t_user_mall = by::usersMall()::tbName();
        $t_phone = by::Phone()::tbName();
        $sql = "SELECT `phone`.`user_id`,`mall`.`uid` FROM {$t_user_mall} as `mall` INNER JOIN {$t_phone} as `phone` ON `mall`.`id` = `phone`.`mall_id` WHERE `phone`.`user_id` IN ({$userIds})";
        $uids = by::dbMaster()->createCommand($sql)->queryAll();
        //user_id可能会重复 ,array_column自动覆盖数据,拿出来的一定是最新的
        $uidArray = array_column($uids, 'uid', 'user_id');
        return $uidArray ?? [];
    }

    /**
     * @param array $data
     * @throws RedisException
     * 加入重试队列
     */
    private function retry(string $type = 'all', array $data = [])
    {
        switch ($type) {
            //判断是否全部失败
            case 'all':
                    $this->pushRedisList($data);
                break;
            case 'portion':
                $retryData = [];
                foreach ($data as $k=>$v) {
                    if ($v['sucess'] != 1) {
                        $retryData[$k][] = $v;
                    }
                }
                if (!empty($retryData)){
                    $this->pushRedisList(array_values($retryData));
                }
                break;
            default:break;
        }
        return true;
    }

    /**
     * @throws RedisException
     * 重试返回code非0的错误信息
     */
    public function retrySend()
    {
        $redis = by::redis('core');
        $r_key = $this->__scorePushList();
        while (true){
            $data = json_decode($redis->rPop($r_key),true);
            if (empty($data)){
                break;
            }
            list($status, $ret) = $this->__request('retry', $data);
            //如果失败加入队列
            if ($status && !empty($ret)) {
                $this->retry('portion', $ret['data'] ?? []);
            }
        }

    }


    public function _delRedisKey()
    {
        $redis = by::redis('core');
        //购买主机配件
        $pushOrderGoodsKey = 'pushOrderGoods';
        //邀请注册
        $inviteRegKey = 'inviteReg';
        //邀请购买
        $inviteBuyKey = 'inviteBuy';
        //产品注册
        $regSnPushKey = 'regSnPush';
        //+86手机号
        $phoneIntegralKey = 'phoneIntegral';
        $redis->del($pushOrderGoodsKey,$inviteBuyKey,$inviteRegKey,$regSnPushKey,$phoneIntegralKey);
        exit('_delRedisKey');
    }


    public function sendMessage()
    {
//        $this->inviteReg();
        $this->inviteBuy();
//        $this->regSn();
//        $this->phone();
        $this->pushOrderGoods();
        return 'success';
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function addWechat(): string
    {
        $tb       = by::WeFocus()::tableName();
        $db       = by::dbMaster();
        $redis    = by::redis();
        $redisKey = 'addWechat';
        while (1) {
            $id = CUtil::uint($redis->get($redisKey));

            $sql  = "SELECT `id`,`unionid` FROM {$tb} WHERE `id` > {$id} LIMIT " . self::LIMIT_NU;
            $data = $db->createCommand($sql)->queryAll();
            if (empty($data)) {
                return 'addWechat-over';
            }
            $end = end($data);
            $id  = $end['id'] ?? 0;
            foreach ($data as $unionId) {
                WeWork::factory()->sendAddWeChat($unionId['unionid'] ?? '');
            }
            $redis->set($redisKey, $id, 7200);
            sleep(10);
        }
    }
}
