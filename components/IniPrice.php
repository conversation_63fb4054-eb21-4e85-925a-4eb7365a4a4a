<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;

/**
 * 自定义价格
 */
class IniPrice
{
    private static $_instance;

    /**
     * @return IniPrice
     */
    public static function factory(): IniPrice
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    private function __iniPriceList($ini_id): string
    {
        $mod  = intval($ini_id) % 10;
        return AppCRedisKeys::iniPriceList($mod);
    }



    /**
     * @param int $ini_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * 插入队列
     */
    public function push(int $ini_id,string $function, array $args, int $time=0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function,$methods)){
            return [false,'方法不存在'];
        }

        if (empty($args)){
            return [false,'缺少参数'];
        }

        $r_key = $this->__iniPriceList($ini_id);

        by::redis('core')->rPush($r_key,
            json_encode(
                [
                    'function' => $function,
                    'args'     => $args,
                    'time'     => $time ?: time(),
                ]
            )
        );

        return [true,'ok'];
    }


    public function synIniPrice($index){
        try {
            $redis_key = $this->__iniPriceList($index);
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $exist_key  = AppCRedisKeys::ProcessExistKey("iniprice");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true){
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'link_run_iniprice');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'link_run_iniprice');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);

                $data    = $ret[1] ?? "";
                if(!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data,true);
                if(empty($aData)) {
                    continue;
                }

                //todo 三次重试
                $msg = '';
                for ($i = 1; $i <= 3; $i++) {
                    list($status,$msg) = $this->run($aData['function'],$aData['args'],$aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i*2);
                }

            }

        }catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'link_run_iniprice');
            exit($error);
        }
    }

    public function run(string $function, array $args, int $time){
        switch ($function){
            default:
                return [false,'function 不存在'];
        }
    }


    /**
     * @throws \yii\db\Exception
     */
    public function synNewIniPrice()
    {
        //同步最新的自定义订单列表
        $data = by::Gini()->getEffectIniList();
        echo 'success';exit;
    }

}
