<?php

namespace app\components;

use app\models\CUtil;

/**
 * Review 公共调用方法
 */
class Review
{
    private static $_instance;

    // 定义实体类型
    const ENTITY_TYPE = 'DREAME_MALL_ORDER_GOODS';

    // 回复人类型
    const REPLIER_TYPE = [
        'MERCHANT' => 1, // 商家
        'USER'     => 2, // 用户
    ];

    const TYPES = [
        'ALL'    => 1, // 首评+追评
        'SINGLE' => 2, // 单条数据
    ];

    // host
    private $host;

    // 签名
    private $sign_key;

    public function __construct()
    {
        // 获取host
        $this->host = CUtil::getConfig('host', 'config', \Yii::$app->id)['mp_cms_host'] ?? '';

        $config = CUtil::getConfig('dreame-cms', 'common', MAIN_MODULE);
        $this->sign_key = $config['sign_key'];
    }

    public static function factory(): Review
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 创建评价
    public function create(array $params)
    {
        $data = [
            'entity_type'        => self::ENTITY_TYPE,
            'id'                 => intval($params['review_id']),
            'entity_id'          => $params['sku'],
            'entity_relation_id' => $params['order_no'],
            'content'            => $params['content'],
            'image_url'          => $params['image_url'],
            'video_url'          => $params['video_url'],
            'label'              => $params['label'],
            'rating'             => $params['rating'],
            'types'              => strval($params['type']),
            'is_anonymous'       => intval($params['is_anonymous']),
            'reviewer_id'        => $params['uid'],
        ];

        if ($params['review_id']) { // 修改
            return $this->request('/api/v1/review/update', $data);
        } else {
            return $this->request('/api/v1/review/create', $data);
        }
    }

    // 批量创建评价
    public function batchCreate(array $params)
    {
        foreach ($params as &$param) {
            $param['entity_type'] = self::ENTITY_TYPE;
        }
        return $this->request('/api/v1/review/batch-create', ['batch_data' => $params], false);
    }

    // 评价详情
    public function detail(int $review_id, $types = 1)
    {
        $types == self::TYPES['ALL'] ?
            $data = [
                'entity_type'     => self::ENTITY_TYPE,
                'first_review_id' => intval($review_id)
            ]
            :
            $data = [
                'entity_type' => self::ENTITY_TYPE,
                'id'          => intval($review_id)
            ];

        return $this->request('/api/v1/review/detail', $data);
    }

    // 获取用户的评价列表
    public function userReviewList(array $params)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'reviewer_id' => $params['uid'],
            'page'        => intval($params['page']),
            'page_size'   => intval($params['page_size']),
        ];
        return $this->request('/api/v1/review/user-list', $data);
    }

    // 获取列表
    public function list(array $params)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'entity_id'   => $params['sku'],
            'is_img'      => intval($params['has_media']),  // 图片和视频 0 全部 1 有图片/视频
            'types'       => intval($params['has_append']), // 追评 0 全部 1 有追评
            'page'        => intval($params['page']),
            'page_size'   => intval($params['page_size']),
        ];
        return $this->request('/api/v1/review/list', $data);
    }

    // 获取列表
    public function backList(array $params)
    {
        $params['entity_type'] = self::ENTITY_TYPE;
        // 将start_create_time、end_create_time从东八区时间转换为零时区时间（UTC 时间）
        if (!empty($params['start_create_time'])) {
            $startDateTime = new \DateTime($params['start_create_time'], new \DateTimeZone('Asia/Shanghai'));
            $startDateTime->setTimezone(new \DateTimeZone('UTC'));
            $params['start_create_time'] = $startDateTime->format('Y-m-d H:i:s');
        }
        if (!empty($params['end_create_time'])) {
            $startDateTime = new \DateTime($params['end_create_time'], new \DateTimeZone('Asia/Shanghai'));
            $startDateTime->setTimezone(new \DateTimeZone('UTC'));
            $params['end_create_time'] = $startDateTime->format('Y-m-d H:i:s');
        }
        return $this->request('/api/v1/review/back-list', $params);
    }

    // 获取列表
    public function reportList(array $params)
    {
        $params['entity_type'] = self::ENTITY_TYPE;
        // 将start_time、end_time从东八区时间转换为零时区时间（UTC 时间）
        if (!empty($params['start_time'])) {
            $startDateTime = new \DateTime($params['start_time'], new \DateTimeZone('Asia/Shanghai'));
            $startDateTime->setTimezone(new \DateTimeZone('UTC'));
            $params['start_time'] = $startDateTime->format('Y-m-d H:i:s');
        }
        if (!empty($params['end_time'])) {
            $startDateTime = new \DateTime($params['end_time'], new \DateTimeZone('Asia/Shanghai'));
            $startDateTime->setTimezone(new \DateTimeZone('UTC'));
            $params['end_time'] = $startDateTime->format('Y-m-d H:i:s');
        }
        return $this->request('/api/v1/report/list', $params);
    }

    // 评价状态
    public function status(array $params)
    {
        // 请求数据
        $data = [
            'entity_type'        => self::ENTITY_TYPE,
            'entity_relation_id' => $params['order_nos'] ?? [],
            'reviewer_id'        => $params['uid'] ?? '',
            'start_time'         => $this->convertTime($params['start_time'] ?? 0),
            'end_time'           => $this->convertTime($params['end_time'] ?? 0),
        ];
        
        list($status, $res) = $this->request('/api/v1/review/count', $data);
        if (!$status || empty($res['list'])) {
            return [true, []];
        }

        $result = [];
        foreach ($res['list'] as $items) {
            $result = array_merge($result, $items);
        }
        return [true, $result];
    }

    // 评价数量
    public function num(array $entity_relation_ids)
    {
        $params = [
            'entity_type'        => self::ENTITY_TYPE,
            'entity_relation_id' => $entity_relation_ids
        ];
        list($status, $res) = $this->request('/api/v1/review/count', $params);
        if (!$status || empty($res['list'])) {
            return [true, []];
        }

        $result = [];
        foreach ($res['list'] as $items) {
            foreach ($items as $item) {
                if (!isset($result[$item['entity_relation_id']][$item['entity_id']])) {
                    $result[$item['entity_relation_id']][$item['entity_id']] = 1;
                } else {
                    $result[$item['entity_relation_id']][$item['entity_id']] += 1;
                }
            }
        }
        return [true, $result];
    }

    // 删除
    public function delete($order_no, $sku)
    {
        $data = [
            'entity_type'        => self::ENTITY_TYPE,
            'entity_relation_id' => $order_no,
            'entity_id'          => $sku,
        ];

        return $this->request('/api/v1/review/batch-delete', $data);
    }

    // 举报
    public function report(array $params)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'review_id'   => intval($params['review_id']),
            'reason_id'   => intval($params['reason_id']),
            'content'     => $params['content'],
            'reporter_id' => $params['uid'],
        ];

        return $this->request('/api/v1/report/create', $data);
    }

    // 举报原因列表
    public function reasonList()
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE
        ];

        return $this->request('/api/v1/report/reason', $data);
    }

    // 审核记录
    public function auditList(int $review_id, int $status)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'review_id'   => intval($review_id),
            'status'   => intval($status),
        ];

        return $this->request('/api/v1/review/audit-list', $data);
    }

    // 审核评价
    public function auditReview(array $params)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'review_id'   => intval($params['review_id']),
            'content'     => $params['content'],
            'status'      => intval($params['status']),
            'reason_id'   => intval($params['reason_id']),
            'auditor_id'  => $params['account'], // 审核人
        ];

        return $this->request('/api/v1/review/audit', $data);
    }

    // 审核举报
    public function auditReport(array $params)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'report_id'   => intval($params['report_id']),
            'status'      => intval($params['status']),
            'auditor_id'  => $params['account'], // 审核人
        ];

        return $this->request('/api/v1/report/audit', $data);
    }

    // 回复
    public function reply(array $params)
    {
        $data = [
            'entity_type'  => self::ENTITY_TYPE,
            'review_id'    => intval($params['review_id']), // 评价ID
            'replier_type' => self::REPLIER_TYPE['MERCHANT'], // 商家
            'replier_id'   => $params['account'], // 回复人
            'content'      => $params['content'],
        ];

        return $this->request('/api/v1/review/reply', $data);
    }

    // 下架
    public function remove(array $review_ids)
    {
        $data = [
            'entity_type' => self::ENTITY_TYPE,
            'id'          => array_map('intval', $review_ids)
        ];

        return $this->request('/api/v1/review/status', $data);
    }

    // 上传Media
    public function upload($file)
    {
        $filePath = $file['tmp_name'];
        $fileData = new \CURLFile($filePath, $file['type'], $file['name']);

        return $this->request('/api/v1/common/upload', ['file' => $fileData]);
    }

    /**
     * 校验内容
     * @param string $content
     * @return array
     */
    public function checkContent(string $content)
    {
        $data = [
            'type'    => 'text',
            'content' => $content,
        ];
        return $this->request('/api/v1/common/detect', $data);
    }
    /**
     * 校验内容
     * @param string $content
     * @return array
     */
    public function newCheckContent(string $content,int $type,$source,$extra = [],$back_url,$unique_id)
    {
        $data = [
            'type'    => $type,
            'content' => $content,
            'source'  => $source,
            'back_url' => $back_url,
            'pass_back' => $unique_id,
            'extra'   => $extra,
        ];
        if ($type == 2) {
            return $this->request('/api/v2/common/detect-async', $data);
        }else{
            return $this->request('/api/v2/common/detect', $data);
        }
    }

    // 请求评价平台
    private function request($path, $params, $is_sign = true)
    {
        try {
            // 请求地址
            $url = $this->host . '/dreame-cms' . $path;

            // 请求头，加上时间戳和签名
            if ($is_sign) {
                $timestamp = intval(microtime(true) * 1000);
                $sign      = MpSign::sign($params, $timestamp, $this->sign_key);
                $header    = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                    "dreame-api-timestamp: {$timestamp}",
                    "dreame-api-sign: {$sign}",
                ];
            } else {
                $header = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                ];
            }

            // 发送请求，设置超时时间为10秒
            $response = CUtil::curl_post($url, json_encode($params, 320), $header, 10, true);

            // 请求结果
            $result = json_decode($response, true);

            if (!isset($result['code'])) {
                // 可以根据具体情况处理无错误码的情况
                // CUtil::debug('请求评价平台异常：' . $response, 'err.review.request');
                CUtil::setLogMsg(
                    "err.review.request",
                    $params,
                    $result,
                    $header,
                    $url,
                    '请求评价平台异常：',
                    [],
                    200
                );
                return [false, []];
            }

            if ($result['code'] != 0) {
                // CUtil::debug('请求评价平台异常：' . $response, 'err.review.request');
                CUtil::setLogMsg(
                    "err.review.request",
                    $params,
                    $result,
                    $header,
                    $url,
                    '请求评价平台异常：',
                    [],
                    200
                );
                return [false, []];
            }

            // 时间转换
            $data = CUtil::convertTimezone('Asia/Shanghai', $result['data'], ['created_at', 'updated_at', 'audit_time']);

            return [true, $data];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('请求评价平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '异常信息' . json_encode($msg, 320), 'err.review.request（3）');

            // 返回缓存数据或默认数据
            return [false, []];
        }
    }

    /**
     * 时间转换
     * @param $timestamp
     * @param string $format
     * @return string
     */
    private function convertTime($timestamp, string $format = 'Y-m-d H:i:s'): string
    {
        if (!$timestamp) {
            return '';
        }

        $dateTime = new \DateTime();
        $dateTime->setTimestamp($timestamp);
        $dateTime->setTimezone(new \DateTimeZone('UTC'));

        return $dateTime->format($format);
    }

}