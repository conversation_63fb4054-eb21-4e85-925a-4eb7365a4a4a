<?php

namespace app\components;

use app\models\CUtil;
use app\models\MyExceptionModel;

class MallLog
{
    private static $_instance = null;

    // 请求的 host
    private static $mall_log_host = null;

    const URL = [
        'TRACE' => '/dreame-mqtt-log/mallLog/trace', //埋点
    ];

    private function __construct()
    {
        self::$mall_log_host = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }


    /**
     * @return MallLog|null
     */
    public static function factory()
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 业务分发
     * @param string $function
     * @param $args
     * @param int $time
     * @return array
     */
    public function run(string $function, $args, int $time = 0): array
    {
        switch ($function) {
            case 'trace': //日志追踪
                return $this->$function($args);
            default:
                return [false, 'function 不存在'];
        }
    }

    /**
     * 日志埋点
     */
    private function trace($log)
    {
        // 请求头信息
        $header = [
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
        ];
        list($status, $ret) = $this->__request('TRACE', $log, 'POST', $header);
        if (!$status) {
            return [false, $ret];
        }
        return [true, $ret['data'] ?? ''];
    }

    /**
     * @param string $event
     * @param $body
     * @param string $method
     * @return array
     * request 调用
     */
    public function __request(string $event, $body, string $method = 'POST', $header = []): array
    {
        $url = self::$mall_log_host . (self::URL[$event] ?? '');
        // 请求头信息合并
        $header = array_merge(
            [
                "Content-Type:application/json",
                "Expect: ",
                "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            ],
            $header
        );

        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method, 60);
        // !YII_ENV_PROD && CUtil::debug("httpcode:{$httpCode}|err:{$err}|data：" . json_encode($body, 320) . " | ret:" . json_encode($ret), "mall_log");
        !YII_ENV_PROD && CUtil::setLogMsg(
            "mall_log",
            $body,
            $ret,
            $header,
            $url,
            '',
            [],
            $httpCode
        );
        return [$ret['code'] === 0, $ret];
    }
}
