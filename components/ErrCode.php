<?php

namespace app\components;

class ErrCode
{
    //错误码字典, 格式为6位数字
    private $ERR_CODE_DICT = array(
        // 通用错误
        "1" => "ok",

        // TODO 自定义错误码
    );

    private static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
        date_default_timezone_set('PRC');
    }

    private function __clone()
    {
    }

    private $_obj = [];


    /**
     * @return ErrCode
     */
    public static function factory(): ErrCode
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * 获取错误信息
     * @param $code
     * @param string $msg
     * @return mixed|string
     */
    public function get($code, string $msg = ""): string
    {
        if ($msg) {
            return $msg;
        }
        return $this->ERR_CODE_DICT[$code] ?? "未知错误";
    }
}