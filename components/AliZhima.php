<?php

namespace app\components;

use AopClient;
use app\models\CUtil;
use app\modules\goods\services\UserOrderTryService;
use Yii;

require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayFundAuthOrderAppFreezeRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayFundAuthOperationDetailQueryRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayFundAuthOperationCancelRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayFundAuthOrderUnfreezeRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayTradePayRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayTradeQueryRequest.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipayTradeRefundRequest.php";

class AliZhima extends AliApplet
{
    const SERVICE_ID = YII_ENV_PROD ? "2024041800000000000095617900" : "2024041800000000000095617900"; //信用服务ID

    const CATEGORY          = "HIGH_DEPOSIT_GENERAL";
    const PID               = YII_ENV_PROD ? "2088541909173799" : "2088541909173799";                         //收款账户的支付宝用户号
    const NOTIFY_URL = [
        'FREEZE'   => '/zhima/notify',
        'UNFREEZE' => '/zhima/notify',
        'CANCEL'   => '/zhima/notify',
        'PAY'      => '/zhima/notify',
    ];

    private $notify_url_prefix;

    private static $_instance;

    /**
     * @var AopClient|null
     */
    private static $alipayClient = null;

    private function __construct()
    {
        $this->notify_url_prefix = CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host'];
    }

    public static function factory(): AliZhima
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Get Alipay client instance
     * @return AopClient
     */
    private static function getAlipayClient(): AopClient
    {
        if (self::$alipayClient === null) {
            self::$alipayClient = new AopClient(self::GetAlipayConfig());
        }
        return self::$alipayClient;
    }

    /**
     * Freeze order 创建冻结订单
     * @param $data
     * @return array
     */
    public function FreezeOrder($data): array
    {
        try {
            // 1、校验参数
            $amount       = $data['price'] ?? 0;
            $outOrderNo   = $data['order_no'] ?? '';
            $orderTitle   = $data['goods_name'] ?? '';
            $outRequestNo = $data['out_request_no'] ?? '';
            if ($amount <= 0) {
                throw new \Exception("金额必须大于0");
            }
            if (empty($outOrderNo)) {
                throw new \Exception("商户订单号不能为空");
            }
            // 2、调用支付宝接口
            $alipayClient = self::getAlipayClient();

            $request = new \AlipayFundAuthOrderAppFreezeRequest();
            //设置异步通知地址
            $request->setNotifyUrl($this->notify_url_prefix . self::NOTIFY_URL['FREEZE']);
            $model = [
                'order_title'          => $orderTitle . "试用押金",
                'amount'               => $amount,
                'payee_user_id'        => self::PID,
                'out_order_no'         => $outOrderNo,
                'out_request_no'       => $outRequestNo,
                'product_code'         => "PREAUTH_PAY",
                'deposit_product_mode' => "DEPOSIT_ONLY",
                'timeout_express'      => '2h',//预授权订单相对超时时间
                'extra_param'          => json_encode(["serviceId" => self::SERVICE_ID, "category" => self::CATEGORY])
            ];

            $modelJson = json_encode($model, JSON_UNESCAPED_UNICODE);
            $request->setBizContent($modelJson);
            $orderStr = $alipayClient->sdkExecute($request);

            // 3、记录日志
            CUtil::debug("请求参数:" . $modelJson . "|返回结果" . $orderStr, 'info.zhiMa.FreezeOrder');

            // 4、返回结果
            return [true, $orderStr];
        } catch (\Exception $e) {
            // 5、异常处理
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.zhima_freeze_order");
            return [false, "调用失败" . $e->getMessage()];
        }
    }

    /**
     * Query order  查询订单
     * @param string $outOrderNo 商户授权资金订单号
     * @param string $outRequestNo 商户的授权资金操作流水号
     * @param string $operationType 查询冻结明细时 operation_type 默认为FREEZE，可不传入 |  查询解冻明细时 operation_type 需传入UNFREEZE |  查询支付明细时  operation_type 需传入PAY
     * @return array
     */
    public function QueryOrder(string $outOrderNo, string $outRequestNo = '', string $operationType = 'FREEZE'): array
    {
        try {
            if (empty($outOrderNo)) {
                throw new \Exception("商户订单号不能为空");
            }
            if (empty($outRequestNo)) {
                throw new \Exception("商户的流水号不能为空");
            }
            $alipayClient = self::getAlipayClient();

            $request = new \AlipayFundAuthOperationDetailQueryRequest();
            $model   = [
                'operation_type' => $operationType,
                'out_order_no'   => $outOrderNo,
                'out_request_no' => $outRequestNo,
            ];

            $request->setBizContent(json_encode($model, JSON_UNESCAPED_UNICODE));
            $responseResult  = $alipayClient->execute($request);
            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);
            // 记录日志
            CUtil::debug("商户订单号：" . $outOrderNo . "|" . "商户的流水号:" . $outRequestNo . "|" . $responseJson, "info.zhiMa.QueryOrder");

            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                return [true, $responseArray];
            } else {
                return [false, $responseArray['sub_msg'] ?? "数据错误"];
            }
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.zhima_query_order");
            return [false, "调用失败" . $e->getMessage()];
        }
    }

    /**
     * Cancel Order 撤销订单
     * @param string $outOrderNo 商户的授权资金订单号
     * @param string $outRequestNo 商户的授权资金操作流水号
     * @param string $remark 附言描述
     * @return array
     */
    public function CancelOrder(string $outOrderNo, string $outRequestNo, string $remark = '撤销授权'): array
    {
        try {
            if (empty($outOrderNo)) {
                throw new \Exception("商户订单号不能为空");
            }
            if (empty($outRequestNo)) {
                throw new \Exception("商户的流水号不能为空");
            }
            // 使用数组构建结构
            $data         = [
                'out_order_no'   => $outOrderNo,               // 商户的授权资金订单号
                'out_request_no' => $outRequestNo,             // 商户的授权资金操作流水号
                'remark'         => $remark,                   // 附言描述
            ];
            $json         = json_encode($data, JSON_UNESCAPED_UNICODE);
            $alipayClient = new \AopClient(self::GetAlipayConfig());
            $request      = new \AlipayFundAuthOperationCancelRequest();
            //设置异步通知地址
            $request->setNotifyUrl($this->notify_url_prefix . self::NOTIFY_URL['CANCEL']);
            $request->setBizContent($json);

            $responseResult = $alipayClient->execute($request);

            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);
            // 记录日志
            CUtil::debug("商户订单号：" . $outOrderNo . "|" . "商户的流水号:" . $outRequestNo . "|" . $responseJson, "info.zhiMa.CancelOrder");

            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                UserOrderTryService::getInstance()->CancelTryOrder($outOrderNo);
                return [true, "取消成功"];
            } else {
                return [false, $responseArray['sub_msg'] ?? "取消失败"];
            }
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.zhima_cancel_order");
            return [false, "取消失败" . $e->getMessage()];
        }
    }


    /**
     * @param string $authNo 支付宝资金授权订单号
     * @param string $outRequestNo 解冻请求流水号
     * @param float $amount 解冻金额
     * @param bool $isComplete 是否守约
     * @param string $remark 附言描述
     * @return array
     */
    public function UnFreezeOrder(string $authNo, string $outRequestNo, $amount = 0, bool $isComplete = true, string $remark = "解冻资金"): array
    {
        try {
            if (empty($authNo)) {
                throw new \Exception("支付宝资金授权订单号不能为空");
            }
            if (empty($outRequestNo)) {
                throw new \Exception("解冻请求流水号不能为空");
            }

            // 使用数组构建结构
            $data = [
                'auth_no'        => $authNo,                // 支付宝资金授权订单号
                'out_request_no' => $outRequestNo,          // 解冻请求流水号
                'amount'         => $amount,                // 解冻的金额
                'remark'         => $remark                 // 附言描述
            ];

            //是否守约
            if ($isComplete) {
                $data['extra_param'] = ['unfreezeBizInfo' => ['bizComplete' => true]];
            }
            $modelJson = json_encode($data, JSON_UNESCAPED_UNICODE);

            $aop     = new \AopClient(self::GetAlipayConfig());
            $request = new \AlipayFundAuthOrderUnfreezeRequest();
            //设置异步通知地址
            $request->setNotifyUrl($this->notify_url_prefix . self::NOTIFY_URL['UNFREEZE']);
            $request->setBizContent($modelJson);
            $responseResult = $aop->execute($request);

            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);

            CUtil::debug("请求参数:" . $modelJson . "|返回结果" . $responseJson, 'info.zhiMa.UnFreezeOrder');
            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                return [true, $responseArray];
            } else {
                return [false, $responseArray];
            }
        } catch (\Exception $e) {
            // 异常处理
            $error = "调用失败：" . $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.zhima_unfreeze_order");
            return [false, "调用失败:" . $e->getMessage()];
        }
    }


    /**
     * @param $data
     * @return array
     * 扣款
     */
    public function TradePay($data): array
    {
        // 执行请求并处理响应
        try {
            $amount     = $data['price'] ?? 0;
            $outOrderNo = $data['order_no'] ?? '';
            $authNo     = $data['auth_no'] ?? '';
            $userId     = $data['user_id'] ?? '';
            $goods      = $data['goods'] ?? [];

            // 检查必要的参数
            if (empty($outOrderNo)) {
                throw new \Exception("商户订单号不能为空");
            }
            if (empty($authNo)) {
                throw new \Exception("资金预授权单号不能为空");
            }
            if ($amount <= 0) {
                throw new \Exception("金额必须大于0");
            }
            if (empty($userId)) {
                throw new \Exception("用户ID不能为空");
            }
            if (empty($goods)) {
                throw new \Exception("商品信息不能为空");
            }

            $goodsDetail = [];
            foreach ($goods as $value) {
                $goodsDetail[] = [
                    'goods_name' => $value['name'],
                    'quantity'   => $value['num'],
                    'price'      => $value['price'],
                ];
            }

            // 初始化支付宝SDK客户端
            $alipayClient = new \AopClient(self::GetAlipayConfig());
            $request      = new \AlipayTradePayRequest();
            $request->setNotifyUrl($this->notify_url_prefix . self::NOTIFY_URL['PAY']);

            // 构建业务参数
            $model = [
                'out_trade_no'        => $outOrderNo,
                'auth_no'             => $authNo,
                'total_amount'        => $amount,
                'auth_confirm_mode'   => "COMPLETE",
                'subject'             => "试用押金",
                'product_code'        => "PREAUTH_PAY",
                'passback_params'     => json_encode(["user_id" => $userId]),
                //'enable_pay_channels' => 'balance',//测试 只扣款余额
                'goods_detail'        => $goodsDetail,
            ];

            $modelJson = json_encode($model, JSON_UNESCAPED_UNICODE);
            // 设置业务参数
            $request->setBizContent($modelJson);
            $responseResult  = $alipayClient->execute($request);
            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);

            CUtil::debug("请求参数：" . $modelJson . "|返回参数:" . $responseJson, 'info.zhiMa.TradePay');
            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                return [true, $responseArray];
            } else {
                return [false, "扣款失败"];
            }
        } catch (\Exception $e) {
            $error = "调用失败：" . $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.trade_pay_order");
            return [false, "系统异常: " . $e->getMessage()];
        }
    }


    public function TradeQuery($outTradeNo): array
    {
        try {
            if (empty($outTradeNo)) {
                throw new \Exception("商户订单号不能为空");
            }

            // 初始化支付宝SDK客户端
            $alipayClient = new \AopClient(self::GetAlipayConfig());
            $request      = new \AlipayTradeQueryRequest();

            // 构建请求模型
            $model = [
                'out_trade_no'  => $outTradeNo,
            ];

            $modelJson = json_encode($model, JSON_UNESCAPED_UNICODE);

            $request->setBizContent($modelJson);
            $responseResult  = $alipayClient->execute($request);
            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);

            // 记录日志
            CUtil::debug("请求参数：" . $modelJson . "|返回参数:" . $responseJson, 'info.zhiMa.TradeQuery');
            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                return [true, $responseArray];
            } else {
                return [false, "查询失败: " . ($responseArray['sub_msg'] ?? "未知错误")];
            }
        } catch (\Exception $e) {
            $error = "调用失败：" . $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, "err.trade_query_order");
            return [false, "系统异常: " . $e->getMessage()];
        }
    }


    public function TradeRefund($data): array
    {
        $outTradeNo   = $data['order_no'] ?? '';
        $refundNo     = $data['refund_no'] ?? '';
        $refundAmount = $data['price'] ?? 0;
        try {
            // 初始化支付宝SDK客户端
            $alipayClient = new \AopClient(self::GetAlipayConfig());
            $request      = new \AlipayTradeRefundRequest();

            // 构建业务参数
            $model = [
                'out_trade_no'   => $outTradeNo,
                'query_options'  => ["refund_detail_item_list"],
                'refund_amount'  => "$refundAmount",
                'refund_reason'  => "正常退款",
                'out_request_no' => $refundNo
            ];

            $modelJson = json_encode($model, JSON_UNESCAPED_UNICODE);
            // 设置业务参数
            $request->setBizContent($modelJson);

            // 执行请求并处理响应
            $responseResult  = $alipayClient->execute($request);
            $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
            $response        = $responseResult->$responseApiName;
            $responseJson    = json_encode($response, 320);
            $responseArray   = json_decode($responseJson, true);

            // 记录日志
            CUtil::debug("请求参数：" . $modelJson . "|返回参数:" . $responseJson, 'info.zhiMa.TradeRefund');
            // 检查响应
            if (!empty($responseArray['code']) && $responseArray['code'] == 10000) {
                return [true, $responseArray];
            } else {
                return [false, "退款失败: " . ($responseArray['sub_msg'] ?? "未知错误")];
            }
        } catch (\Exception $e) {
            return ["success" => false, "message" => "系统异常: " . $e->getMessage()];
        }
    }



}
