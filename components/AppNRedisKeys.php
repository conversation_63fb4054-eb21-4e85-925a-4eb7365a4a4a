<?php

namespace app\components;

use app\models\CUtil;

class AppNRedisKeys
{
    public static $prefix = PRO_NAME; //各项目防Redis冲突唯一标识！！！！

    //类名 + 方法名 + 参数

    // 根据活动ID获取活动详情 （此ID为 member_activity_module_relation表中主键ID）
    public static function memberActivityModuleRelationList($acRelationId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $acRelationId);
    }

    // 根据活动ID获取活动参与记录 （此ID为 member_activity_module_relation表中主键ID）
    public static function newDrawActivityRecord($acRelationId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $acRelationId);
    }

    // 根据活动ID校验是否领取红包 （此ID为 member_activity_module_relation表中主键ID）
    public static function lotteryUsers($acRelationId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $acRelationId);
    }

    // 红包数据队列 （此ID为 member_activity_module_relation表中主键ID）
    public static function envelopeActivity($acRelationId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $acRelationId);
    }

    // 红包数据总库存 （此ID为 member_activity_module_relation表中主键ID）
    public static function envelopeActivityStock($acRelationId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $acRelationId);
    }

    /**
     * @param $userId
     * @return string
     * 获取我的心愿单
     */
    public static function wishlist($userId): string
    {
        return CUtil::getNewAllParams(self::$prefix, __FUNCTION__, $userId);
    }
}

