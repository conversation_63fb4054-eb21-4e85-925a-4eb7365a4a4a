<?php

namespace app\components;

use app\jobs\NoticePushJob;
use app\models\by;
use app\models\CUtil;
use app\modules\main\models\UserModel;
use app\modules\main\models\WxNoticeModel;
use yii\helpers\Json;

/**
 * 生日提醒
 */
class BirthdayReminder
{
    // 50000微秒 = 0.05秒
    const SLEEP_UTIME = 50000;

    // kafka的topic、msg_id
    const KAFKA_TOPIC = 'msgBatchPush';
    const KAFKA_MSG_CONFIG_ID = 1673273275312410625;
    const KAFKA_PUSH_STRATEGY = 'crowd_push';

    /**
     * 发送生日提示
     * @throws \yii\db\Exception
     */
    public function sendBirthdayReminder()
    {
        // 月、天
        $month = date('m');
        $day   = date('d');
        // 查询100个表：t_user_{index}
        for ($i = 0; $i <= 99; $i++) {
            // 查询生日
            $this->getUserIdsByBirthday($i, $month, $day);
            // 每次停止
            usleep(self::SLEEP_UTIME);
        }
    }

    /**
     * 获取生日对应的用户ID
     * @param $index
     * @param $month
     * @param $day
     * @throws \yii\db\Exception
     */
    private function getUserIdsByBirthday($index, $month, $day)
    {
        // 数据表
        $table = UserModel::userTb($index);

        // 缓存，保证数据不会重复发送
        $key = "dreame:BirthdayReminder:getUserIdsByBirthday:index-$index";
        $id = by::redis()->get($key);
        $id = CUtil::uint($id);
        while (true) { // 每次取1000条数据，未注销的用户
            $sql = <<<SQL
select
  {$table}.id as id,
  {$table}.user_id as user_id,
  t_users_mall.uid as uid,
  {$table}.birthday as birthday,
  t_users_mall.is_deleted as is_deleted
from
  {$table}
  left join t_phone on {$table}.user_id = t_phone.user_id
  left join t_users_mall on t_phone.mall_id = t_users_mall.id
where
  {$table}.birthday != 0 AND
  MONTH(CONVERT_TZ(FROM_UNIXTIME(`birthday`), '+00:00', '+08:00')) = {$month} AND 
  DAY(CONVERT_TZ(FROM_UNIXTIME(`birthday`), '+00:00', '+08:00')) = {$day} AND
  {$table}.id > {$id} LIMIT 1000;
SQL;
            // 查询的结果集
            $items = by::dbMaster()->createCommand($sql)->queryAll();
            if (empty($items)) {
                break;
            }
            $end = end($items);
            $id = $end['id'];

            // 推送消息
            $uidList = [];
            foreach ($items as $item) {
                if (!$item['birthday']) {
                    continue;
                }
                // 单条推送模板消息
                if (!$item['is_deleted']) {
                    \Yii::$app->queue->push(new NoticePushJob(['user_id' => $item['user_id'], 'type' => WxNoticeModel::TYPE['BIRTHDAY_REMINDER'], 'params' => []]));
                }

                // 未删除，且有数据
                if (!$item['is_deleted'] && $item['uid']) {
                    $uidList[] = $item['uid'];
                }
            }

            // 批量向kafka发送消息
            $value = [
                "uidList"      => $uidList,
                "msgConfigId"  => self::KAFKA_MSG_CONFIG_ID,
                "pushStrategy" => self::KAFKA_PUSH_STRATEGY,
                "ext"          => ""
            ];

            // 推送kafka 异常不打日志统一处理
            Kafka::factory()->send(self::KAFKA_TOPIC, $value);

            // 存redis
            by::redis()->set($key, $id, 7200);
            usleep(self::SLEEP_UTIME);
        }
    }
}
