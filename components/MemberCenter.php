<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;



use app\models\by;
use app\models\CUtil;
use Faker\Provider\Uuid;
use yii\db\Exception;

class MemberCenter
{
    private static $_instance = [];
    protected $expire_time = YII_ENV_PROD ? 3600 : 60;

    private $dreame_member_center_domain;

    const URL = [
        //会员信息
            'GROWTH_CENTER'             => '/dreame-member-center/int/memberinfo/growth-center',                         //会员中心接口 GET
            'GROWTH_CENTER_V2'          => '/dreame-member-center/int/memberinfo/v2/growth-center',                      //会员中心接口 GET  新版本
            'LEVEL_BENEFIT'             => '/dreame-member-center/int/memberinfo/all-level-benefit',                     //获取所有等级权益数据 GET
            'LEVEL_BENEFIT_V2'          => '/dreame-member-center/int/memberinfo/v2/all-level-benefit',                  //获取所有等级权益数据 GET  新版本
            'BASIC_INFO'                => '/dreame-member-center/int/memberinfo/basic-info',                            //会员基本信息接口 GET
            'BASIC_INFO_V2'             => '/dreame-member-center/int/memberinfo/v2/basic-info',                         //会员基本信息接口 GET 新版本
            'RESET_LEVEL_CHANGE_PROMPT' => '/dreame-member-center/int/memberinfo/reset-level-change-prompt',             //重置等级变更提示
        //任务中心
            'TASK_LIST'                 => '/dreame-member-center/int/task/member-task-list',                            //会员任务中心 GET
            'NO_AUTH_TASK_LIST'         => '/dreame-member-center/int/task/member-task-limit',                           //会员任务中心（无需登录） GET
            'TASK_INFO'                 => '/dreame-member-center/int/task/task-info',                                   //获取任务信息 GET
        //运营说明配置
            'OPERATE_CONFIG'            => '/dreame-member-center/int/describeconfig',                                   //获取运营说明配置 GET
            'IMPORT_CONFIG'             => '/dreame-member-center/int/describeconfig/import',                            //导入说明 POST
            'RULE_FINE_DETAIL'          => '/dreame-member-center/int/rulefine/detail',                                  //获取规则配置详情 GET

        //活动（签到）
        'SIGN_IN'              => '/dreame-activities/int/activites/sign-in',//签到 GET
        'SIGN_IN_STATUS'       => '/dreame-activities/int/activites/sign-in-status',//获取签到状态 GET
        'SIGN_IN_MONTH_DETAIL' => '/dreame-activities/int/activites/month-sign-in-detail',//获取月签到明细 POST
        'SIGN_IN_HOME'         => '/dreame-activities/int/activites/sign-in-home',//签到首页数据 GET
        'CONTINUE_SIGN_IN_INFO'=> '/dreame-activities/int/activites/continue-sign-in-info',//获取连续签到信息GET

        //活动（打卡）
        'CHECK_IN_IN'  => '/dreame-activities/int/checkin/checkIn-in',//打卡 PUT
        'CHECK_IN_IN_DETAIL'  => '/dreame-activities/int/checkin/month-checkIn-in-detail',//打卡明细 POST

        //积分
        'SCORE_GET'                  => '/dreame-point-center/int/totalmemberpoints',                                    //获取会员积分和觅享分 GET
        'SCORE_COLLECT_ALL'          => '/dreame-point-center/int/memberpointgrow/collect-all-points',//领取全部积分 PUT
        'SCORE_COLLECT'              => '/dreame-point-center/int/memberpointgrow/collect-points',//领取积分 PUT
        'SCORE_GET_GROWS'            => '/dreame-point-center/int/memberpointgrow/grows',//觅享分明细 GET
        'SCORE_GET_POINTS'           => '/dreame-point-center/int/memberpointgrow/points',//积分明细 GET
        'SCORE_UNCOLLECT'            => '/dreame-point-center/int/memberpointgrow/uncollect-points',//获取未领取积分明细 GET
        'GOLD_COLLECT_BY_EVENT_CODE' => '/dreame-point-center/int/memberpointgrow/collect-gold-byEventCode', // 领取金币（通过事件码） PUT
        'GOLD_EXCHANGE_POINT' => '/dreame-point-center/int/memberpointgrow/pointChangeSync',//金币兑换积分

        //手动更新积分觅香分
        'UPDATE_MEMBER_GROW'  => '/dreame-point-center/int/totalmemberpoints/manual_update_member_grow', //手动更新觅享分
        'UPDATE_MEMBER_POINT' => '/dreame-point-center/int/totalmemberpoints/manual_update_member_point',//手动更新积分
        'UPDATE_MEMBER_GOLD'  => '/dreame-point-center/int/totalmemberpoints/manual_update_member_gold', //手动更新金币

        //商城推送积分/觅享分
        'POINT_GROW_SAVE'=>'/dreame-event-center/pointAndGrow/batchSave',//推送积分(增减)
        
        // 新增活动规则（打卡、购物返积分）
        'ACTIVITY_RULEFINE'=>'/dreame-member-center/int/rulefine/ext/save',
    ];

    const POINT_GROW_EVENT = [
        'purchase' => 'mall/dreame/offline_purchase_give_point',//购物积分
        'activity' => 'mall/dreame/offline_activity_give_point',//活动积分
        'benifits' => 'mall/dreame/offline_benifits_give_point',//福利积分
    ];


    const CLEAR_REDIS_FUNCTIONS = [
        'basicInfo','basicInfo_V2','levelBenefit_V2','levelBenefit'
    ];

    private function __construct()
    {
        $this->dreame_member_center_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return MemberCenter
     */
    public static function factory(): MemberCenter
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * @param $function
     * @param string $user_id
     * @return string
     * 添加会员权益缓存
     */
    private function __getMemberCenterKey($function, string $user_id = ''): string
    {
        return AppCRedisKeys::memberCenterInfo($function,$user_id);
    }

    /**
     * @param $function
     * @param string $user_id
     * @return string
     * 删除会员权益缓存
     */
    private function __delMemberCenterKey($function, string $user_id=''): string
    {
        $r_key = $this->__getMemberCenterKey($function,$user_id);
        return by::redis('core')->del($r_key);
    }

    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'levelBenefit':
                return $this->$function($args['single_v'] ?? 0);
            case 'operateConfig':
                return $this->$function($args['lang'], $args['category']);
            case 'growthCenter':
            case 'basicInfo':
                return $this->$function($args['user_id'], $args['single'] ?? 0, $args['single_v'] ?? 0);

            case 'signIn':
            case 'signInStatus':
            case 'signInHome':
            case 'scoreGet':
            case 'continueSignInfo':
            case 'resetLevelChangePrompt':
                return $this->$function($args['user_id']);
            case 'taskList':
                return $this->$function($args['user_id'], $args['type'],$args['version']??'');
            case 'scoreCollectAll':
            case 'scoreUncollect':
                return $this->$function($args['user_id'], $args['type']);
            case 'checkInIn':
                return $this->$function($args['user_id'], $args['member_center_save_id'] ?? 0);
            case 'scoreCollect':
                return $this->$function($args['user_id'], $args['id']);
            case 'signInMonthDetail':
                return $this->$function($args['user_id'], $args['period_time'] ?? [],$args['type'] ?? '');
            case 'checkInInDetail':
                return $this->$function($args['user_id'], $args['period_time'] ?? [], $args['member_center_save_id'] ?? 0);
            case 'importConfig':
                return $this->$function($args['file']);
            case 'scoreGetGrows':
                return $this->$function($args['user_id'], $args['type'] ?? 0, $args['page'] ?? 1, $args['size'] ?? 20);
            case 'scoreGetPoints':
            return $this->$function($args['user_id'], $args['type'] ?? 0, $args['asset_type'] ?? 1, $args['page'] ?? 1, $args['size'] ?? 20);
            case 'taskInfo':
                return $this->$function($args['user_id'], $args['taskCode'] ?? '');
            case 'updateMemberGrow':
            case 'updateMemberPoint':
            case 'updateMemberGold':
                return $this->$function($args['user_id'], $args['number'] ?? '');
            case 'pointGrowSave':
                return $this->$function($args['event'], $args['data']);
            case 'collectGoldByEventCode':
                return $this->$function($args['user_id'], $args['group_code'], $args['type_code'], $args['code']);
            case 'ruleFineDetail':
                return $this->$function($args['group_code'], $args['type_code'], $args['code']);
            case 'noAuthTaskList':
                return $this->$function($args['type'], $args['limit'],$args['version']);
            default:
                return [false, 'function 不存在'];
        }
    }


    public function levelBenefit($singleV): array
    {
        $event = $singleV ? 'LEVEL_BENEFIT_V2' : 'LEVEL_BENEFIT';
        $key   = $singleV ? 'levelBenefit_V2' : 'levelBenefit';
        $r_key  = $this->__getMemberCenterKey($key);
        $redis  = by::redis();
        $aJson  = $redis->get($r_key);
        $aData  = (array)json_decode($aJson, true);

        if ($aJson === false) {
            list($s,$benefitData) = $this -> __request($event,[],'GET');
            if(!$s){
                $msg = $benefitData['msg'] ?? '';
                return [false,$msg];
            }
            $aData = $benefitData['data'] ?? [];

            $redis->set($r_key, json_encode($aData), ['ex' => $this->expire_time]);
        }
        return [true,$aData];
    }

    public function operateConfig($lang,$category): array
    {
        list($s,$data) = $this -> __request('OPERATE_CONFIG',[],'GET',[],['lang'=>$lang,'category'=>$category]);
        if(!$s){
            $msg = $data['msg'] ?? '';
            return [false,$msg];
        }
        $realData = $data['data'] ?? [];
        return [true,$realData];
    }

    public function importConfig($file): array
    {
        $header = [
            "Content-Type:multipart/form-data",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ];
        list($s, $data) = $this->__request('IMPORT_CONFIG', ['file'=>$file], 'POST', $header, [],'file');
        if(!$s){
            $msg = $data['msg'] ?? '';
            return [false,$msg];
        }
        $realData = $data['data'] ?? [];
        return [true,$realData];
    }

    /**
     * @throws Exception
     */
    public function growthCenter($user_id,$single,$singleV): array
    {
        //检验新老版本
        $event = $single ? 'GROWTH_CENTER_V2' : 'GROWTH_CENTER';
        return $this->__getDataByUserId($user_id, $event, 'GET');
    }

    /**
     * @throws Exception
     */
    public function basicInfo($user_id, $single, $singleV): array
    {
        $event = $single ? 'BASIC_INFO_V2' : 'BASIC_INFO';
        $key   = $single ? 'basicInfo_V2' : 'basicInfo';
        $r_key = $this->__getMemberCenterKey($key, $user_id);
        $redis = by::redis();
        $aJson = $redis->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
            list($s, $aData) = $this->__getDataByUserId($user_id, $event, 'GET', [], false);
            $redis->set($r_key, json_encode($aData), ['ex' => empty($aData) ? 10 : $this->expire_time]);
        }
        return [true, $aData];
    }

    /**
     * @throws Exception
     */
    public function taskList($user_id, $type = 1,$version): array
    {
        return $this->__getDataByUserId($user_id, 'TASK_LIST', 'GET', ['type' => $type,'version' => $version]);
    }
    
    /**
     * @throws Exception
     */
    public function noAuthTaskList($type = 1, $limit = 3,$version): array
    {
        return $this->__getDataByUserId(0, 'NO_AUTH_TASK_LIST', 'GET', ['type' => $type, 'limit' => $limit,'version' => $version], false);
    }

    //签到

    /**
     * @throws Exception
     */
    public function signIn($user_id): array
    {
        return $this->__getDataByUserId($user_id, 'SIGN_IN', 'PUT');
    }

    /**
     * @throws Exception
     */
    public function signInStatus($user_id): array
    {
        return $this->__getDataByUserId($user_id, 'SIGN_IN_STATUS', 'GET');
    }

    /**
     * @throws Exception
     */
    public function signInHome($user_id): array
    {
        return $this->__getDataByUserId($user_id, 'SIGN_IN_HOME', 'GET');
    }

    /**
     * @throws Exception
     */
    public function signInMonthDetail($user_id, $period_time, $type): array
    {
        return $this->__getDataByUserId($user_id, 'SIGN_IN_MONTH_DETAIL', 'POST', ['period_time' => $period_time, 'type' => $type]);
    }


    //打卡


    /**
     * @throws Exception
     * 用户打卡（活动）
     */
    public function checkInIn($user_id, $member_center_save_id): array
    {
        $arr = [
                'extId'      => $member_center_save_id,
                'eventTimes' => time() * 1000,
        ];
        return $this->__getDataByUserId($user_id, 'CHECK_IN_IN', 'PUT', $arr);
    }

    /**
     * @param $user_id
     * @param $period_time
     * @param $member_center_save_id
     * @return array
     * @throws Exception 用户打卡详情
     */
    public function checkInInDetail($user_id, $period_time, $member_center_save_id): array
    {
        return $this->__getDataByUserId($user_id, 'CHECK_IN_IN_DETAIL', 'POST', ['period_time' => $period_time, 'extId' => $member_center_save_id]);
    }



    //积分

    /**
     * @throws Exception
     */
    public function scoreGet($user_id): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_GET', 'GET');
    }

    /**
     * @throws Exception
     */
    public function scoreCollectAll($user_id, $type = 1): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_COLLECT_ALL', 'PUT', ['type' => $type]);
    }

    /**
     * @throws Exception
     */
    public function scoreCollect($user_id, $id): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_COLLECT', 'PUT',['id'=>$id]);
    }

    /**
     * @throws Exception
     */
    public function scoreGetGrows($user_id, $type = 0, $page = 1, $size = 20): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_GET_GROWS', 'GET', ['type' => $type, 'page' => $page, 'size' => $size]);
    }

    /**
     * @throws Exception
     */
    public function scoreGetPoints($user_id, $type = 0, $assetType = 1, $page = 1, $size = 20): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_GET_POINTS', 'GET', ['type' => $type, 'assetType' => $assetType, 'page' => $page, 'size' => $size]);
    }

    /**
     * @throws Exception
     */
    public function scoreUncollect($user_id, $type = 1): array
    {
        return $this->__getDataByUserId($user_id, 'SCORE_UNCOLLECT', 'GET', ['type' => $type]);
    }

    /**
     * @throws Exception
     */
    public function continueSignInfo($user_id)
    {
        return $this->__getDataByUserId($user_id, 'CONTINUE_SIGN_IN_INFO', 'GET');
    }

    /**
     * @throws Exception
     */
    public function resetLevelChangePrompt($user_id)
    {
        return $this->__getDataByUserId($user_id, 'RESET_LEVEL_CHANGE_PROMPT', 'PUT');
    }

    /**
     * @throws Exception
     */
    public function taskInfo($user_id,$taskCode='')
    {
        return $this->__getDataByUserId($user_id, 'TASK_INFO', 'GET', ['taskCode' => $taskCode]);
    }

    /**
     * @param $user_id
     * @param $number
     * @return array
     * @throws Exception
     * 手动更新觅享分
     */
    public function updateMemberGrow($user_id,$number)
    {
        return $this->__getDataByUserId($user_id, 'UPDATE_MEMBER_GROW', 'GET', ['grow' => $number]);
    }

    /**
     * @param $user_id
     * @param $number
     * @return array
     * @throws Exception
     * 手动更新积分
     */
    public function updateMemberPoint($user_id,$number)
    {
        return $this->__getDataByUserId($user_id, 'UPDATE_MEMBER_POINT', 'GET', ['point' => $number]);
    }


    /**
     * @param $user_id
     * @param $number
     * @return array
     * @throws Exception
     * 手动更新金币
     */
    public function updateMemberGold($user_id,$number): array
    {
        return $this->__getDataByUserId($user_id, 'UPDATE_MEMBER_GOLD', 'GET', ['gold' => $number]);
    }
    /**
     * @param $user_id
     * @param $groupCode
     * @param $typeCode
     * @param $code
     * @return array
     * @throws Exception
     * 通过事件码领取金币
     */
    public function collectGoldByEventCode($user_id, $groupCode, $typeCode, $code): array
    {
        $eventCode = $groupCode . '/' . $typeCode . '/' . $code;
        return $this->__getDataByUserId($user_id, 'GOLD_COLLECT_BY_EVENT_CODE', 'PUT', ['eventCode' => $eventCode]);
    }


    /**
     * @param $groupCode
     * @param $typeCode
     * @param $code
     * @return array
     * 获取规则配置详情
     */
    public function ruleFineDetail($groupCode, $typeCode, $code): array
    {
        // RULE_FINE_DETAIL
        //被邀请人：mall/dreame/be_invited_reg
        //被邀请人：mall/dreame/invite_reg
        list($s,$data) = $this -> __request('RULE_FINE_DETAIL',[],'GET',[],['groupCode' => $groupCode, 'typeCode' => $typeCode, 'code' => $code]);
        if(!$s){
            $msg = $data['msg'] ?? '';
            return [false,$msg];
        }
        $realData = $data['data'] ?? [];
        return [true,$realData];
    }


    /**
     * @param $user_id
     * @param $event
     * @param string $method
     * @param array $arr
     * @param bool $checkUid
     * @return array
     * @throws Exception
     * 公用获取数据方法
     *
     */
    private function __getDataByUserId($user_id, $event, string $method = 'POST', array $arr = [], bool $checkUid = true): array
    {
        //判断是否是游客
        if(strlen($user_id) > 10 ){//游客
            $uid = '';
        }else{
            $user_id = CUtil::uint($user_id);
            //获取uid
            $mallInfo = by::usersMall()->getInfoByUserId($user_id);
            $uid = $mallInfo['uid'] ?? '';
        }
        if(empty($uid) && $checkUid){
            CUtil::debug('用户UID为空|'.$user_id, "warn.member_center.{$event}");
            //异步注册，防止下次失败
            strlen($user_id)<= 10 && Mall::factory()->push($user_id,'centerRegister',['user_id'=>$user_id,'r_id'=>0,'phone'=>$mallInfo['phone']??0]);
            return [false,'用户UID为空'];
        }


        $body = [];

        //时间戳处理
        if($event == 'SIGN_IN_MONTH_DETAIL'){
            $periodTime = $arr['period_time'] ?? [];
            if(!is_array($periodTime)){
                CUtil::debug('查询时间戳错误|'.$user_id.'|'.json_encode($periodTime), "warn.member_center.{$event}");
                return [false,'查询时间戳错误'];
            }
            $periodTime = array_unique(array_filter($periodTime));
            if(empty($periodTime)){
                CUtil::debug('查询时间戳为空|'.$user_id.'|'.json_encode($periodTime), "err.member_center.{$event}");
                return [false,'查询时间戳错误'];
            }
            $monthBeginTimestampList = $this->__getMonthsByPeriodTime($periodTime,true);
            if($monthBeginTimestampList){
                $body = $monthBeginTimestampList;
            }
        }

        //打卡时间处理
        if($event == 'CHECK_IN_IN_DETAIL'){
            $periodTime = $arr['period_time'] ?? [];
            if(!is_array($periodTime)){
                CUtil::debug('查询时间戳错误|'.$user_id.'|'.json_encode($periodTime), "warn.member_center.{$event}");
                return [false,'查询时间戳错误'];
            }
            $periodTime = array_filter($periodTime);
            if(empty($periodTime)){
                CUtil::debug('查询时间戳为空|'.$user_id.'|'.json_encode($periodTime), "err.member_center.{$event}");
                return [false,'查询时间戳错误'];
            }
            sort($periodTime);
            $body = [$periodTime[0]*1000,$periodTime[1]*1000];
        }


        $arr =  array_merge(['uid'=>$uid],$arr);

        list($s,$data) = $this -> __request($event,$body,$method,[],$arr);
        if(!$s){
            $msg = $data['msg'] ?? '';
            return [false,$msg];
        }
        $realData = $data['data'] ?? [];

        //清除用户对应缓存
        foreach (self::CLEAR_REDIS_FUNCTIONS as $func){
            $this-> __delMemberCenterKey($func, $user_id);
        }

        return [true,$realData];
    }

    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param array $header
     * @param array $arr
     * @param string $type
     * @return array
     * 根据URL转接数据
     */
    private function __request($event, array $body = [], string $method = 'POST', array $header = [], array $arr = [], string $type = 'json'): array
    {
        // 获取请求的 URL
        $requestUrl = self::URL[$event] ?? '';
        if (empty($requestUrl)) {
            return [false, '事件不存在！'];
        }
        $url = $this->dreame_member_center_domain . $requestUrl;

        // 特殊 URL 处理
        if ($event == 'SCORE_COLLECT') {
            $id = $arr['id'] ?? '';
            if ($id) {
                $url .= '/' . $id;
            }
            unset($arr['id']);
        }
        if ($event == 'SIGN_IN_MONTH_DETAIL') {
            unset($arr['period_time']);
        }

        // 拼接 URL 参数
        if ($arr) {
            $url .= '?' . http_build_query($arr);
        }

        // 设置请求头部
        $header = empty($header) ? [
                "Content-Type:application/json",
                "cache-control:no-cache",
                "Dreame-internal:4E1C3wQHbecgTGrE==",
                "tenantId:" . CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
                "Expect: "
        ] : $header;

        if (isset($arr['version'])){
            $header[] = "version:" . $arr['version'];
        }

        // 如果是 JSON 类型，则编码请求体
        if ($type == 'json') {
            $body = json_encode($body, 320);
        }

        // 请求超时处理，设置请求的超时时间（例如20秒）
        $timeout = 201;

        // 发送请求
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method, $timeout);

        // 调试模式下打印日志
        if (!YII_ENV_PROD) {
            CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|header:" . json_encode($header, 320) . "|data:" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "member_center.{$event}");
        }

        // 请求错误时记录错误日志
        if (!$status || $httpCode !== 200 || !empty($err) || !isset($ret['code'])) {
            CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|header:" . json_encode($header, 320) . "|data:" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "member_center.{$event}");
            return [false, '请求失败'];
        }

        // 返回正常结果
        return [$ret['code'] === 0, $ret];
    }



    private function __getMonthsByPeriodTime($periodTime,$isms=false): array
    {
        $monthBeginTimestampList = [];
        if(empty($periodTime)|| !is_array($periodTime)) return [];
        $periodTime = array_unique(array_filter($periodTime));
        if(count($periodTime) == 1){
            $time = intval($periodTime[0]);
            $time = strtotime(date('Y-m',$time));
            if($isms) $time = $time.'000';
            $monthBeginTimestampList[] = (int)$time;
        }else{
            $startTime = intval($periodTime[0] ?? 0);
            $endTime = intval($periodTime[1] ?? 0);
            $startYear = date('Y',$startTime);
            $endYear = date('Y',$endTime);
            $startMonth = date('m',$startTime);
            $endMonth = date('m',$endTime);
            $date_arr = [];
            if ($startYear == $endYear) {//本年
                for ($month_i = $startMonth; $month_i <= $endMonth; $month_i++) {
                    $date_arr[] = $startYear . '-' . sprintf("%02d", $month_i);//使用不满2位数时填充0的月份
                }
            } else {//跨年
                for ($year_i = $startYear; $year_i <= $endYear; $year_i++) {
                    if ($year_i == $startYear) {
                        //开始年 和'结束年'不同，月从'开始月'直接循环到12月
                        for ($month_i = $startMonth; $month_i <= 12; $month_i++) {
                            $date_arr[] = $year_i . '-' . sprintf("%02d", $month_i);
                        }
                    } else if ($year_i == $endYear) {
                        //结束年 月循环到'结束月'
                        for ($month_i = 1; $month_i <= $endMonth; $month_i++) {
                            $date_arr[] = $year_i . '-' . sprintf("%02d", $month_i);
                        }
                    } else {
                        //中间年 从1月循环到12月
                        for ($month_i = 1; $month_i <= 12; $month_i++) {
                            $date_arr[] = $year_i . '-' . sprintf("%02d", $month_i);
                        }
                    }
                }
            }
            if($date_arr){
                foreach ($date_arr as $value){
                    $value = strtotime($value);
                    if($isms) $value = $value.'000';
                    $monthBeginTimestampList[]=(int)$value;
                }
            }
        }

         return $monthBeginTimestampList;
    }

    public function pointGrowSave($event, $data)
    {
        if (empty($data) || empty($event)) return [false, '参数错误'];
        $code = self::POINT_GROW_EVENT[$event] ?? '';
        if (empty($code)) {
            CUtil::debug("point_event:{$event}:积分事件不存在~", "err.dreamehome.pointGrowSave");
            return [false, '积分事件不存在'];
        }
        //data是二维数组 划分为40个一组
        $data       = array_chunk($data, 40);
        $resData   = [];
        foreach ($data as $item) {
            $pointInfos = [];
            foreach ($item as $value){
                $model = $value['model'];
                $point = $grow = 0;
                if ($model == 'point') {
                    $point = $value['score'];
                } else {
                    $grow = $value['score'];
                }
                $operType = ($value['type'] == "add") ? 1 : -1;
                // 组合推送数据
                $pointInfos[] = [
                    'uid'       => $value['uid'],
                    'serialNo'  => $value['push_no'],
                    'timestamp' => $value['ctime'] * 1000,
                    'ext'       => json_encode(['source' => $value['source'], 'sub_source' => $value['sub_source']],320),
                    'grow'      => $grow,
                    'point'     => $point,
                    'operType'  => $operType,
                ];
            }

            $body = [
                'code'       => $code,
                'pointInfos' => $pointInfos
            ];

            $header = [
                "Content-Type:application/json",
                "cache-control:no-cache",
                "Dreame-internal:4E1C3wQHbecgTGrE==",
                "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
                "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
                "Expect: "
            ];
            list($s, $data) = $this->__request('POINT_GROW_SAVE', $body, 'POST', $header);
            $resData = array_merge($resData, $data['data'] ?? []);
        }

        return [true, $resData];
    }

    public function memberActivityCheckin($id, $name, $begin_time, $end_time, $daily_points, $checkin_days, $checkin_reward_points, $check_type = 0): array
    {
        $begin_time = mb_strlen($begin_time) > 10 ? $begin_time : $begin_time * 1000;
        $end_time = mb_strlen($end_time) > 10 ? $end_time : $end_time * 1000;

        $body = [
            'id' => $id,
            'name' => $name,
            'is_enable' => 1,
            'code' => 'activity_checkin',
            'ext' => [
                'checkType' => $check_type,
                'continueCheckInConfig' => [
                    'beginDate' => $begin_time,
                    'endDate' => $end_time,
                    'point' => $checkin_reward_points,
                    'days' => $checkin_days,
                    'activity' => [
                        'beginLevelDate' => $begin_time,
                        'endLevelDate' => $end_time,
                        'point' => $daily_points,
                    ]
                ]
            ],
        ];

        if (empty($id)) {
            unset($body['id']);
        }

        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
            "Expect: "
        ];

        list($status, $resp) = $this->__request('ACTIVITY_RULEFINE', $body, 'POST', $header);

        if (! $status) {
            return [false, 0];
        }
        
        return [true, $resp['data']['id'] ?? 0];
    }

    public function memberActivityReturnPoints($id, $name, $begin_time, $end_time, $return_multiple): array
    {
        $begin_time = mb_strlen($begin_time) > 10 ? $begin_time : $begin_time * 1000;
        $end_time = mb_strlen($end_time) > 10 ? $end_time : $end_time * 1000;

        $body = [
            'id' => $id,
            'name' => $name,
            'is_enable' => 1,
            'code' => 'activity_buy_goods',
            'ext' => [
                'activityPointProrate' => [
                    'beginDate' => $begin_time,
                    'endDate' => $end_time,
                    'redouble' => $return_multiple,
                ]
            ],
        ];

        if (empty($id)) {
            unset($body['id']);
        }

        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
            "Expect: "
        ];
        
        list($status, $resp) = $this->__request('ACTIVITY_RULEFINE', $body, 'POST', $header);

        if (! $status) {
            return [false, 0];
        }
        
        return [true, $resp['data']['id'] ?? 0];
    }
    
    /**
     * @param string $code 事件code
     * @param int $user_id 商城用户ID
     * @param int $point 积分数值
     * @param int $gold 金币数值
     * @param int $operType -1：扣积分，1：加积分
     * @param int $operTypeGold -1：扣金币，1：加金币
     * @param string $name 自定义名称，默认按照规则配置
     * 有值的替换自定义名称
     * @return array
     * @throws Exception
     */
    public function syncPointExchange(string $code, int $user_id, int $point, int $gold, int $operType, int $operTypeGold, string $name): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:syncPointExchange|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
        }
        $uid = $mallInfo['uid'];
        $timestamp = round(microtime(true) * 1000);
        $body = [
            'code' => $code,
            'timestamp' => $timestamp,
            'uuid' => Uuid::uuid(),
            'uid' => $uid,
            'point' => $point,
            'gold' => $gold,
            'operType' => $operType,
            'operTypeGold' => $operTypeGold,
            'name' => $name,
        ];

        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
            "Expect: "
        ];
        
        list($status, $resp) = $this->__request('GOLD_EXCHANGE_POINT', $body, 'POST', $header);

        if (! $status) {
            return [false, $resp['msg'] ?? 'unknown error'];
        }
        
        return [true, $resp['msg'] ?? 'ok'];
    }

    /**
     * 金币兑换积分（同步）
     * @param int $user_id 商城用户ID
     * @param int $gold 金币数值
     * @param int $point 积分数值
     * @return array
     * @throws Exception
     */
    public function syncGoldExchangePoint(int $user_id, int $gold, int $point): array
    {
        return $this->syncPointExchange('mall/dreame/point_exchange', $user_id, $point, $gold, 1, -1, '兑换积分');
    }

    /**
     * 扣除金币（同步）
     * @param int $user_id 商城用户ID
     * @param int $gold 金币数值
     * @return array
     * @throws Exception
     */
    public function syncReduceGold(int $user_id, int $gold): array
    {
        return $this->syncPointExchange('mall/dreame/raffle_exchange', $user_id, 0, $gold, 0, -1, '幸运抽奖');
    }
    
    /**
     * 抽奖-抽中积分
     * @param int $user_id 商城用户ID
     * @param int $point 积分
     * @return array
     * @throws Exception
     */
    public function sendDrawPoint(int $user_id, int $point): array
    {
        return $this->syncPointExchange('mall/dreame/raffle', $user_id, $point, 0, 1, 0, '幸运抽奖');
    }

    /**
     * 抽奖-抽中金币
     * @param int $user_id 商城用户ID
     * @param int $gold 金币
     * @return array
     * @throws Exception
     */
    public function sendDrawGold(int $user_id, int $gold): array
    {
        return $this->syncPointExchange('mall/dreame/raffle_exchange', $user_id, 0, $gold, 0, 1, '幸运抽奖');
    }
}
