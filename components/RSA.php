<?php
/**
 * Created by IntelliJ IDEA.
 * User: Wallet
 * Date: 2019/8/26
 * Time: 12:26
 */
namespace app\components;

use app\models\CUtil;

class RSA {

    CONST TYPE = [
        'PUB_KEY_ENCODE' => 0,//公钥加密
        'PRI_KEY_DECODE' => 1,//私钥解密
        'PRI_KEY_ENCODE' => 2,//私钥加密
        'PUB_KEY_DECODE' => 3,//公钥解密
    ];

    //生成公钥私钥配置信息
    CONST CONFIG = [
        "DIGEST_ALG"        => "sha512",
        "PRIVATE_KEY_BITS"  => 4096,
        "PRIVATE_KEY_TYPE"  => OPENSSL_KEYTYPE_RSA,
    ];

    static $public_key;
    static $private_key;
    static $public_key_len;
    static $private_key_len;

    public static function CreateKey() {
        $res        = openssl_pkey_new(self::CONFIG);
        openssl_pkey_export($res, $private_key);
        $public_key = openssl_pkey_get_details($res);
        $public_key = $public_key["key"];
        var_dump($private_key);    //私钥
        var_dump($public_key);     //公钥
    }

    /**
     * @param $string
     * @param int $opt
     * @param string $pub_key : 自定义公钥
     * @param string $pri_key : 自定义私钥
     * @return string
     * 公钥私钥加密解密
     */
    public static function Crypt($string,$opt=0,$pub_key='',$pri_key=''): string
    {
        self::$public_key  = empty($pub_key) ? CUtil::getConfig('rsa_pub_key','security',MAIN_MODULE) : $pub_key;
        self::$private_key = empty($pri_key) ? CUtil::getConfig('rsa_pri_key','security',MAIN_MODULE) : $pri_key;

        $pub_id                 = openssl_pkey_get_public(self::$public_key);
        self::$public_key_len   = openssl_pkey_get_details($pub_id)['bits'];

        $pri_id                 = openssl_pkey_get_private(self::$private_key);
        self::$private_key_len  = openssl_pkey_get_details($pri_id)['bits'];

        switch ($opt) {
            case self::TYPE['PUB_KEY_ENCODE'] : // 客户端公钥加密
                //分段加密
                $crypted = self::pubEnc($string);
                break;
            case self::TYPE['PRI_KEY_DECODE'] : // 服务器私钥解密
                $crypted = self::priDec($string);
                break;
            case self::TYPE['PRI_KEY_ENCODE'] : // 服务器私钥加密
                //分段
                $crypted = self::priEnc($string);
                break;
            case self::TYPE['PUB_KEY_DECODE'] : // 客户端公钥解密
                //分段
                $crypted = self::pubDec($string);
                break;
            default :
                $crypted = ""; break;
        }

        return $crypted;
    }

    /**
     * 公钥加密
     */
    private static function pubEnc($string)
    {
        $part_len = self::$public_key_len / 8 - 11;
        $split    = str_split($string, $part_len);
        $crypto   = '';
        foreach ($split as $chunk) {
            openssl_public_encrypt($chunk, $crypted, self::$public_key);
            $crypto .= $crypted;
        }
        $crypted     = base64_encode($crypto);
        return $crypted;
        /*openssl_public_encrypt($string, $crypted, $public_key);
        $crypted     = base64_encode($crypted);*/
    }

    /**
     * 公钥解密
     */
    private static function pubDec($string)
    {
        $string   = base64_decode($string);
        $part_len = self::$public_key_len / 8 ;
        $split    = str_split($string, $part_len);
        $crypto   = '';
        foreach ($split as $chunk) {
            $crypted = '';
            openssl_public_decrypt($chunk,$crypted, self::$public_key);
            $crypto .= $crypted;
        }
        $crypted = $crypto;
        return $crypted;
        /*$string      = base64_decode($string);
        openssl_public_decrypt($string,$crypted,$public_key);*/
    }

    /**
     * 私钥加密
     */
    private static function priEnc($string)
    {
        $part_len = self::$private_key_len / 8 - 11;
        $split    = str_split($string, $part_len);
        $crypto   = '';
        foreach ($split as $chunk) {
            $crypted = '';
            openssl_private_encrypt($chunk, $crypted, self::$private_key);
            $crypto .= $crypted;
        }
        //var_dump($crypto);
        $crypted     = base64_encode($crypto);
        return $crypted;

        /*openssl_private_encrypt($string, $crypted, $private_key);
        $crypted     = base64_encode($crypted);*/
    }

    /**
     * @param $string
     * 私钥解密
     */
    private static function priDec($string)
    {
        $string      = base64_decode($string);
        $part_len    = self::$private_key_len / 8 ;
        $split       = str_split($string, $part_len);
        $crypto      = '';
        foreach ($split as $chunk) {
            $crypted = '';
            openssl_private_decrypt($chunk,$crypted, self::$private_key);
            $crypto .= $crypted;
        }
        $crypted = $crypto;
        return $crypted;
        /*$string      = base64_decode($string);
        openssl_private_decrypt($string,$crypted,$private_key);*/
    }
}