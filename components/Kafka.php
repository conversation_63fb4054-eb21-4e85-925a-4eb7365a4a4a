<?php

namespace app\components;


use app\exceptions\KafkaException;
use app\models\CUtil;
use ErrorException;
use yii\db\Exception;

class Kafka
{
    private static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
        date_default_timezone_set('PRC');
    }

    private function __clone()
    {
    }

    private $_obj = [];

    // kafka配置topic
    const KAFKA_TOPICS = YII_ENV_PROD ? [ // 生产环境
            'SYNC_MALL_LOG' => [
                    'topic'       => 'dreame_mall_log_prod_business',
                    'action_type' => 'mall-log'
            ],
    ] : (YII_ENV_UAT ? [ // UAT环境
            'SYNC_MALL_LOG' => [
                    'topic'       => 'dreame_mall_log_uat_business',
                    'action_type' => 'mall-log'
            ],
    ] : [ // DEV环境
            'SYNC_MALL_LOG' => [
                    'topic'       => 'dreame_mall_log_dev_business',
                    'action_type' => 'mall-log'
            ],
    ]);

    /**
     * @return Kafka
     */
    public static function factory(): Kafka
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    public function producer($topic, $data = [], $ifSync = false)
    {
        $config_key   = "kafka_produce";//省略kafka_前缀
        $kafka_config = CUtil::getConfig($config_key, 'kafka', \Yii::$app->id);
        if (empty($kafka_config) || empty($data)) {
            return [false, 'kafka 配置不存在或数据不存在！'];
        }


        /**
         * 自定义错误处理器，将 PHP 错误转换为 \ErrorException 异常抛出。
         *
         * @param int    $severity 错误级别
         * @param string $message  错误信息
         * @param string $file     出错文件
         * @param int    $line     出错行号
         * @throws ErrorException
         */
        set_error_handler(/**
         * @throws ErrorException
         */ function ($severity, $message, $file, $line) {
            if (!(error_reporting() & $severity)) {
                return;
            }
            throw new ErrorException($message, 0, $severity, $file, $line);
        });
        try {

            $config = \Kafka\ProducerConfig::getInstance();
            $config->setMetadataRefreshIntervalMs(10000);
            $config->setMetadataBrokerList($kafka_config['brokerList']);
            $config->setBrokerVersion('1.0.0');
            $config->setRequestTimeout(10000);
            $config->setRequiredAck(-1); // 是否需要ack回报.
            $config->setIsAsyn($ifSync); // 是否异步.（异步慎重，定时任务不兼容）
            $config->setProduceInterval(500);
            $producer = new \Kafka\Producer();

            // 添加头部信息
            $headers = ['tenantId' => CUtil::getConfig('tenantId', 'member', MAIN_MODULE)];
            $producer->send([
                    [
                            'topic'   => $topic,
                            'value'   => $data,
                            'key'     => $topic . '_key',
                            'headers' => $headers
                    ],
            ]);

        } catch (\Throwable $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            return [false, $error];
        } finally {
            // 恢复默认的错误处理器
            restore_error_handler();
        }

        return [true, 'ok'];
    }


    public function send($topic, array $payload = [], bool $isSynchronous = false)
    {
        // 将消息转换为 JSON 格式
        $payloadJson = json_encode($payload, 320);

        try {
            // 调用 Kafka 生产者发送消息
            list($status, $info) = $this->producer($topic, $payloadJson, $isSynchronous);

            // 如果 Kafka 返回失败，抛出异常
            if (!$status) {
                throw new KafkaException($info);
            }
        } catch (\Throwable $KafkaException) {
            // 捕获异常，准备发送飞书通知
            $data['title'] = sprintf('%s Kafka异常告警', date('Y-m-d H:i:s'));

            $message = mb_convert_encoding($KafkaException->getMessage(), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]);
            // 将 topic 和异常信息添加到飞书内容中
            $data['contents'][] = sprintf('**%s** 环境，topic：%s，异常信息：%s，请及时关注！', YII_ENV, $topic, $message);

            // 将 payload 添加到飞书消息内容中，确保我们知道推送的消息内容
            $data['contents'][] = sprintf('推送的 Kafka 消息内容：%s', $payloadJson);

            // 发送飞书消息
            CUtil::sendMsgToFs($data, 'kafkaNotice', 'interactive');
        }
    }


    public function consume($topic)
    {
        $config_key   = "kafka_consume";//省略kafka_前缀
        $kafka_config = CUtil::getConfig($config_key, 'kafka', \Yii::$app->id);
        if (empty($kafka_config)) {
            return [false, 'kafka 配置不存在！'];
        }
        is_string($topic) && $topic = array_unique(array_filter(explode(',', $topic)));

        try {
            $config = \Kafka\ConsumerConfig::getInstance();
            $config->setMetadataRefreshIntervalMs($kafka_config['refreshInternalMs']);
            $config->setMetadataBrokerList($kafka_config['brokerList']);
            $config->setGroupId($kafka_config['groupId']);
            $config->setBrokerVersion($kafka_config['brokerVersion']);
            $config->setTopics($topic);
            $consumer = new \Kafka\Consumer();
            $consumer->start(function ($topic, $part, $message) {
                CUtil::debug(json_encode($message), 'kafka.consume.msg');
            });
            return true;
        } catch (\Exception $e) {
            //防止乱码
            $message = mb_convert_encoding($e->getMessage(), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]);
            $error   = "Kafka_consume Error: {$message}";
            trigger_error($error, E_USER_ERROR);
        }
    }


}
