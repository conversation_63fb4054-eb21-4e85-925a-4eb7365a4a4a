<?php


namespace app\components;

use app\models\CUtil;
use Yii;
use app\models\by;

require_once Yii::getAlia<PERSON>("@vendor") . "/mycompany/alipay-aop/AopClient.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/AopCertClient.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/AopCertification.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/AlipayConfig.php";
require_once Yii::getAlias("@vendor") . "/mycompany/alipay-aop/request/AlipaySystemOauthTokenRequest.php";
require_once Yii::getAlia<PERSON>("@vendor") . "/mycompany/alipay-aop/request/AlipayUserInfoShareRequest.php";

class AliApplet
{
    CONST OAUTH_TOKEN_EVENT = "alipay.system.oauth.token";
    CONST USER_INFO_SHARE_EVENT = "alipay.user.info.share";
    
    public static function __getAuthTokenKey($appId,$event,$code): string
    {
        return AppWRedisKeys::GetAlipayAuthTokenKey($appId,$event,$code);
    }

    public static function __getAuthCodeByUserId($userId): string
    {
        return AppWRedisKeys::GetAlipayAuthCodeByUserId($userId);
    }


 


    /**
     * @param $authCode
     * @return array
     * @throws \Exception
     * 用户授权token
     */
    public static function GetUserAuthToken($authCode): array
    {
        $config = self::GetAlipayConfig();
        $appId = $config->getAppId();
        $event = self::OAUTH_TOKEN_EVENT;
        $key = self::__getAuthTokenKey($appId,$event,$authCode);
        $redis = by::redis();
        $token = $redis->get($key);

        // 判断是否存在token，或者token的有效期是否小于30
        if(!$token || $redis->ttl($key) < 30){
            $grant_type = $token ? 'refresh_token' : 'authorization_code';
            $authCode = $token ? json_decode($token, true)['refresh_token'] : $authCode;

            list($status, $msg) = self::AuthToken($authCode, $grant_type);

            if (!$status){
                return [false,$msg];
            }

            // 将新的token存入redis
            $redis->set($key, json_encode($msg, 320), 3600);
            return [true, $msg];
        }

        return [true, json_decode($token, true)];
    }


    public static function GetUserInfoByAccessToken($accessToken): array
    {
        $config =  CUtil::getConfig('alipay_applet', 'alipay', MAIN_MODULE);
        // 初始化SDK
        $aop = new \AopClient();
        $aop->gatewayUrl = $config['serverUrl'];
        $aop->appId = $config['appId'];
        $aop->rsaPrivateKey = $config['rsaPrivateKey'];
        $aop->alipayrsaPublicKey = $config['alipayrsaPublicKey'];
        $aop->apiVersion = '1.0';
        $aop->signType = 'RSA2';
        $aop->postCharset='UTF-8';
        $aop->format='json';
         // 构造请求参数以调用接口
        $request = new \AlipayUserInfoShareRequest ();
        $result = $aop->execute($request,$accessToken); 

        $responseNode = str_replace(".", "_", $request->getApiMethodName()) . "_response";
        $resultCode = $result->$responseNode->code;
        if(!empty($resultCode)&&$resultCode == 10000){
            $arr = json_decode(json_encode($result->$responseNode),true);
            if(!empty($arr)){
                return [true, $arr];
            }else{
                // CUtil::debug("event:alipay.auth|err:{$result->$responseNode->code}| ret:" . json_encode($result,320), "err.alipay.user_info");
                CUtil::setLogMsg(
                    "err.alipay.user_info",
                    $config,
                    json_encode($result,JSON_UNESCAPED_UNICODE),
                    [],
                    'https://openapi.alipay.com/gateway.do',
                    $result->$responseNode->code,
                    [],
                    200
                );
                return [false, $resultCode];
            }
        } else {
            // CUtil::debug("event:alipay.auth|err:{$result->$responseNode->code}| ret:" . json_encode($result,320), "err.alipay.user_info");
            CUtil::setLogMsg(
                "err.alipay.user_info",
                $config,
                json_encode($result,JSON_UNESCAPED_UNICODE),
                [],
                'https://openapi.alipay.com/gateway.do',
                $result->$responseNode->code,
                [],
                200
            );
            return [false, $resultCode];
        }
    }

    /**
     * @param $userId
     * @param $authToken
     * @return bool
     * 设置用户授权code
     */
    public static function SetAlipayAuthCodeByUserId($userId,$authToken): bool
    {
       $key = self::__getAuthCodeByUserId($userId);
       by::redis()->set($key, json_encode($authToken, 320), $authToken['expires_in']-40);
       return true;
    }

    /**
     * @param $userId
     * @return array
     * 获取用户授权code
     */
    public static function GetAlipayAuthCodeByUserId($userId): array
    {
        $key = self::__getAuthCodeByUserId($userId);
        if(!by::redis()->exists($key)){
            return [false,"用户授权令牌不存在"];
        }
        return [true,json_decode(by::redis()->get($key),true)];
    }



    /**
     * @param $code  //authCode 或 refresh_token
     * @param $grant_type
     * @return array
     * @throws \Exception
     *  https://opendocs.alipay.com/open/84bc7352_alipay.system.oauth.token?scene=common&pathHash=fe1502d5
     *  alipay.system.oauth.token(换取授权访问令牌)
     */
    public static function AuthToken($code,$grant_type = 'authorization_code'): array
    {
        // 初始化SDK
        $alipayClient = new \AopClient(self::GetAlipayConfig());
        // 构造请求参数以调用接口
        $request = new \AlipaySystemOauthTokenRequest();
        // 设置刷新令牌
        $request->setRefreshToken($code . "@dreame.tech");
        // 设置授权码
        $request->setCode($code);
        // 设置授权方式
        $request->setGrantType("authorization_code");
        $responseResult  = $alipayClient->execute($request);
        $responseApiName = str_replace(".", "_", $request->getApiMethodName()) . "_response";
        $response        = $responseResult->$responseApiName ?? null;
        if(empty($response)){
            // CUtil::debug("event:alipay.auth|grant_type:{$grant_type}| ret:" . json_encode($responseResult,320), "err.alipay.auth");
            CUtil::setLogMsg(
                "err.alipay.auth",
                ['code'=>$code,'grant_type'=>$grant_type],
                json_encode($responseResult,JSON_UNESCAPED_UNICODE),
                [],
                'https://opendocs.alipay.com/open/84bc7352_alipay.system.oauth.token',
                'event:alipay.auth:授权失败',
                [],
                200
            );
            return [false, "授权失败~"];
        }
        //将$msg转为数组
        $msg = json_decode(json_encode($response), true) ?? [];
        if (is_array($msg)&&!empty($msg)) {
            return [true, $msg];
        }else{
            // CUtil::debug("event:alipay.auth|grant_type:{$grant_type}| ret:" . json_encode($responseResult,320), "err.alipay.auth");
            CUtil::setLogMsg(
                "err.alipay.auth",
                ['code'=>$code,'grant_type'=>$grant_type],
                json_encode($responseResult,JSON_UNESCAPED_UNICODE),
                [],
                'https://opendocs.alipay.com/open/84bc7352_alipay.system.oauth.token',
                'event:alipay.auth:授权失败',
                [],
                200
            );
            return [false, "授权失败~"];
        }
    }

    public static function GetAlipayConfig()
    {
        $config          = CUtil::getConfig('alipay_applet', 'alipay', MAIN_MODULE);
        $privateKey      = $config['privateKey'];
        $alipayPublicKey = $config['alipayPublicKey'];
        $alipayConfig    = new \AlipayConfig();
        $alipayConfig->setServerUrl($config['serverUrl']);
        $alipayConfig->setAppId($config['appId']);
        $alipayConfig->setPrivateKey($privateKey);
        $alipayConfig->setFormat('json');
        $alipayConfig->setAlipayPublicKey($alipayPublicKey);
        $alipayConfig->setCharset('UTF-8');
        $alipayConfig->setSignType('RSA2');
        $alipayConfig->setAlipayPublicCertPath($config['alipayPublicCertPath'] ?? '');
        return $alipayConfig;
    }












}


