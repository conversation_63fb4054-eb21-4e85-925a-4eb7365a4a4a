<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;

/**
 * Mall权限验证加密等
 */
class Device
{
    protected static $_instance = [];
    private $dreame_home_domain;

    public $param;

    const URL = [
        'LIST_V2'                => '/dreame-user-iot/iotuserbind/device/listV2',                  //设备列表 POST
        'LIST_V3'                => '/dreame-user-iot/iotuserbind/device/listV3',                  //设备列表v3版本 POST
        'GET_DEVICE_MESSAGES'    => '/dreame-messaging/user/device-messages',                      //设备消息详情 GET
        'DELETE_DEVICE_MESSAGES' => '/dreame-messaging/user/device-messages',                      //删除设备消息  DELETE,
        'SEND_COMMAND'           => "/dreame-iot-com-10000/device/sendCommand",                    //发送设备指令 POST
        'MARK_READ_BY_DEVICEID'  => "/dreame-messaging/user/device-messages/mark-read-by-deviceid",//标记设备  消息已读 PUT
        'ALIGREEN_CONTENT'       => "/dreame-resource/aligreen/content",                           //阿里云文本审核 POST
        'PROPS'                  => "/dreame-user-iot/iotstatus/props",                            //查询设备最近已上报过的属性记录 POST
    ];

    const DREAME_RLC = '90960f006507601cde1d1a8ceefcff27'; //为当前region|当前语言|当前国家 然后AES加密之后的Hex字符串值


    const CONFIG = YII_ENV_PROD ? [
        'TIMEOUT' => '300',//单位ms 报文超时时间
        'USER'    => 'dreame_sys',
        'PWD'     => '4L6rf5SoBesxlMgc',
        'AESKEY'  => 'ceuK1nSbOav%wR#A'
    ] : [
        'TIMEOUT' => '3600',//单位ms 报文超时时间
        'USER'    => 'dreame_sys',
        'PWD'     => '4L6rf5SoBesxlMgc',
        'AESKEY'  => 'ceuK1nSbOav%wR#A'
    ];


    const CONFIG_CENTER = YII_ENV_PROD ? [
        'TIMEOUT' => '300',//单位ms 报文超时时间
        'USER'    => 'dreame_mall',
        'PWD'     => '4L6rf5SoBesxlMgc',
        'AESKEY'  => 'ceuK1nSbOav%wR#A'
    ] : [
        'TIMEOUT' => '3600',//单位ms 报文超时时间
        'USER'    => 'dreame_mall',
        'PWD'     => '4L6rf5SoBesxlMgc',
        'AESKEY'  => 'ceuK1nSbOav%wR#A'
    ];

    //必要的单例模式
    private function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    private function __clone()
    {
    }


    private $_obj = [];

    /**
     * @return Device
     */
    public static function factory(): Device
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance [__CLASS__] = new static();
        }
        return self::$_instance [__CLASS__];
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    protected function _request($user_id, $event, array $body = [], string $method = 'POST', array $arr = [], int $ttl = 10, int $ifCenter = 0): array
    {
        $url = self::URL[$event];
        if (empty($url)) {
            return [false, 'event不存在'];
        }

        list($status, $dreameAuth) = Mall::factory()->getGenerateJwtToken($user_id);
        if (!$status) {
            return [false, '生成token失败'];
        }

        //特殊调用
        if ($event == 'SEND_COMMAND') {
            $url = str_replace('10000', $body['bindId'] ?? 0, $url);
            unset($body['bindId']);
        }

        $url = $this->dreame_home_domain . $url;

        if ($arr) {
            $url .= '?' . http_build_query($arr);
        }

        $config = empty($ifCenter) ? (self::CONFIG ?? []) : (self::CONFIG_CENTER ?? []);

        $header = [
            "Content-Type:application/json",
            "dreame-meta:cv-wx-1000",
            "Authorization: Basic " . base64_encode("{$config['USER']}:{$config['PWD']}"),
            "dreame-rlc:" . self::DREAME_RLC,
            "dreame-auth:" . $dreameAuth,
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ];

        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body), $header, $method, $ttl);
        // CUtil::debug("event:{$event}|url:{$url}|httpcode:{$httpCode}|err:{$err}|header:" . json_encode($header) . "|data：" . json_encode($body) . " | ret:" . json_encode($ret), "device.{$event}");
        CUtil::setLogMsg(
            "device.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
        if (!$status) {
            return [false, $err];
        }

        //设备不在线返回单独code码80001
        if ($event == 'SEND_COMMAND' && $ret['code'] == 80001) {
            return [$ret['code'], $ret];
        }

        if ($ret['code'] == 401) {
            //重试  清除token
            Mall::factory()->__delGenerateJwtTokenKey($user_id);
            return $this->_request($user_id, $event, $body, $method, $arr);
        }

        return [$ret['code'] === 0, $ret];
    }


    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'list_v2':
            case 'list_v3':
                return $this->$function($args['user_id'], $args['sharedStatus'], $args['lang'], $args['offset'] ?? 1, $args['limit'] ?? 100);
            case 'get_device_messages':
                return $this->$function($args['user_id'], $args['did'], $args['offset'] ?? 0, $args['limit'] ?? 20);
            case 'delete_device_messages':
                return $this->$function($args['user_id'], $args['msgIds'] ?? '', $args['deviceId'] ?? '');
            case 'send_command':
                return $this->$function($args['user_id'], $args['id'] ?? '', $args['did'] ?? '', $args['data'] ?? [], $args['bindId'] ?? '');
            case 'mark_read_by_deviceid':
                return $this->$function($args['user_id'], $args['deviceId'] ?? '');
            case 'aligreen_content':
                return $this->$function($args['user_id'], $args['content'] ?? '');
            case 'props':
                return $this->$function($args['user_id'], $args['did'] ?? '');
            default:
                return [false, 'function 不存在'];
        }
    }


    //阿里云文本审核
    public function aligreen_content($user_id,$content): array
    {
        $body = [
          'content'=>$content
        ];
        list($status, $ret) = $this->_request($user_id, 'ALIGREEN_CONTENT', $body);
        if (!$status) {
            return [false, '审核失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    //设备列表
    public function list_v2($user_id, $sharedStatus, $lang, $offset, $limit): array
    {
        $body = [
            'sharedStatus' => $sharedStatus,
            'lang'         => $lang,
            'current'      => $offset,
            'size'         => $limit,
        ];
        list($status, $ret) = $this->_request($user_id, 'LIST_V2', $body);
        if (!$status) {
            return [false, '获取设备列表失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    public function list_v3($user_id, $sharedStatus, $lang, $offset, $limit): array
    {
        $body = [
                'sharedStatus' => $sharedStatus,
                'lang'         => $lang,
                'current'      => $offset,
                'size'         => $limit,
        ];
        list($status, $ret) = $this->_request($user_id, 'LIST_V3', $body);
        if (!$status) {
            return [false, '获取设备列表失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    //设备消息详情
    public function get_device_messages($user_id, $did, $offset, $limit): array
    {
        if (empty($did)) {
            return [false, '参数错误'];
        }
        $arr = [
            'offset' => $offset,
            'limit'  => $limit,
            'did'    => $did
        ];
        list($status, $ret) = $this->_request($user_id, 'GET_DEVICE_MESSAGES', [], 'GET', $arr);

        if (!$status) {
            return [false, '获取设备消息失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    //删除设备消息
    public function delete_device_messages($user_id, $msgIds, $deviceId): array
    {
        if (empty($msgIds)) {
            return [false, '参数错误'];
        }

        $arr = [
            'msgIds'   => $msgIds,
            'deviceId' => $deviceId
        ];

        list($status, $ret) = $this->_request($user_id, 'DELETE_DEVICE_MESSAGES', [], 'DELETE', $arr);

        if (!$status) {
            return [false, '删除设备消息失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    //发送设备指令
    public function send_command($user_id, $id, $did, $data, $bindId): array
    {
        if (empty($id) || strlen($id) > 9 || empty($did) || empty($bindId)) {
            return [false, '参数错误'];
        }

        $body = [
            'id'     => intval($id),
            'did'    => intval($did),
            'data'   => $data,
            'bindId' => $bindId
        ];

        list($status, $ret) = $this->_request($user_id, 'SEND_COMMAND', $body);

        if (!$status) {
            return [false, $ret['msg'] ?? '操作失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    //标记设备
    public function mark_read_by_deviceid($user_id, $deviceId): array
    {
        if (empty($deviceId)) {
            return [false, '参数错误'];
        }

        $arr = [
            'deviceId' => $deviceId
        ];

        list($status, $ret) = $this->_request($user_id, 'MARK_READ_BY_DEVICEID', [], 'PUT', $arr);

        if (!$status) {
            return [false, '标记设备消息失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }

    public function props($user_id, $did): array
    {
        $body = [
            'did'  => $did,
            'keys' => 1.5,
        ];

        list($status, $ret) = $this->_request($user_id, 'PROPS', $body);
        if (!$status) {
            return [false, '获取设备属性失败'];
        }
        return [$status, $ret['data'] ?? ''];
    }
}
