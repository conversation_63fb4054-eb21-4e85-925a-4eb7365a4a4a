<?php

namespace app\components;

use app\models\CUtil;

// IOT 设备信息相关查询
class IotDs
{
    const URL = YII_ENV_PROD ? "https://ds.dreame.tech:39910" : "https://ds.dreame.tech:39910";

    const METHOD_CODE = [
        'activelist'       => '/mall/activelist', //根据sns和uids获取用户的激活机器列表
        'cleanmodel'       => '/mall/cleanmodel', //根据用户uids和时间范围获取清扫记录
        'judgecleanrecord' => '/mall/judgecleanrecord',//根据sn和uid判断是否有清扫记录
    ];
    protected static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    public static function factory()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new self();
        }

        return self::$_instance[__CLASS__];
    }

    CONST UIDS_REFLECT = YII_ENV_PROD ? []:[
        //UAT => PROD
        'QI799865'=> 'PU739356',
        "NI357681"=> "NI357680",
    ];


    /**
     * @param $uids
     * @return array|string[]
     * UAT和PROD的uid映射
     */
    protected function uidsReflect($uids)
    {
        return array_values(array_map(function($uid) {
            return self::UIDS_REFLECT[$uid] ?? $uid;
        }, $uids));
    }

    /**
     * @param $res
     * @return mixed
     * UAT和PROD的结果映射反转
     */
    protected function ResReflectRev($res)
    {
        $uids_reflect_rev = array_flip(self::UIDS_REFLECT);
        foreach ($res as &$item) {
            if (isset($uids_reflect_rev[$item['uid']])) {
                $item['uid'] = $uids_reflect_rev[$item['uid']];
            }
        }
        return $res;
    }

    public function run(string $function, array $args): array
    {
        switch ($function) {
            case 'activelist':
                return $this->$function($args['sns'] ?? [],$args['uids'] ?? []);
            case 'cleanmodel':
                return $this->$function($args['uids'] ?? [], $args['startTime'] ?? 0, $args['endTime'] ?? 0);
            case 'judgecleanrecord':
                return $this->$function($args['uid'] ?? '', $args['sn'] ?? '');
            default:
                return [false, 'function 不存在'];
        }
    }

    //根据sns和uids获取用户的激活机器列表
    public function activelist($sns,$uids)
    {
        $item = [];
        if(!empty($sns)){
            $item['sns'] = $sns;
        }
        if(!empty($uids)){
            $uids = $this->uidsReflect($uids);
            $item['uids'] = $uids;
        }
        $url       = self::URL . self::METHOD_CODE['activelist'];
        $body      = json_encode($item);
        $headers   = [];
        $headers[] = 'Content-Type:application/json';
        $headers[] = 'Expect: ';
        $res       = CUtil::curl_post($url, $body, $headers, 10, true, '', '', true);
        $data      = (array)json_decode($res, true);
        $code       = CUtil::uint($data['code'] ?? 0);
        if($code != 0){
            // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "err.iot_ds.activelist");
            CUtil::setLogMsg(
                "err.iot_ds.activelist",
                $body,
                $data,
                $headers,
                $url,
                '',
                [],
                200
            );
        }
        // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "iot_ds.activelist");
        $res = $data['data'] ?? [];
        if($res){
            $res = $this->ResReflectRev($res);
        }
        return [true, $res];
    }

    //根据用户uids和时间范围获取清扫记录
    public function cleanmodel($uids,$startTime,$endTime)
    {
        if($uids) $uids = $this->uidsReflect($uids);
        $item = [
            'uids'      => $uids,
            'startTime' => $startTime,
            'endTime'   => $endTime,
        ];

        $url       = self::URL . self::METHOD_CODE['cleanmodel'];
        $body      = json_encode($item);
        $headers   = [];
        $headers[] = 'Content-Type:application/json';
        $headers[] = 'Expect: ';
        $res       = CUtil::curl_post($url, $body, $headers, 10, true, '', '', true);
        $data      = (array)json_decode($res, true);
        $code       = CUtil::uint($data['code'] ?? 0);
        if($code != 0){
            // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "err.iot_ds.cleanmodel");
            CUtil::setLogMsg(
                "err.iot_ds.cleanmodel",
                $body,
                $data,
                $headers,
                $url,
                '',
                [],
                200
            );
        }
        // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "iot_ds.cleanmodel");
        $res = $data['data'] ?? [];
        if($res) $res = $this->ResReflectRev($res);
        return [true, $res ?? []];
    }

    //根据sn和uid判断是否有清扫记录
    public function judgecleanrecord($uid,$sn)
    {
        //1.uid 映射
        if($uid){
            $uid = $this->uidsReflect([$uid])[0];
        }
        //2.请求
        $item = [
            'uid' => $uid,
            'sn'  => $sn,
        ];
        $url       = self::URL . self::METHOD_CODE['judgecleanrecord'];
        $body      = json_encode($item);
        $headers   = [];
        $headers[] = 'Content-Type:application/json';
        $headers[] = 'Expect: ';
        $res       = CUtil::curl_post($url, $body, $headers, 10, true, '', '', true);
        $data      = (array)json_decode($res, true);
        $code       = CUtil::uint($data['code'] ?? 0);
        if($code != 0){
            // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "err.iot_ds.judgecleanrecord");
            CUtil::setLogMsg(
                "err.iot_ds.judgecleanrecord",
                $body,
                $data,
                $headers,
                $url,
                '',
                [],
                200
            );
        }
        // CUtil::debug("url:{$url}|body:{$body}|res:{$res}", "iot_ds.judgecleanrecord");
        $res = $data['data'] ?? [];
        return [true, $res ?? []];
    }
}
