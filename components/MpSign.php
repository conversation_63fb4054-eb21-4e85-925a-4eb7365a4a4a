<?php

namespace app\components;

/**
 * 中台签名验签接口类
 */
class MpSign
{
    /**
     * 验签方法
     */
    public static function checkSign($data, $signKey)
    {
        $sign = $data['sign'];
        $timestamp = $data['timestamp'];
        // 去掉数组中的sign和timestamp
        unset($data['sign']);
        unset($data['timestamp']);

        // 是否判断签名过期时间，后期可以加进来

        // 生成签名
        $newSign = self::sign($data, $timestamp, $signKey);
        if($sign == $newSign){
            return true;
        }else{
            return false;
        }

    }

    // 生成签名,我并不想写那么多的代码，但是他们签名的规则太另类了
    public static function sign($data, int $timestamp, string $signKey){
        if(!is_array($data)){
            // 这里不严谨，但是我不想改了，因为理论上这边传的参数都是数组，如果非数组，那么肯定就是字符串了
            $str = $data;
        }else{
            $str = self::arrayToStr($data);
        }
        $str .= $timestamp . $signKey;
        return md5($str);
    }
    private static function arrayToStr($arr,$split = '&'){
        //如果是索引数组，则使用另外的方法转成字符串
        $bool = self::isMultiDimArray($arr);
        if($bool){
            $str = self::arrayNoKeyToStr($arr);
            return $str;
        }
        $targetArr = [];
        ksort($arr);
        foreach ($arr as $key => $value) {
            // if(is_numeric($key)){
            //     if(is_string($value) || is_numeric($value)){
            //         $targetArr[] = $value;
            //     }
            //     if(is_array($value)){
            //         $excessiveStr = self::arrayToStr($value);
            //         $targetArr[] = '['.$excessiveStr.']';
            //     }
            // }else{
            if(is_string($value) || is_numeric($value)){
                $targetArr[] = sprintf('%s=%s', $key, $value);
            }
            if(is_array($value)){
                $excessiveStr = self::arrayToStr($value);
                $targetArr[] = sprintf('%s=%s', $key, '['.$excessiveStr.']');
            }
            // }

        }
        return  implode($split, $targetArr);
    }
    //判断数组是否为数值数组
    private static function isMultiDimArray($array) {
        $keys = array_keys($array);
        $numKeys = array_map('intval', $keys);
        sort($numKeys);
        return $keys === $numKeys && $numKeys === range(0, count($array) - 1);
    }
    //数值数组特殊处理
    private static function arrayNoKeyToStr($arr){
        $targetArr = [];
        //判断是否为多维数组
        $m_arr = true;
        foreach ($arr as $value){
            if(is_array($value)){
                $targetArr[] = self::arrayToStr($value);
                $m_arr = false;
            }
        }
        if($m_arr){
            return implode(',', $arr);
        }else{
            return implode('', $targetArr);
        }
    }

}