<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;

/**
 * 更新用户的地址
 */
class HandleUserAddress
{
    /**
     * 处理更新
     */
    public function handleUpdate()
    {
        // 获取要更新的 aid
        $aidArr = $this->__getAidsForUpdate();
        $aids = implode("','", array_keys($aidArr));

        // 循环查询 t_address_{index} 的数据
        $index = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        foreach ($index as $i) {
            // 表
            $table = "`db_dreame`.`t_address_{$i}`";
            // redis
            $key = "dreame:HandleUserAddress:handleUpdate:id-$i";
            $id = by::redis()->get($key);
            $id = CUtil::uint($id);
            while (1) {
                $sql = "SELECT `id`, `user_id`, `aid` FROM {$table} WHERE aid IN ('{$aids}') AND id > {$id} LIMIT 500;";
                $items = by::dbMaster()->createCommand($sql)->queryAll();
                if (empty($items)) {
                    break;
                }
                $end = end($items);
                $id = $end['id'];
                // 循环处理
                $address = by::Address();
                foreach ($items as $item) {
                    // 更新数据
                    by::dbMaster()->createCommand()->update($table, ['aid' => $aidArr[$item['aid']]], ['id' => $item['id']])->execute();
                    // 删除缓存
                    $address->__delListCache($item['user_id']);
                    $address->__delCache($item['user_id'], $item['id']);
                }
                // 存redis
                by::redis()->set($key, $id, 7200);
                usleep(500);
            }
        }
    }

    /**
     * 处理删除
     */
    public function handleDelete()
    {
        // 获取要删除的 aid
        $aidArr = $this->__getAidsForDelete();
        $aids = implode("','", $aidArr);

        // 循环查询 t_address_{index} 的数据
        $index = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9];
        foreach ($index as $i) {
            // 表
            $table = "`db_dreame`.`t_address_{$i}`";
            // redis
            $key = "dreame:HandleUserAddress:handleDelete:id-$i";
            $id = by::redis()->get($key);
            $id = CUtil::uint($id);
            while (1) {
                $sql = "SELECT `id`, `user_id`, `aid` FROM {$table} WHERE aid IN ('{$aids}') AND is_del = 0 AND id > {$id} LIMIT 500;";
                $items = by::dbMaster()->createCommand($sql)->queryAll();
                if (empty($items)) {
                    break;
                }
                $end = end($items);
                $id = $end['id'];
                // 批量更新数据（软删除）
                by::dbMaster()->createCommand()->update($table, ['is_del' => 1, 'dtime' => time()], ['id' => array_column($items, 'id')])->execute();
                // 循环处理
                $address = by::Address();
                foreach ($items as $item) {
                    // 删除缓存
                    $address->__delListCache($item['user_id']);
                    $address->__delCache($item['user_id'], $item['id']);
                }
                // 存redis
                by::redis()->set($key, $id, 7200);
                usleep(500);
            }
        }
    }

    /**
     * 获取要更新 t_address_{index} 数据(aid)的 t_area_2 的 id
     * @return array
     * @throws \yii\db\Exception
     */
    public function __getAidsForUpdate(): array
    {
        $sql = <<<EOF
SELECT
t_error.id AS t_error_id,
t_normal.id AS t_normal_id
FROM
(
SELECT*FROM `db_dreame`.`t_area_2` AS area WHERE region_type=3 AND is_show=0) AS t_error
LEFT JOIN (
SELECT*FROM `db_dreame`.`t_area_2` AS area WHERE region_type=3 AND is_show=1) AS t_normal 
ON t_error.pid=t_normal.pid AND t_error.`name` = t_normal.`name`
WHERE t_normal.id is not null;
EOF;
        $items = by::dbMaster()->createCommand($sql)->queryAll();

        // 数据格式：['110228'=>110118, '110229'=>110119]
        $data = [];
        foreach ($items as $item) {
            $data[$item['t_error_id']] = $item['t_normal_id'];
        }
        return $data;
    }

    /**
     * 获取要删除 t_address_{index} 数据的 t_area_2 的 id
     * @return array
     * @throws \yii\db\Exception
     */
    public function __getAidsForDelete(): array
    {
        $sql = <<<EOF
SELECT
t_error.id AS t_error_id
FROM
(
SELECT*FROM `db_dreame`.`t_area_2` AS area WHERE region_type=3 AND is_show=0) AS t_error
LEFT JOIN (
SELECT*FROM `db_dreame`.`t_area_2` AS area WHERE region_type=3 AND is_show=1) AS t_normal 
ON t_error.pid=t_normal.pid AND t_error.`name` = t_normal.`name`
WHERE t_normal.id is null;
EOF;
        $items = by::dbMaster()->createCommand($sql)->queryAll();

        $data = [];
        foreach ($items as $item) {
            $data[] = $item['t_error_id'];
        }
        return $data;
    }
}