<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use Firebase\JWT\BeforeValidException;
use Firebase\JWT\ExpiredException;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use Firebase\JWT\SignatureInvalidException;
use yii\base\Exception;

class JwtTools
{
    private static $_instance = null;
    protected static $jwt_keys = [];
    protected static $jwt_expired_keys = [];
    protected static $jwt_expire_time = null;

    // 有效期

    // 单例模式
    private function __construct()
    {

        self::$jwt_keys         = CUtil::getConfig('jwt_token_key', 'jwt', \Yii::$app->id) ?? [];
        self::$jwt_expired_keys = CUtil::getConfig('jwt_token_expired_key', 'jwt', \Yii::$app->id) ?? [];
        self::$jwt_expire_time  = CUtil::getConfig('jwt_token_expire_time', 'jwt', \Yii::$app->id) ?? 3600;
    }

    private function __clone()
    {
    }

    /**
     * 获取 JwtTools 单例实例
     *
     * @return JwtTools
     */
    public static function factory(): JwtTools
    {
        if (self::$_instance === null) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * 生成 JWT 令牌
     *
     * @param array $userInfo 用户信息
     * @param int $expireTime 过期时间
     * @return string JWT 令牌
     * @throws Exception
     */
    public static function encodeJsonWebToken(array $userInfo, int $expireTime = null): string
    {
        if ($expireTime === null) {
            $expireTime = self::$jwt_expire_time;
        }

        $time    = time();
        $request = \Yii::$app->getRequest();

        $payload = [
                'iss' => $request->getHostInfo(),       // 签发者
                'aud' => $request->getUserIP(),         // 目标接收方（客户端 IP）
                'iat' => $time,                         // 签发时间
                'nbf' => $time,                         // 生效时间
                'exp' => $time + $expireTime,           // 过期时间
                'sub' => $userInfo,
                'jti' => \Yii::$app->security->generateRandomString(16) // 唯一标识
        ];

        // 从密钥数组中随机选择一个
        $keyIndex  = array_rand(self::$jwt_keys);
        $secretKey = self::$jwt_keys[$keyIndex];

        // 自定义 JWT 头部信息，包含 kid（Key ID）
        $header = [
                'kid' => $keyIndex,// token索引
        ];

        try {
            return JWT::encode($payload, $secretKey, 'HS256', null, $header);
        } catch (\Exception $e) {
            CUtil::debug('JWT 生成失败: ' . $e->getMessage() . '| payload:' . json_encode($payload, 320), 'err.jwt.encode');
            return '';
        }
    }

    /**
     * 解析 JWT 令牌
     *
     * @param string $jwtToken JWT 令牌
     * @return array [bool, array|string] 解码后的数据或错误信息
     */
    public static function decodeJsonWebToken(string $jwtToken): array
    {
        try {
            // 解析 JWT 头部，获取 `kid`
            $tokenParts = explode('.', $jwtToken);
            if (count($tokenParts) !== 3) {
                throw new \Exception("JWT 格式错误");
            }

            $header = json_decode(base64_decode($tokenParts[0]),true);

            if (!is_array($header) || !isset($header['kid'])) {
                throw new \Exception("JWT 头部缺少 Key ID");
            }

            $keyIndex = $header['kid'];

            // 获取正确的密钥（包含黑名单轮询）
            $secretKeys = isset(self::$jwt_keys[$keyIndex]) ? [self::$jwt_keys[$keyIndex]] : [];
            if (!empty(self::$jwt_expired_keys)) {
                $secretKeys = array_merge($secretKeys, self::$jwt_expired_keys);
            }

            // 轮询尝试解密
            foreach ($secretKeys as $secretKey) {
                try {
                    $decoded = JWT::decode($jwtToken, new Key($secretKey, 'HS256'));
                    return [true, (array) ($decoded->sub ?? [])];
                } catch (ExpiredException $e) {
                    CUtil::debug("JWT 解码成功但已过期: " . $e->getMessage(), 'warn.jwt.decode');
                    return [false, 'token expired']; // 返回过期状态
                } catch (SignatureInvalidException $e) {
                    CUtil::debug("JWT 签名无效: " . $e->getMessage(), 'warn.jwt.decode');
                } catch (BeforeValidException $e) {
                    CUtil::debug("JWT 令牌尚未生效: " . $e->getMessage(), 'warn.jwt.decode');
                }
            }

            throw new \Exception("所有密钥都无法解码 JWT");
        } catch (\Exception $e) {
            CUtil::debug("JWT 解析失败: " . $e->getMessage(), 'warn.jwt.decode');
            return [false, 'token is invalid or has expired'];
        }
    }


    /**
     * 将 Token 加入黑名单
     *
     * @param int|string $userId 用户ID
     * @param string $token 需要拉黑的 Token，传空则拉黑整个账户
     * @return bool 成功返回 true，失败返回 false
     */
    public static function addTokenToBlacklist($userId, string $token = ''): bool
    {
        $redis          = by::redis();
        $redisKey       = AppCRedisKeys::jwtTokenBlackList($userId);
        $blacklistValue = $token ?: 'all'; // 若 token 为空，则拉黑整个账户
        $expireTime     = $token ? self::$jwt_expire_time : 7200;

        try {
            // 如果黑名单已存在，则不重设过期时间
            if (!$redis->exists($redisKey)) {
                $redis->expire($redisKey, $expireTime);
            }

            return $redis->sAdd($redisKey, $blacklistValue) > 0;
        } catch (\Exception $e) {
            CUtil::debug("无法将 Token 加入黑名单：" . $e->getMessage(), 'err.addTokenToBlacklist');
            return false;
        }
    }

    /**
     * 检查 Token 是否无效
     *
     * @param int|string $userId 用户ID
     * @param string $token 需要验证的 Token
     * @return bool Token 无效返回 true，否则返回 false
     */
    public static function isTokenInvalid($userId, string $token): bool
    {
        $redis    = by::redis();
        $redisKey = AppCRedisKeys::jwtTokenBlackList($userId);

        // 获取所有黑名单项，减少 Redis 查询次数
        $blacklist = $redis->sMembers($redisKey);
        return in_array('all', $blacklist, true) || in_array($token, $blacklist, true);
    }
}
