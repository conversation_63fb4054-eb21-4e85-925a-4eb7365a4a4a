<?php

/**
 * Created by IntelliJ IDEA.
 * https://dwz.cn/console/apidoc/v3
 */

namespace app\components;

use app\models\CUtil;

class Dwz
{
    const URL           = "https://dwz.cn/api/v3/short-urls";
    const TOKEN         = "4b7e5ce867a2fd6773b45a2d30cdff1b";

    protected static $_instance = [];

    //必要的单例模式
    private function __construct()
    {
    }
    private function __clone()
    {
    }

    /**
     * @return Dwz
     * */
    public static function factory()
    {
        if (!isset(self::$_instance['dwz']) || !is_object(self::$_instance['dwz'])) {
            self::$_instance['dwz'] = new self();
        }

        return self::$_instance['dwz'];
    }


    /**
     * @param string $long_url
     * @return array
     * 生成短网址
     */
    public function getDwz($long_url='')
    {
        if (empty($long_url)) {
            return [false, '参数错误'];
        }

        $data   = [
            [
                'LongUrl'           => $long_url,
                'TermOfValidity'    => 'long-term',
            ]

        ];

        return $this->_request($data);
    }

    /**
     * @param array $post
     * @return array
     * 统一请求
     */
    protected function _request($post = [])
    {
        $request                = json_encode($post);

        $headers    = [
            'Dwz-Token:'.self::TOKEN,
            'Content-Type:application/json; charset=UTF-8',
            'Content-Language:zh',
        ];

        $res = CUtil::curl_post(self::URL, $request, $headers, 10, FALSE , '', '', true);

        // !YII_ENV_PROD && CUtil::debug($request.'|'.$res, 'dwz');
        
        $data   = (array)json_decode($res, true);

        if (!isset($data['Code']) || $data['Code'] < 0) {
            $json   = json_encode($data, JSON_UNESCAPED_UNICODE);
            // CUtil::debug("request:{$request}|return:{$json}", "err.dwz");
            CUtil::setLogMsg(
                "err.dwz",
                $request,
                $data,
                $headers,
                self::URL,
                $data['ErrMsg'],
                [],
                200
            );

            $msg    = $data['ErrMsg'] ?? '短网址生成失败';

            return [false, $msg];
        }

        return [true, $data];
    }
}
