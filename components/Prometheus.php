<?php

namespace app\components;

require __DIR__ . '/../vendor/autoload.php';

use app\jobs\MonitorJob;
use app\models\CUtil;
use app\models\MyExceptionModel;
use GuzzleHttp\Exception\GuzzleException;
use Prometheus\Exception\MetricsRegistrationException;
use Prometheus\RenderTextFormat;
use Prometheus\CollectorRegistry;
use Prometheus\Storage\Redis;
use yii\base\Exception;

class Prometheus
{
    private static $_instance = [];
    const URL      = YII_ENV_PROD ? 'https://pushgateway.dreame.tech' : (YII_ENV_TEST ? 'https://pushgateway.dreame.tech' :
        (YII_ENV_DEV ? 'http://10.10.37.100:4201' : 'https://pushgateway.dreame.tech'));//pushgateway 路径
    const JOB      = 'iot_dreame_mall';
    const INSTANCE = YII_ENV . '_interface';
    const USER = 'dreame';
    const PWD = 'dreame@2023';

    //必要的单例模式
    private function __construct()
    {
        date_default_timezone_set('PRC');
    }

    private function __clone()
    {
    }



    /**
     * @return Prometheus
     */
    public static function factory(): Prometheus
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    /**
     * @param string $type
     * @return Redis
     */
    public static function adapter(string $type = 'core'): Redis
    {
        $type       = strval($type);
        $config_key = "redis_{$type}";//省略redis_前缀
        $redis_config = CUtil::getConfig($config_key, 'common', \Yii::$app->id);
        $post_log_key = AppCRedisKeys::postLogKey('step');
        Redis::setPrefix($post_log_key);
        Redis::setDefaultOptions([
            'host'         => $redis_config['hostname'],
            'port'         => $redis_config['port'],
            'timeout'      => 0.1,
            'read_timeout' => '10',
            'password'     => $redis_config['password'],
            'database'     => 9,
        ]);
        return new Redis();
    }



    public function syncPrometheus($function,$args)
    {
        return true;
        \Yii::$app->queueMonitor->push(new MonitorJob(['function'=>$function,'args'=>$args]));
    }


    public function run(string $function, array $args)
    {
        switch ($function){
            case 'pushRegistry':
            case 'interfacePv':
            case 'interfaceUv':
                return $this->$function($args);
            case 'orderReturnAdd':
            case 'refundPush':
                return $this->$function($args['user_id'],$args['refund_no']);
            default:
                return [false,'function 不存在'];
        }
    }


    /**
     * @param $args
     * @return array|void
     */
    private function __interfaceBaseData($args){

        if(empty($args)){
            return [false,'访问地址有误！'];
        }
        $path = empty($args['url']) ? '' : parse_url($args['url'], PHP_URL_PATH);
        if(empty($path)){
            return [false,'访问地址有误！'];
        }
        $viewUrlArr = array_filter(explode('/',$path));
        $nameSpace = 'mall_'.($viewUrlArr[1] ?? '');
        $name = '';
        for($i=2;$i<=count($viewUrlArr);$i++){
            if($i == count($viewUrlArr)){
                $name .= $viewUrlArr[$i]??'';
            }else{
                $name .= $viewUrlArr[$i].'_';
            }
        }

        if(empty($nameSpace)|| empty($name)){
            return [false,'访问地址有误~！'];
        }
        $iRet = CUtil::uint($args['ext']['iRet'] ?? 0);
        $name = str_replace('-','_',$name);

        return [true, [
            'nameSpace' => $nameSpace,
            'name'      => $name,
            'iRet'      => $iRet
        ]];

    }

    /**
     * @throws MetricsRegistrationException
     * @throws GuzzleException
     */
    public function interfacePv($args)
    {
       list($s,$baseData) = $this->__interfaceBaseData($args);
       if(!$s){
           CUtil::debug('Push to server failed:'.$baseData,'err.prometheus.interfacePv');exit();
       }
       $nameSpace = $baseData['nameSpace'];
       $name = $baseData['name'];
       $iRet = $baseData['iRet'];

        $registry = \Prometheus\CollectorRegistry::getDefault();

        $counter = $registry->registerCounter($nameSpace, $name, 'it increases', ['status']);
        if($iRet>0){
            $counter->incBy(1, [true]);
        }else{
            $counter->incBy(1, [false]);
        }

        $url     = self::URL;
        $pushGateway = new PushGateway($url);
        try {
            $pushGateway->push($registry, self::JOB, ['instance' => self::INSTANCE]);
            CUtil::debug('Push to server success: ok'.json_encode($args), 'prometheus.interfacePv');
        } catch (Exception $e) {
            CUtil::debug('Push to server failed:'.$e->getMessage(),'err.prometheus.interfacePv');
        }
    }

    /**
     * @throws MetricsRegistrationException
     * @throws GuzzleException
     */
    public function interfaceUv($args)
    {
        list($s, $baseData) = $this->__interfaceBaseData($args);
        if (!$s) {
            CUtil::debug('Push to server failed: %s' . $baseData, 'err.prometheus.interfaceUv');
            return false;
        }

        $nameSpace = $baseData['nameSpace'];
        $name      = $baseData['name'];
        $iRet      = $baseData['iRet'];
        $adapter  = self::adapter();

        $registry = new CollectorRegistry($adapter);

        $histogram = $registry->registerHistogram($nameSpace, $name, 'it observes', ['status'], [20, 40, 60, 80, 100, 120, 140, 160, 200, 500, 1000, 2000]);
        $histogram->observe(round($args['timeUsed'], 2), [true]);

        $url    = self::URL;
        $header = ['Authorization' => 'Basic ' . base64_encode(self::USER . ':' . self::PWD)];

        $pushGateway = PushGatewayNew::factory($url,null);

        try {
            $pushGateway->pushAdd($registry, self::JOB, ['instance' => self::INSTANCE.'_uv'],$header);
        } catch (MyExceptionModel $e) {
            CUtil::debug('Push to server failed: %s' . $e->getMessage(), 'err.prometheus.interfaceUv');
        }
        return [true,'OK'];
    }


    /**
     * @return void
     * @throws MetricsRegistrationException
     * @throws GuzzleException
     */
    public function pushRegistry($args)
    {
        $adapter  = self::adapter();
        $url      = self::URL;
        $registry = new CollectorRegistry($adapter);
        $gateway = new PushGateway($url);

        $gauge = $registry->getOrRegisterGauge(
            'my_namespace',
            $args['url'],
            'A simple gauge example',
            ['my']
        );
        $gauge->set(42);

        $renderer = new RenderTextFormat();
        $gateway->push(
            $registry,
            'mall_job',
            ['instance' => 'my_instance']
        );
//        $args['instance'] = 'example_instance';
//        try {
//            $gateway->push($registry, 'mall_interface', $args);
//        } catch (\Exception $e) {
//            echo sprintf('Push to server failed: %s', $e->getMessage());
//        }


    }

    /**
     * @param $args
     * @return void
     * @throws GuzzleException
     * @throws MetricsRegistrationException
     * https://github.com/Jimdo/prometheus_client_php 用法
     */
    public function counter($args)
    {
        $url      = self::URL;
        $registry = \Prometheus\CollectorRegistry::getDefault();


        $counter = $registry->getOrRegisterCounter('main', 'm_login', 'it increases', ['type']);
        $counter->incBy(1, ['blue']);

        $counter = $registry->getOrRegisterCounter('main', 'm_login', 'it decrease', ['type']);
        $counter->incBy(1, ['red']);

        $counterB = $registry->getOrRegisterCounter('test', 'some_counter','it increases',['type']);
        $counterB->incBy(2, ['red']);

        $pushGateway = new PushGateway($url);
        try {
            $pushGateway->push($registry, 'mall_oo', ['instance' => 'mall_oo']);
        } catch (Exception $e) {
            echo sprintf('Push to server failed: %s', $e->getMessage());
        }
    }

}
