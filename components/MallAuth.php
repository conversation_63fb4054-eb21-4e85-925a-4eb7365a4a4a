<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;

/**
 * Mall权限验证加密等
 */
class MallAuth
{
    protected static $_instance = [];

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}


    private $_obj = [];

    /**
     * @return MallAuth
     * */
    public static function factory(): MallAuth
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance [__CLASS__] = new static();
        }
        return self::$_instance [__CLASS__];
    }


    public static function encrypt($input, $key) {
        if (substr(PHP_VERSION, 0, 1) == '7') {
            return self::opensslEncrypt($input,$key);
        }else{
            return self::mcryptEncrypt($input,$key);
        }

    }
    public static function decrypt($input, $key) {
        if (substr(PHP_VERSION, 0, 1) == '7') {
            return self::opensslDecrypt($input,$key);
        }else{
            return self::mcryptDecrypt($input,$key);
        }

    }
    /**
     * [encrypt description]
     * 使用mcrypt库进行加密
     * @param  [type] $input
     * @param  [type] $key
     * @return [type]
     */
    public static function mcryptEncrypt($input, $key) {
        $size = mcrypt_get_block_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_ECB);
        $input = self::pkcs5Pad($input, $size);
        $td = mcrypt_module_open(MCRYPT_RIJNDAEL_128, '', MCRYPT_MODE_ECB, '');
        $iv = mcrypt_create_iv (mcrypt_enc_get_iv_size($td), MCRYPT_RAND);//MCRYPT_DEV_URANDOM
        mcrypt_generic_init($td, $key, $iv);
        $data = mcrypt_generic($td, $input);
        mcrypt_generic_deinit($td);
        mcrypt_module_close($td);
        $data = base64_encode($data);
        return $data;
    }
    /**
     * [pkcs5Pad description]
     * @param  [type] $text
     * @param  [type] $blocksize
     * @return [type]
     */
    private static function pkcs5Pad($text, $blocksize) {
        $pad = $blocksize - (strlen($text) % $blocksize);
        return $text . str_repeat(chr($pad), $pad);
    }
    /**
     * [decrypt description]
     * 使用mcrypt库进行解密
     * @param  [type] $sStr
     * @param  [type] $sKey
     * @return [type]
     */
    public static function mcryptDecrypt($sStr, $sKey) {
        $iv = mcrypt_create_iv(mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_ECB), MCRYPT_RAND);//MCRYPT_DEV_URANDOM
        $decrypted = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, $sKey, base64_decode($sStr), MCRYPT_MODE_ECB, $iv);
        $dec_s = strlen($decrypted);
        $padding = ord($decrypted[$dec_s-1]);
        $decrypted = substr($decrypted, 0, -$padding);
        return $decrypted;
    }
    /**
     * [opensslDecrypt description]
     * 使用openssl库进行加密
     * @param  [type] $sStr
     * @param  [type] $sKey
     * @return [type]
     */
    public static function opensslEncrypt($sStr, $sKey, $method = 'AES-128-ECB'){
        $str = openssl_encrypt($sStr,$method,$sKey);
        return $str;
    }
    /**
     * [opensslDecrypt description]
     * 使用openssl库进行解密
     * @param  [type] $sStr
     * @param  [type] $sKey
     * @return [type]
     */
    public static function opensslDecrypt($sStr, $sKey, $method = 'AES-128-ECB'){
        $str = openssl_decrypt($sStr,$method,$sKey);
        return $str;
    }

}
