<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2022/1/14
 * Time: 17:01
 */

namespace app\components;

use app\models\CUtil;

class WeWorkApp {

    protected static $_instance = [];

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}

    private $_obj = [];

    /**
     * @return WeWorkApp
     * */
    public static function factory()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance [__CLASS__] = new static();
        }

        return self::$_instance [__CLASS__];
    }

    /**
     * @param $js_code
     * @return array
     * 从企业微信登录，根据code获取数据
     * https://developer.work.weixin.qq.com/document/path/91507
     */
    public function code2session($js_code): array
    {

        $config  = CUtil::getConfig('wework_app','common',MAIN_MODULE) ;
        $AgentId = $config['AgentId']  ?? "" ;
        $secret  = $config['secret']  ?? "" ;
        if(empty($secret)) {
            return [false,"企业微信未授权当前应用登录"];
        }

        $token  = WeWork::factory()->GetAccessToken($secret,$AgentId);
        if(empty($token)) {
            return [false,"获取通信凭证失败"];
        }

        $url = "https://qyapi.weixin.qq.com/cgi-bin/miniprogram/jscode2session?access_token={$token}&js_code={$js_code}&grant_type=authorization_code";

        //本公司：{"userid":"kevin.lin","session_key":"\/WdMe***MmOUw==","corpid":"ww96d***fb7b","deviceid":"88C5EA30-***2C372C67790B","errcode":0,"errmsg":"ok"}
        //非本公司：{"userid":"wo0dsqDwAA***mRZf_6sieQ","session_key":"FcDick8***nQ==","corpid":"ww1342***aee5","deviceid":"88C5EA30-***-2C372C67790B","errcode":0,"errmsg":"ok"};
        $ret = CUtil::curl_get($url);
        $ret = json_decode($ret, true);
        // !YII_ENV_PROD && CUtil::debug(json_encode($ret), 'wework.access.token');
        !YII_ENV_PROD && CUtil::setLogMsg(
            "wework.access.token",
            [
                'access_token' => $token,
                'js_code'      => $js_code
            ],
            $ret,
            [],
            $url,
            '',
            [],
            200
        );

        if (isset($ret['errcode'])) {
            switch ($ret['errcode']) {
                case 0     :
                    $return = [true, $ret];
                    break;
                case -1    :
                    $return = [false, "系统繁忙，请稍候再试 (errcode:{$ret['errcode']})"];
                    break;
                case 40029 :
                    $return = [false, "code 无效 (errcode:{$ret['errcode']})"];
                    break;
                default :
                    $return = [false, "未知错误 (errcode:{$ret['errcode']})"];
                    break;
            }

            if ($ret['errcode'] != 0) {
                CUtil::debug(json_encode($ret), 'code2.access.token');
            }

        } else {
            $return = [true, $ret];
        }

        return $return;
    }

}
