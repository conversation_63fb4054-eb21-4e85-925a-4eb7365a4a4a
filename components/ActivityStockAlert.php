<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;

/**
 * 活动库存预警
 */
class ActivityStockAlert
{
    // 库存限制（库存阈值）2000
    const STOCK_LIMIT = 2000;

    /**
     * 库存告警
     * @throws \yii\db\Exception
     */
    public function stockAlert()
    {
        // 当前时间戳
        $time = time();
        // 库存限制（库存阈值）
        $stock = self::STOCK_LIMIT;

        // SQL语句
        $sql = <<<SQL
select
  t_ac_m.ac_id, t_activity_config.name, t_ac_m.mc_id, t_ac_m.stock
from
  `db_dreame`.`t_ac_m`
  inner join `db_dreame`.`t_activity_config` on t_ac_m.ac_id = t_activity_config.id
where
  t_activity_config.start_time <= {$time}
  and t_activity_config.end_time >= {$time}
  and t_ac_m.stock < {$stock}
  and t_activity_config.is_delete = 0;
SQL;

        // 查询的结果集
        $items = by::dbMaster()->createCommand($sql)->queryAll();
        if (empty($items)) {
            return;
        }

        // 查看优惠券名称
        $mcIds = array_column($items, 'mc_id');
        $markets = $this->getMarketsByIds($mcIds);
        $data = [];
        foreach ($items as $item) {
            $mcName = $markets[$item['mc_id']]['name'] ?? '';
            $data[] = [
                'ac_id'   => $item['ac_id'],
                'ac_name' => $item['name'],
                'mc_id'   => $item['mc_id'],
                'mc_name' => $mcName,
                'stock'   => $item['stock'],
            ];
        }
        $this->alert($data);
    }

    /**
     * 获取优惠券
     * @param array $mcIds
     * @return array
     * @throws \yii\db\Exception
     */
    private function getMarketsByIds(array $mcIds): array
    {
        $ids = implode(',', $mcIds);
        $sql = <<<SQL
        select id, name, web_name from `db_dreame_goods`.`t_market_config` where id in ({$ids});
SQL;
        $items = by::dbMaster()->createCommand($sql)->queryAll();
        return array_column($items, null, 'id');
    }

    /**
     * 预警
     */
    private function alert($contents)
    {
        if (empty($contents) || !is_array($contents)) {
            return;
        }
        sort($contents);
        // 标题
        $data['title'] = sprintf('%s 活动库存告警', date('Y-m-d'));
        foreach ($contents as $content) {
            $data['contents'][] = sprintf('**%s** 下的优惠券 **%s**，当前库存为 **%d**，请及时关注！', $content['ac_name'], $content['mc_name'], $content['stock']);
        }
        CUtil::sendMsgToFs($data, 'activityStock', 'interactive');
    }
}