<?php

/**
 * Created by PhpStorm.
 * User: CP
 * Date: 2023/8/21
 * Time: 10:29
 * 类名 + 方法名 + 参数
 */

namespace app\components;

use app\models\CUtil;

class AppWRedisKeys extends AppCRedisKeys
{
    //商品主表详情
    public static function getOneGoodsMainByGid($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$gid);
    }

    //商品哈希列表
    public static function getGoodsMainList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //所有商品列表
    public static function getAllMainList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //简化版所有商品
    public static function getAllGoodsSimpleInfo(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    public static function getIndexGoodsQueryList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //商品多规格列表
    public static function getGoodsSpecsListByGid($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //商品多规格列表
    public static function getGoodsSpecsById($gid, $id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $id);
    }

    //积分商品列表
    public static function getGoodsPointsList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //积分商品基础信息
    public static function getGoodsPointsById($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //积分商品规则信息
    public static function getGoodsPointsPriceById($gid, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //根据gid获取skus，一旦创建sku不会改
    public static function getSkusByGids($gids): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$gids);
    }


    //商品id str
    public static function getGoodsOneAtkById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //商品属性名列表
    public static function getGoodsAtkListByGid($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //商品属性名str
    public static function getGoodsOneAtkByName($gid, $at_name): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $at_name);
    }


    //属性值str
    public static function getGoodsAvByAtkId($ak_id, $at_val): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ak_id, $at_val);
    }

    //属性值str
    public static function getGoodsAvById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //属性值列表
    public static function getGoodsAvList($ak_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ak_id);
    }

    //属性值列表
    public static function getSpecsByGidSku($gid, $sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sku);
    }


    //商品库存
    public static function goodsStock($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }


    //商品销量
    public static function goodsSales($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //商品未付款数
    public static function goodsWait($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //商品库存sum
    public static function goodsListStock($skus): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $skus);
    }

    //获取当天打卡的缓存key
    public static function todayCheckInIn($user_id,$time) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $time);
    }



    //共创空间人员
    public static function getCoCreateUserId($id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    public static function getCoCreateUserByUserId($user_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    public static function getCoCreateUserList() :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //共创素材数据
    public static function getCoCreateMaterialId($id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    public static function getCoCreateMaterialList() :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    public static function getCoCreateMaterialListByUserId($user_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    public static function getNowCoCreateMaterialList() :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    public static function getAllCoCreateMaterialList($topic) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$topic);
    }
    //共创统计数据
    public static function getCoCreateStatisticList() :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //根据用户获取共创统计详情数据
    public static function getCoCreateStatisticInfoByUser($user_id,$material_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$material_id);
    }

    //根据用户获取共创统计列表数据
    public static function getCoCreateStatisticListByUser($user_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    public static function getCoCreateStatisticDetailList($material_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$material_id);
    }

    public static function userEnjoyPositionKey($user_id,$topic) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$topic);
    }

    public static function getIFUseByUser($user_id) :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    //分享券领取记录
    public static function GetShareCouponDrawLogList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    public static function GetShareCouponCode($code): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$code);
    }

    public static function GetShareCouponUrlLink($path,$query): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $path, $query);
    }

    public static function GetAlipayAuthTokenKey($appId,$event,$code): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $appId, $event, $code);
    }

    public static function GetAlipayAuthCodeByUserId($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId);
    }

    public static function GetIotProductList() :string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    public static function getGivePointByUid($uid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $uid);
    }
    public static function wrongExcelList($backUserId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $backUserId);
    }
}

