<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;
use app\jobs\MallJob;
use app\modules\main\models\UserModel;
use app\modules\main\services\UserBindService;
use RedisException;
use yii\db\Exception;

/**
 * Mall定时任务
 */
class Mall
{
    private static $_instance;

    private $dreame_home_domain;

    const URL = [
        'REGISTER' => '/dreame-user/dreameMall/syncIot', //会员注册/更新至APP
        'BATCH_REGISTER' => '/dreame-user/dreameMall/batchSyncUserToIot',//批量注册/更新至APP

        //用户中心模块
        'CENTER_REGISTER' => '/dreame-account-center/dreameUserCenter/queryUserInfoByPhone', //Dreame会员中心管理系统 注册/查询（根据手机号）
        'CENTER_QUERY' => '/dreame-account-center/dreameUserCenter/queryUserInfoByUid', //Dreame会员中心管理系统 查询（根据uid）
        'CENTER_UPDATE' => '/dreame-account-center/dreameUserCenter/updateUserInfo',//Dreame会员中心管理系统 更新用户信息
        'CENTER_DISCARD' => '/dreame-account-center/dreameUserCenter/closeAccount',//Dreame会员中心管理系统 注销用户信息
        'CENTER_PARSE_TOKEN' => '/dreame-account-center/dreameUserCenter/parseToken',//Dreame会员中心管理系统 解析用户信息

        //设备控制获取token
        'GENERATE_JWT_TOKEN'=>'/dreame-account-center/dreameUserCenter/generateJwtToken', //根据用户uid获取获取JWTToken

        //商城调用IOT
        'DELETE_CATE'=>'/dreame-product/product/parts/deletePartByPartsId',//根据配件类目Id通知IOT删除配件类目购买配置
    ];

    const ENCRYPT_FIELDS = [
        'nickName', 'uid', 'openid', 'unionid', 'phone', 'realName', 'name', 'partsId'//配件类目Id
    ];

    const REGISTER_USER_INFO = [
        1 => [//dreamehome
            'nick_name' => 'name',//昵称
            'uid' => 'uid',//dreamehome 用户ID
            'phone' => 'phone',//电话号码
            'phone_code' => 'phoneCode',
            'avatar' => 'avatar',//头像
            'app_id' => 'id',//APP唯一标识
            'sex' => 'sex', //性别
            'birthday' => 'birthday',//生日
        ],
        2 => [
            'cate' => 'cate'
        ]
    ];


    const CONFIG = YII_ENV_PROD ? [
        'TIMEOUT' => '300',//单位ms 报文超时时间
        'USER' => 'dreame_sys',
        'PWD' => '4L6rf5SoBesxlMgc',
        'AESKEY' => 'ceuK1nSbOav%wR#A'
    ] : [
        'TIMEOUT' => '3600',//单位ms 报文超时时间
        'USER' => 'dreame_sys',
        'PWD' => '4L6rf5SoBesxlMgc',
        'AESKEY' => 'ceuK1nSbOav%wR#A'
    ];

    const CONFIG_CENTER = YII_ENV_PROD ? [
        'TIMEOUT' => '300',//单位ms 报文超时时间
        'USER' => 'dreame_mall',
        'PWD' => '4L6rf5SoBesxlMgc',
        'AESKEY' => 'ceuK1nSbOav%wR#A'
    ] : [
        'TIMEOUT' => '3000',//单位ms 报文超时时间
        'USER' => 'dreame_mall',
        'PWD' => '4L6rf5SoBesxlMgc',
        'AESKEY' => 'ceuK1nSbOav%wR#A'
    ];


    private function __getGenerateJwtTokenKey($user_id): string
    {
        return AppCRedisKeys::getGenerateJwtTokenKey($user_id);
    }

    public function __delGenerateJwtTokenKey($user_id)
    {
        $redis  = by::redis();
        $r_key  =  $this->__getGenerateJwtTokenKey($user_id);
        $redis->del($r_key);
    }


    public static function needMergeTables($fakeId,$realId)
    {
        $time = time();
        $isSame = false;
        if(intval($realId) % 10 === intval($fakeId) % 10){
            $isSame = true;
        }
        $sameTables = [
            by::Ofinish()::tbName(),
            by::Omain()::tbName($time),
            by::Orgoods()::tbNameByTime($time),
            by::OrefundMain()::tbName(),
            by::Osource()::tbName($time),
            by::osourceR()::tbName($time),
            by::plumbingComment()::tbName(),
            by::plumbingOrder()::tbName(),
            by::plumbingRefund()::tbName(),
            by::Ocfg()::tbNameByTime($time),

        ];
        $diffTables = [
            by::cart()::tbName($fakeId)=>by::cart()::tbName($realId),
            by::Orefund()::tbName($fakeId)=>by::Orefund()::tbName($realId),
            by::Ouser()::tbName($fakeId) =>by::Ouser()::tbName($realId),
            by::Ogoods()::tbName($fakeId)=>by::Ogoods()::tbName($realId),
        ];
        foreach ($diffTables as $item=>$v){
            if($isSame) {
                $sameTables[] = $v;
                unset($diffTables[$item]);
            }
        }

        return [
            'sameTables' => $sameTables,
            'diffTables' => $diffTables,
        ];
    }

    private function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return Mall
     */
    public static function factory(): Mall
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    private function __appList($user_id): string
    {
        $mod  = intval($user_id) % 10;
        return AppCRedisKeys::appList($mod);
    }

    public function __appRetry():string{
        return AppCRedisKeys::appRetry();
    }

    /**
     * @param int $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @param string $uid
     * @return array
     * 插入队列
     */
    public function push(int $user_id,string $function, array $args, int $time=0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function,$methods)){
            return [false,'方法不存在'];
        }

        if (empty($args)){
            return [false,'缺少参数'];
        }

        // $r_key = $this->__appList($user_id);
        // by::redis('core')->rPush($r_key,
        //     json_encode(
        //         [
        //             'function' => $function,
        //             'args' => $args,
        //             'time' => $time ?: time(),
        //         ]
        //     )
        // );

        //注释下面redis，改用supervisors维护进程
        \Yii::$app->queue->push(new MallJob(['user_id' => $user_id, 'function' => $function, 'args' => $args, 'time' => $time]));

        return [true,'ok'];
    }

    /**
     * @deprecated 定时任务处理mall，废弃
     * 改用上面函数push的supervisors维护进程
     * Undocumented function
     *
     * @param [type] $index
     * @return void
     */
    public function SynApp($index){
        try {
            $redis_key = $this->__appList($index);
            $my_pid     = getmypid();
            $redis      = by::redis('core');
            $exist_key  = AppCRedisKeys::ProcessExistKey("app");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true){
                //每小时重启一次 防止内存泄漏
                if(abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}",'link_run_app');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key,0);
                if($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr          = explode('@',$signs);
                    $name         = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill      = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:".$command,'link_run_app');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret     = $redis->BLPOP([$redis_key],7200);

                $data    = $ret[1] ?? "";
                if(!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data,true);
                if(empty($aData)) {
                    continue;
                }

                //todo 三次重试
                for ($i = 1; $i <= 3; $i++) {
                    list($status,$msg) = $this->run($aData['function'],$aData['args'],$aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i*2);
                }

                if(!$status) {
                    //重试也失败 加入到mysql记录
                    by::model('AppLogModel', 'main')->saveLog($aData['function'],$aData['args'],$msg,$aData['time']);
                }
            }


        }catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error,'link_run_app_err');
            exit($error);
        }
    }



    public function run(string $function, array $args, int $time): array
    {
        switch ($function){
            case 'register':
            case 'centerQuery':
            case 'centerUpdate':
                return $this->$function($args['user_id'],1);
            case 'centerRegister':
                return $this->$function($args['user_id'], 1, $args['r_id'] ?? '', $args['phone'] ?? '', $args['shop_code'] ?? '',$args['employee_uid'] ?? '');
            case 'centerDiscard':
                return $this->$function($args['uid']);
            case 'centerParseToken':
                return $this->$function($args['jwtToken']);
            case 'mallUpdate':
                return $this->$function($args['user_id'],$args['user_detail']);
            default:
                return [false,'function 不存在'];
        }
    }




    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 录入会员
     */
    public function register($user_id, $registerType = 1): array
    {
        $body = $this -> packUser($user_id);
        if(empty($body))  return [true, '忽略无手机号用户'];

        $body = self::encryptRegisterData($body);
        list($s, $ret) = $this->_request('REGISTER', $body);
        if (!$s) {
            return [false, $ret];
        }
        //TODO 保存处理
        $data = $ret['data'] ?? [];
        if($data){
            list($status, $data) = $this->_decryptData($data, $registerType);
            if (!$status || empty($data)) {
                return [false, $data];
            }
            list($status, $msg) = by::usersMall()->saveMallInfo($data, $registerType);
            if (!$status) {
                return [false, $msg];
            }
            //4.同步crm
            if(isset($msg['userId'])){
                // Crm::factory()->push($msg['userId'], 'user', ['user_id' => $msg['userId']]);
                Mall::factory()->push($msg['userId'],'centerRegister', ['user_id' => $msg['userId']]);
            }
        }
        return [true,$ret];
    }


    public function processRegister($body, int $registerType = 1, $r_id = '', $phone = '', $shop_code = "",$employee_uid = ''): array
    {
        if(empty($body))  return [true, '忽略无手机号用户'];
        $body = self::encryptRegisterData($body, 1);
        list($s, $ret) = $this->_request('CENTER_REGISTER', $body , 'POST' ,10 , 1);
        if (!$s) {
            return [false, $ret];
        }

        //TODO 保存处理
        $data = $ret['data'] ?? [];
        if($data){
            //这边不需要解密
            list($status, $data) = $this->_decryptData($data, $registerType, [],0,1);
            if (!$status || empty($data)) {
                return [false, $data];
            }
            list($status, $msg) = by::usersMall()->saveMallInfo($data, $registerType);
            !YII_ENV_PROD && CUtil::debug('新用户注册|推送crm|'.$status.'|'.$r_id.'|'.$phone.'|'.json_encode($msg),'crm.tongbu');
            if (!$status) {
                return [false, $msg];
            }
            //4.同步crm
            if(isset($msg['userId'])){
                // Crm::factory()->push($msg['userId'], 'user', ['user_id' => $msg['userId']]);
                //5.绑定推荐人
                if($r_id) by::userRecommend()->bind($msg['userId'], $r_id, $phone, 1);
                //6.绑定门店号码
                if ($shop_code) {
                    $extendData['shop_code'] = $shop_code;
                    by::userExtend()->saveUserExtend($msg['userId'], $extendData);
                }
                // if($employee_uid){
                //     $error = '';
                //     $res = UserBindService::getInstance()->bindEmployee($data['uid'], $employee_uid, 1, $error,1);
                //     if(!$res){
                //         // 绑定失败，记录日志
                //         CUtil::debug('用户绑定员工失败:'.$msg['userId'].'-'.$employee_uid.'-'.$error,'user_bind.info');
                //     }
                // }
            }
            $ret['data']['userId'] = $msg['userId']??'';
            $ret['data']['uid'] = $msg['uid']??''; //返回uid

        }
        return [true,$ret];
    }

    public function centerRegister($user_id, int $registerType = 1, $r_id='', $phone='', $shop_code = '',$employee_uid = ''): array
    {
        $body = $this->packUser($user_id);
        return $this->processRegister($body, $registerType, $r_id, $phone, $shop_code,$employee_uid);
    }

    public function centerAlipayRegister($body, int $registerType = 1, $r_id='', $phone=''): array
    {
        return $this->processRegister($body, $registerType, $r_id, $phone);
    }


    /**
     * @throws Exception
     */
    public function centerQuery($user_id, int $registerType = 1): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id,false);
        $body['uid'] = $mallInfo['uid'] ?? '';
        $body = self::encryptRegisterData($body, 1);
        list($s, $ret) = $this->_request('CENTER_QUERY', $body , 'POST' ,10 , 1);
        if (!$s) {
            return [false, $ret];
        }
        $data = $ret['data'] ?? [];
        return [true,$data];
    }

    /**
     * @throws Exception
     */
    public function centerUpdate($user_id, int $registerType = 1):array
    {
        $body = $this->packUser($user_id);
        if(empty($body))  return [true, '忽略无手机号用户'];
        if(empty($body['uid'])) return [true, '忽略无UID用户'];

        $body = self::encryptRegisterData($body, 1);

        list($s, $ret) = $this->_request('CENTER_UPDATE', $body , 'POST' ,10 , 1);
        if (!$s) {
            return [false, $ret];
        }
        $data = $ret['data'] ?? [];
        return [true,$data];
    }

    public function mallUpdate($user_id,$user_detail): array
    {
        $mallsInfo = by::usersMall()->getMallsByUserId($user_id,false);
        if (isset($user_detail['nick'])){
            $user_detail['nick_name'] = $user_detail['nick'];
        }
        if($mallsInfo){
            $mallIds = array_filter(array_unique(array_column($mallsInfo,'id')));
            by::usersMall()->updateMallsByIds($mallIds,$user_detail);
        }
        return [true,$mallIds??[]];
    }

    /**
     * @throws Exception
     */
    public function centerDiscard($uid): array
    {
        if(empty($uid)) return [true, '忽略无uid用户'];
        $body['uid'] = $uid;
        $body = self::encryptRegisterData($body, 1);
        list($s, $ret) = $this->_request('CENTER_DISCARD', $body , 'POST' ,10 , 1);
        if (!$s) {
            return [false, $ret];
        }
        $data = $ret['data'] ?? [];
        return [true,$data];
    }


    /**
     * @throws Exception
     */
    public function centerParseToken($jwtToken): array
    {
        if(empty($jwtToken)) return [false, 'jwtToken不能为空'];
        $body['jwtToken'] = $jwtToken;
        $body = self::encryptRegisterData($body, 1);
        list($s, $ret) = $this->_request('CENTER_PARSE_TOKEN', $body , 'POST' ,10 , 1);
        if (!$s) {
            return [false, '登录失败'];
        }
        $data = $ret['data'] ?? [];
        return [true,$data];
    }


    public static function encryptRegisterData($data,$ifCenter=0)
    {
        //获取必要参数
        $config = empty($ifCenter)?(self::CONFIG ?? []):(self::CONFIG_CENTER ?? []);
        $aesKey = $config['AESKEY'] ?? "";
        $needEncryptField = self::ENCRYPT_FIELDS;
        if($ifCenter) $needEncryptField[] = 'phoneCode';
        foreach ($data as $item => $value) {
            if (in_array($item, $needEncryptField)) {
                $data[$item] = empty($value) ? '' : MallAuth::encrypt($value, $aesKey);
            }
        }
        return $data;
    }


    public function getCityId($name, $p_id = 1)
    {
        if (is_array($name)|| empty(trim($name))) {
            return '';
        }

        $model = by::model('AreaModel', MAIN_MODULE);
        $province = $model->GetList($p_id);
        $province = array_column($province, null, 'name');
        return $province[$name]['id'] ?? '';
    }

    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param int $ttl
     * @return array
     * 请求
     * @throws Exception
     */
    protected function _request($event, array $body = [], string $method = 'POST', int $ttl = 10 , int $ifCenter = 0): array
    {
        $url = self::URL[$event];
        if (empty($url)) {
            return [false, 'event不存在'];
        }
        $url = $this->dreame_home_domain . $url;

        $config = empty($ifCenter)?(self::CONFIG ?? []):(self::CONFIG_CENTER ?? []);

        $row = by::dbMaster()->createCommand("select uuid() as uuid")->queryOne();
        $uuid = $row['uuid'] ?? '';
        if (empty($uuid)) {
            return [false, '参数生成失败'];
        }
        $uuid = str_replace("-", '', $uuid);//去掉中间的-

        $header = [
            "Content-Type:application/json",
            "Authorization: Basic " . base64_encode("{$config['USER']}:{$config['PWD']}"),
            "i:" . MallAuth::factory()->encrypt($uuid . '_' . time(), $config['AESKEY']),
            "g:" . $uuid,
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ];


        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body), $header, $method, $ttl);
        // CUtil::debug("event:{$event}|url:{$url}|httpcode:{$httpCode}|err:{$err}|header:" . json_encode($header) . "|data：" . json_encode($body) . " | ret:" . json_encode($ret), "dreamehome.{$event}");
        CUtil::setLogMsg(
            "dreamehome.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
//        if (!isset($ret['code']) || $ret['code'] !== 0) CUtil::debug("event:{$event}|URL:{$url}|httpcode:{$httpCode}|err:{$err}|header:" . json_encode($header) . "|data：" . json_encode($body) . " | ret:" . json_encode($ret), "dreamehome.{$event}_err");

        if (!$status) {
            return [false, $err];
        }

        return [$ret['code'] === 0, $ret];
    }


    /**
     * 需要aeskey解码
     * @param $userInfo
     * @param $registerType
     * @param $headers
     * @return array
     */
    public function _format_dreamehome_data($userInfo, $registerType, $headers, $ifCenter = 0, $checkParams = [], $isDecryptData = 1): array
    {
        //校验头部参数
        list($status, $msg) = $this->_checkHeaders($headers, $registerType, $ifCenter);
        if (!$status) {
            return [false, $msg];
        }
        $data = [];
        //是否解密数据  默认解密
        if ($isDecryptData) {
            list($status, $data) = $this->_decryptData($userInfo, $registerType, $checkParams, $isDecryptData, $ifCenter);
            if (!$status || empty($data)) {
                return [false, $data];
            }
        }
        return [true, $data];
    }

    public function _checkHeaders($headers,$registerType,$ifCenter): array
    {
        $config = empty($ifCenter)?(self::CONFIG ?? []):(self::CONFIG_CENTER ?? []);
        $userInfoField = self::REGISTER_USER_INFO[$registerType] ?? [];
        $aesKey = $config['AESKEY'] ?? "";
        if (empty($userInfoField) || empty($aesKey)) {
            return [false, '无用户配置文件！'];
        }
        $authData = explode(' ', $headers['Authorization'])[1] ?? '';
        $encodedData = empty($authData) ? '' : str_replace(' ', '+', $authData);
        //头部数据校验
        list($user, $pass) = empty($encodedData) ? '' : explode(':', base64_decode($encodedData));
        if ($user != $config['USER'] || $pass != $config['PWD']) {
            return [false, '用户认证失败！'];
        }
        //过期时间校验
        $i = $headers['i'];
        $g = $headers['g'];
        if (empty($i) || empty($g)) {
            return [false, '无校验秘钥！'];
        }
        $iParam = MallAuth::factory()->decrypt($i, $aesKey);
        $iParamArr = explode('_', $iParam);
        //uuid校验
        if ($iParamArr[0] !== $g) {
            return [false, '参数校验失败！'];
        }
        //时间校验
        if (abs(time() - $iParamArr[1] ?? 0) > $config['TIMEOUT']) {
            return [false, '时间超限！'];
        }
        return [true,'ok'];
    }


    /**
     * 解密数据
     * @param $userInfo
     * @param $registerType
     * @return array
     */
    public function _decryptData($userInfo, $registerType ,$checkParams=[],$needDecode = 1,$ifCenter = 0): array
    {
        //获取必要参数
        !YII_ENV_PROD && CUtil::debug(json_encode($userInfo),'decrypt_data');
        $config = empty($ifCenter)?(self::CONFIG ?? []):(self::CONFIG_CENTER ?? []);
        $userInfoField = self::REGISTER_USER_INFO[$registerType] ?? [];
        $aesKey = $config['AESKEY'] ?? "";
        if (empty($userInfoField) || empty($aesKey)) {
            return [false, '无用户配置文件！'];
        }
        $data = [];
        foreach ($userInfoField as $item => $v) {
            $userItem = $userInfo[$v] ?? '';
            if (in_array($v, ['phoneCode', 'avatar', 'id', 'sex', 'userId', 'birthday']) || empty($needDecode)) {
                if ($v === 'sex' && $userItem === 3) {//性别转换
                    $userItem = 0;
                }
                if($v === 'birthday'){//本地生日不计算时分秒
                    $userItem = CUtil::uint((int)$userItem/1000);
                }
                $data[$item] = $userItem;
            } else {
                $data[$item] = empty($userItem) ? '' : MallAuth::factory()->decrypt($userItem, $aesKey);
                if ($data[$item] === false) {
                    return [false, '用户参数错误' . $v];
                }
            }
        }
        if($ifCenter && $needDecode){
            $data['phone_code'] = empty($data['phone_code']) ? '':MallAuth::factory()->decrypt($data['phone_code'], $aesKey);
        }
        //参数校验
        if($checkParams){
            foreach ($checkParams as $param){
                if(empty($data[$param])) return [false,'缺失参数'.$param];
            }
        }else{
            if (empty($data['uid']) || empty($data['phone'])) {
                return [false, '用户参数缺失ID或电话号码!'];
            }
        }


        return [true, $data];
    }

    /**
     * APP加密返回数据
     * @param $data
     * @param $registerType
     * @return array
     */
    public function _appCryptReturnData($data, $registerType ,$ifCenter = 0): array
    {
        $config = empty($ifCenter)?(self::CONFIG ?? []):(self::CONFIG_CENTER ?? []);
        $userInfoField = self::REGISTER_USER_INFO[$registerType] ?? [];
        $aesKey = $config['AESKEY'] ?? "";
        if (empty($userInfoField) || empty($aesKey)) {
            return [false, '无用户配置文件！'];
        }
        return [
            'uid' => $data['uid'] ?? ($ifCenter?MallAuth::factory()->encrypt($data['uid'], $aesKey):$data['uid']),
            'id' => $data['id'] ?? '',
            'userId' => $data['user_id'] ?? '',
            'userInfo' => $data,
        ];
    }

    /**
     * @throws Exception
     */
    public function batchRegister($registerType = 1)
    {
        //1.获取当前未同步的用户
        $data = by::users()->getNoSyncUser();
        $userAll = [];
        $ret = [];
        if ($data) {
            foreach ($data as $item) {
                $userAll[] = $item['user_id'] ?? 0;
            }
            //用户分成100个每组
            $userAll = array_chunk(array_unique(array_filter($userAll)), 100);
            foreach ($userAll as $userItem) {
                $userData = [];
                foreach ($userItem as $user_id) {
                    $data = $this->packUser($user_id);
                    if ($data) $userData[] = $data;
                }
                //有需要同步的用户（手机号码存在）
                //TODO 调用APP用户批量同步接口
                if ($userData) {
                    $arr = [];
                    foreach ($userData as $user) {
                        $arr['syncUserInfoList'][] = self::encryptRegisterData($user);
                    }
                    list($s, $ret) = $this->_request('BATCH_REGISTER', $arr,'POST',300);
                    if (!$s) {
                        return [false, $ret];
                    }
                    //TODO 保存处理
                    $data = $ret['data'] ?? [];
                    CUtil::debug('batch-register:'.json_encode($data),'batch.register');
                    if ($data) {
                        foreach ($data as $da) {
                            if (isset($da['uid'])) {
                                list($status, $da) = $this->_decryptData($da, $registerType);
                                if (!$status || empty($da)) {
                                    return [false, $da];
                                }
                                list($status, $msg) = by::usersMall()->saveMallInfo($da, $registerType);
                                if (!$status) {
                                    CUtil::debug('batch-register:'.json_encode($msg),'batch.register');
                                    return [false, $msg];
                                }
                                //todo 注册成功后同步到crm队列
                                if(isset($msg['userId'])){
                                    // Crm::factory()->push($msg['userId'], 'user', ['user_id' => $msg['userId']]);
                                    Mall::factory()->push($msg['userId'], 'centerRegister', ['user_id' => $msg['userId']]);
                                }
                            }
                        }
                    }
                }
            }
        } else {
            $ret = '没有数据，不需要同步';
        }
        echo '同步结束！';exit();
        return [true, $ret];
    }


    /**
     * 为了不影响新增用户单独封装
     * @param $user_id
     * @return array
     * @throws Exception
     */
    public function packUser($user_id): array
    {
        $phone = by::Phone()->GetPhoneByUid($user_id, false);
        $body = [];
        if ($phone) {
            $p_time = by::Phone()->GetCtimeByUid($user_id);
            $mallInfo = by::usersMall()->getMallInfoByUserId($user_id,false);
            $user = by::users()->getOneByUid($user_id,false);
            $main = by::users()->getUserMainInfo($user_id);

            $p_id = $this->getCityId($user['area']['province'] ?? 0);
            $c_id = $this->getCityId($user['area']['city'] ?? 0, $p_id);
            $a_id = $this->getCityId($user['area']['area'] ?? 0, $c_id);

            $nickName = $user['nick'] ?? '';
            $avatar = $user['avatar'] ?? '';

            $body['nickName'] = $nickName;
            $body['name'] = $nickName;
            $body['userId'] = $user_id;
            $body['uid'] = $mallInfo['uid'] ?? '';
            $body['phone'] = $phone;
            $body['phoneCode'] = 86;
            $body['sex'] = empty($user['sex']) ? 3 : CUtil::uint($user['sex']);
            $body['realName'] = $user['real_name'] ?? $user_id;
            $body['status'] = $user['status'] ?? 0;
            $body['age'] = $user['age'] ?? 0;
            if($avatar) $body['avatar'] = $avatar;
            $body['province'] = $p_id;
            $body['city'] = $c_id;
            $body['county'] = $a_id;
            $body['birthday'] = !empty($user['birthday']) ? intval($user['birthday'])*1000 : 0;
            $body['openid'] = $main['openudid']??'';
            $body['unionid'] = $main['unionid']??'';
            $body['time'] = !empty($p_time) ? date('Y-m-d', $p_time) : date('Y-m-d');

            if(empty($body['birthday'])){
                unset($body['birthday']);
            }
        }

        return $body;
    }


    /**
     * @throws Exception
     */
    public function batchDeprecated()
    {
        //1.查找所有游客数据
        $data = by::users()->getALLNoAuthUsers();
        //2.分为500条进行注销
        $successData = [];
        $failureData = [];
        if($data){
            foreach ($data as $key=>$item){
                $data[$key]['openudid'] = $item['openudid'].'|'.START_TIME;
            }
            $dataArr = array_chunk($data,500);
            foreach ($dataArr as $period){
                if($period) {
                    list($status,$data) = by::users()->batchDeprecated($period);
                    if($status){
                        $successData = array_merge($successData,$data);
                    }else{
                        $failureData = array_merge($failureData,$data);
                    }
                }
            }
        }
        CUtil::debug('批量注销成功：'.implode(",",$successData));
        CUtil::debug('批量注销失败：'.implode(",",$failureData));
        echo 'ok';exit();
    }


    /**
     * 合并转移用户(只执行一次)
     */
    public function mergeUser()
    {
        exit(0);//该方法不用了
//        $userArray = self::MERGE_USERS;
        if(empty($userArray)){
            return [true,'不需要合并'];
        }
        $successData = [];
        foreach ($userArray as $realUser=>$fakeUsers){
             list($status,$msg) = $this->_mergingUser($realUser,$fakeUsers);
             if(!$status){
                 $successData['failure'][]= $realUser.'---'.implode(',',$fakeUsers).'---'.$msg;
             }else{
                 $successData['success'][]= $realUser.'---'.implode(',',$fakeUsers);
             }
        }

        CUtil::debug('merge-users:'.json_encode($successData,JSON_UNESCAPED_UNICODE));
        echo json_encode($successData,JSON_UNESCAPED_UNICODE);exit(0);
    }


    protected function _mergingUser($realUser,$fakeUsers=[])
    {
        //1.校验用户是否是使用同一电话号码
        $allUsers = array_merge([$realUser],$fakeUsers);
        list($status,$phoneNums) = by::Phone()->getPhoneNumsByUsers($allUsers);
        if(!$status){
            return [false,$phoneNums];
        }
        if(intval($phoneNums)!==1){
            return [false,'用户不存在或者用户不是同一个手机号码'];
        }
        //2.校验会员是否一致
        list($status,$cardNums) = by::userExtend()->getCardNumsByUsers($allUsers);
        if(!$status){
            return [false,$cardNums];
        }
        if(intval($cardNums)!==1){
            return [false,'用户不是同一个会员卡号'];
        }
        //3.复制相关数据
        $transaction      = by::dbMaster()->beginTransaction();
        try{
            //2.所有需要操作的表
            foreach ($fakeUsers as $k=>$fakeId){
                $tables = self::needMergeTables($fakeId,$realUser);
                CUtil::debug(json_encode($tables));
                $sameTables = $tables['sameTables']??[];
                $diffTables = $tables['diffTables']??[];
                if($sameTables){
                    foreach ($sameTables as $same){
                        list($status,$msg) = $this->_mergeCeilUser($same,$same,$fakeId,$realUser);
                        if(!$status){
                            throw new MyExceptionModel($msg);
                        }
                        continue;
                    }
                }
                if($diffTables){
                    foreach ($diffTables as $fakeTable=>$realTable){
                        list($status,$msg) = $this->_mergeCeilUser($fakeTable,$realTable,$fakeId,$realUser);
                        if(!$status){
                            throw new MyExceptionModel($msg);
                        }
                        continue;
                    }
                }
                by::users()->Deprecated($fakeId,0,1);
                continue;
            }
            $transaction->commit();
            return [true, '合并成功'];
        }catch (\Exception $e){
            $transaction->rollback();
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug('Merge-User:'.$error);
            return [false, $error];
        }
    }


    /**
     * @throws Exception
     */
    protected function _mergeCeilUser($fakeTable, $realTable, $fakeId, $realId){
        //找出对应表的数据
        $sql = "SELECT * FROM {$fakeTable} WHERE `user_id`=:user_id";
        $aDatas      = by::dbMaster()->createCommand($sql, [':user_id' => $fakeId])->queryAll();
        if($aDatas){
            foreach ($aDatas as $aData){
                unset($aData['id']);
                if($aData){
                   list($status,$msg) = $this->_doWellWithData($aData,$fakeTable,$realTable,$fakeId,$realId);
                   if(!$status){
                       return [false,$msg];
                   }
                }
            }
        }

         return [true,'OK'];
    }


    protected function _doWellWithData($aData,$fakeTable,$realTable, $fakeId, $realId){
        if($fakeTable === $realTable){
            //更新
            $sqlUpdate = "UPDATE {$fakeTable} SET `user_id` = {$realId} WHERE `user_id`=:user_id";
            $res = by::dbMaster()->createCommand($sqlUpdate, [':user_id' => $fakeId])->execute();
            if(!$res){
                return [false,'更新失败：'.$fakeId.'---'.$realId];
            }
        }else{
            //查询有没有
            $aData['user_id'] = $realId;
            $fields = array_keys($aData);
            $fields = implode("`,`",$fields);
            $rows   = implode("','",$aData);
            $dup    = [];
            foreach ($aData as $key => $value){
                $dup[] = is_int($value)?"`{$key}` = {$value}": "`{$key}` = '{$value}'";
            }
            $dup1 = implode(' AND ',$dup);
            $dup2    = implode(' , ',$dup);
            $whereSql = "SELECT `user_id` FROM {$realTable} WHERE {$dup1} LIMIT 1";
            $bData = by::dbMaster()->createCommand($whereSql)->queryOne();
            $userId = $bData['user_id'] ?? 0;
            if($userId){
                return [true,'数据已经存在！'];
            }else{
                //创建
                $sql    = "INSERT INTO {$realTable} (`{$fields}`) VALUE ('{$rows}') ON DUPLICATE KEY UPDATE {$dup2}";
                $res    = by::dbMaster()->createCommand($sql)->execute();
                if(!$res){
                    return [false,'创建失败！：'.$realId];
                }

            }
        }
        return  [true,'ok'];
    }

    /**
     * @throws Exception
     */
    public function makeUpCrmUid()
    {
        //寻找mall_id > 60000 的所有用户
        $tb = by::Phone()::tbName();
        $sql = "SELECT `user_id` from {$tb} where `mall_id` > 60000";
        $aData = by::dbMaster()->createCommand($sql)->queryAll();
        CUtil::debug(by::dbMaster()->createCommand($sql)->getRawSql());
        if($aData){
            $userIds = array_column($aData,'user_id');
            $userAll = array_chunk(array_unique(array_filter($userIds)), 300);
            foreach ($userAll as $items){
                foreach ($items as $userId){
                    // Crm::factory()->push($userId, 'user', ['user_id' =>$userId]);
                    Mall::factory()->push($userId, 'centerUpdate', ['user_id' => $userId]);
                    Mall::factory()->push($userId, 'mallUpdate', ['user_id' => $userId]);
                }
                sleep(10);
            }
        }
        echo '推入队列成功！';exit();
    }


    /**
     * @return string
     * @throws Exception
     * 同步用户生日信息
     */
    public function syncUserBirthday()
    {
        $limitNum = 500;
        $tb_phone =  by::Phone()::tbName();
        $db = by::dbMaster();
        $redisKey = 'userBirthday';
        $redis = by::redis('core');
        while (1) {
            $id = CUtil::uint($redis->get($redisKey));
            $sql = "SELECT `id`,`user_id` FROM {$tb_phone} WHERE `mall_id`>0 AND `id`>:id  ORDER BY `id` ASC LIMIT ".$limitNum;
            $phoneInfo = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($phoneInfo)) {
                return 'end_user_birthday';
            }
            $end = end($phoneInfo);
            $id = $end['id'];
            $redis->set($redisKey, $id, 7200);
            //遍历所有的用户ID
            foreach ($phoneInfo as $info){
                $userId = $info['user_id'] ?? 0;
                if($userId){
                    //获取用户详情
                    $userInfo = by::users()->getOneByUid($userId);
                    $birthday = $userInfo['birthday'] ?? 0;
                    $sex = $userInfo['sex'] ?? 0;
                    if($birthday > 0 || $sex>0){
                        $this->centerUpdate($userId);
                    }
                }
            }
        }


    }

    public function delBirthdayKey()
    {
        $redis = by::redis('core');
        $redisKey = 'userBirthday';
        $redis->del($redisKey);
        exit('del_birthday');
    }


    /**
     * @throws Exception
     * @throws RedisException
     */
    public function getGenerateJwtToken($user_id): array
    {
        $user_id = CUtil::uint($user_id);
        if (empty($user_id)) {
            return [false, '参数错误'];
        }

        //获取uid
        $mallInfo = by::usersMall()->getInfoByUserId($user_id);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) {
            return [false, '用户不存在'];
        }


        $body['uid'] = $uid;

        $r_key = $this->__getGenerateJwtTokenKey($user_id);
        $redis = by::redis();
        $aJson = $redis->get($r_key);
        $aData = (array)json_decode($aJson, true);

        if ($aJson === false) {
           $body = self::encryptRegisterData($body, 1);

            list($s, $ret) = $this->_request('GENERATE_JWT_TOKEN', $body, 'POST', 10, 1);

            if (!$s) {
                return [false, $ret];
            }
            $aData       = $ret['data'] ?? [];
            $expire      = $aData['expire'] ?? 0;
            $expire_time = $expire - intval(START_TIME) - 200;
            $redis->set($r_key, json_encode($aData), ['ex' => $expire_time]);
        }


        return [true, $aData['token']];
    }


    /**
     * @throws Exception
     * 删除、修改类目同步IOT
     */
    public function deletePartByCateId($cateId, $cateName = '', $isDelete = true): array
    {
        $body = [
            'partsId' => $cateId
        ];

        $body = self::encryptRegisterData($body);

        $body['isDelete'] = $isDelete;

        $body['partsName'] = $cateName;

        list($success, $response) = $this->_request('DELETE_CATE', $body);

        if (!$success) {
            return [false, $response];
        }

        return [true, []];
    }

}
