<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/8
 * Time: 14:49
 */
namespace app\components;

use app\models\by;
use app\models\CUtil;

abstract class Tencent {

    protected static $_instance = [];

    const UQ_TOKEN = [
        'MINI'  => 0, //小程序TOKEN
        'OA'    => 1, //公众号TOKEN
        'OANEW' => 2, //新公众号TOKEN
    ];

    abstract static function factory();

    /**
     * @param $type :不同的appid
     * @return bool|mixed|string
     * 获取小程序/公共号全局唯一后台接口调用凭据（access_token）。
     * 调用绝大多数后台接口时都需使用 access_token，开发者需要进行妥善保存。
     * 小程序：https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/access-token/auth.getAccessToken.html
     * 公众号：https://developers.weixin.qq.com/doc/offiaccount/Basic_Information/Get_access_token.html
     */
    public function getUniqueAccessToken($type = self::UQ_TOKEN['MINI'])
    {
        $key   = AppCRedisKeys::UniqueAccessToken($type);
        $redis = by::redis('core');
        $token = $redis->get($key);
        if(!empty($token)) {
            return [true,$token];
        }
        if (YII_ENV_DEV || YII_ENV_TEST) {
            // 在开发或测试环境中返回错误信息
            return [false, "请从测试服获取, 手动刷入 {$key}"];
        }

        // 在非生产环境下，特定类型的请求也触发错误
        $invalidTypes = [self::UQ_TOKEN['OA'], self::UQ_TOKEN['OANEW']];
        if (!YII_ENV_PROD && in_array($type, $invalidTypes)) {
            trigger_error("请从测试服获取, 手动刷入 {$key}");
            // 可以选择是否返回额外的信息
            // return [false, "请从测试服获取, 手动刷入 {$key}"];
        }


        $unique_key = CUtil::getAllParams(__FUNCTION__, $type);
        list($anti) = by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,3,"EX");

        if(!$anti) {
            sleep(1);
            $token = $redis->get($key);
            if(!empty($token)) {
                return [true,$token];
            }
            trigger_error("获取UniqueToken 失败(1):{$type}", E_USER_ERROR);
        }


        switch ($type) {
            case self::UQ_TOKEN['MINI'] :
                $config =  CUtil::getConfig('weixin', 'common', MAIN_MODULE);break;
            case self::UQ_TOKEN['OA'] :
                $config =  CUtil::getConfig('wxoa', 'common', MAIN_MODULE);break;
            case self::UQ_TOKEN['OANEW'] :
                $config =  CUtil::getConfig('wxoanew', 'common', MAIN_MODULE);break;
            default :
                break;
        }

        $appId     = $config['appId']     ?? "";
        $appSecret = $config['appSecret'] ?? "";
        $url       = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";
//        if ( $type == self::UQ_TOKEN['OA']){
        if ( $type == self::UQ_TOKEN['OA'] || $type == self::UQ_TOKEN['OANEW']){
            $url  = "https://dreamempcenter.fscloud.com.cn/api/accesstoken";      //为了避免双方抢公众号的access_token,改成从第三方获取access_token,由第三方负责分发
        }
//        if ( $type == self::UQ_TOKEN['OANEW']){
//            $url  = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={$appId}&secret={$appSecret}";   //为了避免双方抢公众号的access_token,改成从第三方获取access_token,由第三方负责分发
//        }
        $aJson     = CUtil::curl_get($url);
        $ret       = (array)json_decode($aJson, true);
        $token     = $ret['access_token'] ?? "";
        if (empty($token)) {
            trigger_error("获取UniqueToken 失败:{$url}|{$aJson}", E_USER_ERROR);
        }

        $expire = isset($ret['expires_in']) ? intval($ret['expires_in']) : 7200;

        $redis->SETEX($key, $expire, $token);


        //删除并发标识
        by::model("CommModel",MAIN_MODULE)->ReqAntiConcurrency(0,$unique_key,0,'DEL');

        return [true,$token];
    }
}
