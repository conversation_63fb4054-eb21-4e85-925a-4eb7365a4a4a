<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/9/9
 * Time: 14:56
 */

namespace app\components;

use app\models\CUtil;

final class TcpClient
{
	protected static $instance = [];


	protected $buffer;
	/**
	 * 消息头
	 * @var array
	 */
	protected $head;

	/**
	 * Socket句柄
	 * @var resource
	 */
	protected $sock;

	/**
	 * 错误信息
	 * @var string
	 */
	protected $error;

	/**
	 * 错误标识
	 * @var int
	 */
	protected $errno;


	/**
	 * 连接超时
	 * @var int
	 */
	const TIMEOUT=1;




	/**
	 * @param string $host
	 * @param string $port
	 * @return UdpClient|null
	 *
	 */
	public static function newInstance(string $host = 'log.ugrow.cn', string $port = '25000'): TcpClient
	{
        $unique_key = CUtil::getAllParams($host,$port);

        if(empty(self::$instance[$unique_key])) {
            self::$instance[$unique_key] = new self($host, $port);
        }

        return self::$instance[$unique_key];
	}

	protected function __construct($host, $port)
	{
		
		$this->sock = fsockopen($host, $port, $this->errno, $this->error, self::TIMEOUT);
		stream_set_timeout($this->sock, self::TIMEOUT);
	}

	private function __clone()
	{
		// TODO: Implement __clone() method.
	}

	/**
	 *
	 * 发送数据到tcp 服务
	 * @param string $data
	 * @param $isClose
	 * @return bool
	 */
	public function sendData($data): bool
	{
		if (empty($data)) {
			return false;
		}

		if (!is_string($data)) {
			$data = strval($data);
		}

		$flag = fwrite($this->sock, $this->__encode($data));
		if($flag === false) {
			$err = error_get_last();
			$this->errno = $err['type'];
			$this->error = $err['message'];
			return false;
		}

		$info = stream_get_meta_data($this->sock);
		if($info['timed_out']) {
			$this->errno = -1;
			$this->error = "socket write timeout";
			return false;
		}

		return true;
	}


	/**简单组装tcp包
	 * @param string $msg
	 * @return string
	 */
	private function __encode(string $msg) {
		$tcpBuffer =TcpBuffer::newInstance();
		$tcpBuffer->setBuffer("");
		$tcpBuffer->write(TcpBuffer::STRING, $msg);
		return $tcpBuffer->getBuffer();
	}


	public function close(){
		fclose($this->sock);
	}
}
