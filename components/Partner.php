<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;

use app\models\by;
use app\models\CUtil;
use Faker\Provider\Uuid;

class Partner
{
    private static $_instance = [];

    private $account_partner_url;

    const URL = [
        'USER'              => '/dreame-account-center/authorization/get-code', //会员授权
    ];


    CONST CONFIG = YII_ENV_PROD ? [
        '10001' => [
            'TIMEOUT' => '300',//单位ms 报文超时时间
            'USER'    => 'dreame_sys',
            'PWD'     => '4L6rf5SoBesxlMgc',
            'AESKEY'  => 'm9m)ksGmjU4N+9n#'
        ],
    ] : [
        //自己公司用
        '10001' => [
            'TIMEOUT' => '300',//单位ms 报文超时时间
            'USER'    => 'xd_yjhx',
            'PWD'     => 'vZm6eUNiv7wWba4n',
            'AESKEY'  => 'm9m)ksGmjU4N+9n#'
        ],
    ];

    private function __construct()
    {
        $this->account_partner_url = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return Partner|array
     */
    public static function factory()
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * 统一调用方法
     */
    public function run(string $function, array $args, int $time = 0): array
    {
        switch ($function) {
            case 'user'://获取鉴权参数
                return $this->$function($args['user_id'],$args['client_id']);
            default:
                return [false, 'function 不存在'];
        }
    }


    /**
     * @param $user_id
     * @param $client_id
     * @return array
     * @throws \yii\db\Exception
     * 用户鉴权
     */
    public function user($user_id,$client_id)
    {
        //获取用户UID
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) return [false, '用户UID不存在！'];

        //参数配置
        $body = [
            'uid' => $uid,
        ];

        //参数加密
        list($s,$body) = $this->encryptData($body,$client_id,['uid']);
        if(!$s){
            return [false,$body];
        }

        //请求数据
        list($status, $ret) = $this->__request('USER',$client_id,$body);
        if(!$status){
            return [false,$ret];
        }

        return [true, $ret['data'] ?? ''];
    }


    /**
     * @param array $params
     * @param string $client_id
     * @param array $needEncryptField
     * @return array
     */
    private function encryptData(array $params, string $client_id, array $needEncryptField = []): array
    {
        if (empty($params)) return $params;
        $config = self::CONFIG[$client_id] ?? [];
        if (empty($config)) {
            return [false, '平台ID不正确！'];
        }
        foreach ($params as $key => $value) {
            if (!empty($value) && in_array($key, $needEncryptField)) {
                $params[$key] = MallAuth::factory()->encrypt($value, $config['AESKEY']);
            }
        }
        return [true, $params];
    }

    /**
     * @param string $event
     * @param string $client_id
     * @param array $body
     * @param string $method
     * @return array
     * request 调用
     */
    public function __request(string $event,string $client_id,array $body=[],string $method = 'POST'): array
    {
        $config = self::CONFIG[$client_id] ?? [];
        if(empty($config)){
            return [false, '平台ID不正确！'];
        }
        $url = $this->account_partner_url . (self::URL[$event] ?? '');
        $uuid   = str_replace("-", '', Uuid::uuid());//去掉中间的-
        $header = [
            "Content-Type:application/json",
            "Authorization: Basic " . base64_encode("{$config['USER']}:{$config['PWD']}"),
            "i:" . MallAuth::factory()->encrypt($uuid . '_' . time(), $config['AESKEY']),
            "g:" . $uuid,
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ];

        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body, 320), $header, $method, 60);
        // CUtil::debug("httpcode:{$httpCode}|err:{$err}|data：" . json_encode($body, 320) . " | ret:" . json_encode($ret), "account_partner");
        CUtil::setLogMsg(
            "account_partner",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
        return [$ret['code'] === 0, $ret];
    }
}
