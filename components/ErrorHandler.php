<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/4/23
 * Time: 11:32
 */

namespace app\components;
use app\models\CUtil;
use yii\web\ErrorHandler AS BaseErrorHandler;

class ErrorHandler extends BaseErrorHandler {

    CONST ORIGIN_ERR = [
        'HANDLE' => 0,
        'RENDER' => 1,
    ];

    /**
     * @param $ext_msg
     * @return bool
     * @throws \Exception
     * components : db_*** 有配置
     *   'commandClass' => 'app\components\Command',
     * 则会执行Mysql短线重连操作
     * 否则由 renderException 抛出 500错误
     * 对应文件：app\prerelease\components\Command.php
     * 本方法只存在 handleError中
     */
    private function __mysqlHasGoneAway($ext_msg) {
        //如果不是yii\db\Exception异常抛出该异常或者不是MySQL server has gone away
        $offset = stripos($ext_msg,'MySQL server has gone away');
        if($offset === false) {
            return false;
        }

        throw new \Exception($ext_msg);
    }

    /**
     * @param $trace
     * @return array
     * 截取部分关键错误信息
     */
    private function __trimTrace($trace): array
    {
        array_shift($trace);
        $count = count($trace);

        if($count > 10) {
            $trim  = IS_CLI ? 8 : 6;
            $trace = array_slice($trace,0,CUtil::uint(count($trace)-$trim));
        }

        $logs    = [];
        foreach ($trace as $tr) {
            if(isset($tr['function']) && isset($tr['class']) && isset($tr['type'])) {
                $logs[]  = "{$tr['class']}{$tr['type']}{$tr['function']}";
            }
        }

        $logs[]  = "......";

        return $logs;
    }

    /**
     * @param int $origin
     * @param string $uniqueId
     * @param string $message
     * @param string $line
     * @param string $file
     * @param string $logs
     * 统一消息收集
     * 此处禁止关闭或阻断，所有环境的错误信息输出！！！！！！！！
     */
    private function __sendMsgToUdp($origin=0,$uniqueId='',$message='',$line='',$file='',$logs='') {

        $message = mb_convert_encoding($message, "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]);
        $cli     = IS_CLI ? "CLI" : "SAPI";

//        CUtil::json_response(-1,"[{$origin}]：{$uniqueId}|{$message} ; IN : {$file} ; At Line : {$line}  | {$logs}");
//        CUtil::sendMsgToUdp([
//            "<font color='info'>{$cli}服务器异常[{$origin}]：{$uniqueId}</font>",
//            "**异常点 :** {$message}\n",
//            "**错误文件 :** {$file} ($line)\n",
//            "**堆栈信息 :** {$logs}\n",
//        ]);
        $config  = CUtil::getConfig('sendLog','common',MAIN_MODULE) ;
        $sendLogIsLock = $config['isLock']??0;
        $sendLogIsLock && CUtil::debug("[{$origin}]：{$uniqueId}|{$message} ; IN : {$file} ; At Line : {$line}  | {$logs}","udpLog");


        //客户端展示
        if (\Yii::$app instanceof \yii\web\Application) {
            CUtil::json_response(-1,"当前网络异常，请检查网络连接({$uniqueId})");
        }

    }

    /**
     * @param int $code
     * @param string $message
     * @param string $file
     * @param int $line
     * @return bool|void
     * @throws \Exception
     * 重写 handleError ，让其在非致命错误的时候，程序可以继续执行
     */
    public function handleError($code, $message, $file, $line ) {

        //暂时关闭Mysql 断线重连检测
        //$this->__mysqlHasGoneAway($message);

        $trace      = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS);
        $logs       = $this->__trimTrace($trace);
        $logs       = implode('<br />',$logs);
        $http_code  = CUtil::getConfig('status','http_code',MAIN_MODULE);
        $statusCode = $http_code['request_err'] ?? 404;
        $uniqueId   = $statusCode."_".microtime(true);

        //todo: SEND TO UDP SERVER
        $this->__sendMsgToUdp(self::ORIGIN_ERR['HANDLE'],$uniqueId,$message,$line,$file,$logs);
    }

    /**
     * @param \Exception $exception
     * 致命错误
     */
    public function renderException($exception) {

        $trace  = $exception->getTrace();
        $logs   = $this->__trimTrace($trace);
        $logs   = implode('<br />',$logs);

        $statusCode = property_exists($exception,'statusCode') ? $exception->statusCode : 0;
        if(empty($statusCode)) {
            $http_code  = CUtil::getConfig('status','http_code',MAIN_MODULE);
            $statusCode = $http_code['inter_err'] ?? 500;
        } else {
            if ($statusCode == 404) {
                CUtil::json_response(-1,"页面未找到");
            }
        }

        $uniqueId       = $statusCode."_".microtime(true);
        $message        = $exception->getMessage();
        $line           = $exception->getLine();
        $file           = $exception->getFile();

        //todo: SEND TO UDP SERVER
        $this->__sendMsgToUdp(self::ORIGIN_ERR['RENDER'],$uniqueId,$message,$line,$file,$logs);
    }
}
