<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/5/8
 * Time: 14:42
 */

namespace app\components;
use app\models\by;
use app\models\CUtil;

class WXOA extends Tencent
{

    CONST TOKEN             = "dreame123";
    CONST EncodingAESKey    = "q23MphDwimhntjOpA6SIDvFxroKeSWoKyHOrDSStIZ5";
    CONST EncodingNewAESKey    = "zgMMn1udg3JJygSMUKIOD1TyltemLuaLMUGjN77Xic0";
    CONST ART               = "https://mp.weixin.qq.com/s?__biz=Mzg2NTkwMTg5MA==&mid=2247498920&idx=1&sn=777a8f67e6cbe5599958842d7a9650c8&source=41#wechat_redirect";

    //必要的单例模式
    private function __construct(){}
    private function __clone(){}

    /**
     * @return WXOA
     * */
    public static function factory()
    {
        if (!isset(self::$_instance['wxoa']) || !is_object(self::$_instance['wxoa'])) {
            self::$_instance ['wxoa'] = new self();
        }

        return self::$_instance ['wxoa'];
    }



    /**
     * @param $openid
     * @param $tpl_id
     * @param $data
     * @param string $pagepath
     * @return array
     * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html
     * 发送模板消息
     */
    public function templatePush($openid,$tpl_id,$data,$pagepath='')
    {
        list($status, $token) = $this->getUniqueAccessToken(empty(CUtil::wxOaLock()) ? self::UQ_TOKEN['OA'] : self::UQ_TOKEN['OANEW']);
        $url   = "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token={$token}";

        $body  = [
            'touser'      => $openid,
            'template_id' => $tpl_id,
            'data'        => $data
        ];

        if ($pagepath) {

            $config                 =  CUtil::getConfig('weixin', 'common', MAIN_MODULE);

            $body['miniprogram']    = [
                'appid'         => $config['appId'],
                'pagepath'      => $pagepath,
            ];
        }

        $buffer = CUtil::curl_post($url,json_encode($body),null,10);

        if (is_string($buffer)) {
            $ret     = (array)json_decode($buffer,true);
            $errcode = $ret['errcode'] ?? "";
            $errmsg  = $ret['errmsg'] ?? "";
            if(!empty($errcode)) {
                return [false,"{$errmsg}({$errcode})"];
            }
        }

        return [true,$buffer];
    }

    /**
     * @param $params
     * @param $notify_data
     * @return array
     * @throws \yii\db\Exception
     * @throws \RedisException
     * 公众号事件
     */
    public function Focus($params,$notify_data): array
    {
        $timestamp = $params['timestamp']      ?? "";
        $nonce     = $params['nonce']          ?? "";
        $signature = $params['signature']      ?? "";
        $openid    = $params['openid']         ?? "";
        $eventKey  = $notify_data['EventKey']       ?? "";

        $bool = $this-> __checkSignature($signature,$timestamp,$nonce);
        if(!$bool) {
            return [false,"非法签名信息"];
        }

        list($status,$info) = $this->GetUserInfo($openid,false);
        if(!$status) {
            return [false,$info];
        }

        !YII_ENV_PROD && CUtil::debug(json_encode($info),'oa_data_uinfo');

        $unionid  = $info['unionid']        ?? "";//取消关注时该值为空

        $event    = $notify_data['Event']   ?? "";
        $event    = strtolower($event);

        //关注推送APP
        if ($event == 'subscribe'){
            $user_id = by::users()->getUserIdByUnionId($unionid,1) ?? '';
            CUtil::debug( '关注推送APP用户|'.$unionid.'|'.$user_id, "msg.SUBSCRIBE.record.send");
            if ($user_id) EventMsg::factory()->run('subscribe', ['user_id' => $user_id]);
        }

        switch ($event) {
            case 'subscribe':
            case 'unsubscribe':
            case 'scan':
                //关注、取消事件
                $sub      = $event == "subscribe" ; //是否关注公众号，0否1是
                $sub      = intval($sub);
                $return   = by::OaFocus()->SaveLog($openid,$unionid,$sub,$eventKey,$info);
                break;
            default:
                $return = [false, '其他事件'];
        }
        return  $return;
    }

    /**
     * @param $signature
     * @param $timestamp
     * @param $nonce
     * @return bool
     * 关注/取消关注公众号消息签名
     */
    private function __checkSignature($signature,$timestamp,$nonce): bool
    {

        $tmpArr = [self::TOKEN, $timestamp, $nonce];
        sort($tmpArr, SORT_STRING);
        $tmpStr = implode( $tmpArr );
        $tmpStr = sha1( $tmpStr );

        return $tmpStr == $signature;
    }

    /**
     * @param $openid : 公众号的openid
     * @param $cache : 是否走缓存数据
     * @return array
     * 获取用户基础信息
     * https://developers.weixin.qq.com/doc/offiaccount/User_Management/Get_users_basic_information_UnionID.html#UinonId
     */
    public function GetUserInfo($openid, $cache = true): array
    {
        $ret    = [];
        $r_key  = AppCRedisKeys::WXOAUinfoKey($openid);
        $redis  = by::redis();
        if ($cache) {
            $aJson      = $redis->get($r_key);
            $ret        = json_decode($aJson, true);
        }
        if (empty($ret)) {
            list($status, $token) = WeiXin::factory()->getUniqueAccessToken(empty(CUtil::wxOaLock()) ? WeiXin::UQ_TOKEN['OA'] : WeiXin::UQ_TOKEN['OANEW']);
            if(!$status) {
                return [false,$token];
            }

            $url                 = "https://api.weixin.qq.com/cgi-bin/user/info?access_token={$token}&openid={$openid}&lang=zh_CN";
            $ret                 = CUtil::curl_get($url);
            $ret                 = (array)json_decode($ret, true);
            if (isset($ret['errcode'])) {
                $return = [false, "{$ret['errmsg']}({$ret['errcode']})"];
            } else {
                $redis->set($r_key, json_encode($ret), 600);
                $return = [true, $ret];
            }
        }

        $return = $return ?? [true, $ret];

        return $return;
    }


}
