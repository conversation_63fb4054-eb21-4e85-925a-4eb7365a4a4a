<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/31
 * Time: 16:02
 */
namespace app\components;

use app\models\CUtil;
use OSS\OssClient;

require_once \Yii::getAlias("@vendor").'/mycompany/aliyun-oss-sdk/autoload.php';
/**
 * Class AliYunOss
 * @package app\components
 *
 * 1、请在 app\web\index.php 中加入
 * require(__DIR__ . '/../vendor/mycompany/aliyun-oss-sdk/autoload.php');
 *
 * 2、请在创建好0777权限 文件临时存放目录 \app\web\Upload\tmp
 *
 * 3、请按模块设置好 $listName 该参数用于生成一个异步队列、维护图片上传至阿里云OSS
 *
 * Api文档地址
 * https://helpcdn.aliyun.com/document_detail/32099.html?spm=a2c4g.11186623.6.785.N7IzLc
 */
class AliYunOss {

    protected static $_instance = [];

    CONST IMG_TYPE            = ['jpg','jpeg','gif','png','mp4','mpeg','wmv','avi'];//图片格式
    CONST FILE_TYPE           = ['ttf','json','csv','xls','xlsx'];//文件格式

    //base64二进制获取的图片格式
    CONST B_IMG_TYPE          = [
        'data:image/jpeg'   => 'jpg',
        'data:image/jpg'    => 'jpg',
        'data:image/png'    => 'png',
        'data:image/gif'    => 'gif'
    ];

    //定义允许上传的类型
    public static function getAllowType() {
        return array_merge(self::IMG_TYPE,self::FILE_TYPE);
    }

    public static $me = [];

    public function __construct() {

    }

    /**
     * @param int $width
     * @param int $height
     * @return string
     * 图片缩放
     */
    public static function XOssResizeUriLink($width=300,$height=300){
        return "?x-oss-process=image/resize,h_{$width},w_{$height}";
    }

    /**
     * @return string
     * 生成简短的唯一流水号
     */
    public function CreateOrderId () {
        $my_pid             = str_pad(substr(getmypid(),0,5), 5, '0' , STR_PAD_LEFT);
        $uniqid             = uniqid();

        $microtime = microtime(true);
        $sec = floor($microtime); // 获取秒数部分
        $mic = round(($microtime - $sec) * 1000); // 将剩余的部分转换为毫秒，并取整
        $mic = str_pad($mic, 3, '0', STR_PAD_LEFT); // 确保毫秒是3位数，不足部分左侧补零

        $rand               = mt_rand(10,99);
        usleep(1000); //休眠1毫秒，保证序列号唯一

        return $uniqid . $mic . $my_pid . $rand;
    }

    /**
     * @param null $object
     * @return bool|string
     * 读取文件类型
     */
    public static function getFileType($object=null) {
        if( is_null($object) ) {
            return false;
        }

        return strtolower(substr($object,strrpos($object,'.')+1)); //得到文件类型，并且都转化成小写
    }

    /**
     * @param $name
     * @return bool
     * 判断是否是图片
     */
    public static function isImage($name) {
        $postfix = strtolower(substr($name,strrpos($name,'.')+1)); //得到文件类型，并且都转化成小写
        if( in_array($postfix, self::IMG_TYPE) ) {
            return true;
        }

        return false;
    }

    /**
     * @param $date
     * @param null $object
     * @return array
     * 获取指定Bucket下的目录
     */
    public static function getBucketDirByDay($date,$object=null) {

        $config     = CUtil::getConfig('AliYunOss','common',MAIN_MODULE);
        $type       = self::getFileType($object);

        switch ($type) {
            case in_array($type,self::FILE_TYPE)   :
                $sub_bucket = $config['fileBucket'];break;
            case in_array($type,self::IMG_TYPE)   :
            default : $sub_bucket = $config['imgBucket'];break;
        }

        if(empty($sub_bucket)) {
            return [false,"BUCKET Not Exist"];
        }

        $date       = $date ? date("Ymd",strtotime($date)) : date("Ymd");
        $bucketDir  = "{$sub_bucket}/".date("Ym",strtotime($date));

        return [true,$bucketDir];
    }

    /**
     * @return AliYunOss
     * 单例模式
     */
    public static function factory() {

        if (!isset(self::$_instance['AliYunOss']) || !is_object ( self::$_instance['AliYunOss'])) {
            self::$_instance['AliYunOss']  = new self();
        }

        return self::$_instance['AliYunOss'];

    }

    /**
     * @param string $module
     * @param string $config_name
     * @param string $config_key
     * @return OssClient
     * 获取oss client实例
     */
    public static function OssClient($module='',$config_key='AliYunOss',$config_name='common') {

        if(!$module) {
            return  null;
        }

        if (!isset(self::$_instance[$module]['OssClient']) || !is_object (self::$_instance[$module]['OssClient'])) {
            $config          = CUtil::getConfig($config_key,$config_name,$module);
            $AccessKeyId     = isset($config['AccessKeyId'])      ? $config['AccessKeyId']     : "";
            $AccessKeySecret = isset($config['AccessKeySecret'])  ? $config['AccessKeySecret'] : "";
            $endpoint        = isset($config['endpoint'])         ? $config['endpoint']        : "";
            self::$_instance[$module]['OssClient'] = new OssClient($AccessKeyId,$AccessKeySecret,$endpoint);
        }

        return self::$_instance[$module]['OssClient'];
    }


    public function uploadFileDirectToOss($file,$allow_type=[])
    {
        try {
            $time = time();
            if(empty($file)) {
                throw new \Exception("文件未上传");
            }
            list($status,$bucket_dir)  = self::getBucketDirByDay(date("Ymd",$time),$file['name']);
            if(!$status) {
                throw new \Exception($bucket_dir);
            }

            $name  = $file['name'];
            $type       = strtolower(substr($name,strrpos($name,'.')+1)); //得到文件类型，并且都转化成小写
            $allow_type = empty($allow_type) ? self::getAllowType() : $allow_type;
            if(!in_array($type, $allow_type)){
                throw new \Exception("非法的文件类型：{$type};仅允许文件格式：".json_encode(self::getAllowType()));
            }
            //异步redis队列 维护上传至OSS
            $config     = CUtil::getConfig('AliYunOss','common',MAIN_MODULE);

            $object = empty($file['name'] ?? '') ? self::CreateOrderId() . '.' . $type : $file['name'];
            $oss_uri    = $config['cdnAddr'].'/'.$bucket_dir.'/';
            $origin     = $oss_uri.$object;
            $link['link']     = $origin;

            //新文件名
            $upload_file = $file['temp_name']??'';
            //todo send to oss
            $OssClient  = self::OssClient(MAIN_MODULE);
            $OssClient->uploadFile($config['bucket'], $bucket_dir.'/'.$object, $upload_file);

            @unlink($upload_file);//删除本地服务器临时文件

            return [true,$link];
        } catch (\Exception $e) {
            return [false,$e->getMessage()];
        }
    }

    /**
     * 上传文件至 OSS （简单写法）
     * @param string $filename
     * @return array
     */
    public function uploadFile(string $filename): array
    {
        try {
            $time = time();

            list($status, $bucket_dir) = self::getBucketDirByDay(date("Ymd", $time), $filename);
            if (!$status) {
                throw new \Exception('Bucket 不存在');
            }

            // 文件类型
            $type = strtolower(substr($filename, strrpos($filename, '.') + 1));
            if (!in_array($type, self::getAllowType())) {
                throw new \Exception('非法的文件类型');
            }

            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);

            $oss_uri = $config['cdnAddr'] . '/' . $bucket_dir . '/';
            $path = $oss_uri . $filename;

            // 上传至OSS
            $OssClient = self::OssClient(MAIN_MODULE);
            $OssClient->uploadFile($config['bucket'], $bucket_dir . '/' . $filename, $filename);

            return [true, $path];

        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }


    /**
     * @param string $content
     * @param string $type
     * @return array
     * 上传图片二维码图片
     */
    public function uploadBinaryImage(string $content,string $type = 'jpg'): array
    {
        try {
            $time = time();

            // 生成唯一的文件名
            $filename = date("YmdHis", $time) . rand(1000, 9999) . '.' .$type;

            list($status, $bucket_dir) = self::getBucketDirByDay(date("Ymd", $time), $filename);
            if (!$status) {
                throw new \Exception('Bucket 不存在');
            }

            $config = CUtil::getConfig('AliYunOss', 'common', MAIN_MODULE);

            $oss_uri = $config['cdnAddr'] . '/' . $bucket_dir . '/';
            $path = $oss_uri . $filename;

            // 上传至OSS
            $OssClient = self::OssClient(MAIN_MODULE);
            $OssClient->putObject($config['bucket'], $bucket_dir . '/' . $filename, $content);

            return [true, $path];

        } catch (\Exception $e) {
            return [false, $e->getMessage()];
        }
    }


    /**
     * @param $file : 二进制文件 (Content-Type: multipart/form-data)
     * @param array $allow_type : 允许类型
     * @param bool $resize : 是否缩放
     * @param int $mb :默认上传上限
     * @return array
     * 图片上传  直接返回拼接好的远程Oss地址【生成一张原图和一张小图地址】
     * 文件信息追加至Redis队列 由Redis队列负责真正上传
     * https://help.aliyun.com/document_detail/44688.html
     */
    public function uploadFileToOss($file,$allow_type=[],$resize=true,$mb=150) {
        try {

            $time = time();

            if(empty($file)) {
                throw new \Exception("图片未上传");
            }

            list($status,$bucket_dir)  = self::getBucketDirByDay(date("Ymd",$time),$file['name']);
            if(!$status) {
                throw new \Exception($bucket_dir);
            }

            //判断是否是通过HTTP POST上传的
            if(!is_uploaded_file($file['tmp_name'])){
                throw new \Exception("仅允许HTTP POST上传");
            }
            $size = sprintf('%.2f',$file['size'] / (1024 *1024));
            if( bccomp($size, $mb) > 0) {
                throw new \Exception("文件最大不超过{$mb}M");
            }

            $name  = $file['name'];
//            if (self::isImage($name)){
//                $valid = @getimagesize($file['tmp_name']);
//                if($valid === false) {
//                    throw new \Exception("文件格式错误");
//                }
//            }

            $type       = strtolower(substr($name,strrpos($name,'.')+1)); //得到文件类型，并且都转化成小写
            $allow_type = empty($allow_type) ? self::getAllowType() : $allow_type;
            if(!in_array($type, $allow_type)){
                throw new \Exception("非法的文件类型：{$type};仅允许文件格式：".json_encode(self::getAllowType()));
            }

            //开始移动文件到相应的文件夹
            if(!is_dir(UPLOAD_TMP_PATH) ) {
                $oldumask = umask(0);
                mkdir(UPLOAD_TMP_PATH,0777,true);
                umask($oldumask);
            }

            //新文件名
            $object = self::CreateOrderId().'.'.$type;
            $upload_file = UPLOAD_TMP_PATH.'/'.$object; //上传文件
            if(move_uploaded_file($file['tmp_name'],$upload_file)){
                //异步redis队列 维护上传至OSS
                $config     = CUtil::getConfig('AliYunOss','common',MAIN_MODULE);

                $oss_uri    = $config['cdnAddr'].'/'.$bucket_dir.'/';
                $origin     = $oss_uri.$object;


                if (self::isImage($name)) {
                    $link['bigImg']   = $origin;
                    $link['smallImg'] = $origin;
                    //缩放
                    $resize && $link['smallImg'] = $origin.self::XOssResizeUriLink(300,300);
                } else {
                    $link['link']     = $origin;
                }

                //todo send to oss
                $OssClient  = self::OssClient(MAIN_MODULE);
                $OssClient->uploadFile($config['bucket'], $bucket_dir.'/'.$object, $upload_file);

                @unlink($upload_file);//删除本地服务器临时文件

                return [true, $link];
            } else {
                throw new \Exception("图片上传失败");
            }
        } catch (\Exception $e) {
            return [false,$e->getMessage()];
        }
    }

    public function sign()
    {
        $config = CUtil::getConfig('AliYunOss', 'common', 'main');
        $id     = isset($config['AccessKeyId']) ? $config['AccessKeyId'] : "";
        $key    = isset($config['AccessKeySecret']) ? $config['AccessKeySecret'] : "";
        list($http, $url) = explode("//", $config['endpoint']);
        // **需维护**
        $host       = 'https://' . $config['bucket'] . '.' . $url;
        $dir        = $config['imgBucket'];
        $now        = time();
        $expire     = 1800;  //设置该policy超时时间是10s. 即这个policy过了这个有效时间，将不能访问。
        $end        = $now + $expire;
        $expiration = str_replace('+00:00', '.000Z', gmdate('c', $end));

        $condition    = array(0 => 'content-length-range', 1 => 0, 2 => 4294967296); //4个G
        $conditions[] = $condition;

        $start        = array(0 => 'starts-with', 1 => '$key', 2 => $dir);
        $conditions[] = $start;

        $arr            = array('expiration' => $expiration, 'conditions' => $conditions);
        $policy         = json_encode($arr);
        $base64_policy  = base64_encode($policy);
        $string_to_sign = $base64_policy;
        $signature      = base64_encode(hash_hmac('sha1', $string_to_sign, $key, true));

        $response              = array();
        $response['accessid']  = $id;
        $response['host']      = $host;
        $response['policy']    = $base64_policy;
        $response['signature'] = $signature;
        $response['expire']    = $end;
        $response['dir']       = $dir;
        $response['cdn']       = $config['cdnAddr'];
        return $response;
    }

    /**
     * @param $picture
     * @param $width
     * @param $high
     * @return string
     * 缩放裁剪图片 裁剪完为正方形
     */
    public function corpPicture($picture,$width,$high): string
    {
        return $picture."?x-oss-process=image/resize,w_{$width}/crop,h_{$high}";
    }
}
