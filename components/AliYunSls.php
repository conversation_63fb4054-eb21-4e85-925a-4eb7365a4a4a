<?php

namespace app\components;

use app\models\CUtil;
use Yii;

require_once Yii::get<PERSON><PERSON><PERSON>("@vendor")."/mycompany/aliyun-log-php-sdk/Log_Autoload.php";


class AliYunSls {

    protected static $logClient = null;

    protected function __construct()
    {

    }
    public static function getLogClient()
    {
        if (self::$logClient === null) {
            $config = CUtil::getConfig('AliYunSls','common',MAIN_MODULE);
            // 初始化LogClient用于发起请求
            static::$logClient =  new \Aliyun_Log_Client($config['endpoint'], $config['AccessKeyId'], $config['AccessKeySecret']);
        }

        return self::$logClient;
    }

    /**
     * @param string $topic
     * @param string $content
     * @param string $level
     * @param $filePath
     * @return void
     * @throws \Aliyun_Log_Exception
     */
    public static function putLog(string $topic,string $content,string $level = 'INFO', $filePath = '') {
        $config   = CUtil::getConfig('AliYunSls', 'common', MAIN_MODULE);
        $project  = $config['project'];
        $logstore = $config['logstore'];
        $client = self::getLogClient();

        // $topic = date('Ymd').'/'.$topic.'.txt';
        $filePath = $filePath ?  : date('Ymd').'/'.$topic.'.txt';

        $contents = [
            'path'    => $filePath."k8s",
            'time'    => date('Y-m-d H:i:s'),
            'level'   => $level,
            'message' => strval($content),
            'project' => PRO_NAME
        ];
        $logItem = new \Aliyun_Log_Models_LogItem();
        $logItem->setTime(time());
        $logItem->setContents($contents);
        $logitems = array($logItem);
        $request = new \Aliyun_Log_Models_PutLogsRequest($project, $logstore,
            $topic, "", $logitems);

        $client->putLogs($request);

    }




}
