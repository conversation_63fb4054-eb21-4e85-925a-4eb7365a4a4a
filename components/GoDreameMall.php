<?php

namespace app\components;

use app\models\CUtil;

/**
 * 商城go重构项目
 */
class GoDreameMall
{
    private static $_instance;
    // host
    private $host;

    // 签名
    private $sign_key;

    public function __construct()
    {
        // 获取host
        $this->host = CUtil::getConfig('host', 'config', \Yii::$app->id)['mp_mall_host'] ?? '';

        $config         = CUtil::getConfig('mp-mall', 'common', MAIN_MODULE);
        $this->sign_key = $config['sign_key'];
    }

    public static function factory(): GoDreameMall
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 注销用户
    public function cancel($user_id): array
    {
        $data = [
            'user_id'   => $user_id,
            'key'       => 'content_delete',
        ];

        return $this->request('/v1/user/cancel', $data);
    }

    // 请求用户平台
    private function request($path, $params, $is_sign = true, $is_log = true)
    {
        try {
            // 请求地址
            $url = $this->host . '/dreame-mall/api' . $path;

            // 请求头，加上时间戳和签名
            if ($is_sign) {
                $timestamp = intval(microtime(true) * 1000);
                $sign      = MpSign::sign($params, $timestamp, $this->sign_key);
                $header    = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                    "dreame-api-timestamp: {$timestamp}",
                    "dreame-api-sign: {$sign}",
                ];
            } else {
                $header = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                ];
            }

            // 发送请求，设置超时时间为10秒
            $response = CUtil::curl_post($url, json_encode($params, 320), $header, 10, true);

            // 请求结果
            $result = json_decode($response, true);

            if (!isset($result['code'])) {
                // 可以根据具体情况处理无错误码的情况
                CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '响应结果' . $response, 'err.mall.request(1)');
                return [false, []];
            }

            if ($is_log && $result['code'] != 0) {
                CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '响应结果' . $response, 'err.mall.request(2)');
                return [false, $result['msg']];
            }

            return [true, $result['data']];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '异常信息' . json_encode($msg, 320), 'err.mall.request(3)');

            // 返回缓存数据或默认数据
            return [false, '请求用户平台异常'];
        }
    }

}