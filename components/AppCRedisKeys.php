<?php

/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/3/19
 * Time: 10:29
 * 类名 + 方法名 + 参数
 */

namespace app\components;

use app\models\CUtil;

class AppCRedisKeys
{
    public static $prefix = PRO_NAME; //各项目防Redis冲突唯一标识！！！！

    //类名 + 方法名 + 参数
    public static function sessionKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //小程序全局唯一后台接口调用凭据
    public static function UniqueAccessToken(string $a_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $a_id);
    }

    // app 获取用户信息
    public static function AppLoginKey(string $jwtToken):string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $jwtToken);
    }

    //根据openudid 获取 user_id
    public static function getUserIdByOpenUdId($openudid, $user_type,$loginType): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $openudid, $user_type,$loginType);
    }

    //根据unionid 获取 user_id
    public static function getUserIdByUnionId($unionid, $user_type): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $unionid, $user_type);
    }

    //访问频率限制
    public static function AccFrequency($unique_key, $user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $unique_key, $user_id);
    }

    //防并发
    public static function ReqAntiConcurrency($unique_key, $user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $unique_key, $user_id);
    }

    //进程是否存在KEY
    public static function ProcessExistKey($p_name)
    {
        return self::$prefix . "|" . __FUNCTION__ . "|{$p_name}";
    }

    //APP发起HTTP请求的操作记录
    public static function postLogKey($app)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $app);
    }

    //接口请求次数统计
    public static function apiRequest()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //接口总耗时
    public static function apiTimeUsed()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //按分钟统计QPS
    public static function getApiQpsKey($date)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $date);
    }


    //根据Uid 获取用户信息
    public static function getOneByUid($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //根据商城ID 获取用户信息
    public static function getMallInfoById($mall_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mall_id);
    }

    //根据uid 获取用户信息
    public static function getMallInfoByUid($uid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $uid);
    }

    //根据uid 获取用户信息
    public static function getMallInfoByUidWithDeleted($uid, $include_deleted): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $uid, $include_deleted);
    }

    //根据user_id获取用户信息
    public static function getMallInfoByUserId($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    public static function getInfoByUserId($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //新注册用户关联openID和userId
    public static function getOpenIdAndUserId($openid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $openid);
    }

    //新注册用户进入redis 获取微信基础数据 手机授权时存入数据库
    public static function getRedisWxLoginParam($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //新注册用户进入redis 获取个人基础数据 手机授权时存入数据库
    public static function getRedisWxDetailParam($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }


    //获取user主表信息
    public static function userMainInfo($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //根据phone获取用户信息(最新的一条信息)
    public static function getWxUserByPhone($phone): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone);
    }

    //正式会员
    public static function getRealMainUserByIdKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //游客
    public static function getRealRMainUserByIdKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //根据mallId获取用户信息(列表)
    public static function getUsersByMallId($mallId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mallId);
    }

    //根据userId获取外来用户信息(列表)
    public static function getMallsByUserId($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId);
    }

    //获取用户积分
    public static function getPointByUser($user_id, $date): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $date);
    }

    //用户信息并发锁
    public static function userProfileConcurrentKey($user_id, $k): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $k);
    }

    //用户信息key
    public static function getUserProfileKey($user_id, $k): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $k);
    }

    //用户信息key
    public static function getUserExtendKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //商品主表详情
    public static function getOneGmainByGid($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //商品主表详情
    public static function getOneGmainBySku($sku,$is_del)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku,$is_del);
    }

    //商品表详情
    public static function getOneGtypeByGid($gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //商品哈希列表
    public static function getGoodsList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //追觅小店可选商品列表
    public static function getStoreGoodsList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //追觅小店店铺内商品已选列表
    public static function getStoreGoodsRealationList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }



    //所有商品表
    public static function getAllGoodsList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //商品属性名str
    public static function getAkByName($gid, $at_name)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $at_name);
    }

    //商品id str
    public static function getOneAkById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //商品属性名列表
    public static function getAkListByGid($gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //商品自定义价格列表
    public static function getIniPriceList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //商品自定义价格单个数据
    public static function getOneIniKey($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //iniPrice 队列处理
    public static function iniPriceList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //商品生效中自定义价格列表
    public static function getEffectIniListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //商品生效中自定义价格列表
    public static function getEffectSkuIniKey($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //自定义价格商品库存
    public static function gIniStock($gini_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gini_id);
    }

    //自定义价格商品销量
    public static function gIniSales($gini_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gini_id);
    }

    //自定义价格商品未付款数
    public static function gIniWait($gini_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gini_id);
    }

    //自定义价格商品库存sum
    public static function gIniListStock($gini_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gini_id);
    }


    //属性值str
    public static function getOneAvByAkId($ak_id, $at_val)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ak_id, $at_val);
    }

    //属性值str
    public static function getOneAvById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //属性值列表
    public static function getAvList($ak_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ak_id);
    }

    //规格sku详情
    public static function getSpecsbySku($sku)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }


    //规格id详情
    public static function getSpecsbyId($gid, $id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $id);
    }

    //规格列表
    public static function getSpecsListByGid($gid,$isDelete): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid,$isDelete);
    }

    //设置价格sku详情
    public static function getGspricebySku($sku, $sprice_type)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku, $sprice_type);
    }

    //设置价格ID详情
    public static function getGspricebyId($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //设置价格列表
    public static function getGspriceListBySku($sku)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //商品主表详情
    public static function getTagListByGid($gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //所有商品对应的tid
    public static function getGidAndTag()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //商品库存
    public static function gStock($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //商品销量
    public static function gSales($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //商品未付款数
    public static function gWait($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //商品库存sum
    public static function gListStock($gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //通过手机号码获取用户信息
    public static function GetUidByPhone($phone)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone);
    }

    //获取用户手机号码
    public static function GetPhoneByUid($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取用户商城号码
    public static function GetMallIdByUid($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //门店列表
    public static function getShopList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //门店经纬度
    public static function getShopPos($shop_name = 0)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $shop_name);
    }

    //门店经纬度
    public static function getOneShopByGid($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //获取用户单个地址
    public static function getOneAddress($user_id, $aid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $aid);
    }

    //订单详情
    public static function getOneOuserInfo($user_id, $order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $order_no);
    }

    //尾款订单详情
    public static function getOneOuserInfoByDeposit($user_id, $deposit_order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $deposit_order_no);
    }


    //订单列表
    public static function getOuserList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //订单商品列表
    public static function getOgoodsList($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //订单商品详情
    public static function getOgoodsInfo($user_id,$order_no,$gid,$sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id,$order_no,$gid,$sid);
    }

    //通过订单号获取支付未退款总价格
    public static function getPriceByOrderNo($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //订单商品列表1
    public static function getOgoodsByGid($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取所有已完成用户订单
    public static function getOrderGidListByUid($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    public static function getOrderListByUid($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //订单商品快照
    public static function getOcfgHash($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //订单商品收发货信息
    public static function getOneOadInfo($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //订单号对应的user_id
    public static function getUserIdByNo($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }


    //用户下单锁
    public static function getUserOrderLock($user_id, $type)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $type);
    }


    //订单号获取订单详情
    public static function getOMInfoByOrderNo($user_id, $order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $order_no);
    }


    //获取用户所有地址
    public static function getAddressList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取用户默认地址
    public static function getDefaultAddress($user_id, $is_del)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $is_del);
    }

    //banner详情
    public static function getBannerById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //banner列表
    public static function getBannerList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //用户购物车
    public static function userCart($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //添加购物车
    public static function userAddCart($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //支付流水
    public static function getOrderPayInfo($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }


    //积分流水记录
    public static function getPointLogList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //积分流水记录详情
    public static function getPointLog($user_id, $id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $id);
    }

    //主表积分流水记录
    public static function getPointDetailList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //地址列表
    public static function getAreaByPid($pid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $pid);
    }

    //地址运费列表
    public static function getOfCfgByCid($type, $cid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $type, $cid);
    }

    //地址运费表
    public static function getFreightByType($type)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $type);
    }

    //地址运费表
    public static function getFreightById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }


    //营销资源列表
    public static function marketConfigList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //营销资源ID列表
    public static function marketConfigIdsByTab()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //营销资源详情
    public static function marketConfigINfo($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //营销资源自定义列表
    public static function getMarketDefineList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //营销资源自定义优惠券详情
    public static function getMarketDefineById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }


    //营销资源自定义优惠券详情
    public static function getMarketDefineByMarketId($marketId, $defineType)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $marketId, $defineType);
    }

    //卡券列表记录
    public static function userCardList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    public static function GetPropsCard($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //批量上传发送卡券记录
    public static function batchSendCard($backUserId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $backUserId);
    }

    //批量废除卡券记录
    public static function batchDelCard($backUserId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $backUserId);
    }

    //单个废除卡券记录
    public static function singleDelCard($backUserId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $backUserId);
    }

    //单个上传卡券记录
    public static function singleSendCard($backUserId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $backUserId);
    }

    //获取用户优惠券列表
    public static function getCardsList($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId);
    }


    //渠道下的关联卡券是否领取
    public static function checkCard($user_id, $market_id, $channel, $get_relation)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $market_id, $channel, $get_relation);
    }


    //产品缓存key
    public static function getOneProduct($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //后台产品列表缓存key
    public static function getProductList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    // 活动配置详情
    public static function getActivityOne($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }


    // 活动配置
    public static function activityConfig($grant_type, $ac_id = 0)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $grant_type, $ac_id);
    }

    //营销资源发放列表
    public static function adminMarketSendKey()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //营销资源发放详情
    public static function adminMarketSendInfo($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //营销资源发放库存
    public static function marketSendStockNum($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //产品集合key
    public static function getProductSnSet()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //注册产品列表
    public static function getProductRegList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //注册产品详情
    public static function getProductReg($user_id, $id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $id);
    }

    public static function getPRegBySn($user_id, $sn)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $sn);
    }

    //退款主表详情
    public static function getOneRefundMain($refund_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $refund_no);
    }

    //退款表详情
    public static function getOneRefund($user_id, $refund_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $refund_no);
    }


    //退款定金主表详情
    public static function getOneDepositRefundMain($refund_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $refund_no);
    }

    //退款表详情
    public static function getOneDepositRefund($user_id, $refund_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $refund_no);
    }

    //导购退款订单
    public static function getSrNoList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //退款表列表
    public static function getListRefund($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取退款金额
    public static function getRePrice($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //退款商品表列表
    public static function getLgByRefundNo($user_id, $refund_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $refund_no);
    }


    //主表注册产品后台表
    public static function getProductDetailList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //将要过期积分的用户集合
    public static function getExpiredUserSet($year, $i)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $year, $i);
    }

    //物流详情
    public static function getInfoExpress($num)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $num);
    }

    //获取express_code
    public static function getRealExpressCode($expressCode): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $expressCode);
    }

    //用户扩展信息
    public static function getExtendByUseridKey($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //导购信息
    public static function getGuideByUidKey($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //导购信息(根据工号)
    public static function getGuideByJobNoKey($job_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $job_no);
    }

    //获取绑手机时间根据uid
    public static function getCtimeByUidKey($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //后台用户列表缓存
    public static function adminUserKey()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //后台导购列表缓存
    public static function adminGuideKey()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //导购订单列表
    public static function getOsourceList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //导购订单总金额
    public static function getOsourceSum($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //通过订单号获取导购id
    public static function getOuidByOrderKey($uid, $order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $uid, $order_no);
    }

    //导购详情
    public static function getOneOsourceInfo($ouid, $order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ouid, $order_no);
    }

    //企业微信同行凭证
    public static function WeworkAccessToKen($corpId, $agent_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $corpId, $agent_id);
    }

    //企业微信同行凭证
    public static function erpAddOrder()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //uv统计
    public static function userUVStat($date)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $date);
    }

    //每日统计pv
    public static function userStat($date)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $date);
    }

    //根据用户id获得导购
    public static function getGuideByUser($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    // 获取套餐sn下的商品sku
    public static function getTcSnGoodsKey($sn)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sn);
    }

    public static function WXOAUinfoKey($openid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $openid);
    }

    public static function OaFocusByOpenIdKey($oa_openid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $oa_openid);
    }

    public static function OaFocusByIdKey($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    public static function OaFocusByUnionidKey($Unionid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $Unionid);
    }

    //公众号推送队列
    public static function OaPushList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //公众号推送记录hash
    public static function OaPushLog($date)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $date);
    }

    //获取产品对应的优惠券列表缓存
    public static function getMIdsByPid($pid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $pid);
    }

    //获取产品绑定优惠券详情
    public static function getPMById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //产品优惠券最大领取次数缓存
    public static function getPMConfig()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //crm api token
    public static function crmAccessToken()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //crm 队列处理
    public static function crmList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //crm 重试队列
    public static function crmRetry()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //event 队列处理
    public static function eventList($mod): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //score 队列处理
    public static function reissueIntegralPushList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //ruiyun 队列处理
    public static function ruiYunList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //crm 重试队列
    public static function ruiYunRetry()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //adv 广告队列处理
    public static function advList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //adv 广告重试队列
    public static function advRetry()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //dreamehome 队列处理
    public static function appList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $mod);
    }

    //dreamehome 重试队列
    public static function appRetry()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    //wx-ulink
    public static function getWxUlinkById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //wx-ulink
    public static function getWxUlinkByMd5($md5_val)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $md5_val);
    }

    //根据crm卡号获取用户id
    public static function getUidByCard($card)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $card);
    }

    //未同步crm订单的积分
    public static function getPointByOrder($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取配置缓存
    public static function getContentConfig()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //用户积分
    public static function scoreAccount($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //内容-话题
    public static function getWtopicList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //内容-话题
    public static function getWtopicById($id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //快递最后一次状态
    public static function expressStatus($nu)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $nu);
    }

    //获取活动对应的优惠券缓存
    public static function getAmIdsByAid($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    //内容-
    public static function getDtagListByDid($did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $did);
    }

    public static function getAmById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    public static function getInfoAcType2ByAcId($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    public static function getInfoAcType3ByAcId($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    public static function getAmListCount($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    public static function getIdByAidAndMid($ac_id, $market_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id, $market_id);
    }

    public static function getCouponNumByAidAndMid($ac_id, $market_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id, $market_id);
    }

    public static function getCouponListByAcId($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    //推荐人订单列表
    public static function getOsourceRList($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //订单渠道来源列表
    public static function getOsourceMList($union): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $union);
    }

    //获取gmv列表
    public static function getGmvByYearList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //推荐人订单详情
    public static function getOsRInfoByOrderNo($r_id, $order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $r_id, $order_no);
    }

    //订单渠道来源详情
    public static function getOsMInfoByOrderNo($user_id, $order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $order_no);
    }

    //获取绑定推荐人列表
    public static function getUserRList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //获取绑定推荐人详情
    public static function getUserRInfoByUid($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //获取邀请注册列表
    public static function getInviteRegList($r_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $r_id);
    }

    //手机号验证码详情
    public static function smsCode($phone, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone, $sid);
    }

    //短信日次数限制
    public static function smsSendByDay($phone, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone, $sid);
    }

    //短信每小时次数限值
    public static function smsSendByHours($phone, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone, $sid);
    }

    //短信每次频率限制
    public static function smsSend($phone, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone, $sid);
    }

    //内容-
    public static function getWdynamicByDid($did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $did);
    }

    //内容-
    public static function getWtagListByDid($did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $did);
    }

    //内容-小程序
    public static function getWdynamicsList($is_temp, $type, $t2)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $is_temp, $type, $t2);
    }

    //内容-是否点赞
    public static function hasPraised($user_id, $did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $did);
    }

    //内容-点赞数
    public static function getPraise($did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $did);
    }

    //内容-上下架状态
    public static function getWdynamicStatus($did)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $did);
    }

    //勘探服务地区搜索次数列表
    public static function getExploreList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //勘探服务地区搜索唯一数据
    public static function getExploreInfoById($cid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $cid);
    }


    //上下水服务订单列表
    public static function getPlumbingList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水服务订单唯一数据
    public static function getPlumbingInfoByOrNo($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //上下水服务sn编码设置列表
    public static function getPlumbingSnList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水服务sn编码设置唯一数据
    public static function getPlumbingSnInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //上下水服务sn编码集合
    public static function getPlumbingSnConfig($type): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $type);
    }

    //上下水服务价格设置
    public static function getPlumbingPrice(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水服务退款订单列表
    public static function getPlumbingRefundList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水服务退款订单唯一数据
    public static function getPlumbingReInfoByReNo($refund_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $refund_no);
    }

    //上下水评价标签列表
    public static function getCommentTagList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水评价标签唯一数据
    public static function getCommentTagInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //上下水评价列表
    public static function getPlumbingCommentList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //上下水评价唯一数据
    public static function getPlumbingCoInfoByOrNo($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    //APP登陆弹窗标识
    public static function getAppRegisterTag($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //H5登陆弹窗标识
    public static function getH5RegisterTag($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //推广参数链接
    public static function getSpreadCode($md5Scene): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $md5Scene);
    }

    //用户浏览商品15s
    public static function userViewGoods($user_id, $gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $gid);
    }

    //用户分享商品
    public static function userShareGoods($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //用户观看直播推送
    public static function watchLive($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //直播列表
    public static function getTxLiveList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //防止缓存穿透
    public static function stopCache(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //定金订单列表
    public static function getDepositList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //定金订单唯一数据
    public static function getDepositInfoByOrNo($user_id,$order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $order_no);
    }

    //预售的订单数量
    public static function getPaidNumByGid($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    //购物车订单商品关系信息
    public static function getOCartHash($cart_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$cart_id);
    }

    //购物车定金订单信息
    public static function getOcartInfo($user_id,$cart_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$cart_id);
    }


    //会员权益信息缓存
    public static function memberCenterInfo(string $function, string $user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $function, $user_id);
    }

    //获取活动列表
    public static function getActivityList()
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);

    }

    //获取活动详情
    public static function getActivityDetailAndCoupon($acId)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }

    //获取用户领取优惠卷及状态
    public static function getUserCoupon($acId)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }


    //根据方法和查询参数获取对应的会员数据
    public static function GetMemberCenterInfo($function, $arr)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $function, json_encode($arr));
    }

    /**
     * @return string
     * 获取类目信息
     */
    public static function getCateList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 获取所有主机信息
     */
    public static function getHostInfo(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 获取配件信息
     */
    public static function getPartInfo(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 获取配件gid
     */
    public static function getPartGid(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $id
     * @return string
     * 通过id获取指定数据
     */
    public static function getOneInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * @param $id
     * @return string
     * 产品注册主机对应配件信息
     */
    public static function GetOnePartSalesByMainId($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * @param $pid
     * @return string
     * 通过pid获取类目详情
     */
    public static function getOneInfoByPid($pid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $pid);
    }

    /**
     * @return string
     * 智能配件列表
     */
    public static function getPartSalesList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 智能主机列表
     */
    public static function getMainSalesList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $id
     * @return string
     * 智能配件详情
     */
    public static function getMainInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * @param $sku
     * @return string
     * 通过sku获取分类信息
     */
    public static function getCateInfoBySku($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    /**
     * @param $sku
     * @return string
     * 获取关联信息
     */
    public static function getInfoByMainSku($sku): string

    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    /**
     * @param $c_id
     * @return string
     * 通过c_id获取分类数据
     */
    public static function getCateInfoByCid($c_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $c_id);
    }

    /**
     * @param $level
     * @param $pid
     * @return string
     * 相同等级类目的分类信息
     */
    public static function getSameData($level, $pid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $level, $pid);
    }

    /**
     * @return string
     * 获取关联主机列表
     */
    public static function getMainList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $main_sku
     * @return string
     * 获取主机信息main_parts种
     */
    public static function getOneInfoByMainSku($main_sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $main_sku);
    }

    /**
     * @param $id
     * @return string
     * 通过id获取part信息 part_sales
     */
    public static function getPartInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }


    /**
     * @param $sku
     * @return string
     * 通过part_sku获取main_sku
     */
    public static function getMainByPartSku($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    /**
     * @param $sku
     * @return string
     * 通过主机sku获取配件id
     */
    public static function getPartIdByMainSku($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$sku);
    }

    /**
     * @return string
     * 获取cid和机型名称
     */
    public static function getCidsBySkus(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //预售商品库存
    public static function gPresaleStock($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //预售商品销量
    public static function gPresaleSales($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //预售商品未付款数
    public static function gPresaleWait($gid, $sid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    //预售商品库存sum
    public static function gPresaleListStock($gid)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }


    //我的定金订单列表
    public static function getMyDepositList($user_id)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    //user_msg 队列处理
    public static function userMsgList($mod)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$mod);
    }


    //定金订单扩展表详情
    public static function getOneDepositEInfo($user_id, $order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id, $order_no);
    }

    //订单列表
    public static function getDepositEList($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }


    //统计商品-订单相关信息
    public static function statisticGoods($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$gid);
    }


    //商品参数-商品参数排序
    public static function getGoodsParamSort($cate_id, $group_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $cate_id, $group_id);
    }

    //商品参数-单个商品的参数
    public static function getOneGoodsParamInfo($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //商品参数-单个商品的参数
    public static function getParamsListBySkus($skus, $isGroup): string
    {
        // 排序
        sort($skus);
        $skus = serialize($skus);
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $isGroup, $skus);
    }

    //商品参数-获取分类列表
    public static function getCateListByPids($pids): string
    {
        // 排序
        sort($pids);
        $pids = serialize($pids);
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $pids);
    }

    //根据 sku 获取参数
    public static function getParamsBySku($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }

    //获取参数，商品参数库表t_gparam的全量数据
    public static function getParams(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //获取商品参数的二级类目分组数据
    public static function getCateGroupByCateId($cId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $cId);
    }

    //获取商品参数的二级类目分组数据
    public static function getCateGroupById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    //获取商品参数的二级类目分组详情列表
    public static function getCateGroupDetailList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //获取商品参数的二级类目分组列表
    public static function getCateGroupList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //获取商品的商品参数列表
    public static function getGoodsParamList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    //获取商品列表
    public static function getGoodsParamGoodsList($cateId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $cateId);
    }

    /**
     * @param $user_id
     * @param $union
     * @param bool $limit
     * @return string
     * 获取用户广告记录单条数据
     */
    public static function getUserAdvInfoKey($user_id, $union, bool $limit = false): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id, $union, $limit);
    }


    /**
     * @param $user_id
     * @return string
     * 获取用户广告最后一条数据
     */
    public static function getLastAdvInfoKey($user_id):string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    /**
     * 获取短信模板列表
     * @return string
     */
    public static function getSmsTmplList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 获取已发送短信的用户列表
     * @param $code
     * @return string
     */
    public static function getSendSmsUserList($code): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $code);
    }

    /**
     * 获取已发送短信的记录列表
     * @return string
     */
    public static function getSmsSendRecordList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $id
     * @return string
     * 获取内容角色信息
     */
    public static function getWikiRoleById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$id);
    }


    /**
     * @param $card_no
     * @return string
     * 获取保修卡信息
     */
    public static function getWarrantyCardByNo($card_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$card_no);
    }



    /**
     * @param $user_id
     * @param $apply_no
     * @return string
     * 获取保修卡申请信息
     */
    public static function getWarrantyApplyByUserAndApply($user_id,$apply_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$apply_no);
    }


    /**
     * @param $user_id
     * @param $sn
     * @return string
     * 获取保修卡申请信息
     */
    public static function getWarrantyApplyByUserAndSn($user_id,$sn): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$sn);
    }

    public static function getWarrantyApplyListByUser($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    /**
     * @param $apply_no
     * @return string
     * 获取保修卡详情信息
     */
    public static function getWarrantyApplyDetailByNo($apply_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$apply_no);
    }

    /**
     * @return string
     * 获取内容管理角色列表
     */
    public static function getRoleList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }
    /**
     * @param $cateId
     * @return string
     * 获取推荐数据
     */
    public static function getRecommendByCateId($cateId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$cateId);
    }

    /**
     * @return string
     * 获取推荐列表
     */
    public static function getRecommendList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $phone
     * @return string
     * 获取用户领取优惠券日志
     */
    public static function getAcDrawLogDetail($phone): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $phone);
    }


    /**
     * @return string
     * 根据话题id获取内容id
     */
    public static function getDidList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 异步导出任务的列表
     * @return string
     */
    public static function getAsyncExportLogList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    /**
     * 获取是否弹出过生日卡片
     * @param $user_id
     * @return string
     */
    public static function hasShowBirthCard($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $user_id);
    }

    /**
     * @param $unionId
     * @return string
     * 获取企业微信用户详情
     */
    public static function getOaInfoByUnionIdKey($unionId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $unionId);
    }


    /**
     * @param $order_no
     * @return string
     * 获取H5订单支付状态
     */
    public static function getH5PayStatus($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

        /**
     * @param $order_no
     * @return string
     * 获取小程序订单支付状态
     */
    public static function getWxPayStatus($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }


    /**
     * 获取Alipay订单支付状态
     * @param $order_no
     * @return string
     */
    public static function getAliPayStatus($order_no): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }


    /**
     * bi 系统 access token
     * @return string
     */
    public static function biAccessToken(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 电子保修卡记录列表
     */
    public static function getWarrantyListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 待审核记录列表
     */
    public static function getWarrantyApplyDetailList(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    /**
     * @param $user_id
     * @param $sn
     * @return string
     * 产品激活标识
     */
    public static function productRegCard($user_id,$sn):string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id,$sn);
    }

    /**
     * @return string
     * 用户来源列表
     */
    public static function getSourceListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 通过source_code获取param参数
     */
    public static function getParamListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 获取用户来源数据
     */
    public static function getLatestSourceListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $user_id
     * @return string
     * 获取用户设备请求token
     */
    public static function getGenerateJwtTokenKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$user_id);
    }

    /**
     * @param string $unionid
     * @param string $user_type
     * @return string
     *
     */
    public static function getRUserIdByUnionId(string $unionid, string $user_type)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $unionid, $user_type);
    }

    /**
     * @param $acId
     * @return string
     * 通过活动id获取活动详情
     */
    public static function getDrawActivityDetailByAcId($acId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }

    /**
     * @param $acId
     * @param $userId
     * @return string
     * 获取活动下抽奖次数
     */
    public static function getUserDrawTimesKey($acId, $userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId, $userId);
    }


    /**
     * @return string
     * 奖品详情
     */
    public static function getPrizeByIdKey($prizeId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$prizeId);
    }

    /**
     * @param $taskCode
     * @return string
     * 通过task_code获取任务详情
     */
    public static function getTaskInfoByTaskCodeKey($taskCode): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$taskCode);
    }

    /**
     * @param $taskId
     * @return string
     * 根据任务ID获取绑定了此任务的活动ID
     */
    public static function getActivityIdByTaskId($taskId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$taskId);
    }

    /**
     * @param $acId
     * @param $userId
     * @return string
     * 返回已完成任务条数
     */
    public static function getTaskCompleteCountKey($acId, $userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$acId, $userId);
    }

    /**
     * @param $taskId
     * @return string
     * 通过任务id获取任务详情
     */
    public static function getTaskInfoByTaskIdKey($taskId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$taskId);
    }
    
    /**
     * 获取任务列表缓存key
     * @return string
     */
    public static function getTaskListCacheKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 通过活动ID获取奖品列表
     * @param $acId
     * @return string
     */
    public static function getPrizeListCacheKey($acId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }

    /**
     * 通过活动ID、用户手机号获取中奖记录
     * @param $acId
     * @param $phone
     * @return string
     */
    public static function getPrizeRecordCountCacheKey($acId, $phone): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId, $phone);
    }

    /**
     * @param $acId
     * @return string
     * 通过活动ID 获取 任务列表信息
     */
    public static function getTaskIdsByAcIdKey($acId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }


    /**
     * @param string $sn
     * @return string
     * 获取商品活动抵扣金额
     */
    public static function getAcDepriceListBySn(string $sn)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sn);
    }

    /**
     * @param string $sn
     * @param string $sku
     * @return string
     * 获取商品活动抵扣金额
     */
    public static function getAcDepriceInfo(string $sn, string $sku)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sn, $sku);
    }


    //以旧换新订单信息
    public static function getOneOtnInfo($order_no)
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $order_no);
    }

    /**
     * @param $acId
     * @param $userId
     * @return string
     * 抽奖活动邀请好友购买Key
     */
    public static function recommendTask($acId,$userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId,$userId);
    }

    /**
     * @param $userId
     * @param $status
     * @return string
     * 用户礼品卡列表Key
     */
    public static function userGiftCardListRedisKey($userId, $status): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId,$status);
    }

    /**
     * @param $id
     * @return string
     * 卡资源详情 前台KEY
     */
    public static function getCardInfoKey($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * @param $id
     * @return string
     * 卡详情 用户-卡表（前台）
     */
    public static function userGiftCardInfoRedisKey($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * 通过分享人ID获取分享记录
     * @param $sharerId
     * @return string
     */
    public static function getUserCardsByShareIdCacheKey($sharerId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sharerId);
    }

    /**
     * @param $userId
     * @param $id
     * @return string
     * 用户分享链接key
     * 用户ID
     * 卡ID
     */
    public static function userShareCardUniqueKey($userId,$id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId,$id);
    }

    /**
     * @param $userId
     * @return string
     * 年终报告
     */
    public static function getYearReportByUserId($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$userId);
    }

    /**
     * @return string
     * wares活动列表
     */
    public static function getWActivityListCacheKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $gid
     * @return string
     * 获取商品信息（先试后买）
     */
    public static function getGoodsTryByIdKey($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    /**
     * @param $gid
     * @param $sid
     * @return string
     * 获取商品价格（先试后买）
     */
    public static function getGoodsTryPriceByIdKey($gid, $sid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid, $sid);
    }

    /**
     * @param $id
     * @return string
     * wares活动详情 （主表）
     */
    public static function getWActivityInfoById($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }

    /**
     * @param $acId
     * @return string
     * 获取活动详情信息（先试后买）
     */
    public static function getWActivityTypeInfoByAcId($acId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }

    /**
     * @return string
     * 先试后买手机号通知限制
     */
    public static function sentNotificationsSet(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @return string
     * 支付宝口令
     */
    public static function getZfwPasswordKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 获取交易单号
     * @param $orderNo
     * @param $payType
     * @return string
     */
    public static function getTradeOrder($orderNo, $payType): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $orderNo, $payType);
    }

    /**
     * 以旧换新有效活动key
     * @param $key
     * @return string
     */
    public static function getTradeInValidActivityKey($key): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$key);
    }


    /**
     * 以旧换新回收类目key
     * @return string
     */
    public static function getTradeInCateListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 获取举报原因列表
     * @return string
     */
    public static function getReportReasonListCacheKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }


    /**
     * @param string $code
     * @return string
     * PC商城获取 Access Token
     */
    public static function weiXinPcAccessToken(string $code): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $code);
    }

    /**
     * @param $state
     * @return string
     * PC商城扫码登陆校验参数
     */
    public static function getQrConnectState($state): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $state);
    }

    /**
     * @param $type
     * @param $uid
     * @return string
     * 获取员工分享码
     */
    public static function getEmployeeShareCard($type,$uid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $type,$uid);
    }
    /**
     * @param $ac_id
     * @return string
     * 新人大礼包活动详情
     */
    public static function getInfoAcType1ByAcId($ac_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id);
    }

    /**
     * 自动发放卡券
     * @param $sku
     * @return string
     */
    public static function getAutoCouponSkuKey($sku): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $sku);
    }


    /**
     * @return string
     * 商品品类Key
     */
    public static function getCategoryListKey(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * @param $categoryId
     * @return string
     * 获取产品站详情Key
     */
    public static function getProductMatrixDetailKey($categoryId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $categoryId);
    }

    /**
     * @param $id
     * @return string
     * 获取产品介绍页详情Key
     */
    public static function getIntroDetailKey($id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $id);
    }
    /**
     * @param $gid
     * @return string
     * 获取活动气氛
     */
    public static function getGoodsAtmosphere($gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gid);
    }

    public static function sendSmsByRefundOrder($type,$user_id): string{
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$type, $user_id);
    }


    /**
     * @param $userId
     * @return string
     * 用户JWT Token黑名单
     */
    public static function jwtTokenBlackList($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId);
    }

    /**
     * @param $userId
     * @return string
     * 记录用户退出登录态
     */
    public static function userLogoutStatus($userId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId);
    }

    /**
     * 数据字典
     * @param string $code 字典标识
     * @return string
     */
    public static function systemDictData(string $code = ''): string
    {
        if (empty($code)) {
            return CUtil::getAllParams(self::$prefix, __FUNCTION__);
        }
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $code);
    }
    
    public static function getPointConfig(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }
    
    public static function getMemberActivityDetail($acId): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $acId);
    }

    /**
     * 国补当前活动
     */
    public static function currentSubsidyActivity(): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__);
    }

    /**
     * 国补活动商品
     */
    public static function getSubsidyActivityGoods($activityId,$gid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__,$activityId,$gid);
    }

    /**
     * 商品推荐次数缓存
     * @param string $gidSid
     * @return string
     */
    public static function getGoodsRecommendKey(string $gidSid): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $gidSid);
    }

    /**
     * 用户推荐列表缓存
     * @param int $userId
     * @param int|string $page
     * @param int|string $pageSize
     * @return string
     */
    public static function getUserRecommendListKey(int $userId, $page, $pageSize): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $userId, $page, $pageSize);
    }

    /**
     * 获取所有推荐的商品列表
     * @param $page
     * @param $pageSize
     * @return string
     */
    public static function getRecommendAllListKey( $page, $pageSize): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $page, $pageSize);
    }
    
    /**
     * 获取邀请好友点赞缓存Key
     * @param int $ac_id 活动ID
     * @param int $inviter_id 邀请人ID
     * @return string
     */
    public static function getInviteLikeCacheKey(int $ac_id, int $inviter_id): string
    {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $ac_id, $inviter_id);
    }
}

