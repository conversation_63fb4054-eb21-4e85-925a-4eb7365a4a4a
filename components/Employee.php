<?php

namespace app\components;

use app\models\CUtil;

/**
 * 员工服务
 */
class Employee
{
    private static $_instance;

    // 员工状态
    const EMPLOYEE_STATUS = [
        'NORMAL' => 1, // 正常
        'STOP'   => 2  // 停用
    ];

    // host
    private $host;

    // 签名
    private $sign_key;

    public function __construct()
    {
        // 获取host
        $this->host = CUtil::getConfig('host', 'config', \Yii::$app->id)['mp_user_host'] ?? '';

        $config         = CUtil::getConfig('mp-user-center', 'common', MAIN_MODULE);
        $this->sign_key = $config['sign_key'];
    }

    public static function factory(): Employee
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    // 员工列表
    public function employeeList(array $params): array
    {
        $data = [
            'uid'         => $params['uid'] ?? [],
            'phone'       => $params['phone'] ?? '',
            'real_name'   => $params['real_name'] ?? '',
            'employee_no' => $params['employee_no'] ?? '',
            'page'        => intval($params['page'] ?? 1),
            'page_size'   => intval($params['page_size'] ?? PHP_INT_MAX),
        ];

        return $this->request('/v1/user/employee-list', $data);
    }

    // 员工详情
    public function employeeInfo(array $params): array
    {
        $data = [
            'uid'         => $params['uid'] ?? '',
            'phone'       => $params['phone'] ?? '',
            'real_name'   => $params['real_name'] ?? '',
            'employee_no' => $params['employee_no'] ?? '',
        ];
        return $this->request('/v1/user/employee-info', $data, true, false); // 查询用户信息时，多说用户非员工，即查询不到
    }

    // 操作增、减分数
    public function optScore(array $params): array
    {
        $data = [
            'uid'         => $params['uid'],
            'score'       => intval($params['score']),
            'handle_type' => intval($params['handle_type']),       // 操作类型：1、增加，2、减少
            'score_type'  => intval($params['score_type']),        // 积分类型：1、积分，2、觅享分，3、微笑分
            'handle_name' => $params['handle_name'] ?? '',         // 操作人
            'source'      => $params['source'] ?: '',              // 来源
            'remark'      => $params['remark'] ?? '',
        ];
        return $this->request('/v1/score/change', $data);
    }

    // 积分记录
    public function scoreRecord(array $params): array
    {
        $data = [
            'uid'         => $params['uid'],
            'score_type'  => intval($params['score_type']),
            'page'        => intval($params['page'] ?? 1),
            'page_size'   => intval($params['page_size'] ?? PHP_INT_MAX),
            'order_field' => $params['order_field'] ?? 'id', // 默认按照id倒序
            'order_type'  => $params['order_type'] ?? 'desc',
        ];
        return $this->request('/v1/score/record', $data);
    }
    public function clearScore(array $params): array
    {
        $data = [
            'uid'         => $params['uid']
        ];
        return $this->request('/v1/score/clear-score', $data);
    }

    // 请求用户平台
    private function request($path, $params, $is_sign = true, $is_log = true)
    {
        try {
            // 请求地址
            $url = $this->host . '/dreame-user-center/internal-api' . $path;

            // 请求头，加上时间戳和签名
            if ($is_sign) {
                $timestamp = intval(microtime(true) * 1000);
                $sign      = MpSign::sign($params, $timestamp, $this->sign_key);
                $header    = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                    "dreame-api-timestamp: {$timestamp}",
                    "dreame-api-sign: {$sign}",
                ];
            } else {
                $header = [
                    "Content-Type: application/json",
                    "Tenant-Id: 000000",
                ];
            }

            // 发送请求，设置超时时间为10秒
            $response = CUtil::curl_post($url, json_encode($params, 320), $header, 10, true);

            // 请求结果
            $result = json_decode($response, true);

            if (!isset($result['code'])) {
                // 可以根据具体情况处理无错误码的情况
                CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '响应结果' . $response, 'err.employee.request（1）');
                return [false, []];
            }

            if ($is_log && $result['code'] != 0) {
                CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '响应结果' . $response, 'err.employee.request（2）');
                return [false, $result['msg']];
            }

            return [true, $result['data']];
        } catch (\Exception $e) {
            // 记录异常日志
            $msg = [
                'message' => $e->getMessage(),
                'file'    => $e->getFile(),
                'line'    => $e->getLine(),
            ];
            CUtil::debug('请求用户平台异常：请求地址' . $url . '请求参数' . json_encode($params, 320) . '异常信息' . json_encode($msg, 320), 'err.employee.request（3）');

            // 返回缓存数据或默认数据
            return [false, '请求用户平台异常'];
        }
    }

}