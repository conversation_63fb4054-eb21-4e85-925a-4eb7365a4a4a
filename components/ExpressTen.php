<?php
/**
 * 快递100接口
 * https://api.kuaidi100.com/document/5f0ffa8f2977d50a94e1023c.html
 */

namespace app\components;

use app\models\by;
use app\models\CUtil;

class ExpressTen
{
    protected static $_instance = [];

    CONST URL       = 'http://poll.kuaidi100.com';
    CONST KEY       = 'LFAZTNQC3380';                       //客户key
    CONST CUSTOMER  = 'B30B38E6B68EED6F77F718CAD3076B44';   //企业授权码

    CONST SALT      = '6296d89f117d6'; //推送随机数

    // 订阅推送地址
    private $push_url;

    protected function __clone(){}

    protected function __construct()
    {
        $this->push_url = (CUtil::getConfig('host', 'config', \Yii::$app->id)['public_host']) . '/comm/express-notify';
    }

    /**
     * @return ExpressTen
     * 单例模式
     */
    public static function factory()
    {

        if (!isset(self::$_instance['ExpressTen']) || !is_object ( self::$_instance['ExpressTen'])) {
            self::$_instance['ExpressTen']  = new self();
        }

        return self::$_instance['ExpressTen'];
    }

    /**
     * @param $num
     * @return string
     * 物流详情缓存
     */
    private function __getInfoKey($num) {
        return AppCRedisKeys::getInfoExpress($num);
    }

    /**
     * @param $expressCode
     * @return string
     *
     */
    private function __getRealExpressCode($expressCode){
        return AppCRedisKeys::getRealExpressCode($expressCode);
    }


    public function getKD100ExpressCode($expressCode)
    {
        $expressArr =CUtil::getConfig('kd100_to_oms','express',MAIN_MODULE);
        if(empty($expressArr)){
            return $expressCode;
        }
        $redis      = by::redis('core');
        $r_key      = $this->__getRealExpressCode($expressCode);
        $aData      = $redis->get($r_key);

        if ($aData === false) {
            foreach ($expressArr as $key=>$express){
                if(in_array(strtolower($expressCode),$express)){
                    $aData = $key;
                    break;
                }
            }
            $redis->set($r_key, $aData, 1800);
        }

        if(empty($aData)){
            $aData = $expressCode;
        }

        return $aData;
    }

    /**
     * @param $num
     * @return string
     * 单号归属公司智能获取快递公司编码
     */
    public function GetComByNum($num)
    {
        $url    = 'http://www.kuaidi100.com/autonumber/auto';
        $url   .= "?num=".$num."&key=".self::KEY;
        $result = CUtil::curl_get($url);
        $data   = str_replace("\"", '"', $result);
        $data   = json_decode($data, true);

        return $data[0]['comCode'] ?? '';
    }

    /**
     * @param string $num
     * @param string $com
     * @param string $phone
     * @param string $from
     * @param string $to
     * @param int $resultv2
     * @return array
     * 快递查询
     */
    public function GetInfo($num= '', $com = '', $phone = '', $from = '', $to = '', $resultv2 = 1)
    {
        $redis      = by::redis('core');
        $r_key      = $this->__getInfoKey($num);
        $aJson      = $redis->get($r_key);
        $aData      = (array)json_decode($aJson, true);

        if ($aJson === false) {
            $method = '/poll/query.do';

            if(empty($num)) {
                return [false, '参数错误'];
            }
            /*if (empty($com)) {
                //归属公司智能判断
                $com = $this->GetComByNum($num);
            }*/

            $arr = [
                'com'       => $com,                    //快递公司编码
                'num'       => $num,                    //快递单号
                'phone'     => $phone,                  //手机号
                'from'      => $from,                   //出发地城市
                'to'        => $to,                     //目的地城市
                'resultv2'  => $resultv2                //开启行政区域解析
            ];

            $data = [
                'customer'  => self::CUSTOMER,
                'param'     => json_encode($arr)
            ];

            //按param + key + customer 的顺序进行MD5加密（注意加密后字符串一定要转32位大写）
            $sign           = md5($data["param"] . self::KEY . $data["customer"]);
            $data["sign"]   = strtoupper($sign);

            $res            = $this->_request($method, $data);
            $res            = str_replace("\"", '"', $res);
            // CUtil::debug("data：".json_encode($data)." | ret:" . $res, "express.query");
            CUtil::setLogMsg(
                "express.query",
                $data,
                $res,
                [],
                self::URL,
                '',
                [],
                200
            );
            $aData          = (array)json_decode($res, true);

            $redis->set($r_key, json_encode($aData), 600);
        }

        return [true, $aData];
    }

    /**
     * @param string $num
     * @param string $com
     * @param string $order_no
     * @return array
     * 订阅推送
     */
    public function poll($num= '', $com = '', $order_no = '', $phone='')
    {
        $method = '/poll';

        $arr    = [
            'company'   => $com,                  //快递公司编码
            'number'    => $num,                  //快递单号
            'key'       => self::KEY,             //客户授权key
            'parameters'=> [
                'callbackurl'   => $this->push_url.'?order_no='.$order_no,//回调地址
                'phone'         => $phone,//电话号码
                'salt'          => self::SALT,    //加密串
                'resultv2'      => '1',           //行政区域解析
            ]
        ];

        $data = [
            'schema'    => 'json',
            'param'     => json_encode($arr),
        ];

        $res    = $this->_request($method, $data);
        $res    = str_replace("\"", '"', $res);
        $aData  = (array)json_decode($res, true);

        if ( in_array($aData['returnCode'], [200, 501]) ) {
            CUtil::debug("{$order_no}|{$res}", 'success.express');
            return [true, 'ok'];
        } else {
            CUtil::debug("{$order_no}|{$res}", 'err.express');
            return [false, $aData['message'] ?? 'error'];
        }
    }

    /**
     * @param $method
     * @param array $arr
     * @return bool|string
     * 统一请求
     */
    protected function _request($method, $arr = [])
    {
        $url        = self::URL.$method;
        $target_Arr = [];
        foreach ($arr as $key => $a) {
            if(!is_array($a)) {
                $target_Arr[] = "{$key}=".urlencode($a);
            }
        }

        $params = implode('&',$target_Arr);
        $res    = CUtil::curl_post($url, $params);

        return $res;
    }

}
