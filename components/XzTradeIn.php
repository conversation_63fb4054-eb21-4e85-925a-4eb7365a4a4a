<?php

namespace app\components;

use app\models\CUtil;

/**
 * 小智（熊洞）回收服务
 */
class XzTradeIn
{
    private static $instance;
    private $url;
    private $appkey;
    private $secret;

    private function __construct()
    {
        $this->loadConfig();
    }

    private function loadConfig()
    {
        $config = CUtil::getConfig('xz_trade_in', 'common', MAIN_MODULE);
        $this->url = $config['url'];
        $this->appkey = $config['appkey'];
        $this->secret = $config['secret'];
    }

    public static function factory(): XzTradeIn
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }

        return self::$instance;
    }

    // 查询回收类目
    public function getCateList(): array
    {
        return $this->sendRequest('cates', [], 'GET');
    }

    // 查询型号模型
    public function getModelList(array $params): array
    {
        if (!isset($params['cate_id']) || !$params['cate_id']) {
            return [false, '参数错误'];
        }
        return $this->sendRequest('cate/models', $params);
    }

    // 查询模型报价
    public function getModelPrice(array $params): array
    {
        if (!isset($params['cate_id']) || !$params['cate_id']) {
            return [false, '参数错误'];
        }
        return $this->sendRequest('cate/inquiry', $params);
    }

    // 创建回收订单
    public function createOrder(array $params): array
    {
        return $this->sendRequest('order/create', $params);
    }

    // 查询订单详情
    public function orderInfo(array $params): array
    {
        if (!isset($params['order_code']) || !$params['order_code']) {
            return [false, '参数错误'];
        }
        return $this->sendRequest('order/info', $params);
    }

    // 订单确认
    public function orderConfirm(array $params): array
    {
        if (!isset($params['order_code']) || !$params['is_confirm']) {
            return [false, '参数错误'];
        }
        $params['remark'] = '无';
        return $this->sendRequest('order/confirm', $params);
    }

    // 订单取消
    public function cancelOrder($params): array
    {
        if (!isset($params['order_code']) || !$params['order_code']) {
            return [false, '参数错误'];
        }
        $params['remark'] = '无';
        return $this->sendRequest('order/cancel', $params);
    }

    // 修改预约时间
    public function updateReseTime($order_code,$rese_time): array
    {
        $params = [
            'order_code' => $order_code,
            'rese_time' => $rese_time,
        ];

        return $this->sendRequest('order/rese/update', $params);
    }

    // 验证签名
    public function checkSign(string $sign, array $params, int $dateline): bool
    {
        return $this->sign($params, $dateline) === $sign;
    }

    // 请求接口
    private function sendRequest(string $path, array $params, string $method = 'POST'): array
    {
        $url = $this->getURL($path, $params, time());
        if ($params) {
            $params = json_encode($params, JSON_UNESCAPED_UNICODE);
        } else {
            $params = '';
        }
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $params, [], $method, 10);
        $log = [
            'url'      => $url,
            'params'   => $params,
            'status'   => $status,
            'httpCode' => $httpCode,
            'ret'      => $ret,
            'err'      => $err,
        ];
        $log = json_encode($log);
        return $this->getReturn($status, $httpCode, $ret, $err, $log);
    }

    // 获取请求地址
    private function getURL(string $path, array $params, int $dateline): string
    {
        return $this->url . $path . '?appkey=' . $this->appkey . '&dateline=' . $dateline . '&sign=' . $this->sign($params, $dateline);
    }

    // 获取返回值
    private function getReturn($status, $httpCode, $ret, $err, $log): array
    {
        // HTTP请求失败
        if (!$status || $httpCode != 200) {
            CUtil::debug('小智（熊洞）接口异常：' . $log, 'err.xzTradeIn');
            return [false, $err];
        }
        // 接口请求失败
        if ($ret['code'] != 200) {
            CUtil::debug('小智（熊洞）接口异常：' . $log, 'err.xzTradeIn');
            return [false, '请求失败'];
        }
        // 请求成功
        return [true, $ret['data']];
    }

    // 签名
    private function sign(array $params, int $dateline): string
    {
        if (!empty($params)) { // 不为空
            $params = json_encode($params, JSON_UNESCAPED_UNICODE);
        } else {
            $params = '{}';
        }
        $str = $this->appkey . $dateline . $params . $this->secret;
        return md5($str);
    }
}