<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;



use app\models\by;
use app\models\CUtil;
use yii\db\Exception;

class E3MemberCenter
{
    private static $_instance = [];
    protected $expire_time = YII_ENV_PROD ? 3600 : 60;

    private $dreame_member_center_domain;

    const URL = [
        'SCORE_SAVE'        => '/dreame-event-center/pointAndGrow/pointSave',//推送积分(增减) POST
    ];

    const POINT_GROW_EVENT = [
        'deduct' => 'mall/dreame/offline_deduct_point',//下单扣减积分
        'reward' => 'mall/dreame/offline_reward_point',//奖励积分
        'refund' => 'mall/dreame/offline_refund_point',//退款退还积分
    ];

    private function __construct()
    {
        $this->dreame_member_center_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return E3MemberCenter
     */
    public static function factory(): E3MemberCenter
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }
    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'pointGrowSave':
                return $this->$function($args['event'], $args['data']);
            default:
                return [false, 'function 不存在'];
        }
    }

    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param array $header
     * @param array $arr
     * @param string $type
     * @return array
     * 根据URL转接数据
     */
    private function __request($event, array $body = [], string $method = 'POST', array $header = [], array $arr =[], string $type = 'json'): array
    {
        $requestUrl = self::URL[$event] ?? '';
        if(empty($requestUrl)){
            return [false,'事件不存在！'];
        }
        $url = $this->dreame_member_center_domain . $requestUrl;

        //URl 特殊处理
        if($arr){
            $url .= '?'.http_build_query($arr);
        }

        $header = empty($header) ? [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Expect: "
        ] : $header;

        $type == 'json' && $body =  json_encode($body, 320);
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method,20);
        !YII_ENV_PROD && CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|header:".json_encode($header,320)."|data：" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "e3.{$event}");
        ($httpCode !== 200 || !empty($err)) && CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|data：" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "e3.{$event}");
        if (!$status) {
            return [false, $err];
        }
        return [$ret['code'] === 0, $ret];
    }

    public function pointGrowSave($event, $data)
    {
        if (empty($data) || empty($event)) return [false, '参数错误'];
        $code = self::POINT_GROW_EVENT[$event] ?? '';
        if (empty($code)) {
            CUtil::debug("point_event:{$event}:积分事件不存在~", "err.dreamehome.SCORE_SAVE");
            return [false, '积分事件不存在'];
        }
        $data['code'] = $code;
        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C3wQHbecgTGrE==",
            "tenantId:".CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
            "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
            "Accept: application/json, text/plain"
        ];
        list($s, $resData) = $this->__request('SCORE_SAVE', $data, 'POST', $header);
        
        return [true, $resData];
    }


}
