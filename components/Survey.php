<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use yii\db\Exception;


/**
 * Survey 问卷
 */
class Survey
{
    protected static $_instance = [];

    private $dreame_home_domain;

    const URL = [
        'QUERY_MARK'  => '/tduck-api/user/form/data/mall/query', // 查询用户分数
        'SURVEY_LIST' => '/tduck-api/user/form/mall/page',       // 查询问卷列表
    ];

    // 必要的单例模式
    private function __construct()
    {
        $this->dreame_home_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_survey_host'] ?? '';
    }

    private function __clone()
    {
    }

    private $_obj = [];

    // 获取单例实例
    public static function factory(): Survey
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance[__CLASS__] = new static();
        }
        return self::$_instance[__CLASS__];
    }


    /**
     * @param int $userId
     * @param string $formKey
     * @param int $activityId
     * @return array
     * @throws Exception
     * 查询用户分数
     */
    public function querySurveyResult(int $userId, string $formKey, int $activityId): array
    {
        // 获取用户dreameUid
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);

        $body = [
            "formKey"    => $formKey,
            'dreameUid'  => $mallInfo['uid'] ?? '',
            'activityId' => $activityId
        ];

        list($status, $ret) = $this->_request('QUERY_MARK', $body);

        if (!$status) {
            return [false, []];
        }
        return [$status, $ret['data'] ?? ''];
    }


    /**
     * @return array
     * 问卷列表
     */
    public function surveyList(): array
    {
        list($status, $ret) = $this->_request('SURVEY_LIST', [], 'GET');
        if (!$status) {
            return [false, []];
        }
        return [$status, $ret['data'] ?? ''];
    }

    // 发起请求
    protected function _request(string $event, array $body = [], string $method = 'POST', array $arr = [], int $ttl = 10, int $ifCenter = 0): array
    {
        //获取路由
        $url = $this->dreame_home_domain . self::URL[$event];

        $header = [
            "Content-Type:application/json",
            "cache-control:no-cache",
            "Dreame-internal:4E1C5wQHbecgTGrE==",
            "Expect: "
        ];

        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body), $header, $method);
        // CUtil::debug("event:{$event}|url:{$url}|httpcode:{$httpCode}|err:{$err}|header:" . json_encode($header) . "|data：" . json_encode($body) . " | ret:" . json_encode($ret), "survey.{$event}");
        CUtil::setLogMsg(
            "survey.{$event}",
            $body,
            $ret,
            $header,
            $url,
            $err,
            [],
            $httpCode
        );
        if (!$status) {
            return [false, $err];
        }

        return [$ret['code'] === 200, $ret];
    }
}
