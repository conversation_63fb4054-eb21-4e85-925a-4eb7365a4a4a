<?php
/**
 * Created by IntelliJ IDEA.
 * User: <EMAIL>
 * Date: 2021/1/7
 * Time: 11:27
 */

namespace app\components;

use app\models\CUtil;

final class TikTok
{

    CONST TIKTOK_HOST = 'v.douyin.com';

    protected static $_instance = null;

    private function __construct() {}

    private function __clone() {}

    /**
     * @return TikTok
     */
    public static function factory(): TikTok
    {

        if (!isset(self::$_instance) || !is_object (self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * @param string $url
     * @return array
     * 抓取抖音分享链接数据
     */
    public function GetTikTokInfo($url=""): array
    {
        //校验发来的URL正确性
        $parse = parse_url($url);
        if (empty($parse['host']) || $parse['host'] != self::TIKTOK_HOST) {
            return [false, '输入抖音视频链接地址'];
        }

        $cl = curl_init($url);
        curl_setopt($cl, CURLOPT_ENCODING, 'UTF-8');
        curl_setopt($cl, CURLOPT_TIMEOUT, 10);
        curl_setopt($cl, CURLOPT_RETURNTRANSFER, true); // 获取数据返回
        curl_setopt($cl, CURLOPT_HTTPHEADER, [
            "user-agent" => "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36"
        ]);

        $result = curl_exec($cl);
        if (curl_errno($cl)) { // 发生错误
            $msg = curl_error($cl);
            return [false, $msg];
        }

        curl_close($cl);

        preg_match("/href=\"([^\"]+)/",$result,$arr);

        $link      = $arr[1] ?? "";
        if(empty($link)) {
            return [false,"无效链接(1)"];
        }

        $part = parse_url($link);
        $path = $part['path'] ?? "";
        if(empty($path)) {
            return [false,"无效链接(2)"];
        }

        $path      = trim($path,"/");
        $params    = explode("/",$path);
        $item_ids  = $params[2] ?? "";
        if(empty($item_ids)) {
            return [false,"无效链接(3)"];
        }

        $target    = "https://www.iesdouyin.com/web/api/v2/aweme/iteminfo/?item_ids={$item_ids}";
        $aJson     = CUtil::curl_get($target);
        $ret       = (array)json_decode($aJson,true);

        if (empty($ret)) {
            return [false, '解析失败'];
        }

        return [true, $ret];
    }
}