<?php
/**
 * <AUTHOR>
 * @date 1/4/2025 上午 11:31
 */

namespace app\components;

use app\models\by;

/**
 * 限流器
 */
class RateLimiter
{
    private $redis;
    private $limit;     // 允许的最大请求次数
    private $windowSec; // 时间窗口（秒）
    
    public function __construct()
    {
        $this->redis = by::redis();
    }
    
    /**
     * 封装的限流方法
     * @param string $prefix_key 限流的key前缀
     * @param int $user_id 用户ID
     * @param int $limit 允许的最大请求次数
     * @param int $window 窗口时间（秒）
     * @return bool
     */
    public function checkRateLimit(string $prefix_key, int $user_id, int $limit, int $window): bool
    {
        $this->setLimit($limit, $window);
        $redisKey = AppCRedisKeys::AccFrequency($prefix_key, $user_id);
        
        return $this->allowedByCounter($redisKey, $window, $limit);
    }

    /**
     * 设置频率限制参数
     * @param int $limit 最大访问次数
     * @param int $windowSec 窗口时间（秒）
     */
    public function setLimit(int $limit, int $windowSec)
    {
        $this->limit = $limit;
        $this->windowSec = $windowSec;
    }

    /**
     * 限流-固定窗口
     * 场景：简单低频
     * @param string $key 限流的key
     * @param int $windowSec 窗口时间（秒）
     * @param int $windowLimit 窗口限制数
     * @return bool
     * @example
     * $redisKey = AppCRedisKeys::AccFrequency('test', 9652);
     * $isAllowed = $rateLimiter->allowedByCounter($redisKey, 10, 5);
     * if ($isAllowed) {
     *     echo 'ok';
     * } else {
     *     http_response_code(429);
     *     echo "Too Many Requests";
     *     exit;
     * }
     */
    public function allowedByCounter(string $key, int $windowSec, int $windowLimit): bool
    {
        // $counter = $this->redis->incr($key);
        //
        // if ($counter == 1) {
        //     $this->redis->expire($key, $windowSec);
        // }
        //
        // if ($counter > $windowLimit) {
        //     return false;
        // }
        //
        // return true;

        // 原子化操作
        $lua = <<<LUA
        local windowSec = tonumber(ARGV[1])
        local windowLimit = tonumber(ARGV[2])

        local counter = tonumber(redis.call('INCR', KEYS[1]))

        if counter > windowLimit then
            return false
        end

        if counter == 1 then
            redis.call('EXPIRE', KEYS[1], windowSec)
        end

        return true
LUA;

        return $this->redis->eval($lua, [$key, $windowSec, $windowLimit], 1);
    }
    
    /**
     * 限流-滑动窗口
     * 场景：高精度控制QPS
     * @param string $key 限流的key
     * @param int $windowSec 窗口时间（秒）
     * @param int $windowLimit 窗口限制数
     * @return bool
     * @example
     * $redisKey = AppCRedisKeys::AccFrequency('test', 9652);
     * $isAllowed = $rateLimiter->allowedBySlidingWindow($redisKey, 10, 5);
     * if ($isAllowed) {
     *     echo 'ok';
     * } else {
     *     http_response_code(429);
     *     echo "Too Many Requests";
     *     exit;
     * }
     */
    public function allowedBySlidingWindow(string $key, int $windowSec, int $windowLimit): bool
    {
        $currentTime = microtime(true);
        
        // // 开启 Redis 事务
        // $this->redis->multi();
        //
        // // 移除窗口外的访问记录
        // $this->redis->zRemRangeByScore($key, 0, $currentTime - $this->windowSec);
        // // 获取当前窗口内的请求数量
        // $requestCount = $this->redis->zCard($key);
        // // 允许访问，记录当前请求时间
        // $this->redis->zAdd($key, $currentTime, $currentTime);
        // // 设置键的过期时间
        // $this->redis->expire($key, $this->windowSec);
        // // 执行事务
        // list(, $requestCount) = $this->redis->exec();
        // CUtil::dump($requestCount);
        // if ($requestCount >= $this->limit) {
        //     return false;
        // }
        //
        // return true;

        // 原子化操作
        $lua = <<<LUA
        local currentTime = tonumber(ARGV[1])
        local windowSec = tonumber(ARGV[2])
        local windowLimit = tonumber(ARGV[3])
        
        -- 获取前N秒的窗口开始时间
        local windowStart = currentTime - windowSec
        -- 删除前N秒到现在的窗口时间
        redis.call('ZREMRANGEBYSCORE', KEYS[1], 0, windowStart)
        -- 获取当前数量
        local currentCount = redis.call('ZCARD', KEYS[1])
        
        if currentCount < windowLimit then
            redis.call('ZADD', KEYS[1], currentTime, currentTime)
            redis.call('EXPIRE', KEYS[1], windowSec)
            return 1
        else
            return 0
        end
LUA;
        
        $result = $this->redis->eval($lua, [$key, $currentTime, $windowSec, $windowLimit], 1);

        return (bool) $result;
    }
    
    /**
     * 限流-令牌桶
     * 场景：允许突发流量但需平滑处理，如秒杀
     * @param string $key 限流的key
     * @param int $create 每秒生成令牌数
     * @param int $consume 每次请求消耗令牌数
     * @param int $capacity 令牌桶最大容量
     * @return bool
     * @example
     * $redisKey = AppCRedisKeys::AccFrequency('test', 9652);
     * $isAllowed = $rateLimiter->allowedByTokenBucket($redisKey, 1, 1, 10);
     * if ($isAllowed) {
     *     echo 'ok';
     * } else {
     *     http_response_code(429);
     *     echo "Too Many Requests";
     *     exit;
     * }
     */
    public function allowedByTokenBucket(string $key, int $create = 1, int $consume = 1, int $capacity = 10): bool
    {
        // 定义Redis key
        $tokenKey = sprintf('%s|token_bucket', $key);
        $lastFillTimeKey = sprintf('%s|last_fill_time', $key);

        // 当前时间戳
        $currentTime = microtime(true);

        // // 获取当前令牌数和上次填充时间
        // $currentTokens = $this->redis->get($tokenKey);
        // $lastFillTime = $this->redis->get($lastFillTimeKey);
        //
        // if (empty($lastFillTime)) {
        //     $currentTokens = $capacity;
        //     $lastFillTime = (float) $currentTime;
        // } else {
        //     $currentTokens = (int) $currentTokens;
        //     $lastFillTime = (float) $lastFillTime;
        // }
        //
        // // 计算自上次填充令牌后已经经过的时间
        // $elapsedTime = $currentTime - $lastFillTime;
        // // 计算可产生的令牌数量，同时不能超过令牌桶容量
        // $tokensToAdd = floor($elapsedTime * $create);
        // $newTokens = min($currentTokens + $tokensToAdd, $capacity);
        //
        // $this->redis->multi();
        //
        // if ($newTokens > 0) {
        //     // 如果桶中有令牌，则请求通过，同时扣除令牌
        //     $this->redis->set($tokenKey, $newTokens - $consume);
        //     // 更新最后一次填充时间
        //     $this->redis->set($lastFillTimeKey, $currentTime);
        //     $this->redis->exec();
        //     return true;
        // } else {
        //     // 如果没有足够的令牌，请求被拒绝
        //     $this->redis->set($tokenKey, $newTokens);
        //     $this->redis->exec();
        //     return false;
        // }
        
        $lua = <<<LUA
        local tokenKey = KEYS[1]
        local lastFillTimeKey = KEYS[2]
        
        -- 令牌桶最大容量
        local capacity = tonumber(ARGV[1])
        -- 每秒生成令牌数
        local create = tonumber(ARGV[2])
        -- 每次请求消耗令牌数
        local consume = tonumber(ARGV[3])
        -- 当前时间
        local currentTime = tonumber(ARGV[4])
        
        -- 获取当前令牌数
        local currentTokens = tonumber(redis.call('GET', tokenKey) or capacity)
        -- 获取上一次填充令牌的时间
        local lastFillTime = tonumber(redis.call('GET', lastFillTimeKey) or 0)
        
        -- 首次则为当前时间
        if lastFillTime == 0 then
            lastFillTime = currentTime
        end
        
        -- 计算自上次填充令牌后已经经过的时间
        local elapsedTime = currentTime - lastFillTime
        -- 计算可产生的令牌数量，同时不能超过令牌桶容量
        local tokensToAdd = math.floor(elapsedTime * create)
        local newTokens = math.min(currentTokens + tokensToAdd, capacity)

        if newTokens > 0 then
            -- 如果桶中有令牌，则请求通过，同时扣除令牌
            redis.call('SET', tokenKey, newTokens - consume)
            -- 更新最后一次填充时间
            redis.call('SET', lastFillTimeKey, currentTime)
            return 1
        else
            redis.call('SET', tokenKey, newTokens)
            return 0
        end
LUA;

        // 执行Lua脚本
        $result = $this->redis->eval($lua, [$tokenKey, $lastFillTimeKey, $capacity, $create, $consume, $currentTime], 2);
        
        return (bool) $result;
    }
}