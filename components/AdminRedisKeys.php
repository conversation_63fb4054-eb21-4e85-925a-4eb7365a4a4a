<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/3/19
 * Time: 10:29
 * 类名 + 方法名 + 参数
 */
namespace app\components;

use app\models\CUtil;

class AdminRedisKeys {

    public static $prefix = PRO_ADMIN;//各项目防Redis冲突唯一标识！！！！
    //类名 + 方法名 + 参数

    //登录token
    public static function sessionKey($user_id): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__,$user_id);
    }

    //登录信息 （废弃）
    public static function adminInfo($token): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__,$token);
    }
    public static function menuTreeList(): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__);
    }
    //角色菜单
    public static function roleMenuList(): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__);
    }
    //角色信息
    public static function roleInfo(): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__);
    }
    //角色路由信息
    public static function rolesAccess($id): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__,$id);
    }

    //发验证码至企业微信
    public static function sendCodeToWx($account): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__,$account);
    }

    //代码同步历史记录
    public static function SyncCodeList(): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__);
    }

    //代码同步详情
    public static function SyncCodeInfo($id): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__,$id);
    }

    //功能锁定
    public static function CmdLock(): string
    {
        return CUtil::getAllParams(self::$prefix,__FUNCTION__);
    }

    //管理后台下载token
    public static function exportToken($token) {
        return CUtil::getAllParams(self::$prefix, __FUNCTION__, $token);
    }
}
