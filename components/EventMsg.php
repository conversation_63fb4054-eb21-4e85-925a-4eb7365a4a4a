<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;

use app\jobs\EmployeeInviteJob;
use app\jobs\EmployeeOptScoreJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\DreamAmbassadorService;
use app\modules\goods\models\GtagModel;
use app\modules\goods\models\OmainModel;
use app\modules\goods\services\DrawActivityService;
use app\modules\main\enums\activity\ActivityConfigEnum;
use app\modules\main\models\UserBindModel;
use Faker\Provider\Uuid;
use yii\db\Exception;

class EventMsg
{
    private static $_instance = [];

    const EVENT_APP_ORDER_DETAIL = 'pagesA/orderDetail/orderDetail';
    const EVENT_APP_REFUND_DETAIL = 'pagesA/refundDetail/refundDetail';
    const EVENT_MSG_ID = [
        'shipped' => '1602548295074652161',//您购买的商品已发货
        'dispatch' => '1602548761133129729',//您购买的商品正在派送中
        'signed' => '1602548954821894145',//您购买的商品已签收
        'refund' => '1602549138435940354',//订单退款提醒
        'paid' => '1602549250767790081',//订单支付提醒
        'abnormal' => '1602549345282236418',//物流异常提醒
    ];

    const EVENT_USER_PRE_ORDER_ID = [
        'order_presale_start' => '1631476332601737217',//尾款支付提醒-尾款开启
        'order_presale_end'   => '1631478719454314498',//尾款支付提醒-尾款结束前
    ];


    const EVENT_CODE = [
            'ORDER_MSG_SEND'         => 'mall/dreame/order',                      //订单相关
            'VIEW_GOODS'             => 'mall/dreame/view_goods',                 //浏览商品页面15s
            'SHARE_GOODS'            => 'mall/dreame/share_goods',                //分享任意商品
            'ADD_CHART'              => 'mall/dreame/goods_add_cart',             //商品加入购物车
            'BUY_GOODS'              => 'mall/dreame/buy_goods',                  //购买商品
            'INVITE_BUY'             => 'mall/dreame/invite_buy',                 //邀请好友购买
            'INVITE_REG'             => 'mall/dreame/invite_reg',                 //邀请好友注册
            'BE_INVITE_REG'          => 'mall/dreame/be_invited_reg',             //邀请好友注册(被邀请人)
            'PRODUCT_REG'            => 'mall/wx/reg_sn',                         //产品注册
            'SUBSCRIBE'              => 'mall/wx/follow_official_account',        //关注微信公众号
            'ADD_WECHAT'             => 'mall/wx/add_wechat',                     //添加追觅心享官微信
            'WATCH_LIVE'             => 'mall/wx/watch_live',                     //观看直播 观看直播满60s
            'DRAW_POINT'             => 'mall/dreame/raffle',                     //抽奖中积分
            'EVALUATE_MAIN_MACHINE'  => 'mall/dreame/evaluate_main_machine',      //评价主机送积分
            'EVALUATE_PARTS'         => 'mall/dreame/evaluate_parts',             //评价配件送积分
            'MEMBER_REG'             => 'mall/dreame/member_reg',                 //注册新会员送积分
            'EMPLOYEE_BUY_GOODS'     => 'mall/dreame/employees_buy_goods',        //员工购买商品
            'EMPLOYEE_INVITE_EVENT'  => 'mall/dreame/employees_invite_event',     //分享裂变
            'DAY_ACTIVITY'           => 'mall/dreame/day_activity',               //浏览增加积分
            'INVITE_BUY_AMBASSADOR'  => 'mall/dreame/invite_buy_ambassador',      //追觅大使积分
            'APP_INVITE_REG'         => 'mall/dreame/app_invite_reg',             //追觅大使积分
            'ONE_YUAN_PURCHASE'      => 'mall/dreame/view_goods_one_yuan',        //逛1元购15秒
            'RICH_PLAN'              => 'mall/dreame/view_goods_rich_plan',       //逛暴富计划15秒
            'FIVE_DISCOUNT_PURCHASE' => 'mall/dreame/view_goods_five_buy',        //逛五折购15秒
            'HALF_PRICE_BUY'         => 'mall/dreame/view_goods_half_buy',        //逛半价购15秒
            'POINTS_SHOPPING'        => 'mall/dreame/view_goods_points_shopping', //逛积分购物15秒
            'APP_INVITE_REG_GOLD'    => 'mall/dreame/app_invite_reg_gold',        //邀请好友得金币
            'APP_SHARE_GOODS'        => 'mall/dreame/app_share_goods',            //转发商品
            'PARTNER_INVITE_FRIEND'  => 'mall/dreame/partner_invite_friend',      //分享裂变
            'SUPER_PARTNER_FIRST_POINT' => 'mall/dreame/super_partner_first_point',      //分享裂变

            'APP_INVITE_REG_MONEY'   => 'mall/dreame/app_invite_reg_money',       //邀请新好友
            'APP_INVITE_FRIEND_LIKE' => 'mall/dreame/app_invite_friend_like',     //邀请好友点赞
            'INVITE_BUY_GOODS_MONEY' => 'mall/dreame/invite_buy_goods_money',     //邀请好友购买商品
            'BUY_GOODS_MONEY'        => 'mall/dreame/buy_goods_money',            //购买商城商品
            'OPEN_SHOP'              => 'mall/dreame/open_shop',                  //开启追觅小店
            'ADD_GROUP_BUY'          => 'mall/dreame/add_group_buy',              //参与团购活动
            'ADD_THREE_BUY'          => 'mall/dreame/add_three_buy',              //参与3折购
            'ADD_RICH_PLAN'          => 'mall/dreame/add_rich_plan',              //参与暴富计划
            'VIEW_GOODS_MONEY'       => 'mall/dreame/view_goods_money',           //逛商城60秒
            'POST_FRIEND'            => 'friend/user_event_app/post',             //朋友板块发帖
            'SHARE_GOODS_MONEY'      => 'mall/dreame/share_goods_money',         // 分享商城商品
            'SHARE_GOODS_FRIEND_MONEY'       => 'mall/dreame/share_goods_friend_money',       // 分享商品给好友
            'VIEW_GOODS_GROUP'               => 'mall/dreame/view_goods_group',              // 逛拼团活动15秒
            'SHARE_GROUP_GOODS'              => 'mall/dreame/share_group_goods',             // 分享拼团商品
            'SHARE_SHOP_GOODS_FRIEND'        => 'mall/dreame/share_shop_goods_friend',        // 分享小店商品给好友
            'FRIEND_BUY_SHOP_GOODS'          => 'mall/dreame/friend_buy_shop_goods',          // 有好友购买追觅小店商品
            'OPEN_RICH_PLAN'                 => 'mall/dreame/open_rich_plan',                 // 开启暴富计划
            'SHARE_RICH_GOODS_FRIEND'        => 'mall/dreame/share_rich_goods_friend',        // 分享暴富计划商品给好友
            'VIEW_GOODS_RICH_PLAN_MONEY'     => 'mall/dreame/view_goods_rich_plan_money',     // 逛暴富计划15秒
            'ONE_YUAN_FRIEND_SUPPORT'        => 'mall/dreame/one_yuan_friend_support',        // 1元购活动完成好友助力
            'ADD_BLIND_PRIZE_DRAW'           => 'mall/dreame/add_blind_prize_draw',       // 参与盲盒抽奖
            'VIEW_GOODS_THREE_BUY'           => 'mall/dreame/view_goods_three_buy',       // 逛3折购15秒
            'VIEW_GOODS_ONE_YUAN_MONEY'      => 'mall/dreame/view_goods_one_yuan_money',  // 逛1元购15秒
    ];

    const IF_SYNC = [
            'ORDER_MSG_SEND'         => false,//订单相关
            'VIEW_GOODS'             => false,//浏览商品页面15s
            'SHARE_GOODS'            => false,//分享任意商品
            'ADD_CHART'              => false,//商品加入购物车
            'BUY_GOODS'              => false,//购买商品
            'INVITE_BUY'             => false,//邀请好友购买
            'INVITE_REG'             => false,//邀请好友注册
            'BE_INVITE_REG'          => false,//邀请好友注册
            'PRODUCT_REG'            => false,//产品注册
            'SUBSCRIBE'              => false,//关注微信公众号
            'ADD_WECHAT'             => false,//添加追觅心享官微信
            'WATCH_LIVE'             => false,//观看直播
            'DRAW_POINT'             => false,//抽奖中积分
            'EVALUATE_MAIN_MACHINE'  => false,//评价主机送积分
            'EVALUATE_PARTS'         => false,//评价配件送积分
            'EMPLOYEE_BUY_GOODS'     => false,//员工购买商品
            'EMPLOYEE_INVITE_EVENT'  => false,//分享裂变
            'MEMBER_REG'             => false,//注册新会员送积分
            'DAY_ACTIVITY'           => false,//注册新会员送积分
            'APP_INVITE_REG'         => false,//注册新会员送积分
            'INVITE_BUY_AMBASSADOR'  => false,//追觅大使积分
            'ONE_YUAN_PURCHASE'      => false,//逛1元购15秒
            'RICH_PLAN'              => false,//逛暴富计划15秒
            'FIVE_DISCOUNT_PURCHASE' => false,//逛五折购15秒
            'HALF_PRICE_BUY'         => false,//逛半价购15秒
            'POINTS_SHOPPING'        => false,//逛积分购物15秒
            'APP_INVITE_REG_GOLD'    => false,//邀请好友得金币
            'APP_SHARE_GOODS'        => false,//转发商品
            'PARTNER_INVITE_FRIEND'  => false,//注册新会员送积分
            'SUPER_PARTNER_FIRST_POINT' => false,//邀请好友一次给10万积分
            'INVITE_BUY_GOODS_MONEY' => false,//邀请好友购买商品送消费金
            'BUY_GOODS_MONEY'        => false,//自己购买商品送消费金
            'OPEN_SHOP'              => false,//申请追觅小店得消费金
            'ADD_RICH_PLAN'          => false,//追觅合伙人得消费金
    ];

    // 机器类型
    const MACHINE_TYPE = [
        'MAIN' => 1, // 主机
        'PART' => 2  // 配件
    ];

    private function __getDetailUrlByEvent($event, $orderNo): string
    {
        if (in_array($event, ['shipped', 'dispatch', 'signed', 'paid', 'abnormal','order_presale_start','order_presale_end'])) {
            return self::EVENT_APP_ORDER_DETAIL . "?order_no=" . $orderNo;
        }
        if (in_array($event, ['refund'])) {
            return self::EVENT_APP_REFUND_DETAIL . "?refundNo=" . $orderNo;
        }
        return '';
    }

    /**
     * @return EventMsg
     */
    public static function factory(): EventMsg
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    private function __eventList($user_id): string
    {
        $mod = intval($user_id) % 10;
        return AppCRedisKeys::eventList($mod);
    }

    /**
     * @param string $user_id
     * @param string $function
     * @param array $args
     * @param int $time
     * @return array
     * @throws \RedisException
     * 插入队列
     */
    public function push(string $user_id, string $function, array $args, int $time = 0): array
    {
        $methods = get_class_methods(__CLASS__);
        if (!in_array($function, $methods)) {
            return [false, '方法不存在'];
        }

        if (empty($args)) {
            return [false, '缺少参数'];
        }

        $r_key = $this->__eventList($user_id);

        by::redis('core')->rPush($r_key,
            json_encode(
                [
                    'function' => $function,
                    'args' => $args,
                    'time' => $time ?: time(),
                ]
            )
        );

        return [true, 'ok'];
    }

    public function synEvent($index)
    {
        try {
            $redis_key = $this->__eventList($index);
            $my_pid = getmypid();
            $redis = by::redis('core');
            $exist_key = AppCRedisKeys::ProcessExistKey("event");
            $start_time = START_TIME;
            by::redis('core')->setOption(\Redis::OPT_READ_TIMEOUT, -1);

            while (true) {
                //每小时重启一次 防止内存泄漏
                if (abs(time() - $start_time) > 3600) {
                    CUtil::debug("退出进程,PID:{$my_pid}", 'link_run_event');
                    exit(0);
                }

                $signs = $redis->GETSET($exist_key, 0);
                if ($signs) {
                    // $signs :  '/service/msg/wx-tpl-msg@15';
                    $Arr = explode('@', $signs);
                    $name = PRO_NAME;
                    $process_name = isset($Arr[0]) ? $Arr[0] : 'error';
                    $sigkill = isset($Arr[1]) ? $Arr[1] : 15;

                    $command = " ps aux | grep {$name} | grep 'yii' | grep '{$process_name}' | grep -v grep | awk '{print $2}' | xargs kill -{$sigkill}";

                    CUtil::debug("杀死所有进程,COMMAND:" . $command, 'link_run_event');
                    system($command);
                    exit(0);
                }

                //安全退出， 计划任务1分后会自动重启 有N个进程就设置n次
                $ret = $redis->BLPOP([$redis_key], 7200);

                $data = $ret[1] ?? "";
                if (!is_string($data) || empty($data)) {
                    continue;
                }

                $aData = json_decode($data, true);
                if (empty($aData)) {
                    continue;
                }

                //todo 三次重试
                $msg = '';
                for ($i = 1; $i <= 3; $i++) {
                    list($status, $msg) = $this->run($aData['function'], $aData['args'], $aData['time']);
                    if ($status) {
                        break;
                    }

                    sleep($i * 2);
                }

                if (!$status) {
                    //重试也失败 加入到mysql记录
                    by::model('EventLogModel', 'main')->saveLog($aData['function'], $aData['args'], $msg, $aData['time']);
                }
            }


        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'link_run_event_err');
            exit($error);
        }
    }

    /**
     * @param $user_id
     * @param $order_no
     * @throws Exception
     * 邀请好友购买和普通购买7天后触发事件
     */
    public function finishGoodsEvent($user_id, $order_no)
    {
        $userInfo = by::userRecommend()->getInfoByUid($user_id);
        //判断是否是邀请购买
        if (isset($userInfo['r_id'])&&!empty($userInfo['r_id'])){
            $this->run('inviteBuy', ['user_id' => $userInfo['r_id'],'order_no'=>$order_no]);
        }
        $this->run('buyGoods',['user_id'=>$user_id,'order_no'=>$order_no]);
    }


    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'viewGoods':
            case 'shareGoods':
            case 'addChart':
            case 'inviteReg':
            case 'beInviteReg':
            case 'addWechat':
            case 'subscribe':
            case 'watchLive':
            case 'partnerInviteFriend':
            case 'appInviteReg':
            case 'browseAddScore':
            case 'oneYuanPurchase':
            case 'richPlan':
            case 'fiveDiscountPurchase':
            case 'halfPriceBuy':
            case 'pointsShopping':
            case 'appInviteRegGold':
            case 'appShareGoods':
            case 'superPartnerFirstPoint':
            case 'addThreeBuy':
            case 'openShop':
            case 'addRichPlan':
            case 'viewGoodsMoney':
            case 'shareGoodsFriendMoney':
            case 'viewGoodsGroup':
            case 'shareGroupGoods':
            case 'shareShopGoodsFriend':
            case 'friendBuyShopGoods':
            case 'openRichPlan':
            case 'shareRichGoodsFriend':
            case 'viewGoodsRichPlanMoney':
            case 'oneYuanFriendSupport':
            case 'addBlindPrizeDraw':
            case 'viewGoodsThreeBuy':
            case 'viewGoodsOneYuanMoney':
            case 'postFriend':
                return $this->$function($args['user_id']);
            case 'shareGoodsMoney':
                return $this->$function($args['user_id'], $args['inviter_id'] ?? 0);
            case 'appInviteRegMoney':
            case 'appInviteFriendLike':
                return $this->$function($args['inviter_id'], $args['invitee_id']);
            case 'buyGoods':
            case 'inviteBuyComm':
            case 'inviteBuy':
            case 'inviteBuyGoodsMoney':
            case 'buyGoodsMoney':
            case 'addGroupBuy':
                return $this->$function($args['user_id'], $args['order_no']);
            case 'productReg':
                return $this->$function($args['user_id'], $args['sn']);
            case 'orderMsgSend':
                return $this->$function($args['event'], $args['order_no'], $args['refund_no'] ?? '');
            case 'preSaleOrderMsg':
                return $this->$function($args['event'], $args['order_no'], $args['pay_remain_time'] ?? '');
            case 'drawPoint':
                return $this->$function($args['user_id'], $args['point']);
            case 'drawGold':
                return $this->$function($args['user_id'], $args['gold']);
            case 'memberReg':
                return $this->$function($args['user_id'], $args['point'] ?? 0, $args['grow'] ?? 0);
            case 'evaluateMachine':
                return $this->$function($args['user_id'], $args['user_type'], $args['review_id'], $args['machine_type'], $args['oper_type']);
            case 'employeeInvite':
                return $this->$function($args['uid'], $args['invite_type'], $args['point'], $args['grow']);
            default:
                return [false, 'function 不存在'];
        }
    }

    /**
     * @throws Exception
     */
    public function shareGoodsFriendMoney($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SHARE_GOODS_FRIEND_MONEY', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function viewGoodsGroup($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('VIEW_GOODS_GROUP', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function shareGroupGoods($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SHARE_GROUP_GOODS', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function shareShopGoodsFriend($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SHARE_SHOP_GOODS_FRIEND', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function friendBuyShopGoods($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('FRIEND_BUY_SHOP_GOODS', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function openRichPlan($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('OPEN_RICH_PLAN', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function shareRichGoodsFriend($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SHARE_RICH_GOODS_FRIEND', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function viewGoodsRichPlanMoney($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('VIEW_GOODS_RICH_PLAN_MONEY', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function oneYuanFriendSupport($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('ONE_YUAN_FRIEND_SUPPORT', $mallInfo['uid'] ?? '', $data);
    }
    /**
     * @throws Exception
     */
    public function addBlindPrizeDraw($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('ADD_BLIND_PRIZE_DRAW', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function viewGoodsThreeBuy($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('VIEW_GOODS_THREE_BUY', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function viewGoodsOneYuanMoney($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('VIEW_GOODS_ONE_YUAN_MONEY', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * @throws Exception
     */
    public function viewGoods($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('VIEW_GOODS', $mallInfo['uid'] ?? '');
    }
    /**
     * @throws Exception
     */
    public function browseAddScore($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('DAY_ACTIVITY', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @throws Exception
     */
    public function appInviteReg($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('APP_INVITE_REG', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @throws Exception
     */
    public function openShop($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('OPEN_SHOP', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @throws Exception
     */
    public function addRichPlan($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('ADD_RICH_PLAN', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @throws Exception
     */
    public function superPartnerFirstPoint($user_id): array
    {
        $data['time'] = time();
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SUPER_PARTNER_FIRST_POINT', $mallInfo['uid'] ?? '',$data);
    }

    public function subscribe($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('SUBSCRIBE', $mallInfo['uid'] ?? '');
    }

    public function watchLive($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('WATCH_LIVE', $mallInfo['uid'] ?? '');
    }
    public function inviteBuyGoodsMoney($user_id,$order_no): array
    {
        $data['order_no'] = $order_no;
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('INVITE_BUY_GOODS_MONEY', $mallInfo['uid'] ?? '',$data);
    }
    public function buyGoodsMoney($user_id,$order_no): array
    {
        $data['order_no'] = $order_no;
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        return $this->__sendMsg('BUY_GOODS_MONEY', $mallInfo['uid'] ?? '',$data);
    }

    public function addWechat($user_id): array
    {
        //抽奖  添加心享官赠送抽奖次数
        DrawActivityService::getInstance()->doActivityTask($user_id, ['task_code'=>'ADD_CUSTOMER_SERVICE','check_task'=>0]);
        $mallInfo    = by::usersMall()->getMallInfoByUserId($user_id);
        return $this -> __sendMsg('ADD_WECHAT',$mallInfo['uid']??'');
    }
    public function appInviteRegMoney(int $inviter_id, int $invitee_id): array
    {
        $data['time'] = time();
        $data['user_id'] = $inviter_id;
        $data['invitee_id'] = $invitee_id;
        $mallInfo = by::usersMall()->getMallInfoByUserId($inviter_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:APP_INVITE_REG_MONEY|user_id:' . $inviter_id . '|用户uid为空', 'warn.uid');
        }
        $data['uid'] = $mallInfo['uid'] ?? '';
        $mallInfo2 = by::usersMall()->getMallInfoByUserId($invitee_id);
        $data['invitee_uid'] = $mallInfo2['uid'] ?? '';
        return $this->__sendMsg('APP_INVITE_REG_MONEY', $mallInfo['uid'] ?? '', $data);
    }
    public function appInviteFriendLike(int $inviter_id, int $invitee_id): array
    {
        $data['time'] = time();
        $data['user_id'] = $inviter_id;
        $data['invitee_id'] = $invitee_id;
        $mallInfo = by::usersMall()->getMallInfoByUserId($inviter_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:APP_INVITE_FRIEND_LIKE|user_id:' . $inviter_id . '|用户uid为空', 'warn.uid');
        }
        $data['uid'] = $mallInfo['uid'] ?? '';
        $mallInfo2 = by::usersMall()->getMallInfoByUserId($invitee_id);
        $data['invitee_uid'] = $mallInfo2['uid'] ?? '';
        return $this->__sendMsg('APP_INVITE_FRIEND_LIKE', $mallInfo['uid'] ?? '', $data);
    }


    /**
     * @throws Exception
     */
    public function preSaleOrderMsg($event, $order_no, $pay_remain_time = ''): array
    {
        $msgConfigId = self::EVENT_USER_PRE_ORDER_ID[$event] ?? '';
        if (empty($msgConfigId) || empty($order_no)) {
            CUtil::debug(json_encode($event,320) . '|' . $order_no, 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }
        //获取商品订单数据
        list($status, $orderData) = $this->GetGoodsInfoByOrderNo($order_no);
        if (!$status) {
            CUtil::debug(json_encode($orderData,320), 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }

        //获取跳转链接
        $linkUrl = $this->__getDetailUrlByEvent($event,$order_no);
        if(empty($linkUrl)){
            CUtil::debug('link_url 不存在'.json_encode($event,320) . '|' .$linkUrl.'|'. $order_no, 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }

        $data = [
            'msgConfigId' => $msgConfigId,
            'goods_name' => $orderData['goods_name'] ?? '',
            'order_sn' => $order_no,
            'goods_num' => $orderData['goods_num'] ?? 0,
            'link_url' => $linkUrl,
        ];

        if($pay_remain_time){
            $data['pay_remain_time'] = $pay_remain_time;
        }

        return $this->__sendMsg('ORDER_MSG_SEND', $orderData['uid'] ?? '', $data);

    }



    /**
     * @throws Exception
     */
    public function orderMsgSend($event, $order_no, $refund_no = ''): array
    {
        $msgConfigId = self::EVENT_MSG_ID[$event] ?? '';
        if (empty($msgConfigId) || empty($order_no)) {
            CUtil::debug(json_encode($event,320) . '|' . $order_no, 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }
        //获取商品订单数据
        list($status, $orderData) = $this->GetGoodsInfoByOrderNo($order_no);
        if (!$status) {
            CUtil::debug(json_encode($orderData,320), 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }

        //获取跳转链接
        $urlOrderNo = ($event == 'refund') ? $refund_no : $order_no;
        $linkUrl = $this->__getDetailUrlByEvent($event,$urlOrderNo);
        if(empty($linkUrl)){
            CUtil::debug('link_url 不存在'.json_encode($event,320) . '|' .$linkUrl.'|'. $urlOrderNo, 'warn.msg.ORDER_MSG_SEND.send');
            return [true,'ok'];
        }

        $data = [
            'msgConfigId' => $msgConfigId,
            'status' => $orderData['status_name'] ?? '',
            'goods_name' => $orderData['goods_name'] ?? '',
            'order_sn' => $order_no,
            'goods_num' => $orderData['goods_num'] ?? 0,
            'link_url' => $linkUrl,
        ];

        return $this->__sendMsg('ORDER_MSG_SEND', $orderData['uid'] ?? '', $data);

    }


    public function __sendMsg($code, $uid, $data = ""): array
    {
         $newCode = self::EVENT_CODE[$code] ?? '';
         if(empty($newCode)){
             CUtil::debug( '事件CODE为空|'.$code.'|'.$uid.'|'.json_encode($data,320) , "warn.msg.{$code}.send");
             return [true,'ok'];
         }
         if(empty($uid)){
             CUtil::debug( '用户uid为空|'.$code.'|'.$uid.'|'.json_encode($data,320) , "warn.msg.{$code}.send");
             return [true,'ok'];
         }


        $ifSync = self::IF_SYNC[$code] ?? false;
         //组合数据
        $sendData = [
            'code'      => $newCode,
            'uuid'      => Uuid::uuid(),
            'uid'       => $uid,
            'timestamp' => CUtil::getMillisecond(),//毫秒级
            'data'      => $data,
            'tenantId'  => CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
        ];

        CUtil::debug(json_encode($sendData, 320), "all.{$code}.send");

        //redis 控制
        $redis     = by::redis('core');
        $uniqueKey = CUtil::getAllParams(__FUNCTION__, $newCode, $uid, json_encode($data, 320));
        list($s)    = by::model("CommModel",MAIN_MODULE)->AccFrequency($uid,$uniqueKey,7200,"EX",1);
        if (!$s) {
            return [true, '数据已经推送'];
        }

        CUtil::debug(json_encode($sendData, 320), "msg.{$code}.send");

        // 推送kafka 异常不打日志统一处理
        Kafka::factory()->send('m_user_e', $sendData, $ifSync);
        $redis->set($uniqueKey, 1, ['NX', 'EX' => YII_ENV_PROD? 7200 : 7200]);

        return [true,'ok'];
    }


    /**
     * @throws Exception
     */
    public function GetGoodsInfoByOrderNo($order_no): array
    {
        $data = [
            'uid' => '',
            'order_no' => $order_no,
            'user_id' => 0,
            'status' => '',
            'status_name' => '',
            'goods_name' => '',
            'goods_num' => 0,
            'link_url' => ''
        ];

        $user_id = by::Omain()->GetUserIdByNo($order_no);

        if (empty($user_id)) {
            CUtil::debug(json_encode($order_no).'|'.$user_id. '|用户不存在', 'warn.msg.order.send');
            return [false, '用户不存在'];
        }
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        $data['uid'] = $mallInfo['uid'] ?? '';
        if (empty($data['uid'])) {
            CUtil::debug(json_encode($order_no).'|'.$user_id.'|用户UID不存在', 'warn.msg.order.send');
            return [false, '用户UID不存在'];
        }
        $orderData = by::Ouser()->CommPackageInfo($user_id, $order_no);
        if (empty($orderData)) {
            CUtil::debug(json_encode($orderData) . '|订单不存在', 'warn.msg.order.send');
            return [false,'订单不存在'];
        }
        //商品统计
        $goods = $orderData['goods'] ?? [];

        if ($goods) {
            foreach ($goods as $good) {
                $data['goods_num'] += $good['num'] ?? 0;
            }
            $data['goods_name'] = $goods[0]['name'] ?? '';
        }

        $order_status = $orderData['status'] ?? 0;
        $data['status'] = $order_status;
        $data['status_name'] = by::Omain()::ORDER_STATUS[$order_status] ?? '';

        return [true, $data];

    }

    /**
     * @throws Exception
     */
    public function getOrderGoodsInfoByOrderNo($order_no, $can_user = false, $can_attr = false, $can_ad = false, $can_remain = false, $cache = false, $isShortCode = false): array
    {
        $user_id = by::Omain()->GetUserIdByNo($order_no);
        if (empty($user_id)) {
            CUtil::debug(json_encode($order_no) . '|用户不存在', 'warn.msg.order.send');
            return [false, '用户不存在'];
        }
        $orderData = by::Ouser()->CommPackageInfo($user_id, $order_no, $can_user, $can_attr, $can_ad, $can_remain,$cache,$isShortCode,true,true);
        if (empty($orderData)) {
            CUtil::debug(json_encode($orderData) . '|订单不存在', 'warn.msg.order.send');
            return [false,'订单不存在'];
        }
        return [true, $orderData];
    }

    /**
     * @param $user_id
     * @param $sn
     * @return array
     * @throws Exception
     * 产品注册
     */
    public function productReg($user_id, $sn): array
    {
        //获取注册产品信息
        $pRegInfo = by::productReg()->getPRegInfo($user_id, $sn);
        if (!$pRegInfo) {
            CUtil::debug('无产品注册信息', 'warn.msg.PRODUCT_REG.send');
            return [true,'无产品注册信息'];
        }
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:PRODUCT_REG|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        return $this->__sendMsg('PRODUCT_REG', $mallInfo['uid'] ?? '', ['sn' => $sn]);
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 加入购物车
     */
    public function addChart($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:ADD_CHART|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        return $this->__sendMsg('ADD_CHART', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 分享商品
     */
    public function shareGoods($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:SHARE_GOODS|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        return $this->__sendMsg('SHARE_GOODS', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 邀请注册
     */
    public function inviteReg($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:INVITE_REG|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        $data['time'] = microtime(true);
        return $this->__sendMsg('INVITE_REG', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 邀请注册
     */
    public function partnerInviteFriend($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:PARTNER_INVITE_FRIEND|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        // $data['time'] = microtime(true);
        return $this->__sendMsg('PARTNER_INVITE_FRIEND', $mallInfo['uid'] ?? '',[]);
    }

    /**
     * @param $user_id
     * @return array
     * @throws Exception
     * 邀请注册(被邀请人)
     */
    public function beInviteReg($user_id): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])){
            CUtil::debug('event:BE_INVITE_REG|user_id:'.$user_id.'|用户uid为空','warn.uid');
        }
        $data['time'] = microtime(true);
        return $this->__sendMsg('BE_INVITE_REG', $mallInfo['uid'] ?? '',$data);
    }
    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 邀请好友购买 统计主机配件数量
     */
    public function inviteBuyComm($user_id,$order_no): array
    {
        !YII_ENV_PROD && CUtil::debug($user_id.'|'.$order_no,'inviterBuy');
        list($status, $orderData) = $this->getOrderGoodsInfoByOrderNo($order_no, false, true,false,false,false,false);
        $goods = $orderData['goods'] ?? [];
        //限制其他订单发放次数
        $user_order_type = intval($orderData['user_order_type'] ?? 0);
        if($user_order_type !== 1) return [true,'ok'];

        $mainMachineNum = 0;
        $partsNum = 0;
        $data = [];
        if($goods){
            foreach ($goods as $value){
                $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
                $tIds = array_column($tidArr, 'tid') ?? [];
                if (in_array(20,$tIds)){
                    $partsNum += CUtil::uint($value['num']);
                }else{
                    $mainMachineNum  += CUtil::uint($value['num']);
                }
                $data['partsNum'] = $partsNum;
                $data['mainMachineNum'] = $mainMachineNum;
            }
            $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
            if (empty($mallInfo['uid'])){
                CUtil::debug('event:INVITE_BUY|user_id:'.$user_id.'|用户uid为空','warn.uid');
            }
            return $this->__sendMsg('INVITE_BUY', $mallInfo['uid'] ?? '',$data);
        }
        return [true,'ok'];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 邀请好友购买 统计主机配件数量
     */
    public function inviteBuy($user_id,$order_no): array
    {
        !YII_ENV_PROD && CUtil::debug($user_id.'|'.$order_no,'inviterBuy');
        list($status, $orderData) = $this->getOrderGoodsInfoByOrderNo($order_no, false, true,false,false,false,false);
        $goods = $orderData['goods'] ?? [];
        //限制其他订单发放次数
        $user_order_type = intval($orderData['user_order_type'] ?? 0);
        if($user_order_type !== 1) return [true,'ok'];

        // 获取推荐人的等级，用于确认积分比例
        $user = byNew::UserEmployeeModel()->getEmployeeInfo($user_id,'user_id');
        // 统一发放积分比例，暂时设置为10倍，20250721修改
        $rate = 5;
        if ($user){
            if ($user['level'] == 2){
                $rate = 20;
            }elseif($user['level'] == 3){
                $rate = 30;
            }
        }

        $mainMachineNum = 0;
        $partsNum = 0;
        $partsPay = 0;
        $mainMachinePay = $orderData['deposit_price'] ?? 0;
        $data = [];
        $data['orderNo'] = $order_no;
        if($goods){
            foreach ($goods as $value){
                $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
                $tIds = array_column($tidArr, 'tid') ?? [];
                if (in_array(20,$tIds)){
                    $partsNum += CUtil::uint($value['num']);
                }else{
                    $mainMachineNum  += CUtil::uint($value['num']);
                }
                if (in_array(20, $tIds)) {
                    // 2025-07-18 仅购买主机才会返利
                    // $partsPay += $value['price'];
                } else {
                    $mainMachinePay += $value['price'];
                }
            }
            // 按照等级发放积分比例计算积分
            $mainMachinePay = round($mainMachinePay * $rate,1);
            $partsPay = round($partsPay * $rate,1);

            // 准备调用数据
            if ($user){
                $data['type'] = 1;
            }
            $data['partsNum'] = $partsNum;
            $data['mainMachineNum'] = $mainMachineNum;
            $data['mainMachinePay'] = $mainMachinePay;
            $data['partsPay'] = $partsPay;
            $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
            if (empty($mallInfo['uid'])){
                CUtil::debug('event:INVITE_BUY|user_id:'.$user_id.'|用户uid为空','warn.uid');
            }

            // 把获取的积分，记录到追觅大使的数据表里
            if ($user && ($mainMachinePay + $partsPay) > 0){
                byNew::UserEmployeeModel()->addEmployeePoint($user_id, $mainMachinePay + $partsPay);
                // 把积分记录到t_osource_r表
                by::osourceR()->updateData(round($mainMachinePay + $partsPay),$order_no,$orderData['user_id'],$orderData['ctime']);
            }
            // 2025-07-29 追觅合伙人不发放积分
            if (!$user){
                return $this->__sendMsg('INVITE_BUY', $mallInfo['uid'] ?? '',$data);
            }
        }
        return [true,'ok'];
    }

    /**
     * @param $user_id
     * @param $order_no
     * @return array
     * @throws Exception
     * 购买主机配件
     */
    public function buyGoods($user_id, $order_no): array
    {

        //获取商品订单数据
        list($status, $orderData) = $this->getOrderGoodsInfoByOrderNo($order_no, false, true);
        if (!$status) {
            CUtil::debug($order_no . '订单不存在', 'err.msg.BUY_GOODS.send');
            return [true, 'ok'];
        }
        //限制其他订单发放次数
        $user_order_type = intval($orderData['user_order_type'] ?? 0);
        // 只有普通订单、和尾款订单、以旧换新订单、内购订单才给发放积分
        if (!in_array($user_order_type, [OmainModel::USER_ORDER_TYPE['COMMON'], OmainModel::USER_ORDER_TYPE['TAIL'], OmainModel::USER_ORDER_TYPE['OTN'], OmainModel::USER_ORDER_TYPE['POINT'], OmainModel::USER_ORDER_TYPE['INTERNAL'],OmainModel::USER_ORDER_TYPE['GROUP_PURCHASE']])) {
            return [true, 'ok'];
        }

        // if (in_array($user_order_type, [OmainModel::USER_ORDER_TYPE['COMMON'], OmainModel::USER_ORDER_TYPE['TAIL'], OmainModel::USER_ORDER_TYPE['OTN'],OmainModel::USER_ORDER_TYPE['GROUP_PURCHASE']])) {
        //     if (empty($orderData['goods'][0]['is_employee'])) { // 会员购买
        //         return $this->memberBuyGoods($user_id, $order_no, $orderData);
        //     } else { // 员工购买
        //         return $this->employeeBuyGoods($user_id, $order_no, $orderData, false);
        //     }

        // } elseif ($user_order_type == OmainModel::USER_ORDER_TYPE['INTERNAL']) { // 内购订单（员工购买）
        //     return $this->employeeBuyGoods($user_id, $order_no, $orderData, true);
        // } else { // 积分商城
        //     if (!empty($orderData['goods'][0]['is_employee'])) { // 微笑大使购买积分商城商品
        //         return $this->employeeBuyPointGoods($user_id, $orderData);
        //     }
        // }

        // 2025-07-28 产品要求，所有自购情况都按会员等级发放积分，特殊发放积分规则都不再适用
        if (in_array($user_order_type, [OmainModel::USER_ORDER_TYPE['COMMON'], OmainModel::USER_ORDER_TYPE['TAIL'], OmainModel::USER_ORDER_TYPE['OTN'],OmainModel::USER_ORDER_TYPE['GROUP_PURCHASE']])) {
            return $this->memberBuyGoods($user_id, $order_no, $orderData);
        }
        return [true, 'ok'];
    }

    /**
     * 抽奖中积分
     * @param int $user_id
     * @param int $point
     * @return array
     * @throws Exception
     */
    public function drawPoint(int $user_id, int $point): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:DRAW_POINT|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
        }
        $data = ['point' => $point, 'timestamp' => CUtil::getMillisecond()];
        return $this->__sendMsg('DRAW_POINT', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * 抽奖中金币
     * @param int $user_id
     * @param int $gold
     * @return array
     * @throws Exception
     */
    public function drawGold(int $user_id, int $gold): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:DRAW_GOLD|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
        }
        $data = ['gold' => $gold, 'timestamp' => CUtil::getMillisecond()];
        return $this->__sendMsg('DRAW_POINT', $mallInfo['uid'] ?? '', $data);
    }
    
    /**
     * @param $user_id
     * @param $point
     * @param $grow
     * @return array
     * @throws Exception
     * 注册新会员送积分
     */
    public function memberReg($user_id, $point, $grow): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:DRAW_POINT|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
        }
        $data = [
            'evaluateType' => 'member_reg',
            'operType'     => 1, //1 赠送积分  -1扣减积分
            'pointValue'   => intval($point),
            'growValue'    => $grow,
            'goldValue'    => 0,
        ];
        return $this->__sendMsg('MEMBER_REG', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * 评价机器送积分
     * @param $user_id
     * @param int $user_type 用户类型：1 商城用户id、2 uid
     * @param int $review_id
     * @param int $machine_type 机器类型：1 主机、2 配件
     * @param int $oper_type 操作类型：-1扣减  1增加
     * @return array
     * @throws Exception
     */
    public function evaluateMachine($user_id, int $user_type, int $review_id, int $machine_type, int $oper_type = 1): array
    {
        // 获取uid
        if ($user_type == 1) { // 商城用户id
            $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
            if (empty($mallInfo['uid'])) {
                CUtil::debug('event:EVALUATE_MACHINE|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
                return [true, 'ok'];
            }
            $uid = $mallInfo['uid'];
        } else {
            $uid = $user_id;
        }

        if ($machine_type == self::MACHINE_TYPE['MAIN']) { // 主机
            // 参数：reviewId只有一个作用，用于防止频率限制。
            $data = ['evaluateType' => 'evaluate_main_machine', 'operType' => $oper_type, 'reviewId' => $review_id];
            return $this->__sendMsg('EVALUATE_MAIN_MACHINE', $uid, $data);
        } else { // 配件
            // 参数
            $data = ['evaluateType' => 'evaluate_parts', 'operType' => $oper_type, 'reviewId' => $review_id];
            return $this->__sendMsg('EVALUATE_PARTS', $uid, $data);
        }
    }

    /**
     * 会员购买
     * @param $user_id
     * @param $order_no
     * @param $orderData
     * @return array
     * @throws Exception
     */
    private function _memberBuyGoods($user_id, $order_no, $orderData): array
    {
        $data           = [];
        $partsPay       = 0;
        $mainMachinePay = 0;
        $goods          = $orderData['goods'] ?? [];
        $deposit_price  = $orderData['deposit_price'] ?? 0;

        if ($goods) {
            $tagTypeMap = by::Gtag()->GetTagTypeMap();
            foreach ($goods as $k => $value) {
                $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
                $tids   = array_column($tidArr, 'tid') ?? [];
                if (in_array(20, $tids)) {

                    $value['goods_type'] = 'part';
                    $partsPay            += $value['price'];
                } else {

                    $value['goods_type'] = 'machine';
                    $mainMachinePay      += $value['price'];
                }
                $tid                                  = array_pop($tids);
                // $data['goods'][$k]['productCategory'] = GtagModel::TAG_TYPE[$tid] ?? '';
                $data['goods'][$k]['productCategory'] = $tagTypeMap[$tid] ?? '';
                $data['goods'][$k]['goodsCategory']   = $value['sku'];
                $data['goods'][$k]['name']            = $value['name'];
                $data['goods'][$k]['num']             = $value['num'];
            }
            $data['partsPay']       = $partsPay;
            $data['mainMachinePay'] = bcadd($mainMachinePay, $deposit_price, 2);
            $data['orderTime']      = empty($orderData['ctime'] ?? 0) ? 0 : $orderData['ctime'] . '000';
            $data['order_no']       = $order_no;
            $mallInfo               = by::usersMall()->getMallInfoByUserId($user_id);
            if (empty($mallInfo['uid'])) {
                CUtil::debug('event:BUY_GOODS|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
            }

            return $this->__sendMsg('BUY_GOODS', $mallInfo['uid'] ?? '', $data);
        }
        return [true, 'ok'];
    }

    /**
     * 会员首次购买
     * @param $user_id
     * @param $order_no
     * @param $orderData
     * @return array
     * @throws Exception
     */
    public function memberBuyGoods($user_id, $order_no, $orderData): array
    {
        $uid = byNew::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            return [true, 'ok'];
        }
        return $this->_memberBuyGoods($user_id, $order_no, $orderData);

        // 获取用户绑定信息
        $userBindParam = [
            'bound_uid' => $uid,
            'bind_status' => UserBindModel::BIND_STATUS['BIND']
        ];
        $userBinds = byNew::UserBindModel()->getBindList($userBindParam, 1, 1);

        if (empty($userBinds[0])) { // 当前购买的用户，没有绑定的推荐人
            return $this->_memberBuyGoods($user_id, $order_no, $orderData);
        }

        // 给绑定人奖励
        // $boundUserOrder = byNew::BoundUserOrderModel()->getOrderList(['bound_uid' => $uid, 'order_no' => $order_no], 1, 1);
        // if ($boundUserOrder) {
        //     // 发放微笑分、积分、成长值
        //     \Yii::$app->queue->push(new EmployeeInviteJob([
        //         'params' => [
        //             'uid'         => $boundUserOrder[0]['uid'],
        //             'score'       => 2000,
        //             'handle_type' => 1,
        //             'score_type'  => 3,
        //             'handle_name' => '',
        //             'source'      => '邀请注册亲友完成购买',
        //             'invite_type' => 0,
        //             'point'       => 1000,
        //             'grow'        => 2000,
        //             'order_no'    => $order_no,
        //         ]
        //     ]));
        // }

        // 是否首次购买（绑定关系后的第一次购买）
        if (!$this->isFirstBuy($uid, $order_no)) {
            return $this->_memberBuyGoods($user_id, $order_no, $orderData);
        }

        // 首次购买
        $goods = $orderData['goods'] ?? [];
        if (empty($goods)) {
            return [true, 'ok'];
        }

        $partsPay = 0;
        $mainMachinePay = $orderData['deposit_price'] ?? 0;
        $data = [
            'goods' => [],
            'order_no' => $order_no,
            'orderTime' => empty($orderData['ctime']) ? 0 : $orderData['ctime'] . '000'
        ];
        $tagTypeMap = by::Gtag()->GetTagTypeMap();
        foreach ($goods as $k => $value) {
            $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
            $tids = array_column($tidArr, 'tid') ?? [];

            if (in_array(20, $tids)) {
                $value['goods_type'] = 'part';
                $partsPay += $value['price'];
            } else {
                $value['goods_type'] = 'machine';
                $mainMachinePay += $value['price'];
            }

            $tid = array_pop($tids);
            $data['goods'][$k] = [
                // 'productCategory' => GtagModel::TAG_TYPE[$tid] ?? '',
                'productCategory' => $tagTypeMap[$tid] ?? '',
                'goodsCategory' => $value['sku'],
                'name' => $value['name'],
                'num' => $value['num']
            ];
        }

        $data['partsPay'] = $partsPay;
        $data['mainMachinePay'] = 0;

        if ($partsPay > 0) {
            //追觅大使积分发放
            if (!$this->dreamAmbassadorSendPoint($partsPay, $user_id, $uid)){
                // 追觅大使积分发放失败，走普通配件购买商品的逻辑
                $this->__sendMsg('BUY_GOODS', $uid, $data);
            }
        }

        if ($mainMachinePay > 0) {
            $data = [
                'innerMallType' => strval(0),
                'shareType' => strval(1),
                'orderTime' => $data['orderTime'],
                'timestamp' => CUtil::getMillisecond(),
                'order_no' => $order_no,
                'mainMachinePay' => $mainMachinePay,
                'partsPay' => 0,
                'mainMachineDesc' => '购买主机',
                'mainMachinePoint' => round($mainMachinePay * 5),
                'mainMachineGrow' => round($mainMachinePay),
                'partsDesc' => '',
                'partsPoint' => 0,
                'partsGrow' => 0,
            ];

            //追觅大使积分发放
            if (!$this->dreamAmbassadorSendPoint($mainMachinePay, $user_id, $uid)) {
                // 追觅大使积分发放失败，走普通主机购买商品的逻辑
                $this->__sendMsg('EMPLOYEE_BUY_GOODS', $uid, $data);
            }
        }

        return [true, 'ok'];
    }

    /**
     * 追觅推广大使积分发放
     */
    public function dreamAmbassadorSendPoint($partsPay, $user_id, $uid): bool
    {

        // 追觅推广大使（邀请人数大于10）邀请人和被邀请人同时获得10倍积分 （与会员积分互斥、不与追觅合伙人互斥）
        $data= by::UserInviteModel()->checkDreameAmbassador($user_id);
        $is_dream_ambassador= $data['is_dream_ambassador'] ?? false;
        $inviter_id = $data['inviter_id'] ?? 0;
        $invitee_id = $data['invitee_id'] ?? 0;
        $total_invites = $data['total_invites'] ?? 0;

        if ($is_dream_ambassador) {
            $dream_ambassador_data = [
                    "pay"                 => $partsPay,
                    "isAmbassador" => 1
            ];
            //邀请人反积分
            $inviter_uid = byNew::Phone()->getUidByUserId($inviter_id);
            $this->__sendMsg('INVITE_BUY_AMBASSADOR', $uid, $dream_ambassador_data);
            //被邀请人反积分
            $this->__sendMsg('INVITE_BUY_AMBASSADOR', $inviter_uid, $dream_ambassador_data);

            // 追觅推广大使
            byNew::UserPopupModel()->savePopup($inviter_id, byNew::UserPopupModel()::POPUP_TYPE['INVITE_FRIEND_BUY'], $partsPay * DreamAmbassadorService::POINT_RATE);
            CUtil::debug( '追觅推广大使被邀请人购物积分发放|invitee_id:' . $user_id . '|inviter_id:' . $inviter_id . '|partsPay:' . $partsPay, 'dream_ambassador_send_point');
            return true;
        }

        return false;
    }

    /**
     * 分享裂变
     * @param $uid
     * @param $inviteType
     * @param $point
     * @param $grow
     * @return array
     */
    public function employeeInvite($uid, $inviteType, $point, $grow)
    {
        $data = [
            'inviteType' => strval($inviteType), // 0 分享好友购买商品 1 邀请好友注册
            'point'      => $point,
            'grow'       => $grow,
            'timestamp'  => time(),
        ];
        return $this->__sendMsg('EMPLOYEE_INVITE_EVENT', $uid, $data);
    }

    /**
     * 员工购买商品
     * @param $user_id
     * @param $order_no
     * @param $orderData
     * @param $isInternal
     * @return array
     * @throws Exception
     */
    private function employeeBuyGoods($user_id, $order_no, $orderData, $isInternal): array
    {
        // todo 微笑大使购买，如果是在活动积分期间内 识别哪个事件赠送积分多  本次年货节 购买配件走普通事件加积分多，购买主机走微笑大使事件加积分多


        $goods = $orderData['goods'] ?? [];
        if (empty($goods)) {
            return [true, 'ok'];
        }

        $partsPay       = 0;
        $mainMachinePay = $orderData['deposit_price'] ?? 0;

        foreach ($goods as $item) {
            $tidArr = by::Gtag()->GetListByGid($item['gid']) ?? [];
            $tids   = array_column($tidArr, 'tid') ?? [];
            if (in_array(20, $tids)) {
                $partsPay += $item['price'];
            } else {
                $mainMachinePay += $item['price'];
            }
        }

        // 获取员工奖励比例
        $employeeRate =         byNew::PointConfigModel()::getConfig()['employee_reward_rate'] ?? 2;

        $data = [
            'innerMallType'    => strval($isInternal ? 1 : 0), // 0非内购商城 1内购商城
            'shareType'        => strval(0),
            'orderTime'        => empty($orderData['ctime'] ?? 0) ? 0 : $orderData['ctime'] . '000',
            'timestamp'        => CUtil::getMillisecond(),
            'order_no'         => $order_no,
            'mainMachinePay'   => $mainMachinePay,
            'partsPay'         => $partsPay,
            'mainMachineDesc'  => $isInternal ? '购买内购商城主机' : '购买主机',
            'mainMachinePoint' => round($mainMachinePay * $employeeRate),
            'mainMachineGrow'  => round($mainMachinePay),
            'partsDesc'        => $isInternal ? '购买内购商城配件' : '购买配件',
            'partsPoint'       => $this->getPartsPoint($user_id, $partsPay, $isInternal),
            'partsGrow'        => round($partsPay * $employeeRate),
        ];

        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            CUtil::debug('event:EMPLOYEE_BUY_GOODS|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
        }

        // 同步增加微笑分
        if (round($mainMachinePay) > 0) {
            \Yii::$app->queue->push(new EmployeeOptScoreJob([
                    'params' => [
                            'uid'         => $uid,
                            'score'       => round($mainMachinePay),
                            'handle_type' => 1,
                            'score_type'  => 3,
                            'handle_name' => '',
                            'source'      => $isInternal ? '购买内购商城主机' : '购买主机',
                    ]
            ]));

            //追觅大使积分发放
            if (!$this->dreamAmbassadorSendPoint($mainMachinePay, $user_id, $uid)) {
                // 追觅大使积分发放失败，走普通配件购买商品的逻辑
                $this->__sendMsg('EMPLOYEE_BUY_GOODS', $uid, $data);
            }
        }

        if (round($partsPay * $employeeRate) > 0) {
            \Yii::$app->queue->push(new EmployeeOptScoreJob([
                    'params' => [
                            'uid'         => $uid,
                            'score'       => round($partsPay * $employeeRate),
                            'handle_type' => 1,
                            'score_type'  => 3,
                            'handle_name' => '',
                            'source'      => $isInternal ? '购买内购商城配件' : '购买配件',
                    ]
            ]));

            //追觅大使积分发放
            if (!$this->dreamAmbassadorSendPoint($partsPay, $user_id, $uid)) {
                // 追觅大使积分发放失败，走普通主机购买商品的逻辑
                $this->__sendMsg('EMPLOYEE_BUY_GOODS', $uid, $data);
            }
        }

        // 给绑定人奖励
        // $boundUserOrder = byNew::BoundUserOrderModel()->getOrderList(['bound_uid' => $uid, 'order_no' => $order_no], 1, 1);
        // if ($boundUserOrder) {
        //     // 发放微笑分、积分、成长值
        //     \Yii::$app->queue->push(new EmployeeInviteJob([
        //         'params' => [
        //             'uid'         => $boundUserOrder[0]['uid'],
        //             'score'       => 2000,
        //             'handle_type' => 1,
        //             'score_type'  => 3,
        //             'handle_name' => '',
        //             'source'      => '邀请注册亲友完成购买',
        //             'invite_type' => 0,
        //             'point'       => 1000,
        //             'grow'        => 2000,
        //             'order_no'    => $order_no,
        //         ]
        //     ]));
        // }

        return $this->__sendMsg('EMPLOYEE_BUY_GOODS', $uid, $data);
    }

    /**
     * 员工购买积分商城商品
     * @param $user_id
     * @param $orderData
     * @return array
     * @throws Exception
     */
    private function employeeBuyPointGoods($user_id, $orderData): array
    {
        $goods = $orderData['goods'] ?? [];
        if (empty($goods)) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $user_id . '|商品列表为空', 'warn.goods');
            return [true, 'ok'];
        }

        // Calculate total payment using array_sum for clarity
        $pay = array_sum(array_column($goods, 'price'));

        $uid = by::Phone()->getUidByUserId($user_id);
        if (empty($uid)) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $user_id . '|用户uid为空', 'warn.uid');
            return [true, 'ok'];
        }

        // Ensure pay is a valid integer
        $score = round($pay);
        if ($score <= 0) {
            CUtil::debug('event:EMPLOYEE_BUY_POINT_GOODS|user_id:' . $user_id . '|无效的支付金额', 'warn.pay');
            return [true, 'ok'];
        }

        \Yii::$app->queue->push(new EmployeeOptScoreJob([
            'params' => [
                'uid'         => $uid,
                'score'       => $score, // 发放微笑分值 = 实际付款金额
                'handle_type' => 1,
                'score_type'  => 3,
                'handle_name' => '',
                'source'      => '购买积分商城商品',
            ]
        ]));

        return [true, 'ok'];
    }

    /**
     * 有绑定关闭后的首次下单
     * @param $uid
     * @param $order_no
     * @return bool
     */
    private function isFirstBuy($uid, $order_no): bool
    {
        $orders = byNew::BoundUserOrderModel()->getOrders(['bound_uid' => $uid]);
        if (empty($orders)) {
            return false;
        }
        return $orders[0]['order_no'] == $order_no;
    }

    /**
     * 获取配件积分
     * @param $user_id
     * @param $partsPay
     * @param $isInternal
     * @return int
     */
    private function getPartsPoint($user_id, $partsPay, $isInternal)
    {
        if ($partsPay <= 0) {
            return 0;
        }

        // 内购商城商品积分计算
        if ($isInternal) {
            return round($partsPay * 10);
        }

        // 用户等级对应的积分倍数配置
        $levelMultipliers = [
                'v1' => 1,
                'v2' => 1,
                'v3' => 1.2,
                'v4' => 1.5,
                'v5' => 2,
        ];

        // 获取用户等级信息
        list($status, $basicInfo) = by::memberCenterModel()->CenterMessage('basicInfo', ['user_id' => $user_id]);
        $level      = $basicInfo['currentLevelInfo']['level']['level'] ?? 'v1';
        $multiplier = $levelMultipliers[$level] ?? 1;

        // 判断是否在活动期间
        $isDoublePoints  = ActivityConfigEnum::judgeActivityTime($user_id, 'POINT_DOUBLING');
        $finalMultiplier = $isDoublePoints ? $multiplier * ActivityConfigEnum::POINT_DOUBLING_MULTIPLE : $multiplier;

        // 计算积分
        return round($partsPay * $finalMultiplier);
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 一元购
     */
    private function oneYuanPurchase($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:ONE_YUAN_PURCHASE|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('ONE_YUAN_PURCHASE', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 逛暴富计划
     */
    private function richPlan($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:RICH_PLAN|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('RICH_PLAN', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 五折购
     */
    private function fiveDiscountPurchase($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:FIVE_DISCOUNT_PURCHASE|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('FIVE_DISCOUNT_PURCHASE', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 半价购
     */
    private function halfPriceBuy($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:HALF_PRICE_BUY|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('HALF_PRICE_BUY', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * 积分商城购物
     */
    private function pointsShopping($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:POINTS_SHOPPING|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('POINTS_SHOPPING', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * APP邀请注册赠送金币
     */
    private function appInviteRegGold($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:APP_INVITE_REG_GOLD|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('APP_INVITE_REG_GOLD', $mallInfo['uid'] ?? '');
    }

    /**
     * @param $userId
     * @return array
     * @throws Exception
     * APP分享商品
     */
    private function appShareGoods($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:APP_SHARE_GOODS|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('APP_SHARE_GOODS', $mallInfo['uid'] ?? '');
    }

    /**
     * 参与团购活动
     * @param $userId
     * @return array
     * @throws Exception
     */
    private function addGroupBuy($userId, $orderNo): array
    {
        $mallInfo         = by::usersMall()->getMallInfoByUserId($userId);
        $data['order_no'] = $orderNo;
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:ADD_GROUP_BUY|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('ADD_GROUP_BUY', $mallInfo['uid'] ?? '', $data);
    }

    /**
     * 三折购活动
     * @return void
     * @throws Exception
     */
    private function addThreeBuy($userId)
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:ADD_THREE_BUY|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        $this->__sendMsg('ADD_THREE_BUY', $mallInfo['uid'] ?? '');
    }

    /**
     * 浏览商品60秒获得消费金
     * @param $userId
     * @return array
     * @throws Exception
     */
    private function viewGoodsMoney($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:VIEW_GOODS_MONEY|user_id:' . $userId . '|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('VIEW_GOODS_MONEY', $mallInfo['uid'] ?? '');
    }

    /**
     * 朋友板块1次发帖，获得消费金奖励
     * @param $userId
     * @return array
     * @throws Exception
     */
    private function postFriend($userId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);

        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:POST_FRIEND|user_id:0|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('POST_FRIEND', $mallInfo['uid'] ?? '');

    }

    /**
     * 分享商城商品
     * @param int $userId
     * @param int $inviterId
     * @return array
     * @throws Exception
     */
    private function shareGoodsMoney($userId, $inviterId): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($inviterId);

        if (empty($mallInfo['uid'])) {
            CUtil::debug('event:SHARE_GOODS_MONEY|user_id:0|用户uid为空', 'warn.uid');
        }
        return $this->__sendMsg('SHARE_GOODS_MONEY', $mallInfo['uid'] ?? '');

    }




}
