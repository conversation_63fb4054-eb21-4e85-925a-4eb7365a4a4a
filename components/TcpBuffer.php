<?php
/**TCP缓冲区类
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/9/10
 * Time: 18:55
 */
namespace app\components;
final class TcpBuffer {

	protected static $instance = NULL;

	/**
	 * 类型：uint8
	 * @var int
	 */
	const UINT8 = 1;

	/**
	 * 类型：uint16
	 * @var int
	 */
	const UINT16 = 2;

	/**
	 * 类型：uint32
	 * @var int
	 */
	const UINT32 = 4;

	/**
	 * 类型：uint64
	 * @var int
	 */
	const UINT64 = 8;

	/**
	 * 类型：binary
	 * @var int
	 */
	const BINARY = 9;

	/**
	 * 类型：string(由一个uint32和一个由uint32的值作为长度的binary组成)
	 * integer
	 */
	const STRING = 10;

	/**
	 * 缓冲区
	 * @var string
	 */
	protected $_buffer;

	/**
	 * 缓冲区的大小
	 * @var int
	 */
	protected $_size;

	/**
	 * 读指针位置
	 * @var int
	 */
	protected $_pos;



	/**
	 * @param string $host
	 * @param string $port
	 * @return UdpClient|null
	 *
	 */
	public static function newInstance(): TcpBuffer
	{
		if (self::$instance == NULL) {

			self::$instance = new TcpBuffer();
		}
		return self::$instance;
	}

	/**
	 * 获取缓冲内容
	 * @return string
	 */
	public function getBuffer() {
		return $this->_buffer;
	}

	/**
	 * 设置缓冲内容
	 * @param string $buf 要解析的内容
	 */
	public function setBuffer($buf) {
		$this->_buffer = $buf;
		$this->_size   = strlen($this->_buffer);
		$this->_pos    = 0;
	}

	/**
	 * 写数据到缓冲区中
	 * @param int $type 数据类型
	 * @param mixed $val 数据值
	 * @return bool 是否成功
	 */
	public function write($type, $val) {
		switch($type) {
			case self::UINT8:
				$this->_buffer .= chr(intval($val));
				$this->_size += self::UINT8;
				break;
			case self::UINT16:
				$this->_buffer .= pack('n', intval($val));
				$this->_size += self::UINT16;
				break;
			case self::UINT32:
				$this->_buffer .= pack('N', intval($val));
				$this->_size += self::UINT32;
				break;
			case self::UINT64:
				if(PHP_MAJOR_VERSION >= 5 && PHP_MINOR_VERSION >= 6 && PHP_RELEASE_VERSION >= 3) {
					$this->_buffer .= pack('J', intval($val));
				} else {
					$val = intval($val);
					$this->_buffer .= pack('N', $val >> 32);
					$this->_buffer .= pack('N', $val & 0xFFFFFFFF);
				}
				$this->_size += self::UINT64;
				break;
			case self::BINARY:
				$this->_buffer .= $val;
				$this->_size += strlen($val);
				break;
			case self::STRING:
				$this->write(self::UINT32, strlen($val));
				$this->write(self::BINARY, $val);
				break;
			default:
				return false;
		}
		return true;
	}

	/**
	 * 从缓冲区中(顺序地)读取一个值
	 * @param int $type 数据类型
	 * @param int $len 长度(只有binary类型时要求指定)
	 * @return mixed 成功时返回指定类型的数据值，失败时返回false
	 */
	public function read($type, $len=0) {
		switch($type) {
			case self::UINT8:
				if($this->_pos + self::UINT8 > $this->_size) {
					return false;
				} else {
					$data = unpack('C', substr($this->_buffer, $this->_pos, self::UINT8));
					$this->_pos += self::UINT8;
					return is_array($data) && isset($data[1]) ? $data[1] : false;
				}
			case self::UINT16:
				if($this->_pos + self::UINT16 > $this->_size) {
					return false;
				} else {
					$data = unpack('n', substr($this->_buffer, $this->_pos, self::UINT16));
					$this->_pos += self::UINT16;
					return is_array($data) && isset($data[1]) ? $data[1] : false;
				}
			case self::UINT32:
				if($this->_pos + self::UINT32 > $this->_size) {
					return false;
				} else {
					$data = unpack('N', substr($this->_buffer, $this->_pos, self::UINT32));
					$this->_pos += self::UINT32;
					return is_array($data) && isset($data[1]) ? $data[1] : false;
				}
			case self::UINT64:
				if($this->_pos + self::UINT64 > $this->_size) {
					return false;
				} else {
					if(PHP_MAJOR_VERSION >= 5 && PHP_MINOR_VERSION >= 6 && PHP_RELEASE_VERSION >= 3) {
						$data = unpack('J', substr($this->_buffer, $this->_pos, self::UINT64));
						$this->_pos += self::UINT64;
						return is_array($data) && isset($data[1]) ? $data[1] : false;
					} else {
						$hi32 = $this->read(self::UINT32);
						$low32 = $this->read(self::UINT32);
						$data = $hi32 === false || $low32 === false ? false : ($hi32 << 32) | $low32;
						return $data;
					}
				}
			case self::BINARY:
				if($this->_pos + $len > $this->_size) {
					return false;
				} else {
					$data = substr($this->_buffer, $this->_pos, $len);
					$this->_pos += $len;
					return $data;
				}
			case self::STRING:
				$len = $this->read(self::UINT32);
				if($len === false) {
					return false;
				}
				return $this->read(self::BINARY, $len);
			default:
				return false;
		}
	}
}