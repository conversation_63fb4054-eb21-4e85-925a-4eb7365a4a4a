<?php
/**
 * Created by PhpStorm.
 * User: Kevin
 * Date: 2018/5/14
 * Time: 17:39
 * https://mp.weixin.qq.com/wiki?t=resource/res_main&id=mp1421140183
 */

namespace app\components;


use app\jobs\SyncPointGrowJob;
use app\models\by;
use app\models\CUtil;
use Faker\Provider\Uuid;
use yii\db\Exception;

class PointCenter
{
    private static $_instance = [];
    protected $expire_time = YII_ENV_PROD ? 3600 : 60;

    private $dreame_member_center_domain;

    const URL = [
        //商城推送积分/觅享分
        'POINT_GROW_SAVE' => '/dreame-event-center/pointAndGrow/batchSave',      //推送积分(批量增减)
        'SCORE_GET_POINTS_BRIEF' => '/dreame-point-center/int/memberpointgrow/points-brief',   //获取积分明细记录 GET
    ];

    const POINT_GROW_EVENT = [
        'purchase' => 'mall/dreame/offline_purchase_give_point',    //购物积分
        'activity' => 'mall/dreame/offline_activity_give_point',    //活动积分
        'benifits' => 'mall/dreame/offline_benifits_give_point',    //福利积分
    ];
    
    /** @var string[] 积分变动类型 */
    const POINT_CHANGE_TYPE = [
        0 => '待领取',
        1 => '发放',
        2 => '失效',
        3 => '失效',
        4 => '消耗',
    ];

    private function __construct()
    {
        $this->dreame_member_center_domain = CUtil::getConfig('host', 'config', \Yii::$app->id)['iot_host'] ?? '';
    }

    /**
     * @return PointCenter
     */
    public static function factory(): PointCenter
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }


    public function run(string $function, array $args, int $time = 0)
    {
        switch ($function) {
            case 'pointGrowSaveByOrderNo':
                return $this->$function($args['user_id'], $args['order_no'] ?? '', $args['refund_no'] ?? '', $args['type']);
            default:
                return [false, 'function 不存在'];
        }
    }


    /**
     * @param $event
     * @param array $body
     * @param string $method
     * @param array $header
     * @param array $arr
     * @param string $type
     * @return array
     * 根据URL转接数据
     */
    private function __request($event, array $body = [], string $method = 'POST', array $header = [], array $arr = [], string $type = 'json'): array
    {
        // 获取请求的 URL
        $requestUrl = self::URL[$event] ?? '';
        if (empty($requestUrl)) {
            return [false, '事件不存在！'];
        }
        $url = $this->dreame_member_center_domain . $requestUrl;

        // 拼接 URL 参数
        if ($arr) {
            $url .= '?' . http_build_query($arr);
        }

        // 设置请求头部
        $header = [
                "Content-Type:application/json",
                "cache-control:no-cache",
                "Dreame-internal:4E1C3wQHbecgTGrE==",
                "tenantId:" . CUtil::getConfig('tenantId', 'member', MAIN_MODULE),
                "Authorization:ZHJlYW1lX2lvdDpwTmIzUDhBMjZ3TGdyRCRW",
                "Expect: "
        ];
        // 如果是 JSON 类型，则编码请求体
        if ($type == 'json') {
            $body = json_encode($body, 320);
        }

        // 请求超时处理，设置请求的超时时间（例如20秒）
        $timeout = 201;

        // 发送请求
        list($status, $httpCode, $ret, $err) = CommCurl::factory()->Send($url, $body, $header, $method, $timeout);

        // 调试模式下打印日志
        if (!YII_ENV_PROD) {
            CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|header:" . json_encode($header, 320) . "|data:" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "point_center.{$event}");
        }

        // 请求错误时记录错误日志
        if (!$status || $httpCode !== 200 || !empty($err) || !isset($ret['code'])) {
            CUtil::debug("httpcode:{$httpCode}|err:{$err}|url:{$url}|header:" . json_encode($header, 320) . "|data:" . ($type == 'json' ? $body : json_encode($body, 320)) . " | ret:" . json_encode($ret), "point_center.{$event}");
            return [false, '请求失败'];
        }

        // 返回正常结果
        return [$ret['code'] === 0, $ret];
    }


    /**
     * @param $event
     * @param $data
     * @return array
     * 批量推送积分
     */
    public function pointGrowBatchSave($event, $data): array
    {
        if (empty($data) || empty($event)) {
            return [false, '参数错误'];
        }

        $code = self::POINT_GROW_EVENT[$event] ?? '';
        if (empty($code)) {
            CUtil::debug("point_event:{$event}:积分事件不存在~", "err.dreamehome.pointGrowSave");
            return [false, '积分事件不存在'];
        }

        // 组合推送数据
        $pointInfos = array_map(function ($value) {
            $point    = $value['model'] === 'point' ? $value['score'] : 0;
            $grow     = $value['model'] !== 'point' ? $value['score'] : 0;
            $operType = $value['type'] === "add" ? 1 : -1;

            return [
                    'uid'       => $value['uid'],
                    'serialNo'  => $value['push_no'],
                    'timestamp' => $value['ctime'] * 1000,
                    'ext'       => json_encode(
                            ['source' => $value['source'], 'sub_source' => $value['sub_source']],
                            JSON_UNESCAPED_UNICODE
                    ),
                    'grow'      => $grow,
                    'point'     => $point,
                    'operType'  => $operType,
                    'desc'      => $value['desc'] ?? null,
            ];
        }, $data);

        $resData = [];
        foreach (array_chunk($pointInfos, 40) as $chunk) {
            $body = [
                    'code'       => $code,
                    'pointInfos' => $chunk
            ];

            list($s, $response) = $this->__request('POINT_GROW_SAVE', $body, 'POST');
            $resData = array_merge($resData, $response['data'] ?? []);
        }

        return [true, $resData];
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param string $refund_no
     * @param string $type
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 订单积分推送
     */
    public function pointGrowSaveByOrderNo($user_id, $order_no, string $refund_no = '', string $type = 'reduce'): array
    {
        if (!empty($refund_no)) {
            list($success, $event, $value) = $this->GetPointDataByRefundNo($user_id, $refund_no, $type);
        } else {
            list($success, $event, $value) = $this->GetPointDataByOrderNo($user_id, $order_no, $type);
        }

        if (!$success) return [false, $event];
        if ($event == -1) return [true, '无积分'];

        list($status, $result) = $this->pointGrowBatchSave($event, [$value]);

        if (!$status) {
            by::model('MemberCenterLogModel', 'log')->saveLog('pointGrowSave', ['event' => $event, 'data' => [$value]], $result, time());
            CUtil::debug("{$user_id}|{$order_no}|" . var_export($value, 1) . '|积分' . ($type == 'add' ? '补偿' : '扣除') . '失败', 'err.pointGrowSave.order');
            // todo 逻辑待梳理 scoreAccount
            $redisKey = AppCRedisKeys::scoreAccount($user_id);
            by::redis('core')->del($redisKey);
            return [false, $result];
        }

        if (empty($refund_no) && $type !== 'add') {
            by::Ouser()->upIot($user_id, $order_no);
        }

        return [true, '积分记录成功'];
    }


    /**
     * @param $user_id
     * @param $order_no
     * @param $type
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 获取订单积分数据（正向）
     */
    public function GetPointDataByOrderNo($user_id, $order_no, $type = 'reduce'): array
    {
        $user_id  = CUtil::uint($user_id);
        $order_no = trim(strval($order_no));

        $orderInfo = by::Ouser()->CommPackageInfo($user_id, $order_no, true, true, true, false, false);

        if (empty($orderInfo)) {
            !YII_ENV_PROD && CUtil::debug("订单|{$user_id}|{$order_no}|" . var_export($orderInfo, 1), 'err.pointGrowSave.order');
            return [false, '订单信息错误', ''];
        }
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) {
            CUtil::debug("订单|{$user_id}|{$order_no}|用户uid为空", 'err.pointGrowSave.order');
            return [false, '用户信息错误', ''];
        }
        $coin = $orderInfo['coin'] ?? 0;

        if (empty($coin)) return [true, -1, ''];
        $event = 'purchase';
        $value = [
                'model'      => 'point',
                'uid'        => $uid,
                'score'      => $orderInfo['coin'],
                'type'       => $type,
                'push_no'    => $order_no,
                'ctime'      => $orderInfo['ctime'] ?? time(),
                'source'     => '订单来源',
                'sub_source' => '订单' . (($type == 'add') ? '返还' : '扣除'),
                'desc'       => '下单抵扣',
        ];
        return [true, $event, $value];
    }


    /**
     * @param $user_id
     * @param $refund_no
     * @param string $type
     * @return array
     * @throws Exception
     * @throws \RedisException
     * 获取退款积分数据（逆向）
     */
    public function GetPointDataByRefundNo($user_id, $refund_no, string $type = 'add'): array
    {
        $user_id    = CUtil::uint($user_id);
        $refund_no  = trim(strval($refund_no));
        $refundInfo = by::Orefund()->CommPackageInfo($user_id, $refund_no, false, true);
        if (empty($refundInfo)) {
            !YII_ENV_PROD && CUtil::debug("退款订单|{$user_id}|{$refund_no}|" . var_export($refundInfo, 1), 'err.pointGrowSave.order');
            return [false, '订单信息错误', ''];
        }
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) {
            CUtil::debug("退款订单|{$user_id}|{$refund_no}|用户uid为空", 'err.pointGrowSave.order');
            return [false, '用户信息错误', ''];
        }
        $coin = $refundInfo['coin'] ?? 0;
        if (empty($coin)) return [true, -1, ''];
        $event = 'purchase';
        $value = [
                'model'      => 'point',
                'uid'        => $uid,
                'score'      => $refundInfo['coin'],
                'type'       => $type,
                'push_no'    => $refund_no,
                'ctime'      => $refundInfo['ctime'] ?? time(),
                'source'     => '退款订单来源',
                'sub_source' => '退款订单' . (($type == 'add') ? '返还' : '扣除'),
                'desc'       => '退货订单积分返还（原订单有积分抵扣）',
        ];
        return [true, $event, $value];
    }
    
    /**
     * 订单推送-正向（扣减积分）
     * @param $user_id
     * @param $order_no
     * @return void
     */
    public function orderPush($user_id, $order_no)
    {
        \Yii::$app->queue->push(new SyncPointGrowJob(
            [
                'function' => 'pointGrowSaveByOrderNo',
                'user_id' => $user_id,
                'order_no'  => $order_no,
                'refund_no'  => '',
                'type'  => 'reduce',
            ]
        ));
    }
    
    /**
     * 退款推送-逆向（返还积分）
     * @param $user_id
     * @param $refund_no
     * @return void
     */
    public function refundPush($user_id, $refund_no)
    {
        \Yii::$app->queue->push(new SyncPointGrowJob(
            [
                'function' => 'pointGrowSaveByOrderNo',
                'user_id' => $user_id,
                'order_no'  => '',
                'refund_no'  => $refund_no,
                'type'  => 'add',
            ]
        ));
    }

    /**
     * 获取积分明细记录
     * @param int $user_id 用户user_id
     * @param int $page 页码
     * @param int $page_size 条数
     * @return array
     * @throws Exception
     */
    public function getScoreLogByUserId(int $user_id, int $page = 1, int $page_size = 10): array
    {
        $mallInfo = by::usersMall()->getMallInfoByUserId($user_id);
        $uid      = $mallInfo['uid'] ?? '';
        if (empty($uid)) {
            return [false, '用户信息错误'];
        }

        $params = [
                'uid'      => $uid,
                'page'     => $page,
                'size'     => $page_size
        ];

        return $this->__request('SCORE_GET_POINTS_BRIEF', [], 'GET', [], $params);
    }


    /**
     * 会员商城邀请好友赠送积分
     *
     * @param int $userId 用户ID
     * @param int $point 要赠送的积分数量
     * @return array [是否成功, 提示信息, 额外数据]
     * @throws Exception
     */
    public function memberMallInvite(int $userId, int $point): array
    {
        // 常量定义
        $source = '邀请好友';
        $model  = 'point';
        $type   = 'add';

        // 参数验证
        if ($userId <= 0 || $point <= 0) {
            CUtil::debug("会员商城邀请好友|参数错误|userId:{$userId}, point:{$point}", 'err.pointGrowSave.invite');
            return [false, '参数错误，用户ID和积分必须为正数'];
        }

        // 获取商城信息
        $mallInfo = by::usersMall()->getMallInfoByUserId($userId);
        $uid      = $mallInfo['uid'] ?? '';

        // 验证用户UID
        if (empty($uid)) {
            CUtil::debug("会员商城邀请好友|用户UID为空|userId:{$userId}", 'err.pointGrowSave.invite');
            return [false, '用户信息错误，未找到对应的UID', ''];
        }

        // 准备积分数据
        $pointData = [
                'model'      => $model,
                'uid'        => $uid,
                'score'      => $point,
                'type'       => $type,
                'push_no'    => Uuid::uuid(),
                'ctime'      => time(),
                'source'     => $source,
                'sub_source' => $source,
                'desc'       => $source,
        ];

        // 批量保存积分记录
        list($status, $result) = $this->pointGrowBatchSave('activity', [$pointData]);

        // 处理保存结果
        if (!$status) {
            $errorMsg = is_string($result) ? $result : '积分增加失败';
            CUtil::debug("会员商城邀请好友|积分增加失败|userId:{$userId}, uid:{$uid}, error:{$errorMsg}", 'err.pointGrowSave.invite');
            return [false, $errorMsg];
        }

        CUtil::debug("会员商城邀请好友|积分增加成功|userId:{$userId}, uid:{$uid}, point:{$point}", 'info.pointGrowSave.invite');
        return [true, '积分增加成功'];
    }
}
