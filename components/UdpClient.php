<?php
/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2019/4/23
 * Time: 16:34
 */
namespace app\components;

use app\models\CUtil;

final class UdpClient {
    protected static $instance = [];

    protected $udp  = NULL;

    protected $host = NULL;

    protected $port = 0;

    /**
     * @param string $host
     * @param string $port
     * @return UdpClient
     */
    public static function newInstance(string $host='udp.crosscp.com', string $port='10086'): UdpClient
    {
        $unique_key = CUtil::getAllParams($host,$port);

        if(empty(self::$instance[$unique_key])) {
            self::$instance[$unique_key] = new self($host, $port);
        }

        return self::$instance[$unique_key];
    }

    protected function __construct( $host, $port ) {
        $this->udp  = socket_create(AF_INET, SOCK_DGRAM, SOL_UDP);

        $this->host = $host;

        $this->port = $port;
    }

    private function __clone()
    {
        // TODO: Implement __clone() method.
    }

    /**
     * MSG_CONFIRM 提供链路层反馈以保持地址映射有效
     * MSG_DONTROUTE 勿将数据包路由出本地网络
     * MSG_DONTWAIT 允许非阻塞行为 - -
     * MSG_EOF 标记记录结束
     * MSG_MORE 允许延迟并写更多数据
     * MSG_NOSIGNAL 在无连接的套接字不产生信号SIGPIPE
     * MSG_OOB 允许发送带外数据
     * 对于报文数据发送，假如报文过长会导致错误。
     * 对于字节流，则会阻塞到数据发送完成。
     * 往服务器发送数据
     * @param string $data
     * @return bool
     */
    public function sendData($data=""): bool
    {
        if(empty($data)){
            return false;
        }

        if(!is_string($data)) {
            $data = strval($data);
        }

        $send_length = 0;
        if(is_resource($this->udp))  {
            //判断是windows还是linux
            $flags = strtoupper(substr(PHP_OS,0,3))==='WIN' ? 0 : MSG_EOF;

            $send_length = @socket_sendto( $this->udp, $data, strlen($data), $flags, $this->host, $this->port );
            //socket_close($this->udp);
        }

        return $send_length > 0 ;
    }
}