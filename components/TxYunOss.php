<?php
/**
 * Created by PhpStorm.
 * User: Administrator
 * Date: 2021/7/21
 * Time: 19:58
 */

namespace app\components;

use app\models\CUtil;
use Qcloud\Cos\Client;

require_once \Yii::getAlias("@vendor") . '/mycompany/txyun-oss-sdk/vendor/autoload.php';

class TxYunOss
{

	protected static $_instance = [];
	CONST IMG_TYPE = ['jpg', 'jpeg', 'gif', 'png'];//图片格式
	//base64二进制获取的图片格式
	CONST B_IMG_TYPE = [
		'data:image/jpeg' => 'jpg',
		'data:image/jpg'  => 'jpg',
		'data:image/png'  => 'png',
		'data:image/gif'  => 'gif'
	];

	//定义允许上传的类型
	public static function getAllowType() {
		return self::IMG_TYPE;
	}


    /**
     * @param int $width
     * @param int $height
     * @return string
     * 图片缩放
     * https://cloud.tencent.com/document/product/460/36380
     * https://cloud.tencent.com/document/product/460/36540
     */
    public static function XOssResizeUriLink($width=300,$height=300){
        return "?imageMogr2/thumbnail/{$width}x{$height}";
    }

	/**
	 * @param null $object
	 * @return bool|string
	 * 读取文件类型
	 */
	public static function getFileType($object = null)
	{

		if (is_null($object)) {
			return false;
		}
		return strtolower(substr($object, strrpos($object, '.') + 1)); //得到文件类型，并且都转化成小写
	}

	/**
	 * @param $date
	 * @param null $object
	 * @return array
	 * 获取指定Bucket下的目录
	 */
	public static function getBucketDirByDay($date, $object = null)
	{

		$config = CUtil::getConfig('TxYunOss', 'common', MAIN_MODULE);
		$type   = self::getFileType($object);
		switch ($type) {
			case in_array($type, self::IMG_TYPE)   :
			default :
				$sub_bucket = $config['imgBucket'];
				break;
		}

		if (empty($sub_bucket)) {
			return [false, "BUCKET Not Exist"];
		}

		$date      = $date ? date("Ymd", strtotime($date)) : date("Ymd");
		$bucketDir = "{$sub_bucket}/" . date("Ym", strtotime($date));

		return [true, $bucketDir];
	}

	/**
	 * @return TxYunOss
	 * 单例模式
	 */
	public static function factory()
	{
		if (!isset(self::$_instance['TxYunOss']) || !is_object(self::$_instance['TxYunOss'])) {
			self::$_instance['TxYunOss'] = new self();
		}
		return self::$_instance['TxYunOss'];
	}


	/**
	 * @return string
	 * 生成简短的唯一流水号
	 */
	public function CreateOrderId () {
		$my_pid             = str_pad(substr(getmypid(),0,5), 5, '0' , STR_PAD_LEFT);
		$uniqid             = uniqid();
		list($sec,$mic)     = explode(".",microtime(true));
		$mic                = str_pad(substr($mic,0,3), 3, '0');
		$rand               = mt_rand(10,99);
		usleep(1000); //休眠1毫秒，保证序列号唯一

		return $uniqid . $mic . $my_pid . $rand;
	}


    /**
     * @param string $module
     * @param string $config_key
     * @param string $config_name
     * 获取oss client实例
     * @return Client
     *
     */
	public static function OssClient($module = MAIN_MODULE, $config_key = 'TxYunOss', $config_name = 'common'){
		if (!$module) {
			return null;
		}

		if (!isset(self::$_instance[$module]['OssClient']) || !is_object(self::$_instance[$module]['OssClient'])) {
			$config    = CUtil::getConfig($config_key, $config_name, $module);
			$secretId  = isset($config['AccessKeyId']) ? $config['AccessKeyId'] : "";           //"云 API 密钥 SecretId";
			$secretKey = isset($config['AccessKeySecret']) ? $config['AccessKeySecret'] : "";   //"云 API 密钥 SecretKey";
			$region    = isset($config['region']) ? $config['region'] : "gz";                   //设置一个默认的存储桶地域
			$schema    = isset($config['schema']) ? $config['schema'] : "https";
			$cosClient = new Client(
				[
                    'region'      => $region,
                    'schema'      => $schema, //协议头部，默认为http
                    'credentials' => [
                        'secretId'  => $secretId,
                        'secretKey' => $secretKey
                    ]
                ]
			);

			self::$_instance[$module]['OssClient'] = $cosClient;
		}
		return self::$_instance[$module]['OssClient'];

	}


	/**
	 * @param $file : 二进制文件 (Content-Type: multipart/form-data)
	 * @param array $allow_type : 允许类型
	 * @param bool $resize : 是否缩放
	 * @return array
	 * 图片上传  直接返回拼接好的远程Oss地址【生成一张原图和一张小图地址】
	 * 文件信息追加至Redis队列 由Redis队列负责真正上传
	 * https://help.aliyun.com/document_detail/44688.html
	 */
	public function uploadFileToOss($file,$allow_type=[],$resize=true) {

			$time = time();
			list($status,$bucket_dir)  = self::getBucketDirByDay(date("Ymd",$time));
			if(!$status) {
				throw new \Exception($bucket_dir);
			}

			if(empty($file)) {
				throw new \Exception("图片未上传");
			}

			//判断是否是通过HTTP POST上传的
			if(!is_uploaded_file($file['tmp_name'])){
				throw new \Exception("仅允许HTTP POST上传");
			}
			$size = sprintf('%.2f',$file['size'] / (1024 *1024));
			if($size > 20) {
				throw new \Exception("图片最大不超过20M");
			}

			$name  = $file['name'];
			$valid = @getimagesize($file['tmp_name']);
			if($valid === false) {
				throw new \Exception("文件格式错误");
			}


			$type       = strtolower(substr($name,strrpos($name,'.')+1)); //得到文件类型，并且都转化成小写
			$allow_type = empty($allow_type) ? self::getAllowType() : $allow_type;
			if(!in_array($type, $allow_type)){
				throw new \Exception("非法的文件类型：{$type};仅允许文件格式：".json_encode(self::getAllowType()));
			}

			//开始移动文件到相应的文件夹
			if(!is_dir(UPLOAD_TMP_PATH) ) {
				$oldumask = umask(0);
				mkdir(UPLOAD_TMP_PATH,0777,true);
				umask($oldumask);
			}

			//新文件名
			$object = self::CreateOrderId().'.'.$type;
			$upload_file = UPLOAD_TMP_PATH.'/'.$object; //上传文件
			if(move_uploaded_file($file['tmp_name'],$upload_file)){
				//异步redis队列 维护上传至OSS
				$config           = CUtil::getConfig('TxYunOss','common',MAIN_MODULE);

				$oss_uri          = $config['cdnAddr'].'/'.$bucket_dir.'/';
				$originImg        = $oss_uri.$object;
				$link['bigImg']   = $originImg;
				$link['smallImg'] = $originImg;

				//缩放
				$resize && $link['smallImg'] = $originImg.self::XOssResizeUriLink(300,300);
				$file = fopen($upload_file, "rb");
				if ($file){
					//todo send to oss
					$OssClient  = self::OssClient(MAIN_MODULE);
                    $OssClient->putObject(array(
						'Bucket' => $config['bucket'],
						'Key'    => $bucket_dir . '/' . $object,
						'Body'   => $file,
					));
				}
				@unlink($upload_file);//删除本地服务器临时文件
				return [true, $link];
			} else {
				throw new \Exception("图片上传失败");
			}
	}



}