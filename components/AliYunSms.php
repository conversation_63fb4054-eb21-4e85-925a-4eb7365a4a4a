<?php
/**
 * Created by PhpStorm.
 * User: Fantasy
 * Date: 2022/5/24
 * Time: 11:56
 */

namespace app\components;

use <PERSON><PERSON>\Api\Sms\Request\V20170525\SendBatchSmsRequest;
use <PERSON><PERSON>\Api\Sms\Request\V20170525\SendSmsRequest;
use <PERSON><PERSON>\Core\Config;
use <PERSON>yun\Core\DefaultAcsClient;
use <PERSON>yun\Core\Profile\DefaultProfile;
use app\models\CUtil;
use Yii;

require_once Yii::getAlias("@vendor")."/mycompany/aliyun-sms-sdk/vendor/autoload.php";

// 加载区域结点配置
Config::load();

class AliYunSms {

    // 批量发送短信的上限：100
    const BATCH_SEND_LIMIT = 100;

    protected static $acsClient = null;

    //验证码模板
    const TEMPLATE_CODE = [
        'admin'                  => 'SMS_286220835', //后台验证码
        'code'                   => 'SMS_467110342', //验证码
        'reg'                    => 'SMS_467030354', //邀请好友
        'bind'                   => 'SMS_466955401', //绑定成功
        'again_bind'             => 'SMS_467345059', //再次绑定
        'order_presale_start'    => 'SMS_467130398', //预售开始
        'order_presale_end'      => 'SMS_467180330', //预售结束
        'activate_inform'        => 'SMS_467415106', //先试后买 用户收到货2天未激活
        'try_end_inform'         => 'SMS_467585115', //试用结束提醒  提前2天
        'return_product_inform'  => 'SMS_468845067', //退机有效期剩余1天
        'trade_in_order_confirm' => 'SMS_467075354 ',//旧机核验结果通知
        'refund_sms'             => 'SMS_480260115 ',//退款短信模版
        'parking_coupon'        => 'SMS_478440803', //停车券发放通知
        'jd_e_card'             => 'SMS_486570207 ',//京东E卡
    ];

    protected function __construct()
    {

    }

    public static function getAcsClient()
    {
        if (self::$acsClient === null) {

            $config = CUtil::getConfig('aliyunsms','common',MAIN_MODULE);

            //初始化acsClient,暂不支持region化
            $profile = DefaultProfile::getProfile($config['Region'], $config['AccessKeyId'], $config['AccessKeySecret']);

            // 增加服务结点
            DefaultProfile::addEndpoint($config['EndPointName'], $config['Region'], $config['Product'], $config['Domain']);

            // 初始化AcsClient用于发起请求
            static::$acsClient = new DefaultAcsClient($profile);
        }

        return self::$acsClient;
    }


    /**
     * @param null $phone
     * @param null $tpl_code
     * @param array $tmp
     * @return array
     * 发送短信验证码
     * 同一个签名，对同一个手机号的发送频率
     * 1分钟内短信发送条数不超过： 1
     * 1小时内短信发送条数不超过： 5
     * 1个自然日内短信发送条数不超过：10
     * https://dysms.console.aliyun.com/dysms.htm?spm=5176.8195934.1283918..52c230c9ZHPVZm#/system/setting/frequency
     */
    public static function sendSms($phone = null, $tpl_code = null, array $tmp = []): array
    {
        if(!CUtil::reg_valid($phone,CUtil::REG_PHONE)) {
            return [false,'非法手机号'];
        }

        if(!in_array($tpl_code,self::TEMPLATE_CODE)) {
            return [false,'模板不合法(不存在或被拉黑:1)'];
        }

        $config = CUtil::getConfig('aliyunsms','common',MAIN_MODULE);

        // 初始化SendSmsRequest实例用于设置发送短信的参数
        $request = new SendSmsRequest();

        //可选-启用https协议
        //$request->setProtocol("https");

        // 必填，设置短信接收号码
        $request->setPhoneNumbers($phone);

        // 必填，设置签名名称，应严格按"签名名称"填写，请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/sign
        $request->setSignName($config['SignName']);

        // 必填，设置模板CODE，应严格按"模板CODE"填写, 请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/template
        $request->setTemplateCode($tpl_code);

        // 可选，设置模板参数, 假如模板中存在变量需要替换则为必填项
        // tmp = ['code'=>123456] ;//短信验证码
        if(!empty($tmp)) {
            $request->setTemplateParam(json_encode($tmp, JSON_UNESCAPED_UNICODE));
        }


        // 可选，设置流水号
        //$request->setOutId("yourOutId");

        // 选填，上行短信扩展码（扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段）
        //$request->setSmsUpExtendCode("1234567");

        // 发起访问请求
        $acsResponse = self::getAcsClient()->getAcsResponse($request);

        if($acsResponse->Code == "OK") {
            return [true,$acsResponse->BizId];
        }

        return [false,$acsResponse->Message];
    }

    /**
     * 批量发送短信，单次最多支持100个号码。
     * 参考文档：https://help.aliyun.com/document_detail/419274.html
     * 同一个签名，对同一个手机号的发送频率
     * 1分钟内短信发送条数不超过： 1
     * 1小时内短信发送条数不超过： 5
     * 1个自然日内短信发送条数不超过：10
     */
    public static function sendBatchSms(array $phones, $tpl_code, array $tpl_params = []): array
    {
        // 检查手机号
        list($status, $res) = self::__checkPhones($phones);
        if (!$status) {
            return [false, $res];
        }

        // 检查手机号数量 与 模板参数数量 是否相等
        if (!empty($tpl_params) && (count($phones) != count($tpl_params))) {
            return [false, '手机号数量与参数数量不一致'];
        }

        // 初始化SendBatchSmsRequest()实例用于设置发送短信的参数
        $request = new SendBatchSmsRequest();

        // 必填，设置短信接收号码
        $request->setPhoneNumberJson(json_encode($phones));

        // 必填，设置签名名称，应严格按"签名名称"填写，请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/sign
        $request->setSignNameJson(self::__getSignNameJson(count($phones)));

        // 必填，设置模板CODE，应严格按"模板CODE"填写, 请参考: https://dysms.console.aliyun.com/dysms.htm#/develop/template
        $request->setTemplateCode($tpl_code);

        // 选填，设置模板变量
        if (!empty($tpl_params)) {
            $request->setTemplateParamJson(json_encode($tpl_params));
        }

        // 发起访问请求
        $acsResponse = self::getAcsClient()->getAcsResponse($request);

        if ($acsResponse->Code == "OK") {
            return [true, $acsResponse->BizId];
        }

        return [false, $acsResponse->Message];
    }

    /**
     * 校验手机号
     * @param array $phones
     * @return array
     */
    private static function __checkPhones(array $phones): array
    {
        // 判空
        if (empty($phones)) {
            return [false, '手机号为空'];
        }

        // 上限
        if (count($phones) > self::BATCH_SEND_LIMIT) {
            return [false, sprintf("手机号超过%s条", self::BATCH_SEND_LIMIT)];
        }

        // 验证格式
        foreach ($phones as $phone) {
            if (!CUtil::reg_valid($phone, CUtil::REG_PHONE)) {
                return [false, '存在非法手机号'];
            }
        }

        return [true, ''];
    }

    /**
     * 获取短信签名
     * @param int $num
     * @return string
     */
    private static function __getSignNameJson(int $num): string
    {
        $config = CUtil::getConfig('aliyunsms', 'common', MAIN_MODULE);
        $signName = $config['SignName'];
        $data = [];
        for ($i = 1; $i <= $num; $i++) {
            $data[] = $signName;
        }
        return json_encode($data);
    }
}
