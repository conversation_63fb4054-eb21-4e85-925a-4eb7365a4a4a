<?php

namespace app\components;

use app\models\by;
use app\models\CUtil;
use RedisException;
use yii\db\Exception;


require_once \Yii::getAlia<PERSON>("@vendor") . '/mycompany/weworkapi_php/callback/WXBizMsgCrypt.php';

/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/8/13
 * Time: 11:49
 * 企业微信功能
 * https://work.weixin.qq.com/api/doc/90000/90135/92130
 */
class WeWork extends Tencent
{

    protected $groupName = '来源门店store';

    protected $tryGroupName = '先试后买';

    //先试后买节点标签
    const TAG = [
        'SUBMIT_SURVEY'  => 'etG8AzTgAAxSpe-oVfVk_in_4T1Di8HA',
        'PASS_SURVEY'    => 'etG8AzTgAAW4X4Gnr8NQhutmAY_38j_A',
        'NO_PASS_SURVEY' => 'etG8AzTgAAn2yZMxmgX55xBS8C_6Pr5Q',
        'SEND'           => 'etG8AzTgAAm8U9IcAMuHwDCwWnDFxszA'
    ];

    const CUSTOMER_SERVICE_USERID = [
        '19942083945',
        '19941903049',
        '19951019314',
        '19951149264',
        '19941904372',
        '18114423637',
        '18114910343'
    ];

    //必要的单例模式
    private function __construct()
    {
    }

    private function __clone()
    {
    }

    private $_obj = [];

    /**
     * @return WeWork
     * */
    public static function factory()
    {
        if (!isset(self::$_instance[__CLASS__]) || !is_object(self::$_instance[__CLASS__])) {
            self::$_instance [__CLASS__] = new static();
        }

        return self::$_instance [__CLASS__];
    }

    /**
     * @return \WXBizMsgCrypt
     */
    public function WXBizMsgCrypt()
    {
        if (!isset($this->_obj[__FUNCTION__]) || !is_object($this->_obj[__FUNCTION__])) {

            $config = CUtil::getConfig('wework', 'common', MAIN_MODULE);
            $token = $config['token'] ?? "";
            $corpId = $config['corpId'] ?? "";
            $encodingAESKey = $config['encodingAESKey'] ?? "";

            $this->_obj[__FUNCTION__] = new \WXBizMsgCrypt($token, $encodingAESKey, $corpId);
        }

        return $this->_obj[__FUNCTION__];
    }


    /**
     * @param string $secret : 指定私钥
     * @param int $AgentId : 应用ID
     * @param string $corpId : 指定企业,为空走默认配置企业
     * @return false|mixed|string
     * 获取access_token
     * https://work.weixin.qq.com/api/doc/90000/90135/91039
     */
    public function GetAccessToken($secret = "", $AgentId = 0, $corpId = "")
    {
        if (!(YII_ENV == 'prod' || YII_ENV == 'uat')) {
            return "";
        }
        $redis  = by::redis('core');
        $config = CUtil::getConfig('wework', 'common', MAIN_MODULE);
        $corpId = empty($corpId) ? ($config['corpId'] ?? "") : $corpId;
        $secret = empty($secret) ? ($config['secret'] ?? "") : $secret;
        $r_key  = AppCRedisKeys::WeworkAccessToKen($corpId, $AgentId);
        $token  = $redis->get($r_key);

        if (empty($token)) {
            $unique_key = CUtil::getAllParams(__CLASS__, __FUNCTION__);
            list($free) = by::users()->ReqAntiConcurrency(0, $unique_key, 3, "EX");
            if (!$free) {
                sleep(1);
                $token = $redis->get($r_key);
                if (!empty($token)) {
                    return $token;
                }
            }

            $url = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={$corpId}&corpsecret={$secret}";

            $aJson = CUtil::curl_get($url);
            $aData = (array)json_decode($aJson, true);
            $token = $aData['access_token'] ?? "";
            $exp   = $aData['expires_in'] ?? "";

            $redis->set($r_key, $token, ['EX' => empty($token) ? 10 : $exp]);
        }

        return $token;
    }


    /**
     * @param $userID
     * @return array
     *  https://developer.work.weixin.qq.com/document/path/90196
     * 获取客户详情
     */
    public function getUser($userID)
    {
        $token = $this->GetAccessToken();
        if (empty($token)) {
            return [false, "获取通信凭证失败"];
        }

        $url = "https://qyapi.weixin.qq.com/cgi-bin/user/get?access_token={$token}&userid={$userID}";
        $aJson = CUtil::curl_get($url);
        $aData = (array)json_decode($aJson, true);

        $errcode = $aData['errcode'] ?? -1;
        $errmsg = $aData['errmsg'] ?? "ERR";
        if ($errcode != 0) {
            return [false, "{$errmsg}({$errcode})"];


        }

        if (empty($aData['userid'])) {
            return [false, "获取客户信息失败"];
        }

        return [true, $aData];
    }

    /**
     * @param $ID
     * @return array
     * https://developer.work.weixin.qq.com/document/path/95351
     * 获取部门详情
     */
    public function getDepartment($ID)
    {
        $token = $this->GetAccessToken();
        if (empty($token)) {
            return [false, "获取通信凭证失败"];
        }

        $url = "https://qyapi.weixin.qq.com/cgi-bin/department/get?access_token={$token}&id={$ID}";
        $aJson = CUtil::curl_get($url);
        $aData = (array)json_decode($aJson, true);

        $errcode = $aData['errcode'] ?? -1;
        $errmsg = $aData['errmsg'] ?? "ERR";
        if ($errcode != 0) {
            return [false, "{$errmsg}({$errcode})"];
        }
        $contact = $aData['department'] ?? [];
        if (empty($contact)) {
            return [false, "获取部门信息失败"];
        }

        return [true, $contact];
    }


    /**
     * https://developer.work.weixin.qq.com/document/path/90930
     * @param $params
     * @return string
     * 企业微信事件回调地址验签
     */
    public function weComSign($params): string
    {
        // 获取请求参数
        $timestamp = $params['timestamp'] ?? "";
        $nonce     = $params['nonce'] ?? "";
        $signature = $params['msg_signature'] ?? "";
        $echostr   = $params['echostr'] ?? "";

        $config         = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
        $corpId         = $config['corpId'] ?? "";
        $encodingAESKey = $config['encodingAESKey'] ?? "";
        $token          = $config['token'] ?? "";

        // 需要返回的明文
        $sEchoStr = "";
        $wxcpt    = new \WXBizMsgCrypt($token, $encodingAESKey, $corpId);
        $errCode  = $wxcpt->VerifyURL($signature, $timestamp, $nonce, $echostr, $sEchoStr);

        if ($errCode != 0) {
            // 验证失败，可以根据需求进行处理，比如记录日志
            CUtil::debug('获取客户详情失败|' . $errCode, "err.msg.ADD_WECHAT.send");
            // 返回错误信息或错误码，或者其他适当的响应
            return "验证失败，错误码：" . $errCode;
        }

        // 验证通过，返回明文消息内容
        return trim($sEchoStr, "\"\xEF\xBB\xBF\r\n");
    }


    /**
     * @param string $accessToken
     * @return array|mixed
     * @throws RedisException
     * 企微存量数据同步商城
     */
    public function getFollowUserList(string $accessToken = '')
    {
        if (empty($accessToken)) {
            return [];
        }

        $url      = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get_follow_user_list?access_token={$accessToken}";
        $response = CUtil::curl_get($url);
        $data     = json_decode($response, true);

        // 检查是否有错误
        $errCode = $data['errcode'] ?? 0;
        if ($errCode !== 0) {
            CUtil::debug("获取客户联系功能的成员列表失败 | access_token: {$accessToken}", "err.WE_FOLLOW_USER_LIST.info");

            // 如果错误代码是42001、40014（AccessToken失效），尝试重新获取AccessToken
            if (in_array($errCode, [42001, 40014])) {
                $this->__delAccessTokenKey();
                $config  = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
                $corpId  = $config['corpId'] ?? "";
                $secret  = $config['secret'] ?? "";
                $agentId = $config['agentId'] ?? "";
                $token   = $this->GetAccessToken($secret, $agentId, $corpId);
                // 重新获取AccessToken后，可以递归调用本函数来获取跟进用户列表
                return $this->getFollowUserList($token); // 请根据您的实际情况传递AccessToken变量名
            }
        }

        // 提取跟进用户列表
        return $data['follow_user'] ?? [];
    }


    /**
     * @param array $queryParams
     * @param string $notify_data
     * @return array
     * @throws Exception
     * @throws RedisException
     */
    public function saveSourceAddWeChat(array $queryParams = [], string $notify_data = ''): array
    {
        list($errCode, $data) = $this->decrypt($queryParams, $notify_data);
        if ($errCode != 0 && YII_ENV_PROD) {
            CUtil::debug('添加事件消息解密失败|' . $errCode, "err.msg.ADD_WECHAT.send");
        }

        $data = CUtil::xmlToArray($data);
        if (isset($data['ChangeType']) && $data['ChangeType'] == 'add_external_contact') {
            $config      = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
            $corpId      = $config['corpId'] ?? "";
            $secret      = $config['secret'] ?? "";
            $agentId     = $config['agentId'] ?? "";
            $accessToken = WeWork::factory()->GetAccessToken($secret, $agentId, $corpId);
            //用户企微ID
            $externalUserID = $data['ExternalUserID'] ?? '';
            //用户详情信息
            $return = $this->getWeUserInfo($accessToken, $externalUserID);
            //用户开放平台唯一ID
            $unionId = $return['external_contact']['unionid'] ?? '';
            //处理标签
            $tags = array_column($return['follow_user'], 'tags');
            //门店名称
            $storeName = $this->getStoreName($tags, $this->groupName);
            //推广来源
            $sourceName = $this->getLatestTag($return['follow_user']);
            $save       = [
                'we_openid' => $externalUserID,
                'unionid'   => $unionId,
                'store'     => $storeName ?? '',
                'source'    => $sourceName ?? '',
                'resources' => json_encode($return, 320),
                'ctime'     => intval(START_TIME),
            ];
            //入库操作  unionid唯一  订单主表中加标识 区分企业微信来源的订单（生成订单时查询此用户是否存在t_we_focus表中）
            by::WeFocus()->saveLog($save);
            //推送事件添加觅享官
            $this->sendAddWeChat($unionId);
        }
        return [true, 'ok'];
    }


    /**
     * @param $accessToken
     * @param $externalUserID
     * @return array
     * 获取客户详情
     * @throws RedisException
     */
    public function getWeUserInfo($accessToken, $externalUserID): array
    {
        $url     = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/get?access_token={$accessToken}&external_userid={$externalUserID}";
        $aJson   = CUtil::curl_get($url);
        $aData   = (array)json_decode($aJson, true);
        $errCode = $aData['errcode'] ?? '';
        $errMsg  = $aData['errmsg'] ?? '';
        if ($errCode != 0) {
            // CUtil::debug('获取获取客户详情失败|' . 'access_token:' . $accessToken . '|errCode:' . $errCode . "|errMsg:" . $errMsg . "|external_userid:" . $externalUserID, "err.WE_USER_INFO.info");
            CUtil::setLogMsg(
                "err.WE_USER_INFO.info",
                [
                    'accessToken'   => $accessToken,
                    'externalUserID' => $externalUserID
                ],
                $aData,
                [],
                $url,
                '获取获取客户详情失败',
                [],
                200
            );
            // 如果错误代码是42001、40014（AccessToken失效），尝试重新获取AccessToken
            if (in_array($errCode, [42001, 40014])) {
                $this->__delAccessTokenKey();
                $config  = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
                $corpId  = $config['corpId'] ?? "";
                $secret  = $config['secret'] ?? "";
                $agentId = $config['agentId'] ?? "";
                $token   = $this->GetAccessToken($secret, $agentId, $corpId);
                // 重新获取AccessToken后，可以递归调用本函数来获取跟进用户列表
                return $this->getWeUserInfo($token, $externalUserID); // 请根据您的实际情况传递AccessToken变量名
            }
        }
        return [
            'external_contact' => $aData['external_contact'] ?? [],
            'follow_user'      => $aData['follow_user'] ?? [],
        ];
    }

    /**
     * @param $stores
     * @param $needName
     * @return null
     * 处理标签获取门店名称
     */
    public function getStoreName($stores, $needName)
    {
        foreach ($stores as $group) {
            foreach ($group as $tag) {
                if ($tag['group_name'] == $needName) {
                    // 找到了目标 group_name，返回对应的 tag_name
                    return $tag['tag_name'];
                }
            }
        }

        // 如果没有找到匹配的 group_name，返回一个默认值
        return null; // 或者抛出异常
    }


    /**
     * @param $params
     * @param $notify_data
     * @return array
     * 解密消息
     */
    public function decrypt($params, $notify_data): array
    {
        $timestamp = $params['timestamp'] ?? "";
        $nonce     = $params['nonce'] ?? "";
        $signature = $params['msg_signature'] ?? "";

        $config         = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
        $corpId         = $config['corpId'] ?? "";
        $encodingAESKey = $config['encodingAESKey'] ?? "";
        $token          = $config['token'] ?? "";
        $wxcpt          = new \WXBizMsgCrypt($token, $encodingAESKey, $corpId);
        $data           = "";
        $errCode        = $wxcpt->DecryptMsg($signature, $timestamp, $nonce, $notify_data, $data);
        return [$errCode, $data];
    }

    /**
     * @throws RedisException
     * 清除accessToken缓存
     */
    public function __delAccessTokenKey()
    {
        $config = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
        $corpId = $config['corpId'] ?? "";
        $secret = $config['secret'] ?? "";
        $agentId  = $config['agentId'] ?? "";
        $redis = by::redis();
        $redisKey = AppCRedisKeys::WeworkAccessToKen($corpId, $agentId);
        $redis->del($redisKey);
    }


    /**
     * @param $accessToken
     * @param $userId
     * @return array
     * @throws Exception
     * @throws RedisException
     * 通过心享官id获取用户列表
     */
    public function getClientList($accessToken, $userId): array
    {
        $url     = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/list?access_token={$accessToken}&userid={$userId}";
        $aJson   = CUtil::curl_get($url);
        $aData   = (array)json_decode($aJson, true);
        $errCode = $aData['errcode'] ?? '';

        if ($errCode != 0) {
            // CUtil::debug('获取客户列表失败|' . 'accessToken:' . $accessToken . '|errCode:' . $errCode . '|user_id:' . $userId, "err.GET_CLIENT_LIST.info");
            CUtil::setLogMsg(
                "err.GET_CLIENT_LIST.info",
                [
                    'accessToken' => $accessToken,
                    'userId'      => $userId
                ],
                $aData,
                [],
                $url,
                '获取客户列表失败',
                [],
                200
            );

            // 如果错误代码是42001、40014（AccessToken失效），尝试重新获取AccessToken
            if (in_array($errCode, [42001, 40014])) {
                $this->__delAccessTokenKey();
                $config  = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
                $corpId  = $config['corpId'] ?? "";
                $secret  = $config['secret'] ?? "";
                $agentId = $config['agentId'] ?? "";
                $token   = $this->GetAccessToken($secret, $agentId, $corpId);
                // 重新获取AccessToken后，可以递归调用本函数来获取跟进用户列表
                return $this->getClientList($token, $userId); // 请根据您的实际情况传递AccessToken变量名
            }
        }
        return $aData['external_userid'] ?? [];
    }


    /**
     * @param $externalUserIds
     * @param $accessToken
     * @return void
     * 存量数据落库
     */
    public function saveLog($externalUserIds,$accessToken)
    {
        foreach ($externalUserIds as $externalUserId){
            //用户详情信息
            $return = $this->getWeUserInfo($accessToken, $externalUserId);
            //用户开放平台唯一ID
            $unionId = $return['external_contact']['unionid'] ?? '';
            //处理标签
            $tags = array_column($return['follow_user'], 'tags');
            //门店名称
            $storeName = $this->getStoreName($tags, $this->groupName);
            $save = [
                'we_openid'  => $externalUserId,
                'unionid'    => $unionId,
                'store'      => $storeName ?? '',
                'resources'  => json_encode($return, 320),
                'ctime'      => intval(START_TIME)
            ];
            //入库操作  unionId唯一  订单主表中加标识 区分企业微信来源的订单（生成订单时查询此用户是否存在t_we_focus表中）
            by::WeFocus()->saveLog($save);
        }
    }

    /**
     * @param $unionId
     * @return void
     * @throws Exception
     * @throws RedisException
     * 发送添加觅享官事件
     */
    public function sendAddWeChat($unionId)
    {
        //推送事件添加觅享官
        $user_id = by::users()->getUserIdByUnionId($unionId, 1) ?? '';
        if ($user_id) {
            EventMsg::factory()->run('addWechat', ['user_id' => $user_id]);
        }
        //没有找到用户不推送事件
    }


    /**
     * @throws Exception
     * @throws RedisException
     * 企业微信存量用户同步
     */
    public function syncHistoryData()
    {
        $config            = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
        $corpId            = $config['corpId'] ?? "";
        $secret            = $config['secret'] ?? "";
        $agentId           = $config['agentId'] ?? "";
        $followAccessToken = $this->GetAccessToken($secret, $agentId, $corpId);

        $followUser = $this->getFollowUserList($followAccessToken);

        $finishFollowUser = [
            'NingLeWen',
            'CaiShiSi',
            'XiaoHao-ShiLiu',
            'xinxiangguan001',
            'ZhaoSiYu',
            'huiyuan2',
            'huiyuan3',
            '19951243732',
            '19951328653',
            'HuTianTian',
            'huiyuan1',
            'xinxiangguan002',
            'xinxiangguan003',
            'xinxiangguan004',
            'xinxiangguan005',
            'xinxiangguan006',
            'xinxiangguan17',
            'QiQi',
        ];

        foreach ($followUser as $item) {
            if (in_array($item, $finishFollowUser)) {
                continue;
            }
            print_r($item);
            print_r(PHP_EOL);
            $accessToken     = $this->GetAccessToken($secret, $agentId, $corpId);
            $externalUserIds = $this->getClientList($accessToken, $item);
            $this->saveLog($externalUserIds, $accessToken);
        }
    }


    /**
     * @param $data
     * @return mixed|string
     * 获取最新的客户来源标签
     */
    public function getLatestTag($data)
    {
        $latestTag        = '';
        $latestCreateTime = 0;

        // 遍历 follow_user 列表
        foreach ($data as $user) {
            // 判断是否有 tags 数据
            if (isset($user['tags'])) {
                foreach ($user['tags'] as $tag) {
                    // 检查 group_name 是否为 "客户来源"
                    if ($tag['group_name'] === $this->tryGroupName) {
                        // 如果当前 createtime 大于等于最新的 createtime
                        if ($user['createtime'] >= $latestCreateTime) {
                            $latestTag        = $tag['tag_name'];
                            $latestCreateTime = $user['createtime'];
                        }
                    }
                }
            }
        }

        return empty($latestTag) ? '' : $latestTag;
    }


    public function markTag($userId = '', $tagScene = ''): array
    {
        if (empty($userId) || empty($tagScene) || !array_key_exists($tagScene, self::TAG)) {
            return [false, '参数错误'];
        }

        try {
            // 1.获取用户external_userid
            $user           = by::users()->getRealMainUserById($userId);
            $unionId        = $user['unionid'] ?? '';
            $oaInfo         = by::WeFocus()->getOaInfoByUnionId($unionId);
            $resources      = json_decode($oaInfo['resources'] ?? '', true);
            $externalUserid = $resources['external_contact']['external_userid'] ?? '';
            if (empty($externalUserid)) {
                throw new \Exception("当前用户没有添加心享官(1) user_id:" . $userId);
            }

            // 2.获取access_token
            $config  = CUtil::getConfig('new_wework', 'common', MAIN_MODULE);
            $corpId  = $config['corpId'] ?? "";
            $secret  = $config['secret'] ?? "";
            $agentId = $config['agentId'] ?? "";
            $token   = $this->GetAccessToken($secret, $agentId, $corpId);

            // 3.获取用户添加的企微客服
            $userInfo        = $this->getWeUserInfo($token, $externalUserid);
            $followUserIds = array_column($userInfo['follow_user'], 'userid');
            // 随机获取用研客服中一个
            $customerService = array_values(array_intersect($followUserIds,self::CUSTOMER_SERVICE_USERID))[0] ?? '';

            if (empty($customerService)) {
                throw new \Exception("当前用户没有添加心享官(2) external_userid:" . $externalUserid);
            }

            // 4.给用户打标签
            list($status, $ret) = $this->markUserTag($token, $customerService, $externalUserid, $tagScene);
            if (!$status) {
                throw new \Exception($ret);
            }

            return [true, 'ok'];
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'warn.mark_tag');
            return [false, '标添加签失败'];
        }
    }

    /**
     * @param $token
     * @param $customerService
     * @param $externalUserid
     * @param $tagScene
     * @return array
     * 打标签
     */
    private function markUserTag($token, $customerService, $externalUserid, $tagScene): array
    {
        $url  = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/mark_tag?access_token=" . $token;
        $body = json_encode([
            'userid'          => $customerService,
            'external_userid' => $externalUserid,
            'add_tag'         => [self::TAG[$tagScene]]
        ], JSON_UNESCAPED_UNICODE);

        $aJson = CUtil::curl_post($url, $body, null, 10, true);
        $aData = json_decode($aJson, true);

        if ($aData['errcode'] != 0) {
            return [false, "错误原因：" . $aData['errmsg'] . "|参数：" . $body];
        }
        return [true, 'ok'];
    }



}
