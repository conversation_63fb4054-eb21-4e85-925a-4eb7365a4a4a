<?php
namespace app\components;

include_once \Yii::get<PERSON>lias("@vendor")."/mycompany/aliyun-php-sdk-core/Config.php";

use app\models\CUtil;
use afs\Request\V20180112 as Afs;
use DefaultProfile;
use DefaultAcsClient;


class AliYunCaptcha {

    /**
     * @return array
     * 短信验证码滑块
     */
    public static function CaptchaVerify($data,$type = "mova_plumbing")
    {
        if ($type == "mova_plumbing") {
            $config = CUtil::getConfig('mova_plumbing_message', 'common', MAIN_MODULE);
            if (empty($config)) {
                return [false, "滑块配置不存在！"];
            }
        } else {
            return [false, "滑块配置不存在！"];
        }

        $needField = ['nc_token','csessionid','sig','scene'];
        foreach ($needField as $field) {
            if (!isset($data[$field])) {
                return [false, "缺少参数：".$field];
            }
        }

        $accessKey    = $config['accesskey'] ?? '';
        $accessSecret = $config['accessSecret'] ?? '';

        $appKey       = $config['appkey'] ?? '';
        $remoteIp     = CUtil::get_client_ip();
        $iClientProfile = DefaultProfile::getProfile("cn-hangzhou", $accessKey, $accessSecret);
        $client = new DefaultAcsClient($iClientProfile);
        DefaultProfile::addEndpoint("cn-hangzhou", "cn-hangzhou", "afs", "afs.aliyuncs.com");
        $request = new Afs\AuthenticateSigRequest();
        $request->setSessionId($data['csessionid']);// 必填参数，从前端获取，不可更改，android和ios只传这个参数即可
        $request->setToken($data['nc_token']);// 必填参数，从前端获取，不可更改
        $request->setSig($data['sig']);// 必填参数，从前端获取，不可更改
        $request->setScene($data['scene']);// 必填参数，从前端获取，不可更改
        $request->setAppKey($appKey);//必填参数，后端填写
        $request->setRemoteIp($remoteIp);//必填参数，后端填写

        $response = $client->getAcsResponse($request);//返回code 100表示验签通过，900表示验签失败
        if ($response->Code == 100) {
            return [true, "验证通过"];
        } else {
            // CUtil::debug("验签失败|request:".json_encode($request,320)."|response:".json_encode($response,320),"warn.captcha");
            CUtil::setLogMsg(
                "warn.captcha",
                json_encode($request,320),
                json_encode($response,320),
                [],
                '',
                '验签失败',
                [],
                $response->Code
            );
            return [false, "验证失败"];
        }
    }
}
