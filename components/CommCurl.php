<?php
/**
 * Created by IntelliJ IDEA.
 * Date: 2021/11/12
 * Time: 11:28
 */

namespace app\components;

use app\models\CUtil;
use phpDocumentor\Reflection\Types\Null_;

final class CommCurl {

    protected static $_instance = null;

    private function __construct() {}

    private function __clone() {}


    //https://www.php.cn/course/1020.html
    const HTTP_CODE = [
        'OK'                    => 200,
        'BAD_REQUEST'           => 400,
    ];

    /**
     * @return CommCurl
     */
    public static function factory(): CommCurl
    {

        if (!isset(self::$_instance) || !is_object (self::$_instance)) {
            self::$_instance = new self();
        }

        return self::$_instance;
    }

    /**
     * @param $url
     * @param $body :请求体
     * @param null $headers : 请求头
     * @param int $timeout : 超时等待时间
     * @param array $ext : 其他各种个性化操作详见： $this->__commSet()
     * @return array
     */
    private function __cPost($url, $body='', $headers = null, $timeout = 10, $ext=[]): array
    {

        $cl         = $this->__commSet($url,$headers,$timeout,$ext);
        curl_setopt_array($cl, [CURLOPT_POSTFIELDS => $body]);
        return $this->__commGet($cl);
    }

    private function __cGet($url, $body='', $headers = null, $timeout = 10, $ext=[]): array
    {
        $cl = $this->__commSet($url,$headers,$timeout,$ext);
        curl_setopt_array($cl, [CURLOPT_CUSTOMREQUEST => "GET",CURLOPT_POSTFIELDS => $body]);
	    return $this->__commGet($cl);
    }

    private function __cPut($url, $body='', $headers = null, $timeout = 10, $ext=[]): array
    {
        $cl = $this->__commSet($url,$headers,$timeout,$ext);
        curl_setopt_array($cl, [CURLOPT_CUSTOMREQUEST => "PUT",CURLOPT_POSTFIELDS => $body]);
	    return $this->__commGet($cl);
    }

    private function __cDelete($url, $body, $headers = null, $timeout = 10, $ext = []): array
    {
        $cl = $this->__commSet($url, $headers, $timeout, $ext);
        curl_setopt_array($cl, [
            CURLOPT_CUSTOMREQUEST  => "DELETE",  // 使用DELETE方法
            CURLOPT_RETURNTRANSFER => true,      // 将响应保存为字符串
            CURLOPT_POSTFIELDS     => $body
        ]);

        return $this->__commGet($cl);
    }



    /**
	 * @param $cl
	 * @return array
	 * 统一结果处理
	 */
    private function __commGet($cl) {

	    $result     = curl_exec($cl);
	    $httpCode   = @curl_getinfo($cl, CURLINFO_HTTP_CODE);
	    $err        = $httpCode == 0 ? curl_error($cl) : '';
	    $err_no     = $httpCode == 0 ? "|".curl_errno($cl) : '';
	    curl_close($cl);

	    return [$httpCode,$result,"{$err}{$err_no}"];
    }

    /**
     * @param $url
     * @param null $headers
     * @param int $timeout
     * @param array $ext
     * @return \CurlHandle|false|resource
     * 初始化及个性化封装
     */
    private function __commSet($url, $headers = null, $timeout = 10, $ext=[]) {

        $https          = $ext['https']         ?? true;    //是否验证https证书和hosts
        $cookie_file    = $ext['cookie_file']   ?? "";      //是否将cookie保存到文件
        $cookie         = $ext['cookie']        ?? [];      //要发送的cookie
        $transfer       = $ext['transfer']      ?? 1;       //1:获取的信息以文件流的形式返回，0:直接输出。
        $ip_resolve     = $ext['ip_resolve']    ?? "IPV4";  //ip协议设置
        $ssl_cert_type  = $ext['ssl_cert_type'] ?? "";      //证书的类型。PEM,DER,ENG。未指定一律不认证
        $ssl_cert_file  = $ext['ssl_cert_file'] ?? "";      //客户端证书路径
        $ssl_key_file   = $ext['ssl_key_file']  ?? "";      //客户端私钥的文件路径

        $cl             = curl_init($url);
        curl_setopt_array($cl, [
            CURLOPT_TIMEOUT         => $timeout,
            CURLOPT_HEADER          => intval(!!$cookie_file),//设置头文件的信息作为数据流输出，还是直接输出。
            CURLOPT_RETURNTRANSFER  => intval(!!$transfer),
        ]);

        $headers && curl_setopt($cl, CURLOPT_HTTPHEADER, $headers);

        curl_setopt_array($cl, [CURLOPT_SSL_VERIFYPEER => false, CURLOPT_SSL_VERIFYHOST => false]);

        $cookie_file && curl_setopt($cl, CURLOPT_COOKIEFILE, $cookie_file);

        $cookie && curl_setopt($cl, CURLOPT_COOKIE, implode(";",$cookie));

        switch ($ip_resolve) {
            case 'IPV4' :
                curl_setopt($cl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
                break;
            case 'IPV6':
                curl_setopt($cl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V6);
                break;
            default:
                break;
        }

        $ssl_cert_type && curl_setopt_array($cl, [
            CURLOPT_SSLCERTTYPE  => $ssl_cert_type,
            CURLOPT_SSLKEYTYPE   => $ssl_cert_type,
            CURLOPT_SSLCERT      => $ssl_cert_file,
            CURLOPT_SSLKEY       => $ssl_key_file,
        ]);

        return $cl;
    }

    /**
     * @param string $url : 目标地址
     * @param string $data : 请求参数
     * @param null $head : 请求头
     * @param string $method : 请求方式
     * @param int $timeout : 超时等待时间
     * @param array $ext : 其他各种个性化操作详见: $this->__commSet()
     * @return array
     * HTTP 请求封装
     * HTTP CODE: https://www.php.cn/course/1020.html
     */
    public function Send($url='',$data='',$head=null,$method='POST',$timeout=10,$ext=[]): array
    {
        switch ($method) {
            case "POST" :
                list($httpCode,$ret,$err) = $this->__cPost($url,$data,$head,$timeout,$ext);
                break;
            case "GET"  :
//                $query  = empty($data) ?  ""   : http_build_query($data);
//                $url    = empty($query) ? $url : "{$url}?{$query}";
                list($httpCode,$ret,$err) = $this->__cGet($url,$data,$head,$timeout,$ext);
                break;
            case "PUT"  :
                list($httpCode,$ret,$err) = $this->__cPut($url,$data,$head,$timeout,$ext);
                break;
            case 'DELETE':
                list($httpCode,$ret,$err) = $this->__cDelete($url,$data,$head,$timeout,$ext);
                break;
            default :
                $httpCode = self::HTTP_CODE['BAD_REQUEST'];
                $ret      = [];
	            $err      = "非法请求|{$httpCode}";
                break;
        }

        //是否要json_decode结果
        $json_decode    = $ext['json_decode'] ?? true;
        $aData          = $json_decode ? (array)json_decode($ret,true) : $ret ;

        // project
        $p = $ext['p'] ?? '';
        $fileName = "err.curl";
        if($p) {
            $fileName = "{$p}_warn.curl";
        }

        if($httpCode == self::HTTP_CODE['OK']) {
            return [true,$httpCode,$aData,$err];
        }

        // CUtil::debug("{$p}\n\t{$url}|".var_export($data,1)."|{$httpCode}|{$ret}|{$err}", $fileName);
        CUtil::setLogMsg(
            $fileName,
            $data,
            $ret,
            $head,
            $url,
            $err,
            [],
            $httpCode
        );

        return [false,$httpCode,$aData,$err];
    }

}
