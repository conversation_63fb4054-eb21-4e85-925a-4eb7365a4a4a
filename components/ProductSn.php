<?php

namespace app\components;

use app\exceptions\ProductSnException;
use app\models\by;
use app\models\CUtil;

/**
 * 获取 SN
 */
class ProductSn
{
    // ProductSn 实例
    public static $_instance;

    // 请求地址
    protected $host;

    // bi配置信息
    protected $auth;
    protected $app_id;
    protected $app_secret;

    // 请求方式（get、post）
    const REQUEST_METHODS = [
        'GET'  => '/RESTAdapter/BIGDATASERVICE_COMMON/GET',
        'POST' => '/RESTAdapter/BIGDATASERVICE_COMMON/POST'
    ];

    // bi系统的配置
    const BI = [ //po_mem_user  SJVKOhHIQ]\iR1
            'auth'       => 'cG9fbWVtX3VzZXI6U0pWS09oSElRXVxpUjE=',
            'app_id'     => '3ea57303Nf3a5N4b8aN87f0N541376eb4720',
            'app_secret' => 'e19b0183N6dabN4c6eNb307N738f99e68cf8'
    ];


    // redis 有效期，2小时
    const REDIS_EXPIRE = 2 * 60 * 60;

    public function __construct()
    {
        // host 地址
        $this->host = CUtil::getConfig('host', 'config', \Yii::$app->id)['crm_host'] ?? '';

        // 获取配置信息
        $bi = self::BI;
        // 配置信息
        $this->auth = $bi['auth'];
        $this->app_id = $bi['app_id'];
        $this->app_secret = $bi['app_secret'];
    }

    private function __clone()
    {
    }

    /**
     * 单例工厂
     * @return ProductSn
     */
    public static function factory(): ProductSn
    {
        if (!isset(self::$_instance) || !is_object(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * 获取产品SN信息
     * @param string $sn
     * @return array
     * @throws ProductSnException
     */
    public function getProductSn(string $sn, string $host = '',$typeNotStr=""): array
    {
        // 1、请求接口
        if ($host) {
            $this->host = $host;
        }

        $url = $this->host . self::REQUEST_METHODS['GET'] . '?url=api/devices/' . $sn;

        try {
            $response = $this->_request($url, [], 'GET', true);
            !YII_ENV_PROD && CUtil::debug($sn.'|'.json_encode($response,320), 'bi.info');
        } catch (\Exception $e) {
            // 记录日志并抛出异常
            CUtil::debug('获取Token失败，错误信息：' . $e->getMessage() . '，请求参数：' . json_encode(['url' => $url]), 'bi.err');
            return [];
        }

        // 2、处理结果
        if ($response['code'] === 0) {
            $snData= $response['data'] ?? [];
            if(isset($typeNotStr) && !empty($typeNotStr) && $snData && is_array($snData)){
              $snData=$this->filterProductSn($snData,$typeNotStr);
            }
            return $snData;
        }

        // 3、记录日志并抛出异常
        // CUtil::debug('获取Token失败，错误信息：' . json_encode($response) . '，请求参数：' . json_encode(['url' => $url]), 'bi.err');
        CUtil::setLogMsg(
            "bi.err",
            [],
            $response,
            [],
            $url,
            'Token获取失败',
            [],
            200
        );
        return [];
    }

    /**
     * @param $snData
     * @param string $typeStr
     * @return array
     */
    public function filterProductSn($snData,$typeNotStr="寄送修"){
            return array_filter($snData, function($item) use($typeNotStr) {
                return isset($item['ddlx']) &&  !in_array($item['ddlx'],explode(",",$typeNotStr));
            });
    }


    /**
     * HTTP请求方法
     * 描述：尝试请求2次，第1次使用缓存中的token，第2次使用刷新后的token。
     * @param string $url
     * @param array $body
     * @param string $method
     * @param bool $is_auth
     * @return array
     * @throws \Exception
     */
    protected function _request(string $url, array $body = [], string $method = 'POST', bool $is_auth = false): array
    {
        // 1、请求头信息
        $header = [
            'content-type'  => 'Content-Type:application/json',
            'authorization' => 'Authorization:Basic ' . $this->auth,
            'expect'        => 'Expect:'
        ];

        // 1.1、是否需要认证
        if ($is_auth) {
            $header['authorization_po_dataplatform'] = 'Authorization_Po_DataPlatform: Bearer ' . $this->__getToken();
        }

        // 2、HTTP请求，请求1次，若失败则重试1次
        $response = $this->__sendHttpRequest($url, $body, $method, $header, $is_auth, 2);

        // 3、成功，返回结果
        if ($response['code'] === 0 && isset($response['data'])) {
            return $response['data'];
        }

        // 4、失败，记录日志并抛出异常
        // CUtil::debug('HTTP请求失败，错误信息：' . json_encode($response) . '，请求参数：' . json_encode($body, 320) . '，请求头信息：' . json_encode($header, 320), 'bi.err');
        CUtil::setLogMsg(
            "bi.err",
            $body,
            $response,
            $header,
            $url,
            'HTTP请求失败',
            [],
            200
        );
        throw new ProductSnException('HTTP请求失败');
    }

    /**
     * 获取token，含自动刷新功能
     * @param bool $cache
     * @param int $retry_count
     * @return string
     * @throws \Exception
     */
    private function __getToken(bool $cache = true, int $retry_count = 3): string
    {
        // 1、查询缓存
        $redis = by::redis('core');
        $redis_key = AppCRedisKeys::biAccessToken();
        if ($cache) {
            $token = $redis->get($redis_key);
            if ($token !== false) {
                return $token;
            }
        }

        // 2、调用bi系统的接口，获取token
        $url = $this->host . self::REQUEST_METHODS['POST'] . '?url=api/auth/token/internal';
        $body = [
            'app_id'     => $this->app_id,
            'app_secret' => $this->app_secret
        ];
        $response = $this->_request($url, $body);

        // 3、成功，获取token、更新缓存
        if ($response['code'] === 0 && isset($response['data']['app_access_token'])) {
            $token = $response['data']['app_access_token'];
            $redis->setex($redis_key, self::REDIS_EXPIRE, $token);
            return $token;
        }

        // 4、失败，重试
        if ($retry_count >= 1) {
            return $this->__getToken(false, $retry_count - 1);
        }

        // 5、失败，记录日志并抛出异常
        // CUtil::debug('获取Token失败，错误信息：' . json_encode($response) . '，请求参数：' . json_encode($body, 320), 'bi.err');
        CUtil::setLogMsg(
            "bi.err",
            $body,
            $response,
            [],
            $url,
            'Token获取失败',
            [],
            200
        );
        throw new ProductSnException('Token获取失败');
    }

    /**
     * 有重试功能的HTTP请求方法
     * @param string $url
     * @param array $body
     * @param string $method
     * @param array $headers
     * @param bool $is_auth
     * @param int $retry_count
     * @return array
     * @throws \Exception
     */
    private function __sendHttpRequest(string $url, array $body, string $method, array $headers, bool $is_auth = false, int $retry_count = 1): array
    {
        $response = [];

        for ($retry = 0; $retry < $retry_count; $retry++) {
            // 1、执行请求
            list($status, $http_code, $ret, $err) = CommCurl::factory()->Send($url, json_encode($body, 320), $headers, $method);

            // 1.1、请求成功，退出循环，返回结果
            if ($status && isset($ret['code'])) {
                $response = [
                    'code' => $ret['code'],
                    'data' => $ret,
                ];
                break;
            }

            // 1.2、请求失败，重试，返回结果
            if ($is_auth && $retry < $retry_count - 1) {
                $headers['authorization_po_dataplatform'] = 'Authorization_Po_DataPlatform:Bearer ' . $this->__getToken(false, 3);
            } else {
                $response = [
                    'code'    => $http_code,
                    'message' => $err,
                ];
                break;
            }
        }
        return $response;
    }

}
