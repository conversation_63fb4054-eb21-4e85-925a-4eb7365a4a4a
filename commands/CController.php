<?php
/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace app\commands;

use yii\console\Controller;

/**
 * Class CController
 * @package app\commands
 */
abstract class CController extends Controller {

    abstract function getOneInstanceActionList();

    public function beforeAction($action): bool
    {
        $actionid   = $action->id;
        $matchstr   = $action->controller->id . "/" . $actionid;
        $ActionList = $this->getOneInstanceActionList();
        if (in_array($actionid, array_keys($ActionList))) {
	        $p_name        = PRO_NAME;
	        $name       = "{$p_name}/modules/main/shell/../../../yii";
            $system      = "ps -ef| grep {$name} | grep yii |grep '{$matchstr}' | grep -v grep |wc -l";
            $console     = system($system);
            $count       = intval($console);
            $process_num = $ActionList[$actionid] ?? 1;
            if ($count > $process_num) {
                echo "This action is running and it is Limit Process is {$process_num}!\r\n";
                exit(0);
            }
        }

        return parent::beforeAction($action);
    }

    public function afterAction($action, $result){
        return parent::afterAction($action, $result);
    }
}
