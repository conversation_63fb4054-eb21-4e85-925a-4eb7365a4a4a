<?php

namespace app\commands\service;

use app\commands\CController;
use app\modules\goods\services\UserOrderTryService;
use RedisException;
use yii\base\InvalidConfigException;
use yii\db\Exception;

class BuyAfterTryController extends CController
{//先试后买定时任务
    
    public function getOneInstanceActionList(): array
    {
        return [
            'get-sns-by-order-nos'    => 1,
            'get-active-times-by-sns' => 1,
            'activate-inform'         => 1,
            'un-arrive-inform'        => 1,
            'try-end-inform'          => 1,
            'return-product-inform'   => 1,
            'end-try-orders'          => 1,
            'wait-deduct-orders'      => 1,
            'converse-user-try-orders'=> 1
        ];
    }

    /**
     * @return bool
     * @throws Exception
     * @throws \yii\base\InvalidConfigException
     * @throws \Exception
     * <AUTHOR>
     * @desc 保存订单的sn
     * @date 2024年5月6日11:04:30
     */
    public function actionGetSnsByOrderNos()
    {
          $ret = UserOrderTryService::getInstance()->SaveSnsByOrders();
          YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * <AUTHOR>
     * @desc 保存订单的激活时间
     * @date 2024年5月6日11:04:24
     *
     */
    public function actionGetActiveTimesBySns()
    {
        $ret = UserOrderTryService::getInstance()->SaveActiveTimeBySns();
        YII_ENV_DEV && var_dump($ret);
    }


    /**
     * @return void
     * @throws RedisException
     * @throws Exception
     * 用户收到货2天未激活，通知用户
     * 短信+飞书
     */
    public function actionActivateInform()
    {
        $ret = UserOrderTryService::getInstance()->activateInform();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * 未到货异常通知
     */
    public function actionUnArriveInform()
    {
        $ret = UserOrderTryService::getInstance()->unArriveInform();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 试用结束提醒  提前2天
     */
    public function actionTryEndInform()
    {
        $ret = UserOrderTryService::getInstance()->tryEndInform();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * @throws RedisException
     * 退机有效期剩余1天
     */
    public function actionReturnProductInform()
    {
        $ret = UserOrderTryService::getInstance()->returnProductInform();
        YII_ENV_DEV && var_dump($ret);
    }


    /**
     * @return void
     * 试用结束-待退机状态
     */
    public function actionEndTryOrders()
    {
        $ret = UserOrderTryService::getInstance()->EndTryOrders();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * 退机超时-待扣款状态
     */
    public function actionWaitDeductOrders()
    {
        $ret = UserOrderTryService::getInstance()->WaitDeductOrders();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @return void
     * @throws Exception
     * 活动转化率
     */
    public function actionConverseUserTryOrders()
    {
        $ret = UserOrderTryService::getInstance()->SaveUserOrderTryConversion();
        YII_ENV_DEV && var_dump($ret);
    }

}
