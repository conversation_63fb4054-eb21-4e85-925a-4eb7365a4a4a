<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\GoodsReviewService;
use app\modules\goods\models\OmainModel;

class ReviewController extends CController
{
    const DAY = 30;   // 30天前的订单
    const INDEX = 10; // 分批次处理：10张表

    public function getOneInstanceActionList(): array
    {
        return [
            'default_review' => 1, // 系统评价
        ];
    }

    // 默认（系统）评价
    public function actionDefaultReview()
    {
        $microSeconds = 200000;

        $timePeriod = $this->getTimePeriod(self::DAY);
        try {
            // 获取订单完成30天前的订单
            for ($i = 0; $i < self::INDEX; $i++) {
                // 获取评价数据
                $data = $this->getReviewData($i, OmainModel::ORDER_STATUS['FINISHED'], $timePeriod);
                // 过滤已评价的订单（cms已处理）
                if (!empty($data)) {
                    GoodsReviewService::getInstance()->batchCreateSystemReview($data);
                }
                // 根据实际情况控制速度
                usleep($microSeconds);
            }
        } catch (\Exception $e) {
            $log = [
                'time_period' => $timePeriod,
                'index'       => $i,
                'data'        => $data ?? '',
                'msg'         => $e->getMessage(),
            ];
            CUtil::debug('系统默认评价异常：' . json_encode($log, JSON_UNESCAPED_UNICODE), 'err.review.system-review');
        }
    }

    /**
     * 获取评价数据
     * @param $index
     * @param $status
     * @param $time
     * @return array
     * @throws \yii\db\Exception
     */
    private function getReviewData($index, $status, $time): array
    {
        // 订单号
        $order_nos = by::Ouser()->getOrderByStatusAndFinish($index, $status, $time);
        if (empty($order_nos)) {
            return [];
        }

        // 订单商品信息
        $orders = by::Ocfg()->getListByOrderNos($order_nos);

        // 用户UID
        $uids = by::Phone()->getUidByUserIds(array_column($orders, 'user_id'));

        $data = [];
        foreach ($orders as $order) {
            if (isset($uids[$order['user_id']]) && ($order['is_internal_purchase'] ?? 0) == 0) { // 非内购
                $data[] = [
                    'order_no' => $order['order_no'],
                    'sku'      => ($order['atype'] == 1) ? $order['spec']['sku'] : $order['sku'],
                    'uid'      => $uids[$order['user_id']],
                ];
            }
        }

        // 过滤先试后买订单
        $try_orders = byNew::UserOrderTry()->GetList([
            CUtil::buildCondition('order_no', 'in', $order_nos),
        ], false);
        $try_order_nos = array_column($try_orders, 'order_no');
        foreach ($data as $key => $item) {
            if (in_array($item['order_no'], $try_order_nos)) {
                unset($data[$key]);
            }
        }

        return array_values($data);
    }

    /**
     * 获取时间段
     * @param int $day
     * @return array
     */
    private function getTimePeriod(int $day = 0): array
    {
        $currentTime = new \DateTime();
        $daysStart = clone $currentTime;
        $daysStart->modify("-{$day} days")->setTime(0, 0, 0);
        $daysStartEnd = clone $daysStart;
        $daysStartEnd->setTime(23, 59, 59);
        return [
            'start' => $daysStart->getTimestamp(),
            'end'   => $daysStartEnd->getTimestamp()
        ];
    }
}