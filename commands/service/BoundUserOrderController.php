<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\CUtil;
use app\modules\back\services\BoundUserOrderService;

/**
 * 绑定的员工订单控制器
 */
class BoundUserOrderController extends CController
{
    function getOneInstanceActionList()
    {
        return [
            'update-order-status' => 1,
        ];
    }

    /**
     * 更新员工绑定的订单状态
     */
    public function actionUpdateOrderStatus()
    {
        try {
            // 每次最多几次循环，最多共处理10万条数据
            $maxLoop      = 100;
            $maxTaskNum   = 100;
            $microSeconds = 200000;

            $service = BoundUserOrderService::getInstance();

            // 最后的id
            $id = 0;

            for ($i = 0; $i < $maxLoop; $i++) {
                // 通知待确认的订单
                $lastId = $service->updateStatus($id, $maxTaskNum);

                // 没有数据，跳出循环
                if ($lastId == $id) {
                    break;
                }

                $id = $lastId;

                // 根据实际情况控制速度
                usleep($microSeconds);
            }
        } catch (\Exception $e) {
            $log = [
                'index' => $i ?? '',
                'msg'   => $e->getMessage(),
            ];
            CUtil::debug('更新订单状态异常：' . json_encode($log, JSON_UNESCAPED_UNICODE), 'err.update-order-status');
        }
    }
}