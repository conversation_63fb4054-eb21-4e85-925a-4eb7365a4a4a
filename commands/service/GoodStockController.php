<?php
/**
 * 更新商品库存
 */
namespace app\commands\service;

use app\commands\CController;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\GoodsService;
use app\modules\goods\models\GmainModel;
use app\modules\goods\models\GoodsCrontabStockModel;
use app\modules\wares\services\goods\GoodsStockService;

/**
 * 定时设置商品库存
 */
class GoodStockController extends CController
{
    function getOneInstanceActionList(): array
    {
        return [
                'up' => 1,
        ];
    }

    public $logFilename = 'goods.crontab.stock';

    /**
     * 定时设置商品库存
     *
     */
    public function actionUp()
    {
        // 读取预存数据
        $time = time();
        $triggerTime1 = date('Y-m-d H:i', $time); // 用于 指定日期
        $triggerTime2 = date('H:i', $time);       // 用于 指定每天时间
        $part1 = GoodsCrontabStockModel::find()
            ->where(['>', 'stock', 0])
            ->andWhere(['status' => 1, 'type' => 1, 'trigger_time' => $triggerTime1])
            ->asArray()->all();

        $part2 = GoodsCrontabStockModel::find()
            ->where(['>', 'stock', 0])
            ->andWhere(['status' => 1, 'type' => 2, 'trigger_time' => $triggerTime2])
            ->asArray()->all();

        $list = array_merge($part1, $part2);
        if (empty($list)) {
            CUtil::debug("定时补库存 没有符合的商品", $this->logFilename);
        }
        foreach ($list as $item) {
            $this->doExec($item['gid'],$item['sid'], $item['stock']);
        }
    }

    public function doExec($goodId, $sid, $stock)
    {
        if ($stock < 1) {
            CUtil::debug("定时补库存 商品预设库存小于1 不设置库存 goodId:{$goodId} stock:{$stock}", $this->logFilename);
            return false;
        }
        $goodInfo = GoodsService::getInstance()->getGoodsDetail($goodId);
        if (empty($goodInfo)) {
            CUtil::debug("定时补库存 商品不存在 goodId:" . $goodId, $this->logFilename);
            return false;
        }

        if ($goodInfo['status'] == 0) {
            //下架 更新缓存
            $act = GmainModel::ACT['DOWN'];
            list($s, $m) = by::Gmain()->batchPut($goodId, $act);
            if (!$s) {
                CUtil::debug("定时补库存 下架失败 goodId:{$goodId} msg:$m", $this->logFilename);
                return false;
            }
        }

        //更新库存
        list($s, $m) = by::GoodsStockModel()->UpdateStock($goodId,$sid,$stock,'SET',true,by::GoodsStockModel()::SOURCE['MAIN']);
        if (!$s) {
            CUtil::debug("定时补库存 更新库存 失败 " . json_encode(['gid' => $goodId,'sid' => $sid, 'stock' => $stock, 'msg' => $m], 320), $this->logFilename);
        }
        CUtil::debug("定时补库存 更新库存 成功 " . json_encode(['gid' => $goodId, 'sid' => $sid, 'stock' => $stock, 'sku' => $m], 320), $this->logFilename);

        //上架 跟新缓存
        $act = GmainModel::ACT['UP'];
        list($s, $m) = by::Gmain()->batchPut($goodId, $act);
        if (!$s) {
            CUtil::debug("定时补库存 上架 失败 " . json_encode(['gid' => $goodId, 'stock' => $stock, 'msg' => $m], 320), $this->logFilename);
            return false;
        }

        return true;
    }

    /**
     * 库存告警通知
     * @return void
     */
    public function actionStockOutNotify()
    {
        try {
            $goodsStockService = GoodsStockService::getInstance();
            $goodsList = $goodsStockService->getStockOutGoodsList();
            if (!empty($goodsList)) {
                $title = sprintf('【%s环境】 %s 产品sku库存告警', YII_ENV, date("Y-m-d"));

                $contents = array_map(function ($item) {
                    return sprintf('商品名：%s，sku：%s，库存：%d，请及时关注！',
                        $item["name"],
                        $item["sku"],
                        $item["stock"]);
                }, $goodsList);

                CUtil::sendMsgToFs([
                    'title'    => $title,
                    'contents' => $contents,
                ], 'skuStockOutNotice', 'interactive');
            }
        } catch (\Throwable $e) {
            CUtil::debug("库存告警通知 失败 " . $e->getMessage(), $this->logFilename);
        }
    }

} 