<?php

namespace app\commands\service;

use app\commands\CController;
use app\components\ActivityStockAlert;
use app\components\BirthdayReminder;

/**
 * 活动
 */
class ActivityController extends CController
{

    function getOneInstanceActionList()
    {
        return [
            'birthday-push' => 1, // 生日推送
        ];
    }

    /**
     * 生日推送
     * @throws \yii\db\Exception
     */
    public function actionBirthdayPush()
    {
        // 生日提醒
        $reminder = new BirthdayReminder();
        $reminder->sendBirthdayReminder();
    }

    /**
     * 活动库存预警
     * @throws \yii\db\Exception
     */
    public function actionStockAlert()
    {
        // 生日提醒
        $alert = new ActivityStockAlert();
        $alert->stockAlert();
    }
}