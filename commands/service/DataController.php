<?php

namespace app\commands\service;
use app\commands\CController;
use app\components\Crm;
use app\components\Mall;
use app\components\RuiYun;
use app\components\StringObj;
use app\models\by;

class DataController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'create-db-tb' => 1,
            'qps-to-db' => 1,
            'user-guide-clean' => 1,
            'statistics'    => 1,
            'wxoa-push'     => 1,
            'syn-app_0' => 1,
            'syn-app_1' => 1,
            'syn-app_2' => 1,
            'syn-app_3' => 1,
            'syn-app_4' => 1,
            'syn-app_5' => 1,
            'syn-app_6' => 1,
            'syn-app_7' => 1,
            'syn-app_8' => 1,
            'syn-app_9' => 1,
            'syn-rui-yun_0' => 1,
            'syn-rui-yun_1' => 1,
            'syn-rui-yun_2' => 1,
            'syn-rui-yun_3' => 1,
            'syn-rui-yun_4' => 1,
            'syn-rui-yun_5' => 1,
            'syn-rui-yun_6' => 1,
            'syn-rui-yun_7' => 1,
            'syn-rui-yun_8' => 1,
            'syn-rui-yun_9' => 1,
            'syn-crm_retry' => 1,
            'final-retry' =>1,
            'app-final-retry' =>1,
            'rui-yun-final-retry' =>1,
            'repeat-live-list'=>1,
            'check-redis-values' =>1,
        ];
    }

    //改为supervisor消息队列最大排队限制数
    const DEFAULT_LIMIT = YII_ENV_PROD ? 500 : 1000;
    private static $supervisor_limit = [
        'queue.messages' => YII_ENV_PROD ? 500 : 1000,
        'queue-log.messages' => YII_ENV_PROD ? 500 : 1000,
    ];

    /**
     * 检查redis队列排队是否过载
     * Undocumented function
     *
     * @return void
     */
    public function actionCheckRedisValues(){
        $redis_queue = \Yii::$app->redis_queue;
        //keys阻塞，如果键多的话，考虑优化
        $keys = $redis_queue->keys('*');
        foreach ($keys as $v){
            // 使用TYPE命令查询键的数据类型
            $type = $redis_queue->type($v);
            // 输出数据类型
            switch (strtoupper($type)) {
                case 'STRING':
                case 'ZSET':
                    break;
                case 'HASH':
                    $len = $redis_queue->hlen($v);
                    if(isset(self::$supervisor_limit[$v])){
                        if($len > self::$supervisor_limit[$v]){
                            //报警
                            $data['title'] = sprintf('%s dreame %s supervisor消息队列排队超限', date('Y-m-d'), YII_ENV);
                            $data['contents'] = ['file:' . __FILE__,'redis地址:' . $redis_queue->hostname, '库:' . $redis_queue->database, 'key:' . $v, 'limit_num:' . self::$supervisor_limit[$v], 'now_num:' . $len];
                            \app\models\CUtil::sendMsgToFs($data, 'checkRedisValues', 'interactive');
                        }
                    }else{
                        if($len > self::DEFAULT_LIMIT){
                            //报警
                            $data['title'] = sprintf('%s dreame %s 消息队列排队超限', date('Y-m-d'), YII_ENV);
                            $data['contents'] = ['file:' . __FILE__,'redis地址:' . $redis_queue->hostname, '库:' . $redis_queue->database, 'key:' . $v, 'limit_num:' . self::DEFAULT_LIMIT, 'now_num:' . $len];
                            \app\models\CUtil::sendMsgToFs($data, 'checkRedisValues', 'interactive');
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        exit(0);
    }

    /**
     * @throws \yii\db\Exception
     * 建表
     */
    public function actionCreateDbTb() {
        by::systemLogsModel()->CreateDbTb();
        by::Wpraise()->CreateDbTb();
    }

    /**
     * @param null $date 指定日期数据，为空则默认昨天
     * 每日凌晨3点QPS记录落地
     */
    public function actionQpsToDb($date = null) {
        $date = is_null($date) ? date("Ymd",strtotime("yesterday")) : $date;
        by::model("MonitorModel",MAIN_MODULE)->QpsDataToDB($date);
    }

    /**
     * 清理过期用户与导购关联关系
     */
    public function actionUserGuideClean(){
        by::userGuide()->getExpire();
    }


    /**
     * 统计上一天的数据写表
     */
    public function actionStatistics()
    {
        by::statistics()->dataToTb();
    }

    /**
     * @throws \yii\db\Exception
     * 微信公众号消息推送
     */
    public function actionWxoaPush()
    {
        by::WxNotice()->OaPush();
    }

    /**
     * 同步App
     */
    public function actionSynApp_0(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_1(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_2(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_3(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_4(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_5(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_6(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_7(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_8(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    public function actionSynApp_9(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        Mall::factory()->SynApp($index);
    }

    /**
     * app最终重试 凌晨执行
     */
    public function actionAppFinalRetry(){
        by::model('AppLogModel', 'main')->finalRetry();
    }

    //瑞云回调
    public function actionSynRuiYun_0(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_1(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_2(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_3(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_4(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_5(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_6(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_7(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_8(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }
    public function actionSynRuiYun_9(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        RuiYun::factory()->synRuiYun($index);
    }

    /**
     * 瑞云最终重试 凌晨执行
     */
    public function actionRuiYunFinalRetry(){
        by::model('RuiYunLogModel', 'main')->finalRetry();
    }


    public function actionRepeatLiveList()
    {
        by::model('LiveModel','main')->repeatLiveList();
    }
}
