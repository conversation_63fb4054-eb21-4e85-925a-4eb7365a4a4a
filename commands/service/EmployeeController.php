<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\CUtil;
use app\modules\back\services\UserEmployeeService;

/**
 * 脚本
 */
class EmployeeController extends CController
{

    function getOneInstanceActionList()
    {
        return [
            'update-employee-level' => 1,
        ];
    }

    // 每月初1号，凌晨5点，更新员工信息

    /**
     * 更新员工等级
     */
    public function actionUpdateEmployeeLevel()
    {
        try {
            // 每次最多几次循环，最多共处理10万条数据
            $maxLoop      = 100;
            $maxTaskNum   = 100;
            $microSeconds = 200000;

            $service = UserEmployeeService::getInstance();

            // 最后的id
            $id = 0;

            for ($i = 0; $i < $maxLoop; $i++) {
                // 通知待确认的订单
                $lastId = $service->updateLevel($id, $maxTaskNum);

                // 没有数据，跳出循环
                if ($lastId == $id) {
                    break;
                }

                $id = $lastId;

                // 根据实际情况控制速度
                usleep($microSeconds);
            }
        } catch (\Exception $e) {
            $log = [
                'index' => $i ?? '',
                'msg'   => $e->getMessage(),
            ];
            CUtil::debug('更新员工状态异常：' . json_encode($log, JSON_UNESCAPED_UNICODE), 'err.employee.update-employee-level');
        }
    }
}