<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\TradeInOrderModel;
use app\modules\main\services\TradeInService;

class TradeInController extends CController
{
    const SMS_CODE = [
        'CONFIRMATION' => 'SMS_467075354' // 旧机核验结果通知
    ];

    public function getOneInstanceActionList(): array
    {
        return [
            'notify' => 1,
            'notify-customer-service' => 1,
        ];
    }

    public function actionNotify()
    {
        // 每次最多几次循环
        $maxLoop = 10;
        $maxTaskNum = 1000;
        $microSeconds = 200000;

        $service = TradeInService::getInstance();

        $id = 0;

        for ($i = 0; $i < $maxLoop; $i++) {
            // 通知待确认的订单
            $status = TradeInOrderModel::STATUS['AWAITING_QUOTE_CONFIRMATION'];
            $lastId = $service->tradeInNotify($id, $status, self::SMS_CODE['CONFIRMATION'], $maxTaskNum);

            // 没有数据，跳出循环
            if ($lastId == $id) {
                break;
            }

            $id = $lastId;

            // 根据实际情况控制速度
            usleep($microSeconds);
        }
    }

    // 通知客服
    public function actionNotifyCustomerService()
    {
        // 每次最多几次循环
        $maxLoop = 10;
        $maxTaskNum = 1000;
        $microSeconds = 200000;

        $id = 0;

        for ($i = 0; $i < $maxLoop; $i++) {
            // 通知待确认的订单
            $status = TradeInOrderModel::STATUS['AWAITING_QUOTE_CONFIRMATION'];
            // 获取订单列表
            $orderList = byNew::TradeInOrderModel()->getOrderListByStatus($id, $status, 3, $maxTaskNum, false);
            if (empty($orderList)) {
                break;
            }

            // 推送消息
            $sendData = [
                'title' => '以旧换新超时未确认订单'
            ];
            foreach ($orderList as $order) {
                $sendData['contents'][] = $order['order_no'];
            }
            CUtil::sendMsgToFs($sendData, 'tradeInOrderConfirm', 'interactive');

            // ID赋值
            $id = end($orderList)['id'];

            // 根据实际情况控制速度
            usleep($microSeconds);
        }
    }

}