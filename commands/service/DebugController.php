<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\by;
use app\models\CUtil;
use app\models\MyExceptionModel;

class DebugController extends CController
{
    public function getOneInstanceActionList(): array
    {
        return [
            'cancel-recover'              => 1,
        ];
    }
    function actionCancelRecover()
    {
        $order_no  = '';
        $tid       = '4200002323202406108844013117';
        $user_id   = 2203052;
        $update_id = 20943;
        $m_id      = 459;
        $pay_type  = 3;
        $num       = 1;

        // 获取订单信息
        $user_id = by::Omain()->GetUserIdByNo($order_no);
        $oInfo   = by::Ouser()->GetInfoByOrderId($user_id, $order_no);
        if (empty($oInfo)) {
            exit("订单不存在");
        }

        // 修改流水
        $aLog = by::model('OPayModel', 'goods')->SaveLog($order_no, ['pay_type' => $pay_type, 'tid' => $tid]);

        $trans = by::dbMaster()->beginTransaction();
        try {
            // 试用优惠券
            if ($oInfo['coupon_id'] > 0) {
                $cardModel = by::userCard();
                $tb        = $cardModel::tbName($user_id);
                $res       = by::dbMaster()->createCommand()->update($tb, [
                    'status'       => $cardModel::STATUS['use'],
                    'use_channel'  => 1,
                    'use_relation' => $order_no,
                    'use_time'     => time(),
                ], ['id' => $update_id])
                    ->execute();

                if (!$res) {
                    throw new \Exception('使用卡券失败（1）');
                }

                list($s) = by::marketConfig()->useNumModify($m_id, 1);
                if (!$s) {
                    throw new \Exception('使用卡券失败（2）');
                }
                //删除缓存
                $cardModel->__delCache($user_id);
            }

            // 修改订单状态
            list($s, $m) = by::Omain()->SyncInfo($user_id, $order_no, by::Omain()::ORDER_STATUS['WAIT_PAY'], $arr ?? []);
            if (!$s) {
                throw new \Exception($m);
            }

            // 修改商品库存
            $oGoods = by::Ogoods()->GetListByOrderNo($user_id, $order_no);
            foreach ($oGoods as $val) {
                $goodsType = $val['goods_type'] ?? '';
                $source    = ($goodsType == by::Ogoods()::GOODS_TYPE['WARES'])
                    ? by::GoodsStockModel()::SOURCE['WARES']
                    : by::GoodsStockModel()::SOURCE['MAIN'];
                list($s, $m) = by::GoodsStockModel()->UpdateStock($val['gid'], $val['sid'], $val['num'], 'WAIT', true, $source);
                if (!$s) {
                    throw new \Exception($m);
                }
                $giniId = $val['gini_id'] ?? 0;
                if ($giniId) {
                    list($sg, $mg) = by::Gini()->UpdateStock($giniId, $val['num'], 'WAIT', true);
                    if (!$sg) {
                        throw new \Exception($mg);
                    }
                }
            }

            // 完成支付
            $mOuser = by::Ouser();
            $order  = $mOuser->CommPackageInfo($user_id, $order_no, false, false, true);
            list($s, $msg) = by::wxPay()->afterPay($user_id, $order_no, ['transaction_id' => $tid], $order, by::wxPay()::SOURCE['MALL']);
            if (!$s) {
                throw new \Exception($msg);
            }

            $trans->commit();

            exit("OK");

        }
        catch (\Exception $e) {
            $trans->rollBack();

            CUtil::debug($order_no . '|' . $e->getMessage(), 'info.debug');
            exit($order_no . '|' . $e->getMessage());
        }


    }


}