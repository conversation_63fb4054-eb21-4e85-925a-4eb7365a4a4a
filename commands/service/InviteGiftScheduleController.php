<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\CUtil;
use app\modules\main\services\invite\InviteGiftService;

/**
 * 邀请奖励定时发放控制器
 */
class InviteGiftScheduleController extends CController
{
    function getOneInstanceActionList(): array
    {
        return [
            'process-scheduled-gifts' => 1, // 处理定时发放奖励
        ];
    }

    /**
     * 处理定时发放奖励
     * 建议配置为每小时执行一次或根据业务需求调整频率
     */
    public function actionProcessScheduledGifts()
    {
        try {
            // 每次处理的最大记录数
            $limit = 100;
            
            $inviteGiftService = InviteGiftService::getInstance();
            list($status, $message) = $inviteGiftService->processScheduledGifts($limit);
            
            if ($status) {
                CUtil::debug('定时发放奖励成功: ' . $message, 'invite_gift_schedule_success');
            } else {
                CUtil::debug('定时发放奖励失败: ' . $message, 'invite_gift_schedule_error');
            }
            
            if (YII_ENV_DEV) {
                echo $message . "\n";
            }
            
        } catch (\Exception $e) {
            $error = '定时发放奖励异常: ' . $e->getMessage() . ' File: ' . $e->getFile() . ' Line: ' . $e->getLine();
            CUtil::debug($error, 'invite_gift_schedule_error');
            
            if (YII_ENV_DEV) {
                echo $error . "\n";
            }
        }
    }
} 