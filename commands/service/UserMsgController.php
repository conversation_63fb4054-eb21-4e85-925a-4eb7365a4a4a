<?php

namespace app\commands\service;
use app\commands\CController;
use app\components\UserMsg;
use app\models\by;
use yii\db\Exception;

class UserMsgController extends CController
{//用户消息推送

    public function getOneInstanceActionList(): array
    {
        return [
            'user-msg_0' => 1,
            'user-msg_1' => 1,
            'user-msg_2' => 1,
            'user-msg_3' => 1,
            'user-msg_4' => 1,
            'user-msg_5' => 1,
            'user-msg_6' => 1,
            'user-msg_7' => 1,
            'user-msg_8' => 1,
            'user-msg_9' => 1,
            'user-msg-send' => 1,
        ];
    }

    /**
     * 每分钟产生对应的自定义价格
     */
    public function actionUserMsg_0(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_1(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_2(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_3(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_4(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_5(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_6(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_7(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_8(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    public function actionUserMsg_9(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        UserMsg::factory()->synUserMsg($index);
    }

    /**
     * 推送消息
     * @throws Exception
     */
    public function actionUserMsgSend(){
        $ret = by::UserMsg()->userMsgSend();
        !YII_ENV_PROD && var_dump($ret);
    }


}
