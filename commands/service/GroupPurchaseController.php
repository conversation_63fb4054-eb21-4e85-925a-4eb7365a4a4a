<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\CUtil;
use app\modules\back\services\GroupPurchaseService;

/**
 * 脚本
 */
class GroupPurchaseController extends CController
{

    function getOneInstanceActionList()
    {
        return [
            'group-purchase-auto-refund' => 1,
            'group-purchase-auto-shipment' => 1,
            'group-purchase-auto-reward' => 1,
            'group-purchase-auto-refund-audit' => 1,
            'group-purchase-auto-success-fail' => 1,
        ];
    }


    /**
     * 团购活动统一退款处理
     */
    public function actionGroupPurchaseAutoRefund()
    {
        try {
            GroupPurchaseService::getInstance()->autoRefund();
        } catch (\Exception $e) {
            CUtil::debug('团购活动统一申请退款处理:'.$e->getMessage(), 'err.group-purchase.auto-refund');
        }
    }

    /**
     * 24 小时自动拼团成功或失败
     */
    public function actionGroupPurchaseAutoSuccessFail()
    {
        try {
            // 先处理订单状态
            GroupPurchaseService::getInstance()->autoSuccessFail();

            // 最后三小时消息推送
            GroupPurchaseService::getInstance()->sendLastThreeHoursNotification();

        } catch (\Exception $e) {
            CUtil::debug('团购活动自动成功:'.$e->getMessage(), 'err.group-purchase.auto-success');
        }
    }
    /**
     * 团购活动统一退款处理
     */
    public function actionGroupPurchaseAutoRefundAudit()
    {
        try {
            GroupPurchaseService::getInstance()->autoRefundAudit();
        } catch (\Exception $e) {
            CUtil::debug('团购活动统一退款处理:'.$e->getMessage(), 'err.group-purchase.auto-refund-audit');
        }
    }
    /**
     * 团购活动统一发货
     */
    public function actionGroupPurchaseAutoShipment()
    {
        try {
            GroupPurchaseService::getInstance()->autoShipment();
        } catch (\Exception $e) {
            CUtil::debug('团购活动统一发货:'.$e->getMessage(), 'err.group-purchase.auto-shipment');
        }
    }
    /**
     * 团购活动批量发放奖励
     */
    public function actionGroupPurchaseAutoReward()
    {
        try {
            GroupPurchaseService::getInstance()->autoReward();
        } catch (\Exception $e) {
            CUtil::debug('团购活动批量发放奖励:'.$e->getMessage(), 'err.group-purchase.auto-reward');
        }
    }
}