<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\by;
use app\models\CUtil;
use app\modules\back\services\CommentAuditService;
use app\modules\back\services\CouponService;

/**
 * 评论审核自动发放奖励
 */
class CommentAuditController extends CController
{
    function getOneInstanceActionList(): array
    {
        return [
                'send-reward' => 1, // 自动发放奖励
        ];
    }

    /**
     * 自动发放奖励
     */
    public function actionSendReward()
    {
        try {
            // 获取当前时间
            $currentTime = time();

            // 查询符合条件的任务
            $tasks = by::CommentTaskModel()::find()
                    ->where(['and',
                             ['<=', 'start_time', $currentTime - 30 * 86400], // start_time + 30天
                             ['>=', 'end_time', $currentTime - 30 * 86400],   // end_time + 30天
                    ])
                    ->all();

            if (empty($tasks)) {
                return;
            }

            foreach ($tasks as $task) {
                // 检查卡券库存
                $stock = CouponService::getInstance()->getStock($task->coupon_id);

                if ($stock <= 0) {
                    CUtil::debug("{$task->id}晒单审核自动卡券库存不足: {$task->coupon_id}", 'comment.audit.send.coupon');
                    continue;
                }

                // 获取待发放奖励的审核记录
                $audits = by::CommentAuditModel()::find()
                        ->where([
                                'comment_task_id' => $task->id,
                                'status'          => 2,
                                'is_reward_sent'  => 0
                        ])->all();

                if (empty($audits)) {
                    continue;
                }

                // 发放奖励
                foreach ($audits as $audit) {
                    // 只有当当前时间大于等于创建时间加30天时，才会执行发放奖励的操作
                    if ($currentTime < ($audit->created_at + 30 * 86400)) {
                        continue;
                    }
                    $msg = CommentAuditService::getInstance()->sendReward($audit->id, '系统自动发放奖励',2);
                    if ($msg['code'] == -1) {
                        CUtil::debug("task_id:{$task->id}" . " 晒单审核自动发放奖励失败: {$msg['message']}", 'comment.audit.send.coupon');
                    }
                }
            }

        } catch (\Exception $e) {
            CUtil::debug('晒单审核自动发放奖励失败', 'comment.audit.send.coupon');
        }
    }
} 