<?php

namespace app\commands\service;

use app\commands\CController;
use app\components\AppCRedisKeys;
use app\components\EventMsg;
use app\components\HandleUserAddress;
use app\components\Mall;
use app\components\ReissueIntegral;
use app\components\RuiYun;
use app\components\Statistics;
use app\components\WeWork;
use app\jobs\FinishTaskJob;
use app\jobs\TestJob;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\services\ErpService;
use RedisException;
use yii\db\Exception;


/**
 * Created by IntelliJ IDEA.
 * User: Kevin
 * Date: 2021/9/13
 * Time: 17:37
 */
class Once2Controller extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'test' => 1
        ];
    }

    public function actionTest()
    {
        $db = by::dbMaster();
        $tb = 'db_dreame_goods.t_uo_ad_2022';

        /*by::dbMaster()->createCommand()->update($tb, ['aid' => 510198], ['order_no' => '20220414769452210001542204919'])->execute();
        by::dbMaster()->createCommand()->update($tb, ['aid' => 500226], ['order_no' => '20220415564172203801732204324'])->execute();
        by::dbMaster()->createCommand()->update($tb, ['aid' => 130622], ['order_no' => '20220413830342208101452204894'])->execute();

        by::Oad()->DelCache('20220414769452210001542204919');
        by::Oad()->DelCache('20220415564172203801732204324');
        by::Oad()->DelCache('20220413830342208101452204894');*/


        $tb = 'db_dreame_goods.t_uo_ad_2022';
        $tb1 = 'db_dreame.t_area';
        $tb2 = 'db_dreame.t_area_2';
        $sql = "select * from {$tb} where id > 9";
        $list = $db->createCommand($sql)->queryAll();

        foreach ($list as $val) {
            $sql1 = "select * from {$tb1} where id = {$val['aid']}";
            $sql2 = "select * from {$tb2} where id = {$val['aid']}";
            $b = $db->createCommand($sql1)->queryOne();
            $b1 = $db->createCommand($sql2)->queryOne();

//            echo $b['name'].'|'.$b1['name'].'-';
            echo $b['name'] . '|' . $b['id'] . '=' . $b1['name'] . '|' . $b1['id'] . '=' . "\n";

        }
    }

    public function actionTest2()
    {
        $db = by::dbMaster();
        $btb = "db_dreame.t_address_";
        $tb1 = 'db_dreame.t_area';
        $tb2 = 'db_dreame.t_area_2';

        for ($i = 0; $i < 10; $i++) {
            $tb = $btb . $i;

            $sql = "select * from {$tb}";
            $list = $db->createCommand($sql)->queryAll();

            foreach ($list as $val) {
                $sql1 = "select * from {$tb1} where id = {$val['aid']} and pid = {$val['cid']}";
                $sql2 = "select * from {$tb2} where id = {$val['aid']} and pid = {$val['cid']}";

                $l1 = $db->createCommand($sql1)->queryOne();
                $l2 = $db->createCommand($sql2)->queryOne();

                if (empty($l2)) {
                    $sql3 = "select * from {$tb2} where `name` = '{$l1['name']}' and pid = {$val['cid']}";
                    $l3 = $db->createCommand($sql3)->queryOne();
//                    echo $l3['name'].'='.$l3['id'].'+';
//                    echo $l1['name'].'|'.$l1['id'].'-'.$l2['name']."\n";

                    if (empty($l3['id'])) {
                        echo $l1['name'] . '|' . $l1['id'] . '-' . $l2['name'] . "\n";
                        continue;
                    }

                    $r = $db->createCommand()->update($tb, ['aid' => $l3['id']], ['id' => $val['id'], 'user_id' => $val['user_id']])->execute();

                    $r_key = AppCRedisKeys::getOneAddress($val['user_id'], $val['id']);
                    by::redis()->del($r_key);
                    echo $r . "\n";

                }


            }

        }
    }

    public function actionTest41()
    {
        RuiYun::factory()->save('202210216482501329012992210701', '695110');
    }

    public function actionTest42()
    {
        RuiYun::factory()->push(612949, 'save', ['order_no' => '202210286300417928451982210385', 'user_id' => 612949]);
    }


    public function actionBuChang()
    {
        $user_id = '781563';
        $order_no = '2022112255125105086550772211988';
        $notify_data = ['success_time' => 0, 'transaction_id' => time() . 'asdfg'];

        $order = by::Ouser()->CommPackageInfo($user_id, $order_no, false, false, true);
        by::wxH5Pay()->afterPay($user_id, $order_no, $notify_data, $order, 1);
        echo 'chenggong';
        exit;
    }

    public function actionTestKafka()
    {
//        Kafka::factory()->send('lanYY',[1,2,3,4,5]);
//        Kafka::factory()->consume('lanYY');
//        EventMsg::factory()->run('orderMsgSend',['event'=>'shipped','order_no'=>'20221216371131723267772212447']);
//        EventMsg::factory()->push(20221216371131723267772212447, 'orderMsgSend', ['event' => 'refund', 'order_no' => '20221216371131723267772212447']);

//        EventMsg::factory()->orderMsgSend( 'shipped', '2023010567991196699611622301877');
//        EventMsg::factory()->orderMsgSend( 'dispatch', '2023010566654196699688662301652');
//        EventMsg::factory()->orderMsgSend( 'signed', '202301056507514247530992301765');
//        EventMsg::factory()->orderMsgSend( 'abnormal', '2023010565076328522004522301294');
        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'shipped', 'order_no' => '202301076206814247545462301307']);
        EventMsg::factory()->run('orderMsgSend', ['event' => 'dispatch', 'order_no' => '202301075636114247538722301789']);
        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'signed', 'order_no' => '2023010756329328522026932301948']);
        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'abnormal', 'order_no' => '202301074624640795115422301337']);

        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'dispatch', 'order_no' => '20230107714072759642152301684']);
        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'signed', 'order_no' => '20230109399921382443392301076']);
        EventMsg::factory()->run('orderMsgSend', ['event' => 'abnormal', 'order_no' => '20230109399921382443392301076']);
        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'shipped', 'order_no' => '20230107714072759642152301684']);
//        AdvAscribe::factory()->push(612951,'tencent',['user_id'=>612951,'url'=>'pages/index/index','event'=>'COMPLETE_ORDER','click_id'=>'bfjnasbfbhasd','extra'=>['order_no'=>'20221215354841975247252212041']]);
//        EventMsg::factory()->push(612951,'subscribe',['user_id'=>612951]);
        exit();
    }


    public function actionOrdersPush()
    {
        $db = by::dbMaster();
        $tb = "`db_dreame_goods`.`t_om_2022`";
        $sql = "select * from {$tb} where id > 1 order by `ctime` DESC LIMIT 100";
        $list = $db->createCommand($sql)->queryAll();
        foreach ($list as $val){
            EventMsg::factory()->run( 'orderMsgSend', ['event' => 'dispatch', 'order_no' => $val['order_no'] ?? '']);
        }
        exit('sadsad');

    }

    public function actionProdKafka()
    {
//        $data = json_decode('{"code":"mall/dreame/order","uuid":"bf11e0e3-f321-3499-91da-ccbd1b1db11a","uid":"XI248307","timestamp":1673089081,"data":{"msgConfigId":"1602549345282236418","status":"","goods_name":"洗地机清洁液（500ML）瓶身带刻度","order_sn":"20230105474833128501272301103","goods_num":1,"link_url":"pages/orderDetail/orderDetail?order_no=20230105474833128501272301103"}}
//',true);
//
//        Kafka::factory()->send('m_user_e',$data);

        EventMsg::factory()->run( 'orderMsgSend', ['event' => 'signed', 'order_no' => '20230107714072759642152301684']);
//        EventMsg::factory()->push('2023010607940243276170822301636', 'orderMsgSend', ['event' => 'signed', 'order_no' => '2023010607940243276170822301636']);
    }

    public function actionRemoveRedis()
    {
//        CommController::actionWeComOa();
//        $r_key = 'dreame|AccFrequency|_isValidLimit|query-order|20001';
//        $redis = by::redis('core');
//        $value = $redis -> get($r_key);
//        $redis -> del($r_key);
//        var_dump($value);exit();
    }


    /**
     * 补发积分
     */
    public function actionInviteReg()
    {
        //邀请好友注册
        ReissueIntegral::factory()->run('inviteReg');
    }

    public function actionInviteBuy()
    {
        //邀请好友购买
        ReissueIntegral::factory()->run('inviteBuy');
    }

    public function actionRegSn()
    {
        //产品注册
        ReissueIntegral::factory()->run('regSn');
    }

    public function actionPushOrderGoods()
    {
        //购买配件/主机
        ReissueIntegral::factory()->run('pushOrderGoods');
    }

    public function actionPhone()
    {
        //+86手机号
        ReissueIntegral::factory()->run('phone');
    }

    public function actionRetrySend()
    {
        //所有方法同步完  最后执行重新发送
        ReissueIntegral::factory()->run('retrySend');
    }

    public function actionDelRedisKey()
    {
        //清除同步id
        ReissueIntegral::factory()->_delRedisKey();
    }
    public function actionCard()
    {
        list($a,$b) = by::OrefundMain()->Rback(548240,'20220426559462206101942204874');
        print_r($b);die();
        list($a,$b) = by::Ouser()->ErpAddOrder(612951,'20221205496761653651332212655');
//        list($status, $msg) = by::OrefundMain()->Rback(573926, '20220703254001873001302207155');
//print_r($msg);die;
//       EventMsg::factory()->finishGoodsEvent(582811,'20220713438581878901842207744');// 普通购买
//        EventMsg::factory()->finishGoodsEvent(548294,'20220916389321540801992209188');
       /* list($s,$data) = by::model("EventCenterModel", MAIN_MODULE)->shareGoods(548240);
        print_r($s);
        print_r($data);*/
//        EventMsg::factory()->run( 'viewGoods', ['user_id'=>612952]);
//        EventMsg::factory()->run('productReg', ['user_id' => 612952, 'sn' => 'SHPH93-GN1070890959603DS']);
//        EventMsg::factory()->run('addChart', ['user_id' => 612952]);
//        EventMsg::factory()->run('shareGoods', ['user_id' => 612952]);
//        EventMsg::factory()->run('inviteReg',['user_id'=>0]);
    }

    public function actionFinishGoods()
    {
        by::Ofinish()->ShellFinish();
    }

    public function actionSendMessage()
    {
        ReissueIntegral::factory()->sendMessage();
    }

    /**
     * @throws Exception
     * 预售商品（普通）到达截止时间恢复普通商品
     */
    public function actionPresaleInvalid()
    {
        by::Gtype0()->presaleInvalid();
    }

    /**
     * @throws Exception
     * 预售商品（一元链接）到达截止时间恢复普通商品
     */
    public function actionCartInvalid99()
    {
        by::Gtype99()->presaleInvalid();
    }
    /**
     * @return void
     * @throws \yii\db\Exception
     * 同步订单数据
     */
    public function actionSyncBirthday()
    {
        Mall::factory()->syncUserBirthday();
    }

    /**
     * @return void
     * 删除key
     */
    public function actionDelBirthdayRedis()
    {
        Mall::factory()->delBirthdayKey();
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function actionOrder()
    {
        $s = by::userAdv()->clearExpireData();
        !YII_ENV_PROD && var_dump($s);
    }


    public function actionOldForNew()
    {
//        $mallInfo = by::usersMall()->getMallInfoByUserId(612954);
//        (new OldForNew())->__request($mallInfo['uid']);
        EventMsg::factory()->run('buyGoods',['user_id'=>612958,'order_no'=>'20230325514121832434752303373']);//发放订单完成奖励
    }

    /**
     * 更新用户地址
     */
    public function actionUpdateUserAddress()
    {
        $handle = new HandleUserAddress();
        $handle->handleUpdate();
        exit("done");
    }

    /**
     * 删除用户地址
     */
    public function actionDeleteUserAddress()
    {
        $handle = new HandleUserAddress();
        $handle->handleDelete();
        exit("done");
    }

    public function actionTruncate()
    {
        (new Statistics())->truncate();
    }

     /*
     * @throws Exception
     * @throws RedisException
     */
    public function actionData()
    {
         (new Statistics())->userLevelInfo();
    }

    public function actionRecommend()
    {
        (new Statistics())->recommendData();
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function actionCsv()
    {
        $fp = fopen('C:\Users\<USER>\Desktop/data.csv', 'r');
        while ($line = fgetcsv($fp)) {
            $json_string = $line[0];
            list($other,$orderNo) = explode('"order_no":"',$json_string);
            $orderNo = str_replace('"}}','',$orderNo);
            list($other,$uid) = explode('"uid":"',$json_string);
            $uid = substr($uid,0,8);
            (new Statistics())->errorData($orderNo,$uid);
        }
        fclose($fp);
        (new Statistics())->userLevelInfo();
    }


    /**
     * 测试消息队列
     * @throws Exception
     */
    public function actionTestJob()
    {
        // 普通队列
        for ($i = 0; $i < 10; $i++) {
            \Yii::$app->queue->push(new TestJob(['data' => 'Hello world, 普通队列' . ($i + 1)]));
        }

        // 延时队列，120秒
        for ($i = 0; $i < 10; $i++) {
            \Yii::$app->queue->delay(2 * 60)->push(new TestJob([
                'data' => 'Hello world, 延时120秒的队列' . ($i + 1) . '，时间：' . date('Y-m-d H:i:s', time())
            ]));
        }
    }


    //一元链接
    public function actionNaryLink()
    {
        (new Statistics())->naryLink();
    }

    //会员复购信息
    public function actionMemberRepeat()
    {
        (new Statistics())->memberRepeatPeople();
        (new Statistics())->memberRepeatBuy('part');
        (new Statistics())->memberRepeatBuy('main');
    }

    //统计配件复购频次
    public function actionRepeatBuyTime()
    {
        (new Statistics())->repeatBuyTime(1);
        (new Statistics())->repeatBuyTime(2);
        (new Statistics())->repeatBuyTime(3);
        (new Statistics())->repeatBuyTime(4);
    }

    //复购次数
    public function actionRepeat()
    {
        (new Statistics())->repeatTime(1);
        (new Statistics())->repeatTime(2);
        (new Statistics())->repeatTime(3);
        (new Statistics())->repeatTime(4);
    }

    //客单价
    public function actionCustomerPrice()
    {
        (new Statistics())->customerPrice();
    }

    //注册数据
    public function actionRegister()
    {
        (new Statistics())->register_data();
        (new Statistics())->buy_data();
    }

    //截断推荐表
    public function actionTruncateRecommend()
    {
        (new Statistics())->truncateRecommend();
    }

    // 统计会员信息
    public function actionMemberBuyInfo()
    {
        (new Statistics())->memberBuyInfo();
        (new Statistics())->memberBuyInfo('main');
    }

    public function actionOrderSend()
    {
        (new ErpService())->orderSendUpdate(['deal_code'=>'2023062763361179934210862306430','shipping_code'=>'zhongtong','shipping_sn'=>'78699632869796']);
    }


    public function actionDeleteActivity()
    {
        //替换ac_id为线上真实活动ID
        $acId=0;
        list($st, $msg) = by::activityConfigModel()->deleteData($acId);
        print_r($msg);die();
    }


    public function actionStatistics()
    {
        (new Statistics())->memberBuyInfo();
        (new Statistics())->memberBuyInfo('main');

        (new Statistics())->memberRepeatPeople();
        (new Statistics())->memberRepeatBuy('part');
        (new Statistics())->memberRepeatBuy('main');

        (new Statistics())->repeatTime(1);
        (new Statistics())->repeatTime(2);
        (new Statistics())->repeatTime(3);
        (new Statistics())->repeatTime(4);
    }

    public function actionAddWechat()
    {

      ReissueIntegral::factory()->addWechat();
    }

    /**
     * @throws Exception
     * @throws RedisException
     */
    public function actionWeWork()
    {

//        \Yii::$app->queue->delay(20)->push(new UpdateUserStoreJob([
//            'unionId' => 'oGGvJ51QCeuVo3vc0sGIlj1zUOC0',
//            'store'   => '杭州七堡花园城店'
//        ]));
        // by::RuserExtend()->updateStoreData('oGGvJ51QCeuVo3vc0sGIlj1zUOC0', '杭州七堡花园城店');
        // by::userExtend()->updateStoreData('oGGvJ51QCeuVo3vc0sGIlj1zUOC0', '杭州七堡花园城店');
        // WeWork::factory()->saveSourceAddWeChat([], '');
        // \Yii::$app->queue->delay(30)->push(new WeWorkSyncJob([
        //     'type'        => 1,
        //     'queryParams' => [],
        //     'xmlData'     => ''
        // ]));
        WeWork::factory()->syncHistoryData();
    }

    public function actionTestToken()
    {

        \Yii::$app->queue->push(new FinishTaskJob(['user_id'=>613029,'order_no'=>'20231011492210607633142310915']));
        // EventMsg::factory()->run('buyGoods',['user_id'=>613029,'order_no'=>'20231011492210607633142310915']);//发放订单完成奖励

    }

    public function actionDepositOrder()
    {
        by::Omain()->addDepositTailRecord(613031,'202312206497524620682862312493');
        //
    }



    public function actionOperPlatForm()
    {
//      (new Statistics())->syncMemberOrderPoint();
      // (new Statistics())->operPlatForm();
        var_dump(by::productReg()->GetRegDataCountByPhone('15171423600'));
    }


    public function actionMarket(): int {
        // 缓存 marketConfig 实例和常量
        $marketConfig   = by::marketConfig();
        $fixedValidType = $marketConfig::VALID_TYPE['fixed'];

        // 查询数据，避免冗余字段查询
        $list = $marketConfig::find()
                ->select(['id', 'resource'])
                ->asArray()
                ->all();

        $updateData = [];
        foreach ($list as $item) {
            // 安全解码 JSON，避免报错
            $resource = json_decode($item['resource'], true);

            if (isset($resource['valid_type']) && $resource['valid_type'] == $fixedValidType) {
                // 获取有效期时间
                list($start_time, $expire_time) = $marketConfig->expireTime($fixedValidType, $resource['valid_val']);

                // 格式化时间并拼接
                $resource['valid_val'] = date('Y-m-d H:i:s', $start_time) . '~' . date('Y-m-d H:i:s', ($expire_time + 86399));
                // 准备更新数据
                $updateData[] = [
                        'id'       => $item['id'],
                        'resource' => json_encode($resource, 320)
                ];
            }
        }

        // 生成批量更新 SQL，使用更安全的方式避免 SQL 注入
        if (!empty($updateData)) {
            $updateSql = CUtil::batchUpdate($updateData, "id", $marketConfig::tableName());

            // 执行批量更新
            return by::dbMaster()->createCommand($updateSql)->execute(); // 返回更新的行数，或作其他处理
        }

        return 0; // 如果没有更新任何数据，返回 0
    }
}
