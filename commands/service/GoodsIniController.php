<?php

namespace app\commands\service;
use app\commands\CController;
use app\components\IniPrice;
use app\models\by;
use yii\db\Exception;

class GoodsIniController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'syn-ini_0' => 1,
            'syn-ini_1' => 1,
            'syn-ini_2' => 1,
            'syn-ini_3' => 1,
            'syn-ini_4' => 1,
            'syn-ini_5' => 1,
            'syn-ini_6' => 1,
            'syn-ini_7' => 1,
            'syn-ini_8' => 1,
            'syn-ini_9' => 1,
            'syn-ini-list'=>1,
        ];
    }

    /**
     * 每分钟产生对应的自定义价格
     */
    public function actionSynIni_0(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_1(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_2(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_3(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_4(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_5(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_6(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_7(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_8(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    public function actionSynIni_9(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        IniPrice::factory()->synIniPrice($index);
    }

    /**
     * @throws Exception
     */
    public function actionSynIniList()
    {
        IniPrice::factory()->synNewIniPrice();
    }
}
