<?php

namespace app\commands\service;
use app\commands\CController;
use app\models\by;
use app\modules\wares\services\goods\IndexGoodsMainService;

class WaresController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'refresh' => 1,
        ];
    }

    /**
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionRefresh()
    {
        $goodsMainModel = by::GoodsMainModel();
        $service = new IndexGoodsMainService();
        $r_key1 = $goodsMainModel->GetAllGoodsSimpleInfoKey();
        $redis = by::redis();
        if(empty($redis->get($r_key1)) || $redis->ttl($r_key1) <= 600){
            $service->GetAllGoodsSimpleInfo($goodsMainModel::SOURCE['POINTS'],false);
        }
        echo 'ok';
    }
}
