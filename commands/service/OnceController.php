<?php

namespace app\commands\service;
use app\commands\CController;


use app\components\ErpNew;
use app\components\IotDs;
use app\components\Mall;
use app\components\Prometheus;
use app\components\RuiYun;
use app\components\WeiXin;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\CouponService;
use app\modules\back\services\PlumbingService;
use app\modules\back\services\PointPushService;
use app\modules\back\services\TryOrdersService;
use app\modules\goods\models\OmainModel;
use app\modules\goods\services\ErpService;
use app\modules\goods\services\StatisticService;
use app\modules\main\models\WxPayModel;
use app\modules\main\services\ActivityConfigService;
use app\modules\main\services\E3Service;
use app\modules\main\services\ErpOrderService;
use app\modules\main\services\GiftCardService;
use app\modules\main\services\ZhimaService;
use app\modules\wares\services\cocreate\CoCreateMaterialService;
use yii\db\Exception;

class OnceController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'up-down'           => 1,
            'cancel-order'      => 1,
            'sync-stock'        => 1,
            'add-order'         => 1,
            'detail-order'      => 1,
            'add-coin'          => 1,
        ];
    }

    /**
     * @throws \yii\db\Exception
     * 同步导购表状态
     */
    public function actionTest() {
        by::OsourceR()->getNeedHandleOrder();
    }


    public function actionOmsBu()
    {
        $tb = 'db_dreame_log.t_erp_err_log';
        $s  = by::dbMaster()->createCommand()->update($tb, ['nums' => 10], ['nums' => 11, 'status' => 1])->execute();
        echo $s;
        exit();
    }



    /**
     * @param int $type
     * @throws \yii\db\Exception
     * 生成地址库js文件
     */
    public function actionTest11($type = 1)
    {
        foreach ([1,2,3] as $type){
            switch ($type) {
                case 1:
                    $sql = "select * from `db_dreame`.`t_area_2` where pid = 1 and is_show =1";
//                $list = by::model('AreaModel', MAIN_MODULE)->GetList(1);
//                $list = by::area()->getList(0);
                    $list = by::dbMaster()->createCommand($sql)->queryAll();
                    $res = [];
                    foreach($list as $val) {
                        $res[] = [
                            'label' => $val['name'],
                            'value' => $val['id'],
                        ];
                    }

                    $json = json_encode($res, JSON_UNESCAPED_UNICODE);
//                $json = 'var provinceData='. json_encode($res, JSON_UNESCAPED_UNICODE). ';export default provinceData;';
                    CUtil::debug($json, 'province.json');
                    break;

                case 2:
                    $sql = "select * from `db_dreame`.`t_area_2` where pid = 1 and is_show =1";
//                $list = by::model('AreaModel', MAIN_MODULE)->GetList(1);
//                $list = by::area()->getList(0);
                    $list = by::dbMaster()->createCommand($sql)->queryAll();
//                $list = by::model('AreaModel', MAIN_MODULE)->GetList(0);
                    $ress = [];
                    foreach($list as $val) {
                        $citylist = by::model('AreaModel', MAIN_MODULE)->GetRealList($val['id']);
                        $res = [];
                        foreach ($citylist as $v) {
                            $res[] = [
                                'label' => $v['name'],
                                'value' => $v['id'],
                            ];
                        }
                        $ress[] = $res;

                    }
                    $json = json_encode($ress, JSON_UNESCAPED_UNICODE);
                    CUtil::debug($json, 'city.json');
                    break;

                case 3:
                    $sql = "select * from `db_dreame`.`t_area_2` where pid = 1 and is_show =1";
//                $list = by::model('AreaModel', MAIN_MODULE)->GetList(1);
//                $list = by::area()->getList(0);
                    $list = by::dbMaster()->createCommand($sql)->queryAll();
                    $resss = [];
                    foreach($list as $val) {
                        $citylist =  by::model('AreaModel', MAIN_MODULE)->GetRealList($val['id']);
                        $ress = [];
                        foreach ($citylist as $v) {
                            $arealist = by::model('AreaModel', MAIN_MODULE)->GetRealList($v['id']);
                            $res = [];
                            foreach ($arealist as $v1) {
                                $res[] = [
                                    'label' => $v1['name'],
                                    'value' => $v1['id'],
                                ];
                            }
                            $ress[] = $res;

                        }
                        $resss[] = $ress;

                    }
                    $json = json_encode($resss, JSON_UNESCAPED_UNICODE);
                    CUtil::debug($json, 'area.json');
            }
        }




    }

    public function actionTest3()
    {
        $url = 'http://39.103.170.7/e3_test/webopm/web/?app_act=order/order_list/get_region&parent_id=';

        $tb = 'db_dreame.t_area_2';

        $sql = "select * from {$tb} where `region_type` = 2";

//        $list = [
//            ['id' => 1]
//        ];
        $list = by::dbMaster()->createCommand($sql)->queryAll();

        foreach($list as $value) {
            $url1 = $url.$value['id'];

            $a = CUtil::curl_get($url1);

            $b = json_decode($a, 1);

            $data = [];
            foreach($b as $val) {

//            exit(var_export($val,1));

                $data[] = [
                    'id' => $val['region_id'],
                    'pid'=> $value['id'],
                    'name'=>$val['region_name'],
                    'region_type' => $val['region_type']
                ];


            }

            $c = by::dbMaster()->createCommand()->batchInsert($tb, ['id','pid','name','region_type'],$data)->execute();

//            $data = [];

            echo $c.'|';

        }

    }

    public function actionTest4(){
        $db = by::dbMaster();
        $tb1 = 'db_dreame.t_area';
        $tb2 = 'db_dreame.t_area_2';
        $sql1 = "select * from {$tb1} where pid = 0";
        $sql2 = "select * from {$tb2} where pid = 1";

        $list1 = $db->createCommand($sql1)->queryAll();
        $list2 = $db->createCommand($sql2)->queryAll();

        $l1 = array_column($list1, 'name', 'id');
        $l2 = array_column($list2, 'name', 'id');


        foreach ($l1 as $k1 => $v1) {
            if (empty($l2[$k1])) {
                echo '无'.$v1."\n";
                continue;
            }

            echo $v1.'-'.$l2[$k1]."\n";
        }
    }


    /**
     * 推送dreamehome注册
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionAppRegister()
    {
//        Mall::factory()->push(110,'centerRegister',['user_id'=>110]);
        Mall::factory()->push(552279,'centerQuery',['user_id'=>552279]);
//        Mall::factory()->push(51,'centerUpdate',['user_id'=>51]);
//        Mall::factory()->push(5100111111,'centerParseToken',['jwtToken'=>'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiIwMDAwMDAiLCJjb3VudHJ5IjoiRVUiLCJ1c2VyX25hbWUiOiJKSzg5OTMzOSIsImF1dGhvcml0aWVzIjpbInVzZXIiXSwiY2xpZW50X2lkIjoiZHJlYW1lX2FwcHYxIiwicm9sZV9uYW1lIjoidXNlciIsInVpZCI6IkpLODk5MzM5IiwidCI6MTY2NDAwOTE0OSwicm9sZV9pZCI6Ii0xIiwidSI6NTA1OTcxMywic2NvcGUiOlsiYWxsIl0sImRvbWFpbiI6bnVsbCwidXNlcl90b2tlbiI6dHJ1ZSwiZXhwIjoxNjY0MDA5NzQ5LCJsYW5nIjoiemgiLCJyZWdpb24iOm51bGwsImp0aSI6ImE3NjVlZjY3LWFjZWQtNDU4NS05ZTY4LTcwY2ExMjg1Mjc5OCJ9.YR_CX-SQ9QERD1mkH2jGtgcEpMBxsjPcWubu72YPUtI']);
//        Mall::factory()->push(CUtil::getMillisecond(),'centerParseToken',['jwtToken'=>'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiIwMDAwMDAiLCJjb3VudHJ5IjoiRVUiLCJ1c2VyX25hbWUiOiJKSzg5OTMzOSIsImF1dGhvcml0aWVzIjpbInVzZXIiXSwiY2xpZW50X2lkIjoiZHJlYW1lX2FwcHYxIiwicm9sZV9uYW1lIjoidXNlciIsInVpZCI6IkpLODk5MzM5IiwidCI6MTY2NDAwOTE0OSwicm9sZV9pZCI6Ii0xIiwidSI6NTA1OTcxMywic2NvcGUiOlsiYWxsIl0sImRvbWFpbiI6bnVsbCwidXNlcl90b2tlbiI6dHJ1ZSwiZXhwIjoxNjY0MDA5NzQ5LCJsYW5nIjoiemgiLCJyZWdpb24iOm51bGwsImp0aSI6ImE3NjVlZjY3LWFjZWQtNDU4NS05ZTY4LTcwY2ExMjg1Mjc5OCJ9.YR_CX-SQ9QERD1mkH2jGtgcEpMBxsjPcWubu72YPUtI']);

    }

    /**
     * 推送dreamehome批量注册
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionAppBatchRegister()
    {
        Mall::factory()->batchRegister();
    }


    public function actionBuGuide()
    {
        $data = by::onceModel()->GuideRecover();
        var_dump($data);
        exit();
    }

//
//    /**
//     * 批量注销未授权用户
//     * @throws \yii\db\Exception
//     */
//    public function actionAppBatchDeprecated()
//    {
//        Mall::factory()->batchDeprecated();
//    }
//
//    public function actionMergeUsers()
//    {
//        Mall::factory()->mergeUser();
//    }
//
//    /**
//     * @throws Exception
//     * 批量订阅只执行一次
//     */
//    public function actionPushNewAccount()
//    {
//        by::Ouser()->pushOrderToNewAccount();
//    }


    /**
     * 退款操作；参数：订单号1:退款单号1；示例：php yii service/once/order-refund-bu 123:abc,456:bbc
     * @param string $params
     * @return string
     * @throws Exception
     * @throws \RedisException
     */
    public function actionOrderRefundBu(string $params = '')
    {
        // 1、验证
        if (!$params) {
            echo '参数为空，退出操作！';
            return '';
        }
        // 2、处理参数
        $orders = [];
        $items = explode(',', $params);
        foreach ($items as $item) {
            // 验证
            if (empty($item)) {
                echo '参数错误，退出操作！';
                return '';
            }
            $item = explode(':', $item);
            // 验证
            if (count($item) != 2 || empty($item[0]) || empty($item[1])) {
                echo '参数错误，退出操作！';
                return '';
            }
            $orders[$item[0]] = $item[1];
        }
        // 3、处理订单数据
        by::onceModel()->refund($orders);
        echo '执行完成！';
        return '';
    }

    /**
     * 订单从待发货，到已完成 参数：订单号:物流单号:物流编号；示例：php yii service/once/from-dfh-to-ywc 2023020352275209558938792302205:SF1617402107058:sf
     * @param string $item
     * @return void
     * @throws Exception
     */
    public function actionFromDfhToYwc(string $item = '')
    {
        // 物流
        $shippingTypes = [
            'zto' => '中通快递',
            'sf'  => '顺丰快递',
        ];

        $item = explode(':', $item);
        // 验证参数
        if (count($item) != 3 || empty($item[0]) || empty($item[1]) || empty($item[2])) {
            echo '参数错误，退出操作！';
            return;
        }

        // 0. 获取订单
        $order = by::Omain()->getInfoByNo($item[0]);
        if (empty($order)) {
            echo '订单不存在，退出操作！';
            return;
        }

        // 1. 待发货 发货 -> 待收货
        $data = [
            'deal_code'     => $item[0],
            'shipping_code' => $item[2],
            'shipping_sn'   => $item[1],
            'shipping_name' => $shippingTypes[$item[2]] ?? ''
        ];

        list($status, $ret) = (new ErpService())->orderSendUpdate($data);
        if (!$status) {
            echo '商品发货失败，退出操作！';
            echo '异常原因：' . $ret;
            return;
        }

        // 2. 待收货 -> 已完成
        $finish_time = intval(START_TIME) - 86400 * 7; // 7 天之前
        list($status, $ret) = by::Omain()->Finish($order['user_id'], $item[0], $finish_time);
        if (!$status) {
            echo '商品收货失败，退出操作！';
            echo '异常原因：' . $ret;
            return;
        }
        echo '执行成功';
    }

    /**
     * 工单退款操作；参数：订单号1:退款单号1；示例：php yii service/once/plumbing-refund-bu 123:abc,456:bbc
     * @param string $params
     * @return string
     * @throws Exception
     * @throws \RedisException
     */
    public function actionPlumbingRefundBu(string $params = '')
    {
        // 1、验证
        if (!$params) {
            echo '参数为空，退出操作！';
            return '';
        }
        // 2、处理参数
        $orders = [];
        $items = explode(',', $params);
        foreach ($items as $item) {
            // 验证
            if (empty($item)) {
                echo '参数错误，退出操作！';
                return '';
            }
            $item = explode(':', $item);
            // 验证
            if (count($item) != 2 || empty($item[0]) || empty($item[1])) {
                echo '参数错误，退出操作！';
                return '';
            }
            $orders[$item[0]] = $item[1];
        }
        // 3、处理订单数据
        PlumbingService::getInstance()->refund($orders);
        echo '执行完成！';
        return '';
    }



    public function actionHelpPay()
    {
        if(!YII_ENV_PROD){


            $mOuser = by::Ouser();
//            $order  = $mOuser->CommPackageInfo(3427, '2023031344533197821290262303921', false, false, true);
//            by::wxH5Pay()->afterPay(3427,'2023031344533197821290262303921',['transaction_id'=>time()],$order,by::wxH5Pay()::SOURCE['MALL']);

            $order  = $mOuser->CommPackageInfo(3462, '2023032452590277637909152303936', false, false, true);
            var_dump($order);
            $s =by::wxPay()->afterPay(3462,'2023032452590277637909152303936',['transaction_id'=>time()],$order,by::wxPay()::SOURCE['MALL']);
            var_dump($s);


            $buList = [
//                '2023030740794248735887542303932'=>3566,
//                '20230309594703392060302442303087'=>3566,
//                '2023031653643929224272572303138'=>3579,
//                '2023031654080949356722842303910'=>3579,
//                '20230321350674017109442582303375'=>3493,
//                '20230321353134017109152752303061'=>3579,
//                '20230321353834016341242562303722'=>3579,
//                '20230321354074017109982482303312'=>3536,
//                '20230321355734017109932432303367'=>3585,
//                '2023032135628367091392352303523'=>3585,
//                '2023032137025373793972482303396'=>3566,
//                '2023032137125377861072572303340'=>3585,
//                '20230322504661345628582762303266'=>3579,
//                '20230322617701435749952032303122'=>3579,
//                '20230322628621473941322302303269'=>3579,
//                '20230322628891471262132672303673'=>3579,
//                '20230322630841470070182402303724'=>3585,
//                '20230322630951473941012522303538'=>3579,
//                '20230323505112134112842572303350'=>3536,
                '2023032452590277637909152303936'=>3462,
            ];

            foreach ($buList as $bu=>$item){
                //订单信息
                $oInfo = by::Odeposit()->CommDepositPackageInfo($item,$bu);
                var_dump($oInfo);
                if($oInfo){
                    //找出流水
                    $pay_log    = by::model('OPayModel','goods')->GetOneInfo($bu);
                    if($pay_log['pay_type'] == 2){
                        $s = by::wxH5Pay()->afterPay($item,$bu,['transaction_id'=>time()],$oInfo,by::wxH5Pay()::SOURCE['DEPOSIT']);
                    }else{
                        $s = by::wxPay()->afterPay($item,$bu,['transaction_id'=>time()],$oInfo,by::wxH5Pay()::SOURCE['DEPOSIT']);
                    }
                    sleep(6);
                    var_dump($s);
                }
            }




        }

    }

    public function actionHelpPay2()
    {
        $buList =  [
                '20241218594841812213393262412224' => 1490190,
                '20241218586211812149713222412774' => 1380894,
                '2024121754590768051243392412107'  => 1961039,
                '202412175511376806870882412540'   => 2934544,
                '202412175545576816320952412411'   => 2934575,
                '2024121761736134170805532412242'  => 3103067,
                '202412184150615477517412412822'   => 2934679,
                '2024121851913117216783542412969'  => 2919275,
                '2024121853894143819210202412761'  => 3107570,
                '20241218570071725982953622412448' => 1001528,
                '2024121858574181175038742412081'  => 2872061,
                '20241218586961811616273622412288' => 1064267,
                '20241218685932890211593142412731' => 898028,
                '20241218700082893256203782412570' => 736511,
                '2024121884916382266914502412742'  => 3109638,
                '2024121853876141485402942412311'  => 3107569,
                '20241218739463144131693862412047' => 1259471,
                '2024121875250340614172262412652'  => 3109720,
                '2024121878130364115933672412286'  => 1879682,
                '2024121879067364623449872412513'  => 2801160,
                '20241217703761913858723882412731' => 2667268,
                '20241218563081437217093922412062' => 612268,
                '20241218567481437510733292412068' => 1001528,
                '20241218577981811133023082412855' => 857804,
                '20241218615642112694383512412149' => 598031,
                '20241218670052633927343482412647' => 1599285,
                '2024121765158161188168402412792'  => 3103599,
                '2024121765255160796923802412682'  => 3103586,
                '202412184694287661330172412988'   => 3072387,
                '2024121850756116955077282412592'  => 3107303,
                '2024121853690141484300282412514'  => 3015768,
                '2024121853898143704329742412983'  => 3107563,
                '20241218768953401863893452412829' => 1282537,
                '2024121765318160815196192412174'  => 3103480
        ];
        foreach ($buList as $bu=>$item){
            //订单信息
            $oInfo = by::Ouser()->CommPackageInfo($item,$bu);
            if($oInfo){
                //找出流水
                $pay_log    = by::model('OPayModel','goods')->GetOneInfo($bu);
                if($pay_log['pay_type'] == 2){
                    $s = by::wxH5Pay()->afterPay($item,$bu,['transaction_id'=>time()],$oInfo,by::wxPay()::SOURCE['MALL']);
                }else{
                    $s = by::wxPay()->afterPay($item,$bu,['transaction_id'=>time()],$oInfo,by::wxPay()::SOURCE['MALL']);
                }
                sleep(2);
                var_dump($s);
            }
        }
    }

    public function actionUserMsgModel()
    {
        $service = new StatisticService();
        $service->statisticGoods(8,false);
    }

    public function actionRefundDeposit()
    {
        $orders = [
            '903662'=>'202308123523338428874402308808',
            '1042077'=>'202308123569939698359472308363',
        ];
        if($orders){
            foreach ($orders as $key=>$order){
                $orderInfo = by::Ouser()->CommPackageInfo($key,$order);
                $depositNo = $orderInfo['deposit_order_no']??'';
                if($depositNo){
                    by::OrefundDepositMain()->_judgeRefundDeposit($key,$depositNo);
                }
                //取消尾款订单
                list($s, $m) = by::Omain()->SyncInfo($key, $order, by::Omain()::ORDER_STATUS['CANCELED'], []);
            }
        }
        var_dump('1111');exit();
    }


    public function actionOmsPush()
    {
        $buList = [
                '2024121754590768051243392412107'  => 1961039,
                '202412175511376806870882412540'   => 2934544,
                '202412175545576816320952412411'   => 2934575,
                '2024121761736134170805532412242'  => 3103067,
                '202412184150615477517412412822'   => 2934679,
                '2024121851913117216783542412969'  => 2919275,
                '2024121853894143819210202412761'  => 3107570,
                '20241218570071725982953622412448' => 1001528,
                '2024121858574181175038742412081'  => 2872061,
                '20241218586961811616273622412288' => 1064267,
                '20241218685932890211593142412731' => 898028,
                '20241218700082893256203782412570' => 736511,
                '2024121884916382266914502412742'  => 3109638,
                '2024121853876141485402942412311'  => 3107569,
                '20241218739463144131693862412047' => 1259471,
                '2024121875250340614172262412652'  => 3109720,
                '2024121878130364115933672412286'  => 1879682,
                '2024121879067364623449872412513'  => 2801160,
                '20241217703761913858723882412731'  => 2667268
        ];

        foreach ($buList as $orderNo => $userId) {
            ErpNew::factory()->synErp('addOrder', ['user_id' => $userId, 'order_no' => $orderNo]);
            print_r("推送成功订单号：{$orderNo}");
            print_r(PHP_EOL);
        }

        exit();
    }


    public function actionReg()
    {
        $tb = by::Phone()::tbName();
        $user_id            = 0;

        while (true) {
            $sql  = " SELECT `user_id`,`phone`FROM  {$tb} WHERE `user_id`>:user_id GROUP BY `phone` ORDER BY `user_id` ASC LIMIT 100";
            $list = by::dbMaster()->createCommand($sql, [':user_id' => $user_id])->queryAll();
            if (empty($list)) {
                break;
            }
            $end     = end($list);
            $user_id = $end['user_id'];
            foreach ($list as $v) {
                $num = by::userCard()->getCountByGetChannel($v['user_id'],3);
                if($num > 5){
                    CUtil::debug($v['phone'].'|'.$num,'product_sn_total');
                }
                var_dump($num);exit();
            }
            usleep(1000);
        }
        return true;
    }


    public function actionRuiYunBuChang()
    {
        $stime = strtotime('2023-04-25 17:30:00');
        $etime = strtotime('2023-04-26 12:00:00');
        $tb                   = by::plumbingOrder()::tbName();
        $sql          = "SELECT * FROM {$tb} WHERE `ctime`>={$stime} and `ctime`<={$etime} ORDER BY `id` ASC ";
        $list = by::dbMaster()->createCommand($sql)->queryAll();
        if($list){
            foreach ($list as $v){
                RuiYun::factory()->push($v['user_id'],'save',['order_no'=>$v['order_no']??'','user_id'=>$v['user_id']]);
            }
        }
    }

    public function actionSendFs()
    {
        $s = CUtil::omsLock(0,'202305');
        var_dump($s);
    }


    public function actionPushErpNew()
    {
        $db         = by::dbMaster();
        $tb         = by::Gmain()::tbName();
        $id         = 0;
        $sql        = "SELECT `id` FROM {$tb} WHERE `id` > :id AND `status` = 0 AND is_del = 0 ORDER BY `id` LIMIT 100";

        while (true) {
            $list   = $db->createCommand($sql, [':id' => $id])->queryAll();
            if (empty($list)) {
                break;
            }

            $end    = end($list);
            $id     = $end['id'];

            foreach($list as $v) {
                ErpNew::factory()->pushGoods($v['id']);
            }

        }
    }


    public function actionBuNote()
    {
        $buList = [
            '202305230298880495289812305973',
            '2023052333461147407422122305511',
            '2023052334508148816484782305143',
            '2023052334752149219687402305419',
            '2023052339217155500662672305041',
            '2023052341773159029842212305624',
            '2023052343533161455400292305258',
            '2023052347629166876559222305730',
            '2023052348352167809045822305185',
            '2023052348913168514754512305741',
            '2023052351844172614458872305473',
            '2023052352006172858988122305641',
            '2023052353487174849282362305921',
            '2023052353754175215957382305264',
            '2023052353999175542305732305454',
            '2023052354241175913677272305358',
            '2023052354271175988586152305728',
            '2023052355346177466880702305524',
            '2023052359268182890981922305822',
            '2023052363277188450847802305552',
            '2023052364165189710700592305556',
            '2023052365096190989979232305255',
            '2023052365178191076924792305514',
            '2023052365266191222565622305962',
            '2023052370903199122583662305991',
            '2023052374428204132087572305043',
            '2023052374571204326183602305283',
            '2023052382319214903978782305878',
            '2023052436971258760866262305537',
            '2023052437598259612940512305851',
        ];
        foreach ($buList as $item){
           $orderInfo = by::Omain()->getInfoByNo($item);
           if($orderInfo){
               ErpNew::factory()->addOrder($orderInfo['user_id'],$orderInfo);
           }
        }
    }



    public function actionChangeOa()
    {
        by::onceModel()->transferOpenid();
    }

    public function actionWxOa()
    {
        $v= by::WxNotice()->wxOaNoticePush(1166268,1,['nickname'=>151515]);
        $v= by::WxNotice()->wxOaNoticePush(903662,1,['nickname'=>151515]);
        var_dump($v);exit();

        $s = WeiXin::factory()->getUniqueAccessToken(WeiXin::UQ_TOKEN['OANEW']);
        var_dump($s);exit();
    }

    public function actionSaveLog()
    {
//        for($i=1;$i<500;$i++){
//            CUtil::debug('测试 oooasdksajhfnjsajndjsajf'.$i,'err.base');
//        }
//        for($i=1;$i<500;$i++){
//            \Yii::$app->queue->push(new AliYunLogJob(['topic'=>'warn.base.queue','content'=>'测试warning queue 506*……*（**……%（*Y*%&*%^%^&&^%^&']));
//        }
//        for($i=1;$i<500;$i++){
//            \Yii::$app->queueLog->push(new AliYunLogJob(['topic'=>'warn.base.queueLog','content'=>'测试warning queueLog R%$%^#%^(*U&*&%%$$&^*&^JKNFGB%*&K<KJGHG']));
//        }


//        $arr = [
//            'cid'    => 'tm',
//            'city'   => 'tm',
//            'detail' => 'tm',
//            'phone'  => 'tm',
//        ];
//
//        $info= Response::responseList([
//            'aid'=>"110101",
//            'area'=>"东城区",
//            'cid'=>"110101",
//            'city'=>"市辖区",
//            'detail'=>"中华人民万岁",
//            'phone'=>"123452315541",
//          ],$arr);
//        var_dump($info);exit();


//        $s = by::WarrantyCard()->CreateCardNo(strtotime('2023-05-24 5:00:00'),5555,'5451251');
//        var_dump($s);

//        $s = substr(md5('dreamehome-openudid-59-1662453808|1666041939.2041' . 'dreame@2023'), 8, 16);
//        var_dump($s);exit();


//        $productSn = ProductSn::factory()->getProductSn("R2263037BCN0065201");

//        $interval = new \DateInterval("P15M"); // 创建一个表示几个月的时间间隔
//        var_dump($interval);exit();
//        $date = new \DateTime('2023-07-26 15:00:00');
//        $interval = new \DateInterval("P12M");
//        $date->add($interval);
//        var_dump($date->format('Y-m-d H:i:s'));exit();
//
//        var_dump($productSn);
//        exit();


//        var_dump(CUtil::removeXss('https://wpm-cdn.dreame.tech/images/202308/64cb16d9d38538663919317.jpeg,https://wpm-cdn.dreame.tech/images/202308/64cb16da2c8041823917022.png'));exit();
        var_dump( trim(strval('<script>alert("XSS攻击！")</script>')));
//       $s= by::WarrantyApply()->GetListByUser('4646',['sns'=>['P2229003CN0264979']]);

    }



    public function actionOmsOrder()
    {
        $st = strtotime('2023-08-02 18:30:00');
        $ed = strtotime('2023-08-03 12:20:00');
        $order_time = [
            'st' => $st,
            'ed' => $ed,
        ];

        $orderNos = [];

        //退款订单
        $list = by::OrefundMain()->GetList('', '', -1, $order_time,1, 1000);
        if($list){
            foreach ($list as $li){
                //查询
                $r_info = by::Orefund()->GetInfoByRefundNo($li['user_id'], $li['refund_no']);
                if($r_info) {
                    by::OrefundMain()->refundPushOms($r_info['m_type'] ?? 0, $li['user_id'], $li['refund_no'], $r_info['order_no']);
                }
            }
            $orderNos = array_column($list,'order_no');
        }


        //新订单
        $listNew = by::Omain()->GetList(2023,'','',-1,-1,$order_time,-1,'',0,1,10000);
        if($listNew){
            foreach ($listNew as $lin){
                if(!in_array($lin['order_no'],$orderNos)){
                    ErpNew::factory()->synErp('addOrder',['user_id'=>$lin['user_id'],'order_no'=>$lin['order_no']]);
                }
            }
        }

        echo 'done'; exit();
    }

    public function actionSn()
    {
//        $sn = 'P2126A123CN12312312';
//        $time = CUtil::getTimeBySn($sn,1691856000);
//        var_dump(date('Y-m-d',$time));exit();
//
//        by::redis()->set('sasdasd',1);

//        (new ErpService())->_waresStock([
//                 ['sku'=>'12211','stock'=>100],
//                 ['sku'=>'xp11401ui','stock'=>1000],
//                 ['sku'=>'12211','stock'=>100],
//                 ['sku'=>'0154511558ii','stock'=>5000],
//                 ['sku'=>'12211','stock'=>500],
//        ]);

by::GoodsStockModel()->GetSkuByGidAndSid(102,23,1);
    }


    public function actionSnm()
    {
        by::onceModel()->warrantyModify();
    }

    public function actionIni()
    {
        echo ini_get('post_max_size').'<br>'; //将打印出post_max_size的值
        echo ini_get('upload_max_filesize').'<br>'; //将打印出upload_max_filesize的值
    }




    /**
     * @throws \Prometheus\Exception\MetricsRegistrationException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function actionPro()
    {
//        Prometheus::factory()->pushRegistry([]);
//        Prometheus::factory()->interfacePv(['url'=>'https://dreame/main/cart/list','iRet'=>'1']);
//        Prometheus::factory()->interfaceUv(['url'=>'https://dreame/main/cart/list','timeUsed'=>68.55524]);
//        Prometheus::factory()->interfaceUv(['url'=>'http://127.0.0.1:8081/back/cate/list','timeUsed'=>31]);
//        Prometheus::factory()->interfaceUv(['url'=>'https://dreame/main/cart/list','timeUsed'=>10]);
//        Prometheus::factory()->interfaceUv(['url'=>'https://dreame/main/cart/list','timeUsed'=>50]);
//        Prometheus::factory()->interfaceUv(['url'=>'https://dreame/main/cart555/list5','timeUsed'=>68.521]);
//        Prometheus::factory()->interfaceUv(['url'=>'https://dreame/main/cart555/list5','timeUsed'=>100]);

//        for ($i=0;$i<100;$i++){
//            Prometheus::factory()->interfaceUv(['url'=>'http://127.0.0.1:8081/back/cate555555/list','timeUsed'=>rand(200, 2000)]);
//        }

        for ($i=0;$i<100;$i++){
//            Prometheus::factory()->syncPrometheus('interfaceUv',['url'=>'http://127.0.0.1:8081/back/catep6/list','timeUsed'=>rand(20, 2000)]);
sleep(15);
            Prometheus::factory()->interfaceUv(['url'=>'http://127.0.0.1:8081/back/cate555555/list','timeUsed'=>rand(200, 2000)]);
        }
//        for ($i=0;$i<100;$i++){
////            Prometheus::factory()->syncPrometheus('interfaceUv',['url'=>'http://127.0.0.1:8081/back/catep6/list','timeUsed'=>rand(20, 2000)]);
//
//            Prometheus::factory()->interfaceUv(['url'=>'http://127.0.0.1:8081/back/cate555555/list','timeUsed'=>rand(200, 2000)]);
//        }


        $str = '{
	"return_code": "SUCCESS",
	"appid": "wx7042d29dafd01227",
	"mch_id": "1636516125",
	"nonce_str": "143ea885ea206e5776b69cbdb6b6f0bc",
	"req_info": "0BC+GluZynzuNccVrZ0gAXln98EJICgf+Ts\/EJMn0+2TEYFx3Fwab9sOketdETxsyZaaBHle8+BmkpPtdYzvgTbfJIqovN8bReySQQkNeqNDsTuX\/KQq2q35pTIzMPMRJJ5iuJlB2gR0dM+7PdnLcRSXF1KCMpWrszqQXxJGXE9hD8r3i\/NlKrZqiVdO+i94O\/RAAgXvijqPdUtUYwFF1VtmD\/BS+dX0uzfFzIZ9w4Kgh+wxRzf83kI4HwMMzfnIWA6JBdbuLl4Nxa88MioAOXuMgNy50gQk+gaipVqr\/3DLP1DcL9m4eB9wmKXDGhfyOFfOQJ0FKVTJF4m5P52WqWFvUBT613X\/XbSZn0ZnmcRfXvAJEnA6FD0DyLSrAKE\/wqYsrFrzHGVoE45Lyt7kkB+DTDww9m5SR4ZJ\/d6VE9xbcPWXWRhjLiwsix83t4gp\/pePNSgU3\/aISbUVUaJcQzpo8EPJX90OXakZ0HH+14yZsgZjIOrfrerjnpvNNSvOrZIxnLC7+7WyDj\/Sa\/+5MOhPSsu3SGQXANxD06t2oJ09hCCJfroSX1USBGOjZrjafTXgEeBESl9xRrDJOAQj2kQxLjL3+tzDM8P9HrC7YwoYBT4ILFPvCDx04jLuRU+0SytoXD0a+PfkirCdS5v1BPm4m7u\/9jqTRstWnEZuCrN04Tg1UExyjMzcVAJxTjR7I15LbOhoNj7Lh+FrISILR2ZxOYoZtCPUF2ghecfVdm2Vyxbzq6\/K6OVSO3u8DPvW6F6eAFQJnXRtZPUgh\/AsZVb13tA8cG7gO8ftZA9TFW9XbfDVF6UMLbdrWM\/UoUcrxJzMnfQqE0VX+7wFloo7hp+hDtCkieWgcDnbDsevqUblfx+S\/J3iBgWFEtlCpA8TPYRfnrUgI7hqdY6WAgXvFeOwroseJeisjgYQPSkY6eLxuXQguDOc7oIGH5xGzsy\/ssZhQojIxYhIvaN3TLCEkPaskQ7qyBqrvOUspOPyUEMS8t78lYLZmS236liBzrHRsSGvNxZNKM2Dwr7TE+8Ct4satEoNEmcv18wbD6DEDRnEPumgsJKd2NNrVzftE\/bHuYzIHFKX2cZiHFkhVI2hoJCbXFsk2EzwDeR5dI+mmp6zOkEt4r03LSCSsJ2\/ZQcdVfdikmrn\/mPvQk+Cr+qXW0iVXoJ1gVr83MnEwPitJHQ="
}';
        $arr = json_decode($str,true);

        $str = CUtil::arrayToXml($arr);
        var_dump($str);exit();


    }


    public function actionWeWorkPhone()
    {
        by::onceModel()->syncWeWorkUserPhone();
    }


    public function actionBy()
    {
//        attr_cnf: [{"at_name":"口味","at_val":["甜","酸"]},{"at_name":"颜色","at_val":["红色","橙色"]}]

        $arr = [
            ['at_name'=>'口味',
            'at_val'=>[
               ['val'=>'甜',
               'image'=>'http://abc1.jpg'],
                [
                    'val'=>'酸',
                    'image'=>'http://abc2.jpg'
                ]
            ]],
            ['at_name'=>'颜色',
             'at_val'=>[
                 ['val'=>'红色',
                  'image'=>'http://abc3.jpg'],
                 [
                     'val'=>'橙色',
                     'image'=>'http://abc4.jpg'
                 ]
             ]],
        ];

        $arr = [
//          ['platform'=>1,'image'=>'http://abc.jpg'],
//          ['platform'=>2,'image'=>'http://abc2.jpg'],
            'type'=>'price',
            'act'=>'up',
        ];




        var_dump(json_encode($arr,300),date('Y-m-d H:i:s',0),strtotime(1693035289));exit();

//        [{"at_name":"口味","at_val":{"甜":"http:\/\/abc1.jpg","酸":"http:\/\/abc2.jpg"}},{"at_name":"颜色","at_val":{"红色":"http:\/\/abc2.jpg","橙色":"http:\/\/abc3.jpg"}}]

//        [{"at_name":"口味","at_val":[{"val":"甜","image":"http:\/\/abc1.jpg"},{"val":"酸","image":"http:\/\/abc2.jpg"}]}, {"at_name":"颜色","at_val":[{"val":"红色","image":"http:\/\/abc3.jpg"},{"val":"橙色","image":"http:\/\/abc4.jpg"}]}]

//        [{"at_val":"口味:甜,颜色:红色","sku":"12211","price":"0.01","points":"500","exchange":"1","coupon_id":"0","image":"http://abc.jpg"}]

    }


    public function actionBuyPoint()
    {
        $s = by::BasePayModel()->afterPay(612958,'202309145799621440363292309217',
            ['transaction_id'=>1512251021101,'time_end'=>'2023-09-14 16:57:20'],0,by::wxPay()::SOURCE['POINTS'],1);
        var_dump($s);
    }



    public function actionUpdateCo()
    {
        byNew::CoCreateUserModel()->UpdateInfoByPhone(5340);
    }

    public function actionPushE3()
    {
        $arr = [
          ['order_no'=>'20231031540122411363153252310393','user_id'=>1315544],
          ['order_no'=>'20231031540112420387153952310549','user_id'=>1380347],
          ['order_no'=>'20231031540082422392223092310074','user_id'=>1407721]
        ];
        foreach ($arr as $item) {
           $s = ErpNew::factory()->addOrder($item['user_id'], $item['order_no']);
           $a = by::Ouser()->CommPackageInfo($item['user_id'], $item['order_no'],false,false,true);
           var_dump(json_encode($a),320);
           var_dump(empty($a['address']));
           var_dump($a['address']);
           var_dump($s);
        }
    }


    public function actionSort()
    {
        for ($i = 0; $i < 1000; $i++) {
            $materialId = rand(118, 149);
            $topic = "car";
            $enjoy = rand(1, 3);
            CoCreateMaterialService::getInstance()->AddImageSort($materialId,$topic,$enjoy);
        }
    }


    public function actionPush()
    {
        $sql = "SELECT `cs`.`material_id`,`cs`.`enjoy`,`cm`.`topic` FROM  `db_dreame_wares`.`t_cocreate_statistic` AS `cs` JOIN `db_dreame_wares`.`t_cocreate_material` AS `cm` ON `cs`.`material_id` = `cm`.`id` WHERE  `cm`.`is_del` = 0 ORDER BY `cs`.`id` DESC ";
        $aData = by::dbMaster()->createCommand($sql)->queryAll();
        if($aData){
            foreach ($aData as $item) {
                CoCreateMaterialService::getInstance()->AddImageSort($item['material_id'],$item['topic'],$item['enjoy']);
            }
        }
        var_dump("exit");

    }

    public function actionSyncTopic()
    {
        $topicList = byNew::CoCreateMaterialModel()::TOPIC_LIST;
        $topicDb   = "`db_dreame_cocreate`.`c_image_topic`";
        $rows   = [];
        $fields = [];
        foreach ($topicList as $item) {
            $sql = "SELECT * FROM {$topicDb} WHERE `tag` = '{$item['key']}'";
            $data = by::dbMaster()->createCommand($sql)->queryOne();
            if($data){
                continue;
            }
            $row    = [
                'tag'      => $item['key'],
                'tag_name' => $item['value'],
            ];
            $fields = array_keys($row);
            $rows[] = $row;
        }
        by::dbMaster()->createCommand()->batchInsert($topicDb, $fields, $rows)->execute();
    }


    public function actionSyncImage()
    {
        $topicDb   = "`db_dreame_cocreate`.`c_image_topic`";
        $sql       = "SELECT * FROM {$topicDb}";
        $data = by::dbMaster()->createCommand($sql)->queryAll();
        $data = array_column($data,'id','tag');

        $materialDb = "`db_dreame_wares`.`t_cocreate_material`";
        $imageDb= "`db_dreame_cocreate`.`c_image_material`";
        $sql = "SELECT  `a`.* FROM  {$materialDb}  as `a` LEFT JOIN {$imageDb} AS `b` ON `a`.`url` = `b`.`url` WHERE `b`.`id` IS NULL";
        $aData = by::dbMaster()->createCommand($sql)->queryAll();
        if($aData){
            $rows         = [];
            $fields       = [];
            foreach ($aData as $item) {
                $row = [
                    'name'        => $item['name'],
                    'topic_id'    => $data[$item['topic']] ?? 0,
                    'url'         => $item['url'],
                    'create_time' => date('Y-m-d H:i:s', $item['ctime']),
                    'update_time' => date('Y-m-d H:i:s', $item['utime']),
                    'is_deleted'  => $item['is_del'],
                    'delete_time' => null
                ];

                if($item['is_del'] == 1){
                    $row['delete_time'] = date('Y-m-d H:i:s',$item['dtime']);
                }
                $fields = array_keys($row);
                $rows[] = $row;
            }
            by::dbMaster()->createCommand()->batchInsert($imageDb, $fields, $rows)->execute();
        }
    }
//    /usr/bin/php -f ./yii service/once/sync-topic
//    /usr/bin/php -f ./yii service/once/sync-image

//    /usr/bin/php -f ./yii service/once/sync-wares

    /**
     * @return void
     * @throws Exception
     * @throws \RedisException
     * 年终报告脚本 串行依次执行
     */
    public function actionYearReport()
    {
        byNew::YearReportModel()->syncAllUsers();
        byNew::YearReportModel()->syncAllUserBuy();
        byNew::YearReportModel()->syncDrawTimes();
        byNew::YearReportModel()->syncAddCartData();
        byNew::YearReportModel()->syncHairdryerData();
    }

    public function actionProductData()
    {
        byNew::YearReportModel()->syncProductData();
    }


    public function actionLastOrderTime()
    {
        byNew::YearReportModel()->syncLastOrderTime();
    }


    public function actionCheckIn()
    {
        byNew::YearReportModel()->syncCheckInTime();
        byNew::YearReportModel()->syncCheckInRate();
    }

    public function actionCheckOut()
    {
        var_dump(by::activityConfigModel()->activityRefundRule(548555,69));
        exit();

        $list = [
            ['sn'=>"",'id'=>50],
            ['sn'=>"44",'id'=>51],
            ['sn'=>"R2310B",'id'=>52],
            ['sn'=>"R2310C",'id'=>53],
            ['sn'=>"R2310F",'id'=>54],
            ['sn'=>"R2310F",'id'=>55],
            ['sn'=>"R2310F",'id'=>56],
        ];

        //判断是否存在最长的SN编码
        $sns = array_column($list, 'sn', 'id');
        // 第一次遍历，找出最长元素的长度
        $max_length = max(array_map('strlen', $sns));
        // 第二次遍历，找出所有长度等于最长长度的元素
        $longest_elements = array_filter($sns, function ($item) use ($max_length) {
            return strlen($item) == $max_length;
        });
        // 检查最长元素是否唯一
        if (count($longest_elements) > 1) {
           //输出这些最长的元素
            $snsAll = array_column($list,null, 'id');
            $listNew = [];
            foreach ($longest_elements as $key => $value) {
                if(isset($snsAll[$key])){
                    $listNew[] = $snsAll[$key];
                }
            }
           var_dump($listNew);
        } else {
            $key = array_keys($longest_elements)[0];
            var_dump($key);
        }
        exit();
    }


    public function actionQueryMark()
    {
//        list($status,$snInfo) = ErpOms::factory()->run('querySn',['order_nos'=>['DC202305090002'],'user_id'=>5770]);
//        $snArr = array_column($snInfo,'sn','dealCode');
//        $sn = $snArr['DC202305090002'] ?? '';
//       print_r($sn);exit();
//        $module = 'back';
//        $class_name = 'TryOrdersService';
//        $fun = 'exportData';
//
//        $obj = CUtil::getClass("services", $class_name, $module, [], []);
//        $res = call_user_func_array([$obj, $fun], [1,2,3,4,5,5,5,5,5,5,5,5]);
        $res = TryOrdersService::getInstance()->exportData();
        var_dump($res);
    }


    public function actionSign()
    {
        // $s = IotDs::factory()->run('cleanmodel', ['uids' => ['EU490876'],'startTime'=>1715752121,'endTime'=>'1717379027']);
        // var_dump($s);exit();
    }





    public function actionSign1(): string
    {
        $query['$tenant'] = "dreametst";
        $query['$reqid'] = "0eb30914-b001-397b-8d7c-01fdba3a0c4e";
        $query['$appid'] = "mova";
        $query['$timestamp'] = 1717064059898;
        $signKey = "Vo5crr3tqvk2c9TpxaxV1f1HYopaefakmIPqq8e8";
        ksort($query);
        $stringA = '';
        foreach ($query as $item){
            $stringA .= is_array($item) ? json_encode($item) : $item;
        }
        var_dump(strtoupper(md5($stringA . $signKey)));exit();
        return strtoupper(md5($stringA . $signKey));
    }

    public function actionPushRuiYun()
    {
        // $sql = "SELECT  * from `db_dreame_goods`.`t_plumbing_order` WHERE `ctime`>1717032758  and (`ruiyun_id` = '' or `ruiyun_detail_id` = '') ORDER BY `id` DESC";
        // $aData = by::dbMaster()->createCommand($sql)->queryAll();
        // if($aData){
        //     foreach ($aData as $item) {
        //         RuiYun::factory()->push($item['user_id'],'save',['order_no'=>$item['order_no']??'','user_id'=>$item['user_id']]);
        //     }
        // }
        // exit("OK");
//        $order_no = "20240613330341934266633012406413";
//        $aLog = by::model('OPayModel', 'goods')->GetOneInfo($order_no, false);
//        echo $aLog['tid'];
//        AliZhima::CancelOrder($order_no, $aLog['tid'] ?? '');


//        list($status, $data) = IotDs::factory()->run('cleanmodel', ['uids' => ['YH451730','NI357681'],'startTime'=>1717223633,'endTime'=>1718692433]);
        list($status, $data) = IotDs::factory()->run('activelist', ['sns' => ['R9301038ACN0008901']]);
        var_dump($status, $data);exit();


    }


    public function actionTryOrdersPhone()
    {
        $orderInfos = byNew::UserOrderTry()->GetList([]);
        if($orderInfos){
            foreach ($orderInfos as $orderInfo) {
                $order_no = $orderInfo['order_no'];
                $user_id = $orderInfo['user_id'];
                $phone = by::Phone()->GetPhoneByUid($user_id);
                if($phone){
                    $data = [
                        'phone_str' => byNew::UserOrderTry()->PhoneEncrypt($phone),
                        'order_no' => $order_no,
                        'user_id' => $user_id,
                    ];
                    byNew::UserOrderTry()->SaveLog($data);
                }
                continue;
            }
        }
        exit("OK");

    }
        // }
        // exit("OK");



//        by::model('RuiYunLogModel', 'main')->retry(1);
//        $s = by::model('WxPayModel', 'main')->wxOrderQuery("2024061051825243893898132406486",1,3);
//        list($s, $res) = AliPayModel::getInstance()->query("2024061051825243893898132406486", false);
//        var_dump($s);exit();

    public function actionUserKo()
    {
            $s= base64_encode("po_mem_user:SJVKOhHIQ]\iR1");
        var_dump($s);exit();
    }

    public function actionKo()
    {
//        $s = by::Omain()->CheckZhiMaOrderPayStatus('20240711607711026753923332407559','20240711607711026753923332407559_1720687972');
//        var_dump($s);exit();

//        $s = by::Omain()->Finish(1729193, '20240712359152200201323422407285');
//        var_dump($s);exit();


//        $regWhiteModel = byNew::RegWhiteListModel();
//        if($regWhiteModel->GetOneInfo(['sn'=>'R2228Z2CMCN06194999'])){
//            $regWhiteModel->SaveLog(['sn'=>'R2228Z2CMCN06194999','is_reg'=>1,'reg_time'=>time(),'reg_user_id'=>6273]);
//        }
//        var_dump(by::product()->match('R24851'));exit();
//
//        $sevice = WarrantyCardService::getInstance();
//        $period_time = $sevice->GetPeriodTimeBySn('6061', 'R2401041DCN00097089', strtotime('2024-07-18'));
//        var_dump($period_time);exit();
//
//
//        $sn_time = CUtil::getTimeBySn('R2401041DCN00097089', 1721281510);
//        var_dump($sn_time);exit();
//        if ($sn_time === false) {
//            return [false, '购买时间有误，请确认~', self::ERR_CODE['ERR']];
//        }

//       $s  = by::userCard()->UnLockCard(6272, 41,null);
//       var_dump($s);exit();

        $oldSn = byNew::SnRenovateModel()->GetOldSnByNewSn("R9316T446EU5399001");
        var_dump($oldSn);exit();
//       $s  = by::userCard()->UnLockCard(6272, 41,null);
//       var_dump($s);exit();

//        list($status, $res) = PointPushService::getInstance()->PushDataByPushNos("U6125P20240721145554338,U6125P20240721150821494");
//        var_dump($res);exit();

        $s = PointPushService::getInstance()->exportPointPushData();
        var_dump($s);exit();

        $sn_time = CUtil::getTimeBySn('R2401041DCN00097089', 1721281510);
        var_dump($sn_time);exit();
        if ($sn_time === false) {
            return [false, '购买时间有误，请确认~', self::ERR_CODE['ERR']];
        }

       $s  = by::userCard()->UnLockCard(6272, 41,null);
       var_dump($s);exit();

        $s = PointPushService::getInstance()->exportPointPushData();
        var_dump($s);exit();
    }



    /**
     * @param string $params 参数 order_no（订单号）:total_fee（总金额:分）:refund_fee（退款金额:分）:pay_type（支付方式）:refund_no（退款单号 随机写）
     * @return void
     */
    public function actionWxPayRefund(string $params = '')
    {
        $params = explode(':', $params);
        list($order_no, $total_fee, $refund_fee, $pay_type, $refund_no) = $params;


        $config = CUtil::getConfig('weixin', 'common', MAIN_MODULE);
        $appid  = $config['appId'] ?? "";

        if ($pay_type == OmainModel::PAY_BY_WX_APP) {
            $appid = WxPayModel::APP_PAY_APP_ID;
        }


        $aData['appid']         = $appid;
        $aData['mch_id']        = WxPayModel::MCH_ID;
        $aData['nonce_str']     = CUtil::createVerifyCode(10, 1);
        $aData['out_trade_no']  = $order_no;
        $aData['out_refund_no'] = $refund_no; //商户系统内部的退款单号
        $aData['total_fee']     = $total_fee ?? 0;
        $aData['refund_fee']    = $refund_fee ?? 0;
        $aData['sign']          = $this->_getSign($aData);
        $xml                    = CUtil::arrayToXml($aData);

        $response = CUtil::curl_post(WxPayModel::WX_REFUND_ORDER_URL, $xml, null, 10, true, 'IPV4', 'wx');
        print_r($response);die();
    }

    protected function _getSign($aData = [])
    {
        ksort($aData);
        $tmp = [];
        foreach ($aData as $key => $value) {
            !empty($value) && $tmp[] = "{$key}={$value}";
        }

        $key = WxPayModel::WX_MCH_ID_KEY;
        $stringA = implode("&", $tmp);
        $stringSignTemp = "{$stringA}&key={$key}";

        $sign = strtoupper(md5($stringSignTemp));

        return $sign;
    }
}
