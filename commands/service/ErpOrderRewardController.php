<?php

namespace app\commands\service;

use app\commands\CController;
use app\models\CUtil;
use app\modules\back\services\UserEmployeeService;
use app\modules\main\services\ErpOrderService;

/**
 * 脚本
 */
class ErpOrderRewardController extends CController
{

    function getOneInstanceActionList()
    {
        return [
            'erp-order-reward' => 1,
            'erp-order-retry-reward' => 1,
        ];
    }

    // 凌晨1点，线下订单积分发放

    /**
     * 线下订单积分发放
     */
    public function actionErpOrderReward()
    {
        try {
            ErpOrderService::getInstance()->cronOrderReward();
        } catch (\Exception $e) {
            CUtil::debug('线下订单积分发放失败', 'err.erp.erp-order-reward');
        }
    }
    /**
     * 线下订单积分发放
     */
    public function actionErpOrderRetryReward()
    {
        try {
            ErpOrderService::getInstance()->cronRetryOrderReward();
        } catch (\Exception $e) {
            CUtil::debug('线下订单积分补发失败', 'err.erp.erp-order-retry-reward');
        }
    }
}