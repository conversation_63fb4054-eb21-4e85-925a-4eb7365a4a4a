<?php

namespace app\commands\service;
use app\commands\CController;
use app\components\AdvAscribe;
use app\components\IniPrice;
use app\models\by;
use yii\db\Exception;

class AdvController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'syn-adv_0' => 1,
            'syn-adv_1' => 1,
            'syn-adv_2' => 1,
            'syn-adv_3' => 1,
            'syn-adv_4' => 1,
            'syn-adv_5' => 1,
            'syn-adv_6' => 1,
            'syn-adv_7' => 1,
            'syn-adv_8' => 1,
            'syn-adv_9' => 1,
            'final-retry' =>1,
            'clear-adv' =>1,
        ];
    }

    /**
     * 每分钟产生对应的自定义价格
     */
    public function actionSynAdv_0(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_1(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_2(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_3(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_4(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_5(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_6(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_7(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_8(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    public function actionSynAdv_9(){
        $func  = explode('_',__FUNCTION__);
        $index = $func[1] ?? -1;
        AdvAscribe::factory()->synAdv($index);
    }

    /**
     * adv最终重试 凌晨执行
     */
    public function actionFinalRetry(){
        by::model('AdvLogModel', 'main')->finalRetry();
    }

    /**
     * @throws Exception
     * @throws \RedisException
     */
    public function actionClearAdv()
    {
        $s = by::userAdv()->clearExpireData();
        !YII_ENV_PROD && var_dump($s);
    }

}
