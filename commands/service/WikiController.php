<?php

namespace app\commands\service;
use app\commands\CController;
use app\models\by;

class WikiController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'refresh'           => 1,
            'del-dtopic'        => 1,
        ];
    }

    /**
     * @throws \yii\db\Exception
     * 每分钟刷入数据
     */
    public function actionRefresh() {

        $sorts = by::WdynamicMain()::DYNAMIC_SORT;

        foreach ($sorts as $sort) {
            by::WdynamicMain()->SyncDynamicsList($sort);
        }

    }

    /**
     * 删除话题，内容关联关系也清除
     */
    public function actionDelDtopic()
    {
        by::Wtopic()->ShellDel();
    }



}
