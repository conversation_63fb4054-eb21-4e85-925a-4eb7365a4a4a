<?php

namespace app\commands\service;
use app\commands\CController;
use app\components\ErpNew;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\services\GmainService;
use app\modules\goods\services\OmainService;
use yii\db\Exception;

class GoodsController extends CController
{

    public function getOneInstanceActionList(): array
    {
        return [
            'up-down'              => 1,
            'up-down99'            => 1,
            'cancel-order'         => 1,
            'order-notify'         => 5,
            'sync-stock'           => 1,
            'add-order'            => 1,
            'detail-order'         => 1,
            'reject-refund'        => 1,
            'check-order'          => 1,
            'finish-order'         => 1,
            'order-reward'         => 1,
            'finish-order-reward'  => 1,
            'cancel-deposit-order' => 1,
            'change-deposit-goods' => 1,
            'cancel-tail-order'    => 1,
            'erp-retry'            => 1,
        ];
    }

    /**
     * @throws Exception
     * 商品上下架
     */
    public function actionUpDown() {
        by::Gtype0()->UpDown();
    }

    /**
     * @throws Exception
     * 一元链接商品上下架
     */
    public function actionUpDown99() {
        by::Gtype99()->UpDown();
    }

    /**
     * @throws Exception
     * 取消订单记录
     */
    public function actionCancelOrder() {
        $ret = by::Omain()->Cancel();
        YII_ENV_DEV && var_dump($ret);
    }

    /**
     * @throws Exception
     * @throws \RedisException
     * 取消订单记录前5分钟通知
     */
    public function actionOrderNotify() {
        $ret = by::Omain()->cancelOrderNotifyUser();
        YII_ENV_DEV && var_dump($ret);
    }
    /**
     * @throws Exception
     * 同步库存
     */
    public function actionSyncStock()
    {
        $lockOldErp = CUtil::omsLock(0,time());
        if($lockOldErp){
            return true;
        }
        by::Gmain()->SyncStock();
    }

    /**
     * @throws Exception
     * 新增erp订单
     */
    public function actionAddOrder()
    {
        by::Ouser()->ShellAddOrder();
    }

    /**
     * @throws Exception
     * 获取erp订单详情（发货）
     */
    public function actionDetailOrder()
    {
        //为防止新旧版本切换发不了货，暂时不予关闭
//        by::Ouser()->ShellDetailOrder();
        return true;
    }


    /**
     * @return void
     * ERP重试推送
     */
    public function actionErpRetry()
    {
        by::model('ErpLogModel', 'main')->finalRetry();
    }

    /**
     * @throws Exception
     * 退货退款7天内没提交退款物流，自动拒绝退款
     */
    public function actionRejectRefund()
    {
        by::OrefundMain()->ExpressTime();
    }

    /**
     * @throws Exception
     * 发货十天后查询快递是否签收
     */
    public function actionCheckOrder()
    {
        by::Ofinish()->ShellCheck();
    }

    /**
     * @throws Exception
     * 签收后7天置为已完成
     */
    public function actionFinishOrder()
    {
        by::Ofinish()->ShellFinish();
    }

    /**
     * @throws Exception
     * 已完成订单发放奖励
     */
    public function actionOrderReward()
    {
        by::OsourceR()->orderReward();
    }


    /**
     * @return void
     * 已完成订单发放用户奖励
     */
    public function actionFinishOrderReward()
    {
        by::Ouser()->finishOrderReward();
    }


//    ========================定金订单处理=======================
    /**
     * 取消订单
     */
    public function actionCancelDepositOrder() {
        $ret = by::Odeposit()->Cancel();
        YII_ENV_DEV && var_dump($ret);
    }


    /**
     * 预售商品变成正常商品
     *
     */
    public function actionChangeDepositGoods()
    {
        //预售商品变成普通商品
        //取消未付款的定金订单
        $gmainService = new GmainService();
        $ret = $gmainService->changePresaleGoodAndOrder();
        YII_ENV_DEV && var_dump($ret);
    }


    /**
     * @return void
     * @throws Exception
     * 预售尾款取消
     */
    public function actionCancelTailOrder()
    {
        $omainService = new OmainService();
        $ret = $omainService->CancelTailOrder();
        YII_ENV_DEV && var_dump($ret);
    }




}
