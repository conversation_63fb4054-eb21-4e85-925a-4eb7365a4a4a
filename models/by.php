<?php
/**
 * Created by PhpStor<PERSON>.
 * User: Kevin
 * Date: 2018/7/9
 * Time: 10:30
 */

namespace app\models;
use app\modules\asset\models\PointLogModel;
use app\modules\asset\models\PointModel;
use app\modules\cart\models\CartModel;
use app\modules\goods\models\GakModel;
use app\modules\goods\models\GavModel;
use app\modules\goods\models\GiniModel;
use app\modules\goods\models\GmainAcDeprice;
use app\modules\goods\models\GmainModel;
use app\modules\goods\models\gparam\GparamCateGroupDetailModel;
use app\modules\goods\models\gparam\GparamCateGroupModel;
use app\modules\goods\models\gparam\GparamGoodsModel;
use app\modules\goods\models\gparam\GparamModel;
use app\modules\goods\models\GpreStockModel;
use app\modules\goods\models\GrecommendModel;
use app\modules\goods\models\GspecsModel;
use app\modules\goods\models\GspriceModel;
use app\modules\goods\models\GstockModel;
use app\modules\goods\models\GtagModel;
use app\modules\goods\models\GtcModel;
use app\modules\goods\models\Gtype0Model;
use app\modules\goods\models\Gtype99Model;
use app\modules\goods\models\OadModel;
use app\modules\goods\models\OcfgModel;
use app\modules\goods\models\OdepositEModel;
use app\modules\goods\models\OdepositModel;
use app\modules\goods\models\OfinishModel;
use app\modules\goods\models\OfreightModel;
use app\modules\goods\models\OgoodsModel;
use app\modules\goods\models\OmainModel;
use app\modules\goods\models\OneYuanSeckillModel;
use app\modules\goods\models\OPayModel;
use app\modules\goods\models\OrefundDepositMainModel;
use app\modules\goods\models\OrefundDepositModel;
use app\modules\goods\models\OrefundMainModel;
use app\modules\goods\models\OrefundModel;
use app\modules\goods\models\OrgoodsModel;
use app\modules\goods\models\OsourceMModel;
use app\modules\goods\models\OsourceModel;
use app\modules\goods\models\OtnModel;
use app\modules\goods\models\SmsTmplModel;
use app\modules\goods\models\SmsSendRecordModel;
use app\modules\goods\models\OsourceRModel;
use app\modules\goods\models\OuserModel;
use app\modules\goods\models\SpreadModel;
use app\modules\goods\models\UserInviteGiftRulesModel;
use app\modules\goods\models\UserInviteGiftsModel;
use app\modules\goods\models\UserInviteLikeModel;
use app\modules\goods\models\UserInviteModel;
use app\modules\goods\models\UserInviteRankModel;
use app\modules\goods\models\UserInviteRuleGiftItemsModel;
use app\modules\log\models\AcDrawLogModel;
use app\modules\log\models\OrderZwxModel;
use app\modules\log\models\WarrantyApplyDetailModel;
use app\modules\log\models\WarrantyApplyModel;
use app\modules\log\models\WarrantyCardModel;
use app\modules\main\models\ActivityConfigModel;
use app\modules\main\models\AcType2Model;
use app\modules\main\models\AcType3Model;
use app\modules\main\models\AcType7Model;
use app\modules\main\models\AddressModel;
use app\modules\main\models\AdvAscribeModel;
use app\modules\main\models\CommentAuditModel;
use app\modules\main\models\CommentTaskModel;
use app\modules\main\models\CommModel;
use app\modules\main\models\MemberErrLogModel;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\OmsModel;
use app\modules\main\models\PmConfigModel;
use app\modules\main\models\UserDictModel;
use app\modules\main\models\UsersPlatformModel;
use app\modules\main\models\AmModel;
use app\modules\main\models\BasePayModel;
use app\modules\main\models\CateModel;
use app\modules\main\models\DataModel;
use app\modules\main\models\GcateModel;
use app\modules\main\models\GuideModel;
use app\modules\main\models\LoginModel;
use app\modules\main\models\MainPartModel;
use app\modules\main\models\MainSalesModel;
use app\modules\main\models\MarketConfigModel;
use app\modules\main\models\MarketDefineModel;
use app\modules\main\models\MarketingInformation;
use app\modules\main\models\MarketSendModel;
use app\modules\main\models\MemberCenterModel;
use app\modules\main\models\OaFocusModel;
use app\modules\main\models\PartsSalesModel;
use app\modules\main\models\PhoneModel;
use app\modules\main\models\PmarketModel;
use app\modules\main\models\ProductModel;
use app\modules\main\models\ProductRegModel;
use app\modules\main\models\RetailersModel;
use app\modules\main\models\SearchModel;
use app\modules\main\models\SourceConfigModel;
use app\modules\main\models\SourceExtendModel;
use app\modules\main\models\StatisticsModel;
use app\modules\main\models\UserAdvModel;
use app\modules\main\models\UserCardModel;
use app\modules\main\models\UserExtendModel;
use app\modules\main\models\UserGuideModel;
use app\modules\main\models\UserModel;
use app\modules\main\models\UserProfileModel;
use app\modules\main\models\UserRecommendModel;
use app\modules\main\models\WeFocusModel;
use app\modules\main\models\WxH5PayModel;
use app\modules\main\models\WxNoticeModel;
use app\modules\main\models\WxPayModel;
use app\modules\main\models\WxUlinkModel;
use app\modules\main\v1\models\RguideModel;
use app\modules\main\v1\models\RuserExtendModel;
use app\modules\main\v1\models\RuserGuideModel;
use app\modules\main\v1\models\RuserModel;
use app\modules\main\v1\models\RuserRecommendModel;
use app\modules\mall\models\OnceModel;
use app\modules\mall\models\UsersMallModel;
use app\modules\msg\models\UserMsgModel;
use app\modules\plumbing\models\CommentTagModel;
use app\modules\plumbing\models\ExploreSearchModel;
use app\modules\plumbing\models\PlumbingCommentModel;
use app\modules\plumbing\models\PlumbingOrderModel;
use app\modules\plumbing\models\PlumbingPriceModel;
use app\modules\plumbing\models\PlumbingRefundModel;
use app\modules\plumbing\models\PlumbingSnModel;
use app\modules\rbac\models\RolesModel;
use app\modules\rbac\models\SystemLogsModel;
use app\modules\rbac\models\UriModel;
use app\modules\rbac\models\UserModel as adminUserModel;
use app\modules\wiki\models\DocModel;
use app\modules\wiki\models\SconfigModel;
use app\modules\wares\models\GoodsAtkModel;
use app\modules\wares\models\GoodsAvModel;
use app\modules\wares\models\GoodsMainModel;
use app\modules\wares\models\GoodsPointsModel;
use app\modules\wares\models\GoodsPointsPriceModel;
use app\modules\wares\models\GoodsSpecsModel;
use app\modules\wares\models\GoodsStockModel;
use app\modules\wiki\models\RoleModel;
use app\modules\wiki\models\WdtopicModel;
use app\modules\wiki\models\WdynamicMainModel;
use app\modules\wiki\models\WdynamicModel;
use app\modules\wiki\models\WpraiseModel;
use app\modules\wiki\models\WtopicModel;
use app\modules\goods\models\AsyncExportLogModel;
use app\modules\goods\models\CouponCodeModel;
use app\modules\goods\models\CouponModel;
use app\modules\goods\models\E3OrderGoodModel;
use app\modules\goods\models\E3OrderModel;
use app\modules\goods\models\ErpOrderModel;
use app\modules\goods\models\ErpOrderProductModel;
use app\modules\log\models\UserChangeLogModel;
use yii\db\Connection;

class by
{
    public static $by = [];

    /**
     * @param $class_name
     * @param null $module
     * @param null $param
     * @param null $param1
     * @param bool $clear_cache
     * @param bool $dir
     * @return mixed
     * 单例模式获取Model类
     */
    public static function model($class_name, $module = null, $param = null, $param1 = null, $clear_cache = false, $dir = 'models')
    {
        $class_key = $module ? $class_name . "|" . $module : $class_name;
        $class_key = __FUNCTION__ . "|" . $class_key;

        if ($clear_cache) {
            unset(self::$by[$class_key]);
        }

        if (!isset(self::$by[$class_key]) || !is_object(self::$by[$class_key])) {
            self::$by[$class_key] = CUtil::getClass($dir, $class_name, $module, $param, $param1);
        }

        return self::$by[$class_key];
    }

    /**
     * @param $type
     * @return \Redis
     * 超级Redis 封装
     */
    public static function redis($type = 'core'): \Redis
    {

        $type       = strval($type);
        $config_key = "redis_{$type}";//省略redis_前缀
        $class_key  = __FUNCTION__ . "|{$config_key}";

        if (!isset(self::$by[$class_key]) || !is_object(self::$by[$class_key])) {

            try {

                if (!extension_loaded('redis')) {
                    throw new \Exception("Redis 扩展未加载");
                }

                $redis_config = CUtil::getConfig($config_key, 'common', \Yii::$app->id);
                if (empty($redis_config)) {
                    throw new \Exception("{$config_key}配置不存在");
                }

                $redis = new \Redis();
                $fun = $redis_config['fun'];
                $redis->$fun($redis_config['hostname'], $redis_config['port'], $redis_config['connectionTimeout']);

                if (!empty($redis_config['password'])) {
                    $redis->auth($redis_config['password']);
                }

                $redis->select($redis_config['database']);

            } catch (\Exception $e) {

                //防止乱码
                $message = mb_convert_encoding($e->getMessage(), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]);
                $error   = "Redis Error: {$message}";
                trigger_error($error, E_USER_ERROR);
            }

            self::$by[$class_key] = $redis;
        }

        return self::$by[$class_key];
    }


    /**
     * @param string $db_name
     * @param bool $clear_cache
     * @return Connection
     * 能保证数据库事物唯一性！！！
     * 但此方法只能用createCommand模式操作数据库
     */
    public static function dbMaster($db_name = 'db_app', $clear_cache = false): Connection
    {

        $class_key = __FUNCTION__ . "|{$db_name}";

        if ($clear_cache) {
            unset(self::$by[$class_key]);
        }

        if (!isset(self::$by[$class_key]) || !is_object(self::$by[$class_key])) {

            try {
                self::$by[$class_key] = \Yii::$app->$db_name;
            } catch (\Exception $e) {
                //防止乱码
                $message = mb_convert_encoding($e->getMessage(), "UTF-8", ["UTF-8", "GBK", "GB2312", "BIG5"]);
                $error   = "MySQL Error: {$message}";
                trigger_error($error, E_USER_ERROR);
            }
        }

        return self::$by[$class_key];
    }

    /**
     * @param bool $clear_cache
     * @return UserModel
     * 前台用户模型
     */
    public static function users($clear_cache = false): UserModel
    {
        return self::model('UserModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param false $clear_cache
     * @return LoginModel
     */
    public static function login($clear_cache = false): LoginModel
    {
        return self::model('LoginModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param false $clear_cache
     * @return UriModel
     * 后台管理菜单
     */
    public static function adminUriModel($clear_cache = false): UriModel
    {
        return self::model('UriModel', "rbac", null, null, $clear_cache);
    }

    /**
     * @param false $clear_cache
     * @return RolesModel
     * 后台角色
     */
    public static function adminRolesModel($clear_cache = false): RolesModel
    {
        return self::model('RolesModel', "rbac", null, null, $clear_cache);
    }

    /**
     * @param false $clear_cache
     * @return adminUserModel
     * 后台用户
     */
    public static function adminUserModel($clear_cache = false): adminUserModel
    {
        return self::model('UserModel', "rbac", null, null, $clear_cache);
    }

    /**
     * @param false $clear_cache
     * @return SystemLogsModel
     * 后台日志
     */
    public static function systemLogsModel($clear_cache = false): SystemLogsModel
    {
        return self::model('SystemLogsModel', "rbac", null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return PointModel
     * 积分
     */
    public static function point($clear_cache = false): PointModel
    {
        return self::model('PointModel', "asset", null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return PointLogModel
     * 积分记录
     */
    public static function pointLog($clear_cache = false): PointLogModel
    {
        return self::model('PointLogModel', "asset", null, null, $clear_cache);
    }

	/**
	 * 用户属性表
	 * @param bool $clear_cache
	 * @return UserProfileModel
	 */
	public static function userProfile($clear_cache = false):UserProfileModel
	{
		return self::model('UserProfileModel', MAIN_MODULE, null, null, $clear_cache);
	}

    /**
     * @param bool $clear_cache
     * @return GmainModel
     * 商品主表
     */
    public static function Gmain($clear_cache = false): GmainModel
    {
        return self::model('GmainModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return Gtype0Model
     * 商品type-0 表
     */
    public static function Gtype0($clear_cache = false): Gtype0Model
    {
        return self::model('Gtype0Model', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return Gtype99Model
     * 商品type-99 表
     */
    public static function Gtype99($clear_cache = false): Gtype99Model
    {
        return self::model('Gtype99Model', 'goods', null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return GiniModel
     * 商品自定义价格
     *
     */
    public static function Gini(bool $clear_cache = false): GiniModel
    {
        return self::model('GiniModel', 'goods', null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return GspriceModel
     * 商品自定义价格
     *
     */
    public static function Gsprice(bool $clear_cache = false): GspriceModel
    {
        return self::model('GspriceModel', 'goods', null, null, $clear_cache);
    }


    /**
     * 支付流水
     * @param bool $clear_cache
     * @return OPayModel
     */
    public static function oPay(bool $clear_cache = false): OPayModel
    {
        return self::model('OPayModel', 'goods', null, null, $clear_cache);
    }


    /**
     * 微信支付
     * @param bool $clear_cache
     * @return WxPayModel
     */
    public static function wxPay($clear_cache = false):WxPayModel
    {
        return self::model('WxPayModel', MAIN_MODULE, null, null, $clear_cache);
    }


    /**
     * 微信H5支付
     * @param bool $clear_cache
     * @return WxH5PayModel
     */
    public static function wxH5Pay($clear_cache = false):WxH5PayModel
    {
        return self::model('WxH5PayModel', MAIN_MODULE, null, null, $clear_cache);
    }


	/**
	 * @param false $clear_cache
	 * @return PhoneModel
	 */
	public static function Phone($clear_cache = false): PhoneModel
	{
		return self::model('PhoneModel', MAIN_MODULE, null, null, $clear_cache);
	}

    /**
     * @param bool $clear_cache
     * @return GakModel
     * 商品属性表
     */
    public static function Gak($clear_cache = false): GakModel
    {
        return self::model('GakModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GavModel
     * 商品属性值表
     */
    public static function Gav($clear_cache = false): GavModel
    {
        return self::model('GavModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GspecsModel
     * 商品属性specs表
     */
    public static function Gspecs($clear_cache = false): GspecsModel
    {
        return self::model('GspecsModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GstockModel
     * 商品库存表
     */
    public static function Gstock($clear_cache = false): GstockModel
    {
        return self::model('GstockModel', 'goods', null, null, $clear_cache);
    }


	/**
	 * 门店配置信息
	 * @param bool $clear_cache
	 * @return RetailersModel
	 */
	public static function retailers($clear_cache = false):RetailersModel
	{
		return self::model('RetailersModel', MAIN_MODULE, null, null, $clear_cache);
	}

	/**
	 * 门店配置信息
	 * @param bool $clear_cache
	 * @return AddressModel
	 */
	public static function Address($clear_cache = false):AddressModel
	{
		return self::model('AddressModel', MAIN_MODULE, null, null, $clear_cache);
	}

    /**
     * @param bool $clear_cache
     * @return OmainModel
     * 订单主表
     */
    public static function Omain($clear_cache = false): OmainModel
    {
        return self::model('OmainModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return mixed
     * 定金订单表
     */
    public static function Odeposit($clear_cache = false): OdepositModel
    {
        return self::model('OdepositModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OuserModel
     * 订单表
     */
    public static function Ouser($clear_cache = false): OuserModel
    {
        return self::model('OuserModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OgoodsModel
     * 订单商品表
     */
    public static function Ogoods($clear_cache = false): OgoodsModel
    {
        return self::model('OgoodsModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OcfgModel
     * 订单商品快照表
     */
    public static function Ocfg($clear_cache = false): OcfgModel
    {
        return self::model('OcfgModel', 'goods', null, null, $clear_cache);
    }

	/**
	 * 购物车模块
	 * @param bool $clear_cache
	 * @return CartModel
	 */
	public static function cart($clear_cache = false): CartModel
	{
		return self::model('CartModel', 'cart', null, null, $clear_cache);
	}

    /**
     * @param bool $clear_cache
     * @return OadModel
     * 订单地址表
     */
    public static function Oad($clear_cache = false): OadModel
    {
        return self::model('OadModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OrefundMainModel
     * 退款主表
     */
    public static function OrefundMain($clear_cache = false): OrefundMainModel
    {
        return self::model('OrefundMainModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OrefundModel
     * 退款表
     */
    public static function Orefund($clear_cache = false): OrefundModel
    {
        return self::model('OrefundModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OrgoodsModel
     * 退款商品表
     */
    public static function Orgoods($clear_cache = false): OrgoodsModel
    {
        return self::model('OrgoodsModel', 'goods', null, null, $clear_cache);
    }

	/**
	 * @param bool $clear_cache
	 * @return MarketConfigModel
	 * TODO 营销配置管理
	 */
	public static function marketConfig($clear_cache = false):MarketConfigModel
	{
		return self::model('MarketConfigModel', MAIN_MODULE, null, null, $clear_cache);
	}


    /**
     * @param bool $clear_cache
     * @return MarketDefineModel
     * TODO 自定义营销资源表
     */
    public static function marketDefine($clear_cache = false):MarketDefineModel
    {
        return self::model('MarketDefineModel', MAIN_MODULE, null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return ProductModel
     * 服务产品表
     */
    public static function product($clear_cache = false): ProductModel
    {
        return self::model('ProductModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return ProductRegModel
     * 服务产品z注册表
     */
    public static function productReg($clear_cache = false): ProductRegModel
    {
        return self::model('ProductRegModel', 'main', null, null, $clear_cache);
    }

	/**
	 * @param bool $clear_cache
	 * @return ActivityConfigModel
	 * TODO 活动配置管理
	 */
	public static function activityConfigModel($clear_cache = false):ActivityConfigModel
	{
		return self::model('ActivityConfigModel', MAIN_MODULE, null, null, $clear_cache);
	}

	/**
	 * @param false $clear_cache
	 * @return UserCardModel
	 * TODO 卡券记录
	 */
	public static function userCard($clear_cache = false):UserCardModel
	{
		return self::model('UserCardModel', MAIN_MODULE, null, null, $clear_cache);
	}

	/**
	 * @param bool $clear_cache
	 * @return MarketSendModel
	 * TODO 营销资源发放管理
	 */
	public static function marketSend($clear_cache = false):MarketSendModel
	{
		return self::model('MarketSendModel', MAIN_MODULE, null, null, $clear_cache);
	}

	/**
	 * @param bool $clear_cache
	 * @return GmainModel
	 * 商品标签
	 */
	public static function Gtag($clear_cache = false): GtagModel
	{
		return self::model('GtagModel', 'goods', null, null, $clear_cache);
	}

    /**
     * @param bool $clear_cache
     * @return UserExtendModel
     * 商品标签
     */
    public static function userExtend($clear_cache = false): UserExtendModel
    {
        return self::model('UserExtendModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OsourceModel
     * 导购订单表
     */
    public static function Osource($clear_cache = false): OsourceModel
    {
        return self::model('OsourceModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GuideModel
     * 导购表
     */
    public static function guide($clear_cache = false): GuideModel
    {
        return self::model('GuideModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return StatisticsModel
     * 数据统计model
     */
    public static function statistics($clear_cache = false)
    {
        return self::model('StatisticsModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return UserGuideModel
     * 数据统计model
     */
    public static function userGuide($clear_cache = false)
    {
        return self::model('UserGuideModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GtcModel
     * oms套餐model
     */
    public static function GtcModel($clear_cache = false)
    {
        return self::model('GtcModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OaFocusModel
     * 公众号关注
     */
    public static function OaFocus($clear_cache = false)
    {
        return self::model('OaFocusModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WxNoticeModel
     */
    public static function WxNotice($clear_cache = false)
    {
        return self::model('WxNoticeModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return PmarketModel
     */
    public static function pMarket($clear_cache = false)
    {
        return self::model('PmarketModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OfinishModel
     */
    public static function Ofinish($clear_cache = false): OfinishModel
    {
        return self::model('OfinishModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WdynamicMainModel
     * 内容模块
     */
    public static function WdynamicMain($clear_cache = false): WdynamicMainModel
    {
        return self::model('WdynamicMainModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WdynamicModel
     * 内容模块
     */
    public static function Wdynamic($clear_cache = false): WdynamicModel
    {
        return self::model('WdynamicModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WtopicModel
     * 内容模块
     */
    public static function Wtopic($clear_cache = false): WtopicModel
    {
        return self::model('WtopicModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return AmModel
     * 活动绑定优惠券
     */
    public static function aM(bool $clear_cache = false): AmModel
    {
        return self::model('AmModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return AcType2Model
     * 推荐有礼表
     */
    public static function acType2(bool $clear_cache = false): AcType2Model
    {
        return self::model('AcType2Model', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return AcType3Model
     */
    public static function acType3(bool $clear_cache = false): AcType3Model
    {
        return self::model('AcType3Model', 'main', null, null, $clear_cache);
    }

    public static function acType7(bool $clear_cache = false): AcType7Model
    {
        return self::model('AcType7Model', 'main', null, null, $clear_cache);
    }
    /**
     * @param bool $clear_cache
     * @return OsourceRModel
     * 推荐人订单表
     */
    public static function osourceR(bool $clear_cache = false): OsourceRModel
    {
        return self::model('OsourceRModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return UserRecommendModel
     * 推荐人绑定
     */
    public static function userRecommend(bool $clear_cache = false): UserRecommendModel
    {
        return self::model('UserRecommendModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OsourceMModel
     * 订单来源表
     */
    public static function osourceM(bool $clear_cache = false): OsourceMModel
    {
        return self::model('OsourceMModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WdtopicModel
     * 内容模块
     */
    public static function Wdtopic($clear_cache = false): WdtopicModel
    {
        return self::model('WdtopicModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return WpraiseModel
     * 内容模块-点赞
     */
    public static function Wpraise($clear_cache = false): WpraiseModel
    {
        return self::model('WpraiseModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return SconfigModel
     * 用户调研
     */
    public static function Sconfig($clear_cache = false): SconfigModel
    {
        return self::model('SconfigModel', 'wiki', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return PlumbingOrderModel
     * 上下水服务订单
     */
    public static function plumbingOrder($clear_cache = false): PlumbingOrderModel
    {
        return self::model('PlumbingOrderModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return ExploreSearchModel
     * 勘探服务地区搜索
     */
    public static function exploreSearch($clear_cache = false): ExploreSearchModel
    {
        return self::model('ExploreSearchModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return UsersMallModel
     */
    public static function usersMall($clear_cache = false):UsersMallModel
    {
        return self::model('UsersMallModel', MALL_MODULE, null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return UserModel
     * no_auth表 用户模型
     */
    public static function Rusers($clear_cache = false): RuserModel
    {
        return self::model('RuserModel', MAIN_MODULE_V1, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return RguideModel
     * no_auth表 用户引导
     */
    public static function Rguide($clear_cache = false): RguideModel
    {
        return self::model('RguideModel', MAIN_MODULE_V1, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return RuserExtendModel
     * no_auth表 用户扩展信息
     */
    public static function RuserExtend($clear_cache = false): RuserExtendModel
    {
        return self::model('RuserExtendModel', MAIN_MODULE_V1, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return RuserRecommendModel
     * no_auth表 用户推荐表
     */
    public static function RuserRecommend($clear_cache = false): RuserRecommendModel
    {
        return self::model('RuserRecommendModel', MAIN_MODULE_V1, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return RuserGuideModel
     * no_auth表 用户推荐表
     */
    public static function RuserGuide($clear_cache = false): RuserGuideModel
    {
        return self::model('RuserGuideModel', MAIN_MODULE_V1, null, null, $clear_cache);
    }

    /**
     * 文档管理
     * @param $clear_cache
     * @return DocModel
     */
    public static function Doc($clear_cache = false): DocModel
    {
        return self::model('DocModel', 'wiki', null, null, $clear_cache);
    }


    /**
     * @param $clear_cache
     * @return PlumbingSnModel
     * 上下水服务sn编码配置
     */
    public static function plumbingSn($clear_cache = false): PlumbingSnModel
    {
        return self::model('PlumbingSnModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return PlumbingPriceModel
     * 上下水服务价格配置
     */
    public static function plumbingPrice($clear_cache = false): PlumbingPriceModel
    {
        return self::model('PlumbingPriceModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return PlumbingRefundModel
     * 上下水服务退款订单
     */
    public static function plumbingRefund($clear_cache = false): PlumbingRefundModel
    {
        return self::model('PlumbingRefundModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return PlumbingCommentModel
     * 上下水评价列表
     */
    public static function plumbingComment($clear_cache = false): PlumbingCommentModel
    {
        return self::model('PlumbingCommentModel', 'plumbing', null, null, $clear_cache);
    }

    /**
     * @param $clear_cache
     * @return CommentTagModel
     * 上下水评价标签
     */
    public static function commentTag($clear_cache = false): CommentTagModel
    {
        return self::model('CommentTagModel', 'plumbing', null, null, $clear_cache);
    }


    /**
     * @param $clear_cache
     * @return SearchModel
     * 搜索MODEL
     */
    public static function search($clear_cache = false): SearchModel
    {
        return self::model('SearchModel', MAIN_MODULE, null, null, $clear_cache);
    }


    /**
     * @param $clear_cache
     * @return SpreadModel
     * 推广MODEL
     */
    public static function spread($clear_cache = false):SpreadModel
    {
        return self::model('SpreadModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return DataModel
     * main 通用MODEL
     */
    public static function dataModel($clear_cache = false): DataModel
    {
        return self::model('DataModel', MAIN_MODULE, null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return mixed
     * main 广告推广MODEL
     */
    public static function advAscribeModel(bool $clear_cache = false):AdvAscribeModel
    {
        return self::model('AdvAscribeModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OrefundMainModel
     * 定金退款主表
     */
    public static function OrefundDepositMain($clear_cache = false): OrefundDepositMainModel
    {
        return self::model('OrefundDepositMainModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OrefundModel
     * 退款表
     */
    public static function OrefundDeposit($clear_cache = false): OrefundDepositModel
    {
        return self::model('OrefundDepositModel', 'goods', null, null, $clear_cache);
    }


    /**
     * @param $clear_cache
     * @return OdepositEModel
     * 定金订单扩展表
     */
    public static function OdepositE($clear_cache=false):OdepositEModel
    {
        return self::model('OdepositEModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return mixed
     * main 会员权益MODEL
     */
    public static function memberCenterModel(bool $clear_cache = false):MemberCenterModel
    {
        return self::model('MemberCenterModel', MAIN_MODULE, null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return OnceModel
     * 一次性任务MODEL
     */
    public static function onceModel(bool $clear_cache = false):OnceModel
    {
        return self::model('OnceModel', MALL_MODULE, null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return CateModel
     * 商品类目MODEL
     */
    public static function cateModel(bool $clear_cache = false): CateModel
    {
        return self::model('CateModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @return PartsSalesModel
     * 智能配件model
     */
    public static function partsSalesModel(bool $clear_cache = false): PartsSalesModel
    {
        return self::model('PartsSalesModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return GcateModel
     * 商品分类绑定MODEL
     */
    public static function gCateModel(bool $clear_cache = false): GcateModel
    {
        return self::model('GcateModel', MAIN_MODULE, null, null, $clear_cache);
    }

    /**
     * @return MainSalesModel
     * 主机model
     */
    public static function mainSalesModel(bool $clear_cache = false):MainSalesModel
    {
        return self::model('MainSalesModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return MainPartModel
     * 主机配件原始关联信息model
     */
    public static function mainPartModel(bool $clear_cache = false):MainPartModel
    {
        return self::model('MainPartModel', 'main', null, null, $clear_cache);
    }

    /**
     * @return GpreStockModel
     * 预售商品库存表
     */
    public static function Gprestock($clear_cache = false): GpreStockModel
    {
        return self::model('GpreStockModel', 'goods', null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return UserMsgModel
     * 推送消息表
     */
    public static function UserMsg($clear_cache = false): UserMsgModel
    {
        return self::model('UserMsgModel', MSG_MODULE, null, null, $clear_cache);
    }


    /**
     * @param $clear_cache
     * @return OfreightModel
     */
    public static function Ofreight($clear_cache = false): OfreightModel
    {
        return self::model('OfreightModel', 'goods', null, null, $clear_cache);
    }


    public static function OrderZwx($clear_cache = false): OrderZwxModel
    {
        return self::model('OrderZwxModel', 'log', null, null, $clear_cache);
    }


    /**
     * @param bool $clear_cache
     * @return GparamModel
     * 商品参数
     */
    public static function GparamModel($clear_cache = false): GparamModel
    {
        return self::model('GparamModel', 'goods', null, null, $clear_cache, 'models\gparam');
    }

    /**
     * @param bool $clear_cache
     * @return GparamGoodsModel
     * 商品的商品参数
     */
    public static function GparamGoodsModel($clear_cache = false): GparamGoodsModel
    {
        return self::model('GparamGoodsModel', 'goods', null, null, $clear_cache, 'models\gparam');
    }

    /**
     * @param bool $clear_cache
     * @return GparamCateGroupModel
     * 商品参数的二级类目分组
     */
    public static function GparamCateGroupModel($clear_cache = false): GparamCateGroupModel
    {
        return self::model('GparamCateGroupModel', 'goods', null, null, $clear_cache, 'models\gparam');
    }

    /**
     * @param bool $clear_cache
     * @return GparamCateGroupDetailModel
     * 商品参数的二级类目分组详情
     */
    public static function GparamCateGroupDetailModel($clear_cache = false): GparamCateGroupDetailModel
    {
        return self::model('GparamCateGroupDetailModel', 'goods', null, null, $clear_cache, 'models\gparam');

    }

    public static function userAdv($clear_cache= false): UserAdvModel
    {
        return self::model('UserAdvModel', MAIN_MODULE, null, null, $clear_cache);
    }

    public static function marketInformation($clear_cache= false): MarketingInformation
    {
        return self::model('MarketingInformation', MAIN_MODULE, null, null, $clear_cache);
    }

    public static function smsTmplModel($clear_cache= false): SmsTmplModel
    {
        return self::model('SmsTmplModel', 'goods', null, null, $clear_cache);
    }

    public static function smsSendRecordModel($clear_cache= false): SmsSendRecordModel
    {
        return self::model('SmsSendRecordModel', 'goods', null, null, $clear_cache);
    }

    public static function GoodsRecommend($clear_cache= false): GrecommendModel
    {
        return self::model('GrecommendModel', 'goods', null, null, $clear_cache);
    }

    public static function AcDrawLogModel($clear_cache= false):AcDrawLogModel
    {
        return self::model('AcDrawLogModel', 'log', null, null, $clear_cache);
    }

    public static function WikiRole($clear_cache= false):RoleModel
    {
        return self::model('RoleModel', 'wiki', null, null, $clear_cache);
    }


    public static function WarrantyCard($clear_cache= false):WarrantyCardModel
    {
        return self::model('WarrantyCardModel', 'log', null, null, $clear_cache);
    }

    public static function WarrantyApply($clear_cache= false):WarrantyApplyModel
    {
        return self::model('WarrantyApplyModel', 'log', null, null, $clear_cache);
    }

    public static function WarrantyApplyDetail($clear_cache= false):WarrantyApplyDetailModel
    {
        return self::model('WarrantyApplyDetailModel', 'log', null, null, $clear_cache);
    }

    public static function AsyncExportLog($clear_cache = false): AsyncExportLogModel
    {
        return self::model('AsyncExportLogModel', 'goods', null, null, $clear_cache);
    }

    public static function GoodsMainModel($clear_cache = false): GoodsMainModel
    {
        return self::model('GoodsMainModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsPointsModel($clear_cache = false): GoodsPointsModel
    {
        return self::model('GoodsPointsModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsPointsPriceModel($clear_cache = false): GoodsPointsPriceModel
    {
        return self::model('GoodsPointsPriceModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsSpecsModel($clear_cache = false): GoodsSpecsModel
    {
        return self::model('GoodsSpecsModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsAtkModel($clear_cache = false): GoodsAtkModel
    {
        return self::model('GoodsAtkModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsAvModel($clear_cache = false): GoodsAvModel
    {
        return self::model('GoodsAvModel', 'wares', null, null, $clear_cache);
    }

    public static function GoodsStockModel($clear_cache = false): GoodsStockModel
    {
        return self::model('GoodsStockModel', 'wares', null, null, $clear_cache);
    }

    public static function WeFocus($clear_cache= false):WeFocusModel
    {
        return self::model('WeFocusModel', 'main', null, null, $clear_cache);
    }

    public static function SourceConfig($clear_cache= false):SourceConfigModel
    {
        return self::model('SourceConfigModel', 'main', null, null, $clear_cache);
    }

    public static function SourceExtend($clear_cache= false):SourceExtendModel
    {
        return self::model('SourceExtendModel', 'main', null, null, $clear_cache);
    }

    public static function BasePayModel($clear_cache= false):BasePayModel
    {
        return self::model('BasePayModel', 'main', null, null, $clear_cache);
    }

    public static function GmainAcDepriceModel($clear_cache= false):GmainAcDeprice
    {
        return self::model('GmainAcDeprice', 'goods', null, null, $clear_cache);
    }

    public static function OtnModel($clear_cache= false):OtnModel
    {
        return self::model('OtnModel', 'goods', null, null, $clear_cache);
    }

    public static function UsersPlatformModeModel($clear_cache=false):UsersPlatformModel
    {
        return self::model('UsersPlatformModel', 'main', null, null, $clear_cache);
    }

    public static function oms(bool $clear_cache = false): OmsModel
    {
        return self::model('OmsModel', 'main', null, null, $clear_cache);
    }

    public static function WxUlinkModel($clear_cache = false): WxUlinkModel
    {
        return self::model('WxUlinkModel', 'main', null, null, $clear_cache);
    }
    public static function PmConfigModel($clear_cache = false):PmConfigModel
    {
        return self::model('PmConfigModel', 'main', null, null, $clear_cache);
    }

    public static function E3OrderGoodModel($clear_cache = false): E3OrderGoodModel
    {
        return self::model('E3OrderGoodModel', 'goods', null, null, $clear_cache);
    }
    public static function E3OrderModel($clear_cache = false): E3OrderModel
    {
        return self::model('E3OrderModel', 'goods', null, null, $clear_cache);
    }

    public static function CommModel($clear_cache = false):CommModel
    {
        return self::model('CommModel', 'main', null, null, $clear_cache);
    }
    public static function CouponModel($clear_cache = false): CouponModel
    {
        return self::model('CouponModel', 'goods', null, null, $clear_cache);
    }
    public static function CouponCodeModel($clear_cache = false): CouponCodeModel
    {
        return self::model('CouponCodeModel', 'goods', null, null, $clear_cache);
    }
    public static function userChangeLogModel($clear_cache = false):UserChangeLogModel{
        return self::model('UserChangeLogModel', 'log', null, null, $clear_cache);
    }
    public static function ErpOrderProductModel($clear_cache = false): ErpOrderProductModel
    {
        return self::model('ErpOrderProductModel', 'goods', null, null, $clear_cache);
    }
    public static function ErpOrderModel($clear_cache = false): ErpOrderModel
    {
        return self::model('ErpOrderModel', 'goods', null, null, $clear_cache);
    }

    public static function MemberActivityModel($clear_cache = false): MemberActivityModel
    {
        return self::model('MemberActivityModel', 'main', null, null, $clear_cache);
    }
    public static function MemberErrLogModel($clear_cache = false):MemberErrLogModel
    {
        return self::model('MemberErrLogModel', 'main', null, null, $clear_cache);
    }

    public static function CommentAuditModel($clear_cache = false): CommentAuditModel
    {
        return self::model('CommentAuditModel', 'main', null, null, $clear_cache);
    }

    public static function CommentTaskModel($clear_cache = false): CommentTaskModel
    {
        return self::model('CommentTaskModel', 'main', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return OneYuanSeckillModel
     * 一元秒杀模型
     */
    public static function OneYuanSeckillModel(bool $clear_cache = false): OneYuanSeckillModel
    {
        return self::model('OneYuanSeckillModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return UserInviteRankModel
     * 用户邀请排行榜表
     */
    public static function UserInviteRankModel(bool $clear_cache = false): UserInviteRankModel
    {
        return self::model('UserInviteRankModel', 'goods', null, null, $clear_cache);
    }

    /**
     * @param bool $clear_cache
     * @return UserInviteModel
     * 用户邀请模型
     */
    public static function UserInviteModel(bool $clear_cache = false): UserInviteModel
    {
        return self::model('UserInviteModel', 'goods', null, null, $clear_cache);
    }

    public static function UserInviteGiftsModel(bool $clear_cache = false): UserInviteGiftsModel
    {
        return self::model('UserInviteGiftsModel', 'goods', null, null, $clear_cache);
    }
    public static function InviteGiftRulesModel(bool $clear_cache = false): UserInviteGiftRulesModel
    {
        return self::model('UserInviteGiftRulesModel', 'goods', null, null, $clear_cache);
    }
    public static function InviteRuleGiftItemsModel(bool $clear_cache = false): UserInviteRuleGiftItemsModel
    {
        return self::model('UserInviteRuleGiftItemsModel', 'goods', null, null, $clear_cache);
    }
    public static function UserInviteLikeModel(bool $clear_cache = false): UserInviteLikeModel
    {
        return self::model('UserInviteLikeModel', 'goods', null, null, $clear_cache);
    }

    public static function userDictModel(bool $clear_cache = false): UserDictModel
    {
        return self::model('UserDictModel', 'main', null, null, $clear_cache);
    }
}
