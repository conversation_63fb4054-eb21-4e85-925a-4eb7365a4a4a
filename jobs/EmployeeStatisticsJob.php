<?php

namespace app\jobs;

use app\components\EventMsg;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\UserEmployeeService;

/**
 * 用户统计
 */
class EmployeeStatisticsJob extends BaseJob
{
    public $field;
    public $user_id;
    public $number;
    public $addOrsubtract;
    public $order_no;
    public $type; //1直接处理，2支付成功，3退款成功

    public function execute($queue)
    {
        if ($this->type == 1) {
            list($status, $res) = byNew::EmployeeStatisticsModel()->patch($this->user_id,$this->field,$this->number,$this->addOrsubtract);
            if (!$status) {
                CUtil::debug(sprintf("修改统计数据失败：参数：%s-%s-%s-%s-%s，异常：%s", $this->user_id,$this->order_no,$this->field,$this->number,$this->addOrsubtract, $res), 'err.EmployeeStatistics.job');
            }
            // 如果是增加邀请人，判断一下其他的数据
            // if ($this->field == 'recommend_num'){
            //     $this->becomeUserEmployee($this->user_id);
            // }
        }else{
            if ($this->order_no != ''){
                $info     = by::Ouser()->CommPackageInfo($this->user_id,$this->order_no,false,true,true,true,true,false,true,true);
                // 判断订单是否存在
                if ($info){
                    // 判断是否是普通订单
                    if ($info['relation_type'] != '1'){
                        return true;
                    }
                    // 判断用户是否是追觅大使
                    $empRes = byNew::UserEmployeeModel()->getEmployeeInfo($this->user_id, 'user_id');
                    if ($empRes){
                        // 是追觅大使，则增加自购金额
                        list($status, $res) = byNew::EmployeeStatisticsModel()->patch($this->user_id,'self_buy_amount',$info['price'],$this->addOrsubtract);
                        if (!$status) {
                            CUtil::debug(sprintf("修改统计数据失败：参数：%s-%s-%s-%s-%s，异常：%s", $this->user_id,$this->order_no,$this->field,$this->number,$this->addOrsubtract, $res), 'err.EmployeeStatistics.job');
                        }
                    }
                        // 如果不是追觅大使，判断一下他的父级是不是追觅大使
                    $zinfo = byNew::UserBindModel()->getInfoByUserId($this->user_id);
                    $r_id = $zinfo['user_id'] ?? 0;
                    if ($r_id){
                        $empRes = byNew::UserEmployeeModel()->getEmployeeInfo($r_id, 'user_id');
                        if ($empRes){
                            list($status, $res) = byNew::EmployeeStatisticsModel()->patch($r_id,'recommend_buy_amount',$info['price'],$this->addOrsubtract);
                            if (!$status) {
                                CUtil::debug(sprintf("修改统计数据失败：参数：%s-%s-%s-%s-%s，异常：%s", $this->user_id,$this->order_no,$this->field,$this->number,$this->addOrsubtract, $res), 'err.EmployeeStatistics.job');
                            }
                            $commission = $this->disposeCommission($this->user_id,$info,$empRes,$this->type);
                            if($commission){
                                list($status, $res) = byNew::EmployeeStatisticsModel()->patch($r_id,'commission',$commission,$this->addOrsubtract);
                                if (!$status) {
                                    CUtil::debug(sprintf("修改统计数据失败：参数：%s-%s-%s-%s-%s，异常：%s", $this->user_id,$this->order_no,$this->field,$this->number,$this->addOrsubtract, $res), 'err.EmployeeStatistics.job');
                                }
                            }
                        }
                    }
                    
                }
            }
        }   
    }

    public function disposeCommission($user_id,$info,$empRes,$type){
        // $type = 2代表支付成功，3代表退款成功
        $commissionInfo = byNew::SalesCommissionModel()->getInfo($info['order_no']);
        $goods = $info['goods'] ?? [];
        if ($goods){
            $rate = 0.2;
            if ($empRes['level'] == 2){
                $rate = 0.2;
            }elseif($empRes['level'] == 3){
                $rate = 0.2;
            }
            $mainMachinePay = $info['deposit_price'] ?? 0;
            foreach ($goods as $value){
                $tidArr = by::Gtag()->GetListByGid($value['gid']) ?? [];
                $tIds = array_column($tidArr, 'tid') ?? [];
                if (in_array(20, $tIds)) {
                    // 2025-07-18 仅购买主机才会返利
                    // $partsPay += $value['price'];
                } else {
                    $mainMachinePay += $value['price'];
                }
            }
            if ($type == 2){
                if ($commissionInfo){
                    return 0;
                }
                $save = [];
                $save['order_no'] = $info['order_no'];
                $save['user_id'] = $user_id;
                $save['referrer'] = $empRes['user_id'];
                $save['price'] = round($mainMachinePay * 100);
                $save['status'] = 300;
                $save['rate'] = $rate;
                $save['commission'] = round($mainMachinePay * $rate * 100);
                $save['ctime'] = $info['ctime'];
                $save['utime'] = time();
                $save['extend'] = json_encode($empRes);
                byNew::SalesCommissionModel()->saveData($save);
                
            }else if ($type == 3){
                if ($commissionInfo){
                    byNew::SalesCommissionModel()->updateStatus($commissionInfo['id'],['status'=>20000000]);
                }
            }
            return round($mainMachinePay * $rate * 100);
        }else{
            return 0;
        }
    }
    public function becomeUserEmployee($user_id){
        $param = ['user_id'=>$user_id];
        $param['is_new_user'] = 1;
        $count = byNew::UserBindModel()->getBindCount($param);
        // 如果大于10，则给身份,uat环境先用2个判断，方便测试，正式环境改成10个
        if ($count >= 10){
            $user = byNew::UserEmployeeModel()->getEmployeeInfo($user_id,'user_id');
            if($user){
                return true;
            }
            $data = [
                'type' => 2,
                'level' => 1,
                'employee_status' => 2,
                'employee_id' => '',
                'ctime' => time(),
                'utime' => time(),
            ];

            // 获取用户数据
            $user_data = by::Phone()->getDataByUserId($user_id);
            if (empty($user_data)) {
                return false;
            }
            $data['uid'] = $user_data['uid'];
            $data['user_id'] = $user_id;
            $status = byNew::UserEmployeeModel()->saveData($data);
            if ($status){
                EventMsg::factory()->run('superPartnerFirstPoint', ['user_id' => $user_id]);
            }
        } 
    }

}