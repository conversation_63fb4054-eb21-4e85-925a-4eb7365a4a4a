<?php

namespace app\jobs;

use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\StoreGoodsRelationModel;

class SalesCommissionJob extends BaseJob
{

    public $recomand_user_id; //推荐用户
    public $order_no;
    public $price;
    public $user_id;  //下单用户
    public $status;

    public function execute($queue)
    {
        $info = by::Ouser()->CommPackageInfo($this->user_id,$this->order_no,false,true,true,true,true,false,true,true);
        //记一下日志，看是否调用
        CUtil::debug(sprintf("插入结佣数据：参数：%s-%s-%s-%s", $this->recomand_user_id,$info['order_no'],$this->user_id,$this->status), 'Info.SalesCommissionJob.job');
        $this->disposeCommission($this->user_id, $this->recomand_user_id,$info,$this->price,$this->status);
    }

    public function disposeCommission($user_id, $recomand_user_id,$orderInfo,$price,$status){
        $commissionInfo = byNew::SalesCommissionModel()->getInfo($orderInfo['order_no']);
        $goods = $orderInfo['goods'] ?? [];
        if ($goods){
            $commission = 0;
            if ($status == 0){
                if ($commissionInfo){
                    return 0;
                }
                $mainMachinePay = 0;
                $rate = 0.1;
                foreach ($goods as $value){
                    $gid = $value['gid'] ?? 0;
                    if (empty($gid)){
                        continue;
                    }
                    // 店铺分享的商品已下架, 不计算佣金
                    $storeGoodInfo = StoreGoodsRelationModel::instance()->getInfo([
                        'goods_id' => $gid,
                        'user_id' => $recomand_user_id,
                    ]);
                    if (!$storeGoodInfo || 1 == $storeGoodInfo['status'] ) {
                        continue;
                    }
                    $mainMachinePay += round($value['price'] * 100);
                    // todo 佣金比例暂时写死，后续根据活动调整
                    // 获取比例
                    $rateInfo = byNew::StoreGoodsModel()->getInfo(['goods_id'=>$gid]);
                    $rate = $rateInfo['rate'] ?? 0.1;
                    
                    $commission += round($value['price'] * $rate * 100);
                }
                $save = [];
                $halfPriceBuyId = CUtil::getConfig('consume_money_id', 'config', \Yii::$app->id);
                $save['activity_id'] = $halfPriceBuyId;
                $save['order_no'] = $orderInfo['order_no'];
                $save['user_id'] = $user_id;
                $save['referrer'] = $recomand_user_id;
                $save['price'] = $mainMachinePay;
                $save['status'] = $status;
                $save['rate'] = $rate;
                $save['commission'] = $commission;
                $save['ctime'] = time();
                $save['utime'] = time();
                byNew::SalesCommissionModel()->saveData($save);

            }else {
                if ($commissionInfo){
                    byNew::SalesCommissionModel()->updateStatus($commissionInfo['id'],['status'=>$status]);
                }
            }
            return $commission;
        }else{
            return 0;
        }
        
    }
}