<?php

namespace app\jobs;

use app\components\EventMsg;
use app\components\MemberCenter;
use app\exceptions\DrawActivityException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\DrawExternalCouponModel;

/**
 * 处理抽奖任务
 */
class HandleDrawActivityJob extends BaseJob
{
    // 失败重试3次
    protected $attempt = 3;

    // 用户ID
    public $user_id;
    // 用户手机号
    public $user_phone;
    // 活动ID
    public $activity_id;
    // 奖品ID
    public $prize_id;
    // 任务消耗类型 1=按抽奖次数，2=按消耗金币
    public $consume_type;
    // 消耗金币
    public $consume_gold;

    /**
     * @param $queue
     * @return void
     */
    public function execute($queue)
    {
        // 1、奖品信息
        $prizeData = byNew::DrawPrize()->getPrizeListByIds([$this->prize_id])[0] ?? null;
        if (!$prizeData) {
            $this->logError("奖品不存在：参数：user_id:{$this->user_id}, activity_id:{$this->activity_id}, prize_id:{$this->prize_id}");
            return;
        }

        // 2、处理数据
        $prizeRecordId = $this->handle($this->user_id, $this->user_phone, $this->activity_id, $this->prize_id, $prizeData, $this->consume_type ?? 0, $this->consume_gold ?? 0);
        if (!$prizeRecordId) {
            return;
        }

        // 3、发放奖品
        try {
            $this->awardPrize($prizeData, $prizeRecordId);
        } catch (\Exception $e) {
            $this->logError("发放奖品异常：参数：user_id:{$this->user_id}, activity_id:{$this->activity_id}, prize_id:{$this->prize_id}, prize_data:" . json_encode($prizeData) . "；异常：file:{$e->getFile()}, line:{$e->getLine()}, code:{$e->getCode()}, msg:{$e->getMessage()}");
        }
    }

    /**
     * 发放奖品
     * @param array $prizeData
     * @param int $prizeRecordId
     * @return void
     * @throws \yii\db\Exception
     */
    private function awardPrize(array $prizeData, int $prizeRecordId)
    {
        $drawPrize = byNew::DrawPrize();
        switch ($prizeData['type']) {
            case $drawPrize::PRIZE_TYPES['POINT']:
                // EventMsg::factory()->run('drawPoint', ['user_id' => $this->user_id, 'point' => $prizeData['value']]);
                list($s, $msg) = MemberCenter::factory()->sendDrawPoint($this->user_id, $prizeData['value']);
                if (! $s) {
                    throw new DrawActivityException($msg);
                }
                break;

            case $drawPrize::PRIZE_TYPES['GOLD']: // 金币奖品
                // EventMsg::factory()->run('drawGold', ['user_id' => $this->user_id, 'gold' => $prizeData['value']]);
                // 加金币
                // list($s, $msg) = MemberCenter::factory()->sendDrawGold($this->user_id, $prizeData['value']);
                $consume_money = empty($prizeData['value']) ? 0 : bcmul($prizeData['value'], 100);
                if (empty($consume_money)) {
                    break;
                }
                $status = byNew::UserShopMoneyModel()->AddOrSubtract($this->user_id, 'add', 2, $consume_money, $this->user_id, 'mall/custom/consume_money_draw:盲盒抽奖');
                if (! $status) {
                    throw new DrawActivityException('盲盒抽奖新增失败');
                }
                \Yii::$app->queue->push(new UpdateConsumeMoneyRankJob([
                    'user_id' => $this->user_id,
                    'consume_money' => $consume_money,
                    'request_id' => 'draw',
                ]));
                break;

            case $drawPrize::PRIZE_TYPES['COUPON']:
                by::userCard()->setCard($this->user_id, $prizeData['value'], by::userCard()::GET_CHANNEL['draw_activity']);
                break;

            case $drawPrize::PRIZE_TYPES['EXTERNAL_COUPON']:
                $this->updateExternalCoupon($prizeRecordId, DrawExternalCouponModel::COUPON_TYPES['QING_XI_DAO_JIA']);
                break;
            
            case $drawPrize::PRIZE_TYPES['FREE_AGAIN']:
                
                break;
        }
    }
    
    /**
     * 扣减奖品库存
     * 扣减抽奖次数
     * 添加抽奖记录
     * @param int $user_id
     * @param int $user_phone
     * @param int $activity_id
     * @param int $prize_id
     * @param array $prizeData
     * @param int $consume_type
     * @param int $consume_gold
     * @return int
     */
    private function handle(int $user_id, int $user_phone, int $activity_id, int $prize_id, array $prizeData, int $consume_type = 1, int $consume_gold = 0): int
    {
        $db = by::dbMaster();

        // 1、执行更新操作
        $transaction = $db->beginTransaction();
        try {
            $now = time();
            $prize = $this->lockPrize($activity_id, $prize_id);

            // 消耗类型为次数的校验抽奖次数
            if ($consume_type == 1) {
                $task_record = $this->lockTaskRecord($activity_id, $user_id, $now);
            }

            // 更新库存和抽奖次数
            $db->createCommand()
                ->update(byNew::DrawActivityPrize()::tbName(), ['prize_num' => $prize['prize_num'] - 1, 'prize_issue_num' => $prize['prize_issue_num'] + 1, 'utime' => $now], ['id' => $prize['id']])
                ->execute();

            // 耗类型为次数的才更新抽奖次数
            if ($consume_type == 1) {
                $db->createCommand()
                    ->update(byNew::DrawActivityTaskRecord()::tbName(), ['draw_times' => $task_record['draw_times'] - 1, 'used_draw_times' => $task_record['used_draw_times'] + 1, 'utime' => $now], ['id' => $task_record['id']])
                    ->execute();
            }

            // 插入中奖记录
            $prizeRecordId = 0;
//            if (!$prize['is_default']) { // 默认奖品，不插入中奖记录
                $prize_record = [
                    'activity_id' => $activity_id,
                    'prize_id'    => $prize_id,
                    'user_id'     => $user_id,
                    'user_phone'  => $user_phone,
                    'prize_name'  => $prizeData['name'],
                    'prize_type'  => $prizeData['type'],
                    'prize_value' => $prizeData['value'], // 奖品值：当是第三方券时，展示具体的券码
                    'draw_time'   => $now,
                    'consume_gold' => empty($consume_gold) ? 0 : $consume_gold, // 抽奖消耗的消费金
                ];
                $db->createCommand()->insert(byNew::DrawActivityPrizeRecord()::tbName(), $prize_record)->execute();
                // 获取中奖记录id
                $prizeRecordId = $db->getLastInsertID();
//            }
            $transaction->commit();
            return $prizeRecordId;
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 异常日志
            $this->logError("处理数据异常：参数：user_id:{$user_id}, activity_id:{$activity_id}, prize_id:{$prize_id}；异常：file:{$e->getFile()}, line:{$e->getLine()}, code:{$e->getCode()}, msg:{$e->getMessage()}");
        }
        return 0;
    }

    /**
     * 更新第三方券码
     * @param int $prizeRecordId
     * @param int $couponType
     * @return void
     */
    private function updateExternalCoupon(int $prizeRecordId, int $couponType)
    {
        $db = by::dbMaster();
        // 1、执行更新操作
        $transaction = $db->beginTransaction();
        try {
            $now = time();
            $coupon = $this->lockExternalCoupon($couponType);

            // 更新中奖记录和券码状态
            $db->createCommand()
                ->update(byNew::DrawActivityPrizeRecord()::tbName(), ['prize_value' => $coupon['code'], 'utime' => $now], ['id' => $prizeRecordId])
                ->execute();
            $drawExternalCoupon = byNew::DrawExternalCoupon();
            $db->createCommand()
                ->update($drawExternalCoupon::tbName(), ['status' => $drawExternalCoupon::COUPON_STATUS['USED'], 'issue_time' => $now], ['id' => $coupon['id']])
                ->execute();

            $transaction->commit();
        } catch (\Exception $e) {
            $transaction->rollBack();
            // 异常日志
            $this->logError("更新第三方券码异常：参数：prize_record_id:{$prizeRecordId}, coupon_type:{$couponType}；异常：file:{$e->getFile()}, line:{$e->getLine()}, code:{$e->getCode()}, msg:{$e->getMessage()}");
        }
    }

    /**
     * 获取库存（悲观锁）锁库存
     * @param int $activity_id
     * @param int $prize_id
     * @return array
     * @throws DrawActivityException
     * @throws \yii\db\Exception
     */
    private function lockPrize(int $activity_id, int $prize_id): array
    {
        $sql = "SELECT `id`, `prize_num`, `prize_issue_num`, `is_default` FROM " . byNew::DrawActivityPrize()::tbName() . " WHERE activity_id=:activity_id AND prize_id=:prize_id LIMIT 1 FOR UPDATE";
        $prize = by::dbMaster()->createCommand($sql, [':activity_id' => $activity_id, ':prize_id' => $prize_id])->queryOne();
        if ($prize['prize_num'] <= 0) {
            throw new DrawActivityException('库存不足');
        }
        return $prize;
    }

    /**
     * 获取抽奖次数
     * @param int $activity_id
     * @param int $user_id
     * @param int $now
     * @return array
     * @throws DrawActivityException
     * @throws \yii\db\Exception
     */
    private function lockTaskRecord(int $activity_id, int $user_id, int $now): array
    {
        $sql = "SELECT `id`, `draw_times`, `used_draw_times` FROM " . byNew::DrawActivityTaskRecord()::tbName() . " WHERE activity_id=:activity_id AND user_id=:user_id AND draw_times > 0 AND draw_expire_time > :now ORDER BY draw_expire_time ASC LIMIT 1 FOR UPDATE";
        $task_record = by::dbMaster()->createCommand($sql, [':activity_id' => $activity_id, ':user_id' => $user_id, ':now' => $now])->queryOne();
        if ($task_record['draw_times'] <= 0) {
            throw new DrawActivityException('抽奖次数不足');
        }
        return $task_record;
    }

    /**
     * 获取未使用的优惠券
     * @param int $type
     * @return array
     * @throws DrawActivityException
     * @throws \yii\db\Exception
     */
    private function lockExternalCoupon(int $type): array
    {
        $sql = "SELECT `id`, `code` FROM " . byNew::DrawExternalCoupon()::tbName() . " WHERE status = :status AND type = :type limit 1 FOR UPDATE";
        $coupon = by::dbMaster()->createCommand($sql, [':status' => DrawExternalCouponModel::COUPON_STATUS['UN_USED'], ':type' => $type])->queryOne();
        if (empty($coupon)) {
            throw new DrawActivityException('券码库存不足');
        }
        return $coupon;
    }

    private function logError(string $message)
    {
        CUtil::debug($message, 'err.draw-activity.job');
    }

}