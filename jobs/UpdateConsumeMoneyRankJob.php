<?php

namespace app\jobs;

use app\models\byNew;
use app\models\CUtil;

/**
 * 更新消费金排行
 */
class UpdateConsumeMoneyRankJob extends BaseJob
{
    public $user_id;
    public $consume_money;
    public $request_id;

    public function execute($queue)
    {
        if (! empty($this->user_id)) {
            try {
                $status = byNew::UserConsumeMoneyRankModel()->updateUserRank($this->user_id);
                CUtil::debug(sprintf('更新消费金排行榜统计：request_id：%s user_id：%s consume_money：%s status：%s', $this->request_id ?? '', $this->user_id, $this->consume_money, $status), 'info.UpdateConsumeMoneyRankJob');
            } catch (\Throwable $e) {
                CUtil::debug(sprintf('更新消费金排行榜统计失败：user_id：%s', $this->user_id), 'err.UpdateConsumeMoneyRankJob');
            }
        }
    }
}