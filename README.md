# api-dreamemall

# 注意要点 所有linux 环境必须是dreame文件夹部署代码

追觅商城API
1.composer self-update --1 限制为版本1
2.composer global require "fxp/composer-asset-plugin:^1.3.1"
composer require yiisoft/yii2
3.composer update --no-plugins
4.文件my_common.php 替换在modules下面config下
5.本地配置在文件根目录下新建.env 文件，填入dev，并且在config 下配置dev文件夹仿照prod，直接修改dev文件，避免上传不必要文件
6.sql文件夹下面所有sql文件统一都要执行
7.登录时查看redis工具  DAdmin|sendCodeToWx| + 用户名  获取验证码
8.将redis 过期时间设置为3600s,并且重启，要不然不操作会自动断开
# redis 查看最大连接数(本地)
127.0.0.1:6379> config get maxclients
1) "maxclients"
2) "10000"
# Redis 设置最大连接数（本地）
127.0.0.1:6379> config set maxclients 10000
OK
# 定时任务
9.本地定时任务命令 
./yii service/once2/app-（只用于测试）
./yii service/once2/app-
10.生产环境定时任务
/usr/bin/php -f ./yii service/once2/app-

[//]: # (/usr/bin/php -f ./yii service/once2/send-message)

[//]: # (/usr/bin/php -f ./yii service/once2/sync-birthday)
/usr/bin/php -f ./yii service/once/help-pay2
/usr/bin/php -f ./yii service/once/refund-deposit
/usr/bin/php -f ./yii service/goods/change-deposit-goods
/usr/bin/php -f ./yii service/buy-after-try/get-active-times-by-sns

11.sudo service cron status # 查看crontab服务状态
sudo service cron start # 启动crontab服务
sudo service cron stop # 关闭crontab服务
sudo service cron restart # 重启crontab服务
sudo service cron reload # 重新载入crontab配置

//无需登陆验证uri
$config['noCheckUri'] = [
'main/login/index',
'main/v1/login/index',
'main/goods/list',
'main/home/<USER>',
'main/goods/tag-list',
];


生产环境
redis内/外网地址：r-uf6n4flguyf6vz1ywq.redis.rds.aliyuncs.com/r-mall-cn.redis.rds.aliyuncs.com
端口：6379

mysql内/外网地址：pc-uf6z8c4j58ho37h5u.rwlb.rds.aliyuncs.com/mall-cn.rwlb.rds.aliyuncs.com
端口：3306

密码和以前一样




[//]: # (INSERT INTO `db_dreame_goods`.`t_opay_2023`)

[//]: # (&#40;`id`, `order_no`, `prepay_id`, `tid`, `pay_type`, `h5_url`, `price`, `ptime`, `pay_time`&#41; VALUES &#40;900, '2023031344533197821290262303921', '', '', 99, '', 100, 1672714992, 0&#41;;)


[//]: # (https://open.feishu.cn/open-apis/bot/v2/hook/434ed926-9e5b-4398-8b08-685282bec156)

