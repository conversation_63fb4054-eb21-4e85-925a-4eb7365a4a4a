<?php

/**
 * 第三方接口
 */

namespace app\controllers;

use app\components\Crm;
use app\components\WeiXin;
use app\components\WXOA;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\services\ErpService;
use Yii;
use yii\web\Controller;
use app\modules\main\services\E3Service;
use app\modules\main\services\ErpOrderService;

class CpController extends Controller {

    //第三方鉴权 client_id=10001&code=123332&security_key=652xudul8zxph1m9zzrdxeblfa7dex67&timestamp=1638858148
    CONST API_CONFIG = YII_ENV_PROD ?
            [
                '10001' => [
                    'SECURITY_KEY' => '8cde118f4bd7b56490a2145a2f27aac5',
                    'IPS'          => [],
                    'LIMIT'        => 10000,
                    'API_LIST'     => ['wx-ulink'],
                ],
                '10010' => [
                    'SECURITY_KEY' => '1b3b8b81c23bcae841b1c43c5858b6fb',
                    'IPS'          => ['***************'],
                    'LIMIT'        => 1000,
                    'API_LIST'     => ['point-notice'],
                ],
                '20001' => [ //多麦第三方接口
                    'SECURITY_KEY' => 'cvt7xbhuzv02eq48ppgvbpervaxju3x8',
                    'IPS'          => ['**************','*************'],
                    'LIMIT'        => 2000,
                    'API_LIST'     => ['spread-code','query-order'],
                ],
                '30001' => [ //E3+第三方接口
                    'SECURITY_KEY' => 'gK4fJ7sM5dS8oR8pY3kI7lN3lQ2kS3aI',
                    'IPS'          => [],
                    'LIMIT'        => 2000,
                    'API_LIST'     => ['e3-center'],
                ],
            ] : [
                //自己公司用
                '10001' => [
                    'SECURITY_KEY' => '8cde118f4bd7b56490a2145a2f27aac5',
                    'IPS'          => [],
                    'LIMIT'        => 3000,
                    'API_LIST'     => ['wx-ulink'],
                ],
                '10010' => [
                    'SECURITY_KEY' => '652xudul8zxph1m9zzrdxeblfa7dex67',
                    'IPS'          => [],
                    'LIMIT'        => 1000,
                    'API_LIST'     => ['point-notice'],
                ],
                '20001' => [ //多麦第三方接口
                    'SECURITY_KEY' => 'ucs4lh7kkgdcccqeumgeeo6g9s1ywq7e',
                    'IPS'          => ['***************','*************','************','127.0.0.1','*************','*************'],
                    'LIMIT'        => 2000,
                    'API_LIST'     => ['spread-code','query-order'],
                ],
                '30001' => [ //E3+第三方接口
                    'SECURITY_KEY' => '908c9a564a86426585b29f5335b619bc',
                    'IPS'          => [],
                    'LIMIT'        => 2000,
                    'API_LIST'     => ['e3-center'],
                ],
            ];

    CONST REDIRECT_URI = YII_ENV_PROD ? '' : ' ';
    //无需验证方法
    CONST NO_AUTH_ACTINO = ['redirect-uri'];

    protected $_client_id = null;                         //渠道id
    protected $_sign = null;                         //签名
    protected $_timestamp = null;                         //签名时间
    protected $_security_key = null;                         //签名私钥
    protected $_ips = [];                           //ip白名单
    protected $_limit = 0;                            //频率限制
    protected $_api_list = [];                           //接口权限
    protected $_redirect_uri = '';                           //授权后回调地址
    protected $_expire = YII_ENV_PROD ? 120 : 86400;   //签名有效期 单位：120秒
    protected $_aPost = [];                           //临时接收参数

    /**
     * @param array  $arr
     * @param string $security_key
     * @param string $salt
     * @param string $ext
     * @return string
     * 返回MD5签名
     */
    private function __getSign($arr = [], $security_key = '', $salt = '', $ext = '&'): string
    {
        $target_str = $this->__kV2String($arr, $security_key, $salt, $ext);
        $target_str = strtolower($target_str);

        return md5($target_str);
    }

    /**
     * @param        $arr
     * @param        $security_key
     * @param string $salt
     * @param string $ext
     * @return string
     * 返回一维数组 key-value 待签名字符串
     */
    private function __kV2String($arr = [], $security_key = '', $salt = '', $ext = '&'): string
    {
        //对关联数组按照键名进行升序排序：
        ksort($arr, SORT_STRING); //SORT_STRING - 把每一项作为字符串来处理。
        $target_Arr = [];
        foreach ($arr as $key => $a) {
            if (!is_array($a)) {
                $target_Arr[] = "{$key}={$a}";
            }
        }

        $security_str = empty($salt) ? $security_key : "{$salt}|{$security_key}";
        $target_Arr[] = "security_key=" . $security_str;

        return implode($ext, $target_Arr);
    }

    /**
     * 初始化基础参数
     */
    protected function _initBase()
    {
        $this->_client_id = CUtil::getRequestParam("body", 'client_id');
        $api_config = self::API_CONFIG[$this->_client_id] ?? [];

        if (empty($api_config)) {
            CUtil::json_response(-1, '无效的授权信息');
        }

        $this->_security_key = $api_config['SECURITY_KEY'] ?? '';
        $this->_ips          = $api_config['IPS'] ?? [];
        $this->_limit        = $api_config['LIMIT'] ?? 10;
        $this->_api_list     = $api_config['API_LIST'] ?? [];
        $this->_redirect_uri = $api_config['REDIRECT_URI'] ?? '';

        $this->_aPost = CUtil::getRequestParam(null, null, null);
        if (Yii::$app->request->isGet) {
            if (isset($this->_aPost['r'])) {
                unset($this->_aPost['r']);
            }

            if (isset($this->_aPost['redirect_uri_params'])) {
                $this->_aPost['redirect_uri_params'] = urlencode($this->_aPost['redirect_uri_params']);
            }
        }

        $this->_sign = empty($this->_aPost['sign']) ? "" : trim($this->_aPost['sign']);

        $this->_timestamp = $this->_aPost['timestamp'] ?? 0;
        $this->_timestamp = CUtil::uint($this->_timestamp);

        $this->_client_id && $this->_aPost['client_id'] = $this->_client_id;
    }

    /**
     * @param $action_id
     * 频率限制
     */
    protected function _isValidLimit($action_id)
    {
        // 获取唯一标识键
        $unique_key = CUtil::getAllParams(__FUNCTION__, $action_id);

        // 调用频率限制模型方法
        list($isAllowed) = by::CommModel()->frequencyLimitation(
                $this->_client_id,  // 客户端ID
                $unique_key,        // 唯一键
                10,         // 超时时间（秒）
                $this->_limit       // 请求限制次数
        );

        // 如果超过限制，返回错误响应
        if (!$isAllowed) {
            CUtil::json_response(-1, '请求过于频繁，请稍后再试~');
        }
    }

    /**
     * 签名合法性校验
     */
    protected function _isValidSign()
    {
        unset($this->_aPost['sign']);

        //开发服测试服调试
        if (!YII_ENV_PROD && isset($this->_aPost['teaccaaa'])) {
            unset($this->_aPost['teaccaaa']);
            die($this->__getSign($this->_aPost, $this->_security_key));
        }
        //有效期检验
        if ($this->_timestamp < intval(START_TIME) - $this->_expire) {
            CUtil::json_response(-1, '签名已失效');
        }

        //签名校验
        $verify = $this->__getSign($this->_aPost, $this->_security_key);
        if ($this->_sign !== $verify) {
            $target_str = $this->__kV2String($this->_aPost, $this->_security_key, '', '&');
            $target_str = strtolower($target_str);

            CUtil::debug("{$target_str}|{$this->_sign}|{$verify}", 't12');

            CUtil::json_response(-1, '无效的签名信息');
        }

    }

    /**
     * ip白名单校验
     */
    protected function _isValidIp()
    {
        if (!empty($this->_ips)) {
            $client_ip = CUtil::get_client_ip();
            if (!in_array($client_ip, $this->_ips)) {
                CUtil::debug("{$this->_client_id}|{$client_ip}", 'cp.ip.white');
                CUtil::json_response(-1, '请联系管理员添加白名单');
            }
        }
    }

    /**
     * @param $action_id
     * 接口权限校验
     */
    protected function _isValidPermissions($action_id)
    {
        if (!empty($this->_api_list)) {
            if (!in_array($action_id, $this->_api_list)) {
                CUtil::debug("{$this->_client_id}|{$action_id}", 'cp.api.white');
                CUtil::json_response(-1, '请联系管理员添加权限');
            }
        }
    }

    /**
     * @param $action_id
     * 记录请求日志
     */
    protected function _saveReqLog($action_id)
    {
        $client_ip = CUtil::get_client_ip();
        $params    = json_encode($this->_aPost);
        CUtil::debug("{$client_ip}|$action_id|$params", 'cp.req.log');
    }

    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action)
    {
        $action_id = $action->controller->action->id;
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            Yii::$app->request->headers->add('request_id', $request_id);
        }
        if (in_array($action_id, self::NO_AUTH_ACTINO)) {
            return true;
        }

        $this->_initBase();

        $this->_saveReqLog($action_id);

        $this->_isValidPermissions($action_id);

//        $this->_isValidLimit($action_id); // 去掉频率限制

        if ($action_id != 'authorize') {

            $this->_isValidIp();
        }

        //签名合法性校验
        $this->_isValidSign();

        return true;
    }


    /**
     * wx url_link
     */
    public function actionWxUlink()
    {
        $code = Yii::$app->request->post('code', 0);
        if (empty($code)) {
            CUtil::json_response(-1, '参数错误');
        }

        $code = str_replace(" ","+",trim($code));

        list($status, $ulink) = by::model('WxUlinkModel', 'main')->GetUlinkBycode($code);

        if (!$status) {
            CUtil::json_response(-1, $ulink);
        }

        CUtil::json_response(1, 'ok', ['ulink' => $ulink]);
    }

    /**
     * crm平台发送积分变更提醒
     */
    public function actionPointNotice(){
        $post       = Yii::$app->request->post();
        $card       = $post['card'] ?? '';
        $type       = $post['type'] ?? '';
        $score      = $post['score'] ?? '';
        $date       = $post['date'] ?? '';
        $balance    = $post['balance'] ?? '';


        list($status, $msg) = Crm::factory()->pointPush($card,$type,$score,$balance,$date);

        if (!$status) {
            CUtil::json_response(-1, $msg);
        }

        CUtil::json_response(1, 'ok');

    }


    public function actionSpreadCode()
    {
        $post     = Yii::$app->request->post();
        $unionArr =  CUtil::getConfig('unionFields', 'spread', MAIN_MODULE) ?? [];
        $euidArr = CUtil::getConfig('euidFields', 'spread', MAIN_MODULE) ?? [];
        $union = $euid = '';
        foreach ($unionArr as $union){
            $union = $post[$union] ?? '';
            if(!empty($union)) {
                break;
            }
        }

        foreach ($euidArr as $euid){
            $euid = $post[$euid] ?? '';
            if(!empty($euid)) {
                break;
            }
        }

        $union    = CUtil::removeXss($union);
        $euid     = CUtil::removeXss($euid);
        $pageView = CUtil::removeXss($post['page_view']??'');

        //参数校验
        if(empty($union)){
            CUtil::json_response(-1, '渠道来源未传！');
        }
        if(empty($euid)){
            CUtil::json_response(-1, '渠道来源-标识参数未传！');
        }
        if(empty($pageView)){
            CUtil::json_response(-1, '推广页面未传！');
        }


        //获取url的参数
        $urlArr = parse_url($pageView);

        $path = trim($urlArr['path'] ?? '');
        $query = $urlArr['query'] ?? '';

        $scene = 'union='.$union.'&euid='.$euid;
        $url = $path . '/?' . (empty($query) ? $scene : ($query . '&' . $scene));

        $md5Scene = substr(md5($url),8,16);

        $data = [
          'path' => $path,
          'query'=> $query,
          'scene'=> $scene
        ];

        //设置redis 缓存
        by::spread()->setSpreadCode($md5Scene,$data);

        //中间页
        $frontPage = 'pages/duomai/duomai';
        //生成太阳码
        //太阳码
        list($s, $sunCode)  = WeiXin::factory()->getWxaCodeUnlimit($frontPage,'cps='.$md5Scene,false);
        //二维码url
        $wxPlatformHost = CUtil::getConfig('wxPlatformHost', 'common', MAIN_MODULE);
        //生成对应的url
        $url = $wxPlatformHost.'/duomai_share/?cps='.$md5Scene;


        $data = [
            'sun_code' => empty($s) ? '' : base64_encode($sunCode),
            'scene'    => $scene,
            'url'      => $url,
        ];

        CUtil::json_response(1, 'ok',$data);
    }


    /**
     * 多麦查询订单接口
     * @throws \yii\db\Exception
     */
    public function actionQueryOrder()
    {
        $post    = Yii::$app->request->post();
        $union = trim($post['union'] ?? '');
        $stime   = intval($post['stime'] ?? 0);
        $etime   = intval($post['etime'] ?? 0);
        $sn      = trim($post['sn'] ?? 0);
        //sn 进行防护处理
        $sn  = str_replace(PHP_EOL, ' ', $sn);
        $sn  = CUtil::removeXss($sn);

        if (empty($union)) {
            CUtil::json_response(-1, 'union 必填');
        }
        if (empty($stime) || empty($etime)) {
            CUtil::json_response(-1, '起始时间和结束时间必填');
        }
        if ($stime >= $etime) {
            CUtil::json_response(-1, '起始时间必须小于结束时间');
        }
        if (abs($etime - $stime) > 30 * 24 * 3600) {
            CUtil::json_response(-1, '查询时间间隔必须小于30天');
        }

        //查询订单详情
        $input = [
            'stime'=>$stime,
            'etime'=>$etime,
            'order_no'=>$sn,
        ];

        //获取年份
        $syear = date('Y',$stime);
        $eyear = date('Y',$etime);

        $years = array_unique([$syear,$eyear]);
        $list = [];
        foreach ($years as $year) {
            $item = by::osourceM()->getList($union,$year,$input,true);
            $list = array_merge($list,$item);
        }

        $data = [];
        //返回封装
        if ($list) {
            foreach ($list as $li) {
                $oInfo  = $li['o_info'] ?? [];
                $goods  = $oInfo['goods'] ?? [];
                $detail = [];
                if ($oInfo && $goods) {
                    foreach ($goods as $g) {
                        $detail[] = [
                            'goods_id'    => $g['sku'] ?? '',
                            'goods_name'  => $g['name'] ?? '',
                            'goods_price' => $g['uprice'] ?? '',
                            'goods_ta'    => $g['num'] ?? 1,
                            'totalPrice'  => $g['price'] ?? 1,
                            'commission'  => 0,//这边默认 0
                            'rate'        => 0,//这边默认 0
                        ];
                    }
                    $data[] = [
                        'euid'              => $li['euid'] ?? '',
                        'order_sn'          => $oInfo['order_no'] ?? '',
                        'order_time'        => empty($oInfo['ctime'] ?? '') ? '' : date('Y-m-d H:i:s', $oInfo['ctime']),
                        'orders_price'      => $oInfo['price'] ?? 0,
                        'order_status'      => by::Omain()::STATUS_NAME[$oInfo['status']] ?? '未知',
                        'order_status_code' => $oInfo['status'] ?? -1,
                        'referer'           => $li['referer'] ?? '',
                        'currency'          => 'CNY',
                        'timezone'          => 'UTC+8',
                        'test'              => YII_ENV_PROD ? 0 : 1,
                        'detail'            => $detail,
                    ];
                }
            }
        }

        CUtil::json_response(1, 'ok',$data);

    }

    // E3+ 公共调用接口
    public function actionE3Center(){
        $post = Yii::$app->request->post();
        $event = $post['event'] ?? '';
        $data = $post['data'] ?? '';
        if (empty($data) || empty($event)) {
            CUtil::json_response(-1, '参数错误,event和data不能为空');
        }
        $data = json_decode($data,true);
        switch ($event) {
            case 'member-info':
                list($data,$status,$msg) = E3Service::getInstance()->getMemberInfo($data);
                break;
            case 'buy-info':
                list($data,$status,$msg) = E3Service::getInstance()->getBuyInfo($data);
                break;
            case 'save-order':
                list($data,$status,$msg) = E3Service::getInstance()->saveOrder($data);
                break;
            case 'order-reward':
                list($data,$status,$msg) = E3Service::getInstance()->orderReward($data);
                break;
            case 'order-refund':
                list($data,$status,$msg) = E3Service::getInstance()->orderRefund($data);
                break;
            case 'save-erp-order':
                list($data,$status,$msg) = ErpOrderService::getInstance()->saveOrder($data);
                break;
            default:
                CUtil::json_response(-1, 'event 参数错误');
                break;
            }
        // todo 返回错误码要根据不同错误做不同处理,目前统一返回-1，社区忙完优化
        if (!$status){
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok',$data);
    }
}
