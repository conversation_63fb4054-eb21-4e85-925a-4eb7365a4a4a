<?php

namespace app\controllers;
use app\components\AppCRedisKeys;
use app\models\by;
use app\models\CUtil;
use Yii;
use yii\web\Controller;

class SiteController extends Controller {

    public $layout = false;
    public $enableCsrfValidation = false;
/**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool{
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            \Yii::$app->request->headers->add('request_id', $request_id);
        }
        return parent::beforeAction($action);
    }
    public function actionIndex(){
        YII_ENV_PROD && exit(AppCRedisKeys::$prefix);
        $projectRoot = Yii::getAlias('@app');
        header('Content-Type: application/json');


        //设置docs路径
        $dir = dirname($projectRoot . '/web/swagger-docs/swagger.json');
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }


        //生成文件
        $dirs = [[$projectRoot . '/modules/main/schemas',$projectRoot . '/modules/main/controllers'], [$projectRoot . '/modules/back/schemas',$projectRoot . '/modules/back/controllers', $projectRoot . '/modules/rbac/controllers']];
        foreach ($dirs as $key=>$item){
            $swagger = \OpenApi\scan($item);
            $swagger = json_encode($swagger) ;
            $json_file = $projectRoot . "/web/swagger-docs/swagger-".($key+1).".json";
            $is_write = file_put_contents($json_file, $swagger);
        }
        if ($is_write) {
            $this->redirect('/swagger-ui/dist/index.html');
        }
    }

}
