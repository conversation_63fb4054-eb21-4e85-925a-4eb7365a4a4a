<?php

namespace app\controllers;

use app\components\AliYunOss;
use app\components\AliYunSls;
use app\components\AliYunSms;
use app\components\AliZhima;
use app\components\AppCRedisKeys;
use app\components\Device;
use app\components\Mall;
use app\components\MessagePush;
use app\components\PointCenter;
use app\components\EventMsg;
use app\components\JwtTools;
use app\components\ErpNew;
use app\components\MemberCenter;
use app\components\ProductSn;
use app\components\RateLimiter;
use app\components\RuiYun;
use app\components\WeWork;
use app\components\WXOA;
use app\jobs\GroupPurchaseJob;
use app\jobs\SyncPointGrowJob;
use app\jobs\TestJob;
use app\jobs\TryBuyUserPathJob;
use app\jobs\VirtualGoodsJob;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\back\services\SmsService;
use app\modules\back\services\TryOrdersService;
use app\modules\back\services\UserTryService;
use app\modules\goods\services\DrawActivityService;
use app\modules\goods\services\ErpService;
use app\modules\goods\services\UserOrderTryService;
use app\modules\main\enums\user\UserTypeEnum;
use app\modules\main\models\ActivityConfigModel;
use app\modules\main\models\AreaModel;
use app\modules\main\models\BannerModel;
use app\modules\main\models\MemberActivityModel;
use app\modules\main\models\ProductModel;
use app\modules\main\models\MemberErrLogModel;
use app\modules\main\models\ProductRegModel;
use app\modules\main\models\RetailersModel;
use app\modules\main\services\DataService;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\services\GroupPurchaseService;
use app\modules\main\services\invite\InviteGiftService;
use app\modules\main\services\invite\RegMallInviteHandler;
use app\modules\main\services\LoginService;
use Faker\Provider\Uuid;
use yii\db\Exception;
use yii\helpers\VarDumper;
use yii\web\Controller;
use app\jobs\UserOrderTryJob;

class TestController extends Controller
{
    public $layout               = false;
    public $enableCsrfValidation = false;

    const TYPE = [
            1 => ['shipping_code' => 'JT', 'shipping_sn' => 'JT3120543246974', 'shipping_name' => '极兔速运'],//在途
            2 => ['shipping_code' => 'sf', 'shipping_sn' => 'SF1643643288017', 'shipping_name' => '顺丰快递'],//签收
            3 => ['shipping_code' => 'zto', 'shipping_sn' => '78700064953602', 'shipping_name' => '中通快递'],//派件
            4 => ['shipping_code' => 'zto', 'shipping_sn' => '78699578262116', 'shipping_name' => '中通快递'],//退回
    ];

    public function actionIndexTest()
    {
        // 获取当前时间戳
        $cart_time = intval(START_TIME);
        // 计算本周结束时间（周日的23:59:59）
        $end_of_week = strtotime('next Sunday 23:59:59');
        // 计算过期时间（当前时间到本周结束的秒数）
        $expire_time = $end_of_week - $cart_time;
        var_dump($cart_time.'--'.$end_of_week.'--'.$expire_time);
    }

    /**
     * @throws Exception
     * 接口模拟OMS E3+ 发货 更新物流信息
     */
    public function actionErpTest()
    {
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        $orderNo = \Yii::$app->request->post('order_no') ?? '';
        $type    = \Yii::$app->request->post('type') ?? '';
        if (empty($orderNo) || empty($type)) {
            CUtil::json_response(-1, '参数错误');
        }
        $typeKeys = array_keys(self::TYPE);
        if (!in_array($type, $typeKeys)) {
            CUtil::json_response(-1, '类型有误，请重试！');
        }
        $type = self::TYPE[$type];
        $data = [
                'deal_code'     => $orderNo,
                'shipping_code' => $type['shipping_code'],
                'shipping_sn'   => $type['shipping_sn'],
                'shipping_name' => $type['shipping_name']
        ];

        list($s, $ret) = (new ErpService())->orderSendUpdate($data);
        if (!$s) {
            CUtil::json_response(-1, $ret);
        }
        CUtil::json_response(1, 'ok');
    }


    /**
     * @throws Exception
     */
    public function actionDelete()
    {
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        $phone  = \Yii::$app->request->post('phone') ?? '';
        $userId = \Yii::$app->request->post('user_id') ?? '';
        if (empty($phone) || empty($userId)) {
            CUtil::json_response(-1, '手机号或user_id不能为空');
        }
        $grantType = \Yii::$app->request->post('grant_type') ?? '';
        $birthday  = \Yii::$app->request->post('birthday') ?? 0;
        $date      = \Yii::$app->request->post('date') ?? '';
        $drawTime  = \Yii::$app->request->post('draw_time') ?? 0;


        $drawTb = by::AcDrawLogModel()::getTable($phone);
        $where  = [];
        switch ($grantType) {
            case ActivityConfigModel::GRANT_TYPE['newUser']:
                //修改is_new_gift  清除 redis
                $tb = by::users()::userTb($userId);
                by::dbMaster()->createCommand()->update($tb, ['is_new_gift' => 0], ['user_id' => $userId])->execute();
                $cardTb = by::userCard()::tbName($userId);
                $ids    = by::activityConfigModel()->getActivityIdsByType($grantType);
                by::dbMaster()->createCommand()->delete($cardTb, ['get_relation' => $ids])->execute();
                break;
            case ActivityConfigModel::GRANT_TYPE['monthly']:
                if (empty($date)) CUtil::json_response(-1, '当前日期不能为空');
                $year  = date('Y', $drawTime);   //将时间戳转换为年
                $month = date('Ym', $drawTime);  // 将时间戳转换为年月格式
                $where = $drawTime
                        ? ['phone' => $phone, 'year' => $year, 'month' => $month, 'grant_type' => $grantType]
                        : ['phone' => $phone, 'grant_type' => $grantType];
                //by::dbMaster()->createCommand()->delete($tb, $where)->execute();

                break;
            case ActivityConfigModel::GRANT_TYPE['birthday']:
                if (empty($birthday)) CUtil::json_response(-1, '生日不能为空');
                $year  = date('Y', $drawTime);//将时间戳转换为年
                $where = $drawTime
                        ? ['phone' => $phone, 'year' => $year, 'grant_type' => $grantType]
                        : ['phone' => $phone, 'grant_type' => $grantType];

                $UserTb = by::users()::userTb($userId);
                by::dbMaster()->createCommand()->update($UserTb, ['birthday' => $birthday], ['user_id' => $userId])->execute();
                //by::dbMaster()->createCommand()->delete($tb, $where)->execute();
                break;
            default:
                CUtil::json_response(-1, '活动类型不支持修改~');
        }

        $cardTb   = by::userCard()::tbName($userId);
        $drawInfo = by::AcDrawLogModel()->GetDetail($phone, ['grant_type' => $grantType]);
        $acId     = $drawInfo['ac_id'] ?? 0;
        //清除t_ac_draw_og表数据
        by::dbMaster()->createCommand()->delete($drawTb, $where)->execute();
        //清除t_user_card表数据
        by::dbMaster()->createCommand()->delete($cardTb, ['get_relation' => $acId])->execute();

        //清除缓存
        by::AcDrawLogModel()->__delCacheOne($phone);
        by::userCard()->__delCache($userId);
        by::users()->selfDelCache($userId, $phone);
        by::userCard()->__delCache($userId);
        CUtil::json_response(1, '已清除数据~');
    }

    /**
     * 获取BI系统中的SN信息
     * @throws \app\exceptions\ProductSnException
     */
    public function actionSn()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        // 非生产环境
        $params    = \Yii::$app->request->get();
        $sn        = $params['sn'] ?? '';
        $typeStr   = $params['ddlx'] ?? '';
        $isCheckBi = $params['check_bi'] ?? 0;

        if (empty($isCheckBi)) {
            $str   = substr($sn, 6, 3);
            $arr   = str_split($str);
            $timeY = CUtil::getSnTimeY($arr[0] ?? '');
            $timeM = CUtil::getSnTimeM($arr[1] ?? '');
            $timeD = CUtil::getSnTimeD($arr[2] ?? '');
            $res   = $timeY . '-' . $timeM . '-' . $timeD;
        } else {
            $host = "http://dreamepopap01.dreame.tech:50000";
            $res  = ProductSn::factory()->getProductSn($sn, $host, $typeStr);
        }


        CUtil::json_response(1, 'ok', $res);
    }


    /**
     * @throws Exception
     */
    public function actionStock()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        // 非生产环境
        $params   = \Yii::$app->request->get();
        $sku      = $params['sku'] ?? '';
        $stock    = $params['stock'] ?? '';
        $gidInfos = by::GoodsPointsModel()->GetList(['is_send' => 2], 1, 0);
        $gids     = array_column($gidInfos, 'gid');
        $skus     = by::GoodsPointsPriceModel()->GetSkusByGids($gids);
        by::GoodsStockModel()->UpdateStock(0, 0, $stock, 'SET', true, by::GoodsStockModel()::SOURCE['OTHER'], $sku);
        if (in_array($sku, $skus)) CUtil::json_response(1, 'ok! but this sku is not send!', []);

        CUtil::json_response(1, 'ok', []);
    }

    /**
     * @throws Exception
     */
    public function actionFinishOrderReward()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        by::Ouser()->finishOrderReward();
    }

    /**
     * 订单奖励：给邀请人奖励
     * @return void
     * @throws Exception
     */
    public function actionOrderReward()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        by::OsourceR()->orderReward();
    }

    /**
     * @throws Exception
     */
    public function actionUpload()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        @ini_set('post_max_size', '20M');
        @ini_set('upload_max_filesize', '20M');
        $post = \Yii::$app->request->post();
        $file = $_FILES['file'] ?? "";
        if (empty($file)) CUtil::json_response(-1, 'false', "文件必须上传");
        list($s, $url) = AliYunOss::factory()->uploadFileToOss($file);
        CUtil::json_response(1, 'ok', $url);
    }

    function getCombinations($set, $includeElement)
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $result = [];
        $subset = [];
        $n      = count($set);

        function helper($set, $includeElement, &$result, &$subset, $index)
        {
            if ($index == count($set)) {
                // 如果组合中包含指定元素，则加入结果集
                if (in_array($includeElement, $subset)) {
                    $result[] = $subset;
                }
                return;
            }

            // 不包含当前元素的组合
            helper($set, $includeElement, $result, $subset, $index + 1);

            // 包含当前元素的组合
            $subset[] = $set[$index];
            helper($set, $includeElement, $result, $subset, $index + 1);
            array_pop($subset);  // 回溯
        }

        helper($set, $includeElement, $result, $subset, 0);

        // 计算每个组合的和并返回结果
        $combinationSums = [];
        foreach ($result as $combination) {
            $combinationSums[] = [
                    'combination' => $combination,
                    'sum'         => array_sum($combination)
            ];
        }

        return $combinationSums;
    }


    public function actionA()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $rule      = [
                'id' => '3',

        ];
        $giftItems = [
                [
                        'id'        => '2',
                        'gift_id'   => '',
                        'gift_type' => '3',
                        'quantity'  => '500',

                ]
        ];
        $userInviteGiftsModel = by::UserInviteGiftsModel();

        $handler  = new RegMallInviteHandler();
        $handler->processInviteGifts(12295,3,3);
//        InviteGiftService::getInstance()->processAutoGrant(12295, $rule, $giftItems, $userInviteGiftsModel);

//        PointCenter::factory()->memBerMallInvite(12295,500);
    }


    public function actionB()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $fp = fopen('/tmp/门店地址省市区.csv', 'r');
        // 跳过 CSV 表头（第一行）
        fgetcsv($fp);

        $tb = RetailersModel::tbName();
        while ($line = fgetcsv($fp)) {
            $id          = mb_convert_encoding($line[0], 'UTF-8', 'GBK');
            $shop_name   = mb_convert_encoding($line[1], 'UTF-8', 'GBK');
            $shop_addr   = mb_convert_encoding($line[2], 'UTF-8', 'GBK');
            $province    = mb_convert_encoding($line[3], 'UTF-8', 'GBK');
            $province_id = mb_convert_encoding($line[4], 'UTF-8', 'GBK');
            $city        = mb_convert_encoding($line[5], 'UTF-8', 'GBK');
            $city_id     = mb_convert_encoding($line[6], 'UTF-8', 'GBK');
            $district    = mb_convert_encoding($line[7], 'UTF-8', 'GBK');
            $district_id = mb_convert_encoding($line[8], 'UTF-8', 'GBK');

            $updateSql = <<<SQL
 UPDATE  {$tb}
    SET 
        shop_name = "{$shop_name}",
        shop_addr = "{$shop_addr}",
        province = "{$province}",
        province_id = {$province_id},
        city = "{$city}",
        city_id = {$city_id},
        district = "{$district}",
        district_id = {$district_id}
    WHERE id = {$id}
SQL;
            by::dbMaster()->createCommand($updateSql)->execute();
        }
        echo 'ok';
        exit();
    }

    /**
     * @return void
     * 开始试用--没验签
     */
    public function actionTry()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $post     = \Yii::$app->request->post();
        $order_no = $post['order_no'] ?? '';
        if (empty($order_no)) {
            CUtil::json_response(-1, "订单编号必传");
        }

        list($status, $msg) = TryOrdersService::getInstance()->BeginTry($order_no);
        if (!$status) {
            CUtil::json_response(-1, $msg);
        }
        CUtil::json_response(1, 'ok');
    }

    /**
     * 测试支付宝支付
     * @return void
     */
    public function actionAlipayPay()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        // 临时处理跨域问题
        header('Access-Control-Allow-Origin:*');
        header('Access-Control-Allow-Headers:*');
        header('Access-Control-REQUEST-METHOD:GET,POST');

        list($status, $res) = AliPayModel::getInstance()->test('dreame00001', 'order_no_' . date('YmdHis') . rand(1000, 9999), '0.02');
        if ($status) {
            CUtil::json_response(1, 'ok', $res);
        }
        CUtil::json_response(-1, $res);
    }


    public function actionUnFreeze()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        $orderNo = \Yii::$app->request->post('order_no');
        if (empty($orderNo)) {
            CUtil::json_response(-1, '请输入订单号');
        }

        $tryOrder = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $orderNo)]);
        if (empty($tryOrder)) {
            CUtil::json_response(-1, '订单不存在');
        }

        if (empty($tryOrder['auth_no'])) {
            CUtil::json_response(-1, '该订单未在支付宝冻结金额');
        }

        list($status, $data) = AliZhima::factory()->UnFreezeOrder($tryOrder['auth_no'], $tryOrder['auth_no'] . '_' . time(), $tryOrder['amount']);
        if (!$status) {
            CUtil::json_response(-1, $data['sub_msg']);
        }

        CUtil::json_response(1, '完结成功');
    }

    public function actionRback()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        $refundNo          = \Yii::$app->request->post('refund_no');
        $data['refund_id'] = $refundNo;
        list($status, $data) = (new ErpService())->refundWarehouseAgree($data);
        if (!$status) {
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, '退货回调成功');
    }


    public function actionRedirect()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $params = \Yii::$app->request->get();
        $config = CUtil::getConfig('pc-weixin', 'common', MAIN_MODULE);
        $url    = $config['web_uri'];

        $code     = $params['code'] ?? '';
        $state    = $params['state'] ?? '';
        $userType = UserTypeEnum::USER_TYPE['WECHAT_SCAN'];

        // 校验参数
        if (empty($code) || empty($state)) {
            CUtil::json_response(-1, "缺少认证参数");
            return; // 确保脚本终止
        }

        // 获取用户信息
        list($status, $data) = LoginService::getInstance()->getUserWxInfo($code, $state, $userType);

        // 检查用户信息获取状态
        if (!$status) {
            CUtil::json_response(-1, $data);
            return; // 确保脚本终止
        }

        // 如果用户未登录且存在用户ID和用户存在标识
        if (empty($data['sessid']) && $data['user_id'] && $data['exists']) {
            $aData = [];
            if (!empty($this->userTmpInfo['session_key'])) {
                $aData = ['session_key' => $this->userTmpInfo['session_key'] ?? ''];
            }
            list($status, $init_session) = by::login()->initUserLoginTarget($data['user_id'], $aData);

            if ($status === false) {
                CUtil::json_response(-100, "登录失败");
                return; // 确保脚本终止
            }
            $data['sessid'] = $init_session;
        }

        $redirectParam = [
                'exists'   => $data['exists'],
                'sessid'   => $data['sessid'],
                'user_id'  => $data['user_id'],
                'avatar'   => $data['avatar'],
                'nickname' => $data['nickname'],
        ];

        // 构建重定向 URL
        $queryString = http_build_query($redirectParam);
        $redirectUrl = $url . '?' . $queryString;

        // 执行重定向
        header("Location: $redirectUrl");
        exit();
    }


    public function actionTest()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        by::redis()->set('llll', 'test', 600);
    }

    public function actionFeishuEventNotify()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $content = file_get_contents('php://input');
        $post    = json_decode($content, true);
        error_log(var_export(array(date('Y-m-d H:i:s'), $post), true), 3, __FILE__ . '.log');
        echo 'success';
        exit;
    }


    public function actionResetUser()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $phone = \Yii::$app->request->post('phone');
        if (empty($phone) || strlen($phone) != 11) {
            CUtil::json_response(-1, '请输入正确的手机号');
        }

        $userIds = by::Phone()->GetUidsByPhone($phone, 100, false);

        if ($userIds) {
            $users = implode("','", $userIds);
            //获取积分总数
            $tb        = by::userRecommend()::tbName();
            $updateSql = "UPDATE {$tb} SET `bind_reward_point` = 0 WHERE `user_id` IN ('{$users}')";
            by::dbMaster()->createCommand($updateSql)->execute();
        }
        CUtil::json_response(1, 'OK');
    }


    public function actionSendCoupon()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $job = new VirtualGoodsJob([
                'order_no' => '202502113781300112123142502920',
                'user_id'  => 7995,
                'gid'      => 375,
                'sid'      => 0,
        ]);
        $job->execute(\Yii::$app->queue);


        exit;


    }


    public function actionMarket()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        // 缓存 marketConfig 实例和常量
        $marketConfig   = by::marketConfig();
        $fixedValidType = $marketConfig::VALID_TYPE['fixed'];

        // 查询数据，避免冗余字段查询
        $list = $marketConfig::find()
                ->select(['id', 'resource'])
                ->asArray()
                ->all();

        $updateData = [];
        foreach ($list as $item) {
            // 安全解码 JSON，避免报错
            $resource = json_decode($item['resource'], true);

            if (isset($resource['valid_type']) && $resource['valid_type'] == $fixedValidType) {
                // 获取有效期时间
                list($start_time, $expire_time) = $marketConfig->expireTime($fixedValidType, $resource['valid_val']);

                // 格式化时间并拼接
                $resource['valid_val'] = date('Y-m-d H:i:s', $start_time) . '~' . date('Y-m-d H:i:s', ($expire_time + 86399));
                // 准备更新数据
                $updateData[] = [
                        'id'       => $item['id'],
                        'resource' => json_encode($resource, 320)
                ];
            }
        }

        // 生成批量更新 SQL，使用更安全的方式避免 SQL 注入
        if (!empty($updateData)) {
            $updateSql = CUtil::batchUpdate($updateData, "id", $marketConfig::tableName());

            // 执行批量更新
            return by::dbMaster()->createCommand($updateSql)->execute(); // 返回更新的行数，或作其他处理
        }

        return 0; // 如果没有更新任何数据，返回 0
    }

    public function actionTest2()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $rateLimiter = new RateLimiter();
        if ($rateLimiter->checkRateLimit("ratelimit", 9652, 5, 10)) {
            echo 'ok';
        } else {
            http_response_code(429);
            echo "Too Many Requests";
            exit;
        }
        // $user_id = rand(1000, 9999);
        // var_dump($user_id);
        // $redisKey = AppCRedisKeys::AccFrequency('test', $user_id);
        // // $isAllowed = $rateLimiter->allowedByTokenBucket($redisKey, 1, 1, 5);
        // $isAllowed = $rateLimiter->allowedByCounter($redisKey, 10, 1);
        //
        // if ($isAllowed) {
        //     echo 'ok';
        // } else {
        //     http_response_code(429);
        //     echo "Too Many Requests";
        //     exit;
        // }
    }

    public function actionTest3()
    {
        // $btime = strtotime("2025-04-01 00:00:00") * 1000;
        // $etime = strtotime("2025-04-30 00:00:00") * 1000;
        // // MemberCenter::factory()->memberActivityCheckin(0, '测试活动积分打卡', $btime, $etime, 100, 7, 2025);
        // MemberCenter::factory()->memberActivityReturnPoints(0, '测试活动积分打卡', $btime, $etime, 4);
        //
        // $users = [
        //     ['name' => 'Alice', 'department' => 'HR'],
        //     ['name' => 'Bob', 'department' => 'IT'],
        //     ['name' => 'Charlie', 'department' => 'HR'],
        // ];
        //
        // $groupedUsers = CUtil::groupBy($users, 'department');
        // var_dump($groupedUsers);
        //
        // var_dump(MemberActivityModel::getInstance()->getAvailableIds());
        //
        // $res = DrawActivityService::getInstance()->doActivityTask(10003, ['task_code' => 'INVITE_FRIEND', 'order_no' => '20250424536240010005462504865', 'invitee_id' => 10003]);
        // $res = DrawActivityService::getInstance()->doActivityTask(10003, ['task_code' => 'BUY_FITTING', 'order_no' => '20250424536240010005462504865', 'invitee_id' => 10003]);
        // var_dump($res);
        
        // $r = by::redis();
        // list($status, $data) = MemberCenter::factory()->scoreGet(12125);
        // var_dump($status, $data);exit;
        var_dump(EventMsg::factory()->run('appInviteRegMoney', ['user_id' => 12125]));
        var_dump(EventMsg::factory()->run('appInviteFriendLike', ['user_id' => 12125]));
        exit;
        $gdata = by::Gmain()->checkThirdGoods(267);
        var_dump($gdata);exit;
        // list($s, $d) = EventMsg::factory()->drawPoint(12125, 100);
        // list($s, $d) = MemberCenter::factory()->sendDrawPoint(12125, 100);
        //
        // var_dump($s, $d);
        // exit;
        
        list($status, $data) = MemberCenter::factory()->ruleFineDetail('mall', 'dreame', 'invite_reg');
        var_dump($status, $data);exit;
        
        $config = CUtil::dictData('draw_gold_unlock_config');
        var_dump($config);exit;
        $config = array_column($config, null, 'label');
        $exchange_gold_rate = (int) ($config['exchange_gold_rate']['value'] ?? 0);
        $exchange_point_rate = (int) ($config['exchange_point_rate']['value'] ?? 0);
        var_dump($config,$exchange_gold_rate,$exchange_point_rate);exit;
        list($status, $data) = MemberCenter::factory()->syncPointExchange('mall/dreame/raffle_exchange', 12125, 0, 20, 0, 1, '金币消耗');
        var_dump($status, $data);exit;
        list($status, $data) = MemberCenter::factory()->reduceGold(12125, 10000);
        var_dump($data);
        // if (! $status) {
        //     // TODO
        // }
        // $config = CUtil::dictData('draw_gold_config');
        // $config = array_column($config, null, 'label');
        // var_dump($config);
        //
        // var_dump($data);exit;
    }


    public function actionRuiyun()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $params = [
                'province_id' => 'a324e5fa-8e16-e611-80c0-000c292d3ec1',
                'city_id'     => '3c580001-0100-7f7c-0000-0617aef237dc'
        ];
//        RuiYun::factory()->getAddress('PROVINCE');
//        RuiYun::factory()->getAddress('CITY',$params);
        RuiYun::factory()->getAddress('COUNTY', $params);
    }

    /**
     * @throws \Throwable
     */
    public function actionTest4()
    {

//        $job=new GroupPurchaseJob([
//                'order_no'          => '20250618352160011335612506925',
//                'group_purchase_id' => 359,
//                'user_id'           => 7995,
//        ]);
//        $job->execute(\Yii::$app->queue);

        // 业务逻辑
//        $groupPurchaseService = GroupPurchaseService::getInstance();
//        list($status, $data, $code) = $groupPurchaseService->verifyQualification(7995);

//       $canBuy= GroupPurchaseService::getInstance()->limitBuy(33,1212,7995);
        $activity = byNew::SubsidyActivityGoodsModel()->clearCache(1);

        dd($activity);

    }

    public function actionUser5()
    {
        $phone = \Yii::$app->request->post('phone') ?? '';
        if (empty($phone)) {
            CUtil::json_response(-1, '手机号必传');
        }
//
//        $time = \Yii::$app->request->post('time') ?? '';
//        if (empty($time)) {
//            CUtil::json_response(-1, '时间戳必传');
//        }
//        // 简单加签名
//        $sign = md5($phone . $time . 'derema@2025113');
//        if ($sign !== \Yii::$app->request->post('sign')) {
//            CUtil::json_response(-1, '签名错误');
//        }

        try {
            $nickName          = '';
            $body['nickName']  = $nickName;
            $body['name']      = $nickName;
            $body['uid']       = '';
            $body['phone']     = $phone;
            $body['phoneCode'] = 86;
            $body['sex']       = 3;
            $body['realName']  = '';
            $body['status']    = $user['status'] ?? 0;
            $body['age']       = $user['age'] ?? 0;
            $body['province']  = 0;
            $body['city']      = 0;
            $body['county']    = 0;
            $body['birthday']  = 0;
            // $body['openid'] = $main['openudid']??'';
            // $body['unionid'] = $main['unionid']??'';
            $body['time'] = !empty($p_time) ? date('Y-m-d', $p_time) : date('Y-m-d');

            $data = Mall::factory()->processRegister($body, 1, '', $phone);

            CUtil::json_response(1, 'success', $data[1] ?? '');
        } catch (\Throwable $e) {
            CUtil::json_response(-1, $e->getMessage());
        }
    }

    // 支付宝app退款
    public function actionAliAppRefund()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }

        $order_no      = \Yii::$app->request->post('order_no') ?? '';
        $refund_no     = \Yii::$app->request->post('refund_no') ?? '';
        $refund_amount = \Yii::$app->request->post('refund_amount') ?? '';


        list($status, $res) = AliPayModel::getInstance()->refund($order_no, $refund_no, $refund_amount);
        if (!$status) {
            CUtil::json_response(-1, $res);
        }
        CUtil::json_response(1, 'ok', $res);
    }


}


