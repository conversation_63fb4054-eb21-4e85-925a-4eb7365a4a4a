<?php

namespace app\controllers;

use app\components\AliApplet;
use app\components\AliZhima;
use app\jobs\ZhiMaLogJob;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\services\UserOrderTryService;
use app\modules\main\services\ZhimaService;
use yii\web\Controller;

require_once \Yii::getAlias("@vendor") . "/mycompany/alipay-aop/AopClient.php";

class ZhimaController extends Controller
{

    public $layout               = false;
    public $enableCsrfValidation = false;

    const PAY_EXPIRE  = YII_ENV_PROD ? 180 : 30;
    const NOTIFY_TYPE = [
        'UNFREEZE' => 'fund_auth_unfreeze',        //资金授权订单解冻通知
        'FREEZE'   => 'fund_auth_freeze',          //资金预授权冻结成功
        'CANCEL'   => 'fund_auth_operation_cancel',//资金预授权明细撤销
        'PAY'      => 'trade_status_sync',         //交易扣款
    ];
    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool{
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            \Yii::$app->request->headers->add('request_id', $request_id);
        }
        return parent::beforeAction($action);
    }


    public function actionNotify()
    {
        $data = \Yii::$app->request->post();
        CUtil::debug(json_encode($data), 'notice.zhima_notify');

        try {
            if (!isset($data['notify_type'])) {
                throw new \Exception("notify_type 不存在");
            }

            $alipayClient = new \AopClient(AliApplet::GetAlipayConfig());
            $signType     = $data['sign_type'] ?? 'RSA2';

            //验签
            $isVerified = $alipayClient->rsaCheckV1($data, null, $signType);
            if (!$isVerified) {
                throw new \Exception("验签失败");
            }
            list($status, $result) = $this->handleNotificationType($data['notify_type'], $data);
            if (!$status) {
                throw new \Exception($result);
            }

            //异步记录回调日志
            \Yii::$app->queue->push(new ZhiMaLogJob(['data' => $data]));

            CUtil::debug("success", 'notice.zhima_notify');
            echo 'success';
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::debug($error, 'err.zhima_notify');
            CUtil::debug("fail", 'notice.zhima_notify');
            echo 'fail';
        }
    }

    protected function handleNotificationType(string $type, array $data): array
    {
        switch ($type) {
            case self::NOTIFY_TYPE['FREEZE']:
                return $this->handleFreezeSuccess($data);
            case self::NOTIFY_TYPE['UNFREEZE']:
                return $this->handleUnfreezeSuccess($data);
            case self::NOTIFY_TYPE['CANCEL']:
                return $this->handleCancelSuccess($data);
            case self::NOTIFY_TYPE['PAY']:
                return $this->handlePaySuccess($data);
            default:
                $this->logUnhandledType($type);
                return [false, "未知类型"];
        }
    }

    //冻结成功
    protected function handleFreezeSuccess(array $data): array
    {
        $payeeUserId = $data['payee_user_id'];//收款支付宝用户号

        // 1.校验收款方是否正确
        if ($payeeUserId !== AliZhima::PID) {
            return [false, "收款方错误"];
        }
        //调用Service层回调方法
        list($status, $result) = ZhimaService::getInstance()->ZhiMaFreezeNotify($data);
        if (!$status) {
            return [false, $result];
        }
        return [true, '冻结成功'];
    }

    //解冻成功
    protected function handleUnfreezeSuccess(array $data): array
    {
        //gmt_create=2024-04-25+10%3A11%3A07&charset=UTF-8&rest_credit_amount=0.00&operation_type=UNFREEZE&sign=BwLTqnEskBQQLdkuXQ%2BFVVCZwecInsMyqwBmn3Kp33btSCHX%2BNoJ%2BFiEJKeIXf8H%2BIu3yCmZqFGtsCDQno%2Fly4HlYYxMKcmXtrAr0FoS9X92yxsa1gky6%2B5XcY4Rqjd%2BlYYaXxwLF4x%2BoJSPZ7VELHTy95%2F1Ck3LJybAGlD51o7IG2COggCyiXG9yMyUJpuEI8SqUkFFIDNal9rBimICUAWtJz4S59XcaXCYVUB%2B73i8szPRrducdEWgze8c1GOJC7KceTGunkRZLClyrQhp%2BFqNq%2FTBGuXW3joa%2B3hGvsN3vohv%2BZ3SqWBd91%2BBQ7QqtdY3MjztiYdqIZ56jZ8rNQ%3D%3D&rest_fund_amount=0.00&auth_no=2024042510002001130544251620&notify_id=2024042501222101108068241487544050&total_freeze_credit_amount=0.01&notify_type=fund_auth_unfreeze&gmt_trans=2024-04-25+10%3A11%3A07&operation_id=20240425288864511305&total_pay_fund_amount=0.00&out_request_no=202404221700251714010725&app_id=****************&sign_type=RSA2&rest_amount=0.00&amount=0.01&notify_time=2024-04-25+10%3A11%3A08&fund_amount=0.00&total_pay_credit_amount=0.00&credit_amount=0.01&out_order_no=20240422170025&total_freeze_fund_amount=0.00&version=1.0&total_unfreeze_fund_amount=0.00&total_pay_amount=0.00&total_freeze_amount=0.01&total_unfreeze_credit_amount=0.01&auth_app_id=****************&total_unfreeze_amount=0.01&status=SUCCESS&payer_logon_id=156****1192
        $order_no = $data['out_order_no'] ?? "";
        if(empty($order_no)){
            return [false, "订单号不存在"];
        }
        $tryOrder = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no)]);
        if(empty($tryOrder)){
            return [false, "先试后买订单不存在"];
        }
        $tryStatus = $tryOrder['try_status'] ?? -1;
        $save = ['user_id' => $tryOrder['user_id'], 'order_no' => $order_no, 'try_status' => byNew::UserOrderTry()::TRY_STATUS['FINISHED']];
        if($tryStatus > byNew::UserOrderTry()::TRY_STATUS['WAIT_TRY']){ //体验过才能算正常完结 有完结时间，未体验过的不算
            $save['over_time'] = time();
        }
        list($s, $m) = byNew::UserOrderTry()->SaveLog($save);
    
        if (!$s) {
            return [false, "先试后买订单扣款失败~订单状态更新失败~"];
        }
    
        return [true, '解冻成功'];
    }

    //取消成功
    protected function handleCancelSuccess(array $data): array
    {
        //{"charset":"UTF-8","notify_time":"2024-05-21 15:29:03","sign":"EgGE0pYfNbBb\/b\/nB22Sa30uUThAaoWfKSaPvVRHpoddFZjTphxljMvICRHWr0nBH6UdP5WNMn3MrNe4eK1FNzMjWyKX3j79s7h+z+cGMNKjvkK0jm3G57LKzD8WtvBiqnQR6QeF7O1YQSzhidpFQN85Xl6fvt8zo2lfNozhba88YFGkBgRkAu6gVEVjNzCYYhgjwtIeB9S4olku9ztrol11JBVy1ZJZUSVlD4SoplN5TT4t63WjoCqRFsn4gNnkQpn4N4AFbi+uMmDlYsSt\/MjPibplt3Y9CJwBAPKdP1xuRg5qxTDwRoGW0Tpl8CakqUvLQhJK86PzI8364GqFqA==","out_order_no":"20240521549422543997723532405453","version":"1.0","auth_no":"2024052110002001130553104417","notify_id":"2024052101222152903068161412897053","notify_type":"fund_auth_operation_cancel","action":"close","auth_app_id":"****************","operation_id":"20240521468006551305","out_request_no":"20240521549422543997723532405453_1716275742","app_id":"****************","sign_type":"RSA2"}
       $order_no = $data['out_order_no'] ?? "";
       return  UserOrderTryService::getInstance()->CancelTryOrder($order_no);
    }

    //扣款成功
    protected function handlePaySuccess(array $data): array
    {
        // {"gmt_create":"2024-05-21 15:33:36","charset":"UTF-8","seller_email":"<EMAIL>","subject":"\u6d4b\u8bd5\u5546\u54c1\u79df\u91d1","sign":"lANoD4Q7K14wBmc6aA8nX5VkzyYEhLMcLwqRuu1ZNPV3gUGcJ9AqFCF8LWsquZkt0iU9NsILx0WsOr85HihNei+cz1RRE3kHfyZpE4P8y9r15QEFHzXXpC1rhHNI09GNRFjGlmtLpUJf9M\/1u0dkMEVws9EtQzrDDOyYqHHZ9F7JXJKn0bS4Ed1Hq603xKtS0ZAsIz8LICUe3Mpz8KyEY7CTXPgAyNi5Rc\/opFUS\/sdM9fgcuB31XgH0r5tKZW2ZM63HQ8xipHmGhgjyeFBDuhH6wjS4LG5UJcYfercvAOqmnwopeze6YM4H7cBLxNhCEdAka2kRp8nAXK+Eb54STQ==","buyer_open_id":"013aB61TwTTbbPGjDB_2GsjRS9UB5-Q7wOFXSAi9YTzOzgd","invoice_amount":"0.02","notify_id":"2024052101222153338081131418809727","fund_bill_list":"[{\"amount\":\"0.02\",\"fundChannel\":\"ALIPAYACCOUNT\"}]","notify_type":"trade_status_sync","trade_status":"TRADE_SUCCESS","receipt_amount":"0.02","app_id":"****************","buyer_pay_amount":"0.02","sign_type":"RSA2","seller_id":"****************","gmt_payment":"2024-05-21 15:33:37","notify_time":"2024-05-21 15:33:38","merchant_app_id":"****************","passback_params":"{\"user_id\":\"123456\"}","version":"1.0","out_trade_no":"20240521559632543835543632405108","total_amount":"0.02","trade_no":"2024052122001481131443641480","auth_app_id":"****************","buyer_logon_id":"156****1192","point_amount":"0.00"}
        $passback_params = json_decode($data['passback_params'], true);
        $user_id = $passback_params['user_id'] ?? 0;
        $order_no = $data['out_trade_no'] ?? "";
    
        if(empty($user_id) || empty($order_no)){
            return [false, "用户ID或订单号不存在"];
        }
    
        if($data['trade_status'] !== 'TRADE_SUCCESS'){
            return [false, "交易状态异常"];
        }
    
        // 更新先试后买订单状态 （扣款成功）
        $tryOrder = byNew::UserOrderTry()->GetOneInfo([CUtil::buildCondition('order_no', '=', $order_no),CUtil::buildCondition('user_id', '=', $user_id)]);
        if(empty($tryOrder)){
            return [false, "先试后买订单不存在"];
        }
        $save = ['user_id' => $user_id, 'order_no' => $order_no, 'try_status' => byNew::UserOrderTry()::TRY_STATUS['DEDUCTED'],'over_time'=>time()];
        list($s, $m) = byNew::UserOrderTry()->SaveLog($save);
    
        if (!$s) {
            return [false, "先试后买订单扣款失败~订单状态更新失败~"];
        }
    
        return [true, '扣款成功'];
    }

    protected function logUnhandledType(string $type)
    {
        CUtil::debug("未知类型: {$type}", 'err.zhima_notify');
    }


    public function actionTest()
    {
        // 生产环境
        if (YII_ENV_PROD) {
            CUtil::json_response(-1, '非法操作');
        }
        //退款
//        $data = [
//            'order_no'   => '20240520354404010264143502405686',
//            'price'=>0.02,
//            'refund_no'=>'20240520354404010264143502405686_17150607591'
//        ];
//        list($status, $data) = AliZhima::TradeRefund($data);
//        print_r($data);die();


        //查询支付
//        list($status, $data) = AliZhima::TradeQuery('20240508345403101838513312405380');
//        print_r($data);die();

        //扣款
//        $data = [
//            'order_no'   => '20240521559632543835543632405108',
//            'auth_no'    => '2024052110002001130553285179',
//            'price'      => '0.02',
//            'goods_name' => '测试商品',
//            'user_id'    => '123456'
//        ];
//        list($sta, $data) = AliZhima::TradePay($data);
//        print_r($data);
//        die();


//        查询
//        list($status, $data) = AliZhima::QueryOrder('20240507495593048110573292405650','20240507495593048110573292405650_1715060759');
//        print_r($data);die();


//        完结
        list($status, $data) = AliZhima::factory()->UnFreezeOrder('2024061710002001870532265945', '2024061710002001870532265945_1715060751',0.01,true);
        print_r($data);
        die();
        //创建
        $orderNo = "20240422" . rand(100000, 999999);
        list($sta, $data) = AliZhima::factory()->FreezeOrder(['price' => 0.01, 'order_no' => $orderNo, 'goods_name' => '测试商品气球🎈']);
        if (!$sta) {
            CUtil::json_response(-1, $data);
        }
        CUtil::json_response(1, 'ok', $data);
    }


}
