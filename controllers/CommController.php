<?php

namespace app\controllers;

use app\components\EventMsg;
use app\components\LockRedis;
use app\components\WeWork;
use app\components\WXOA;
use app\components\XzTradeIn;
use app\jobs\WeWorkSyncJob;
use app\models\BusinessException;
use app\models\by;
use app\models\byNew;
use app\models\CUtil;
use app\modules\goods\models\StoreGoodsModel;
use app\modules\main\models\pay\AliPayModel;
use app\modules\main\models\pay\MpPayModel;
use app\modules\main\services\pay\PayService;
use app\modules\main\services\refund\RefundService;
use app\modules\main\services\TradeInService;
use app\modules\main\services\TurboModeService;
use PhpOffice\PhpSpreadsheet\IOFactory;
use RedisException;
use yii\db\Exception;
use yii\web\Controller;

class CommController extends Controller
{

    public $layout = false;
    public $enableCsrfValidation = false;

    const PAY_EXPIRE = YII_ENV_PROD ? 180 : 30;

    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool{
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            \Yii::$app->request->headers->add('request_id', $request_id);
        }
        return parent::beforeAction($action);
    }

    /**
     * 支付通知
     * 注意此接口无需权限校验
     * @throws Exception
     */
    public function actionNotify()
    {

        $notify_data = file_get_contents("php://input");

        !YII_ENV_PROD && CUtil::debug($notify_data, 'notify.data');

        $notify_data = CUtil::xmlToArray($notify_data);

        list($status, $ret) = by::wxPay()->notify($notify_data);

        //数据收集
        by::model('CommModel', MAIN_MODULE)->recordPostLog([
            'pay_info' => json_encode($notify_data),
            'start_time' => START_TIME,
            'iRet' => intval($status),
            'sMsg' => $ret,
            'end_time' => microtime(true),
        ]);

        if ($status) {
            return 'SUCCESS';
        } else {
            CUtil::debug("输出|{$ret}" . '|' . json_encode($notify_data), 'err.n.pay');
            return "";
        }

    }

    /**
     * 支付宝支付、退款回调
     * @return string
     */
    public function actionAlipayNotify()
    {
        // 处理跨域
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-REQUEST-METHOD: GET, POST');

        $params = \Yii::$app->request->post();
        CUtil::debug("请求参数：" . json_encode($params), 'alipay.notify');

        if (!AliPayModel::getInstance()->verifyNotify($params)) {
            CUtil::debug("验签结果：fail", 'err.alipay.notify');
            return "fail";
        }

        // 处理业务
        if (isset($params['refund_fee'])) {
            if (($params['trade_status'] == RefundService::ALIPAY_TRADE_CLOSED) && ($params['refund_fee'] == 0)) { // 商家主动关闭交易、用户未付款交易超时关闭、全额退款后关闭（第二次回调）
                return 'success';
            }
            list($status, $ret) = RefundService::getInstance()->handleNotify(RefundService::REFUND_TYPE['ALIPAY'], $params);
        } else {
            list($status, $ret) = PayService::getInstance()->handleNotify(PayService::PAY_TYPE['ALIPAY'], $params);
        }

        // 数据收集
        $this->recordNotification($params, $status);

        $message = $status ? "处理结果：success" : "处理结果：fail" . json_encode($params);
        CUtil::debug($message, $status ? 'alipay.notify' : 'err.alipay.notify');

        return $status ? 'success' : 'fail';
    }

    private function recordNotification($params, $status)
    {
        by::model('CommModel', MAIN_MODULE)->recordPostLog([
            'pay_info'   => json_encode($params),
            'start_time' => START_TIME,
            'iRet'       => $status ? 1 : 0,
            'sMsg'       => $status ? 'pay notify success and push job success' : 'pay notify failed',
            'end_time'   => microtime(true),
        ]);
    }

    /**
     * 支付通知
     * 注意此接口无需权限校验
     * @throws Exception
     */
    public function actionH5Notify()
    {

        $notify_data = file_get_contents("php://input");

        !YII_ENV_PROD && CUtil::debug(json_encode($notify_data), 'notify.h5.data');

        $code = 0;
        $message = '';
        //判断回调是否为json数据
        if (is_null(json_decode($notify_data))) {
            $code = 'FAIL';
            $message = 'H5回调数据不是json数据';
            CUtil::debug($message . '|' . json_encode($notify_data), 'err.n.pay');
            return json_encode(['code' => $code, 'message' => $message]);
        }

        //判断必要参数是否正确
        $notify_data = json_decode($notify_data, true);
        $resource = $notify_data['resource'] ?? [];
        if (empty($resource) || !isset($resource['ciphertext']) || !isset($resource['nonce'])) {
            $code = 'FAIL';
            $message = '回调数据参数有误！';
            CUtil::debug($message . '|' . json_encode($notify_data), 'err.n.pay');
            return json_encode(['code' => $code, 'message' => $message]);
        }

        //参数解密是否正确
        list($status, $data) = by::wxH5Pay()->decryptNotifyData($resource['associated_data'] ?? '', $resource['nonce'] ?? '', $resource['ciphertext'] ?? '');
        if ($status === false) {
            CUtil::debug($data . '|' . json_encode($notify_data), 'err.n.pay');
            $code = 'FAIL';
            $message = $data;
            return json_encode(['code' => $code, 'message' => $message]);
        }

        //解密数据处理
        list($status, $ret) = by::wxH5Pay()->notify($data);

        //数据收集
        by::model('CommModel', MAIN_MODULE)->recordPostLog([
            'pay_info' => json_encode($notify_data),
            'start_time' => START_TIME,
            'iRet' => intval($status),
            'sMsg' => $ret,
            'end_time' => microtime(true),
        ]);

        if ($status) {
            return '';
        } else {
            CUtil::debug("输出|{$ret}" . '|' . json_encode($notify_data), 'err.n.pay');
            $code = 'FAIL';
            $message = 'H5回调数据处理失败！';
            return json_encode(['code' => $code, 'message' => $message]);
        }
    }

    /**
     * 支付回调通知
     * @return string
     */
    public function actionMpPayNotify()
    {
        // 处理跨域
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-REQUEST-METHOD: GET, POST');

        // 获取请求参数
        $rawBody = \Yii::$app->request->getRawBody();
        $params = json_decode($rawBody, true);
        CUtil::debug("请求参数：" . json_encode($params), 'mp_pay.notify');
        
        // 验签
        $status = MpPayModel::getInstance()->verifyNotify($params);
        if (!$status) {
            $msg = [
                '错误描述：验签失败',
                '请求参数：' . json_encode($params, JSON_UNESCAPED_UNICODE)
            ];
            CUtil::debug(json_encode($msg, JSON_UNESCAPED_UNICODE), 'err.mp_pay.notify');
            return 'fail';
        }

        // 处理业务
        list($status, $ret) = PayService::getInstance()->handleNotify(PayService::PAY_TYPE['MP_PAY'], $params);

        // 数据收集
        $this->recordNotification($params, $status);

        $message = $status ? "处理结果：success" : "处理结果：fail" . json_encode($params);
        CUtil::debug($message, $status ? 'mp_pay.notify' : 'err.mp_pay.notify');

        return $status ? 'success' : 'fail';
    }

    /**
     * 支付回调通知
     * @return string
     */
    public function actionMpRefundNotify()
    {
        // 处理跨域
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Headers: *');
        header('Access-Control-REQUEST-METHOD: GET, POST');

        // 获取请求参数
        $rawBody = \Yii::$app->request->getRawBody();
        $params = json_decode($rawBody, true);
        CUtil::debug("请求参数：" . json_encode($params), 'mp_refund.notify');

        // 验签
        $status = MpPayModel::getInstance()->verifyNotify($params);
        if (!$status) {
            $msg = [
                '错误描述：验签失败',
                '请求参数：' . json_encode($params, JSON_UNESCAPED_UNICODE)
            ];
            CUtil::debug(json_encode($msg, JSON_UNESCAPED_UNICODE), 'err.mp_refund.notify');
            return 'fail';
        }

        // 处理业务
        list($status, $ret) = RefundService::getInstance()->handleNotify(RefundService::REFUND_TYPE['MP_PAY'], $params);

        // 数据收集
        $this->recordNotification($params, $status);

        $message = $status ? "处理结果：success" : "处理结果：fail" . json_encode($params);
        CUtil::debug($message, $status ? 'mp_refund.notify' : 'err.mp_refund.notify');

        return $status ? 'success' : 'fail';
    }


    /**
     * 退款通知
     * 注意此接口无需权限校验
     * @throws Exception
     */
    public function actionRefundNotify()
    {

        $notify_data = file_get_contents("php://input");

        !YII_ENV_PROD && CUtil::debug($notify_data, 'refund.data');

        $notify_data = CUtil::xmlToArray($notify_data);

        list($status, $ret) = by::WxPay()->refundNotify($notify_data);

        //数据收集
        by::model('CommModel', MAIN_MODULE)->recordPostLog([
            'refund_info' => $notify_data,
            'start_time' => START_TIME,
            'iRet' => intval($status),
            'sMsg' => $ret,
            'end_time' => microtime(true),
        ]);

        if ($status) {
            //https://pay.weixin.qq.com/wiki/doc/api/wxa/wxa_api.php?chapter=9_7&index=8
            //收到微信支付结果通知后，请严格按照示例返回参数给微信支付：
            return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
        } else {
            //发送消息到飞书
//            CUtil::sendMsgToUdp([
//                "**项目 :** dreame\n",
//                "**摘要 :** 退款回调失败\n",
//                "**详情 :** {$ret}\n",
//                "**环境 :** " . (YII_ENV_PROD ? 'release' : 'test') . "\n",
//                "**时间 :** " . date("Y-m-d H:i:s") . "\n",
//            ], '', '', 'p_1644549157');

            CUtil::debug("输出|{$ret}" . '|' . json_encode($notify_data), 'err.nr.pay');
            return "";
        }
    }


    /**
     * h5退款通知
     * 注意此接口无需权限校验
     * @throws Exception
     */
    public function actionRefundH5Notify()
    {

        $notify_data = file_get_contents("php://input");
        !YII_ENV_PROD && CUtil::debug($notify_data, 'refund.h5.data');

        $code = 0;
        $message = '';
        //判断回调是否为json数据
        if (is_null(json_decode($notify_data))) {
            $code = 'FAIL';
            $message = 'H5退款回调数据不是json数据';
            CUtil::debug($message . '|' . json_encode($notify_data), 'refund.h5.data');
            return json_encode(['code' => $code, 'message' => $message]);
        }

        //判断必要参数是否正确
        $notify_data = json_decode($notify_data, true);
        $resource = $notify_data['resource'] ?? [];
        if (empty($resource) || !isset($resource['ciphertext']) || !isset($resource['nonce'])) {
            $code = 'FAIL';
            $message = 'H5退款回调数据参数有误！';
            CUtil::debug($message . '|' . json_encode($notify_data), 'refund.h5.data');
            return json_encode(['code' => $code, 'message' => $message]);
        }

        //参数解密是否正确
        list($status, $data) = by::wxH5Pay()->decryptNotifyData($resource['associated_data'] ?? '', $resource['nonce'] ?? '', $resource['ciphertext'] ?? '');
        if ($status === false) {
            CUtil::debug($data . '|' . json_encode($notify_data), 'refund.h5.data');
            $code = 'FAIL';
            $message = $data;
            return json_encode(['code' => $code, 'message' => $message]);
        }

        list($status, $ret) = by::wxH5Pay()->refundNotify($data);

        //数据收集
        by::model('CommModel', MAIN_MODULE)->recordPostLog([
            'refund_info' => $notify_data,
            'start_time' => START_TIME,
            'iRet' => intval($status),
            'sMsg' => $ret,
            'end_time' => microtime(true),
        ]);

        if ($status) {
            return "";
        } else {
            //发送消息到飞书
//            CUtil::sendMsgToUdp([
//                "**项目 :** dreame\n",
//                "**摘要 :** 退款回调失败\n",
//                "**详情 :** {$ret}\n",
//                "**环境 :** " . (YII_ENV_PROD ? 'release' : 'test') . "\n",
//                "**时间 :** " . date("Y-m-d H:i:s") . "\n",
//            ], '', '', 'p_1644549157');

            CUtil::debug("输出|{$ret}" . '|' . json_encode($notify_data), 'refund.h5.data');
            $code = 'FAIL';
            $message = 'H5退款回调数据处理失败！';
            return json_encode(['code' => $code, 'message' => $message]);
        }
    }


    /**
     * 公众号消息推送通知
     * 所有服务器IP都要加入白名单
     * https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Receiving_event_pushes.html
     *
     *公众号平台认证 GET : {"r":"comm\/oa","signature":"594de0f02121501f4b9af46d06fb4963916ee836","echostr":"4595343961953877958","timestamp":"**********","nonce":"*********"}"
     *
     *关注/取消关注 GET : {"r":"comm\/oa","signature":"7137054c3d5c54308b9e6c656bc081f4b1271b93","timestamp":"**********","nonce":"*********","openid":"oxJ-86paj4u7WUl_n5qY5azJI_go"}
     *
     * notify_data : {"ToUserName":"gh_8dbdb58d8ffb","FromUserName":"oxJ-86paj4u7WUl_n5qY5azJI_go","CreateTime":"**********","MsgType":"event","Event":"unsubscribe","EventKey":{}}
     * @throws Exception
     */
    public function actionOa()
    {
        ob_clean();
        header('content-type:text');

        $params = \Yii::$app->request->get();
        $echostr = $params['echostr'] ?? "";

        if(!CUtil::wxOaLock()){
            echo $echostr;exit();
        }

        $notify_data = file_get_contents("php://input");
        $notify_data = CUtil::xmlToArray($notify_data);

        $status = "";
        $ret = "";
        if (!empty($notify_data)) {
            list($status, $ret) = WXOA::factory()->Focus($params, $notify_data);
        }

        !YII_ENV_PROD && CUtil::debug(json_encode($params) . "|" . json_encode($notify_data) . "|{$status}|{$ret}", 'oa_data');

        echo $echostr;
    }


    /**
     * @param $order_no
     * @return false|string
     * https://api.kuaidi100.com/document/5f0ffa8f2977d50a94e1023c.html
     * 快递订阅推送
     * @throws Exception
     */
    public function actionExpressNotify($order_no)
    {
        $u = \Yii::$app->request->getAbsoluteUrl();

        CUtil::debug("{$order_no}|{$u}", 'express.notify');

        $notify_data = \Yii::$app->request->post();

        CUtil::debug($order_no . '|' . json_encode($notify_data), 'express.notify');

        list($status, $msg) = by::Ofinish()->notify($order_no, $notify_data);

        CUtil::debug("{$order_no}|{$msg}|{$status}", 'express.r.notify');

        $return = [
            'result' => true,
            'returnCode' => 200,
            'message' => '成功',
        ];
        return json_encode($return, JSON_UNESCAPED_UNICODE);

    }

    /**
     * @return string|void
     * @throws RedisException
     * @throws Exception
     * 企业微信回调
     */
    public function actionWeComOa()
    {
        $request    = \Yii::$app->request;
        $currentUrl = $request->url;
        // 进行URL解码
        $decodedUrl = urldecode($currentUrl);
        // 解析URL，获取查询字符串部分
        $urlParts    = parse_url($decodedUrl);
        $queryString = $urlParts['query'] ?? '';
        // 解析查询字符串，获取参数数组
        parse_str($queryString, $queryParams);

        // 处理 GET 请求
        if ($request->isGet) {
            return WeWork::factory()->weComSign($queryParams);
        }

        // 处理 POST 请求
        if ($request->isPost) {
            $xmlData = file_get_contents("php://input");
            if (!empty($xmlData)) {
                // 延时队列，异步同步用户 推送添加觅享官事件
                $expireTime = rand(180, 300);
                \Yii::$app->queue->delay($expireTime)->push(new WeWorkSyncJob([
                    'type'        => 1,
                    'queryParams' => $queryParams,
                    'xmlData'     => $xmlData
                ]));
                return 'ok';
            }
        }
    }

    /**
     * 更新以旧换新状态
     * @return false|string
     * @throws Exception
     */
    public function actionSyncTradeInStatus()
    {
        $request = \Yii::$app->request;

        // 校验请求方式
        if (!$request->isPost) {
            return $this->jsonResponse(202, '请求方式错误');
        }

        $params = json_decode($request->getRawBody(), true);
        $sign = $request->get('sign', '');
        $dateline = $request->get('dateline', '');

        // 校验签名
        if (!XzTradeIn::factory()->checkSign($sign, $params, $dateline)) {
            return $this->jsonResponse(202, '签名错误');
        }

        // 校验参数
        if (!isset($params['mer_order_no'], $params['order_code'], $params['order_status'])) {
            return $this->jsonResponse(202, '参数错误');
        }

        // 业务逻辑处理
        list($status, $ret) = TradeInService::getInstance()->updateStatus(
            $params['mer_order_no'],
            $params['order_code'],
            $params['order_status'],
            json_encode($params)
        );

        // 结果返回
        return $status ? $this->jsonResponse(200, '成功', []) : $this->jsonResponse(202, $ret);
    }

    protected function jsonResponse($code, $message, $data = null)
    {
        $response = [
            'code' => $code,
            'msg'  => $message,
            'data' => $data,
        ];
        return json_encode($response, JSON_UNESCAPED_UNICODE);
    }

    // 用来接收会员中心的回调
    public function actionUserCenterNotify(){
        // 接收数据
        $notify_data = file_get_contents("php://input");
        if(empty($notify_data)) return 'fail';
        
        // 解析数据
        $notify_data = json_decode($notify_data, true);
        if(!isset($notify_data['list'])){
            return 'fail';
        }
        // 理论上数据格式应该是二维数组，但为了保险起见还是判断一下
        foreach($notify_data['list'] as $value){
            if(!is_array($value)){
                // 不是数组则直接跳过
                continue;
            }
            // 验证员工是否存在
            if(!isset($value['uid'])){
                continue;
            }
            $employee = byNew::UserEmployeeModel()->getEmployeeInfo($value['uid'],'employee_id');
            if(empty($employee)){
                // 员工不存在则跳过
                continue;
            }
            // 数据存在则更新员工信息
            $updateData = [];
            if(isset($value['status'])){
                if($value['status'] == 5){
                    $updateData['employee_status'] = 2;
                }else{
                    $updateData['employee_status'] = $value['status'];
                }
            }
            if(isset($value['employee_no'])){
                $updateData['employee_no'] = $value['employee_no'];
            }
            if(isset($value['four_average_score']) && !is_null($value['four_average_score'])){
                $updateData['four_average_score'] = $value['four_average_score'];
            }
            if(isset($value['three_average_score']) && !is_null($value['three_average_score'])){
                $updateData['three_average_score'] = $value['three_average_score'];
            }
            if(isset($value['last_month_score']) && !is_null($value['last_month_score'])){
                $updateData['last_month_score'] = $value['last_month_score'];
            }
            // 更新员工信息
            if(count($updateData)){
                byNew::UserEmployeeModel()->updateEmployeeInfo($value['uid'],'employee_id',$updateData);
            }else{
                // 没有需要更新的数据则跳过
                continue;
            }
        }
        return 'success';
    }

    // 用于接收cms图片验证的回调
    public function actionCmsImageVerifyNotify(){
        // 接收数据
        $notify_data = file_get_contents("php://input");
        if(empty($notify_data)) return 'fail';
        
        // 解析数据
        $notify_data = json_decode($notify_data, true);
        if(!isset($notify_data['pass_back'])){
            return 'fail';
        }
        CUtil::debug('请求参数' . json_encode($notify_data), 'info.cms.notify');
        $status = by::users()->updateMemberAvatarByNotify($notify_data['pass_back'], $notify_data['status']);
        if (!$status) {
            return 'fail';
        }
        return 'success';
    }

    public function actionDrawActivity(){
        $post = \Yii::$app->request->post();
        $acid = $post['acid'] ?? 0;
        $reward = $post['reward'] ?? 0;
        $file = $_FILES['file'] ?? null;
        $codeList = [];
        $key = "draw_activity".$acid;
        $key1 = "draw_activity_stock".$acid;
        
        if ($file) {
            //解析券码文档
            $spreadsheet = IOFactory::load($file['tmp_name']);
            $sheet       = $spreadsheet->getActiveSheet();

            foreach ($sheet->getRowIterator() as $index => $row) {
                if ($index == 1) continue;
                // 读取行数据
                $cellIterator = $row->getCellIterator();
                $cellIterator->setIterateOnlyExistingCells(false); // 获取所有单元格，即使它们没有值
                $rowNumber = $row->getRowIndex();

                $rowData = [];
                foreach ($cellIterator as $cell) {
                    $val = $cell->getValue();
                    // 去除两端空格
                    $val = trim($val);
                    $rowData[] = $val;
                }
                $child = [];
                $child['id'] = $reward;
                // 默认只有两列数据，第一列为金额，第二列为URL
                if (isset($rowData[0])) {
                    $child['name'] = $rowData[0];
                }
                if (isset($rowData[1])) {
                    $child['link'] = $rowData[1];
                }
                $codeList[] = $child;
            }
            if (count($codeList) == 0) {
                return '文件内容为空';
            }
            // 默认导入的表格是顺序的，要对数组进行乱序
            shuffle($codeList);
            foreach($codeList as $val) {
                $b = by::redis('core')->LPUSH($key,json_encode($val));
            }
            // $b = by::redis('core')->LPOP($key);
            by::redis('core')->set($key1,count($codeList),['EX' => 86400]);
            return '导入成功:'.$b.'-'.count($codeList);
        } else {
            return '文件不存在';
        }
    }

    public function actionCheck()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        CUtil::json_response(1, 'ok',TurboModeService::getInstance()->check());

    }

    public function actionClearCache()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        TurboModeService::getInstance()->clearAllCache();
        CUtil::json_response(1, 'ok');
    }

    public function actionChangeData()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        byNew::UserBindModel()->handleUidData();
        byNew::UserEmployeeModel()->handleUidData();
        CUtil::json_response(1, 'ok');
    }
    public function actionHandleHistoryData()
    {
        // 验证
        $pwd = \Yii::$app->request->post('pass', '');
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        // 获取合伙人列表
        $list = byNew::UserEmployeeModel()->getEmployeeAll([]);
        foreach ($list as $item) {
            EventMsg::factory()->run('openRichPlan', ['user_id' => $item['user_id']]);
        }
        CUtil::json_response(1, 'ok');
    }

    // /comm/sync-consume-money
    public function actionSyncConsumeMoney()
    {
        // 验证
        $post = \Yii::$app->request->post();
        CUtil::debug('同步消费金请求参数' . json_encode(array_merge($post, ['pass' => '****']), JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), 'info.sync.consume_money');
        $pwd = $post['pass'] ?? '';
        if ($pwd != 'kDx23Fds3') {
            CUtil::json_response(-1, '无法通过验证');
        }
        $request_id = $post['request_id'] ?? '';
        $uid = $post['uid'] ?? '';
        $event_name = $post['event_name'] ?? '';                 // 事件名称
        $event_code = $post['event_code'] ?? '';                 // 事件编号
        $op_type = (int) ($post['op_type'] ?? 0);               // 操作类型 -1=减少，1=增加
        $consume_money = (int) ($post['consume_money'] ?? 0);   // 消费金（单位分） 增加时正整数，减少时负数
        if (empty($request_id)) {
            CUtil::json_response(-1, 'request_id不能为空');
        }
        if (empty($uid)) {
            CUtil::json_response(-1, 'uid不能为空');
        }
        if (empty($op_type)) {
            CUtil::json_response(-1, '操作类型错误');
        }
        if (empty($consume_money)) {
            CUtil::json_response(-1, '消费金不能为空');
        }
        $redis = by::redis();
        
        $result_key = CUtil::getAllParams(__FUNCTION__, 'result', $request_id);
        // 设置分布式锁key
        $unique_key = CUtil::getAllParams(__FUNCTION__, 'lock', $request_id);
        // 设置当前请求的唯一值，用于当前请求只能释放自己的锁
        $unique_val = md5($unique_key.microtime(true).uniqid());
        $mux = new LockRedis();

        try {
            // 加锁
            $lock = $mux->lock($unique_key, $unique_val, 30);
            if (! $lock) {
                throw new BusinessException('处理中，请勿重复操作');
            }

            $result = $redis->get($result_key);
            if (! $result) {
                $mallInfo = by::usersMall()->getMallInfoByUid($uid, false);
                $mallId = $mallInfo['id'] ?? 0;
                if ($mallId) {
                    $userInfo = by::users()->getUsersByMallId($mallId, false);
                    
                    if ($userInfo) {
                        $user_id = $userInfo[0]['user_id'] ?? 0;
                        if (empty($user_id)) {
                            CUtil::debug(sprintf('API-处理消费金失败: %s 未查询到uid对应的用户 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                            throw new BusinessException('未查询到uid对应的用户1');
                        }

                        if ($op_type > 0) {
                            $status = byNew::UserShopMoneyModel()->AddOrSubtract($user_id, 'add', 2, $consume_money, $user_id, sprintf('%s:%s', $event_code, $event_name));
                        } else {
                            $status = byNew::UserShopMoneyModel()->AddOrSubtract($user_id, 'subtract', 2, $consume_money, $user_id, sprintf('%s:%s', $event_code, $event_name));
                        }

                        if (! $status) {
                            CUtil::debug(sprintf('API-处理消费金失败 status：false request_id：%s', $request_id), 'err.SyncConsumeMoney');
                            throw new BusinessException('处理消费金失败');
                        }
                        $redis->set($result_key, json_encode($post, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), ['EX' => 1800]);
                        $mux->freed($unique_key, $unique_val);
                        CUtil::json_response(1, 'ok');
                    } else {
                        CUtil::debug(sprintf('API-处理消费金失败: %s 未找到mallId对应的用户 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                        throw new BusinessException('未查询到uid对应的用户2');
                    }
                } else {
                    CUtil::debug(sprintf('API-处理消费金失败: %s 未找到对应的mall表记录 request_id：%s', $uid, $request_id), 'err.SyncConsumeMoney');
                    throw new BusinessException('uid不存在');
                }
            } else {
                $mux->freed($unique_key, $unique_val);
                CUtil::json_response(1, 'ok');
            }
        } catch (\Throwable $e) {
            $mux->freed($unique_key, $unique_val);
            CUtil::json_response(-1, sprintf('处理消费金失败：%s', $e->getMessage()));
        }
    }
}
