<?php

namespace app\controllers;

use app\models\CUtil;
use app\modules\main\models\CommModel;
use yii\web\Controller;
use yii\console\Exception;

/**
 * 调度器
 */
class SchedulerController extends Controller
{

    // 签名有效期
    const expire = 60;
    const sign="uTcr8a3d";

    public function beforeAction($action)
    {
        // 设置跨域响应头
        header('Access-Control-Allow-Origin', '*');
        header('Access-Control-Allow-Headers', '*');
        header('Access-Control-Request-Method', 'GET,POST');
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            \Yii::$app->request->headers->add('request_id', $request_id);
        }
        return $this->checkWithGet();

    }

    /**
     * 运行脚本
     * @return void
     */
    public function actionRun()
    {
        $command = \Yii::$app->request->get('command');
        try {
            $command = 'php ' . \Yii::getAlias('@app') . "/yii {$command}"; // 路径到 Yii 控制台入口脚本

            exec($command, $output);

            CUtil::json_response(1, 'ok', $output);
        } catch (Exception $e) {
            // 记录日志
            CUtil::debug("脚本运行失败：" . $command . "，message：" . $e->getMessage() . "，file：" . $e->getFile() . "，line：" . $e->getLine(), 'err.scheduler_run');
            CUtil::json_response(-1, $e->getMessage());
        }
    }

    /**
     *  常用测试检查 zan未使用
     * @return false
     */
    protected function checkWithPost(){

        if (!\Yii::$app->request->isPost) {
            return $this->sendErrorResponse('请求方式错误');
        }

        $data = \Yii::$app->request->post();

        if (!isset($data['api'], $data['sign'], $data['sign_time'])) {
            return $this->sendErrorResponse('缺少必要的参数');
        }

        $api = $data['api'];
        $sign = $data['sign'];
        $signTime = $data['sign_time'];

        unset($data['sign']);

        list($status, $securityKey) = CommModel::getApiKey($api);
        if ($status == -1) {
            return $this->sendErrorResponse('无效的Api信息');
        }

        if ($signTime < intval(START_TIME) - self::expire) {
            return $this->sendErrorResponse('签名已失效');
        }

        $expectedSign = CommModel::getSign($data, $securityKey);
        if ($sign !== $expectedSign) {
            return $this->sendErrorResponse('无效的签名信息');
        }

    }

    /**
     *  常用测试检查 zan未使用
     * @return false
     */
    protected function checkWithGet(){

        $sign = \Yii::$app->request->get('sign');
        if ($sign != self::sign) {
            return $this->sendErrorResponse('sign is illegality');
        }
        $command = \Yii::$app->request->get('command');
        if (! isset($command)) {
            return $this->sendErrorResponse('command is empty');
        }
        return true;


    }


    /**
     * 错误响应
     * @param string $message 错误信息
     * @return false
     */
    protected function sendErrorResponse(string $message)
    {
        CUtil::json_response(-1, $message);
        return false;
    }
}