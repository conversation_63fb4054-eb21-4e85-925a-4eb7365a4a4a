<?php

/**
 * 第三方接口 OMS
 */

namespace app\controllers;


use app\models\CUtil;
use app\modules\goods\services\ErpService;
use app\modules\main\services\OmsService;
use yii\db\Exception;
use yii\web\Controller;

class ErpController extends Controller {

    public $layout = false;
    public $enableCsrfValidation = false;

    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool{
        // 往header里面添加request_id
        $request_id = CUtil::getRequestParam('headers','request_id','');
        if(empty($request_id)){
            $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
            \Yii::$app->request->headers->add('request_id', $request_id);
        }
        return parent::beforeAction($action);
    }

    /**
     * e3+回写：通过rpc调用
     * @return void
     * @throws \yii\db\Exception
     */
    public function actionRewrite()
    {
        $notify_data   = file_get_contents("php://input");
        $post = json_decode($notify_data,true) ?? null;
        $method = $post['method'] ?? '';
        $data   = $post['data'] ?? [];
        if (empty($method) || empty($data)) {
            CUtil::json_response(-1, '参数有误！');
        }

        //开关
        $lockKey = CUtil::omsLock(0,time());
        if(!$lockKey){
            CUtil::json_response(-1, 'oms 回写开关未开放！');
        }

        $erpService = new ErpService();
        list($s,$data) = $erpService->rewrite($method,$data);
        if(!$s){
            CUtil::json_response(-1, $data);
        }

        CUtil::json_response(1, 'ok', ['data' => $data]);
    }

    /**
     * e3+回写：通过e3+直接调用
     * @return void
     * @throws Exception
     */
    public function actionE3Rewrite()
    {
        $raw        = file_get_contents("php://input");
        $omsService = new OmsService();
        list($s, $data) = $omsService->rewriteOmsData($raw);
        if (!$s) {
            CUtil::oms_response(999999, $data);
        }
        CUtil::oms_response(100000, 'success', $data);
    }

}
