<?php
namespace app\controllers;

use app\components\AdminRedisKeys;
use app\jobs\AsyncExportJob;
use app\models\by;
use app\models\CUtil;
use app\modules\goods\models\AsyncExportLogModel;
use Yii;
use app\modules\main\controllers\CommController;

/**
 * @deprecated
 * 处理导出用
 * 管理后台需要用get请求
 */
class SpecialController extends CommController
{

    //系统用户
    protected $_token    = null;
    protected $_user_info = 0;
    protected $_act_type = 0;
    protected $_module   = '';
    protected $_class    = '';
    protected $_fun      = '';
    protected $_params   = [];
    protected $_filename = '';
    protected $_path_info = null; //用于权限校验

    //是否能查看敏感信息
    public $viewSensitive = false;

    /**
     * 导出类型对应的方法（params顺序一定要和方法里的接收顺序一致）
     */
    CONST ACT_TYPE = [
        '1002' => [
            'module'    => 'asset',
            'class'     => 'PointDetailModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => 2022, 'user_id' => 0, 'phone' => '', 'type' => -1, 's_time' => 0, 'e_time' => 0, 'viewSensitive'=> false
            ],
            'filename'  => '积分记录',
            'path_info' => 'back/export-1002'
        ], //积分记录
        '1003' => [
            'module'    => MAIN_MODULE,
            'class'     => 'ProductDetailModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => 2022, 'user_id' => 0, 'phone' => '', 'sn' => '', 's_time' => 0, 'e_time' => 0, 'viewSensitive'=> false
            ],
            'filename'  => '产品注册记录',
            'path_info' => 'back/export-1003'
        ], //产品注册记录
        '1004' => [
            'module'    => MAIN_MODULE,
            'class'     => 'UserExtendModel',
            'fun'       => 'exportData',
            'params'    => [
                'user_id' => 0, 'phone' => 0, 'source' => 0, 's_time' => 0, 'e_time' => 0, 'vip_s_time' => 0, 'vip_e_time' => 0, 'is_export_point' => 0, 'viewSensitive'=> false
            ],
            'filename'  => '用户列表',
            'path_info' => 'back/export-1004'
        ], //用户列表
        '1005' => [
            'module'    => 'goods',
            'class'     => 'OmainModel',
            'fun'       => 'batchExportData',
            'params'    => [
                'year' => '', 'order_no' => '', 'user_iden' => '', 'status' => -1, 'source' => -1, 'order_st' => 0, 'order_ed' => 0, 'p_sources'=> '-1', 'viewSensitive'=> false
            ],
            'filename'  => '订单列表',
            'path_info' => 'back/export-1005'
        ], //订单管理->订单列表->订单导出
        '1006' => [
            'module'    => 'goods',
            'class'     => 'OmainModel',
            'fun'       => 'batchExportGoodsData',
            'params'    => [
                'year' => '', 'order_no' => '', 'user_iden' => '', 'status' => -1, 'source' => -1, 'order_st' => 0, 'order_ed' => 0,'viewSensitive'=> false
            ],
            'filename'  => '订单商品',
            'path_info' => 'back/export-1006'
        ], //订单管理->订单列表->订单商品导出
        '1007' => [
            'module'    => 'goods',
            'class'     => 'GmainModel',
            'fun'       => 'exportData',
            'params'    => [
                'version' => '', 'type' => -1, 'status' => -1, 'name' => '', 'sku' => '', 'tid' => -1,'viewSensitive'=> false
            ],
            'filename'  => '商品列表',
            'path_info' => 'back/export-1007'
        ], //商品管理->商品列表->导出
        '1008' => [
            'module'    => 'goods',
            'class'     => 'OsourceModel',
            'fun'       => 'exportData',
            'params'    => [
                'year' => '', 'order_no' => '', 'order_st' => 0, 'order_ed' => 0, 'user_iden' => '','job_no'=>'','viewSensitive'=> false
            ],
            'filename'  => '导购记录',
            'path_info' => 'back/export-1008'
        ], //导购管理->导购记录->导出
        '1028' => [
            'module'    => 'goods',
            'class'     => 'OsourceModel',
            'fun'       => 'exportGoodsData',
            'params'    => [
                'year' => '', 'order_no' => '', 'order_st' => 0, 'order_ed' => 0, 'user_iden' => '','job_no'=>'','viewSensitive'=> false
            ],
            'filename'  => '导购订单商品记录',
            'path_info' => 'back/export-1028'
        ], //导购管理->导购订单商品记录->导出
        '1009' => [
            'module'    => 'main',
            'class'     => 'GuideModel',
            'fun'       => 'exportData',
            'params'    => [
                'user_id' => 0, 'name' => '', 'status' => 0,'job_no'=>'','viewSensitive'=> false
            ],
            'filename'  => '导购列表',
            'path_info' => 'back/export-1009'
        ], //导购管理->导购列表->导出
        '1010' => [
            'module'    => 'goods',
            'class'     => 'OrefundMainModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_iden' => '', 'status' => -1, 'order_st' => 0, 'order_ed' => 0,'viewSensitive'=> false
            ],
            'filename'  => '退款订单列表',
            'path_info' => 'back/export-1010'
        ], //订单管理->退款订单列表->退款订单导出
        '1011' => [
            'module'    => 'goods',
            'class'     => 'OrefundMainModel',
            'fun'       => 'exportGoodsData',
            'params'    => [
                'order_no' => '', 'user_iden' => '', 'status' => -1, 'order_st' => 0, 'order_ed' => 0,'viewSensitive'=> false
            ],
            'filename'  => '退款订单商品',
            'path_info' => 'back/export-1011'
        ], //内容管理->内容列表->导出
        '1012' => [
            'module'    => 'wiki',
            'class'     => 'WdynamicMainModel',
            'fun'       => 'ExportData',
            'params'    => [
                'status' => -1, 'type' => -1, 't1' => -1, 't2' => -1, 'title' => '','viewSensitive'=> false
            ],
            'filename'  => '内容列表',
            'path_info' => 'back/export-1012'
        ], //订单管理->退款订单列表->退款商品导出
        '1013' => [
            'module'    => 'main',
            'class'     => 'MarketConfigModel',
            'fun'       => 'exportData',
            'params'    => [
                'name' => '', 'status' => -1, 'type' => -1, 'is_delete' => 0,'viewSensitive'=> false
            ],
            'filename'  => '营销列表',
            'path_info' => 'back/export-1013'
        ], //营销配置-营销资源-列表导出
        '1014' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingOrderModel',
            'fun'       => 'exploreExportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'status' => -1, 's_time' => 0, 'e_time' => 0, 'refund_status' => '', 'is_export' => -1, 'phones' => '', 'id' => 0,'viewSensitive'=> false
            ],
            'filename'  => '勘测工单列表',
            'path_info' => 'back/export-1014'
        ], //预约管理-勘测订单-列表导出
        '1015' => [
            'module'    => 'plumbing',
            'class'     => 'ExploreSearchModel',
            'fun'       => 'exportData',
            'params'    => [
                'cname' => '','viewSensitive'=> false
            ],
            'filename'  => '查询记录列表',
            'path_info' => 'back/export-1015'
        ], //预约管理-查询记录-列表导出
        '1016' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingOrderModel',
            'fun'       => 'installExportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'status' => -1, 's_time' => 0, 'e_time' => 0, 'is_export' => -1, 'phones' => '', 'id' => 0,'viewSensitive'=> false
            ],
            'filename'  => '安装工单列表',
            'path_info' => 'back/export-1016'
        ],//预约管理-安装订单-列表导出
        '1017' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingRefundModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'type' => 0, 'status' => 0, 's_time' => 0, 'e_time' => 0,'viewSensitive'=> false
            ],
            'filename'  => '退款工单列表',
            'path_info' => 'back/export-1017'
        ],//预约管理-退款订单-列表导出
        '1018' => [
            'module'    => 'plumbing',
            'class'     => 'PlumbingCommentModel',
            'fun'       => 'exportData',
            'params'    => [
                'order_no' => '', 'user_msg' => '', 'type' => 0,'viewSensitive'=> false
            ],
            'filename'  => '评论中心列表',
            'path_info' => 'back/export-1018'
        ],//预约管理-评论中心-列表导出
        '1019' => [
            'module'    => 'rbac',
            'class'     => 'SystemLogsModel',
            'fun'       => 'exportData',
            'params'    => [
                'admin_id' => '', 'start_time' => 0, 'end_time' => 0, 'module_id' => 0,  'year' => 2022, 'viewSensitive'=> false
            ],
            'filename'  => '日志列表',
            'path_info' => 'back/export-1019'
        ],//系统设置-系统日志-列表导出
    ];

    private function __initActType()
    {
        $config             = self::ACT_TYPE[$this->_act_type] ?? [];

        if (empty($config)) {
            CUtil::json_response(-1, '类型错误');
        }

        $this->_module      = $config['module']     ?? '';
        $this->_class       = $config['class']      ?? '';
        $this->_fun         = $config['fun']        ?? '';
        $this->_params      = $config['params']     ?? [];
        $this->_filename    = $config['filename']   ?? '';
        $this->_path_info   = $config['path_info']  ?? null;

    }

    protected function _initBase()
    {
        //拒绝一切非get 请求
        if(!Yii::$app->request->isGet) {
            CUtil::json_response(-1,'请求方式错误');
        }

        $this->_token       = CUtil::getRequestParam("get",'token','');
        $this->_token       = substr($this->_token,0,32); // md5长度
    }

    /**
     * token校验
     */
    protected function _isValidToken()
    {
        $redis  = by::redis();
        $r_key  = AdminRedisKeys::exportToken($this->_token);

        $json   = $redis->get($r_key);

        if (empty($json)) {
            CUtil::json_response(-1,'签名已失效(1)');
        }

        $json   = urldecode($json);
        $json   = CUtil::decrypt($json, 'EXPORT_TOKEN');

        $this->_aPost = (array)json_decode($json, true);

        //token只用一次
        $redis->del($r_key);

        $this->_sessid  = $this->_aPost['sessid'] ?? '';
        $this->_sessid  = substr($this->_sessid,0,32); // md5长度

        $this->_sign_time   = $this->_aPost['sign_time'] ?? 0;
        $this->_sign_time   = CUtil::uint($this->_sign_time);

        $this->_act_type    = $this->_aPost['act_type'] ?? 0;

        unset($this->_aPost['sign']);

        //有效期检验
        if($this->_sign_time < intval(START_TIME) - $this->_expire) {
            CUtil::json_response(-1,'签名已失效(2)');
        }
    }

    /**
     * @return void
     * 是否能查看敏感信息
     */
    protected function _viewSensitive(){
        //RBAC权限校验
        $check_ret = by::adminUserModel()->checkAuth($this->_user_info, 'back/data/view_sensitive');
        if($check_ret) {
            $this->viewSensitive = true;
        }
    }

    /**
     * @param \yii\base\Action $action
     * @return bool
     * 数据来源合法性验证
     */
    public function beforeAction($action): bool
    {
        try{
            // 往header里面添加request_id
            $request_id = CUtil::getRequestParam('headers','request_id','');
            if(empty($request_id)){
                $request_id = PRO_NAME . '.' . CUtil::getUniqueID();
                Yii::$app->request->headers->add('request_id', $request_id);
            }
            $this->_initBase();

            //token校验
            $this->_isValidToken();

            //登陆态校验
            list($status,$this->_user_info) = by::adminUserModel()->checkLogin($this->_sessid);
            if(!$status) {
                $this->_expired();
            }

            $this->__initActType();

            //敏感信息查看
            $this->_viewSensitive();

            //RBAC权限校验
            $check_ret = by::adminUserModel()->checkAuth($this->_user_info, $this->_path_info);
            if($check_ret === false) {
                CUtil::json_response(401, "没有访问的权限");
            }

            return true;
        } catch (\Exception $e) {
            $error = $e->getMessage() . "|" . $e->getFile() . ":" . $e->getLine();
            CUtil::writeLog(__FUNCTION__, 0, $error, 0, 'error', __METHOD__);
            return false;
        }

    }

    /**
     * 统一导出接口
     * @throws \yii\db\Exception
     */
    public function actionExport()
    {
        $params = [];
        foreach ($this->_params as $param => $default) {
            if (!isset($this->_aPost[$param]) && is_string($default)) {
                switch ($default) {
                    case 'now_y':
                        $default = date('Y');
                        break;
                }
            }
            $params[$param] = $this->_aPost[$param] ?? $default;
        }

        $params['viewSensitive'] = $this->viewSensitive;

        // 异步处理
        $filename = sprintf('%s-%s-%d.xlsx', $this->_filename, date('Ymd'), mt_rand(1000, 9999));
        $admin_user_id = $this->_user_info['id'] ?? 0;
        $time = time();
        $data = [
            'name'          => $filename,
            'export_time'   => $time,
            'admin_user_id' => $admin_user_id,
            'status'        => by::AsyncExportLog()::STATUS['DOING'], // 进行中
            'ctime'         => $time,
            'utime'         => $time,
        ];
        $id = by::AsyncExportLog()->saveLog(0, $data);
        $action = [
            '_class'  => $this->_class,
            '_module' => $this->_module,
            '_fun'    => $this->_fun,
            '_params' => $params,
        ];
        // 插入队列
        \Yii::$app->queue->push(new AsyncExportJob([
            'id'       => $id,
            'filename' => $filename,
            'action'   => $action
        ]));
        // 返回ok
        CUtil::json_response(1, 'ok');
    }
}
