<?php

namespace app\constants;

/**
 * 响应状态码
 */
class RespStatusCodeConst
{
    // 成功
    const SUCCESS_CODE = 1;

    // 失败
    const ERROR_CODE = -1;

    // 系统异常
    const SYS_ERROR_CODE = -10000;

    // 订单业务异常
    const ORDER_ERROR_CODE = -20000;

    // 短信业务异常
    const SMS_ERROR_CODE      = -30000;
    const SMS_SEND_ERROR_CODE = -30001;
    const PARAM_ERROR_CODE    = 56010001;
    const AUTH_ERROR_CODE     = 56010002;
    const LOGIN_ERROR_CODE     = 56010003;

    // 将错误码映射到消息描述
    private static $errorCodeMessages = [
            self::SUCCESS_CODE        => "操作成功",
            self::ERROR_CODE          => "操作失败",
            self::SYS_ERROR_CODE      => "系统异常",
            self::ORDER_ERROR_CODE    => "订单业务异常",
            self::SMS_ERROR_CODE      => "短信业务异常",
            self::SMS_SEND_ERROR_CODE => "短信发送失败",
            self::PARAM_ERROR_CODE    => "参数错误",
            self::AUTH_ERROR_CODE     => "认证错误",
            self::LOGIN_ERROR_CODE    => "授权登录后再操作",
    ];

    /**
     * 根据错误码获取消息描述
     *
     * @param int $code 错误码
     * @return string 错误消息
     */
    public static function getMessageByCode(int $code): string
    {
        return self::$errorCodeMessages[$code] ?? "未知的错误";
    }
}


// 调用示例
//$code = RespStatusCodeConst::SUCCESS_CODE;
//echo RespStatusCodeConst::getMessageByCode($code); // 输出: 操作成功